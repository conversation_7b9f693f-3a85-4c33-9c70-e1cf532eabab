//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vJAXB 2.1.3 in JDK 1.6
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2014.05.06 at 03:52:27 下午 CST
//


package com.huazheng.bpm.entity.procnotify;

import javax.xml.bind.annotation.*;


/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="description" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="condition" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="conditionMode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="calcs" type="{http://www.w3.org/2001/XMLSchema}anyType"/>
 *       &lt;/sequence>
 *       &lt;attribute name="name" type="{http://www.w3.org/2001/XMLSchema}string" />
 *       &lt;attribute name="groupNo" use="required" type="{http://www.w3.org/2001/XMLSchema}int" />
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "description",
        "condition",
        "conditionMode",
        "calcs"
})
@XmlRootElement(name = "userRule", namespace = "http://www.bpmhome.cn/bpm/plugins/userCalc/base")
public class UserRule {

    @XmlElement(namespace = "http://www.bpmhome.cn/bpm/plugins/userCalc/base")
    protected String description;
    @XmlElement(namespace = "http://www.bpmhome.cn/bpm/plugins/userCalc/base")
    protected String condition;
    @XmlElement(namespace = "http://www.bpmhome.cn/bpm/plugins/userCalc/base")
    protected String conditionMode;
    @XmlElement(namespace = "http://www.bpmhome.cn/bpm/plugins/userCalc/base", required = true)
    protected Object calcs;
    @XmlAttribute
    protected String name;
    @XmlAttribute(required = true)
    protected int groupNo;

    /**
     * Gets the value of the description property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getDescription() {
        return description;
    }

    /**
     * Sets the value of the description property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDescription(String value) {
        this.description = value;
    }

    /**
     * Gets the value of the condition property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getCondition() {
        return condition;
    }

    /**
     * Sets the value of the condition property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCondition(String value) {
        this.condition = value;
    }

    /**
     * Gets the value of the conditionMode property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getConditionMode() {
        return conditionMode;
    }

    /**
     * Sets the value of the conditionMode property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setConditionMode(String value) {
        this.conditionMode = value;
    }

    /**
     * Gets the value of the calcs property.
     *
     * @return possible object is
     * {@link Object }
     */
    public Object getCalcs() {
        return calcs;
    }

    /**
     * Sets the value of the calcs property.
     *
     * @param value allowed object is
     *              {@link Object }
     */
    public void setCalcs(Object value) {
        this.calcs = value;
    }

    /**
     * Gets the value of the name property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the value of the name property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * Gets the value of the groupNo property.
     */
    public int getGroupNo() {
        return groupNo;
    }

    /**
     * Sets the value of the groupNo property.
     */
    public void setGroupNo(int value) {
        this.groupNo = value;
    }

}
