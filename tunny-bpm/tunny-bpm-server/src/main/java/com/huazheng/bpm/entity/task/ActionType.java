//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4-2
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.11.24 at 11:54:23 AM CST
//


package com.huazheng.bpm.entity.task;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for actionType.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="actionType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="approve"/>
 *     &lt;enumeration value="assist"/>
 *     &lt;enumeration value="reject"/>
 *     &lt;enumeration value="rejectToPrevious"/>
 *     &lt;enumeration value="rejectToStart"/>
 *     &lt;enumeration value="recover"/>
 *     &lt;enumeration value="commu"/>
 *     &lt;enumeration value="trans"/>
 *     &lt;enumeration value="other"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */
@XmlType(name = "actionType")
@XmlEnum
public enum ActionType {


    /**
     * 审批
     */
    @XmlEnumValue("approve")
    APPROVE("approve"),

    /**
     * 辅助
     */
    @XmlEnumValue("assist")
    ASSIST("assist"),

    /**
     * 驳回
     */
    @XmlEnumValue("reject")
    REJECT("reject"),

    /**
     * 驳回上一步
     */
    @XmlEnumValue("rejectToPrevious")
    REJECT_TO_PREVIOUS("rejectToPrevious"),

    /**
     * 驳回至发起人
     */
    @XmlEnumValue("rejectToStart")
    REJECT_TO_START("rejectToStart"),

    /**
     * 撤销
     */
    @XmlEnumValue("revoke")
    REVOKE("revoke"),

    /**
     * 沟通
     */
    @XmlEnumValue("commu")
    COMMU("commu"),

    /**
     * 任务流转
     */
    @XmlEnumValue("trans")
    TRANS("trans"),

    /**
     * 其他
     */
    @XmlEnumValue("other")
    OTHER("other");
    private final String value;

    ActionType(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static ActionType fromValue(String v) {
        for (ActionType c : ActionType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
