package com.huazheng.bpm.model.node;

import java.io.Serializable;

/**
 * 流程的bo定义
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-上午9:49:57
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class ProcBoDefine implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    /**
     * 是否物理表保存方式
     */
    private boolean isSaveTable = false;

    /**
     * BO的名称。
     */
    private String name = "";

    /**
     * BO的KEY，在流程定义条件等地方使用。
     */
    private String key = "";

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 是否必须。
     */
    private boolean isRequired = false;

    public ProcBoDefine() {
    }

    public ProcBoDefine(String name, String key) {
        this.name = name;
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public boolean isRequired() {
        return isRequired;
    }

    public void setRequired(boolean isRequired) {
        this.isRequired = isRequired;
    }

    public boolean isSaveTable() {
        return isSaveTable;
    }

    public void setSaveTable(boolean isSaveTable) {
        this.isSaveTable = isSaveTable;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

}
