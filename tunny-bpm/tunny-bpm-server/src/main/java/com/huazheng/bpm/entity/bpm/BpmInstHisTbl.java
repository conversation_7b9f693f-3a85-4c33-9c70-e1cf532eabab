package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.entity.framework.AbstractPo;
import com.huazheng.bpm.entity.inst.IBpmProcInst;
import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * 流程实例历史表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：huangchunyan
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-01-19 15:56:36
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmInstHisTbl extends AbstractPo<String> implements IBpmProcInst {
    protected String id;        /*流程实例ID*/
    protected String subject;        /*流程实例标题*/
    protected String procDefId;        /*流程定义ID*/
    protected String bpmnDefId;        /*BPMN流程定义ID*/
    protected String procDefKey;        /*流程定义Key*/
    protected String procDefName;        /*流程名称*/
    protected String bizKey;        /*关联数据业务主键*/
    protected String formType;    /*绑定的表单类型*/
    protected String formKey;        /*绑定的表单主键*/
    protected String status;        /*实例状态*/
    protected java.util.Date endTime;        /*实例结束时间*/
    protected Long duration;        /*持续时间*/
    protected String typeId;        /*所属分类ID*/
    protected String resultType;        /*流程结束后的最终审批结果，agree=同意；refuse=拒绝*/
    protected String bpmnInstId;        /*BPMN流程实例ID*/
    protected String createBy;        /*创建人ID*/
    protected java.util.Date createTime;        /*创建时间*/
    protected String updateBy;        /*更新人ID*/
    protected java.util.Date updateTime;        /*更新时间*/
    protected String isFormmal;        /*是否正式数据 0非正式,1正式*/
    protected String parentInstId;        /*父实例Id*/
    protected Short isForbidden;        /*是否禁止*/
    protected String dataMode;        /*数据保存模式*/
    protected String formName;        /*表单名称*/
    protected String formSource;        /*表单来源*/
    protected String projectuuid;        /*项目uuid*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 流程实例ID
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    /**
     * 返回 流程实例标题
     *
     * @return
     */
    public String getSubject() {
        return this.subject;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    /**
     * 返回 流程定义ID
     *
     * @return
     */
    public String getProcDefId() {
        return this.procDefId;
    }

    public void setBpmnDefId(String bpmnDefId) {
        this.bpmnDefId = bpmnDefId;
    }

    /**
     * 返回 BPMN流程定义ID
     *
     * @return
     */
    public String getBpmnDefId() {
        return this.bpmnDefId;
    }

    public void setProcDefKey(String procDefKey) {
        this.procDefKey = procDefKey;
    }

    /**
     * 返回 流程定义Key
     *
     * @return
     */
    public String getProcDefKey() {
        return this.procDefKey;
    }

    public void setProcDefName(String procDefName) {
        this.procDefName = procDefName;
    }

    /**
     * 返回 流程名称
     *
     * @return
     */
    public String getProcDefName() {
        return this.procDefName;
    }

    public void setBizKey(String bizKey) {
        this.bizKey = bizKey;
    }

    /**
     * 返回 关联数据业务主键
     *
     * @return
     */
    public String getBizKey() {
        return this.bizKey;
    }

    public void setFormType(String formType) {
        this.formType = formType;
    }

    /**
     * 返回 绑定的表单主键
     *
     * @return
     */
    public String getFormType() {
        return this.formType;
    }

    public void setFormKey(String formKey) {
        this.formKey = formKey;
    }

    /**
     * 返回 绑定的表单主键
     *
     * @return
     */
    public String getFormKey() {
        return this.formKey;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 返回 实例状态
     *
     * @return
     */
    public String getStatus() {
        return this.status;
    }

    public void setEndTime(java.util.Date endTime) {
        this.endTime = endTime;
    }

    /**
     * 返回 实例结束时间
     *
     * @return
     */
    public java.util.Date getEndTime() {
        return this.endTime;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    /**
     * 返回 持续时间
     *
     * @return
     */
    public Long getDuration() {
        return this.duration;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    /**
     * 返回 所属分类ID
     *
     * @return
     */
    public String getTypeId() {
        return this.typeId;
    }

    public void setResultType(String resultType) {
        this.resultType = resultType;
    }

    /**
     * 返回 流程结束后的最终审批结果，agree=同意；refuse=拒绝
     *
     * @return
     */
    public String getResultType() {
        return this.resultType;
    }

    public void setBpmnInstId(String bpmnInstId) {
        this.bpmnInstId = bpmnInstId;
    }

    /**
     * 返回 BPMN流程实例ID
     *
     * @return
     */
    public String getBpmnInstId() {
        return this.bpmnInstId;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     * 返回 创建人ID
     *
     * @return
     */
    public String getCreateBy() {
        return this.createBy;
    }

    public void setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 返回 创建时间
     *
     * @return
     */
    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 返回 更新人ID
     *
     * @return
     */
    public String getUpdateBy() {
        return this.updateBy;
    }

    public void setUpdateTime(java.util.Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 返回 更新时间
     *
     * @return
     */
    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    public void setIsFormmal(String isFormmal) {
        this.isFormmal = isFormmal;
    }

    /**
     * 返回 是否正式数据 0非正式,1正式
     *
     * @return
     */
    public String getIsFormmal() {
        return this.isFormmal;
    }

    public void setParentInstId(String parentInstId) {
        this.parentInstId = parentInstId;
    }

    /**
     * 返回 父实例Id
     *
     * @return
     */
    public String getParentInstId() {
        return this.parentInstId;
    }

    public void setIsForbidden(Short isForbidden) {
        this.isForbidden = isForbidden;
    }

    /**
     * 返回 是否禁止
     *
     * @return
     */
    public Short getIsForbidden() {
        return this.isForbidden;
    }

    public void setDataMode(String dataMode) {
        this.dataMode = dataMode;
    }

    /**
     * 返回 数据保存模式
     *
     * @return
     */
    public String getDataMode() {
        return this.dataMode;
    }

    public String getFormName() {
        return formName;
    }

    public void setFormName(String formName) {
        this.formName = formName;
    }

    public String getFormSource() {
        return formSource;
    }

    public void setFormSource(String formSource) {
        this.formSource = formSource;
    }

    public String getProjectuuid() {
        return projectuuid;
    }

    public void setProjectuuid(String projectuuid) {
        this.projectuuid = projectuuid;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("subject", this.subject)
                .append("procDefId", this.procDefId)
                .append("bpmnDefId", this.bpmnDefId)
                .append("procDefKey", this.procDefKey)
                .append("procDefName", this.procDefName)
                .append("bizKey", this.bizKey)
                .append("formType", this.formType)
                .append("formKey", this.formKey)
                .append("status", this.status)
                .append("endTime", this.endTime)
                .append("duration", this.duration)
                .append("typeId", this.typeId)
                .append("resultType", this.resultType)
                .append("bpmnInstId", this.bpmnInstId)
                .append("createBy", this.createBy)
                .append("createTime", this.createTime)
                .append("updateBy", this.updateBy)
                .append("updateTime", this.updateTime)
                .append("isFormmal", this.isFormmal)
                .append("parentInstId", this.parentInstId)
                .append("isForbidden", this.isForbidden)
                .append("dataMode", this.dataMode)
                .append("formName", this.formName)
                .append("formSource", this.formSource)
                .append("projectuuid", this.projectuuid)
                .toString();
    }
}
