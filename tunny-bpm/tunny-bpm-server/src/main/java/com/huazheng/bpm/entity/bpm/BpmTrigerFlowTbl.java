package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * 触发新流程 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-08-23 19:01:23
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmTrigerFlowTbl extends AbstractPo<String> {
    protected String id;        /*主键*/
    protected String defId;        /*流程定义ID*/
    protected String nodeId;        /*节点ID*/
    protected String trigerFlowKey;        /*触发流程Key*/
    protected String action;        /*触发动作*/
    protected String trigerType;        /*触发类型*/
    protected String callStartPage;        /*调用启动页面*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setDefId(String defId) {
        this.defId = defId;
    }

    /**
     * 返回 流程定义ID
     *
     * @return
     */
    public String getDefId() {
        return this.defId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    /**
     * 返回 节点ID
     *
     * @return
     */
    public String getNodeId() {
        return this.nodeId;
    }

    public void setTrigerFlowKey(String trigerFlowKey) {
        this.trigerFlowKey = trigerFlowKey;
    }

    /**
     * 返回 触发流程Key
     *
     * @return
     */
    public String getTrigerFlowKey() {
        return this.trigerFlowKey;
    }

    public void setAction(String action) {
        this.action = action;
    }

    /**
     * 返回 触发动作
     *
     * @return
     */
    public String getAction() {
        return this.action;
    }

    public void setTrigerType(String trigerType) {
        this.trigerType = trigerType;
    }

    /**
     * 返回 触发类型
     *
     * @return
     */
    public String getTrigerType() {
        return this.trigerType;
    }

    public void setCallStartPage(String callStartPage) {
        this.callStartPage = callStartPage;
    }

    /**
     * 返回 调用启动页面
     *
     * @return
     */
    public String getCallStartPage() {
        return this.callStartPage;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("defId", this.defId)
                .append("nodeId", this.nodeId)
                .append("trigerFlowKey", this.trigerFlowKey)
                .append("action", this.action)
                .append("trigerType", this.trigerType)
                .append("callStartPage", this.callStartPage)
                .toString();
    }
}
