package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmOperNotifyRecerDao;
import com.huazheng.bpm.dao.bpm.BpmOperNotifyRecerQueryDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmOperNotifyRecerPo;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

/**
 * 流程通知接收列表 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-03-28 09:43:38
 * </pre>
 */
@SuppressWarnings("serial")
@Service
@Scope("prototype")
public class BpmOperNotifyRecer extends AbstractDomain<String, BpmOperNotifyRecerPo> {

    private BpmOperNotifyRecerDao bpmOperNotifyRecerDao = null;
    private BpmOperNotifyRecerQueryDao bpmOperNotifyRecerQueryDao = null;


    protected void init() {
        bpmOperNotifyRecerDao = AppUtil.getBean(BpmOperNotifyRecerDao.class);
        bpmOperNotifyRecerQueryDao = AppUtil.getBean(BpmOperNotifyRecerQueryDao.class);
        this.setDao(bpmOperNotifyRecerDao);
    }

    @Override
    protected void onSave() {
        if (BeanUtils.isNotEmpty(getId()))
            setNewFlag(BeanUtils.isEmpty(bpmOperNotifyRecerQueryDao.get(getId())));
    }

}
