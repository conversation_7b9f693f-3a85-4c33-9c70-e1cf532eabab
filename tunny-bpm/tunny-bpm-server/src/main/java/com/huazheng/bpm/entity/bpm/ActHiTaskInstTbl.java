package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;


/**
 * 对象功能:act_hi_taskinst Tbl对象
 * 开发公司:广州流辰信息技术有限公司
 * 开发人员:cjj
 * 创建时间:2016-04-03 21:44:13
 */
@SuppressWarnings("serial")
public class ActHiTaskInstTbl extends AbstractPo<String> {
    protected String id; /*ID_*/
    protected String procDefId; /*PROC_DEF_ID_*/
    protected String taskDefKey; /*TASK_DEF_KEY_*/
    protected String procInstId; /*PROC_INST_ID_*/
    protected String executionId; /*EXECUTION_ID_*/
    protected String name; /*NAME_*/
    protected String parentTaskId; /*PARENT_TASK_ID_*/
    protected String description; /*DESCRIPTION_*/
    protected String owner; /*OWNER_*/
    protected String assignee; /*ASSIGNEE_*/
    protected java.util.Date startTime; /*START_TIME_*/
    protected java.util.Date claimTime; /*CLAIM_TIME_*/
    protected java.util.Date endTime; /*END_TIME_*/
    protected Long duration; /*DURATION_*/
    protected String deleteReason; /*DELETE_REASON_*/
    protected Integer priority; /*PRIORITY_*/
    protected java.util.Date dueDate; /*DUE_DATE_*/
    protected String formKey; /*FORM_KEY_*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 ID_
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    /**
     * 返回 PROC_DEF_ID_
     *
     * @return
     */
    public String getProcDefId() {
        return this.procDefId;
    }

    public void setTaskDefKey(String taskDefKey) {
        this.taskDefKey = taskDefKey;
    }

    /**
     * 返回 TASK_DEF_KEY_
     *
     * @return
     */
    public String getTaskDefKey() {
        return this.taskDefKey;
    }

    public void setProcInstId(String procInstId) {
        this.procInstId = procInstId;
    }

    /**
     * 返回 PROC_INST_ID_
     *
     * @return
     */
    public String getProcInstId() {
        return this.procInstId;
    }

    public void setExecutionId(String executionId) {
        this.executionId = executionId;
    }

    /**
     * 返回 EXECUTION_ID_
     *
     * @return
     */
    public String getExecutionId() {
        return this.executionId;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 返回 NAME_
     *
     * @return
     */
    public String getName() {
        return this.name;
    }

    public void setParentTaskId(String parentTaskId) {
        this.parentTaskId = parentTaskId;
    }

    /**
     * 返回 PARENT_TASK_ID_
     *
     * @return
     */
    public String getParentTaskId() {
        return this.parentTaskId;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 返回 DESCRIPTION_
     *
     * @return
     */
    public String getDescription() {
        return this.description;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    /**
     * 返回 OWNER_
     *
     * @return
     */
    public String getOwner() {
        return this.owner;
    }

    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }

    /**
     * 返回 ASSIGNEE_
     *
     * @return
     */
    public String getAssignee() {
        return this.assignee;
    }

    public void setStartTime(java.util.Date startTime) {
        this.startTime = startTime;
    }

    /**
     * 返回 START_TIME_
     *
     * @return
     */
    public java.util.Date getStartTime() {
        return this.startTime;
    }

    public void setClaimTime(java.util.Date claimTime) {
        this.claimTime = claimTime;
    }

    /**
     * 返回 CLAIM_TIME_
     *
     * @return
     */
    public java.util.Date getClaimTime() {
        return this.claimTime;
    }

    public void setEndTime(java.util.Date endTime) {
        this.endTime = endTime;
    }

    /**
     * 返回 END_TIME_
     *
     * @return
     */
    public java.util.Date getEndTime() {
        return this.endTime;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    /**
     * 返回 DURATION_
     *
     * @return
     */
    public Long getDuration() {
        return this.duration;
    }

    public void setDeleteReason(String deleteReason) {
        this.deleteReason = deleteReason;
    }

    /**
     * 返回 DELETE_REASON_
     *
     * @return
     */
    public String getDeleteReason() {
        return this.deleteReason;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    /**
     * 返回 PRIORITY_
     *
     * @return
     */
    public Integer getPriority() {
        return this.priority;
    }

    public void setDueDate(java.util.Date dueDate) {
        this.dueDate = dueDate;
    }

    /**
     * 返回 DUE_DATE_
     *
     * @return
     */
    public java.util.Date getDueDate() {
        return this.dueDate;
    }

    public void setFormKey(String formKey) {
        this.formKey = formKey;
    }

    /**
     * 返回 FORM_KEY_
     *
     * @return
     */
    public String getFormKey() {
        return this.formKey;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("procDefId", this.procDefId)
                .append("taskDefKey", this.taskDefKey)
                .append("procInstId", this.procInstId)
                .append("executionId", this.executionId)
                .append("name", this.name)
                .append("parentTaskId", this.parentTaskId)
                .append("description", this.description)
                .append("owner", this.owner)
                .append("assignee", this.assignee)
                .append("startTime", this.startTime)
                .append("claimTime", this.claimTime)
                .append("endTime", this.endTime)
                .append("duration", this.duration)
                .append("deleteReason", this.deleteReason)
                .append("priority", this.priority)
                .append("dueDate", this.dueDate)
                .append("formKey", this.formKey)
                .toString();
    }
}
