package com.huazheng.bpm.entity.org;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.huazheng.bpm.model.base.PartyEntity;
import com.huazheng.bpm.util.core.JacksonUtil;

import java.util.Collections;
import java.util.List;

/**
 * 参与者 实体对象。
 *
 * <pre>
 * 构建组：ibps-org-biz
 * 作者：huangchunyan
 * 邮箱：<EMAIL>
 * 日期：2016-06-16 17:26:46
 * 版权：广州流辰信息技术有限公司
 * </pre>
 */
@SuppressWarnings("serial")
public class PartyEntityPo extends PartyEntityTbl implements PartyEntity {

    @JsonIgnore
    @Override
    public String getIdentityType() {
        return partyType;
    }

    public static PartyEntityPo fromJsonString(String data) {
        if (JacksonUtil.isNotJsonObject(data)) {
            return null;
        }
        return JacksonUtil.getDTO(data, PartyEntityPo.class);
    }

    public static List<PartyEntityPo> fromJsonArrayString(String listData) {
        if (JacksonUtil.isNotJsonArray(listData)) {
            return Collections.emptyList();
        }
        return JacksonUtil.getDTOList(listData, PartyEntityPo.class);
    }

}
