package com.huazheng.bpm.domain.bpm;


import com.huazheng.bpm.dao.bpm.BpmAuthDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmAuthDefPo;
import com.huazheng.bpm.entity.bpm.BpmAuthPo;
import com.huazheng.bpm.entity.rights.RightsKey;
import com.huazheng.bpm.repository.BpmAuthDefRepository;
import com.huazheng.bpm.service.IRightsDefMgrService;
import com.huazheng.bpm.util.json.JsonUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 流程分管授权 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-02-06 15:02:10
 * </pre>
 */
@SuppressWarnings("serial")
@Service
@Scope("prototype")
public class BpmAuth extends AbstractDomain<String, BpmAuthPo> {

    @Resource
    private BpmAuthDao bpmAuthDao;
    @Resource
    private BpmAuthDefRepository bpmAuthDefRepository;
    @Resource
    private IRightsDefMgrService rightsDefMgrService;
    private BpmAuthDef bpmAuthDefDomain;

    @Override
    protected void init() {
        this.setDao(bpmAuthDao);
        bpmAuthDefDomain = bpmAuthDefRepository.newInstance();
    }

    /* (non-Javadoc)
     * @see com.lc.ibps.base.framework.domain.AbstractDomain#save()
     */
    @Override
    public void save() {
        super.save();

        BpmAuthPo bpmAuth = getData();
        // 保存权限
        rightsDefMgrService.save(RightsKey.BPM_AUTH.getKey(), getId(), bpmAuth.getRightsOwner());
        this.saveRightsDef(getId(), bpmAuth.getRightsDef());
    }

    /**
     * 保存流程权限定义
     *
     * @param authId
     * @param rightsDef
     */
    private void saveRightsDef(String authId, String rightsDef) {
        if (JsonUtil.isEmpty(rightsDef)) {
            return;
        }

        JSONArray jsonArray = JSONArray.fromObject(rightsDef);
        List<BpmAuthDefPo> bpmAuthDefList = new ArrayList<BpmAuthDefPo>();
        for (Object obj : jsonArray) {
            JSONObject json = (JSONObject) obj;
            BpmAuthDefPo bpmAuthDef = new BpmAuthDefPo();
            bpmAuthDef.setDefKey(json.getString("defKey"));
            bpmAuthDef.setDefName(json.getString("defName"));
            bpmAuthDef.setRights(json.getString("rights"));
            bpmAuthDefList.add(bpmAuthDef);
        }

        bpmAuthDefDomain.save(authId, bpmAuthDefList);
    }

    /* (non-Javadoc)
     * @see com.lc.ibps.base.framework.domain.AbstractDomain#deleteByIds(java.io.Serializable[])
     */
    @Override
    public void deleteByIds(String... ids_) {
        super.deleteByIds(ids_);
        bpmAuthDefDomain.delByAuthIds(ids_);
        rightsDefMgrService.delByIdType(RightsKey.BPM_AUTH.getKey(), ids_);
    }

}
