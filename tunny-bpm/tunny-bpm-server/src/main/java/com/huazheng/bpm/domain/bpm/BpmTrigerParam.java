package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmTrigerParamDao;
import com.huazheng.bpm.dao.bpm.BpmTrigerParamQueryDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmTrigerParamPo;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;


/**
 * 触发参数 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-08-23 19:01:24
 * </pre>
 */
@SuppressWarnings("serial")
@Service
@Scope("prototype")
public class BpmTrigerParam extends AbstractDomain<String, BpmTrigerParamPo> {

    private BpmTrigerParamDao bpmTrigerParamDao = null;
    private BpmTrigerParamQueryDao bpmTrigerParamQueryDao = null;


    protected void init() {
        bpmTrigerParamDao = AppUtil.getBean(BpmTrigerParamDao.class);
        bpmTrigerParamQueryDao = AppUtil.getBean(BpmTrigerParamQueryDao.class);
        this.setDao(bpmTrigerParamDao);
    }

    @Override
    protected void onSave() {
        if (BeanUtils.isNotEmpty(getId()))
            setNewFlag(BeanUtils.isEmpty(bpmTrigerParamQueryDao.get(getId())));
    }


}
