package com.huazheng.bpm.plugin.usercalc.role.runtime;


import com.huazheng.bpm.api.entity.SysUser;
import com.huazheng.bpm.api.feign.RemoteBpmUserService;
import com.huazheng.bpm.entity.constant.BpmConstants;
import com.huazheng.bpm.entity.org.PartyEntityPo;
import com.huazheng.bpm.model.base.BpmIdentity;
import com.huazheng.bpm.model.base.PartyEntity;
import com.huazheng.bpm.model.task.IBpmTaskApproval;
import com.huazheng.bpm.plugin.AbstractUserCalcPlugin;
import com.huazheng.bpm.plugin.IBpmPluginDefine;
import com.huazheng.bpm.plugin.usercalc.role.def.RolePluginDefine;
import com.huazheng.bpm.service.BpmApprovalService;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.session.BpmUserCalcPluginSession;
import com.huazheng.bpm.util.string.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 角色运行时插件
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月14日-上午10:25:46
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@Service("rolePlugin")
public class RolePlugin extends AbstractUserCalcPlugin {

    @Resource
    private RemoteBpmUserService remoteBpmUserService;
    @Resource
    private BpmApprovalService bpmApprovalService;

    @Override
    public List<BpmIdentity> queryByPluginDef(BpmUserCalcPluginSession pluginSession, IBpmPluginDefine pluginDef) {
        RolePluginDefine def = (RolePluginDefine) pluginDef;

        List<BpmIdentity> users = new ArrayList<>();
//		List<BpmIdentity> orgs = getIdentity(def, pluginSession);
//		for(BpmIdentity org:orgs){
//			String tmpUsers = null;
//			//defaultPartyUserService.findUserByRoleIdJson(org.getId());
//			if(StringUtil.isNotEmpty(tmpUsers)){
//				for(String user:tmpUsers.split(",")){
//					BpmIdentity identity=getBpmIdentityConverter().convertByUserId(user);
//					if(!users.contains(identity)){
//						users.add(identity);
//					}
//				}
//			}
//		}

        return getIdentity(def, pluginSession);
    }

    /**
     * 根据配置获取所在的组。
     *
     * @param def
     * @param pluginSession
     * @return List&lt;Group>
     */
    private List<BpmIdentity> getIdentity(RolePluginDefine def, BpmUserCalcPluginSession pluginSession) {
        Map<String, Object> vars = pluginSession.getVariables();
        String source = def.getSource();

        List<PartyEntity> list = getRoles(def, pluginSession, vars, source);

        if (BeanUtils.isNotEmpty(list)) {
            return getIdentity(list);
        }

        return Collections.emptyList();
    }

    private List<PartyEntity> getRoles(RolePluginDefine def, BpmUserCalcPluginSession pluginSession,
                                       Map<String, Object> vars, String source) {
        List<PartyEntity> list = new ArrayList<PartyEntity>();
        if ("start".equals(source)) {
            String startId = (String) vars.get(BpmConstants.START_USER);
            //	list = cast2Entity(DefaultPartyRolePo.fromJsonArrayString(roleService.getByUserIdJson(startId)));
        } else if ("prev".equals(source)) {
            String userId = null;
            if (AbstractUserCalcPlugin.isPreVrewModel.get() == null) {
                if (vars.containsKey(BpmConstants.PREV_USER)) {
                    userId = vars.get(BpmConstants.PREV_USER).toString();
                } else {
                    userId = vars.get(BpmConstants.CUR_USER).toString();
                }
                //	list = cast2Entity(DefaultPartyRolePo.fromJsonArrayString(roleService.getByUserIdJson(userId)));
            }
        } else if ("spec".equals(source)) {
            String roleAlias = def.getRoleKey();
            if (roleAlias == null) {
                return Collections.emptyList();
            }
            list = getByRoleAlias(roleAlias, pluginSession);
        } else if ("node".equals(source)) {
            String processInstanceId = (String) pluginSession.getVariables().get(BpmConstants.PROCESS_INST_ID);
            List<IBpmTaskApproval> taskOpinionList = bpmApprovalService.findByInstNodeId(processInstanceId, def.getNodeId(), false);
            IBpmTaskApproval taskOpinion = null;
            if (taskOpinionList.size() > 0) {
                taskOpinion = taskOpinionList.get(taskOpinionList.size() - 1);
            }

            if (taskOpinion != null) {
                //	list = cast2Entity(DefaultPartyRolePo.fromJsonArrayString(
                //		roleService.getByUserIdJson(taskOpinion.getAuditor())));
            }
        }
        return list;
    }

    private List<BpmIdentity> getIdentity(List<PartyEntity> rslist) {
        List<Map<String, Object>> gs = new ArrayList<Map<String, Object>>();
        for (PartyEntity itm : rslist) {
            Map<String, Object> rs = new HashMap<String, Object>();
            rs.put(BpmIdentity.IDENT_ID, itm.getId());
            rs.put(BpmIdentity.IDENT_NAME, itm.getName());
            rs.put(BpmIdentity.IDENT_TYPE, itm.getIdentityType());
            rs.put(BpmIdentity.IDENT_PARTY_TYPE, itm.getIdentityType());
            gs.add(rs);
        }
        return getBpmIdentityConverter().convertByMapList(gs);
    }

    private List<PartyEntity> getByRoleAlias(String roleAlias, BpmUserCalcPluginSession pluginSession) {
        List<PartyEntity> list = new ArrayList<PartyEntity>();
        List<Integer> roleList = new ArrayList<>();
        String[] roleAliasArr = roleAlias.split(",");
        for (String roleAlias_ : roleAliasArr) {
            Integer roleId = Integer.valueOf(roleAlias_);
            roleList.add(roleId);
        }
        Map<String, Object> var = pluginSession.getVariables();

        Map<String, Object> map = new HashMap<>();
        //2020-6-29 xurui 重构逻辑（针对地产开发的逻辑），如果流程发起时传入了项目UUID，则调用新接口查询角色下的人员，如果没传则按旧接口查询
        String projectuuid = null;
        if (var.get("projectuuid") != null) {
            projectuuid = String.valueOf(var.get("projectuuid"));
        }
        List<SysUser> userList = null;
        if (StringUtil.isNotEmpty(projectuuid)) {
            userList = remoteBpmUserService.selectuserbyrolelistandprojectuuid(StringUtils.join(roleList.toArray(), ","), projectuuid);
        } else {
            userList = remoteBpmUserService.selectUserByRole(roleList);
        }
        if (userList != null && userList.size() > 0) {
            for (int i = 0; i < userList.size(); i++) {
                PartyEntityPo partyEntity = new PartyEntityPo();
                partyEntity.setId(userList.get(i).getEmpno());
                partyEntity.setName(userList.get(i).getUserRealname());
                partyEntity.setAlias(userList.get(i).getUserId().toString());
                partyEntity.setPartyType("employee");
                list.add(partyEntity);
            }
        }
        return list;
    }

    /**
     * List<DefaultPartyRolePo>转换为List<PartyEntity>
     *
     * @param roles
     * @return
     */
//	private List<PartyEntity> cast2Entity(List<DefaultPartyRolePo> roles) {
//		if(BeanUtils.isEmpty(roles)){
//			return Collections.emptyList();
//		}
//
//		List<PartyEntity> list = new ArrayList<PartyEntity>();
//		for(DefaultPartyRolePo role : roles){
//			list.add(role);
//		}
//
//		return list;
//	}

}
