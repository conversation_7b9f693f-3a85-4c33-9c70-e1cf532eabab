package com.huazheng.bpm.plugin.usercalc;

import java.io.Serializable;

@SuppressWarnings("serial")
public class ExecutorVar implements Serializable {
    public static final String SOURCE_BO_VAR = "boVar";
    public static final String SOURCE_FLOW_VAR = "flowVar";
    public static final String SOURCE_CONT_VAR = "consVar";

    public static final String EXECUTOR_TYPE_USER = "user";
    public static final String EXECUTOR_TYPE_PARTY = "party";
    public static final String EXECUTOR_TYPE_ORG = "org";
    public static final String EXECUTOR_TYPE_POS = "pos";
    public static final String EXECUTOR_TYPE_ROLE = "role";
    public static final String EXECUTOR_TYPE_FIXED = "fixed";

    public static final String VALTYPE_USERID = "userId";
    public static final String VALTYPE_ACCOUNT = "account";
    public static final String VALTYPE_ORGID = "orgId";
    public static final String VALTYPE_ORGKEY = "orgKey";

    /**
     * 来自BO还是流程变量
     */
    private String source = "";

    private boolean isParty = false;

    /**
     * party/org/user/fixed
     **/
    private String executorType = "";

    /**
     * 如果executorType是user或org则有valType
     **/
    private String valType;

    /**
     * name
     */
    private String name;

    /**
     * 在规则设置的时候，提供值与改对象匹配
     */
    private String value;

    public ExecutorVar() {
    }

    public ExecutorVar(String source, String executorType, String valType, String value) {
        this.source = source;
        this.valType = valType;
        this.executorType = executorType;
        this.value = value;
    }

    public ExecutorVar(String source, String name, String executorType, String valType, String value) {
        this.source = source;
        this.name = name;
        this.executorType = executorType;
        this.valType = valType;
        this.value = value;
    }

//	public ExecutorVar(String source, String name, String executorType, String userValType, String orgValType, String partyValType) {
//		this.source = source;
//		this.name = name;
//		this.executorType = executorType;
//		this.userValType = userValType;
//		this.orgValType = orgValType;
//		this.partyValType = partyValType;
//	}
//
//	public ExecutorVar(String source, String name, String executorType, String userValType, String orgValType, String partyValType, String partyType) {
//		this.source = source;
//		this.name = name;
//		this.executorType = executorType;
//		this.userValType = userValType;
//		this.orgValType = orgValType;
//		this.partyValType = partyValType;
//		this.partyType = partyType;
//	}

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public boolean isParty() {
        return isParty;
    }

    public void setParty(boolean isParty) {
        this.isParty = isParty;
    }

    public String getExecutorType() {
        return executorType;
    }

    public void setExecutorType(String executorType) {
        this.executorType = executorType;
    }

    public String getValType() {
        return valType;
    }

    public void setValType(String valType) {
        this.valType = valType;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
