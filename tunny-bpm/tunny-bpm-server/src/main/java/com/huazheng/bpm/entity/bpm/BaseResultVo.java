package com.huazheng.bpm.entity.bpm;


import com.huazheng.bpm.model.base.IDataObject;

public class BaseResultVo {

    // 操作逻辑内容
    protected String action = "";

    // 对应表名
    protected String tableName = "";

    // 自己的实例
    protected IDataObject dataObject;

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public IDataObject getDataObject() {
        return dataObject;
    }

    public void setDataObject(IDataObject dataObject) {
        this.dataObject = dataObject;
    }

}
