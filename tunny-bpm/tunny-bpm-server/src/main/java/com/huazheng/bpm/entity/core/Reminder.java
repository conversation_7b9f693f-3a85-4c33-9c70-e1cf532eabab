//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4-2
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.08.25 at 03:37:49 PM CST
//


package com.huazheng.bpm.entity.core;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for reminder complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="reminder">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="procDefId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="nodeId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="relNodeId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="relNodeEvent" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="relTimeType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="cronExpression" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="callScript" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="startTime" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="interval" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="sendTimes" type="{http://www.w3.org/2001/XMLSchema}short" minOccurs="0"/>
 *         &lt;element name="dueTime" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="dueAction" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="msgTypeHtml" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="html" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="msgTypePt" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="plainText" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "reminder", propOrder = {
        "id",
        "name",
        "procDefId",
        "nodeId",
        "relNodeId",
        "relNodeEvent",
        "relTimeType",
        "cronExpression",
        "callScript",
        "startTime",
        "interval",
        "sendTimes",
        "dueTime",
        "dueAction",
        "msgTypeHtml",
        "html",
        "msgTypePt",
        "plainText"
})
public class Reminder {

    @XmlElement(required = true)
    protected String id;
    @XmlElement(required = true)
    protected String name;
    @XmlElement(required = true)
    protected String procDefId;
    @XmlElement(required = true)
    protected String nodeId;
    @XmlElement(required = true)
    protected String relNodeId;
    @XmlElement(required = true)
    protected String relNodeEvent;
    @XmlElement(required = true)
    protected String relTimeType;
    protected String cronExpression;
    protected String callScript;
    protected Integer startTime;
    protected Integer interval;
    protected Short sendTimes;
    protected Integer dueTime;
    protected String dueAction;
    protected String msgTypeHtml;
    protected String html;
    protected String msgTypePt;
    protected String plainText;

    /**
     * Gets the value of the id property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setId(String value) {
        this.id = value;
    }

    /**
     * Gets the value of the name property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the value of the name property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * Gets the value of the procDefId property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getProcDefId() {
        return procDefId;
    }

    /**
     * Sets the value of the procDefId property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setProcDefId(String value) {
        this.procDefId = value;
    }

    /**
     * Gets the value of the nodeId property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getNodeId() {
        return nodeId;
    }

    /**
     * Sets the value of the nodeId property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setNodeId(String value) {
        this.nodeId = value;
    }

    /**
     * Gets the value of the relNodeId property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getRelNodeId() {
        return relNodeId;
    }

    /**
     * Sets the value of the relNodeId property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setRelNodeId(String value) {
        this.relNodeId = value;
    }

    /**
     * Gets the value of the relNodeEvent property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getRelNodeEvent() {
        return relNodeEvent;
    }

    /**
     * Sets the value of the relNodeEvent property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setRelNodeEvent(String value) {
        this.relNodeEvent = value;
    }

    /**
     * Gets the value of the relTimeType property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getRelTimeType() {
        return relTimeType;
    }

    /**
     * Sets the value of the relTimeType property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setRelTimeType(String value) {
        this.relTimeType = value;
    }

    /**
     * Gets the value of the cronExpression property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getCronExpression() {
        return cronExpression;
    }

    /**
     * Sets the value of the cronExpression property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCronExpression(String value) {
        this.cronExpression = value;
    }

    /**
     * Gets the value of the callScript property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getCallScript() {
        return callScript;
    }

    /**
     * Sets the value of the callScript property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCallScript(String value) {
        this.callScript = value;
    }

    /**
     * Gets the value of the startTime property.
     *
     * @return possible object is
     * {@link Integer }
     */
    public Integer getStartTime() {
        return startTime;
    }

    /**
     * Sets the value of the startTime property.
     *
     * @param value allowed object is
     *              {@link Integer }
     */
    public void setStartTime(Integer value) {
        this.startTime = value;
    }

    /**
     * Gets the value of the interval property.
     *
     * @return possible object is
     * {@link Integer }
     */
    public Integer getInterval() {
        return interval;
    }

    /**
     * Sets the value of the interval property.
     *
     * @param value allowed object is
     *              {@link Integer }
     */
    public void setInterval(Integer value) {
        this.interval = value;
    }

    /**
     * Gets the value of the sendTimes property.
     *
     * @return possible object is
     * {@link Short }
     */
    public Short getSendTimes() {
        return sendTimes;
    }

    /**
     * Sets the value of the sendTimes property.
     *
     * @param value allowed object is
     *              {@link Short }
     */
    public void setSendTimes(Short value) {
        this.sendTimes = value;
    }

    /**
     * Gets the value of the dueTime property.
     *
     * @return possible object is
     * {@link Integer }
     */
    public Integer getDueTime() {
        return dueTime;
    }

    /**
     * Sets the value of the dueTime property.
     *
     * @param value allowed object is
     *              {@link Integer }
     */
    public void setDueTime(Integer value) {
        this.dueTime = value;
    }

    /**
     * Gets the value of the dueAction property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getDueAction() {
        return dueAction;
    }

    /**
     * Sets the value of the dueAction property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDueAction(String value) {
        this.dueAction = value;
    }

    /**
     * Gets the value of the msgTypeHtml property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getMsgTypeHtml() {
        return msgTypeHtml;
    }

    /**
     * Sets the value of the msgTypeHtml property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setMsgTypeHtml(String value) {
        this.msgTypeHtml = value;
    }

    /**
     * Gets the value of the html property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getHtml() {
        return html;
    }

    /**
     * Sets the value of the html property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setHtml(String value) {
        this.html = value;
    }

    /**
     * Gets the value of the msgTypePt property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getMsgTypePt() {
        return msgTypePt;
    }

    /**
     * Sets the value of the msgTypePt property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setMsgTypePt(String value) {
        this.msgTypePt = value;
    }

    /**
     * Gets the value of the plainText property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getPlainText() {
        return plainText;
    }

    /**
     * Sets the value of the plainText property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPlainText(String value) {
        this.plainText = value;
    }

}
