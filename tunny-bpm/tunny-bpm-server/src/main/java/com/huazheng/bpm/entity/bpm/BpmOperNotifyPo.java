package com.huazheng.bpm.entity.bpm;

import java.util.ArrayList;
import java.util.List;

/**
 * 流程通知 实体对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-03-28 09:43:38
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmOperNotifyPo extends BpmOperNotifyTbl {
    /**
     * 抄送
     */
    public final static String CC = "cc";
    /**
     * 密送
     */
    public final static String BCC = "bcc";
    /**
     * 转发
     */
    public final static String FW = "fw";

    /**
     * 创建一个新的实例 BpmOperNotifyPo.
     */
    public BpmOperNotifyPo() {
    }

    /**
     * 创建一个新的实例 BpmOperNotifyPo.
     */
    public BpmOperNotifyPo(String bpmnDefId, String bpmnInstId, String procDefId, String procInstId
            , String nodeId, String notifier, String notifyTitle
            , String notifyType, String notifyContent, String notifyHtmlContent) {
        this.bpmnDefId = bpmnDefId;
        this.bpmnInstId = bpmnInstId;
        this.procDefId = procDefId;
        this.procInstId = procInstId;
        this.nodeId = nodeId;
        this.notifyTitle = notifyTitle;
        this.notifier = notifier;
        this.notifyType = notifyType;
        this.notifyContent = notifyContent;
        this.notifyHtmlContent = notifyHtmlContent;
    }

    private boolean delBeforeSave = true;

    public boolean isDelBeforeSave() {
        return delBeforeSave;
    }

    public void setDelBeforeSave(boolean delBeforeSave) {
        this.delBeforeSave = delBeforeSave;
    }

    private List<BpmOperNotifyRecerPo> bpmOperNotifyRecerPoList = new ArrayList<BpmOperNotifyRecerPo>();

    public List<BpmOperNotifyRecerPo> getBpmOperNotifyRecerPoList() {
        return bpmOperNotifyRecerPoList;
    }

    public void setBpmOperNotifyRecerPoList(List<BpmOperNotifyRecerPo> bpmOperNotifyRecerPoList) {
        this.bpmOperNotifyRecerPoList = bpmOperNotifyRecerPoList;
    }

    protected String procDefName;/*流程定义名称*/
    protected String nodeName;/*流程定义节点名称*/
    protected String notifierName;/*通知人姓名*/
    protected String isRead;/*是否已读*/

    public String getProcDefName() {
        return procDefName;
    }

    public void setProcDefName(String bpmDefName) {
        this.procDefName = bpmDefName;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getNotifierName() {
        return notifierName;
    }

    public void setNotifierName(String notifierName) {
        this.notifierName = notifierName;
    }

    public String getIsRead() {
        return isRead;
    }

    public void setIsRead(String isRead) {
        this.isRead = isRead;
    }

}
