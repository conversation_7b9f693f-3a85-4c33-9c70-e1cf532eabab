package com.huazheng.bpm.model.form;

/**
 * 保存数据方式。
 *
 * <pre>
 * 构建组：ibps-base-bo
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年12月22日-上午9:29:00
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class DataSaveMode {
    private DataSaveMode() {
    }

    /**
     * 保存数据方式 - 实例对象
     */
    public static final String INSTANCE = "instance";

    /**
     * 保存数据方式 - 物理表
     */
    public static final String TABLE = "table";

    /**
     * 保存数据方式 - 所有方式
     */
    public static final String ALL = "all";

}
