package com.huazheng.bpm.entity.org;

import com.huazheng.bpm.entity.framework.AbstractPo;

/**
 * 参与者  实体对象。
 *
 * <pre>
 * 构建组：ibps-org-biz
 * 作者：huangchunyan
 * 邮箱：<EMAIL>
 * 日期：2016-06-20 09:07:07
 * 版权：广州流辰信息技术有限公司
 * </pre>
 */
@SuppressWarnings("serial")
public class PartyEntityTbl extends AbstractPo<String> {

    public String id; /*ID*/

    //@CheckEnum(value = PartyType.class, message = "{com.lc.ibps.org.party.persistence.entity.PartyEntityTbl.partyType.enum}")
    public String partyType; /*参与者类型。org=组织；employee=员工；position=岗位*/


    public String alias;/*参与者别名，员工别名使用account*/
    public String parentId; /*树 - 父参与者 ID*/

    public String path; /*树 - 路径*/

    public Integer depth; /*树 - 所属层次*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 ID
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setPartyType(String type) {
        this.partyType = type;
    }

    /**
     * 返回 参与者类型。org=组织；employee=员工；position=岗位
     *
     * @return
     */
    public String getPartyType() {
        return this.partyType;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    /**
     * 返回 树 - 父参与者 ID
     *
     * @return
     */
    public String getParentId() {
        return this.parentId;
    }

    public void setPath(String path) {
        this.path = path;
    }

    /**
     * 返回 树 - 路径
     *
     * @return
     */
    public String getPath() {
        return this.path;
    }

    public void setDepth(Integer depth) {
        this.depth = depth;
    }

    /**
     * 返回 树 - 所属层次
     *
     * @return
     */
    public Integer getDepth() {
        return this.depth;
    }

    public interface ValidGroup {

        interface AddAttr {
        }

        interface SaveEmployee {
        }

        interface SavePosition {
        }

        interface SaveOrg {
        }

        interface SaveRole {
        }
    }
}
