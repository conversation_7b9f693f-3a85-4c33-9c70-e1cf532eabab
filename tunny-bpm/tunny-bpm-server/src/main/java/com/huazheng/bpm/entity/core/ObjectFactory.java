//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4-2
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.08.25 at 03:37:49 PM CST
//


package com.huazheng.bpm.entity.core;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each
 * Java content interface and Java element interface
 * generated in the com.huazheng.bpm.biz.bpmn.core.bpmdef package.
 * <p>An ObjectFactory allows you to programatically
 * construct new instances of the Java representation
 * for XML content. The Java representation of XML
 * content can consist of schema derived interfaces
 * and classes representing the binding of schema
 * type definitions, element declarations and model
 * groups.  Factory methods for each of these are
 * provided in this class.
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _Bpmdef_QNAME = new QName("http://www.bpmhome.cn/ibps-bpm-def", "bpmdef");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.huazheng.bpm.biz.bpmn.core.bpmdef
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link Bpmdef }
     */
    public Bpmdef createBpmdef() {
        return new Bpmdef();
    }

    /**
     * Create an instance of {@link Authdef }
     */
    public Authdef createAuthdef() {
        return new Authdef();
    }

    /**
     * Create an instance of {@link Reminders }
     */
    public Reminders createReminders() {
        return new Reminders();
    }

    /**
     * Create an instance of {@link Reminder }
     */
    public Reminder createReminder() {
        return new Reminder();
    }

    /**
     * Create an instance of {@link Auths }
     */
    public Auths createAuths() {
        return new Auths();
    }

    /**
     * Create an instance of {@link TrigerParam }
     */
    public TrigerParam createTrigerParam() {
        return new TrigerParam();
    }

    /**
     * Create an instance of {@link Bpmdefxml }
     */
    public Bpmdefxml createBpmdefxml() {
        return new Bpmdefxml();
    }

    /**
     * Create an instance of {@link TrigerFlows }
     */
    public TrigerFlows createTrigerFlows() {
        return new TrigerFlows();
    }

    /**
     * Create an instance of {@link Authdefs }
     */
    public Authdefs createAuthdefs() {
        return new Authdefs();
    }

    /**
     * Create an instance of {@link Rightdefs }
     */
    public Rightdefs createRightdefs() {
        return new Rightdefs();
    }

    /**
     * Create an instance of {@link Rightdef }
     */
    public Rightdef createRightdef() {
        return new Rightdef();
    }

    /**
     * Create an instance of {@link TrigerParams }
     */
    public TrigerParams createTrigerParams() {
        return new TrigerParams();
    }

    /**
     * Create an instance of {@link Auth }
     */
    public Auth createAuth() {
        return new Auth();
    }

    /**
     * Create an instance of {@link TrigerFlow }
     */
    public TrigerFlow createTrigerFlow() {
        return new TrigerFlow();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Bpmdef }{@code >}}
     */
    @XmlElementDecl(namespace = "http://www.bpmhome.cn/ibps-bpm-def", name = "bpmdef")
    public JAXBElement<Bpmdef> createBpmdef(Bpmdef value) {
        return new JAXBElement<Bpmdef>(_Bpmdef_QNAME, Bpmdef.class, null, value);
    }

}
