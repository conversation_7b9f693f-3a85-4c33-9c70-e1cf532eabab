package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.model.base.AuthorizeRightVo;
import com.huazheng.bpm.util.core.JacksonUtil;

import javax.xml.bind.annotation.XmlAttribute;
import java.util.Collections;
import java.util.List;


/**
 * 流程定义
 * 实体对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2016-12-09 16:33:01
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmDefinePo extends BpmDefineTbl {
    @XmlAttribute(name = "designer")
    protected String designer = "web设计器"; /* 流程设计器 */
    protected String typeName;/* 流程分类名称 */
    protected AuthorizeRightVo authorizeRight;/* 流程分管授权权限对象 */
    protected List<BpmTaskReminderPo> reminders;/* 催办设置  */
    protected List<BpmTrigerFlowPo> trigerFlows;/* 触发流程设置  */
    protected List<BpmAuthPo> auths;/* 分管授权  */

    public List<BpmAuthPo> getAuths() {
        return auths;
    }

    public void setAuths(List<BpmAuthPo> auths) {
        this.auths = auths;
    }

    public List<BpmTrigerFlowPo> getTrigerFlows() {
        return trigerFlows;
    }

    public void setTrigerFlows(List<BpmTrigerFlowPo> trigerFlows) {
        this.trigerFlows = trigerFlows;
    }

    public List<BpmTaskReminderPo> getReminders() {
        return reminders;
    }

    public void setReminders(List<BpmTaskReminderPo> reminders) {
        this.reminders = reminders;
    }

    public AuthorizeRightVo getAuthorizeRight() {
        return authorizeRight;
    }

    public void setAuthorizeRight(AuthorizeRightVo authorizeRight) {
        this.authorizeRight = authorizeRight;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    /**
     * designer
     *
     * @return the designer
     */
    public String getDesigner() {
        return designer;
    }

    /**
     * @param designer the designer to set
     */
    public void setDesigner(String designer) {
        this.designer = designer;
    }

    public static BpmDefinePo fromJsonString(String data) {
        if (!JacksonUtil.isJsonObject(data)) {
            return null;
        }
        return JacksonUtil.getDTO(data, BpmDefinePo.class);
    }

    public static List<BpmDefinePo> fromJsonArrayString(String listData) {
        if (!JacksonUtil.isJsonArray(listData)) {
            return Collections.emptyList();
        }
        return JacksonUtil.getDTOList(listData, BpmDefinePo.class);
    }
}
