package com.huazheng.bpm.entity.rights;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;


/**
 * 权限定义  Tbl实体对象。
 *
 * <pre>
 * 构建组：ibps-common-biz
 * 作者：hcy
 * 邮箱：<EMAIL>
 * 日期：2015-12-16 09:29:19
 * 版权：广州流辰信息技术有限公司
 * </pre>
 */
@SuppressWarnings("serial")
public class RightsDefTbl extends AbstractPo<String> {
    protected String id; /* ID */
    protected String entityType; /* 实体类型，通过数据字典维护。 */
    protected String entityId; /* 实体 ID */
    protected String type; /* 权限类型 */
    protected String rightsId; /* 权限对象ID */
    protected String rightsName; /* 权限对象名 */
    protected java.util.Date createTime; /* 创建时间 */
    protected java.util.Date updateTime; /* 更新时间 */

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 ID
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    /**
     * 返回 实体类型，通过数据字典维护。
     *
     * @return
     */
    public String getEntityType() {
        return this.entityType;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    /**
     * 返回 实体 ID
     *
     * @return
     */
    public String getEntityId() {
        return this.entityId;
    }

    public void setType(String type) {
        this.type = type;
    }

    /**
     * 返回 权限类型
     *
     * @return
     */
    public String getType() {
        return this.type;
    }

    public void setRightsId(String rightsId) {
        this.rightsId = rightsId;
    }

    /**
     * 返回 权限对象ID
     *
     * @return
     */
    public String getRightsId() {
        return this.rightsId;
    }

    public void setRightsName(String rightsName) {
        this.rightsName = rightsName;
    }

    /**
     * 返回 权限对象名
     *
     * @return
     */
    public String getRightsName() {
        return this.rightsName;
    }

    public void setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 返回 创建时间
     *
     * @return
     */
    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public void setUpdateTime(java.util.Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 返回 更新时间
     *
     * @return
     */
    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this).append("id", this.id).append("entityType", this.entityType)
                .append("entityId", this.entityId).append("type", this.type).append("rightsId", this.rightsId)
                .append("rightsName", this.rightsName).append("createTime", this.createTime)
                .append("updateTime", this.updateTime).toString();
    }
}
