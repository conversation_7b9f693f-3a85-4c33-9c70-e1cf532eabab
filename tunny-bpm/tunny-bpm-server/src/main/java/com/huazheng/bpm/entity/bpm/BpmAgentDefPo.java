package com.huazheng.bpm.entity.bpm;

/**
 * 流程代理定义 实体对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-03-30 17:29:14
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmAgentDefPo extends BpmAgentDefTbl {
    protected String procDefName;/*流程定义名称*/
    protected String nodeName;/*节点名称*/

    public String getProcDefName() {
        return procDefName;
    }

    public void setProcDefName(String procDefName) {
        this.procDefName = procDefName;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }
}
