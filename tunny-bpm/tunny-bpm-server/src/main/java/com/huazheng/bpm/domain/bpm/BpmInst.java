package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmInstDao;
import com.huazheng.bpm.dao.bpm.BpmInstHisQueryDao;
import com.huazheng.bpm.dao.bpm.BpmInstQueryDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmInstPo;
import com.huazheng.bpm.entity.constant.ProcInstStatus;
import com.huazheng.bpm.entity.inst.IBpmProcInst;
import com.huazheng.bpm.repository.BpmInstRepository;
import com.huazheng.bpm.repository.BpmTaskChangeRepository;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.web.ResultMessage;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 流程实例
 * 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：luodx
 * 邮箱地址：<EMAIL>
 * 创建时间：2016-12-13 09:25:42
 * </pre>
 */
@SuppressWarnings("serial")
@Transactional
@Scope("prototype")
@Service
public class BpmInst extends AbstractDomain<String, BpmInstPo> {
    @Resource
    private BpmInstDao bpmInstDao;
    @Resource
    private BpmInstRepository bpmInstRepository;
    @Resource
    private BpmTaskChangeRepository bpmTaskChangeRepository;
    @Resource
    private BpmInstQueryDao bpmInstQueryDao;
    @Resource
    private BpmInstHisQueryDao bpmInstHisQueryDao;
    @Resource
    private ActExecution actExecutionDomain;
    @Resource
    private BpmApprove bpmApproveDomain;
    @Resource
    private BpmTaskAssign bpmTaskAssignDomain;
    @Resource
    private BpmTask bpmTaskDomain;
    @Resource
    private BpmTaskSign bpmTaskSignDomain;

    @Override
    protected void init() {
        this.setDao(bpmInstDao);
    }

    public void remove() {
        String processInstId = getId();
        remove(processInstId);
    }

    /**
     * 根据实例id删除实例数据，包含任务、act（变量、任务、人员等）、人员指派
     *
     * @param processInstId
     */
    public void remove(String processInstId) {
        BpmInstPo inst = bpmInstQueryDao.get(processInstId);
        // 草稿
        if (ProcInstStatus.STATUS_DRAFT.getKey().equals(inst.getStatus())) {
            super.delete(processInstId);
        } else {
            /*
             * 父级
             * 当前
             * 子集
             */
            IBpmProcInst topInstance = bpmInstRepository.getTopBpmProcInst(processInstId);
            String topInstId = topInstance.getId();
            List<String> instIdList = bpmInstRepository.findIdsByParentId(topInstId, true);

            List<String> bpmnInstList = bpmInstQueryDao.findInstIdsByIds(instIdList);
            if (BeanUtils.isEmpty(bpmnInstList)) {
                throw new RuntimeException("流程实例数据查找数据失败！数据=" + Arrays.toString(instIdList.toArray()));
            }

            // 删除
            removeCascade(instIdList);

            // 删除流程数据。
            actExecutionDomain.delByInstList(bpmnInstList);

            // 删除关联的实例。
            String topBpmnInstId = topInstance.getBpmnInstId();
            actExecutionDomain.delete(topBpmnInstId);
        }
    }

    /**
     * 删除任务数据 删除任务人员数据 删除实例数据 删除抄送数据 状态数据 TASK_READ BPM_TASK_SIGNDATA
     * BPM_TASK_TURN
     *
     * @param instList void
     * @throws Exception
     */
    private void removeCascade(List<String> instList) {
        // 删除意见数据
        bpmApproveDomain.delByInstList(instList);
        // 删除候选人数据
        bpmTaskAssignDomain.delByInst(instList);
        // 删除任务
        bpmTaskDomain.delByInst(instList);
        // 删除会签数据
        bpmTaskSignDomain.delByInst(instList);
        // 删除转办代理信息
        BpmTaskChange bpmTaskChangeDomain = bpmTaskChangeRepository.newInstance();
        bpmTaskChangeDomain.delByInst(instList);
        //TODO 删除执行信息

        for (String id : instList) {
            super.delete(id);
        }
    }

    /**
     * 更新时如果状态为结束或者手工结束，则删除运行实例数据，更新历史数据。
     */
    @Override
    public void update() {
        BpmInstPo entity = getData();
        String status = entity.getStatus();
        // 流程结束时，删除当前实例数据并归档。
        if (ProcInstStatus.STATUS_END.getKey().equals(status)
                || ProcInstStatus.STATUS_MANUAL_END.getKey().equals(status)) {
            super.update();
            //bpmInstDao.createHistory(entity);
        } else {
            super.update();
        }
    }

    /**
     * 根据实例id修改实例状态
     *
     * @param procInstId
     * @param status
     */
    public void updateStatusByBpmnInstanceId(String procInstId, String status) {
        bpmInstDao.updateStatusByInstId(procInstId, status);
    }

    /**
     * 修改实例状态，instid使用newInstance注入
     *
     * @param status
     */
    public void updateStatusByInstanceId(String status) {
        String processInstanceId = getId();
        bpmInstDao.updateStatusById(processInstanceId, status);
    }

    /**
     * 根据流程定义key修改实例禁止状态
     *
     * @param defKey
     * @param isForbidden
     */
    public void updForbiddenByDefKey(String defKey, Integer isForbidden) {
        bpmInstDao.updateForbiddenByDefKey(defKey, isForbidden);
    }

    /**
     * 修改实例禁止状态，instid使用newIntance注入
     *
     * @param isForbidden
     */
    public void updForbiddenByInstId(Integer isForbidden) {
        String instId = getId();
        bpmInstDao.updateForbiddenByInstId(instId, isForbidden);
    }

    /**
     * 清除测试数据
     *
     * @param defKey
     */
    public void removeTestInstByDefKey(String defKey) {
        List<BpmInstPo> list = bpmInstQueryDao.findByDefKeyFormal(defKey, IBpmProcInst.FORMAL_NO);
        for (BpmInstPo instance : list) {
            remove(instance.getId());
        }
    }

    /**
     * 从追回节点取得后续任务节点对应的任务。
     *
     * <pre>
     * 1.根据流程实例获取相关的所有任务。
     * 2.取得从追回节点之后的任务节点。
     * 3.遍历后续的任务节点，取得这些节点对应的任务。
     * 4.构建一个Map，map中键为节点ID,值为节点任务列表。
     * 5.如果map为空表示后续没有任务了，不可以追回。
     * 6.如果一个节点对应多个任务，表示这个是一个多实例任务。
     * </pre>
     *
     * @param bpmTasks
     * @param bpmNodeDef
     * @return ResultMessage
     */
    @SuppressWarnings("unused")
//	private ResultMessage isNextTask(List<DefaultBpmTaskPo> bpmTasks, BpmNodeDef bpmNodeDef) {
//		ResultMessage message = ResultMessage.getSuccess();
//		// 取得当前节点后的任务节点。
//		List<BpmNodeDef> nodeDefs = bpmNodeDef.getInnerOutcomeTaskNodes(true);
//
//		// List<DefaultBpmTask> list=new ArrayList<DefaultBpmTask>();
//		Map<String, List<DefaultBpmTaskPo>> taskMap = new HashMap<String, List<DefaultBpmTaskPo>>();
//
//		for (BpmNodeDef nodeDef : nodeDefs) {
//
//			for (DefaultBpmTaskPo bpmTask : bpmTasks) {
//				String nodeId = bpmTask.getNodeId();
//
//				if (!nodeDef.getNodeId().equals(nodeId)) {
//					continue;
//				}
//
//				if (taskMap.containsKey(nodeId)) {
//					List<DefaultBpmTaskPo> list = taskMap.get(nodeId);
//					list.add(bpmTask);
//				} else {
//					List<DefaultBpmTaskPo> list = new ArrayList<DefaultBpmTaskPo>();
//					list.add(bpmTask);
//					taskMap.put(nodeId, list);
//				}
//				break;
//			}
//		}
//		if (taskMap.size() == 0) {
//			message.setResult(ResultMessage.ERROR);
//			message.setMessage("下一步任务已经完成不能撤销");
//			return message;
//		}
//
//		message.addVariable("taskMap", taskMap);
//
//		return message;
//	}

//	@SuppressWarnings("unused")
    private ResultMessage checkInstance(IBpmProcInst processInstance) {
        ResultMessage message = ResultMessage.getSuccess();
        String status = processInstance.getStatus();
        if (ProcInstStatus.STATUS_RUNNING.getKey().equals(status)) {
            return message;
        }
        message.setResult(ResultMessage.ERROR);
        String msg = "";
        if (ProcInstStatus.STATUS_REJECT.getKey().equals(status)) {
            msg = "流程被驳回";
        } else if (ProcInstStatus.STATUS_REJECT_TOSTART.getKey().equals(status)) {
            msg = "流程被驳回到发起人";
        } else if (ProcInstStatus.STATUS_END.getKey().equals(status)) {
            msg = "流程实例已结束";
        } else if (ProcInstStatus.STATUS_END.getKey().equals(status)) {
            msg = "流程实例被人工终止";
        } else if (ProcInstStatus.STATUS_DRAFT.getKey().equals(status)) {
            msg = "流程实例为草稿状态";
        } else if (ProcInstStatus.STATUS_REVOKE.getKey().equals(status)) {
            msg = "流程实例为撤销状态";
        } else if (ProcInstStatus.STATUS_REVOKE_TOSTART.getKey().equals(status)) {
            msg = "流程实例为撤销状态";
        }
        message.setMessage(msg);
        return message;
    }


    /**1=请先设置当前执行人!
     * 2=发起节点后有多个节点!
     * 3=流程已处于第一个节点!
     * 4=当前执行人和流程发起人不是同一个人!
     * 5=任务已在发起节点,不能再撤销!
     * 0=驳回成功!
     * @param instanceId
     * @param currentUser
     * @return
     */
//	public Integer canRevokeToStart(String instanceId, User currentUser) {
//		IBpmProcInst instance = bpmInstQueryDao.get(instanceId);
//		String defId = instance.getProcDefId();
//		List<BpmNodeDef> nodeDefs = bpmDefinitionAccessor.getStartNodes(defId);
//
//		if (currentUser == null) {
//			return 1;
//		}
//
//		if (nodeDefs.size() > 1) {
//			return 2;
//		}
//
//		String status = instance.getStatus();
//		// 流程状态。
//		if (ProcessInstanceStatus.STATUS_REVOKE_TOSTART.getKey().equals(status)
//				|| ProcessInstanceStatus.STATUS_BACK_TOSTART.getKey().equals(status)) {
//			return 3;
//		}
//
//		if (!currentUser.getUserId().equals(instance.getCreateBy())) {
//			return 4;
//		}
//		// 验证任务是否已经在发起节点。
//		boolean rtn = validTask(instance.getBpmnInstId(), nodeDefs.get(0));
//		if (!rtn) {
//			return 5;
//		}
//
//		return 0;
//	}
//
//	private boolean validTask(String bpmnInstId, BpmNodeDef nodeDef) {
//		String nodeId = nodeDef.getNodeId();
//		List<ActTaskPo> list = actTaskQueryDao.getByInstId(bpmnInstId);
//
//		for (ActTaskPo task : list) {
//			if (nodeId.equals(task.getTaskDefKey())) {
//				return false;
//			}
//		}
//		return true;
//	}

    /**
     * 批量删除流程实例
     *
     * @param ids
     */
    public void removeByIds(String[] ids) {
        if (BeanUtils.isNotEmpty(ids)) {
            for (String id : ids) {
                IBpmProcInst topInstance = bpmInstRepository.getTopBpmProcInst(id);
                String topInstId = topInstance.getId();
                List<String> instIdList = bpmInstRepository.findIdsByParentId(topInstId, true);
                // 删除流程数据
                List<String> bpmnInstList = bpmInstQueryDao.findInstIdsByIds(instIdList);
                actExecutionDomain.delByInstList(bpmnInstList);
                // 删除关联的实例
                String topBpmnInstId = topInstance.getBpmnInstId();
                actExecutionDomain.delete(topBpmnInstId);
                // 删除
                removeCascade(instIdList);
            }
        }
    }

    /**
     * 流程实例归档
     *
     * @param procInstId
     */
    public void createHistory(String procInstId) {
        BpmInstPo po = bpmInstQueryDao.get(procInstId);
        if (BeanUtils.isNotEmpty(po)) {
            bpmInstDao.createHistory(po);
        }
    }

    public void update(BpmInstPo po) {
        bpmInstDao.update(po);
    }
}
