package com.huazheng.bpm.entity.bpm;


import com.huazheng.bpm.util.string.StringUtil;

/**
 * 流程实例历史 实体对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：huangchunyan
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-01-19 15:56:36
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmInstHisPo extends BpmInstHisTbl {
    private String creator;/* 创建人姓名 */
    private String updator;/* 修改人姓名 */
    private String forbidden;/* 是否禁止 */

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getUpdator() {
        return updator;
    }

    public void setUpdator(String updator) {
        this.updator = updator;
    }

    public String getForbidden() {
        if (StringUtil.isNotEmpty(forbidden)) {
            return forbidden;
        }
        switch (isForbidden) {
            case 0:
                return "未禁止";
            case 1:
                return "禁止";
            default:
                break;
        }
        return "未禁止";
    }

    public void setForbidden(String forbidden) {
        this.forbidden = forbidden;
    }
}
