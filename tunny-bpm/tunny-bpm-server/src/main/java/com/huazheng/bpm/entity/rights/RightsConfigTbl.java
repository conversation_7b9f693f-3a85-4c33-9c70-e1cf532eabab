package com.huazheng.bpm.entity.rights;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;


/**
 * 权限配置 Tbl对象。
 *
 * <pre>
 * 构建组：ibps-common-biz
 * 作者：hcy
 * 邮箱：<EMAIL>
 * 日期：2015-12-16 09:29:19
 * 版权：广州流辰信息技术有限公司
 * </pre>
 */
@SuppressWarnings("serial")
public class RightsConfigTbl extends AbstractPo<String> {
    protected String id; /*ID*/
    protected String name; /*类型名称*/
    protected String key; /*类型KEY*/
    protected String entityType; /*实体类型*/
    protected String entityIdKey; /*实体ID的KEY，默认是ID_*/
    protected String ownRights; /*拥有的权限*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 ID
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 返回 类型名称
     *
     * @return
     */
    public String getName() {
        return this.name;
    }

    public void setKey(String key) {
        this.key = key;
    }

    /**
     * 返回 类型KEY
     *
     * @return
     */
    public String getKey() {
        return this.key;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    /**
     * 返回 实体类型
     *
     * @return
     */
    public String getEntityType() {
        return this.entityType;
    }

    public void setEntityIdKey(String entityIdKey) {
        this.entityIdKey = entityIdKey;
    }

    /**
     * 返回 实体ID的KEY，默认是ID_
     *
     * @return
     */
    public String getEntityIdKey() {
        return this.entityIdKey;
    }

    public void setOwnRights(String ownRights) {
        this.ownRights = ownRights;
    }

    /**
     * 返回 拥有的权限
     *
     * @return
     */
    public String getOwnRights() {
        return this.ownRights;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("name", this.name)
                .append("key", this.key)
                .append("entityType", this.entityType)
                .append("entityIdKey", this.entityIdKey)
                .append("ownRights", this.ownRights)
                .toString();
    }
}
