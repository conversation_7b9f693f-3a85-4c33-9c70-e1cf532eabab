package com.huazheng.bpm.entity.inst;

/**
 * Bpm节点执行实体接口
 * 构建组：ibps-bpmn-api
 * 作者：simon cai
 * <EMAIL>
 * 日期:2013-11-7-下午2:58:34
 * 版权：广州流辰信息技术有限公司版权所有
 */
public interface BpmExecution {
    /**
     * 执行ID
     *
     * @return
     */
    String getExecId();

    /**
     * 获取节点编码
     *
     * @return
     */
    String getNodeKey();

    /**
     * 返回流程实例ID
     *
     * @return
     */
    String getProcInstId();

    /**
     * 流程定义ID
     *
     * @return
     */
    String getProcDefId();

    /**
     * 父执行ID
     *
     * @return
     */
    String getParentExecId();

    /**
     * 关联数据业务主键
     *
     * @return
     */
    String getBizKey();

    /**
     * 是否同步
     *
     * @return
     */
    boolean isConcurrent();
}
