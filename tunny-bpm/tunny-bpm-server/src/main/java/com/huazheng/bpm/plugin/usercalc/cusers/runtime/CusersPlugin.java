package com.huazheng.bpm.plugin.usercalc.cusers.runtime;

import com.huazheng.bpm.entity.constant.BpmConstants;
import com.huazheng.bpm.model.base.BpmIdentity;
import com.huazheng.bpm.model.task.IBpmTaskApproval;
import com.huazheng.bpm.plugin.AbstractUserCalcPlugin;
import com.huazheng.bpm.plugin.IBpmPluginDefine;
import com.huazheng.bpm.plugin.usercalc.ExecutorVar;
import com.huazheng.bpm.plugin.usercalc.UserCalcHelper;
import com.huazheng.bpm.plugin.usercalc.cusers.def.CusersPluginDefine;
import com.huazheng.bpm.service.BpmApprovalService;
import com.huazheng.bpm.service.IPartyUserService;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.session.BpmUserCalcPluginSession;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 指定用户插件
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月28日-下午3:10:41
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@Service("cusersPlugin")
@Transactional
public class CusersPlugin extends AbstractUserCalcPlugin {

    @Resource
    private BpmApprovalService bpmApprovalService;

    @Override
    public List<BpmIdentity> queryByPluginDef(
            BpmUserCalcPluginSession pluginSession, IBpmPluginDefine pluginDef) {

        IPartyUserService partyUserService = AppUtil.getBean(IPartyUserService.class);
        List<BpmIdentity> list = new ArrayList<BpmIdentity>();

        CusersPluginDefine def = (CusersPluginDefine) pluginDef;
        Map<String, Object> vars = pluginSession.getVariables();
        String source = def.getSource();

        if ("start".equals(source)) {
            String startId = (String) vars.get(BpmConstants.START_USER);
            BpmIdentity bpmIdentity = getBpmIdentityConverter().convertByUserId(startId);
            list.add(bpmIdentity);
        } else if ("prev".equals(source)) {
            String userId = null;
            if (AbstractUserCalcPlugin.isPreVrewModel.get() == null) {
                if (vars.containsKey(BpmConstants.PREV_USER)) {
                    userId = vars.get(BpmConstants.PREV_USER).toString();
                } else {
                    userId = vars.get(BpmConstants.CUR_USER).toString();
                }
                BpmIdentity bpmIdentity = getBpmIdentityConverter().convertByUserId(userId);
                list.add(bpmIdentity);
            }
        } else if ("spec".equals(source)) {
            String userKeys = def.getAccount();
            String[] aryAccount = userKeys.split(",");
            JSONObject user = null;
            //后续需要修改接口；
            for (String account : aryAccount) {

//				if(partyUserService!=null){
//					user = JSONObject.fromObject(partyUserService.getByAccountJson(account));
//				}else{
//					// TODO rest
//					break;
//				}
                BpmIdentity bpmIdentity = getBpmIdentityConverter().convertByUserId(account);
                list.add(bpmIdentity);
            }
        } else if ("var".equals(source)) {
            ExecutorVar executorVar = def.getExecutorVar();
            UserCalcHelper uch = new UserCalcHelper();
            List<String> pks = uch.getCalcsPKByExecutor(executorVar, pluginSession);
            for (String pk : pks) {
                BpmIdentity bpmIdentity = getBpmIdentityConverter().convertByUserId(pk);
                list.add(bpmIdentity);
            }
        } else if ("node".equals(source)) {
            String processInstanceId = (String) pluginSession.getVariables().get(BpmConstants.PROCESS_INST_ID);
            List<IBpmTaskApproval> taskOpinionList = bpmApprovalService.findByInstNodeId(processInstanceId, def.getNodeId(), false);
            IBpmTaskApproval taskOpinion = null;
            if (taskOpinionList.size() > 0) {
                taskOpinion = taskOpinionList.get(taskOpinionList.size() - 1);
            }

            if (taskOpinion != null) {
                BpmIdentity bpmIdentity = getBpmIdentityConverter().convertByUserId(taskOpinion.getAuditor());
                list.add(bpmIdentity);
            }
        }

        return list;
    }

}
