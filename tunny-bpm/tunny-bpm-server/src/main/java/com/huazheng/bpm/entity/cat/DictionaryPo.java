package com.huazheng.bpm.entity.cat;


import com.huazheng.bpm.model.cat.IDictionary;

/**
 * 分类标识表 实体对象。
 *
 * <pre>
 *
 * 构建组：bps-common-biz
 * 作者：huangcy
 * 邮箱：<EMAIL>
 * 日期：2015-11-19 14:17:31
 * 版权：广州流辰信息技术有限公司
 * </pre>
 */
@SuppressWarnings("serial")
public class DictionaryPo extends DictionaryTbl implements IDictionary {

    public final static String ROOT_ID = "0";

    /**
     * type key
     */
    protected String typeKey;

    public String getTypeKey() {
        return typeKey;
    }

    public void setTypeKey(String typeKey) {
        this.typeKey = typeKey;
    }

}
