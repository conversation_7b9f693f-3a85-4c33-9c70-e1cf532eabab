package com.huazheng.bpm.entity.bpm;

/**
 * 流程操作日志 实体对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-04-21 11:18:52
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmOperLogPo extends BpmOperLogTbl {
    protected String operTypeName;
    protected String creator;
    protected String procDefName;

    public String getOperTypeName() {
        return operTypeName;
    }

    public void setOperTypeName(String operTypeName) {
        this.operTypeName = operTypeName;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getProcDefName() {
        return procDefName;
    }

    public void setProcDefName(String procDefName) {
        this.procDefName = procDefName;
    }
}
