package com.huazheng.bpm.model.setting;


import com.huazheng.bpm.model.define.IBpmVariableDefine;
import com.huazheng.bpm.model.form.IForm;
import com.huazheng.bpm.model.node.Attribute;
import com.huazheng.bpm.model.node.IExtForm;
import com.huazheng.bpm.model.node.ProcBoDefine;

import java.util.List;

/**
 * 全局设置。
 *
 * <pre>
 *
 * 构建组：ibps-bpmn-biz
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2017年1月13日-上午11:46:07
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class BpmDefineGlobal {
    // 流程定义Key
    private String id;
    // bo
    private ProcBoDefine bo;
    // 全局表单
    private IExtForm globalForm;
    // 节点表单
    private IForm instForm;
    // 扩展属性
    private List<Attribute> attributes;

    // 流程变量
    private List<IBpmVariableDefine> variables;

    // 办结抄送
    private String procNotify;
    // 插件JSON（必须包含“pluginType”）
    private String pluginJson;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public ProcBoDefine getBo() {
        return bo;
    }

    public void setBo(ProcBoDefine bo) {
        this.bo = bo;
    }

    public IExtForm getGlobalForm() {
        return globalForm;
    }

    public void setGlobalForm(IExtForm globalForm) {
        this.globalForm = globalForm;
    }

    public IForm getInstForm() {
        return instForm;
    }

    public void setInstForm(IForm instForm) {
        this.instForm = instForm;
    }

    public List<Attribute> getAttributes() {
        return attributes;
    }

    public void setAttributes(List<Attribute> attributes) {
        this.attributes = attributes;
    }

    public List<IBpmVariableDefine> getVariables() {
        return variables;
    }

    public void setVariables(List<IBpmVariableDefine> variables) {
        this.variables = variables;
    }

    public String getProcNotify() {
        return procNotify;
    }

    public void setProcNotify(String procNotify) {
        this.procNotify = procNotify;
    }

    public String getPluginJson() {
        return pluginJson;
    }

    public void setPluginJson(String pluginJson) {
        this.pluginJson = pluginJson;
    }

}
