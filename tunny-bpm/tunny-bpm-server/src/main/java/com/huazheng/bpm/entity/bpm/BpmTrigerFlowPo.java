package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.model.node.ITrigerFlow;
import com.huazheng.bpm.model.node.ITrigerParam;
import com.huazheng.bpm.util.core.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 触发新流程 实体对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-08-23 19:01:23
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmTrigerFlowPo extends BpmTrigerFlowTbl implements ITrigerFlow {
    protected String defName;                /*流程定义名称*/
    protected String nodeName;            /*节点名称*/
    protected String trigerFlowName;        /*触发流程名称*/
    protected String actionName;            /*触发动作名称*/
    private boolean delBeforeSave = true;

    public boolean isDelBeforeSave() {
        return delBeforeSave;
    }

    public void setDelBeforeSave(boolean delBeforeSave) {
        this.delBeforeSave = delBeforeSave;
    }

    private List<ITrigerParam> params = new ArrayList<ITrigerParam>();
    private List<BpmTrigerParamPo> bpmTrigerParamPoList = new ArrayList<BpmTrigerParamPo>();

    public List<BpmTrigerParamPo> getBpmTrigerParamPoList() {
        if (BeanUtils.isEmpty(bpmTrigerParamPoList)) {
            initParamPos(this.params);
        }

        return bpmTrigerParamPoList;
    }

    public void setBpmTrigerParamPoList(List<BpmTrigerParamPo> bpmTrigerParamPoList) {
        this.bpmTrigerParamPoList = bpmTrigerParamPoList;
    }

    public String getDefName() {
        return defName;
    }

    public void setDefName(String defName) {
        this.defName = defName;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getTrigerFlowName() {
        return trigerFlowName;
    }

    public void setTrigerFlowName(String trigerFlowName) {
        this.trigerFlowName = trigerFlowName;
    }

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    @Override
    public void setParams(List<ITrigerParam> params) {
        this.params = params;
    }

    @Override
    public List<ITrigerParam> getParams() {
        if (BeanUtils.isEmpty(params)) {
            initParams(this.bpmTrigerParamPoList);
        }
        return params;
    }

    private void initParams(List<BpmTrigerParamPo> bpmTrigerParamPoList) {
        params = new ArrayList<ITrigerParam>();
        if (BeanUtils.isEmpty(bpmTrigerParamPoList)) {
            return;
        }

        for (BpmTrigerParamPo po : bpmTrigerParamPoList) {
            params.add(po);
        }
    }

    private void initParamPos(List<ITrigerParam> params) {
        bpmTrigerParamPoList = new ArrayList<BpmTrigerParamPo>();
        if (BeanUtils.isEmpty(params)) {
            return;
        }

        for (ITrigerParam p : params) {
            bpmTrigerParamPoList.add((BpmTrigerParamPo) p);
        }
    }

}
