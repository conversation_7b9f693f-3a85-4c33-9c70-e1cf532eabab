package com.huazheng.bpm.entity.bpm;


import com.huazheng.bpm.model.base.AuthorizeRightVo;
import com.huazheng.bpm.model.base.BpmIdentity;
import com.huazheng.bpm.model.task.IBpmTask;
import com.huazheng.bpm.model.task.SkipRes;
import com.huazheng.bpm.util.core.StringPool;

import java.util.List;

/**
 * 流程任务 实体对象
 *
 * <pre>
 *
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：simon cai
 * 邮箱地址：<EMAIL>
 * 创建时间：2016-12-14 15:30:23
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmTaskPo extends BpmTaskTbl implements IBpmTask, Cloneable {

    public static final Integer UNSUSPEND = 1;
    public static final Integer SUSPEND = 2;

    public static final Integer LOCK = 1;
    public static final Integer UNLOCK = 0;

    protected String allowShfit = StringPool.Y;
    protected AuthorizeRightVo authorizeRight; /* 流程分管授权权限对象 */

    public AuthorizeRightVo getAuthorizeRight() {
        return authorizeRight;
    }

    public void setAuthorizeRight(AuthorizeRightVo authorizeRight) {
        this.authorizeRight = authorizeRight;
    }

    /**
     * 任务执行人是否为空，这个不保存到数据库。
     */
    protected transient boolean isIdentityEmpty = false;

    public boolean isIdentityEmpty() {
        return isIdentityEmpty;
    }

    public void setIdentityEmpty(boolean isIdentityEmpty) {
        this.isIdentityEmpty = isIdentityEmpty;
    }

    // 任务候选人
    protected transient List<BpmIdentity> identityList = null;

    public List<BpmIdentity> getIdentityList() {
        return identityList;
    }

    public void setIdentityList(List<BpmIdentity> identityList) {
        this.identityList = identityList;
    }

    protected transient SkipRes skipResult = new SkipRes();

    public SkipRes getSkipResult() {
        return skipResult;
    }

    public void setSkipResult(SkipRes skipResult) {
        this.skipResult = skipResult;
    }

    /**
     * 所属人
     */
    protected String ownerName;

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    protected int remindTimes;

    public int getRemindTimes() {
        return remindTimes;
    }

    public void setRemindTimes(int remindTimes) {
        this.remindTimes = remindTimes;
    }

    public BpmTaskPo clone() {
        BpmTaskPo o = null;
        try {
            o = (BpmTaskPo) super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return o;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.huazheng.bpm.api.bpmn.model.task.IBpmTask#getOwnerId()
     */
    @Override
    public String getOwnerId() {
        return null;
    }

    public String getAllowShfit() {
        return allowShfit;
    }

    public void setAllowShfit(String allowShfit) {
        this.allowShfit = allowShfit;
    }

}
