package com.huazheng.bpm.model.base;

/**
 * 流程图样式。
 *
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年11月29日-下午3:13:57
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class FlowImageStyle {

    // 边框颜色
    private String borderColor;
    // 边框粗细
    private Float borderWidth;
    // 背景颜色
    private String backgroundColor;

    public FlowImageStyle() {
        super();
    }

    public String getBorderColor() {
        return borderColor;
    }

    public void setBorderColor(String borderColor) {
        this.borderColor = borderColor;
    }

    public Float getBorderWidth() {
        return borderWidth;
    }

    public void setBorderWidth(Float borderWidth) {
        this.borderWidth = borderWidth;
    }

    public String getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

}
