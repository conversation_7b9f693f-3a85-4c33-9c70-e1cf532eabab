package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmOperLogDao;
import com.huazheng.bpm.dao.bpm.BpmOperLogQueryDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmOperLogPo;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

/**
 * 流程操作日志 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-04-21 11:18:52
 * </pre>
 */
@SuppressWarnings("serial")
@Service
@Scope("prototype")
public class BpmOperLog extends AbstractDomain<String, BpmOperLogPo> {

    private BpmOperLogDao bpmOperLogDao = null;
    private BpmOperLogQueryDao bpmOperLogQueryDao = null;


    protected void init() {
        bpmOperLogDao = AppUtil.getBean(BpmOperLogDao.class);
        bpmOperLogQueryDao = AppUtil.getBean(BpmOperLogQueryDao.class);
        this.setDao(bpmOperLogDao);
    }

    @Override
    protected void onSave() {
        if (BeanUtils.isNotEmpty(getId()))
            setNewFlag(BeanUtils.isEmpty(bpmOperLogQueryDao.get(getId())));
    }

}
