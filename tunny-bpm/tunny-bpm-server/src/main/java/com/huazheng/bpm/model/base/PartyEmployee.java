package com.huazheng.bpm.model.base;

/**
 * 员工实体接口
 *
 * <pre>
 * 构建组：ibps-api-base
 * 作者：zhongjh
 * 邮箱：<EMAIL>
 * 日期：2017年8月11日-上午12:26:15
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface PartyEmployee extends IdentityType {

    /**
     * 获取员工ID
     *
     * @return
     */
    String getId();

    /**
     * 获取员工邮箱地址
     *
     * @return
     */
    String getEmail();

    /**
     * 获取员工电话号码
     *
     * @return
     */
    String getMobile();

    /**
     * 获取员工地址
     *
     * @return
     */
    String getAddress();

    /**
     * 获取员工微信号
     *
     * @return
     */
    String getWcAccount();

    /**
     * 获取员工岗位ID
     *
     * @return
     */
    String getPositions();

    /**
     * 获取员工组织ID
     *
     * @return
     */
    String getGroupID();


}
