package com.huazheng.bpm.plugin.usercalc.pos.context;

import com.huazheng.bpm.plugin.AbstractUserCalcPluginContext;
import com.huazheng.bpm.plugin.IBpmPluginDefine;
import com.huazheng.bpm.plugin.IPluginParser;
import com.huazheng.bpm.plugin.usercalc.pos.def.PosPluginDefine;
import com.huazheng.bpm.plugin.usercalc.pos.runtime.PosPlugin;
import com.huazheng.bpm.util.base.XmlUtil;
import com.jamesmurty.utils.XMLBuilder;
import net.sf.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.w3c.dom.Element;

import javax.xml.parsers.FactoryConfigurationError;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;

/**
 * 岗位插件
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月14日-上午10:20:40
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
@Service("posPluginContext")
@Transactional
@Scope("prototype")
public class PosPluginContext extends AbstractUserCalcPluginContext implements IPluginParser {

    @Override
    public String getDescription() {
        PosPluginDefine def = (PosPluginDefine) this.getBpmPluginDefine();
        String source = def.getSource();
        StringBuffer sb = new StringBuffer();

        if ("start".equals(source)) {
            sb.append("发起人岗位");
        } else if ("prev".equals(source)) {
            sb.append("上一步执行人岗位");
        } else if ("spec".equals(source)) {
            sb.append("指定岗位");
            sb.append("[");
            sb.append(def.getPosName());
            sb.append("]");
        } else if ("node".equals(source)) {
            sb.append("节点[").append(def.getNodeName()).append("]");
        }

        return sb.toString();
    }

    @Override
    public String getTitle() {
        return "岗位";
    }

    @Override
    public Class<? extends PosPlugin> getPluginClass() {
        return PosPlugin.class;
    }

    //	<?xml version="1.0" encoding="UTF-8"?>
//	<pos xmlns="http://www.bpmhome.cn/bpm/plugins/userCalc/pos"
//	      source="" logicCal="" extract="">
//	    <ps posKey="" posName=""/>
//	</pos>
    @Override
    public String getPluginXml() {
        PosPluginDefine def = (PosPluginDefine) this.getBpmPluginDefine();
        String source = def.getSource();

        XMLBuilder xmlBuilder;
        try {
            xmlBuilder = XMLBuilder.create("pos")
                    .a("xmlns", "http://www.bpmhome.cn/bpm/plugins/userCalc/pos")
                    .a("source", source)
                    .a("logicCal", def.getLogicCal().getKey())
                    .a("extract", def.getExtract().getKey());
            if ("spec".equals(source)) {
                xmlBuilder.e("positions")
                        .a("posKey", def.getPosKey())
                        .a("posName", def.getPosName());
            } else if ("node".equals(source)) {
                xmlBuilder.e("nodes").a("nodeId", def.getNodeId()).a("nodeName", def.getNodeName()).up();
            }

            return xmlBuilder.asString();
        } catch (ParserConfigurationException e) {
            e.printStackTrace();
        } catch (FactoryConfigurationError e) {
            e.printStackTrace();
        } catch (TransformerException e) {
            e.printStackTrace();
        }
        return "";
    }

    @Override
    protected IBpmPluginDefine parseElement(Element element) {
        PosPluginDefine def = new PosPluginDefine();

        String source = element.getAttribute("source");
        def.setSource(source);
        if ("spec".equals(source)) {
            Element posEl = XmlUtil.getChildNodeByName(element, "positions");
            String posKey = posEl.getAttribute("posKey");
            String posName = posEl.getAttribute("posName");
            def.setPosKey(posKey);
            def.setPosName(posName);
        } else if ("node".equals(source)) {
            Element memberEl = XmlUtil.getChildNodeByName(element, "nodes");
            String nodeId = memberEl.getAttribute("nodeId");
            String nodeName = memberEl.getAttribute("nodeName");

            def.setNodeId(nodeId);
            def.setNodeName(nodeName);
        }

        return def;
    }

    @Override
    protected IBpmPluginDefine parseJson(JSONObject pluginJson) {
        PosPluginDefine def = new PosPluginDefine();
        String source = pluginJson.getString("source");
        def.setSource(source);
        if ("spec".equals(source)) {
            String posKey = pluginJson.getString("posKey");
            String posName = pluginJson.getString("posName");
            def.setPosKey(posKey);
            def.setPosName(posName);
        } else if ("node".equals(source)) {
            String nodeId = pluginJson.getString("nodeId");
            String nodeName = pluginJson.getString("nodeName");
            def.setNodeId(nodeId);
            def.setNodeName(nodeName);
        }

        return def;
    }

}
