package com.huazheng.bpm.entity.bpm;

import java.util.Date;

/**
 * 任务催办记录 实体对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-03-22 16:02:00
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmTaskReminderRecPo extends BpmTaskReminderRecTbl {

    public BpmTaskReminderRecPo() {
    }

    public BpmTaskReminderRecPo(String id, String procDefId, String bpmTaskId,
                                Date remindTime, String userId, String procInstId,
                                Short remindType) {
        super();
        this.id = id;
        this.procDefId = procDefId;
        this.taskId = bpmTaskId;
        this.remindTime = remindTime;
        this.userId = userId;
        this.procInstId = procInstId;
        this.remindType = remindType;
    }
}
