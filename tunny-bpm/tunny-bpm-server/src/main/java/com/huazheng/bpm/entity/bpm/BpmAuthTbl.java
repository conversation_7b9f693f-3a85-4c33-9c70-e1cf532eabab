package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;


/**
 * 流程分管授权 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-02-06 15:02:10
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmAuthTbl extends AbstractPo<String> {
    protected String id;        /*主键*/
    protected String name;        /*流程授权说明*/
    protected String type;        /*TYPE_*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 返回 流程授权说明
     *
     * @return
     */
    public String getName() {
        return this.name;
    }

    public void setType(String type) {
        this.type = type;
    }

    /**
     * 返回 TYPE_
     *
     * @return
     */
    public String getType() {
        return this.type;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("name", this.name)
                .append("type", this.type)
                .toString();
    }
}
