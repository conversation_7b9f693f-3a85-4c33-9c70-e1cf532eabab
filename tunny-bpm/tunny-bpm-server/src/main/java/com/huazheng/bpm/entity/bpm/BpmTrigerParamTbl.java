package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 触发参数 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-08-23 19:01:24
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmTrigerParamTbl extends AbstractPo<String> {
    protected String id;                /*主键*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmTrigerParamTbl.trigerId")
    protected String trigerId;        /*触发ID*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmTrigerParamTbl.srcAttr")
    protected String srcAttr;            /*源属性*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmTrigerParamTbl.srcAttrName")
    protected String srcAttrName;        /*源属性名*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmTrigerParamTbl.destAttr")
    protected String destAttr;        /*目标属性*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmTrigerParamTbl.destAttrName")
    protected String destAttrName;    /*目标属性名*/
    protected String allowEmpty;        /*允许为空*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setTrigerId(String trigerId) {
        this.trigerId = trigerId;
    }

    /**
     * 返回 触发ID
     *
     * @return
     */
    public String getTrigerId() {
        return this.trigerId;
    }

    public void setSrcAttr(String srcAttr) {
        this.srcAttr = srcAttr;
    }

    /**
     * 返回 源属性
     *
     * @return
     */
    public String getSrcAttr() {
        return this.srcAttr;
    }

    public void setDestAttr(String destAttr) {
        this.destAttr = destAttr;
    }

    /**
     * 返回 目标属性
     *
     * @return
     */
    public String getDestAttr() {
        return this.destAttr;
    }

    public String getSrcAttrName() {
        return srcAttrName;
    }

    public void setSrcAttrName(String srcAttrName) {
        this.srcAttrName = srcAttrName;
    }

    public String getDestAttrName() {
        return destAttrName;
    }

    public void setDestAttrName(String destAttrName) {
        this.destAttrName = destAttrName;
    }

    public void setAllowEmpty(String allowEmpty) {
        this.allowEmpty = allowEmpty;
    }

    /**
     * 返回 允许为空
     *
     * @return
     */
    public String getAllowEmpty() {
        return this.allowEmpty;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("trigerId", this.trigerId)
                .append("srcAttr", this.srcAttr)
                .append("srcAttrName", this.srcAttrName)
                .append("destAttr", this.destAttr)
                .append("destAttrName", this.destAttrName)
                .append("allowEmpty", this.allowEmpty)
                .toString();
    }
}
