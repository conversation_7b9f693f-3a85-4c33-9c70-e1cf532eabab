package com.huazheng.bpm.model.node;

import com.huazheng.bpm.model.define.BpmSubTableRight;
import com.huazheng.bpm.model.define.IBpmVariableDefine;
import com.huazheng.bpm.util.string.StringUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户任务节点定义
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午12:00:06
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
public class UserTaskNodeDefine extends BaseNodeDefine implements IMultiInstanceDefine {

    /**
     * 跳转规则列表。
     * 备注：如果在会签节点定义了这个规则，那么完成时可以跳转到某个节点。
     */
    private List<DefaultJumpRule> jumpRuleList;

    /**
     * 节点变量
     */
    private List<IBpmVariableDefine> variableList;

    /**
     * 子表权限
     */
    private List<BpmSubTableRight> bpmSubTableRights = new ArrayList<BpmSubTableRight>();

    public List<DefaultJumpRule> getJumpRuleList() {
        return jumpRuleList;
    }

    public void setJumpRuleList(List<DefaultJumpRule> jumpRuleList) {
        this.jumpRuleList = jumpRuleList;
    }

    public List<IBpmVariableDefine> getVariableList() {
        return variableList;
    }

    public void setVariableList(List<IBpmVariableDefine> variableList) {
        this.variableList = variableList;
    }

    @Override
    public boolean supportMuliInstance() {
        return false;
    }

    @Override
    public boolean isParallel() {
        return false;
    }

    @Override
    public void setSupportMuliInstance(boolean support) {

    }

    @Override
    public void setParallel(boolean isParallel) {

    }

    public List<BpmSubTableRight> getBpmSubTableRights() {
        return bpmSubTableRights;
    }

    public void setBpmSubTableRights(List<BpmSubTableRight> bpmSubTableRights) {
        this.bpmSubTableRights = bpmSubTableRights;
    }

    public void addBpmSubTableRight(BpmSubTableRight tableRight) {
        bpmSubTableRights.add(tableRight);
    }

    public BpmSubTableRight getBpmSubTableRight() {
        for (BpmSubTableRight right : this.bpmSubTableRights) {
            if (StringUtil.isEmpty(right.getParentDefKey())) {
                return right;
            }
        }
        return null;
    }

    /**
     * 根据父key获取子表表单权限
     *
     * @param parentDefKey
     * @return BpmSubTableRight
     */
    public BpmSubTableRight getBpmSubTableRightByParentDefKey(String parentDefKey) {
        for (BpmSubTableRight right : this.bpmSubTableRights) {
            if (StringUtil.isNotEmpty(right.getParentDefKey()) && right.getParentDefKey().equals(parentDefKey)) {
                return right;
            }
        }
        return null;
    }

}
