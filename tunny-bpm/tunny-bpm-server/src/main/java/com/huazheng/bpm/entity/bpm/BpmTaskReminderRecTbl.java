package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * 任务催办记录 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-03-22 16:02:00
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmTaskReminderRecTbl extends AbstractPo<String> {
    protected String id;        /*主键*/
    protected String procDefId;        /*流程定义ID*/
    protected String procInstId;        /*流程实例ID*/
    protected String taskId;        /*任务ID*/
    protected String userId;        /*用户ID*/
    protected Short remindType;        /*提醒类型(1，消息提醒,2,完成流程办结的处理动作)*/
    protected java.util.Date remindTime;        /*催办时间*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    /**
     * 返回 流程定义ID
     *
     * @return
     */
    public String getProcDefId() {
        return this.procDefId;
    }

    public void setProcInstId(String procInstId) {
        this.procInstId = procInstId;
    }

    /**
     * 返回 流程实例ID
     *
     * @return
     */
    public String getProcInstId() {
        return this.procInstId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    /**
     * 返回 任务ID
     *
     * @return
     */
    public String getTaskId() {
        return this.taskId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * 返回 用户ID
     *
     * @return
     */
    public String getUserId() {
        return this.userId;
    }

    public void setRemindType(Short remindType) {
        this.remindType = remindType;
    }

    /**
     * 返回 提醒类型(1，消息提醒,2,完成流程办结的处理动作)
     *
     * @return
     */
    public Short getRemindType() {
        return this.remindType;
    }

    public void setRemindTime(java.util.Date remindTime) {
        this.remindTime = remindTime;
    }

    /**
     * 返回 催办时间
     *
     * @return
     */
    public java.util.Date getRemindTime() {
        return this.remindTime;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("procDefId", this.procDefId)
                .append("procInstId", this.procInstId)
                .append("taskId", this.taskId)
                .append("userId", this.userId)
                .append("remindType", this.remindType)
                .append("remindTime", this.remindTime)
                .toString();
    }
}
