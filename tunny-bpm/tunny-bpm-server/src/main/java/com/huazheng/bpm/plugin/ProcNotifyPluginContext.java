package com.huazheng.bpm.plugin;

import com.huazheng.bpm.entity.constant.EventType;
import com.huazheng.bpm.entity.core.ProcNotify;
import com.huazheng.bpm.entity.procnotify.OnEnd;
import com.huazheng.bpm.model.base.NotifyItem;
import com.huazheng.bpm.model.base.NotifyVo;
import com.huazheng.bpm.model.core.ProcNotifyPluginDefine;
import com.huazheng.bpm.plugin.task.userassign.plugin.UserQueryPlugin;
import com.huazheng.bpm.util.base.XmlUtil;
import com.huazheng.bpm.util.core.JAXBUtil;
import com.huazheng.bpm.util.core.NotifyUtil;
import com.huazheng.bpm.util.json.JsonUtil;
import com.jamesmurty.utils.XMLBuilder;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import org.w3c.dom.Element;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 流程办结通知
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午7:35:03
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
public class ProcNotifyPluginContext extends AbstractBpmExecutionPluginContext implements IUserQueryPluginContext {

    @Override
    public List<EventType> getEventTypeList() {
        List<EventType> eventTypes = new ArrayList<EventType>();
        eventTypes.add(EventType.END_EVENT);
        return eventTypes;
    }

    @Override
    public Class<? extends IRuntimePlugin<?, ?, ?>> getPluginClass() {
        return ProcNotifyPlugin.class;
    }

    /* (non-Javadoc)
     * @see com.lc.ibps.bpmn.api.plugin.context.IUserQueryPluginContext#getUserQueryPluginClass()
     */
    @Override
    public Class<? extends IRuntimePlugin<?, ?, ?>> getUserQueryPluginClass() {
        return UserQueryPlugin.class;
    }

    private NotifyVo convert(OnEnd onEnd, Element pluginEl) {
        NotifyVo notifyVo = new NotifyVo();
        notifyVo.setEventType(EventType.END_EVENT);
        Element onEndEl = XmlUtil.getChildNodeByName(pluginEl, "onEnd");
        List<NotifyItem> notifyItems = NotifyUtil.parseNotifyItems(onEndEl);
        notifyVo.setNotifyItemList(notifyItems);
        return notifyVo;
    }

    /**
     * 构建插件XML。
     * xml格式如下。
     * <pre>
     * &lt;procNotify xmlns="http://www.bpmhome.cn/bpm/plugins/execution/procNotify" >
     * &lt;onEnd >
     * &lt;notify xmlns="http://www.bpmhome.cn/bpm/plugins/task/baseNotify" msgTypes="sms">
     *   &lt;userRule xmlns="http://www.bpmhome.cn/bpm/plugins/userCalc/base" groupNo="1">
     *        &lt;calcs>&lt;/calcs>
     *   &lt;/userRule>
     * &lt;/notify>
     * &lt;notify xmlns="http://www.bpmhome.cn/bpm/plugins/task/baseNotify" msgTypes="mail">&lt;/notify>
     * &lt;/onEnd>
     * &lt;/procNotify>
     * </pre>
     */
    @Override
    public String getPluginXml() {
        ProcNotifyPluginDefine def = (ProcNotifyPluginDefine) this.getBpmPluginDefine();
        try {
            XMLBuilder xmlBuilder = XMLBuilder.create("procNotify")
                    .a("xmlns", "http://www.bpmhome.cn/bpm/plugins/execution/procNotify");

            xmlBuilder = xmlBuilder.e("onEnd");
            NotifyVo createVo = def.getNotifyVoMap().get(EventType.END_EVENT);
            if (createVo != null) {
                NotifyUtil.handXmlBuilder(createVo, xmlBuilder);
                return xmlBuilder.asString();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    @Override
    public String getJson() {
        ProcNotifyPluginDefine def = (ProcNotifyPluginDefine) this.getBpmPluginDefine();
        Collection<NotifyVo> assignRules = def.getNotifyVoMap().values();
        JsonConfig config = new JsonConfig();
        NotifyUtil.getJsonConfig(config, assignRules);
        JSONObject jsonObject = JSONObject.fromObject(def, config);
        Object json = jsonObject.getJSONObject("notifyVoMap").getJSONObject("endEvent").getJSONArray("notify");
        return json.toString();
    }

    @Override
    public IBpmPluginDefine parseJson(String pluginJson) {
        ProcNotifyPluginDefine procNotifyPluginDef = new ProcNotifyPluginDefine();
        if (JsonUtil.isEmpty(pluginJson)) {
            return procNotifyPluginDef;
        }
        JSONArray endJson = JSONArray.fromObject(pluginJson);
        NotifyVo createVo = NotifyUtil.getNotifyVo(endJson);

        procNotifyPluginDef.addNotifyVo(EventType.END_EVENT, createVo);

        return procNotifyPluginDef;
    }

    @Override
    public IBpmPluginDefine parseElement(Element element) {
        String xml = XmlUtil.getXML(element);
        ProcNotifyPluginDefine procNotifyPluginDef = new ProcNotifyPluginDefine();
        try {
            ProcNotify procNotify = (ProcNotify) JAXBUtil.unmarshall(xml, com.huazheng.bpm.entity.core.ObjectFactory.class);
            List<NotifyVo> notifyVoList = new ArrayList<NotifyVo>();
            OnEnd onEnd = procNotify.getOnEnd();
            if (onEnd != null) {
                NotifyVo notifyVoOnEnd = convert(onEnd, element);
                notifyVoList.add(notifyVoOnEnd);
                procNotifyPluginDef.getNotifyVoMap().put(EventType.END_EVENT, notifyVoOnEnd);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return procNotifyPluginDef;
    }

    @Override
    public String getTitle() {
        return "流程办结通知";
    }

}
