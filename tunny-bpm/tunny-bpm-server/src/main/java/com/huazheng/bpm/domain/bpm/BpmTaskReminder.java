package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmTaskReminderDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmTaskReminderPo;
import com.huazheng.bpm.model.node.IReminderDef;
import com.huazheng.bpm.model.setting.BpmDefineNode;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 任务催办设置
 * 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-03-22 11:46:22
 * </pre>
 */
@SuppressWarnings("serial")
@Service
@Scope("prototype")
public class BpmTaskReminder extends AbstractDomain<String, BpmTaskReminderPo> {

    private BpmTaskReminderDao bpmTaskReminderDao = null;


    protected void init() {
        bpmTaskReminderDao = AppUtil.getBean(BpmTaskReminderDao.class);
        this.setDao(bpmTaskReminderDao);
    }

    /**
     * 保存催办
     *
     * @param nodes
     */
    public void save(String defId, List<BpmDefineNode> nodes) {
        List<IReminderDef> reminders = null;
        for (BpmDefineNode node : nodes) {
            reminders = node.getReminders();
            if (BeanUtils.isEmpty(reminders)) {
                continue;
            }

            bpmTaskReminderDao.deleteByProcNodeId(defId, node.getId());

            for (IReminderDef reminder : reminders) {
                bpmTaskReminderDao.create((BpmTaskReminderPo) reminder);
            }
        }
    }

}
