package com.huazheng.bpm.plugin;

/**
 * 插件解析器
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月9日-下午3:56:54
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface IPluginParser {

    /**
     * 返回插件xml
     *
     * @return String
     */
    String getPluginXml();

    /**
     * 解析插件定义。
     *
     * @param pluginDefineJson
     * @return IBpmPluginDefine
     */
    void parse(String pluginDefineJson);

    /**
     * 返回JSON
     *
     * @return String
     */
    String getJson();

    /**
     * 插件类型。
     *
     * @return String
     */
    String getType();

}
