package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 任务催办设置
 * 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-03-22 11:46:22
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmTaskReminderTbl extends AbstractPo<String> {
    protected String id;        /*主键*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmTaskReminderTbl.name")
    protected String name;        /*名称*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmTaskReminderTbl.procDefId")
    protected String procDefId;        /*流程定义ID*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmTaskReminderTbl.nodeId")
    protected String nodeId;        /*当前节点ID*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmTaskReminderTbl.relNodeId")
    protected String relNodeId;        /*相对节点*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmTaskReminderTbl.relNodeEvent")
    protected String relNodeEvent;        /*相对处理事件*/
    protected String relTimeType;        /*相对时间类型*/
    protected String cronExpression;        /*条件表达式*/
    protected String callScript;        /*调用指定方法*/
    protected Integer startTime;        /*开始发送时间*/
    protected Integer interval;        /*发送时间间隔*/
    protected Short sendTimes;        /*发送次数*/
    protected Integer dueTime;        /*到期时间*/
    protected String dueAction;        /*到期执行动作*/
    protected String msgTypeHtml;        /*富文本的信息类型*/
    protected String html;        /*富文本内容*/
    protected String msgTypePt;        /*普通文本的信息类型*/
    protected String plainText;        /*普通文本内容*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 返回 名称
     *
     * @return
     */
    public String getName() {
        return this.name;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    /**
     * 返回 流程定义ID
     *
     * @return
     */
    public String getProcDefId() {
        return this.procDefId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    /**
     * 返回 当前节点ID
     *
     * @return
     */
    public String getNodeId() {
        return this.nodeId;
    }

    public void setRelNodeId(String relNodeId) {
        this.relNodeId = relNodeId;
    }

    /**
     * 返回 相对节点
     *
     * @return
     */
    public String getRelNodeId() {
        return this.relNodeId;
    }

    public void setRelNodeEvent(String relNodeEvent) {
        this.relNodeEvent = relNodeEvent;
    }

    /**
     * 返回 相对处理事件
     *
     * @return
     */
    public String getRelNodeEvent() {
        return this.relNodeEvent;
    }

    public void setRelTimeType(String relTimeType) {
        this.relTimeType = relTimeType;
    }

    /**
     * 返回 相对时间类型
     *
     * @return
     */
    public String getRelTimeType() {
        return this.relTimeType;
    }

    public void setCronExpression(String cronExpression) {
        this.cronExpression = cronExpression;
    }

    /**
     * 返回 条件表达式
     *
     * @return
     */
    public String getCronExpression() {
        return this.cronExpression;
    }

    public void setCallScript(String callScript) {
        this.callScript = callScript;
    }

    /**
     * 返回 调用指定方法
     *
     * @return
     */
    public String getCallScript() {
        return this.callScript;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    /**
     * 返回 开始发送时间
     *
     * @return
     */
    public Integer getStartTime() {
        return this.startTime;
    }

    public void setInterval(Integer interval) {
        this.interval = interval;
    }

    /**
     * 返回 发送时间间隔
     *
     * @return
     */
    public Integer getInterval() {
        return this.interval;
    }

    public void setSendTimes(Short sendTimes) {
        this.sendTimes = sendTimes;
    }

    /**
     * 返回 发送次数
     *
     * @return
     */
    public Short getSendTimes() {
        return this.sendTimes;
    }

    public void setDueTime(Integer dueTime) {
        this.dueTime = dueTime;
    }

    /**
     * 返回 到期时间
     *
     * @return
     */
    public Integer getDueTime() {
        return this.dueTime;
    }

    public void setDueAction(String dueAction) {
        this.dueAction = dueAction;
    }

    /**
     * 返回 到期执行动作
     *
     * @return
     */
    public String getDueAction() {
        return this.dueAction;
    }

    public void setMsgTypeHtml(String msgTypeHtml) {
        this.msgTypeHtml = msgTypeHtml;
    }

    /**
     * 返回 富文本的信息类型
     *
     * @return
     */
    public String getMsgTypeHtml() {
        return this.msgTypeHtml;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    /**
     * 返回 富文本内容
     *
     * @return
     */
    public String getHtml() {
        return this.html;
    }

    public void setMsgTypePt(String msgTypePt) {
        this.msgTypePt = msgTypePt;
    }

    /**
     * 返回 普通文本的信息类型
     *
     * @return
     */
    public String getMsgTypePt() {
        return this.msgTypePt;
    }

    public void setPlainText(String plainText) {
        this.plainText = plainText;
    }

    /**
     * 返回 普通文本内容
     *
     * @return
     */
    public String getPlainText() {
        return this.plainText;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("name", this.name)
                .append("procDefId", this.procDefId)
                .append("nodeId", this.nodeId)
                .append("relNodeId", this.relNodeId)
                .append("relNodeEvent", this.relNodeEvent)
                .append("relTimeType", this.relTimeType)
                .append("cronExpression", this.cronExpression)
                .append("callScript", this.callScript)
                .append("startTime", this.startTime)
                .append("interval", this.interval)
                .append("sendTimes", this.sendTimes)
                .append("dueTime", this.dueTime)
                .append("dueAction", this.dueAction)
                .append("msgTypeHtml", this.msgTypeHtml)
                .append("html", this.html)
                .append("msgTypePt", this.msgTypePt)
                .append("plainText", this.plainText)
                .toString();
    }
}
