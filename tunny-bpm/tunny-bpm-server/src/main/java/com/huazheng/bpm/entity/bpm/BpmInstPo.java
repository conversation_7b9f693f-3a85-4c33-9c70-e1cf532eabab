package com.huazheng.bpm.entity.bpm;


import com.huazheng.bpm.model.base.AuthorizeRightVo;
import com.huazheng.bpm.util.string.StringUtil;

/**
 * 流程实例 实体对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：luodx
 * 邮箱地址：<EMAIL>
 * 创建时间：2016-12-13 09:25:42
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmInstPo extends BpmInstTbl {
    private String creator;/*创建人姓名*/
    private String updator;/*修改人姓名*/
    private String forbidden;/*是否禁止*/
    protected AuthorizeRightVo authorizeRight;  /*流程分管授权权限对象*/

    /**
     * 可撤销任务用
     */
    private java.util.Date taskEndTime;/*流程任务结束时间*/
    private String taskId;/*流程任务ID*/
    private String myNode;/*我审批的流程任务节点名称*/
    private String curNode;/*当前审批的流程任务节点名称*/

    public AuthorizeRightVo getAuthorizeRight() {
        return authorizeRight;
    }

    public void setAuthorizeRight(AuthorizeRightVo authorizeRight) {
        this.authorizeRight = authorizeRight;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getUpdator() {
        return updator;
    }

    public void setUpdator(String updator) {
        this.updator = updator;
    }

    public String getForbidden() {
        if (StringUtil.isNotEmpty(forbidden)) {
            return forbidden;
        }
        switch (isForbidden) {
            case 0:
                return "未禁止";
            case 1:
                return "禁止";
            default:
                break;
        }
        return "未禁止";
    }

    public void setForbidden(String forbidden) {
        this.forbidden = forbidden;
    }

    public java.util.Date getTaskEndTime() {
        return taskEndTime;
    }

    public void setTaskEndTime(java.util.Date taskEndTime) {
        this.taskEndTime = taskEndTime;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getMyNode() {
        return myNode;
    }

    public void setMyNode(String myNode) {
        this.myNode = myNode;
    }

    public String getCurNode() {
        return curNode;
    }

    public void setCurNode(String curNode) {
        this.curNode = curNode;
    }
}
