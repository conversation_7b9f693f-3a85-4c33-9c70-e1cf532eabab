package com.huazheng.bpm.entity.party;

import com.huazheng.bpm.entity.org.PartyEntityTbl;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 岗位 实体对象。
 *
 * <pre>
 * 构建组：ibps-org-biz
 * 作者：huangchunyan
 * 邮箱：<EMAIL>
 * 日期：2016-06-20 09:07:07
 * 版权：广州流辰信息技术有限公司
 * </pre>
 */
@SuppressWarnings("serial")
public class PartyPositionTbl extends PartyEntityTbl {
    protected String id; /*ID*/
    @NotBlank(message = "{com.huazheng.bpm.entity.party.PartyPositionTbl.levelID}", groups = {ValidGroup.SavePosition.class})
    protected String levelID; /*岗位等级ID。从参与者等级中获得。*/
    protected String type = "no"; /*岗位业务类型。从枚举获得，一个岗位关联一个业务类型。*/
    @NotBlank(message = "{com.huazheng.bpm.entity.party.PartyPositionTbl.desc}", groups = {ValidGroup.SavePosition.class})
    protected String desc; /*岗位说明*/
    @NotBlank(message = "{com.huazheng.bpm.entity.party.PartyPositionTbl.posAlias}", groups = {ValidGroup.SavePosition.class})
    protected String posAlias; /*岗位别名*/
    protected String orgID; /*关联组织。保存组织ID。*/
    protected String relRoles; /*关联角色。保存角色ID的串，多个之间逗号分隔。*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 ID
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public String getPosAlias() {
        return posAlias;
    }

    public void setPosAlias(String posAlias) {
        this.posAlias = posAlias;
    }

    public void setLevelID(String levelID) {
        this.levelID = levelID;
    }

    /**
     * 返回 岗位等级数值。从参与者等级中获得。
     *
     * @return
     */
    public String getLevelID() {
        return this.levelID;
    }

    public void setType(String type) {
        this.type = type;
    }

    /**
     * 返回 岗位业务类型。从枚举获得，一个岗位关联一个业务类型。
     *
     * @return
     */
    public String getType() {
        return this.type;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     * 返回 岗位说明
     *
     * @return
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 返回 关联组织。保存组织ID。
     *
     * @return
     */
    public String getOrgID() {
        return orgID;
    }

    public void setOrgID(String orgID) {
        this.orgID = orgID;
    }

    public void setRelRoles(String relRoles) {
        this.relRoles = relRoles;
    }

    /**
     * 返回 关联角色。保存角色ID的串，多个之间逗号分隔。
     *
     * @return
     */
    public String getRelRoles() {
        return this.relRoles;
    }
}
