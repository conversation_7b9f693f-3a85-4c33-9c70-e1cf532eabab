package com.huazheng.bpm.plugin.usercalc;

import com.huazheng.bpm.entity.constant.BpmConstants;
import com.huazheng.bpm.exception.UserCalcException;
import com.huazheng.bpm.model.base.IDataObject;
import com.huazheng.bpm.model.base.PartyEntity;
import com.huazheng.bpm.model.engine.IPartyEngine;
import com.huazheng.bpm.plugin.DataObjectHandler;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.json.JsonUtil;
import com.huazheng.bpm.util.session.BpmUserCalcPluginSession;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import javax.annotation.Resource;
import java.util.*;

public class UserCalcHelper {

    @Resource
    private IPartyEngine orgEngine;

    /**
     * 流程变量JSON解析       将参数和值进行匹配
     *
     * @param executorVar
     * @param pluginSession
     * @return userIds  or  groupIds
     */
    public List<String> getCalcsPKByExecutor(ExecutorVar executorVar, BpmUserCalcPluginSession pluginSession) {
        return getCalcsPKByExecutor(executorVar, pluginSession.getVariables());
    }

    /**
     * 流程变量JSON解析       将参数和值进行匹配
     *
     * @param executorVar
     * @param
     * @return userIds  or  groupIds
     */
    public List<String> getCalcsPKByExecutor(ExecutorVar executorVar, Map<String, Object> vars) {
        List<String> ids = new ArrayList<String>();  /// 需要返回的 id  List

        //预览模式     (所有参数都是ID)
//		if(AbstractUserCalcPlugin.isPreVrewModel.get() != null && AbstractUserCalcPlugin.isPreVrewModel.get()){
//			String Id = (String) vars.get(executorVar.getName());
//			ids.add(Id);
//			return ids;
//		}

        // 非预览模式
        String PK = ""; //从流程变量，或者bo中获取值 中间变量

        //非预览模式
        if (ExecutorVar.SOURCE_BO_VAR.equals(executorVar.getSource())) {
            String[] BOData = executorVar.getName().split("\\.");
            if (BOData.length != 2) {
                throw new UserCalcException("BO[" + executorVar.getValue() + "]数据 格式不合法");
            }
            DataObjectHandler dataObjectHandler = (DataObjectHandler) AppUtil.getBean("dataObjectHandler");
            String instanceId = (String) vars.get(BpmConstants.PROCESS_INST_ID);
            IDataObject databoject = dataObjectHandler.getByBoCodeFromContext(instanceId, BOData[0]);

            if (BeanUtils.isEmpty(databoject)) {
                throw new UserCalcException("业务数据为空！");
            }

            PK = (String) databoject.get(BOData[1]);
        }
        //流程变量
        else if (ExecutorVar.SOURCE_FLOW_VAR.equals(executorVar.getSource())) {
            PK = (String) vars.get(executorVar.getName());
        }
        //流程常量
        else if (ExecutorVar.SOURCE_CONT_VAR.equals(executorVar.getSource())) {
            PK = (String) vars.get(executorVar.getName());
        }

        if (BeanUtils.isEmpty(PK)) {
            return Collections.emptyList();
        }

        //判断PK是否json格式
        String[] PKs = null;
        if (JsonUtil.isJsonArray(PK)) {
            JSONArray arr = JSONArray.fromObject(PK);
            List<String> list = new ArrayList<String>();
            if (arr.size() > 0) {
                for (int i = 0; i < arr.size(); i++) {
                    JSONObject job = arr.getJSONObject(i);
                    list.add(job.getString("id"));
                }
            }
            PKs = new String[list.size()];
            list.toArray(PKs);

        } else {
            PKs = PK.split(",");
        }

        //如果是固定值，返回值
        if ("fixed".equals(executorVar.getExecutorType())) {
            ids.addAll(Arrays.asList(PKs));
            return ids;
        }

        //用户参数
        if (ExecutorVar.EXECUTOR_TYPE_USER.equals(executorVar.getExecutorType())) {
            String valType = executorVar.getValType();
            if (ExecutorVar.VALTYPE_USERID.equals(valType)) {
                ids.addAll(Arrays.asList(PKs));
            } //account
            else if (ExecutorVar.VALTYPE_ACCOUNT.equals(valType)) {
                for (String account : PKs) {
//					User u = DefaultPartyUserPo.fromJsonString2(
//							orgEngine.getPartyUserService().getByAccountJson(account));

//					if(u!=null) ids.add(u.getUserId());
                }
            }
        } else if (ExecutorVar.EXECUTOR_TYPE_ORG.equals(executorVar.getExecutorType())) {
            String valType = executorVar.getValType();
            //id的形式的数据
            if (ExecutorVar.VALTYPE_ORGID.equals(valType)) {
                ids.addAll(Arrays.asList(PKs));
            }
                //key 的形式的数据
            else if (ExecutorVar.VALTYPE_ORGKEY.equals(valType)) {
                for (String orgAlias : PKs) {
                    PartyEntity g = orgEngine.getPartyEntityService().getByAlias(orgAlias);
                    if (g != null) {
                        ids.add(g.getId());
                    }
                }
            }
        }

        return ids;
    }
}
