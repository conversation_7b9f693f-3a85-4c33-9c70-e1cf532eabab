package com.huazheng.bpm.domain.bpm;


import com.huazheng.bpm.dao.bpm.BpmExecSeqDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmExecSeqPo;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 流程任务执行顺序 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-01-09 11:32:01
 * </pre>
 */
@SuppressWarnings("serial")
@Service
@Scope("prototype")
public class BpmExecSeq extends AbstractDomain<String, BpmExecSeqPo> {

    @Resource
    private BpmExecSeqDao bpmExecSeqDao;

    @Override
    protected void init() {
        this.setDao(bpmExecSeqDao);
    }
}
