package com.huazheng.bpm.model.base;

/**
 * 定时器执行日志接口
 *
 * <pre>
 * 构建组：ibps-component-quartz
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2015-10-16-上午10:30:08
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface SchedulerLog {

    /**
     * 成功=1
     */
    int STATE_SUCCESS = 1;

    /**
     * 失败 = 0
     */
    int STATE_FAIL = 0;

    /**
     * 返回 任务名
     *
     * @return
     */
    String getJobName();

    void setJobName(String jobName);

    /**
     * 返回 触发器名称
     *
     * @return
     */
    String getGroup();

    void setGroup(String group);

    /**
     * 返回 触发器名称
     *
     * @return
     */
    String getTrigName();

    void setTrigName(String trigName);

    /**
     * 返回 开始时间
     *
     * @return
     */
    java.util.Date getStartTime();

    void setStartTime(java.util.Date startTime);

    /**
     * 返回 结束时间
     *
     * @return
     */
    java.util.Date getEndTime();

    void setEndTime(java.util.Date endTime);

    /**
     * 返回 内容
     *
     * @return
     */
    String getContent();

    void setContent(String content);

    /**
     * 返回 状态
     *
     * @return
     */
    String getState();

    void setState(String state);

    /**
     * 返回 运行时长
     *
     * @return
     */
    Long getRunTime();

    void setRunTime(Long runTime);

}
