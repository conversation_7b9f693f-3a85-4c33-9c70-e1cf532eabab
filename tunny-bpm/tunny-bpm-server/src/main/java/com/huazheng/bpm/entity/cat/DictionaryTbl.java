package com.huazheng.bpm.entity.cat;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;


/**
 * 数据字典  实体对象。
 *
 * <pre>
 * 构建组：bps-common-biz
 * 作者：huangcy
 * 邮箱：<EMAIL>
 * 日期：2015-11-19 14:17:31
 * 版权：广州流辰信息技术有限公司
 * </pre>
 */
@SuppressWarnings("serial")
public class DictionaryTbl extends AbstractPo<String> {

    protected String id; /*主键*/
    protected String typeId; /*类型ID*/
    protected String key; /*字典值代码,在同一个字典中值不能重复*/
    protected String name; /*字典值名称*/
    protected String parentId; /*父ID*/
    protected Integer sn; /* 序号 */

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    /**
     * 返回 类型ID
     *
     * @return
     */
    public String getTypeId() {
        return this.typeId;
    }

    public void setKey(String key) {
        this.key = key;
    }

    /**
     * 返回 字典值代码,在同一个字典中值不能重复
     *
     * @return
     */
    public String getKey() {
        return this.key;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 返回 字典值名称
     *
     * @return
     */
    public String getName() {
        return this.name;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    /**
     * 返回 父ID
     *
     * @return
     */
    public String getParentId() {
        return this.parentId;
    }

    /**
     * 返回 排序
     *
     * @return
     */
    public Integer getSn() {
        return sn;
    }

    public void setSn(Integer sn) {
        this.sn = sn;
    }


    /**
     * @see java.lang.Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("typeId", this.typeId)
                .append("key", this.key)
                .append("name", this.name)
                .append("parentId", this.parentId)
                .append("sn", this.sn)
                .toString();
    }
}
