package com.huazheng.bpm.model.base;

/**
 * <pre>
 *
 * 构建组：ibps-base-service
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年4月20日-下午5:06:54
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class DefaultInvokeCmd implements InvokeCmd {
    private String address; /* 调用地址 */
    private String username; /* 服务的用户名 */
    private String password; /* 服务的用户密码 */
    private String operatorName; /* 操作方法名 */
    private String operatorNamespace; /* 操作的名称空间 */
    private String jsonParam; /* 操作的调用参数 */
    private String type = "webservice"; /* 调用的服务类型 */
    private Boolean soapAction = false; /* 构建xml时xmlns的配置方式 */

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getOperatorNamespace() {
        return operatorNamespace;
    }

    public void setOperatorNamespace(String operatorNamespace) {
        this.operatorNamespace = operatorNamespace;
    }

    public String getJsonParam() {
        return jsonParam;
    }

    public void setJsonParam(String jsonParam) {
        this.jsonParam = jsonParam;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean getSoapAction() {
        return soapAction;
    }

    public void setSoapAction(Boolean soapAction) {
        this.soapAction = soapAction;
    }
}
