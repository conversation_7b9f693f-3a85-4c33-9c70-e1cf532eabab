package com.huazheng.bpm.entity.bpm;


import com.huazheng.bpm.util.core.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 流程执行记录 实体对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-01-04 14:29:57
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmExecPo extends BpmExecTbl implements Cloneable {

    /**
     * 执行状态：初始化
     */
    public final static Short EXEC_STATUS_INIT = 0;

    /**
     * 执行状态：完成
     */
    public final static Short EXEC_STATUS_COMPLETE = 1;

    /**
     * 执行状态：驳回
     */
    public final static Short EXEC_STATUS_REJECT = 2;

    /**
     * 执行状态：被驳回，
     * 同步任务/多实例任务其中一个分支驳回，其他分支如被结束则为该状态
     */
    public final static Short EXEC_STATUS_REJECT_BY = 3;

    /**
     * 执行状态：取消，
     * 会签任务已达规则未完成任务都设置取消
     */
    public final static Short EXEC_STATUS_CANCEL = 4;

    /**
     * 执行状态：撤销，
     * 任务被上一执行人撤销
     */
    public final static Short EXEC_STATUS_REVOKE = 5;

    /**
     * 按照流程图执行
     */
    public final static String HAND_MODE_NORMAL = "normal";

    /**
     * 指定跳转节点
     */
    public final static String HAND_MODE_DIRECT = "direct";

    /**
     * 指定跳转节点
     */
    public final static String HAND_MODE_NORMAL_REJECT_START = "handModeNormalRejectStart";

    /**
     * 按照流程图执行的驳回时目标节点ID
     */
    public final static String HAND_MODE_NORMAL_TARGET_NODE_ID = "targetNodeId";

    /**
     * 是否回收目标节点之后的所有节点的任务
     */
    public final static String HAND_MODE_NORMAL_IS_CANCLE_NODE_BEHAND_TASK = "isCancleNodeBehandTask";

    /**
     * 源执行信息列表
     */
    private List<BpmExecPo> srcExecList = new ArrayList<BpmExecPo>();

    /**
     * 源流程执行信息列表
     */
    private List<BpmExecPo> srcProcExecList = new ArrayList<BpmExecPo>();

    /**
     * 执行顺序列表
     */
    private List<BpmExecSeqPo> execSeqList = new ArrayList<BpmExecSeqPo>();

    /**
     * 添加父执行信息
     *
     * @param bpmExecPo
     */
    public void addSrcExec(BpmExecPo bpmExecPo) {
        if (BeanUtils.isNotEmpty(bpmExecPo)) {
            srcExecList.add(bpmExecPo);
        }
    }

    /**
     * 添加父执行信息-流程顺序
     *
     * @param bpmExecPo
     */
    public void addSrcProcExec(BpmExecPo bpmExecPo) {
        if (BeanUtils.isNotEmpty(bpmExecPo)) {
            srcProcExecList.add(bpmExecPo);
        }
    }

    /**
     * 添加执行顺序
     *
     * @param bpmExecPo
     */
    public void addExecSeq(BpmExecSeqPo bpmExecSeqPo) {
        if (BeanUtils.isNotEmpty(bpmExecSeqPo)) {
            execSeqList.add(bpmExecSeqPo);
        }
    }

    /**
     * 添加父执行信息
     *
     * @param bpmExecPo
     */
    public void addAllSrcExec(List<BpmExecPo> bpmExecPos) {
        if (BeanUtils.isNotEmpty(bpmExecPos)) {
            srcExecList.addAll(bpmExecPos);
        }
    }

    /**
     * 添加父执行信息-流程顺序
     *
     * @param bpmExecPo
     */
    public void addAllSrcProcExec(List<BpmExecPo> bpmExecPos) {
        if (BeanUtils.isNotEmpty(bpmExecPos)) {
            srcProcExecList.addAll(bpmExecPos);
        }
    }

    public List<BpmExecPo> getSrcExecList() {
        return srcExecList;
    }

    public void setSrcExecList(List<BpmExecPo> srcExecList) {
        this.srcExecList = srcExecList;
    }

    public List<BpmExecPo> getSrcProcExecList() {
        return srcProcExecList;
    }

    public void setSrcProcExecList(List<BpmExecPo> srcProcExecList) {
        this.srcProcExecList = srcProcExecList;
    }

    public List<BpmExecSeqPo> getExecSeqList() {
        return execSeqList;
    }

    public void setExecSeqList(List<BpmExecSeqPo> execSeqList) {
        this.execSeqList = execSeqList;
    }

    /* (non-Javadoc)
     * @see com.lc.ibps.bpmn.persistence.entity.BpmExecTbl#getTargetAction()
     */
    @Override
    public String getTargetAction() {
        if (null == super.getTargetAction()) {
            return HAND_MODE_NORMAL;
        }
        return super.getTargetAction();
    }

    /* (non-Javadoc)
     * @see java.lang.Object#clone()
     */
    @Override
    public BpmExecPo clone() throws CloneNotSupportedException {
        return (BpmExecPo) super.clone();
    }
}
