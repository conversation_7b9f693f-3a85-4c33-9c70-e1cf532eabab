package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmBusRelDao;
import com.huazheng.bpm.dao.bpm.BpmBusRelQueryDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmBusRelPo;
import com.huazheng.bpm.util.string.StringUtil;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 业务数据关联
 * 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：luodx
 * 邮箱地址：<EMAIL>
 * 创建时间：2016-12-12 15:56:36
 * </pre>
 */
@SuppressWarnings("serial")
@Transactional
@Scope("prototype")
@Service
public class BpmBusRel extends AbstractDomain<String, BpmBusRelPo> {

    @Resource
    private BpmBusRelDao bpmBusRelDao;
    @Resource
    private BpmBusRelQueryDao bpmBusRelQueryDao;

    protected void init() {
        this.setDao(bpmBusRelDao);
    }

    public void create() {
        BpmBusRelPo entity = getData();
        if (StringUtil.isEmpty(entity.getFormIdentify())) entity.setFormIdentify(BpmBusRelPo.TABLE_UNCREATED);
//		if(bpmBusRelQueryDao.isSupportPartition()) bpmBusLinkQueryDao.findPartitionIfNotExit(entity.getFormIdentify());
        super.create();
    }

    public void delByBusKey(boolean isNumber) {
        String businessKey = getData().getBusinesskey();
        String formIdentity = getData().getFormIdentify();
        bpmBusRelDao.delByBusKey(businessKey, formIdentity);
    }

}
