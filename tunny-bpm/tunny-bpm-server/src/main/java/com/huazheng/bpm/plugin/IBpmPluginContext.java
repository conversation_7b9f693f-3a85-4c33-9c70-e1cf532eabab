package com.huazheng.bpm.plugin;

import com.huazheng.bpm.entity.constant.EventType;

import java.util.List;

/**
 * 插件上下文
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月8日-下午3:40:55
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface IBpmPluginContext extends IPluginContext {

    /**
     * 返回该插件关联的事件集合
     *
     * @return List<EventType>
     * @throws
     * @since 1.0.0
     */
    List<EventType> getEventTypeList();

}
