package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.Date;

/**
 * 流程任务变更 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-04-11 19:45:10
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmTaskChangeTbl extends AbstractPo<String> {
    protected String id;        /*ID*/
    protected String taskId;        /*任务ID*/
    protected String taskSubject;        /*事项标题*/
    protected String taskName;        /*任务名称*/
    protected String procInstId;        /*流程实例ID*/
    protected String nodeId;        /*任务节点ID*/
    protected String changeType;        /*更改类型*/
    protected String status;        /*状态*/
    protected String ownerId;        /*所属人ID*/
    protected String executorId;        /*执行人ID*/
    protected String comment;        /*变更描述*/
    protected Date createTime;        /*任务创建时间*/
    protected Date completeTime;        /*任务完成时间*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 ID
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    /**
     * 返回 任务ID
     *
     * @return
     */
    public String getTaskId() {
        return this.taskId;
    }

    public void setTaskSubject(String taskSubject) {
        this.taskSubject = taskSubject;
    }

    /**
     * 返回 事项标题
     *
     * @return
     */
    public String getTaskSubject() {
        return this.taskSubject;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    /**
     * 返回 任务名称
     *
     * @return
     */
    public String getTaskName() {
        return this.taskName;
    }

    public void setProcInstId(String procInstId) {
        this.procInstId = procInstId;
    }

    /**
     * 返回 流程实例ID
     *
     * @return
     */
    public String getProcInstId() {
        return this.procInstId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    /**
     * 返回 任务节点ID
     *
     * @return
     */
    public String getNodeId() {
        return this.nodeId;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }

    /**
     * 返回 更改类型
     *
     * @return
     */
    public String getChangeType() {
        return this.changeType;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 返回 状态
     *
     * @return
     */
    public String getStatus() {
        return this.status;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * 返回 所属人ID
     *
     * @return
     */
    public String getOwnerId() {
        return this.ownerId;
    }

    public void setExecutorId(String executorId) {
        this.executorId = executorId;
    }

    /**
     * 返回 执行人ID
     *
     * @return
     */
    public String getExecutorId() {
        return this.executorId;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    /**
     * 返回 变更描述
     *
     * @return
     */
    public String getComment() {
        return this.comment;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 返回 任务创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    /**
     * 返回 任务完成时间
     *
     * @return
     */
    public Date getCompleteTime() {
        return this.completeTime;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("taskId", this.taskId)
                .append("taskSubject", this.taskSubject)
                .append("taskName", this.taskName)
                .append("procInstId", this.procInstId)
                .append("nodeId", this.nodeId)
                .append("changeType", this.changeType)
                .append("status", this.status)
                .append("ownerId", this.ownerId)
                .append("executorId", this.executorId)
                .append("comment", this.comment)
                .append("createTime", this.createTime)
                .append("completeTime", this.completeTime)
                .toString();
    }
}
