package com.huazheng.bpm.entity.rights;

/**
 * 权限上下文。
 *
 * <pre>
 * 构建组：ibps-common-biz
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年1月19日-下午2:50:10
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class RightsContext {
    //关联用户ID
    private String userId;
    //权限类型
    private String entityType;
    //实体表
    private String entityTable;
    //实体IDkey 如果不写默认是"ID_"
    private String entityIdKey = "ID_";

    public RightsContext() {
        super();
    }

    public RightsContext(String entityType, String entityTable, String entityIdKey) {
        super();
        this.entityType = entityType;
        this.entityTable = entityTable;
        if (null == entityIdKey) {
            entityIdKey = "ID_";
        }
        this.entityIdKey = entityIdKey;
    }

    public RightsContext(String userId, String entityType, String entityTable, String entityIdKey) {
        super();
        this.userId = userId;
        this.entityType = entityType;
        this.entityTable = entityTable;
        if (null == entityIdKey) {
            entityIdKey = "ID_";
        }
        this.entityIdKey = entityIdKey;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getEntityType() {
        return entityType;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    public String getEntityIdKey() {
        return entityIdKey;
    }

    public void setEntityIdKey(String entityIdKey) {
        this.entityIdKey = entityIdKey;
    }

    public String getEntityTable() {
        return entityTable;
    }

    public void setEntityTable(String entityTable) {
        this.entityTable = entityTable;
    }

}
