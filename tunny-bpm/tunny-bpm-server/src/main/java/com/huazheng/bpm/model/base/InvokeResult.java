package com.huazheng.bpm.model.base;

import java.util.List;

/**
 * <pre>
 *
 * 构建组：ibps-base-service
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年4月20日-下午5:06:07
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface InvokeResult {
    Object getObject();

    List<Object> getList();

    String getJson();

    Boolean isList();

    Boolean isVoid();

    Boolean isFault();

    Exception getException();
}
