package com.huazheng.bpm.entity.msg;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;


/**
 * 消息模版  实体对象。
 *
 * <pre>
 * 构建组：ibps-common-biz
 * 作者：hcy
 * 邮箱：<EMAIL>
 * 日期：2015-12-16 09:29:19
 * 版权：广州流辰信息技术有限公司
 * </pre>
 */
@SuppressWarnings("serial")
public class MessageTemplateTbl extends AbstractPo<String> {
    protected String id; /*主键*/
    protected String name; /*模版名称*/
    protected String key; /*模版业务键*/
    protected String typeKey; /*模板分类。可以按任务操作类型分类，也可以按其它方式分类。*/
    protected String subTypeKey; /*模板二级分类。*/
    protected Boolean isDefault = false; /*是否默认模板*/
    protected String subject; /*标题*/
    protected String plain; /*纯文本*/
    protected String html; /*模版体HTML*/
    protected String wechat; /*微信模版体HTML*/
    protected String createBy; /*创建人ID*/
    protected java.util.Date createTime; /*创建时间*/
    protected String createOrgId; /*创建者所属组织ID*/
    protected String updateBy; /*更新人ID*/
    protected java.util.Date updateTime; /*更新时间*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 返回 模版名称
     *
     * @return
     */
    public String getName() {
        return this.name;
    }

    public void setKey(String key) {
        this.key = key;
    }

    /**
     * 返回 模版业务键
     *
     * @return
     */
    public String getKey() {
        return this.key;
    }

    public void setTypeKey(String typeKey) {
        this.typeKey = typeKey;
    }

    /**
     * 返回 模板分类。可以按任务操作类型分类，也可以按其它方式分类。
     *
     * @return
     */
    public String getTypeKey() {
        return this.typeKey;
    }

    public void setSubTypeKey(String subTypeKey) {
        this.subTypeKey = subTypeKey;
    }

    /**
     * 返回 模板二级分类。
     *
     * @return
     */
    public String getSubTypeKey() {
        return this.subTypeKey;
    }

    /**
     * 返回 是否默认模板。对于同一组（模板分类+二级分类）下的多个模板其中默认的一个。
     *
     * @return
     */
    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    /**
     * 返回 标题
     *
     * @return
     */
    public String getSubject() {
        return this.subject;
    }

    public void setPlain(String plain) {
        this.plain = plain;
    }

    /**
     * 返回 纯文本
     *
     * @return
     */
    public String getPlain() {
        return this.plain;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    /**
     * 返回 模版体HTML
     *
     * @return
     */
    public String getHtml() {
        return this.html;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     * 返回 微信模版体HTML
     *
     * @return
     */
    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    /**
     * 返回 创建人ID
     *
     * @return
     */
    public String getCreateBy() {
        return this.createBy;
    }

    public void setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 返回 创建时间
     *
     * @return
     */
    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateOrgId(String createOrgId) {
        this.createOrgId = createOrgId;
    }

    /**
     * 返回 创建者所属组织ID
     *
     * @return
     */
    public String getCreateOrgId() {
        return this.createOrgId;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 返回 更新人ID
     *
     * @return
     */
    public String getUpdateBy() {
        return this.updateBy;
    }

    public void setUpdateTime(java.util.Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 返回 更新时间
     *
     * @return
     */
    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("name", this.name)
                .append("key", this.key)
                .append("typeKey", this.typeKey)
                .append("subTypeKey", this.subTypeKey)
                .append("isDefault", this.isDefault)
                .append("subject", this.subject)
                .append("plain", this.plain)
                .append("html", this.html)
                .append("createBy", this.createBy)
                .append("createTime", this.createTime)
                .append("createOrgId", this.createOrgId)
                .append("updateBy", this.updateBy)
                .append("updateTime", this.updateTime)
                .toString();
    }
}
