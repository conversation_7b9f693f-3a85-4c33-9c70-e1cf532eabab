package com.huazheng.bpm.plugin.factory;


import com.huazheng.bpm.entity.constant.AopType;
import com.huazheng.bpm.entity.constant.EventType;
import com.huazheng.bpm.plugin.*;
import com.huazheng.bpm.util.core.AppUtil;
import org.springframework.stereotype.Service;

/**
 * TODO（用一句话描述这个类做什么用）。
 * <pre>
 * 构建组：ibps-bpmn-plugin-base
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午8:56:18
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@Service("bpmPluginFactory")
public class DefaultBpmPluginFactory implements IBpmPluginFactory {

    @Override
    public IBpmExecutionPlugin buildExecutionPlugin(
            IBpmPluginContext bpmPluginContext, EventType eventType) {
        if (bpmPluginContext.getEventTypeList().contains(eventType)) {
            try {
                IBpmExecutionPlugin bpmExecutionPlugin = (IBpmExecutionPlugin) AppUtil.getBean(bpmPluginContext.getPluginClass());
                return bpmExecutionPlugin;
            } catch (Exception e) {

            }
        }
        return null;
    }

    @Override
    public IBpmTaskPlugin buildTaskPlugin(
            IBpmPluginContext bpmPluginContext, EventType eventType) {
        if (bpmPluginContext.getEventTypeList().contains(eventType)) {
            try {
                IBpmTaskPlugin bpmTaskPlugin = (IBpmTaskPlugin) AppUtil.getBean(bpmPluginContext.getPluginClass());
                return bpmTaskPlugin;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    @Override
    public ProcessInstAopPlugin buildProcessInstAopPlugin(
            IProcInstAopPluginContext processInstAopPluginContext,
            AopType aopType) {
        if (processInstAopPluginContext.getAopType().equals(aopType)) {
            try {
                ProcessInstAopPlugin processInstAopPlugin = (ProcessInstAopPlugin) AppUtil.getBean(processInstAopPluginContext.getPluginClass());
                return processInstAopPlugin;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    @Override
    public ITaskAopPlugin buildTaskAopPlugin(
            ITaskAopPluginContext taskAopPluginContext, AopType aopType) {
        if (taskAopPluginContext.getAopType().equals(aopType)) {
            try {
                ITaskAopPlugin taskAopPlugin = (ITaskAopPlugin) AppUtil.getBean(taskAopPluginContext.getPluginClass());
                return taskAopPlugin;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
