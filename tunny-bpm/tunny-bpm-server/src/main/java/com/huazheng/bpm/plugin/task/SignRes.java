/**
 * 描述：TODO
 * 包名：com.lc.bpmn.api.plugin.core.execution.sign
 * 文件名：SignResult.java
 * <EMAIL>
 * 日期2015-4-11-上午9:45:53
 * 版权：广州流辰信息技术有限公司版权所有
 */
package com.huazheng.bpm.plugin.task;


import com.huazheng.bpm.entity.constant.NodeStatus;

/**
 * 会签结果对象。
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：simon cai
 * 邮箱:<EMAIL>
 * 日期：2016-12-30-上午9:45:53
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class SignRes {
    /**
     * 判断该会签handler是否完成
     */
    private boolean isComplete = false;

    /**
     * 要更新为的节点状态
     */
    private NodeStatus nodeStatus;

    /**
     * 批次号
     */
    private String batch;

    public SignRes() {

    }

    public SignRes(boolean isComplete, NodeStatus nodeStatus) {
        this.isComplete = isComplete;
        this.nodeStatus = nodeStatus;
    }

    public String getBatch() {
        return batch;
    }

    public void setBatch(String batch) {
        this.batch = batch;
    }

    public boolean isComplete() {
        return isComplete;
    }

    public void setComplete(boolean isComplete) {
        this.isComplete = isComplete;
    }

    public NodeStatus getNodeStatus() {
        return nodeStatus;
    }

    public void setNodeStatus(NodeStatus nodeStatus) {
        this.nodeStatus = nodeStatus;
    }

}
