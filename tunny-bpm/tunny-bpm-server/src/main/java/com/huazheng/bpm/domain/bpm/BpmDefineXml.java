package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmDefineXmlDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmDefineXmlPo;
import com.huazheng.bpm.util.core.AppUtil;
import org.springframework.context.annotation.Scope;
import org.springframework.transaction.annotation.Transactional;

/**
 * 流程定义大数据
 * 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2016-12-09 16:33:56
 * </pre>
 */
@SuppressWarnings("serial")
@Transactional
@Scope("prototype")
public class BpmDefineXml extends AbstractDomain<String, BpmDefineXmlPo> {

    private BpmDefineXmlDao bpmDefineXmlDao = null;

    protected void init() {
        bpmDefineXmlDao = AppUtil.getBean(BpmDefineXmlDao.class);
        this.setDao(bpmDefineXmlDao);
    }
}
