/**
 * 描述：TODO
 * 包名：com.lc.bpmn.api.plugin.core.session
 * 文件名：BpmExecutionPluginSession.java
 * <EMAIL>
 * 日期2015-2-23-下午8:56:43
 * 版权：广州流辰信息技术有限公司版权所有
 */
package com.huazheng.bpm.plugin;

import com.huazheng.bpm.entity.constant.EventType;
import com.huazheng.bpm.model.delegate.BpmDelegateExecution;
import com.huazheng.bpm.util.session.BpmPluginSession;

/**
 * <pre>
 * 描述：支持执行类插件执行的会话数据
 * 构建组：ibps-bpmn-api
 * 作者：Winston Yan
 * 邮箱：<EMAIL>
 * 日期：2015-2-23-下午8:56:43
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface BpmExecutionPluginSession extends BpmPluginSession {
    BpmDelegateExecution getBpmDelegateExecution();

    EventType getEventType();
}
