package com.huazheng.bpm.plugin.factory;


import com.huazheng.bpm.entity.constant.AopType;
import com.huazheng.bpm.entity.constant.EventType;
import com.huazheng.bpm.plugin.*;

/**
 * 插件工厂
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午5:36:50
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface IBpmPluginFactory {
    /**
     * 返回流程的插件实例
     *
     * @param pluginDefs
     * @param eventType
     * @return List<AbstractBpmPlugin>
     * @throws
     * @since 1.0.0
     */
    IBpmExecutionPlugin buildExecutionPlugin(IBpmPluginContext bpmPluginContext, EventType eventType);

    /**
     * 获取节点插件实例
     *
     * @param pluginDefs
     * @param eventType
     * @return List<BpmPlugin>
     * @throws
     * @since 1.0.0
     */
    IBpmTaskPlugin buildTaskPlugin(IBpmPluginContext bpmPluginContext, EventType eventType);

    ProcessInstAopPlugin buildProcessInstAopPlugin(IProcInstAopPluginContext processInstAopPluginContext, AopType aopType);

    ITaskAopPlugin buildTaskAopPlugin(ITaskAopPluginContext taskAopPluginContext, AopType aopType);
}
