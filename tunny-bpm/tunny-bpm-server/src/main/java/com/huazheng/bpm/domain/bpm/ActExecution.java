package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.ActExecutionDao;
import com.huazheng.bpm.dao.bpm.ActExecutionQueryDao;
import com.huazheng.bpm.dao.bpm.BpmInstQueryDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.ActExecutionPo;
import com.huazheng.bpm.entity.bpm.BpmInstPo;
import com.huazheng.bpm.repository.BpmExecRepository;
import com.huazheng.bpm.util.core.BeanUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@SuppressWarnings("serial")
@Transactional
@Scope("prototype")
@Service
public class ActExecution extends AbstractDomain<String, ActExecutionPo> {

    @Resource
    private ActExecutionQueryDao actExecutionQueryDao;
    @Resource
    private ActExecutionDao actExecutionDao;
    @Resource
    private BpmInstQueryDao bpmInstQueryDao;
    @Resource
    private BpmExecRepository bpmExecRepository;

    @Override
    protected void init() {
        this.setDao(actExecutionDao);
    }

    public void delByInstList(List<String> instList) {
        if (BeanUtils.isEmpty(instList)) {
            return;
        }

        //删除运行的数据
        //实例数据
        actExecutionDao.delVarsByInstList(instList);
        //删除候选人
        actExecutionDao.delCandidateByInstList(instList);
        //删除任务
        actExecutionDao.delTaskByByInstList(instList);
        //删除事件订阅
        actExecutionDao.delEventSubByInstList(instList);
        //删除实例
        List<String> allActList = this.getExecutionCascade(instList);
        this.delExecutionByInstList(allActList);

        //历史数据删除
        //历史变量清除
        actExecutionDao.delHiVarByInstList(instList);
        //历史候选人清除
        actExecutionDao.delHiCandidateByInstList(instList);
        //历史任务清除
        actExecutionDao.delHiTaskByInstList(instList);
        //历史实例清除
        actExecutionDao.delHiActInstByInstList(instList);
        //历史流程实例清除
        actExecutionDao.delHiProcinstByInstList(instList);
    }

    /**
     * 根据act执行数据删除数据
     *
     * @param allActList
     */
    private void delExecutionByInstList(List<String> allActList) {
        if (BeanUtils.isNotEmpty(allActList)) {
            for (String id : allActList) {
                actExecutionDao.delete(id);
            }
        }
    }

    /**
     * 获取act执行数据
     *
     * @param instList
     */
    private List<String> getExecutionCascade(List<String> instList) {
        if (BeanUtils.isEmpty(instList)) {
            return null;
        }

        List<String> rsList = actExecutionQueryDao.getByInstList(instList);
        if (BeanUtils.isNotEmpty(rsList)) {
            rsList.removeAll(instList);
        }

        List<String> rsListTmp = this.getExecutionCascade(rsList);
        if (BeanUtils.isNotEmpty(rsListTmp)) {
            instList.removeAll(rsListTmp);
            instList.addAll(rsListTmp);
        }

        Collections.sort(instList);
        Collections.reverse(instList);

        return instList;
    }

    /**
     * 根据实例id及扩展执行记录节点id删除activiti执行数据
     *
     * @param instId
     * @param targetNodeId
     * @param notIncludeExecuteIds
     */
    public void delByInstIdExecNodeId(String procInstId, String targetNodeId, String notIncludeExecuteIds) {
        BpmInstPo instPo = bpmInstQueryDao.get(procInstId);
        if (BeanUtils.isEmpty(instPo)) {
            return;
        }

        List<String> nodeIds = bpmExecRepository.findBehandNodeIds(procInstId, targetNodeId, true);
        if (BeanUtils.isEmpty(nodeIds)) {
            return;
        }

        actExecutionDao.delByInstIdExecNodeId(instPo.getBpmnInstId(), nodeIds, notIncludeExecuteIds);
    }

}
