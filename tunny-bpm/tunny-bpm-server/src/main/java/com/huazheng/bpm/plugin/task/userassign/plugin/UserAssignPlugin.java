package com.huazheng.bpm.plugin.task.userassign.plugin;


import com.huazheng.bpm.entity.constant.BpmConstants;
import com.huazheng.bpm.model.base.BpmIdentity;
import com.huazheng.bpm.model.delegate.BpmDelegateTask;
import com.huazheng.bpm.model.node.UserAssignRule;
import com.huazheng.bpm.plugin.BaseUserAssignPlugin;
import com.huazheng.bpm.plugin.IBpmTaskPluginDefine;
import com.huazheng.bpm.plugin.task.userassign.def.UserAssignPluginDefine;
import com.huazheng.bpm.service.IBpmDefineReader;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.session.BpmTaskPluginSession;
import com.huazheng.bpm.util.session.BpmUserCalcPluginSession;
import com.huazheng.bpm.util.string.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户分配插件，继承自{@linkplain com.lc.ibps.bpmn.plugin.task.userassign.plugin.UserAssignPlugin 基础用户插件}。
 * 调用节点配置插件计算用户并添加到当前任务执行人中。
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月14日-上午9:27:51
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@Service("userAssignPlugin")
public class UserAssignPlugin extends BaseUserAssignPlugin {
    @Resource
    private IBpmDefineReader bpmDefineReader;

    @Override
    public void executeExt(BpmTaskPluginSession pluginSession, IBpmTaskPluginDefine pluginDef) {
        BpmDelegateTask bpmDelegateTask = pluginSession.getBpmDelegateTask();
        String parentFlowKey = (String) bpmDelegateTask.getSupperVariable(BpmConstants.BPM_FLOW_KEY);

        UserAssignPluginDefine assignPluginDef = (UserAssignPluginDefine) pluginDef;
        if (StringUtil.isNotEmpty(parentFlowKey)) {
            handelRules(assignPluginDef, parentFlowKey);
        }

        BpmUserCalcPluginSession bpmUserCalcPluginSession = getBpmPluginSessionFactory().buildBpmUserCalcPluginSession(bpmDelegateTask);
        //调用人员计算插件对人员进行计算。
        UserQueryPlugin userQueryPlugin = AppUtil.getBean(UserQueryPlugin.class);
        List<BpmIdentity> bpmIdentities = userQueryPlugin.execute(bpmUserCalcPluginSession, assignPluginDef);

        //将人员添加到执行人中。
        if (BeanUtils.isNotEmpty(bpmIdentities)) {
            bpmDelegateTask.addExecutors(bpmIdentities);
        }
    }

    /**
     * 如果是子流程，处理userRules , local_类型的 groupNo加 100 使子流程独自配置排在后面。
     */
    private void handelRules(UserAssignPluginDefine assignPluginDef, String parentFlowKey) {
        List<UserAssignRule> rules = assignPluginDef.getRuleList();
        if (BeanUtils.isEmpty(rules)) {
            return;
        }
        List<UserAssignRule> extractUserRule = new ArrayList<UserAssignRule>();

        for (UserAssignRule rule : rules) {
            if (rule.getParentFlowKey().equals(BpmConstants.LOCAL)) {
                rule.setGroupNo(rule.getGroupNo() + 100);
                extractUserRule.add(rule);
            }
//TODO			else if(rule.getParentFlowKey().equals(parentFlowKey)){
            else {
                extractUserRule.add(rule);
            }
        }
        assignPluginDef.setRuleList(extractUserRule);
    }

}
