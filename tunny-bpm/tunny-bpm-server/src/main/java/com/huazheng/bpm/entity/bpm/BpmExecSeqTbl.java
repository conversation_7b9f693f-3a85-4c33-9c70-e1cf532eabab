package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.Date;


/**
 * 流程任务执行顺序 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-01-09 11:32:01
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmExecSeqTbl extends AbstractPo<String> {
    protected String id;        /*主键*/
    protected String srcExecId;        /*源执行ID*/
    protected String srcProcExecId;        /*源流程执行ID*/
    protected String targetExecId;        /*目标执行ID*/
    protected Date createTime;

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setSrcExecId(String srcExecId) {
        this.srcExecId = srcExecId;
    }

    /**
     * 返回 源执行ID
     *
     * @return
     */
    public String getSrcExecId() {
        return this.srcExecId;
    }

    public void setSrcProcExecId(String srcProcExecId) {
        this.srcProcExecId = srcProcExecId;
    }

    /**
     * 返回 源流程执行ID
     *
     * @return
     */
    public String getSrcProcExecId() {
        return this.srcProcExecId;
    }

    public void setTargetExecId(String targetExecId) {
        this.targetExecId = targetExecId;
    }

    /**
     * 返回 目标执行ID
     *
     * @return
     */
    public String getTargetExecId() {
        return this.targetExecId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("srcExecId", this.srcExecId)
                .append("srcProcExecId", this.srcProcExecId)
                .append("targetExecId", this.targetExecId)
                .append("createTime", this.createTime)
                .toString();
    }
}
