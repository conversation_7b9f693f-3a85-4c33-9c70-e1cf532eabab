package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmTaskAssignDao;
import com.huazheng.bpm.dao.bpm.BpmTaskAssignQueryDao;
import com.huazheng.bpm.dao.bpm.BpmTaskQueryDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmTaskAssignPo;
import com.huazheng.bpm.model.base.BpmIdentity;
import com.huazheng.bpm.model.task.IBpmTask;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 任务候选人 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：simon cai
 * 邮箱地址：<EMAIL>
 * 创建时间：2016-12-14 19:35:05
 * </pre>
 */
@SuppressWarnings("serial")
@Transactional
@Scope("prototype")
@Service
public class BpmTaskAssign extends AbstractDomain<String, BpmTaskAssignPo> {

    @Resource
    private BpmTaskAssignDao bpmTaskAssignDao;
    @Resource
    private BpmTaskAssignQueryDao bpmTaskAssignQueryDao;
    @Resource
    private BpmTaskQueryDao bpmTaskQueryDao;

    protected void init() {
        bpmTaskAssignDao = AppUtil.getBean(BpmTaskAssignDao.class);
        this.setDao(bpmTaskAssignDao);
    }

    /**
     * 添加任务候选人。
     * <pre>
     * 	1.如果候选人为user类型，那么直接添加候选人。
     *  2.如果为用户组合，那么将用户组合分割开，循环添加候选人。
     *  3.如果候选人为组织。
     *  	1.用户抽取类型为抽取。
     *  		则将组织作为执行人插入候选人表。
     *  	2.如果抽取类型为延迟抽取。
     *  		那么将组织中的人员计算出来，添加到候选人表中。
     * </pre>
     *
     * @param BpmTask 关联任务ID
     * @param list    候选人列表。
     *                void
     */
    public void addCandidate(IBpmTask task, List<BpmIdentity> list) {
        if (BeanUtils.isEmpty(task) || BeanUtils.isEmpty(list)) {
            return;
        }

        String taskId = task.getId();
        String instId = task.getProcInstId();
        String type = null;
        BpmTaskAssignPo po = null;

        for (BpmIdentity identity : list) {
            if (BpmIdentity.TYPE_PARTY.equals(identity.getType())) {
                type = identity.getGroupType();
            } else {
                type = identity.getType();
            }
            po = bpmTaskAssignQueryDao.getByTaskExeType(taskId, identity.getId(), type);                            //候选人记录
            if (BeanUtils.isNotEmpty(po)) {
                continue;
            }

//			String gid=identity.getId();																			//判断执行人为group
//			PartyGroupRepository partyGroupRepository = AppUtil.getBean(PartyGroupRepository.class);
//			PartyGroupPo partyGroupPo= partyGroupRepository.get(gid);
//			if(JacksonUtil.isNotEmpty(partyGroupPo)){
//				type=BpmIdentity.TYPE_GROUP;
//			}
            BpmTaskAssignPo taskAssign = new BpmTaskAssignPo();
            taskAssign.setTaskId(taskId);
            taskAssign.setProcInstId(instId);
            taskAssign.setType(type);                                                                            //加入类型
            taskAssign.setExecutor(identity.getId());
            bpmTaskAssignDao.create(taskAssign);
        }
    }

    /**
     * 添加任务候选人。
     * <pre>
     * 	1.如果候选人为user类型，那么直接添加候选人。
     *  2.如果为用户组合，那么将用户组合分割开，循环添加候选人。
     *  3.如果候选人为组织。
     *  	1.用户抽取类型为抽取。
     *  		则将组织作为执行人插入候选人表。
     *  	2.如果抽取类型为延迟抽取。
     *  		那么将组织中的人员计算出来，添加到候选人表中。
     * </pre>
     *
     * @param BpmTask 关联任务ID
     * @param userIds 候选人列表
     *                void
     */
    public void addAssign(IBpmTask task, String[] userIds) {
        if (BeanUtils.isEmpty(task) || BeanUtils.isEmpty(userIds)) {
            return;
        }

        String taskId = task.getId();
        String instId = task.getProcInstId();
        String type = BpmIdentity.TYPE_USER;
        BpmTaskAssignPo po = null;

        for (String userId : userIds) {
            po = bpmTaskAssignQueryDao.getByTaskExeType(taskId, userId, type);
            if (BeanUtils.isNotEmpty(po)) {
                continue;
            }

            BpmTaskAssignPo taskAssign = new BpmTaskAssignPo();
            taskAssign.setTaskId(taskId);
            taskAssign.setProcInstId(instId);
            taskAssign.setType(type);
            taskAssign.setExecutor(userId);
            bpmTaskAssignDao.create(taskAssign);
        }
    }

    /**
     * 根据任务ID删除候选人。
     *
     * @param taskId
     */
    public void delByTask(String taskId) {
        bpmTaskAssignDao.delByTask(taskId);
    }

    /**
     * 根据实例ID删除流程。
     *
     * @param instList void
     */
    public void delByInst(List<String> instList) {
        bpmTaskAssignDao.delByInst(instList);
    }

    public void addCandidate(String taskId, List<BpmIdentity> list) {
        IBpmTask bpmTask = bpmTaskQueryDao.get(taskId);
        addCandidate(bpmTask, list);
    }

}
