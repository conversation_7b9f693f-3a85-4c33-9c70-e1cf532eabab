package com.huazheng.bpm.plugin;

import com.huazheng.bpm.entity.constant.BpmConstants;
import com.huazheng.bpm.handler.model.DefaultMsgVo;
import com.huazheng.bpm.handler.model.MsgVo;
import com.huazheng.bpm.model.base.MsgType;
import com.huazheng.bpm.model.base.NotifyItem;
import com.huazheng.bpm.model.base.TemplateVo;
import com.huazheng.bpm.mq.IJmsProducer;
import com.huazheng.bpm.service.TemplateService;
import com.huazheng.bpm.util.base.FreemarkerEngine;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.helper.UserAssignRuleQueryHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * TODO（用一句话描述这个类做什么用）。
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月14日-上午9:40:32
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@Service("notifyHelper")
public class NotifyHelper {

    @Resource
    private TemplateService templateService;

    /**
     * @param notifyItem
     * @param typeKey
     * @param vars
     */
    public void notify(NotifyItem notifyItem, String typeKey, Map<String, Object> vars) {
        List<String> pluginUsers = UserAssignRuleQueryHelper.queryUsersWithExtract(notifyItem.getUserAssignRules(), vars);
        notify(pluginUsers, notifyItem.getMsgTypes(), typeKey, vars);
    }

    /**
     * 向指定的接收人发送消息
     *
     * @param recievers
     * @param msgTypeKeys
     * @param typeKey
     * @param vars
     */
    public void notify(List<String> recievers, List<String> msgTypeKeys, String typeKey, Map<String, Object> vars) {
        TemplateService templateService = AppUtil.getBean(TemplateService.class);
        TemplateVo templateVo = templateService.getDefaultTemplate(typeKey);

        IJmsProducer jmsProducer = AppUtil.getBean(IJmsProducer.class);
        if (jmsProducer == null) {
            return;
        }

        // 发送人
        String sender = vars.get(BpmConstants.CUR_USER).toString();

        String html = templateVo.getHtml();
        String text = templateVo.getPlain();
        String subject = templateVo.getSubject();
        try {
            FreemarkerEngine freemarkEngine = AppUtil.getBean(FreemarkerEngine.class);
            html = freemarkEngine.parseByStringTemplate(html, vars);
            text = freemarkEngine.parseByStringTemplate(text, vars);
            subject = freemarkEngine.parseByStringTemplate(subject, vars);
        } catch (Exception e) {
            // TODO: handle exception
        }
        for (String type : msgTypeKeys) {
            if (MsgType.SMS.key().equals(type)) {
                html = text;
            }
            MsgVo msgVo = new DefaultMsgVo(subject, html, sender, recievers, type);
            jmsProducer.sendToQueue(msgVo);
        }
    }

}
