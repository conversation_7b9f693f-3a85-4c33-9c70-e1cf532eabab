package com.huazheng.bpm.entity.framework;

import java.util.List;

/**
 * 树分类模型接口
 *
 * <AUTHOR>
 */
public interface TreeType<P extends PO<?>> {
    /**
     * 返回当前节点的ID
     *
     * @return
     */
    String getId();

    /**
     * 返回当前节点的显示名称
     *
     * @return
     */
    String getName();

    /**
     * 返回父节点的ID
     *
     * @return
     */
    String getParentId();

    /**
     * 返回当前节点在树中的路径。路径结构由 ID和小数点合成，如ID1.ID2.ID3
     *
     * @return
     */
    String getPath();

    /**
     * 返回当前节点在树中的层次，根节点为1，下面的每层加1.
     *
     * @return
     */
    Integer getDepth();

    /**
     * 返回排序号
     *
     * @return
     */
    Integer getSn();

    List<P> getSubs();

    void addSub(P po);

    String toTreeString();
}
