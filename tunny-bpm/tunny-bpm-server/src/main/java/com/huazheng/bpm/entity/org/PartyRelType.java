package com.huazheng.bpm.entity.org;

import java.util.*;

/**
 * 人员与组织关系枚举
 *
 * <pre>
 * 构建组：ibps-api-biz
 * 作者：huangchunyan
 * 邮箱：<EMAIL>
 * 日期：2015-11-2-下午3:39:36
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum PartyRelType {

    SLAVE_DEFAULT("slaveDefault", "从属", "组织", "用户"), UNDER("under", "上下级", "上司", "下属"), GRADE_ADMIN("gradeAdmin", "分级管理员", "组织", "员工"), ORG_MANAGER("orgManager", "组织负责人", "组织", "员工"), PRINCIPAL("principal", "主负责人", "岗位", "员工"), MAIN_POST("mainPost", "主岗位", "岗位", "员工"), ROLE_ASSIGNABLE("roleAssignable", "可分配角色", "参与者", "角色"), ORG_POST("orgPost", "组织岗位关联", "组织", "岗位"), ORG_ROLE("orgRole", "组织角色关联", "组织", "角色"), ORG_USER("orgUser", "组织人员关联", "组织", "员工"), POST_ROLE("postRole", "岗位角色关联", "岗位", "角色"), POST_USER("postUser", "岗位人员关联", "岗位", "人员"), USER_ROLE("userRole", "用户角色关联", "用户", "角色");

    private String key;
    private String label;
    private String currentName;
    private String relName;

    @Override
    public String toString() {
        return key;
    }

    /**
     * 创建一个新的实例 PartyRelType.
     *
     * @param key
     * @param label
     * @param currentName
     * @param relName
     */
    PartyRelType(String key, String label, String currentName, String relName) {
        this.key = key;
        this.label = label;
        this.currentName = currentName;
        this.relName = relName;
    }

    public String key() {
        return key;
    }

    public String label() {
        return label;
    }

    public String currentName() {
        return currentName;
    }

    public String relName() {
        return relName;
    }

    public static String getLabel(String value) {
        for (PartyRelType vl : values()) {
            if (vl.key.equals(value)) {
                return vl.label;
            }
        }
        return value;
    }

    public static PartyRelType get(String value) {
        for (PartyRelType p : PartyRelType.values()) {
            if (p.key().equals(value)) {
                return p;
            }
        }
        return null;
    }

    public static List<PartyRelType> list() {
        return new ArrayList<PartyRelType>(Arrays.asList(values()));
    }

    /**
     * 转成map使用
     *
     * @return
     */
    public static List<Map<String, String>> getlist() {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        List<PartyRelType> rlist = list();
        for (PartyRelType prt : rlist) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("key", prt.key());
            map.put("label", prt.label());
            list.add(map);
        }
        return list;
    }

    /**
     * 值是否存在
     *
     * @param _value
     * @return
     */
    public static boolean containsValue(String value) {
        for (PartyRelType pt : values()) {
            if (pt.key.equalsIgnoreCase(value)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 当前关系名称是否存在
     *
     * @param _value
     * @return
     */
    public static boolean containsCurrName(String value) {
        for (PartyRelType pt : values()) {
            if (pt.currentName.equalsIgnoreCase(value)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 当前关联名称是否存在
     *
     * @param _value
     * @return
     */
    public static boolean containsRelName(String value) {
        for (PartyRelType pt : values()) {
            if (pt.relName.equalsIgnoreCase(value)) {
                return true;
            }
        }
        return false;
    }
}
