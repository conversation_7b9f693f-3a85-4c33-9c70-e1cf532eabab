package com.huazheng.bpm.entity.core;


import com.huazheng.bpm.util.string.StringCollections;

import java.util.List;


public class OptionEntity {
    private String value;
    private String label;

    public OptionEntity(String option, String optionToken) {
        List<String> vl = StringCollections.toList(option, optionToken);
        value = vl.get(0);
        label = vl.get(1);
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

}
