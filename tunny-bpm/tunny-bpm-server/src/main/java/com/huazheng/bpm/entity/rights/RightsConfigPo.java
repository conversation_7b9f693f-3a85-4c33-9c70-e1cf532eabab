package com.huazheng.bpm.entity.rights;


import com.huazheng.bpm.util.core.JacksonUtil;

import java.util.Collections;
import java.util.List;


/**
 * 权限配置PO对象。
 *
 * <pre>
 * 构建组：ibps-common-biz
 * 作者：hcy
 * 邮箱：<EMAIL>
 * 日期：2015-12-16 09:29:19
 * 版权：广州流辰信息技术有限公司
 * </pre>
 */
@SuppressWarnings("serial")
public class RightsConfigPo extends RightsConfigTbl {

    public static RightsConfigPo fromJsonString(String data) {
        if (!JacksonUtil.isJsonObject(data)) {
            return null;
        }
        return JacksonUtil.getDTO(data, RightsConfigPo.class);
    }

    public static List<RightsConfigPo> fromJsonArrayString(String listData) {
        if (!JacksonUtil.isJsonArray(listData)) {
            return Collections.emptyList();
        }
        return JacksonUtil.getDTOList(listData, RightsConfigPo.class);
    }

}
