package com.huazheng.bpm.entity.constant;

/**
 * 任务状态。
 * <pre>
 * 普通任务(NORMAL)
 * 代理任务(AGENT)
 * 转交任务(DELIVERTO)
 * 流转源任务(TRANSFORMING ,在列表不可见)
 * 接收流转任务(TRANSFORMED)
 * 通知任务(COMMU)
 *
 * 构建组：ibps-bpmn-api
 * 作者：simon cai
 * 邮箱:<EMAIL>
 * 日期:2015-2-15-下午11:27:12
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum TaskType {
    NORMAL("NORMAL", "普通任务"),
    AGENT("AGENT", "代理任务"),
    DELIVERTO("DELIVERTO", "转交任务"),
    TRANSFORMING("TRANSFORMING", "流转源任务"),
    TRANSFORMED("TRANSFORMED", "接收流转任务"),
    COMMU("COMMU", "通知任务");

    // 键
    private String key = "";
    // 值
    private String value = "";

    // 构造方法
    TaskType(String key, String value) {
        this.key = key;
        this.value = value;
    }

    // =====getting and setting=====
    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return key;
    }

    /**
     * 通过key获取对象
     *
     * @param key
     * @return
     */
    public static TaskType fromKey(String key) {
        for (TaskType c : TaskType.values()) {
            if (c.getKey().equalsIgnoreCase(key)) {
                return c;
            }
        }
        throw new IllegalArgumentException(key);
    }
}
