/**
 * 描述：TODO
 * 包名：com.lc.bpmn.api.plugin.core.factory
 * 文件名：BpmPluginSessionFactory.java
 * <EMAIL>
 * 日期2015-2-23-下午8:55:51
 * 版权：广州流辰信息技术有限公司版权所有
 */
package com.huazheng.bpm.plugin.factory;


import com.huazheng.bpm.model.base.IDataObject;
import com.huazheng.bpm.model.delegate.BpmDelegateExecution;
import com.huazheng.bpm.model.delegate.BpmDelegateTask;
import com.huazheng.bpm.plugin.BpmExecutionPluginSession;
import com.huazheng.bpm.util.cmd.ProcInstCmd;
import com.huazheng.bpm.util.cmd.TaskFinishCmd;
import com.huazheng.bpm.util.session.*;

import java.util.Map;

//import com.lc.bo.api.model.DataObject;

/**
 * <pre>
 * 描述：构造插件执行所需的会话数据的工厂
 * 构建组：ibps-bpmn-api
 * 作者：Winston Yan
 * 邮箱：<EMAIL>
 * 日期：2015-2-23-下午8:55:51
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface BpmPluginSessionFactory {
    /**
     * 构造任务插件服务
     * @param bpmDelegateTask
     * @return
     * BpmTaskPluginSession
     * @exception
     * @since 1.0.0
     */
    BpmTaskPluginSession buildBpmTaskPluginSession(BpmDelegateTask bpmDelegateTask);

    /**
     * 构造执行插件服务
     * @param bpmDelegateExecution
     * @return
     * BpmExecutionPluginSession
     * @exception
     * @since 1.0.0
     */
    BpmExecutionPluginSession buildBpmExecutionPluginSession(BpmDelegateExecution bpmDelegateExecution);

    BpmUserCalcPluginSession buildBpmUserCalcPluginSession(Map<String, Object> variables);

    /**
     * 构建BpmUserCalcPluginSessionsession对象。
     * @param variables
     * @param dataObject
     * @return
     * BpmUserCalcPluginSession
     */
    BpmUserCalcPluginSession buildBpmUserCalcPluginSession(Map<String, Object> variables, IDataObject dataObject);

    BpmUserCalcPluginSession buildBpmUserCalcPluginSession(BpmDelegateTask bpmDelegateTask);

    ProcessInstAopPluginSession buildProcessInstAopPluginSession(ProcInstCmd procInstCmd);

    TaskAopPluginSession buildTaskAopPluginSession(TaskFinishCmd taskFinishCmd);

    TaskActionPluginSession buildTaskActionPluginSession(BpmDelegateTask bpmDelegateTask, TaskFinishCmd taskFinishCmd);

    ExecutionActionPluginSession buildExecutionActionPluginSession(BpmDelegateExecution bpmDelegateExecution, TaskFinishCmd taskFinishCmd);


}
