package com.huazheng.bpm.entity.party;

import com.huazheng.bpm.entity.org.PartyEntityTbl;
import org.hibernate.validator.constraints.NotBlank;


/**
 * 角色  实体对象。
 *
 * <pre>
 * 构建组：ibps-org-biz
 * 作者：huangchunyan
 * 邮箱：<EMAIL>
 * 日期：2016-06-20 09:07:07
 * 版权：广州流辰信息技术有限公司
 * </pre>
 */
@SuppressWarnings("serial")
public class PartyRoleTbl extends PartyEntityTbl {
    protected String id; /*主键*/
    @NotBlank(message = "{com.huazheng.bpm.entity.party.PartyRoleTbl.roleNote}", groups = {ValidGroup.SaveRole.class})
    protected String roleNote; /*角色描述*/
    @NotBlank(message = "{com.huazheng.bpm.entity.party.PartyRoleTbl.roleAlias}", groups = {ValidGroup.SaveRole.class})
    protected String roleAlias; /*角色别名*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 返回 角色名
     *
     * @return
     */
    public String getName() {
        return this.name;
    }

    public void setRoleNote(String roleNote) {
        this.roleNote = roleNote;
    }

    /**
     * 返回 角色描述
     *
     * @return
     */
    public String getRoleNote() {
        return this.roleNote;
    }

    public void setRoleAlias(String roleAlias) {
        this.roleAlias = roleAlias;
    }

    /**
     * 返回 角色别名
     *
     * @return
     */
    public String getRoleAlias() {
        return this.roleAlias;
    }
}
