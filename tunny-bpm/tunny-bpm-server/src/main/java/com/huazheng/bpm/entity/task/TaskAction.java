//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4-2
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.11.24 at 11:54:23 AM CST
//


package com.huazheng.bpm.entity.task;

import javax.xml.bind.annotation.*;


/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="description" minOccurs="0">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="handlerClass" minOccurs="0">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *       &lt;attribute name="name" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
 *       &lt;attribute name="supportType" use="required" type="{http://www.bpmhome.cn/bpm/plugin/taskAction}supportType" />
 *       &lt;attribute name="actionType" use="required" type="{http://www.bpmhome.cn/bpm/plugin/taskAction}actionType" />
 *       &lt;attribute name="script" use="required" type="{http://www.w3.org/2001/XMLSchema}boolean" />
 *       &lt;attribute name="init" type="{http://www.w3.org/2001/XMLSchema}boolean" />
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "description",
        "handlerClass"
})
@XmlRootElement(name = "taskAction")
public class TaskAction {

    protected TaskAction.Description description;
    protected TaskAction.HandlerClass handlerClass;
    @XmlAttribute(name = "name", required = true)
    protected String name;
    @XmlAttribute(name = "supportType", required = true)
    protected SupportType supportType;
    @XmlAttribute(name = "actionType", required = true)
    protected ActionType actionType;
    @XmlAttribute(name = "script", required = true)
    protected boolean script;
    @XmlAttribute(name = "init")
    protected Boolean init;

    /**
     * Gets the value of the description property.
     *
     * @return possible object is
     * {@link TaskAction.Description }
     */
    public TaskAction.Description getDescription() {
        return description;
    }

    /**
     * Sets the value of the description property.
     *
     * @param value allowed object is
     *              {@link TaskAction.Description }
     */
    public void setDescription(TaskAction.Description value) {
        this.description = value;
    }

    /**
     * Gets the value of the handlerClass property.
     *
     * @return possible object is
     * {@link TaskAction.HandlerClass }
     */
    public TaskAction.HandlerClass getHandlerClass() {
        return handlerClass;
    }

    /**
     * Sets the value of the handlerClass property.
     *
     * @param value allowed object is
     *              {@link TaskAction.HandlerClass }
     */
    public void setHandlerClass(TaskAction.HandlerClass value) {
        this.handlerClass = value;
    }

    /**
     * Gets the value of the name property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the value of the name property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * Gets the value of the supportType property.
     *
     * @return possible object is
     * {@link SupportType }
     */
    public SupportType getSupportType() {
        return supportType;
    }

    /**
     * Sets the value of the supportType property.
     *
     * @param value allowed object is
     *              {@link SupportType }
     */
    public void setSupportType(SupportType value) {
        this.supportType = value;
    }

    /**
     * Gets the value of the actionType property.
     *
     * @return possible object is
     * {@link ActionType }
     */
    public ActionType getActionType() {
        return actionType;
    }

    /**
     * Sets the value of the actionType property.
     *
     * @param value allowed object is
     *              {@link ActionType }
     */
    public void setActionType(ActionType value) {
        this.actionType = value;
    }

    /**
     * Gets the value of the script property.
     */
    public boolean isScript() {
        return script;
    }

    /**
     * Sets the value of the script property.
     */
    public void setScript(boolean value) {
        this.script = value;
    }

    /**
     * Gets the value of the init property.
     *
     * @return possible object is
     * {@link Boolean }
     */
    public Boolean isInit() {
        return init;
    }

    /**
     * Sets the value of the init property.
     *
     * @param value allowed object is
     *              {@link Boolean }
     */
    public void setInit(Boolean value) {
        this.init = value;
    }


    /**
     * <p>Java class for anonymous complex type.
     *
     * <p>The following schema fragment specifies the expected content contained within this class.
     *
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    public static class Description {

        @XmlAttribute(name = "value", required = true)
        protected String value;

        /**
         * Gets the value of the value property.
         *
         * @return possible object is
         * {@link String }
         */
        public String getValue() {
            return value;
        }

        /**
         * Sets the value of the value property.
         *
         * @param value allowed object is
         *              {@link String }
         */
        public void setValue(String value) {
            this.value = value;
        }

    }


    /**
     * <p>Java class for anonymous complex type.
     *
     * <p>The following schema fragment specifies the expected content contained within this class.
     *
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;attribute name="value" use="required" type="{http://www.w3.org/2001/XMLSchema}string" />
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    public static class HandlerClass {

        @XmlAttribute(name = "value", required = true)
        protected String value;

        /**
         * Gets the value of the value property.
         *
         * @return possible object is
         * {@link String }
         */
        public String getValue() {
            return value;
        }

        /**
         * Sets the value of the value property.
         *
         * @param value allowed object is
         *              {@link String }
         */
        public void setValue(String value) {
            this.value = value;
        }

    }

}
