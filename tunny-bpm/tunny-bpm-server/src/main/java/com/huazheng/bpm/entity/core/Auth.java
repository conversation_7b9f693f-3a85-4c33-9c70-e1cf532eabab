//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4-2
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.08.25 at 03:37:49 PM CST
//


package com.huazheng.bpm.entity.core;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for auth complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="auth">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="type" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="authdefs" type="{http://www.bpmhome.cn/ibps-bpm-def}authdefs" minOccurs="0"/>
 *         &lt;element name="rightdefs" type="{http://www.bpmhome.cn/ibps-bpm-def}rightdefs" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "auth", propOrder = {
        "id",
        "name",
        "type",
        "authdefs",
        "rightdefs"
})
public class Auth {

    @XmlElement(required = true)
    protected String id;
    @XmlElement(required = true)
    protected String name;
    @XmlElement(required = true)
    protected String type;
    protected Authdefs authdefs;
    protected Rightdefs rightdefs;

    /**
     * Gets the value of the id property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setId(String value) {
        this.id = value;
    }

    /**
     * Gets the value of the name property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the value of the name property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * Gets the value of the type property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setType(String value) {
        this.type = value;
    }

    /**
     * Gets the value of the authdefs property.
     *
     * @return possible object is
     * {@link Authdefs }
     */
    public Authdefs getAuthdefs() {
        return authdefs;
    }

    /**
     * Sets the value of the authdefs property.
     *
     * @param value allowed object is
     *              {@link Authdefs }
     */
    public void setAuthdefs(Authdefs value) {
        this.authdefs = value;
    }

    /**
     * Gets the value of the rightdefs property.
     *
     * @return possible object is
     * {@link Rightdefs }
     */
    public Rightdefs getRightdefs() {
        return rightdefs;
    }

    /**
     * Sets the value of the rightdefs property.
     *
     * @param value allowed object is
     *              {@link Rightdefs }
     */
    public void setRightdefs(Rightdefs value) {
        this.rightdefs = value;
    }

}
