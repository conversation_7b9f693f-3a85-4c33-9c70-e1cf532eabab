package com.huazheng.bpm.model.core;


import com.huazheng.bpm.entity.constant.EventType;
import com.huazheng.bpm.model.base.NotifyVo;
import com.huazheng.bpm.plugin.AbstractBpmExecutionPluginDefine;

import java.util.HashMap;
import java.util.Map;

/**
 * TODO（用一句话描述这个类做什么用）。
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午7:37:13
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
public class ProcNotifyPluginDefine extends AbstractBpmExecutionPluginDefine {

    /**
     * 信息配置集合
     */
    private Map<EventType, NotifyVo> notifyVoMap = new HashMap<EventType, NotifyVo>();

    public void addNotifyVo(EventType eventType, NotifyVo notifyVo) {
        notifyVo.setEventType(eventType);
        notifyVoMap.put(eventType, notifyVo);
    }

    public Map<EventType, NotifyVo> getNotifyVoMap() {
        return notifyVoMap;
    }
}
