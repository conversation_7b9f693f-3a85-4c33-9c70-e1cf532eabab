package com.huazheng.bpm.plugin.usercalc.role.def;


import com.huazheng.bpm.plugin.AbstractUserCalcPluginDefine;

/**
 * 角色插件定义
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月14日-上午10:23:39
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
public class RolePluginDefine extends AbstractUserCalcPluginDefine {

	/*
	<role xmlns="http://www.bpmhome.cn/bpm/plugins/userCalc/role"
	      source="">
	    <roles roleKey="" roleName=""/>
	</role>
	*/

    /**
     * 来源。：当前用户所有组织，发起人所在组织，上一步执行人所在组织，变量，指定组织
     */
    private String source = "";

    /**
     * 指定的角色key。
     */
    private String roleKey = "";

    /**
     * 指定的角色名称。
     */
    private String roleName = "";
    private String nodeId = "";
    private String nodeName = "";

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getRoleKey() {
        return roleKey;
    }

    public void setRoleKey(String roleKey) {
        this.roleKey = roleKey;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

}
