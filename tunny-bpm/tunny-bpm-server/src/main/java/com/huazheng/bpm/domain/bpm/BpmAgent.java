package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmAgentConditionDao;
import com.huazheng.bpm.dao.bpm.BpmAgentDao;
import com.huazheng.bpm.dao.bpm.BpmAgentDefDao;
import com.huazheng.bpm.dao.bpm.BpmAgentQueryDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmAgentConditionPo;
import com.huazheng.bpm.entity.bpm.BpmAgentDefPo;
import com.huazheng.bpm.entity.bpm.BpmAgentPo;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

/**
 * 流程代理 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-03-30 17:29:13
 * </pre>
 */
@SuppressWarnings("serial")
@Service
@Scope("prototype")
public class BpmAgent extends AbstractDomain<String, BpmAgentPo> {

    private BpmAgentDao bpmAgentDao = null;
    private BpmAgentQueryDao bpmAgentQueryDao = null;

    private BpmAgentDefDao bpmAgentDefDao = null;
    private BpmAgentConditionDao bpmAgentConditionDao = null;

    protected void init() {
        bpmAgentDao = AppUtil.getBean(BpmAgentDao.class);
        bpmAgentQueryDao = AppUtil.getBean(BpmAgentQueryDao.class);
        bpmAgentDefDao = AppUtil.getBean(BpmAgentDefDao.class);
        bpmAgentConditionDao = AppUtil.getBean(BpmAgentConditionDao.class);
        this.setDao(bpmAgentDao);
    }

    @Override
    protected void onSave() {
        if (BeanUtils.isNotEmpty(getId()))
            setNewFlag(BeanUtils.isEmpty(bpmAgentQueryDao.get(getId())));
    }

    /**
     * 主从表一并保存
     * void
     *
     * @throws
     * @since 1.0.0
     */
    public void saveCascade() {
        save();
        if (getData().isDelBeforeSave()) {
            bpmAgentDefDao.deleteByMainId(getId());
            bpmAgentConditionDao.deleteByMainId(getId());
        }

        if (BeanUtils.isNotEmpty(getData().getBpmAgentDefPoList())) {
            for (BpmAgentDefPo bpmAgentDefPo : getData().getBpmAgentDefPoList()) {
                //设置外键
                bpmAgentDefPo.setAgentId(getId());

                bpmAgentDefDao.create(bpmAgentDefPo);
            }
        }
        if (BeanUtils.isNotEmpty(getData().getBpmAgentConditionPoList())) {
            for (BpmAgentConditionPo bpmAgentConditionPo : getData().getBpmAgentConditionPoList()) {
                //设置外键
                bpmAgentConditionPo.setAgentId(getId());

                bpmAgentConditionDao.create(bpmAgentConditionPo);
            }
        }
    }

    /**
     * 主从表一并删除
     * void
     *
     * @throws
     * @since 1.0.0
     */
    public void deleteByIdsCascade(String[] ids) {
        for (String id : ids) {
            bpmAgentDefDao.deleteByMainId(id);
            bpmAgentConditionDao.deleteByMainId(id);
        }
        deleteByIds(ids);
    }

    /**
     * 设置 流程代理状态
     *
     * @param id
     * @param isEnabled
     */
    public void setEnable(String id, String isEnabled) {
        BpmAgentPo po = bpmAgentQueryDao.get(id);
        po.setIsEnabled(isEnabled);
        setData(po);
        update();
    }
}
