//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4-2
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.11.24 at 11:54:23 AM CST
//


package com.huazheng.bpm.entity.task;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for supportType.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="supportType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="usertask"/>
 *     &lt;enumeration value="signtask"/>
 *     &lt;enumeration value="both"/>
 *     &lt;enumeration value="start"/>
 *     &lt;enumeration value="all"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */
@XmlType(name = "supportType")
@XmlEnum
public enum SupportType {


    /**
     * 用户任务专用
     */
    @XmlEnumValue("usertask")
    USERTASK("usertask"),

    /**
     * 会签任务专用
     */
    @XmlEnumValue("signtask")
    SIGNTASK("signtask"),

    /**
     * 支持普通用户任务和会签任务
     */
    @XmlEnumValue("both")
    BOTH("both"),

    /**
     * 起始节点
     */
    @XmlEnumValue("start")
    START("start"),

    /**
     * 所有节点
     */
    @XmlEnumValue("all")
    ALL("all");
    private final String value;

    SupportType(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static SupportType fromValue(String v) {
        for (SupportType c : SupportType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
