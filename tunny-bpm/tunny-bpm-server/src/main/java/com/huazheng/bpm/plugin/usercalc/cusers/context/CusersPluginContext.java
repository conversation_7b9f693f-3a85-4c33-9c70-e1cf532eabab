package com.huazheng.bpm.plugin.usercalc.cusers.context;

import com.huazheng.bpm.plugin.AbstractUserCalcPluginContext;
import com.huazheng.bpm.plugin.IBpmPluginDefine;
import com.huazheng.bpm.plugin.IPluginParser;
import com.huazheng.bpm.plugin.IRuntimePlugin;
import com.huazheng.bpm.plugin.usercalc.ExecutorVar;
import com.huazheng.bpm.plugin.usercalc.cusers.def.CusersPluginDefine;
import com.huazheng.bpm.plugin.usercalc.cusers.runtime.CusersPlugin;
import com.huazheng.bpm.util.base.XmlUtil;
import com.jamesmurty.utils.XMLBuilder;
import net.sf.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.w3c.dom.Element;

import javax.xml.parsers.FactoryConfigurationError;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;

/**
 * 指定用户插件上下文
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午7:00:34
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
@Service("cusersPluginContext")
@Scope("prototype")
public class CusersPluginContext extends AbstractUserCalcPluginContext implements IPluginParser {

    /*
     * <cusers
     * xmlns="http://www.bpmhome.cn/bpm/plugins/userCalc/cusers"
     * logicCal="or"
     * extract="no"
     * type="start">
     * <executorVar name=""/>
     * <members account="" fullName=""/>
     * <nodes nodeId="" nodeName=""/>
     * </cusers>
     */

    @Override
    public String getDescription() {
        CusersPluginDefine def = (CusersPluginDefine) this.getBpmPluginDefine();
        String source = def.getSource();
        String str = "";
        if ("start".equals(source)) {
            str = "发起人";
        } else if ("prev".equals(source)) {
            str = "上一步执行人";
        } else if ("spec".equals(source)) {
            str = "指定人[" + def.getFullName() + "]";
        } else if ("var".equals(source)) {
            str = "人员变量[" + def.getExecutorVar().getName() + "]";
        } else if ("node".equals(source)) {
            str = "节点[" + def.getNodeName() + "]";
        } else {
            str = "人员插件";
        }

        return str;
    }

    @Override
    public String getTitle() {
        return "用户";
    }

    @Override
    public Class<? extends IRuntimePlugin<?, ?, ?>> getPluginClass() {
        return CusersPlugin.class;
    }

    @Override
    public String getPluginXml() {
        CusersPluginDefine def = (CusersPluginDefine) this.getBpmPluginDefine();
        try {
            String source = def.getSource();

            XMLBuilder xmlBuilder = XMLBuilder.create("cusers")
                    .a("xmlns", "http://www.bpmhome.cn/bpm/plugins/userCalc/cusers")
                    .a("extract", def.getExtract().getKey())
                    .a("logicCal", def.getLogicCal().getKey())
                    .a("source", def.getSource()).up();
            if ("spec".equals(source)) {
                xmlBuilder.e("members").a("account", def.getAccount()).a("fullName", def.getFullName()).up();
            } else if ("var".equals(source)) {
                xmlBuilder.e("executorVar").a("source", def.getExecutorVar().getSource())
                        .a("name", def.getExecutorVar().getName())
                        .a("value", def.getExecutorVar().getValue())
                        .a("executorType", def.getExecutorVar().getExecutorType())
                        .a("valType", def.getExecutorVar().getValType()).up();
            } else if ("node".equals(source)) {
                xmlBuilder.e("nodes").a("nodeId", def.getNodeId()).a("nodeName", def.getNodeName()).up();
            }

            return xmlBuilder.asString();
        } catch (ParserConfigurationException e) {
            e.printStackTrace();
        } catch (FactoryConfigurationError e) {
            e.printStackTrace();
        } catch (TransformerException e) {
            e.printStackTrace();
        }

        return "";
    }

    @Override
    protected IBpmPluginDefine parseElement(Element element) {
        String source = element.getAttribute("source");
        CusersPluginDefine def = new CusersPluginDefine();
        def.setSource(source);

        if ("spec".equals(source)) {
            Element memberEl = XmlUtil.getChildNodeByName(element, "members");
            String account = memberEl.getAttribute("account");
            String fullName = memberEl.getAttribute("fullName");

            def.setAccount(account);
            def.setFullName(fullName);
        } else if ("var".equals(source)) {
            Element varEl = XmlUtil.getChildNodeByName(element, "executorVar");
            String varSource = varEl.getAttribute("source");
            String executorType = varEl.getAttribute("executorType");
            String valType = varEl.getAttribute("valType");
            String name = varEl.getAttribute("name");
            String value = varEl.getAttribute("value");
            ExecutorVar executorVar = new ExecutorVar(varSource, name, executorType, valType, value);
            def.setExecutorVar(executorVar);
        } else if ("node".equals(source)) {
            Element memberEl = XmlUtil.getChildNodeByName(element, "nodes");
            String nodeId = memberEl.getAttribute("nodeId");
            String nodeName = memberEl.getAttribute("nodeName");

            def.setNodeId(nodeId);
            def.setNodeName(nodeName);
        }

        return def;
    }

    @Override
    protected IBpmPluginDefine parseJson(JSONObject pluginJson) {
        String source = pluginJson.getString("source");

        CusersPluginDefine def = new CusersPluginDefine();
        def.setSource(source);

        if ("spec".equals(source)) {
            String accounts = pluginJson.getString("account");
            String fullName = pluginJson.getString("fullName");
            def.setAccount(accounts);
            def.setFullName(fullName);
        } else if ("var".equals(source)) {
            JSONObject var = pluginJson.getJSONObject("executorVar");
            ExecutorVar executorVar = (ExecutorVar) JSONObject.toBean(var, ExecutorVar.class);
            def.setExecutorVar(executorVar);
        } else if ("node".equals(source)) {
            String nodeId = pluginJson.getString("nodeId");
            String nodeName = pluginJson.getString("nodeName");
            def.setNodeId(nodeId);
            def.setNodeName(nodeName);
        }

        return def;
    }

}
