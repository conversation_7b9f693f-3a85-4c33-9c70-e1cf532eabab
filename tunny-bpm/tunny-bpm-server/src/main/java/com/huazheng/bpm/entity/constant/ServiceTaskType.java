package com.huazheng.bpm.entity.constant;

/**
 * 服务任务
 * <pre>
 *
 * 构建组：ibps-bpmn-api
 * 作者：simon cai
 * 邮箱:<EMAIL>
 * 日期:2017-6-22-上午11:37:12
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum ServiceTaskType {

    MESSAGE("message", "普通任务"),
    SCRIPT("script", "脚本任务"),
    SERVICE("service", "服务任务");

    // 键
    private String key = "";
    // 值
    private String value = "";

    // 构造方法
    ServiceTaskType(String key, String value) {
        this.key = key;
        this.value = value;
    }

    // =====getting and setting=====
    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return key;
    }

    /**
     * 通过key获取对象
     *
     * @param key
     * @return
     */
    public static ServiceTaskType fromKey(String key) {
        for (ServiceTaskType c : ServiceTaskType.values()) {
            if (c.getKey().equalsIgnoreCase(key)) {
                return c;
            }
        }
        throw new IllegalArgumentException(key);
    }
}
