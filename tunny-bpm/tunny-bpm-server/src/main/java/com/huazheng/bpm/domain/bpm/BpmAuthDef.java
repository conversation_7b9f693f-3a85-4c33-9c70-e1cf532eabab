package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmAuthDefDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmAuthDefPo;
import com.huazheng.bpm.util.db.UniqueIdUtil;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 流程授权定义 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-02-06 15:00:58
 * </pre>
 */
@SuppressWarnings("serial")
@Service
@Scope("prototype")
public class BpmAuthDef extends AbstractDomain<String, BpmAuthDefPo> {

    @Resource
    private BpmAuthDefDao bpmAuthDefDao;

    @Override
    protected void init() {
        this.setDao(bpmAuthDefDao);
    }

    /**
     * TODO方法名称描述
     *
     * @param authId
     * @param bpmAuthDefList
     */
    public void save(String authId, List<BpmAuthDefPo> bpmAuthDefList) {
        // 删除流程权限定义
        bpmAuthDefDao.delByAuthId(authId);
        // 添加权限定义
        for (BpmAuthDefPo bpmAuthDef : bpmAuthDefList) {
            bpmAuthDef.setId(UniqueIdUtil.getId());
            bpmAuthDef.setAuthId(authId);
            bpmAuthDefDao.create(bpmAuthDef);
        }
    }

    /**
     * 根据权限id删除权限定义
     *
     * @param ids_
     */
    public void delByAuthIds(String... ids_) {
        for (String authId : ids_) {
            bpmAuthDefDao.delByAuthId(authId);
        }
    }
}
