//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4-2
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.08.25 at 03:37:49 PM CST
//


package com.huazheng.bpm.entity.core;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for bpmdefxml complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="bpmdefxml">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="defxml" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="bpmnxml" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "bpmdefxml", propOrder = {
        "defxml",
        "bpmnxml"
})
public class Bpmdefxml {

    @XmlElement(required = true)
    protected String defxml;
    @XmlElement(required = true)
    protected String bpmnxml;

    /**
     * Gets the value of the defxml property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getDefxml() {
        return defxml;
    }

    /**
     * Sets the value of the defxml property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDefxml(String value) {
        this.defxml = value;
    }

    /**
     * Gets the value of the bpmnxml property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getBpmnxml() {
        return bpmnxml;
    }

    /**
     * Sets the value of the bpmnxml property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBpmnxml(String value) {
        this.bpmnxml = value;
    }

}
