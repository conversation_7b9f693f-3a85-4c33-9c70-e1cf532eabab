/**
 * 描述：触发流程实体参数映射-接口
 * 包名：com.huazheng.bpm.api.bpmn.model.node
 * 文件名：ITrigerParam.java
 * 作者：eddy
 * 日期：2017年8月25日-上午8:25:38
 * 版权：广州流辰信息技术有限公司版权所有
 */
package com.huazheng.bpm.model.node;

/**
 * 触发流程实体参数映射-接口
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2017年8月25日-上午8:25:38
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface ITrigerParam {

    /**
     * 主流程属性
     *
     * @param srcAttr
     */
    void setSrcAttr(String srcAttr);

    String getSrcAttr();

    /**
     * 触发流程属性
     *
     * @param destAttr
     */
    void setDestAttr(String destAttr);

    String getDestAttr();

    /**
     * 主流程属性名
     *
     * @return
     */
    String getSrcAttrName();

    void setSrcAttrName(String srcAttrName);

    /**
     * 触发流程属性名
     *
     * @return
     */
    String getDestAttrName();

    void setDestAttrName(String destAttrName);

    /**
     * 是否允许为空
     *
     * @param allowEmpty
     */
    void setAllowEmpty(String allowEmpty);

    String getAllowEmpty();

}
