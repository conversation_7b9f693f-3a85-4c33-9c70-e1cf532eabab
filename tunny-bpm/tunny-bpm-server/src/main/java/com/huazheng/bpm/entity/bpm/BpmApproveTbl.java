package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;


/**
 * 流程审批意见
 * 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：luodx
 * 邮箱地址：<EMAIL>
 * 创建时间：2016-12-12 15:53:51
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmApproveTbl extends AbstractPo<String> {
    protected String id;        /*意见ID*/
    protected String procDefId;        /*流程定义ID*/
    protected String supInstId;        /*父流程实例ID*/
    protected String procInstId;        /*PROC_INST_ID_*/
    protected String taskKey;        /*任务定义Key*/
    protected String taskId;        /*任务ID*/
    protected String taskName;        /*任务名称*/
    protected String token;        /*任务令牌*/
    protected String qualfieds;        /*有审批资格用户ID串*/
    protected String auditor;        /*执行人ID*/
    protected String opinion;        /*审批意见*/
    protected String status;        /*审批状态。start=发起流程；create=任务创建；agree=同意；against=反对；return=驳回；abandon=弃权；retrieve=追回*/
    protected java.util.Date createTime;        /*执行开始时间*/
    protected java.util.Date assignTime;        /*任务分配用户时间*/
    protected java.util.Date completeTime;        /*结束时间*/
    protected Long durMs;        /*持续时间(ms)*/
    protected Short interpose;        /*是否干预*/
    protected String signName;        /*执行人签章*/
    protected String phone;        /*执行人联系电话*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 意见ID
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    /**
     * 返回 流程定义ID
     *
     * @return
     */
    public String getProcDefId() {
        return this.procDefId;
    }

    public void setSupInstId(String supInstId) {
        this.supInstId = supInstId;
    }

    /**
     * 返回 父流程实例ID
     *
     * @return
     */
    public String getSupInstId() {
        return this.supInstId;
    }

    public void setProcInstId(String procInstId) {
        this.procInstId = procInstId;
    }

    /**
     * 返回 PROC_INST_ID_
     *
     * @return
     */
    public String getProcInstId() {
        return this.procInstId;
    }

    public void setTaskKey(String taskKey) {
        this.taskKey = taskKey;
    }

    /**
     * 返回 任务定义Key
     *
     * @return
     */
    public String getTaskKey() {
        return this.taskKey;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    /**
     * 返回 任务ID
     *
     * @return
     */
    public String getTaskId() {
        return this.taskId;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    /**
     * 返回 任务名称
     *
     * @return
     */
    public String getTaskName() {
        return this.taskName;
    }

    public void setToken(String token) {
        this.token = token;
    }

    /**
     * 返回 任务令牌
     *
     * @return
     */
    public String getToken() {
        return this.token;
    }

    public void setQualfieds(String qualfieds) {
        this.qualfieds = qualfieds;
    }

    /**
     * 返回 有审批资格用户ID串
     *
     * @return
     */
    public String getQualfieds() {
        return this.qualfieds;
    }

    public void setAuditor(String auditor) {
        this.auditor = auditor;
    }

    /**
     * 返回 执行人ID
     *
     * @return
     */
    public String getAuditor() {
        return this.auditor;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    /**
     * 返回 审批意见
     *
     * @return
     */
    public String getOpinion() {
        return this.opinion;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 返回 审批状态。start=发起流程；create=任务创建；agree=同意；against=反对；return=驳回；abandon=弃权；retrieve=追回
     *
     * @return
     */
    public String getStatus() {
        return this.status;
    }

    public void setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 返回 执行开始时间
     *
     * @return
     */
    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public void setAssignTime(java.util.Date assignTime) {
        this.assignTime = assignTime;
    }

    /**
     * 返回 任务分配用户时间
     *
     * @return
     */
    public java.util.Date getAssignTime() {
        return this.assignTime;
    }

    public void setCompleteTime(java.util.Date completeTime) {
        this.completeTime = completeTime;
    }

    /**
     * 返回 结束时间
     *
     * @return
     */
    public java.util.Date getCompleteTime() {
        return this.completeTime;
    }

    public void setDurMs(Long durMs) {
        this.durMs = durMs;
    }

    /**
     * 返回 持续时间(ms)
     *
     * @return
     */
    public Long getDurMs() {
        return this.durMs;
    }

    public void setInterpose(Short interpose) {
        this.interpose = interpose;
    }

    /**
     * 返回 是否干预
     *
     * @return
     */
    public Short getInterpose() {
        return this.interpose;
    }

    public String getSignName() {
        return signName;
    }

    public void setSignName(String signName) {
        this.signName = signName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("procDefId", this.procDefId)
                .append("supInstId", this.supInstId)
                .append("procInstId", this.procInstId)
                .append("taskKey", this.taskKey)
                .append("taskId", this.taskId)
                .append("taskName", this.taskName)
                .append("token", this.token)
                .append("qualfieds", this.qualfieds)
                .append("auditor", this.auditor)
                .append("opinion", this.opinion)
                .append("status", this.status)
                .append("createTime", this.createTime)
                .append("assignTime", this.assignTime)
                .append("completeTime", this.completeTime)
                .append("durMs", this.durMs)
                .append("interpose", this.interpose)
                .toString();
    }
}
