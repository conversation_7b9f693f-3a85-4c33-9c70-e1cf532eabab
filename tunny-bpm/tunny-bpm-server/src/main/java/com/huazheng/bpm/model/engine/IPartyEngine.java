package com.huazheng.bpm.model.engine;


import com.huazheng.bpm.service.IPartyEntityService;
import com.huazheng.bpm.service.IPartyUserService;

/**
 * 参与者架构引擎服务类。
 *
 * <pre>
 * 构建组：ibps-api-org
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年9月20日-下午2:37:58
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface IPartyEngine {

    /**
     * 返回参与者服务类
     *
     * @return
     */
    IPartyEntityService getPartyEntityService();

    /**
     * 返回用户服务类
     *
     * @return
     */
    IPartyUserService getPartyUserService();
}
