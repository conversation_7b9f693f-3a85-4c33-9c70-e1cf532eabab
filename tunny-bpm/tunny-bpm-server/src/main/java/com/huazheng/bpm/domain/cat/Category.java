package com.huazheng.bpm.domain.cat;

import com.huazheng.bpm.dao.cat.CategoryDao;
import com.huazheng.bpm.dao.cat.CategoryQueryDao;
import com.huazheng.bpm.dao.cat.TypeQueryDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.cat.CategoryPo;
import com.huazheng.bpm.entity.cat.TypePo;
import com.huazheng.bpm.util.core.BeanUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 分类领域驱动类
 * <pre>
 * 构建组：ibps-common-biz
 * 作者：simon cai
 * 邮箱：<EMAIL>
 * 日期：2016年9月19日-上午9:43:00
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
@Transactional
@Scope("prototype")
public class Category extends AbstractDomain<String, CategoryPo> {
    @Resource
    private CategoryDao categoryDao;
    @Resource
    private TypeQueryDao typeQueryDao;
    @Resource
    private CategoryQueryDao categoryQueryDao;
    @Resource
    private Type typeDomain;

    @Override
    protected void init() {
        this.setDao(categoryDao);
    }

    public void delCascadeByIds(String[] aryIds) {
        for (String id : aryIds) {
            CategoryPo category = categoryQueryDao.get(id);
            //如果是系统默认的就不能删除
            if (BeanUtils.isEmpty(category) || category.getFlag().equals(CategoryPo.FLAG_Y)) {
                continue;
            }
            List<TypePo> typeList = typeQueryDao.findByCategoryKey(category.getCategoryKey());
            for (TypePo type : typeList) {
                typeDomain.delCascadeById(type.getId());
            }
            categoryDao.delete(id);
        }
    }
}
