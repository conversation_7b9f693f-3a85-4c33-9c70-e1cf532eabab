package com.huazheng.bpm.entity.constant;

import javax.xml.namespace.QName;

/**
 * BPM常用变量。
 *
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：simon cai
 * 邮箱:<EMAIL>
 * 日期:2015-4-1-下午5:54:18
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface BpmConstants {

    String TRUE = "true";

    String FALSE = "false";

    String NO = "no";

    String YES = "yes";

    String PROCDEF_PREFIX = "procdef_";

    /**
     * 流程实例标题。
     */
    String SUBJECT = "subject_";
    /**
     * 业务主键。
     */
    String BUSINESS_KEY = "businessKey_";

    /**
     * 发起人
     */
    String START_USER = "startUser";

    /**
     * bpm系统产生的实例ID.
     */
    String PROCESS_INST_ID = "instanceId_";

    /**
     * 任务执行人。
     */
    String ASIGNEE = "assignee";

    /**
     * 父实例ID。
     */
    String PROCESS_PARENT_INST_ID = "parentInstanceId_";

    String PROCESS_DEF_ID = "processDefId_";

    // 是否子流程实例。
    String IS_SUB_PROCESS = "isSubProcess_";

    // 会签用户变量。
    String SIGN_USERIDS = "signUsers_";

    /**
     * 流程会签结果。
     */
    String SIGN_RESULT = "signResult_";

    /**
     * 直接会签，需要手会签规则限制，
     * 这个放到TaskFinishCmd的getTransitVars方法获取。
     */
    String SIGN_DIRECT = "signDirect";

    /**
     * 保存外部子流程变量。
     */
    String CALL_ACTIVITI_VARS = "callActivityVars_";

    // 空用户。
    String EmptyUser = "0";

    /**
     * 多实例常量。
     */
    String MULTI_INSTANCE = "multiInstance";

    /**
     * 多实例串行。
     */
    String MULTI_INSTANCE_SEQUENTIAL = "sequential";

    String SIGN_BATCH = "signBatch_";

    /**
     * 多实例并行。
     */
    String MULTI_INSTANCE_PARALLEL = "parallel";
    /**
     * 多实例的个数。
     */
    String NUMBER_OF_INSTANCES = "nrOfInstances";
    /**
     * 多实例活动的个数。
     */
    String NUMBER_OF_ACTIVE_INSTANCES = "nrOfActiveInstances";
    /**
     * 多实例完成的个数。
     */
    String NUMBER_OF_COMPLETED_INSTANCES = "nrOfCompletedInstances";
    /**
     * 流程实例顺序。
     */
    String NUMBER_OF_LOOPCOUNTER = "loopCounter";

    /**
     * 流程的EXECUTIONID。
     */
    String BPMN_EXECUTION_ID = "bmpnExecutionId";

    /**
     * 流程实例ID。
     */
    String BPMN_INST_ID = "bmpnInstId";

    /**
     * 流程KEY。
     */
    String BPM_FLOW_KEY = "flowKey_";

    /**
     * 表单标识。
     */
    String BPM_FORM_IDENTITY = "form_identity_";


    /**
     * 执行任务拦截对象名称
     */
    String BPM_RESULT = "bpm_result_";

    /**
     * 执行任务类型
     */
    String BPM_FLOW_EXECUTE_TYPE = "bpm_flow_excute_type";

    /**
     * 执行任务类型:启动流程
     */
    String BPM_FLOW_START = "bpm_flow_start_";

    /**
     * 执行任务类型:执行下一步(审批)
     */
    String BPM_FLOW_NEXT = "bpm_flow_next_";

    String BPM_SKIP_TYPE = "skipType_";

    /**
     * 流程节点用户，用于存直接指定流程的人员。
     */
    String BPM_NODE_USERS = "bpm_node_users_";
    /**
     * 指定跳过第一个节点后第二节点执行人
     */
    String SEC_BPM_NODE_USERS = "sec_bpm_node_users_";

    /**
     * 流程实例。
     */
    String PROCESS_INST = "processInstance";

    /**
     * local。
     */
    String LOCAL = "local_";

    /**
     * 流程任务。
     */
    String BPM_TASK = "bpmTask_";
    /**
     * 命名空间。
     */
    String BPM_XMLNS = "http://www.bpmhome.cn/bpm";

    QName _Order_QNAME = new QName("http://www.bpmhome.cn/BPMN20EXT", "order");

    String TOKEN_NAME = "token_";

    String TOKEN_PRE = "T_";

    /**
     * 回退模式。
     */
    String BACK_HAND_MODE = "backHandMode";

    //系统用户Id(用于定时任务)
    String SYSTEM_USER_ID = "-1";
    //系统用户(用于定时任务)
    String SYSTEM_USER_NAME = "系统";
    /**
     * bo初始化中的bo字段
     */
    String BO_FIELD_PATH = "bo_field_path_";

    /**
     * bo实例
     */
    String BO_INST = "bo_inst_";

    /**
     * 父级堆栈ID。
     */
    String PARENT_STACK = "parentStack";

    /**
     * 父级执行信息
     */
    String PARENT_EXEC = "parentExec_";

    /**
     * 当前执行ID
     */
    String CURR_EXEC_ID = "currExecId_";
    /**
     * 父级执行ID
     */
    String PARENT_EXEC_ID = "parentExecId_";
    /**
     * 流程父级执行ID
     */
    String PROC_PARENT_EXEC_ID = "procParentExecId_";
    /**
     * 目标节点ID
     */
    String TARGET_NODE_ID = "targetNodeId_";

    String CUR_USER = "curUser";

    String CUR_USER_NAME = "curUserName";

    String PREV_USER = "prevUser";
    /**
     * 任务标志
     */
    String USER_TASK = "userTask";

    /**
     * 用户
     */
    String USER_ID = "userId";
    String USER_NAME = "userName";
    String USER_ACCOUNT = "account";

    /**
     * 组织
     */
    String GROUP_ID = "groupId";
    String GROUP_NAME = "groupName";
    String GROUP_TYPE = "groupType";

    String ORG_ID = "orgId";
    String ORG_NAME = "orgName";
    String ORG_LEVEL = "orgLevel";
    String ORG_TYPE = "orgType";

    String PARTY_ID = "partyId";
    String PARTY_NAME = "partyName";
    String PARTY_TYPE = "partyType";

}
