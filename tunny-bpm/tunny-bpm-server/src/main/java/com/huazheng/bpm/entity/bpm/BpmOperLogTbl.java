package com.huazheng.bpm.entity.bpm;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huazheng.bpm.entity.framework.AbstractPo;
import com.huazheng.bpm.util.core.StringPool;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.hibernate.validator.constraints.NotBlank;

import java.util.Date;

/**
 * 流程操作日志 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-04-21 11:18:52
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmOperLogTbl extends AbstractPo<String> {
    protected String id;        /*ID*/
    @NotBlank(message = "com.lc.ibps.bpmn.provider.public.procDefId")
    protected String procDefId;        /*流程定义ID*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmOperLogTbl.procDefKey")
    protected String procDefKey;        /*流程业务主键*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmOperLogTbl.nodeId")
    protected String nodeId;        /*任务节点ID*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmOperLogTbl.procInstId")
    protected String procInstId;        /*流程实例ID*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmOperLogTbl.procInstSubject")
    protected String procInstSubject;        /*事项标题*/
    protected String taskId;        /*任务ID*/
    protected String option;        /*操作意见*/
    protected String operType;        /*操作类型*/
    protected String interpose;        /*是否干预*/
    protected String content;        /*操作内容*/
    protected String createBy;        /*操作人*/
    @JsonFormat(pattern = StringPool.DATE_FORMAT_DATETIME)
    protected Date createTime;        /*操作时间*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 ID
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    /**
     * 返回 流程定义ID
     *
     * @return
     */
    public String getProcDefId() {
        return this.procDefId;
    }

    public void setProcDefKey(String procDefKey) {
        this.procDefKey = procDefKey;
    }

    /**
     * 返回 流程业务主键
     *
     * @return
     */
    public String getProcDefKey() {
        return this.procDefKey;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    /**
     * 返回 任务节点ID
     *
     * @return
     */
    public String getNodeId() {
        return this.nodeId;
    }

    public void setProcInstId(String procInstId) {
        this.procInstId = procInstId;
    }

    /**
     * 返回 流程实例ID
     *
     * @return
     */
    public String getProcInstId() {
        return this.procInstId;
    }

    public void setProcInstSubject(String procInstSubject) {
        this.procInstSubject = procInstSubject;
    }

    /**
     * 返回 事项标题
     *
     * @return
     */
    public String getProcInstSubject() {
        return this.procInstSubject;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    /**
     * 返回 任务ID
     *
     * @return
     */
    public String getTaskId() {
        return this.taskId;
    }

    public void setOption(String option) {
        this.option = option;
    }

    /**
     * 返回 操作意见
     *
     * @return
     */
    public String getOption() {
        return this.option;
    }

    public void setOperType(String operType) {
        this.operType = operType;
    }

    /**
     * 返回 操作类型
     *
     * @return
     */
    public String getOperType() {
        return this.operType;
    }

    public void setInterpose(String interpose) {
        this.interpose = interpose;
    }

    /**
     * 返回 是否干预
     *
     * @return
     */
    public String getInterpose() {
        return this.interpose;
    }

    public void setContent(String content) {
        this.content = content;
    }

    /**
     * 返回 操作内容
     *
     * @return
     */
    public String getContent() {
        return this.content;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     * 返回 操作人
     *
     * @return
     */
    public String getCreateBy() {
        return this.createBy;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 返回 操作时间
     *
     * @return
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("procDefId", this.procDefId)
                .append("procDefKey", this.procDefKey)
                .append("nodeId", this.nodeId)
                .append("procInstId", this.procInstId)
                .append("procInstSubject", this.procInstSubject)
                .append("taskId", this.taskId)
                .append("option", this.option)
                .append("operType", this.operType)
                .append("interpose", this.interpose)
                .append("content", this.content)
                .append("createBy", this.createBy)
                .append("createTime", this.createTime)
                .toString();
    }
}
