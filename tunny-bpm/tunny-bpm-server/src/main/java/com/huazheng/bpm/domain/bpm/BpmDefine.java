package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.*;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.*;
import com.huazheng.bpm.entity.constant.NodeType;
import com.huazheng.bpm.entity.inst.IBpmProcInst;
import com.huazheng.bpm.event.BpmDefineDelEvent;
import com.huazheng.bpm.exception.BoBaseException;
import com.huazheng.bpm.handler.AttributesBpmnNodeXmlHandler;
import com.huazheng.bpm.model.define.BpmDefineAttributes;
import com.huazheng.bpm.model.define.IBpmDefine;
import com.huazheng.bpm.model.form.BpmFormPermissionVo;
import com.huazheng.bpm.model.node.Attribute;
import com.huazheng.bpm.model.setting.BpmDefineSetting;
import com.huazheng.bpm.repository.BpmAuthRepository;
import com.huazheng.bpm.repository.BpmDefineRepository;
import com.huazheng.bpm.repository.BpmTaskReminderRepository;
import com.huazheng.bpm.repository.BpmTrigerFlowRepository;
import com.huazheng.bpm.service.DefTransform;
import com.huazheng.bpm.service.NatProDefineService;
import com.huazheng.bpm.util.base.*;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.core.StringPool;
import com.huazheng.bpm.util.db.UniqueIdUtil;
import com.huazheng.bpm.util.helper.BpmDefineSettinglBuilder;
import com.huazheng.bpm.util.helper.RightsDefBuilder;
import com.huazheng.bpm.util.json.JsonUtil;
import com.huazheng.bpm.util.string.StringUtil;
import com.huazheng.bpm.util.web.ContextUtil;
import org.dom4j.Document;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 流程定义
 * 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2016-12-09 16:33:01
 * </pre>
 */
@SuppressWarnings("serial")
@Transactional
@Scope("prototype")
@Service
public class BpmDefine extends AbstractDomain<String, BpmDefinePo> {
    private Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private BpmDefineDao bpmDefineDao;
    @Resource
    private CacheKeyGenerator cacheKeyGenerator;
    @Resource
    private ICache iCache;
    @Resource
    private BpmDefineRepository bpmDefineRepository;
    @Resource
    private BpmTaskReminderDao bpmTaskReminderDao;
    @Resource
    private BpmTaskReminderRepository bpmTaskReminderRepository;
    @Resource
    private BpmTrigerFlowRepository bpmTrigerFlowRepository;
    @Resource
    private BpmTaskReminder bpmTaskReminderDomain;
    @Resource
    private BpmAuthRepository bpmAuthRepository;
    @Resource
    private BpmAuth bpmAuthDomain;
    @Resource
    private BpmDefineXmlDao bpmDefineXmlDao;
    @Resource
    private BpmDefineXmlQueryDao bpmDefineXmlQueryDao;
    @Resource
    private NatProDefineService natProDefineService;
    @Resource
    private DefXmlTransForm defXmlTransForm;
    @Resource
    private BpmInst bpmIstDomain;
    @Resource
    private BpmInstDao bpmInstDao;
    @Resource
    private BpmInstQueryDao bpmInstQueryDao;
    private AttributesBpmnNodeXmlHandler attributesBpmnNodeXmlHandler;

    protected void init() {
        bpmDefineDao = AppUtil.getBean(BpmDefineDao.class);
        this.setDao(bpmDefineDao);
    }

    /**
     * 流程发布
     *
     * @param bpmDefinition
     * @return
     */
    public boolean deploy() {
        IBpmDefine bpmDefine = getData();
        // 数据有效性判断
        if (!isAvailable(bpmDefine)) {
            return false;
        }

        // 如果是新的流程定义标识
        boolean isNewDef = StringUtil.isEmpty(bpmDefine.getStatus());
        //是否为草稿
        boolean isDraft = false;
        //旧的bpmnXml数据
        String oldBpmnXml = "";
        if (isNewDef) {
            // 判断defKey是否存在，存在则不允许发布
            BpmDefinePo mainBpmDefine = bpmDefineRepository.getByDefKey(bpmDefine.getDefKey(), false);
            if (mainBpmDefine != null) {
                return false;
            }
        } else {
            BpmDefinePo tempDef = bpmDefineRepository.get(bpmDefine.getDefId());
            oldBpmnXml = tempDef.getBpmnXml();
            if (tempDef != null && StringUtil.isEmpty(tempDef.getBpmnDefId())) {
                isDraft = true;
            }
        }

        // 调用native的xml转换接口，转换defXml为bpmnXml
        bpmDefine.setDefXml(this.transXmlns(bpmDefine.getDefXml()));
        String bpmnXml = this.convertBpmnXmlByDesignFile(bpmDefine);
        if (StringUtil.isEmpty(bpmnXml)) {
            return false;
        }
        //更新插件
        bpmnXml = this.updateBpmnXmlPlugins(bpmnXml, oldBpmnXml);

        // 调用native接口发布流程
        String deployId = null;
        String bpmnDefId = null;
        try {
            deployId = natProDefineService.deploy("", bpmDefine.getName(), bpmnXml);
            bpmnDefId = natProDefineService.getProcessDefinitionIdByDeployId(deployId);
        } catch (UnsupportedEncodingException e) {
            return false;
        }

        BpmDefinePo def = (BpmDefinePo) bpmDefine;
        // 根据该流程是否已持久化做分支处理
        if (isNewDef) { // 新流程定义
            def.setVersion(1);
            def.setMainDefId(bpmDefine.getDefId());
            def.setIsMain("Y");
            def.setStatus(IBpmDefine.STATUS.DEPLOY);
            def.setTestStatus(IBpmDefine.TEST_STATUS.TEST);

            def.setBpmnDefId(bpmnDefId);
            def.setBpmnDeployId(deployId);
            def.setBpmnXml(bpmnXml);
            def.setUpdateTime(new Date());
            this.setData(def);
            //保存
            this.create();
        } else if (isDraft) {//若从草稿进行发布，即需要更新该定义
            def.setStatus(IBpmDefine.STATUS.DEPLOY);
            def.setTestStatus(IBpmDefine.TEST_STATUS.TEST);
            def.setVersion(1);
            def.setMainDefId(bpmDefine.getDefId());
            def.setIsMain("Y");
            def.setBpmnDefId(bpmnDefId);
            def.setBpmnDeployId(deployId);
            def.setBpmnXml(bpmnXml);
            def.setUpdateTime(new Date());
            this.setData(def);
            this.update();
        } else {//发布新版本
            BpmDefinePo oldBpmDefinition = (BpmDefinePo) bpmDefine;

            oldBpmDefinition.setStatus(IBpmDefine.STATUS.DEPLOY);
            oldBpmDefinition.setTestStatus(IBpmDefine.TEST_STATUS.TEST);

            oldBpmDefinition.setBpmnDefId(bpmnDefId);
            oldBpmDefinition.setBpmnDeployId(deployId);
            oldBpmDefinition.setBpmnXml(bpmnXml);
            oldBpmDefinition.setUpdateTime(new Date());
            this.setData(oldBpmDefinition);
            //根据旧的流程定义克隆一份，并将新的流程定义作为主版本
            this.cloneToMain();
        }

        return true;
    }

    /**
     * 更新流程定义xml
     *
     * @param defId
     * @param defXml
     * @return
     */
    public String updateBpmDefXml(String defId, String defXml) {
        BpmDefinePo bpmDefine = bpmDefineRepository.get(defId);
        BpmDefineXmlPo bpmDefineXml = bpmDefine.getBpmDefineXmlPo();
        if (bpmDefineXml == null) return null;

        bpmDefineXml.setDefXml(this.transXmlns(defXml));
        DefTransform trans = natProDefineService.getDefTransform();
        String bpmnDefXml = trans.convert(bpmDefine.getDefKey(), bpmDefine.getName(), this.transXmlns(defXml));
        bpmDefineXml.setBpmnXml(bpmnDefXml);
        bpmDefineXmlDao.update(bpmDefineXml);
        //清理缓存
        publishEvent(bpmDefine);
        return bpmnDefXml;
    }

    /**
     * 更新流程发布xml
     *
     * @param defId
     * @param bpmnXml
     * @return
     */
    public void updateBpmnXml(String defId, String bpmnXml) {
        BpmDefineXmlPo bpmDefineXml = bpmDefineXmlQueryDao.get(defId);
        if (bpmDefineXml == null) return;

        bpmDefineXml.setBpmnXml(bpmnXml);
        updateBpmXmlPo(bpmDefineXml);
    }

    /**
     * 更新流程xml
     *
     * @param bpmXmlPo
     */
    public void updateBpmXmlPo(BpmDefineXmlPo bpmXmlPo) {
        BpmDefinePo bpmDefinePo = bpmDefineRepository.get(bpmXmlPo.getId());

        bpmDefineXmlDao.update(bpmXmlPo);
        // 更新activiti的XML
        String deployId = bpmDefinePo.getBpmnDeployId();
        natProDefineService.writeDefXml(deployId, bpmXmlPo.getBpmnXml());
        //清理缓存
        publishEvent(bpmDefinePo);
    }

    /**
     * 发布流程时，更新版本号
     *
     * @param entityId 实体id
     */
    public void updateMainVersion(String entityId) {
        IBpmDefine def = bpmDefineRepository.get(entityId);
        bpmDefineDao.updateToMain(entityId);
        AppUtil.publishEvent(new BpmDefineDelEvent(def));
    }

    /**
     * 更新流程状态
     *
     * @param defId
     * @param status
     */
    public void updateStatus(String defId, String status) {
        bpmDefineDao.updateStatus(defId, status);
    }

    /**
     * 更新流程定义
     *
     * @param bpmDefine
     * @return
     */
    public boolean update(IBpmDefine bpmDefine) {
        // 进行数据有效性判断
        if (!isAvailable(bpmDefine)) return false;

        // 调用native的xml转换接口，转换defXml为bpmnXml
        bpmDefine.setDefXml(this.transXmlns(bpmDefine.getDefXml()));
        String bpmnXml = convertBpmnXmlByDesignFile(bpmDefine);
        if (StringUtil.isEmpty(bpmnXml)) {
            return false;
        }

        //更新插件
        BpmDefinePo oldBpmDefine = bpmDefineRepository.get(bpmDefine.getDefId());
        String oldBpmnXml = oldBpmDefine.getBpmnXml();
        bpmnXml = updateBpmnXmlPlugins(bpmnXml, oldBpmnXml);

        // 保存流程
        BpmDefinePo def = (BpmDefinePo) bpmDefine;
        // 合并配置，防止旧版本配置数据丢失
        bpmnXml = mergeBpmnXml(bpmnXml, oldBpmnXml);
        def.setBpmnXml(bpmnXml);
        //确定是否已经发布，为空则为未发布
        if (StringUtil.isNotEmpty(bpmDefine.getBpmnDeployId())) {
            //发布流程引擎中的定义
            natProDefineService.writeDefXml(bpmDefine.getBpmnDeployId(), bpmnXml);
        }

        this.setData(def);
        this.update();

        // 催办数据
        this.updateReminders(getId(), null, bpmnXml);

        return true;
    }

    /**
     * 保存草稿
     *
     * @param bpmDefine
     * @return
     */
    public boolean saveDraft(IBpmDefine bpmDefine) {
        //先判断是否可以处理
        if (bpmDefine == null
                || StringUtil.isEmpty(bpmDefine.getDefKey())) {
            return false;
        }

        BpmDefinePo def = (BpmDefinePo) bpmDefine;
        bpmDefine.setDefXml(this.transXmlns(bpmDefine.getDefXml()));
        String bpmnXml = convertBpmnXmlByDesignFile(bpmDefine);
        def.setBpmnXml(bpmnXml);
        def.setIsMain("Y");
        def.setTestStatus(IBpmDefine.TEST_STATUS.TEST);
        //草稿默认为版本0
        def.setVersion(0);
        def.setMainDefId(bpmDefine.getDefId());
        def.setUpdateTime(new Date());

        setData(def);
        if (StringUtil.isEmpty(def.getStatus())) {
            def.setStatus(IBpmDefine.STATUS.DRAFT);
            this.create();
        } else {
            this.update();
        }

        return true;
    }

    /**
     * 更新流程类型
     *
     * @param typeId
     * @param defIds
     */
    public void updateDefineType(String typeId, List<String> defIds) {
        if (BeanUtils.isEmpty(defIds)) return;

        bpmDefineDao.updateDefineType(typeId, defIds);
    }

    /**
     * 保存扩展属性
     *
     * @param defId
     * @param attributes
     */
    public void saveProperties(String defId, BpmDefineAttributes attributes) {
        String status = attributes.getStatus();

        attributesBpmnNodeXmlHandler = AppUtil.getBean(AttributesBpmnNodeXmlHandler.class);

        attributesBpmnNodeXmlHandler.saveBpmnNodeXml(defId, attributes.toList());
        // 更新测试状态
        BpmDefinePo bpmDefine = bpmDefineRepository.get(defId);

        // 更新状态
        String oldStatus = bpmDefine.getStatus();

        bpmDefine.setTestStatus(attributes.getTestStatus());
        bpmDefine.setStatus(status);
        this.setData(bpmDefine);
        //修改流程定义
        this.update();
        // 修改流程定义状态
        this.updateBpmDefineStatus(oldStatus);
    }

    /**
     * 根据定义id删除
     *
     * @param defId
     */
    public void remove(String defId) {
        IBpmDefine bpmDef = bpmDefineRepository.get(defId);
        publishEvent(bpmDef);

        //根据流程定义ID获取实例列表。
        List<BpmInstPo> instList = bpmInstQueryDao.findByDefId(defId);
        for (BpmInstPo inst : instList) {
            bpmInstDao.delete(inst.getId());
        }

        // 删除催办数据
        bpmTaskReminderDao.deleteByProcNodeId(defId, null);

        // 删除表单权限数据
        //formRightsService.removeByFlowKey(bpmDef.getDefKey());

        //删除流程引擎表
        removeActvitiByDefId(defId);
        //删除流程定义表
        bpmDefineDao.delete(defId);
    }

    /**
     * 根据定义id列表批量删除
     *
     * @param defIds
     */
    public void removeByDefIds(String... defIds) {
        if (BeanUtils.isEmpty(defIds)) return;

        for (String defId : defIds) {
            remove(defId);
        }
    }

    /**
     * 转换文件头，否则转换异常
     * 更新、发布、草稿都要转换，controller转换去掉
     */
    public String transXmlns(String defXml) {
        return defXml
                .replaceAll("xmlns=\"", "xns=\"")
                .replaceAll("<task", "<userTask")
                .replaceAll("</task", "</userTask");
    }

    /*################## 私有 ##################*/

    /**
     * 催办数据合并
     *
     * @param defId
     * @param oldDefId
     * @param bpmnXml
     */
    @SuppressWarnings("unchecked")
    private void updateReminders(String defId, String oldDefId, String bpmnXml) {
        String id_QName_ = "id";
        String process_QName_ = "process";

        Document newDoc = Dom4jUtil.loadXml(bpmnXml);
        Element newRootElement = newDoc.getRootElement();
        Element newProcessElement = newRootElement.element(process_QName_);
        List<Element> newElements = newProcessElement.elements();

        // 节点
        Set<String> nodeIdSetAll = new HashSet<String>();
        Set<String> nodeIdSet = new HashSet<String>();
        resolveReminders(id_QName_, newElements, nodeIdSetAll, nodeIdSet);

        if (BeanUtils.isEmpty(nodeIdSet)) {
            return;
        }

        removeReminders(defId, oldDefId, nodeIdSetAll, nodeIdSet);
    }

    /**
     * 解析催办数据
     *
     * @param id_QName_
     * @param newElements
     * @param nodeIdSetAll
     * @param nodeIdSet
     */
    @SuppressWarnings("unchecked")
    private void resolveReminders(String id_QName_, List<Element> newElements, Set<String> nodeIdSetAll,
                                  Set<String> nodeIdSet) {
        String nodeId;
        String nodeType;
        Element tmpNewElement;
        for (int i = 0, len = newElements.size(); i < len; i++) {
            tmpNewElement = newElements.get(i);
            nodeId = tmpNewElement.attributeValue(id_QName_);
            nodeType = tmpNewElement.getName();
            logger.debug("element name is <{}>,ID is <{}>.", nodeType, nodeId);
            if (StringUtil.isNotEmpty(nodeId)) {
                nodeIdSetAll.add(nodeId);
            }
            // 找出用户任务、会签任务节点
            if (NodeType.USERTASK.getKey().equals(nodeType)
                    || NodeType.SIGNTASK.getKey().equals(nodeType)) {
                nodeIdSet.add(nodeId);
            } else if (NodeType.SUBPROCESS.getKey().equals(nodeType)) {
                // 内部子流程合并催办
                List<Element> newSubElements = tmpNewElement.elements();
                for (int j = 0, jlen = newSubElements.size(); j < jlen; j++) {
                    resolveReminders(id_QName_, newSubElements, nodeIdSetAll, nodeIdSet);
                }
            }
        }
    }

    /**
     * 删除冗余数据，某些在流程图中移除的节点催办数据
     *
     * @param defId
     * @param oldDefId
     * @param nodeIdSetAll
     * @param nodeIdSet
     */
    private void removeReminders(String defId, String oldDefId, Set<String> nodeIdSetAll, Set<String> nodeIdSet) {
        List<BpmTaskReminderPo> reminders = null;
        if (StringUtil.isEmpty(oldDefId)) {
            reminders = bpmTaskReminderRepository.findByDefId(defId);
            if (BeanUtils.isEmpty(reminders)) {
                return;
            }

            removeReminders(nodeIdSetAll, nodeIdSet, reminders);
        } else {
            reminders = bpmTaskReminderRepository.findByDefId(oldDefId);
            if (BeanUtils.isEmpty(reminders)) {
                return;
            }

            removeReminders(defId, nodeIdSetAll, nodeIdSet, reminders);
        }
    }

    /**
     * 删除冗余数据，某些在流程图中移除的节点催办数据
     *
     * @param nodeIdSetAll
     * @param nodeIdSet
     * @param reminders
     */
    private void removeReminders(Set<String> nodeIdSetAll, Set<String> nodeIdSet, List<BpmTaskReminderPo> reminders) {
        // 删除冗余数据，某些在流程图中移除的节点催办数据
        for (BpmTaskReminderPo reminder : reminders) {
            if (nodeIdSet.contains(reminder.getNodeId()) && nodeIdSetAll.contains(reminder.getRelNodeId())) {
                continue;
            } else if (nodeIdSet.contains(reminder.getNodeId()) && !nodeIdSetAll.contains(reminder.getRelNodeId())) {
                reminder.setRelNodeId(reminder.getNodeId());
                bpmTaskReminderDao.create(reminder);
            }

            bpmTaskReminderDao.delete(reminder.getId());
        }
    }

    /**
     * 删除冗余数据，某些在流程图中移除的节点催办数据
     *
     * @param defId
     * @param nodeIdSetAll
     * @param nodeIdSet
     * @param reminders
     */
    private void removeReminders(String defId, Set<String> nodeIdSetAll, Set<String> nodeIdSet,
                                 List<BpmTaskReminderPo> reminders) {
        // 删除冗余数据，某些在流程图中移除的节点催办数据
        for (BpmTaskReminderPo reminder : reminders) {
            if (nodeIdSet.contains(reminder.getNodeId()) && nodeIdSetAll.contains(reminder.getRelNodeId())) {
                reminder.setId(UniqueIdUtil.getId());
                reminder.setProcDefId(defId);
                bpmTaskReminderDao.create(reminder);
            } else if (nodeIdSet.contains(reminder.getNodeId()) && !nodeIdSetAll.contains(reminder.getRelNodeId())) {
                reminder.setId(UniqueIdUtil.getId());
                reminder.setProcDefId(defId);
                reminder.setRelNodeId(reminder.getNodeId());
                bpmTaskReminderDao.create(reminder);
            }
        }
    }

    /* 触发流程数据合并 */

    /**
     * 触发流程数据合并
     *
     * @param defId
     * @param oldDefId
     * @param bpmnXml
     */
    @SuppressWarnings("unchecked")
    private void updateTrigerFlows(String defId, String oldDefId, String bpmnXml) {
        String id_QName_ = "id";
        String process_QName_ = "process";

        Document newDoc = Dom4jUtil.loadXml(bpmnXml);
        Element newRootElement = newDoc.getRootElement();
        Element newProcessElement = newRootElement.element(process_QName_);
        List<Element> newElements = newProcessElement.elements();

        // 节点
        Set<String> nodeIdSetAll = new HashSet<String>();
        Set<String> nodeIdSet = new HashSet<String>();
        resolveTrigerFlows(id_QName_, newElements, nodeIdSetAll, nodeIdSet);

        if (BeanUtils.isEmpty(nodeIdSet)) {
            return;
        }

        removeTrigerFlows(defId, oldDefId, nodeIdSetAll, nodeIdSet);
    }

    /**
     * 解析触发流程数据
     *
     * @param id_QName_
     * @param newElements
     * @param nodeIdSetAll
     * @param nodeIdSet
     */
    @SuppressWarnings("unchecked")
    private void resolveTrigerFlows(String id_QName_, List<Element> newElements, Set<String> nodeIdSetAll,
                                    Set<String> nodeIdSet) {
        String nodeId;
        String nodeType;
        Element tmpNewElement;
        for (int i = 0, len = newElements.size(); i < len; i++) {
            tmpNewElement = newElements.get(i);
            nodeId = tmpNewElement.attributeValue(id_QName_);
            nodeType = tmpNewElement.getName();
            logger.debug("element name is <{}>,ID is <{}>.", nodeType, nodeId);
            if (StringUtil.isNotEmpty(nodeId)) {
                nodeIdSetAll.add(nodeId);
            }
            // 找出用户任务、会签任务节点
            if (NodeType.USERTASK.getKey().equals(nodeType)
                    || NodeType.SIGNTASK.getKey().equals(nodeType)) {
                nodeIdSet.add(nodeId);
            } else if (NodeType.SUBPROCESS.getKey().equals(nodeType)) {
                // 内部子流程合并触发流程
                List<Element> newSubElements = tmpNewElement.elements();
                for (int j = 0, jlen = newSubElements.size(); j < jlen; j++) {
                    resolveTrigerFlows(id_QName_, newSubElements, nodeIdSetAll, nodeIdSet);
                }
            }
        }
    }

    /**
     * 删除冗余数据，某些在流程图中移除的节点触发流程数据
     *
     * @param defId
     * @param oldDefId
     * @param nodeIdSetAll
     * @param nodeIdSet
     */
    private void removeTrigerFlows(String defId, String oldDefId, Set<String> nodeIdSetAll, Set<String> nodeIdSet) {
        List<BpmTrigerFlowPo> trigerFlows = null;
        if (StringUtil.isEmpty(oldDefId)) {
            trigerFlows = bpmTrigerFlowRepository.findByDefId(defId);
            if (BeanUtils.isEmpty(trigerFlows)) {
                return;
            }

            removeTrigerFlows(nodeIdSetAll, nodeIdSet, trigerFlows);
        } else {
            trigerFlows = bpmTrigerFlowRepository.findByDefId(oldDefId);
            if (BeanUtils.isEmpty(trigerFlows)) {
                return;
            }

            removeTrigerFlows(defId, nodeIdSetAll, nodeIdSet, trigerFlows);
        }
    }

    /**
     * 删除冗余数据，某些在流程图中移除的节点触发流程数据
     *
     * @param nodeIdSetAll
     * @param nodeIdSet
     * @param
     */
    private void removeTrigerFlows(Set<String> nodeIdSetAll, Set<String> nodeIdSet, List<BpmTrigerFlowPo> trigerFlows) {
        // 删除冗余数据，某些在流程图中移除的节点触发流程数据
        for (BpmTrigerFlowPo trigerFlow : trigerFlows) {
            if (nodeIdSet.contains(trigerFlow.getNodeId())) {
                continue;
            }

            BpmTrigerFlow bpmTrigerFlowDomain = bpmTrigerFlowRepository.newInstance();
            bpmTrigerFlowDomain.deleteByIdsCascade(new String[]{trigerFlow.getId()});
        }
    }

    /**
     * 删除冗余数据，某些在流程图中移除的节点触发流程数据
     *
     * @param defId
     * @param nodeIdSetAll
     * @param nodeIdSet
     * @param
     */
    private void removeTrigerFlows(String defId, Set<String> nodeIdSetAll, Set<String> nodeIdSet,
                                   List<BpmTrigerFlowPo> trigerFlows) {
        // 删除冗余数据，某些在流程图中移除的节点触发流程数据
        for (BpmTrigerFlowPo trigerFlow : trigerFlows) {
            if (nodeIdSet.contains(trigerFlow.getNodeId())) {
                trigerFlow.setId(UniqueIdUtil.getId());
                trigerFlow.setDefId(defId);
                BpmTrigerFlow bpmTrigerFlowDomain = bpmTrigerFlowRepository.newInstance(trigerFlow);
                bpmTrigerFlowDomain.saveCascade();
            }
        }
    }

    /**
     * 流程发布xml配置合并
     *
     * @param newBpmnXml
     * @param oldBpmnXml
     * @return
     */
    private String mergeBpmnXml(String newBpmnXml, String oldBpmnXml) {
        String id_QName_ = "id";
        String process_QName_ = "process";
        String conditionExpression_QName_ = "conditionExpression";
        String extension_QName_ = "extensionElements";

        Document newDoc = Dom4jUtil.loadXml(newBpmnXml);
        Element newRootElement = newDoc.getRootElement();
        Element newProcessElement = newRootElement.element(process_QName_);
        String newProcessId = newProcessElement.attributeValue(id_QName_);

        Document oldDoc = Dom4jUtil.loadXml(oldBpmnXml);
        Element oldRootElement = oldDoc.getRootElement();
        Element oldProcessElement = oldRootElement.element(process_QName_);
        String oldProcessId = oldProcessElement.attributeValue(id_QName_);

        if (newProcessId.equals(oldProcessId)) {
            mergeBpmnXml(id_QName_, NodeType.SUBPROCESS.getKey(),
                    conditionExpression_QName_, extension_QName_,
                    newProcessElement, oldProcessElement);
        }

        return newDoc.asXML();
    }

    /**
     * 流程发布xml配置合并
     *
     * @param id_QName_
     * @param sub_process_QName_
     * @param conditionExpression_QName_
     * @param extension_QName_
     * @param newProcessElement
     * @param oldProcessElement
     */
    @SuppressWarnings("unchecked")
    private void mergeBpmnXml(String id_QName_, String sub_process_QName_, String conditionExpression_QName_,
                              String extension_QName_, Element newProcessElement, Element oldProcessElement) {
        Element newExtensionElements = null;
        Element oldExtensionElements = null;
        /*
         * 1、全局配置复制
         * 	a、remove extensionElements
         * 	b、add extensionElements
         */
        newExtensionElements = newProcessElement.element(extension_QName_);
        oldExtensionElements = oldProcessElement.element(extension_QName_);
        copySubElement(newExtensionElements, oldExtensionElements);

        // 2、节点配置复制
        List<Element> newElements = newProcessElement.elements();
        for (int i = 0, len = newElements.size(); i < len; i++) {
            mergeBpmnXml(id_QName_, sub_process_QName_,
                    conditionExpression_QName_, extension_QName_,
                    oldProcessElement, newElements, i);
        }
    }

    /**
     * 流程发布xml配置合并
     *
     * @param id_QName_
     * @param sub_process_QName_
     * @param conditionExpression_QName_
     * @param extension_QName_
     * @param oldProcessElement
     * @param newElements
     * @param i
     */
    @SuppressWarnings("unchecked")
    private void mergeBpmnXml(String id_QName_, String sub_process_QName_, String conditionExpression_QName_,
                              String extension_QName_, Element oldProcessElement, List<Element> newElements, int i) {
        Element newExtensionElements;
        Element oldExtensionElements;
        Element oldConditionExpression;
        Element tmpNewElement;
        Element tmpOldElement;
        /*
         *  a、remove extensionElements
         *  b、add extensionElements
         */
        tmpNewElement = newElements.get(i);
        logger.debug("element name is <{}>,ID is <{}>.", tmpNewElement.getName(), tmpNewElement.attributeValue(id_QName_));

        tmpOldElement = BpmDefineXmlUtil.elementByID(oldProcessElement, "*", tmpNewElement.attributeValue(id_QName_));
        logger.debug("tmpOldElement is {}", tmpOldElement);

        if (BeanUtils.isNotEmpty(tmpOldElement) && tmpNewElement.getName().equals(tmpOldElement.getName())) {
            if (!isSameType(tmpNewElement, tmpOldElement)) {
                throw new RuntimeException("多实例节点不能与单实例节点合并，请删除节点【" + tmpNewElement.attributeValue(id_QName_) + "】重新添加需要的节点！");
            }

            newExtensionElements = tmpNewElement.element(extension_QName_);
            oldExtensionElements = tmpOldElement.element(extension_QName_);
            if (BeanUtils.isNotEmpty(oldExtensionElements)) {
                copySubElement(newExtensionElements, oldExtensionElements);
            }

            oldConditionExpression = tmpOldElement.element(conditionExpression_QName_);
            if (BeanUtils.isNotEmpty(oldConditionExpression)) {
                oldConditionExpression.setParent(null);
                tmpNewElement.add(oldConditionExpression);
            }

            // 内部子流程合并
            if (sub_process_QName_.equals(tmpNewElement.getQName().getName())) {
                List<Element> newSubElements = tmpNewElement.elements();
                for (int j = 0, len = newSubElements.size(); j < len; j++) {
                    mergeBpmnXml(id_QName_, sub_process_QName_,
                            conditionExpression_QName_, extension_QName_,
                            oldProcessElement, newSubElements, j);
                }
            }
        }
    }

    private boolean isSameType(Element tmpNewElement, Element tmpOldElement) {
        String multiQName = "multiInstanceLoopCharacteristics";

        Element multiElemNew = tmpNewElement.element(multiQName);
        Element multiElemOld = tmpOldElement.element(multiQName);

        return (BeanUtils.isEmpty(multiElemNew) && BeanUtils.isEmpty(multiElemOld))
                || (BeanUtils.isNotEmpty(multiElemNew) && BeanUtils.isNotEmpty(multiElemOld));
    }

    /**
     * 复制子节点
     *
     * @param newElement
     * @param oldElement
     */
    @SuppressWarnings("unchecked")
    private void copySubElement(Element newElement, Element oldElement) {
        // 清空所有子节点
        List<Element> newElementSub = newElement.elements();
        for (int i = 0, len = newElementSub.size(); i < len; i++) {
            newElement.remove(newElementSub.get(i));
        }

        // 复制旧节点下所有子节点到新节点下
        List<Element> oldElementSub = oldElement.elements();
        Element sub = null;
        for (int i = 0, len = oldElementSub.size(); i < len; i++) {
            sub = oldElementSub.get(i);
            sub.setParent(null);
            newElement.add(sub);
        }
    }

    /**
     * <pre>
     * 对传入的流程定义克隆一份，
     * 将新产生的流程定义设置为主版本，
     * 并更新其它版本的流程定义为历史版本 （传入的流程定义可以是主版本，也可以是历史版本）
     * </pre>
     *
     * @return BpmDefine
     */
    private BpmDefinePo cloneToMain() {
        BpmDefinePo bpmDefine = getData();
        //克隆流程定义并新建
        BpmDefinePo newBpmDefine = null;
        try {
            newBpmDefine = (BpmDefinePo) bpmDefine.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
            return null;
        }

        // 合并配置，防止旧版本配置数据丢失
        BpmDefineXmlPo oldBpmnXmlPo = bpmDefineXmlQueryDao.get(getId());
        String oldBpmnXml = oldBpmnXmlPo.getBpmnXml();
        String bpmnXml = mergeBpmnXml(bpmDefine.getBpmnXml(), oldBpmnXml);
        newBpmDefine.setBpmnXml(bpmnXml);

        newBpmDefine.setDefId(UniqueIdUtil.getId());
        //将新的流程定义设置为主版本
        newBpmDefine.setIsMain("Y");
        //设置版本号
        Integer maxVersion = bpmDefineRepository.getMaxVersion(bpmDefine.getDefKey());
        newBpmDefine.setVersion(maxVersion);

        bpmDefineDao.create(newBpmDefine);

        //更新其他版本为非主版本，更新当前的为主版本
        updateMainVersion(newBpmDefine.getDefId());

        // 催办数据更新
        this.updateReminders(newBpmDefine.getDefId(), getId(), bpmnXml);

        // 触发流程数据更新
        this.updateTrigerFlows(newBpmDefine.getDefId(), getId(), bpmnXml);

        return newBpmDefine;
    }

    /**
     * 删除流程引擎的流程数据。
     *
     * @param defId void
     */
    private void removeActvitiByDefId(String defId) {
        bpmDefineDao.delActByteArrayByDefId(defId);
        bpmDefineDao.delActDeployByDefId(defId);
        bpmDefineDao.delActDefByDefId(defId);
    }

    /**
     * 修改流程定义状态
     *
     * @param oldStatus
     */
    private void updateBpmDefineStatus(String oldStatus) {
        BpmDefinePo bpmDefine = getData();
        String status = bpmDefine.getStatus();
        // 原来为草稿改成发布
        if (IBpmDefine.STATUS.DRAFT.equalsIgnoreCase(oldStatus)
                && IBpmDefine.STATUS.DEPLOY.equalsIgnoreCase(status)) {
            this.deploy();
        }
        // 状态修改成发布。
        if (!status.equalsIgnoreCase(oldStatus) && IBpmDefine.STATUS.DEPLOY.equalsIgnoreCase(status)) {
            bpmIstDomain.updForbiddenByDefKey(bpmDefine.getDefKey(), IBpmProcInst.FORBIDDEN_NO);
        }
        // 将流程实例修改成禁用实例。
        if (!status.equalsIgnoreCase(oldStatus) && IBpmDefine.STATUS.FORBIDDEN_INSTANCE.equalsIgnoreCase(status)) {
            bpmIstDomain.updForbiddenByDefKey(bpmDefine.getDefKey(), IBpmProcInst.FORBIDDEN_YES);
        }
    }

    /**
     * 清理缓存
     */
    public void publishEvent(IBpmDefine define) {
        cleanCache(define);
        AppUtil.publishEvent(new BpmDefineDelEvent(define));
    }

    private void cleanCache(IBpmDefine define) {
        String defId = define.getDefId();

        CacheKey cacheKey = cacheKeyGenerator.generate(IBpmDefine.BPM_DEFINITION + defId);
        iCache.delByKey(cacheKey.getDefKey());

        cacheKey = cacheKeyGenerator.generate(IBpmDefine.BPM_BPMN_ID + define.getBpmnDefId());
        iCache.delByKey(cacheKey.getDefKey());
    }

    /**
     * 更新插件
     *
     * @param newBpmnXml
     * @param oldBpmnXml
     * @return
     */
    private String updateBpmnXmlPlugins(String newBpmnXml, String oldBpmnXml) {
        if (StringUtil.isEmpty(oldBpmnXml)) {
            return newBpmnXml;
        }
        String bpmnXml = defXmlTransForm.transform(oldBpmnXml, newBpmnXml);
        return bpmnXml;
    }

    /**
     * 根据根据设计文件转换成标准的bpmn20定义文件。
     *
     * @param bpmDefine
     * @return String
     */
    private String convertBpmnXmlByDesignFile(IBpmDefine bpmDefine) {
        String bpmnXml = "";

        try {
            DefTransform trans = natProDefineService.getDefTransform();
            bpmnXml = trans.convert(bpmDefine.getDefKey(), bpmDefine.getName(), bpmDefine.getDefXml());
        } catch (Exception e) {
            e.printStackTrace();
        }

        return bpmnXml;
    }

    /**
     * 判断流程定义的数据是否有效（即包含必须的一些数据）
     *
     * @param bpmDefine
     * @return boolean
     */
    private boolean isAvailable(IBpmDefine bpmDefine) {
        return bpmDefine != null
                && !StringUtil.isEmpty(bpmDefine.getName())
                && !StringUtil.isEmpty(bpmDefine.getDefKey())
                && !StringUtil.isEmpty(bpmDefine.getDefXml());
    }

    /**
     * 流程定义导入
     *
     * @param unZipFilePath
     */
    public void importBpm(String unZipFilePath, String curUserId) {
        File dir = new File(unZipFilePath); // 改目录下的所有文件
        File[] files = dir.listFiles();
        if (files == null) {
            return;
        }

        for (int i = 0; i < files.length; i++) {
            if (files[i].isDirectory()) { // 如果是目录，则回调
                importBpm(files[i].getAbsolutePath(), curUserId);
            } else {
                importBpmFile(files[i], curUserId);
            }
        }
    }

    /**
     * 流程定义文件导入
     *
     * @param file
     */
    private void importBpmFile(File file, String curUserId) {
        String fileName = file.getName();
        String fileSuffix = StringUtil.substringAfterLast(fileName, ".");
        if (StringUtil.isNotEmpty(fileSuffix) && DataFormat.XML.value().equalsIgnoreCase(fileSuffix)) {
            String filePath = file.getAbsolutePath();
            String xmlStr = FileUtil.readFile(filePath); // 获取xml
            BpmDefinePo bpmDef = bpmDefineRepository.parse(xmlStr, DataFormat.XML); // xml 转

            importBpm(bpmDef, curUserId);
        } else {
            throw new BoBaseException("文件格式不正确！");
        }
    }

    /**
     * 导入Bpm
     *
     * @param bpmDef
     */
    private void importBpm(BpmDefinePo bpmDef, String curUserId) {
        if (BeanUtils.isEmpty(bpmDef)) return;

        // 调用native接口发布流程
        reDeploy(bpmDef);

        // 设置defId
        String defId = UniqueIdUtil.getId();
        bpmDef.setDefId(defId);

        // 设置版本
        setVersion(bpmDef);

        // 保存催办
        importReminder(bpmDef, defId);

        // 保存触发流程
        importTrigerFlow(bpmDef, defId);

        // 保存分管授权
        importRights(bpmDef);

        // 保存def、defxml
        bpmDef.setUpdateBy(curUserId);
        bpmDef.setUpdateTime(new Date());
        bpmDefineDao.create(bpmDef);

        updateMainVersion(bpmDef.getDefId());
    }

    /**
     * 设置版本
     *
     * @param bpmDef
     */
    private void setVersion(BpmDefinePo bpmDef) {
        // 设置IsMain
        bpmDef.setIsMain("Y");
        if (!IBpmDefine.STATUS.DRAFT.equals(bpmDef.getStatus())) {
            // 设置版本号
            Integer maxVersion = bpmDefineRepository.getMaxVersion(bpmDef.getDefKey());
            bpmDef.setVersion(maxVersion);
        }
    }

    /**
     * 重新发布
     *
     * @param bpmDef
     */
    private void reDeploy(BpmDefinePo bpmDef) {
        String deployId = null;
        String bpmnDefId = null;
        try {
            deployId = natProDefineService.deploy("", bpmDef.getName(), bpmDef.getBpmnXml());
            bpmnDefId = natProDefineService.getProcessDefinitionIdByDeployId(deployId);
        } catch (UnsupportedEncodingException e) {
            return;
        }

        // 设置bpmn字段值
        bpmDef.setBpmnDefId(bpmnDefId);
        bpmDef.setBpmnDeployId(deployId);
    }

    /**
     * 导入分管授权
     *
     * @param bpmDef
     */
    private void importRights(BpmDefinePo bpmDef) {
        List<BpmAuthPo> auths = bpmDef.getAuths();
        if (BeanUtils.isNotEmpty(auths)) {
            String rightJson = null;
            String authDefJson = null;
            for (BpmAuthPo auth : auths) {
                auth.setId(null);
                rightJson = RightsDefBuilder.build(auth.getRightsOwnerList());
                auth.setRightsOwner(rightJson);
                authDefJson = JsonUtil.getJSONString(auth.getRightsDefList());
                auth.setRightsDef(authDefJson);
                bpmAuthDomain = bpmAuthRepository.newInstance(auth);
                bpmAuthDomain.save();
            }
        }
    }

    /**
     * 导入催办设置
     *
     * @param bpmDef
     * @param defId
     */
    private void importReminder(BpmDefinePo bpmDef, String defId) {
        List<BpmTaskReminderPo> reminders = bpmDef.getReminders();
        if (BeanUtils.isNotEmpty(reminders)) {
            for (BpmTaskReminderPo reminder : reminders) {
                reminder.setId(null);
                reminder.setProcDefId(defId);
                bpmTaskReminderDomain = bpmTaskReminderRepository.newInstance(reminder);
                bpmTaskReminderDomain.save();
            }
        }
    }

    /**
     * 导入触发流程设置
     *
     * @param bpmDef
     * @param defId
     */
    private void importTrigerFlow(BpmDefinePo bpmDef, String defId) {
        List<BpmTrigerFlowPo> trigerFlows = bpmDef.getTrigerFlows();
        if (BeanUtils.isNotEmpty(trigerFlows)) {
            for (BpmTrigerFlowPo trigerFlow : trigerFlows) {
                trigerFlow.setId(null);
                trigerFlow.setDefId(defId);
                BpmTrigerFlow bpmTrigerFlowDomain = bpmTrigerFlowRepository.newInstance(trigerFlow);
                bpmTrigerFlowDomain.saveCascade();
            }
        }
    }

    /**
     * 保存流程设置
     *
     * @param defId
     * @param data
     */
    public void saveSetting(String defId, String data) {
        String bpmnXml = BpmDefineXmlUtil.getNewXml(defId, data);
        // 保存催办
        this.saveReminders(defId, data);
        // 保存触发流程
        this.saveTrigerFlows(defId, data);
        // 保存相关设置
        this.updateBpmnXml(defId, bpmnXml);
        // 保存状态
        this.updateStatusTestStatus(defId, data);
    }

    private void updateStatusTestStatus(String defId, String data) {
        BpmDefinePo po = bpmDefineRepository.get(defId);
        if (BeanUtils.isEmpty(po)) {
            return;
        }
        BpmDefineSetting setting = BpmDefineSettinglBuilder.build(data);
        List<Attribute> attrs = setting.getGlobal().getAttributes();
        if (BeanUtils.isEmpty(attrs)) {
            return;
        }

        //testStatus
        po.setTestStatus(this.getAttrValue(attrs, "testStatus"));
        //tatus
        po.setStatus(this.getAttrValue(attrs, "status"));
        bpmDefineDao.update(po);
    }

    private String getAttrValue(List<Attribute> attrs, String key) {
        for (Attribute attr : attrs) {
            if (key.equals(attr.getName())) {
                return attr.getValue();
            }
        }
        if ("testStatus".equals(key)) {
            return IBpmDefine.TEST_STATUS.TEST;
        } else if ("status".equals(key)) {
            return IBpmDefine.STATUS.DEPLOY;
        }

        return null;
    }

    /**
     * 保存催办
     *
     * @param data
     */
    private void saveReminders(String defId, String data) {
        BpmDefineSetting setting = BpmDefineSettinglBuilder.build(data);
        if (BeanUtils.isEmpty(setting.getNodes())) {
            return;
        }
        BpmTaskReminder bpmTaskReminder = bpmTaskReminderRepository.newInstance();
        bpmTaskReminder.save(defId, setting.getNodes());
    }

    /**
     * 保存触发流程
     *
     * @param data
     */
    private void saveTrigerFlows(String defId, String data) {
        BpmDefineSetting setting = BpmDefineSettinglBuilder.build(data);
        if (BeanUtils.isEmpty(setting.getNodes())) {
            return;
        }
        // 保存触发流程
        BpmTrigerFlow bpmTrigerFlow = bpmTrigerFlowRepository.newInstance();
        bpmTrigerFlow.save(defId, setting.getNodes());
    }

    /**
     * 复制流程定义
     *
     * @param
     * @param newDefKey
     */
    public void copy(String oldDefId, String newDefKey) {
        // 校验流程定义
        BpmDefinePo bpmDefinePo = validation(oldDefId, newDefKey);

        String newDefId = UniqueIdUtil.getId();
        String oldDefKey = bpmDefinePo.getDefKey();

        // 保存新流程定义
        copyBpmDefine(newDefKey, bpmDefinePo, newDefId, oldDefKey);

        // 复制催办
        copyReminder(oldDefId, newDefId);

        // 复制触发流程
        copyTrigerFlow(oldDefId, newDefId);

        // 复制表单权限
        BpmFormPermissionVo vo = new BpmFormPermissionVo(oldDefKey, newDefKey);
        //formRightsService.copyPermissionByFlowKey(vo);
    }

    /**
     * 复制催办
     *
     * @param oldDefId
     * @param newDefId
     */
    private void copyReminder(String oldDefId, String newDefId) {
        List<BpmTaskReminderPo> reminders = bpmTaskReminderRepository.findByDefId(oldDefId);
        if (BeanUtils.isEmpty(reminders)) {
            return;
        }

        for (BpmTaskReminderPo reminder : reminders) {
            reminder.setId(UniqueIdUtil.getId());
            reminder.setProcDefId(newDefId);
            bpmTaskReminderDao.create(reminder);
        }
    }

    /**
     * 复制触发流程
     *
     * @param oldDefId
     * @param newDefId
     */
    private void copyTrigerFlow(String oldDefId, String newDefId) {
        List<BpmTrigerFlowPo> trigerFlows = bpmTrigerFlowRepository.findByDefId(oldDefId);
        if (BeanUtils.isEmpty(trigerFlows)) {
            return;
        }

        for (BpmTrigerFlowPo trigerFlow : trigerFlows) {
            trigerFlow.setId(UniqueIdUtil.getId());
            trigerFlow.setDefId(newDefId);
            BpmTrigerFlow bpmTrigerFlowDomain = bpmTrigerFlowRepository.newInstance(trigerFlow);
            bpmTrigerFlowDomain.saveCascade();
        }
    }

    /**
     * 复制流程定义
     *
     * @param newDefKey
     * @param bpmDefinePo
     * @param newDefId
     * @param oldDefKey
     */
    private void copyBpmDefine(String newDefKey, BpmDefinePo bpmDefinePo, String newDefId, String oldDefKey) {
        // 替换流程定义 ID
        bpmDefinePo.setDefId(newDefId);
        // 替换流程定义 Key
        bpmDefinePo.setDefKey(newDefKey);

        BpmDefineXmlPo bpmDefineXmlPo = bpmDefinePo.getBpmDefineXmlPo();
        // 替换流程定义 ID
        bpmDefineXmlPo.setId(newDefId);

        // 替换流程定义xml ID
        replaceDefXmlKey(newDefKey, oldDefKey, bpmDefineXmlPo);

        // 替换流程定义发布xml ID
        replaceBpmnXmlKey(newDefKey, oldDefKey, bpmDefineXmlPo);

        if (IBpmDefine.STATUS.DRAFT.equals(bpmDefinePo.getStatus())) {
            setDraftAttr(bpmDefinePo);
        } else {
            setDeployAttr(newDefKey, bpmDefinePo, newDefId, bpmDefineXmlPo);
        }

        // 保存新流程定义
        bpmDefineDao.create(bpmDefinePo);
    }

    /**
     * 设置发布属性
     *
     * @param newDefKey
     * @param bpmDefinePo
     * @param newDefId
     * @param bpmDefineXmlPo
     */
    private void setDeployAttr(String newDefKey, BpmDefinePo bpmDefinePo, String newDefId,
                               BpmDefineXmlPo bpmDefineXmlPo) {
        String deployId = null;
        String bpmnDefId = null;
        try {
            deployId = natProDefineService.deploy("", bpmDefinePo.getName(), bpmDefineXmlPo.getBpmnXml());
            bpmnDefId = natProDefineService.getProcessDefinitionIdByDeployId(deployId);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("流程定义【" + newDefKey + "】发布失败！");
        }

        bpmDefinePo.setBpmnDefId(bpmnDefId);
        bpmDefinePo.setBpmnDeployId(deployId);
        bpmDefinePo.setMainDefId(newDefId);
        bpmDefinePo.setVersion(1);
        bpmDefinePo.setIsMain(StringPool.Y);
        bpmDefinePo.setCreateBy(ContextUtil.getCurrentUserId());
        bpmDefinePo.setCreateTime(new Date());
        bpmDefinePo.setUpdateBy(null);
        bpmDefinePo.setUpdateTime(null);
    }

    /**
     * 设置草稿属性
     *
     * @param bpmDefinePo
     */
    private void setDraftAttr(BpmDefinePo bpmDefinePo) {
        bpmDefinePo.setBpmnDefId(null);
        bpmDefinePo.setBpmnDeployId(null);
        bpmDefinePo.setMainDefId(null);
        bpmDefinePo.setVersion(0);
        bpmDefinePo.setIsMain(StringPool.Y);
        bpmDefinePo.setCreateBy(ContextUtil.getCurrentUserId());
        bpmDefinePo.setCreateTime(new Date());
        bpmDefinePo.setUpdateBy(null);
        bpmDefinePo.setUpdateTime(null);
    }

    /**
     * 替换流程发布xml key
     *
     * @param newDefKey
     * @param oldDefKey
     * @param bpmDefineXmlPo
     */
    private void replaceBpmnXmlKey(String newDefKey, String oldDefKey, BpmDefineXmlPo bpmDefineXmlPo) {
        String bpmnXml = bpmDefineXmlPo.getBpmnXml();
        Document bpmnDoc = Dom4jUtil.loadXml(bpmnXml);
        Element bpmnRootElm = bpmnDoc.getRootElement();
        Element bpmnElm = BpmDefineXmlUtil.elementByID(bpmnRootElm, "process", oldDefKey);
        if (BeanUtils.isEmpty(bpmnElm)) {
            throw new RuntimeException("流程定义Key【" + oldDefKey + "】与xml中Key不一致！");
        }
        bpmnElm.remove(bpmnElm.attribute("id"));
        bpmnElm.addAttribute("id", newDefKey);
        bpmDefineXmlPo.setBpmnXml(bpmnDoc.asXML());
    }

    /**
     * 替换流程定义xml key
     *
     * @param newDefKey
     * @param oldDefKey
     * @param bpmDefineXmlPo
     */
    private void replaceDefXmlKey(String newDefKey, String oldDefKey, BpmDefineXmlPo bpmDefineXmlPo) {
        String defXml = bpmDefineXmlPo.getDefXml();
        Document defDoc = Dom4jUtil.loadXml(defXml);
        Element defRootElm = defDoc.getRootElement();
        Element defElm = BpmDefineXmlUtil.elementByID(defRootElm, "process", oldDefKey);
        if (BeanUtils.isEmpty(defElm)) {
            throw new RuntimeException("流程定义Key【" + oldDefKey + "】与xml中Key不一致！");
        }
        defElm.remove(defElm.attribute("id"));
        defElm.addAttribute("id", newDefKey);
        bpmDefineXmlPo.setDefXml(defDoc.asXML());
    }

    /**
     * 校验流程定义
     *
     * @param oldDefId
     * @param newDefKey
     * @return
     */
    private BpmDefinePo validation(String oldDefId, String newDefKey) {
        // 校验流程定义是否存在
        BpmDefinePo vBpmDefinePo = bpmDefineRepository.getByDefKey(newDefKey, false);
        if (BeanUtils.isNotEmpty(vBpmDefinePo)) {
            throw new RuntimeException("流程定义【" + newDefKey + "】已存在！");
        }

        // 复制流程定义
        BpmDefinePo bpmDefinePo = bpmDefineRepository.getByDefId(oldDefId, true);
        if (BeanUtils.isEmpty(bpmDefinePo)
                || BeanUtils.isEmpty(bpmDefinePo.getBpmDefineXmlPo())) {
            throw new RuntimeException("流程定义【" + oldDefId + "】不存在！");
        }
        return bpmDefinePo;
    }

}
