package com.huazheng.bpm.model.base;

/**
 * 有资格的执行人。
 *
 * <pre>
 *
 * 构建组：ibps-bpmn-api
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2017年2月9日-上午9:07:02
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class QualifiedExecutor {

    // 执行人类型
    private String type;
    // 执行人ID
    private String executId;
    // 执行人名称
    private String executor;

    public QualifiedExecutor() {
        super();
    }

    public QualifiedExecutor(String type, String executId, String executor) {
        super();
        this.type = type;
        this.executId = executId;
        this.executor = executor;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getExecutId() {
        return executId;
    }

    public void setExecutId(String executId) {
        this.executId = executId;
    }

    public String getExecutor() {
        return executor;
    }

    public void setExecutor(String executor) {
        this.executor = executor;
    }

}
