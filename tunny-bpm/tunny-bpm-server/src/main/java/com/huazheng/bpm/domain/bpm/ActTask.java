package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.ActTaskDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.ActTaskPo;
import org.springframework.context.annotation.Scope;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@SuppressWarnings("serial")
@Transactional
@Scope("prototype")
public class ActTask extends AbstractDomain<String, ActTaskPo> {

    @Resource
    private ActTaskDao actTaskDao;

    @Override
    protected void init() {
        this.setDao(actTaskDao);
    }
}
