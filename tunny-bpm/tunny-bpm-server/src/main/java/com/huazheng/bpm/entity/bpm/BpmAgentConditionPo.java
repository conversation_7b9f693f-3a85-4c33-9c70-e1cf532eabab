package com.huazheng.bpm.entity.bpm;

import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * 流程代理条件 实体对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-03-30 17:29:14
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmAgentConditionPo extends BpmAgentConditionTbl {

    protected String agenterName;/*代理人名称*/

    public String getAgenterName() {
        return agenterName;
    }

    public void setAgenterName(String agenterName) {
        this.agenterName = agenterName;
    }

    protected String id;        /*主键*/
    protected String agentId;        /*代理ID*/
    protected String agenterId;        /*代理人ID*/
    protected String name;        /*条件名称*/
    protected String desc;        /*条件描述*/
    protected String condition;        /*条件内容*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    /**
     * 返回 代理ID
     *
     * @return
     */
    public String getAgentId() {
        return this.agentId;
    }

    public void setAgenterId(String agenterId) {
        this.agenterId = agenterId;
    }

    /**
     * 返回 代理人ID
     *
     * @return
     */
    public String getAgenterId() {
        return this.agenterId;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 返回 条件名称
     *
     * @return
     */
    public String getName() {
        return this.name;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     * 返回 条件描述
     *
     * @return
     */
    public String getDesc() {
        return this.desc;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    /**
     * 返回 条件内容
     *
     * @return
     */
    public String getCondition() {
        return this.condition;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("agentId", this.agentId)
                .append("agenterId", this.agenterId)
                .append("name", this.name)
                .append("desc", this.desc)
                .append("condition", this.condition)
                .toString();
    }
}
