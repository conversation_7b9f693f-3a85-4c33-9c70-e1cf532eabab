package com.huazheng.bpm.entity.constant;

/**
 * 逻辑计算类型。
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：simon cai
 * 邮箱:<EMAIL>
 * 日期:2015-3-10-下午6:19:05
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum LogicType {

    AND("and", "与"),
    OR("or", "或"),
    EXCLUDE("exclude", "非");

    // 键
    private String key = "";
    // 值
    private String value = "";

    // 构造方法
    LogicType(String key, String value) {
        this.key = key;
        this.value = value;
    }

    // =====getting and setting=====
    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return key;
    }

    /**
     * 通过key获取对象
     *
     * @param key
     * @return
     */
    public static LogicType from<PERSON>ey(String key) {
        for (LogicType c : LogicType.values()) {
            if (c.getKey().equalsIgnoreCase(key)) {
                return c;
            }
        }
        throw new IllegalArgumentException(key);
    }
}
