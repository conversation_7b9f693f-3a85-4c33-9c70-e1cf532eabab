package com.huazheng.bpm.plugin;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.huazheng.bpm.exception.WorkFlowException;
import com.huazheng.bpm.handler.ServiceClient;
import com.huazheng.bpm.handler.WebServiceParamHandler;
import com.huazheng.bpm.model.base.DefaultInvokeCmd;
import com.huazheng.bpm.model.base.InvokeCmd;
import com.huazheng.bpm.model.base.InvokeResult;
import com.huazheng.bpm.service.GroovyScriptEngine;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 服务节点执行
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午7:50:31
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@Service("WebServiceTaskPlugin")
public class WebServiceTaskPlugin extends AbstractBpmExecutionPlugin {

    @Resource
    private GroovyScriptEngine groovyScriptEngine;
    @Resource
    private ServiceClient serviceClient;

    @Override
    public Void execute(BpmExecutionPluginSession pluginSession, IBpmExecutionPluginDefine pluginDef) {
        WebServiceNodePluginDefine webServiceDef = (WebServiceNodePluginDefine) pluginDef;

        String serviceType = webServiceDef.getServiceType();

        if ("rest".equals(serviceType)) {
            restHandleResult(pluginSession, webServiceDef);
        } else {
            InvokeCmd invokeCmd = new DefaultInvokeCmd();
            invokeCmd.setAddress(webServiceDef.getAddress());
            invokeCmd.setOperatorName(webServiceDef.getMethodName());
            invokeCmd.setSoapAction(webServiceDef.isSoapAction());
            invokeCmd.setOperatorNamespace(webServiceDef.getNamespace());

            String MethordParams = getJsonParam(webServiceDef, pluginSession);
            invokeCmd.setJsonParam(MethordParams);

            InvokeResult invokeResult = serviceClient.invoke(invokeCmd);

            handleResult(invokeResult, pluginSession, webServiceDef);
        }

        return null;
    }

    /***
     * 拼装请求参数
     * @return
     */
    private String getJsonParam(WebServiceNodePluginDefine webServiceDef, BpmExecutionPluginSession pluginSession) {
        String inputStr = webServiceDef.getInput();
        JsonParser parser = new JsonParser();
        JsonElement jsonElement = parser.parse(inputStr);
        JsonObject jsonParamObj = new JsonObject();
        Map<String, Object> variables = pluginSession.getBpmDelegateExecution().getVariables();

        if (jsonElement.isJsonArray()) {
            WebServiceParamHandler.buildJsonParam(jsonElement.getAsJsonArray(), jsonParamObj, variables, WebServiceParamHandler.INPUT);
        }
        return jsonParamObj.toString();
    }

    /**
     * 处理结果
     *
     * @param invokeResult
     * @param pluginSession
     * @param webServiceDef
     */
    private void handleResult(InvokeResult invokeResult, BpmExecutionPluginSession pluginSession, WebServiceNodePluginDefine webServiceDef) {
        if (invokeResult.isFault()) {
            throw new WorkFlowException("webService 调用异常！");
        }

        if (invokeResult.isVoid()) {
            return;
        }

        String outputStr = webServiceDef.getOutput();
        JsonParser parser = new JsonParser();
        JsonElement jsonElement = parser.parse(outputStr);
        JsonObject jsonParamObj = new JsonObject();

        Map<String, Object> variables = pluginSession.getBpmDelegateExecution().getVariables();
        String paramKey = "";
        if (jsonElement.isJsonArray()) {
            paramKey = (String) WebServiceParamHandler.buildJsonParam(jsonElement.getAsJsonArray(),
                    jsonParamObj, variables, WebServiceParamHandler.FLOW_OUTPUT);
        }
        if (paramKey == null || paramKey.isEmpty()) {
            return;
        }
        //  返回值参数（可能是复合类型，携带多个参数） 他所对应的流程变量根据返回值进行修改，或者执行脚本。
        Object obj;
        if (invokeResult.isList()) {
            obj = invokeResult.getList();
        } else {
            obj = invokeResult.getObject();
        }

        pluginSession.getBpmDelegateExecution().setVariable(paramKey, obj);
    }

    /**
     * rest服务节点调用
     *
     * @param pluginSession
     * @param webServiceDef
     */
    private void restHandleResult(BpmExecutionPluginSession pluginSession, WebServiceNodePluginDefine webServiceDef) {
        String url = webServiceDef.getUrl();
        String detail = webServiceDef.getInput();
        try {
            //HttpClient client = new HttpClient();
			/*String validCode = client.getProperty("webapi.validCode");
			String baseURL = client.getProperty("webapi.baseURL")+ client.getProperty("webapi.anonymousTokenURL");
			Response res = client.get(baseURL,
					new PostParameter[] { new PostParameter("validCode", validCode) }, false, null);*/
            //String token = res.asJSONObject().getString("token");

            CloseableHttpResponse response = null;
            CloseableHttpClient apiclient = HttpClients.createDefault();
			/*JSONObject obj = JSONObject.fromObject(detail);
			Map<String, Object> map = (Map<String, Object>) JSONObject.toBean(obj, Map.class);*/
            URIBuilder uriBuilder = new URIBuilder();
			/*for (Map.Entry<String, Object> entry : map.entrySet()) {
				String key = entry.getKey();
				String value = entry.getValue().toString();
				if (value.isEmpty() || value == null) {

				} else {
					uriBuilder.addParameter(key, value);
				}
			}*/
            JSONArray inputs = JSONArray.fromObject(webServiceDef.getInput());
            //PostParameter[] params = new PostParameter[inputs.size()];
            //PostParameter p = null;
            for (int idx = 0; idx < inputs.size(); idx++) {
                JSONObject jsonp = JSONObject.fromObject(inputs.get(idx));
                String key = jsonp.getString("name");
//				String valtype = jsonp.getString("valtype");
                String value = jsonp.getString("defaultValue");
//				if(ValType.STEADY.equals(valtype)){
//					value = jsonp.getString("value");
//				}
//				else if(ValType.PROCESS.equals(valtype)){
//
//				}
//				else if(ValType.STRIPT.equals(valtype)){
//					String script=jsonp.getString("value");
//					BpmDelegateExecution execution= pluginSession.getBpmDelegateExecution();
//					Map<String, Object> vars=new HashMap<String, Object>();
//					vars.put(BpmConstants.BPMN_EXECUTION_ID, execution.getId());
//					vars.put(BpmConstants.BPMN_INST_ID, execution.getBpmnInstId());
//					vars.putAll( execution.getVariables());
//					value = groovyScriptEngine.executeString(script, vars);
//				}
//				else if(ValType.FORM.equals(valtype)){
//
//				}

				/*if(key.equals("token")){
					//p = new PostParameter("token",token);
				}else*/
                if (value.isEmpty() || value == null) {

                } else {
                    uriBuilder.addParameter(key, value);
                }
            }

            HttpGet get = new HttpGet(url + uriBuilder.build());

            response = apiclient.execute(get);
            HttpEntity entity = response.getEntity();
            JSONObject res = JSONObject.fromObject(EntityUtils.toString(entity, "utf-8"));
            int code = response.getStatusLine().getStatusCode();
            if (code == HttpStatus.OK.value()) {
                pluginSession.getBpmDelegateExecution().setVariable("result", res);
            } else {
                throw new WorkFlowException("rest 调用异常！");
            }
        } catch (Exception e) {
            throw new WorkFlowException("rest 调用异常！");
        }
    }

}
