package com.huazheng.bpm.entity.bpm;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huazheng.bpm.entity.framework.AbstractPo;
import com.huazheng.bpm.util.core.StringPool;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.Date;

/**
 * 流程代理 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-03-30 17:29:13
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmAgentTbl extends AbstractPo<String> {
    protected String id;        /*主键*/
    protected String title;        /*标题*/
    protected String delegatorId;        /*委托人ID*/
    protected String agenterId;        /*代理人ID*/
    protected String procDefKey;        /*流程key*/
    protected String isEnabled;        /*是否可用,enabled/disabled*/
    @JsonFormat(pattern = StringPool.DATE_FORMAT_DATE)
    protected Date effectiveTime;        /*生效时间*/
    @JsonFormat(pattern = StringPool.DATE_FORMAT_DATE)
    protected Date expiryTime;        /*失效时间*/
    protected String agentType;        /*代理类型，all-全权代理、part-部分代理、condition-条件代理*/
    protected String createBy;        /*创建人*/
    @JsonFormat(pattern = StringPool.DATE_FORMAT_DATETIME)
    protected Date createTime;        /*创建时间*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * 返回 标题
     *
     * @return
     */
    public String getTitle() {
        return this.title;
    }

    public void setDelegatorId(String delegatorId) {
        this.delegatorId = delegatorId;
    }

    /**
     * 返回 委托人ID
     *
     * @return
     */
    public String getDelegatorId() {
        return this.delegatorId;
    }

    public void setAgenterId(String agenterId) {
        this.agenterId = agenterId;
    }

    /**
     * 返回 代理人ID
     *
     * @return
     */
    public String getAgenterId() {
        return this.agenterId;
    }

    public void setProcDefKey(String procDefKey) {
        this.procDefKey = procDefKey;
    }

    /**
     * 返回 流程key
     *
     * @return
     */
    public String getProcDefKey() {
        return this.procDefKey;
    }

    public void setIsEnabled(String isEnabled) {
        this.isEnabled = isEnabled;
    }

    /**
     * 返回 是否可用,enabled/disabled
     *
     * @return
     */
    public String getIsEnabled() {
        return this.isEnabled;
    }

    public void setEffectiveTime(Date effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    /**
     * 返回 生效时间
     *
     * @return
     */
    public Date getEffectiveTime() {
        return this.effectiveTime;
    }

    public void setExpiryTime(Date expiryTime) {
        this.expiryTime = expiryTime;
    }

    /**
     * 返回 失效时间
     *
     * @return
     */
    public Date getExpiryTime() {
        return this.expiryTime;
    }

    public void setAgentType(String agentType) {
        this.agentType = agentType;
    }

    /**
     * 返回 代理类型，all-全权代理、part-部分代理、condition-条件代理
     *
     * @return
     */
    public String getAgentType() {
        return this.agentType;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     * 返回 创建人
     *
     * @return
     */
    public String getCreateBy() {
        return this.createBy;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 返回 创建时间
     *
     * @return
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("title", this.title)
                .append("delegatorId", this.delegatorId)
                .append("agenterId", this.agenterId)
                .append("isEnabled", this.isEnabled)
                .append("effectiveTime", this.effectiveTime)
                .append("expiryTime", this.expiryTime)
                .append("agentType", this.agentType)
                .append("createBy", this.createBy)
                .append("createTime", this.createTime)
                .toString();
    }
}
