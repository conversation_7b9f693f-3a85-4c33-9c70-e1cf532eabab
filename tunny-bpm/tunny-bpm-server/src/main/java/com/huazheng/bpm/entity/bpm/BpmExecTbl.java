package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;


/**
 * 流程执行记录 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-01-04 14:29:57
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmExecTbl extends AbstractPo<String> {
    protected String id;        /*主键*/
    protected String procDefId;        /*流程定义ID*/
    protected String nodeId;        /*节点ID*/
    protected String nodeName;        /*节点名*/
    protected String nodeType;        /*节点类型：startEvent、endEvent、userTask、scriptTask...*/
    protected Short isMulitiTask;        /*是否多实例任务：1=是,0=否*/
    protected Short isGatewayMetjoin;        /*是否聚合网关:1=是,0=否*/
    protected String parentProcInstId;        /*父流程实例ID*/
    protected String procInstId;        /*流程实例ID*/
    protected String taskId;        /*任务ID*/
    protected String taskToken;        /*是针对分发任务时，携带的令牌，方便查找其父任务堆栈*/
    protected String targetNode;        /*跳转节点*/
    protected String targetAction;        /*执行类型,正常审批,normal,直接返回direct.*/
    protected String targetToken;        /*执行令牌*/
    protected Short interpose;        /*是否干预执行：0,否,1,是,干预执行。表示执行人不在候选人中。*/
    protected String exerId;        /*执行人ID*/
    protected Short execStatus;        /*执行状态(0,初始,1,完成,2,取消)*/
    protected java.util.Date startTime;        /*开始时间*/
    protected java.util.Date endTime;        /*结束时间*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    /**
     * 返回 流程定义ID
     *
     * @return
     */
    public String getProcDefId() {
        return this.procDefId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    /**
     * 返回 节点ID
     *
     * @return
     */
    public String getNodeId() {
        return this.nodeId;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    /**
     * 返回 节点名
     *
     * @return
     */
    public String getNodeName() {
        return this.nodeName;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    /**
     * 返回 节点类型：startEvent、endEvent、userTask、scriptTask...
     *
     * @return
     */
    public String getNodeType() {
        return this.nodeType;
    }

    public void setIsMulitiTask(Short isMulitiTask) {
        this.isMulitiTask = isMulitiTask;
    }

    /**
     * 返回 是否多实例任务：1=是,0=否。
     *
     * @return
     */
    public Short getIsMulitiTask() {
        return this.isMulitiTask;
    }

    public Short getIsGatewayMetjoin() {
        return isGatewayMetjoin;
    }

    public void setIsGatewayMetjoin(Short isGatewayMetjoin) {
        this.isGatewayMetjoin = isGatewayMetjoin;
    }

    public void setParentProcInstId(String parentProcInstId) {
        this.parentProcInstId = parentProcInstId;
    }

    /**
     * 返回 父流程实例ID
     *
     * @return
     */
    public String getParentProcInstId() {
        if (null == this.parentProcInstId) this.parentProcInstId = "0";
        return this.parentProcInstId;
    }

    public void setProcInstId(String procInstId) {
        this.procInstId = procInstId;
    }

    /**
     * 返回 流程实例ID
     *
     * @return
     */
    public String getProcInstId() {
        return this.procInstId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    /**
     * 返回 任务ID
     *
     * @return
     */
    public String getTaskId() {
        if (null == this.taskId) this.taskId = "0";
        return this.taskId;
    }

    public void setTaskToken(String taskToken) {
        this.taskToken = taskToken;
    }

    /**
     * 返回 是针对分发任务时，携带的令牌，方便查找其父任务堆栈
     *
     * @return
     */
    public String getTaskToken() {
        return this.taskToken;
    }

    /**
     * 返回 跳转节点
     *
     * @return
     */
    public String getTargetNode() {
        return targetNode;
    }

    public void setTargetNode(String targetNode) {
        this.targetNode = targetNode;
    }

    public void setTargetAction(String targetAction) {
        this.targetAction = targetAction;
    }

    /**
     * 返回 执行类型,正常审批,normal,直接返回direct.
     *
     * @return
     */
    public String getTargetAction() {
        return this.targetAction;
    }

    public void setTargetToken(String targetToken) {
        this.targetToken = targetToken;
    }

    /**
     * 返回 执行令牌
     *
     * @return
     */
    public String getTargetToken() {
        return this.targetToken;
    }

    public void setInterpose(Short interpose) {
        this.interpose = interpose;
    }

    /**
     * 返回 是否干预执行：0,否,1,是,干预执行。表示执行人不在候选人中。
     *
     * @return
     */
    public Short getInterpose() {
        return this.interpose;
    }

    public void setExerId(String exerId) {
        this.exerId = exerId;
    }

    /**
     * 返回 执行人ID
     *
     * @return
     */
    public String getExerId() {
        return this.exerId;
    }

    public void setExecStatus(Short execStatus) {
        this.execStatus = execStatus;
    }

    /**
     * 返回 执行状态(0,初始,1,完成,2,取消)
     *
     * @return
     */
    public Short getExecStatus() {
        if (null == this.execStatus) this.execStatus = (short) 0;
        return this.execStatus;
    }

    public void setStartTime(java.util.Date startTime) {
        this.startTime = startTime;
    }

    /**
     * 返回 开始时间
     *
     * @return
     */
    public java.util.Date getStartTime() {
        return this.startTime;
    }

    public void setEndTime(java.util.Date endTime) {
        this.endTime = endTime;
    }

    /**
     * 返回 结束时间
     *
     * @return
     */
    public java.util.Date getEndTime() {
        return this.endTime;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("procDefId", this.procDefId)
                .append("nodeId", this.nodeId)
                .append("nodeName", this.nodeName)
                .append("nodeType", this.nodeType)
                .append("isMulitiTask", this.isMulitiTask)
                .append("isGatewayMetjoin", this.isGatewayMetjoin)
                .append("parentProcInstId", this.parentProcInstId)
                .append("procInstId", this.procInstId)
                .append("taskId", this.taskId)
                .append("taskToken", this.taskToken)
                .append("targetAction", this.targetAction)
                .append("targetToken", this.targetToken)
                .append("interpose", this.interpose)
                .append("exerId", this.exerId)
                .append("execStatus", this.execStatus)
                .append("startTime", this.startTime)
                .append("endTime", this.endTime)
                .toString();
    }
}
