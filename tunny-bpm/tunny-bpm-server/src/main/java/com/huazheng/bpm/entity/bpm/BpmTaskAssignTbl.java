package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * 任务候选人 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：simon cai
 * 邮箱地址：<EMAIL>
 * 创建时间：2016-12-14 19:35:04
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmTaskAssignTbl extends AbstractPo<String> {
    protected String id;        /*主键*/
    protected String taskId;        /*任务ID*/
    protected String type;        /*候选人类型*/
    protected String executor;        /*执行人ID*/
    protected String procInstId;        /*流程实例ID*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    /**
     * 返回 任务ID
     *
     * @return
     */
    public String getTaskId() {
        return this.taskId;
    }

    public void setType(String type) {
        this.type = type;
    }

    /**
     * 返回 候选人类型
     *
     * @return
     */
    public String getType() {
        return this.type;
    }

    public void setExecutor(String executor) {
        this.executor = executor;
    }

    /**
     * 返回 执行人ID
     *
     * @return
     */
    public String getExecutor() {
        return this.executor;
    }

    public void setProcInstId(String procInstId) {
        this.procInstId = procInstId;
    }

    /**
     * 返回 流程实例ID
     *
     * @return
     */
    public String getProcInstId() {
        return this.procInstId;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("taskId", this.taskId)
                .append("type", this.type)
                .append("executor", this.executor)
                .append("procInstId", this.procInstId)
                .toString();
    }
}
