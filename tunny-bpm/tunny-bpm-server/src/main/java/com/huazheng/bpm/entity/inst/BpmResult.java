package com.huazheng.bpm.entity.inst;


import com.huazheng.bpm.entity.constant.DataType;

import java.util.HashMap;
import java.util.Map;


/**
 * 流程执行结果。
 * <pre>
 * 这个类用于 com.lc.bpmn.core.aspect.BpmAspect，业务代码计算结果。
 * 构建组：ibps-bpmn-api
 * 作者：simon cai
 * 邮箱:<EMAIL>
 * 日期:2015-6-5-上午10:56:12
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class BpmResult {

    /**
     * 业务主键
     */
    private String businessKey = "";

    /**
     * 业务主键类型
     */
    private DataType dataType = DataType.STRING;

    /**
     * 流程变量数据。
     */
    private Map<String, Object> vars = new HashMap<String, Object>();

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public DataType getDataType() {
        return dataType;
    }

    public void setDataType(DataType dataType) {
        this.dataType = dataType;
    }

    public Map<String, Object> getVars() {
        return vars;
    }

    public void setVars(Map<String, Object> vars) {
        this.vars = vars;
    }

    public void addVariable(String name, Object value) {
        this.vars.put(name, value);
    }


}
