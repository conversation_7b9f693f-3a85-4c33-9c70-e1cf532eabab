package com.huazheng.bpm.plugin.task.userassign.context;

import com.huazheng.bpm.plugin.IPluginContext;
import com.huazheng.bpm.plugin.IPluginParser;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import net.sf.json.processors.JsonBeanProcessor;

/**
 * 序列化JSON是添加pluginType属性。
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：caixy
 * 邮箱:<EMAIL>
 * 日期:2014-7-23-下午2:10:07
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class PluginContextPluginTypeProcessor implements JsonBeanProcessor {

    @Override
    public JSONObject processBean(Object obj, JsonConfig config) {
        if (!(obj instanceof IPluginParser)) {
            return new JSONObject(true);
        }
        IPluginContext context = (IPluginContext) obj;

        return JSONObject.fromObject(context.getBpmPluginDefine(), config)
                .element("pluginType", ((IPluginParser) context).getType());

    }

}
