package com.huazheng.bpm.entity.constant;

/**
 * 审批动作类型。
 *
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：simon cai
 * 邮箱:<EMAIL>
 * 日期:2015-4-1-下午5:54:18
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum ActionType {
    /**
     * 审批
     */
    APPROVE("approve", "审批"),
    /**
     * 辅助
     */
    ASSIST("assist", "辅助"),
    /**
     * 驳回
     */
    REJECT("reject", "驳回"),
    /**
     * 驳回到发起人
     */
    REJECT_TO_START("rejectToStart", "驳回到发起人"),
    /**
     * 驳回上一步
     */
    REJECT_TO_PREVIOUS("rejectToPrevious", "驳回上一步"),
    /**
     * 沟通
     */
    COMMU("commu", "沟通"),
    /**
     * 撤销
     */
    REVOKE("revoke", "撤销"),
    /**
     * 流转
     */
    TRANS("trans", "流转"),
    /**
     * 其他
     */
    OTHER("other", "其他");

    // 键
    private String key = "";
    // 值
    private String value = "";

    // 构造方法
    ActionType(String key, String value) {
        this.key = key;
        this.value = value;
    }

    // =====getting and setting=====
    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return key;
    }

    /**
     * 通过key获取对象
     *
     * @param key
     * @return
     */
    public static ActionType fromKey(String key) {
        for (ActionType c : ActionType.values()) {
            if (c.getKey().equalsIgnoreCase(key)) {
                return c;
            }
        }
        throw new IllegalArgumentException(key);
    }

}
