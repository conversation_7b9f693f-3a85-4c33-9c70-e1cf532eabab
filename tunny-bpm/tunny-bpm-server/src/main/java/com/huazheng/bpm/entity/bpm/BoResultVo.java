package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.model.base.IBoDef;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.ArrayList;
import java.util.List;

/**
 * BO结果。
 *
 * <pre>
 *
 * 构建组：ibps-base-bo
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年5月27日-上午11:05:07
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class BoResultVo extends BaseResultVo {

    // 保存模式db,object
    private String saveMode = "";

    // bo定义实例
    private IBoDef boDef;

    private String resultId = "";

    private List<SubDataVo> subDataList = new ArrayList<SubDataVo>();

    public String getResultId() {
        return resultId;
    }

    public void setResultId(String resultId) {
        this.resultId = resultId;
    }

    public List<SubDataVo> getSubDataList() {
        return subDataList;
    }

    public void setSubDataList(List<SubDataVo> subDataList) {
        this.subDataList = subDataList;
    }

    public IBoDef getBoDef() {
        return boDef;
    }

    public void setBoDef(IBoDef boDef) {
        this.boDef = boDef;
    }

    public String getSaveMode() {
        return saveMode;
    }

    public void setSaveMode(String saveMode) {
        this.saveMode = saveMode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("saveMode", saveMode).append("boDef", boDef)
                .append("resultId", resultId).append("tableName", tableName).append("action", action)
                .append("dataObject", dataObject).append("subDataList", subDataList).toString();
    }
}
