package com.huazheng.bpm.entity.bpm;


import com.huazheng.bpm.model.base.QualifiedExecutor;
import com.huazheng.bpm.model.task.IBpmTaskApproval;
import com.huazheng.bpm.util.db.UniqueIdUtil;

import java.util.List;

/**
 * 流程审批意见 实体对象
 *
 * <pre>
 *
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：luodx
 * 邮箱地址：<EMAIL>
 * 创建时间：2016-12-12 15:53:51
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmApprovePo extends BpmApproveTbl implements IBpmTaskApproval {
    public static final String OPINION_FLAG = "__form_opinion";
    public static String STATUS_AWAITTING_CHECK = "awaiting_check";
    public static String STATUS_TRANSFORMING = "transforming";

    public BpmApprovePo() {
    }

    public BpmApprovePo(String procInstId, String procDefId, String status) {
        this.id = UniqueIdUtil.getId();
        this.procInstId = procInstId;
        this.procDefId = procDefId;
        this.status = status;
    }

    //有资格的执行人
    protected List<QualifiedExecutor> qualifiedExecutor;

    // 执行人头像图片
    protected String userImg;

    protected String statusName;

    protected String auditorName;

    protected String subject;

    protected String batch;

    protected boolean callSub = false;

    @Override
    public String getBatch() {
        return batch;
    }

    public void setBatch(String batch) {
        this.batch = batch;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    @Override
    public boolean isCallSub() {
        return callSub;
    }

    public void setCallSub(boolean callSub) {
        this.callSub = callSub;
    }

    @Override
    public List<QualifiedExecutor> getQualifiedExecutor() {
        return qualifiedExecutor;
    }

    @Override
    public void setQualifiedExecutor(List<QualifiedExecutor> qualifiedExecutor) {
        this.qualifiedExecutor = qualifiedExecutor;
    }

    @Override
    public String getAuditorName() {
        return auditorName;
    }

    @Override
    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    /**
     * userImg
     *
     * @return the userImg
     */

    public String getUserImg() {
        return userImg;
    }

    /**
     * @param userImg the userImg to set
     */
    public void setUserImg(String userImg) {
        this.userImg = userImg;
    }

}
