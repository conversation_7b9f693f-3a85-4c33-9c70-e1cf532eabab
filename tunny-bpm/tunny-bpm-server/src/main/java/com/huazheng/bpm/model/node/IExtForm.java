package com.huazheng.bpm.model.node;


import com.huazheng.bpm.model.form.IForm;

/**
 * 表单扩展接口
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-上午10:13:32
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface IExtForm extends IForm {

    /**
     * 获取前置处理
     *
     * @return
     */
    String getPrevHandler();

    /**
     * 设置前置处理
     *
     * @param prevHandler
     */
    void setPrevHandler(String prevHandler);

    /**
     * 获取后置处理
     *
     * @return
     */
    String getPostHandler();

    /**
     * 设置后置处理
     *
     * @param postHandler
     */
    void setPostHandler(String postHandler);
}
