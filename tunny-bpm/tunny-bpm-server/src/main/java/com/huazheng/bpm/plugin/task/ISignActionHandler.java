package com.huazheng.bpm.plugin.task;


import com.huazheng.bpm.model.delegate.BpmDelegateExecution;
import com.huazheng.bpm.util.cmd.TaskFinishCmd;

/**
 * 会签处理接口
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月30日-上午9:20:35
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface ISignActionHandler {

    SignRes handleByActionType(TaskFinishCmd cmd, BpmDelegateExecution bpmDelegateExecution, String userId);
}
