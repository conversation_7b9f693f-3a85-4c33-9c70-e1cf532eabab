package com.huazheng.bpm.plugin.usercalc.pos.runtime;


import com.huazheng.bpm.api.entity.SysUser;
import com.huazheng.bpm.api.feign.RemoteBpmUserService;
import com.huazheng.bpm.entity.constant.BpmConstants;
import com.huazheng.bpm.entity.org.PartyEntityPo;
import com.huazheng.bpm.model.base.BpmIdentity;
import com.huazheng.bpm.model.base.PartyEntity;
import com.huazheng.bpm.model.task.IBpmTaskApproval;
import com.huazheng.bpm.plugin.AbstractUserCalcPlugin;
import com.huazheng.bpm.plugin.IBpmPluginDefine;
import com.huazheng.bpm.plugin.usercalc.pos.def.PosPluginDefine;
import com.huazheng.bpm.service.BpmApprovalService;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.session.BpmUserCalcPluginSession;
import net.sf.json.JSONArray;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * 岗位插件
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月14日-上午10:22:34
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@Service("posPlugin")
@Transactional
public class PosPlugin extends AbstractUserCalcPlugin {


    //	@Resource
//	private SysPostService sysPostService;
    @Resource
    private RemoteBpmUserService remoteBpmUserService;
    @Resource
    private BpmApprovalService bpmApprovalService;

    @Override
    public List<BpmIdentity> queryByPluginDef(BpmUserCalcPluginSession pluginSession, IBpmPluginDefine pluginDef) {
        PosPluginDefine def = (PosPluginDefine) pluginDef;

        List<BpmIdentity> users = new ArrayList<>();
        List<BpmIdentity> orgs = getIdentity(def, pluginSession);
//		for(BpmIdentity org:orgs){
//			List<User> tmpUsers = cast2User(DefaultPartyUserPo.fromJsonArrayString2(
//					userService.findByPartyJson(org.getId(), org.getGroupType())
//					));
//			for(User user:tmpUsers){
//				BpmIdentity identity=getBpmIdentityConverter().convertByUserId(user.getUserId());
//				if(!users.contains(identity)){
//					users.add(identity);
//				}
//			}
//		}

        return getIdentity(def, pluginSession);
    }

    /**
     * 根据配置获取所在的组。
     *
     * @param def
     * @param pluginSession
     * @return List&lt;Group>
     */
    private List<BpmIdentity> getIdentity(PosPluginDefine def, BpmUserCalcPluginSession pluginSession) {
        Map<String, Object> vars = pluginSession.getVariables();
        String source = def.getSource();

        List<PartyEntity> list = getPoses(def, pluginSession, vars, source);

        if (BeanUtils.isNotEmpty(list)) {
            return getIdentity(list);
        }

        return Collections.emptyList();
    }

    private List<PartyEntity> getPoses(PosPluginDefine def, BpmUserCalcPluginSession pluginSession,
                                       Map<String, Object> vars, String source) {
        List<PartyEntity> list = new ArrayList<PartyEntity>();
        if ("start".equals(source)) {
            String startId = (String) vars.get(BpmConstants.START_USER);
            //	String data = posService.findByUserId(startId);
            //list.addAll(PartyPositionPo.fromJsonArrayString(data));
        } else if ("prev".equals(source)) {
            String userId = null;
            if (AbstractUserCalcPlugin.isPreVrewModel.get() == null) {
                if (vars.containsKey(BpmConstants.PREV_USER)) {
                    userId = vars.get(BpmConstants.PREV_USER).toString();
                } else {
                    userId = vars.get(BpmConstants.CUR_USER).toString();
                }
                //	String data = posService.findByUserId(userId);
                //	list.addAll(PartyPositionPo.fromJsonArrayString(data));
            }
        } else if ("spec".equals(source)) {
            String roleAlias = def.getPosKey();
            if (roleAlias == null) {
                return Collections.emptyList();
            }
            list = getByRoleAlias(roleAlias, pluginSession);
        } else if ("node".equals(source)) {
            String processInstanceId = (String) pluginSession.getVariables().get(BpmConstants.PROCESS_INST_ID);
            List<IBpmTaskApproval> taskOpinionList = bpmApprovalService.findByInstNodeId(processInstanceId, def.getNodeId(), false);
            IBpmTaskApproval taskOpinion = null;
            if (taskOpinionList.size() > 0) {
                taskOpinion = taskOpinionList.get(taskOpinionList.size() - 1);
            }

            if (taskOpinion != null) {
                //	String data = posService.findByUserId(taskOpinion.getAuditor());
                //list.addAll(PartyPositionPo.fromJsonArrayString(data));
            }
        }
        return list;
    }

    private List<BpmIdentity> getIdentity(List<PartyEntity> rslist) {
        List<Map<String, Object>> gs = new ArrayList<Map<String, Object>>();
        for (PartyEntity itm : rslist) {
            Map<String, Object> rs = new HashMap<String, Object>();
            rs.put(BpmIdentity.IDENT_ID, itm.getId());
            rs.put(BpmIdentity.IDENT_NAME, itm.getName());
            rs.put(BpmIdentity.IDENT_TYPE, itm.getIdentityType());
            //rs.put(BpmIdentity.IDENT_PARTY_TYPE, itm.getIdentityType());
            rs.put(BpmIdentity.IDENT_PARTY_TYPE, BpmIdentity.TYPE_PARTY);

            gs.add(rs);
        }
        return getBpmIdentityConverter().convertByMapList(gs);
    }

    private List<PartyEntity> getByRoleAlias(String roleAlias, BpmUserCalcPluginSession pluginSession) {
        List<PartyEntity> list = new ArrayList<PartyEntity>();
        List<SysUser> userList = new ArrayList<>();
        userList = remoteBpmUserService.selectUserByPost(roleAlias);
        JSONArray json = JSONArray.fromObject(userList);
        String obj = String.valueOf(json);

//		if(JacksonUtil.isJsonObject(obj)){
//			List<PartyEntity> list1 = JacksonUtil.getDTOList(obj, PartyEntityPo.class);
//			//list.add(JacksonUtil.getDTOList(obj, PartyEntityPo.class));
//		}
        if (userList != null && userList.size() > 0) {
            for (int i = 0; i < userList.size(); i++) {
                PartyEntityPo partyEntity = new PartyEntityPo();
                partyEntity.setId(userList.get(i).getEmpno());
                partyEntity.setName(userList.get(i).getUserRealname());
                partyEntity.setAlias(userList.get(i).getUserId().toString());
                partyEntity.setPartyType("employee");
                list.add(partyEntity);
            }

        }

        return list;
    }

    /**
     * List<DefaultPartyUserPo>转换为List<User>
     *
     * @param users
     * @return
     */
//	private List<User> cast2User(List<DefaultPartyUserPo> users) {
//		if(BeanUtils.isEmpty(users)) return Collections.emptyList();
//		List<User> res = new ArrayList<User>();
//		for(DefaultPartyUserPo user : users){
//			res.add(user);
//		}
//		return res;
//	}

}
