/**
 * 描述：触发流程实体-接口
 * 包名：com.huazheng.bpm.api.bpmn.model.node
 * 文件名：ITrigerFlow.java
 * 作者：eddy
 * 日期：2017年8月25日-上午8:21:22
 * 版权：广州流辰信息技术有限公司版权所有
 */
package com.huazheng.bpm.model.node;

import java.util.List;

/**
 * 触发流程实体-接口
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2017年8月25日-上午8:21:22
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface ITrigerFlow {

    /**
     * 主流程定义ID
     *
     * @param defId
     */
    void setDefId(String defId);

    String getDefId();

    /**
     * 主流程节点ID
     *
     * @param nodeId
     */
    void setNodeId(String nodeId);

    String getNodeId();

    /**
     * 触发动作
     *
     * @param action
     */
    void setAction(String action);

    String getAction();

    /**
     * 触发流程
     *
     * @param trigerFlowKey
     */
    void setTrigerFlowKey(String trigerFlowKey);

    String getTrigerFlowKey();

    /**
     * 触发类型
     *
     * @param trigerType
     */
    void setTrigerType(String trigerType);

    String getTrigerType();

    /**
     * 是否调用启动页面
     *
     * @param callStartPage
     */
    void setCallStartPage(String callStartPage);

    String getCallStartPage();

    /**
     * 数据映射
     *
     * @param params
     */
    void setParams(List<ITrigerParam> params);

    List<ITrigerParam> getParams();
}
