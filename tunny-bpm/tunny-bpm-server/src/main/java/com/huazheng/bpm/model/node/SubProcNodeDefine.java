package com.huazheng.bpm.model.node;


import com.huazheng.bpm.entity.constant.NodeType;
import com.huazheng.bpm.model.define.IBpmProcDefine;
import com.huazheng.bpm.model.define.IBpmProcExtendDefine;

/**
 * 子流程节点
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月9日-下午5:41:11
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
public class SubProcNodeDefine extends BaseNodeDefine implements IMultiInstanceDefine {

    public SubProcNodeDefine() {
        setType(NodeType.SUBPROCESS);
    }

    private IBpmProcDefine<IBpmProcExtendDefine> bpmChildProcDefine;

    /**
     * 是否串行会签。
     */
    private boolean isParallel = false;

    private boolean setSupportMuliInstance = false;

    @Override
    public boolean supportMuliInstance() {
        return this.setSupportMuliInstance;
    }

    @Override
    public boolean isParallel() {
        return isParallel;
    }

    public void setParallel(boolean isParallel) {
        this.isParallel = isParallel;
    }

    /**
     * 获得内部子流程的流程定义。
     *
     * @return Map<String, BpmNodeDef> key：nodeId，value：BpmNodeDef
     * @throws
     * @since 1.0.0
     */
    public IBpmProcDefine<? extends IBpmProcExtendDefine> getBpmChildProcDefine() {
        return bpmChildProcDefine;
    }

    public void setBpmChildProcDefine(IBpmProcDefine<IBpmProcExtendDefine> bpmChildProcDefine) {
        this.bpmChildProcDefine = bpmChildProcDefine;
    }

    @Override
    public void setSupportMuliInstance(boolean support) {
        this.setSupportMuliInstance = support;
    }

}
