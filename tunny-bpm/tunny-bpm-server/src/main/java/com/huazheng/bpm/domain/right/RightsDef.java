package com.huazheng.bpm.domain.right;

import com.huazheng.bpm.dao.right.RightsDefDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.rights.RightsDefPo;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.db.UniqueIdUtil;
import com.huazheng.bpm.util.json.JsonUtil;
import com.huazheng.bpm.util.string.StringUtil;
import org.springframework.context.annotation.Scope;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@SuppressWarnings("serial")
@Transactional
@Scope("prototype")
public class RightsDef extends AbstractDomain<String, RightsDefPo> {

    @Resource
    private RightsDefDao rightsDefDao;

    @Override
    protected void init() {
        this.setDao(rightsDefDao);
    }

    @SuppressWarnings("unchecked")
    public void save(String entityType, String entityId, String rights) {
        rightsDefDao.delByTypeId(entityType, entityId);
        List<RightsDefPo> RightsDefPoList = JsonUtil.getDTOList(rights, RightsDefPo.class);
        if (BeanUtils.isEmpty(RightsDefPoList)) {
            return;
        }
        for (RightsDefPo RightsDefPo : RightsDefPoList) {
            String rightsIds = RightsDefPo.getRightsId();
            if (StringUtil.isEmpty(rightsIds)) {
                RightsDefPo.setId(UniqueIdUtil.getId());
                RightsDefPo.setEntityType(entityType);
                RightsDefPo.setEntityId(entityId);
                rightsDefDao.create(RightsDefPo);
            } else {
                String[] rightsIdAry = StringUtil.split(rightsIds, ",");
                String[] rightsNameAry = StringUtil.split(RightsDefPo.getRightsName(), ",");
                for (int i = 0; i < rightsIdAry.length; i++) {
                    RightsDefPo def = new RightsDefPo();
                    def.setId(UniqueIdUtil.getId());
                    def.setEntityType(entityType);
                    def.setEntityId(entityId);
                    def.setType(RightsDefPo.getType());
                    def.setRightsId(rightsIdAry[i]);
                    def.setRightsName(rightsNameAry[i]);
                    def.setCreateTime(new Date());
                    rightsDefDao.create(def);
                }
            }
        }
    }

    /**
     * 权限重置
     *
     * @param vo
     */
    public void removeRights(RightsVo vo) {
        String[] rightsIds = vo.getRightsIds();
        for (String rightsId : rightsIds) {
            rightsDefDao.deleteByParams(vo.getEntityId(), vo.getEntityType(), rightsId, vo.getRightsType());
        }
    }

    /**
     * 根据实体id列表、实体类型删除权限数据
     *
     * @param entityType
     * @param entityIds
     */
    public void delByIdType(String entityType, String... entityIds) {
        for (String entityId : entityIds) {
            rightsDefDao.delByTypeId(entityType, entityId);
        }
    }

}
