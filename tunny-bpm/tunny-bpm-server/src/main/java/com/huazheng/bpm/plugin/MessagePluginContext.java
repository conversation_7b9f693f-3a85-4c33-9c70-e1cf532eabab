//package com.huazheng.bpm.plugin;
//
//import com.huazheng.bpm.entity.constant.EventType;
//import com.huazheng.bpm.entity.constant.ServiceTaskType;
//import com.huazheng.bpm.model.node.UserAssignRule;
//import com.huazheng.bpm.util.base.XmlUtil;
//import com.huazheng.bpm.util.core.BeanUtils;
//import com.huazheng.bpm.util.core.JAXBUtil;
//import com.huazheng.bpm.util.helper.UserAssignRuleParser;
//import com.huazheng.bpm.util.json.JsonUtil;
//import com.huazheng.bpm.util.string.StringUtil;
//import com.jamesmurty.utils.XMLBuilder;
//import net.sf.json.*;
//import org.w3c.dom.Element;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 消息插件
// * <pre>
// * 构建组：ibps-bpmn-plugin
// * 作者：eddy
// * 邮箱：<EMAIL>
// * 日期：2016年12月13日-下午7:31:35
// * 版权：广州流辰信息技术有限公司版权所有
// * </pre>
// */
//@SuppressWarnings("serial")
//public class MessagePluginContext extends AbstractBpmExecutionPluginContext {
//
//	@Override
//	public List<EventType> getEventTypeList() {
//		List<EventType> list=new ArrayList<EventType>();
//		list.add(EventType.AUTO_TASK_EVENT);
//		return list;
//	}
//
//	public Class<? extends IRuntimePlugin<?, ?, ?>> getPluginClass() {
//		return MessagePlugin.class;
//	}
//
//	/**
//	 * 插件的XML格式。
//	 *<pre>
//	 *&lt;?xml version="1.0" encoding="UTF-8"?>
//	*&lt;message xmlns="http://www.bpmhome.cn/bpm/plugins/execution/message" >
//    *&lt;html msgType="">
//    *    &lt;userRule xmlns="http://www.bpmhome.cn/bpm/plugins/userCalc/base" groupNo="1">
//    *        &lt;calcs>&lt;/calcs>
//    *    &lt;/userRule>
//    *    &lt;subject>&lt;/subject>
//    *    &lt;content>&lt;/content>
//    *&lt;/html>
//    *&lt;plainText msgType="">
//    *    &lt;userRule xmlns="http://www.bpmhome.cn/bpm/plugins/userCalc/base" groupNo="1">
//    *        &lt;calcs>&lt;/calcs>
//    *    &lt;/userRule>
//    *    &lt;content>&lt;/content>
//    *&lt;/plainText>
//	*&lt;/message>
//	*</pre>
//	 */
//	@Override
//	public String getPluginXml() {
//		MessagePluginDefine pluginDef=(MessagePluginDefine) this.getBpmPluginDefine();
//		try {
//			XMLBuilder xmlBuilder = XMLBuilder.create("message")
//					.a("xmlns", "http://www.bpmhome.cn/bpm/plugins/execution/message")
//					.a("subject",pluginDef.getSubject())
//					.a("notifyType", pluginDef.getNotifyType());
//
//			if(StringUtil.isNotEmpty(pluginDef.getExternalClass()))
//				xmlBuilder.a("externalClass", pluginDef.getExternalClass());
//
//			xmlBuilder= xmlBuilder
//					.e("html").d(pluginDef.getHtml()).up()
//					.e("plainText").d(pluginDef.getPlainText()).up();
//
//			UserAssignRuleParser.handXmlBulider(xmlBuilder, pluginDef.getUsers());
//
//
//			return xmlBuilder.asString();
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return "";
//	}
//
//	@Override
//	public String getJson() {
//		MessagePluginDefine pluginDef=(MessagePluginDefine)this.getBpmPluginDefine();
//
//		List<UserAssignRule> assignRules=new ArrayList<UserAssignRule>();
//
//
//		assignRules.addAll(pluginDef.getUsers());
//
//		JsonConfig config=new JsonConfig();
//
//		config.registerJsonBeanProcessor(this.getClass(),new PluginContextPluginTypeProcessor());
//
//		UserAssignRuleParser.handJsonConfig(config, assignRules);
//
//		JSON json= JSONSerializer.toJSON(pluginDef, config);
//
//		return json.toString();
//	}
//
//	/**
//	* {"externalClass":"com.lc.Demo","htmlSetting":{"content":"","msgType":[],
//	*"ruleList":[{"calcs":[{"account":"zhangyg","extract":"no","logicCal":"or","pluginName":"",
//	*	"source":"spec","userName":"zhangyg","var":"","pluginType":"cusers","description":"zhangyg"}],
//	*"condition":"aaa>0","conditionMode":"","description":"","groupNo":1,"name":""}],"subject":""},
//	*"plainTextSetting":{"content":"","msgType":"","ruleList":[]},"pluginName":""}
//	 */
//	@Override
//	protected IBpmPluginDefine parseJson(String pluginJson) {
//		JSONObject jsonObject=JSONObject.fromObject(pluginJson);
//
//		return  handPluginDef(jsonObject);
//	}
//
//	private MessagePluginDefine handPluginDef(JSONObject jsonObject) {
//		MessagePluginDefine pluginDef=new MessagePluginDefine();
//		if(jsonObject.containsKey("externalClass")){
//			String externalClass = jsonObject.getString("externalClass");
//			pluginDef.setExternalClass(externalClass);
//		}
//
//		pluginDef.setNotifyType(JsonUtil.getString(jsonObject, "notifyType") );
//
//		pluginDef.setSubject(JsonUtil.getString(jsonObject, "subject") );
//
//		pluginDef.setHtml(JsonUtil.getString(jsonObject, "html"));
//
//		pluginDef.setPlainText(JsonUtil.getString(jsonObject, "plainText"));
//
//
//		List<UserAssignRule> ruleList= getRulesByJson(jsonObject);
//		pluginDef.setUsers(ruleList);
//
//		return pluginDef;
//
//	}
//
//
//	/**
//	 * 根据JSONArray返回用户规则列表。
//	 * @param jsonAry
//	 * @return  List&lt;UserAssignRule>
//	 */
//	private List<UserAssignRule> getRulesByJson(JSONObject jsonObject) {
//		List<UserAssignRule> rules=new ArrayList<UserAssignRule>();
//		Object object  = jsonObject.get("users");
//		if(JsonUtil.isEmpty(object))
//			return rules;
//		JSONArray rulesAry = (JSONArray) object;
//
//		if(BeanUtils.isEmpty(rulesAry)) return rules;
//
//		for(Object obj:rulesAry){
//			UserAssignRule rule= UserAssignRuleParser.getUserAssignRule((JSONObject) obj);
//			rules.add(rule);
//		}
//		return rules;
//	}
//
//	@Override
//	protected IBpmPluginDefine parseElement(Element element) {
//		String xml = XmlUtil.getXML(element);
//		MessagePluginDefine pluginDef=new MessagePluginDefine();
//		try {
//			Message message = (Message)JAXBUtil.unmarshall(xml,com.lc.ibps.bpmn.plugin.execution.message.entity.ObjectFactory.class);
//			//外部数据获取类。
//			String externalClass=message.getExternalClass();
//			if(StringUtil.isNotEmpty(externalClass))
//				pluginDef.setExternalClass(externalClass);
//
//			pluginDef.setNotifyType(message.getNotifyType());
//			pluginDef.setSubject(message.getSubject());
//			pluginDef.setHtml(message.getHtml());
//			pluginDef.setPlainText(message.getPlainText());
//
//
//			List<UserAssignRule> list=UserAssignRuleParser.parse(element);
//
//			pluginDef.setUsers(list);
//
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return pluginDef;
//	}
//
//	@Override
//	public String getTitle() {
//		return ServiceTaskType.MESSAGE.getKey();
//	}
//
//}
