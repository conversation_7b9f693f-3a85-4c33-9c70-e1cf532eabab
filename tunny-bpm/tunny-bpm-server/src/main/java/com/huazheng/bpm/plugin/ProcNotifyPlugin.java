package com.huazheng.bpm.plugin;

import com.huazheng.bpm.entity.constant.BpmConstants;
import com.huazheng.bpm.entity.constant.TemplateVar;
import com.huazheng.bpm.model.base.NotifyVo;
import com.huazheng.bpm.model.core.ProcNotifyPluginDefine;
import com.huazheng.bpm.model.delegate.BpmDelegateExecution;
import com.huazheng.bpm.util.base.ContextThreadUtil;
import com.huazheng.bpm.util.cmd.ActionCmd;
import com.huazheng.bpm.util.cmd.TaskFinishCmd;

import java.util.Map;

/**
 * TODO（用一句话描述这个类做什么用）。
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午7:39:06
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class ProcNotifyPlugin extends AbstractBpmExecutionPlugin {

    public Void execute(BpmExecutionPluginSession pluginSession,
                        IBpmExecutionPluginDefine pluginDef) {
        BpmDelegateExecution delegateExecution = pluginSession.getBpmDelegateExecution();
        //获得流程变量
        Map<String, Object> variables = delegateExecution.getVariables();
        String procInstId = (String) variables.get(BpmConstants.PROCESS_INST_ID);

        //获取通知项
        NotifyVo notifyVo = ((ProcNotifyPluginDefine) pluginDef).getNotifyVoMap().get(pluginSession.getEventType());

        if (notifyVo != null) {
            ActionCmd actionCmd = ContextThreadUtil.getActionCmd(procInstId);
            if (actionCmd instanceof TaskFinishCmd) {
                String cause = ((TaskFinishCmd) actionCmd).getApprovalOpinion();
                variables.put(TemplateVar.CAUSE.getKey(), cause);
            }
        }

        return null;
    }

}
