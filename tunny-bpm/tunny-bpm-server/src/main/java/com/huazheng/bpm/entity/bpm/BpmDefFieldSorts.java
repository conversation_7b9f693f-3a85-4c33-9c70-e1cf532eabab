package com.huazheng.bpm.entity.bpm;


import com.huazheng.bpm.domain.mybatis.DefaultFieldSort;
import com.huazheng.bpm.util.base.Direction;
import com.huazheng.bpm.util.base.FieldSort;

import java.util.ArrayList;
import java.util.List;

/**
 * 简化流程定义排序字段的构造（对使用者屏蔽字段名称）
 * <pre>
 * 构建组：ibps-bpmn-biz
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月20日-上午11:34:00
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class BpmDefFieldSorts {
    private List<FieldSort> fieldSorts = new ArrayList<FieldSort>();

    /**
     * fieldSorts
     *
     * @return the fieldSorts
     * @since 1.0.0
     */

    public List<FieldSort> getFieldSorts() {
        return fieldSorts;
    }

    public BpmDefFieldSorts addDefId() {
        DefaultFieldSort defaultFieldSort = new DefaultFieldSort("DEF_ID_", Direction.DESC);
        fieldSorts.add(defaultFieldSort);
        return this;
    }

    public BpmDefFieldSorts addCreateTime() {
        DefaultFieldSort defaultFieldSort = new DefaultFieldSort("CREATE_TIME_", Direction.DESC);
        fieldSorts.add(defaultFieldSort);
        return this;
    }

    public BpmDefFieldSorts addIsMain() {
        DefaultFieldSort defaultFieldSort = new DefaultFieldSort("IS_MAIN_", Direction.DESC);
        fieldSorts.add(defaultFieldSort);
        return this;
    }
}
