package com.huazheng.bpm.plugin;

import com.huazheng.bpm.util.base.FreemarkerEngine;
import com.huazheng.bpm.util.core.AppUtil;

/**
 * 抽象插件运行实例
 * <pre>
 * 构建组：ibps-bpmn-plugin-base
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月14日-上午9:29:29
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public abstract class AbstractBpmPlugin {

    private FreemarkerEngine freemarkerEngine;

    protected FreemarkerEngine getFreemarkerEngine() {
        if (freemarkerEngine == null) {
            freemarkerEngine = AppUtil.getBean(FreemarkerEngine.class);
        }
        return freemarkerEngine;
    }

}
