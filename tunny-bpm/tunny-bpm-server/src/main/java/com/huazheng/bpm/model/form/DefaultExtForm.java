package com.huazheng.bpm.model.form;


import com.huazheng.bpm.model.node.IExtForm;

/**
 * 表单扩展
 * <pre>
 * 构建组：ibps-bpmn-biz
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-上午10:27:00
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class DefaultExtForm extends DefaultForm implements IExtForm {
    private static final long serialVersionUID = 1L;

    private String prevHandler;

    private String postHandler;

    public String getPrevHandler() {
        return prevHandler;
    }

    public void setPrevHandler(String prevHandler) {
        this.prevHandler = prevHandler;
    }

    public String getPostHandler() {
        return postHandler;
    }

    public void setPostHandler(String postHandler) {
        this.postHandler = postHandler;
    }

}
