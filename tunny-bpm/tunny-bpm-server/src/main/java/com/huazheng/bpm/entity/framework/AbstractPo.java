package com.huazheng.bpm.entity.framework;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.core.JacksonUtil;
import com.huazheng.bpm.util.core.StringPool;
import com.huazheng.bpm.util.helper.MapBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * 抽象PO。
 *
 * <pre>
 * 构建组：ibps-base-framework-ddd
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2017年6月13日-下午6:27:42
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
public abstract class AbstractPo<PK extends Serializable> implements PO<PK>, Serializable {

    protected PK pk;
    protected String name;
    protected String ip;
    protected String createBy;
    @JsonFormat(pattern = StringPool.DATE_FORMAT_DATETIME)
    protected Date createTime;
    protected String updateBy;
    @JsonFormat(pattern = StringPool.DATE_FORMAT_DATETIME)
    protected Date updateTime;
    /**
     * 创建人组织ID
     */
    protected String createOrgId;

    public void setPk(PK id) {
        this.pk = id;
        if (id instanceof String) {
            setId((String) id);
        }
    }

    public String getPk() {
        if (pk instanceof String) {
            return (String) pk;
        }
        return "";
    }

    public abstract void setId(String id);

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateOrgId() {
        return createOrgId;
    }

    public void setCreateOrgId(String createOrgId) {
        this.createOrgId = createOrgId;
    }

    /**
     * 创建Map构建工具
     *
     * @return {@link com.lc.ibps.base.framework.helper.MapBuilder}
     */
    public MapBuilder b() {
        return new MapBuilder();
    }

    /**
     * 将对象以JSON字符串输出
     *
     * @return json字符串
     */
    public String toJsonString() {
        return JacksonUtil.toJsonString(this);
    }

    @Override
    public String toString() {
        return toJsonString();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (BeanUtils.isEmpty(this.getId())) return false;
        if (BeanUtils.isEmpty(((AbstractPo<?>) obj).getId())) return false;

        return this.getId().equals(((AbstractPo<?>) obj).getId());
    }
}
