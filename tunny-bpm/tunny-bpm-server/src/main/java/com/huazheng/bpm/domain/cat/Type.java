package com.huazheng.bpm.domain.cat;

import com.huazheng.bpm.dao.cat.*;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.cat.CategoryPo;
import com.huazheng.bpm.entity.cat.TypePo;
import com.huazheng.bpm.entity.cat.TypeXml;
import com.huazheng.bpm.model.cat.CategoryConstants;
import com.huazheng.bpm.model.cat.TypeVo;
import com.huazheng.bpm.repository.TypeRepository;
import com.huazheng.bpm.util.base.Dom4jUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.core.JAXBUtil;
import com.huazheng.bpm.util.core.StringPool;
import com.huazheng.bpm.util.db.UniqueIdUtil;
import com.huazheng.bpm.util.string.StringUtil;
import org.dom4j.Document;
import org.dom4j.Element;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.xml.bind.JAXBException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * 类型领域驱动
 *
 * <pre>
 *
 * 构建组：ibps-common-biz
 * 作者：simon cai
 * 邮箱：<EMAIL>
 * 日期：2016年9月19日-上午10:42:46
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
@Transactional
@Scope("prototype")
@Service
public class Type extends AbstractDomain<String, TypePo> {
    @Resource
    private TypeDao typeDao;
    @Resource
    private TypeQueryDao typeQueryDao;
    @Resource
    private CategoryDao categoryDao;
    @Resource
    private CategoryQueryDao categoryQueryDao;
    @Resource
    private DictionaryDao dictionaryDao;
    @Resource
    TypeRepository typeRepository;

    @Override
    protected void init() {
        this.setDao(typeDao);
    }

    /**
     * 根据分类id，更新序列号
     *
     * @param typeIds
     */
    public void sortSave(String[] typeIds) {
        for (int i = 0; i < typeIds.length; i++) {
            String typeId = typeIds[i];
            int sn = i + 1;
            typeDao.updateSn(typeId, sn);
        }
    }

    /**
     * 根据id删除所有相关的分类信息
     *
     * @param id
     */
    public void delCascadeById(String id) {
        TypePo type = typeQueryDao.get(id);
        if (BeanUtils.isEmpty(type)) {
            return;
        }
        boolean isDict = (CategoryConstants.CAT_DIC.key()).equals(type.getCategoryKey());
        // 根据path获取所有的子数据
        String path = type.getPath();
        if (StringUtil.isEmpty(path)) {
            path = null;
        }
        List<TypePo> typeList = typeQueryDao.getByPath(path);
        typeList.add(type);
        for (TypePo typeTemp : typeList) {
            typeDao.delete(typeTemp.getId());
            if (isDict) {
                // 如果是数据字典类型的，则把数据字典数据也删除
                dictionaryDao.delByTypeId(typeTemp.getId());
            }
        }
    }

    /**
     * 分类导入
     *
     * @param inputStream
     * @param typeId
     * @throws JAXBException
     * @throws UnsupportedEncodingException
     */
    public void importXml(InputStream inputStream, String typeId, String userId)
            throws UnsupportedEncodingException, JAXBException {
        Document doc = Dom4jUtil.loadXml(inputStream);
        Element root = doc.getRootElement();
        // 验证格式是否正确
        this.checkXMLFormat(root);

        String xmlStr = root.asXML();
        TypeXml typeXml = (TypeXml) JAXBUtil.unmarshall(xmlStr, TypeXml.class);

        dealTypeByXml(typeXml, typeId, userId);
    }

    private void checkXMLFormat(Element root) {
        // TODO Auto-generated method stub

    }

    private void dealTypeByXml(TypeXml typeXml, String typeId, String userId) {
        if (BeanUtils.isEmpty(typeXml)) {
            return;
        }
        TypePo type = typeQueryDao.get(typeId);
        String categoryKey = null;
        boolean isRoot = true;
        if (BeanUtils.isEmpty(type)) {
            CategoryPo category = categoryQueryDao.get(typeId);
            categoryKey = category.getCategoryKey();
        } else {
            categoryKey = type.getCategoryKey();
            isRoot = false;
        }

        Short isCate = typeXml.getIsCate();

        String path = typeId + ".";
        if (!isRoot) {
            path = type.getPath() + ".";
        }
        String parentId = typeId;
        if (BeanUtils.isEmpty(isCate) || isCate == 0) {
            TypePo rootType = typeQueryDao.getByCategoryKeyAndTypeKey(categoryKey, typeXml.getTypeKey());

            if (BeanUtils.isEmpty(rootType)) {
                rootType = buildType(typeXml, parentId, path, categoryKey, userId);
                typeDao.create(rootType);
            }
            parentId = rootType.getId();
            path = rootType.getPath();
        }
        dealImpSubType(typeXml.getTypes(), parentId, path, categoryKey, userId);
    }

    private void dealImpSubType(List<TypeXml> types, String parentId, String path, String categoryKey, String userId) {
        if (BeanUtils.isEmpty(types)) {
            return;
        }

        for (TypeXml typeXml : types) {
            TypePo type = typeQueryDao.getByCategoryKeyAndTypeKey(categoryKey, typeXml.getTypeKey());

            if (BeanUtils.isEmpty(type)) {
                type = buildType(typeXml, parentId, path, categoryKey, userId);
                typeDao.create(type);
            }
            dealImpSubType(typeXml.getTypes(), type.getId(), type.getPath(), categoryKey, userId);
        }
    }

    private TypePo buildType(TypeXml typeXml, String parentId, String path, String categoryKey, String userId) {
        TypePo type = new TypePo();
        String id = UniqueIdUtil.getId();
        type.setId(id);
        type.setName(typeXml.getName());
        type.setStruType(typeXml.getStruType());
        type.setTypeKey(typeXml.getTypeKey());
        type.setCategoryKey(categoryKey);

        type.setPath(path + id + ".");
        type.setParentId(parentId);
        type.setOwnerId("0");
        type.setCreateBy(userId);
        type.setDepth(1);
        type.setSn(0);
        if (BeanUtils.isNotEmpty(typeXml.getTypes())) {
            type.setIsLeaf(StringPool.N);
        } else {
            type.setIsLeaf(StringPool.Y);
        }
        return type;
    }

    public String saveType(TypeVo typeVo) {
        // 初始提示信息字符
        String resultMsg = null;
        String id = typeVo.getId();

        // 当不是数据字典，默认给树形值
        if (BeanUtils.isEmpty(typeVo.getStruType())) {
            typeVo.setStruType(TypePo.STRUTYPE_TREE);
        }

        if (StringUtil.isEmpty(id)) {
            typeVo.setId(UniqueIdUtil.getId());
            // 如果不是根节点，则需要更新分类的叶子
            if (!typeVo.getIsRoot()) {
                TypePo parentType = typeQueryDao.get(typeVo.getParentId());
                if (BeanUtils.isNotEmpty(parentType)) {
                    parentType.setIsLeaf(TypePo.IS_LEAF_NO);
                    this.setData(parentType);
                    this.update();
                }
            }
            TypePo po = typeRepository.initFromTypeVo(typeVo);
            this.setData(po);
            this.create();
            resultMsg = "添加分类成功";
        } else {
            TypePo po = typeRepository.get(id);
            po.setName(typeVo.getName());
            po.setTypeKey(typeVo.getTypeKey());
            po.setStruType(typeVo.getStruType());
            this.setData(po);
            this.update();
            resultMsg = "更新分类成功";
        }
        return resultMsg;
    }

}
