//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4-2
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.08.25 at 03:37:49 PM CST
//


package com.huazheng.bpm.entity.core;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for authdef complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="authdef">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="authId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="defKey" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="defName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="rights" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "authdef", propOrder = {
        "id",
        "authId",
        "defKey",
        "defName",
        "rights"
})
public class Authdef {

    @XmlElement(required = true)
    protected String id;
    @XmlElement(required = true)
    protected String authId;
    @XmlElement(required = true)
    protected String defKey;
    @XmlElement(required = true)
    protected String defName;
    @XmlElement(required = true)
    protected String rights;

    /**
     * Gets the value of the id property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setId(String value) {
        this.id = value;
    }

    /**
     * Gets the value of the authId property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getAuthId() {
        return authId;
    }

    /**
     * Sets the value of the authId property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setAuthId(String value) {
        this.authId = value;
    }

    /**
     * Gets the value of the defKey property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getDefKey() {
        return defKey;
    }

    /**
     * Sets the value of the defKey property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDefKey(String value) {
        this.defKey = value;
    }

    /**
     * Gets the value of the defName property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getDefName() {
        return defName;
    }

    /**
     * Sets the value of the defName property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDefName(String value) {
        this.defName = value;
    }

    /**
     * Gets the value of the rights property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getRights() {
        return rights;
    }

    /**
     * Sets the value of the rights property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setRights(String value) {
        this.rights = value;
    }

}
