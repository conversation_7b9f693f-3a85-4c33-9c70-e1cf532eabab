package com.huazheng.bpm.plugin.usercalc.role.context;

import com.huazheng.bpm.plugin.AbstractUserCalcPluginContext;
import com.huazheng.bpm.plugin.IBpmPluginDefine;
import com.huazheng.bpm.plugin.IPluginParser;
import com.huazheng.bpm.plugin.usercalc.role.def.RolePluginDefine;
import com.huazheng.bpm.plugin.usercalc.role.runtime.RolePlugin;
import com.huazheng.bpm.util.base.XmlUtil;
import com.jamesmurty.utils.XMLBuilder;
import net.sf.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.w3c.dom.Element;

import javax.xml.parsers.FactoryConfigurationError;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;

/**
 * 角色插件上下文
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月14日-上午10:24:01
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
@Service("rolePluginContext")
@Scope("prototype")
public class RolePluginContext extends AbstractUserCalcPluginContext implements IPluginParser {

    /*
    <role xmlns="http://www.bpmhome.cn/bpm/plugins/userCalc/role"
          source="" logicCal="" extract="">
        <roles roleKey="" roleName=""/>
    </role>
    */
    @Override
    public String getDescription() {
        RolePluginDefine def = (RolePluginDefine) this.getBpmPluginDefine();
        String source = def.getSource();
        StringBuffer sb = new StringBuffer();

        if ("start".equals(source)) {
            sb.append("发起人角色");
        } else if ("prev".equals(source)) {
            sb.append("上一步执行人角色");
        } else if ("spec".equals(source)) {
            sb.append("指定角色");
            sb.append("[");
            sb.append(def.getRoleName());
            sb.append("]");
        } else if ("node".equals(source)) {
            sb.append("节点[").append(def.getNodeName()).append("]");
        }

        return sb.toString();
    }

    @Override
    public String getTitle() {
        return "角色";
    }

    @Override
    public Class<? extends RolePlugin> getPluginClass() {
        return RolePlugin.class;
    }

    @Override
    public String getPluginXml() {
        RolePluginDefine def = (RolePluginDefine) this.getBpmPluginDefine();
        String source = def.getSource();

        XMLBuilder xmlBuilder;
        try {
            xmlBuilder = XMLBuilder.create("role")
                    .a("xmlns", "http://www.bpmhome.cn/bpm/plugins/userCalc/role")
                    .a("source", source)
                    .a("logicCal", def.getLogicCal().getKey())
                    .a("extract", def.getExtract().getKey());
            if ("spec".equals(source)) {
                xmlBuilder.e("roles")
                        .a("roleKey", def.getRoleKey())
                        .a("roleName", def.getRoleName());
            } else if ("node".equals(source)) {
                xmlBuilder.e("nodes").a("nodeId", def.getNodeId()).a("nodeName", def.getNodeName()).up();
            }

            return xmlBuilder.asString();
        } catch (ParserConfigurationException e) {
            e.printStackTrace();
        } catch (FactoryConfigurationError e) {
            e.printStackTrace();
        } catch (TransformerException e) {
            e.printStackTrace();
        }
        return "";
    }

    @Override
    protected IBpmPluginDefine parseElement(Element element) {
        RolePluginDefine def = new RolePluginDefine();

        String source = element.getAttribute("source");
        def.setSource(source);
        if ("spec".equals(source)) {
            Element rolesEl = XmlUtil.getChildNodeByName(element, "roles");
            String roleKey = rolesEl.getAttribute("roleKey");
            String roleName = rolesEl.getAttribute("roleName");
            def.setRoleKey(roleKey);
            def.setRoleName(roleName);
        } else if ("node".equals(source)) {
            Element memberEl = XmlUtil.getChildNodeByName(element, "nodes");
            String nodeId = memberEl.getAttribute("nodeId");
            String nodeName = memberEl.getAttribute("nodeName");

            def.setNodeId(nodeId);
            def.setNodeName(nodeName);
        }

        return def;
    }

    @Override
    protected IBpmPluginDefine parseJson(JSONObject pluginJson) {
        RolePluginDefine def = new RolePluginDefine();
        String source = pluginJson.getString("source");
        def.setSource(source);
        if ("spec".equals(source)) {
            String roleKey = pluginJson.getString("roleKey");
            String roleName = pluginJson.getString("roleName");
            def.setRoleKey(roleKey);
            def.setRoleName(roleName);
        } else if ("node".equals(source)) {
            String nodeId = pluginJson.getString("nodeId");
            String nodeName = pluginJson.getString("nodeName");
            def.setNodeId(nodeId);
            def.setNodeName(nodeName);
        }

        return def;
    }

}
