package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmTaskReminderRecDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmTaskReminderRecPo;
import com.huazheng.bpm.util.core.AppUtil;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

/**
 * 任务催办记录 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-03-22 16:02:00
 * </pre>
 */
@SuppressWarnings("serial")
@Service
@Scope("prototype")
public class BpmTaskReminderRec extends AbstractDomain<String, BpmTaskReminderRecPo> {

    private BpmTaskReminderRecDao bpmTaskReminderRecDao = null;


    protected void init() {
        bpmTaskReminderRecDao = AppUtil.getBean(BpmTaskReminderRecDao.class);
        this.setDao(bpmTaskReminderRecDao);
    }


    /**
     * 清除催办记录数据
     */
    public void delExpired() {
        bpmTaskReminderRecDao.delExpired();
    }

}
