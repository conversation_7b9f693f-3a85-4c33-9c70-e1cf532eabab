package com.huazheng.bpm.plugin;

/**
 * 抽象的插件定义基类
 * <pre>
 * 构建组：ibps-bpmn-plugin-base
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午6:57:18
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
public abstract class AbstractBpmPluginDefine implements IBpmPluginDefine {

    /**
     * 插件名称
     */
    protected String pluginName;

    public String getPluginName() {
        return pluginName;
    }

    public void setPluginName(String pluginName) {
        this.pluginName = pluginName;
    }

}
