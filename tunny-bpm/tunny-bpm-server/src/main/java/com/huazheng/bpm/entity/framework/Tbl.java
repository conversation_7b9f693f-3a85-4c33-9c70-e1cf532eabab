package com.huazheng.bpm.entity.framework;

import java.util.Date;

/**
 * 表接口。
 *
 * <pre>
 *
 * 构建组：ibps-base-framework-ddd
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2017年6月13日-下午6:29:09
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface Tbl<PK> {
    /**
     * 主键
     */
    PK getId();

    /**
     * 名称
     */
    String getName();

    /**
     * 创建人
     */
    String getCreateBy();

    /**
     * 创建时间
     */
    Date getCreateTime();

    /**
     * 更新人
     */
    String getUpdateBy();

    /**
     * 更新时间
     */
    Date getUpdateTime();
}
