//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4-2
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.08.25 at 03:37:49 PM CST
//


package com.huazheng.bpm.entity.core;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for trigerFlow complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="trigerFlow">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="defId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="nodeId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="trigerFlowKey" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="action" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="trigerType" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="callStartPage" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="trigerParams" type="{http://www.bpmhome.cn/ibps-bpm-def}trigerParams" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "trigerFlow", propOrder = {
        "defId",
        "nodeId",
        "trigerFlowKey",
        "action",
        "trigerType",
        "callStartPage",
        "trigerParams"
})
public class TrigerFlow {

    @XmlElement(required = true)
    protected String defId;
    @XmlElement(required = true)
    protected String nodeId;
    protected String trigerFlowKey;
    protected String action;
    protected String trigerType;
    protected String callStartPage;
    protected TrigerParams trigerParams;

    /**
     * Gets the value of the defId property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getDefId() {
        return defId;
    }

    /**
     * Sets the value of the defId property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDefId(String value) {
        this.defId = value;
    }

    /**
     * Gets the value of the nodeId property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getNodeId() {
        return nodeId;
    }

    /**
     * Sets the value of the nodeId property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setNodeId(String value) {
        this.nodeId = value;
    }

    /**
     * Gets the value of the trigerFlowKey property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getTrigerFlowKey() {
        return trigerFlowKey;
    }

    /**
     * Sets the value of the trigerFlowKey property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setTrigerFlowKey(String value) {
        this.trigerFlowKey = value;
    }

    /**
     * Gets the value of the action property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getAction() {
        return action;
    }

    /**
     * Sets the value of the action property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setAction(String value) {
        this.action = value;
    }

    /**
     * Gets the value of the trigerType property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getTrigerType() {
        return trigerType;
    }

    /**
     * Sets the value of the trigerType property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setTrigerType(String value) {
        this.trigerType = value;
    }

    /**
     * Gets the value of the callStartPage property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getCallStartPage() {
        return callStartPage;
    }

    /**
     * Sets the value of the callStartPage property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCallStartPage(String value) {
        this.callStartPage = value;
    }

    /**
     * Gets the value of the trigerParams property.
     *
     * @return possible object is
     * {@link TrigerParams }
     */
    public TrigerParams getTrigerParams() {
        return trigerParams;
    }

    /**
     * Sets the value of the trigerParams property.
     *
     * @param value allowed object is
     *              {@link TrigerParams }
     */
    public void setTrigerParams(TrigerParams value) {
        this.trigerParams = value;
    }

}
