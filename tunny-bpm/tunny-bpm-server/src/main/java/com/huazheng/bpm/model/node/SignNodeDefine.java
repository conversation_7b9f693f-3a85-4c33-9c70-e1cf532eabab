package com.huazheng.bpm.model.node;

import java.util.List;

/**
 * 会签定义，这里定义会签的规则和特权
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午12:08:41
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
public class SignNodeDefine extends UserTaskNodeDefine {

    /**
     * 是否串行会签。
     */
    private boolean isParallel = false;

    /**
     * 会签规则
     */
    private SignRule signRule;

    /**
     * 特权模式。
     */
    private List<PrivilegeItem> privilegeList;

    @Override
    public void setParallel(boolean isParallel) {
        this.isParallel = isParallel;
    }

    @Override
    public boolean isParallel() {
        return this.isParallel;
    }

    /**
     * 会签规则。
     *
     * @return SignRule
     */
    public SignRule getSignRule() {
        return signRule;
    }

    public void setSignRule(SignRule signRule) {
        this.signRule = signRule;
    }

    /**
     * 特权列表。
     *
     * @return List&lt;PrivilegeItem>
     */
    public List<PrivilegeItem> getPrivilegeList() {
        return privilegeList;
    }

    public void setPrivilegeList(List<PrivilegeItem> privilegeList) {
        this.privilegeList = privilegeList;
    }

    @Override
    public boolean supportMuliInstance() {
        return true;
    }

}
