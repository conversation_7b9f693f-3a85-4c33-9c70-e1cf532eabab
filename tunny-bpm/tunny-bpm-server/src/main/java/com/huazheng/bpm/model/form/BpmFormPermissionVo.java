package com.huazheng.bpm.model.form;

/**
 * 流程表单权限VO
 * <pre>
 * 构建组：ibps-api-form
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2017年6月8日-下午6:33:16
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class BpmFormPermissionVo {

    private String oldFlowKey;
    private String newFlowKey;

    public BpmFormPermissionVo() {
        super();
    }

    public BpmFormPermissionVo(String oldFlowKey, String newFlowKey) {
        super();
        this.oldFlowKey = oldFlowKey;
        this.newFlowKey = newFlowKey;
    }

    public String getOldFlowKey() {
        return oldFlowKey;
    }

    public void setOldFlowKey(String oldFlowKey) {
        this.oldFlowKey = oldFlowKey;
    }

    public String getNewFlowKey() {
        return newFlowKey;
    }

    public void setNewFlowKey(String newFlowKey) {
        this.newFlowKey = newFlowKey;
    }

}
