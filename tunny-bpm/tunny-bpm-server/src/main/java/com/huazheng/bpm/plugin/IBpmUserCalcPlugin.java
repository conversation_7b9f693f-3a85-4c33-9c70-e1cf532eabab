package com.huazheng.bpm.plugin;


import com.huazheng.bpm.model.base.BpmIdentity;
import com.huazheng.bpm.model.node.UserAssignRule;
import com.huazheng.bpm.util.session.BpmUserCalcPluginSession;

import java.util.List;

/**
 * 用户计算接口
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午7:18:15
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface IBpmUserCalcPlugin extends IRuntimePlugin<BpmUserCalcPluginSession, IBpmPluginDefine, List<BpmIdentity>>, IRuntimePlugin2<BpmUserCalcPluginSession, List<UserAssignRule>, List<BpmIdentity>> {

}
