package com.huazheng.bpm.model.node;

import java.io.Serializable;

/**
 * 流程节点按钮定义。
 *
 * <pre>
 *
 * 构建组：ibps-bpmn-api
 * 作者：simon cai
 * 邮箱:<EMAIL>
 * 日期:2015-6-5-下午9:10:21
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
public class Button implements Serializable {

    public Button() {
    }

    public Button(String name, String alias) {
        super();
        this.name = name;
        this.alias = alias;
    }

    public Button(String name, String alias, boolean supportScript) {
        this.name = name;
        this.alias = alias;
        this.supportScript = supportScript;
    }

    public Button(String name, String alias, String beforeScript, String afterScript) {
        this.name = name;
        this.alias = alias;
        this.beforeScript = beforeScript;
        this.afterScript = afterScript;
    }

    public Button(String name, String alias, String aliasName, Boolean supportScript) {
        super();
        this.name = name;
        this.alias = alias;
        this.aliasName = aliasName;
        this.supportScript = supportScript;
    }

    /**
     * 按钮名
     */
    protected String name;
    /**
     * 按钮别名
     */
    protected String alias;

    /**
     * 按钮code ,自定义按钮需要设置
     */
    protected String code;

    /**
     * 前置脚本
     */
    protected String beforeScript;
    /**
     * 后置脚本
     */
    protected String afterScript;
    /**
     * 是否支持脚本
     */
    protected Boolean supportScript;

    /**
     * 类型别名 ==非数据字段
     */
    protected String aliasName;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getBeforeScript() {
        return beforeScript;
    }

    public void setBeforeScript(String beforeScript) {
        this.beforeScript = beforeScript;
    }

    public String getAfterScript() {
        return afterScript;
    }

    public void setAfterScript(String afterScript) {
        this.afterScript = afterScript;
    }

    public Boolean getSupportScript() {
        return supportScript;
    }

    public void setSupportScript(Boolean supportScript) {
        this.supportScript = supportScript;
    }

    public String getAliasName() {
        return aliasName;
    }

    public void setAliasName(String aliasName) {
        this.aliasName = aliasName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

}
