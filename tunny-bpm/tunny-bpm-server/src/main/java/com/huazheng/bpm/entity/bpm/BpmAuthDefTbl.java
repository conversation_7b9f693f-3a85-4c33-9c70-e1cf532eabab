package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;


/**
 * 流程授权定义 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-02-06 15:00:58
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmAuthDefTbl extends AbstractPo<String> {
    protected String id;        /*主键*/
    protected String authId;        /*流程授权ID*/
    protected String defKey;        /*授权流程KEY*/
    protected String defName;        /*授权流程名称*/
    protected String rights;        /*授权内容*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    /**
     * 返回 流程授权ID
     *
     * @return
     */
    public String getAuthId() {
        return this.authId;
    }

    public void setDefKey(String defKey) {
        this.defKey = defKey;
    }

    /**
     * 返回 授权流程KEY
     *
     * @return
     */
    public String getDefKey() {
        return this.defKey;
    }

    public void setDefName(String defName) {
        this.defName = defName;
    }

    /**
     * 返回 授权流程名称
     *
     * @return
     */
    public String getDefName() {
        return this.defName;
    }

    public void setRights(String rights) {
        this.rights = rights;
    }

    /**
     * 返回 授权内容
     *
     * @return
     */
    public String getRights() {
        return this.rights;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("authId", this.authId)
                .append("defKey", this.defKey)
                .append("defName", this.defName)
                .append("rights", this.rights)
                .toString();
    }
}
