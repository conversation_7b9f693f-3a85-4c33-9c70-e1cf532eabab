package com.huazheng.bpm.model.node;

/**
 * 跳转规则
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午12:03:35
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class DefaultJumpRule implements IJumpRule {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    /**
     * 规则名称
     */
    private String ruleName = "";

    /**
     * 目标节点
     */
    private String targetNode = "";

    /**
     * 条件
     */
    private String condition = "";

    public DefaultJumpRule() {
    }

    public DefaultJumpRule(String ruleName, String targetNode, String condition) {
        this.ruleName = ruleName;
        this.targetNode = targetNode;
        this.condition = condition;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getTargetNode() {
        return targetNode;
    }

    public void setTargetNode(String targetNode) {
        this.targetNode = targetNode;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

}
