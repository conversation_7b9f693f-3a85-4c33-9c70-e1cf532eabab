package com.huazheng.bpm.plugin;

import com.huazheng.bpm.entity.constant.EventType;
import com.huazheng.bpm.entity.constant.ServiceTaskType;
import com.huazheng.bpm.util.base.XmlUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.json.JsonUtil;
import com.jamesmurty.utils.XMLBuilder;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;
import org.w3c.dom.Element;

import java.util.ArrayList;
import java.util.List;

/**
 * TODO（用一句话描述这个类做什么用）。
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午7:48:17
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
@Service("webServicePluginContext")
public class WebServicePluginContext extends AbstractBpmExecutionPluginContext {

    @Override
    public List<EventType> getEventTypeList() {
        List<EventType> list = new ArrayList<EventType>();
        list.add(EventType.AUTO_TASK_EVENT);
        return list;
    }

    @Override
    public Class<? extends IRuntimePlugin<?, ?, ?>> getPluginClass() {
        return WebServiceTaskPlugin.class;
    }

    /**
     * 对象转成 bpmn xml
     */
    @Override
    public String getPluginXml() {
        WebServiceNodePluginDefine def = (WebServiceNodePluginDefine) this.getBpmPluginDefine();
        XMLBuilder xmlBuilder;
        if (BeanUtils.isEmpty(def)) {
            return "";
        }
        try {
            xmlBuilder = XMLBuilder.create("webService")
                    .a("xmlns", "http://www.bpmhome.cn/bpm/plugins/execution/webService")
                    .a("url", def.getUrl())
                    .a("address", def.getAddress())
                    .a("methodName", def.getMethodName())
                    .a("namespace", def.getNamespace())
                    .a("soapAction", def.isSoapAction() ? "Y" : "N")
                    .a("serviceType", def.getServiceType())
                    .e("input").d(def.getInput()).up()
                    .e("output").d(def.getOutput());
            return xmlBuilder.asString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    @Override
    protected IBpmPluginDefine parseElement(Element element) {
        WebServiceNodePluginDefine def = new WebServiceNodePluginDefine();
        def.setUrl(element.getAttribute("url"));
        def.setAddress(element.getAttribute("address"));
        def.setMethodName(element.getAttribute("methodName"));
        def.setNamespace(element.getAttribute("namespace"));
        def.setSoapAction("Y,y,1,true,TRUE".contains(element.getAttribute("soapAction")));
        def.setServiceType(element.getAttribute("serviceType"));

        Element inputEl = XmlUtil.getChildNodeByName(element, "input");
        String input = inputEl.getTextContent();
        def.setInput(input);

        Element outputEl = XmlUtil.getChildNodeByName(element, "output");
        String output = outputEl.getTextContent();
        def.setOutput(output);

        return def;
    }

    /**
     * 从bpmn.xml中取得并转成json
     */
    @Override
    public String getJson() {
        WebServiceNodePluginDefine def = (WebServiceNodePluginDefine) this.getBpmPluginDefine();
        JSONObject object = JSONObject.fromObject(def);
        return object.toString();
    }

    /**
     * json转成对象
     */
    @Override
    protected IBpmPluginDefine parseJson(String pluginJson) {
        WebServiceNodePluginDefine bpmPluginDef = new WebServiceNodePluginDefine();
        JSONObject ser = JSONObject.fromObject(JSONObject.fromObject(pluginJson).getString("service"));
        JSONObject jsonObject = JSONObject.fromObject(ser.getString("serviceJson"));
        if (JsonUtil.isEmpty(jsonObject)) {
            return bpmPluginDef;
        }
        String serviceType = JsonUtil.getString(jsonObject, "serviceType");
        if (BeanUtils.isEmpty(serviceType)) {
            return bpmPluginDef;
        }
        bpmPluginDef.setServiceType(serviceType);
        bpmPluginDef.setUrl(jsonObject.getString("url"));
        bpmPluginDef.setMethodName(jsonObject.getString("methodName"));
        if (!"rest".equals(serviceType)) {
            bpmPluginDef.setAddress(jsonObject.getString("address"));
            bpmPluginDef.setNamespace(jsonObject.getString("namespace"));
            boolean soapAction = false;
            if ("Y,y,1,true,TRUE".contains(jsonObject.getString("soapAction"))) {
                soapAction = true;
            }
            bpmPluginDef.setSoapAction(soapAction);
            bpmPluginDef.setOutput(jsonObject.getString("output"));
        }
        bpmPluginDef.setInput(jsonObject.getString("input"));

        return bpmPluginDef;
    }

    @Override
    public String getTitle() {
        return ServiceTaskType.SERVICE.getKey();
    }

}
