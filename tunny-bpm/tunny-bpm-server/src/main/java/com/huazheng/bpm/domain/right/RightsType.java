package com.huazheng.bpm.domain.right;


import com.huazheng.bpm.model.base.IdentityConstants;

/**
 * 权限类型。
 *
 * <pre>
 * 构建组：ibps-common-biz
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年1月19日-下午2:56:04
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum RightsType {
    NONE("none", "none", "无"),
    ALL("all", IdentityConstants.ALL, "所有人"),
    USER("employee", IdentityConstants.USER, "用户"),
    ROLE("role", IdentityConstants.ROLE, "角色"),
    ORG("org", IdentityConstants.ORG, "组织(本层级)"),
    ORG_SUB("orgSub", IdentityConstants.ORG, "组织(包含子组织)"),
    POSITION("position", IdentityConstants.POSITION, "岗位");

    //key
    private String key;
    //对应的维度key
    private String dimkey;
    //显示名称
    private String label;

    RightsType(String key, String dimkey, String label) {
        this.key = key;
        this.dimkey = dimkey;
        this.label = label;
    }

    public String getKey() {
        return key;
    }

    public String getDimkey() {
        return dimkey;
    }

    public String getLabel() {
        return label;
    }

    /**
     * 通过key获取对象
     *
     * @param key
     * @return
     */
    public static RightsType fromKey(String key) {
        for (RightsType c : RightsType.values()) {
            if (c.getKey().equalsIgnoreCase(key)) {
                return c;
            }
        }
        return null;
    }
}
