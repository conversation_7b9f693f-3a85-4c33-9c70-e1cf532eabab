package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.builder.BpmOperLogBuilder;
import com.huazheng.bpm.dao.bpm.BpmTaskChangeAssignDao;
import com.huazheng.bpm.dao.bpm.BpmTaskChangeDao;
import com.huazheng.bpm.dao.bpm.BpmTaskChangeQueryDao;
import com.huazheng.bpm.dao.bpm.BpmTaskQueryDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmOperLogPo;
import com.huazheng.bpm.entity.bpm.BpmTaskChangeAssignPo;
import com.huazheng.bpm.entity.bpm.BpmTaskChangePo;
import com.huazheng.bpm.entity.bpm.BpmTaskPo;
import com.huazheng.bpm.entity.constant.BpmOperTypeEnum;
import com.huazheng.bpm.model.base.BpmIdentity;
import com.huazheng.bpm.model.delegate.BpmDelegateTask;
import com.huazheng.bpm.model.task.IBpmTask;
import com.huazheng.bpm.repository.BpmTaskRepository;
import com.huazheng.bpm.util.base.BpmOperLogThread;
import com.huazheng.bpm.util.base.ContextThreadUtil;
import com.huazheng.bpm.util.base.ThreadUtil;
import com.huazheng.bpm.util.cmd.ActionCmd;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.Map.Entry;

/**
 * 流程任务变更 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-04-11 19:45:10
 * </pre>
 */
@SuppressWarnings("serial")
@Service
@Scope("prototype")
public class BpmTaskChange extends AbstractDomain<String, BpmTaskChangePo> {

    private BpmTaskChangeDao bpmTaskChangeDao = null;
    private BpmTaskChangeQueryDao bpmTaskChangeQueryDao = null;
    private BpmTaskQueryDao bpmTaskQueryDao = null;
    private BpmTaskChangeAssignDao bpmTaskChangeAssignDao = null;

    protected void init() {
        bpmTaskChangeDao = AppUtil.getBean(BpmTaskChangeDao.class);
        bpmTaskChangeQueryDao = AppUtil.getBean(BpmTaskChangeQueryDao.class);
        bpmTaskQueryDao = AppUtil.getBean(BpmTaskQueryDao.class);
        bpmTaskChangeAssignDao = AppUtil.getBean(BpmTaskChangeAssignDao.class);
        this.setDao(bpmTaskChangeDao);
    }

    @Override
    protected void onSave() {
        if (BeanUtils.isNotEmpty(getId()))
            setNewFlag(BeanUtils.isEmpty(bpmTaskChangeQueryDao.get(getId())));
    }

    /**
     * 转办
     */
    @Deprecated
    public void shift() {
        ActionCmd actionCmd = ContextThreadUtil.getActionCmd(getData().getProcInstId());
        saveCascade();
        //saveLog(BpmOperTypeEnum.SHFIT, actionCmd.getCurUser());
    }

    /**
     * 转办
     */
    public void shift(String userId) {
        saveCascade();
        //saveLog(BpmOperTypeEnum.SHFIT, userId);
    }

    /**
     * 撤销
     */
    @Deprecated
    public void unshift() {
        ActionCmd actionCmd = ContextThreadUtil.getActionCmd(getData().getProcInstId());
        update();
        //saveLog(BpmOperTypeEnum.REVOKE, actionCmd.getCurUser());
    }

    /**
     * 撤销
     */
    public void unshift(String userId) {
        update();
        //saveLog(BpmOperTypeEnum.REVOKE, userId);
    }

    private void saveLog(BpmOperTypeEnum operType, String curUser) {
        BpmTaskRepository bpmTaskRepository = AppUtil.getBean(BpmTaskRepository.class);
        IBpmTask bpmTask = bpmTaskRepository.get(getData().getTaskId());
        // 流程操作日志
        BpmOperLogPo po = BpmOperLogBuilder.build(bpmTask, operType, curUser);
        BpmOperLogThread thread = new BpmOperLogThread(po);
        ThreadUtil.execute(ThreadUtil.THREAD_TYPE_DEFAULT, thread);
    }

    /**
     * 主从表一并保存
     * void
     *
     * @throws
     * @since 1.0.0
     */
    public void saveCascade() {
        save();
        if (getData().isDelBeforeSave()) {
            bpmTaskChangeAssignDao.deleteByMainId(getId());
        }

        if (BeanUtils.isNotEmpty(getData().getBpmTaskChangeAssignPoList())) {
            for (BpmTaskChangeAssignPo bpmTaskChangeAssignPo : getData().getBpmTaskChangeAssignPoList()) {
                //设置外键
                bpmTaskChangeAssignPo.setTaskChangeId(getId());

                bpmTaskChangeAssignDao.create(bpmTaskChangeAssignPo);
            }
        }

    }

    /**
     * 主从表一并删除
     * void
     *
     * @throws
     * @since 1.0.0
     */
    public void deleteByIdsCascade(String[] ids) {
        for (String id : ids) {
            bpmTaskChangeAssignDao.deleteByMainId(id);
        }
        deleteByIds(ids);
    }

    /**
     * 设置代理
     *
     * @param delegateTask
     * @param agentIdentityMap
     */
    public void assign(BpmDelegateTask delegateTask, Map<String, List<BpmIdentity>> agentIdentityMap) {
        if (BeanUtils.isEmpty(agentIdentityMap)) {
            return;
        }

        BpmTaskPo bpmTask = bpmTaskQueryDao.getByRelateTask(delegateTask.getId());
        Set<Entry<String, List<BpmIdentity>>> entrySet = agentIdentityMap.entrySet();
        for (Entry<String, List<BpmIdentity>> entry : entrySet) {
            BpmTaskChangePo bpmTaskChangePo = new BpmTaskChangePo();
            bpmTaskChangePo.setOwnerId(entry.getKey());
            bpmTaskChangePo.setChangeType(BpmTaskChangePo.CHANGE_ASSIGNEE);
            bpmTaskChangePo.setStatus(BpmTaskChangePo.CHANGE_STATUS_RUNNING);
            bpmTaskChangePo.setTaskId(bpmTask.getId());
            bpmTaskChangePo.setTaskName(bpmTask.getName());
            bpmTaskChangePo.setTaskSubject(bpmTask.getSubject());
            bpmTaskChangePo.setNodeId(bpmTask.getNodeId());
            bpmTaskChangePo.setProcInstId(bpmTask.getProcInstId());
            bpmTaskChangePo.setCreateTime(new Date());
            bpmTaskChangePo.setComment(BpmTaskChangePo.COMMENT_ASSIGNEE_DEF);

            List<BpmIdentity> agentIdentityList = entry.getValue();
            List<BpmTaskChangeAssignPo> agentAssignList = new ArrayList<BpmTaskChangeAssignPo>();
            for (BpmIdentity identity : agentIdentityList) {
                BpmTaskChangeAssignPo bpmTaskChangeAssignPo = new BpmTaskChangeAssignPo();
                bpmTaskChangeAssignPo.setType(identity.getType());
                bpmTaskChangeAssignPo.setExecutor(identity.getId());

                agentAssignList.add(bpmTaskChangeAssignPo);
            }
            bpmTaskChangePo.setBpmTaskChangeAssignPoList(agentAssignList);
            setData(bpmTaskChangePo);
            saveCascade();
        }
    }

    /**
     * 设置代理
     *
     * @param delegateTask
     * @param
     */
    public void complete(BpmDelegateTask delegateTask) {
        if (BeanUtils.isEmpty(delegateTask)) {
            return;
        }

        bpmTaskChangeDao.complete(delegateTask.getId(), delegateTask.getVariable("curUser").toString(), new Date());
    }

    /**
     * 根据实例ID删除转办代理数据
     *
     * @param instList
     */
    public void delByInst(List<String> instList) {
        if (BeanUtils.isEmpty(instList)) {
            return;
        }

        List<BpmTaskChangePo> rs = bpmTaskChangeQueryDao.findByInst(instList);
        if (BeanUtils.isEmpty(rs)) {
            return;
        }

        for (BpmTaskChangePo bpmTaskChangePo : rs) {
            bpmTaskChangeAssignDao.deleteByMainId(bpmTaskChangePo.getId());
            delete(bpmTaskChangePo.getId());
        }
    }
}
