package com.huazheng.bpm.plugin;


import com.huazheng.bpm.entity.inst.IBpmProcInst;
import com.huazheng.bpm.model.base.IDataObject;

/**
 * bo对象处理。
 * 用于流程修改bo实例的数据。
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：simon cai
 * 邮箱:<EMAIL>
 * 日期:2015-8-12-下午1:55:13
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface DataObjectHandler {

    /**
     * 流程启动时显示表单数据进行修改。
     * <pre>
     * 执行bo的前置脚本
     * </pre>
     *
     * @param defId
     * @param dataObject void
     */
    void handShowData(String defId, IDataObject dataObject);

    /**
     * 在任务审批时修改表单显示数据。
     * <pre>
     * 执行bo的前置脚本
     * </pre>
     *
     * @param instance
     * @param nodeId
     * @param dataObject void
     */
    void handShowData(IBpmProcInst instance, String nodeId, IDataObject dataObject);

    /**
     * 用于流程启动时修改数据。
     * <pre>
     * 执行bo的后置脚本
     * </pre>
     *
     * @param instance
     * @param dataObject void
     */
    void handSaveData(IBpmProcInst instance, IDataObject dataObject);

    /**
     * 用于流程审批时修改数据。
     * <pre>
     * 执行bo的后置脚本
     * </pre>
     *
     * @param instance
     * @param nodeId
     * @param dataObject void
     */
    void handSaveData(IBpmProcInst instance, String nodeId, IDataObject dataObject);

    /**
     * 从流程上下文中获取BO实例对象。
     * <pre>
     * bo对象从流程前置处理监听器中进行注入。
     * </pre>
     *
     * @param instanceId 流程实例ID
     * @param boCode     对应的BoCode
     * @return DataObject
     */
    IDataObject getByBoCodeFromContext(String instanceId, String boCode);
}
