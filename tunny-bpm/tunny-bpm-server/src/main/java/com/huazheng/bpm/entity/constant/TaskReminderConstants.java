package com.huazheng.bpm.entity.constant;

/**
 * 任务催办常量参数
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2017年3月22日-下午4:25:17
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class TaskReminderConstants {
    public final static String TASK_EVENT_CREATE = "create"; // 任务创建
    public final static String TASK_EVENT_COMPLETE = "complete"; // 任务完成

    public final static String TASK_TIME_TYPE_WORKTIME = "worktime"; // 工作日
    public final static String TASK_TIME_TYPE_CALTIME = "caltime"; // 日历日

    public final static String TASK_DUE_ACTION_NO_ACTION = "no-action"; // 无动作
    public final static String TASK_DUE_ACTION_AUTO_NEXT = "auto-next"; // 自动下一个任务
    public final static String TASK_DUE_ACTION_END_PROCESS = "end-process"; // 结束任务
    public final static String TASK_DUE_ACTION_CALL_METHOD = "call-method"; // 调用方法

}
