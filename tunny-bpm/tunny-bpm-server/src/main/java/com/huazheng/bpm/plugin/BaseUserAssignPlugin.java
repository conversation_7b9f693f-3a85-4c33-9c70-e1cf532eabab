package com.huazheng.bpm.plugin;


import com.huazheng.bpm.entity.constant.BpmConstants;
import com.huazheng.bpm.entity.constant.MultiInstanceType;
import com.huazheng.bpm.exception.ProcessDefException;
import com.huazheng.bpm.model.base.BpmIdentity;
import com.huazheng.bpm.model.define.IBpmProcDefine;
import com.huazheng.bpm.model.delegate.BpmDelegateTask;
import com.huazheng.bpm.model.node.IBpmNodeDefine;
import com.huazheng.bpm.model.node.IMultiInstanceDefine;
import com.huazheng.bpm.service.BpmDefineService;
import com.huazheng.bpm.service.IBpmDefineReader;
import com.huazheng.bpm.util.base.ContextThreadUtil;
import com.huazheng.bpm.util.cmd.ActionCmd;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.session.BpmTaskPluginSession;
import com.huazheng.bpm.util.string.StringUtil;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


/**
 * 基础用户分配插件， 用户计算插件继承这个类,默认的用户插件为{@linkplain com.huazheng.bpm.api.bpmn.plugin.task.userassign.plugin.UserAssignPlugin 用户插件}。
 * 这个类优先处理了如下事情，而不是从插件计算出来的用户。
 * 1.如果是多实例内部子流程的情况。
 * 从变量取得用户调用delegateTask.addExecutor方法添加用户并返回,
 * 这个变量的获取需要参考,{@linkplain com.lc.ibps.bpmn.activiti.ext.identity.ActUserService 多实例用户计算}类。
 * <p>
 * 2.如果是多实例外部子流程的情况。
 * 从变量取得用户调用delegateTask.addExecutor方法添加用户并返回,
 * 这个变量的获取需要参考:{@linkplain com.lc.ibps.bpmn.activiti.ext.identity.ActUserService 多实例用户计算}类。
 * <p>
 * 3.从上下文获取人员获取到了直接返回。
 * <p>
 * 4.实现子类实现抽象方法获取从插件获取人员。
 * <p>
 * 最终人员分配参考 {@linkplain com.lc.ibps.bpmn.listener.TaskCreateEventListener 任务创建事件监听器}类。
 * <pre>
 * 构建组：ibps-bpmn-plugin-base
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月14日-上午9:28:33
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public abstract class BaseUserAssignPlugin extends AbstractBpmTaskPlugin {

    @Resource
    private IBpmDefineReader bpmDefineReader;
    @Resource
    private BpmDefineService bpmDefineService;

    public abstract void executeExt(BpmTaskPluginSession pluginSession, IBpmTaskPluginDefine pluginDef);

    @SuppressWarnings("unchecked")
    @Override
    public Void execute(BpmTaskPluginSession pluginSession,
                        IBpmTaskPluginDefine pluginDef) {
        BpmDelegateTask bpmTask = pluginSession.getBpmDelegateTask();
        //子流程多实例处理情况。
        boolean isSubProcessMulti = handSubProcessUser(bpmTask);

        if (isSubProcessMulti) {
            return null;
        }

        //多实例外部子流程
        boolean isExtProcessMulti = handExtSubProcessUser(bpmTask);
        if (isExtProcessMulti) {
            return null;
        }

        String instId = (String) bpmTask.getVariable(BpmConstants.PROCESS_INST_ID);

        ActionCmd taskCmd = ContextThreadUtil.getActionCmd(instId);
        Map<String, List<BpmIdentity>> identityMap = taskCmd.getBpmIdentities();

        String nodeId = bpmTask.getTaskDefinitionKey();

        List<BpmIdentity> identityList = identityMap.get(nodeId);

        //如果已经指定了人员则直接终止，不再执行从配置读取人员。
        if (BeanUtils.isNotEmpty(identityList)) {
            return null;
        }

        //从流程变量中获取是否设置了人员，如果设置则从流程变量中获取。
        //by xurui 2022-5-13 注释掉这部分，审批时让流程变量nodeUsers流转到TaskCreateEventListener监听器中去处理
//		Map<String,List< BpmIdentity>> nodeUsers=(Map<String,List< BpmIdentity>>) taskCmd.getTransitVars(BpmConstants.BPM_NODE_USERS);
//
//		if(nodeUsers!=null && nodeUsers.containsKey(nodeId)){
//			List<BpmIdentity> bpmIdentitys=nodeUsers.get(nodeId);
//			bpmTask.addExecutors(bpmIdentitys);
//			return null;
//		}
        //调用其他的插件进行运算。
        executeExt(pluginSession, pluginDef);

        return null;
    }

    /**
     * 处理多实例内部子流程用户。
     *
     * @param delegateTask
     * @return boolean
     */
    private boolean handSubProcessUser(BpmDelegateTask delegateTask) {
        String bpmnDefId = delegateTask.getBpmnDefId();
        IBpmNodeDefine bpmNodeDef = bpmDefineService.getBpmNodeDef(bpmnDefId, delegateTask.getTaskDefinitionKey());
        IBpmNodeDefine parentBpmNodeDef = bpmNodeDef.getParentBpmNodeDefine();
        if (parentBpmNodeDef == null) {
            return false;
        }
        if (!(parentBpmNodeDef instanceof IMultiInstanceDefine)) {
            return false;
        }

        IMultiInstanceDefine multiDef = (IMultiInstanceDefine) parentBpmNodeDef;
        if (!multiDef.supportMuliInstance()) {
            return false;
        }

        //若为多实例子流程中的任务，则从线程中的人员取出，并且把该人员从线程中删除
        BpmIdentity bpmIdentity = (BpmIdentity) delegateTask.getVariable(BpmConstants.ASIGNEE);

        if (bpmIdentity != null) {
            delegateTask.addExecutor(bpmIdentity);
            return true;
        }
        return false;
    }

    /**
     * 处理多实例外部子流程多实例流程的人员。
     *
     * @param delegateTask
     * @return boolean
     */
    private boolean handExtSubProcessUser(BpmDelegateTask delegateTask) {
        String nodeId = delegateTask.getTaskDefinitionKey();
        //判断当是否为外部子流程，判断是否有上级执行ID，没有则返回
        String supperExeId = delegateTask.getSupperExecutionId();
        if (StringUtil.isEmpty(supperExeId)) {
            return false;
        }
        //是否为多实例，不是则返回
        MultiInstanceType multiType = delegateTask.supperMultiInstanceType();
        if (multiType.equals(MultiInstanceType.NO)) {
            return false;
        }

        String bpmnDefId = delegateTask.getBpmnDefId();
        IBpmNodeDefine bpmNodeDef = bpmDefineService.getBpmNodeDef(bpmnDefId, nodeId);
        IBpmProcDefine<?> procDef = bpmNodeDef.getBpmProcDefine();

        List<IBpmNodeDefine> bpmNodeDefList = procDef.getStartNodes();
        if (bpmNodeDefList.size() > 1) {
            throw new ProcessDefException("多实例子流程发起节点后只能有一个用户任务节点");
        }

        //若为多实例子流程中的任务，则从线程中的人员取出，并且把该人员从线程中删除
        BpmIdentity bpmIdentity = (BpmIdentity) delegateTask.getSupperVariable(BpmConstants.ASIGNEE);

        if (bpmIdentity != null) {
            delegateTask.addExecutor(bpmIdentity);
            return true;
        }
        return false;
    }

}
