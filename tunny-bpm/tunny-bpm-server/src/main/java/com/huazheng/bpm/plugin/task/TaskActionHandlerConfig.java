package com.huazheng.bpm.plugin.task;


import com.huazheng.bpm.handler.ITaskActionHandler;
import com.huazheng.bpm.service.ITaskActionHandlerDefine;

import java.util.Collection;
import java.util.List;

/**
 * 任务动作执行插件配置接口
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月26日-下午4:02:47
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface TaskActionHandlerConfig {

    /**
     * 通过动作名称获取得任务处理器
     *
     * @param actionName
     * @return TaskActionHandler
     */
    ITaskActionHandler getTaskActionHandler(String actionType);

    /**
     * 通过动作名称获得任务定义
     *
     * @param actionName
     * @return TaskActionHandlerDef
     */
    ITaskActionHandlerDefine getTaskActionHandlerDef(String actionType);

    /**
     * 初始化任务执行配置
     * void
     */
    void init();

    /**
     * 获取ActionHandlerDef列表。
     *
     * @return List&lt;TaskActionHandlerDef>
     */
    Collection<ITaskActionHandlerDefine> getActionHandlerDefList();

    /**
     * 获取全部的按钮定义。
     *
     * @return Collection&lt;TaskActionHandlerDef>
     */
    List<? extends ITaskActionHandlerDefine> getAllActionHandlerDefList();

}
