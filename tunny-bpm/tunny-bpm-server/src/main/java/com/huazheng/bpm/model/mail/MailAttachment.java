package com.huazheng.bpm.model.mail;

import java.io.InputStream;

/**
 * 邮件附件类。
 *
 * <pre>
 * 构建组：ibps-component-mail
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年4月26日-上午10:22:56
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class MailAttachment {
    /**
     * 文件名
     */
    protected String fileName;

    /**
     * 文件数据流
     */
    protected InputStream inputStream;

    /**
     * 邮件附件类构造方法
     */
    public MailAttachment() {
    }

    public MailAttachment(String fileName) {
        super();
        this.fileName = fileName;
    }

    /**
     * 邮件附件类构造方法
     *
     * @param fileName    文件名
     * @param inputStream 文件流
     */
    public MailAttachment(String fileName, InputStream inputStream) {
        this.fileName = fileName;
        this.inputStream = inputStream;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public InputStream getInputStream() {
        return inputStream;
    }

    public void setInputStream(InputStream inputStream) {
        this.inputStream = inputStream;
    }
}
