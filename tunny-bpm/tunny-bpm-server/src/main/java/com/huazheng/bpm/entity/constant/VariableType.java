//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vJAXB 2.1.10 in JDK 6
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2015.03.16 at 05:17:06 下午 CST
//


package com.huazheng.bpm.entity.constant;


public enum VariableType {
    /**
     * 字符串
     */
    STRING("string", "字符串"),
    /**
     * 数字
     */
    NUMBER("number", "数字"),
    /**
     * 日期
     */
    DATETIME("datetime", "日期");

    // 键
    private String key = "";
    // 值
    private String value = "";

    // 构造方法
    VariableType(String key, String value) {
        this.key = key;
        this.value = value;
    }

    // =====getting and setting=====
    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return key;
    }

    /**
     * 通过key获取对象
     *
     * @param key
     * @return
     */
    public static VariableType fromKey(String key) {
        for (VariableType c : VariableType.values()) {
            if (c.getKey().equalsIgnoreCase(key)) {
                return c;
            }
        }
        throw new IllegalArgumentException(key);
    }

}
