package com.huazheng.bpm.model.node;


import com.huazheng.bpm.plugin.IBpmPluginContext;
import com.huazheng.bpm.plugin.IBpmPluginDefine;
import com.huazheng.bpm.util.core.BeanUtils;

import java.util.List;

/**
 * 自动节点
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午12:11:14
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
public class AutoTaskDefine extends BaseNodeDefine {

    public IBpmPluginContext getAutoTaskBpmPluginContext() {
        List<IBpmPluginContext> list = getBpmPluginContextList();
        if (BeanUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    public IBpmPluginDefine getAutoTaskBpmPluginDefine() {
        IBpmPluginContext autoTaskContext = getAutoTaskBpmPluginContext();
        if (autoTaskContext == null) {
            return null;
        }
        IBpmPluginDefine pluginDef = autoTaskContext.getBpmPluginDefine();
        return pluginDef;
    }

}
