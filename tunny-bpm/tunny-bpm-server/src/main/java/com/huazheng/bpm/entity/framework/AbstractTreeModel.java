package com.huazheng.bpm.entity.framework;

import java.util.ArrayList;
import java.util.List;

/**
 * 树结构抽象类.
 *
 * <pre>
 *
 * 构建组：ibps-base-framework-ddd
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2017年5月22日-上午12:20:28
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings({"serial", "rawtypes"})
public abstract class AbstractTreeModel<T extends String> extends AbstractPo<T> implements TreeType {
    private List<TreeType> subs = new ArrayList<TreeType>();

    @Override
    public List<TreeType> getSubs() {
        return subs;
    }

    public void setSubs(List<TreeType> _subs) {
        this.subs = _subs;
    }

    public String toTreeString() {
        return "Type [id=" + getId() + ", name=" + getName() + ", parentId=" + getParentId() + ", depth=" + getDepth()
                + ", path=" + getPath() + ", sn=" + getSn() + "]";
    }
}
