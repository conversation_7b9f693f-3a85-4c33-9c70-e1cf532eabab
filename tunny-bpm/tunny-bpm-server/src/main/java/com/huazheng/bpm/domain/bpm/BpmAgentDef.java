package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmAgentDefDao;
import com.huazheng.bpm.dao.bpm.BpmAgentDefQueryDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmAgentDefPo;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

/**
 * 流程代理定义 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-03-30 17:29:14
 * </pre>
 */
@SuppressWarnings("serial")
@Service
@Scope("prototype")
public class BpmAgentDef extends AbstractDomain<String, BpmAgentDefPo> {

    private BpmAgentDefDao bpmAgentDefDao = null;
    private BpmAgentDefQueryDao bpmAgentDefQueryDao = null;


    protected void init() {
        bpmAgentDefDao = AppUtil.getBean(BpmAgentDefDao.class);
        bpmAgentDefQueryDao = AppUtil.getBean(BpmAgentDefQueryDao.class);
        this.setDao(bpmAgentDefDao);
    }

    @Override
    protected void onSave() {
        if (BeanUtils.isNotEmpty(getId()))
            setNewFlag(BeanUtils.isEmpty(bpmAgentDefQueryDao.get(getId())));
    }

}
