package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.builder.BpmOperLogBuilder;
import com.huazheng.bpm.dao.bpm.*;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.*;
import com.huazheng.bpm.entity.constant.*;
import com.huazheng.bpm.entity.inst.IBpmProcInst;
import com.huazheng.bpm.event.NoExecutorEvent;
import com.huazheng.bpm.event.NoExecutorModel;
import com.huazheng.bpm.model.base.BpmIdentity;
import com.huazheng.bpm.model.delegate.BpmDelegateTask;
import com.huazheng.bpm.model.node.IBpmNodeDefine;
import com.huazheng.bpm.model.node.SignNodeDefine;
import com.huazheng.bpm.model.task.IBpmTask;
import com.huazheng.bpm.repository.BpmExecRepository;
import com.huazheng.bpm.repository.BpmTaskAssignRepository;
import com.huazheng.bpm.repository.BpmTaskSignRepository;
import com.huazheng.bpm.service.*;
import com.huazheng.bpm.util.base.*;
import com.huazheng.bpm.util.cmd.ActionCmd;
import com.huazheng.bpm.util.cmd.IbpsTaskFinishCmd;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.db.UniqueIdUtil;
import com.huazheng.bpm.util.json.JsonUtil;
import com.huazheng.bpm.util.web.ResultMessage;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 流程任务
 * 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：simon cai
 * 邮箱地址：<EMAIL>
 * 创建时间：2016-12-14 15:30:23
 * </pre>
 */
@SuppressWarnings("serial")
@Transactional
@Scope("prototype")
@Service
public class BpmTask extends AbstractDomain<String, BpmTaskPo> {

    @Resource
    private BpmTaskDao bpmTaskDao;
    @Resource
    private BpmTaskQueryDao bpmTaskQueryDao;
    @Resource
    private BpmTaskSignQueryDao bpmTaskSignQueryDao;
    @Resource
    private BpmTaskSignRepository bpmTaskSignRepository;
    @Resource
    private NatTaskService natTaskService;
    @Resource
    private BpmTaskAssignRepository bpmTaskAssignRepository;
    @Resource
    private BpmTaskAssign bpmTaskAssignDomain;
    @Resource
    private ActTaskQueryDao actTaskQueryDao;
    @Resource
    private ActTaskService actTaskService;
    @Resource
    private IBpmDefineReader bpmDefineReader;
    @Resource
    private NatProInstService natProInstService;
    @Resource
    private BpmTaskSign bpmTaskSignDomain;
    @Resource
    private BpmIdentityExtractService bpmIdentExtractService;
    @Resource
    private BpmExecRepository bpmExecRepository;
    @Resource
    private BpmApproveQueryDao bpmApproveQueryDao;
    @Resource
    private BpmApproveDao bpmApproveDao;

    protected void init() {
        bpmTaskDao = AppUtil.getBean(BpmTaskDao.class);
        this.setDao(bpmTaskDao);
    }

    /**
     * 根据关联任务删除记录。
     *
     * @param tasks void
     */
    public void delByRelateTaskId(List<BpmTaskPo> tasks) {
        if (BeanUtils.isEmpty(tasks))
            return;
        for (BpmTaskPo task : tasks) {
            this.delByRelateTaskId(task.getId());
        }

    }

    /**
     * 根据关联任务Id删除记录。
     *
     * @param relateTaskId void
     */
    public void delByRelateTaskId(String relateTaskId) {
        bpmTaskAssignDomain.delByTask(relateTaskId);
        bpmTaskDao.delByTask(relateTaskId);
    }

    /**
     * 对任务分配用户。
     *
     * @param task void
     */
    public void assignUser(BpmDelegateTask delegateTask, List<BpmIdentity> identityList, String curUserId) {
        // 修改任务执行人。
        BpmTaskPo bpmTask = bpmTaskQueryDao.getByRelateTask(delegateTask.getId());
        // 设置当前执行人是否为空。
        bpmTask.setIdentityEmpty(BeanUtils.isEmpty(identityList));

        if (BeanUtils.isEmpty(identityList)) {
            ContextThreadUtil.addTask(bpmTask);
            return;
        }
        // 设置任务执行人列表。
        bpmTask.setIdentityList(identityList);

        Map<String, Object> vars = delegateTask.getVariables();

        ActionCmd cmd = ContextThreadUtil.getActionCmd(bpmTask.getProcInstId());

        ActionType actionType = ActionType.APPROVE;
        String actionName = "start";
        String opinion = "";
        if (cmd instanceof IbpsTaskFinishCmd) {
            IbpsTaskFinishCmd finishCmd = (IbpsTaskFinishCmd) cmd;
            actionType = finishCmd.getActionType();
            actionName = finishCmd.getActionName();
            opinion = finishCmd.getApprovalOpinion();
        }
        // 将用户抽取出来。
        List<Map<String, String>> userList = bpmIdentExtractService.extractUser(identityList);

        // 加到候选人
        bpmTaskAssignDomain.addCandidate(bpmTask, identityList);

        // 添加流程任务对象到流程的线程变量。
        ContextThreadUtil.addTask(bpmTask);

        // 当用户组的人员为空时抛出事件。
        publishIdentityListWhenEmpty(bpmTask, identityList, userList);
//		// 撤销时不通知执行人
//		// 跳过任务时不做通知
//		BpmUtil.setTaskSkip((IBpmTask) bpmTask, curUserId);
//		if (!ActionType.REVOKE.equals(actionType) && !bpmTask.getSkipResult().isSkipTask()) {
//			NotifyTaskModel notifyTaskModel = NotifyTaskModel.getNotifyModel(bpmTask.getTaskId(), bpmTask.getBpmnInstId(),
//					bpmTask.getProcInstId(), bpmTask.getSubject(), bpmTask.getNodeId(), bpmTask.getName(),
//					bpmTask.getBpmnDefId(), vars, userList, actionType, actionName, opinion);
//			NotifyUtil.publishNotifyEvent(notifyTaskModel);
//		}
    }

    /**
     * 当用户组的人员为空时抛出事件。
     *
     * @param bpmTask
     * @param list    void
     */
    private void publishIdentityListWhenEmpty(IBpmTask bpmTask, List<BpmIdentity> list, List<Map<String, String>> users) {
        if (BeanUtils.isNotEmpty(users))
            return;
        NoExecutorModel noExcutor = NoExecutorModel.getNoExecutorModel(bpmTask.getTaskId(), bpmTask.getBpmnInstId(),
                bpmTask.getSubject(), bpmTask.getNodeId(), bpmTask.getName(), bpmTask.getBpmnDefId());
        noExcutor.setIdentifyList(list);
        NoExecutorEvent ev = new NoExecutorEvent(noExcutor);
        AppUtil.publishEvent(ev);
    }

    /**
     * 补签用户。
     *
     * @param taskId   任务ID
     * @param aryUsers 会签人员ID
     * @return ResultMessage
     */
    @SuppressWarnings("unchecked")
    public ResultMessage addSignTask(String curUser, String taskId, String[] aryUsers, String[] messageTypes, String addReason) {
        if (aryUsers == null || aryUsers.length == 0)
            return ResultMessage.getFail("没有指定执行人!");

        IBpmTask bpmTask = bpmTaskQueryDao.get(taskId);
        String bpmnTaskId = bpmTask.getTaskId();

        ActTaskPo actTask = actTaskQueryDao.get(bpmnTaskId);

        IBpmNodeDefine nodeDef = bpmDefineReader.getNode(bpmTask.getProcDefId(), actTask.getTaskDefKey());

        if (!(nodeDef instanceof SignNodeDefine))
            return ResultMessage.getFail("当前节点不是会签节点!");

        SignNodeDefine signNodeDef = (SignNodeDefine) nodeDef;

        String actInstId = actTask.getProcInstId();
        String executionId = actTask.getExecutionId();
        String nodeId = nodeDef.getNodeId();
        String instId = bpmTask.getProcInstId();

        BpmTaskSignPo taskSignPo = bpmTaskSignQueryDao.getByTaskId(taskId, curUser);

        if (BeanUtils.isEmpty(taskSignPo))
            return ResultMessage.getFail("没有会签数据!");

        List<BpmTaskSignPo> signDataList = bpmTaskSignQueryDao.getVoteByInstNode(instId, nodeId, taskSignPo.getBatch());
        if (BeanUtils.isEmpty(signDataList))
            return ResultMessage.getFail("没有会签数据!");

        List<BpmIdentity> users = getCanAddUsers(signDataList, aryUsers);
        if (BeanUtils.isEmpty(users))
            return ResultMessage.getFail("指定的人员已存在!");

        int userAmount = users.size();

        Integer nrOfInstances = (Integer) natProInstService.getVariable(executionId,
                BpmConstants.NUMBER_OF_INSTANCES);

        if (nrOfInstances != null) {
            natProInstService.setVariable(executionId, BpmConstants.NUMBER_OF_INSTANCES,
                    nrOfInstances + userAmount);
        }

        // 并行
        if (signNodeDef.isParallel()) {
            Integer loopCounter = nrOfInstances - 1;
            // 添加活动的线程个数
            Integer nrOfActiveInstances = (Integer) natProInstService.getVariable(executionId,
                    BpmConstants.NUMBER_OF_ACTIVE_INSTANCES);
            natProInstService.setVariable(executionId, BpmConstants.NUMBER_OF_ACTIVE_INSTANCES,
                    nrOfActiveInstances + userAmount);
            for (int i = 0; i < userAmount; i++) {
                BpmIdentity bpmIdentity = users.get(i);
                // 创建流程引擎任务。
                ActTaskPo newActTask = actTaskService.createTask(taskId, bpmIdentity.getId());

                String newExecutionId = newActTask.getExecutionId();

                Integer index = loopCounter + i + 1;

                natProInstService.setVariableLocal(newExecutionId, BpmConstants.NUMBER_OF_LOOPCOUNTER, index);
                natProInstService.setVariableLocal(newExecutionId, BpmConstants.ASIGNEE, bpmIdentity);

                BpmTaskSignPo signData = new BpmTaskSignPo();
                signData.setId(UniqueIdUtil.getId());
                signData.setDefId(newActTask.getProcDefId());
                signData.setInstId(instId);
                signData.setActInstId(actInstId);
                signData.setNodeId(nodeId);
                signData.setTaskId(newActTask.getId());
                signData.setQualifiedId(bpmIdentity.getId());
                signData.setCreateTime(new Date());
                signData.setVoteResult(VoteResult.NO.getKey());
                signData.setIndex(index.shortValue());
                signData.setBatch(taskSignPo.getBatch());
                bpmTaskSignDomain = bpmTaskSignRepository.newInstance(signData);
                bpmTaskSignDomain.create();

                if (BeanUtils.isNotEmpty(messageTypes)) {
                    BpmTaskPo task = bpmTaskQueryDao.get(newActTask.getId());
                    for (String notifyType : messageTypes)
                        NotifyUtil.notify(task, addReason, bpmIdentity.getId(), notifyType, TemplateType.BPM_ADD_SIGN_TASK.getKey(), curUser);
                }
            }
        }
        // 串行。
        else {
            String varName = BpmConstants.SIGN_USERIDS + nodeId;
            List<BpmIdentity> addList = new ArrayList<BpmIdentity>();

            for (int i = 0; i < userAmount; i++) {
                Integer index = nrOfInstances + i;
                BpmIdentity bpmIdentity = users.get(i);
                BpmTaskSignPo signData = new BpmTaskSignPo();
                signData.setId(UniqueIdUtil.getId());
                signData.setDefId(actTask.getProcDefId());
                signData.setInstId(instId);
                signData.setActInstId(actInstId);
                signData.setNodeId(nodeId);
                signData.setTaskId("");
                signData.setQualifiedId(bpmIdentity.getId());
                signData.setCreateTime(new Date());
                signData.setVoteResult(VoteResult.NO.getKey());
                signData.setIndex(index.shortValue());
                signData.setBatch(taskSignPo.getBatch());
                bpmTaskSignDomain = bpmTaskSignRepository.newInstance(signData);
                bpmTaskSignDomain.create();
                addList.add(bpmIdentity);
            }
            // 修改串行的流程变量。
            List<BpmIdentity> list = (List<BpmIdentity>) natProInstService.getVariable(executionId, varName);
            list.addAll(addList);
            natProInstService.setVariable(executionId, varName, list);
        }

        ResultMessage rtnMessage = ResultMessage.getSuccess("补签成功!");
        rtnMessage.addVariable("actTask", actTask);
        rtnMessage.addVariable("users", users);

        bpmTaskSignDomain.setPrivilege(taskId, PrivilegeMode.ALLOW_ADD_SIGN.getKey());

        //saveLog(bpmTask, BpmOperTypeEnum.ADD_SIGN, addReason, users);

        return rtnMessage;
    }

    /**
     * 补签用户。
     *
     * @param taskId   任务ID
     * @param aryUsers 会签人员ID
     * @return ResultMessage
     */
    @SuppressWarnings("unchecked")
    public ResultMessage addSignTask(String curUser, Boolean isSuper, String taskId, String[] aryUsers, String[] messageTypes, String addReason) {
        if (aryUsers == null || aryUsers.length == 0)
            return ResultMessage.getFail("没有指定执行人!");

        IBpmTask bpmTask = bpmTaskQueryDao.get(taskId);
        String bpmnTaskId = bpmTask.getTaskId();

        ActTaskPo actTask = actTaskQueryDao.get(bpmnTaskId);

        IBpmNodeDefine nodeDef = bpmDefineReader.getNode(bpmTask.getProcDefId(), actTask.getTaskDefKey());

        if (!(nodeDef instanceof SignNodeDefine))
            return ResultMessage.getFail("当前节点不是会签节点!");

        SignNodeDefine signNodeDef = (SignNodeDefine) nodeDef;

        String actInstId = actTask.getProcInstId();
        String executionId = actTask.getExecutionId();
        String nodeId = nodeDef.getNodeId();
        String instId = bpmTask.getProcInstId();

        BpmTaskSignPo taskSignPo = bpmTaskSignQueryDao.getByTaskId(taskId, curUser);

        //管理员干预补签 2018-06-06 zhongjh
        if (isSuper && BeanUtils.isEmpty(taskSignPo)) {
            List<BpmTaskSignPo> list = bpmTaskSignRepository.findByTaskId(taskId);
            if (list.size() > 0) {
                taskSignPo = list.get(0);
            }
        }

        if (BeanUtils.isEmpty(taskSignPo))
            return ResultMessage.getFail("没有会签数据!");

        List<BpmTaskSignPo> signDataList = bpmTaskSignQueryDao.getVoteByInstNode(instId, nodeId, taskSignPo.getBatch());
        if (BeanUtils.isEmpty(signDataList))
            return ResultMessage.getFail("没有会签数据!");

        List<BpmIdentity> users = getCanAddUsers(signDataList, aryUsers);
        if (BeanUtils.isEmpty(users))
            return ResultMessage.getFail("指定的人员已存在!");

        int userAmount = users.size();

        Integer nrOfInstances = (Integer) natProInstService.getVariable(executionId,
                BpmConstants.NUMBER_OF_INSTANCES);

        if (nrOfInstances != null) {
            natProInstService.setVariable(executionId, BpmConstants.NUMBER_OF_INSTANCES,
                    nrOfInstances + userAmount);
        }

        // 并行
        if (signNodeDef.isParallel()) {
            Integer loopCounter = nrOfInstances - 1;
            // 添加活动的线程个数
            Integer nrOfActiveInstances = (Integer) natProInstService.getVariable(executionId,
                    BpmConstants.NUMBER_OF_ACTIVE_INSTANCES);
            natProInstService.setVariable(executionId, BpmConstants.NUMBER_OF_ACTIVE_INSTANCES,
                    nrOfActiveInstances + userAmount);
            for (int i = 0; i < userAmount; i++) {
                BpmIdentity bpmIdentity = users.get(i);
                // 创建流程引擎任务。
                ActTaskPo newActTask = actTaskService.createTask(taskId, bpmIdentity.getId());

                String newExecutionId = newActTask.getExecutionId();

                Integer index = loopCounter + i + 1;

                natProInstService.setVariableLocal(newExecutionId, BpmConstants.NUMBER_OF_LOOPCOUNTER, index);
                natProInstService.setVariableLocal(newExecutionId, BpmConstants.ASIGNEE, bpmIdentity);

                BpmTaskSignPo signData = new BpmTaskSignPo();
                signData.setId(UniqueIdUtil.getId());
                signData.setDefId(newActTask.getProcDefId());
                signData.setInstId(instId);
                signData.setActInstId(actInstId);
                signData.setNodeId(nodeId);
                signData.setTaskId(newActTask.getId());
                signData.setQualifiedId(bpmIdentity.getId());
                signData.setCreateTime(new Date());
                signData.setVoteResult(VoteResult.NO.getKey());
                signData.setIndex(index.shortValue());
                signData.setBatch(taskSignPo.getBatch());
                bpmTaskSignDomain = bpmTaskSignRepository.newInstance(signData);
                bpmTaskSignDomain.create();

                if (BeanUtils.isNotEmpty(messageTypes)) {
                    BpmTaskPo task = bpmTaskQueryDao.get(newActTask.getId());
                    for (String notifyType : messageTypes)
                        NotifyUtil.notify(task, addReason, bpmIdentity.getId(), notifyType, TemplateType.BPM_ADD_SIGN_TASK.getKey(), curUser);
                }
            }
        }
        // 串行。
        else {
            String varName = BpmConstants.SIGN_USERIDS + nodeId;
            List<BpmIdentity> addList = new ArrayList<BpmIdentity>();

            for (int i = 0; i < userAmount; i++) {
                Integer index = nrOfInstances + i;
                BpmIdentity bpmIdentity = users.get(i);
                BpmTaskSignPo signData = new BpmTaskSignPo();
                signData.setId(UniqueIdUtil.getId());
                signData.setDefId(actTask.getProcDefId());
                signData.setInstId(instId);
                signData.setActInstId(actInstId);
                signData.setNodeId(nodeId);
                signData.setTaskId("");
                signData.setQualifiedId(bpmIdentity.getId());
                signData.setCreateTime(new Date());
                signData.setVoteResult(VoteResult.NO.getKey());
                signData.setIndex(index.shortValue());
                signData.setBatch(taskSignPo.getBatch());
                bpmTaskSignDomain = bpmTaskSignRepository.newInstance(signData);
                bpmTaskSignDomain.create();
                addList.add(bpmIdentity);
            }
            // 修改串行的流程变量。
            List<BpmIdentity> list = (List<BpmIdentity>) natProInstService.getVariable(executionId, varName);
            list.addAll(addList);
            natProInstService.setVariable(executionId, varName, list);
        }

        ResultMessage rtnMessage = ResultMessage.getSuccess("补签成功!");
        rtnMessage.addVariable("actTask", actTask);
        rtnMessage.addVariable("users", users);

        bpmTaskSignDomain.setPrivilege(taskId, PrivilegeMode.ALLOW_ADD_SIGN.getKey(), isSuper);

        //saveLog(bpmTask, BpmOperTypeEnum.ADD_SIGN, addReason, users);

        return rtnMessage;
    }

    private void saveLog(IBpmTask bpmTask, BpmOperTypeEnum operType, String addReason, List<BpmIdentity> users) {
        // 流程操作日志
        addReason += "人员名单：";
        StringBuilder builder = new StringBuilder();
        for (BpmIdentity bpmIdentity : users) {
            builder.append("admin"
                    //DefaultPartyUserPo.fromJsonString2(userService.getByIdJson(bpmIdentity.getId())).getFullname()
            ).append("、");
        }
        if (builder.length() > 0) builder.setLength(builder.length() - 1);

        BpmOperLogPo po = BpmOperLogBuilder.build(bpmTask, operType, addReason + builder.toString());
        BpmOperLogThread thread = new BpmOperLogThread(po);
        ThreadUtil.execute(ThreadUtil.THREAD_TYPE_DEFAULT, thread);
    }

    /**
     * 获取可以进行补签的人员。
     *
     * @param list
     * @param aryUsers
     * @return List&lt;BpmIdentity>
     */
    private List<BpmIdentity> getCanAddUsers(List<BpmTaskSignPo> list, String[] aryUsers) {
        List<BpmIdentity> rtnList = new ArrayList<BpmIdentity>();
        List<String> userList = new ArrayList<String>();

        for (BpmTaskSignPo signData : list) {
            userList.add(signData.getQualifiedId());
        }

        for (String userId : aryUsers) {
            if (userList.contains(userId))
                continue;
            BpmIdentity identity = DefaultBpmIdentity.getIdentityByUserId(userId);
            rtnList.add(identity);
        }
        return rtnList;
    }

    /**
     * 锁定任务为某用户
     *
     * @param taskId
     * @param userId void
     */
    public void lock() {
        bpmTaskDao.lock(getId(), getData().getLockUser(), BpmTaskPo.LOCK);
    }

    /**
     * 解锁任务
     *
     * @param taskId void
     */
    public void unlock() {
        // 设置其值为0，即表示为空，用0值方便数据库索引建立及提交查询速度
        bpmTaskDao.lock(getId(), BpmConstants.EmptyUser, BpmTaskPo.UNLOCK);
    }

    /**
     * 设置任务的执行用户
     *
     * @param taskId
     * @param assigneeId 任务的执行人Id void
     */
    public void assignTask(String assigneeId) {
        // 修改执行人发布事件
        bpmTaskDao.updOwnerById(getId(), assigneeId);
        natTaskService.setAssignee(getData().getTaskId(), assigneeId);
    }

    /**
     * 根据实例ID删除流程
     *
     * @param instList void
     */
    public void delByInst(List<String> instList) {
        bpmTaskDao.delByInst(instList);
    }

    /**
     * 根据父ID删除任务
     *
     * @param parentId void
     */
    public void delByParentId(String parentId) {
        bpmTaskDao.delByParent(parentId);
    }

    /**
     * 根据任务实例创建任务
     *
     * <pre>
     * 1.创建Bpm_task记录。
     * 2.创建ACT_RU_TASK记录。
     * </pre>
     *
     * @param bpmProcessInstance void
     */
    public void createTask(IBpmProcInst instance) {
        ActTaskPo actTask = new ActTaskPo();
        actTask.setId(UniqueIdUtil.getId());
        actTask.setRev(1);
        actTask.setExecutionId(instance.getBpmnInstId());
        actTask.setProcInstId(instance.getBpmnInstId());
        actTask.setAssignee(instance.getCreateBy());
    }

    /**
     * 根据实例id及扩展执行记录节点id删除流程扩展任务数据
     * tasks、assign
     *
     * @param instId
     * @param targetNodeId
     */
    public void delByInstIdExecNodeId(String procInstId, String targetNodeId) {
        List<String> taskIds = bpmExecRepository.findBehandTaskIds(procInstId, targetNodeId, true);
        if (BeanUtils.isEmpty(taskIds)) return;

        for (String taskId : taskIds) {
            // 删除流程扩展任务数据
            bpmTaskDao.delByTask(taskId);

            // 删除流程扩展任务候选人数据
            bpmTaskAssignDomain.delByTask(taskId);

            // 删除流程扩展会签数据
            bpmTaskSignDomain.delByTask(taskId);
        }
    }

    /**
     * 指定任务执行人
     *
     * @param taskIdArr
     * @param userIdArr
     */
    public void assignee(String[] taskIdArr, String[] userIdArr) {
        if (BeanUtils.isEmpty(taskIdArr)) {
            throw new RuntimeException("指定执行人失败，任务ID为空！");
        }
        if (BeanUtils.isEmpty(userIdArr)) {
            throw new RuntimeException("指定执行人失败，人员ID为空！");
        }

        BpmTaskPo po = null;
        boolean isSign = false;
        StringBuilder taskMsg = new StringBuilder();
        StringBuilder signMsg = new StringBuilder();
        BpmApprovePo bpmApprovePo = null;
        for (String taskId : taskIdArr) {
            po = bpmTaskQueryDao.get(taskId);
            if (BeanUtils.isEmpty(po)) {
                taskMsg.append("【").append(taskId).append("】");
                continue;
            }

            //TODO 转办代理任务-暂时不能指定执行人

            // 会签数据
            isSign = bpmTaskSignQueryDao.isSign(taskId);
            if (isSign) {
                signMsg.append("【").append(po.getName()).append("】");
                continue;
            }

            // 任务执行人
            bpmTaskAssignDomain.addAssign(po, userIdArr);

            // 审批意见
            bpmApprovePo = bpmApproveQueryDao.getByTaskId(taskId, null);
            if (BeanUtils.isEmpty(bpmApprovePo)) {
                continue;
            }
            this.updateApprove(bpmApprovePo, userIdArr);
        }

        StringBuilder resultMsg = new StringBuilder();
        if (taskMsg.length() > 0) {
            resultMsg.append(taskMsg).append("任务不存在！");
        }
        if (signMsg.length() > 0) {
            resultMsg.append(taskMsg).append("会签任务不支持指定执行人，如需变更请使用会签特权补签！");
        }

        if (resultMsg.length() > 0) {
            throw new RuntimeException(resultMsg.toString());
        }
    }

    /**
     * 更新审批意见候选人
     *
     * @param bpmApprovePo
     * @param userIdArr
     */
    private void updateApprove(BpmApprovePo bpmApprovePo, String[] userIdArr) {
        String qualfieds = bpmApprovePo.getQualfieds();
        if (JsonUtil.isJsonArray(qualfieds)) {
            JSONArray jsonArray = JSONArray.fromObject(qualfieds);
            u:
            for (String id : userIdArr) {
                for (int i = 0, len = jsonArray.size(); i < len; i++) {
                    if (jsonArray.getJSONObject(i).getString(BpmIdentity.IDENT_ID).equals(id)
                            && jsonArray.getJSONObject(i).getString(BpmIdentity.IDENT_TYPE).equals(BpmIdentity.TYPE_USER)) {
                        continue u;
                    }
                }
                JSONObject jsonObject = new JSONObject();
                jsonObject.accumulate(BpmIdentity.IDENT_ID, id);
                jsonObject.accumulate(BpmIdentity.IDENT_TYPE, BpmIdentity.TYPE_USER);
                jsonArray.add(jsonObject);
            }
            bpmApprovePo.setQualfieds(jsonArray.toString());
        } else {
            JSONArray jsonArray = new JSONArray();
            for (String id : userIdArr) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.accumulate(BpmIdentity.IDENT_ID, id);
                jsonObject.accumulate(BpmIdentity.IDENT_TYPE, BpmIdentity.TYPE_USER);
                jsonArray.add(jsonObject);
            }
            bpmApprovePo.setQualfieds(jsonArray.toString());
        }
        bpmApproveDao.update(bpmApprovePo);
    }

    public void updateStatusByBpmnInstanceId(String processInstId, int state) {
        bpmTaskDao.updateSuspendState(processInstId, state);
    }

}
