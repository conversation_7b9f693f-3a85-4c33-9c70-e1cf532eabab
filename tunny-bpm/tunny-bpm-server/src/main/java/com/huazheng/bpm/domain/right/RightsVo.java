package com.huazheng.bpm.domain.right;

public class RightsVo {
    private String entityId;/*实体id*/
    private String entityType;/*权限类型，desktopManage*/
    private String[] rightsIds;/*权限id*/
    private String rightsType;/*权限控制类型,employee、org、position*/

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public String getEntityType() {
        return entityType;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    public String[] getRightsIds() {
        return rightsIds;
    }

    public void setRightsIds(String[] rightsIds) {
        this.rightsIds = rightsIds;
    }

    public String getRightsType() {
        return rightsType;
    }

    public void setRightsType(String rightsType) {
        this.rightsType = rightsType;
    }

    public RightsVo() {
    }

    public RightsVo(String entityId, String entityType, String[] rightsIds, String rightsType) {
        super();
        this.entityId = entityId;
        this.entityType = entityType;
        this.rightsIds = rightsIds;
        this.rightsType = rightsType;
    }
}
