package com.huazheng.bpm.entity.jms;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.huazheng.bpm.entity.party.PartyUserPo;
import com.huazheng.bpm.model.base.IdentityConstants;
import com.huazheng.bpm.util.core.JacksonUtil;

import java.util.Collections;
import java.util.List;

/**
 * 默认用户  实体对象。
 *
 * <pre>
 * 构建组：ibps-org-biz
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年9月1日-下午6:03:22
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
public class DefaultPartyUserPo extends PartyUserPo {

    protected String fullname;
    protected String status; /*状态*/
    protected String gender; /*male*/
    protected String mobile; /*手机*/
    protected String email; /*邮件*/
    protected String address; /*地址*/
    protected String qq; /*QQ号*/
    protected String photo; /*照片*/
    protected String wcAccount; /*微信登录账号*/

    @JsonIgnore
    @Override
    public String getIdentityType() {
        return IdentityConstants.USER;
    }

    @Override
    public String getUserId() {
        return id;
    }

    @Override
    public void setUserId(String userId) {
        id = userId;
    }

    @Override
    public String getFullname() {
        return fullname;
    }

    @Override
    public void setFullname(String fullName) {
        this.fullname = fullName;
    }

    @Override
    public String getEmail() {
        return email;
    }

    @Override
    public String getMobile() {
        return mobile;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getQq() {
        return qq;
    }

    public void setQq(String qq) {
        this.qq = qq;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public boolean isSuper() {
        return 'Y' == this.isSuper;
    }

    public String getWcAccount() {
        return wcAccount;
    }

    public void setWcAccount(String wcAccount) {
        this.wcAccount = wcAccount;
    }

    public static DefaultPartyUserPo fromJsonString2(String data) {
        if (!JacksonUtil.isJsonObject(data)) {
            return null;
        }
        return JacksonUtil.getDTO(data, DefaultPartyUserPo.class);
    }

    public static List<DefaultPartyUserPo> fromJsonArrayString2(String listData) {
        if (!JacksonUtil.isJsonArray(listData)) {
            return Collections.emptyList();
        }
        return JacksonUtil.getDTOList(listData, DefaultPartyUserPo.class);
    }

}