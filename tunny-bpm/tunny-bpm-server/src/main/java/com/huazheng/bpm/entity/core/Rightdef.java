//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4-2
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.08.25 at 03:37:49 PM CST
//


package com.huazheng.bpm.entity.core;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for rightdef complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="rightdef">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="entityType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="entityId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="type" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="rightsId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="rightsName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "rightdef", propOrder = {
        "id",
        "entityType",
        "entityId",
        "type",
        "rightsId",
        "rightsName"
})
public class Rightdef {

    @XmlElement(required = true)
    protected String id;
    @XmlElement(required = true)
    protected String entityType;
    @XmlElement(required = true)
    protected String entityId;
    @XmlElement(required = true)
    protected String type;
    @XmlElement(required = true)
    protected String rightsId;
    @XmlElement(required = true)
    protected String rightsName;

    /**
     * Gets the value of the id property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setId(String value) {
        this.id = value;
    }

    /**
     * Gets the value of the entityType property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getEntityType() {
        return entityType;
    }

    /**
     * Sets the value of the entityType property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setEntityType(String value) {
        this.entityType = value;
    }

    /**
     * Gets the value of the entityId property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getEntityId() {
        return entityId;
    }

    /**
     * Sets the value of the entityId property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setEntityId(String value) {
        this.entityId = value;
    }

    /**
     * Gets the value of the type property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setType(String value) {
        this.type = value;
    }

    /**
     * Gets the value of the rightsId property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getRightsId() {
        return rightsId;
    }

    /**
     * Sets the value of the rightsId property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setRightsId(String value) {
        this.rightsId = value;
    }

    /**
     * Gets the value of the rightsName property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getRightsName() {
        return rightsName;
    }

    /**
     * Sets the value of the rightsName property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setRightsName(String value) {
        this.rightsName = value;
    }

}
