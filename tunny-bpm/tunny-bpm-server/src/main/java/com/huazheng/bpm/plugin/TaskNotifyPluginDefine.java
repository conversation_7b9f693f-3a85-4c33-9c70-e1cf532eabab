package com.huazheng.bpm.plugin;

import com.huazheng.bpm.entity.constant.EventType;
import com.huazheng.bpm.model.base.NotifyVo;

import java.util.HashMap;
import java.util.Map;

/**
 * TODO（用一句话描述这个类做什么用）。
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月14日-上午9:37:56
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
public class TaskNotifyPluginDefine extends AbstractBpmTaskPluginDefine {

    /**
     * 信息配置集合
     */
    private Map<EventType, NotifyVo> notifyVos = new HashMap<EventType, NotifyVo>();

    public void addNotifyVo(EventType eventType, NotifyVo notifyVo) {
        notifyVo.setEventType(eventType);
        notifyVos.put(eventType, notifyVo);
    }

    public Map<EventType, NotifyVo> getNotifyVos() {
        return notifyVos;
    }
}
