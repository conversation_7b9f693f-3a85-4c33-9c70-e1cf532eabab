package com.huazheng.bpm.entity.cat;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;


/**
 * 分类标识表  实体对象。
 *
 * <pre>
 * 构建组：bps-common-biz
 * 作者：huangcy
 * 邮箱：<EMAIL>
 * 日期：2015-11-19 14:17:31
 * 版权：广州流辰信息技术有限公司
 * </pre>
 */
@SuppressWarnings("serial")
public class CategoryTbl extends AbstractPo<String> {

    protected String id; /*主键ID*/
    protected String categoryKey; /*分类组业务主键*/
    protected String name; /*分类名*/
    protected Integer flag = 0; /*是否系统默认(0,否,1,系统默认不可修改)*/
    protected Integer sn; /*序号*/
    protected String type = "0"; /*结构类型。0=平铺结构；1=树型结构*/
    protected String createBy; /*创建人ID*/
    protected java.util.Date createTime; /*创建时间*/
    protected String createOrgId; /*创建者所属组织ID*/
    protected String updateBy; /*更新人ID*/
    protected java.util.Date updateTime; /*更新时间*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键ID
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setCategoryKey(String categoryKey) {
        this.categoryKey = categoryKey;
    }

    /**
     * 返回 分类组业务主键
     *
     * @return
     */
    public String getCategoryKey() {
        return this.categoryKey;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 返回 分类名
     *
     * @return
     */
    public String getName() {
        return this.name;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    /**
     * 返回 是否系统默认(0,否,1,系统默认不可修改)
     *
     * @return
     */
    public Integer getFlag() {
        return this.flag;
    }

    public void setSn(Integer sn) {
        this.sn = sn;
    }

    /**
     * 返回 序号
     *
     * @return
     */
    public Integer getSn() {
        return this.sn;
    }

    public void setType(String type) {
        this.type = type;
    }

    /**
     * 返回 结构类型。0=平铺结构；1=树型结构
     *
     * @return
     */
    public String getType() {
        return this.type;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     * 返回 创建人ID
     *
     * @return
     */
    public String getCreateBy() {
        return this.createBy;
    }

    public void setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 返回 创建时间
     *
     * @return
     */
    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateOrgId(String createOrgId) {
        this.createOrgId = createOrgId;
    }

    /**
     * 返回 创建者所属组织ID
     *
     * @return
     */
    public String getCreateOrgId() {
        return this.createOrgId;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 返回 更新人ID
     *
     * @return
     */
    public String getUpdateBy() {
        return this.updateBy;
    }

    public void setUpdateTime(java.util.Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 返回 更新时间
     *
     * @return
     */
    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    /**
     * @see java.lang.Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("categoryKey", this.categoryKey)
                .append("name", this.name)
                .append("flag", this.flag)
                .append("sn", this.sn)
                .append("type", this.type)
                .append("createBy", this.createBy)
                .append("createTime", this.createTime)
                .append("createOrgId", this.createOrgId)
                .append("updateBy", this.updateBy)
                .append("updateTime", this.updateTime)
                .toString();
    }
}
