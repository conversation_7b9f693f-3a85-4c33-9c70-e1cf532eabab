package com.huazheng.bpm.model.mail;


import com.huazheng.bpm.util.core.JacksonUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 邮件实体类。
 *
 * <pre>
 * 构建组：ibps-component-mail
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年4月26日-上午10:21:59
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class Mail implements Serializable {

    private static final long serialVersionUID = 4311266253309771066L;
    /**
     * 每种邮箱中，邮件的唯一ID
     */
    protected String messageId;
    /**
     * 发件人显示名
     */
    protected String senderName;
    /**
     * 发件人地址
     */
    protected String senderAddress;
    /**
     * 邮件主题
     */
    protected String subject;
    /**
     * 邮件内容
     */
    protected String content;
    /**
     * 发送时间
     */
    protected Date sendDate;
    /**
     * 收件人显示名
     */
    protected String receiverName;
    /**
     * 收件人地址
     */
    protected String receiverAddresses;
    /**
     * 抄送人显示名
     */
    protected String ccName;
    /**
     * 抄送人地址
     */
    protected String ccAddresses;
    /**
     * 暗送人显示名
     */
    protected String bccName;
    /**
     * 暗送人地址
     */
    protected String bccAddresses;

    /**
     * 是否已读
     */
    protected Boolean isRead = false;

    /**
     * 是否回执
     */
    protected Boolean isReceipt = false;
    /**
     * 邮件附件
     */
    protected List<MailAttachment> attachments = new ArrayList<MailAttachment>();

    public String getSenderAddress() {
        return senderAddress;
    }

    public void setSenderAddress(String senderAddress) {
        this.senderAddress = senderAddress;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getBccAddresses() {
        return bccAddresses;
    }

    public void setBccAddresses(String bccAddresses) {
        this.bccAddresses = bccAddresses;
    }

    public List<MailAttachment> getMailAttachments() {
        return attachments;
    }

    public void setMailAttachments(List<MailAttachment> attachments) {
        this.attachments = attachments;
    }

    public Date getSendDate() {
        return sendDate;
    }

    public void setSendDate(Date sendDate) {
        this.sendDate = sendDate;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getCcName() {
        return ccName;
    }

    public void setCcName(String ccName) {
        this.ccName = ccName;
    }

    public String getCcAddresses() {
        return ccAddresses;
    }

    public void setCcAddresses(String ccAddresses) {
        this.ccAddresses = ccAddresses;
    }

    public String getBccName() {
        return bccName;
    }

    public void setBccName(String bccName) {
        this.bccName = bccName;
    }

    public String getReceiverAddresses() {
        return receiverAddresses;
    }

    public void setReceiverAddresses(String receiverAddresses) {
        this.receiverAddresses = receiverAddresses;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }


    public Boolean getIsRead() {
        return isRead;
    }

    public void setIsRead(Boolean isRead) {
        this.isRead = isRead;
    }

    public Boolean getIsReceipt() {
        return isReceipt;
    }

    public void setIsReceipt(Boolean isReceipt) {
        this.isReceipt = isReceipt;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((messageId == null) ? 0 : messageId.hashCode());
        result = prime * result
                + ((bccAddresses == null) ? 0 : bccAddresses.hashCode());
        result = prime * result + ((bccName == null) ? 0 : bccName.hashCode());
        result = prime * result + ((content == null) ? 0 : content.hashCode());
        result = prime * result
                + ((ccAddresses == null) ? 0 : ccAddresses.hashCode());
        result = prime * result
                + ((ccName == null) ? 0 : ccName.hashCode());
        result = prime * result + ((attachments == null) ? 0 : attachments.hashCode());
        result = prime
                * result
                + ((receiverAddresses == null) ? 0 : receiverAddresses
                .hashCode());
        result = prime * result
                + ((receiverName == null) ? 0 : receiverName.hashCode());
        result = prime * result
                + ((sendDate == null) ? 0 : sendDate.hashCode());
        result = prime * result
                + ((senderAddress == null) ? 0 : senderAddress.hashCode());
        result = prime * result
                + ((senderName == null) ? 0 : senderName.hashCode());
        result = prime * result + ((subject == null) ? 0 : subject.hashCode());
        return result;
    }

    @Override
    public String toString() {
        return "Mail [messageId=" + messageId + ", senderName=" + senderName
                + ", senderAddress=" + senderAddress + ", subject=" + subject
                + ", content=" + content + ", sendDate=" + sendDate
                + ", receiverName=" + receiverName + ", receiverAddresses="
                + receiverAddresses + ", ccName=" + ccName
                + ", ccAddresses=" + ccAddresses + ", bccName="
                + bccName + ", bccAddresses=" + bccAddresses + ", attachments="
                + attachments + "]";
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof Mail)) {
            return false;
        }
        Mail other = (Mail) obj;
        if (messageId == null) {
            if (other.messageId != null) {
                return false;
            }
        } else if (!messageId.equals(other.messageId)) {
            return false;
        }
        if (bccAddresses == null) {
            if (other.bccAddresses != null) {
                return false;
            }
        } else if (!bccAddresses.equals(other.bccAddresses)) {
            return false;
        }
        if (bccName == null) {
            if (other.bccName != null) {
                return false;
            }
        } else if (!bccName.equals(other.bccName)) {
            return false;
        }
        if (content == null) {
            if (other.content != null) {
                return false;
            }
        } else if (!content.equals(other.content)) {
            return false;
        }
        if (ccAddresses == null) {
            if (other.ccAddresses != null) {
                return false;
            }
        } else if (!ccAddresses.equals(other.ccAddresses)) {
            return false;
        }
        if (ccName == null) {
            if (other.ccName != null) {
                return false;
            }
        } else if (!ccName.equals(other.ccName)) {
            return false;
        }
        if (attachments == null) {
            if (other.attachments != null) {
                return false;
            }
        } else if (!attachments.equals(other.attachments)) {
            return false;
        }
        if (receiverAddresses == null) {
            if (other.receiverAddresses != null) {
                return false;
            }
        } else if (!receiverAddresses.equals(other.receiverAddresses)) {
            return false;
        }
        if (receiverName == null) {
            if (other.receiverName != null) {
                return false;
            }
        } else if (!receiverName.equals(other.receiverName)) {
            return false;
        }
        if (sendDate == null) {
            if (other.sendDate != null) {
                return false;
            }
        } else if (!sendDate.equals(other.sendDate)) {
            return false;
        }
        if (senderAddress == null) {
            if (other.senderAddress != null) {
                return false;
            }
        } else if (!senderAddress.equals(other.senderAddress)) {
            return false;
        }
        if (senderName == null) {
            if (other.senderName != null) {
                return false;
            }
        } else if (!senderName.equals(other.senderName)) {
            return false;
        }
        if (subject == null) {
            return other.subject == null;
        } else {
            return subject.equals(other.subject);
        }
    }

    public String toJsonString() {
        return JacksonUtil.toJsonString(this);
    }
}
