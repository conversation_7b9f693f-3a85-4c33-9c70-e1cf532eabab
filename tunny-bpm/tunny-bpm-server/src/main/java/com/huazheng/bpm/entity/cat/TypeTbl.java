package com.huazheng.bpm.entity.cat;


import com.huazheng.bpm.entity.framework.AbstractTreeModel;
import com.huazheng.bpm.entity.framework.PO;

/**
 * 数据字典  实体对象。
 *
 * <pre>
 * 构建组：bps-common-biz
 * 作者：huangcy
 * 邮箱：<EMAIL>
 * 日期：2015-11-19 14:17:31
 * 版权：广州流辰信息技术有限公司
 * </pre>
 */
@SuppressWarnings("serial")
public class TypeTbl extends AbstractTreeModel<String> {

    protected String id; /*分类ID*/
    protected String categoryKey; /*分类组键*/
    protected String name; /*分类名称*/
    protected String typeKey; /*节点的分类Key(在本分类树中唯一)*/
    protected String struType;/*结构类型。0=平铺结构；1=树型结构*/
    protected String parentId; /*父节点*/
    protected Integer depth; /*层次*/
    protected String path; /*路径*/
    protected String isLeaf; /*是否叶子节点。Y=是；N=否*/
    protected String ownerId; /*所属人ID (如果为0表示这个分类为公共分类,有用户id 表示为私有分类)*/
    protected Integer sn; /*序号*/
    protected String createBy; /*创建人ID*/
    protected java.util.Date createTime; /*创建时间*/
    protected String createOrgId; /*创建者所属组织ID*/
    protected String updateBy; /*更新人ID*/
    protected java.util.Date updateTime; /*更新时间*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 分类ID
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setCategoryKey(String categoryKey) {
        this.categoryKey = categoryKey;
    }

    /**
     * 返回 分类组键
     *
     * @return
     */
    public String getCategoryKey() {
        return this.categoryKey;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 返回 分类名称
     *
     * @return
     */
    public String getName() {
        return this.name;
    }

    public void setTypeKey(String typeKey) {
        this.typeKey = typeKey;
    }

    /**
     * 返回 节点的分类Key(在本分类树中唯一)
     *
     * @return
     */
    public String getTypeKey() {
        return this.typeKey;
    }

    public void setStruType(String struType) {
        this.struType = struType;
    }

    /**
     * 结构类型。0=平铺结构；1=树型结构
     *
     * @return
     */
    public String getStruType() {
        return this.struType;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    /**
     * 返回 父节点
     *
     * @return
     */
    public String getParentId() {
        return this.parentId;
    }

    public void setDepth(Integer depth) {
        this.depth = depth;
    }

    /**
     * 返回 层次
     *
     * @return
     */
    public Integer getDepth() {
        return this.depth;
    }

    public void setPath(String path) {
        this.path = path;
    }

    /**
     * 返回 路径
     *
     * @return
     */
    public String getPath() {
        return this.path;
    }

    public void setIsLeaf(String isLeaf) {
        this.isLeaf = isLeaf;
    }

    /**
     * 返回 是否叶子节点。Y=是；N=否
     *
     * @return
     */
    public String getIsLeaf() {
        return this.isLeaf;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * 返回 所属人ID (如果为0表示这个分类为公共分类,有用户id 表示为私有分类)
     *
     * @return
     */
    public String getOwnerId() {
        return this.ownerId;
    }

    public void setSn(Integer sn) {
        this.sn = sn;
    }

    /**
     * 返回 序号
     *
     * @return
     */
    public Integer getSn() {
        return this.sn;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     * 返回 创建人ID
     *
     * @return
     */
    public String getCreateBy() {
        return this.createBy;
    }

    public void setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 返回 创建时间
     *
     * @return
     */
    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateOrgId(String createOrgId) {
        this.createOrgId = createOrgId;
    }

    /**
     * 返回 创建者所属组织ID
     *
     * @return
     */
    public String getCreateOrgId() {
        return this.createOrgId;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 返回 更新人ID
     *
     * @return
     */
    public String getUpdateBy() {
        return this.updateBy;
    }

    public void setUpdateTime(java.util.Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 返回 更新时间
     *
     * @return
     */
    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    @SuppressWarnings("rawtypes")
    @Override
    public void addSub(PO po) {


    }
}
