package com.huazheng.bpm.plugin.task.userassign.def;


import com.huazheng.bpm.model.node.UserAssignRule;
import com.huazheng.bpm.plugin.AbstractBpmTaskPluginDefine;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户授权插件
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月14日-上午9:24:33
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
public class UserAssignPluginDefine extends AbstractBpmTaskPluginDefine {
    private List<UserAssignRule> ruleList = new ArrayList<UserAssignRule>();

    public List<UserAssignRule> getRuleList() {
        return ruleList;
    }

    public void setRuleList(List<UserAssignRule> ruleList) {
        this.ruleList = ruleList;
    }

}
