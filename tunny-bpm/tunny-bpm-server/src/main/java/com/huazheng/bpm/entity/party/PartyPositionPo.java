package com.huazheng.bpm.entity.party;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.huazheng.bpm.model.base.IdentityConstants;
import com.huazheng.bpm.model.base.PartyEntity;
import com.huazheng.bpm.util.core.JacksonUtil;
import com.huazheng.bpm.util.string.StringUtil;

import java.util.Collections;
import java.util.List;

/**
 * 岗位  实体对象。
 *
 * <pre>
 * 构建组：ibps-org-biz
 * 作者：huangchunyan
 * 邮箱：<EMAIL>
 * 日期：2016-06-16 17:26:46
 * 版权：广州流辰信息技术有限公司
 * </pre>
 */
@SuppressWarnings("serial")
public class PartyPositionPo extends PartyPositionTbl implements PartyEntity {
    protected Integer level; /*级别数值*/
    protected String levelName; /*等级名*/
    protected String orgName; /*组织名*/
    protected String isMainPost; /*是否主岗位*/
    protected String isPrincipal; /*是否主负责人*/

    public String getIsMainPost() {
        if (StringUtil.isBlank(isMainPost)) {
            this.isMainPost = "N";
        }
        return isMainPost;
    }

    public void setIsMainPost(String isMainPost) {
        if (StringUtil.isBlank(isMainPost)) {
            this.isMainPost = "N";
        } else {
            this.isMainPost = isMainPost;
        }
    }

    public String getIsPrincipal() {
        if (StringUtil.isBlank(isPrincipal)) {
            this.isPrincipal = "N";
        }
        return isPrincipal;
    }

    public void setIsPrincipal(String isPrincipal) {
        if (StringUtil.isBlank(isPrincipal)) {
            this.isPrincipal = "N";
        } else {
            this.isPrincipal = isPrincipal;
        }
    }

    public String getLevelName() {
        return levelName;
    }

    public void setLevelName(String levelName) {
        this.levelName = levelName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    @JsonIgnore
    @Override
    public String getIdentityType() {
        return IdentityConstants.POSITION;
    }

    @JsonIgnore
    @Override
    public String getAlias() {
        return this.posAlias;
    }

    public static PartyPositionPo fromJsonString(String data) {
        if (JacksonUtil.isNotJsonObject(data)) {
            return null;
        }
        return JacksonUtil.getDTO(data, PartyPositionPo.class);
    }

    public static List<PartyPositionPo> fromJsonArrayString(String listData) {
        if (JacksonUtil.isNotJsonArray(listData)) {
            return Collections.emptyList();
        }
        return JacksonUtil.getDTOList(listData, PartyPositionPo.class);
    }

}
