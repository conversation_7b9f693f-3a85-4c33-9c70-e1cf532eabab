package com.huazheng.bpm.model.setting;

import java.util.List;

/**
 * 节点设置。
 *
 * <pre>
 * 构建组：ibps-bpmn-biz
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2017年1月13日-上午11:44:07
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class BpmDefineSetting {
    // 全局
    private BpmDefineGlobal global;
    // 节点
    private List<BpmDefineNode> nodes;

    public BpmDefineGlobal getGlobal() {
        return global;
    }

    public void setGlobal(BpmDefineGlobal global) {
        this.global = global;
    }

    public List<BpmDefineNode> getNodes() {
        return nodes;
    }

    public void setNodes(List<BpmDefineNode> nodes) {
        this.nodes = nodes;
    }
}
