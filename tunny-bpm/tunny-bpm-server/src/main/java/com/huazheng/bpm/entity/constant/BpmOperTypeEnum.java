package com.huazheng.bpm.entity.constant;

import java.util.Arrays;
import java.util.List;

public enum BpmOperTypeEnum {

    START("start", "流程启动"),
    SUB_START("subStart", "内部子流程启动"),
    CALL_START("callStart", "外部子流程启动"),
    END("end", "流程结束"),
    SUB_END("subEnd", "内部子流程结束"),
    CALL_END("callEnd", "外部子流程结束"),
    MANUAL_END("manualEnd", "终止流程"),
    AGREE("agree", "同意"),
    OPPOSE("oppose", "反对"),
    ABANDON("abandon", "弃权"),
    ADD_SIGN("addSign", "补签"),
    REJECT("reject", "驳回"),
    REJECT_TO_START("rejectToStart", "驳回到发起人"),
    SHFIT("shfit", "转办"),
    REVOKE("revoke", "撤销"),
    SAVE_DRAFT("saveDraft", "保存草稿"),
    SUSPEND("suspend", "挂起"),
    LOCK("lock", "锁定"),
    UNLOCK("unlock", "解锁"),
    FLOW_IMAGE("flowImage", "查看流程图"),
    FLOW_HISTORY("flowHistory", "查看审批历史"),
    CLEAN_DATA("cleanData", "清除数据"),
    DROP_INST("manual_end", "删除实例");

    // 键
    private String key = "";
    // 值
    private String value = "";

    // 构造方法
    BpmOperTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    // =====getting and setting=====
    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return key;
    }

    /**
     * 通过key获取对象
     *
     * @param key
     * @return
     */
    public static BpmOperTypeEnum fromKey(String key) {
        for (BpmOperTypeEnum c : BpmOperTypeEnum.values()) {
            if (c.getKey().equalsIgnoreCase(key)) {
                return c;
            }
        }
        throw new IllegalArgumentException(key);
    }

    public static List<BpmOperTypeEnum> list() {
        return Arrays.asList(values());
    }
}
