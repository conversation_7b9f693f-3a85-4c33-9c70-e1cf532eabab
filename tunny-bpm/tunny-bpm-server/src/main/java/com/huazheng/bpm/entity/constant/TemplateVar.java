package com.huazheng.bpm.entity.constant;

/**
 * 模版变量。
 *
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：simon cai
 * 邮箱：<EMAIL>
 * 日期：2015年12月23日-下午8:17:55
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum TemplateVar {
    BASE_URL("baseUrl", "url"),
    //流程实例标题
    INST_SUBJECT("instSubject", "流程实例标题"),
    //流程实例ID
    INST_ID("instId", "流程实例ID"),
    //节点名称
    NODE_NAME("nodeName", "节点名称"),
    //任务标题
    TASK_SUBJECT("taskSubject", "任务标题"),
    //任务ID
    TASK_ID("taskId", "任务ID"),
    //原因
    CAUSE("cause", "原因"),
    //委托人
    DELEGATE("delegate", "委托人"),
    //代理人
    AGENT("agent", "代理人"),

    RECEIVERID("receiverId", "接收人ID"),

    RECEIVER("receiver", "接收人"),

    SENDER("sender", "发送人"),

    CURRENT_USER("current_user", "当前用户");
    // 键
    private String key = "";
    // 值
    private String value = "";

    // 构造方法
    TemplateVar(String key, String value) {
        this.key = key;
        this.value = value;
    }

    // =====getting and setting=====
    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return key;
    }

    /**
     * 通过key获取对象
     *
     * @param key
     * @return
     */
    public static TemplateVar fromKey(String key) {
        for (TemplateVar c : TemplateVar.values()) {
            if (c.getKey().equalsIgnoreCase(key)) {
                return c;
            }
        }
        throw new IllegalArgumentException(key);
    }

}
