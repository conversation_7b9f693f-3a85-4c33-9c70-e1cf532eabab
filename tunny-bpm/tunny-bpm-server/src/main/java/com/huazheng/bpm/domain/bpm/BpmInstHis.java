package com.huazheng.bpm.domain.bpm;


import com.huazheng.bpm.dao.bpm.BpmInstHisDao;
import com.huazheng.bpm.dao.bpm.BpmInstHisQueryDao;
import com.huazheng.bpm.dao.bpm.BpmInstQueryDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmInstHisPo;
import com.huazheng.bpm.entity.constant.ProcInstStatus;
import com.huazheng.bpm.entity.inst.IBpmProcInst;
import com.huazheng.bpm.repository.BpmInstHisRepository;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 流程实例历史 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：huangchunyan
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-01-19 15:56:36
 * </pre>
 */
@SuppressWarnings("serial")
@Service
@Scope("prototype")
public class BpmInstHis extends AbstractDomain<String, BpmInstHisPo> {

    @Resource
    private BpmInstHisDao bpmInstHisDao;
    @Resource
    private BpmInstQueryDao bpmInstQueryDao;
    @Resource
    private BpmInstHisRepository bpmInstHisRepository;
    @Resource
    private BpmInstHisQueryDao bpmInstHisQueryDao;
    @Resource
    private ActExecution actExecutionDomain;
    @Resource
    private BpmApprove bpmApproveDomain;
    @Resource
    private BpmTaskAssign bpmTaskAssignDomain;
    @Resource
    private BpmTask bpmTaskDomain;
    @Resource
    private BpmTaskSign bpmTaskSignDomain;

    @Override
    protected void init() {
        this.setDao(bpmInstHisDao);
    }

    /**
     * 根据实例id删除实例数据，包含任务、act、人员指派
     *
     * @param processInstId
     */
    public void remove(String processInstId) {
        BpmInstHisPo inst = bpmInstHisQueryDao.get(processInstId);
        // 草稿
        if (ProcInstStatus.STATUS_DRAFT.getKey().equals(inst.getStatus())) {
            super.delete(processInstId);
        } else {
            IBpmProcInst topInstance = bpmInstHisRepository.getTopBpmProcInst(processInstId);
            String topInstId = topInstance.getId();
            String topBpmnInstId = topInstance.getBpmnInstId();

            List<String> instIdList = bpmInstHisRepository.findIdsByParentId(topInstId, true);

            List<String> bpmnInstList = bpmInstQueryDao.findInstIdsByIds(instIdList);
            // 删除
            removeCascade(instIdList);
            // 删除流程数据。
            actExecutionDomain.delByInstList(bpmnInstList);
            // 删除关联的实例。
            actExecutionDomain.delete(topBpmnInstId);
        }
    }

    /**
     * 删除任务数据 删除任务人员数据 删除实例数据 删除抄送数据 状态数据 TASK_READ BPM_TASK_SIGNDATA
     * BPM_TASK_TURN
     *
     * @param instList void
     * @throws Exception
     */
    private void removeCascade(List<String> instList) {
        // 删除意见数据
        //bpmApproveDomain.delHisByInstList(instList);
        // 会签数据
        //bpmTaskSignDomain.delByInst(instList);

        for (String id : instList) {
            super.delete(id);
        }
    }

    /**
     * 清除测试数据
     *
     * @param defKey
     */
    public void removeTestInstByDefKey(String defKey) {
        List<BpmInstHisPo> hisList = bpmInstHisQueryDao.findByDefKeyFormal(defKey, IBpmProcInst.FORMAL_NO);
        for (BpmInstHisPo instance : hisList) {
            remove(instance.getId());
        }
    }
}
