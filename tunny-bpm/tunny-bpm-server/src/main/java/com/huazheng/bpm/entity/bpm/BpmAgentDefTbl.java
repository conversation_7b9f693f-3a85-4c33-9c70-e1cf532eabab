package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;


/**
 * 流程代理定义 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-03-30 17:29:14
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmAgentDefTbl extends AbstractPo<String> {
    protected String id;        /*主键*/
    protected String agentId;        /*代理ID*/
    protected String procDefKey;        /*流程key*/
    protected String nodeId;        /*节点ID*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    /**
     * 返回 代理ID
     *
     * @return
     */
    public String getAgentId() {
        return this.agentId;
    }

    public void setProcDefKey(String procDefKey) {
        this.procDefKey = procDefKey;
    }

    /**
     * 返回 流程key
     *
     * @return
     */
    public String getProcDefKey() {
        return this.procDefKey;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    /**
     * 返回 节点ID
     *
     * @return
     */
    public String getNodeId() {
        return this.nodeId;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("agentId", this.agentId)
                .append("procDefKey", this.procDefKey)
                .append("nodeId", this.nodeId)
                .toString();
    }
}
