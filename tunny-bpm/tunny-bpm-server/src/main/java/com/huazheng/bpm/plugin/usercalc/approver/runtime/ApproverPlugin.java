package com.huazheng.bpm.plugin.usercalc.approver.runtime;


import com.huazheng.bpm.entity.constant.BpmConstants;
import com.huazheng.bpm.model.base.BpmIdentity;
import com.huazheng.bpm.model.task.IBpmTaskApproval;
import com.huazheng.bpm.plugin.AbstractUserCalcPlugin;
import com.huazheng.bpm.plugin.IBpmPluginDefine;
import com.huazheng.bpm.service.BpmApprovalService;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.session.BpmUserCalcPluginSession;
import com.huazheng.bpm.util.string.StringUtil;
import com.huazheng.bpm.util.string.StringValidator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 流程实例审批人
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月14日-上午9:58:06
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@Service("approverPlugin")
public class ApproverPlugin extends AbstractUserCalcPlugin {

    @Resource
    private BpmApprovalService bpmApprovalService;

    @Override
    public List<BpmIdentity> queryByPluginDef(
            BpmUserCalcPluginSession pluginSession, IBpmPluginDefine pluginDef) {
        String processInstanceId = (String) pluginSession.getVariables().get(BpmConstants.PROCESS_INST_ID);
        List<BpmIdentity> bpmIdentities = new ArrayList<BpmIdentity>();
        List<IBpmTaskApproval> taskOpinionList = bpmApprovalService.findInstApproves(processInstanceId);
        if (BeanUtils.isEmpty(taskOpinionList)) {
            return bpmIdentities;
        }
        for (IBpmTaskApproval taskOpinion : taskOpinionList) {
            if (StringUtil.isEmpty(taskOpinion.getAuditor())
                    || StringValidator.isZeroEmpty(taskOpinion.getAuditor())) {
                continue;
            }

            BpmIdentity bpmIdentity = getBpmIdentityConverter().convertByUserId(taskOpinion.getAuditor());
            bpmIdentities.remove(bpmIdentity);
            bpmIdentities.add(bpmIdentity);
        }

        return bpmIdentities;
    }

    @Override
    public boolean supportPreView() {
        return false;
    }

}
