package com.huazheng.bpm.plugin;


import com.huazheng.bpm.entity.constant.ExtractType;
import com.huazheng.bpm.entity.constant.LogicType;

/**
 * 人员抽取插件定义接口
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午6:58:48
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface IBpmUserCalcPluginDefine extends IBpmPluginDefine {

    /**
     * 获取抽取类型。
     *
     * @return ExtractType
     */
    ExtractType getExtract();

    /**
     * 设置抽取类型。
     *
     * @param type void
     */
    void setExtract(ExtractType type);

    /**
     * 逻辑类型。
     *
     * @return LogicType
     */
    LogicType getLogicCal();

    /**
     * 设置逻辑类型
     *
     * @param logicType void
     */
    void setLogicCal(LogicType logicType);

}
