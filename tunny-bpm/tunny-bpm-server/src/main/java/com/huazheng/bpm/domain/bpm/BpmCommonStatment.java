package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmCommonStatmentDao;
import com.huazheng.bpm.dao.bpm.BpmCommonStatmentQueryDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmCommonStatmentPo;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.core.StringPool;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 常用语 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：zhongjh
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-10-28 10:10:24
 * </pre>
 */
@SuppressWarnings("serial")
@Service
@Scope("prototype")
public class BpmCommonStatment extends AbstractDomain<String, BpmCommonStatmentPo> {

    @Resource
    private BpmCommonStatmentDao bpmCommonStatmentDao;
    @Resource
    private BpmCommonStatmentQueryDao bpmCommonStatmentQueryDao;


    protected void init() {
        bpmCommonStatmentDao = AppUtil.getBean(BpmCommonStatmentDao.class);
        this.setDao(bpmCommonStatmentDao);
    }

    public void updataTimes(String content, String userId, String actionName) {
        BpmCommonStatmentPo po = isExist(content, userId, actionName);
        if (BeanUtils.isNotEmpty(po.getId())) {
            po.setTimes(po.getTimes() + 1);
            bpmCommonStatmentDao.update(po);
        } else {
            po.setIsDefault("N");
            po.setTimes(1);
            bpmCommonStatmentDao.create(po);
        }
    }

    public BpmCommonStatmentPo isExist(String content, String userId, String actionName) {
        List<BpmCommonStatmentPo> list = bpmCommonStatmentQueryDao
                .findByParams(actionName, userId, content, null);

        return BeanUtils.isNotEmpty(list) ? list.get(0) : new BpmCommonStatmentPo();
    }

    /**
     * 取消用户某类型的默认值
     *
     * @param action
     * @param userId
     */
    public void cancelOtherDefault(String action, String userId) {
        bpmCommonStatmentDao.cancelOtherDefault(action, userId);
    }

    /**
     * 设置默认常用语
     */
    public void setDefault() {
        BpmCommonStatmentPo po = getData();
        cancelOtherDefault(po.getAction(), po.getCreateBy());
        po.setIsDefault(StringPool.Y);
        update();
    }

}
