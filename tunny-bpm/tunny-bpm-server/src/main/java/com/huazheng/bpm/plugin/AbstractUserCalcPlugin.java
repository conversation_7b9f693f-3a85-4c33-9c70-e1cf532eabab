package com.huazheng.bpm.plugin;


import com.huazheng.bpm.entity.constant.ExtractType;
import com.huazheng.bpm.model.base.BpmIdentity;
import com.huazheng.bpm.model.node.UserAssignRule;
import com.huazheng.bpm.service.BpmIdentityConverter;
import com.huazheng.bpm.service.BpmIdentityExtractService;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.session.BpmUserCalcPluginSession;

import javax.annotation.Resource;
import java.util.*;

/**
 * 抽象用户计算插件，其他的人员计算插件策略实现这个接口
 * <pre>
 * 构建组：ibps-bpmn-plugin-base
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午7:02:23
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public abstract class AbstractUserCalcPlugin extends AbstractBpmPlugin implements IBpmUserCalcPlugin {
    //是否是预览模式
    public static ThreadLocal<Boolean> isPreVrewModel = new ThreadLocal<Boolean>();

    private BpmIdentityConverter bpmIdentityConverter;
    @Resource
    private BpmIdentityExtractService extractService;
    //private BpmIdentityExtractService extractService = AppUtil.getBean(BpmIdentityExtractService.class);

    protected BpmIdentityConverter getBpmIdentityConverter() {
        if (bpmIdentityConverter == null)
            bpmIdentityConverter = AppUtil.getBean(BpmIdentityConverter.class);
        return bpmIdentityConverter;
    }

    /**
     * 插件查询数据，由具体类进行实现。
     *
     * @param pluginSession
     * @param pluginDef
     * @return List&lt;BpmIdentity>
     */
    protected abstract List<BpmIdentity> queryByPluginDef(BpmUserCalcPluginSession pluginSession, IBpmPluginDefine pluginDef);

    /**
     * 查询人员。
     */
    public List<BpmIdentity> execute(BpmUserCalcPluginSession pluginSession, IBpmPluginDefine pluginDef) {
        // 如果是预览模式，而且当前插件不支持预览。则返回空集合。
        Boolean preVrewModel = isPreVrewModel.get();
        if (preVrewModel != null && preVrewModel) {
            if (!this.supportPreView()) return Collections.emptyList();
        }

        List<BpmIdentity> list = queryByPluginDef(pluginSession, pluginDef);
        if (BeanUtils.isEmpty(list)) return list;

        ExtractType extractType = ((AbstractUserCalcPluginDefine) pluginDef).getExtract();
        //排除重复选择，使用LinkedHashSet进行排除重复。
        Set<BpmIdentity> set = new LinkedHashSet<BpmIdentity>();
        List<BpmIdentity> rtnList = new ArrayList<BpmIdentity>();

        //根据抽取类型查询人员。
        list = extract(list, extractType);

        set.addAll(list);

        rtnList.addAll(set);

        return rtnList;
    }

    public List<BpmIdentity> run(BpmUserCalcPluginSession pluginSession, List<UserAssignRule> userAssignRules) {
        return null;
    }

    /**
     * 抽取处理
     *
     * @param bpmIdentities    待处理的实体集合
     * @param extractType      抽取类型（通过getExtractType方法获得）
     * @param userRelGroupType 用户和用户组的关系类型键（key）
     *                         void
     */
    @SuppressWarnings("unchecked")
    protected List<BpmIdentity> extract(List<BpmIdentity> bpmIdentities, ExtractType extractType) {
        //数据有效性判断
        if (BeanUtils.isEmpty(bpmIdentities)) return Collections.EMPTY_LIST;

        //临时存储抽取结果
        List<BpmIdentity> results = new ArrayList<BpmIdentity>();

        // 根据类型做不同处理
        switch (extractType) {
            case EXACT_NOEXACT:
                //不抽取
                results = bpmIdentities;
                break;
            case EXACT_EXACT_USER:
                //抽取人员
                results = extractService.extractBpmIdentity(bpmIdentities);
                break;
            default:
                results = extractService.extractBpmIdentity(bpmIdentities);
                break;
        }

        return results;
    }

    /**
     * 是否支持预览。如果不支持子类重写改方法。
     *
     * @return
     */
    public boolean supportPreView() {
        return true;
    }
}
