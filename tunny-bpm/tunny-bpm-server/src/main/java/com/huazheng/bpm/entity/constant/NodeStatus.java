package com.huazheng.bpm.entity.constant;

/**
 * 节点状态。
 * <pre>
 * 描述：流程节点状态。
 * 构建组：ibps-bpmn-api
 * 作者：simon cai
 * 邮箱:<EMAIL>
 * 日期:2015-3-18-下午1:51:05
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum NodeStatus {

    PENDING("pending", "待审批", "#F55353", 1),
    START("submit", "提交", "#F89801", 2),
    RE_SUBMIT("resubmit", "重新提交", "#E8D367", 3),
    AGREE("agree", "同意", "#58BC5B", 4),
    OPPOSE("oppose", "反对", "#2886C6", 5),
    REJECT("reject", "驳回", "#700101", 6),
    REJECT_TO_START("rejectToStart", "驳回到发起人", "#A54E4E", 7),
    REVOKE("revoke", "撤销", "#569296", 8),
    RECOVER_TO_START("recoverToStart", "撤销到发起人", "#58A7CB", 9),
    SIGN_PASS("sign_pass", "会签通过", "#6FA547", 10),
    SIGN_NO_PASS("sign_no_pass", "会签不通过", "#AFAB3D", 11),
    ABANDON("abandon", "弃权", "#026670", 12),
    MANUAL_END("manual_end", "人工终止", "#BC79B8", 13),
    COMPLETE("complete", "完成", "#5B6956", 14),
    SUSPEND("suspend", "挂起", "#C33A1F", 15),

    SIGN_BACK_CANCEL("signBackCancel", "驳回取消", "#C33A1F", 16),
    SIGN_RECOVER_CANCEL("signRecoverCancel", "撤销取消", "#C33A1F", 17),
    AGREE_SIGN_PASS_CANCEL("passCancel", "通过取消", "#C33A1F", 18),
    OPPOSE_SIGN_NOPASS_CANCEL("notPassCancel", "不通过取消", "#C33A1F", 19),
    TRANS_FORMING("transforming", "流转中", "#C33A1F", 20),
    TRANS_AGREE("transAgree", "流转同意", "#C33A1F", 21),
    TRANS_OPPOSE("transOppose", "流转反对", "#C33A1F", 22),
    //	SKIP("skip","跳过执行","#C33A1F",23),
    SKIP("skip", "重复跳过", "#C33A1F", 23),

    CALL_SUB_PROC("callSubProc", "外部子流程", "#C33A1F", 24),
    AUTOTASK("autoTask", "自动节点", "#C33A1A", 25);
//	MESSAGE("messageTask","消息节点","#C33A2A",24),
//	SCRIPT("scriptTask","脚本节点","#C33A3A",25),
//	SERVICE("serviceTask","服务节点","#C33A4A",26);

    // 键
    private String key = "";
    // 值
    private String value = "";

    //颜色
    private String color = "";
    //排序
    private Integer order = 0;

    // 构造方法
    NodeStatus(String key, String value, String color, Integer order) {
        this.key = key;
        this.value = value;
        this.color = color;
        this.order = order;
    }


    // =====getting and setting=====
    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getColor() {
        return color;
    }


    public void setColor(String color) {
        this.color = color;
    }

    public Integer getOrder() {
        return order;
    }


    public void setOrder(Integer order) {
        this.order = order;
    }


    @Override
    public String toString() {
        return key;
    }

    /**
     * 通过key获取对象
     *
     * @param key
     * @return
     */
    public static NodeStatus fromKey(String key) {
        for (NodeStatus c : NodeStatus.values()) {
            if (c.getKey().equalsIgnoreCase(key)) {
                return c;
            }
        }
        throw new IllegalArgumentException(key);
    }

}
