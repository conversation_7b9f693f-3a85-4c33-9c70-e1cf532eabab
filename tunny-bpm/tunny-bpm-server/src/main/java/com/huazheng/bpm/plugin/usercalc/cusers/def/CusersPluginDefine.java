package com.huazheng.bpm.plugin.usercalc.cusers.def;


import com.huazheng.bpm.plugin.AbstractUserCalcPluginDefine;
import com.huazheng.bpm.plugin.usercalc.ExecutorVar;

/**
 * 指定用户插件定义
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午6:55:58
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
public class CusersPluginDefine extends AbstractUserCalcPluginDefine {

    /**
     * 来源
     */
    private String source = "";

    /**
     * 变量
     */
    private ExecutorVar executorVar;

    /**
     * 名称，使用逗号分隔。
     */
    private String fullName = "";
    /**
     * 帐号，使用逗号分隔。
     */
    private String account = "";

    private String nodeName = "";
    private String nodeId = "";

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public void setExecutorVar(ExecutorVar executorVar) {
        this.executorVar = executorVar;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public ExecutorVar getExecutorVar() {
        return executorVar;
    }

}
