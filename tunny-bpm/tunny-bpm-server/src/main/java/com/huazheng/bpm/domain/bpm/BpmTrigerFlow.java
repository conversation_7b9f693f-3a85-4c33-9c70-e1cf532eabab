package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmTrigerFlowDao;
import com.huazheng.bpm.dao.bpm.BpmTrigerFlowQueryDao;
import com.huazheng.bpm.dao.bpm.BpmTrigerParamDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmTrigerFlowPo;
import com.huazheng.bpm.entity.bpm.BpmTrigerParamPo;
import com.huazheng.bpm.model.node.ITrigerFlow;
import com.huazheng.bpm.model.setting.BpmDefineNode;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 触发新流程 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-08-23 19:01:23
 * </pre>
 */
@SuppressWarnings("serial")
@Service
@Scope("prototype")
public class BpmTrigerFlow extends AbstractDomain<String, BpmTrigerFlowPo> {

    private BpmTrigerFlowDao bpmTrigerFlowDao = null;
    private BpmTrigerFlowQueryDao bpmTrigerFlowQueryDao = null;

    private BpmTrigerParamDao bpmTrigerParamDao = null;


    protected void init() {
        bpmTrigerFlowDao = AppUtil.getBean(BpmTrigerFlowDao.class);
        bpmTrigerFlowQueryDao = AppUtil.getBean(BpmTrigerFlowQueryDao.class);
        bpmTrigerParamDao = AppUtil.getBean(BpmTrigerParamDao.class);
        this.setDao(bpmTrigerFlowDao);
    }

    @Override
    protected void onSave() {
        if (BeanUtils.isNotEmpty(getId()))
            setNewFlag(BeanUtils.isEmpty(bpmTrigerFlowQueryDao.get(getId())));
    }

    /**
     * 主从表一并保存
     * void
     *
     * @throws
     * @since 1.0.0
     */
    public void saveCascade() {
        save();
        if (getData().isDelBeforeSave()) {
            bpmTrigerParamDao.deleteByMainId(getData().getId());
        }

        if (BeanUtils.isNotEmpty(getData().getBpmTrigerParamPoList())) {
            for (BpmTrigerParamPo bpmTrigerParamPo : getData().getBpmTrigerParamPoList()) {
                //设置外键
                bpmTrigerParamPo.setTrigerId(getData().getId());
                bpmTrigerParamDao.create(bpmTrigerParamPo);
            }
        }
    }

    /**
     * 主从表一并删除
     * void
     *
     * @throws
     * @since 1.0.0
     */
    public void deleteByIdsCascade(String[] ids) {
        for (String id : ids) {
            BpmTrigerFlowPo po = bpmTrigerFlowQueryDao.get(id);
            if (BeanUtils.isNotEmpty(po) && BeanUtils.isNotEmpty(po.getId())) {
                bpmTrigerParamDao.deleteByMainId(po.getId());
            }
        }
        deleteByIds(ids);
    }

    /**
     * 保存触发流程
     *
     * @param defId
     * @param nodes
     */
    public void save(String defId, List<BpmDefineNode> nodes) {
        List<ITrigerFlow> trigerFlows = null;
        for (BpmDefineNode node : nodes) {
            trigerFlows = node.getTrigerFlows();
            if (BeanUtils.isEmpty(trigerFlows)) {
                continue;
            }

            List<BpmTrigerFlowPo> opoList = bpmTrigerFlowQueryDao.findByDefIdAndNodeId(defId, node.getId());
            if (BeanUtils.isNotEmpty(opoList)) {
                for (BpmTrigerFlowPo po : opoList) {
                    bpmTrigerParamDao.deleteByMainId(po.getId());
                }
                bpmTrigerFlowDao.deleteByProcNodeId(defId, node.getId());
            }

            for (ITrigerFlow trigerFlow : trigerFlows) {
                //trigerFlow.setDefId(defId);
                //trigerFlow.setNodeId(node.getId());
                setData((BpmTrigerFlowPo) trigerFlow);
                saveCascade();
            }
        }
    }

}
