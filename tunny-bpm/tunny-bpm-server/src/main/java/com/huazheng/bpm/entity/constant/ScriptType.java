package com.huazheng.bpm.entity.constant;


/**
 * 脚本类型
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：simon cai
 * 邮箱:<EMAIL>
 * 日期:2015-7-16-下午11:39:48
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum ScriptType {

    START("start", "开始脚本"),
    END("end", "结束脚本"),
    CREATE("create", "创建脚本"),
    COMPLETE("complete", "结束脚本");

    // 键
    private String key = "";
    // 值
    private String value = "";

    // 构造方法
    ScriptType(String key, String value) {
        this.key = key;
        this.value = value;
    }

    // =====getting and setting=====
    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return key;
    }

    /**
     * 通过key获取对象
     *
     * @param key
     * @return
     */
    public static ScriptType fromKey(String key) {
        for (ScriptType c : ScriptType.values()) {
            if (c.getKey().equalsIgnoreCase(key)) {
                return c;
            }
        }
        throw new IllegalArgumentException(key);
    }

}
