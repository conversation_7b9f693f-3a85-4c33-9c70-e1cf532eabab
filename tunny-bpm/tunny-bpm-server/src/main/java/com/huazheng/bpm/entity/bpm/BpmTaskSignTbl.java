package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * 会签数据
 * 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：simon cai
 * 邮箱地址：<EMAIL>
 * 创建时间：2016-12-20 20:47:28
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmTaskSignTbl extends AbstractPo<String> {
    protected String id;        /*主键*/
    protected String defId;        /*流程定义ID*/
    protected String instId;        /*流程实例ID*/
    protected String actInstId;        /*ACT流程实例ID*/
    protected String nodeId;        /*节点ID*/
    protected String taskId;        /*流程任务ID*/
    protected String qualifiedId;        /*有资格审批的成员ID*/
    protected java.util.Date createTime;        /*创建时间*/
    protected String voteResult;        /*投票结果(no 未投票 通过 agree,abandon 弃权,oppose 反对)*/
    protected String signResult;        /*会签结果(通过 agree,反对 oppose)*/
    protected String privilege;        /*使用特权（all/direct/oneticket/allowAddSign） @link PrivilegeMode*/
    protected String voteId;        /*投票人*/
    protected String batch;        /*批次号*/
    protected java.util.Date voteTime;        /*投票时间*/
    protected Integer index;        /*投票次序*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setDefId(String defId) {
        this.defId = defId;
    }

    /**
     * 返回 流程定义ID
     *
     * @return
     */
    public String getDefId() {
        return this.defId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    /**
     * 返回 流程实例ID
     *
     * @return
     */
    public String getInstId() {
        return this.instId;
    }

    public void setActInstId(String actInstId) {
        this.actInstId = actInstId;
    }

    /**
     * 返回 ACT流程实例ID
     *
     * @return
     */
    public String getActInstId() {
        return this.actInstId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    /**
     * 返回 节点ID
     *
     * @return
     */
    public String getNodeId() {
        return this.nodeId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    /**
     * 返回 流程任务ID
     *
     * @return
     */
    public String getTaskId() {
        return this.taskId;
    }

    public String getBatch() {
        return batch;
    }

    public void setBatch(String batch) {
        this.batch = batch;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public void setQualifiedId(String qualifiedId) {
        this.qualifiedId = qualifiedId;
    }

    /**
     * 返回 有资格审批的成员ID
     *
     * @return
     */
    public String getQualifiedId() {
        return this.qualifiedId;
    }

    public void setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 返回 创建时间
     *
     * @return
     */
    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public void setVoteResult(String voteResult) {
        this.voteResult = voteResult;
    }

    /**
     * 返回 投票结果(no 未投票 通过 agree,abandon 弃权,oppose 反对)
     *
     * @return
     */
    public String getVoteResult() {
        return this.voteResult;
    }

    public String getSignResult() {
        return signResult;
    }

    public void setSignResult(String signResult) {
        this.signResult = signResult;
    }

    public String getPrivilege() {
        return privilege;
    }

    public void setPrivilege(String privilege) {
        this.privilege = privilege;
    }

    public void setVoteId(String voteId) {
        this.voteId = voteId;
    }

    /**
     * 返回 投票人
     *
     * @return
     */
    public String getVoteId() {
        return this.voteId;
    }

    public void setVoteTime(java.util.Date voteTime) {
        this.voteTime = voteTime;
    }

    /**
     * 返回 投票时间
     *
     * @return
     */
    public java.util.Date getVoteTime() {
        return this.voteTime;
    }

    public void setIndex(int i) {
        this.index = i;
    }

    /**
     * 返回 投票次序
     *
     * @return
     */
    public Integer getIndex() {
        return this.index;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("defId", this.defId)
                .append("instId", this.instId)
                .append("actInstId", this.actInstId)
                .append("nodeId", this.nodeId)
                .append("taskId", this.taskId)
                .append("qualifiedId", this.qualifiedId)
                .append("createTime", this.createTime)
                .append("voteResult", this.voteResult)
                .append("signResult", this.signResult)
                .append("voteId", this.voteId)
                .append("batch", this.batch)
                .append("voteTime", this.voteTime)
                .append("index", this.index)
                .toString();
    }
}
