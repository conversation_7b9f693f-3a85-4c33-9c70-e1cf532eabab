package com.huazheng.bpm.model.node;


import com.huazheng.bpm.entity.constant.NodeType;
import com.huazheng.bpm.entity.constant.ScriptType;
import com.huazheng.bpm.model.define.FormInitItem;
import com.huazheng.bpm.model.define.IBpmProcDefine;
import com.huazheng.bpm.model.define.NodeAttributes;
import com.huazheng.bpm.model.form.IForm;
import com.huazheng.bpm.plugin.IBpmPluginContext;
import com.huazheng.bpm.plugin.ITaskAopPluginContext;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * BPMN节点定义
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月7日-下午5:46:35
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface IBpmNodeDefine extends Serializable {

    /**
     * 是否多实例
     */
    String MULTI = "multi";

    /**
     * 串行并行
     */
    String PARALLEL = "parallel";

    /**
     * 子流程件。
     */
    String FLOWKEY = "flowKey";

    /**
     * 流程定义ID
     */
    String PROCESS_DEF_ID = "defId";

    /**
     * 运行时的网关执行信息Id
     */
    String getBpmGatewayExecId();

    /**
     * 运行时的网关执行信息Id
     */
    void setBpmGatewayExecId(String gatewayExecId);

    /**
     * 取得节点的ID
     *
     * @return
     */
    String getNodeId();

    /**
     * 设置节点ID
     *
     * @return
     */
    void setNodeId(String nodeId);

    /**
     * 取得节点的名称
     *
     * @return
     */
    String getName();

    /**
     * 设置节点的名称
     *
     * @return
     */
    void setName(String name);

    /**
     * 取得节点的类型
     *
     * @return
     */
    NodeType getType();

    /**
     * 设置节点的类型
     *
     * @return
     */
    void setType(NodeType type);

    /**
     * 取得节点的排序
     *
     * @return
     */
    Integer getOrder();

    /**
     * 设置节点的排序
     *
     * @return
     */
    void setOrder(Integer order);

    /**
     * 取得通知类型。
     *
     * @return String
     */
    String getNotifyType();

    /**
     * 取得当前节点的所有入口节点集合
     *
     * @return
     */
    List<IBpmNodeDefine> getIncomeNodeList();

    /**
     * 取得当前节点的所有出口节点集合
     *
     * @return
     */
    List<IBpmNodeDefine> getOutgoingNodeList();

    /**
     * 获取跳出的任务节点
     *
     * @return List&lt;BpmNodeDefine>
     */
    List<IBpmNodeDefine> getOutgoingTaskNodeList();

    /**
     * 取得当前节点的后续任务节点，只包括流程中的任务节点，不包括内部子流程和外部子流程的节点。
     *
     * @param includeSignTask 是否包含会签节点。
     * @return List&lt;BpmNodeDefine>
     */
    List<IBpmNodeDefine> getInnerOutgoingTaskNodeList(boolean includeSignTask);

    /**
     * 取得节点的事件插件
     *
     * @return List&lt;BpmPluginContext>
     */
    List<IBpmPluginContext> getBpmPluginContextList();

    List<ITaskAopPluginContext> getTaskAopPluginContextList();

    /**
     * 获取节点所在的流程定义。
     *
     * @return IBpmProcDefine
     * @throws
     * @since 1.0.0
     */
    IBpmProcDefine<?> getBpmProcDefine();

    /**
     * 获得属性值集合
     *
     * @return String
     */
    String getAttribute(String name);

    /**
     * 添加入口的节点定义。
     *
     * @param bpmNodeDefine void
     */
    void addIncomeNode(IBpmNodeDefine bpmNodeDefine);

    /**
     * 添加出口的节点定义。
     *
     * @param bpmNodeDefine void
     */
    void addOutgoingNode(IBpmNodeDefine bpmNodeDefine);

    /**
     * 获取上级节点。
     *
     * @return BpmNodeDefine
     */
    IBpmNodeDefine getParentBpmNodeDefine();

    /**
     * 取得实际的路径。
     *
     * @return String
     */
    String getRealPath();

    /**
     * 获取根的流程定义。
     *
     * @return IBpmProcDefine
     */
    IBpmProcDefine<?> getRootProcDefine();

    /**
     * 获取表单定义。
     *
     * @return Form
     */
    IForm getForm();

    /**
     * 根据插件类名获取插件实例上下文定义。
     *
     * @param cls
     * @return BpmPluginContext
     */
    IBpmPluginContext getPluginContext(Class<?> cls);

    /**
     * 设置子流程表单。
     *
     * @param list void
     */
    void setSubProcFormList(List<IForm> list);

    /**
     * 获取子流程表单配置。
     *
     * @return List&lt;Form>
     */
    List<IForm> getSubProcFormList();

    /**
     * 根据流程定义获取表单配置。
     *
     * @param bpmProcDefineKey
     * @return Form
     */
    IForm getForm(String bpmProcDefineKey);

    /**
     * 获取节点外向的分支条件表达式。
     *
     * <pre>
     * 	返回分支条件。
     *  键为分支节点ID
     *  值为分支脚本
     * </pre>
     *
     * @return Map&lt;String,String>
     */
    Map<String, String> getConditionMap();

    /**
     * 设置分支条件。
     *
     * @param conditionMap void
     */
    void setConditionMap(Map<String, String> conditionMap);

    /**
     * 添加条件
     *
     * @param targetNode 目标节点
     * @param condition  条件 void
     */
    void addCondition(String targetNode, String condition);

    /**
     * 获取节点外向的分支条件表达式。
     *
     * <pre>
     * 	返回分支条件。
     *  键为分支节点ID
     *  值为分支脚本
     * </pre>
     *
     * @return Map&lt;String,String>
     */
    Map<String, String> getConditionDisplayMap();

    /**
     * 设置分支条件。
     *
     * @param conditionMap void
     */
    void setConditionDisplayMap(Map<String, String> conditionDisplayMap);

    /**
     * 添加条件
     *
     * @param targetNode 目标节点
     * @param condition  条件
     * @param display    条件展示 void
     */
    void addCondition(String targetNode, String condition, String display);

    /**
     * 获取节点脚本。
     *
     * @return Map&lt;String,String>
     */
    Map<ScriptType, String> getScriptMap();

    /**
     * 添加脚本。
     *
     * @param scriptType 类型
     * @param script     脚本 void
     */
    void addScript(ScriptType scriptType, String script);

    /**
     * 取得节点表单初始化数据。
     *
     * @return FormInitItem
     */
    FormInitItem getFormInitItem();

    /**
     * 取得节点表单初始化数据。
     *
     * @param parentDefKey 流程key。
     * @return FormInitItem
     */
    FormInitItem getFormInitItemByParentKey(String parentDefKey);

    List<NodeAttributes> getNodePropertiesList();

    void setNodePropertiesList(List<NodeAttributes> nodePropertiesList);

    void addNodeProperties(NodeAttributes nodeProperties);

    /**
     * 取得节点属性信息。
     *
     * @return NodeProperties
     */
    NodeAttributes getLocalProperties();

    /**
     * 取得节点属性信息。
     *
     * @return List&lt;Attribute>
     */
    List<Attribute> getLocalAttributes();

    void addLocalAttribute(Attribute attribute);

    /**
     * 根据父key获取节点属性。
     *
     * @param parentProcDefineKey
     * @return NodeProperties
     */
    NodeAttributes getPropByParentProcDefineKey(String parentProcDefineKey);

    /**
     * 获取节点按钮。
     *
     * <pre>
     * 1.获取节点配置按钮数据。
     * 2.如果获取不到,则获取节点的默认按钮。
     * </pre>
     *
     * @return List&lt;Button>
     */
    List<Button> getButtonList();


    /**
     * 是否有自定义按钮
     */
    boolean isDefaultBtn();
}
