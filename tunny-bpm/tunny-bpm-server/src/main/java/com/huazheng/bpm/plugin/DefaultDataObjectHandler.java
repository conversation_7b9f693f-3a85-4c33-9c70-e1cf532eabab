package com.huazheng.bpm.plugin;

import com.huazheng.bpm.element.bpm.SourceType;
import com.huazheng.bpm.entity.constant.BpmConstants;
import com.huazheng.bpm.entity.inst.IBpmProcInst;
import com.huazheng.bpm.model.base.IDataObject;
import com.huazheng.bpm.model.define.FieldInitSetting;
import com.huazheng.bpm.model.define.FormInitItem;
import com.huazheng.bpm.model.node.IBpmNodeDefine;
import com.huazheng.bpm.repository.BpmInstRepository;
import com.huazheng.bpm.service.BpmProcInstService;
import com.huazheng.bpm.service.GroovyScriptEngine;
import com.huazheng.bpm.service.IBpmDefineReader;
import com.huazheng.bpm.util.base.ContextThreadUtil;
import com.huazheng.bpm.util.cmd.ActionCmd;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.string.StringValidator;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 负责根据配置修改表单数据。
 *
 * <pre>
 *
 * 构建组：ibps-bpmn-core
 * 作者：caixy
 * 邮箱:<EMAIL>
 * 日期:2014年8月24日-下午4:01:32
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@Service("dataObjectHandler")
@Transactional
public class DefaultDataObjectHandler implements DataObjectHandler {

    @Resource
    private IBpmDefineReader bpmDefineReader;
    @Resource
    private BpmInstRepository bpmInstRepository;
    @Resource
    private GroovyScriptEngine groovyScriptEngine;
    @Resource
    private BpmProcInstService bpmInstService;

    @Override
    public void handShowData(String defId, IDataObject dataObject) {
        FormInitItem formInitItem = getFormInitItem(defId);
        List<FieldInitSetting> fieldInitSettings = getFieldSetting(formInitItem, true);
        if (fieldInitSettings == null) {
            return;
        }

        setDataObject(fieldInitSettings, dataObject);
    }

    @Override
    public void handSaveData(IBpmProcInst instance, IDataObject dataObject) {
        FormInitItem formInitItem = getFormInitItem(instance.getProcDefId());
        List<FieldInitSetting> fieldInitSettings = getFieldSetting(formInitItem, false);
        if (fieldInitSettings == null) {
            return;
        }

        setDataObject(fieldInitSettings, dataObject);
    }

    @Override
    public void handSaveData(IBpmProcInst instance, String nodeId, IDataObject dataObject) {
        // 获取节点修改配置。
        FormInitItem formInitItem = getFormInitItem(instance, nodeId);
        List<FieldInitSetting> fieldInitSettings = getFieldSetting(formInitItem, false);
        if (fieldInitSettings == null) {
            return;
        }

        setDataObject(fieldInitSettings, dataObject);
    }

    /**
     * 修改BO数据。
     *
     * @param fieldInitSettings
     * @param dataObject        void
     */
    private void setDataObject(List<FieldInitSetting> fieldInitSettings, IDataObject dataObject) {
        for (FieldInitSetting setting : fieldInitSettings) {
            String boDefCode = dataObject.getIboDef().getCode();
            if (!boDefCode.equals(setting.getBoDefCode())) {
                continue;
            }
            Object data = null;
            String fieldName = setting.getFieldName();
            String script = setting.getSetting();
            // 获取流水号
            if (setting.getSourceType().equals(SourceType.SEQ_NO.value())) {
                //data = identityService.genNextNo(script);
                dataObject.set(fieldName, data);
            }
            // 通过脚本获取值
            else {
                // 行运算
                if (fieldName.matches("^\\w+\\.\\w+\\[i\\]\\.\\w+$")) {
                    columnCalc(fieldName, script, dataObject);
                } else {
                    data = getData(script, dataObject);
                    dataObject.set(fieldName, data);
                }
            }
        }
    }

    // 行运算
    private void columnCalc(String fieldName, String script, IDataObject dataObject) {
        Pattern regex = Pattern.compile("^(\\w+\\.\\w+)\\[i\\]\\.(\\w+)$");
        Matcher regexMatcher = regex.matcher(fieldName);
        if (regexMatcher.matches()) {
            String path = regexMatcher.group(1);
            String field = regexMatcher.group(2);

            List<IDataObject> dataObjects = dataObject.getDataObjects(path);

            if (BeanUtils.isNotEmpty(dataObjects)) {
                //TODO 无限级子对象数据
                for (IDataObject subDataObject : dataObjects) {
                    Object data = getData(script, subDataObject);
                    subDataObject.set(subDataObject.getIboDef().getName() + "." + field, data);
                }
                dataObject.setDataObjects(path, dataObjects);
            }
        }
    }

    /**
     * 根据脚本产生计算结果。
     *
     * <pre>
     * 	脚本如下：
     *  script.sum("{}");
     *  在脚本中获取字段bo如下。
     *  bo.get("字段名");
     * </pre>
     *
     * @param script
     * @param dataObject
     * @return Object
     */
    private Object getData(String script, IDataObject dataObject) {
        Map<String, Object> map = new HashMap<String, Object>();
        StringBuffer resultString = new StringBuffer();
        Pattern regex = Pattern.compile("\\{(.*)\\}");
        Matcher regexMatcher = regex.matcher(script);
        while (regexMatcher.find()) {
            String field = regexMatcher.group(1);
            String mapKey = buildScriptMap(field, dataObject, map);
            if (BeanUtils.isNotEmpty(mapKey)) {
                regexMatcher.appendReplacement(resultString, mapKey);
            }
        }
        regexMatcher.appendTail(resultString);
        Object obj = groovyScriptEngine.executeObject(resultString.toString(), map);
        return obj;
    }

    // 构建groovy脚本执行的上下文
    private String buildScriptMap(String boScript, IDataObject dataObject, Map<String, Object> map) {
        Pattern regex = Pattern.compile("^\\w+\\.\\w+\\[i\\]\\.(\\w+)$");
        Matcher regexMatcher = regex.matcher(boScript);
        // 行运算 a.b[i].c
        while (regexMatcher.matches()) {
            String key = regexMatcher.group(1);
            Object obj = dataObject.get(key);
            map.put(BpmConstants.BO_FIELD_PATH + key, obj);
            return BpmConstants.BO_FIELD_PATH + key;
        }
        regex = Pattern.compile("^(\\w+\\.(\\w+))\\[\\]$");
        regexMatcher = regex.matcher(boScript);
        // 取子模型所有记录 a.b[]
        while (regexMatcher.matches()) {
            String path = regexMatcher.group(1);
            String key = regexMatcher.group(2);
            List<IDataObject> dataObjects = dataObject.getDataObjects(path);
            map.put(BpmConstants.BO_FIELD_PATH + key, dataObjects);
            return BpmConstants.BO_FIELD_PATH + key;
        }
        regex = Pattern.compile("^(\\w+\\.\\w+)\\[\\]\\.(\\w+)$");
        regexMatcher = regex.matcher(boScript);
        // 取子模型某列所有记录 a.b[].c
        while (regexMatcher.matches()) {
            String path = regexMatcher.group(1);
            String key = regexMatcher.group(2);
            List<Object> list = new ArrayList<Object>();
            List<IDataObject> dataObjects = dataObject.getDataObjects(path);
            for (IDataObject subDataObject : dataObjects) {
                Object obj = subDataObject.get(key);
                list.add(obj);
            }
            map.put(BpmConstants.BO_FIELD_PATH + key, list);
            return BpmConstants.BO_FIELD_PATH + key;
        }
        regex = Pattern.compile("^\\w+\\.(\\w+)$");
        regexMatcher = regex.matcher(boScript);
        // 取主模型字段 a.b
        while (regexMatcher.matches()) {
            String key = regexMatcher.group(1);
            Object obj = dataObject.get(key);
            map.put(BpmConstants.BO_FIELD_PATH + key, obj);
            return BpmConstants.BO_FIELD_PATH + key;
        }
        return null;
    }

    @Override
    public void handShowData(IBpmProcInst instance, String nodeId, IDataObject dataObject) {
        FormInitItem formInitItem = getFormInitItem(instance, nodeId);
        List<FieldInitSetting> fieldInitSettings = getFieldSetting(formInitItem, true);
        if (fieldInitSettings == null) {
            return;
        }

        setDataObject(fieldInitSettings, dataObject);
    }

    private FormInitItem getFormInitItem(IBpmProcInst instance, String nodeId) {
        FormInitItem formInitItem = null;
        IBpmNodeDefine bpmNodeDef = bpmDefineReader.getNode(instance.getProcDefId(), nodeId);
        if (!StringValidator.isZeroEmpty(instance.getParentInstId())) {
            IBpmProcInst parentInst = bpmInstRepository.get(instance.getParentInstId());
            String defKey = parentInst.getProcDefKey();
            formInitItem = bpmNodeDef.getFormInitItemByParentKey(defKey);
            if (formInitItem != null) {
                return formInitItem;
            }
        }
        return bpmNodeDef.getFormInitItem();
    }

    private FormInitItem getFormInitItem(String defId) {
        IBpmNodeDefine bpmNodeDef = bpmDefineReader.getStartEvent(defId);
        return bpmNodeDef.getFormInitItem();
    }

    private List<FieldInitSetting> getFieldSetting(FormInitItem formInitItem, boolean isShow) {
        if (formInitItem == null) {
            return null;
        }

        if (isShow) {
            return formInitItem.getShowFieldsSetting();
        } else {
            return formInitItem.getSaveFieldsSetting();
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public IDataObject getByBoCodeFromContext(String instanceId, String boCode) {
        ActionCmd cmd = ContextThreadUtil.getActionCmd(instanceId);
        //当获取cmd为空时，则通过实例id获取表单数据
        if (BeanUtils.isEmpty(cmd)) {
            //通过实例id获取相关信息
            IBpmProcInst bpmProcessInstance = bpmInstService.getProcInst(instanceId);
            //IDataObject	dataObject = bpmBoService.getDataByInst(bpmProcessInstance);
            //return dataObject;
        }
        Map<String, IDataObject> boMap = (Map<String, IDataObject>) cmd.getTransitVars(BpmConstants.BO_INST);
        if (boMap == null) {
            return null;
        }
        return boMap.get(boCode);
    }
}
