package com.huazheng.bpm.plugin;

import org.w3c.dom.Element;

import java.io.Serializable;

/**
 * 插件上下文
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月8日-下午3:41:27
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface IPluginContext extends Serializable {

    String PLUGIN_CONTEXT = "PluginContext";

    /**
     * 插件运行时的Class
     *
     * @return String
     * @throws
     * @since 1.0.0
     */
    Class<? extends IRuntimePlugin<?, ?, ?>> getPluginClass();

    /**
     * 返回流程插件定义
     *
     * @return IBpmPluginDefine
     * @throws
     * @since 1.0.0
     */
    IBpmPluginDefine getBpmPluginDefine();

    /**
     * 根据XML及XSD解析实现类实例
     *
     * @return IBpmPluginDefine
     * @throws
     * @since 1.0.0
     */
    IBpmPluginDefine parse(Element element);

    /**
     * 获取插件标题。
     *
     * @return String
     */
    String getTitle();

}
