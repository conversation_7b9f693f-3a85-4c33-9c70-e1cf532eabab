package com.huazheng.bpm.entity.constant;

/**
 * 抄送类型。
 *
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：simon cai
 * 邮箱:<EMAIL>
 * 日期:2015-4-1-下午5:54:18
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum CopyToType {
    COPYTO("copyTo", "抄送"),
    TRANS("trans", "流转");

    // 键
    private String key = "";
    // 值
    private String value = "";

    // 构造方法
    CopyToType(String key, String value) {
        this.key = key;
        this.value = value;
    }

    // =====getting and setting=====
    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return key;
    }

    /**
     * 通过key获取对象
     *
     * @param key
     * @return
     */
    public static CopyToType from<PERSON>ey(String key) {
        for (CopyToType c : CopyToType.values()) {
            if (c.getKey().equalsIgnoreCase(key)) {
                return c;
            }
        }
        throw new IllegalArgumentException(key);
    }
}
