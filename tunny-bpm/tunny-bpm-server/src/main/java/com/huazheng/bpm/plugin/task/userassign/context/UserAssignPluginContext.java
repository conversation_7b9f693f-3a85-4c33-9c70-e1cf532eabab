package com.huazheng.bpm.plugin.task.userassign.context;

import com.huazheng.bpm.entity.constant.BpmConstants;
import com.huazheng.bpm.entity.constant.EventType;
import com.huazheng.bpm.model.node.UserAssignRule;
import com.huazheng.bpm.plugin.*;
import com.huazheng.bpm.plugin.task.userassign.def.UserAssignPluginDefine;
import com.huazheng.bpm.plugin.task.userassign.plugin.UserAssignPlugin;
import com.huazheng.bpm.plugin.task.userassign.plugin.UserQueryPlugin;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.helper.UserAssignRuleParser;
import com.huazheng.bpm.util.string.StringUtil;
import com.jamesmurty.utils.XMLBuilder;
import net.sf.json.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.w3c.dom.Element;

import java.util.ArrayList;
import java.util.List;

/**
 * 流程用户解析插件
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月14日-上午8:45:54
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
@Service("userAssignPluginContext")
@Scope("prototype")
public class UserAssignPluginContext extends AbstractBpmTaskPluginContext implements IBpmTaskPluginContext, IUserQueryPluginContext {

    /* (non-Javadoc)
     * @see com.lc.ibps.bpmn.api.plugin.context.IPluginContext#getPluginClass()
     */
    @Override
    public Class<? extends IRuntimePlugin<?, ?, ?>> getPluginClass() {
        return UserAssignPlugin.class;
    }

    @Override
    public Class<? extends IRuntimePlugin<?, ?, ?>> getUserQueryPluginClass() {
        return UserQueryPlugin.class;
    }

    @Override
    public List<EventType> getEventTypeList() {
        List<EventType> eventTypes = new ArrayList<EventType>();
        eventTypes.add(EventType.TASK_CREATE_EVENT);
        //eventTypes.add(EventType.TASK_SIGN_CREATE_EVENT);// TODO 会签创建时事件
        return eventTypes;
    }

    @Override
    public String getPluginXml() {
        UserAssignPluginDefine def = (UserAssignPluginDefine) getBpmPluginDefine();
        if (def.getRuleList().size() == 0) {
            return "";
        }
        try {
            XMLBuilder xmlBuilder = XMLBuilder.create("userAssign")
                    .a("xmlns", "http://www.bpmhome.cn/bpm/plugins/task/userAssign");

            UserAssignRuleParser.handXmlBulider(xmlBuilder, def.getRuleList());

            return xmlBuilder.asString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 获取插件的JSON数据。
     * [
     * {"calcs":[
     * {"extractType":"no","groupKeys":"zhu","groupNames":"广州","groupType":"org","logicType":"or","pluginName":"","pluginType":"group"},
     * {"extractType":"no","logicType":"or","pluginName":"","script":"return false","pluginType":"hrScript"}
     * ],
     * "condition":"total>1","conditionMode":"1","description":"","groupNo":1,"name":""}
     * ]
     */
    @Override
    public String getJson() {
        return getJsonByParentFlowKey(BpmConstants.LOCAL);
    }

    /***
     *  通过 flowKey 获取指定类型的用户抽取列表
     * @param FlowKey
     * @return
     */
    public String getJsonByParentFlowKey(String flowKey) {
        if (StringUtil.isEmpty(flowKey)) {
            flowKey = BpmConstants.LOCAL;
        }

        List<UserAssignRule> ruleList = ((UserAssignPluginDefine) this.getBpmPluginDefine()).getRuleList();
        if (BeanUtils.isEmpty(ruleList)) {
            return "[]";
        }

        List<UserAssignRule> rules = new ArrayList<UserAssignRule>();
        for (UserAssignRule rule : ruleList) {
            if (StringUtil.isEmpty(rule.getParentFlowKey())) {
                rule.setParentFlowKey(BpmConstants.LOCAL); //如果为空，改为local_
            }

            if (rule.getParentFlowKey().equals(flowKey)) {
                rules.add(rule);
            }
        }
        if (rules.size() == 0) {
            return "[]";
        }

        JsonConfig config = new JsonConfig();
        UserAssignRuleParser.handJsonConfig(config, rules);
        JSON json = JSONSerializer.toJSON(rules, config);

        return json.toString();
    }

    @Override
    protected IBpmPluginDefine parseElement(Element element) {
        UserAssignPluginDefine userAssignPluginDef = new UserAssignPluginDefine();
        List<UserAssignRule> userAssignRules = UserAssignRuleParser.parse(element);
        userAssignPluginDef.setRuleList(userAssignRules);
        return userAssignPluginDef;
    }

    /**
     * 根据JSON 解析插件定义。
     */
    @Override
    public IBpmPluginDefine parseJson(String pluginJson) {
        UserAssignPluginDefine def = new UserAssignPluginDefine();
        if (StringUtil.isEmpty(pluginJson)) {
            return def;
        }
        JSONArray jsonArray = JSONArray.fromObject(pluginJson);
        List<UserAssignRule> ruleList = new ArrayList<UserAssignRule>();
        for (Object obj : jsonArray) {
            JSONObject jsonObj = (JSONObject) obj;
            UserAssignRule rule = UserAssignRuleParser.getUserAssignRule(jsonObj);
            ruleList.add(rule);
        }
        def.setRuleList(ruleList);
        return def;
    }

    @Override
    public String getTitle() {
        return "用户分配插件";
    }

}
