package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmApproveDao;
import com.huazheng.bpm.dao.bpm.BpmApproveQueryDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmApprovePo;
import com.huazheng.bpm.model.base.ApprovalVo;
import com.huazheng.bpm.util.core.BeanUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 流程审批意见领域对象实体
 *
 * <pre>
 *
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：luodx
 * 邮箱地址：<EMAIL>
 * 创建时间：2016-12-12 15:53:51
 * </pre>
 */
@SuppressWarnings("serial")
@Transactional
@Scope("prototype")
@Service
public class BpmApprove extends AbstractDomain<String, BpmApprovePo> {
    @Resource
    private BpmApproveDao bpmApproveDao;
    @Resource
    private BpmApproveQueryDao bpmApproveQueryDao;

    @Override
    protected void init() {
        this.setDao(bpmApproveDao);
    }

    /**
     * 根据流程实例ID获取相关正在运行的流程实例，添加审批意见历史表
     *
     * @param instId 流程实例ID
     */
    public void archiveHistory(String instId) {
        bpmApproveDao.archiveHistory(instId);
    }

    /**
     * 根据正在运行实例id删除对应的审批意见信息
     *
     * @param instId 流程实例ID
     */
    public void delByInstId(String instId) {
        bpmApproveDao.delByInstId(instId);
    }

    /**
     * 根据正在运行实例ID数组批量删除审批意见数据
     *
     * @param instList 程实例ID数组
     */
    public void delByInstList(List<String> instList) {
        bpmApproveDao.delByInstList(instList);
    }

    /**
     * 更新待审批节点状态。
     *
     * @param procInstId 实例ID
     * @param nodeId     节点ID
     * @param status     节点审批状态：同意，反对等
     */
    public void updStatusByWait(String procInstId, String nodeId, String status, String auditor) {
        bpmApproveDao.updStatusByWait(procInstId, nodeId, status, auditor);
    }

    /**
     * 根据审批vo对象更新审批意见数据
     *
     * @param vo
     */
    public void updateByVo(ApprovalVo vo) {
        //获取实例ID
        String instId = vo.getInstId();
        //获取是否已有审批意见数据
        BpmApprovePo po = bpmApproveQueryDao.getByInstNodeId(instId, vo.getNodeId(), vo.getTaskId());
        // 更新记录
        if (BeanUtils.isNotEmpty(po)) {
            po.setStatus(vo.getNodeStatus().getKey());
            bpmApproveDao.update(po);
        }
    }

}
