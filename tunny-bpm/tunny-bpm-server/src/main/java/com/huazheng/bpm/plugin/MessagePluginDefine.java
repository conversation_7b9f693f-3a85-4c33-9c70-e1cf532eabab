package com.huazheng.bpm.plugin;


import com.huazheng.bpm.model.node.UserAssignRule;

import java.util.ArrayList;
import java.util.List;

/**
 * TODO（用一句话描述这个类做什么用）。
 *
 * <pre>
 *
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午7:28:19
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
public class MessagePluginDefine extends AbstractBpmExecutionPluginDefine {

    /**
     * 外部class。
     */
    private String externalClass = "";

    /**
     * 消息类型
     */
    private String notifyType = "";

    /**
     * 标题
     */
    private String subject = "";
    /**
     * 模版内容。
     */
    private String html = "";

    /**
     * 普通文本
     */
    private String plainText = "";

    /**
     * 用户规则列表。
     */
    private List<UserAssignRule> users = new ArrayList<UserAssignRule>();

    public String getExternalClass() {
        return externalClass;
    }

    public void setExternalClass(String externalClass) {
        this.externalClass = externalClass;
    }

    public String getNotifyType() {
        return notifyType;
    }

    public void setNotifyType(String notifyType) {
        this.notifyType = notifyType;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getHtml() {
        return html;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    public String getPlainText() {
        return plainText;
    }

    public void setPlainText(String plainText) {
        this.plainText = plainText;
    }

    public List<UserAssignRule> getUsers() {
        return users;
    }

    public void setUsers(List<UserAssignRule> users) {
        this.users = users;
    }

}
