package com.huazheng.bpm.domain.right;

import com.huazheng.bpm.dao.right.RightsConfigDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.rights.RightsConfigPo;
import org.springframework.context.annotation.Scope;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@SuppressWarnings("serial")
@Transactional
@Scope("prototype")
public class RightsConfig extends AbstractDomain<String, RightsConfigPo> {
    @Resource
    private RightsConfigDao rightsConfigDao;

    @Override
    protected void init() {
        this.setDao(rightsConfigDao);
    }

}
