package com.huazheng.bpm.entity.constant;

/**
 * 消息模版分类，详细
 *
 * <pre>
 * 构建组：ibps-common-biz
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年2月25日-上午9:47:31
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum TemplateType {

    TASK_CREATE("taskCreate", "任务创建通知"),
    TASK_BACK("taskBack", "任务驳回通知"),
    TASK_COMPLETE("taskComplete", "任务完成通知"),
    PROCESS_END("processEnd", "流程结束通知"),
    BPMN_APPROVAL("bpmnApproval", "审批提醒"),
    BPMN_BACK("bpmnBack", "驳回提醒"),
    BPMN_RECOVER("bpmnRecover", "撤销提醒"),
    BPMN_AGENT("bpmnAgent", "代理任务审批"),
    BPMN_DELEGATE("bpmnDelegate", "通知被代理人"),
    BPMN_TASK_TRANS("bpmnTaskTrans", "任务流转任务"),
    BPM_TRANS_FEEDBACK("bpmTransFeedBack", "流转通知任务反馈意见"),
    BPM_TRANS_CANCEL("bpmTransCancel", "流转任务取消"),
    BPM_COMMU_SEND("bpmCommuSend", "沟通通知"),
    BPM_COMMU_FEEDBACK("bpmCommuFeedBack", "沟通反馈通知"),
    BPM_HAND_TO("bpmHandTo", "转交通知"),
    BPM_ADD_SIGN_TASK("addSignTask", "加签通知"),
    BPM_ENDPROCESS("bpmEndProcess", "终止流程"),
    BPM_TASK_CANCEL("bpmTaskCancel", "任务取消");

    //key
    private String key;
    //显示名称
    private String label;


    TemplateType(String key, String label) {
        this.key = key;
        this.label = label;
    }

    public String getKey() {
        return key;
    }

    public String getLabel() {
        return label;
    }
}
