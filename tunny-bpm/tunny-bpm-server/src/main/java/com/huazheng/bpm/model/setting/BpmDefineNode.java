package com.huazheng.bpm.model.setting;


import com.huazheng.bpm.model.define.IBpmVariableDefine;
import com.huazheng.bpm.model.define.UserScript;
import com.huazheng.bpm.model.node.*;

import java.util.List;
import java.util.Map;

public class BpmDefineNode {
    // 节点Id
    private String id;
    // 节点名称
    private String nodeName;
    // 节点类型
    private String nodeType;
    // 父类Id
    private String parentId;
    // 表单
    private IExtForm form;
    // 脚本
    private List<UserScript> scripts;
    // 按钮
    private List<Button> buttons;
    // 用户
    private String users;
    // 跳转规则
    private List<IJumpRule> jumpRules;
    // 其他参数
    private List<Attribute> attributes;
    //流程变量
    private List<IBpmVariableDefine> variables;
    // 催办
    private List<IReminderDef> reminders;
    // 触发流程
    private List<ITrigerFlow> trigerFlows;
    // 会签规则
    private SignRule signRule;
    // 特权
    private List<PrivilegeItem> privileges;
    // 分支条件
    private Map<String, String> conditions;
    // 外部子流程
    private CallActivityNodeDefine callActivity;
    // 抄送
    private String procNotify;
    //插件JSON（必须包含“pluginType”）
    private String pluginJson;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }


    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public IExtForm getForm() {
        return form;
    }

    public void setForm(IExtForm form) {
        this.form = form;
    }

    public List<UserScript> getScripts() {
        return scripts;
    }

    public void setScripts(List<UserScript> scripts) {
        this.scripts = scripts;
    }

    public List<Button> getButtons() {
        return buttons;
    }

    public void setButtons(List<Button> buttons) {
        this.buttons = buttons;
    }

    public String getUsers() {
        return users;
    }

    public void setUsers(String users) {
        this.users = users;
    }

    public List<IJumpRule> getJumpRules() {
        return jumpRules;
    }

    public void setJumpRules(List<IJumpRule> jumpRules) {
        this.jumpRules = jumpRules;
    }

    public List<Attribute> getAttributes() {
        return attributes;
    }

    public void setAttributes(List<Attribute> attributes) {
        this.attributes = attributes;
    }

    public SignRule getSignRule() {
        return signRule;
    }

    public void setSignRule(SignRule signRule) {
        this.signRule = signRule;
    }

    public List<PrivilegeItem> getPrivileges() {
        return privileges;
    }

    public void setPrivileges(List<PrivilegeItem> privileges) {
        this.privileges = privileges;
    }

    public Map<String, String> getConditions() {
        return conditions;
    }

    public void setConditions(Map<String, String> conditions) {
        this.conditions = conditions;
    }


    public List<IReminderDef> getReminders() {
        return reminders;
    }

    public void setReminders(List<IReminderDef> reminders) {
        this.reminders = reminders;
    }

    public List<ITrigerFlow> getTrigerFlows() {
        return trigerFlows;
    }

    public void setTrigerFlows(List<ITrigerFlow> trigerFlows) {
        this.trigerFlows = trigerFlows;
    }

    public CallActivityNodeDefine getCallActivity() {
        return callActivity;
    }

    public void setCallActivity(CallActivityNodeDefine callActivity) {
        this.callActivity = callActivity;
    }

    public String getProcNotify() {
        return procNotify;
    }

    public void setProcNotify(String procNotify) {
        this.procNotify = procNotify;
    }

    public String getPluginJson() {
        return pluginJson;
    }

    public void setPluginJson(String pluginJson) {
        this.pluginJson = pluginJson;
    }

    public List<IBpmVariableDefine> getVariables() {
        return variables;
    }

    public void setVariables(List<IBpmVariableDefine> variables) {
        this.variables = variables;
    }

}
