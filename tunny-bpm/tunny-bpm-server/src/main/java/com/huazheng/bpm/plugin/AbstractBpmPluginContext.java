package com.huazheng.bpm.plugin;

import com.huazheng.bpm.util.string.StringUtil;
import org.w3c.dom.Element;

/**
 * 插件抽象类
 * <pre>
 * 构建组：ibps-bpmn-plugin-core
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月10日-下午4:02:52
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
public abstract class AbstractBpmPluginContext implements IBpmPluginContext, IPluginParser {

    private IBpmPluginDefine bpmPluginDefine;

    @Override
    public IBpmPluginDefine getBpmPluginDefine() {
        return bpmPluginDefine;
    }

    public void setBpmPluginDefine(IBpmPluginDefine bpmPluginDefine) {
        this.bpmPluginDefine = bpmPluginDefine;
    }

    protected abstract IBpmPluginDefine parseJson(String pluginJson);

    protected abstract IBpmPluginDefine parseElement(Element element);

    /**
     * 父类实现将解析的插件定义设置到前实例。
     */
    public IBpmPluginDefine parse(Element element) {
        IBpmPluginDefine pluginDefine = parseElement(element);
        this.setBpmPluginDefine(pluginDefine);

        return pluginDefine;
    }

    @Override
    public void parse(String pluginDefJson) {
        IBpmPluginDefine bpmPluginDefine = parseJson(pluginDefJson);
        setBpmPluginDefine(bpmPluginDefine);
    }

    @Override
    public String getType() {
        return StringUtil.lowerFirst(this.getClass().getSimpleName().replaceAll(IBpmPluginContext.PLUGIN_CONTEXT, ""));
    }

}
