package com.huazheng.bpm.plugin;


import com.huazheng.bpm.service.ITaskActionHandlerDefine;

import java.util.Map;

/**
 * 任务操作句柄上下文对象
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月26日-下午4:10:44
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface ITaskActionHandlerContext {

    /**
     * 获得任务操作句柄实例的Map集合
     *
     * @return Map<String, TaskActionHandler>
     */
    Map<String, String> getTaskActionHandlers();

    /**
     * 获得任务操作定义实例的Map集合
     *
     * @return Map<String, TaskActionHandlerDef>
     */
    Map<String, ITaskActionHandlerDefine> getTaskActionHandlerDefs();
}
