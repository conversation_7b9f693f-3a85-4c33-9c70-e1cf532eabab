package com.huazheng.bpm.entity.jms;

import java.io.Serializable;

/**
 * 状态编码。
 *
 * <pre>
 *
 * 构建组：ibps-api-base
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年2月16日-下午10:40:29
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
public class BaseAPIResult implements Serializable {
    /**
     * 状态码
     */
    private String statusCode;
    /**
     * 返回信息
     */
    private String msg;

    public BaseAPIResult() {
        this.statusCode = "";
        this.msg = "";
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

}
