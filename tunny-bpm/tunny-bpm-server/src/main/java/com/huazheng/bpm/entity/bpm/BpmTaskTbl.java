package com.huazheng.bpm.entity.bpm;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huazheng.bpm.entity.framework.AbstractPo;
import com.huazheng.bpm.util.core.StringPool;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 流程任务
 * 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：simon cai
 * 邮箱地址：<EMAIL>
 * 创建时间：2016-12-14 15:30:23
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmTaskTbl extends AbstractPo<String> {
    protected String id;        /*任务ID*/
    @NotBlank(message = "com.huazheng.bpm.entity.bpm.BpmTaskTbl.name")
    protected String name;        /*任务名称*/
    @NotBlank(message = "com.huazheng.bpm.entity.bpm.BpmTaskTbl.subject")
    protected String subject;        /*待办事项标题*/
    @NotBlank(message = "com.huazheng.bpm.entity.bpm.BpmTaskTbl.procInstId")
    protected String procInstId;        /*关联 - 流程实例ID*/
    @NotBlank(message = "com.huazheng.bpm.entity.bpm.BpmTaskTbl.taskId")
    protected String taskId;        /*关联的任务ID*/
    @NotBlank(message = "com.huazheng.bpm.entity.bpm.BpmTaskTbl.execId")
    protected String execId;        /*关联 - 节点执行ID*/
    @NotBlank(message = "com.huazheng.bpm.entity.bpm.BpmTaskTbl.nodeId")
    protected String nodeId;        /*关联 - 任务节点ID*/
    @NotBlank(message = "com.lc.ibps.bpmn.provider.public.procDefId")
    protected String procDefId;        /*关联 - 流程定义ID*/
    @NotBlank(message = "com.huazheng.bpm.entity.bpm.BpmTaskTbl.procDefKey")
    protected String procDefKey;        /*关联 - 流程业务主键*/
    @NotBlank(message = "com.huazheng.bpm.entity.bpm.BpmTaskTbl.procDefName")
    protected String procDefName;        /*关联 - 流程名称*/
    protected String status;        /*任务状态*/
    protected Integer priority;        /*任务优先级*/
    @JsonFormat(pattern = StringPool.DATE_FORMAT_DATETIME)
    protected java.util.Date createTime;        /*任务创建时间*/
    @JsonFormat(pattern = StringPool.DATE_FORMAT_DATETIME)
    protected java.util.Date dueTime;        /*任务到期时间*/
    protected Integer suspendState = 1;        /*是否挂起(1正常,2挂起)*/
    protected String parentId;        /*父任务ID*/
    @NotBlank(message = "com.huazheng.bpm.entity.bpm.BpmTaskTbl.bpmnInstId")
    protected String bpmnInstId;        /*BPMN流程实例ID*/
    @NotBlank(message = "com.huazheng.bpm.entity.bpm.BpmTaskTbl.bpmnDefId")
    protected String bpmnDefId;        /*BPMN流程定义ID*/
    protected String typeId;        /*分类ID*/
    protected Integer lockState = 0;        /*是否锁定(0正常,1锁定)*/
    protected String lockUser = "0";        /*任务执行人ID*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 任务ID
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 返回 任务名称
     *
     * @return
     */
    public String getName() {
        return this.name;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    /**
     * 返回 待办事项标题
     *
     * @return
     */
    public String getSubject() {
        return this.subject;
    }

    public void setProcInstId(String procInstId) {
        this.procInstId = procInstId;
    }

    /**
     * 返回 关联 - 流程实例ID
     *
     * @return
     */
    public String getProcInstId() {
        return this.procInstId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    /**
     * 返回 关联的任务ID
     *
     * @return
     */
    public String getTaskId() {
        return this.taskId;
    }

    public void setExecId(String execId) {
        this.execId = execId;
    }

    /**
     * 返回 关联 - 节点执行ID
     *
     * @return
     */
    public String getExecId() {
        return this.execId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    /**
     * 返回 关联 - 任务节点ID
     *
     * @return
     */
    public String getNodeId() {
        return this.nodeId;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    /**
     * 返回 关联 - 流程定义ID
     *
     * @return
     */
    public String getProcDefId() {
        return this.procDefId;
    }

    public void setProcDefKey(String procDefKey) {
        this.procDefKey = procDefKey;
    }

    /**
     * 返回 关联 - 流程业务主键
     *
     * @return
     */
    public String getProcDefKey() {
        return this.procDefKey;
    }

    public void setProcDefName(String procDefName) {
        this.procDefName = procDefName;
    }

    /**
     * 返回 关联 - 流程名称
     *
     * @return
     */
    public String getProcDefName() {
        return this.procDefName;
    }

    public String getLockUser() {
        return lockUser;
    }

    public void setLockUser(String lockUser) {
        this.lockUser = lockUser;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 返回 任务状态
     *
     * @return
     */
    public String getStatus() {
        return this.status;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    /**
     * 返回 任务优先级
     *
     * @return
     */
    public Integer getPriority() {
        return this.priority;
    }

    public void setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 返回 任务创建时间
     *
     * @return
     */
    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public void setDueTime(java.util.Date dueTime) {
        this.dueTime = dueTime;
    }

    /**
     * 返回 任务到期时间
     *
     * @return
     */
    public java.util.Date getDueTime() {
        return this.dueTime;
    }

    public void setSuspendState(Integer suspendState) {
        this.suspendState = suspendState;
    }

    /**
     * 返回 是否挂起(0正常,1挂起)
     *
     * @return
     */
    public Integer getSuspendState() {
        return this.suspendState;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    /**
     * 返回 父任务ID
     *
     * @return
     */
    public String getParentId() {
        return this.parentId;
    }

    public void setBpmnInstId(String bpmnInstId) {
        this.bpmnInstId = bpmnInstId;
    }

    /**
     * 返回 BPMN流程实例ID
     *
     * @return
     */
    public String getBpmnInstId() {
        return this.bpmnInstId;
    }

    public void setBpmnDefId(String bpmnDefId) {
        this.bpmnDefId = bpmnDefId;
    }

    /**
     * 返回 BPMN流程定义ID
     *
     * @return
     */
    public String getBpmnDefId() {
        return this.bpmnDefId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    /**
     * 返回 分类ID
     *
     * @return
     */
    public String getTypeId() {
        return this.typeId;
    }

    public void setLockState(Integer lockState) {
        this.lockState = lockState;
    }

    /**
     * 返回 是否锁定(0正常,1锁定)
     *
     * @return
     */
    public Integer getLockState() {
        return this.lockState;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("name", this.name)
                .append("subject", this.subject)
                .append("procInstId", this.procInstId)
                .append("taskId", this.taskId)
                .append("execId", this.execId)
                .append("nodeId", this.nodeId)
                .append("procDefId", this.procDefId)
                .append("procDefKey", this.procDefKey)
                .append("procDefName", this.procDefName)
                .append("status", this.status)
                .append("priority", this.priority)
                .append("createTime", this.createTime)
                .append("dueTime", this.dueTime)
                .append("suspendState", this.suspendState)
                .append("parentId", this.parentId)
                .append("bpmnInstId", this.bpmnInstId)
                .append("bpmnDefId", this.bpmnDefId)
                .append("typeId", this.typeId)
                .append("lockState", this.lockState)
                .append("lockUser", this.lockUser)
                .toString();
    }
}
