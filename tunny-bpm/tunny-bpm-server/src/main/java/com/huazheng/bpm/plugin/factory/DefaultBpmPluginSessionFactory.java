package com.huazheng.bpm.plugin.factory;


import com.huazheng.bpm.model.base.IDataObject;
import com.huazheng.bpm.model.delegate.BpmDelegateExecution;
import com.huazheng.bpm.model.delegate.BpmDelegateTask;
import com.huazheng.bpm.plugin.*;
import com.huazheng.bpm.util.cmd.ProcInstCmd;
import com.huazheng.bpm.util.cmd.TaskFinishCmd;
import com.huazheng.bpm.util.session.*;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service("bpmPluginSessionFactory")
public class DefaultBpmPluginSessionFactory implements BpmPluginSessionFactory {

    @Override
    public BpmTaskPluginSession buildBpmTaskPluginSession(
            BpmDelegateTask bpmDelegateTask) {
        DefaultBpmTaskPluginSession bpmTaskPluginSession = new DefaultBpmTaskPluginSession();
        bpmTaskPluginSession.setBpmDelegateTask(bpmDelegateTask);
        return bpmTaskPluginSession;
    }

    @Override
    public BpmExecutionPluginSession buildBpmExecutionPluginSession(
            BpmDelegateExecution bpmDelegateExecution) {
        DefaultBpmExecutionPluginSession bpmExecutionPluginSession = new DefaultBpmExecutionPluginSession();
        bpmExecutionPluginSession.setBpmDelegateExecution(bpmDelegateExecution);
        return bpmExecutionPluginSession;
    }

    @Override
    public BpmUserCalcPluginSession buildBpmUserCalcPluginSession(Map<String, Object> variables) {
        DefaultBpmUserCalcPluginSession defaultBpmUserCalcPluginSession = new DefaultBpmUserCalcPluginSession();
        defaultBpmUserCalcPluginSession.setVariables(variables);
        return defaultBpmUserCalcPluginSession;
    }

    @Override
    public BpmUserCalcPluginSession buildBpmUserCalcPluginSession(
            BpmDelegateTask bpmDelegateTask) {
        DefaultBpmUserCalcPluginSession defaultBpmUserCalcPluginSession = new DefaultBpmUserCalcPluginSession();
        defaultBpmUserCalcPluginSession.setVariables(bpmDelegateTask.getVariables());
        defaultBpmUserCalcPluginSession.setBpmDelegateTask(bpmDelegateTask);
        return defaultBpmUserCalcPluginSession;
    }

    @Override
    public ProcessInstAopPluginSession buildProcessInstAopPluginSession(
            ProcInstCmd procInstCmd) {
        DefaultProcessInstAopPluginSession processInstAopPluginSession = new DefaultProcessInstAopPluginSession();
        processInstAopPluginSession.setProcessInstCmd(procInstCmd);
        return processInstAopPluginSession;
    }

    @Override
    public TaskAopPluginSession buildTaskAopPluginSession(
            TaskFinishCmd taskFinishCmd) {
        DefaultTaskAopPluginSession taskAopPluginSession = new DefaultTaskAopPluginSession();
        taskAopPluginSession.setTaskFinishCmd(taskFinishCmd);
        return taskAopPluginSession;
    }

    @Override
    public TaskActionPluginSession buildTaskActionPluginSession(BpmDelegateTask bpmDelegateTask,
                                                                TaskFinishCmd taskFinishCmd) {
        DefaultTaskActionPluginSession taskActionPluginSession = new DefaultTaskActionPluginSession();
        taskActionPluginSession.setTaskFinishCmd(taskFinishCmd);
        taskActionPluginSession.setBpmDelegateTask(bpmDelegateTask);
        return taskActionPluginSession;
    }

    @Override
    public ExecutionActionPluginSession buildExecutionActionPluginSession(
            BpmDelegateExecution bpmDelegateExecution,
            TaskFinishCmd taskFinishCmd) {
        DefaultExecutionActionPluginSession pluginSession = new DefaultExecutionActionPluginSession();
        pluginSession.setBpmDelegateExecution(bpmDelegateExecution);
        pluginSession.setTaskFinishCmd(taskFinishCmd);
        return pluginSession;
    }

    @Override
    public BpmUserCalcPluginSession buildBpmUserCalcPluginSession(
            Map<String, Object> variables, IDataObject dataObject) {
        DefaultBpmUserCalcPluginSession defaultBpmUserCalcPluginSession = new DefaultBpmUserCalcPluginSession();
        defaultBpmUserCalcPluginSession.setVariables(variables);
        defaultBpmUserCalcPluginSession.setDataObject(dataObject);
        return defaultBpmUserCalcPluginSession;
    }

}
