//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vJAXB 2.1.3 in JDK 1.6
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2014.05.06 at 03:52:27 下午 CST
//


package com.huazheng.bpm.entity.procnotify;


import javax.xml.bind.annotation.*;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{http://www.bpmhome.cn/bpm/plugins/task/baseNotify}notify" maxOccurs="unbounded"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "notify"
})
@XmlRootElement(name = "onEnd")
public class OnEnd {

    @XmlElement(namespace = "http://www.bpmhome.cn/bpm/plugins/task/baseNotify", required = true)
    protected List<Notify> notify;

    /**
     * Gets the value of the notify property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the notify property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getNotify().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Notify }
     */
    public List<Notify> getNotify() {
        if (notify == null) {
            notify = new ArrayList<Notify>();
        }
        return this.notify;
    }

}
