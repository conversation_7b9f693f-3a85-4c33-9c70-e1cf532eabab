package com.huazheng.bpm.entity.bpm;

import java.util.ArrayList;
import java.util.List;

/**
 * 流程任务变更 实体对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-04-11 19:45:10
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmTaskChangePo extends BpmTaskChangeTbl {
    private boolean delBeforeSave = true;

    public boolean isDelBeforeSave() {
        return delBeforeSave;
    }

    public void setDelBeforeSave(boolean delBeforeSave) {
        this.delBeforeSave = delBeforeSave;
    }

    private List<BpmTaskChangeAssignPo> bpmTaskChangeAssignPoList = new ArrayList<BpmTaskChangeAssignPo>();

    public List<BpmTaskChangeAssignPo> getBpmTaskChangeAssignPoList() {
        return bpmTaskChangeAssignPoList;
    }

    public void setBpmTaskChangeAssignPoList(List<BpmTaskChangeAssignPo> bpmTaskChangeAssignPoList) {
        this.bpmTaskChangeAssignPoList = bpmTaskChangeAssignPoList;
    }

    public static final String CHANGE_ASSIGNEE = "assignee";/*代理*/
    public static final String CHANGE_SHIFT = "shift";/*转办*/

    public static final String CHANGE_STATUS_RUNNING = "running";/*运行中*/
    public static final String CHANGE_STATUS_FINISH = "finish";/*完成*/
    public static final String CHANGE_STATUS_CANCEL = "cancel";/*取消*/

    public static final String COMMENT_ASSIGNEE_DEF = "默认代理描述";
    public static final String COMMENT_SHIFT_DEF = "默认转办描述";

    protected String ownerName;        /*所属人名称*/
    protected String executorName;        /*执行人名称*/

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getExecutorName() {
        return executorName;
    }

    public void setExecutorName(String executorName) {
        this.executorName = executorName;
    }

}
