package com.huazheng.bpm.entity.party;

import com.huazheng.bpm.entity.org.PartyEntityTbl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 组织树 实体对象。
 *
 * <pre>
 *
 * 构建组：ibps-org-biz
 * 作者：huangcy
 * 邮箱：<EMAIL>
 * 日期：2015-11-19 14:17:31
 * 版权：广州流辰信息技术有限公司
 * </pre>
 */
@SuppressWarnings("serial")
public class PartyEntityTreePo extends PartyEntityTbl {
    /*更改为系统有的图片*/
    public static final String ICON_COMORG = "/styles/theme/default/images/icons/u_darkblue/u_zzgl_darkblue.png";
    protected Long sn; /* 序号 */
    protected String icon; /* 图标 */
    protected String type;
    protected boolean nocheck = false;
    protected boolean chkDisabled = false;
    protected boolean click = true;
    protected String title = ""; // *title 默认为name 、如果name添加了 css 、则默认为 “” */
    protected String open = "true";/*树默认打开*/

    public PartyEntityTreePo() {
    }

    public PartyEntityTreePo(String name, String id, String parentId, String icon) {
        setName(name);
        this.parentId = parentId;
        this.id = id;
        this.icon = icon;
    }

    /**
     * GroupList2TreeList
     */
    public static List<PartyEntityTreePo> GroupList2TreeList(List<PartyEntityTreePo> groupList, String icon) {
        if (groupList == null || groupList.size() == 0) {
            return Collections.emptyList();
        }

        List<PartyEntityTreePo> groupTreeList = new ArrayList<PartyEntityTreePo>();
        for (PartyEntityTreePo group : groupList) {
            PartyEntityTreePo grouptree = new PartyEntityTreePo(group);
            grouptree.setIcon(icon);
            groupTreeList.add(grouptree);
        }
        return groupTreeList;
    }

    public PartyEntityTreePo(PartyEntityTbl group) {
        this.id = group.id;
        this.name = group.getName();
        this.type = group.partyType;
        this.parentId = group.parentId;
        this.depth = group.depth;
        this.path = group.path;
        this.createBy = group.getCreateBy();
        this.createTime = group.getCreateTime();
        this.updateBy = group.getUpdateBy();
        this.updateTime = group.getUpdateTime();

        if (!this.name.contains("style=")) {
            this.title = name;
        }
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public void setName(String name) {
        this.name = name;
        // 将title 设置成name
        if ("".equals(title) && !this.name.contains("style=")) {
            this.title = name;
        }
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getSn() {
        return sn;
    }

    public void setSn(Long sn) {
        this.sn = sn;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public boolean isNocheck() {
        return nocheck;
    }

    public void setNocheck(boolean nocheck) {
        this.nocheck = nocheck;
    }

    public boolean isChkDisabled() {
        return chkDisabled;
    }

    public boolean isClick() {
        return click;
    }

    public void setClick(boolean click) {
        this.click = click;
    }

    public void setChkDisabled(boolean chkDisabled) {
        this.chkDisabled = chkDisabled;
    }

    /**
     * open
     *
     * @return the open
     */

    public String getOpen() {
        return open;
    }

    /**
     * @param open the open to set
     */
    public void setOpen(String open) {
        this.open = open;
    }


}
