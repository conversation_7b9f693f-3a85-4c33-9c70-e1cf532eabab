package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmExecDao;
import com.huazheng.bpm.dao.bpm.BpmExecSeqDao;
import com.huazheng.bpm.dao.bpm.BpmExecSeqQueryDao;
import com.huazheng.bpm.dao.bpm.BpmInstQueryDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmExecPo;
import com.huazheng.bpm.entity.bpm.BpmExecSeqPo;
import com.huazheng.bpm.entity.bpm.BpmInstPo;
import com.huazheng.bpm.entity.bpm.BpmTaskPo;
import com.huazheng.bpm.entity.constant.BpmConstants;
import com.huazheng.bpm.entity.constant.MultiInstanceType;
import com.huazheng.bpm.entity.constant.NodeType;
import com.huazheng.bpm.model.delegate.BpmDelegateExecution;
import com.huazheng.bpm.model.delegate.BpmDelegateTask;
import com.huazheng.bpm.model.node.IBpmNodeDefine;
import com.huazheng.bpm.model.task.IBpmTask;
import com.huazheng.bpm.repository.BpmExecRepository;
import com.huazheng.bpm.service.IBpmDefineReader;
import com.huazheng.bpm.service.NatTaskService;
import com.huazheng.bpm.util.base.BpmExecUtil;
import com.huazheng.bpm.util.base.ContextThreadUtil;
import com.huazheng.bpm.util.cmd.ActionCmd;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.core.ContextVariableUtil;
import com.huazheng.bpm.util.db.UniqueIdUtil;
import com.huazheng.bpm.util.string.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

/**
 * 流程执行记录 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-01-04 14:29:57
 * </pre>
 */
@SuppressWarnings("serial")
@Service
@Scope("prototype")
public class BpmExec extends AbstractDomain<String, BpmExecPo> {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private IBpmDefineReader bpmDefineReader;
    @Resource
    private BpmExecRepository bpmExecRepository;
    @Resource
    private BpmExecDao bpmExecDao;
    @Resource
    private BpmExecSeqQueryDao bpmExecSeqQueryDao;
    @Resource
    private BpmExecSeqDao bpmExecSeqDao;
    @Resource
    private BpmInstQueryDao bpmInstQueryDao;
    @Resource
    private NatTaskService natTaskService;

    @Override
    protected void init() {
        this.setDao(bpmExecDao);
    }

    /**
     * 记录自动任务执行信息
     * 服务任务、脚本任务、消息任务
     *
     * @param delegateTask
     */
    public void pushAutoTask(BpmDelegateExecution execution) {
        String bpmnInstId = execution.getBpmnInstId();
        BpmInstPo pinst = bpmInstQueryDao.getByInstId(bpmnInstId);
        String procInstId = pinst.getId();
        ActionCmd cmd = ContextThreadUtil.getActionCmd(procInstId);
        IBpmTask bpmTask = (IBpmTask) cmd.getTransitVars(BpmConstants.BPM_TASK);
        BpmDelegateTask delegateTask = natTaskService.getByTaskId(bpmTask.getTaskId());

        BpmExecPo parentExec = null;
        BpmExecPo bpmExec = null;

        // 上一步执行信息ID
        Object pExecIdObject = delegateTask.getVariable(BpmConstants.PARENT_EXEC_ID);
        String parentExecId = pExecIdObject != null ? pExecIdObject.toString() : String.valueOf(0);
        logger.debug("parentExecId is <{}>.", parentExecId);
        parentExec = bpmExecRepository.get(parentExecId);

        MultiInstanceType multiInstType = delegateTask.multiInstanceType();
        Short isMulitiTask = (short) (MultiInstanceType.NO.equals(multiInstType) ? 0 : 1);

        String procDefId = pinst.getProcDefId();
        String nodeId = execution.getNodeId();
        IBpmNodeDefine autoNode = bpmDefineReader.getNode(procDefId, nodeId);

        bpmExec = bpmExecRepository.constructByAutoNode(autoNode, bpmTask, parentExec, "0");
        bpmExec.setIsGatewayMetjoin((short) 0);
        bpmExec.setIsMulitiTask(isMulitiTask);
        bpmExec.setNodeId(nodeId);

        this.create(bpmExec);

        ContextVariableUtil.INSTANCE.setVariable(delegateTask.getExecutionId(), BpmConstants.PARENT_EXEC_ID, bpmExec.getId());
    }

    /**
     * 记录单实例、串行多实例外部子流程结束网关执行信息
     * 并将更新父流程的最新父执行ID
     *
     * @param delegateTask
     */
    public void pushCallActivityEndGateWay(BpmDelegateExecution execution) {
        String bpmnInstId = execution.getBpmnInstId();
        BpmInstPo pinst = bpmInstQueryDao.getByInstId(bpmnInstId);
        String procInstId = pinst.getId();
        ActionCmd cmd = ContextThreadUtil.getActionCmd(procInstId);
        IBpmTask bpmTask = (IBpmTask) cmd.getTransitVars(BpmConstants.BPM_TASK);
        BpmDelegateTask delegateTask = natTaskService.getByTaskId(bpmTask.getTaskId());

        BpmExecPo parentExec = null;
        BpmExecPo gatewayExec = null;

        // 上一步执行信息ID
        Object pExecIdObject = delegateTask.getVariable(BpmConstants.PARENT_EXEC_ID);
        String parentExecId = pExecIdObject != null ? pExecIdObject.toString() : String.valueOf(0);
        logger.debug("parentExecId is <{}>.", parentExecId);
        parentExec = bpmExecRepository.get(parentExecId);

        MultiInstanceType multiInstType = delegateTask.multiInstanceType();
        Short isMulitiTask = (short) (MultiInstanceType.NO.equals(multiInstType) ? 0 : 1);

        String procDefId = pinst.getProcDefId();
        String nodeId = execution.getNodeId();
        IBpmNodeDefine gatewayNode = bpmDefineReader.getNode(procDefId, nodeId);

        gatewayExec = bpmExecRepository.constructByAutoNode(gatewayNode, bpmTask, parentExec, "0");
        gatewayExec.setIsGatewayMetjoin((short) 0);
        gatewayExec.setIsMulitiTask(isMulitiTask);
        gatewayExec.setNodeType(NodeType.SUBENDGATEWAY.getKey());
        gatewayExec.setStartTime(new Date());
        gatewayExec.setEndTime(new Date());
        gatewayExec.setExecStatus(BpmExecPo.EXEC_STATUS_COMPLETE);
        gatewayExec.setNodeId(NodeType.SUBENDGATEWAY.getKey() + "-" + nodeId);

        this.create(gatewayExec);

        ContextVariableUtil.INSTANCE.setVariable(delegateTask.getSupperExecutionId(), BpmConstants.PARENT_EXEC_ID, gatewayExec.getId());
    }

    /**
     * 补签任务记录执行信息
     *
     * @param delegateTask
     */
    public void pushAddSignTask(BpmTaskPo bpmTask, String copyTaskId) {
        BpmExecPo bpmExec = bpmExecRepository.getByTaskId(copyTaskId);
        if (BeanUtils.isEmpty(bpmExec)) {
            return;
        }

        List<BpmExecSeqPo> targetSeqList = bpmExecSeqQueryDao.findByTargetExecId(bpmExec.getId());
        BpmExecSeqPo seq = targetSeqList.get(0);
        seq.setId(null);
        seq.setCreateTime(new Date());
        bpmExec.addExecSeq(seq);

        bpmExec.setId(UniqueIdUtil.getId());
        bpmExec.setTaskId(bpmTask.getId());
        bpmExec.setCreateTime(new Date());

        this.create(bpmExec);
    }

    /**
     * 记录执行信息
     *
     * <pre>
     * 1.普通节点；
     * 2.网关节点：分支网关、同步网关、条件同步网关；
     * 3.会签节点；
     * 4.内部子流程节点；
     * 5.外部子流程节点；
     * </pre>
     *
     * @param delegateTask
     */
    public void push(BpmDelegateTask delegateTask) {
        String procDefId = (String) delegateTask.getVariable(BpmConstants.PROCESS_DEF_ID);
        String procInstId = (String) delegateTask.getVariable(BpmConstants.PROCESS_INST_ID);
        String superProcInstId = (String) delegateTask.getVariable(BpmConstants.PROCESS_PARENT_INST_ID);
        String nodeId = delegateTask.getTaskDefinitionKey();

        // 令牌
        Object tokenObject = delegateTask.getVariable(BpmConstants.TOKEN_NAME);
        String token = tokenObject != null ? tokenObject.toString() : null;

        // 上一步执行信息ID
        Object pExecIdObject = delegateTask.getVariable(BpmConstants.PARENT_EXEC_ID);
        String parentExecId = pExecIdObject != null ? pExecIdObject.toString() : String.valueOf(0);
        logger.debug("parentExecId is <{}>.", parentExecId);

        BpmExecPo parentExec = null;
        BpmExecPo bpmExec = null;
        BpmExecPo gatewayExec = null;
        String targetNodeType = null;
        IBpmTask bpmTask = getByTaskId(procInstId, delegateTask.getId());
        ActionCmd cmd = ContextThreadUtil.getActionCmd(procInstId);
        ActionCmd pcmd = ContextThreadUtil.getActionCmd(superProcInstId);

        parentExec = bpmExecRepository.get(parentExecId);

        MultiInstanceType multiInstType = delegateTask.multiInstanceType();
        Short isMulitiTask = (short) (MultiInstanceType.NO.equals(multiInstType) ? 0 : 1);

        // 记录网关
        IBpmNodeDefine gatewayNode = BpmExecUtil.getInComeGateway(procDefId, nodeId, parentExec);

        // 记录节点
        IBpmNodeDefine bpmNodeDef = bpmDefineReader.getNode(procDefId, nodeId);
        targetNodeType = bpmNodeDef.getType().getKey();

        Short isGatewayMentJoin = 0;

        boolean isGatewayUnmetJoinEvent = false;// 是否聚合未完毕事件
        boolean isGatewayMetJoinEvent = false;// 是否聚合完毕事件
        if (cmd.getTransitVars("CurrentEventType") != null
                && "GatewayUnmetJoinEvent".equals(cmd.getTransitVars("CurrentEventType").toString())) {
            isGatewayUnmetJoinEvent = true;
            targetNodeType = cmd.getTransitVars("GatewayUnmetNoteType").toString();
            nodeId = bpmTask.getNodeId();
            isGatewayMentJoin = 1;
        }
        if (cmd.getTransitVars("CurrentEventType") != null
                && "GatewayMetJoinEvent".equals(cmd.getTransitVars("CurrentEventType").toString())) {
            isGatewayMetJoinEvent = true;
            targetNodeType = cmd.getTransitVars("GatewayMetNoteType").toString();
            nodeId = bpmTask.getNodeId();
            isGatewayMentJoin = 1;
            cmd.addTransitVars("CurrentEventType", null);//聚合后创建下一个任务不能再聚合
        }

        // 外部子流程聚合
        if (BeanUtils.isNotEmpty(pcmd)) {
            if (pcmd.getTransitVars("CurrentEventType") != null
                    && "GatewayUnmetJoinEvent".equals(pcmd.getTransitVars("CurrentEventType").toString())) {
                isGatewayUnmetJoinEvent = true;
                targetNodeType = pcmd.getTransitVars("GatewayUnmetNoteType").toString();
                nodeId = bpmTask.getNodeId();
                isGatewayMentJoin = 1;
            }
            if (pcmd.getTransitVars("CurrentEventType") != null
                    && "GatewayMetJoinEvent".equals(pcmd.getTransitVars("CurrentEventType").toString())) {
                isGatewayMetJoinEvent = true;
                targetNodeType = pcmd.getTransitVars("GatewayMetNoteType").toString();
                nodeId = bpmTask.getNodeId();
                isGatewayMentJoin = 1;
                pcmd.addTransitVars("CurrentEventType", null);//聚合后创建下一个任务不能再聚合
            }
        }

        // 记录网关
        if (BeanUtils.isNotEmpty(gatewayNode)
                && (MultiInstanceType.NO.equals(multiInstType)
                || !NodeType.SUBENDGATEWAY.getKey().equalsIgnoreCase(gatewayNode.getType().getKey()))
        ) {
            logger.debug("Incoming gateway is not null.Gatway node is <{}/{}>", gatewayNode.getNodeId(), gatewayNode.getName());
            gatewayExec = bpmExecRepository.constructByAutoNode(gatewayNode, bpmTask, parentExec, superProcInstId);
            gatewayExec.setIsGatewayMetjoin((short) 0);

            if (StringUtil.isEmpty(superProcInstId)
                    || !NodeType.SUBSTARTGATEWAY.getKey().equalsIgnoreCase(gatewayNode.getType().getKey())) {
                // 读取数据库中取网关执行信息
                BpmExecPo gatewayExec0 = bpmExecRepository.getFirstByInstNodeStatus(procInstId, gatewayNode.getNodeId(), BpmExecPo.EXEC_STATUS_COMPLETE);
                if (BeanUtils.isEmpty(gatewayExec0)) {
                    gatewayExec0 = bpmExecRepository.getFirstByInstNodeStatus(procInstId, gatewayNode.getNodeId(), BpmExecPo.EXEC_STATUS_INIT);
                }
                if (BeanUtils.isNotEmpty(gatewayExec0)) {
                    gatewayNode.setBpmGatewayExecId(gatewayExec0.getId());
                    gatewayExec.setId(gatewayExec0.getId());
                    cmd.addGateway(gatewayNode);
                }
            }

            if (!cmd.getGateways().contains(gatewayNode)) {
                // 记录网关执行信息
                this.create(gatewayExec);
                // 添加记录网关执行信息到线程中
                gatewayNode.setBpmGatewayExecId(gatewayExec.getId());
                cmd.addGateway(gatewayNode);

                if (NodeType.SUBENDGATEWAY.getKey().equalsIgnoreCase(gatewayNode.getType().getKey())
                        && StringUtil.isNotEmpty(superProcInstId)) {
                    Short status = BpmExecPo.EXEC_STATUS_INIT;
                    if (isGatewayMetJoinEvent) {
                        status = BpmExecPo.EXEC_STATUS_COMPLETE;
                    }
                    this.goToSuperInstExec(delegateTask, gatewayExec, gatewayNode.getNodeId(), gatewayNode.getType().getKey(), status);
                }
            } else {
                for (IBpmNodeDefine theGatewayNode : cmd.getGateways()) {
                    if (theGatewayNode.getNodeId().equals(gatewayNode.getNodeId())) {
                        // 设置为同一个网关执行ID
                        gatewayExec.setId(theGatewayNode.getBpmGatewayExecId());
                    }
                }
            }

            parentExec = gatewayExec;
        }

        if (isGatewayUnmetJoinEvent) {
            BpmExecPo tmpBpmExec = bpmExecRepository.getFirstByInstNodeStatus(procInstId, nodeId, BpmExecPo.EXEC_STATUS_INIT);
            if (BeanUtils.isEmpty(tmpBpmExec)) {
                bpmExec = bpmExecRepository.construct(bpmTask, parentExec, superProcInstId);
                bpmExec.setIsMulitiTask(isMulitiTask);
                bpmExec.setTaskToken(token);
                bpmExec.setTaskId("0");
                bpmExec.setExerId("0");
                bpmExec.setIsGatewayMetjoin(isGatewayMentJoin);
                bpmExec.setNodeType(targetNodeType);

                if (StringUtil.isNotEmpty(superProcInstId)) {
                    bpmExec.setExecStatus(BpmExecPo.EXEC_STATUS_COMPLETE);
                }

                this.create(bpmExec);
                ContextVariableUtil.INSTANCE.removeVariable(delegateTask.getExecutionId(), BpmConstants.TARGET_NODE_ID);
            } else {
                bpmExec = bpmExecRepository.construct(bpmTask, parentExec, superProcInstId);
                List<BpmExecSeqPo> execSeqList = bpmExec.getExecSeqList();
                bpmExec = bpmExecRepository.getByTaskId(bpmTask.getTaskId());
                for (BpmExecSeqPo seqPo : execSeqList) {
                    seqPo.setSrcExecId(bpmExec.getId());
                    seqPo.setSrcProcExecId(bpmExec.getId());
                    seqPo.setTargetExecId(tmpBpmExec.getId());

                    bpmExecSeqDao.create(seqPo);// 建立执行关系
                }
            }

        } else if (isGatewayMetJoinEvent) {
            bpmExec = bpmExecRepository.construct(bpmTask, parentExec, superProcInstId);
            BpmExecPo tmpBpmExec = bpmExecRepository.getByTaskId(bpmTask.getTaskId());
            List<BpmExecSeqPo> execSeqList = bpmExec.getExecSeqList();

            bpmExec = bpmExecRepository.getFirstByInstNodeStatus(procInstId, nodeId, BpmExecPo.EXEC_STATUS_INIT);
            if (BeanUtils.isEmpty(bpmExec)) {
                return;
            }

            for (BpmExecSeqPo seqPo : execSeqList) {
                seqPo.setSrcExecId(tmpBpmExec.getId());
                seqPo.setSrcProcExecId(tmpBpmExec.getId());
                seqPo.setTargetExecId(bpmExec.getId());

                bpmExecSeqDao.create(seqPo);// 建立执行关系
            }

            // 聚合完成，修改执行状态
            bpmExec.setExecStatus(BpmExecPo.EXEC_STATUS_COMPLETE);
            bpmExec.setEndTime(new Date());
            bpmExecDao.update(bpmExec);
            ContextVariableUtil.INSTANCE.setVariable(delegateTask.getExecutionId(), BpmConstants.PARENT_EXEC_ID, bpmExec.getId());
        } else if (MultiInstanceType.NO.equals(multiInstType)) {// 非多实例任务
            bpmExec = bpmExecRepository.construct(bpmTask, parentExec, superProcInstId);
            bpmExec.setIsMulitiTask(isMulitiTask);
            bpmExec.setTaskToken(token);
            bpmExec.setIsGatewayMetjoin(isGatewayMentJoin);
            bpmExec.setNodeType(targetNodeType);

            // 目标节点ID
            Object targetNodeIdObject = delegateTask.getVariable(BpmConstants.TARGET_NODE_ID);
            logger.debug("parentExecId is <{}>.", targetNodeIdObject);
            if (null != targetNodeIdObject) {
                bpmExec.setTargetNode(targetNodeIdObject.toString());
            }

            this.create(bpmExec);
            ContextVariableUtil.INSTANCE.removeVariable(delegateTask.getExecutionId(), BpmConstants.TARGET_NODE_ID);
        } else {
            List<BpmExecPo> tmpExecList = bpmExecRepository.findByInstNodeStatus(procInstId, nodeId, null);

            if (BeanUtils.isEmpty(tmpExecList)) {
                bpmExec = bpmExecRepository.construct(bpmTask, parentExec, superProcInstId);
                bpmExec.setIsMulitiTask(isMulitiTask);
                bpmExec.setTaskToken(token);
                bpmExec.setNodeType(targetNodeType);
            } else {
                bpmExec = tmpExecList.get(0);
                List<BpmExecSeqPo> targetSeqList = bpmExecSeqQueryDao.findByTargetExecId(bpmExec.getId());
                BpmExecSeqPo seq = targetSeqList.get(0);
                seq.setId(null);
                seq.setCreateTime(new Date());
                bpmExec.addExecSeq(seq);
                bpmExec.setExecStatus(BpmExecPo.EXEC_STATUS_INIT);
                bpmExec.setId(UniqueIdUtil.getId());
            }
            bpmExec.setIsGatewayMetjoin(isGatewayMentJoin);
            bpmExec.setTaskId(bpmTask.getId());
            this.create(bpmExec);
        }
    }

    /**
     * 执行信息回归到父实例上
     *
     * @param delegateTask
     * @param superBpmExec
     * @param nodeId
     * @param targetNodeType
     * @param status
     */
    private void goToSuperInstExec(BpmDelegateTask delegateTask, BpmExecPo superBpmExec, String nodeId, String targetNodeType, Short status) {
        String procInstId = (String) delegateTask.getVariable(BpmConstants.PROCESS_INST_ID);
        String superProcInstId = (String) delegateTask.getVariable(BpmConstants.PROCESS_PARENT_INST_ID);
        String superExecutionId = delegateTask.getSupperExecutionId();

        BpmInstPo inst = bpmInstQueryDao.get(superProcInstId);
        IBpmTask bpmTask = getByTaskId(procInstId, delegateTask.getId());
        BpmExecPo bpmExec = bpmExecRepository.getFirstByInstNodeStatus(superProcInstId, nodeId, BpmExecPo.EXEC_STATUS_INIT);
        if (BeanUtils.isEmpty(bpmExec)) {
            bpmExec = bpmExecRepository.construct(bpmTask, superBpmExec, "0");
            bpmExec.setTaskId("0");
            bpmExec.setExerId("0");
            bpmExec.setProcDefId(inst.getProcDefId());
            bpmExec.setProcInstId(superProcInstId);
            bpmExec.setNodeId(nodeId);
            bpmExec.setNodeType(targetNodeType);
            bpmExec.setExecStatus(status);
            bpmExec.setIsGatewayMetjoin((short) 1);
            bpmExec.setIsMulitiTask((short) 1);

            this.create(bpmExec);
        } else {
            BpmExecPo bpmExecTmp = bpmExecRepository.construct(bpmTask, superBpmExec, "0");
            List<BpmExecSeqPo> execSeqList = bpmExecTmp.getExecSeqList();

            for (BpmExecSeqPo seqPo : execSeqList) {
                seqPo.setTargetExecId(bpmExec.getId());

                bpmExecSeqDao.create(seqPo);// 建立执行关系
            }

            if (BpmExecPo.EXEC_STATUS_COMPLETE.equals(status)) {
                bpmExec.setEndTime(new Date());
                bpmExec.setExecStatus(status);
                bpmExecDao.update(bpmExec);
            }
        }

        ContextVariableUtil.INSTANCE.setVariable(superExecutionId, BpmConstants.PARENT_EXEC_ID, bpmExec.getId());
    }

    /**
     * 创建
     *
     * @param po
     */
    private void create(BpmExecPo po) {
        bpmExecDao.create(po);
        List<BpmExecSeqPo> seqList = po.getExecSeqList();
        for (BpmExecSeqPo seqPo : seqList) {
            seqPo.setTargetExecId(po.getId());
            bpmExecSeqDao.create(seqPo);
        }
    }

    /**
     * 撤销执行信息
     *
     * @param instId
     * @param currentNode
     * @param currentToken
     * @param handleMode
     * @param destinationNode
     * @param destinationToken
     * @param userId
     */
    public void pop(String instId, String currentNode, String currentToken, String handleMode,
                    String destinationNode, String destinationToken, String userId) {
        // 目标节点不为空,如果处理模式传入为空，那么处理模式默认为直接跳转回来
        if (StringUtil.isEmpty(handleMode)) {
            handleMode = BpmExecPo.HAND_MODE_DIRECT;
        }

        // 获取目标节点的执行信息数据
        BpmExecPo targetExec = bpmExecRepository.getFirstByInstNodeStatus(instId, destinationNode, BpmExecPo.EXEC_STATUS_COMPLETE);
        if (BeanUtils.isEmpty(targetExec)) {
            throw new RuntimeException("没有找到目标节点执行信息数据!");
        }

        // 如果驳回处理完直接返回：2、目标节点执行信息设置targetNode和targetToken 3、将parentExec放入线程变量
        if (!BpmExecPo.HAND_MODE_DIRECT.equals(handleMode)) {
            // 按照流程图执行，删除目标节点之后所有执行信息信息
            // 临时存放在CMD中，当新的目标节点创建成功后删除之后所有任务
            ActionCmd actionCmd = ContextThreadUtil.getActionCmd(instId);
            actionCmd.addTransitVars(BpmExecPo.HAND_MODE_NORMAL_IS_CANCLE_NODE_BEHAND_TASK, true);
            actionCmd.addTransitVars(BpmExecPo.HAND_MODE_NORMAL_TARGET_NODE_ID, targetExec.getNodeId());
        }
    }

    /**
     * 撤销执行信息
     *
     * @param instId
     * @param currentNode
     * @param handleMode
     * @param userId
     */
    public void popToStart(String instId, String currentNode, String handleMode, String userId) {
        BpmExecPo targetExec = bpmExecRepository.getInit(instId);
        // 目标节点不为空，如果处理模式传入为空，处理模式默认指定跳转
        if (StringUtil.isEmpty(handleMode)) {
            handleMode = BpmExecPo.HAND_MODE_DIRECT;
        }

        if (!BpmExecPo.HAND_MODE_DIRECT.equals(handleMode)) {
            // 按流程图走,临时存放在CMD中，当新的目标节点创建成功后删除之后所有任务
            ActionCmd actionCmd = ContextThreadUtil.getActionCmd(instId);
            actionCmd.addTransitVars(BpmExecPo.HAND_MODE_NORMAL_IS_CANCLE_NODE_BEHAND_TASK, true);
            actionCmd.addTransitVars(BpmExecPo.HAND_MODE_NORMAL_TARGET_NODE_ID, targetExec.getNodeId());
            actionCmd.addTransitVars(BpmExecPo.HAND_MODE_NORMAL_REJECT_START, true);
        }
    }

    /**
     * 删除执行信息
     *
     * @param instId
     * @param targetNodeId
     * @param notIncludeExecuteIds
     */
    public void removeBpmExec(String procInstId, String targetNodeId) {
        List<String> execIds = bpmExecRepository.findBehandExecIds(procInstId, targetNodeId, true);
        if (BeanUtils.isEmpty(execIds)) {
            return;
        }

        bpmExecDao.removeByIds(execIds);
    }

    /**
     * 获取任务
     *
     * @param instId
     * @param taskId
     * @return
     */
    private IBpmTask getByTaskId(String instId, String taskId) {
        IBpmTask bpmTask = null;
        Set<IBpmTask> taskSet = ContextThreadUtil.getByInstId(instId);
        if (BeanUtils.isEmpty(taskSet)) {
            return null;
        }

        for (Iterator<IBpmTask> it = taskSet.iterator(); it.hasNext(); ) {
            bpmTask = it.next();
            if (bpmTask.getTaskId().equals(taskId)) {
                break;
            }
        }

        return bpmTask;
    }

    /**
     * 更新待审批执行数据为取消
     *
     * @param instId
     * @param nodeId
     * @param execStatusCancel
     */
    public void cancelExec(String instId, String nodeId, Short execStatusCancel) {
        bpmExecDao.cancelExec(instId, nodeId, execStatusCancel);
    }

}
