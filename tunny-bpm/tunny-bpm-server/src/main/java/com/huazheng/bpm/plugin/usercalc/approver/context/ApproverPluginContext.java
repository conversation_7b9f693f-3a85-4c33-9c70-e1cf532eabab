package com.huazheng.bpm.plugin.usercalc.approver.context;

import com.huazheng.bpm.plugin.AbstractUserCalcPluginContext;
import com.huazheng.bpm.plugin.IBpmPluginDefine;
import com.huazheng.bpm.plugin.IRuntimePlugin;
import com.huazheng.bpm.plugin.usercalc.approver.def.ApproverPluginDefine;
import com.huazheng.bpm.plugin.usercalc.approver.runtime.ApproverPlugin;
import com.jamesmurty.utils.XMLBuilder;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;
import org.w3c.dom.Element;

import javax.xml.parsers.FactoryConfigurationError;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;

/**
 * 流程实例审批人
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月14日-上午9:56:46
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
@Service("approverPluginContext")
public class ApproverPluginContext extends AbstractUserCalcPluginContext {

    @Override
    public String getDescription() {
        return "流程实例审批人";
    }

    @Override
    public String getTitle() {
        return "流程实例审批人";
    }

    @Override
    public Class<? extends IRuntimePlugin<?, ?, ?>> getPluginClass() {
        return ApproverPlugin.class;
    }

    @Override
    public String getPluginXml() {
        ApproverPluginDefine def = (ApproverPluginDefine) getBpmPluginDefine();
        XMLBuilder xmlBuilder;
        try {
            xmlBuilder = XMLBuilder.create("approver")
                    .a("xmlns", "http://www.bpmhome.cn/bpm/plugins/userCalc/approver")
                    .a("logicCal", def.getLogicCal().getKey())
                    .a("extract", def.getExtract().getKey());
            return xmlBuilder.asString();
        } catch (ParserConfigurationException e) {
            e.printStackTrace();
        } catch (FactoryConfigurationError e) {
            e.printStackTrace();
        } catch (TransformerException e) {
            e.printStackTrace();
        }
        return "";
    }

    @Override
    protected IBpmPluginDefine parseElement(Element element) {
        ApproverPluginDefine def = new ApproverPluginDefine();
        return def;
    }

    @Override
    protected IBpmPluginDefine parseJson(JSONObject pluginJson) {
        ApproverPluginDefine def = new ApproverPluginDefine();
        return def;
    }

}
