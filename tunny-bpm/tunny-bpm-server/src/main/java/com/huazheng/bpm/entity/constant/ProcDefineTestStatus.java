package com.huazheng.bpm.entity.constant;

/**
 * 流程定义测试状态枚举
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月20日-上午11:38:48
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum ProcDefineTestStatus {

    TEST("test", "测试"), RUN("run", "正式");
    private String key;
    private String name;

    ProcDefineTestStatus(String _key, String _name) {
        key = _key;
        name = _name;
    }

    public String getKey() {
        return key;
    }

    public String getName() {
        return name;
    }
}
