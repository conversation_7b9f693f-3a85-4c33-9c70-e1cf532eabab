package com.huazheng.bpm.domain.msg;

import com.huazheng.bpm.dao.msg.MessageTemplateDao;
import com.huazheng.bpm.dao.msg.MessageTemplateQueryDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.msg.MessageTemplatePo;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.db.UniqueIdUtil;
import com.huazheng.bpm.util.string.StringUtil;
import org.springframework.context.annotation.Scope;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@SuppressWarnings("serial")
@Transactional
@Scope("prototype")
public class MessageTemplate extends AbstractDomain<String, MessageTemplatePo> {
    @Resource
    private MessageTemplateDao messageTemplateDao;
    @Resource
    private MessageTemplateQueryDao messageTemplateQueryDao;

    @Override
    protected void init() {
        this.setDao(messageTemplateDao);
    }

    public void save() {
        MessageTemplatePo messageTemplate = getData();
        String id = messageTemplate.getId();
        //只有一个默认
        if (messageTemplate.getIsDefault()) {
            MessageTemplatePo msg = messageTemplateQueryDao.getDefault(messageTemplate.getTypeKey());
            if (BeanUtils.isNotEmpty(msg)) {
                msg.setIsDefault(false);
                messageTemplateDao.update(msg);
            }
        }

        if (StringUtil.isEmpty(id)) {
            messageTemplate.setId(UniqueIdUtil.getId());
            messageTemplateDao.create(messageTemplate);
        } else {
            messageTemplateDao.update(messageTemplate);
        }
    }
}
