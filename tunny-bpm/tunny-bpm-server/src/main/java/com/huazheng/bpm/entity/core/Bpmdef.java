//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4-2
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.08.25 at 03:37:49 PM CST
//


package com.huazheng.bpm.entity.core;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for bpmdef complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="bpmdef">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="defId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="defKey" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="desc" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="typeId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="testStatus" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="bpmnDefId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="bpmnDeployId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="version" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="mainDefId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="isMain" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="reason" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="bpmdefxml" type="{http://www.bpmhome.cn/ibps-bpm-def}bpmdefxml"/>
 *         &lt;element name="reminders" type="{http://www.bpmhome.cn/ibps-bpm-def}reminders" minOccurs="0"/>
 *         &lt;element name="trigerFlows" type="{http://www.bpmhome.cn/ibps-bpm-def}trigerFlows" minOccurs="0"/>
 *         &lt;element name="auths" type="{http://www.bpmhome.cn/ibps-bpm-def}auths" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "bpmdef", propOrder = {
        "defId",
        "name",
        "defKey",
        "desc",
        "typeId",
        "status",
        "testStatus",
        "bpmnDefId",
        "bpmnDeployId",
        "version",
        "mainDefId",
        "isMain",
        "reason",
        "bpmdefxml",
        "reminders",
        "trigerFlows",
        "auths"
})
public class Bpmdef {

    @XmlElement(required = true)
    protected String defId;
    @XmlElement(required = true)
    protected String name;
    @XmlElement(required = true)
    protected String defKey;
    protected String desc;
    protected String typeId;
    @XmlElement(required = true)
    protected String status;
    @XmlElement(required = true)
    protected String testStatus;
    protected String bpmnDefId;
    protected String bpmnDeployId;
    protected int version;
    protected String mainDefId;
    @XmlElement(required = true)
    protected String isMain;
    protected String reason;
    @XmlElement(required = true)
    protected Bpmdefxml bpmdefxml;
    protected Reminders reminders;
    protected TrigerFlows trigerFlows;
    protected Auths auths;

    /**
     * Gets the value of the defId property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getDefId() {
        return defId;
    }

    /**
     * Sets the value of the defId property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDefId(String value) {
        this.defId = value;
    }

    /**
     * Gets the value of the name property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the value of the name property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * Gets the value of the defKey property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getDefKey() {
        return defKey;
    }

    /**
     * Sets the value of the defKey property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDefKey(String value) {
        this.defKey = value;
    }

    /**
     * Gets the value of the desc property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getDesc() {
        return desc;
    }

    /**
     * Sets the value of the desc property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDesc(String value) {
        this.desc = value;
    }

    /**
     * Gets the value of the typeId property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getTypeId() {
        return typeId;
    }

    /**
     * Sets the value of the typeId property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setTypeId(String value) {
        this.typeId = value;
    }

    /**
     * Gets the value of the status property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setStatus(String value) {
        this.status = value;
    }

    /**
     * Gets the value of the testStatus property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getTestStatus() {
        return testStatus;
    }

    /**
     * Sets the value of the testStatus property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setTestStatus(String value) {
        this.testStatus = value;
    }

    /**
     * Gets the value of the bpmnDefId property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getBpmnDefId() {
        return bpmnDefId;
    }

    /**
     * Sets the value of the bpmnDefId property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBpmnDefId(String value) {
        this.bpmnDefId = value;
    }

    /**
     * Gets the value of the bpmnDeployId property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getBpmnDeployId() {
        return bpmnDeployId;
    }

    /**
     * Sets the value of the bpmnDeployId property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBpmnDeployId(String value) {
        this.bpmnDeployId = value;
    }

    /**
     * Gets the value of the version property.
     */
    public int getVersion() {
        return version;
    }

    /**
     * Sets the value of the version property.
     */
    public void setVersion(int value) {
        this.version = value;
    }

    /**
     * Gets the value of the mainDefId property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getMainDefId() {
        return mainDefId;
    }

    /**
     * Sets the value of the mainDefId property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setMainDefId(String value) {
        this.mainDefId = value;
    }

    /**
     * Gets the value of the isMain property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getIsMain() {
        return isMain;
    }

    /**
     * Sets the value of the isMain property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setIsMain(String value) {
        this.isMain = value;
    }

    /**
     * Gets the value of the reason property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getReason() {
        return reason;
    }

    /**
     * Sets the value of the reason property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setReason(String value) {
        this.reason = value;
    }

    /**
     * Gets the value of the bpmdefxml property.
     *
     * @return possible object is
     * {@link Bpmdefxml }
     */
    public Bpmdefxml getBpmdefxml() {
        return bpmdefxml;
    }

    /**
     * Sets the value of the bpmdefxml property.
     *
     * @param value allowed object is
     *              {@link Bpmdefxml }
     */
    public void setBpmdefxml(Bpmdefxml value) {
        this.bpmdefxml = value;
    }

    /**
     * Gets the value of the reminders property.
     *
     * @return possible object is
     * {@link Reminders }
     */
    public Reminders getReminders() {
        return reminders;
    }

    /**
     * Sets the value of the reminders property.
     *
     * @param value allowed object is
     *              {@link Reminders }
     */
    public void setReminders(Reminders value) {
        this.reminders = value;
    }

    /**
     * Gets the value of the trigerFlows property.
     *
     * @return possible object is
     * {@link TrigerFlows }
     */
    public TrigerFlows getTrigerFlows() {
        return trigerFlows;
    }

    /**
     * Sets the value of the trigerFlows property.
     *
     * @param value allowed object is
     *              {@link TrigerFlows }
     */
    public void setTrigerFlows(TrigerFlows value) {
        this.trigerFlows = value;
    }

    /**
     * Gets the value of the auths property.
     *
     * @return possible object is
     * {@link Auths }
     */
    public Auths getAuths() {
        return auths;
    }

    /**
     * Sets the value of the auths property.
     *
     * @param value allowed object is
     *              {@link Auths }
     */
    public void setAuths(Auths value) {
        this.auths = value;
    }

}
