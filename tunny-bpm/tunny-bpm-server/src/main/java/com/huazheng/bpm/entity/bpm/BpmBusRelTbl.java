package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;


/**
 * 业务数据关联
 * 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：luodx
 * 邮箱地址：<EMAIL>
 * 创建时间：2016-12-12 15:56:36
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmBusRelTbl extends AbstractPo<String> {
    protected String id;        /*主键*/
    protected String defId;        /*流程定义ID*/
    protected String procInstId;        /*流程实例ID*/
    protected String businesskey;        /*业务键字符型*/
    protected String formIdentify;        /*表单标识*/
    protected String startId;        /*发起人*/
    protected java.util.Date createDate;        /*创建时间*/
    protected Integer isMain;        /*主记录*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setDefId(String defId) {
        this.defId = defId;
    }

    /**
     * 返回 流程定义ID
     *
     * @return
     */
    public String getDefId() {
        return this.defId;
    }

    public void setProcInstId(String procInstId) {
        this.procInstId = procInstId;
    }

    /**
     * 返回 流程实例ID
     *
     * @return
     */
    public String getProcInstId() {
        return this.procInstId;
    }

    public void setBusinesskey(String businesskey) {
        this.businesskey = businesskey;
    }

    /**
     * 返回 业务键字符型
     *
     * @return
     */
    public String getBusinesskey() {
        return this.businesskey;
    }

    public void setFormIdentify(String formIdentify) {
        this.formIdentify = formIdentify;
    }

    /**
     * 返回 表单标识
     *
     * @return
     */
    public String getFormIdentify() {
        return this.formIdentify;
    }

    public void setStartId(String startId) {
        this.startId = startId;
    }

    /**
     * 返回 发起人
     *
     * @return
     */
    public String getStartId() {
        return this.startId;
    }

    public void setCreateDate(java.util.Date createDate) {
        this.createDate = createDate;
    }

    /**
     * 返回 创建时间
     *
     * @return
     */
    public java.util.Date getCreateDate() {
        return this.createDate;
    }

    public void setIsMain(Integer isMain) {
        this.isMain = isMain;
    }

    /**
     * 返回 主记录
     *
     * @return
     */
    public Integer getIsMain() {
        return this.isMain;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("defId", this.defId)
                .append("procInstId", this.procInstId)
                .append("businesskey", this.businesskey)
                .append("formIdentify", this.formIdentify)
                .append("startId", this.startId)
                .append("createDate", this.createDate)
                .append("isMain", this.isMain)
                .toString();
    }
}
