package com.huazheng.bpm.model.http;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.security.AccessControlException;
import java.util.Properties;

/**
 * 读取配置文件。
 *
 * <pre>
 *
 * 构建组：ibps-platform-webapi
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年5月16日-下午4:53:18
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class Configuration {
    private static Properties defaultProperty;

    static String PREFIX = "webapi.";

    static {
        init();
    }

    /* package */
    static void init() {
        defaultProperty = new Properties();
        defaultProperty.setProperty(PREFIX + "debug", "true");
        // defaultProperty.setProperty(PREFIX+"source", CONSUMER_KEY);
        // defaultProperty.setProperty(PREFIX+"clientVersion","");
        defaultProperty.setProperty(PREFIX + "clientURL", "");
        defaultProperty.setProperty(PREFIX + "http.userAgent", "");
        // defaultProperty.setProperty(PREFIX+"user","");
        // defaultProperty.setProperty(PREFIX+"password","");
        defaultProperty.setProperty(PREFIX + "http.useSSL", "false");
        // defaultProperty.setProperty(PREFIX+"http.proxyHost","");
        defaultProperty.setProperty(PREFIX + "http.proxyHost.fallback", "http.proxyHost");
        // defaultProperty.setProperty(PREFIX+"http.proxyUser","");
        // defaultProperty.setProperty(PREFIX+"http.proxyPassword","");
        // defaultProperty.setProperty(PREFIX+"http.proxyPort","");
        defaultProperty.setProperty(PREFIX + "http.proxyPort.fallback", "http.proxyPort");
        defaultProperty.setProperty(PREFIX + "http.connectionTimeout", "20000");
        defaultProperty.setProperty(PREFIX + "http.readTimeout", "120000");
        defaultProperty.setProperty(PREFIX + "http.retryCount", "3");
        defaultProperty.setProperty(PREFIX + "http.retryIntervalSecs", "10");
        // defaultProperty.setProperty(PREFIX+"oauth.consumerKey","");
        // defaultProperty.setProperty(PREFIX+"oauth.consumerSecret","");
        defaultProperty.setProperty(PREFIX + "async.numThreads", "1");
        defaultProperty.setProperty(PREFIX + "clientVersion", Version.getVersion());
        try {
            // Android platform should have dalvik.system.VMRuntime in the
            // classpath.
            // @see
            // http://developer.android.com/reference/dalvik/system/VMRuntime.html
            Class.forName("dalvik.system.VMRuntime");
            defaultProperty.setProperty(PREFIX + "dalvik", "true");
        } catch (ClassNotFoundException cnfe) {
            defaultProperty.setProperty(PREFIX + "dalvik", "false");
        }
        DALVIK = getBoolean(PREFIX + "dalvik");
        String t4jProps = PREFIX + "properties";
        boolean loaded = loadProperties(defaultProperty,
                "." + File.separatorChar + "conf" + File.separatorChar + t4jProps)
                || loadProperties(defaultProperty, Configuration.class.getResourceAsStream("/WEB-INF/conf" + t4jProps))
                || loadProperties(defaultProperty, Configuration.class.getResourceAsStream("/conf/" + t4jProps));
        System.out.println(loaded);
    }

    private static boolean loadProperties(Properties props, String path) {
        try {
            File file = new File(path);
            if (file.exists() && file.isFile()) {
                props.load(new FileInputStream(file));
                return true;
            }
        } catch (Exception ignore) {
        }
        return false;
    }

    private static boolean loadProperties(Properties props, InputStream is) {
        try {
            props.load(is);
            return true;
        } catch (Exception ignore) {
        }
        return false;
    }

    private static boolean DALVIK;

    public static boolean isDalvik() {
        return DALVIK;
    }

    public static boolean useSSL() {
        return getBoolean(PREFIX + "http.useSSL");
    }

    public static String getScheme() {
        return useSSL() ? "https://" : "http://";
    }

    public static String getCilentVersion() {
        return getProperty(PREFIX + "clientVersion");
    }

    public static String getCilentVersion(String clientVersion) {
        return getProperty(PREFIX + "clientVersion", clientVersion);
    }

    public static String getSource() {
        return getProperty(PREFIX + "source");
    }

    public static String getSource(String source) {
        return getProperty(PREFIX + "source", source);
    }

    public static String getProxyHost() {
        return getProperty(PREFIX + "http.proxyHost");
    }

    public static String getProxyHost(String proxyHost) {
        return getProperty(PREFIX + "http.proxyHost", proxyHost);
    }

    public static String getProxyUser() {
        return getProperty(PREFIX + "http.proxyUser");
    }

    public static String getProxyUser(String user) {
        return getProperty(PREFIX + "http.proxyUser", user);
    }

    public static String getClientURL() {
        return getProperty(PREFIX + "clientURL");
    }

    public static String getClientURL(String clientURL) {
        return getProperty(PREFIX + "clientURL", clientURL);
    }

    public static String getProxyPassword() {
        return getProperty(PREFIX + "http.proxyPassword");
    }

    public static String getProxyPassword(String password) {
        return getProperty(PREFIX + "http.proxyPassword", password);
    }

    public static int getProxyPort() {
        return getIntProperty(PREFIX + "http.proxyPort");
    }

    public static int getProxyPort(int port) {
        return getIntProperty(PREFIX + "http.proxyPort", port);
    }

    public static int getConnectionTimeout() {
        return getIntProperty(PREFIX + "http.connectionTimeout");
    }

    public static int getConnectionTimeout(int connectionTimeout) {
        return getIntProperty(PREFIX + "http.connectionTimeout", connectionTimeout);
    }

    public static int getReadTimeout() {
        return getIntProperty(PREFIX + "http.readTimeout");
    }

    public static int getReadTimeout(int readTimeout) {
        return getIntProperty(PREFIX + "http.readTimeout", readTimeout);
    }

    public static int getRetryCount() {
        return getIntProperty(PREFIX + "http.retryCount");
    }

    public static int getRetryCount(int retryCount) {
        return getIntProperty(PREFIX + "http.retryCount", retryCount);
    }

    public static int getRetryIntervalSecs() {
        return getIntProperty(PREFIX + "http.retryIntervalSecs");
    }

    public static int getRetryIntervalSecs(int retryIntervalSecs) {
        return getIntProperty(PREFIX + "http.retryIntervalSecs", retryIntervalSecs);
    }

    public static String getUser() {
        return getProperty(PREFIX + "user");
    }

    public static String getUser(String userId) {
        return getProperty(PREFIX + "user", userId);
    }

    public static String getPassword() {
        return getProperty(PREFIX + "password");
    }

    public static String getPassword(String password) {
        return getProperty(PREFIX + "password", password);
    }

    public static String getUserAgent() {
        return getProperty(PREFIX + "http.userAgent");
    }

    public static String getUserAgent(String userAgent) {
        return getProperty(PREFIX + "http.userAgent", userAgent);
    }

    public static String getOAuthConsumerKey() {
        return getProperty(PREFIX + "oauth.consumerKey");
    }

    public static String getOAuthConsumerKey(String consumerKey) {
        return getProperty(PREFIX + "oauth.consumerKey", consumerKey);
    }

    public static String getOAuthConsumerSecret() {
        return getProperty(PREFIX + "oauth.consumerSecret");
    }

    public static String getOAuthConsumerSecret(String consumerSecret) {
        return getProperty(PREFIX + "oauth.consumerSecret", consumerSecret);
    }

    public static boolean getBoolean(String name) {
        String value = getProperty(name);
        return Boolean.valueOf(value);
    }

    public static int getIntProperty(String name) {
        String value = getProperty(name);
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException nfe) {
            return -1;
        }
    }

    public static int getIntProperty(String name, int fallbackValue) {
        String value = getProperty(name, String.valueOf(fallbackValue));
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException nfe) {
            return -1;
        }
    }

    public static long getLongProperty(String name) {
        String value = getProperty(name);
        try {
            return Long.parseLong(value);
        } catch (NumberFormatException nfe) {
            return -1;
        }
    }

    public static String getProperty(String name) {
        return getProperty(name, null);
    }

    public static String getProperty(String name, String fallbackValue) {
        String value;
        try {
            value = System.getProperty(name, fallbackValue);
            if (null == value) {
                value = defaultProperty.getProperty(name);
            }
            if (null == value) {
                String fallback = defaultProperty.getProperty(name + ".fallback");
                if (null != fallback) {
                    value = System.getProperty(fallback);
                }
            }
        } catch (AccessControlException ace) {
            // Unsigned applet cannot access System properties
            value = fallbackValue;
        }
        return replace(value);
    }

    private static String replace(String value) {
        if (null == value) {
            return value;
        }
        String newValue = value;
        int openBrace = 0;
        if (-1 != (openBrace = value.indexOf("{", openBrace))) {
            int closeBrace = value.indexOf("}", openBrace);
            if (closeBrace > (openBrace + 1)) {
                String name = value.substring(openBrace + 1, closeBrace);
                if (name.length() > 0) {
                    newValue = value.substring(0, openBrace) + getProperty(name) + value.substring(closeBrace + 1);

                }
            }
        }
        if (newValue.equals(value)) {
            return value;
        } else {
            return replace(newValue);
        }
    }

    public static int getNumberOfAsyncThreads() {
        return getIntProperty(PREFIX + "async.numThreads");
    }

    public static boolean getDebug() {
        return getBoolean(PREFIX + "debug");

    }
}
