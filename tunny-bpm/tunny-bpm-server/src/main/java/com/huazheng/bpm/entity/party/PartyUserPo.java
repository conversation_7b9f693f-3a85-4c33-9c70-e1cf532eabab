package com.huazheng.bpm.entity.party;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.huazheng.bpm.model.base.IdentityConstants;
import com.huazheng.bpm.model.base.User;
import com.huazheng.bpm.util.core.JacksonUtil;

import java.util.Collections;
import java.util.List;

/**
 * 用户  实体对象。
 *
 * <pre>
 * 构建组：ibps-org-biz
 * 作者：huangchunyan
 * 邮箱：<EMAIL>
 * 日期：2016-06-16 17:26:46
 * 版权：广州流辰信息技术有限公司
 * </pre>
 */
@SuppressWarnings("serial")
public class PartyUserPo extends PartyUserTbl implements User {
    public static final String DEFAULT_USER_IMAGE = "commons/image/default_use_image.jpg";
    public static final String DEFAULT_USER_ACCOUNT = "admin"; //"guest";

    protected String wcAccount;
    protected String fullname;

    @JsonIgnore
    @Override
    public String getIdentityType() {
        return IdentityConstants.USER;
    }

    @Override
    public String getUserId() {
        return id;
    }

    @Override
    public void setUserId(String userId) {
        id = userId;
    }

    @Override
    public String getFullname() {
        return fullname;
    }

    @Override
    public void setFullname(String fullName) {
        this.fullname = fullName;
    }

    @Override
    public String getEmail() {
        return null;
    }

    @Override
    public String getMobile() {
        return null;
    }

    @Override
    public boolean isSuper() {
        return false;
    }

    @Override
    public String getPhoto() {
        return null;
    }

    public String getWcAccount() {
        return wcAccount;
    }

    public void setWcAccount(String wcAccount) {
        this.wcAccount = wcAccount;
    }

    public static PartyUserPo fromJsonString(String data) {
        if (JacksonUtil.isNotJsonObject(data)) {
            return null;
        }
        return JacksonUtil.getDTO(data, PartyUserPo.class);
    }

    public static List<PartyUserPo> fromJsonArrayString(String listData) {
        if (JacksonUtil.isNotJsonArray(listData)) {
            return Collections.emptyList();
        }
        return JacksonUtil.getDTOList(listData, PartyUserPo.class);
    }
}
