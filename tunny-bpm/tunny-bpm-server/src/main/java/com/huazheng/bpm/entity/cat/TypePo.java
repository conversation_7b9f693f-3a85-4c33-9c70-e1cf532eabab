package com.huazheng.bpm.entity.cat;


import com.huazheng.bpm.model.cat.IType;

/**
 * 分类标识表  实体对象。
 *
 * <pre>
 * 构建组：bps-common-biz
 * 作者：huangcy
 * 邮箱：<EMAIL>
 * 日期：2015-11-19 14:17:31
 * 版权：广州流辰信息技术有限公司
 * </pre>
 */
@SuppressWarnings("serial")
public class TypePo extends TypeTbl implements IType {

    /**
     * 根结点的父ID
     */
    public final static String ROOT_PID = "-1";// 重要

    /**
     * 根节点的ID
     */
    public final static String ROOT_ID = "0";// 重要

    /**
     * 是否有子节点
     */
    public final static String IS_LEAF_YES = "Y"; //是
    public final static String IS_LEAF_NO = "N";  //否

    /**
     * 结构类型
     */
    public final static String STRUTYPE_TREE = "1";
    public final static String STRUTYPE_TILE = "0";

}
