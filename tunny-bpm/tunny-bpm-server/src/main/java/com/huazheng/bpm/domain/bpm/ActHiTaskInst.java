package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.ActHiTaskInstDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.ActHiTaskInstPo;
import org.springframework.context.annotation.Scope;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@SuppressWarnings("serial")
@Transactional
@Scope("prototype")
public class ActHiTaskInst extends AbstractDomain<String, ActHiTaskInstPo> {

    @Resource
    private ActHiTaskInstDao actHiTaskInstDao;

    @Override
    protected void init() {
        this.setDao(actHiTaskInstDao);
    }
}
