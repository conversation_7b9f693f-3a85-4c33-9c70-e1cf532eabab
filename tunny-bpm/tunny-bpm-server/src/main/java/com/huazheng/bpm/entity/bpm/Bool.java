package com.huazheng.bpm.entity.bpm;

/**
 * 通用布尔。
 *
 * <pre>
 * 构建组：ibps-base-core
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2015-11-2-下午2:12:06
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum Bool {
    TRUE('Y', "是"), FALSE('N', "否");
    private char value;
    private String label;

    Bool(char value, String label) {
        this.value = value;
        this.label = label;
    }

    public char value() {
        return value;
    }

    public String valueToString() {
        return String.valueOf(value);
    }

    public String label() {
        return label;
    }

    public static Bool fromValue(char value) {
        if (value == 'Y') {
            return Bool.TRUE;
        }
        return Bool.FALSE;
    }

    public static Bool fromValue(String value) {
        if ("Y".equals(value)) {
            return Bool.TRUE;
        }
        return Bool.FALSE;
    }

    /*
     * (non-Javadoc)
     *
     * @see java.lang.Enum#toString()
     */
    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
