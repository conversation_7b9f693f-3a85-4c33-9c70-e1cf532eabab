package com.huazheng.bpm.model.base;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlTransient;
import java.util.Date;

/**
 * 实体的抽象类。
 *
 * <pre>
 * 构建组：ibps-base-framework
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2015-10-10-上午10:33:16
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.NONE)
public abstract class AbstractModel<T> implements BaseModel {
    /**
     * 创建人
     */
    protected String createBy;
    /**
     * 更新人
     */
    protected String updateBy;
    /**
     * 创建时间
     */
    @XmlTransient
    protected Date createTime;
    /**
     * 更新时间
     */
    protected Date updateTime;
    /**
     * 创建人组织ID
     */
    protected String createOrgId;

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateOrgId() {
        return createOrgId;
    }

    public void setCreateOrgId(String createOrgId) {
        this.createOrgId = createOrgId;
    }

    /**
     * 设置主键ID
     *
     * @param id
     */
    public abstract void setId(T id);

    /**
     * 获取主键ID
     *
     * @return
     */
    public abstract T getId();

}
