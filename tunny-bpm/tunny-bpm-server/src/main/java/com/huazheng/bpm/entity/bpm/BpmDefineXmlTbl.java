package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;


/**
 * 流程定义大数据
 * 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2016-12-09 16:33:56
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmDefineXmlTbl extends AbstractPo<String> {
    protected String id;        /*流程定义ID*/
    protected String defXml;        /*流程定义XML*/
    protected String bpmnXml;        /*流程定义BPMN格式XML*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 流程定义ID
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setDefXml(String defXml) {
        this.defXml = defXml;
    }

    /**
     * 返回 流程定义XML
     *
     * @return
     */
    public String getDefXml() {
        return this.defXml;
    }

    public void setBpmnXml(String bpmnXml) {
        this.bpmnXml = bpmnXml;
    }

    /**
     * 返回 流程定义BPMN格式XML
     *
     * @return
     */
    public String getBpmnXml() {
        return this.bpmnXml;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("defXml", this.defXml)
                .append("bpmnXml", this.bpmnXml)
                .toString();
    }
}
