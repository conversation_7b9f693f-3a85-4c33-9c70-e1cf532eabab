package com.huazheng.bpm.plugin.task.userassign.plugin;


import com.huazheng.bpm.model.base.BpmIdentity;
import com.huazheng.bpm.model.node.UserAssignRule;
import com.huazheng.bpm.plugin.AbstractUserCalcPlugin;
import com.huazheng.bpm.plugin.IBpmPluginDefine;
import com.huazheng.bpm.plugin.task.userassign.def.UserAssignPluginDefine;
import com.huazheng.bpm.service.IPartyUserService;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.helper.UserAssignRuleQueryHelper;
import com.huazheng.bpm.util.session.BpmUserCalcPluginSession;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 人员计算插件
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月14日-上午9:33:38
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@Service("userQueryPlugin")
@Transactional
public class UserQueryPlugin extends AbstractUserCalcPlugin {

    @Override
    public List<BpmIdentity> execute(BpmUserCalcPluginSession pluginSession, IBpmPluginDefine pluginDef) {
        List<BpmIdentity> identitieList = new ArrayList<BpmIdentity>();
        List<UserAssignRule> userAssignRules = getUserAssignRules(pluginDef);
        if (BeanUtils.isEmpty(userAssignRules)) {
            return identitieList;
        }
        List<BpmIdentity> bpmIdentities = UserAssignRuleQueryHelper.query(userAssignRules, pluginSession);

        if (BeanUtils.isNotEmpty(bpmIdentities)) {
            IPartyUserService defaultUserService = AppUtil.getBean(IPartyUserService.class);
            for (BpmIdentity identity : bpmIdentities) {
                if (identity == null) {
                    continue;
                }
                if (BeanUtils.isNotEmpty(identity.getType())
                        && BpmIdentity.TYPE_USER.equals(identity.getType()))
                    //continue;
                    //后续需要修改接口，判断人员未禁用
                    //&& defaultUserService.isDeleted(identity.getId()))

                {
                    identitieList.add(identity);
                }
            }
        }

        return identitieList;
    }

    @Override
    public List<BpmIdentity> run(BpmUserCalcPluginSession pluginSession, List<UserAssignRule> userAssignRules) {
        List<BpmIdentity> bpmIdentities = UserAssignRuleQueryHelper.query(userAssignRules, pluginSession);

        List<BpmIdentity> identitieList = new ArrayList<BpmIdentity>();
        if (BeanUtils.isNotEmpty(bpmIdentities)) {
            IPartyUserService defaultUserService = AppUtil.getBean(IPartyUserService.class);
            for (BpmIdentity identity : bpmIdentities) {
                if (identity == null) {
                    continue;
                }
                if (BeanUtils.isNotEmpty(identity.getType())
                        && BpmIdentity.TYPE_USER.equals(identity.getType())
                        && defaultUserService.isDeleted(identity.getId())) {
                    continue;
                }
                identitieList.add(identity);
            }
        }

        return identitieList;
    }

    private List<UserAssignRule> getUserAssignRules(IBpmPluginDefine pluginDef) {
        List<UserAssignRule> userAssignRules = null;

        if (pluginDef instanceof UserAssignPluginDefine) {
            UserAssignPluginDefine assignPluginDef = (UserAssignPluginDefine) pluginDef;
            userAssignRules = assignPluginDef.getRuleList();
        }

        return userAssignRules;
    }

    @Override
    public List<BpmIdentity> queryByPluginDef(
            BpmUserCalcPluginSession pluginSession, IBpmPluginDefine pluginDef) {
        return null;
    }

}
