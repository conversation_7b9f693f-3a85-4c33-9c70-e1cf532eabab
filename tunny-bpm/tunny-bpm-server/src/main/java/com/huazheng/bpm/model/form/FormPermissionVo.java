package com.huazheng.bpm.model.form;

import java.util.HashMap;
import java.util.Map;


/**
 * 表单权限VO。
 *
 * <pre>
 *
 * 构建组：ibps-api-form
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年11月5日-下午4:46:29
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class FormPermissionVo {

    public static final String FLOW_KEY = "flowKey";
    public static final String NODE_ID = "nodeId";
    public static final String PARENT_FLOW_KEY = "parentFlowKey";

    public static final String RIGHTS_KEY = "rightsKey";

    private String userId; // 用户ID
    private String formKey; // 表单KEY
    private RightsScope rightsScope;// 权限范围
    private Map<String, String> rightsMap = new HashMap<String, String>();

    public FormPermissionVo() {
        super();
    }

    public FormPermissionVo(RightsScope rightsScope, String userId, String formKey, Map<String, String> rightsMap) {
        super();
        this.rightsScope = rightsScope;
        this.userId = userId;
        this.formKey = formKey;
        this.rightsMap = rightsMap;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFormKey() {
        return formKey;
    }

    public void setFormKey(String formKey) {
        this.formKey = formKey;
    }

    public RightsScope getRightsScope() {
        return rightsScope;
    }

    public void setRightsScope(RightsScope rightsScope) {
        this.rightsScope = rightsScope;
    }

    public Map<String, String> getRightsMap() {
        return rightsMap;
    }

    public void setRightsMap(Map<String, String> rightsMap) {
        this.rightsMap = rightsMap;
    }
}
