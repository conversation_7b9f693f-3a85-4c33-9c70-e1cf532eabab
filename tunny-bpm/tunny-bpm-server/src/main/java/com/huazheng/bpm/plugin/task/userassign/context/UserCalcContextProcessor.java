package com.huazheng.bpm.plugin.task.userassign.context;

import com.huazheng.bpm.plugin.IPluginParser;
import com.huazheng.bpm.plugin.IUserCalcPluginContext;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import net.sf.json.processors.JsonBeanProcessor;

public class UserCalcContextProcessor implements JsonBeanProcessor {

    @Override
    public JSONObject processBean(Object obj, JsonConfig config) {
        if (!(obj instanceof IUserCalcPluginContext)) {
            return new JSONObject(true);
        }
        IUserCalcPluginContext context = (IUserCalcPluginContext) obj;
        return JSONObject.fromObject(context.getBpmPluginDefine(), config)
                .element("pluginType", ((IPluginParser) context).getType())
                .element("description", context.getDescription());
    }

}
