package com.huazheng.bpm.model.node;

import com.huazheng.bpm.entity.constant.DecideType;
import com.huazheng.bpm.entity.constant.FollowMode;
import com.huazheng.bpm.entity.constant.VoteType;
import net.sf.json.JSONObject;

import java.io.Serializable;
import java.util.HashMap;

/**
 * 会签规则
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午12:40:07
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class SignRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 决策类型。
     * 同意，拒绝。
     */
    private DecideType decideType = DecideType.REFUSE;

    /**
     * 投票类型
     * 票数，百分比。
     */
    private VoteType voteType = VoteType.AMOUNT;

    /**
     * 后续处理模式。
     */
    private FollowMode followMode = FollowMode.COMPLETE;

    /**
     * 票数。
     */
    private int voteAmount = 1;

    public SignRule() {
    }

    public SignRule(DecideType decideType, VoteType voteType, FollowMode followMode, int voteAmount) {
        this.decideType = decideType;
        this.voteType = voteType;
        this.followMode = followMode;
        this.voteAmount = voteAmount;
    }

    public DecideType getDecideType() {
        return decideType;
    }

    public void setDecideType(DecideType decideType) {
        this.decideType = decideType;
    }

    public VoteType getVoteType() {
        return voteType;
    }

    public void setVoteType(VoteType voteType) {
        this.voteType = voteType;
    }

    public FollowMode getFollowMode() {
        return followMode;
    }

    public void setFollowMode(FollowMode followMode) {
        this.followMode = followMode;
    }

    public int getVoteAmount() {
        return voteAmount;
    }

    public void setVoteAmount(int voteAmount) {
        this.voteAmount = voteAmount;
    }

    public static String toJson(SignRule signRule) {
        if (signRule == null) {
            return "{}";
        }
        HashMap<String, Object> map = new HashMap<String, Object>();
        map.put("voteAmount", signRule.getVoteAmount());
        map.put("followMode", signRule.getFollowMode().getKey());
        map.put("voteType", signRule.getVoteType().getKey());
        map.put("decideType", signRule.getDecideType().getKey());
        return JSONObject.fromObject(map).toString();
    }
}
