package com.huazheng.bpm.entity.bpm;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huazheng.bpm.entity.framework.AbstractPo;
import com.huazheng.bpm.model.define.IBpmDefine;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.core.StringPool;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 流程定义 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2016-12-09 16:33:01
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmDefineTbl extends AbstractPo<String> implements IBpmDefine, Cloneable {
    protected String defId;        /*流程定义ID*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmDefineTbl.name")
    protected String name;        /*流程名称*/
    @NotBlank(message = "com.lc.ibps.bpmn.provider.public.defKey")
    protected String defKey;        /*流程业务主键*/
    protected String desc;        /*流程描述*/
    protected String typeId;        /*所属分类ID*/
    protected String status;        /*流程状态。草稿、发布、禁用*/
    protected String testStatus;        /*测试状态*/
    protected String bpmnDefId;        /*BPMN - 流程定义ID*/
    protected String bpmnDeployId;        /*BPMN - 流程发布ID*/
    protected Integer version;        /*版本 - 当前版本号*/
    protected String mainDefId;        /*版本 - 主版本流程ID*/
    protected String isMain;        /*版本 - 是否主版本*/
    protected String reason;        /*版本 - 变更理由*/
    protected String createBy;        /*创建人ID*/
    @JsonFormat(pattern = StringPool.DATE_FORMAT_DATETIME)
    protected java.util.Date createTime;        /*创建时间*/
    protected String updateBy;        /*更新人ID*/
    @JsonFormat(pattern = StringPool.DATE_FORMAT_DATETIME)
    protected java.util.Date updateTime;        /*更新时间*/
    protected String note;        /*备注*/
    protected String system;    /*系统标识*/
    protected BpmDefineXmlPo bpmDefineXmlPo = new BpmDefineXmlPo();

    @Override
    public void setId(String defId) {
        this.defId = defId;
    }

    @Override
    public String getId() {
        return defId;
    }

    public void setDefId(String defId) {
        this.defId = defId;
    }

    /**
     * 返回 流程定义ID
     *
     * @return
     */
    public String getDefId() {
        return this.defId;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 返回 流程名称
     *
     * @return
     */
    public String getName() {
        return this.name;
    }

    public void setDefKey(String defKey) {
        this.defKey = defKey;
    }

    /**
     * 返回 流程业务主键
     *
     * @return
     */
    public String getDefKey() {
        return this.defKey;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     * 返回 流程描述
     *
     * @return
     */
    public String getDesc() {
        return this.desc;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    /**
     * 返回 所属分类ID
     *
     * @return
     */
    public String getTypeId() {
        return this.typeId;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 返回 流程状态。草稿、发布、禁用
     *
     * @return
     */
    public String getStatus() {
        return this.status;
    }

    public void setTestStatus(String testStatus) {
        this.testStatus = testStatus;
    }

    /**
     * 返回 测试状态
     *
     * @return
     */
    public String getTestStatus() {
        return this.testStatus;
    }

    public void setBpmnDefId(String bpmnDefId) {
        this.bpmnDefId = bpmnDefId;
    }

    /**
     * 返回 BPMN - 流程定义ID
     *
     * @return
     */
    public String getBpmnDefId() {
        return this.bpmnDefId;
    }

    public void setBpmnDeployId(String bpmnDeployId) {
        this.bpmnDeployId = bpmnDeployId;
    }

    /**
     * 返回 BPMN - 流程发布ID
     *
     * @return
     */
    public String getBpmnDeployId() {
        return this.bpmnDeployId;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    /**
     * 返回 版本 - 当前版本号
     *
     * @return
     */
    public Integer getVersion() {
        return this.version;
    }

    public void setMainDefId(String mainDefId) {
        this.mainDefId = mainDefId;
    }

    /**
     * 返回 版本 - 主版本流程ID
     *
     * @return
     */
    public String getMainDefId() {
        return this.mainDefId;
    }

    public void setIsMain(String isMain) {
        this.isMain = isMain;
    }

    /**
     * 返回 版本 - 是否主版本
     *
     * @return
     */
    public String getIsMain() {
        return this.isMain;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    /**
     * 返回 版本 - 变更理由
     *
     * @return
     */
    public String getReason() {
        return this.reason;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     * 返回 创建人ID
     *
     * @return
     */
    public String getCreateBy() {
        return this.createBy;
    }

    public void setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 返回 创建时间
     *
     * @return
     */
    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 返回 更新人ID
     *
     * @return
     */
    public String getUpdateBy() {
        return this.updateBy;
    }

    public void setUpdateTime(java.util.Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 返回 更新时间
     *
     * @return
     */
    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("defId", this.defId)
                .append("name", this.name)
                .append("defKey", this.defKey)
                .append("desc", this.desc)
                .append("typeId", this.typeId)
                .append("status", this.status)
                .append("testStatus", this.testStatus)
                .append("bpmnDefId", this.bpmnDefId)
                .append("bpmnDeployId", this.bpmnDeployId)
                .append("version", this.version)
                .append("mainDefId", this.mainDefId)
                .append("isMain", this.isMain)
                .append("reason", this.reason)
                .append("createBy", this.createBy)
                .append("createTime", this.createTime)
                .append("updateBy", this.updateBy)
                .append("updateTime", this.updateTime)
                .append("system", this.system)
                .toString();
    }

    public BpmDefineXmlPo getBpmDefineXmlPo() {
        return bpmDefineXmlPo;
    }

    public void setBpmDefineXmlPo(BpmDefineXmlPo bpmDefineXmlPo) {
        this.bpmDefineXmlPo = bpmDefineXmlPo;
    }

    /* (non-Javadoc)
     * @see com.huazheng.bpm.api.bpmn.model.define.IBpmDefine#getDefXml()
     */
    @Override
    public String getDefXml() {
        if (BeanUtils.isEmpty(bpmDefineXmlPo)) return "";
        return bpmDefineXmlPo.defXml;
    }

    /* (non-Javadoc)
     * @see com.huazheng.bpm.api.bpmn.model.define.IBpmDefine#getBpmnXml()
     */
    @Override
    public String getBpmnXml() {
        if (BeanUtils.isEmpty(bpmDefineXmlPo)) return "";
        return bpmDefineXmlPo.bpmnXml;
    }

    public void setDefXml(String defXml) {
        if (BeanUtils.isEmpty(bpmDefineXmlPo)) bpmDefineXmlPo = new BpmDefineXmlPo();
        bpmDefineXmlPo.defXml = defXml;
    }

    public void setBpmnXml(String bpmnXml) {
        if (BeanUtils.isEmpty(bpmDefineXmlPo)) bpmDefineXmlPo = new BpmDefineXmlPo();
        bpmDefineXmlPo.bpmnXml = bpmnXml;
    }

    /* (non-Javadoc)
     * @see com.huazheng.bpm.api.bpmn.model.define.IBpmDefine#isMain()
     */
    @Override
    public boolean isMain() {
        return StringPool.Y == this.isMain;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#clone()
     */
    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }
}
