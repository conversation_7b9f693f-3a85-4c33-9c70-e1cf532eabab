package com.huazheng.bpm.entity.bpm;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huazheng.bpm.entity.framework.AbstractPo;
import com.huazheng.bpm.util.core.StringPool;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.hibernate.validator.constraints.NotBlank;


/**
 * 常用语 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：zhongjh
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-10-28 15:34:46
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmCommonStatmentTbl extends AbstractPo<String> {
    protected String id;        /*主键*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmCommonStatmentTbl.value")
    protected String value;        /*内容*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmCommonStatmentTbl.action")
    protected String action;        /*动作类型*/
    protected String isDefault;        /*是否默认*/
    protected String createBy;        /*创建人ID*/
    @JsonFormat(pattern = StringPool.DATE_FORMAT_DATETIME)
    protected java.util.Date createTime;        /*创建时间*/
    protected Integer times = 0;        /*使用次数*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setValue(String value) {
        this.value = value;
    }

    /**
     * 返回 内容
     *
     * @return
     */
    public String getValue() {
        return this.value;
    }

    public void setAction(String action) {
        this.action = action;
    }

    /**
     * 返回 动作类型
     *
     * @return
     */
    public String getAction() {
        return this.action;
    }

    public void setIsDefault(String isDefault) {
        this.isDefault = isDefault;
    }

    /**
     * 返回 是否默认
     *
     * @return
     */
    public String getIsDefault() {
        return this.isDefault;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     * 返回 创建人ID
     *
     * @return
     */
    public String getCreateBy() {
        return this.createBy;
    }

    public void setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 返回 创建时间
     *
     * @return
     */
    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public void setTimes(Integer times) {
        this.times = times;
    }

    /**
     * 返回 使用次数
     *
     * @return
     */
    public Integer getTimes() {
        return this.times;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("value", this.value)
                .append("action", this.action)
                .append("isDefault", this.isDefault)
                .append("createBy", this.createBy)
                .append("createTime", this.createTime)
                .append("times", this.times)
                .toString();
    }
}
