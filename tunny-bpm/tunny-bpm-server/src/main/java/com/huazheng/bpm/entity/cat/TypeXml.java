package com.huazheng.bpm.entity.cat;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * 分类导出对象
 *
 * <pre>
 * 构建组：ibps-common-biz
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年10月25日-下午7:33:01
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@XmlRootElement(name = "type")
@XmlAccessorType(XmlAccessType.NONE)
public class TypeXml {

    public static Short IS_CATE_YES = 1;
    public static Short IS_CATE_NO = 0;

    @XmlAttribute
    protected String name; /*分类名称*/
    @XmlAttribute
    protected String typeKey; /*节点的分类Key(在本分类树中唯一)*/
    @XmlAttribute
    protected String struType;/*结构类型。0=平铺结构；1=树型结构*/
    @XmlAttribute
    protected Short isCate;/* 根节点是否是分类标识节点，0=不是，1=是*/

    protected List<TypeXml> types;

    public TypeXml() {
        super();
    }

    public TypeXml(String name, String typeKey, String struType) {
        super();
        this.name = name;
        this.typeKey = typeKey;
        this.struType = struType;
        this.isCate = IS_CATE_NO;
    }

    public TypeXml(String name, String typeKey, String struType, Short isCate) {
        super();
        this.name = name;
        this.typeKey = typeKey;
        this.struType = struType;
        this.isCate = isCate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTypeKey() {
        return typeKey;
    }

    public void setTypeKey(String typeKey) {
        this.typeKey = typeKey;
    }

    public String getStruType() {
        return struType;
    }

    public void setStruType(String struType) {
        this.struType = struType;
    }


    public Short getIsCate() {
        return isCate;
    }

    public void setIsCate(Short isCate) {
        this.isCate = isCate;
    }

    public List<TypeXml> getTypes() {
        return types;
    }

    @XmlElement(name = "type")
    public void setTypes(List<TypeXml> types) {
        this.types = types;
    }

}
