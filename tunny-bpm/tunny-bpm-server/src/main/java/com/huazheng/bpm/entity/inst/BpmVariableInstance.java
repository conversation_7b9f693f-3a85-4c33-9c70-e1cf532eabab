package com.huazheng.bpm.entity.inst;

/**
 * 流程变量实例
 * 构建组：ibps-bpmn-api
 * 作者：simon cai
 * <EMAIL>
 * 日期:2013-11-14-下午4:30:46
 * 版权：广州流辰信息技术有限公司版权所有
 */
public interface BpmVariableInstance {
    /**
     * 变量实例ID
     *
     * @return String
     * @throws
     * @since 1.0.0
     */
    String getId();

    /**
     * 流程节点Id
     *
     * @return String
     * @throws
     * @since 1.0.0
     */
    String getNodeId();

    /**
     * 流程实例ID
     *
     * @return String
     * @throws
     * @since 1.0.0
     */
    String getProcInstId();

    /**
     * 流程定义Id
     *
     * @return String
     * @throws
     * @since 1.0.0
     */
    String getProcDefId();

    /**
     * 流程变量Key
     *
     * @return String
     * @throws
     * @since 1.0.0
     */
    String getVarKey();

    /**
     * 流程变量值
     *
     * @return Object
     * @throws
     * @since 1.0.0
     */
    Object getValue();
}
