//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vJAXB 2.1.3 in JDK 1.6
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2014.05.06 at 03:52:27 下午 CST
//


package com.huazheng.bpm.entity.core;

import com.huazheng.bpm.entity.procnotify.OnEnd;

import javax.xml.bind.annotation.*;


/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{http://www.bpmhome.cn/bpm/plugins/execution/procNotify}onEnd"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "onEnd"
})
@XmlRootElement(name = "procNotify")
public class ProcNotify {

    @XmlElement(required = true)
    protected OnEnd onEnd;

    /**
     * Gets the value of the onEnd property.
     *
     * @return possible object is
     * {@link OnEnd }
     */
    public OnEnd getOnEnd() {
        return onEnd;
    }

    /**
     * Sets the value of the onEnd property.
     *
     * @param value allowed object is
     *              {@link OnEnd }
     */
    public void setOnEnd(OnEnd value) {
        this.onEnd = value;
    }

}
