package com.huazheng.bpm.entity.bpm;

/**
 * 数据格式。
 *
 * <pre>
 *
 * 构建组：ibps-base-bo
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年5月27日-上午9:52:46
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum DataFormat {
    JSON("json"), // json
    XML("xml"); // xml
    private String value;

    DataFormat(String value) {
        this.value = value;
    }

    public String value() {
        return value;
    }

    public static DataFormat fromKey(String key) {
        for (DataFormat c : DataFormat.values()) {
            if (c.value().equalsIgnoreCase(key)) {
                return c;
            }
        }
        throw new IllegalArgumentException(key);
    }
}
