package com.huazheng.bpm.entity.constant;

/**
 * 流程授权常量参数
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月20日-上午11:46:03
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class BpmAuthContants {

    /**
     * 流程授权启动类型
     */
    public static final String START = "start";
    /**
     * 流程授权定义类型
     */
    public static final String MANAGE = "manage";
    /**
     * 流程授权任务类型
     */
    public static final String TASK = "task";
    /**
     * 流程授权实例类型
     */
    public static final String INSTANCE = "instance";

    public static final String AUTH_TYPE = "authType";
    public static final String AUTH_IDS = "authIds";
    public static final String AUTH_RIGHT_MAP = "authRightMap";
}
