package com.huazheng.bpm.domain;


import com.huazheng.bpm.dao.base.IDao;
import com.huazheng.bpm.entity.framework.AbstractPo;
import com.huazheng.bpm.entity.framework.PO;
import com.huazheng.bpm.util.helper.MapBuilder;
import com.huazheng.bpm.util.string.StringUtil;

import javax.annotation.Resource;
import java.io.Serializable;

@SuppressWarnings("serial")
public abstract class AbstractDomain<PK extends Serializable, P extends PO<PK>> implements Domain<PK, P>, Serializable {

    private P data;

    private IDao<PK, P> dao;
    @Resource
    private IdGenerator idGenerator;

    private boolean isNewFlag = false;

    public P getData() {
        return data;
    }

    public PK getId() {
        if (data != null) {
            return data.getId();
        }
        return null;
    }

    public void setData(P data_) {
        data = data_;
        init();
    }

    protected abstract void init();

    public void save() {
        onSave();
        if (isNew()) {
            if (((AbstractPo<?>) data).getId() == null || StringUtil.isEmpty(data.getId().toString())) {
                ((AbstractPo<?>) data).setId(idGenerator.getId());
            }
            create();
        } else {
            update();
        }
    }

    protected void onSave() {
    }

    protected boolean isNew() {
        if (isNewFlag) {
            return true;
        }
        boolean isNew = false;
        if (data.getId() == null) {
            isNew = true;
        } else if (data.getId() instanceof String) {
            if (StringUtil.isEmpty((String) data.getId())) {
                isNew = true;
            }
        }
        return isNew;
    }

    protected boolean isUpdate() {
        return !isNew();
    }

    public void create() {
        if (((AbstractPo<?>) data).getId() == null || StringUtil.isEmpty(data.getId().toString())) {
            ((AbstractPo<?>) data).setId(idGenerator.getId());
        }
        IDao<PK, P> dao = getDao();
        dao.create(data);
        //getDao().create(data);
    }

    public void update() {
        getDao().update(data);
    }

    public void delete() {
        getDao().delete(getId());
    }

    public void delete(PK id_) {
        init();
        getDao().delete(id_);
    }

    @SuppressWarnings("unchecked")
    public void deleteByIds(PK... ids_) {
        init();
        getDao().deleteByIds(ids_);
    }

    protected IDao<PK, P> getDao() {
        return dao;
    }

    protected void setDao(IDao<PK, P> dao) {
        this.dao = dao;
    }

    public IdGenerator getIdGenerator() {
        return idGenerator;
    }

    public boolean isNewFlag() {
        return isNewFlag;
    }

    public void setNewFlag(boolean isNewFlag) {
        this.isNewFlag = isNewFlag;
    }

    protected MapBuilder b() {
        return new MapBuilder();
    }
}
