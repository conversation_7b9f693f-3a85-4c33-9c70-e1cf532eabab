package com.huazheng.bpm.entity.inst;


/**
 * 流程实例抄送 entity对象。
 *
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2017年4月7日-下午8:07:33
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface BpmProCpto {


    /**
     * 返回 主键
     *
     * @return
     */
    String getId();

    /**
     * 返回 流程实例ID
     *
     * @return
     */
    String getInstId();

    /**
     * 返回 ACT实例ID
     *
     * @return
     */
    String getBpmnInstId();

    /**
     * 返回 节点ID
     *
     * @return
     */
    String getNodeId();


    /**
     * 返回 抄送时间
     *
     * @return
     */
    java.util.Date getCreateTime();

    /**
     * 返回 意见
     *
     * @return
     */
    String getOpinion();

    /**
     * 返回 流程实例标题
     *
     * @return
     */
    String getSubject();


    /**
     * 返回 抄送类型(copyto抄送,trans转发)
     *
     * @return
     */
    String getType();

    /**
     * 返回 流程发起人
     *
     * @return
     */
    String getStartorId();

    /**
     * 发起人。
     *
     * @return String
     */
    String getStartor();

    /**
     * 返回 流程分类
     *
     * @return
     */
    String getTypeId();

}
