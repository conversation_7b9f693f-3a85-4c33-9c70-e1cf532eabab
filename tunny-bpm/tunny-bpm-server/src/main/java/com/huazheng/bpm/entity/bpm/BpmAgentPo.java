package com.huazheng.bpm.entity.bpm;

import java.util.ArrayList;
import java.util.List;

/**
 * 流程代理 实体对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-03-30 17:29:13
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmAgentPo extends BpmAgentTbl {
    public static String ENABLED = "enabled";/*代理状态：启用*/
    public static String DISABLED = "disabled";/*代理状态：禁用*/

    public static String AGENT_TYPE_ALL = "all";/*代理类型：全权代理*/
    public static String AGENT_TYPE_PART = "part";/*代理类型：部分代理*/
    public static String AGENT_TYPE_CONDITION = "condition";/*代理类型：条件代理*/

    protected String delegatorName;/*委托人名称*/
    protected String agenterName;/*代理人名称*/
    protected String procDefId;/*流程id*/
    protected String procDefName;/*流程名称*/

    private boolean delBeforeSave = true;

    public boolean isDelBeforeSave() {
        return delBeforeSave;
    }

    public void setDelBeforeSave(boolean delBeforeSave) {
        this.delBeforeSave = delBeforeSave;
    }

    private List<BpmAgentDefPo> bpmAgentDefPoList = new ArrayList<BpmAgentDefPo>();

    public List<BpmAgentDefPo> getBpmAgentDefPoList() {
        return bpmAgentDefPoList;
    }

    public void setBpmAgentDefPoList(List<BpmAgentDefPo> bpmAgentDefPoList) {
        this.bpmAgentDefPoList = bpmAgentDefPoList;
    }

    private List<BpmAgentConditionPo> bpmAgentConditionPoList = new ArrayList<BpmAgentConditionPo>();

    public List<BpmAgentConditionPo> getBpmAgentConditionPoList() {
        return bpmAgentConditionPoList;
    }

    public void setBpmAgentConditionPoList(List<BpmAgentConditionPo> bpmAgentConditionPoList) {
        this.bpmAgentConditionPoList = bpmAgentConditionPoList;
    }

    public String getDelegatorName() {
        return delegatorName;
    }

    public void setDelegatorName(String delegatorName) {
        this.delegatorName = delegatorName;
    }

    public String getAgenterName() {
        return agenterName;
    }

    public void setAgenterName(String agenterName) {
        this.agenterName = agenterName;
    }

    public String getProcDefName() {
        return procDefName;
    }

    public void setProcDefName(String procDefName) {
        this.procDefName = procDefName;
    }

    public String getProcDefId() {
        return procDefId;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }
}
