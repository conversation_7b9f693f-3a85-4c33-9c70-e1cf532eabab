package com.huazheng.bpm.model.define;


import com.huazheng.bpm.plugin.IBpmPluginContext;
import com.huazheng.bpm.plugin.IProcInstAopPluginContext;

import java.io.Serializable;
import java.util.List;

/**
 * BPMN流程定义扩展接口
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月7日-下午5:42:06
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface IBpmProcExtendDefine extends Serializable {

    /**
     * 流程定义扩展插件。
     *
     * @return List&lt;IBpmPluginContext>
     */
    List<IBpmPluginContext> getBpmPluginContextList();

    IBpmPluginContext getBpmPluginContext(Class<?> clazz);

    /**
     * 流程AOP插件。
     *
     * @return List&lt;ProcessInstAopPluginContext>
     */
    List<IProcInstAopPluginContext> getProcInstAopPluginContextList();

    /**
     * 流程定义扩展属性。
     *
     * @return BpmDefineExtendProperties
     */
    BpmDefineAttributes getExtendAttributes();

}
