package com.huazheng.bpm.model.form;


import com.huazheng.bpm.entity.framework.AbstractPo;
import com.huazheng.bpm.util.string.StringUtil;

/**
 * 设置表单类
 *
 * <pre>
 *
 * 构建组：ibps-bpmn-biz
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-上午10:26:01
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class DefaultForm extends AbstractPo<String> implements IFormModel {
    private static final long serialVersionUID = 1L;
    private String nodeId; /* 节点ID */
    private String parentFlowKey; /* 父节点流程key */
    private FormCategory type; /* 设置表单 */
    private String formValue; /* 表单值 */
    private String editUrl; /* 编辑URL地址 */

    private String id; /* 主键 */
    private String name; /* 表单名称 */
    private String key; /* 表单业务主键 */
    private Integer version; /* 表单版本 */
    private String formData; /* 表单数据 */

    private String templateId; /* 打印模版ID */
    private String templateName; /* 打印模版名称 */

    public DefaultForm() {
        super();
    }

    public DefaultForm(IForm frm) {
        nodeId = frm.getNodeId();
        name = frm.getName();
        key = frm.getKey();
        version = frm.getVersion();
        parentFlowKey = frm.getParentFlowKey();
        type = frm.getType();
        formValue = frm.getFormValue();
        editUrl = frm.getEditUrl();
        templateId = frm.getTemplateId();
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getParentFlowKey() {
        if (StringUtil.isEmpty(parentFlowKey)) {
            return IForm.LOCAL;
        }
        return parentFlowKey;
    }

    public void setParentFlowKey(String parentFlowKey) {
        this.parentFlowKey = parentFlowKey;
    }

    public FormCategory getType() {
        return type;
    }

    public void setType(FormCategory type) {
        this.type = type;
    }

    public String getFormValue() {
        return formValue;
    }

    public void setFormValue(String formValue) {
        this.formValue = formValue;
    }

    public String getEditUrl() {
        return editUrl;
    }

    public void setEditUrl(String editUrl) {
        this.editUrl = editUrl;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getFormData() {
        return formData;
    }

    public void setFormData(String formData) {
        this.formData = formData;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.huazheng.bpm.api.bpmn.model.form.IForm#getVersion()
     */
    @Override
    public Integer getVersion() {
        return version;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.huazheng.bpm.api.bpmn.model.form.IForm#setVersion(java.lang.Integer)
     */
    @Override
    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

}
