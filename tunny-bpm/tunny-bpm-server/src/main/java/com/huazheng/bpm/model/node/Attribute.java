package com.huazheng.bpm.model.node;

import java.io.Serializable;

/**
 * 流程属性
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-上午9:49:57
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class Attribute implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    private String name = "";

    /**
     * 值
     */
    private String value = "";

    public Attribute() {
    }

    public Attribute(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

}
