package com.huazheng.bpm.entity.bpm;


import com.huazheng.bpm.entity.rights.RightsDefPo;

import java.util.ArrayList;
import java.util.List;

/**
 * 流程分管授权 实体对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-02-06 15:02:10
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmAuthPo extends BpmAuthTbl {

    /**
     * 授权对象列表
     */
    protected List<RightsDefPo> rightsOwnerList = new ArrayList<RightsDefPo>();

    /**
     * 授权流程列表
     */
    protected List<BpmAuthDefPo> rightsDefList = new ArrayList<BpmAuthDefPo>();

    /**
     * 授权对象名称(仅用于存放授权对象的JSON数据)
     * <p>
     * { "allJson":"N", "user":"[
     * {\"ownerId\":\"10000011230209\",\"ownerName\":\"审查B\"},
     * {\"ownerId\":\"10000011230207\",\"ownerName\":\"审查C\"} ]", "role":"[
     * {\"ownerId\":\"10000000080083\",\"ownerName\":\"角色管理员\"},
     * {\"ownerId\":\"10000000440185\",\"ownerName\":\"组织负责人\"} ]", "orgJson":"[
     * {\"ownerId\":\"10000011230194\",\"ownerName\":\"采购组织B\"} ]",
     * "grantJson":"[ {\"ownerId\":\"10000016090060\",\"ownerName\":\"广州分公司\"}
     * ]", "positionJson":"[
     * {\"ownerId\":\"10000011230203\",\"ownerName\":\"采购组织A_采购专员\"},
     * {\"ownerId\":\"37\",\"ownerName\":\"广州宏天白云区分公司销售组织_宏天软件工程师\"} ]" }
     */
    protected String rightsOwner;

    /**
     * 授权流程名称(仅用于存放授权流程的JSON数据)
     * <p>
     * {"defArry": [{ "defId":"10000018130014", "defKey":"zchz",
     * "defName":"周程汇总", "right":{"edit":"N","del":"N","start":"N","set":"N"} },
     * {"defId":"10000017980009", "defKey":"csjdsz", "defName":"测试节点设置",
     * "right":{"edit":"N","del":"N","start":"N","set":"N"} },
     * {"defId":"10000017860008", "defKey":"gxzlc", "defName":"共享子流程",
     * "right":{"edit":"N","del":"N","start":"N","set":"N"}}] }
     */
    protected String rightsDef;

    public List<RightsDefPo> getRightsOwnerList() {
        return rightsOwnerList;
    }

    public void setRightsOwnerList(List<RightsDefPo> rightsOwnerList) {
        this.rightsOwnerList = rightsOwnerList;
    }

    public List<BpmAuthDefPo> getRightsDefList() {
        return rightsDefList;
    }

    public void setRightsDefList(List<BpmAuthDefPo> rightsDefList) {
        this.rightsDefList = rightsDefList;
    }

    public String getRightsOwner() {
        return rightsOwner;
    }

    public void setRightsOwner(String rightsOwner) {
        this.rightsOwner = rightsOwner;
    }

    public String getRightsDef() {
        return rightsDef;
    }

    public void setRightsDef(String rightsDef) {
        this.rightsDef = rightsDef;
    }
}
