package com.huazheng.bpm.plugin;


import com.huazheng.bpm.plugin.factory.BpmPluginSessionFactory;
import com.huazheng.bpm.util.core.AppUtil;

/**
 * TODO（用一句话描述这个类做什么用）。
 * <pre>
 * 构建组：ibps-bpmn-plugin-base
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月14日-上午9:29:13
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public abstract class AbstractBpmTaskPlugin extends AbstractBpmPlugin implements IBpmTaskPlugin {
    private BpmPluginSessionFactory bpmPluginSessionFactory;

    public BpmPluginSessionFactory getBpmPluginSessionFactory() {
        if (bpmPluginSessionFactory == null) {
            this.bpmPluginSessionFactory = AppUtil.getBean(BpmPluginSessionFactory.class);
        }
        return this.bpmPluginSessionFactory;
    }
}
