package com.huazheng.bpm.model.node;


import com.huazheng.bpm.entity.constant.PrivilegeMode;

import java.io.Serializable;
import java.util.List;

/**
 * 特权对象定义
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午12:38:01
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class PrivilegeItem implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    public PrivilegeItem() {
    }

    public PrivilegeItem(PrivilegeMode privilegeMode, List<UserAssignRule> userRuleList) {
        this.privilegeMode = privilegeMode;
        this.userRuleList = userRuleList;
    }

    /**
     * 特权模式
     */
    private PrivilegeMode privilegeMode;

    /**
     * 对应的成员插件。
     */
    private List<UserAssignRule> userRuleList;

    public PrivilegeMode getPrivilegeMode() {
        return privilegeMode;
    }

    public void setPrivilegeMode(PrivilegeMode privilegeMode) {
        this.privilegeMode = privilegeMode;
    }

    public List<UserAssignRule> getUserRuleList() {
        return userRuleList;
    }

    public void setUserRuleList(List<UserAssignRule> userRuleList) {
        this.userRuleList = userRuleList;
    }

}
