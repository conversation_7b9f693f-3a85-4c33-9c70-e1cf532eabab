package com.huazheng.bpm.entity.bpm;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huazheng.bpm.entity.framework.AbstractPo;
import com.huazheng.bpm.util.core.StringPool;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.hibernate.validator.constraints.NotBlank;

import java.util.Date;

/**
 * 流程通知 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-03-28 09:43:38
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmOperNotifyTbl extends AbstractPo<String> {
    protected String id;        /*主键*/
    @NotBlank(message = "com.lc.ibps.bpmn.provider.public.procDefId")
    protected String procDefId;        /*流程定义ID*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmOperNotifyTbl.procInstId")
    protected String procInstId;        /*流程实例ID*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmOperNotifyTbl.bpmnDefId")
    protected String bpmnDefId;        /*ACT定义ID*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmOperNotifyTbl.bpmnInstId")
    protected String bpmnInstId;        /*ACT实例ID*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmOperNotifyTbl.nodeId")
    protected String nodeId;        /*节点ID*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmOperNotifyTbl.notifyTitle")
    protected String notifyTitle;        /*通知标题*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmOperNotifyTbl.notifyType")
    protected String notifyType;        /*通知类型：cc-抄送、bcc-密送、fw-转发*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmOperNotifyTbl.notifyContent")
    protected String notifyContent;        /*通知内容*/
    @NotBlank(message = "com.lc.ibps.bpmn.persistence.entity.BpmOperNotifyTbl.notifyHtmlContent")
    protected String notifyHtmlContent;        /*通知内容富文本*/
    protected String notifier;        /*通知人*/
    @JsonFormat(pattern = StringPool.DATE_FORMAT_DATETIME)
    protected Date createTime;        /*通知时间*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    /**
     * 返回 流程定义ID
     *
     * @return
     */
    public String getProcDefId() {
        return this.procDefId;
    }

    public void setProcInstId(String procInstId) {
        this.procInstId = procInstId;
    }

    /**
     * 返回 流程实例ID
     *
     * @return
     */
    public String getProcInstId() {
        return this.procInstId;
    }

    public void setBpmnDefId(String bpmnDefId) {
        this.bpmnDefId = bpmnDefId;
    }

    /**
     * 返回 ACT定义ID
     *
     * @return
     */
    public String getBpmnDefId() {
        return this.bpmnDefId;
    }

    public void setBpmnInstId(String bpmnInstId) {
        this.bpmnInstId = bpmnInstId;
    }

    /**
     * 返回 ACT实例ID
     *
     * @return
     */
    public String getBpmnInstId() {
        return this.bpmnInstId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    /**
     * 返回 节点ID
     *
     * @return
     */
    public String getNodeId() {
        return this.nodeId;
    }

    public void setNotifyTitle(String notifyTitle) {
        this.notifyTitle = notifyTitle;
    }

    /**
     * 返回 通知标题
     *
     * @return
     */
    public String getNotifyTitle() {
        return this.notifyTitle;
    }

    public void setNotifyType(String notifyType) {
        this.notifyType = notifyType;
    }

    /**
     * 返回 通知类型：cc-抄送、bcc-密送、fw-转发
     *
     * @return
     */
    public String getNotifyType() {
        return this.notifyType;
    }

    public void setNotifyContent(String notifyContent) {
        this.notifyContent = notifyContent;
    }

    /**
     * 返回 通知内容
     *
     * @return
     */
    public String getNotifyContent() {
        return this.notifyContent;
    }

    public void setNotifyHtmlContent(String notifyHtmlContent) {
        this.notifyHtmlContent = notifyHtmlContent;
    }

    /**
     * 返回 通知内容
     *
     * @return
     */
    public String getNotifyHtmlContent() {
        return this.notifyHtmlContent;
    }

    public void setNotifier(String notifier) {
        this.notifier = notifier;
    }

    /**
     * 返回 通知人
     *
     * @return
     */
    public String getNotifier() {
        return this.notifier;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 返回 通知时间
     *
     * @return
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("procDefId", this.procDefId)
                .append("procInstId", this.procInstId)
                .append("bpmnDefId", this.bpmnDefId)
                .append("bpmnInstId", this.bpmnInstId)
                .append("nodeId", this.nodeId)
                .append("notifyTitle", this.notifyTitle)
                .append("notifyType", this.notifyType)
                .append("notifyContent", this.notifyContent)
                .append("notifier", this.notifier)
                .append("createTime", this.createTime)
                .toString();
    }
}
