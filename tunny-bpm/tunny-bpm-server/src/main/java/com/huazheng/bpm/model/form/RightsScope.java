package com.huazheng.bpm.model.form;

import org.apache.commons.lang.StringUtils;

/**
 * * 权限级别。
 *
 * <pre>
 *
 * 构建组：ibps-api-form
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年11月11日-下午3:27:59
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum RightsScope {

    FORM("form", "表单权限"),

    FLOW("flow", "流程权限"),

    NODE("node", "节点权限"),

    INST("inst", "实例权限"),

    BIZ("biz", "业务数据模版"),

    DATA("data", "数据模版");

    private String key;
    private String label;

    RightsScope(String key, String label) {
        this.key = key;
        this.label = label;
    }

    public String key() {
        return key;
    }

    public String label() {
        return label;
    }

    /**
     * 通过key获取对象
     *
     * @param key
     * @return
     */
    public static RightsScope fromKey(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        for (RightsScope c : RightsScope.values()) {
            if (c.key().equalsIgnoreCase(key)) {
                return c;
            }
        }
        return null;
    }
}
