package com.huazheng.bpm.model.form;

/**
 * 表单类型枚举
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-上午10:28:35
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum FormCategory {
    INNER("inner"), /*在线表单*/
    URL_LOAD("urlLoad"), /*url表单*/
    FRAME("frame"); /*嵌套表单*/

    private final String value;

    FormCategory(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static FormCategory get(String v) {
        for (FormCategory c : FormCategory.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
