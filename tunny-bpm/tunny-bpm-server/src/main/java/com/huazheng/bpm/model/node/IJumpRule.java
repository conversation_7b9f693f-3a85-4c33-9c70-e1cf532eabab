package com.huazheng.bpm.model.node;

import java.io.Serializable;

/**
 * 跳转规则接口
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午12:03:49
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface IJumpRule extends Serializable {

    /**
     * 规则名称。
     *
     * @return String
     * @throws
     * @since 1.0.0
     */
    String getRuleName();

    /**
     * 跳转到的节点。
     *
     * @return String
     * @throws
     * @since 1.0.0
     */
    String getTargetNode();

    /**
     * 规则的条件。
     *
     * @return String
     * @throws
     * @since 1.0.0
     */
    String getCondition();

}
