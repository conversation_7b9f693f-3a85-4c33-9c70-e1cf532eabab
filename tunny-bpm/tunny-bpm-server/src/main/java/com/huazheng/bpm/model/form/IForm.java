package com.huazheng.bpm.model.form;

/**
 * 表单接口类
 *
 * <pre>
 * 作者：HuangYx
 * 邮箱:<EMAIL>
 * 日期:2015-09-20-下午3:29:31
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface IForm {
    /**
     * 本地标识符
     */
    String LOCAL = "local_";

    /**
     * 获取表单所绑定的节点
     *
     * @return
     */
    String getNodeId();

    /**
     * 设置表单所绑定的节点
     *
     * @param nodeId
     */
    void setNodeId(String nodeId);

    /**
     * 获取父流程KEY
     *
     * @return
     */
    String getParentFlowKey();

    /**
     * 设置父流程KEY
     *
     * @param parentFlowKey
     */
    void setParentFlowKey(String parentFlowKey);

    /**
     * 获取表单业务键
     *
     * @return
     */
    String getKey();

    /**
     * 设置表单业务键
     *
     * @param key
     */
    void setKey(String key);

    /**
     * 获取表单版本
     *
     * @return
     */
    Integer getVersion();

    /**
     * 设置表单版本
     *
     * @param version
     */
    void setVersion(Integer version);

    /**
     * 获取表单名称
     *
     * @return
     */
    String getName();

    /**
     * 设置表单名称
     *
     * @param name
     */
    void setName(String name);

    /**
     * 获取表单分类
     *
     * @return
     */
    FormCategory getType();

    /**
     * 设置表单分类
     *
     * @param type
     */
    void setType(FormCategory type);

    /**
     * 获取表单值
     *
     * <pre>
     * 不同类型的表单该字段的值不一样：
     * 1、INNER（在线表单）：存放表单key
     * 2、URL_LOAD（使用jquery的load方式加载的表单）：存放表单url
     * 3、FRAME（以iframe方式嵌入的表单）：存放表单的url
     * </pre>
     *
     * @return
     */
    String getFormValue();

    /**
     * 设置表单值
     *
     * @param formValue
     */
    void setFormValue(String formValue);

    /**
     * 获取FRAME表单的编辑地址
     *
     * @return
     */
    String getEditUrl();

    /**
     * 设置FRAME表单的编辑地址
     *
     * @param editUrl
     */
    void setEditUrl(String editUrl);

    /**
     * 设置打印模版ID
     *
     * @return
     */
    String getTemplateId();

    /**
     * 设置打印模版ID
     *
     * @return
     */
    void setTemplateId(String templateId);

    /**
     * 获取打印模版名称
     *
     * @return
     */
    String getTemplateName();

    /**
     * 设置打印模版名称
     *
     * @return
     */
    void setTemplateName(String templateName);

}
