package com.huazheng.bpm.model.base;

import com.huazheng.bpm.model.define.IBpmVariableDefine;
import com.huazheng.bpm.util.time.DateFormatUtil;
import org.apache.commons.lang.StringUtils;

/**
 * 流程定义中的变量定义
 * <pre>
 * 构建组：ibps-bpmn-biz
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月10日-下午5:41:55
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmVariableDefine implements IBpmVariableDefine {

    private String nodeId = "";
    //变量名
    private String name = "";
    //变量Key
    private String key = "";
    //数据类型
    private String dataType = "";
    //缺省值
    private Object defaultVal = "";
    //是否必需
    private boolean isRequired = false;
    //变量描述
    private String description = "";

    public BpmVariableDefine() {
    }

    public static Object getValue(String dataType, String defaultVal) {
        if (IBpmVariableDefine.VAR_TYPE_DOUBLE.equals(dataType)) {
            return new Double(StringUtils.isNotEmpty(defaultVal) ? defaultVal : "0");
        } else if (IBpmVariableDefine.VAR_TYPE_FLOAT.equals(dataType)) {
            return new Float(StringUtils.isNotEmpty(defaultVal) ? defaultVal : "0");
        } else if (IBpmVariableDefine.VAR_TYPE_INT.equals(dataType)) {
            return new Integer(StringUtils.isNotEmpty(defaultVal) ? defaultVal : "0");
        } else if (IBpmVariableDefine.VAR_TYPE_DATE.equals(dataType)) {
            return DateFormatUtil.parse(defaultVal, "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd");
        }

        return defaultVal;
    }

    public BpmVariableDefine(String name, String key, String dataType,
                             String defaultVal, boolean isRequired, String description) {
        this.name = name;
        this.key = key;
        this.dataType = dataType;
        this.defaultVal = getValue(dataType, defaultVal);
        this.isRequired = isRequired;
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public Object getDefaultVal() {
        return defaultVal;
    }

    public void setDefaultVal(Object defaultVal) {
        this.defaultVal = defaultVal;
    }

    public void setDefaultVal(String defaulVal2) {
        this.defaultVal = getValue(dataType, defaulVal2);
    }

    public boolean isRequired() {
        return isRequired;
    }

    public void setRequired(boolean isRequired) {
        this.isRequired = isRequired;
    }

    public String getDescription() {
        return description == null ? "" : description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String getNodeId() {
        return this.nodeId;
    }

    @Override
    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }
}
