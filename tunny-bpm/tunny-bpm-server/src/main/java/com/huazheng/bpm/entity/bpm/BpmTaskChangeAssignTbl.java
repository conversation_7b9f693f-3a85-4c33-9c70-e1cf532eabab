package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * 任务变更候选人 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-04-11 19:45:11
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmTaskChangeAssignTbl extends AbstractPo<String> {
    protected String id;        /*主键*/
    protected String taskChangeId;        /*任务变更ID*/
    protected String type;        /*候选人类型*/
    protected String executor;        /*执行人ID*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setTaskChangeId(String taskChangeId) {
        this.taskChangeId = taskChangeId;
    }

    /**
     * 返回 任务变更ID
     *
     * @return
     */
    public String getTaskChangeId() {
        return this.taskChangeId;
    }

    public void setType(String type) {
        this.type = type;
    }

    /**
     * 返回 候选人类型
     *
     * @return
     */
    public String getType() {
        return this.type;
    }

    public void setExecutor(String executor) {
        this.executor = executor;
    }

    /**
     * 返回 执行人ID
     *
     * @return
     */
    public String getExecutor() {
        return this.executor;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("taskChangeId", this.taskChangeId)
                .append("type", this.type)
                .append("executor", this.executor)
                .toString();
    }
}
