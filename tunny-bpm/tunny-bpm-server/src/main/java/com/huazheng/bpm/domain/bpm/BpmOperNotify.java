package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmOperNotifyDao;
import com.huazheng.bpm.dao.bpm.BpmOperNotifyQueryDao;
import com.huazheng.bpm.dao.bpm.BpmOperNotifyRecerDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmOperNotifyPo;
import com.huazheng.bpm.entity.bpm.BpmOperNotifyRecerPo;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.web.ContextUtil;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

/**
 * 流程通知 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-03-28 09:43:38
 * </pre>
 */
@SuppressWarnings("serial")
@Service
@Scope("prototype")
public class BpmOperNotify extends AbstractDomain<String, BpmOperNotifyPo> {

    private BpmOperNotifyDao bpmOperNotifyDao = null;
    private BpmOperNotifyQueryDao bpmOperNotifyQueryDao = null;

    private BpmOperNotifyRecerDao bpmOperNotifyRecerDao = null;

    protected void init() {
        bpmOperNotifyDao = AppUtil.getBean(BpmOperNotifyDao.class);
        bpmOperNotifyQueryDao = AppUtil.getBean(BpmOperNotifyQueryDao.class);
        bpmOperNotifyRecerDao = AppUtil.getBean(BpmOperNotifyRecerDao.class);
        this.setDao(bpmOperNotifyDao);
    }

    @Override
    protected void onSave() {
        if (BeanUtils.isNotEmpty(getId()))
            setNewFlag(BeanUtils.isEmpty(bpmOperNotifyQueryDao.get(getId())));
    }

    /**
     * 主从表一并保存
     * void
     *
     * @throws
     * @since 1.0.0
     */
    public void saveCascade() {
        save();
        if (getData().isDelBeforeSave()) {
            bpmOperNotifyRecerDao.deleteByMainId(getId());
        }

        if (BeanUtils.isNotEmpty(getData().getBpmOperNotifyRecerPoList())) {
            for (BpmOperNotifyRecerPo bpmOperNotifyRecerPo : getData().getBpmOperNotifyRecerPoList()) {
                //设置外键
                bpmOperNotifyRecerPo.setNotifyId(getId());

                bpmOperNotifyRecerDao.create(bpmOperNotifyRecerPo);
            }
        }
    }

    /**
     * 主从表一并删除
     * void
     *
     * @throws
     * @since 1.0.0
     */
    public void deleteByIdsCascade(String[] ids) {
        for (String id : ids) {
            bpmOperNotifyRecerDao.deleteByMainId(id);
        }
        deleteByIds(ids);
    }

    /**
     * 设置已读
     *
     * @param id
     */
    public void read(String id) {
        bpmOperNotifyRecerDao.read(id, ContextUtil.getCurrentUserId());
    }

    /**
     * 设置已读
     *
     * @param id
     */
    public void read(String[] ids) {
        if (BeanUtils.isEmpty(ids)) {
            return;
        }

        for (String id : ids) {
            bpmOperNotifyRecerDao.read(id, ContextUtil.getCurrentUserId());
        }
    }
}
