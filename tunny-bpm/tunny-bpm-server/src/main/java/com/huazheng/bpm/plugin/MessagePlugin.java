package com.huazheng.bpm.plugin;

import com.huazheng.bpm.entity.constant.BpmConstants;
import com.huazheng.bpm.model.base.BpmIdentity;
import com.huazheng.bpm.model.base.ContentType;
import com.huazheng.bpm.model.base.MsgType;
import com.huazheng.bpm.model.base.NeedSubject;
import com.huazheng.bpm.model.delegate.BpmDelegateExecution;
import com.huazheng.bpm.plugin.factory.BpmPluginSessionFactory;
import com.huazheng.bpm.util.base.FreemarkerEngine;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.helper.UserAssignRuleQueryHelper;
import com.huazheng.bpm.util.session.BpmUserCalcPluginSession;
import com.huazheng.bpm.util.string.StringCollections;
import com.huazheng.bpm.util.string.StringUtil;
import freemarker.template.TemplateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 消息插件
 *
 * <pre>
 *
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月13日-下午5:50:46
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class MessagePlugin extends AbstractBpmExecutionPlugin {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private BpmPluginSessionFactory sessionFactory;
//
//	@Autowired(required = false)
//	private IJmsProducer jmsProducer;

    @Resource
    private FreemarkerEngine freemarkEngine;

    public Void execute(BpmExecutionPluginSession pluginSession, IBpmExecutionPluginDefine pluginDef) {
        BpmDelegateExecution delegateExecution = pluginSession.getBpmDelegateExecution();

        Map<String, Object> vars = delegateExecution.getVariables();
        // 发送人
        String sender = vars.get(BpmConstants.CUR_USER).toString();
        BpmUserCalcPluginSession bpmUserCalcPluginSession = sessionFactory.buildBpmUserCalcPluginSession(vars);

        MessagePluginDefine messageDef = (MessagePluginDefine) pluginDef;
        String notifyType = messageDef.getNotifyType();
        if (StringUtil.isEmpty(notifyType))
            return null;
        // 处理变量数据。
        handData(messageDef, vars, delegateExecution);

        // 查询要通知的用户
        List<BpmIdentity> notifyIdentities = UserAssignRuleQueryHelper.queryExtract(messageDef.getUsers(),
                bpmUserCalcPluginSession);
        List<String> receivers = queryAndConvert(notifyIdentities);
        String subject = parse(messageDef.getSubject(), vars);
        String html = parse(messageDef.getHtml(), vars);
        String plainText = parse(messageDef.getPlainText(), vars);

        String[] notifyTypes = StringCollections.toArray(notifyType);
        //不同类型发送不同消息
        for (String type : notifyTypes) {
            MsgType msgType = MsgType.get(type);
            if (BeanUtils.isEmpty(msgType))
                continue;
            this.send(msgType.getNeedSubject().equals(NeedSubject.YES) ? subject : "",
                    msgType.getContentType().equals(ContentType.HTML) ? html : plainText, receivers, type, sender);
        }

        return null;
    }

    private void send(String subject, String content, List<String> receivers, String notifyType, String sender) {
//		if (jmsProducer == null)
//			return;
        if (StringUtil.isEmpty(notifyType))
            return;

        String[] aryType = notifyType.split(",");
//		for (String type : aryType) {
//			MsgVo msgVo = new DefaultMsgVo(subject, content, sender, receivers, type);
//			jmsProducer.sendToQueue(msgVo);
//		}
    }

    /**
     * 处理外部数据并添加到表单中。
     *
     * @param messageDef
     * @param vars
     * @param execution  void
     */
    private void handData(MessagePluginDefine messageDef, Map<String, Object> vars, BpmDelegateExecution execution) {
        String externalClass = messageDef.getExternalClass();
        if (StringUtil.isEmpty(externalClass))
            return;

        String instId = (String) vars.get(BpmConstants.PROCESS_INST_ID);
        String bpmnDefId = execution.getBpmnDefId();
        String bpmnInstId = execution.getBpmnInstId();
        String nodeId = execution.getNodeId();
        String executionId = execution.getId();

        try {
            Map<String, Object> varMap = null;
            Class<?> cls = Class.forName(externalClass);
//			if (BeanUtils.isNotEmpty(cls)) {
//				IExternalData data = (IExternalData) cls.newInstance();
//				varMap = data.getData(bpmnDefId, bpmnInstId, instId, nodeId, executionId);
//			} else {
//				varMap = new HashMap<String, Object>();
//			}
            varMap = new HashMap<String, Object>();
            vars.putAll(varMap);
        } catch (ClassNotFoundException e) {
            logger.debug(e.getMessage());
        }
//		} catch (InstantiationException e) {
//		logger.debug(e.getMessage());
//	} catch (IllegalAccessException e) {
//		logger.debug(e.getMessage());
//	}
    }

    private String parse(String template, Object obj) {
        String temp = "";
        try {
            temp = freemarkEngine.parseByStringTemplate(template, obj);
        } catch (TemplateException e) {
            logger.debug(e.getMessage());
        } catch (IOException e) {
            logger.debug(e.getMessage());
        }
        return temp;
    }

    private List<String> queryAndConvert(List<BpmIdentity> bpmIdentities) {
        List<String> userList = new ArrayList<String>();
        for (BpmIdentity bpmIdentity : bpmIdentities) {
            userList.add(bpmIdentity.getId());
        }
        return userList;
    }

}
