package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmAgentConditionDao;
import com.huazheng.bpm.dao.bpm.BpmAgentConditionQueryDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmAgentConditionPo;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

/**
 * 流程代理条件 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-03-30 17:29:15
 * </pre>
 */
@SuppressWarnings("serial")
@Service
@Scope("prototype")
public class BpmAgentCondition extends AbstractDomain<String, BpmAgentConditionPo> {

    private BpmAgentConditionDao bpmAgentConditionDao = null;
    private BpmAgentConditionQueryDao bpmAgentConditionQueryDao = null;


    protected void init() {
        bpmAgentConditionDao = AppUtil.getBean(BpmAgentConditionDao.class);
        bpmAgentConditionQueryDao = AppUtil.getBean(BpmAgentConditionQueryDao.class);
        this.setDao(bpmAgentConditionDao);
    }

    @Override
    protected void onSave() {
        if (BeanUtils.isNotEmpty(getId()))
            setNewFlag(BeanUtils.isEmpty(bpmAgentConditionQueryDao.get(getId())));
    }

}
