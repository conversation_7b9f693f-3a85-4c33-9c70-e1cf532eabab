package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.entity.framework.AbstractPo;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.Date;

/**
 * 流程通知接收列表 表对象
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-03-28 09:43:38
 * </pre>
 */
@SuppressWarnings("serial")
public class BpmOperNotifyRecerTbl extends AbstractPo<String> {
    protected String id;        /*主键*/
    protected String notifyId;        /*通知ID*/
    protected String receiverId;        /*接收人ID*/
    protected String isRead;        /*是否已读*/
    protected Date updateTime;        /*阅读时间*/

    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 主键
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setNotifyId(String notifyId) {
        this.notifyId = notifyId;
    }

    /**
     * 返回 通知ID
     *
     * @return
     */
    public String getNotifyId() {
        return this.notifyId;
    }

    public void setReceiverId(String receiverId) {
        this.receiverId = receiverId;
    }

    /**
     * 返回 接收人ID
     *
     * @return
     */
    public String getReceiverId() {
        return this.receiverId;
    }

    public void setIsRead(String idRead) {
        this.isRead = idRead;
    }

    /**
     * 返回 是否已读
     *
     * @return
     */
    public String getIsRead() {
        return this.isRead;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 返回 阅读时间
     *
     * @return
     */
    public Date getUpdateTime() {
        return this.updateTime;
    }

    /**
     * @see Object#toString()
     */
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.id)
                .append("notifyId", this.notifyId)
                .append("receiverId", this.receiverId)
                .append("isRead", this.isRead)
                .append("updateTime", this.updateTime)
                .toString();
    }
}
