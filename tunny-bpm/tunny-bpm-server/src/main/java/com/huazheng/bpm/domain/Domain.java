package com.huazheng.bpm.domain;


import com.huazheng.bpm.entity.framework.PO;

import java.io.Serializable;


public interface Domain<PK extends Serializable, P extends PO<PK>> {
    /**
     * 获取主键
     *
     * @return
     */
    PK getId();

    /**
     * 获取数据
     *
     * @return
     */
    P getData();

    /**
     * 设置数据
     *
     * @param data_
     */
    void setData(P data_);

    /**
     * 保存记录
     */
    void save();

    /**
     * 创建记录
     */
    void create();

    /**
     * 修改记录
     */
    void update();

    /**
     * 删除记录
     */
    void delete();

    /**
     * 根据主键删除记录
     */
    void delete(PK id_);

    /**
     * 按实体IDs删除记录
     *
     * @param ids
     */
    @SuppressWarnings("unchecked")
    void deleteByIds(PK... ids_);
}
