package com.huazheng.bpm.entity.party;

import com.huazheng.bpm.entity.framework.AbstractPo;
import com.huazheng.bpm.entity.org.PartyEntityTbl;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 用户  实体对象。
 *
 * <pre>
 * 构建组：ibps-org-biz
 * 作者：huangchunyan
 * 邮箱：<EMAIL>
 * 日期：2016-06-20 09:07:07
 * 版权：广州流辰信息技术有限公司
 * </pre>
 */
@SuppressWarnings("serial")
public class PartyUserTbl extends AbstractPo<String> {
    protected String id; /*ID*/
    @NotBlank(message = "{com.lc.ibps.org.party.persistence.entity.PartyUserTbl.account}", groups = {PartyEntityTbl.ValidGroup.SaveEmployee.class})
    protected String account; /*登录账号*/
    @NotBlank(message = "{com.lc.ibps.org.party.persistence.entity.PartyUserTbl.password}", groups = {PartyEntityTbl.ValidGroup.SaveEmployee.class})
    protected String password; /*密码*/
    protected char isSuper; /*是否超级管理员*/
    protected String dataCheck; /*数据校验码。由帐号、铭文密码和是否超级管理员三者一起加密形成校验码*/


    public void setId(String id) {
        this.id = id;
    }

    /**
     * 返回 ID
     *
     * @return
     */
    public String getId() {
        return this.id;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    /**
     * 返回 登录账号
     *
     * @return
     */
    public String getAccount() {
        return this.account;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * 返回 密码
     *
     * @return
     */
    public String getPassword() {
        return this.password;
    }

    public void setIsSuper(char isSuper) {
        this.isSuper = isSuper;
    }

    /**
     * 返回 是否超级管理员
     *
     * @return
     */
    public char getIsSuper() {
        return this.isSuper;
    }

    public void setDataCheck(String dataCheck) {
        this.dataCheck = dataCheck;
    }

    /**
     * 返回 数据校验码。由帐号、铭文密码和是否超级管理员三者一起加密形成校验码
     *
     * @return
     */
    public String getDataCheck() {
        return this.dataCheck;
    }

}
