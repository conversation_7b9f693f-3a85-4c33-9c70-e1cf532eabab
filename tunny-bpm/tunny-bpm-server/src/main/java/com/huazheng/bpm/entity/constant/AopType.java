package com.huazheng.bpm.entity.constant;

/**
 * AOP处理器类型
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月12日-下午2:32:45
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum AopType {
    PREV("prev", "前置"),
    POST("post", "后置");

    // 键
    private String key = "";
    // 值
    private String value = "";

    // 构造方法
    AopType(String key, String value) {
        this.key = key;
        this.value = value;
    }

    // =====getting and setting=====
    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return key;
    }

    /**
     * 通过key获取对象
     *
     * @param key
     * @return
     */
    public static AopType get(String key) {
        for (AopType c : AopType.values()) {
            if (c.getKey().equalsIgnoreCase(key)) {
                return c;
            }
        }
        throw new IllegalArgumentException(key);
    }
}
