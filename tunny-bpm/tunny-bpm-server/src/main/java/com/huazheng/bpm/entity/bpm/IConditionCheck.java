package com.huazheng.bpm.entity.bpm;

import com.huazheng.bpm.util.session.BpmUserCalcPluginSession;

import java.util.Map;


/**
 * 条件检查接口。
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：simon cai
 * 邮箱:<EMAIL>
 * 日期:2015-7-8-下午2:42:17
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface IConditionCheck {

    boolean check(String condition, String model, BpmUserCalcPluginSession session);

    boolean check(String condition, String model, Map<String, Object> vars);
}
