package com.huazheng.bpm.plugin.usercalc.pos.def;


import com.huazheng.bpm.plugin.AbstractUserCalcPluginDefine;

/**
 * TODO（用一句话描述这个类做什么用）。
 * <pre>
 * 构建组：ibps-bpmn-plugin
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月14日-上午10:20:30
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@SuppressWarnings("serial")
public class PosPluginDefine extends AbstractUserCalcPluginDefine {

    /**
     * 来源
     */
    private String source = "";

    /**
     * 指定的岗位key。
     */
    private String posKey = "";

    /**
     * 指定的岗位名称。
     */
    private String posName = "";
    private String nodeId = "";
    private String nodeName = "";

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getPosKey() {
        return posKey;
    }

    public void setPosKey(String posKey) {
        this.posKey = posKey;
    }

    public String getPosName() {
        return posName;
    }

    public void setPosName(String posName) {
        this.posName = posName;
    }

}
