/**
 * 描述：TODO
 * 包名：com.huazheng.bpm.api.bpmn.model.node
 * 文件名：IReminderDef.java
 * 作者：eddy
 * 日期：2017年3月23日-上午11:46:20
 * 版权：广州流辰信息技术有限公司版权所有
 */
package com.huazheng.bpm.model.node;

/**
 * 催办定义接口
 * <pre>
 * 构建组：ibps-bpmn-api
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2017年3月23日-上午11:46:20
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface IReminderDef {

    void setId(String id);

    /**
     * 返回 主键
     * @return
     */
    String getId();

    void setName(String name);

    /**
     * 返回 名称
     * @return
     */
    String getName();

    void setProcDefId(String procDefId);

    /**
     * 返回 流程定义ID
     * @return
     */
    String getProcDefId();

    void setNodeId(String nodeId);

    /**
     * 返回 当前节点ID
     * @return
     */
    String getNodeId();

    void setRelNodeId(String relNodeId);

    /**
     * 返回 相对节点
     * @return
     */
    String getRelNodeId();

    void setRelNodeEvent(String relNodeEvent);

    /**
     * 返回 相对处理事件
     * @return
     */
    String getRelNodeEvent();

    void setRelTimeType(String relTimeType);

    /**
     * 返回 相对时间类型
     * @return
     */
    String getRelTimeType();

    void setCronExpression(String cronExpression);

    /**
     * 返回 条件表达式
     * @return
     */
    String getCronExpression();

    void setCallScript(String callScript);

    /**
     * 返回 调用指定方法
     * @return
     */
    String getCallScript();

    void setStartTime(Integer startTime);

    /**
     * 返回 开始发送时间
     * @return
     */
    Integer getStartTime();

    void setInterval(Integer interval);

    /**
     * 返回 发送时间间隔
     * @return
     */
    Integer getInterval();

    void setSendTimes(Short sendTimes);

    /**
     * 返回 发送次数
     * @return
     */
    Short getSendTimes();

    void setDueTime(Integer dueTime);

    /**
     * 返回 到期时间
     * @return
     */
    Integer getDueTime();

    void setDueAction(String dueAction);

    /**
     * 返回 到期执行动作
     * @return
     */
    String getDueAction();

    void setMsgTypeHtml(String msgTypeHtml);

    /**
     * 返回 富文本的信息类型
     * @return
     */
    String getMsgTypeHtml();

    void setHtml(String html);

    /**
     * 返回 富文本内容
     * @return
     */
    String getHtml();

    void setMsgTypePt(String msgTypePt);

    /**
     * 返回 普通文本的信息类型
     * @return
     */
    String getMsgTypePt();

    void setPlainText(String plainText);

    /**
     * 返回 普通文本内容
     * @return
     */
    String getPlainText();
}
