package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmTaskSignDao;
import com.huazheng.bpm.dao.bpm.BpmTaskSignQueryDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmTaskSignPo;
import com.huazheng.bpm.entity.constant.BpmConstants;
import com.huazheng.bpm.repository.BpmTaskSignRepository;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.web.ContextUtil;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


/**
 * 会签数据
 * 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：simon cai
 * 邮箱地址：<EMAIL>
 * 创建时间：2016-12-20 20:47:29
 * </pre>
 */
@SuppressWarnings("serial")
@Transactional
@Scope("prototype")
@Service
public class BpmTaskSign extends AbstractDomain<String, BpmTaskSignPo> {

    @Resource
    private BpmTaskSignDao bpmTaskSignDao;
    @Resource
    private BpmTaskSignRepository bpmTaskSignRepository;
    @Resource
    private BpmTaskSignQueryDao bpmTaskSignQueryDao;

    protected void init() {
        this.setDao(bpmTaskSignDao);
    }

    public void delByInstNode(String instId, String nodeId) {
        bpmTaskSignDao.delByInstNode(instId, nodeId);
    }

    public void delByInst(List<String> instList) {
        bpmTaskSignDao.delByInst(instList);
    }

    public void vote(String curUser) {
        String instId = getData().getInstId();
        String nodeId = getData().getNodeId();
        String taskId = getData().getTaskId();
        Integer index = getData().getIndex();
        String actionName = getData().getVoteResult();

        BpmTaskSignPo signData = bpmTaskSignRepository.getByInstNodeIdx(instId, nodeId, index, getData().getBatch());
        signData.setVoteResult(actionName);
        signData.setVoteId(curUser != null ? curUser : BpmConstants.SYSTEM_USER_ID);
        signData.setTaskId(taskId);
        signData.setVoteTime(new Date());
        //更新会签数据。
        bpmTaskSignDao.update(signData);
    }

    public void setSequentialTaskId(String batch, Integer idx, String taskId) {
        BpmTaskSignPo signData = bpmTaskSignQueryDao.getByBatchIdx(batch, idx);
        signData.setTaskId(taskId);
        //更新会签数据
        bpmTaskSignDao.update(signData);
    }

    /**
     * 根据任务ID删除会签数据
     *
     * @param taskId
     */
    public void delByTask(String taskId) {
        bpmTaskSignDao.delByTask(taskId);
    }

    /**
     * 设置会签结果
     *
     * @param instId
     * @param taskId
     * @param userId
     * @param signResult
     */
    public void setSignResult(String taskId, Integer loopCounter, String signResult) {
        BpmTaskSignPo signData = bpmTaskSignQueryDao.getByTaskId(taskId, loopCounter);
        signData.setSignResult(signResult);
        bpmTaskSignDao.update(signData);
    }

    /**
     * 设置会签特权使用
     *
     * @param instId
     * @param taskId
     * @param userId
     * @param signResult
     */
    public void setPrivilege(String taskId, Integer loopCounter, String privilege) {
        BpmTaskSignPo signData = bpmTaskSignQueryDao.getByTaskId(taskId, loopCounter);
        if (BeanUtils.isEmpty(signData.getPrivilege())) {
            signData.setPrivilege(privilege);
        } else {
            signData.setPrivilege(signData.getPrivilege() + "," + privilege);
        }
        bpmTaskSignDao.update(signData);
    }

    /**
     * 设置会签特权使用
     *
     * @param instId
     * @param taskId
     * @param userId
     * @param signResult
     */
    public void setPrivilege(String taskId, String privilege) {
        BpmTaskSignPo signData = bpmTaskSignQueryDao.getByTaskId(taskId, ContextUtil.getCurrentUserId());
        if (ContextUtil.isSuper() && BeanUtils.isEmpty(signData)) return;
        if (BeanUtils.isEmpty(signData.getPrivilege())) {
            signData.setPrivilege(privilege);
        } else {
            signData.setPrivilege(signData.getPrivilege() + "," + privilege);
        }
        bpmTaskSignDao.update(signData);
    }

    /**
     * 设置会签特权使用
     *
     * @param taskId
     * @param privilege
     * @param isSuper
     */
    public void setPrivilege(String taskId, String privilege, Boolean isSuper) {
        BpmTaskSignPo signData = bpmTaskSignQueryDao.getByTaskId(taskId, ContextUtil.getCurrentUserId());
        if (isSuper && BeanUtils.isEmpty(signData)) return;
        if (BeanUtils.isEmpty(signData.getPrivilege())) {
            signData.setPrivilege(privilege);
        } else {
            signData.setPrivilege(signData.getPrivilege() + "," + privilege);
        }
        bpmTaskSignDao.update(signData);
    }

}
