package com.huazheng.bpm.domain.bpm;

import com.huazheng.bpm.dao.bpm.BpmTaskChangeAssignDao;
import com.huazheng.bpm.dao.bpm.BpmTaskChangeAssignQueryDao;
import com.huazheng.bpm.domain.AbstractDomain;
import com.huazheng.bpm.entity.bpm.BpmTaskChangeAssignPo;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

/**
 * 任务变更候选人 领域对象实体
 *
 * <pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：eddy
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-04-11 19:45:11
 * </pre>
 */
@SuppressWarnings("serial")
@Service
@Scope("prototype")
public class BpmTaskChangeAssign extends AbstractDomain<String, BpmTaskChangeAssignPo> {

    private BpmTaskChangeAssignDao bpmTaskChangeAssignDao = null;
    private BpmTaskChangeAssignQueryDao bpmTaskChangeAssignQueryDao = null;


    protected void init() {
        bpmTaskChangeAssignDao = AppUtil.getBean(BpmTaskChangeAssignDao.class);
        bpmTaskChangeAssignQueryDao = AppUtil.getBean(BpmTaskChangeAssignQueryDao.class);
        this.setDao(bpmTaskChangeAssignDao);
    }

    @Override
    protected void onSave() {
        if (BeanUtils.isNotEmpty(getId()))
            setNewFlag(BeanUtils.isEmpty(bpmTaskChangeAssignQueryDao.get(getId())));
    }

}
