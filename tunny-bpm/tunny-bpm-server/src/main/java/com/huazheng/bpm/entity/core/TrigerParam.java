//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4-2
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.08.25 at 03:37:49 PM CST
//


package com.huazheng.bpm.entity.core;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for trigerParam complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="trigerParam">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="srcAttr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="srcAttrName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="destAttr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="destAttrName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="allowEmpty" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "trigerParam", propOrder = {
        "srcAttr",
        "srcAttrName",
        "destAttr",
        "destAttrName",
        "allowEmpty"
})
public class TrigerParam {

    @XmlElement(required = true)
    protected String srcAttr;
    @XmlElement(required = true)
    protected String srcAttrName;
    @XmlElement(required = true)
    protected String destAttr;
    @XmlElement(required = true)
    protected String destAttrName;
    @XmlElement(required = true)
    protected String allowEmpty;

    /**
     * Gets the value of the srcAttr property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getSrcAttr() {
        return srcAttr;
    }

    /**
     * Sets the value of the srcAttr property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSrcAttr(String value) {
        this.srcAttr = value;
    }

    /**
     * Gets the value of the srcAttrName property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getSrcAttrName() {
        return srcAttrName;
    }

    /**
     * Sets the value of the srcAttrName property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSrcAttrName(String value) {
        this.srcAttrName = value;
    }

    /**
     * Gets the value of the destAttr property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getDestAttr() {
        return destAttr;
    }

    /**
     * Sets the value of the destAttr property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDestAttr(String value) {
        this.destAttr = value;
    }

    /**
     * Gets the value of the destAttrName property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getDestAttrName() {
        return destAttrName;
    }

    /**
     * Sets the value of the destAttrName property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDestAttrName(String value) {
        this.destAttrName = value;
    }

    /**
     * Gets the value of the allowEmpty property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getAllowEmpty() {
        return allowEmpty;
    }

    /**
     * Sets the value of the allowEmpty property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setAllowEmpty(String value) {
        this.allowEmpty = value;
    }

}
