package com.huazheng.tunny.tools.net;

import static org.assertj.core.api.Assertions.*;

import org.junit.Test;

public class IPUtilTest {

	@Test
	public void stringAndInt() {

		assertThat(IPUtil.ipv4StringToInt("***********")).isEqualTo(-1062731775);
		assertThat(IPUtil.ipv4StringToInt("***********")).isEqualTo(-1062731774);

		assertThat(IPUtil.intToIpv4String(-1062731775)).isEqualTo("***********");
		assertThat(IPUtil.intToIpv4String(-1062731774)).isEqualTo("***********");
	}

	@Test
	public void inetAddress() {

		assertThat(IPUtil.fromInt(-1062731775).getHostAddress()).isEqualTo("***********");
		assertThat(IPUtil.fromInt(-1062731774).getHostAddress()).isEqualTo("***********");

		assertThat(IPUtil.fromIpString("***********").getHostAddress()).isEqualTo("***********");
		assertThat(IPUtil.fromIpString("***********").getHostAddress()).isEqualTo("***********");
		assertThat(IPUtil.fromIpv4String("***********").getHostAddress()).isEqualTo("***********");
		assertThat(IPUtil.fromIpv4String("***********").getHostAddress()).isEqualTo("***********");

		assertThat(IPUtil.toInt(IPUtil.fromIpString("***********"))).isEqualTo(-1062731775);
	}
}
