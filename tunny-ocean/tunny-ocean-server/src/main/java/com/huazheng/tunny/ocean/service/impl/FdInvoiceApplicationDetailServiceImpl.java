package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.SelectInvoiceCostDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.vo.FdInvoiceStatisticsVO;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.FdCosdetailService;
import com.huazheng.tunny.ocean.service.FdInvoiceApplicationDetailService;
import com.huazheng.tunny.ocean.util.PermissionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;

@Service("fdInvoiceApplicationDetailService")
public class FdInvoiceApplicationDetailServiceImpl extends ServiceImpl<FdInvoiceApplicationDetailMapper, FdInvoiceApplicationDetail> implements FdInvoiceApplicationDetailService {

    @Autowired
    private FdInvoiceApplicationDetailMapper fdInvoiceApplicationDetailMapper;
    @Autowired
    private PermissionUtil permissionUtil;
    @Autowired
    private FdCosdetailService fdCosdetailService;
    @Autowired
    private WaybillContainerInfoMapper waybillContainerInfoMapper;
    @Autowired
    private WaybillParticipantsMapper waybillParticipantsMapper;
    @Autowired
    private WaybillGoodsInfoMapper waybillGoodsInfoMapper;
    @Autowired
    private FdShippingAccoundetailMapper fdShippingAccoundetailMapper;
    @Autowired
    private FdBillSubDetailMapper fdBillSubDetailMapper;
    @Autowired
    private FdInvoiceApplicationMapper fdInvoiceApplicationMapper;
    @Value("${db.database}")
    private String database;
    @Autowired
    private FdInvoiceCheckHistoryMapper fdInvoiceCheckHistoryMapper;

    /**
     * 查询开票申请明细表信息
     *
     * @param id 开票申请明细表ID
     * @return 开票申请明细表信息
     */
    @Override
    public FdInvoiceApplicationDetail selectFdInvoiceApplicationDetailById(Integer id) {
        return fdInvoiceApplicationDetailMapper.selectFdInvoiceApplicationDetailById(id);
    }

    /**
     * 查询开票申请明细表列表
     *
     * @param fdInvoiceApplicationDetail 开票申请明细表信息
     * @return 开票申请明细表集合
     */
    @Override
    public List<FdInvoiceApplicationDetail> selectFdInvoiceApplicationDetailList(FdInvoiceApplicationDetail fdInvoiceApplicationDetail) {
        return fdInvoiceApplicationDetailMapper.selectFdInvoiceApplicationDetailList(fdInvoiceApplicationDetail);
    }


    /**
     * 分页模糊查询开票申请明细表列表
     *
     * @return 开票申请明细表集合
     */
    @Override
    public Page selectFdInvoiceApplicationDetailListByLike(Query query) {
        FdInvoiceApplicationDetail fdInvoiceApplicationDetail = BeanUtil.mapToBean(query.getCondition(), FdInvoiceApplicationDetail.class, false);
        query.setRecords(fdInvoiceApplicationDetailMapper.selectFdInvoiceApplicationDetailListByLike(query, fdInvoiceApplicationDetail));
        return query;
    }

    /**
     * 查询用户下的费用明细
     *
     * @return 开票申请明细表集合

    @Override
    public Page selectCanInvoiceInfoByLike(Query query) {
        FdInvoiceApplicationDetail fdInvoiceApplicationDetail = BeanUtil.mapToBean(query.getCondition(), FdInvoiceApplicationDetail.class, false);
        if (StrUtil.isNotEmpty(fdInvoiceApplicationDetail.getPlatformCode())) {
            fdInvoiceApplicationDetail.setPlatformCode(permissionUtil.getPcPermisson(fdInvoiceApplicationDetail.getPlatformCode(), fdInvoiceApplicationDetail.getPlatformLevel()));
            fdInvoiceApplicationDetail.setPlatformLevel(null);
            fdInvoiceApplicationDetail.setPlatformName(null);
        }
//        fdInvoiceApplicationDetail.setPlatformCode(null);
        fdInvoiceApplicationDetail.setDatabase(database);
        List<FdInvoiceApplicationDetail> fdInvoiceApplicationDetails = fdInvoiceApplicationDetailMapper.selectCanInvoiceInfoByLike(query, fdInvoiceApplicationDetail);
        if(CollUtil.isNotEmpty(fdInvoiceApplicationDetails)){
            List<String> waybillNos = new ArrayList<>();
            for (FdInvoiceApplicationDetail fd:fdInvoiceApplicationDetails
             ) {
                waybillNos.add(fd.getTransportOrderNumber());
            }
            if(CollUtil.isNotEmpty(waybillNos)){
                List<WaybillContainerInfo> list = waybillContainerInfoMapper.getList(waybillNos);
                if(CollUtil.isNotEmpty(list)){
                    for (FdInvoiceApplicationDetail fd:fdInvoiceApplicationDetails
                    ) {
                        for (WaybillContainerInfo wci:list
                             ) {
                            if(wci.getWaybillNo().equals(fd.getTransportOrderNumber())&&wci.getContainerNo().equals(fd.getContainerNumber())){
                                fd.setType(wci.getIdentification());
                                fd.setPortAgent(wci.getPortAgent());
                                fd.setContainerOwner(wci.getContainerOwner());

                                fd.setContainerTypeCode(wci.getContainerTypeCode());
                                fd.setContainerTypeName(wci.getContainerTypeName());
                                fd.setContainerType(wci.getContainerType());

                                fd.setDestinationName(wci.getStartStationName());
                                fd.setDestination(wci.getEndStationName());
                                fd.setPortStation(wci.getPortStation());
                                fd.setHatchNo(wci.getHatchNo());
                            }
                        }
                    }
                }

                List<WaybillGoodsInfo> list2 = waybillGoodsInfoMapper.getList(waybillNos);
                if(CollUtil.isNotEmpty(list2)){
                    for (FdInvoiceApplicationDetail fd:fdInvoiceApplicationDetails
                    ) {
                        for (WaybillGoodsInfo wgi:list2
                        ) {
                            if(wgi.getWaybillNo().equals(fd.getTransportOrderNumber())&&wgi.getContainerNo().equals(fd.getContainerNumber())){
                                fd.setGoodsName(wgi.getGoodsChineseName());
                            }
                        }
                    }
                }

                List<WaybillParticipants> list3 = waybillParticipantsMapper.getList(database,waybillNos);
                if(CollUtil.isNotEmpty(list3)){
                    for (FdInvoiceApplicationDetail fd:fdInvoiceApplicationDetails
                    ) {
                        for (WaybillParticipants wp:list3
                        ) {
                            if(wp.getWaybillNo().equals(fd.getTransportOrderNumber())&&wp.getContainerNo().equals(fd.getContainerNumber())){
                                fd.setConsignorName(wp.getConsignorName());
                                fd.setDestinationCountry(wp.getResveredField01());
                            }
                        }
                    }
                }
            }
        }


        query.setRecords(fdInvoiceApplicationDetails);
//        query.setTotal(fdInvoiceApplicationDetailMapper.selectCanInvoiceInfoByLikeCount(fdInvoiceApplicationDetail));
        return query;
    }*/

    /**
     * 查询费用明细
     * @param query
     * @return
     */
    @Override
    public Page selectCanInvoiceInfo(Query query) {
        // 查询DTO
        SelectInvoiceCostDTO selectInvoiceCostDTO = BeanUtil.mapToBean(query.getCondition(), SelectInvoiceCostDTO.class, false);

        List<FdInvoiceApplicationDetail> fdInvoiceApplicationDetails = fdInvoiceApplicationDetailMapper.selectUnInvoiceInfo(query, selectInvoiceCostDTO);

        return query.setRecords(fdInvoiceApplicationDetails);
    }

    @Override
    public Page selectCanInvoiceInfoNew(Query query) {
        // 查询DTO
        SelectInvoiceCostDTO selectInvoiceCostDTO = BeanUtil.mapToBean(query.getCondition(), SelectInvoiceCostDTO.class, false);

        List<FdInvoiceApplicationDetail> fdInvoiceApplicationDetails = fdInvoiceApplicationDetailMapper.selectUnInvoiceInfoNew(query, selectInvoiceCostDTO);

        return query.setRecords(fdInvoiceApplicationDetails);
    }

    @Override
    public Page selectCanInvoiceInfos(Query query) {
        FdInvoiceApplicationDetail fdInvoiceApplicationDetail = BeanUtil.mapToBean(query.getCondition(), FdInvoiceApplicationDetail.class, false);
        List<FdInvoiceApplicationDetail> fdInvoiceApplicationDetails = fdInvoiceApplicationDetailMapper.selectCanInvoiceInfos(query, fdInvoiceApplicationDetail);
        query.setRecords(fdInvoiceApplicationDetails);
        return query;
    }

    /**
     * 新增开票申请明细表
     *
     * @param detailList 开票申请明细表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R insertFdInvoiceApplicationDetail(List<FdInvoiceApplicationDetail> detailList) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        StringBuilder builder = new StringBuilder();
        for (FdInvoiceApplicationDetail detail : detailList){
            detail.setDelFlag("Y");
            detail.setCreateUsercode(userInfo.getUserName());
            detail.setCreateUserrealname(userInfo.getRealName());
            detail.setCreateTime(LocalDateTime.now());
            if("1".equals(detail.getPlatformLevel())) {
                //2021-12-11 添加开票明时把省级班列号带上
                FdCosdetail fdCosdetail = new FdCosdetail();
                fdCosdetail.setUuid(detail.getUuid());
                fdCosdetail.setPlatformLevel(detail.getPlatformLevel());
                String trainsNumberByUuid = fdCosdetailService.selectProvinceTrainsNumberByUuid(fdCosdetail);
                detail.setStandbyC(trainsNumberByUuid);
            }
            fdInvoiceApplicationDetailMapper.insert(detail);
            builder.append(detail.getId() + ",");
        }
        String ids = "";
        if (builder == null || builder.length() > 0){
            ids = builder.substring(0, builder.length() - 1);
        }
        return new R<>(200, true, ids);
    }

    /**
     * 修改开票申请明细表
     *
     * @param fdInvoiceApplicationDetail 开票申请明细表信息
     * @return 结果
     */
    @Override
    public int updateFdInvoiceApplicationDetail(FdInvoiceApplicationDetail fdInvoiceApplicationDetail) {
        return fdInvoiceApplicationDetailMapper.updateFdInvoiceApplicationDetail(fdInvoiceApplicationDetail);
    }


    /**
     * 删除开票申请明细表
     *
     * @param id 开票申请明细表ID
     * @return 结果
     */
    @Override
    public int deleteFdInvoiceApplicationDetailById(Integer id) {
        return fdInvoiceApplicationDetailMapper.deleteFdInvoiceApplicationDetailById(id);
    }



    /**
     * 批量删除开票申请明细表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFdInvoiceApplicationDetailByIds(Integer[] ids) {
        return fdInvoiceApplicationDetailMapper.deleteFdInvoiceApplicationDetailByIds(ids);
    }

    /**
     * 作废开票
     * @param id
     * @return
     */
    @Override
    public R cancel(Integer id) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        final FdInvoiceApplicationDetail fdInvoiceApplicationDetail = fdInvoiceApplicationDetailMapper.selectFdInvoiceApplicationDetailById(id);
        if(fdInvoiceApplicationDetail!=null){
            if(StrUtil.isNotEmpty(fdInvoiceApplicationDetail.getCodeBbCategoriesCode())&&StrUtil.isNotEmpty(fdInvoiceApplicationDetail.getUuid())){
                //运费关联作废台账详情
                if("f_fee_type".equals(fdInvoiceApplicationDetail.getCodeBbCategoriesCode())){
                    FdShippingAccoundetail updateObj = new FdShippingAccoundetail();
                    updateObj.setRowId(Long.parseLong(fdInvoiceApplicationDetail.getUuid()));
                    if(StrUtil.isNotEmpty(fdInvoiceApplicationDetail.getCodeSsCategoriesCode())){
                        if("f_overseas_fee".equals(fdInvoiceApplicationDetail.getCodeSsCategoriesCode())){
                            updateObj.setIsInvoiceJw("0");
                            updateObj.setInvoiceNumberJw("开票已作废");
                        }else if("f_domestic_fee".equals(fdInvoiceApplicationDetail.getCodeSsCategoriesCode())){
                            updateObj.setIsInvoiceJn("0");
                            updateObj.setInvoiceNumberJn("开票已作废");
                        }
                    }
                    updateObj.setUpdateWho(userInfo.getUserName());
                    updateObj.setUpdateWhoName(userInfo.getRealName());
                    updateObj.setUpdateTime(LocalDateTime.now());
                    fdShippingAccoundetailMapper.updateFdShippingAccoundetail(updateObj);
                }else{
                    //杂费关联作废子账单
                    FdBillSubDetail updateObj = new FdBillSubDetail();
                    updateObj.setUuid(fdInvoiceApplicationDetail.getUuid());
                    updateObj.setIsInvoice("0");
                    updateObj.setInvoiceApplicationCode("开票已作废");
                    updateObj.setUpdateWho(userInfo.getUserName());
                    updateObj.setUpdateWhoName(userInfo.getRealName());
                    updateObj.setUpdateTime(LocalDateTime.now());
                    fdBillSubDetailMapper.updateFdBillSubDetail(updateObj);
                }
            }

            //作废开票明细
            FdInvoiceApplicationDetail delObj = new FdInvoiceApplicationDetail();
            delObj.setId(id);
            delObj.setDelFlag("Y");
            delObj.setDelUsercode(userInfo.getUserName());
            delObj.setDelUserrealname(userInfo.getRealName());
            delObj.setDelTime(LocalDateTime.now());
            fdInvoiceApplicationDetailMapper.updateFdInvoiceApplicationDetail(delObj);

            FdInvoiceApplicationDetail sel2 = new FdInvoiceApplicationDetail();
            sel2.setInvoiceApplicationCode(fdInvoiceApplicationDetail.getInvoiceApplicationCode());
            sel2.setDelFlag("Y");
            final List<FdInvoiceApplicationDetail> fdInvoiceApplicationDetails = fdInvoiceApplicationDetailMapper.selectFdInvoiceApplicationDetailList(sel2);

            FdInvoiceApplication sel = new FdInvoiceApplication();
            sel.setInvoiceApplicationCode(fdInvoiceApplicationDetail.getInvoiceApplicationCode());
            sel.setDeleteFlag("N");
            final List<FdInvoiceApplication> fdInvoiceApplications = fdInvoiceApplicationMapper.selectFdInvoiceApplicationList(sel);
            if(CollUtil.isNotEmpty(fdInvoiceApplications)){
                //更新之后是否还存在子表数据
                FdInvoiceApplication newObj = new FdInvoiceApplication();
                newObj.setId(fdInvoiceApplications.get(0).getId());
                if(CollUtil.isNotEmpty(fdInvoiceApplicationDetails)){
                    //更新开票总金额
                    final BigDecimal subtract = fdInvoiceApplications.get(0).getInvoiceAmount().subtract(fdInvoiceApplicationDetail.getLocalCurrencyAmount());
                    newObj.setInvoiceAmount(subtract);
                    newObj.setUpdateWho(userInfo.getUserName());
                    newObj.setUpdateWhoName(userInfo.getRealName());
                    newObj.setUpdateTime(LocalDateTime.now());
                }else{
                    //删除主表数据
                    newObj.setDeleteFlag("Y");
                    newObj.setStatus("3");
                    newObj.setDeleteWho(userInfo.getUserName());
                    newObj.setDeleteWhoName(userInfo.getRealName());
                    newObj.setDeleteTime(LocalDateTime.now());
                }

                fdInvoiceApplicationMapper.updateFdInvoiceApplication(newObj);
            }

        }else{
            return new R(500,Boolean.FALSE,null,"未查询到该条开票数据！");
        }
        return new R(200,Boolean.TRUE,null,"作废成功！");
    }


    @Override
    public Page selectInvoiceStatisticsPage2(Query query) {
        FdInvoiceApplicationDetail fdInvoiceApplicationDetail =  BeanUtil.mapToBean(query.getCondition(), FdInvoiceApplicationDetail.class,false);
        query.setRecords(fdInvoiceApplicationDetailMapper.selectInvoiceStatisticsPage2(query,fdInvoiceApplicationDetail));
        return query;
    }

    /**
     * @param fdInvoiceStatisticsVO
     * @return
     */
    @Override
    public R selectInvoiceStatisticsList(FdInvoiceStatisticsVO fdInvoiceStatisticsVO) {
        HashMap<String,Object> map = new HashMap<>();
        final List<FdInvoiceStatisticsVO> fdInvoiceStatisticsVOS = fdInvoiceApplicationDetailMapper.selectInvoiceStatisticsList(fdInvoiceStatisticsVO);
        final FdInvoiceStatisticsVO fdInvoiceStatistics = fdInvoiceApplicationDetailMapper.selectInvoiceStatisticsTotal(fdInvoiceStatisticsVO);
        map.put("list",fdInvoiceStatisticsVOS);
        map.put("total",fdInvoiceStatistics);
        return new R<>(200,Boolean.TRUE,map,"success");
    }

    /**
     * @param fdInvoiceStatisticsVO
     * @return
     */
    @Override
    public R selectInvoiceStatisticsWk(FdInvoiceStatisticsVO fdInvoiceStatisticsVO) {
        final List<FdInvoiceStatisticsVO> fdInvoiceStatisticsVOS = fdInvoiceApplicationDetailMapper.selectInvoiceStatisticsWk(fdInvoiceStatisticsVO);
        return new R<>(200,Boolean.TRUE,fdInvoiceStatisticsVOS,"success");
    }

    /**
     * @param fdInvoiceStatisticsVO
     * @return
     */
    @Override
    public R selectInvoiceStatisticsYk(FdInvoiceStatisticsVO fdInvoiceStatisticsVO) {
        final List<FdInvoiceStatisticsVO> fdInvoiceStatisticsVOS = fdInvoiceApplicationDetailMapper.selectInvoiceStatisticsYk(fdInvoiceStatisticsVO);
        return new R<>(200,Boolean.TRUE,fdInvoiceStatisticsVOS,"success");
    }
}
