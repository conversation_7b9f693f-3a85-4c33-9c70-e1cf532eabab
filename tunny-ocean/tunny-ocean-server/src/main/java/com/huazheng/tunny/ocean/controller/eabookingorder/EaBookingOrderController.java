package com.huazheng.tunny.ocean.controller.eabookingorder;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.util.R;
import com.huazheng.tunny.ocean.api.dto.eaboolingorder.EaBookingOrderDto;
import com.huazheng.tunny.ocean.api.entity.eabookingorder.EaBookingOrder;
import com.huazheng.tunny.ocean.service.eabookingorder.EaBookingOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 订舱订单表
 *
 * <AUTHOR> code generator
 * @date 2025-06-16 15:23:50
 */
@Slf4j
@RestController
@RequestMapping("/eabookingorder")
public class EaBookingOrderController {

    @Autowired
    private EaBookingOrderService eaBookingOrderService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  eaBookingOrderService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return eaBookingOrderService.selectEaBookingOrderListByLike(new Query<>(params));
    }

    /**
     * 基本信息详情
     * @param orderId
     * @return R
     */
    @GetMapping("/{orderId}")
    public R info(@PathVariable("orderId") Long orderId) {
        return eaBookingOrderService.selectEaBookingOrderByOrderId(orderId);
    }
    /**
     * 查询是否已订舱
     * @param shiftNo
     * @return R
     */
    @GetMapping("/haveBooked")
    public R haveBooked(String shiftNo) {
        return eaBookingOrderService.selectIsHaveBooked(shiftNo);
    }

    /**
     * 保存
     * @param eaBookingOrder
     * @return R
     */
    @PostMapping("/save")
    public R save(@RequestBody EaBookingOrder eaBookingOrder) {
        if(eaBookingOrder.getOrderId() != null){
            return eaBookingOrderService.updateEaBookingOrderById(eaBookingOrder);
        }
        return eaBookingOrderService.save(eaBookingOrder);
    }

    /**
     * 修改
     * @param eaBookingOrder
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody EaBookingOrder eaBookingOrder) {

        return eaBookingOrderService.updateEaBookingOrderById(eaBookingOrder);
    }
    /**
     * 订单审核
     * @param eaBookingOrder
     * @return R
     */
    @PostMapping("/orderReview")
    public R orderReview(@RequestBody EaBookingOrderDto eaBookingOrder) {

        return eaBookingOrderService.updateEaBookingOrderReview(eaBookingOrder);
    }
    /**
     * 订单撤回
     * @param eaBookingOrder
     * @return R
     */
    @PostMapping("/orderWithdrawal")
    public R orderWithdrawal(@RequestBody EaBookingOrderDto eaBookingOrder) {

        return eaBookingOrderService.updateEaBookingOrderWithdrawal(eaBookingOrder);
    }
    /**
     * 订单提交
     * @param eaBookingOrder
     * @return R
     */
    @PostMapping("/submitOrder")
    public R submitOrder(@RequestBody EaBookingOrderDto eaBookingOrder) {

        return eaBookingOrderService.updateEaBookingOrderSubmitOrder(eaBookingOrder);
    }

    

    /**
     * 删除
     * @param orderId
     * @return R
     */
    @GetMapping("/del/{orderId}")
    public R delete(@PathVariable  Long orderId) {
        eaBookingOrderService.deleteById(orderId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param orderIds
     * @return R
     */
    @PostMapping("/del")
    public R delObjs(@RequestBody  List<Long> orderIds) {

        return eaBookingOrderService.del(orderIds);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<EaBookingOrder> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<EaBookingOrder> list = eaBookingOrderService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), EaBookingOrder.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }
}
