package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfWaybillGoods;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 *  服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-16 13:04:00
 */
public interface EfWaybillGoodsService extends IService<EfWaybillGoods> {
    /**
     * 查询信息
     *
     * @param rowId ID
     * @return 信息
     */
    public EfWaybillGoods selectEfWaybillGoodsById(String rowId);

    public List<EfWaybillGoods> selectList(String waybillNo);

    /**
     * 查询列表
     *
     * @param efWaybillGoods 信息
     * @return 集合
     */
    public List<EfWaybillGoods> selectEfWaybillGoodsList(EfWaybillGoods efWaybillGoods);


    /**
     * 分页模糊查询列表
     * @return 集合
     */
    public Page selectEfWaybillGoodsListByLike(Query query);



    /**
     * 新增
     *
     * @param efWaybillGoods 信息
     * @return 结果
     */
    public int insertEfWaybillGoods(EfWaybillGoods efWaybillGoods);

    /**
     * 修改
     *
     * @param efWaybillGoods 信息
     * @return 结果
     */
    public int updateEfWaybillGoods(EfWaybillGoods efWaybillGoods);

    /**
     * 删除
     *
     * @param rowId ID
     * @return 结果
     */
    public int deleteEfWaybillGoodsById(String rowId);

    /**
     * 批量删除
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfWaybillGoodsByIds(Integer[] rowIds);

}

