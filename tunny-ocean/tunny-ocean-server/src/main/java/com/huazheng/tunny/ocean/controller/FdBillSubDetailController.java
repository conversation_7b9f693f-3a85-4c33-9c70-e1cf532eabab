package com.huazheng.tunny.ocean.controller;

import com.huazheng.tunny.ocean.api.entity.FdBillSubDetail;
import com.huazheng.tunny.ocean.service.FdBillSubDetailService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 子账单详情
 *
 * <AUTHOR>
 * @date 2023-07-19 13:11:45
 */
@Slf4j
@RestController
@RequestMapping("/fdbillsubdetail")
public class FdBillSubDetailController {

    @Autowired
    private FdBillSubDetailService fdBillSubDetailService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdBillSubDetailService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdBillSubDetailService.selectFdBillSubDetailListByLike(new Query<>(params));
    }

    /**
     * 信息
     * @param uuid
     * @return R
     */
    @GetMapping("/{uuid}")
    public R info(@PathVariable("uuid") String uuid) {
        FdBillSubDetail fdBillSubDetail =fdBillSubDetailService.selectById(uuid);
        return new R<>(fdBillSubDetail);
    }

    /**
     * 保存
     * @param fdBillSubDetail
     * @return R
     */
    @PostMapping
    public R save(@RequestBody FdBillSubDetail fdBillSubDetail) {
        fdBillSubDetailService.insert(fdBillSubDetail);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param fdBillSubDetail
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody FdBillSubDetail fdBillSubDetail) {
        fdBillSubDetailService.updateById(fdBillSubDetail);
        return new R<>(Boolean.TRUE);
    }

    

    /**
     * 删除
     * @param uuid
     * @return R
     */
    @GetMapping("/del/{uuid}")
    public R delete(@PathVariable  String uuid) {
        fdBillSubDetailService.deleteById(uuid);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param uuids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> uuids) {
        fdBillSubDetailService.deleteBatchIds(uuids);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<FdBillSubDetail> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<FdBillSubDetail> list = fdBillSubDetailService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), FdBillSubDetail.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }



}
