package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.InvoiceApplicationHeader;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 开票申请主表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-16 17:56:23
 */
public interface InvoiceApplicationHeaderService extends IService<InvoiceApplicationHeader> {
    /**
     * 查询开票申请主表信息
     *
     * @param rowId 开票申请主表ID
     * @return 开票申请主表信息
     */
    public InvoiceApplicationHeader selectInvoiceApplicationHeaderById(String rowId);

    /**
     * 查询开票申请主表列表
     *
     * @param invoiceApplicationHeader 开票申请主表信息
     * @return 开票申请主表集合
     */
    public List<InvoiceApplicationHeader> selectInvoiceApplicationHeaderList(InvoiceApplicationHeader invoiceApplicationHeader);


    /**
     * 分页模糊查询开票申请主表列表
     * @return 开票申请主表集合
     */
    public Page selectInvoiceApplicationHeaderListByLike(Query query);



    /**
     * 新增开票申请主表
     *
     * @param invoiceApplicationHeader 开票申请主表信息
     * @return 结果
     */
    public int insertInvoiceApplicationHeader(InvoiceApplicationHeader invoiceApplicationHeader);

    /**
     * 修改开票申请主表
     *
     * @param invoiceApplicationHeader 开票申请主表信息
     * @return 结果
     */
    public int updateInvoiceApplicationHeader(InvoiceApplicationHeader invoiceApplicationHeader);

    /**
     * 删除开票申请主表
     *
     * @param rowId 开票申请主表ID
     * @return 结果
     */
    public int deleteInvoiceApplicationHeaderById(String rowId);

    /**
     * 批量删除开票申请主表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteInvoiceApplicationHeaderByIds(Integer[] rowIds);

}

