package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.BillBalanceBindingCity;
import com.huazheng.tunny.ocean.api.vo.BillBalanceMainCitySubDetailVO;
import com.huazheng.tunny.ocean.mapper.BillBalanceBindingCityMapper;
import com.huazheng.tunny.ocean.service.BillBalanceBindingCityService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service("billBalanceBindingCityService")
public class BillBalanceBindingCityServiceImpl extends ServiceImpl<BillBalanceBindingCityMapper, BillBalanceBindingCity> implements BillBalanceBindingCityService {

    @Autowired
    private BillBalanceBindingCityMapper billBalanceBindingCityMapper;

    /**
     * 查询应付账单结算绑定账单表（市）信息
     *
     * @param id 应付账单结算绑定账单表（市）ID
     * @return 应付账单结算绑定账单表（市）信息
     */
    @Override
    public BillBalanceBindingCity selectBillBalanceBindingCityById(Integer id) {
        return billBalanceBindingCityMapper.selectBillBalanceBindingCityById(id);
    }

    /**
     * 查询应付账单结算绑定账单表（市）列表
     *
     * @param billBalanceBindingCity 应付账单结算绑定账单表（市）信息
     * @return 应付账单结算绑定账单表（市）集合
     */
    @Override
    public List<BillBalanceBindingCity> selectBillBalanceBindingCityList(BillBalanceBindingCity billBalanceBindingCity) {
        return billBalanceBindingCityMapper.selectBillBalanceBindingCityList(billBalanceBindingCity);
    }


    /**
     * 分页模糊查询应付账单结算绑定账单表（市）列表
     *
     * @return 应付账单结算绑定账单表（市）集合
     */
    @Override
    public Page selectBillBalanceBindingCityListByLike(Query query) {
        BillBalanceBindingCity billBalanceBindingCity = BeanUtil.mapToBean(query.getCondition(), BillBalanceBindingCity.class, false);
        query.setRecords(billBalanceBindingCityMapper.selectBillBalanceBindingCityListByLike(query, billBalanceBindingCity));
        return query;
    }

    /**
     * 新增应付账单结算绑定账单表（市）
     *
     * @param billBalanceBindingCity 应付账单结算绑定账单表（市）信息
     * @return 结果
     */
    @Override
    public int insertBillBalanceBindingCity(BillBalanceBindingCity billBalanceBindingCity) {
        return billBalanceBindingCityMapper.insertBillBalanceBindingCity(billBalanceBindingCity);
    }

    /**
     * 修改应付账单结算绑定账单表（市）
     *
     * @param billBalanceBindingCity 应付账单结算绑定账单表（市）信息
     * @return 结果
     */
    @Override
    public int updateBillBalanceBindingCity(BillBalanceBindingCity billBalanceBindingCity) {
        return billBalanceBindingCityMapper.updateBillBalanceBindingCity(billBalanceBindingCity);
    }


    /**
     * 删除应付账单结算绑定账单表（市）
     *
     * @param id 应付账单结算绑定账单表（市）ID
     * @return 结果
     */
    public int deleteBillBalanceBindingCityById(Integer id) {
        return billBalanceBindingCityMapper.deleteBillBalanceBindingCityById(id);
    }

    ;


    /**
     * 批量删除应付账单结算绑定账单表（市）对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBillBalanceBindingCityByIds(Integer[] ids) {
        return billBalanceBindingCityMapper.deleteBillBalanceBindingCityByIds(ids);
    }

    /**
     * 根据结算单账号获取子帐单列表
     *
     * @param balanceNo 结算单账号
     * @return
     */
    @Override
    public List<BillBalanceMainCitySubDetailVO> selectBillSubListByBalanceNo(String balanceNo) {
        List<BillBalanceMainCitySubDetailVO> billBalanceMainCitySubDetailVOS = billBalanceBindingCityMapper.selectBillSubListByBalanceNo(balanceNo);
        if(CollectionUtil.isNotEmpty(billBalanceMainCitySubDetailVOS) && !billBalanceMainCitySubDetailVOS.stream().anyMatch(Objects::isNull)){
            for(BillBalanceMainCitySubDetailVO billBalanceMainCitySubDetailVO:billBalanceMainCitySubDetailVOS){
                String byCostType = billBalanceBindingCityMapper.selectByCostType(billBalanceMainCitySubDetailVO.getBillCode());
                if(StringUtils.isNotEmpty(byCostType)){
                    billBalanceMainCitySubDetailVO.setProductNames(byCostType);
                }
            }
            return billBalanceMainCitySubDetailVOS;
        }

        return new ArrayList<>();
    }

    @Override
    public List<BillBalanceMainCitySubDetailVO> selectProvinceBillSubListByBalanceNo(String balanceNo) {
        List<BillBalanceMainCitySubDetailVO> billBalanceMainCitySubDetailVOS = billBalanceBindingCityMapper.selectProvinceBillSubListByBalanceNo(balanceNo);
        if(CollectionUtil.isNotEmpty(billBalanceMainCitySubDetailVOS) && !billBalanceMainCitySubDetailVOS.stream().anyMatch(Objects::isNull)){
            for(BillBalanceMainCitySubDetailVO billBalanceMainCitySubDetailVO:billBalanceMainCitySubDetailVOS){
                String byCostType = billBalanceBindingCityMapper.selectByCostType(billBalanceMainCitySubDetailVO.getBillCode());
                if(StringUtils.isNotEmpty(byCostType)){
                    billBalanceMainCitySubDetailVO.setProductNames(byCostType);
                }
            }
            return billBalanceMainCitySubDetailVOS;
        }

        return new ArrayList<>();
    }

    @Override
    public List<BillBalanceMainCitySubDetailVO> selectCustomerBillSubListByBalanceNo(String balanceNo) {
        List<BillBalanceMainCitySubDetailVO> billBalanceMainCitySubDetailVOS = billBalanceBindingCityMapper.selectCustomerBillSubListByBalanceNo(balanceNo);
        if(CollectionUtil.isNotEmpty(billBalanceMainCitySubDetailVOS) && !billBalanceMainCitySubDetailVOS.stream().anyMatch(Objects::isNull)){
            for(BillBalanceMainCitySubDetailVO billBalanceMainCitySubDetailVO:billBalanceMainCitySubDetailVOS){
                String byCostType = billBalanceBindingCityMapper.selectByCostType(billBalanceMainCitySubDetailVO.getBillCode());
                if(StringUtils.isNotEmpty(byCostType)){
                    billBalanceMainCitySubDetailVO.setProductNames(byCostType);
                }
            }
            return billBalanceMainCitySubDetailVOS;
        }

        return new ArrayList<>();
    }


    @Override
    public int updateDeletedBillBalanceBindingByBillCode(List<String> subBillNos,String balanceNo) {
        return billBalanceBindingCityMapper.updateDeletedBillBalanceBindingByBillCode(subBillNos,balanceNo);
    }

    /**
     * 根据结算单查询子帐单编码
     * @param balanceNo
     * @return
     */
    @Override
    public List<String> selectBindingSubBillByBalanceCode(String balanceNo) {
        return billBalanceBindingCityMapper.selectBindingSubBillByBalanceCode(balanceNo);
    }

}
