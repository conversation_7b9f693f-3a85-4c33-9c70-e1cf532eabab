package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FiExporfinancingAcceptMapper;
import com.huazheng.tunny.ocean.api.entity.FiExporfinancingAccept;
import com.huazheng.tunny.ocean.service.FiExporfinancingAcceptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fiExporfinancingAcceptService")
public class FiExporfinancingAcceptServiceImpl extends ServiceImpl<FiExporfinancingAcceptMapper, FiExporfinancingAccept> implements FiExporfinancingAcceptService {

    @Autowired
    private FiExporfinancingAcceptMapper fiExporfinancingAcceptMapper;

    public FiExporfinancingAcceptMapper getFiExporfinancingAcceptMapper() {
        return fiExporfinancingAcceptMapper;
    }

    public void setFiExporfinancingAcceptMapper(FiExporfinancingAcceptMapper fiExporfinancingAcceptMapper) {
        this.fiExporfinancingAcceptMapper = fiExporfinancingAcceptMapper;
    }

    /**
     * 查询出口融资受理信息表信息
     *
     * @param rowId 出口融资受理信息表ID
     * @return 出口融资受理信息表信息
     */
    @Override
    public FiExporfinancingAccept selectFiExporfinancingAcceptById(String rowId)
    {
        return fiExporfinancingAcceptMapper.selectFiExporfinancingAcceptById(rowId);
    }

    @Override
    public FiExporfinancingAccept selectFiExporfinancingAcceptByAssetCode(FiExporfinancingAccept fiExporfinancingAccept) {
        return fiExporfinancingAcceptMapper.selectFiExporfinancingAcceptByAssetCode(fiExporfinancingAccept);
    }

    /**
     * 查询出口融资受理信息表列表
     *
     * @param fiExporfinancingAccept 出口融资受理信息表信息
     * @return 出口融资受理信息表集合
     */
    @Override
    public List<FiExporfinancingAccept> selectFiExporfinancingAcceptList(FiExporfinancingAccept fiExporfinancingAccept)
    {
        return fiExporfinancingAcceptMapper.selectFiExporfinancingAcceptList(fiExporfinancingAccept);
    }


    /**
     * 分页模糊查询出口融资受理信息表列表
     * @return 出口融资受理信息表集合
     */
    @Override
    public Page selectFiExporfinancingAcceptListByLike(Query query)
    {
        FiExporfinancingAccept fiExporfinancingAccept =  BeanUtil.mapToBean(query.getCondition(), FiExporfinancingAccept.class,false);
        query.setRecords(fiExporfinancingAcceptMapper.selectFiExporfinancingAcceptListByLike(query,fiExporfinancingAccept));
        return query;
    }

    /**
     * 新增出口融资受理信息表
     *
     * @param fiExporfinancingAccept 出口融资受理信息表信息
     * @return 结果
     */
    @Override
    public int insertFiExporfinancingAccept(FiExporfinancingAccept fiExporfinancingAccept)
    {
        return fiExporfinancingAcceptMapper.insertFiExporfinancingAccept(fiExporfinancingAccept);
    }

    /**
     * 修改出口融资受理信息表
     *
     * @param fiExporfinancingAccept 出口融资受理信息表信息
     * @return 结果
     */
    @Override
    public int updateFiExporfinancingAccept(FiExporfinancingAccept fiExporfinancingAccept)
    {
        return fiExporfinancingAcceptMapper.updateFiExporfinancingAccept(fiExporfinancingAccept);
    }


    /**
     * 删除出口融资受理信息表
     *
     * @param rowId 出口融资受理信息表ID
     * @return 结果
     */
    public int deleteFiExporfinancingAcceptById(String rowId)
    {
        return fiExporfinancingAcceptMapper.deleteFiExporfinancingAcceptById( rowId);
    };


    /**
     * 批量删除出口融资受理信息表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiExporfinancingAcceptByIds(Integer[] rowIds)
    {
        return fiExporfinancingAcceptMapper.deleteFiExporfinancingAcceptByIds( rowIds);
    }

}
