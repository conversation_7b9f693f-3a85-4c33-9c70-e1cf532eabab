package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.FdShippingAccoundetailDTO;
import com.huazheng.tunny.ocean.api.dto.FdShippingAccountDTO;
import com.huazheng.tunny.ocean.api.entity.FdShippingAccoundetail;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

/**
 * 发运台账子表 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:29:59
 */
public interface FdShippingAccoundetailService extends IService<FdShippingAccoundetail> {
    /**
     * 查询发运台账子表信息
     *
     * @param rowId 发运台账子表ID
     * @return 发运台账子表信息
     */
    public FdShippingAccoundetail selectFdShippingAccoundetailById(String rowId);

    /**
     * 查询发运台账子表列表
     *
     * @param fdShippingAccoundetail 发运台账子表信息
     * @return 发运台账子表集合
     */
    public List<FdShippingAccoundetail> selectFdShippingAccoundetailList(FdShippingAccoundetail fdShippingAccoundetail);

    /**
     * 查询省平台首页数据
     * @param customerNo
     * @return
     */
    public FdShippingAccoundetail selectProvinceIndexInfo(String customerNo);

    /**
     * 分页模糊查询发运台账子表列表
     *
     * @return 发运台账子表集合
     */
    public Page selectFdShippingAccoundetailListByLike(Query query);

    public List<FdShippingAccoundetail> list(FdShippingAccoundetail fdShippingAccoundetail);

    public List<FdShippingAccoundetail> listWithCancel(FdShippingAccoundetail fdShippingAccoundetail);

    /**
     * 台账明细刷新接口
     * @param fdShippingAccoundetail
     * @return
     */
    public R refreshAccoundetailList(FdShippingAccoundetail fdShippingAccoundetail);

    /**
     * 导入文件
     * @param listob
     * @return
     */
    public R importFile(List<List<Object>> listob);

    /**
     * 根据班列查询箱号信息
     * @param query
     * @return
     */
    public Page containerInfoPage(Query query);

    public List containerInfoList(FdShippingAccoundetail fdShippingAccoundetail);

    public List containerInfoListNew(FdShippingAccoundetail fdShippingAccoundetail);

    public List containerInfoForTz(FdShippingAccoundetail fdShippingAccoundetail);
    public R selectContainerInfoLists(Map<String, Object> params);

    /**
     * 班列发运明细表（导出）
     * @param params
     * @return
     */
    public List<FdShippingAccoundetailDTO> selectShippingDetailExport(Map<String, Object> params);

    public List<FdShippingAccoundetailDTO> selectShippingDetailExport2(String shiftNo,String billBalanceCode);

    public Page selectFreightAccountingList(Query query);

    public List<FdShippingAccoundetailDTO> selectFreightAccountingListExport(FdShippingAccoundetailDTO fdShippingAccoundetailDTO);
    /**
     * 省平台-运费核算表（导出）
     * @param provinceShiftNo
     * @return
     */
    public R selectAccountingCostInfo(String provinceShiftNo);
    /**
     * 省平台-运费核算表附表（导出）
     * @param params
     * @return
     */
    public List<FdShippingAccoundetailDTO> selectFreightAccountingExport(Map<String, Object> params);

    /**
     * 新增发运台账子表
     *
     * @param fdShippingAccoundetail 发运台账子表信息
     * @return 结果
     */
    public int insertFdShippingAccoundetail(FdShippingAccoundetail fdShippingAccoundetail);

    /**
     * 修改发运台账子表
     *
     * @param fdShippingAccoundetail 发运台账子表信息
     * @return 结果
     */
    public int updateFdShippingAccoundetail(FdShippingAccoundetail fdShippingAccoundetail);

    /**
     * 补充资料
     */
    public int updateFdShippingAccoundetailList(List<FdShippingAccoundetail> fdShippingAccoundetail);

    public int updateFdShippingAccoundetailList2(List<FdShippingAccoundetail> fdShippingAccoundetail);

    /**
     * 删除发运台账子表
     *
     * @param rowId 发运台账子表ID
     * @return 结果
     */
    public int deleteFdShippingAccoundetailById(String rowId);

    /**
     * 批量删除发运台账子表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdShippingAccoundetailByIds(Integer[] rowIds);

    public List<FdShippingAccountDTO> selectShippingDetailAccountExport(Map<String, Object> params);

    public FdShippingAccoundetail selectAccountCodeAndContainerNumber(FdShippingAccoundetail fdShippingAccoundetail);

    public List<FdShippingAccoundetail> selectFdShippingAccoundetailListByCode(FdShippingAccoundetail fdShippingAccoundetail);

    public int updateFdShippingAccoundetailByAccountCode(FdShippingAccoundetail fdShippingAccoundetail);

    public R importBasicData2(MultipartFile file, String accountCode, String shiftNo);

    public R importBasicDataTwo(MultipartFile file, String accountCode, String shiftNo);

    void exportCityTemplate(FdShippingAccoundetail detail, HttpServletResponse response) throws Exception;

    void exportCityTemplateTwo(FdShippingAccoundetail detail, HttpServletResponse response) throws Exception;

    public int updateFdShippingAccoundetailByAccountCode3(FdShippingAccoundetail fdShippingAccoundetail);

    public List<FdShippingAccoundetail> selectGroupList(FdShippingAccoundetail fdShippingAccoundetail);

    public R updateByAccountCodeByGroup(List<FdShippingAccoundetail> fdShippingAccoundetail);

    public void updateFdShippingAccoundetailByWaybill(FdShippingAccoundetail fdShippingAccoundetail);

    public void updateWaybillInfo(String accountCode,List<FdShippingAccoundetail> fdShippingAccoundetail);

    R weChatList(FdShippingAccoundetail fdShippingAccoundetail);
}

