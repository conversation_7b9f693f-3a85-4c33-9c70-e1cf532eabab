package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.FdRemittanceRecord;

import java.util.List;
import java.util.Map;

/**
 * 汇款记录表 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:30:42
 */
public interface FdRemittanceRecordService extends IService<FdRemittanceRecord> {
    /**
     * 查询汇款记录表信息
     *
     * @param id 汇款记录表ID
     * @return 汇款记录表信息
     */
    public FdRemittanceRecord selectFdRemittanceRecordById(Long id);

    /**
     * 查询汇款记录表列表
     *
     * @param fdRemittanceRecord 汇款记录表信息
     * @return 汇款记录表集合
     */
    public List<FdRemittanceRecord> selectFdRemittanceRecordList(FdRemittanceRecord fdRemittanceRecord);

    public Page selectFdRemittanceRecordListByLike(Query query);


    /**
     * 分页模糊查询汇款记录表列表
     *
     * @return 汇款记录表集合
     */
    public Page page2(Query query);

    /**
     * 根据用户code查询汇款未使用明细
     *
     * @param fdRemittanceRecord
     * @return
     */
    public R getInfoByCustomerCode(FdRemittanceRecord fdRemittanceRecord);

    public R selectInfoByBillCode(String billCode, String incomeFlag);

    /**
     * 汇款一级列表
     *
     * @param query
     * @return
     */
    public Page selectRemittanceRecordListPage(Query query);

    public List<FdRemittanceRecord> selectRemittanceRecordList(Map<String, Object> param);

    public List<FdRemittanceRecord> selectRemittanceRecordListSheng(Map<String, Object> param);

    /**
     * 新增汇款记录表
     *
     * @param fdRemittanceRecord 汇款记录表信息
     * @return 结果
     */
    public int insertFdRemittanceRecord(FdRemittanceRecord fdRemittanceRecord);

    /**
     * 转为预收款
     *
     * @param fdRemittanceRecord 汇款记录表信息
     * @return 结果
     */
    public int convertToAdvance(FdRemittanceRecord fdRemittanceRecord);

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    public int insertFdRemittanceRecordList(List<FdRemittanceRecord> list);

    /**
     * 修改汇款记录表
     *
     * @param fdRemittanceRecord 汇款记录表信息
     * @return 结果
     */
    public int updateFdRemittanceRecord(FdRemittanceRecord fdRemittanceRecord);

    /**
     * 根据单号更改汇款记录
     *
     * @param fdRemittanceRecord
     * @return
     */
    public int updateByRecordCode(FdRemittanceRecord fdRemittanceRecord);

    /**
     * 删除汇款记录表
     *
     * @param id 汇款记录表ID
     * @return 结果
     */
    public int deleteFdRemittanceRecordById(Long id);

    /**
     * 批量删除汇款记录表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdRemittanceRecordByIds(Integer[] ids);

    public List<FdRemittanceRecord> selectDetailsList(FdRemittanceRecord fdRemittanceRecord);

    public Page selectDetails(Query query);

    /**
     * 收款记录认领
     *
     * @Param: fdRemittanceRecord
     * @Return:
     * @Author: Howe
     * @Date: 2024/6/17 下午5:46
     **/
    R claimRemittance(FdRemittanceRecord fdRemittanceRecord);

    /**
     * 认领作废
     *
     * @Param: null
     * @Return:
     * @Author: Howe
     * @Date: 2024/6/22 下午2:05
     **/
    R claimInvalid(FdRemittanceRecord fdRemittanceRecord);

    R claimInvalidProvince(FdRemittanceRecord fdRemittanceRecord);

    /**
     * 保存接口
     *
     * @Param: fdRemittanceRecord
     * @Return:
     * @Author: Howe
     * @Date: 2024/6/22 下午6:05
     **/
    R save(FdRemittanceRecord fdRemittanceRecord);

    R saveTwo(FdRemittanceRecord fdRemittanceRecord);
}

