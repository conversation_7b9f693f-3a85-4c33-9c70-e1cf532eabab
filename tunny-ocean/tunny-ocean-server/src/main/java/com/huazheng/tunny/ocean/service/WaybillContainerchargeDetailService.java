package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.WaybillContainerchargeDetail;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 运单集装箱费用信息表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-06 10:54:32
 */
public interface WaybillContainerchargeDetailService extends IService<WaybillContainerchargeDetail> {
    /**
     * 查询运单集装箱费用信息表信息
     *
     * @param rowId 运单集装箱费用信息表ID
     * @return 运单集装箱费用信息表信息
     */
    public WaybillContainerchargeDetail selectWaybillContainerchargeDetailById(String rowId);

    /**
     * 查询运单集装箱费用信息表列表
     *
     * @param waybillContainerchargeDetail 运单集装箱费用信息表信息
     * @return 运单集装箱费用信息表集合
     */
    public List<WaybillContainerchargeDetail> selectWaybillContainerchargeDetailList(WaybillContainerchargeDetail waybillContainerchargeDetail);

    /**
     * 分页模糊查询运单集装箱费用信息表列表
     * @return 运单集装箱费用信息表集合
     */
    public Page selectWaybillContainerchargeDetailListByLike(Query query);

    /**
     * 批量新增运单集装箱费用信息表
     *
     * @param waybillContainerchargeDetail 运单集装箱费用信息表信息
     * @return 结果
     */
    public int insertWaybillContainerchargeDetailBatch(List<WaybillContainerchargeDetail> waybillContainerchargeDetail);

    /**
     * 新增运单集装箱费用信息表
     *
     * @param waybillContainerchargeDetail 运单集装箱费用信息表信息
     * @return 结果
     */
    public int insertWaybillContainerchargeDetail(WaybillContainerchargeDetail waybillContainerchargeDetail);

    /**
     * 修改运单集装箱费用信息表
     *
     * @param waybillContainerchargeDetail 运单集装箱费用信息表信息
     * @return 结果
     */
    public int updateWaybillContainerchargeDetailBatch(List<WaybillContainerchargeDetail> waybillContainerchargeDetail);

    /**
     * 修改运单集装箱费用信息表
     *
     * @param waybillContainerchargeDetail 运单集装箱费用信息表信息
     * @return 结果
     */
    public int updateWaybillContainerchargeDetail(WaybillContainerchargeDetail waybillContainerchargeDetail);

    /**
     * 删除运单集装箱费用信息表
     *
     * @param rowId 运单集装箱费用信息表ID
     * @return 结果
     */
    public int deleteWaybillContainerchargeDetailById(String rowId);

    /**
     * 批量删除运单集装箱费用信息表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteWaybillContainerchargeDetailByIds(Integer[] rowIds);


}

