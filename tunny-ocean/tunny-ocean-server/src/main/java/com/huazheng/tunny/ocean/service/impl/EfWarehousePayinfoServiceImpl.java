package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.EfWarehousePayinfoMapper;
import com.huazheng.tunny.ocean.api.entity.EfWarehousePayinfo;
import com.huazheng.tunny.ocean.service.EfWarehousePayinfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("efWarehousePayinfoService")
public class EfWarehousePayinfoServiceImpl extends ServiceImpl<EfWarehousePayinfoMapper, EfWarehousePayinfo> implements EfWarehousePayinfoService {

    @Autowired
    private EfWarehousePayinfoMapper efWarehousePayinfoMapper;

    public EfWarehousePayinfoMapper getEfWarehousePayinfoMapper() {
        return efWarehousePayinfoMapper;
    }

    public void setEfWarehousePayinfoMapper(EfWarehousePayinfoMapper efWarehousePayinfoMapper) {
        this.efWarehousePayinfoMapper = efWarehousePayinfoMapper;
    }

    /**
     * 查询融资放款信息信息
     *
     * @param rowId 融资放款信息ID
     * @return 融资放款信息信息
     */
    @Override
    public EfWarehousePayinfo selectEfWarehousePayinfoById(String rowId)
    {
        return efWarehousePayinfoMapper.selectEfWarehousePayinfoById(rowId);
    }

    /**
     * 查询融资放款信息列表
     *
     * @param efWarehousePayinfo 融资放款信息信息
     * @return 融资放款信息集合
     */
    @Override
    public List<EfWarehousePayinfo> selectEfWarehousePayinfoList(EfWarehousePayinfo efWarehousePayinfo)
    {
        return efWarehousePayinfoMapper.selectEfWarehousePayinfoList(efWarehousePayinfo);
    }

    @Override
    public List<EfWarehousePayinfo> selectEfWarehousePaySum(EfWarehousePayinfo efWarehousePayinfo)
    {
        return efWarehousePayinfoMapper.selectEfWarehousePaySum(efWarehousePayinfo);
    }


    /**
     * 分页模糊查询融资放款信息列表
     * @return 融资放款信息集合
     */
    @Override
    public Page selectEfWarehousePayinfoListByLike(Query query)
    {
        EfWarehousePayinfo efWarehousePayinfo =  BeanUtil.mapToBean(query.getCondition(), EfWarehousePayinfo.class,false);
        query.setRecords(efWarehousePayinfoMapper.selectEfWarehousePayinfoListByLike(query,efWarehousePayinfo));
        return query;
    }

    /**
     * 新增融资放款信息
     *
     * @param efWarehousePayinfo 融资放款信息信息
     * @return 结果
     */
    @Override
    public int insertEfWarehousePayinfo(EfWarehousePayinfo efWarehousePayinfo)
    {
        return efWarehousePayinfoMapper.insertEfWarehousePayinfo(efWarehousePayinfo);
    }

    /**
     * 修改融资放款信息
     *
     * @param efWarehousePayinfo 融资放款信息信息
     * @return 结果
     */
    @Override
    public int updateEfWarehousePayinfo(EfWarehousePayinfo efWarehousePayinfo)
    {
        return efWarehousePayinfoMapper.updateEfWarehousePayinfo(efWarehousePayinfo);
    }


    /**
     * 删除融资放款信息
     *
     * @param rowId 融资放款信息ID
     * @return 结果
     */
    public int deleteEfWarehousePayinfoById(String rowId)
    {
        return efWarehousePayinfoMapper.deleteEfWarehousePayinfoById( rowId);
    };


    /**
     * 批量删除融资放款信息对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfWarehousePayinfoByIds(Integer[] rowIds)
    {
        return efWarehousePayinfoMapper.deleteEfWarehousePayinfoByIds( rowIds);
    }

}
