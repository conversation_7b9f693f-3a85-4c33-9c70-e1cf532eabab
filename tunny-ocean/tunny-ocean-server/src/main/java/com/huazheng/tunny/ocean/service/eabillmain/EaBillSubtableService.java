package com.huazheng.tunny.ocean.service.eabillmain;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.eabusinessprocess.EaBusinessProcessAppendDto;
import com.huazheng.tunny.ocean.api.entity.eabillmain.EaBillMain;
import com.huazheng.tunny.ocean.api.entity.eabillmain.EaBillSubtable;

import java.util.List;

/**
 * 账单子表 服务接口层
 *
 * <AUTHOR>
 * @date 2025-06-30 18:36:02
 */
public interface EaBillSubtableService extends IService<EaBillSubtable> {
    /**
     * 查询账单子表信息
     *
     * @param billSubtableId 账单子表ID
     * @return 账单子表信息
     */
    public EaBillSubtable selectEaBillSubtableById(Long billSubtableId);

    /**
     * 查询账单子表列表
     *
     * @param eaBillSubtable 账单子表信息
     * @return 账单子表集合
     */
    public List<EaBillSubtable> selectEaBillSubtableList(EaBillSubtable eaBillSubtable);


    /**
     * 分页模糊查询账单子表列表
     * @return 账单子表集合
     */
    public Page selectEaBillSubtableListByLike(Query query);



    /**
     * 新增账单子表
     *
     * @param eaBillSubtable 账单子表信息
     * @return 结果
     */
    public int insertEaBillSubtable(EaBillSubtable eaBillSubtable);

    /**
     * 修改账单子表
     *
     * @param eaBillSubtable 账单子表信息
     * @return 结果
     */
    public int updateEaBillSubtable(EaBillSubtable eaBillSubtable);

    /**
     * 删除账单子表
     *
     * @param billSubtableId 账单子表ID
     * @return 结果
     */
    public int deleteEaBillSubtableById(Long billSubtableId);

    /**
     * 批量删除账单子表
     *
     * @param billSubtableIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEaBillSubtableByIds(Integer[] billSubtableIds);

    /**
     * 备份一次账单子表
     *
     * @param eaBillSubtable 备份逻辑参数
     * @return 结果
     */
    public R backupBillSubtable(EaBillSubtable eaBillSubtable);

    int saveBillSubtableInfo(List<EaBusinessProcessAppendDto> list);
}

