package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.ShiftStat;
import com.huazheng.tunny.ocean.api.entity.ShiftStatDetail;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Service
 *
 * <AUTHOR>
 * @since 2025-05-06 16:19:09
 */
public interface ShiftStatService extends IService<ShiftStat> {

    /**
     * 班次统计表分页
     *
     * @param shiftStat 查询条件
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:19:09
     */
    Page<ShiftStat> page(ShiftStat shiftStat);

    /**
     * 班次统计表详情
     *
     * @param statId 班次统计表详情ID
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:19:09
     */
    R<ShiftStat> info(Integer statId);

    /**
     * 班次统计表保存
     *
     * @param shiftStat 班次统计
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:19:09
     */
    R<ShiftStat> save(ShiftStat shiftStat);

    /**
     * 班次统计表修改
     *
     * @param shiftStat 班次统计
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:19:09
     */
    R<T> update(ShiftStat shiftStat);

    /**
     * 班次统计表删除
     *
     * @param statIds 统计 ID
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:19:09
     */
    R<T> delete(Long[] statIds);

    /**
     * 班次统计表列表
     *
     * @param shiftStat 查询条件
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:19:09
     */
    R<List<ShiftStat>> list(ShiftStat shiftStat);


    /**
     * 模板导入
     *
     * @param file 导入的中亚或者中欧模板
     * @param type 类型
     * @return R
     * <AUTHOR>
     * @since 2025/5/7 上午9:10
     **/
    R<List<ShiftStatDetail>> importExcel(MultipartFile file, String type);

    /**
     * 模板导入
     *
     * @param statId ID
     * @param response response
     * <AUTHOR>
     * @since 2025/5/7 上午9:10
     **/
    void exportExcel(Long statId, HttpServletResponse response);

}

