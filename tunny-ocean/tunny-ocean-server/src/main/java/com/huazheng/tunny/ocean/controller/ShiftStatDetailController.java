package com.huazheng.tunny.ocean.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.ShiftStatDetail;
import com.huazheng.tunny.ocean.service.ShiftStatDetailService;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 班次统计明细表
 *
 * <AUTHOR>
 * @since 2025-05-06 16:18:59
 */
@RestController
@RequestMapping("shiftStatDetail")
public class ShiftStatDetailController {

    @Autowired
    private ShiftStatDetailService shiftStatDetailservice;

    /**
     * 班次统计明细表分页
     *
     * @param params
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:18:59
     */
    @GetMapping("/page")
    public R<Page<ShiftStatDetail>> page(ShiftStatDetail shiftStatDetail) {
        return R.success(shiftStatDetailservice.page(shiftStatDetail));
    }

    /**
     * 班次统计明细表详情
     *
     * @param id
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:18:59
     */
    @GetMapping("/{detailId}")
    public R<ShiftStatDetail> info(@PathVariable("detailId") Integer detailId) {
        return shiftStatDetailservice.info(detailId);
    }

    /**
     * 班次统计明细表保存
     *
     * @param param
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:18:59
     */
    @PostMapping("/save")
    public R<T> save(@RequestBody ShiftStatDetail shiftStatDetail) {
        return shiftStatDetailservice.save(shiftStatDetail);
    }

    /**
     * 班次统计明细表修改
     *
     * @param param
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:18:59
     */
    @PostMapping("/renew")
    public R<T> update(@RequestBody ShiftStatDetail shiftStatDetail) {
        return shiftStatDetailservice.update(shiftStatDetail);
    }

    /**
     * 班次统计明细表删除
     *
     * @param detailId
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:18:59
     */
    @PostMapping("/del/{detailIds}")
    public R<T> delete(@PathVariable Integer[] detailIds) {
        return shiftStatDetailservice.delete(detailIds);
    }

    /**
     * 班次统计明细表列表
     *
     * @param param
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:18:59
     */
    @GetMapping("/list")
    public R<List<ShiftStatDetail>> list(ShiftStatDetail shiftStatDetail) {
        return shiftStatDetailservice.list(shiftStatDetail);
    }

}
