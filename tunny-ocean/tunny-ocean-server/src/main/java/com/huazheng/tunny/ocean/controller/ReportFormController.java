package com.huazheng.tunny.ocean.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.QueryMap;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.service.ReportFormService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * @Description: 报表
 * @Author: 徐瑞
 * @Date: 2024-7-24 15:35:45
 */
@RestController
@RequestMapping("/reportForm")
public class ReportFormController {

    @Autowired
    private ReportFormService service;

    /**
     * @Description: 班列统计表(省)
     * @Author: 徐瑞
     * @Date: 2024-7-24 15:35:45
     */
    @GetMapping("/provincialTrainStatisticsForm")
    public Page provincialTrainStatisticsForm(@RequestParam Map<String, Object> params) {
        return service.provincialTrainStatisticsForm(new QueryMap<>(params));
    }

    /**
     * 班列统计表(省)导出
     *
     * @Description: 班列统计表(省)导出
     * @Author: 徐瑞
     * @Date: 2024-7-24 15:35:45
     */
    @GetMapping("/provincialTrainStatisticsFormExport")
    public void provincialTrainStatisticsFormExport(@RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        service.provincialTrainStatisticsFormExport(params, response);
    }

    /**
     * @Description: 班列统计表(市)
     * @Author: 徐瑞
     * @Date: 2024-7-24 15:35:45
     */
    @GetMapping("/cityTrainStatisticsForm")
    public Page cityTrainStatisticsForm(@RequestParam Map<String, Object> params) {
        params.put("platformCode", SecurityUtils.getUserInfo().getPlatformCode());
        return service.cityTrainStatisticsForm(new QueryMap<>(params));
    }

    /**
     * 班列统计表(市)导出
     *
     * @Author: 徐瑞
     * @Date: 2024-7-24 15:35:45
     */
    @GetMapping("/cityTrainStatisticsFormExport")
    public void cityTrainStatisticsFormExport(@RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        params.put("platformCode", SecurityUtils.getUserInfo().getPlatformCode());
        service.cityTrainStatisticsFormExport(params, response);
    }

    /**
     * @Description: 发运明细表(省)
     * @Author: 徐瑞
     * @Date: 2024-7-24 15:35:45
     */
    @GetMapping("/provincialShippingDetailForm")
    public Page provincialShippingDetailForm(@RequestParam Map<String, Object> params) {
        return service.provincialShippingDetailForm(new QueryMap<>(params));
    }

    /**
     * 发运明细表(省)导出
     * @Author: 徐瑞
     * @Date: 2024-7-24 15:35:45
     */
    @GetMapping("/provincialShippingDetailFormExport")
    public void provincialShippingDetailFormExport(@RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        service.provincialShippingDetailFormExport(params, response);
    }

    /**
     * @Description: 发运明细表(市)
     * @Author: 徐瑞
     * @Date: 2024-7-24 15:35:45
     */
    @GetMapping("/cityShippingDetailForm")
    public Page cityShippingDetailForm(@RequestParam Map<String, Object> params) {
        params.put("platformCode", SecurityUtils.getUserInfo().getPlatformCode());
        return service.cityShippingDetailForm(new QueryMap<>(params));
    }

    /**
     * 发运明细表(市)导出
     * @Author: 徐瑞
     * @Date: 2024-7-24 15:35:45
     */
    @GetMapping("/cityShippingDetailFormExport")
    public void cityShippingDetailFormExport(@RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        params.put("platformCode", SecurityUtils.getUserInfo().getPlatformCode());
        service.cityShippingDetailFormExport(params,response);
    }

    /**
     * 订单明细表
     *
     * @Description: 订单明细表
     * @Author: zhr
     * @Date: 2024-7-24 15:35:45
     */
    @GetMapping("/billDetailForm")
    public Page billDetailForm(@RequestParam Map<String, Object> params) {
        params.put("platformCode", SecurityUtils.getUserInfo().getPlatformCode());
        return service.billDetailForm(new QueryMap<>(params));
    }

    /**
     * 订单明细表导出
     *
     * @Description: 订单明细表导出
     * @Author: zhr
     * @Date: 2024-7-24 15:35:45
     */
    @GetMapping("/billDetailFormExport")
    public void billDetailFormExport(@RequestParam Map<String, Object> params, HttpServletResponse res) throws Exception {
        params.put("platformCode", SecurityUtils.getUserInfo().getPlatformCode());
        service.billDetailFormExport(new QueryMap<>(params), res);
    }

}
