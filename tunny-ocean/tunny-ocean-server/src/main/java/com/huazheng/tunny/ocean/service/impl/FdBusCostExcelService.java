package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.huazheng.tunny.common.core.util.ExcelObjectCheckerUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.GoodsDTO;
import com.huazheng.tunny.ocean.api.dto.RequesdetailDTO;
import com.huazheng.tunny.ocean.api.dto.RequesheaderDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.enums.IdentificationEnum;
import com.huazheng.tunny.ocean.api.feign.RemoteAdminService;
import com.huazheng.tunny.ocean.api.vo.CustomerPlatformVO;
import com.huazheng.tunny.ocean.api.vo.SysDictVo;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.FdBusCostService;
import com.huazheng.tunny.ocean.util.CheckUtil;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @since 2025-04-25 13:19
 */
@Service
public class FdBusCostExcelService {

    @Autowired
    private CustomerPlatformInfoMapper customerPlatformInfoMapper;
    @Autowired
    private ShifmanagementMapper shifmanagementMapper;
    @Autowired
    private ContainerTypeDataMapper containerTypeDataMapper;
    @Autowired
    private StationManagementMapper stationManagementMapper;
    @Autowired
    private FdBusCostMapper fdBusCostMapper;
    @Autowired
    private RemoteAdminService remoteAdminService;
    @Resource
    private FdBusCostService fdBusCostService;

    private static final String CONTAINER_OWNER_0 = "自备箱";
    private static final String CONTAINER_OWNER_1 = "中铁箱";

    private static final String EXCEL_FIELD_YES = "是";
    private static final String EXCEL_FIELD_NO = "否";
    private static final Set<String> STANDARD_CONTAINER_TYPES = new HashSet<>(Arrays.asList("20", "40", "45"));
    private static final String GP_SUFFIX = "GP";
    /**
     * 单客户模板
     */
    private static final String TEMPLATE_SINGLE = "SINGLE";
    /**
     * 多客户模板
     */
    private static final String TEMPLATE_MULTIPLE = "MULTIPLE";

    public R createBusinessFlowBySingleCustomer(MultipartFile file) throws Exception {
        R<T> checkResult = checkFileForUnknownObjects(file);
        if (checkResult.getCode() != 0) {
            return checkResult;
        }

        // 使用流两次需重复打开流
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        // 读取第一行用于校验客户、班次
        List<Object> titleList = reader.readRow(0);
        if (CollUtil.isEmpty(titleList)) {
            return R.error("模板标题为空");
        }
        CustomerPlatformVO customerPlatform = checkCustomer(titleList);
        if (customerPlatform == null) {
            return R.error("查询不到客户信息");
        }
        Object shiftNoCell = titleList.get(5);
        if (shiftNoCell == null) {
            return R.error("班次为空");
        }
        String shiftNo = shiftNoCell.toString();
        Shifmanagement shift = checkShift(shiftNo);
        if (shift == null) {
            return R.error("查询不到班次信息");
        }

        //校验业务流程单
        R<T> checkBusinessFlow = checkBusinessFlow(shift, customerPlatform);
        if (checkBusinessFlow.getCode() != 0) {
            return checkBusinessFlow;
        }

        //获取模板明细
        List<Map<String, Object>> resultList = getDetailList(reader);

        List<SysDictVo> countryList = getCountryList();
        //查询箱型
        List<ContainerTypeData> containerTypeDataList = getContainerTypeList();
        //校验明细
        R<?> checkDetail = validateData(resultList, countryList, containerTypeDataList, TEMPLATE_SINGLE);
        if (checkDetail.getCode() != 0) {
            return checkDetail;
        }

        RequesheaderDTO requesheaderDTO = new RequesheaderDTO();
        requesheaderDTO.setBookingCustcode(customerPlatform.getCustomerCode());
        requesheaderDTO.setBookingCustname(customerPlatform.getCustomerName());
        requesheaderDTO.setShiftNo(shift.getShiftId());
        requesheaderDTO.setResveredField06(shift.getOverseasAgency());
        requesheaderDTO.setTrip(shift.getTrip());
        requesheaderDTO.setResveredField06(titleList.get(7) == null ? null : titleList.get(7).toString().trim());
        requesheaderDTO.setRemarks(titleList.get(9) == null ? null : titleList.get(9).toString().trim());

        //遍历明细
        List<RequesdetailDTO> requesdetailDTOList = convertRowsToDetailList(resultList, countryList, containerTypeDataList, customerPlatform);
        requesheaderDTO.setDetails(requesdetailDTOList);
        return fdBusCostService.saveBookingAndWaybillForCity(requesheaderDTO);

    }


    /**
     * 获取明细
     *
     * @param reader 读取模板
     * @return List<Map < String, Object>> 明细
     * <AUTHOR>
     * @since 2025/4/27 下午5:55
     **/
    private List<Map<String, Object>> getDetailList(ExcelReader reader) {
        // 读取第三行作为原始表头
        List<Object> rawHeaderRow = reader.readRow(2);
        List<String> fixedHeaders = fixDuplicateHeaders(rawHeaderRow);

        // 读取明细行（从第四行开始，即索引为3）
        List<List<Object>> dataRows = reader.read(3);

        List<Map<String, Object>> resultList = new ArrayList<>();
        for (List<Object> row : dataRows) {
            Map<String, Object> rowMap = new LinkedHashMap<>();
            for (int i = 0; i < fixedHeaders.size(); i++) {
                String key = fixedHeaders.get(i);
                Object value = i < row.size() ? row.get(i) : null;
                rowMap.put(key, value);
            }
            resultList.add(rowMap);
        }
        return resultList;
    }

    /**
     * 校验客户是否合法  selectCustomerPlatform
     *
     * @param titleList 模板标题
     * @return R
     * <AUTHOR>
     * @since 2025/4/25 下午2:36
     **/
    private CustomerPlatformVO checkCustomer(List<Object> titleList) {

        String customerCode = titleList.get(1).toString();
        if (StrUtil.isBlank(customerCode)) {
            return null;
        }
        CustomerPlatformInfo customerPlatformInfo = new CustomerPlatformInfo();
        customerPlatformInfo.setCustomerCode(customerCode);
        List<CustomerPlatformVO> customerPlatformInfos = customerPlatformInfoMapper.selectCustomerPlatform(customerPlatformInfo);
        return customerPlatformInfos.get(0);
    }

    /**
     * 校验班次
     *
     * @param shiftNo 班次号
     * @return R 错误信息
     * <AUTHOR>
     * @since 2025/4/25 下午3:47
     **/
    private Shifmanagement checkShift(String shiftNo) {

        if (StrUtil.isBlank(shiftNo)) {
            return null;
        }
        Shifmanagement shift = new Shifmanagement();
        shift.setShiftId(shiftNo);
        shift.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        shift.setDeleteFlag("N");
        shift.setReleaseStatus("1");
        List<Shifmanagement> shifList = shifmanagementMapper.selectShifmanagementListByLike(shift);
        return shifList.get(0);
    }

    /**
     * 校验业务流程单
     *
     * @param shiftManagement , customerPlatform
     * @return R
     * <AUTHOR>
     * @since 2025/4/27 上午11:49
     **/
    private R<T> checkBusinessFlow(Shifmanagement shiftManagement, CustomerPlatformVO customerPlatform) {
        FdBusCost busCost = new FdBusCost();
        busCost.setShiftNo(shiftManagement.getShiftId());
        busCost.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        busCost.setCustomerCode(customerPlatform.getCustomerCode());
        busCost.setDeleteFlag("N");
        List<FdBusCost> list = fdBusCostMapper.selectFdBusCostList(busCost);
        if (CollUtil.isNotEmpty(list)) {
            for (FdBusCost cost : list) {
                if ("1".equals(cost.getAuditStatus())) {
                    return R.error("该客户存在审批中的业务流程单，请先驳回再导入：" + cost.getCostCode());
                } else if ("2".equals(cost.getAuditStatus())) {
                    return R.error("该客户存在已审批的业务流程单，不允许导入：" + cost.getCostCode());
                }
            }
        }
        return R.success();
    }


    /**
     * 校验明细
     *
     * @param dataList 明细
     * @return R 错误信息
     * <AUTHOR>
     * @since 2025/4/25 下午3:58
     **/
    public R<?> validateData(List<Map<String, Object>> dataList, List<SysDictVo> countryList, List<ContainerTypeData> containerTypeDataList, String templateType) {
        if (CollUtil.isEmpty(dataList)) {
            return R.error("导入数据为空");
        }

        StringBuffer errorBuffer = new StringBuffer();
        // 使用线程安全的 StringBuffer 和锁来保证并发安全
        Object lock = new Object();

        IntStream.range(0, dataList.size()).parallel().forEach(i -> {
            Map<String, Object> row = dataList.get(i);

            validateField(row, "箱号", i, errorBuffer, lock, containerNo -> {
                if (!CheckUtil.verifyCntrCode(containerNo)) {
                    appendError(errorBuffer, i, lock, "箱号格式错误：" + containerNo);
                }
            });
            String cellContainerTypeCode = getSafeString(row.get("箱型"));

            if (STANDARD_CONTAINER_TYPES.contains(cellContainerTypeCode)) {
                cellContainerTypeCode = cellContainerTypeCode + GP_SUFFIX;
                row.put("箱型", cellContainerTypeCode);
            }

            validateField(row, "箱型", i, errorBuffer, lock, containerTypeCode -> {
                boolean invalid = containerTypeDataList.stream().noneMatch(typeData -> containerTypeCode.equals(typeData.getContainerTypeCode()));
                if (invalid) {
                    appendError(errorBuffer, i, lock, "箱型代码不存在：" + containerTypeCode);
                }
            });

            validateField(row, "箱属", i, errorBuffer, lock, containerOwner -> {
                if (!CONTAINER_OWNER_0.equals(containerOwner) && !CONTAINER_OWNER_1.equals(containerOwner)) {
                    appendError(errorBuffer, i, lock, "箱属错误：" + containerOwner);
                }
            });

            validateField(row, "类型", i, errorBuffer, lock, containerType -> {
                if (!IdentificationEnum.IDENTIFICATION_I.getValue().equals(containerType) && !IdentificationEnum.IDENTIFICATION_E.getValue().equals(containerType) && !IdentificationEnum.IDENTIFICATION_P.getValue().equals(containerType)) {
                    appendError(errorBuffer, i, lock, "类型错误：" + containerType);
                }
            });

            validateField(row, "品名", i, errorBuffer, lock, null);
            validateField(row, "件数", i, errorBuffer, lock, goodsQty -> {
                if (!NumberUtil.isNumber(goodsQty)) {
                    appendError(errorBuffer, i, lock, "件数不是数字：" + goodsQty);
                }
            });
            validateField(row, "货重", i, errorBuffer, lock, goodsWeight -> {
                if (!NumberUtil.isNumber(goodsWeight)) {
                    appendError(errorBuffer, i, lock, "货重不是数字：" + goodsWeight);
                }
            });
            validateField(row, "箱重", i, errorBuffer, lock, deadWeight -> {
                if (!NumberUtil.isNumber(deadWeight)) {
                    appendError(errorBuffer, i, lock, "箱重不是数字：" + deadWeight);
                }
            });

            validateField(row, "收货人", i, errorBuffer, lock, null);

            validateField(row, "到站", i, errorBuffer, lock, destinationName -> {
                if (CollUtil.isEmpty(getDestinationList(destinationName))) {
                    appendError(errorBuffer, i, lock, "到站不存在：" + destinationName);
                }
            });

            validateField(row, "发站", i, errorBuffer, lock, departureName -> {
                if (CollUtil.isEmpty(getDestinationList(departureName))) {
                    appendError(errorBuffer, i, lock, "发站不存在：" + departureName);
                }
            });

            validateField(row, "目的国", i, errorBuffer, lock, destinationCountry -> {
                if (countryList.stream().noneMatch(country -> destinationCountry.equals(country.getName()))) {
                    appendError(errorBuffer, i, lock, "目的国不存在：" + destinationCountry);
                }
            });

            validateField(row, "是否全程", i, errorBuffer, lock, isFull -> {
                if (!EXCEL_FIELD_YES.equals(isFull) && !EXCEL_FIELD_NO.equals(isFull)) {
                    appendError(errorBuffer, i, lock, "是否全程错误：" + isFull);
                }
            });

            validateField(row, "有色金属", i, errorBuffer, lock, nonFerrous -> {
                if (!EXCEL_FIELD_YES.equals(nonFerrous) && !EXCEL_FIELD_NO.equals(nonFerrous)) {
                    appendError(errorBuffer, i, lock, "有色金属错误：" + nonFerrous);
                }
            });

            //境内运费(人民币)、境外运费、境内运费(人民币)_2、境外运费_2、汇率、汇率_2 不校验是否为空，若不为空校验是否为数字
            String overseasFreightOc = getSafeString(row.get("境外运费"));
            if (StrUtil.isNotBlank(overseasFreightOc) && !NumberUtil.isNumber(overseasFreightOc)) {
                appendError(errorBuffer, i, lock, "境外运费不是数字：" + overseasFreightOc);
            }
            String domesticFreight = getSafeString(row.get("境内运费(人民币)"));
            if (StrUtil.isNotBlank(domesticFreight) && !NumberUtil.isNumber(domesticFreight)) {
                appendError(errorBuffer, i, lock, "境内运费(人民币)不是数字：" + domesticFreight);
            }

            String yfOverseasFreightOc = getSafeString(row.get("境外运费_2"));
            if (StrUtil.isNotBlank(yfOverseasFreightOc) && !NumberUtil.isNumber(yfOverseasFreightOc)) {
                appendError(errorBuffer, i, lock, "应付境外运费不是数字：" + yfOverseasFreightOc);
            }
            String yfDomesticFreight = getSafeString(row.get("境内运费(人民币)_2"));
            if (StrUtil.isNotBlank(yfDomesticFreight) && !NumberUtil.isNumber(yfDomesticFreight)) {
                appendError(errorBuffer, i, lock, "应付境内运费(人民币)不是数字：" + yfDomesticFreight);
            }
            String exchangeRate = getSafeString(row.get("汇率"));
            if (StrUtil.isNotBlank(exchangeRate) && !NumberUtil.isNumber(exchangeRate)) {
                appendError(errorBuffer, i, lock, "汇率不是数字：" + exchangeRate);
            }
            String yfExchangeRate = getSafeString(row.get("汇率_2"));
            if (StrUtil.isNotBlank(yfExchangeRate) && !NumberUtil.isNumber(yfExchangeRate)) {
                appendError(errorBuffer, i, lock, "应付汇率不是数字：" + yfExchangeRate);
            }

            if (templateType.equals(TEMPLATE_MULTIPLE)) {
                //校验订舱客户编码不可为空
                validateField(row, "订舱客户编码", i, errorBuffer, lock, null);
            }
        });


        if (errorBuffer.length() > 0) {
            return R.error(errorBuffer.toString());
        }
        return R.success();
    }


    /**
     * 获取字符串
     *
     * @param obj 单元格对象
     * @return String
     * <AUTHOR>
     * @since 2025/4/27 下午1:55
     **/
    private String getSafeString(Object obj) {
        return obj == null ? "" : obj.toString().trim();
    }

    /**
     * 校验字段
     *
     * @param row, key, index, errorBuffer, lock, consumer
     * <AUTHOR>
     * @since 2025/4/27 下午1:56
     **/
    private void validateField(Map<String, Object> row, String key, int index, StringBuffer errorBuffer, Object lock, Consumer<String> consumer) {
        String value = getSafeString(row.get(key));
        if (value.isEmpty()) {
            appendError(errorBuffer, index, lock, key + "为空；");
        } else if (consumer != null) {
            consumer.accept(value);
        }
    }

    /**
     * 追加错误信息
     *
     * @param errorBuffer, index, lock, errorMsg
     * <AUTHOR>
     * @since 2025/4/27 下午1:56
     **/
    private void appendError(StringBuffer errorBuffer, int index, Object lock, String errorMsg) {
        synchronized (lock) {
            errorBuffer.append("行").append(index + 4).append(errorMsg).append(";").append("\n");
        }
    }


    /**
     * 将每一行的数据转换为 RequesdetailDTO 对象，并将这些对象添加到 details 列表中
     *
     * @param resultList 行数据列表
     * @return List<RequesdetailDTO> RequesdetailDTO 对象列表
     * <AUTHOR>
     * @since 2025/4/25 下午6:00
     **/
    private List<RequesdetailDTO> convertRowsToDetailList(List<Map<String, Object>> resultList, List<SysDictVo> countryList, List<ContainerTypeData> containerTypeDataList, CustomerPlatformVO customerPlatform) {
        List<RequesdetailDTO> detailList = new ArrayList<>();
        for (Map<String, Object> rowMap : resultList) {
            RequesdetailDTO requesdetailDTO = new RequesdetailDTO();
            requesdetailDTO.setOriCustcode(customerPlatform.getCustomerCode());
            requesdetailDTO.setOriCustname(customerPlatform.getCustomerName());
            requesdetailDTO.setContainerNo(getSafeString(rowMap.get("箱号")));
            ContainerTypeData typeData = containerTypeDataList.stream().filter(typeData1 -> typeData1.getContainerTypeCode().equals(getSafeString(rowMap.get("箱型")))).findFirst().orElse(null);
            if (typeData != null) {
                requesdetailDTO.setContainerTypeCode(getSafeString(rowMap.get("箱型")));
                requesdetailDTO.setContainerTypeName(typeData.getContainerTypeName());
                requesdetailDTO.setContainerType(typeData.getContainerTypeSize());
            }

            requesdetailDTO.setBox(CONTAINER_OWNER_0.equals(getSafeString(rowMap.get("箱属"))) ? "0" : "1");
            requesdetailDTO.setIdentification(IdentificationEnum.fromValue(getSafeString(rowMap.get("类型"))));


            requesdetailDTO.setGoodsName(getSafeString(rowMap.get("品名")));

            GoodsDTO goodsDTO = new GoodsDTO();
            goodsDTO.setGoodsNums(getSafeString(rowMap.get("件数")));
            goodsDTO.setGoodsChineseName(getSafeString(rowMap.get("品名")));
            goodsDTO.setGoodsWeight(parseFloatSafe(rowMap.get("货重")));
            goodsDTO.setContainerNo(getSafeString(rowMap.get("箱号")));
            requesdetailDTO.setGoods(Collections.singletonList(goodsDTO));

            requesdetailDTO.setDeadWeight(parseFloatSafe(rowMap.get("箱重")));
            requesdetailDTO.setConsigneeName(getSafeString(rowMap.get("收货人")));
            requesdetailDTO.setGoodsOwner(getSafeString(rowMap.get("货主")));
            requesdetailDTO.setEndCompilation(getStationCodeByName(getSafeString(rowMap.get("到站"))));
            requesdetailDTO.setEndStationName(getSafeString(rowMap.get("到站")));
            requesdetailDTO.setStationCompilation(getStationCodeByName(getSafeString(rowMap.get("发站"))));
            requesdetailDTO.setStartStationName(getSafeString(rowMap.get("发站")));
            requesdetailDTO.setConsigneeCountryCode(getCountryCodeByName(getSafeString(rowMap.get("目的国")), countryList));
            requesdetailDTO.setConsignorCountryCode(getCountryCodeByStationName(requesdetailDTO.getStartStationName(), countryList));
            requesdetailDTO.setTitle(getSafeString(rowMap.get("海关封")));
            requesdetailDTO.setPortAgent(getSafeString(rowMap.get("口岸代理")));
            requesdetailDTO.setIsFull(EXCEL_FIELD_YES.equals(getSafeString(rowMap.get("是否全程"))) ? "1" : "0");
            requesdetailDTO.setNonFerrous(EXCEL_FIELD_YES.equals(getSafeString(rowMap.get("有色金属"))) ? "1" : "0");
            requesdetailDTO.setRemarks(getSafeString(rowMap.get("箱备注")));
            requesdetailDTO.setGoodsOrigin(getSafeString(rowMap.get("境内货源地/目的地")));
            requesdetailDTO.setClearanceNumber(getSafeString(rowMap.get("报关单号")));
            requesdetailDTO.setCustomsSeal(getSafeString(rowMap.get("海关封")));
            requesdetailDTO.setTrainNumber(getSafeString(rowMap.get("车号")));
            requesdetailDTO.setWaybillDemandNumber(getSafeString(rowMap.get("运单需求号")));
            requesdetailDTO.setWaybillLnNumber(getSafeString(rowMap.get("国联运单号")));
            requesdetailDTO.setDestinationCountryCode(getCountryCodeByName(getSafeString(rowMap.get("目的国")), countryList));
            requesdetailDTO.setDestinationCountryName(getSafeString(rowMap.get("目的国")));
            //应收
            //境外运费(原币)
            requesdetailDTO.setOverseasFreightOc(parseBigDecimalSafe(rowMap.get("境外运费")));
            //汇率 若为0 则默认为1
            requesdetailDTO.setExchangeRate(parseBigDecimalSafe(rowMap.get("汇率")).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.valueOf(1) : parseBigDecimalSafe(rowMap.get("汇率")));
            //境外运费(人民币) 境外原币乘以汇率
            requesdetailDTO.setOverseasFreightCny(requesdetailDTO.getOverseasFreightOc().multiply(requesdetailDTO.getExchangeRate()));
            //境内运费
            requesdetailDTO.setDomesticFreight(parseBigDecimalSafe(rowMap.get("境内运费(人民币)")));
            //币种
            requesdetailDTO.setMonetaryType(StrUtil.isNotBlank(getSafeString(rowMap.get("境外币种"))) ? getSafeString(rowMap.get("境外币种")) : "人民币");

            //应付
            //应付境内运费
            requesdetailDTO.setYfDomesticFreight(parseBigDecimalSafe(rowMap.get("境内运费(人民币)_2")));
            //应付境外运费(原币)
            requesdetailDTO.setYfOverseasFreightOc(parseBigDecimalSafe(rowMap.get("境外运费_2")));
            //应付汇率
            requesdetailDTO.setYfExchangeRate(parseBigDecimalSafe(parseBigDecimalSafe(rowMap.get("汇率_2")).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.valueOf(1) : parseBigDecimalSafe(rowMap.get("汇率_2"))));
            //应付境外运费(人民币)
            requesdetailDTO.setYfOverseasFreightCny(requesdetailDTO.getYfOverseasFreightOc().multiply(requesdetailDTO.getYfExchangeRate()));
            //币种
            requesdetailDTO.setYfMonetaryType(StrUtil.isNotBlank(getSafeString(rowMap.get("境外币种_2"))) ? getSafeString(rowMap.get("境外币种_2")) : "人民币");


            detailList.add(requesdetailDTO);
        }
        return detailList;
    }

    /**
     * 转浮点
     *
     * @param obj 对象
     * @return BigDecimal
     * <AUTHOR>
     * @since 2025/4/27 下午2:33
     **/
    private static Float parseFloatSafe(Object obj) {
        try {
            return obj == null ? 0f : Float.parseFloat(obj.toString().trim());
        } catch (NumberFormatException e) {
            return 0f;
        }
    }

    /**
     * 转数字
     * @param obj 对象
     * @return BigDecimal
     * <AUTHOR>
     * @since 2025/4/27 下午2:33
     **/
    private static BigDecimal parseBigDecimalSafe(Object obj) {

        try {
            return obj == null ? BigDecimal.ZERO : new BigDecimal(obj.toString().trim());
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 查询箱型
     *
     * @return List<ContainerTypeData>  箱型列表
     * <AUTHOR>
     * @since 2025/4/25 下午4:19
     **/
    public List<ContainerTypeData> getContainerTypeList() {
        ContainerTypeData containerTypeData = new ContainerTypeData();
        containerTypeData.setDeleteFlag("N");
        return containerTypeDataMapper.selectContainerTypeDataList(containerTypeData);
    }


    /**
     * 查询到站
     *
     * @param stationName 站点名称
     * @return List<StationManagement> 站点列表
     * <AUTHOR>
     * @since 2025/4/25 下午5:34
     **/
    public List<StationManagement> getDestinationList(String stationName) {
        StationManagement stationManagement = new StationManagement();
        stationManagement.setStationName(stationName);
        return stationManagementMapper.selectStationList(stationManagement);
    }

    /**
     * 查询国家列表
     *
     * @return List<SysDictVo> 国家列表
     * <AUTHOR>
     * @since 2025/4/25 下午5:51
     **/
    public List<SysDictVo> getCountryList() {
        String data = remoteAdminService.selectDictByType2("country_type");
        return JSONUtil.toList(JSONUtil.parseArray(data), SysDictVo.class);
    }

    /**
     * 处理重复列名
     *
     * @param originalHeaders 列标题
     * @return List<String> 列标题
     * <AUTHOR>
     * @since 2025/4/25 下午5:26
     **/
    private List<String> fixDuplicateHeaders(List<Object> originalHeaders) {
        Map<String, Integer> nameCount = new HashMap<>(40);
        List<String> fixedHeaders = new ArrayList<>();

        for (Object header : originalHeaders) {
            String name = header == null ? "空列" : header.toString().trim();

            name =StrUtil.replace(name, "（", "(");
            name =StrUtil.replace(name, "）", ")");
            name = name.replace("*", "");
            String specialColumn = "境外运费(原币)";
            if (specialColumn.equals(name)) {
                name = "境外运费";
            }
            int count = nameCount.getOrDefault(name, 0) + 1;
            nameCount.put(name, count);

            if (count == 1) {
                fixedHeaders.add(name);
            } else {
                fixedHeaders.add(name + "_" + count);
            }
        }

        return fixedHeaders;
    }


    /**
     * 根据站点名称获取站点代码
     *
     * @param stationName 站点名称
     * @return java.lang.String
     * <AUTHOR>
     * @since 2025/4/27 上午10:54
     **/
    private String getStationCodeByName(String stationName) {

        List<StationManagement> stationManagementList = getDestinationList(stationName);
        if (CollUtil.isNotEmpty(stationManagementList)) {
            return stationManagementList.get(0).getStationCode();
        }
        return null;
    }

    /**
     * 根据国家名称获取国家代码
     *
     * @param countryName 国家名称
     * @return java.lang.String
     * <AUTHOR>
     * @since 2025/4/27 上午10:53
     **/
    private String getCountryCodeByName(String countryName, List<SysDictVo> countryList) {
        for (SysDictVo sysDictVo : countryList) {
            if (sysDictVo.getName().equals(countryName)) {
                return sysDictVo.getCode();
            }
        }
        return null;
    }


    /**
     * 根据站点名称查询国家代码
     *
     * @param stationName 站点名称
     * @return 国家名称
     * <AUTHOR>
     * @since 2025/4/27 上午10:54
     **/
    private String getCountryCodeByStationName(String stationName, List<SysDictVo> countryList) {
        List<StationManagement> stationManagementList = getDestinationList(stationName);
        if (CollUtil.isNotEmpty(stationManagementList)) {
            String nation = stationManagementList.get(0).getNation();
            for (SysDictVo sysDictVo : countryList) {
                if (sysDictVo.getName().equals(nation)) {
                    return sysDictVo.getCode();
                }
            }
        }
        return null;
    }

    /**
     * 检查文件是否存在未知对象
     *
     * @param file 文件
     * @return R
     * <AUTHOR>
     * @since 2025/4/27 下午6:00
     **/
    private R<T> checkFileForUnknownObjects(MultipartFile file) {
        try {
            boolean isLargeNumberOfObjects = ExcelObjectCheckerUtil.containsLargeNumberOfObjects(file.getInputStream(), 0);
            if (isLargeNumberOfObjects) {
                return R.error("文件中存在未知对象文件，请使用快捷键<Ctrl+G或F5>，进行定位，删除对象后再进行提交！");
            }
        } catch (Exception e) {
            return R.error("检查文件时发生错误：" + e.getMessage());
        }
        return R.success();
    }

    /**
     * 多客户导入
     *
     * @param file 导入文件
     * @return R
     * <AUTHOR>
     * @since 2025/4/27 下午5:34
     **/
    public R createBusinessFlowByMultipleCustomer(MultipartFile file) throws Exception {
        R<T> checkResult = checkFileForUnknownObjects(file);
        if (checkResult.getCode() != 0) {
            return checkResult;
        }
        // 使用流两次需重复打开流
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        // 读取第一行用于校验客户、班次
        List<Object> titleList = reader.readRow(0);
        if (CollUtil.isEmpty(titleList)) {
            return R.error("模板标题为空");
        }
        Object shiftNoCell = titleList.get(1);
        if (shiftNoCell == null) {
            return R.error("班次为空");
        }
        String shiftNo = shiftNoCell.toString();
        Shifmanagement shift = checkShift(shiftNo);
        if (shift == null) {
            return R.error("查询不到班次信息");
        }
        //获取模板明细
        List<Map<String, Object>> resultList = getDetailList(reader);
        //检查客户是否合法
        Set<Object> customerCodes = getUniqueBookingCustCodes(resultList);
        Map<String, CustomerPlatformVO> customerMap = new HashMap<>(10);
        for (Object customerCode : customerCodes) {
            CustomerPlatformInfo customerPlatformInfo = new CustomerPlatformInfo();
            customerPlatformInfo.setCustomerCode(customerCode.toString());
            List<CustomerPlatformVO> customerPlatformInfos = customerPlatformInfoMapper.selectCustomerPlatform(customerPlatformInfo);
            if (CollUtil.isEmpty(customerPlatformInfos)) {
                return R.error("客户编码：" + customerCode + "不存在");
            }
            //校验业务流程单
            R<T> checkBusinessFlow = checkBusinessFlow(shift, customerPlatformInfos.get(0));
            if (checkBusinessFlow.getCode() != 0) {
                return checkBusinessFlow;
            }
            customerMap.put(customerCode.toString(), customerPlatformInfos.get(0));
        }

        List<SysDictVo> countryList = getCountryList();
        //查询箱型
        List<ContainerTypeData> containerTypeDataList = getContainerTypeList();
        //校验明细
        R<?> checkDetail = validateData(resultList, countryList, containerTypeDataList, TEMPLATE_MULTIPLE);
        if (checkDetail.getCode() != 0) {
            return checkDetail;
        }

        //把对象按照订舱客户编码*，然后按照客户分组进行业务单创建
        List<RequesheaderDTO> requesHeaders = new ArrayList<>();
        Map<String, List<Map<String, Object>>> groupedData = resultList.stream()
                .collect(Collectors.groupingBy(row -> getSafeString(row.get("订舱客户编码"))));
        groupedData.forEach((customerCode, rows) -> {
            CustomerPlatformVO customerPlatform = customerMap.get(customerCode);
            RequesheaderDTO requesheaderDTO = new RequesheaderDTO();
            requesheaderDTO.setBookingCustcode(customerPlatform.getCustomerCode());
            requesheaderDTO.setBookingCustname(customerPlatform.getCustomerName());
            requesheaderDTO.setShiftNo(shift.getShiftId());
            requesheaderDTO.setResveredField06(shift.getOverseasAgency());
            requesheaderDTO.setTrip(shift.getTrip());
            requesheaderDTO.setResveredField06(titleList.get(3) == null ? null : titleList.get(3).toString().trim());
            requesheaderDTO.setRemarks(titleList.get(5) == null ? null : titleList.get(5).toString().trim());
            //遍历明细
            List<RequesdetailDTO> requesdetailDTOList = convertRowsToDetailList(rows, countryList, containerTypeDataList, customerPlatform);
            requesheaderDTO.setDetails(requesdetailDTOList);
            requesHeaders.add(requesheaderDTO);
        });


        return fdBusCostService.saveBookingAndWaybillListForCity(requesHeaders);
    }

    /**
     * 取出所有的客户
     *
     * @param resultList 明细
     * @return java.util.Set<java.lang.Object>
     * <AUTHOR>
     * @since 2025/4/27 下午6:10
     **/
    private Set<Object> getUniqueBookingCustCodes(List<Map<String, Object>> resultList) {

        // 使用流过滤并收集 "订舱客户编码" 字段的所有不重复值
        return resultList.stream()
                // 获取"订舱客户编码"字段的值
                .map(row -> row.get("订舱客户编码"))
                // 过滤掉值为 null 的项
                .filter(Objects::nonNull)
                // 转换成 Set 自动去重
                .collect(Collectors.toSet());
    }
}
