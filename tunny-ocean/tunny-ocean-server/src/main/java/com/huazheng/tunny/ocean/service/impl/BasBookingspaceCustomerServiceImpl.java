package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.BasBookingspaceCustomerMapper;
import com.huazheng.tunny.ocean.api.entity.BasBookingspaceCustomer;
import com.huazheng.tunny.ocean.service.BasBookingspaceCustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("basBookingspaceCustomerService")
public class BasBookingspaceCustomerServiceImpl extends ServiceImpl<BasBookingspaceCustomerMapper, BasBookingspaceCustomer> implements BasBookingspaceCustomerService {

    @Autowired
    private BasBookingspaceCustomerMapper basBookingspaceCustomerMapper;

    public BasBookingspaceCustomerMapper getBasBookingspaceCustomerMapper() {
        return basBookingspaceCustomerMapper;
    }

    public void setBasBookingspaceCustomerMapper(BasBookingspaceCustomerMapper basBookingspaceCustomerMapper) {
        this.basBookingspaceCustomerMapper = basBookingspaceCustomerMapper;
    }

    /**
     * 查询订舱客户信息表信息
     *
     * @param rowId 订舱客户信息表ID
     * @return 订舱客户信息表信息
     */
    @Override
    public BasBookingspaceCustomer selectBasBookingspaceCustomerById(String rowId)
    {
        return basBookingspaceCustomerMapper.selectBasBookingspaceCustomerById(rowId);
    }

    /**
     * 查询订舱客户信息表列表
     *
     * @param basBookingspaceCustomer 订舱客户信息表信息
     * @return 订舱客户信息表集合
     */
    @Override
    public List<BasBookingspaceCustomer> selectBasBookingspaceCustomerList(BasBookingspaceCustomer basBookingspaceCustomer)
    {
        return basBookingspaceCustomerMapper.selectBasBookingspaceCustomerList(basBookingspaceCustomer);
    }


    /**
     * 分页模糊查询订舱客户信息表列表
     * @return 订舱客户信息表集合
     */
    @Override
    public Page selectBasBookingspaceCustomerListByLike(Query query)
    {
        BasBookingspaceCustomer basBookingspaceCustomer =  BeanUtil.mapToBean(query.getCondition(), BasBookingspaceCustomer.class,false);
        query.setRecords(basBookingspaceCustomerMapper.selectBasBookingspaceCustomerListByLike(query,basBookingspaceCustomer));
        return query;
    }

    /**
     * 新增订舱客户信息表
     *
     * @param basBookingspaceCustomer 订舱客户信息表信息
     * @return 结果
     */
    @Override
    public int insertBasBookingspaceCustomer(BasBookingspaceCustomer basBookingspaceCustomer)
    {
        return basBookingspaceCustomerMapper.insertBasBookingspaceCustomer(basBookingspaceCustomer);
    }

    /**
     * 修改订舱客户信息表
     *
     * @param basBookingspaceCustomer 订舱客户信息表信息
     * @return 结果
     */
    @Override
    public int updateBasBookingspaceCustomer(BasBookingspaceCustomer basBookingspaceCustomer)
    {
        return basBookingspaceCustomerMapper.updateBasBookingspaceCustomer(basBookingspaceCustomer);
    }


    /**
     * 删除订舱客户信息表
     *
     * @param rowId 订舱客户信息表ID
     * @return 结果
     */
    public int deleteBasBookingspaceCustomerById(String rowId)
    {
        return basBookingspaceCustomerMapper.deleteBasBookingspaceCustomerById( rowId);
    };


    /**
     * 批量删除订舱客户信息表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBasBookingspaceCustomerByIds(Integer[] rowIds)
    {
        return basBookingspaceCustomerMapper.deleteBasBookingspaceCustomerByIds( rowIds);
    }

}
