package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.GoodsData;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 货物数据 服务接口层
 *
 * <AUTHOR>
 * @date 2023-06-29 14:27:37
 */
public interface GoodsDataService extends IService<GoodsData> {
    /**
     * 查询货物数据信息
     *
     * @param rowId 货物数据ID
     * @return 货物数据信息
     */
    public GoodsData selectGoodsDataById(String rowId);

    /**
     * 查询货物数据列表
     *
     * @param goodsData 货物数据信息
     * @return 货物数据集合
     */
    public List<GoodsData> selectGoodsDataList(GoodsData goodsData);

    public List<GoodsData> selectDuplicate(GoodsData goodsData);

    /**
     * 分页模糊查询货物数据列表
     * @return 货物数据集合
     */
    public Page selectGoodsDataListByLike(Query query);

    public Page pageForCity(Query query);

    /**
     * 新增货物数据
     *
     * @param goodsData 货物数据信息
     * @return 结果
     */
    public int insertGoodsData(GoodsData goodsData);

    /**
     * 修改货物数据
     *
     * @param goodsData 货物数据信息
     * @return 结果
     */
    public int updateGoodsData(GoodsData goodsData);

    public int commit(GoodsData goodsData);

    public void commitList(List<String> rowIds);
    /**
     * 删除货物数据
     *
     * @param rowId 货物数据ID
     * @return 结果
     */
    public int deleteGoodsDataById(String rowId);

    /**
     * 批量删除货物数据
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteGoodsDataByIds(Integer[] rowIds);

}

