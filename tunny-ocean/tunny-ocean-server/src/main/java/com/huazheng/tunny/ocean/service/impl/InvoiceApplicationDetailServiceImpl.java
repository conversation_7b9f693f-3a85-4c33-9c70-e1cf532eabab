package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.InvoiceApplicationDetailMapper;
import com.huazheng.tunny.ocean.api.entity.InvoiceApplicationDetail;
import com.huazheng.tunny.ocean.service.InvoiceApplicationDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("invoiceApplicationDetailService")
public class InvoiceApplicationDetailServiceImpl extends ServiceImpl<InvoiceApplicationDetailMapper, InvoiceApplicationDetail> implements InvoiceApplicationDetailService {

    @Autowired
    private InvoiceApplicationDetailMapper invoiceApplicationDetailMapper;

    public InvoiceApplicationDetailMapper getInvoiceApplicationDetailMapper() {
        return invoiceApplicationDetailMapper;
    }

    public void setInvoiceApplicationDetailMapper(InvoiceApplicationDetailMapper invoiceApplicationDetailMapper) {
        this.invoiceApplicationDetailMapper = invoiceApplicationDetailMapper;
    }

    /**
     * 查询开票申请子表信息
     *
     * @param rowId 开票申请子表ID
     * @return 开票申请子表信息
     */
    @Override
    public InvoiceApplicationDetail selectInvoiceApplicationDetailById(String rowId)
    {
        return invoiceApplicationDetailMapper.selectInvoiceApplicationDetailById(rowId);
    }

    /**
     * 查询开票申请子表列表
     *
     * @param invoiceApplicationDetail 开票申请子表信息
     * @return 开票申请子表集合
     */
    @Override
    public List<InvoiceApplicationDetail> selectInvoiceApplicationDetailList(InvoiceApplicationDetail invoiceApplicationDetail)
    {
        return invoiceApplicationDetailMapper.selectInvoiceApplicationDetailList(invoiceApplicationDetail);
    }


    /**
     * 分页模糊查询开票申请子表列表
     * @return 开票申请子表集合
     */
    @Override
    public Page selectInvoiceApplicationDetailListByLike(Query query)
    {
        InvoiceApplicationDetail invoiceApplicationDetail =  BeanUtil.mapToBean(query.getCondition(), InvoiceApplicationDetail.class,false);
        query.setRecords(invoiceApplicationDetailMapper.selectInvoiceApplicationDetailListByLike(query,invoiceApplicationDetail));
        return query;
    }

    /**
     * 新增开票申请子表
     *
     * @param invoiceApplicationDetail 开票申请子表信息
     * @return 结果
     */
    @Override
    public int insertInvoiceApplicationDetail(InvoiceApplicationDetail invoiceApplicationDetail)
    {
        return invoiceApplicationDetailMapper.insertInvoiceApplicationDetail(invoiceApplicationDetail);
    }

    /**
     * 修改开票申请子表
     *
     * @param invoiceApplicationDetail 开票申请子表信息
     * @return 结果
     */
    @Override
    public int updateInvoiceApplicationDetail(InvoiceApplicationDetail invoiceApplicationDetail)
    {
        return invoiceApplicationDetailMapper.updateInvoiceApplicationDetail(invoiceApplicationDetail);
    }


    /**
     * 删除开票申请子表
     *
     * @param rowId 开票申请子表ID
     * @return 结果
     */
    public int deleteInvoiceApplicationDetailById(String rowId)
    {
        return invoiceApplicationDetailMapper.deleteInvoiceApplicationDetailById( rowId);
    };


    /**
     * 批量删除开票申请子表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteInvoiceApplicationDetailByIds(Integer[] rowIds)
    {
        return invoiceApplicationDetailMapper.deleteInvoiceApplicationDetailByIds( rowIds);
    }

}
