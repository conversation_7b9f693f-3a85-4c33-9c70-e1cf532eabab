package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.BasColumnStatus;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 列展示状态表 服务接口层
 *
 * <AUTHOR>
 * @date 2024-04-23 14:48:54
 */
public interface BasColumnStatusService extends IService<BasColumnStatus> {
    /**
     * 查询列展示状态表信息
     *
     * @param id 列展示状态表ID
     * @return 列展示状态表信息
     */
    public BasColumnStatus selectBasColumnStatusById(Integer id);

    /**
     * 查询列展示状态表列表
     *
     * @param basColumnStatus 列展示状态表信息
     * @return 列展示状态表集合
     */
    public List<BasColumnStatus> selectBasColumnStatusList(BasColumnStatus basColumnStatus);


    /**
     * 分页模糊查询列展示状态表列表
     * @return 列展示状态表集合
     */
    public Page selectBasColumnStatusListByLike(Query query);



    /**
     * 新增列展示状态表
     *
     * @param basColumnStatus 列展示状态表信息
     * @return 结果
     */
    public int insertBasColumnStatus(BasColumnStatus basColumnStatus);

    /**
     * 修改列展示状态表
     *
     * @param basColumnStatus 列展示状态表信息
     * @return 结果
     */
    public int updateBasColumnStatus(BasColumnStatus basColumnStatus);

    /**
     * 删除列展示状态表
     *
     * @param id 列展示状态表ID
     * @return 结果
     */
    public int deleteBasColumnStatusById(Integer id);

    /**
     * 批量删除列展示状态表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteBasColumnStatusByIds(Integer[] ids);

}

