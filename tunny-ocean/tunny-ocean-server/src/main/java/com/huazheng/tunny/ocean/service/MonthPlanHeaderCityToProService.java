package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.MonthPlanHeaderCityToPro;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import io.swagger.models.auth.In;

import java.util.List;

/**
 * 月计划申请表(市平台提交到省平台) 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-07 11:49:40
 */
public interface MonthPlanHeaderCityToProService extends IService<MonthPlanHeaderCityToPro> {
    /**
     * 查询月计划申请表(市平台提交到省平台)信息
     *
     * @param rowId 月计划申请表(市平台提交到省平台)ID
     * @return 月计划申请表(市平台提交到省平台)信息
     */
    public MonthPlanHeaderCityToPro selectMonthPlanHeaderCityToProById(String rowId);

    /**
     * 查询月计划申请表(市平台提交到省平台)列表
     *
     * @param monthPlanHeaderCityToPro 月计划申请表(市平台提交到省平台)信息
     * @return 月计划申请表(市平台提交到省平台)集合
     */
    public List<MonthPlanHeaderCityToPro> selectMonthPlanHeaderCityToProList(MonthPlanHeaderCityToPro monthPlanHeaderCityToPro);


    /**
     * 分页模糊查询月计划申请表(市平台提交到省平台)列表
     * @return 月计划申请表(市平台提交到省平台)集合
     */
    public Page selectMonthPlanHeaderCityToProListByLike(Query query);

    public Page selectMonthPlanHeaderCityToProListByLike1(Query query);

    public Integer selectMonthPlanHeaderCityToProListByLikeCount(MonthPlanHeaderCityToPro monthPlanHeaderCityToPro);

    /**
     * 新增月计划申请表(市平台提交到省平台)
     *
     * @param monthPlanHeaderCityToPro 月计划申请表(市平台提交到省平台)信息
     * @return 结果
     */
    public int insertMonthPlanHeaderCityToPro(MonthPlanHeaderCityToPro monthPlanHeaderCityToPro);

    /**
     * 修改月计划申请表(市平台提交到省平台)
     *
     * @param monthPlanHeaderCityToPro 月计划申请表(市平台提交到省平台)信息
     * @return 结果
     */
    public int updateMonthPlanHeaderCityToPro(MonthPlanHeaderCityToPro monthPlanHeaderCityToPro);

    public int updateMonthPlanHeaderCityToProByNo(MonthPlanHeaderCityToPro monthPlanHeaderCityToPro);

    /**
     * 删除月计划申请表(市平台提交到省平台)
     *
     * @param monthPlanHeaderCityToPro 月计划申请表(市平台提交到省平台)ID
     * @return 结果
     */
    public int deleteMonthPlanHeaderCityToProById(MonthPlanHeaderCityToPro monthPlanHeaderCityToPro);

    /**
     * 批量删除月计划申请表(市平台提交到省平台)
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteMonthPlanHeaderCityToProByIds(Integer[] rowIds);

}

