package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.WaybillContainerchargeHeaderMapper;
import com.huazheng.tunny.ocean.api.entity.WaybillContainerchargeHeader;
import com.huazheng.tunny.ocean.service.WaybillContainerchargeHeaderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("waybillContainerchargeHeaderService")
public class WaybillContainerchargeHeaderServiceImpl extends ServiceImpl<WaybillContainerchargeHeaderMapper, WaybillContainerchargeHeader> implements WaybillContainerchargeHeaderService {

    @Autowired
    private WaybillContainerchargeHeaderMapper waybillContainerchargeHeaderMapper;

    public WaybillContainerchargeHeaderMapper getWaybillContainerchargeHeaderMapper() {
        return waybillContainerchargeHeaderMapper;
    }

    public void setWaybillContainerchargeHeaderMapper(WaybillContainerchargeHeaderMapper waybillContainerchargeHeaderMapper) {
        this.waybillContainerchargeHeaderMapper = waybillContainerchargeHeaderMapper;
    }

    /**
     * 查询运单费用汇总表-挂账用信息
     *
     * @param rowId 运单费用汇总表-挂账用ID
     * @return 运单费用汇总表-挂账用信息
     */
    @Override
    public WaybillContainerchargeHeader selectWaybillContainerchargeHeaderById(String rowId) {
        return waybillContainerchargeHeaderMapper.selectWaybillContainerchargeHeaderById(rowId);
    }

    /**
     * 查询运单费用汇总表-挂账用列表
     *
     * @param waybillContainerchargeHeader 运单费用汇总表-挂账用信息
     * @return 运单费用汇总表-挂账用集合
     */
    @Override
    public List<WaybillContainerchargeHeader> selectWaybillContainerchargeHeaderList(WaybillContainerchargeHeader waybillContainerchargeHeader) {
        return waybillContainerchargeHeaderMapper.selectWaybillContainerchargeHeaderList(waybillContainerchargeHeader);
    }


    /**
     * 分页模糊查询运单费用汇总表-挂账用列表
     *
     * @return 运单费用汇总表-挂账用集合
     */
    @Override
    public Page selectWaybillContainerchargeHeaderListByLike(Query query) {
        WaybillContainerchargeHeader waybillContainerchargeHeader = BeanUtil.mapToBean(query.getCondition(), WaybillContainerchargeHeader.class, false);
        //0-市平台查询财务 1-省平台财务
        String str = "0";
        if(str.equals(waybillContainerchargeHeader.getResveredField01())){
            List<WaybillContainerchargeHeader> waybillContainerchargeHeaders = waybillContainerchargeHeaderMapper.selectWaybillContainerchargeHeaderListByLike(query, waybillContainerchargeHeader);
            query.setRecords(waybillContainerchargeHeaders);
        }
        return query;
    }

    /**
     * 新增运单费用汇总表-挂账用
     *
     * @param waybillContainerchargeHeader 运单费用汇总表-挂账用信息
     * @return 结果
     */
    @Override
    public int insertWaybillContainerchargeHeader(WaybillContainerchargeHeader waybillContainerchargeHeader) {
        return waybillContainerchargeHeaderMapper.insertWaybillContainerchargeHeader(waybillContainerchargeHeader);
    }

    /**
     * 修改运单费用汇总表-挂账用
     *
     * @param waybillContainerchargeHeader 运单费用汇总表-挂账用信息
     * @return 结果
     */
    @Override
    public int updateWaybillContainerchargeHeader(WaybillContainerchargeHeader waybillContainerchargeHeader) {
        return waybillContainerchargeHeaderMapper.updateWaybillContainerchargeHeader(waybillContainerchargeHeader);
    }

    @Override
    public int updateFee(WaybillContainerchargeHeader waybillContainerchargeHeader) {
        return waybillContainerchargeHeaderMapper.updateFee(waybillContainerchargeHeader);
    }


    /**
     * 删除运单费用汇总表-挂账用
     *
     * @param rowId 运单费用汇总表-挂账用ID
     * @return 结果
     */
    public int deleteWaybillContainerchargeHeaderById(String rowId) {
        return waybillContainerchargeHeaderMapper.deleteWaybillContainerchargeHeaderById(rowId);
    }

    ;


    /**
     * 批量删除运单费用汇总表-挂账用对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteWaybillContainerchargeHeaderByIds(Integer[] rowIds) {
        return waybillContainerchargeHeaderMapper.deleteWaybillContainerchargeHeaderByIds(rowIds);
    }

}
