package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.BasRoleMenu;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 角色菜单关联表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-01 10:36:52
 */
public interface BasRoleMenuService extends IService<BasRoleMenu> {
    /**
     * 查询角色菜单关联表信息
     *
     * @param rowId 角色菜单关联表ID
     * @return 角色菜单关联表信息
     */
    public BasRoleMenu selectBasRoleMenuById(String rowId);

    /**
     * 查询角色菜单关联表列表
     *
     * @param basRoleMenu 角色菜单关联表信息
     * @return 角色菜单关联表集合
     */
    public List<BasRoleMenu> selectBasRoleMenuList(BasRoleMenu basRoleMenu);


    /**
     * 分页模糊查询角色菜单关联表列表
     * @return 角色菜单关联表集合
     */
    public Page selectBasRoleMenuListByLike(Query query);



    /**
     * 新增角色菜单关联表
     *
     * @param basRoleMenu 角色菜单关联表信息
     * @return 结果
     */
    public int insertBasRoleMenu(BasRoleMenu basRoleMenu);

    /**
     * 修改角色菜单关联表
     *
     * @param basRoleMenu 角色菜单关联表信息
     * @return 结果
     */
    public int updateBasRoleMenu(BasRoleMenu basRoleMenu);

    /**
     * 删除角色菜单关联表
     *
     * @param rowId 角色菜单关联表ID
     * @return 结果
     */
    public int deleteBasRoleMenuById(String rowId);

    /**
     * 批量删除角色菜单关联表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteBasRoleMenuByIds(Integer[] rowIds);

}

