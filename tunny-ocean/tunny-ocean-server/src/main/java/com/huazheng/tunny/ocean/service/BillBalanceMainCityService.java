package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.BalanceDetailDTO;
import com.huazheng.tunny.ocean.api.dto.BillBalanceApproveInfoDTO;
import com.huazheng.tunny.ocean.api.dto.BillBalanceMainCityDTO;
import com.huazheng.tunny.ocean.api.dto.BindingSubBillDTO;
import com.huazheng.tunny.ocean.api.entity.BillBalanceMainCity;
import com.huazheng.tunny.ocean.api.vo.BillBalanceMainCityDetailVO;
import com.huazheng.tunny.ocean.api.vo.SelectPayUserVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 应收账单结算（市） 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-06-15 14:15:46
 */
public interface BillBalanceMainCityService extends IService<BillBalanceMainCity> {
    /**
     * 查询应收账单结算（市）信息
     *
     * @param id 应收账单结算（市）ID
     * @return 应收账单结算（市）信息
     */
    public BillBalanceMainCityDetailVO selectBillBalanceMainCityById(Integer id, Integer pageType);

    /**
     * 查询应收账单结算（市）列表
     *
     * @param billBalanceMainCity 应收账单结算（市）信息
     * @return 应收账单结算（市）集合
     */
    public List<BillBalanceMainCity> selectBillBalanceMainCityList(BillBalanceMainCity billBalanceMainCity);


    /**
     * 分页模糊查询应收账单结算（市）列表
     *
     * @return 应收账单结算（市）集合
     */
    public Page selectBillBalanceMainCityListByLike(Query query);

    public Integer selectBillBalanceMainCityCount(BillBalanceMainCity billBalanceMainCity);


    /**
     * 新增应收账单结算（市）
     *
     * @param billBalanceMainCityDTO 应收账单结算（市）信息
     * @return 结果
     */
    public R insertBillBalanceMainCity(BillBalanceMainCityDTO billBalanceMainCityDTO);

    /**
     * 修改应收账单结算（市）
     *
     * @param billBalanceMainCityDTO 应收账单结算（市）信息
     * @return 结果
     */
    public R updateBillBalanceMainCity(BillBalanceMainCityDTO billBalanceMainCityDTO);

    public R updateRevoke(Integer id);

    R bindingSubBill(BindingSubBillDTO bindingSubBillDTO);

    R removeBindingSubBill(BindingSubBillDTO bindingSubBillDTO);

    /**
     * 删除应收账单结算（市）
     *
     * @param id 应收账单结算（市）ID
     * @return 结果
     */
    public int deleteBillBalanceMainCityById(Integer id);

    /**
     * 批量删除应收账单结算（市）
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteBillBalanceMainCityByIds(Integer[] ids);

    /**
     * 查询余额列表
     *
     * @param query
     * @return
     */
    public Page selectBalanceDetail(Query query);

    /**
     * 绑定余额数据
     *
     * @param balanceDetailDTOS
     * @return
     */
    public R bindingBalance(List<BalanceDetailDTO> balanceDetailDTOS);

    /**
     * 解除绑定余额数据
     *
     * @param balanceDetailDTO
     * @return
     */
    public R removeBalance(BalanceDetailDTO balanceDetailDTO);

    /**
     * 修改绑定余额的本次抵扣金额
     *
     * @param balanceDetailDTO
     * @return
     */
    public R updateBalanceAmount(BalanceDetailDTO balanceDetailDTO);

    /**
     * 新增审批明细记录
     *
     * @param approveInfoDTO
     * @return
     */
    public R saveApproveInfo(BillBalanceApproveInfoDTO approveInfoDTO);

    public R updateRemoveById(Integer id, Integer pageType);

    public List<SelectPayUserVO> selectPayeeUserList();

    public List<SelectPayUserVO> selectRevenueUserList();

    public void updateBillStatusByCode(String jsCode, String billStatus, String platformCode);

    public Page pageByProvince(Query query);

    public Page pageByProvinceTwo(Query query);

    R updateBillOff(Map<String, Object> params);

    R submitWriteOff(BillBalanceMainCityDTO billBalanceMainCityDTO);

    R exportFreightAccount(BillBalanceMainCity billBalanceMainCity, HttpServletResponse response);

    R exportFreightAccountTwo(BillBalanceMainCity billBalanceMainCity, HttpServletResponse response);

    public R confirmAndVerify(BillBalanceMainCityDTO billBalanceMainCityDTO);

    R exportFreightAccountCity(BillBalanceMainCity billBalanceMainCity, HttpServletResponse response);
}

