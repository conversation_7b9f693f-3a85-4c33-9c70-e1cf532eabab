package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.FdBusCostDetailDTO;
import com.huazheng.tunny.ocean.api.entity.FdBusCost;
import com.huazheng.tunny.ocean.api.entity.FdBusCostDetail;
import com.huazheng.tunny.ocean.api.entity.WaybillContainerInfo;

import java.util.List;
import java.util.Map;

/**
 * 业务流程单子表 服务接口层
 *
 * <AUTHOR>
 * @date 2024-05-23 17:10:53
 */
public interface FdBusCostDetailService extends IService<FdBusCostDetail> {
    /**
     * 查询业务流程单子表信息
     *
     * @param id 业务流程单子表ID
     * @return 业务流程单子表信息
     */
    public FdBusCostDetail selectFdBusCostDetailById(Integer id);

    /**
     * 查询业务流程单子表列表
     *
     * @param fdBusCostDetail 业务流程单子表信息
     * @return 业务流程单子表集合
     */
    public List<FdBusCostDetail> selectFdBusCostDetailList(FdBusCostDetail fdBusCostDetail);


    /**
     * 分页模糊查询业务流程单子表列表
     *
     * @return 业务流程单子表集合
     */
    public Page selectFdBusCostDetailListByLike(Query query);

    public Map reveivePage(Query query);

    public Map payPage(Query query);

    /**
     * 新增业务流程单子表
     *
     * @param fdBusCostDetail 业务流程单子表信息
     * @return 结果
     */
    public int insertFdBusCostDetail(FdBusCostDetail fdBusCostDetail);

    public R saveReceive(FdBusCostDetail fdBusCostDetail);

    public R saveReceiveList(FdBusCostDetail fdBusCostDetail);

    public R saveList(FdBusCost fdBusCost);

    public R savePay(FdBusCostDetail fdBusCostDetail);

    public R savePayList(FdBusCostDetail fdBusCostDetail);

    /**
     * 修改业务流程单子表
     *
     * @param fdBusCostDetail 业务流程单子表信息
     * @return 结果
     */
    public int updateFdBusCostDetail(FdBusCostDetail fdBusCostDetail);

    public int updateReceive(FdBusCostDetail fdBusCostDetail);

    public int updatePay(FdBusCostDetail fdBusCostDetail);

    /**
     * 删除业务流程单子表
     *
     * @param id 业务流程单子表ID
     * @return 结果
     */
    public int deleteFdBusCostDetailById(Integer id);

    /**
     * 批量删除业务流程单子表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public R deleteFdBusCostDetailByIds(Integer[] ids);

    public List<WaybillContainerInfo> selectListWithBusCost(FdBusCostDetail fdBusCostDetail);

    public List<WaybillContainerInfo> selectListWithBusCostDel(FdBusCostDetail fdBusCostDetail);

    public List<WaybillContainerInfo> selectContainerTypeCode(FdBusCostDetail fdBusCostDetail);

    public List<WaybillContainerInfo> selectEndCompilation(FdBusCostDetail fdBusCostDetail);

    List<FdBusCostDetail> selectFdExchangeRateByCostInfo(String containerNumber, String bbCode, String ssCode, String billCode);

    List<FdBusCostDetailDTO> selectFdBusCostDetailFromLower(FdBusCostDetailDTO fdBusCostDetail);

    Map<String, Object> weChatList(FdBusCostDetail fdBusCostDetail);
}

