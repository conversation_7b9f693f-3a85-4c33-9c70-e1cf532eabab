package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.FdShippingAccount;
import com.huazheng.tunny.ocean.mapper.FdShippingAccoundetailSubMapper;
import com.huazheng.tunny.ocean.api.entity.FdShippingAccoundetailSub;
import com.huazheng.tunny.ocean.mapper.FdShippingAccountMapper;
import com.huazheng.tunny.ocean.service.FdShippingAccoundetailSubService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

@Service("fdShippingAccoundetailSubService")
public class FdShippingAccoundetailSubServiceImpl extends ServiceImpl<FdShippingAccoundetailSubMapper, FdShippingAccoundetailSub> implements FdShippingAccoundetailSubService {

    @Autowired
    private FdShippingAccoundetailSubMapper fdShippingAccoundetailSubMapper;
    @Autowired
    private FdShippingAccountMapper fdShippingAccountMapper;

    /**
     * 查询发运台账分表信息
     *
     * @param rowId 发运台账分表ID
     * @return 发运台账分表信息
     */
    @Override
    public FdShippingAccoundetailSub selectFdShippingAccoundetailSubById(Long rowId)
    {
        return fdShippingAccoundetailSubMapper.selectFdShippingAccoundetailSubById(rowId);
    }

    /**
     * 查询发运台账分表列表
     *
     * @param fdShippingAccoundetailSub 发运台账分表信息
     * @return 发运台账分表集合
     */
    @Override
    public List<FdShippingAccoundetailSub> selectFdShippingAccoundetailSubList(FdShippingAccoundetailSub fdShippingAccoundetailSub)
    {
        return fdShippingAccoundetailSubMapper.selectFdShippingAccoundetailSubList(fdShippingAccoundetailSub);
    }


    /**
     * 分页模糊查询发运台账分表列表
     * @return 发运台账分表集合
     */
    @Override
    public Page selectFdShippingAccoundetailSubListByLike(Query query)
    {
        FdShippingAccoundetailSub fdShippingAccoundetailSub =  BeanUtil.mapToBean(query.getCondition(), FdShippingAccoundetailSub.class,false);
        query.setRecords(fdShippingAccoundetailSubMapper.selectFdShippingAccoundetailSubListByLike(query,fdShippingAccoundetailSub));
        return query;
    }

    /**
     * 新增发运台账分表
     *
     * @param fdShippingAccoundetailSub 发运台账分表信息
     * @return 结果
     */
    @Override
    public int insertFdShippingAccoundetailSub(FdShippingAccoundetailSub fdShippingAccoundetailSub)
    {
        return fdShippingAccoundetailSubMapper.insertFdShippingAccoundetailSub(fdShippingAccoundetailSub);
    }

    /**
     * 修改发运台账分表
     *
     * @param fdShippingAccoundetailSub 发运台账分表信息
     * @return 结果
     */
    @Override
    public int updateFdShippingAccoundetailSub(FdShippingAccoundetailSub fdShippingAccoundetailSub)
    {
        return fdShippingAccoundetailSubMapper.updateFdShippingAccoundetailSub(fdShippingAccoundetailSub);
    }


    /**
     * 删除发运台账分表
     *
     * @param rowId 发运台账分表ID
     * @return 结果
     */
    public int deleteFdShippingAccoundetailSubById(Long rowId)
    {
        return fdShippingAccoundetailSubMapper.deleteFdShippingAccoundetailSubById( rowId);
    };


    /**
     * 批量删除发运台账分表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFdShippingAccoundetailSubByIds(Integer[] rowIds)
    {
        return fdShippingAccoundetailSubMapper.deleteFdShippingAccoundetailSubByIds( rowIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R insertFdShippingAccoundetailSubList(List<FdShippingAccoundetailSub> list, String accountCode)
    {
        if(StrUtil.isNotEmpty(accountCode)){
            FdShippingAccount fdShippingAccount = new FdShippingAccount();
            fdShippingAccount.setAccountCode(accountCode);
            fdShippingAccount.setStatus("4");
            fdShippingAccountMapper.updateFdShippingAccountByAccountCode(fdShippingAccount);
            if(CollUtil.isNotEmpty(list)){
                //删除历史数据
                FdShippingAccoundetailSub del = new FdShippingAccoundetailSub();
                del.setAccountCode(accountCode);
                del.setDeleteFlag("Y");
                fdShippingAccoundetailSubMapper.updateFdShippingAccoundetailSubByAccountCode(del);

                for (FdShippingAccoundetailSub fdShippingAccoundetailSub:list
                ) {
                    //如果客户没有导入铁路运费，则默认填入省运费
                    if(fdShippingAccoundetailSub.getRrOverseasFreightOc()==null || fdShippingAccoundetailSub.getRrOverseasFreightOc().compareTo(BigDecimal.ZERO)==0){
                        fdShippingAccoundetailSub.setRrOverseasFreightOc(fdShippingAccoundetailSub.getOverseasFreightOc());
                    }
                    if(fdShippingAccoundetailSub.getRrOverseasFreightCny()==null || fdShippingAccoundetailSub.getRrOverseasFreightCny().compareTo(BigDecimal.ZERO)==0){
                        fdShippingAccoundetailSub.setOverseasFreightCny(fdShippingAccoundetailSub.getOverseasFreightCny());
                    }
                    if(fdShippingAccoundetailSub.getRrDomesticFreight()==null || fdShippingAccoundetailSub.getRrDomesticFreight().compareTo(BigDecimal.ZERO)==0){
                        fdShippingAccoundetailSub.setRrDomesticFreight(fdShippingAccoundetailSub.getDomesticFreight());
                    }
                    fdShippingAccoundetailSubMapper.insertFdShippingAccoundetailSub(fdShippingAccoundetailSub);
                }
            }
            return new R<>(200,Boolean.TRUE,null,"二次导入完成！");
        }else{
            return new R<>(500,Boolean.FALSE,null,"台账编码不存在！");
        }

    }

}
