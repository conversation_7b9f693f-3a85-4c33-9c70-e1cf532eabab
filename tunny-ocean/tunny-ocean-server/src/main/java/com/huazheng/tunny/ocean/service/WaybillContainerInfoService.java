package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.BookingRequesdetail;
import com.huazheng.tunny.ocean.api.entity.WaybillContainerInfo;
import com.huazheng.tunny.ocean.api.entity.WaybillHeader;
import com.huazheng.tunny.ocean.api.vo.ContainerInfoVO;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 运单集装箱信息表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-05 10:57:25
 */
public interface WaybillContainerInfoService extends IService<WaybillContainerInfo> {
    /**
     * 查询运单集装箱信息表信息
     *
     * @param rowId 运单集装箱信息表ID
     * @return 运单集装箱信息表信息
     */
    public WaybillContainerInfo selectWaybillContainerInfoById(String rowId);

    /**
     * 查询运单集装箱信息表列表
     *
     * @param waybillContainerInfo 运单集装箱信息表信息
     * @return 运单集装箱信息表集合
     */
    public List<WaybillContainerInfo> selectWaybillContainerInfoList(WaybillContainerInfo waybillContainerInfo);

    public List<WaybillContainerInfo> selectWaybillContainerInfoList2(WaybillContainerInfo waybillContainerInfo);


    /**
     * 分页模糊查询运单集装箱信息表列表
     *
     * @return 运单集装箱信息表集合
     */
    public Page selectWaybillContainerInfoListByLike(Query query);

    public Page selectWaybillContainerInfoAndBasChangeboxDetail(Query query);

    public String selectBasChangeboxDetailForSh(String data);

    /**
     * 当前运单下集装箱、货物、参与方分页信息
     */
    public Page selectConGoodsPantsPage(Query query);

    public Page selectConGoodsPantsPageSh(Query query);

    /**
     * 当前班次下集装箱详细信息分页
     */
    public Page selectConInfoPage(Query query);


    /**
     * 新增运单集装箱信息表
     *
     * @param waybillContainerInfo 运单集装箱信息表信息
     * @return 结果
     */
    public int insertWaybillContainerInfo(WaybillContainerInfo waybillContainerInfo);

    /**
     * 批量修改运单集装箱信息表
     *
     * @param waybillContainerInfo 运单集装箱信息表信息
     * @return 结果
     */
    public int updateContainerInfoBatch(List<WaybillContainerInfo> waybillContainerInfo);

    public void insertOrUpdateContainerInfo(List<WaybillContainerInfo> waybillContainerInfo, List<String> waybillCodes);

    /**
     * 修改运单集装箱信息表
     *
     * @param waybillContainerInfo 运单集装箱信息表信息
     * @return 结果
     */
    public int updateContainerInfo(WaybillContainerInfo waybillContainerInfo);

    public int updateContainerInfoBatchs(List<WaybillContainerInfo> waybillContainerInfo);

    public int updateContainerInfo1(WaybillContainerInfo waybillContainerInfo);

    /**
     * 删除运单集装箱信息表
     *
     * @param waybillContainerInfo 运单集装箱信息表ID
     * @return 结果
     */
    public int deleteWaybillContainerInfoById(WaybillContainerInfo waybillContainerInfo);

    /**
     * 批量删除运单集装箱信息表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteWaybillContainerInfoByIds(Integer[] rowIds);

    /**
     * 运费总金额
     */
    public BigDecimal getTotalAmount(String waybillNo);

    public BigDecimal getTotalAmountList(List<String> stringList);

    public Map<String, Object> customerBill(Query query);

    public List<ContainerInfoVO> customerBill2(WaybillContainerInfo waybillContainerInfo);

    public BigDecimal customerBillCapital(WaybillContainerInfo waybillContainerInfo);

    public List<Map<String, String>> selectNumByCountry(WaybillContainerInfo info);

    public List<Map<String, String>> selectNumFromCountry(WaybillContainerInfo info);

    public Integer selectConInfoByCount(WaybillContainerInfo info);

    public List<WaybillContainerInfo> selectConInfoByContainerType(WaybillContainerInfo infos);

    public List<WaybillContainerInfo> selectWaybillContainerInfoLists(WaybillContainerInfo infos);

    public int updateWaybillContainerInfos(List<WaybillContainerInfo> waybillContainerInfo);

    public WaybillContainerInfo checkWaybillContainerInfo(WaybillContainerInfo info);

    public List<WaybillContainerInfo> selectWaybillInfo(WaybillContainerInfo info);

    public String getNum(String shiftNo);

    public R feeImported2(MultipartFile file, String waybillNo);

    Map<String, Object> getContainerTypeNum(WaybillContainerInfo waybillContainerInfo);

    R importFileTzTemplate(MultipartFile file, String rowId);

    void getWayBillCodes(WaybillHeader waybillHeader, List<String> waybillCodes);

    public Map<String,Object> weChatList(WaybillContainerInfo waybillContainerInfo);
}

