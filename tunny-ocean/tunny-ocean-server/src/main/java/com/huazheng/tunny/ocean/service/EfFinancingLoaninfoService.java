package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfFinancingLoaninfo;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 仓单质押银行放款信息 服务接口层
 *
 * <AUTHOR>
 * @date 2023-02-08 15:13:58
 */
public interface EfFinancingLoaninfoService extends IService<EfFinancingLoaninfo> {
    /**
     * 查询仓单质押银行放款信息信息
     *
     * @param rowId 仓单质押银行放款信息ID
     * @return 仓单质押银行放款信息信息
     */
    public EfFinancingLoaninfo selectEfFinancingLoaninfoById(String rowId);

    /**
     * 查询仓单质押银行放款信息列表
     *
     * @param efFinancingLoaninfo 仓单质押银行放款信息信息
     * @return 仓单质押银行放款信息集合
     */
    public List<EfFinancingLoaninfo> selectEfFinancingLoaninfoList(EfFinancingLoaninfo efFinancingLoaninfo);


    /**
     * 分页模糊查询仓单质押银行放款信息列表
     * @return 仓单质押银行放款信息集合
     */
    public Page selectEfFinancingLoaninfoListByLike(Query query);



    /**
     * 新增仓单质押银行放款信息
     *
     * @param efFinancingLoaninfo 仓单质押银行放款信息信息
     * @return 结果
     */
    public int insertEfFinancingLoaninfo(EfFinancingLoaninfo efFinancingLoaninfo);

    /**
     * 修改仓单质押银行放款信息
     *
     * @param efFinancingLoaninfo 仓单质押银行放款信息信息
     * @return 结果
     */
    public int updateEfFinancingLoaninfo(EfFinancingLoaninfo efFinancingLoaninfo);

    /**
     * 删除仓单质押银行放款信息
     *
     * @param rowId 仓单质押银行放款信息ID
     * @return 结果
     */
    public int deleteEfFinancingLoaninfoById(String rowId);

    /**
     * 批量删除仓单质押银行放款信息
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfFinancingLoaninfoByIds(Integer[] rowIds);

}

