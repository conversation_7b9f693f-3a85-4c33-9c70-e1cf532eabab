package com.huazheng.tunny.ocean.controller.eabookingorder;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.huazheng.tunny.ocean.api.dto.eabusinessprocess.EaBusinessProcessAppendDto;
import com.huazheng.tunny.ocean.api.dto.eabusinessprocess.EaBusinessProcessEditDto;
import com.huazheng.tunny.ocean.util.R;
import com.huazheng.tunny.ocean.api.entity.eabookingorder.EaContainerSummary;
import com.huazheng.tunny.ocean.service.eabookingorder.EaContainerSummaryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 集装箱汇总信息表
 *
 * <AUTHOR> code generator
 * @date 2025-06-16 15:23:42
 */
@Slf4j
@RestController
@RequestMapping("/eacontainersummary")
public class EaContainerSummaryController {

    @Autowired
    private EaContainerSummaryService eaContainerSummaryService;

    /**
    *  箱汇列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public R page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  eaContainerSummaryService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return eaContainerSummaryService.selectEaContainerSummaryListByLike(params);
    }
    /**
    *  应收应付列表
    * @param params
    * @return
    */
    @GetMapping("/list")
    public Map list(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return eaContainerSummaryService.selectEaContainerSummaryList(params);
    }

    /**
     * 信息
     * @param containerSummaryId
     * @return R
     */
    @GetMapping("/{containerSummaryId}")
    public R info(@PathVariable("containerSummaryId") Long containerSummaryId) {
        EaContainerSummary eaContainerSummary =eaContainerSummaryService.selectById(containerSummaryId);
        return new R<>(eaContainerSummary);
    }

    /**
     * 保存
     * @param eaContainerSummary
     * @return R
     */
    @PostMapping
    public R save(@RequestBody EaContainerSummary eaContainerSummary) {
        eaContainerSummaryService.insert(eaContainerSummary);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改运费/杂费
     * @param eaBusinessProcessEditDto
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody EaBusinessProcessEditDto eaBusinessProcessEditDto) {

        return eaContainerSummaryService.updateEaContainerSummaryByOrderCode(eaBusinessProcessEditDto);
    }

    

    /**
     * 删除
     * @param containerSummaryId
     * @return R
     */
    @GetMapping("/del/{containerSummaryId}")
    public R delete(@PathVariable  Long containerSummaryId) {
        eaContainerSummaryService.deleteById(containerSummaryId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param containerSummaryIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<Long> containerSummaryIds) {
        eaContainerSummaryService.deleteBatchIds(containerSummaryIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<EaContainerSummary> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<EaContainerSummary> list = eaContainerSummaryService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), EaContainerSummary.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }
    /**
     * 追加费用
     * @param eaBusinessProcessAppendDto
     * @return R
     */
    @PostMapping("/additionalCharges")
    public R additionalCharges(@RequestBody EaBusinessProcessAppendDto eaBusinessProcessAppendDto) {

        return eaContainerSummaryService.additionalCharges(eaBusinessProcessAppendDto);
    }
}
