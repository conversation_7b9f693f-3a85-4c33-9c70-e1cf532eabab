package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.BasChangeboxContainerInfo;
import com.huazheng.tunny.ocean.api.entity.BasChangeboxRetreat;
import com.huazheng.tunny.ocean.api.entity.FdInvoiceApplicationDetail;
import com.huazheng.tunny.ocean.mapper.BasChangeboxContainerInfoMapper;
import com.huazheng.tunny.ocean.mapper.BasChangeboxRetreatMapper;
import com.huazheng.tunny.ocean.mapper.FdInvoiceApplicationDetailMapper;
import com.huazheng.tunny.ocean.service.BasChangeboxContainerInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service("basChangeboxContainerInfoService")
public class BasChangeboxContainerInfoServiceImpl extends ServiceImpl<BasChangeboxContainerInfoMapper, BasChangeboxContainerInfo> implements BasChangeboxContainerInfoService {

    @Autowired
    private BasChangeboxContainerInfoMapper basChangeboxContainerInfoMapper;

    @Autowired
    private BasChangeboxRetreatMapper basChangeboxRetreatMapper;
    @Autowired
    private FdInvoiceApplicationDetailMapper fdInvoiceApplicationDetailMapper;
    /**
     * 查询换箱集装箱信息表信息
     *
     * @param rowId 换箱集装箱信息表ID
     * @return 换箱集装箱信息表信息
     */
    @Override
    public BasChangeboxContainerInfo selectBasChangeboxContainerInfoById(String rowId) {
        return basChangeboxContainerInfoMapper.selectBasChangeboxContainerInfoById(rowId);
    }

    /**
     * 查询换箱集装箱信息表列表
     *
     * @param basChangeboxContainerInfo 换箱集装箱信息表信息
     * @return 换箱集装箱信息表集合
     */
    @Override
    public List<BasChangeboxContainerInfo> selectBasChangeboxContainerInfoList(BasChangeboxContainerInfo basChangeboxContainerInfo) {
        return basChangeboxContainerInfoMapper.selectBasChangeboxContainerInfoList(basChangeboxContainerInfo);
    }


    /**
     * 分页模糊查询换箱集装箱信息表列表
     *
     * @return 换箱集装箱信息表集合
     */
    @Override
    public Page selectBasChangeboxContainerInfoListByLike(Query query) {
        BasChangeboxContainerInfo basChangeboxContainerInfo = BeanUtil.mapToBean(query.getCondition(), BasChangeboxContainerInfo.class, false);
        query.setRecords(basChangeboxContainerInfoMapper.selectBasChangeboxContainerInfoListByLike(query, basChangeboxContainerInfo));
        return query;
    }

    /**
     * 新增换箱集装箱信息表
     *
     * @param basChangeboxContainerInfo 换箱集装箱信息表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R insertBasChangeboxContainerInfo(BasChangeboxContainerInfo basChangeboxContainerInfo) {
        Boolean flag = checkContainerNo(basChangeboxContainerInfo.getBusinessid(), basChangeboxContainerInfo.getContainerNo());
        if (flag) {
            return new R<>(new Throwable("该箱号已存在"));
        }
        basChangeboxContainerInfo.setRowId(UUID.randomUUID().toString());
        basChangeboxContainerInfo.setBoxnumberStatus("2");
        basChangeboxContainerInfoMapper.insertBasChangeboxContainerInfo(basChangeboxContainerInfo);
        return new R<>(0, Boolean.TRUE, null, "新增成功");
    }

    /**
     * 修改换箱集装箱信息表
     *
     * @param basChangeboxContainerInfo 换箱集装箱信息表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateBasChangeboxContainerInfo(BasChangeboxContainerInfo basChangeboxContainerInfo) {
        EntityWrapper<BasChangeboxContainerInfo> entityWrapper = new EntityWrapper<>();
        entityWrapper.eq("businessID", basChangeboxContainerInfo.getBusinessid());
        entityWrapper.eq("container_no", basChangeboxContainerInfo.getContainerNo());
        entityWrapper.eq("delete_flag", "N");
        entityWrapper.in("boxnumber_status", "0,2");
        entityWrapper.ne("row_id", basChangeboxContainerInfo.getRowId());
        List<BasChangeboxContainerInfo> infos = this.selectList(entityWrapper);
        if (CollUtil.isNotEmpty(infos)) {
            return new R<>(new Throwable("该箱号已存在"));
        }
        basChangeboxContainerInfoMapper.updateBasChangeboxContainerInfo(basChangeboxContainerInfo);
        return new R<>(0, Boolean.TRUE, null, "修改成功");
    }

    @Override
    public int deleteObj(BasChangeboxContainerInfo basChangeboxContainerInfo) {
        basChangeboxContainerInfo.setDeleteFlag("Y");
        return basChangeboxContainerInfoMapper.updateBasChangeboxContainerInfo(basChangeboxContainerInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R changeBoxnumberStatus(BasChangeboxContainerInfo basChangeboxContainerInfo) {
        if ("0".equals(basChangeboxContainerInfo.getBoxnumberStatus())) {
            Boolean flag = checkContainerNo(basChangeboxContainerInfo.getBusinessid(), basChangeboxContainerInfo.getContainerNo());
            if (flag) {
                return new R<>(new Throwable("该箱号已存在"));
            }
        } else if ("1".equals(basChangeboxContainerInfo.getBoxnumberStatus())) {
            BasChangeboxRetreat sel = new BasChangeboxRetreat();
            sel.setBusinessid(basChangeboxContainerInfo.getBusinessid());
            sel.setDeleteFlag("N");
            List<BasChangeboxRetreat> basChangeboxRetreats = basChangeboxRetreatMapper.selectBasChangeboxRetreatList(sel);
            if(CollUtil.isNotEmpty(basChangeboxRetreats)){
                FdInvoiceApplicationDetail sel2 = new FdInvoiceApplicationDetail();
                sel2.setShiftNo(basChangeboxRetreats.get(0).getShiftNo());
                sel2.setContainerNumber(basChangeboxContainerInfo.getContainerNo());
                sel2.setDelFlag("N");
                List<FdInvoiceApplicationDetail> invoiceApplicationDetails = fdInvoiceApplicationDetailMapper.selectFdInvoiceApplicationDetailList2(sel2);
                if(CollUtil.isNotEmpty(invoiceApplicationDetails)){
                    return new R<>(new Throwable("该箱号已开票，无法撤箱"));
                }
            }
        }
        basChangeboxContainerInfoMapper.updateBasChangeboxContainerInfo(basChangeboxContainerInfo);
        return new R<>(0, Boolean.TRUE, null, "修改成功");
    }


    /**
     * 删除换箱集装箱信息表
     *
     * @param rowId 换箱集装箱信息表ID
     * @return 结果
     */
    public int deleteBasChangeboxContainerInfoById(String rowId) {
        return basChangeboxContainerInfoMapper.deleteBasChangeboxContainerInfoById(rowId);
    }

    ;


    /**
     * 批量删除换箱集装箱信息表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBasChangeboxContainerInfoByIds(Integer[] rowIds) {
        return basChangeboxContainerInfoMapper.deleteBasChangeboxContainerInfoByIds(rowIds);
    }

    /**
     * 当前运单下集装箱、货物、参与方分页信息
     */
    @Override
    public List<BasChangeboxContainerInfo> selectConGoodsPantsAdd(BasChangeboxContainerInfo basChangeboxContainerInfo) {
        basChangeboxContainerInfo.setBoxnumberStatus("2");
        return basChangeboxContainerInfoMapper.selectConGoodsPantsList(basChangeboxContainerInfo);

    }

    @Override
    public List<BasChangeboxContainerInfo> selectConGoodsPantsDel(BasChangeboxContainerInfo basChangeboxContainerInfo) {
        return basChangeboxContainerInfoMapper.selectConGoodsPantsDel(basChangeboxContainerInfo);

    }

    public Boolean checkContainerNo(String businessid, String containerNo) {
        Boolean flag = false;
        EntityWrapper<BasChangeboxContainerInfo> entityWrapper = new EntityWrapper<>();
        entityWrapper.eq("businessID", businessid);
        entityWrapper.eq("container_no", containerNo);
        entityWrapper.eq("delete_flag", "N");
        entityWrapper.in("boxnumber_status", "0,2");
        List<BasChangeboxContainerInfo> infos = this.selectList(entityWrapper);
        if (CollUtil.isNotEmpty(infos)) {
            flag = true;
        }
        return flag;
    }
}
