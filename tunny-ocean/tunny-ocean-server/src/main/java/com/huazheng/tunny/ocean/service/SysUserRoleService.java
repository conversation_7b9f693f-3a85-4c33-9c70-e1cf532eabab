package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.SysUserRole;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 用户角色表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-08-06 17:19:42
 */
public interface SysUserRoleService extends IService<SysUserRole> {
    /**
     * 查询用户角色表信息
     *
     * @param userId 用户角色表ID
     * @return 用户角色表信息
     */
    public SysUserRole selectSysUserRoleById(Integer userId);

    /**
     * 查询用户角色表列表
     *
     * @param sysUserRole 用户角色表信息
     * @return 用户角色表集合
     */
    public List<SysUserRole> selectSysUserRoleList(SysUserRole sysUserRole);


    /**
     * 分页模糊查询用户角色表列表
     * @return 用户角色表集合
     */
    public Page selectSysUserRoleListByLike(Query query);



    /**
     * 新增用户角色表
     *
     * @param sysUserRole 用户角色表信息
     * @return 结果
     */
    public int insertSysUserRole(SysUserRole sysUserRole);

    /**
     * 修改用户角色表
     *
     * @param sysUserRole 用户角色表信息
     * @return 结果
     */
    public int updateSysUserRole(SysUserRole sysUserRole);

    /**
     * 删除用户角色表
     *
     * @param userId 用户角色表ID
     * @return 结果
     */
    public int deleteSysUserRoleById(Integer userId);

    /**
     * 批量删除用户角色表
     *
     * @param userIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysUserRoleByIds(Integer[] userIds);

}

