package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.ocean.api.entity.FdInvoiceCheck;
import com.huazheng.tunny.ocean.api.vo.InvoiceStatisticsDetailListVO;
import com.huazheng.tunny.ocean.api.vo.SelectInvoiceStatisticsCostListVO;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-07-23 14:23:39
 */
public interface FdInvoiceCheckService extends IService<FdInvoiceCheck> {
    /**
     * 查询信息
     *
     * @param id ID
     * @return 信息
     */
    public FdInvoiceCheck selectFdInvoiceCheckById(Integer id);

    /**
     * 查询列表
     *
     * @param fdInvoiceCheck 信息
     * @return 集合
     */
    public List<FdInvoiceCheck> selectFdInvoiceCheckList(FdInvoiceCheck fdInvoiceCheck);

    /**
     * 发票核对列表
     *
     * @param map
     * @return
     */
    public List<FdInvoiceCheck> selectFdInvoiceCheckListNotPage(Map<String, Object> map);

    /**
     * 分页模糊查询列表
     *
     * @return 集合
     */
    public Page selectFdInvoiceCheckListByLike(Query query);


    /**
     * 新增
     *
     * @param fdInvoiceCheck 信息
     * @return 结果
     */
    public int insertFdInvoiceCheck(FdInvoiceCheck fdInvoiceCheck);

    /**
     * 修改
     *
     * @param fdInvoiceCheck 信息
     * @return 结果
     */
    public int updateFdInvoiceCheck(FdInvoiceCheck fdInvoiceCheck);

    /**
     * 删除
     *
     * @param id ID
     * @return 结果
     */
    public int deleteFdInvoiceCheckById(Integer id);

    /**
     * 批量删除
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdInvoiceCheckByIds(Integer[] ids);

    /**
     * 核对导入信息
     *
     * @param list
     * @return
     */
    public Boolean selectCheckByList(List<FdInvoiceCheck> list, SecruityUser userInfo);

    /**
     * 批量存储核对信息存储核对历史表
     *
     * @param fdInvoiceChecks
     */
    public void insertInvoiceCheckBatch(List<FdInvoiceCheck> fdInvoiceChecks);

    /**
     * 查询历史数据列表
     *
     * @param query
     * @return
     */
    public Page selectFdInvoiceCheckHistoryListByLike(Query query);

    /**
     * 获取发票统计
     */
    Page selectInvoiceStatistics(Query query);

    /**
     * 统计详情
     * @param platformCode
     * @param planShipTime
     * @param shippingLine
     * @param trip
     * @return
     */
    List<InvoiceStatisticsDetailListVO> selectInvoiceStatisticsDetail(String platformCode,
                                                                      String planShipTime,
                                                                      String shippingLine,
                                                                      String trip,
                                                                      String provinceShiftNo);


    List<SelectInvoiceStatisticsCostListVO> selectInvoiceStatisticsCostListVO(String shiftNo, Integer isInvoice);


    R importCostLedgerExcel(MultipartFile file) throws IOException;


    R importCentralAsiaLedgerExcel(MultipartFile file) throws Exception;
}

