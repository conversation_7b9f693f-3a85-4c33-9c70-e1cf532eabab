package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.BillSubPayCity;
import com.huazheng.tunny.ocean.api.vo.BillDealWithCityAndCostVO;
import com.huazheng.tunny.ocean.mapper.BillSubPayCityMapper;
import com.huazheng.tunny.ocean.service.BillSubPayCityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Service("billSubPayCityService")
public class BillSubPayCityServiceImpl extends ServiceImpl<BillSubPayCityMapper, BillSubPayCity> implements BillSubPayCityService {

    @Autowired
    private BillSubPayCityMapper billSubPayCityMapper;

    /**
     * 查询应付账单（市）子账单表信息
     *
     * @param id 应付账单（市）子账单表ID
     * @return 应付账单（市）子账单表信息
     */
    @Override
    public BillSubPayCity selectBillSubPayCityById(Integer id)
    {
        return billSubPayCityMapper.selectBillSubPayCityById(id);
    }

    /**
     * 查询应付账单（市）子账单表列表
     *
     * @param billSubPayCity 应付账单（市）子账单表信息
     * @return 应付账单（市）子账单表集合
     */
    @Override
    public List<BillSubPayCity> selectBillSubPayCityList(BillSubPayCity billSubPayCity)
    {
        return billSubPayCityMapper.selectBillSubPayCityList(billSubPayCity);
    }


    /**
     * 分页模糊查询应付账单（市）子账单表列表
     * @return 应付账单（市）子账单表集合
     */
    @Override
    public Page selectBillSubPayCityListByLike(Query query)
    {
        BillSubPayCity billSubPayCity =  BeanUtil.mapToBean(query.getCondition(), BillSubPayCity.class,false);
        query.setRecords(billSubPayCityMapper.selectBillSubPayCityListByLike(query,billSubPayCity));
        return query;
    }

    /**
     * 新增应付账单（市）子账单表
     *
     * @param billSubPayCity 应付账单（市）子账单表信息
     * @return 结果
     */
    @Override
    public int insertBillSubPayCity(BillSubPayCity billSubPayCity)
    {
        return billSubPayCityMapper.insertBillSubPayCity(billSubPayCity);
    }

    /**
     * 修改应付账单（市）子账单表
     *
     * @param billSubPayCity 应付账单（市）子账单表信息
     * @return 结果
     */
    @Override
    public int updateBillSubPayCity(BillSubPayCity billSubPayCity)
    {
        return billSubPayCityMapper.updateBillSubPayCity(billSubPayCity);
    }


    /**
     * 删除应付账单（市）子账单表
     *
     * @param id 应付账单（市）子账单表ID
     * @return 结果
     */
    public int deleteBillSubPayCityById(Integer id)
    {
        return billSubPayCityMapper.deleteBillSubPayCityById( id);
    };


    /**
     * 批量删除应付账单（市）子账单表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBillSubPayCityByIds(Integer[] ids)
    {
        return billSubPayCityMapper.deleteBillSubPayCityByIds( ids);
    }


    @Override
    public List<BillDealWithCityAndCostVO> selectFdBillSubByBillNo(String billNo,String customer,String platformCode) {
        return billSubPayCityMapper.selectFdBillSubByBillNo(billNo,customer,platformCode);
    }

    @Override
    public List<BillDealWithCityAndCostVO> selectFdBillSubByShiftNo(String shiftNo, String platformCode) {
        return billSubPayCityMapper.selectFdBillSubByShiftNo(shiftNo,platformCode);
    }

    @Override
    public List<String> selectBillOfCostByBillCode(String billNo) {
        return billSubPayCityMapper.selectBillOfCostByBillCode(billNo);
    }

    /**
     * 根据编号集合获取班次号
     * @param billNos
     * @return
     */
    @Override
    public String selectShiftNoBySubBillNo(List<String> billNos) {
        return billSubPayCityMapper.selectShiftNoBySubBillNo(billNos);
    }

    @Override
    public String selectProvinceShiftNoBySubBillNo(List<String> billNos) {
        return billSubPayCityMapper.selectProvinceShiftNoBySubBillNo(billNos);
    }

    @Override
    public String selectCustomerShiftNoBySubBillNo(List<String> billNos) {
        return billSubPayCityMapper.selectCustomerShiftNoBySubBillNo(billNos);
    }

    @Override
    public int updateStatusBySubBill(List<String> subBillNos,String status) {
        return billSubPayCityMapper.updateStatusBySubBill(subBillNos,status);
    }

    @Override
    public int updateProvinceStatusBySubBill(List<String> subBillNos, String status) {
        return billSubPayCityMapper.updateProvinceStatusBySubBill(subBillNos,status);
    }

    @Override
    public int updateCustomerStatusBySubBill(List<String> subBillNos, String status) {
        return billSubPayCityMapper.updateCustomerStatusBySubBill(subBillNos,status);
    }

    /**
     * 根据结算单号查询出结算单绑定的子账单总金额
     * @param billNo
     * @return
     */
    @Override
    public BigDecimal selectSumAmountByBalanceNo(String billNo) {
        return billSubPayCityMapper.selectSumAmountByBalanceNo(billNo);
    }

    @Override
    public BigDecimal selectProvinceSumAmountByBalanceNo(String billNo) {
        return billSubPayCityMapper.selectProvinceSumAmountByBalanceNo(billNo);
    }

    @Override
    public BigDecimal selectCustomerSumAmountByBalanceNo(String billNo) {
        return billSubPayCityMapper.selectCustomerSumAmountByBalanceNo(billNo);
    }
}
