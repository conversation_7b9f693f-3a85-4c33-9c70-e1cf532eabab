package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.MonthBookingplanHeader;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 月计划申请表(订舱客户提交到市平台) 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-07 11:48:41
 */
public interface MonthBookingplanHeaderService extends IService<MonthBookingplanHeader> {
    /**
     * 查询月计划申请表(订舱客户提交到市平台)信息
     *
     * @param rowId 月计划申请表(订舱客户提交到市平台)ID
     * @return 月计划申请表(订舱客户提交到市平台)信息
     */
    public MonthBookingplanHeader selectMonthBookingplanHeaderById(String rowId);

    /**
     * 查询月计划申请表(订舱客户提交到市平台)列表
     *
     * @param monthBookingplanHeader 月计划申请表(订舱客户提交到市平台)信息
     * @return 月计划申请表(订舱客户提交到市平台)集合
     */
    public List<MonthBookingplanHeader> selectMonthBookingplanHeaderList(MonthBookingplanHeader monthBookingplanHeader);


    /**
     * 分页模糊查询月计划申请表(订舱客户提交到市平台)列表
     * @return 月计划申请表(订舱客户提交到市平台)集合
     */
    public Page selectMonthBookingplanHeaderListByLike(Query query);


    /**
     * 分页模糊查询月计划申请表(订舱客户提交到市平台)列表
     * @return 月计划申请表(订舱客户提交到市平台)集合
     */
    public Page selectMonthBookingplanHeaderListByLike1(Query query);



    /**
     * 新增月计划申请表(订舱客户提交到市平台)
     *
     * @param monthBookingplanHeader 月计划申请表(订舱客户提交到市平台)信息
     * @return 结果
     */
    public R insertMonthBookingplanHeader(MonthBookingplanHeader monthBookingplanHeader);

    /**
     * 修改月计划申请表(订舱客户提交到市平台)
     *
     * @param planCode 编号数组
     * @return 结果
     */
    public R updateMonthBookingplanHeader(String[] planCode/*List<MonthBookingplanHeader> monthBookingplanHeader*/);

    /**
     * 提交
     * @param planCode 提交月计划申请表(订舱客户提交到市平台)信息
     * @return 结果
     */
    public int commitStatus(String planCode);

    /**
     * 删除月计划申请表(订舱客户提交到市平台)
     *
     * @param rowId 月计划申请表(订舱客户提交到市平台)ID
     * @return 结果
     */
    public int deleteMonthBookingplanHeaderById(String rowId);

    /**
     * 批量删除月计划申请表(订舱客户提交到市平台)
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteMonthBookingplanHeaderByIds(Integer[] rowIds);

}

