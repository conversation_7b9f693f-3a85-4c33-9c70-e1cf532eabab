package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FdBillSubDetailMapper;
import com.huazheng.tunny.ocean.api.entity.FdBillSubDetail;
import com.huazheng.tunny.ocean.service.FdBillSubDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fdBillSubDetailService")
public class FdBillSubDetailServiceImpl extends ServiceImpl<FdBillSubDetailMapper, FdBillSubDetail> implements FdBillSubDetailService {

    @Autowired
    private FdBillSubDetailMapper fdBillSubDetailMapper;

    public FdBillSubDetailMapper getFdBillSubDetailMapper() {
        return fdBillSubDetailMapper;
    }

    public void setFdBillSubDetailMapper(FdBillSubDetailMapper fdBillSubDetailMapper) {
        this.fdBillSubDetailMapper = fdBillSubDetailMapper;
    }

    /**
     * 查询子账单详情信息
     *
     * @param uuid 子账单详情ID
     * @return 子账单详情信息
     */
    @Override
    public FdBillSubDetail selectFdBillSubDetailById(String uuid)
    {
        return fdBillSubDetailMapper.selectFdBillSubDetailById(uuid);
    }

    /**
     * 查询子账单详情列表
     *
     * @param fdBillSubDetail 子账单详情信息
     * @return 子账单详情集合
     */
    @Override
    public List<FdBillSubDetail> selectFdBillSubDetailList(FdBillSubDetail fdBillSubDetail)
    {
        return fdBillSubDetailMapper.selectFdBillSubDetailList(fdBillSubDetail);
    }


    /**
     * 分页模糊查询子账单详情列表
     * @return 子账单详情集合
     */
    @Override
    public Page selectFdBillSubDetailListByLike(Query query)
    {
        FdBillSubDetail fdBillSubDetail =  BeanUtil.mapToBean(query.getCondition(), FdBillSubDetail.class,false);
        query.setRecords(fdBillSubDetailMapper.selectFdBillSubDetailListByLike(query,fdBillSubDetail));
        return query;
    }

    /**
     * 新增子账单详情
     *
     * @param fdBillSubDetail 子账单详情信息
     * @return 结果
     */
    @Override
    public int insertFdBillSubDetail(FdBillSubDetail fdBillSubDetail)
    {
        return fdBillSubDetailMapper.insertFdBillSubDetail(fdBillSubDetail);
    }

    /**
     * 修改子账单详情
     *
     * @param fdBillSubDetail 子账单详情信息
     * @return 结果
     */
    @Override
    public int updateFdBillSubDetail(FdBillSubDetail fdBillSubDetail)
    {
        return fdBillSubDetailMapper.updateFdBillSubDetail(fdBillSubDetail);
    }

    @Override
    public int updateFdBillSubDetailByCode(FdBillSubDetail fdBillSubDetail)
    {
        return fdBillSubDetailMapper.updateFdBillSubDetailByCode(fdBillSubDetail);
    }


    /**
     * 删除子账单详情
     *
     * @param uuid 子账单详情ID
     * @return 结果
     */
    public int deleteFdBillSubDetailById(String uuid)
    {
        return fdBillSubDetailMapper.deleteFdBillSubDetailById( uuid);
    };


    /**
     * 批量删除子账单详情对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFdBillSubDetailByIds(Integer[] uuids)
    {
        return fdBillSubDetailMapper.deleteFdBillSubDetailByIds( uuids);
    }

}
