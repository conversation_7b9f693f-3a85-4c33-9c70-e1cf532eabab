package com.huazheng.tunny.ocean.service.eabillmain;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.eabillmain.EaBillMainDTO;
import com.huazheng.tunny.ocean.api.entity.BillBalanceMainCity;
import com.huazheng.tunny.ocean.api.entity.eabillmain.EaBillMain;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 账单主表 服务接口层
 *
 * <AUTHOR>
 * @date 2025-06-30 18:35:54
 */
public interface EaBillMainService extends IService<EaBillMain> {
    /**
     * 查询账单主表信息
     *
     * @param billId 账单主表ID
     * @return 账单主表信息
     */
    public EaBillMainDTO selectEaBillMainById(Long billId);

    /**
     * 查询账单主表列表
     *
     * @param eaBillMain 账单主表信息
     * @return 账单主表集合
     */
    public List<EaBillMain> selectEaBillMainList(EaBillMain eaBillMain);


    /**
     * 分页模糊查询账单主表列表
     * @return 账单主表集合
     */
    public Page selectEaBillMainListByLike(Query query);



    /**
     * 新增账单主表
     *
     * @param eaBillMain 账单主表信息
     * @return 结果
     */
    public int insertEaBillMain(EaBillMain eaBillMain);

    /**
     * 修改账单主表
     *
     * @param eaBillMain 账单主表信息
     * @return 结果
     */
    public int updateEaBillMain(EaBillMain eaBillMain);

    /**
     * 删除账单主表
     *
     * @param billId 账单主表ID
     * @return 结果
     */
    public int deleteEaBillMainById(Long billId);

    /**
     * 批量删除账单主表
     *
     * @param billIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEaBillMainByIds(Integer[] billIds);
    /**
     * 结算
     *
     * @param eaBillMain 需要删除的数据ID
     * @return 结果
     */
    R settlement(EaBillMain eaBillMain);
    /**
     * 取消结算
     *
     * @param eaBillMain 需要删除的数据ID
     * @return 结果
     */
    R revokeSettlement(EaBillMain eaBillMain);
    /**
     * 生成账单（一次）
     *
     * @param eaBillMain 生成逻辑参数
     * @return 结果
     */
    R generateBill(EaBillMain eaBillMain);
    /**
     * 生成二次账单
     *
     * @param eaBillMain 生成逻辑参数
     * @return 结果
     */
    R generateBillTwo(EaBillMain eaBillMain);

    R delBill(EaBillMain eaBillMain);

    R exportFreightAccountCity(EaBillMain eaBillMain, HttpServletResponse response);
}

