package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.BillSubDetailPayCity;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 应付账单（市）子账单详情 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-06-11 16:26:13
 */
public interface BillSubDetailPayCityService extends IService<BillSubDetailPayCity> {
    /**
     * 查询应付账单（市）子账单详情信息
     *
     * @param id 应付账单（市）子账单详情ID
     * @return 应付账单（市）子账单详情信息
     */
    public BillSubDetailPayCity selectBillSubDetailPayCityById(Integer id);

    /**
     * 查询应付账单（市）子账单详情列表
     *
     * @param billSubDetailPayCity 应付账单（市）子账单详情信息
     * @return 应付账单（市）子账单详情集合
     */
    public List<BillSubDetailPayCity> selectBillSubDetailPayCityList(BillSubDetailPayCity billSubDetailPayCity);


    /**
     * 分页模糊查询应付账单（市）子账单详情列表
     * @return 应付账单（市）子账单详情集合
     */
    public Page selectBillSubDetailPayCityListByLike(Query query);



    /**
     * 新增应付账单（市）子账单详情
     *
     * @param billSubDetailPayCity 应付账单（市）子账单详情信息
     * @return 结果
     */
    public int insertBillSubDetailPayCity(BillSubDetailPayCity billSubDetailPayCity);

    /**
     * 修改应付账单（市）子账单详情
     *
     * @param billSubDetailPayCity 应付账单（市）子账单详情信息
     * @return 结果
     */
    public int updateBillSubDetailPayCity(BillSubDetailPayCity billSubDetailPayCity);

    /**
     * 删除应付账单（市）子账单详情
     *
     * @param id 应付账单（市）子账单详情ID
     * @return 结果
     */
    public int deleteBillSubDetailPayCityById(Integer id);

    /**
     * 批量删除应付账单（市）子账单详情
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteBillSubDetailPayCityByIds(Integer[] ids);

}

