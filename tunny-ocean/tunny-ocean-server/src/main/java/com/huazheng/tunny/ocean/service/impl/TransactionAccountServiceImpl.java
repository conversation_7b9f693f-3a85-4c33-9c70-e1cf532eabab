package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.TransactionAccountMapper;
import com.huazheng.tunny.ocean.api.entity.TransactionAccount;
import com.huazheng.tunny.ocean.service.TransactionAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("transactionAccountService")
public class TransactionAccountServiceImpl extends ServiceImpl<TransactionAccountMapper, TransactionAccount> implements TransactionAccountService {

    @Autowired
    private TransactionAccountMapper transactionAccountMapper;

    public TransactionAccountMapper getTransactionAccountMapper() {
        return transactionAccountMapper;
    }

    public void setTransactionAccountMapper(TransactionAccountMapper transactionAccountMapper) {
        this.transactionAccountMapper = transactionAccountMapper;
    }

    /**
     * 查询订舱客户交易台账表信息
     *
     * @param rowId 订舱客户交易台账表ID
     * @return 订舱客户交易台账表信息
     */
    @Override
    public TransactionAccount selectTransactionAccountById(String rowId)
    {
        return transactionAccountMapper.selectTransactionAccountById(rowId);
    }

    /**
     * 查询订舱客户交易台账表列表
     *
     * @param transactionAccount 订舱客户交易台账表信息
     * @return 订舱客户交易台账表集合
     */
    @Override
    public List<TransactionAccount> selectTransactionAccountList(TransactionAccount transactionAccount)
    {
        return transactionAccountMapper.selectTransactionAccountList(transactionAccount);
    }


    /**
     * 分页模糊查询订舱客户交易台账表列表
     * @return 订舱客户交易台账表集合
     */
    @Override
    public Page selectTransactionAccountListByLike(Query query)
    {
        TransactionAccount transactionAccount =  BeanUtil.mapToBean(query.getCondition(), TransactionAccount.class,false);
        List<TransactionAccount> transactionAccounts = transactionAccountMapper.selectTransactionAccountListByLike(query, transactionAccount);
        query.setRecords(transactionAccounts);
        query.setTotal(transactionAccountMapper.selectAllNo(transactionAccount));
        return query;
    }

    /**
     * 新增订舱客户交易台账表
     *
     * @param transactionAccount 订舱客户交易台账表信息
     * @return 结果
     */
    @Override
    public int insertTransactionAccount(TransactionAccount transactionAccount)
    {
        return transactionAccountMapper.insertTransactionAccount(transactionAccount);
    }

    /**
     * 修改订舱客户交易台账表
     *
     * @param transactionAccount 订舱客户交易台账表信息
     * @return 结果
     */
    @Override
    public int updateTransactionAccount(TransactionAccount transactionAccount)
    {
        return transactionAccountMapper.updateTransactionAccount(transactionAccount);
    }
    @Override
    public int updateTransactionAccountByNo(TransactionAccount transactionAccount)
    {
        return transactionAccountMapper.updateTransactionAccountByNo(transactionAccount);
    }

    /**
     * 删除订舱客户交易台账表
     *
     * @param rowId 订舱客户交易台账表ID
     * @return 结果
     */
    public int deleteTransactionAccountById(String rowId)
    {
        return transactionAccountMapper.deleteTransactionAccountById( rowId);
    };


    /**
     * 批量删除订舱客户交易台账表对象
     *
     * @return 结果
     */
    @Override
    public int deleteTransactionAccountByIds(Integer[] rowIds)
    {
        return transactionAccountMapper.deleteTransactionAccountByIds( rowIds);
    }

}
