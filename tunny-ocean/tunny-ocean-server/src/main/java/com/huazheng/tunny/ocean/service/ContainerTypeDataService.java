package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.ContainerTypeData;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 箱型数据管理表 服务接口层
 *
 * <AUTHOR>
 * @date 2023-07-03 09:43:51
 */
public interface ContainerTypeDataService extends IService<ContainerTypeData> {
    /**
     * 查询箱型数据管理表信息
     *
     * @param rowId 箱型数据管理表ID
     * @return 箱型数据管理表信息
     */
    public ContainerTypeData selectContainerTypeDataById(String rowId);

    /**
     * 查询箱型数据管理表列表
     *
     * @param containerTypeData 箱型数据管理表信息
     * @return 箱型数据管理表集合
     */
    public List<ContainerTypeData> selectContainerTypeDataList(ContainerTypeData containerTypeData);


    /**
     * 分页模糊查询箱型数据管理表列表
     * @return 箱型数据管理表集合
     */
    public Page selectContainerTypeDataListByLike(Query query);

    public List<ContainerTypeData> selectContainerTypeDataListByLike(ContainerTypeData containerTypeData);

    /**
     * 新增箱型数据管理表
     *
     * @param containerTypeData 箱型数据管理表信息
     * @return 结果
     */
    public int insertContainerTypeData(ContainerTypeData containerTypeData);

    /**
     * 修改箱型数据管理表
     *
     * @param containerTypeData 箱型数据管理表信息
     * @return 结果
     */
    public int updateContainerTypeData(ContainerTypeData containerTypeData);

    /**
     * 删除箱型数据管理表
     *
     * @param rowId 箱型数据管理表ID
     * @return 结果
     */
    public int deleteContainerTypeDataById(String rowId);

    /**
     * 批量删除箱型数据管理表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteContainerTypeDataByIds(Integer[] rowIds);

    public List<ContainerTypeData> selectDuplicate(ContainerTypeData containerTypeData);

    List<ContainerTypeData> selectSizeList(Map<String, Object> params);
}

