package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.WaybillGoodsInfo;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 运单货物信息表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-05 10:57:40
 */
public interface WaybillGoodsInfoService extends IService<WaybillGoodsInfo> {
    /**
     * 查询运单货物信息表信息
     *
     * @param rowId 运单货物信息表ID
     * @return 运单货物信息表信息
     */
    public WaybillGoodsInfo selectWaybillGoodsInfoById(String rowId);

    /**
     * 查询运单货物信息表列表
     *
     * @param waybillGoodsInfo 运单货物信息表信息
     * @return 运单货物信息表集合
     */
    public List<WaybillGoodsInfo> selectWaybillGoodsInfoList(WaybillGoodsInfo waybillGoodsInfo);


    /**
     * 分页模糊查询运单货物信息表列表
     * @return 运单货物信息表集合
     */
    public Page selectWaybillGoodsInfoListByLike(Query query);



    /**
     * 新增运单货物信息表
     *
     * @param waybillGoodsInfo 运单货物信息表信息
     * @return 结果
     */
    public int insertWaybillGoodsInfo(List<WaybillGoodsInfo> waybillGoodsInfo);

    public void insertOrUpdateWaybillGoodsInfo(List<WaybillGoodsInfo> waybillGoodsInfo,List<String> waybillCodes);

    /**
     * 修改运单货物信息表
     *
     * @param list 运单货物信息表信息
     * @return 结果
     */
    public int updateGoodsInfoBatch(List<WaybillGoodsInfo> list);

    /**
     * 删除运单货物信息表
     *
     * @param rowId 运单货物信息表ID
     * @return 结果
     */
    public int deleteWaybillGoodsInfoById(String rowId);

    /**
     * 批量删除运单货物信息表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteWaybillGoodsInfoByIds(Integer[] rowIds);

    public int updateGoodsInfo(WaybillGoodsInfo waybillGoodsInfo);

    public int deleteWaybillGoodsInfoByContainerNo(WaybillGoodsInfo waybillGoodsInfo);

}

