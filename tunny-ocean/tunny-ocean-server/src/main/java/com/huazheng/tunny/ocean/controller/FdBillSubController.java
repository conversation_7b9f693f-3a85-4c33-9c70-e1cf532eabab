package com.huazheng.tunny.ocean.controller;

import cn.hutool.core.collection.CollUtil;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.vo.FdBillVO;
import com.huazheng.tunny.ocean.service.FdBillSubDetailService;
import com.huazheng.tunny.ocean.service.FdBillSubService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.service.FdShippingAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 子账单表
 *
 * <AUTHOR>
 * @date 2023-07-19 13:11:36
 */
@Slf4j
@RestController
@RequestMapping("/fdbillsub")
public class FdBillSubController {

    @Autowired
    private FdBillSubService fdBillSubService;
    @Autowired
    private FdBillSubDetailService fdBillSubDetailService;
    @Autowired
    private FdShippingAccountService fdShippingAccountService;
    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdBillSubService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdBillSubService.selectFdBillSubListByLike(new Query<>(params));
    }

    /**
     *  列表
     * @param fdBillSub
     * @return
     */
    @PostMapping("/list")
    public List<FdBillSub> list(@RequestBody FdBillSub fdBillSub) {
        //数据库字段值完整查询
        // return  fdBillSubService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        fdBillSub.setDeleteFlag("N");
        return fdBillSubService.selectFdBillSubList(fdBillSub);
    }

    /**
     * 信息
     * @param uuid
     * @return R
     */
    @GetMapping("/{uuid}")
    public R info(@PathVariable("uuid") String uuid) {
        FdBillSub fdBillSub =fdBillSubService.selectFdBillSubById(uuid);
        return new R<>(fdBillSub);
    }

    /**
     * 保存
     * @param fdBillSub
     * @return R
     */
    @PostMapping
    public R save(@RequestBody FdBillSub fdBillSub) {
        return fdBillSubService.insertFdBillSub(fdBillSub);
    }

    /**
     * 修改
     * @param fdBillSub
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody FdBillSub fdBillSub) {
        fdBillSubService.updateFdBillSub(fdBillSub);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 应付账单--财务确认支付
     * @param fdBillSub
     * @return R
     */
    @PostMapping("/confirmPayment")
    public R confirmPayment(@RequestBody FdBillSub fdBillSub) {
        fdBillSubService.updateFdBillSub(fdBillSub);
        return new R<>(Boolean.TRUE);
    }

    

    /**
     * 删除
     * @param uuid
     * @return R
     */
    @GetMapping("/del/{uuid}")
    public R delete(@PathVariable  String uuid) {
        fdBillSubService.deleteById(uuid);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改账单状态
     * @param fdBillSub
     * @return R
     */
    @PostMapping("/updateStatus")
    public R updateStatus(@RequestBody FdBillSub fdBillSub) {
        fdBillSubService.updateStatus(fdBillSub);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param uuids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> uuids) {
        fdBillSubService.deleteBatchIds(uuids);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<FdBillSub> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<FdBillSub> list = fdBillSubService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), FdBillSub.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }

    /**
     * 余额抵扣
     *
     */

    @PostMapping("/deduction")
    @Transactional(rollbackFor = Exception.class)
    public R deduction(@RequestBody FdBillSub fdBillSub) {
        fdBillSubService.deduction(fdBillSub);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 支付
     */
    @PostMapping("/updatePayment")
    public R updatePayment(@RequestBody FdBillSub fdBillSub) {
        if(StrUtil.isNotEmpty(fdBillSub.getBillSubCode())){
            fdBillSub.setBillingState("3");
            FdBillSub sel = new FdBillSub();
            sel.setBillSubCode(fdBillSub.getBillSubCode());
            sel.setDeleteFlag("N");
            final List<FdBillSub> fdBills = fdBillSubService.selectFdBillSubList(sel);
            if(CollUtil.isNotEmpty(fdBills)){
                //应收账单的应收金额为<=0，则无需人工核销
                if(StrUtil.isNotEmpty(fdBills.get(0).getPlatformLevel()) && "1".equals(fdBills.get(0).getPlatformLevel())){
                    BigDecimal billAmount = BigDecimal.valueOf(0);
                    BigDecimal offsetBalance = BigDecimal.valueOf(0);
                    if(fdBills.get(0).getBillAmount()!=null){
                        billAmount = fdBills.get(0).getBillAmount();
                    }
                    if(fdBills.get(0).getOffsetBalance()!=null){
                        offsetBalance = fdBills.get(0).getOffsetBalance();
                    }
                    if((billAmount.subtract(offsetBalance)).compareTo(BigDecimal.ZERO) == 0){
                        fdBillSub.setBillingState("4");

                    }
                }
            }
            fdBillSub.setIsPay("1");
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            fdBillSub.setUpdateWho(userInfo.getUserName());
            fdBillSub.setUpdateWhoName(userInfo.getRealName());
            fdBillSub.setUpdateTime(LocalDateTime.now());
            fdBillSubService.updateFdBillSub(fdBillSub);

            return new R(true);
        }else{
            return new R(500,Boolean.FALSE,null,"未接收到子账单编码");
        }
    }

    @PostMapping("/accounting")
    public R accounting(@RequestBody FdBillSub fdBillSub) {

        //记录交易明细
        fdBillSubService.accounting(fdBillSub);

        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param fdBillSub
     * @return R
     */
    @PostMapping("/reject")
    public R reject(@RequestBody FdBillSub fdBillSub) {
        fdBillSub.setBillingState("0");
        return fdBillSubService.reject(fdBillSub);
    }

}
