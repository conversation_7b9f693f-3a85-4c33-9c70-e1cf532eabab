package com.huazheng.tunny.ocean.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.huazheng.tunny.ocean.api.entity.FiLoanRecordNew;
import com.huazheng.tunny.ocean.service.FiLoanRecordNewService;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 企业信用贷-贷款记录（新建）
 *
 * <AUTHOR> code ocean
 * @date 2022-03-16 15:10:37
 */
@RestController
@RequestMapping("/filoanrecordnew")
@Slf4j
public class FiLoanRecordNewController {
    @Autowired
    private FiLoanRecordNewService fiLoanRecordNewService;
    @Autowired
    private SignatureController signatureController;

    @Value("${path.windowsPath}")
    private String windowsPath;
    @Value("${path.linuxPath}")
    private String linuxPath;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fiLoanRecordNewService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fiLoanRecordNewService.selectFiLoanRecordNewListByLike(new Query<>(params));
    }

    @GetMapping("/statisticsSum")
    public R statisticsSum(@RequestParam Map<String, Object> params){

        return fiLoanRecordNewService.statisticsSum(params);
    }





    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        FiLoanRecordNew fiLoanRecordNew =fiLoanRecordNewService.selectById(rowId);
        return new R<>(fiLoanRecordNew);
    }

    /**
     * 保存
     * @param fiLoanRecordNew
     * @return R
     */
    @PostMapping
    public R save(@RequestBody FiLoanRecordNew fiLoanRecordNew) {
        fiLoanRecordNewService.insert(fiLoanRecordNew);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param fiLoanRecordNew
     * @return R
     */
    @PutMapping
    public R update(@RequestBody FiLoanRecordNew fiLoanRecordNew) {
        fiLoanRecordNewService.updateById(fiLoanRecordNew);
        return new R<>(Boolean.TRUE);
    }


    /**
     * 暴露给中超的新增修改的接口
     */
    @PostMapping("/addorUpdateFiloanrecordnew")
    public String addorUpdateFiloanrecordnew(@RequestBody JSONObject jsonObject) {
        String data = signatureController.getPost(jsonObject);
        String content = null;
        FiLoanRecordNew fiLoanRecordNew = JSONUtil.toBean(data, FiLoanRecordNew.class);
        R r = fiLoanRecordNewService.addorUpdateFiloanrecordnew(fiLoanRecordNew);
        content = JSONUtil.parseObj(r, false).toStringPretty();
        //调用接口
        String result = signatureController.returnPost("/filoanrecordnew/addorUpdateFiloanrecordnew", content);
        return result;
    }



    /**
     * 删除
     * @param rowId
     * @return R
     */
    @DeleteMapping("/{rowId}")
    public R delete(@PathVariable  String rowId) {
        fiLoanRecordNewService.deleteById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        fiLoanRecordNewService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<FiLoanRecordNew> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = fiLoanRecordNewService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<FiLoanRecordNew> list = reader.readAll(FiLoanRecordNew.class);
        fiLoanRecordNewService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }

    @GetMapping("/fiLoanRecordNewExport")
    public void fiLoanRecordNewExport(FiLoanRecordNew fiLoanRecordNew, HttpServletResponse res) throws Exception{
        List<FiLoanRecordNew> list = fiLoanRecordNewService.selectFiLoanRecordNewExportList(fiLoanRecordNew);
        if(CollUtil.isNotEmpty(list)){
            for(int i = 0;i<list.size();i++){
                list.get(i).setRowId(i+1+"");
                if(list.get(i).getState()!=null && !"".equals(list.get(i).getState())){
                    if("PAY".equals(list.get(i).getState())){
                        list.get(i).setState("放款");
                    }else if("REPAY".equals(list.get(i).getState())){
                        list.get(i).setState("已还款");
                    }else if("OUTSTANDING".equals(list.get(i).getState())){
                        list.get(i).setState("未还款");
                    }else if("CORRECTION".equals(list.get(i).getState())){
                        list.get(i).setState("已冲正");
                    }
                }
            }
        }
        com.alibaba.excel.ExcelWriter excelWriter=null;
        String templateFileName = "fiLoanRecordNewExport.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)){
            templateFileName = linuxPath + templateFileName;
        }else{
            templateFileName = windowsPath + templateFileName;
        }
        String fileName = new String("轻资产信用贷列表导出.xlsx".getBytes("utf-8"), "iso-8859-1");
        //文件格式xlsx
        res.setHeader("Content-Type","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        res.setHeader( "Content-Disposition", "attachment;filename=" + fileName);
        res.addHeader("Pargam", "no-cache");
        res.addHeader("Cache-Control", "no-cache");
        excelWriter = EasyExcel.write(res.getOutputStream()).withTemplate(templateFileName).build();
        WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "Sheet1").build();

        if(CollUtil.isNotEmpty(list)){
            excelWriter.fill(list, writeSheet1);
        }
        excelWriter.finish();
    }
}
