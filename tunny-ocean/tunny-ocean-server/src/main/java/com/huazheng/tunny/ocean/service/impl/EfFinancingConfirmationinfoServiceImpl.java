package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.EfFinancingConfirmationinfoMapper;
import com.huazheng.tunny.ocean.api.entity.EfFinancingConfirmationinfo;
import com.huazheng.tunny.ocean.service.EfFinancingConfirmationinfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("efFinancingConfirmationinfoService")
public class EfFinancingConfirmationinfoServiceImpl extends ServiceImpl<EfFinancingConfirmationinfoMapper, EfFinancingConfirmationinfo> implements EfFinancingConfirmationinfoService {

    @Autowired
    private EfFinancingConfirmationinfoMapper efFinancingConfirmationinfoMapper;

    public EfFinancingConfirmationinfoMapper getEfFinancingConfirmationinfoMapper() {
        return efFinancingConfirmationinfoMapper;
    }

    public void setEfFinancingConfirmationinfoMapper(EfFinancingConfirmationinfoMapper efFinancingConfirmationinfoMapper) {
        this.efFinancingConfirmationinfoMapper = efFinancingConfirmationinfoMapper;
    }

    /**
     * 查询仓单质押企业确认信息信息
     *
     * @param rowId 仓单质押企业确认信息ID
     * @return 仓单质押企业确认信息信息
     */
    @Override
    public EfFinancingConfirmationinfo selectEfFinancingConfirmationinfoById(String rowId)
    {
        return efFinancingConfirmationinfoMapper.selectEfFinancingConfirmationinfoById(rowId);
    }

    /**
     * 查询仓单质押企业确认信息列表
     *
     * @param efFinancingConfirmationinfo 仓单质押企业确认信息信息
     * @return 仓单质押企业确认信息集合
     */
    @Override
    public List<EfFinancingConfirmationinfo> selectEfFinancingConfirmationinfoList(EfFinancingConfirmationinfo efFinancingConfirmationinfo)
    {
        return efFinancingConfirmationinfoMapper.selectEfFinancingConfirmationinfoList(efFinancingConfirmationinfo);
    }


    /**
     * 分页模糊查询仓单质押企业确认信息列表
     * @return 仓单质押企业确认信息集合
     */
    @Override
    public Page selectEfFinancingConfirmationinfoListByLike(Query query)
    {
        EfFinancingConfirmationinfo efFinancingConfirmationinfo =  BeanUtil.mapToBean(query.getCondition(), EfFinancingConfirmationinfo.class,false);
        query.setRecords(efFinancingConfirmationinfoMapper.selectEfFinancingConfirmationinfoListByLike(query,efFinancingConfirmationinfo));
        return query;
    }

    /**
     * 新增仓单质押企业确认信息
     *
     * @param efFinancingConfirmationinfo 仓单质押企业确认信息信息
     * @return 结果
     */
    @Override
    public int insertEfFinancingConfirmationinfo(EfFinancingConfirmationinfo efFinancingConfirmationinfo)
    {
        return efFinancingConfirmationinfoMapper.insertEfFinancingConfirmationinfo(efFinancingConfirmationinfo);
    }

    /**
     * 修改仓单质押企业确认信息
     *
     * @param efFinancingConfirmationinfo 仓单质押企业确认信息信息
     * @return 结果
     */
    @Override
    public int updateEfFinancingConfirmationinfo(EfFinancingConfirmationinfo efFinancingConfirmationinfo)
    {
        return efFinancingConfirmationinfoMapper.updateEfFinancingConfirmationinfo(efFinancingConfirmationinfo);
    }


    /**
     * 删除仓单质押企业确认信息
     *
     * @param rowId 仓单质押企业确认信息ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingConfirmationinfoById(String rowId)
    {
        return efFinancingConfirmationinfoMapper.deleteEfFinancingConfirmationinfoById( rowId);
    };


    /**
     * 批量删除仓单质押企业确认信息对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingConfirmationinfoByIds(Integer[] rowIds)
    {
        return efFinancingConfirmationinfoMapper.deleteEfFinancingConfirmationinfoByIds( rowIds);
    }

}
