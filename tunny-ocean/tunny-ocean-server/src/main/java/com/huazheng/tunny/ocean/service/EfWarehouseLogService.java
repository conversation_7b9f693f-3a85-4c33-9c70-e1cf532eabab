package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseLog;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 仓单质押业务记录 服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-05 10:54:21
 */
public interface EfWarehouseLogService extends IService<EfWarehouseLog> {
    /**
     * 查询仓单质押业务记录信息
     *
     * @param rowId 仓单质押业务记录ID
     * @return 仓单质押业务记录信息
     */
    public EfWarehouseLog selectEfWarehouseLogById(String rowId);

    /**
     * 查询仓单质押业务记录列表
     *
     * @param efWarehouseLog 仓单质押业务记录信息
     * @return 仓单质押业务记录集合
     */
    public List<EfWarehouseLog> selectEfWarehouseLogList(EfWarehouseLog efWarehouseLog);


    /**
     * 分页模糊查询仓单质押业务记录列表
     * @return 仓单质押业务记录集合
     */
    public Page selectEfWarehouseLogListByLike(Query query);



    /**
     * 新增仓单质押业务记录
     *
     * @param efWarehouseLog 仓单质押业务记录信息
     * @return 结果
     */
    public int insertEfWarehouseLog(EfWarehouseLog efWarehouseLog);

    /**
     * 修改仓单质押业务记录
     *
     * @param efWarehouseLog 仓单质押业务记录信息
     * @return 结果
     */
    public int updateEfWarehouseLog(EfWarehouseLog efWarehouseLog);

    /**
     * 删除仓单质押业务记录
     *
     * @param rowId 仓单质押业务记录ID
     * @return 结果
     */
    public int deleteEfWarehouseLogById(String rowId);

    /**
     * 批量删除仓单质押业务记录
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfWarehouseLogByIds(Integer[] rowIds);

}

