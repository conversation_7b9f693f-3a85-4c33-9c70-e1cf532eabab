package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseDisposeinfo;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 融资处置信息 服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-04 10:08:19
 */
public interface EfWarehouseDisposeinfoService extends IService<EfWarehouseDisposeinfo> {
    /**
     * 查询融资处置信息信息
     *
     * @param rowId 融资处置信息ID
     * @return 融资处置信息信息
     */
    public EfWarehouseDisposeinfo selectEfWarehouseDisposeinfoById(String rowId);

    /**
     * 查询融资处置信息列表
     *
     * @param efWarehouseDisposeinfo 融资处置信息信息
     * @return 融资处置信息集合
     */
    public List<EfWarehouseDisposeinfo> selectEfWarehouseDisposeinfoList(EfWarehouseDisposeinfo efWarehouseDisposeinfo);


    /**
     * 分页模糊查询融资处置信息列表
     * @return 融资处置信息集合
     */
    public Page selectEfWarehouseDisposeinfoListByLike(Query query);



    /**
     * 新增融资处置信息
     *
     * @param efWarehouseDisposeinfo 融资处置信息信息
     * @return 结果
     */
    public int insertEfWarehouseDisposeinfo(EfWarehouseDisposeinfo efWarehouseDisposeinfo);

    /**
     * 修改融资处置信息
     *
     * @param efWarehouseDisposeinfo 融资处置信息信息
     * @return 结果
     */
    public int updateEfWarehouseDisposeinfo(EfWarehouseDisposeinfo efWarehouseDisposeinfo);

    /**
     * 删除融资处置信息
     *
     * @param rowId 融资处置信息ID
     * @return 结果
     */
    public int deleteEfWarehouseDisposeinfoById(String rowId);

    /**
     * 批量删除融资处置信息
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfWarehouseDisposeinfoByIds(Integer[] rowIds);

}

