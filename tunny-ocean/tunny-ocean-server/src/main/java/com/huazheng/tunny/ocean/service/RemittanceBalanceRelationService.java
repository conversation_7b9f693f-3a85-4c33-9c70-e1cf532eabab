package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.RemittanceBalanceRelation;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 收款结算关系表 服务接口层
 *
 * <AUTHOR>
 * @date 2024-06-18 10:17:05
 */
public interface RemittanceBalanceRelationService extends IService<RemittanceBalanceRelation> {
    /**
     * 查询收款结算关系表信息
     *
     * @param id 收款结算关系表ID
     * @return 收款结算关系表信息
     */
    public RemittanceBalanceRelation selectRemittanceBalanceRelationById(Integer id);

    /**
     * 查询收款结算关系表列表
     *
     * @param remittanceBalanceRelation 收款结算关系表信息
     * @return 收款结算关系表集合
     */
    public List<RemittanceBalanceRelation> selectRemittanceBalanceRelationList(RemittanceBalanceRelation remittanceBalanceRelation);


    /**
     * 分页模糊查询收款结算关系表列表
     * @return 收款结算关系表集合
     */
    public Page selectRemittanceBalanceRelationListByLike(Query query);



    /**
     * 新增收款结算关系表
     *
     * @param remittanceBalanceRelation 收款结算关系表信息
     * @return 结果
     */
    public int insertRemittanceBalanceRelation(RemittanceBalanceRelation remittanceBalanceRelation);

    /**
     * 修改收款结算关系表
     *
     * @param remittanceBalanceRelation 收款结算关系表信息
     * @return 结果
     */
    public int updateRemittanceBalanceRelation(RemittanceBalanceRelation remittanceBalanceRelation);

    /**
     * 删除收款结算关系表
     *
     * @param id 收款结算关系表ID
     * @return 结果
     */
    public int deleteRemittanceBalanceRelationById(Integer id);

    /**
     * 批量删除收款结算关系表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteRemittanceBalanceRelationByIds(Integer[] ids);

}

