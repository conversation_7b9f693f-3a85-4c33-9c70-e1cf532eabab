package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.EfFinancingApprovalinfo;

import java.util.List;

/**
 * 仓单质押银行审批信息 服务接口层
 *
 * <AUTHOR>
 * @date 2023-02-08 15:14:12
 */
public interface EfFinancingApprovalinfoService extends IService<EfFinancingApprovalinfo> {
    /**
     * 查询仓单质押银行审批信息信息
     *
     * @param rowId 仓单质押银行审批信息ID
     * @return 仓单质押银行审批信息信息
     */
    public EfFinancingApprovalinfo selectEfFinancingApprovalinfoById(String rowId);

    /**
     * 查询仓单质押银行审批信息列表
     *
     * @param efFinancingApprovalinfo 仓单质押银行审批信息信息
     * @return 仓单质押银行审批信息集合
     */
    public List<EfFinancingApprovalinfo> selectEfFinancingApprovalinfoList(EfFinancingApprovalinfo efFinancingApprovalinfo);


    /**
     * 分页模糊查询仓单质押银行审批信息列表
     * @return 仓单质押银行审批信息集合
     */
    public Page selectEfFinancingApprovalinfoListByLike(Query query);



    /**
     * 新增仓单质押银行审批信息
     *
     * @param efFinancingApprovalinfo 仓单质押银行审批信息信息
     * @return 结果
     */
    public int insertEfFinancingApprovalinfo(EfFinancingApprovalinfo efFinancingApprovalinfo);

    /**
     * 修改仓单质押银行审批信息
     *
     * @param efFinancingApprovalinfo 仓单质押银行审批信息信息
     * @return 结果
     */
    public int updateEfFinancingApprovalinfo(EfFinancingApprovalinfo efFinancingApprovalinfo);

    /**
     * 删除仓单质押银行审批信息
     *
     * @param rowId 仓单质押银行审批信息ID
     * @return 结果
     */
    public int deleteEfFinancingApprovalinfoById(String rowId);

    /**
     * 批量删除仓单质押银行审批信息
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfFinancingApprovalinfoByIds(Integer[] rowIds);

}

