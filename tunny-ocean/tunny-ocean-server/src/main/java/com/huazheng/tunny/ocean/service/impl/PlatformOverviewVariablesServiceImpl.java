package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.PlatformOverviewVariables;
import com.huazheng.tunny.ocean.mapper.PlatformOverviewVariablesMapper;
import com.huazheng.tunny.ocean.service.PlatformOverviewVariablesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service("platformOverviewVariablesService")
public class PlatformOverviewVariablesServiceImpl extends ServiceImpl<PlatformOverviewVariablesMapper, PlatformOverviewVariables> implements PlatformOverviewVariablesService {

    @Autowired
    private PlatformOverviewVariablesMapper platformOverviewVariablesMapper;

    /**
     * 查询平台概览变量信息
     *
     * @param id 平台概览变量ID
     * @return 平台概览变量信息
     */
    @Override
    public PlatformOverviewVariables selectPlatformOverviewVariablesById(Integer id) {
        return platformOverviewVariablesMapper.selectPlatformOverviewVariablesById(id);
    }

    /**
     * 查询平台概览变量列表
     *
     * @param platformOverviewVariables 平台概览变量信息
     * @return 平台概览变量集合
     */
    @Override
    public List<PlatformOverviewVariables> selectPlatformOverviewVariablesList(PlatformOverviewVariables platformOverviewVariables) {
        return platformOverviewVariablesMapper.selectPlatformOverviewVariablesList(platformOverviewVariables);
    }


    /**
     * 分页模糊查询平台概览变量列表
     *
     * @return 平台概览变量集合
     */
    @Override
    public Page selectPlatformOverviewVariablesListByLike(Query query) {
        PlatformOverviewVariables platformOverviewVariables = BeanUtil.mapToBean(query.getCondition(), PlatformOverviewVariables.class, false);
        if ("1".equals(SecurityUtils.getUserInfo().getPlatformLevel())) {
            platformOverviewVariables.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        }
        query.setRecords(platformOverviewVariablesMapper.selectPlatformOverviewVariablesListByLike(query, platformOverviewVariables));
        return query;
    }

    /**
     * 新增平台概览变量
     *
     * @param platformOverviewVariables 平台概览变量信息
     * @return 结果
     */
    @Override
    public int insertPlatformOverviewVariables(PlatformOverviewVariables platformOverviewVariables) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        platformOverviewVariables.setAddTime(LocalDateTime.now());
        platformOverviewVariables.setAddWho(userInfo.getUserName());
        platformOverviewVariables.setAddWhoName(userInfo.getRealName());
        platformOverviewVariables.setDeleteFlag("N");

        PlatformOverviewVariables sel = new PlatformOverviewVariables();
        sel.setPlatformCode(platformOverviewVariables.getPlatformCode());
        sel.setTrip(platformOverviewVariables.getTrip());
        sel.setCity(platformOverviewVariables.getCity());
        sel.setAreaCode(platformOverviewVariables.getAreaCode());
        sel.setDeleteFlag("N");
        List<PlatformOverviewVariables> list = platformOverviewVariablesMapper.selectPlatformOverviewVariablesList(sel);
        if(CollUtil.isNotEmpty(list)){
            platformOverviewVariables.setId(list.get(0).getId());
            return platformOverviewVariablesMapper.updatePlatformOverviewVariables(platformOverviewVariables);
        }
        return platformOverviewVariablesMapper.insertPlatformOverviewVariables(platformOverviewVariables);
    }

    /**
     * 修改平台概览变量
     *
     * @param platformOverviewVariables 平台概览变量信息
     * @return 结果
     */
    @Override
    public int updatePlatformOverviewVariables(PlatformOverviewVariables platformOverviewVariables) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        platformOverviewVariables.setAddTime(LocalDateTime.now());
        platformOverviewVariables.setAddWho(userInfo.getUserName());
        platformOverviewVariables.setAddWhoName(userInfo.getRealName());
        return platformOverviewVariablesMapper.updatePlatformOverviewVariables(platformOverviewVariables);
    }


    /**
     * 删除平台概览变量
     *
     * @param id 平台概览变量ID
     * @return 结果
     */
    public int deletePlatformOverviewVariablesById(Integer id) {
        return platformOverviewVariablesMapper.deletePlatformOverviewVariablesById(id);
    }

    ;


    /**
     * 批量删除平台概览变量对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deletePlatformOverviewVariablesByIds(Integer[] ids) {
        return platformOverviewVariablesMapper.deletePlatformOverviewVariablesByIds(ids);
    }

}
