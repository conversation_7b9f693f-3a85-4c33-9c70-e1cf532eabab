package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.admin.api.entity.SysUser;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.CustomerPlatformInfo;
import com.huazheng.tunny.ocean.api.entity.UserInfo;
import com.huazheng.tunny.ocean.api.vo.CustomerInfoVO;
import com.huazheng.tunny.ocean.mapper.CustomerInfoMapper;
import com.huazheng.tunny.ocean.api.entity.CustomerInfo;
import com.huazheng.tunny.ocean.mapper.CustomerPlatformInfoMapper;
import com.huazheng.tunny.ocean.mapper.SysDictMapper;
import com.huazheng.tunny.ocean.service.CustomerInfoService;
import com.huazheng.tunny.ocean.service.CustomerPlatformInfoService;
import com.huazheng.tunny.ocean.service.SysNoConfigService;
import com.huazheng.tunny.ocean.util.PermissionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service("customerInfoService")
public class CustomerInfoServiceImpl extends ServiceImpl<CustomerInfoMapper, CustomerInfo> implements CustomerInfoService {

    @Autowired
    private CustomerInfoMapper customerInfoMapper;

    @Autowired
    private CustomerPlatformInfoMapper customerPlatformInfoMapper;

    @Autowired
    private SysNoConfigService sysNoConfigService;


    @Autowired
    private PermissionUtil permissionUtil;

    public CustomerInfoMapper getCustomerInfoMapper() {
        return customerInfoMapper;
    }

    public void setCustomerInfoMapper(CustomerInfoMapper customerInfoMapper) {
        this.customerInfoMapper = customerInfoMapper;
    }

    public CustomerPlatformInfoMapper getCustomerPlatformInfoMapper() {
        return customerPlatformInfoMapper;
    }

    public void setCustomerPlatformInfoMapper(CustomerPlatformInfoMapper customerPlatformInfoMapper) {
        this.customerPlatformInfoMapper = customerPlatformInfoMapper;
    }

    public SysNoConfigService getSysNoConfigService() {
        return sysNoConfigService;
    }

    public void setSysNoConfigService(SysNoConfigService sysNoConfigService) {
        this.sysNoConfigService = sysNoConfigService;
    }

    public PermissionUtil getPermissionUtil() {
        return permissionUtil;
    }

    public void setPermissionUtil(PermissionUtil permissionUtil) {
        this.permissionUtil = permissionUtil;
    }

    @Value("${db.database}")
    private String database;
    /**
     * 查询客户信息主表信息
     *
     * @param info 客户信息主表ID
     * @return 客户信息主表信息
     */
    @Override
    public CustomerInfo selectCustomegenNorInfoById(CustomerPlatformInfo info) {
        return customerInfoMapper.selectCustomerInfoById(info);
    }

    /**
     * 查询客户信息主表信息
     *
     * @param rowId 客户信息主表ID
     * @return 客户信息主表信息
     */
    @Override
    public CustomerInfo selectCustomegenNorInfoById1(String rowId) {
        return customerInfoMapper.selectCustomerInfoById1(rowId);
    }

    @Override
    public CustomerInfo selectCustomegenNorInfoByCustomerCode(CustomerInfo customerInfo) {
        return customerInfoMapper.selectCustomegenNorInfoByCustomerCode(customerInfo);
    }

    @Override
    public List<Map<String, Object>> listCustomerInfo(Map<String, Object> map) {
        return customerInfoMapper.listCustomerInfo(map);
    }

    /**
     * 查询客户信息主表列表
     *
     * @param customerInfo 客户信息主表信息
     * @return 客户信息主表集合
     */
    @Override
    public List<CustomerInfo> selectCustomerInfoList(CustomerInfo customerInfo) {
        return customerInfoMapper.selectCustomerInfoList(customerInfo);
    }

    @Override
    public List<CustomerInfo> getCustomerByPlatformCode(CustomerInfo customerInfo) {
        customerInfo.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        List<CustomerInfo> customerByPlatformCode = customerInfoMapper.getCustomerByPlatformCode(customerInfo);
        String platformLevel = SecurityUtils.getUserInfo().getPlatformLevel();
        if("2".equals(platformLevel)){
            CustomerInfo customerInfo1 = new CustomerInfo();
            customerInfo1.setCustomerCode("ztdl");
            customerInfo1.setCompanyName("中铁国际多式联运有限公司");
        }
        return customerByPlatformCode;
    }

    @Override
    public List<CustomerInfo> getCityByPlatformCode(CustomerInfo customerInfo) {
        customerInfo.setPlatformCodePro(SecurityUtils.getUserInfo().getPlatformCode());
        return customerInfoMapper.getCityByPlatformCode(customerInfo);
    }

    @Override
    public List<CustomerInfo> selectCustomerInfoListBySocialUcCode(CustomerInfo customerInfo) {
        return customerInfoMapper.selectCustomerInfoListBySocialUcCode(customerInfo);
    }

    @Override
    public List<CustomerInfo> selectCustomerInfoListBySocialUcCode2(CustomerInfo customerInfo) {
        return customerInfoMapper.selectCustomerInfoListBySocialUcCode2(customerInfo);
    }

    @Override
    public List<CustomerInfo> selectCustomerInfoList1(CustomerInfo customerInfo) {
        customerInfo.setDatabase(database);
        return customerInfoMapper.selectCustomerInfoList1(customerInfo);
    }

    /**
     * 根据编码查询该用户省级平台信息
     *
     * @param customerCode
     * @return
     */
    @Override
    public CustomerInfo selectInfoByCode(String customerCode) {
        CustomerInfo customerInfo=new CustomerInfo();
        customerInfo.setCustomerCode(customerCode);
        customerInfo.setDatabase(database);
        return customerInfoMapper.selectInfoByCode(customerInfo);
    }

    /**
     * 根据平台查询用户编码
     *
     * @param platformCode
     * @return
     */
    @Override
    public String selectCustomerNoByPlatform(String platformCode) {
        return customerInfoMapper.selectCustomerNoByPlatform(platformCode);
    }

    /**
     * 分页模糊查询客户信息主表列表
     *
     * @return 客户信息主表集合
     */
    @Override
    public Page selectCustomerInfoListByLike(Query query) {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.resvered_field_02 asc,x.add_time");
            query.setAsc(Boolean.FALSE);
        }
        CustomerInfo customerInfo = BeanUtil.mapToBean(query.getCondition(), CustomerInfo.class, false);
        List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoListByLike(query, customerInfo);
        query.setRecords(customerInfos);
        return query;
    }

    @Override
    public R listXlk(Map<String, Object> params) {
        CustomerInfo customerInfo = BeanUtil.mapToBean(params, CustomerInfo.class, false);
        String s="";
        String customerFlag = "2";
        if(customerFlag.equals(customerInfo.getCustomerFlag())){
            s="1";
        }else{
            s="0";
        }
        customerInfo.setCustomerCode(permissionUtil.getPcPermissonCustomer(customerInfo.getCustomerCode(), s));
        List<CustomerInfo> customerInfos = customerInfoMapper.selectListXlk(customerInfo);
        return new R(Boolean.TRUE, customerInfos);
    }

    @Override
    public Page selectCustomerInfoPage(Query query) {
        CustomerInfo customerInfo = BeanUtil.mapToBean(query.getCondition(), CustomerInfo.class, false);
        List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoPage(query, customerInfo);
        query.setRecords(customerInfos);
        query.setTotal(customerInfoMapper.selectAllTwo(customerInfo));
        return query;
    }

    /**
     * 新增客户信息主表
     *
     * @param customerInfo 客户信息主表信息
     * @return 结果
     */
    @Override
    public int insertCustomerInfo(CustomerInfo customerInfo) {
        return customerInfoMapper.insertCustomerInfo(customerInfo);
    }

    @Override
    public List<CustomerInfo> isHaveMe(CustomerInfo customerInfo) {
        return customerInfoMapper.queryCus(customerInfo);
    }

    /**
     * 客户、市平台注册
     *
     * @param customerInfo
     * @return R
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R cusRegister(CustomerInfoVO customerInfo) {
        CustomerPlatformInfo customerPlatformInfo = customerInfo.getCustomerPlatformInfo();

        //根据选择的市平台编码查出来对应的省平台信息 塞入子表对应字段中
        CustomerPlatformInfo customerPlatformInfo1 = new CustomerPlatformInfo();
        customerPlatformInfo1.setPlatformCode(customerPlatformInfo.getPlatformCode());
        List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoMapper.selectCustomerPlatformInfoList(customerPlatformInfo1);
        if (customerPlatformInfos!=null && customerPlatformInfos.size()>0){
            String platformCodePro = null;
            String platformCodeProName = null;
            for (CustomerPlatformInfo platformInfo : customerPlatformInfos) {
                if (StrUtil.isNotEmpty(platformInfo.getPlatformCodePro())){
                    platformCodePro = platformInfo.getPlatformCodePro();
                    platformCodeProName = platformInfo.getResveredField03();
                }
            }
            customerPlatformInfo.setPlatformCodePro(platformCodePro);
            customerPlatformInfo.setResveredField03(platformCodeProName);
        }

        SecruityUser user = SecurityUtils.getUserInfo();
        customerPlatformInfo.setAddTime(new Date());
        customerPlatformInfo.setAddWho(user.getUserName());
        customerPlatformInfo.setAddWhoName(user.getUserName());
        customerInfo.setAddTime(new Date());
        customerInfo.setAddWho(user.getUserName());
        customerInfo.setAddWhoName(user.getRealName());
        //根据输入社会统一信用代码,查询当前登录客户有没有注册过当前输入的签约平台（子表），有提示已注册
        List<CustomerInfo> list = customerInfoMapper.queryCus(customerInfo);
        if (CollUtil.isNotEmpty(list)) {
            CustomerInfo cus = list.get(0);
            CustomerPlatformInfo cpfi = new CustomerPlatformInfo();
            cpfi.setCustomerCode(cus.getCustomerCode());
            cpfi.setPlatformCode(customerPlatformInfo.getPlatformCode());
            List tempList = customerPlatformInfoMapper.queryPlat(cpfi);
            if (CollUtil.isNotEmpty(tempList)) {
                return new R(Boolean.FALSE, "尊敬的客户，该平台您已签约，请检查，如有疑问请联系系统管理员");
            } else {
                //当前填写平台未签约，但已注册
                customerPlatformInfo.setCustomerCode(cus.getCustomerCode());
                customerInfo.getCustomerPlatformInfo().setAuditStatus("1");
                customerPlatformInfoMapper.insertCustomerPlatformInfo(customerInfo.getCustomerPlatformInfo());
                return new R(Boolean.FALSE, "注册成功，等待审核");
            }
        } else {
            //没有，则进行主子表基础信息新增操作
            String customerNoMes = sysNoConfigService.genNo("CUS");
            String str = "，";
            if (customerNoMes.contains(str)) {
                return new R(Boolean.FALSE, customerNoMes);
            }
            customerInfo.setCustomerCode(customerNoMes);
            customerPlatformInfo.setCustomerCode(customerNoMes);
            customerPlatformInfo.setAuditStatus("1");
            try {
                customerInfoMapper.insertCustomerInfo(customerInfo);
                customerPlatformInfoMapper.insertCustomerPlatformInfo(customerPlatformInfo);
            } catch (Exception e) {
                System.out.println(e.getMessage());
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                e.printStackTrace();
                return new R(Boolean.FALSE, "注册失败，请联系管理员");
            }
            return new R(Boolean.TRUE, "注册成功，等待审核");
        }
    }

    /**
     * 修改客户信息主表
     *
     * @param customerInfo 客户信息主表信息
     * @return 结果
     */
    @Override
    public int updateCustomerInfo(CustomerInfo customerInfo) {
        return customerInfoMapper.updateCustomerInfo(customerInfo);
    }


    /**
     * 删除客户信息主表
     *
     * @param customerInfo 客户信息主表ID
     * @return 结果
     */
    @Override
    public int deleteCustomerInfoById(CustomerInfo customerInfo) {
        return customerInfoMapper.deleteCustomerInfoById(customerInfo);
    }

    ;


    /**
     * 批量删除客户信息主表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteCustomerInfoByIds(Integer[] rowIds) {
        return customerInfoMapper.deleteCustomerInfoByIds(rowIds);
    }

    @Override
    public List<CustomerInfo> selectCustomerInfoSocialUcCode(CustomerInfo customerInfo) {
        return customerInfoMapper.selectCustomerInfoSocialUcCode(customerInfo);
    }

    @Override
    public List<CustomerInfo> selectCustomerInfoByresveredField(CustomerInfo customerInfo) {
        return customerInfoMapper.selectCustomerInfoByresveredField(customerInfo);
    }


    @Override
    public int updateCustomerInfoBySocialUcCode(CustomerInfo customerInfo) {
        return customerInfoMapper.updateCustomerInfoBySocialUcCode(customerInfo);
    }

    @Override
    public R getUserWithMiniPlatform(CustomerInfo customerInfo) {
        String customerCode = customerInfo.getCustomerCode();
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        if(StrUtil.isBlank(customerCode) && "0".equals(userInfo.getPlatformLevel())){
            customerCode = userInfo.getPlatformCode();
        }
        String username = null;
        if("0".equals(userInfo.getPlatformLevel()) && "1".equals(userInfo.getDataFlag())){
            username = userInfo.getUserName();
        }
        return R.success(customerInfoMapper.getUserWithMiniPlatform(customerCode,username));
    }

}
