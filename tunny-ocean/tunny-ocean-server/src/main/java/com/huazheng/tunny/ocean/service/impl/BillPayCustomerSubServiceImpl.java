package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.api.vo.BillDealWithCityAndCostVO;
import com.huazheng.tunny.ocean.mapper.BillPayCustomerSubMapper;
import com.huazheng.tunny.ocean.api.entity.BillPayCustomerSub;
import com.huazheng.tunny.ocean.service.BillPayCustomerSubService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("billPayCustomerSubService")
public class BillPayCustomerSubServiceImpl extends ServiceImpl<BillPayCustomerSubMapper, BillPayCustomerSub> implements BillPayCustomerSubService {

    @Autowired
    private BillPayCustomerSubMapper billPayCustomerSubMapper;

    /**
     * 查询应收账单（市）子账单表信息
     *
     * @param id 应收账单（市）子账单表ID
     * @return 应收账单（市）子账单表信息
     */
    @Override
    public BillPayCustomerSub selectBillSubIncomeCityById(Integer id)
    {
        return billPayCustomerSubMapper.selectBillSubIncomeCityById(id);
    }

    /**
     * 查询应收账单（市）子账单表列表
     *
     * @param billPayCustomerSub 应收账单（市）子账单表信息
     * @return 应收账单（市）子账单表集合
     */
    @Override
    public List<BillPayCustomerSub> selectBillSubIncomeCityList(BillPayCustomerSub billPayCustomerSub)
    {
        return billPayCustomerSubMapper.selectBillSubIncomeCityList(billPayCustomerSub);
    }


    /**
     * 分页模糊查询应收账单（市）子账单表列表
     * @return 应收账单（市）子账单表集合
     */
    @Override
    public Page selectBillSubIncomeCityListByLike(Query query)
    {
        BillPayCustomerSub billPayCustomerSub =  BeanUtil.mapToBean(query.getCondition(), BillPayCustomerSub.class,false);
        query.setRecords(billPayCustomerSubMapper.selectBillSubIncomeCityListByLike(query, billPayCustomerSub));
        return query;
    }

    /**
     * 新增应收账单（市）子账单表
     *
     * @param billPayCustomerSub 应收账单（市）子账单表信息
     * @return 结果
     */
    @Override
    public int insertBillSubIncomeCity(BillPayCustomerSub billPayCustomerSub)
    {
        return billPayCustomerSubMapper.insertBillSubIncomeCity(billPayCustomerSub);
    }

    /**
     * 修改应收账单（市）子账单表
     *
     * @param billPayCustomerSub 应收账单（市）子账单表信息
     * @return 结果
     */
    @Override
    public int updateBillSubIncomeCity(BillPayCustomerSub billPayCustomerSub)
    {
        return billPayCustomerSubMapper.updateBillSubIncomeCity(billPayCustomerSub);
    }


    /**
     * 删除应收账单（市）子账单表
     *
     * @param id 应收账单（市）子账单表ID
     * @return 结果
     */
    public int deleteBillSubIncomeCityById(Integer id)
    {
        return billPayCustomerSubMapper.deleteBillSubIncomeCityById( id);
    };


    /**
     * 批量删除应收账单（市）子账单表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBillSubIncomeCityByIds(Integer[] ids)
    {
        return billPayCustomerSubMapper.deleteBillSubIncomeCityByIds( ids);
    }

    @Override
    public List<BillDealWithCityAndCostVO> selectFdBillSubByBillNo(String billNo,String customerName,String platformCode) {
        return billPayCustomerSubMapper.selectFdBillSubByBillNo(billNo,customerName,platformCode);
    }

    @Override
    public List<String> selectBillOfCostByBillCode(String billNo) {
        return billPayCustomerSubMapper.selectBillOfCostByBillCode(billNo);
    }

}
