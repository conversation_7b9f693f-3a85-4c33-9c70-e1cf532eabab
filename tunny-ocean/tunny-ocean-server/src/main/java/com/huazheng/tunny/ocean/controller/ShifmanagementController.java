package com.huazheng.tunny.ocean.controller;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.FdShippingAccoundetailDTO;
import com.huazheng.tunny.ocean.api.dto.ShiftManagementWechatDTO;
import com.huazheng.tunny.ocean.api.dto.ShippingPlanReportDTO;
import com.huazheng.tunny.ocean.api.entity.OperationLog;
import com.huazheng.tunny.ocean.api.entity.Shifmanagement;
import com.huazheng.tunny.ocean.api.vo.FdShippingAccountVO;
import com.huazheng.tunny.ocean.api.vo.ShifmanagementVO;
import com.huazheng.tunny.ocean.service.OperationLogService;
import com.huazheng.tunny.ocean.service.ProvinceShiftNoService;
import com.huazheng.tunny.ocean.service.ShifmanagementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.*;

/**
 * 班次管理
 *
 * <AUTHOR> code ocean
 * @date 2021-07-02 14:46:01
 */
@RestController
@RequestMapping("/shifmanagement")
@Slf4j
public class ShifmanagementController {
    @Autowired
    private ShifmanagementService shifmanagementService;
    @Autowired
    private SignatureController signatureController;
    @Autowired
    private OperationLogService operationLogService;

    @Autowired
    private ProvinceShiftNoService provinceShiftNoService;
    @Value("${path.windowsPath}")
    private String windowsPath;
    @Value("${path.linuxPath}")
    private String linuxPath;

    /**
     * 可撤换箱班次列表
     *
     * @param params
     * @return
     */
    @GetMapping("/selectToChangeList")
    public Page<Shifmanagement> selectToChangeList(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return shifmanagementService.selectToChangeList(new Query<>(params));
    }

    /**
     * 可撤换箱班次列表-市
     *
     * @param params
     * @return
     */
    @GetMapping("/selectToChangeListCity")
    public Page<Shifmanagement> selectToChangeListCity(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return shifmanagementService.selectToChangeListCity(new Query<>(params));
    }

    /**
     * 分页列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return shifmanagementService.selectShifmanagementListByLike(new Query<>(params));
    }

    @GetMapping("/page2")
    public Page page2(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return shifmanagementService.selectShifmanagementListByLike2(new Query<>(params));
    }

    /**
     * 分页列表统计
     *
     * @param params
     * @return
     */
    @GetMapping("/countForPage")
    public R countForPage(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return shifmanagementService.countForPage(new Query<>(params));
    }

    /**
     * 列表
     *
     * @return
     */
    @GetMapping("/list")
    public List page(@RequestBody Shifmanagement shifmanagement) {
        //对象模糊查询
        return shifmanagementService.selectShifmanagementList(shifmanagement);
    }

    /**
     * 班次日历
     *
     * @param shifmanagement
     * @return
     */
    @PostMapping("/findShiftMesList")
    public R findShiftMesList(@RequestBody Shifmanagement shifmanagement) {
        //根据发运月份、所属市平台查询班次信息list
        return new R(shifmanagementService.selectShifmanagementList(shifmanagement));
    }


    /**
     * 班次客户信息
     *
     * @param params
     * @return
     */
    @GetMapping("/findSpaceNums")
    public Page findSpaceNums(@RequestParam Map<String, Object> params) {
        return shifmanagementService.findSpaceNums(new Query<>(params));
    }

    /**
     * 班次客户信息(新)
     * customerBooking
     *
     * @param params
     * @return
     */
    @GetMapping("/customerBookingNums")
    public R customerBookingNums(@RequestParam Map<String, Object> params) {
        return shifmanagementService.customerBookingNums(params);
    }

    /**
     * 订舱平台班次日历
     *
     * @param shifmanagement
     * @return
     */
    @PostMapping("/findShiftCan")
    public R<List<ShifmanagementVO>> findShiftCanTotal(@RequestBody Shifmanagement shifmanagement) {
        //根据发运月份、所属市平台查询班次信息list
        return new R(shifmanagementService.findShiftCan(shifmanagement));
    }

    /**
     * 订舱平台班次列表
     *
     * @param shifmanagement
     * @return
     */
    @PostMapping("/findShiftCanList")
    public R<List<ShifmanagementVO>> findShiftCanList(@RequestBody Shifmanagement shifmanagement) {
        //根据发运月份、所属市平台查询班次信息list
        return new R(shifmanagementService.findShiftCanList(shifmanagement));
    }

    /**
     * 订舱平台班次分页列表
     *
     * @param params
     * @return
     */
    @GetMapping("/findShiftCanPage")
    public Page<ShifmanagementVO> findShiftCanPage(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return shifmanagementService.findShiftCanPage(new Query<>(params));
    }

    /**
     * 可定班次数量
     *
     * @param shifmanagement
     * @return
     */
    @PostMapping("/findShiftCanCount")
    public Integer findShiftCanCount(@RequestBody Shifmanagement shifmanagement) {
        //对象模糊查询
        return shifmanagementService.findShiftCanCount(shifmanagement);
    }

    @PostMapping("/findShiftCanForSh")
    public String findShiftCanForSh(@RequestBody JSONObject jsonObject) {
        String content = null;
        String data = null;
        String content2 = null;
        try {
            data = signatureController.getPost(jsonObject);
            content2 = String.valueOf(jsonObject.get("content"));
            Shifmanagement shifmanagement = JSONUtil.toBean(data, Shifmanagement.class);
            if (StrUtil.isEmpty(shifmanagement.getResveredField04())) {
                content = JSONUtil.parseObj(new R<>(Boolean.FALSE, "系统错误：没有接收到客户编码！"), false).toStringPretty();
                String result = signatureController.returnPostSh2("/shifmanagement/findShiftCanForSh", content);
                return result;
            }
            List<Shifmanagement> shiftCanForSh = shifmanagementService.findShiftCanForSh(shifmanagement);
            content = JSONUtil.parseObj(new R<>(Boolean.TRUE, shiftCanForSh), false).toStringPretty();
        } catch (Exception e) {
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE, "系统错误：" + e), false).toStringPretty();
            OperationLog log4 = new OperationLog();
            log4.setUuid(UUID.randomUUID().toString());
            log4.setProcessType("查询班列计划");
            log4.setOperationCode("sh");
            log4.setOperationName("上合");
            log4.setOperationTime(new Date());
            log4.setOperationOpinion(content2 + data);
            operationLogService.insertOperationLog(log4);
        }
        //调用接口
        String result = signatureController.returnPostSh2("/shifmanagement/findShiftCanForSh", content);
        return result;
    }

    /**
     * 信息
     *
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        Shifmanagement shifmanagement = shifmanagementService.selectShifmanagementById(rowId);
        return new R<>(shifmanagement);
    }

    /**
     * 班次详情页
     *
     * @param rowId
     * @return R
     */
    @GetMapping("/selectShiftDetail")
    public R selectShiftDetail(String rowId) {
        return new R<>(shifmanagementService.selectShiftDetail(rowId));
    }

    /**
     * 保存
     *
     * @param shifmanagement
     * @return R
     */
    @PostMapping
    public R save(@RequestBody Shifmanagement shifmanagement) {
        R r = shifmanagementService.insertShifmanagement(shifmanagement);
        return r;
    }

    /**
     * 班次发布
     *
     * @param shifmanagement
     * @return
     */
    @PostMapping("/shiftRelease")
    public R shiftRelease(@RequestBody Shifmanagement shifmanagement) {
        return shifmanagementService.shiftRelease(shifmanagement);
    }

    /**
     * 班次撤销
     *
     * @param shifmanagement
     * @return
     */
    @PostMapping("/updatereleaseStatus")
    public R updatereleaseStatus(@RequestBody Shifmanagement shifmanagement) {
        return shifmanagementService.updatereleaseStatus(shifmanagement);
    }

    /**
     * 修改/删除班次管理
     *
     * @param shifmanagement
     * @return R
     */
    @PutMapping
    public R update(@RequestBody Shifmanagement shifmanagement) {
        return shifmanagementService.updateShifmanagement(shifmanagement);
    }

    /**
     * 修改班次发运时间
     *
     * @param shifmanagement
     * @return R
     */
    @PutMapping("/updateShippingTime")
    public R updateShippingTime(@RequestBody Shifmanagement shifmanagement) {
        return shifmanagementService.updateShippingTime(shifmanagement);
    }


    /**
     * 删除
     *
     * @param rowId
     * @return R
     */
    @DeleteMapping("/{rowId}")
    public R delete(@PathVariable String rowId) {
        shifmanagementService.deleteById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     *
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody List<String> rowIds) {
        shifmanagementService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 导入EXCEL
     * <p>
     *
     * @param titleStr
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<Shifmanagement> list = reader.readAll(Shifmanagement.class);
        shifmanagementService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }

    //8济南分-发运计划报表
    @GetMapping("/shippingPlanReport")
    public void shippingPlanReport(Shifmanagement shifmanagement, HttpServletResponse res) throws Exception {

        List<ShippingPlanReportDTO> shippingPlanReportDTOS = shifmanagementService.shippingPlanReportList2(shifmanagement);
        Long num = 1L;
        HashMap<String, Object> map = new HashMap<>();
        FdShippingAccoundetailDTO header = new FdShippingAccoundetailDTO();

        if (shifmanagement.getPlanTime() != null) {
            map.put("planTime", shifmanagement.getPlanTime());
        }
        ExcelWriter excelWriter = null;
        String templateFileName = "shippingPlan.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)) {
            templateFileName = linuxPath + templateFileName;
        } else {
            templateFileName = windowsPath + templateFileName;
        }
        String fileName = URLEncoder.encode("济南分-发运计划", "UTF-8").replaceAll("\\+", "%20");
        res.setHeader("Content-disposition", "attachment;filename*=" + fileName + ".xlsx");
        excelWriter = EasyExcel.write(res.getOutputStream()).withTemplate(templateFileName).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        if (map != null) {
            excelWriter.fill(map, writeSheet);//存入map
        }
        if (shippingPlanReportDTOS != null) {
            excelWriter.fill(shippingPlanReportDTOS, writeSheet);//存入list
        }
        excelWriter.finish();
    }

    @GetMapping("/shippingPlanList")
    public Page<ShippingPlanReportDTO> shippingPlanList(@RequestParam Map<String, Object> params) {
        return shifmanagementService.shippingPlanReportList(new Query<>(params));
    }

    /**
     * 修改班次发运线路
     *
     * @param shifmanagement
     * @return
     */
    @PostMapping("/updateShippingLine")
    public R updateShippingLine(@RequestBody Shifmanagement shifmanagement) {
        return shifmanagementService.updateShippingLine(shifmanagement);
    }

    /**
     * 修改班次名称
     *
     * @param shifmanagement
     * @return
     */
    @PostMapping("/updateShiftName")
    public R updateShiftName(@RequestBody Shifmanagement shifmanagement) {
        return shifmanagementService.updateShiftName(shifmanagement);
    }

    @PostMapping("/getProvinceShiftNo")
    public R getProvinceShiftNo(@RequestBody FdShippingAccountVO vo) {
        return provinceShiftNoService.getProvinceShiftNo(vo);
    }

    /**
     * 班次新增指定客户
     *
     * @param shifmanagement
     * @return
     */
    @PostMapping("/addCustomer")
    public R addCustomer(@RequestBody Shifmanagement shifmanagement) {
        return shifmanagementService.addCustomer(shifmanagement);
    }

    /**
     * 修改班次舱位数
     *
     * @param shifmanagement
     * @return
     */
    @PostMapping("/updateShiftNumOfTruePositions")
    public R updateShiftNumOfTruePositions(@RequestBody Shifmanagement shifmanagement) {
        return shifmanagementService.updateShiftNumOfTruePositions(shifmanagement);
    }

    /**
     * 小程序发运列表
     *
     * @param params
     * @return
     */
    @GetMapping("/pageWechat")
    public Page<ShifmanagementVO> pageWechat(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return shifmanagementService.pageWechat(new Query<>(params));
    }

    /**
     * 发运城市列表
     *
     * @param shiftManagementWechatDTO
     * @return
     */
    @PostMapping("/selectCityList")
    public R selectCityList(@RequestBody ShiftManagementWechatDTO shiftManagementWechatDTO) {
        return shifmanagementService.selectCityList(shiftManagementWechatDTO);
    }


    /**
     * 更新班次信息并同步台账
     *
     * @param shifmanagement 班次信息
     * @return R
     * <AUTHOR>
     * @since 2025/8/2 14:03
     **/
    @PostMapping("/updateShiftAndSyncLedger")
    public R updateShiftAndSyncLedger(@RequestBody Shifmanagement shifmanagement) {
        return shifmanagementService.updateShiftAndSyncLedger(shifmanagement);
    }
}