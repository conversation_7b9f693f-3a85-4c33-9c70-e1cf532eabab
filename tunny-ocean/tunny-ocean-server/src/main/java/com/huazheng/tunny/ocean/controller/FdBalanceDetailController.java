package com.huazheng.tunny.ocean.controller;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.FdBalanceDetail;
import com.huazheng.tunny.ocean.api.entity.LineManagement;
import com.huazheng.tunny.ocean.api.entity.Shifmanagement;
import com.huazheng.tunny.ocean.mapper.ShifmanagementMapper;
import com.huazheng.tunny.ocean.service.FdBalanceDetailService;
import com.huazheng.tunny.ocean.service.LineManagementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 余额明细表
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:30:20
 */
@RestController
@RequestMapping("/fdBalanceDetail")
@Slf4j
public class FdBalanceDetailController {
    @Autowired
    private FdBalanceDetailService fdBalanceDetailService;
    @Autowired
    private LineManagementService lineManagementService;
    @Autowired
    private ShifmanagementMapper shifmanagementMapper;

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {

        //对象模糊查询
        return fdBalanceDetailService.selectFdBalanceDetailByLike(new Query<>(params));
    }

    @GetMapping("/pageTotal")
    public R pageTotal(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdBillSubService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdBalanceDetailService.pageTotal(new Query<>(params));
    }

    @GetMapping("/getList")
    public R getList(FdBalanceDetail fdBalanceDetail) {
        List<FdBalanceDetail> list = fdBalanceDetailService.selectFdBalanceDetailList(fdBalanceDetail);
        return new R<>(200, Boolean.TRUE, list);
    }

    @GetMapping("/getList2")
    public R getList2(FdBalanceDetail fdBalanceDetail) {
        final String shiftId = fdBalanceDetail.getShiftNo();
        Shifmanagement shifmanagement = new Shifmanagement();
        shifmanagement.setShiftId(shiftId);
        shifmanagement.setDeleteFlag("N");
        final List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementListByLike(shifmanagement);
        fdBalanceDetail.setShiftNo(null);
        if (CollUtil.isNotEmpty(shifmanagements)) {
            LineManagement lineManagement = new LineManagement();
            lineManagement.setLineCode(shifmanagements.get(0).getShippingLineCode());
            lineManagement.setDeleteFlag("N");
            final List<LineManagement> lineManagements = lineManagementService.selectLineManagementList(lineManagement);
            if (CollUtil.isNotEmpty(lineManagements)) {
                fdBalanceDetail.setShUnitCode(lineManagements.get(0).getAreaCode());
            } else {
                return new R<>(200, Boolean.TRUE, null, "未查询到该班次线路信息");
            }
        } else {
            return new R<>(200, Boolean.TRUE, null, "该班次未配置发运线路编码");
        }
        List<FdBalanceDetail> list = fdBalanceDetailService.selectFdBalanceDetailList(fdBalanceDetail);
        return new R<>(200, Boolean.TRUE, list);
    }

    @GetMapping("/getAccountList")
    public R getAccountList(@RequestParam Map<String, Object> params) {
        List<FdBalanceDetail> list = fdBalanceDetailService.getAccountList(new Query<>(params));
        return new R<>(200, Boolean.TRUE, list);
    }

    /**
     * 作废余额
     *
     * @param id
     * @return R
     */
    @PostMapping("/cancel/{id}")
    public R cancel(@PathVariable("id") Integer id) {
        return fdBalanceDetailService.cancel(id);
    }


    /**
     * 根据客户编码查询余额总数
     *
     * @Param: null
     * @Return:
     * @Author: Howe
     * @Date: 2024/6/24 上午10:29
     **/
    @GetMapping("/customer/fdBalance/page")
    public Page selectTotalPageByCustomer(@RequestParam Map<String, Object> params) {
        return fdBalanceDetailService.selectTotalPageByCustomer(new Query<>(params));
    }


    /**
     * 保存流水
     *
     * @Param: null
     * @Return:
     * @Author: Howe
     * @Date: 2024/6/25 下午4:23
     **/
    @PostMapping("/save")
    public R insertFdBalanceDetail(@RequestBody FdBalanceDetail fdBalanceDetail) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        fdBalanceDetail.setAddWhoName(userInfo.getUserName());
        fdBalanceDetail.setAddWhoName(userInfo.getRealName());
        fdBalanceDetail.setAddTime(LocalDateTime.now());
        fdBalanceDetail.setRemainingAmount(fdBalanceDetail.getTotalAmount());
        fdBalanceDetail.setAvailableAmount(fdBalanceDetail.getTotalAmount());
        fdBalanceDetail.setLockingAmount(BigDecimal.ZERO);
        return fdBalanceDetailService.insertFdBalanceDetail(fdBalanceDetail);
    }
}
