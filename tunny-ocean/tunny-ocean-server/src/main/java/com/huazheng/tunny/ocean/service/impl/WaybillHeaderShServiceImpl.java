package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.util.StringUtils;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.*;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.vo.IndexVO;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service("waybillHeaderShService")
public class WaybillHeaderShServiceImpl extends ServiceImpl<WaybillHeaderShMapper, WaybillHeaderSh> implements WaybillHeaderShService {

    @Autowired
    private WaybillHeaderShMapper waybillHeaderShMapper;

    public WaybillHeaderShMapper getWaybillHeaderShMapper() {
        return waybillHeaderShMapper;
    }

    public void setWaybillHeaderShMapper(WaybillHeaderShMapper waybillHeaderShMapper) {
        this.waybillHeaderShMapper = waybillHeaderShMapper;
    }

    /**
     * 分页模糊查询运单主表列表
     *
     * @return 运单主表集合
     */
    @Override
    public Page selectWaybillHeaderListByLike(Query query) {
        WaybillHeaderSh waybillHeaderSh = BeanUtil.mapToBean(query.getCondition(), WaybillHeaderSh.class, false);
        Integer c = waybillHeaderShMapper.queryCount(waybillHeaderSh);
        if (c != null && c != 0) {
            query.setTotal(c);
            List<WaybillHeaderSh> waybillHeaders = waybillHeaderShMapper.selectWaybillHeaderShListByLike(query, waybillHeaderSh);
            query.setRecords(waybillHeaders);
        }
        return query;
    }

}
