package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.EfFinancingReleaseGoods;
import com.huazheng.tunny.ocean.api.entity.EfFinancingWarehouse;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseOperation;
import com.huazheng.tunny.ocean.controller.SignatureController;
import com.huazheng.tunny.ocean.mapper.EfFinancingPledgechangeinfoMapper;
import com.huazheng.tunny.ocean.api.entity.EfFinancingPledgechangeinfo;
import com.huazheng.tunny.ocean.mapper.EfFinancingReleaseGoodsMapper;
import com.huazheng.tunny.ocean.mapper.EfFinancingWarehouseMapper;
import com.huazheng.tunny.ocean.service.EfFinancingPledgechangeinfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import java.util.UUID;

@Service("efFinancingPledgechangeinfoService")
public class EfFinancingPledgechangeinfoServiceImpl extends ServiceImpl<EfFinancingPledgechangeinfoMapper, EfFinancingPledgechangeinfo> implements EfFinancingPledgechangeinfoService {

    @Autowired
    private EfFinancingPledgechangeinfoMapper efFinancingPledgechangeinfoMapper;
    @Autowired
    private EfFinancingReleaseGoodsMapper efFinancingReleaseGoodsMapper;
    @Autowired
    private EfFinancingWarehouseMapper efFinancingWarehouseMapper;
    @Autowired
    private SignatureController signatureController;

    /**
     * 查询解/补质押物信息
     *
     * @param rowId 解/补质押物ID
     * @return 解/补质押物信息
     */
    @Override
    public EfFinancingPledgechangeinfo selectEfFinancingPledgechangeinfoById(String rowId)
    {
        EfFinancingPledgechangeinfo efFinancingPledgechangeinfo = efFinancingPledgechangeinfoMapper.selectEfFinancingPledgechangeinfoById(rowId);
        EfFinancingReleaseGoods goods = new EfFinancingReleaseGoods();
        goods.setApplicationNo(efFinancingPledgechangeinfo.getApplicationNo());
        goods.setDeleteFlag("N");
        List<EfFinancingReleaseGoods> efFinancingReleaseGoods = efFinancingReleaseGoodsMapper.selectEfFinancingReleaseGoodsList(goods);
        efFinancingPledgechangeinfo.setAddGoods(efFinancingReleaseGoods);
        return efFinancingPledgechangeinfo;
    }

    /**
     * 查询解/补质押物列表
     *
     * @param efFinancingPledgechangeinfo 解/补质押物信息
     * @return 解/补质押物集合
     */
    @Override
    public List<EfFinancingPledgechangeinfo> selectEfFinancingPledgechangeinfoList(EfFinancingPledgechangeinfo efFinancingPledgechangeinfo)
    {
        return efFinancingPledgechangeinfoMapper.selectEfFinancingPledgechangeinfoList(efFinancingPledgechangeinfo);
    }


    /**
     * 分页模糊查询解/补质押物列表
     * @return 解/补质押物集合
     */
    @Override
    public Page selectEfFinancingPledgechangeinfoListByLike(Query query)
    {
        EfFinancingPledgechangeinfo efFinancingPledgechangeinfo =  BeanUtil.mapToBean(query.getCondition(), EfFinancingPledgechangeinfo.class,false);
        query.setRecords(efFinancingPledgechangeinfoMapper.selectEfFinancingPledgechangeinfoListByLike(query,efFinancingPledgechangeinfo));
        return query;
    }

    /**
     * 新增解/补质押物
     *
     * @param efFinancingPledgechangeinfo 解/补质押物信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String insertEfFinancingPledgechangeinfo(EfFinancingPledgechangeinfo efFinancingPledgechangeinfo)
    {
        if(efFinancingPledgechangeinfo!=null){
            if(CollUtil.isNotEmpty(efFinancingPledgechangeinfo.getAddGoods())){
                for (EfFinancingReleaseGoods releaseGoods:efFinancingPledgechangeinfo.getAddGoods()
                     ) {
                    releaseGoods.setRowId(UUID.randomUUID().toString());
                    releaseGoods.setDeleteFlag("N");
                    releaseGoods.setAddWho("ZC");
                    releaseGoods.setAddWhoName("中钞");
                    releaseGoods.setAddTime(LocalDateTime.now());
                    efFinancingReleaseGoodsMapper.insertEfFinancingReleaseGoods(releaseGoods);
                }
                efFinancingPledgechangeinfo.setRowId(UUID.randomUUID().toString());
                efFinancingPledgechangeinfo.setDeleteFlag("N");
                efFinancingPledgechangeinfo.setAddWho("ZC");
                efFinancingPledgechangeinfo.setAddWhoName("中钞");
                efFinancingPledgechangeinfo.setAddTime(LocalDateTime.now());
                efFinancingPledgechangeinfoMapper.insertEfFinancingPledgechangeinfo(efFinancingPledgechangeinfo);
            }else{
                return JSONUtil.parseObj(new R<>(false,"没有需要补充质押货物数据"), false).toStringPretty();
            }
        }else{
            return JSONUtil.parseObj(new R<>(false,"没有接收到数据"), false).toStringPretty();
        }
        return JSONUtil.parseObj(new R<>(true,"SUCCESS"), false).toStringPretty();
    }

    /**
     * 修改解/补质押物
     *
     * @param efFinancingPledgechangeinfo 解/补质押物信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateEfFinancingPledgechangeinfo(EfFinancingPledgechangeinfo efFinancingPledgechangeinfo)
    {
        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(efFinancingPledgechangeinfo!=null&&CollUtil.isNotEmpty(efFinancingPledgechangeinfo.getAddGoods())){
            //调用E融接口，插入申请数据
            /*String json = JSONUtil.parseObj(efFinancingPledgechangeinfo, true).toStringPretty();
            final String result = signatureController.doPostEf("/applyIncreasePledgeToStorager", json);
            JSONObject dataObject = JSONUtil.parseObj(result);
            String code = String.valueOf(dataObject.get("code"));
            if(!"SUCCESS".equals(code)){
                from = "E融平台:";
                msg = String.valueOf(dataObject.get("msg"));
                flag = false;
                return new R<>(flag,from+msg);
            }*/

            for (EfFinancingReleaseGoods goods:efFinancingPledgechangeinfo.getAddGoods()
                 ) {
//                状态待处理
                goods.setStatus("2");
                goods.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                goods.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                goods.setUpdateTime(LocalDateTime.now());
                efFinancingReleaseGoodsMapper.updateEfFinancingReleaseGoods(goods);
            }
            efFinancingPledgechangeinfoMapper.updateEfFinancingPledgechangeinfo(efFinancingPledgechangeinfo);
        }else{
            msg = "没有接收到补仓数据";
            flag = false;
        }
        return new R<>(flag,from+msg);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R refuse(EfFinancingPledgechangeinfo efFinancingPledgechangeinfo)
    {
        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        EfFinancingPledgechangeinfo pledgechangeinfo = efFinancingPledgechangeinfoMapper.selectEfFinancingPledgechangeinfoByApplicationNo(efFinancingPledgechangeinfo.getApplicationNo());
        if(pledgechangeinfo!=null){
            EfFinancingWarehouse efFinancingWarehouse = new EfFinancingWarehouse();
            efFinancingWarehouse.setWarehouseReceiptNo(pledgechangeinfo.getWarehouseReceiptNo());
            efFinancingWarehouse.setDeleteFlag("N");
            //查询仓单数据
            List<EfFinancingWarehouse> warehouseList = efFinancingWarehouseMapper.selectEfFinancingWarehouseList(efFinancingWarehouse);
            if(CollUtil.isNotEmpty(warehouseList)&&warehouseList.size()>0){
                //设置拒绝补仓
                EfFinancingWarehouse ware = warehouseList.get(0);
                EfWarehouseOperation operation = new EfWarehouseOperation();
                operation.setOperationTime(String.valueOf(LocalDateTime.now()));
                operation.setOperationType("REFUSE_INCREASE_PLEDGE");
                ware.setOperationInfo(operation);
                //调用中钞接口，插入申请数据
                String json = JSONUtil.parseObj(ware, true).toStringPretty();
                final String result = signatureController.doPost("/syncWarehouseReceipt", json);
                JSONObject dataObject = JSONUtil.parseObj(result);
                String code = String.valueOf(dataObject.get("code"));
                if(!"SUCCESS".equals(code)){
                    from = "跨境平台:";
                    msg = dataObject.get("msg") +" 错误代码："+code;
                    flag = false;
                    return new R<>(flag,from+msg);
                }
                //修改补仓状态
                EfFinancingReleaseGoods goods = new EfFinancingReleaseGoods();
                goods.setApplicationNo(efFinancingPledgechangeinfo.getApplicationNo());
//                goods.setStatus("REFUSE_INCREASE_PLEDGE");
                goods.setStatus("3");
                goods.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                goods.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                goods.setUpdateTime(LocalDateTime.now());
                efFinancingReleaseGoodsMapper.updateEfFinancingReleaseGoods(goods);
            }else{
                msg = "没有查询到仓单数据";
                flag = false;
            }
        }else{
            msg = "没有查询到补仓申请数据";
            flag = false;
        }
        return new R<>(flag,from+msg);
    }


    /**
     * 删除解/补质押物
     *
     * @param rowId 解/补质押物ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingPledgechangeinfoById(String rowId)
    {
        return efFinancingPledgechangeinfoMapper.deleteEfFinancingPledgechangeinfoById( rowId);
    };


    /**
     * 批量删除解/补质押物对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingPledgechangeinfoByIds(Integer[] rowIds)
    {
        return efFinancingPledgechangeinfoMapper.deleteEfFinancingPledgechangeinfoByIds( rowIds);
    }

}
