package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.api.entity.BookingRequesdetail;
import com.huazheng.tunny.ocean.api.entity.BookingRequesheader;
import com.huazheng.tunny.ocean.api.entity.WaybillHeader;
import com.huazheng.tunny.ocean.mapper.BookingRequesdetailMapper;
import com.huazheng.tunny.ocean.mapper.BookingRequesheaderMapper;
import com.huazheng.tunny.ocean.mapper.SpaceOccupyMapper;
import com.huazheng.tunny.ocean.api.entity.SpaceOccupy;
import com.huazheng.tunny.ocean.mapper.WaybillHeaderMapper;
import com.huazheng.tunny.ocean.service.SpaceOccupyService;
import org.apache.xmlbeans.xml.stream.Space;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.List;

@Service("spaceOccupyService")
public class SpaceOccupyServiceImpl extends ServiceImpl<SpaceOccupyMapper, SpaceOccupy> implements SpaceOccupyService {

    @Autowired
    private SpaceOccupyMapper spaceOccupyMapper;

    @Autowired
    private BookingRequesdetailMapper detailMapper;

    @Autowired
    private BookingRequesheaderMapper headerMapper;

    @Autowired
    private WaybillHeaderMapper waybillHeaderMapper;

    public SpaceOccupyMapper getSpaceOccupyMapper() {
        return spaceOccupyMapper;
    }

    public void setSpaceOccupyMapper(SpaceOccupyMapper spaceOccupyMapper) {
        this.spaceOccupyMapper = spaceOccupyMapper;
    }

    public BookingRequesdetailMapper getDetailMapper() {
        return detailMapper;
    }

    public void setDetailMapper(BookingRequesdetailMapper detailMapper) {
        this.detailMapper = detailMapper;
    }

    public BookingRequesheaderMapper getHeaderMapper() {
        return headerMapper;
    }

    public void setHeaderMapper(BookingRequesheaderMapper headerMapper) {
        this.headerMapper = headerMapper;
    }

    public WaybillHeaderMapper getWaybillHeaderMapper() {
        return waybillHeaderMapper;
    }

    public void setWaybillHeaderMapper(WaybillHeaderMapper waybillHeaderMapper) {
        this.waybillHeaderMapper = waybillHeaderMapper;
    }

    /**
     * 查询舱位占用表(用来统计还有多少舱位可以使用)信息
     *
     * @param rowId 舱位占用表(用来统计还有多少舱位可以使用)ID
     * @return 舱位占用表(用来统计还有多少舱位可以使用)信息
     */
    @Override
    public SpaceOccupy selectSpaceOccupyById(String rowId)
    {
        return spaceOccupyMapper.selectSpaceOccupyById(rowId);
    }

    /**
     * 查询舱位占用表(用来统计还有多少舱位可以使用)列表
     *
     * @param spaceOccupy 舱位占用表(用来统计还有多少舱位可以使用)信息
     * @return 舱位占用表(用来统计还有多少舱位可以使用)集合
     */
    @Override
    public List<SpaceOccupy> selectSpaceOccupyList(SpaceOccupy spaceOccupy)
    {
        return spaceOccupyMapper.selectSpaceOccupyList(spaceOccupy);
    }


    /**
     * 分页模糊查询舱位占用表(用来统计还有多少舱位可以使用)列表
     * @return 舱位占用表(用来统计还有多少舱位可以使用)集合
     */
    @Override
    public Page selectSpaceOccupyListByLike(Query query)
    {
        SpaceOccupy spaceOccupy =  BeanUtil.mapToBean(query.getCondition(), SpaceOccupy.class,false);
        query.setRecords(spaceOccupyMapper.selectSpaceOccupyListByLike(query,spaceOccupy));
        return query;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSpaceNums(String appNo) {
        BookingRequesdetail detail=new BookingRequesdetail();
        detail.setOrderNo(appNo);
        detail.setDeleteFlag("N");
        float spaceNums=0;
        Integer totalCases = 0;
        List<BookingRequesdetail> list=detailMapper.selectBookingRequesdetailList(detail);
        if(list != null && list.size()>0){
            totalCases = list.size();
            for (BookingRequesdetail bookingRequesdetail:list) {
                String type=bookingRequesdetail.getContainerType().substring(0,2);
                if ("20".equals(type)){
                    spaceNums=spaceNums+0.5f;
                }else if("40".equals(type)||"45".equals(type)){
                    spaceNums=spaceNums+1;
                }
            }
        }

        BookingRequesheader header=new BookingRequesheader();
        header.setOrderNo(appNo);
        header.setSpaceNums(spaceNums);
        header.setTotalCases(String.valueOf(totalCases));
        try {
            headerMapper.updateBookingRequesheader(header);
            WaybillHeader waybillHeader=new WaybillHeader();
            waybillHeader.setOrderNo(appNo);
            waybillHeader.setResveredField04(String.valueOf(spaceNums));
            waybillHeader.setTotalCases(String.valueOf(totalCases));
            waybillHeaderMapper.updateWaybillHeader(waybillHeader);
            SpaceOccupy occupy=new SpaceOccupy();
            occupy.setOrderNo(appNo);
            occupy.setSpaceNums(String.valueOf(spaceNums));
            spaceOccupyMapper.updateSpaceOccupy(occupy);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(e.getMessage()+"撤、换箱后更新仓位占用信息失败");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }

    }

    /**
     * 新增舱位占用表(用来统计还有多少舱位可以使用)
     *
     * @param spaceOccupy 舱位占用表(用来统计还有多少舱位可以使用)信息
     * @return 结果
     */
    @Override
    public int insertSpaceOccupy(SpaceOccupy spaceOccupy)
    {
        return spaceOccupyMapper.insertSpaceOccupy(spaceOccupy);
    }

    /**
     * 修改舱位占用表(用来统计还有多少舱位可以使用)
     *
     * @param spaceOccupy 舱位占用表(用来统计还有多少舱位可以使用)信息
     * @return 结果
     */
    @Override
    public int updateSpaceOccupy(SpaceOccupy spaceOccupy)
    {
        return spaceOccupyMapper.updateSpaceOccupy(spaceOccupy);
    }


    /**
     * 删除舱位占用表(用来统计还有多少舱位可以使用)
     *
     * @param rowId 舱位占用表(用来统计还有多少舱位可以使用)ID
     * @return 结果
     */
    @Override
    public int deleteSpaceOccupyById(String rowId)
    {
        return spaceOccupyMapper.deleteSpaceOccupyById( rowId);
    };


    /**
     * 批量删除舱位占用表(用来统计还有多少舱位可以使用)对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSpaceOccupyByIds(Integer[] rowIds)
    {
        return spaceOccupyMapper.deleteSpaceOccupyByIds( rowIds);
    }
}
