package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.QueryMap;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * @Description: 报表
 * @Author: 徐瑞
 * @Date: 2024-7-24 15:35:45
 */
public interface ReportFormService {
    /**
     * @Description: 班列统计表(省)
     * @Author: 徐瑞
     * @Date: 2024-7-24 15:35:45
     */
    Page provincialTrainStatisticsForm(QueryMap query);

    void provincialTrainStatisticsFormExport(Map<String, Object> params,HttpServletResponse response) throws Exception;

    /**
     * @Description: 班列统计表(市)
     * @Author: 徐瑞
     * @Date: 2024-7-24 15:35:45
     */
    Page cityTrainStatisticsForm(QueryMap query);

    void cityTrainStatisticsFormExport(Map<String, Object> params,HttpServletResponse response) throws Exception;
    /**
     * @Description: 发运明细表(省)
     * @Author: 徐瑞
     * @Date: 2024-7-24 15:35:45
     */
    Page provincialShippingDetailForm(QueryMap query);

    void provincialShippingDetailFormExport(Map<String, Object> params,HttpServletResponse response) throws Exception;

    /**
     * @Description: 发运明细表(市)
     * @Author: 徐瑞
     * @Date: 2024-7-24 15:35:45
     */
    Page cityShippingDetailForm(QueryMap query);

    void cityShippingDetailFormExport(Map<String, Object> params, HttpServletResponse response) throws Exception;

    Page billDetailForm(QueryMap query);

    void billDetailFormExport(QueryMap query, HttpServletResponse res) throws Exception;
}

