package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.TransactionAccount;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 订舱客户交易台账表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-14 09:52:53
 */
public interface TransactionAccountService extends IService<TransactionAccount> {
    /**
     * 查询订舱客户交易台账表信息
     *
     * @param rowId 订舱客户交易台账表ID
     * @return 订舱客户交易台账表信息
     */
    public TransactionAccount selectTransactionAccountById(String rowId);

    /**
     * 查询订舱客户交易台账表列表
     *
     * @param transactionAccount 订舱客户交易台账表信息
     * @return 订舱客户交易台账表集合
     */
    public List<TransactionAccount> selectTransactionAccountList(TransactionAccount transactionAccount);


    /**
     * 分页模糊查询订舱客户交易台账表列表
     * @return 订舱客户交易台账表集合
     */
    public Page selectTransactionAccountListByLike(Query query);



    /**
     * 新增订舱客户交易台账表
     *
     * @param transactionAccount 订舱客户交易台账表信息
     * @return 结果
     */
    public int insertTransactionAccount(TransactionAccount transactionAccount);

    /**
     * 修改订舱客户交易台账表
     *
     * @param transactionAccount 订舱客户交易台账表信息
     * @return 结果
     */
    public int updateTransactionAccount(TransactionAccount transactionAccount);

    public int updateTransactionAccountByNo(TransactionAccount transactionAccount);

    /**
     * 删除订舱客户交易台账表
     *
     * @param rowId 订舱客户交易台账表ID
     * @return 结果
     */
    public int deleteTransactionAccountById(String rowId);

    /**
     * 批量删除订舱客户交易台账表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTransactionAccountByIds(Integer[] rowIds);

}

