package com.huazheng.tunny.ocean.controller;

import com.huazheng.tunny.ocean.service.LedgerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/** 台账管理多联需求接口
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2024/6/24 16:03
 */
@RestController
@RequestMapping("/LedgerController")
@Slf4j
public class LedgerController {

    @Autowired
    private LedgerService ledgerService;


}
