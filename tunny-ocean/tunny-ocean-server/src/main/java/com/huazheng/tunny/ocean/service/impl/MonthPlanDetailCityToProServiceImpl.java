package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.MonthPlanDetailCityToProMapper;
import com.huazheng.tunny.ocean.api.entity.MonthPlanDetailCityToPro;
import com.huazheng.tunny.ocean.service.MonthPlanDetailCityToProService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("monthPlanDetailCityToProService")
public class MonthPlanDetailCityToProServiceImpl extends ServiceImpl<MonthPlanDetailCityToProMapper, MonthPlanDetailCityToPro> implements MonthPlanDetailCityToProService {

    @Autowired
    private MonthPlanDetailCityToProMapper monthPlanDetailCityToProMapper;

    public MonthPlanDetailCityToProMapper getMonthPlanDetailCityToProMapper() {
        return monthPlanDetailCityToProMapper;
    }

    public void setMonthPlanDetailCityToProMapper(MonthPlanDetailCityToProMapper monthPlanDetailCityToProMapper) {
        this.monthPlanDetailCityToProMapper = monthPlanDetailCityToProMapper;
    }

    /**
     * 查询月计划申请子表(市平台提交到省平台)信息
     *
     * @param rowId 月计划申请子表(市平台提交到省平台)ID
     * @return 月计划申请子表(市平台提交到省平台)信息
     */
    @Override
    public MonthPlanDetailCityToPro selectMonthPlanDetailCityToProById(String rowId)
    {
        return monthPlanDetailCityToProMapper.selectMonthPlanDetailCityToProById(rowId);
    }

    /**
     * 查询月计划申请子表(市平台提交到省平台)列表
     *
     * @param monthPlanDetailCityToPro 月计划申请子表(市平台提交到省平台)信息
     * @return 月计划申请子表(市平台提交到省平台)集合
     */
    @Override
    public List<MonthPlanDetailCityToPro> selectMonthPlanDetailCityToProList(MonthPlanDetailCityToPro monthPlanDetailCityToPro)
    {
        return monthPlanDetailCityToProMapper.selectMonthPlanDetailCityToProList(monthPlanDetailCityToPro);
    }


    /**
     * 分页模糊查询月计划申请子表(市平台提交到省平台)列表
     * @return 月计划申请子表(市平台提交到省平台)集合
     */
    @Override
    public Page selectMonthPlanDetailCityToProListByLike(Query query)
    {
        MonthPlanDetailCityToPro monthPlanDetailCityToPro =  BeanUtil.mapToBean(query.getCondition(), MonthPlanDetailCityToPro.class,false);
        List<MonthPlanDetailCityToPro> monthPlanDetailCityToPros = monthPlanDetailCityToProMapper.selectMonthPlanDetailCityToProListByLike(query, monthPlanDetailCityToPro);
        query.setRecords(monthPlanDetailCityToPros);
        query.setTotal(monthPlanDetailCityToProMapper.selectAllNo(monthPlanDetailCityToPro));
        return query;
    }

    /**
     * 新增月计划申请子表(市平台提交到省平台)
     *
     * @param monthPlanDetailCityToPro 月计划申请子表(市平台提交到省平台)信息
     * @return 结果
     */
    @Override
    public int insertMonthPlanDetailCityToPro(MonthPlanDetailCityToPro monthPlanDetailCityToPro)
    {
        return monthPlanDetailCityToProMapper.insertMonthPlanDetailCityToPro(monthPlanDetailCityToPro);
    }

    /**
     * 修改月计划申请子表(市平台提交到省平台)
     *
     * @param monthPlanDetailCityToPro 月计划申请子表(市平台提交到省平台)信息
     * @return 结果
     */
    @Override
    public int updateMonthPlanDetailCityToPro(MonthPlanDetailCityToPro monthPlanDetailCityToPro)
    {
        return monthPlanDetailCityToProMapper.updateMonthPlanDetailCityToPro(monthPlanDetailCityToPro);
    }

    @Override
    public int updateMonthPlanDetailCityToProByNo(MonthPlanDetailCityToPro monthPlanDetailCityToPro)
    {
        return monthPlanDetailCityToProMapper.updateMonthPlanDetailCityToProByNo(monthPlanDetailCityToPro);
    }
    /**
     * 删除月计划申请子表(市平台提交到省平台)
     *
     * @param monthPlanDetailCityToPro 月计划申请子表(市平台提交到省平台)ID
     * @return 结果
     */
    public int deleteMonthPlanDetailCityToProById(MonthPlanDetailCityToPro monthPlanDetailCityToPro)
    {
        return monthPlanDetailCityToProMapper.deleteMonthPlanDetailCityToProById( monthPlanDetailCityToPro);
    };


    /**
     * 批量删除月计划申请子表(市平台提交到省平台)对象
     *
     * @return 结果
     */
    @Override
    public int deleteMonthPlanDetailCityToProByIds(Integer[] rowIds)
    {
        return monthPlanDetailCityToProMapper.deleteMonthPlanDetailCityToProByIds( rowIds);
    }

    @Override
    public List<MonthPlanDetailCityToPro> selectListByMonth(MonthPlanDetailCityToPro monthPlanDetailCityToPro)
    {
        return monthPlanDetailCityToProMapper.selectListByMonth(monthPlanDetailCityToPro);
    }
}
