package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.FdInvoiceCheckHistory;

import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.util.List;

/**
 *  服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-07-23 17:42:58
 */
public interface FdInvoiceCheckHistoryService extends IService<FdInvoiceCheckHistory> {
    /**
     * 查询信息
     *
     * @param id ID
     * @return 信息
     */
    public FdInvoiceCheckHistory selectFdInvoiceCheckHistoryById(Integer id);

    /**
     * 查询列表
     *
     * @param fdInvoiceCheckHistory 信息
     * @return 集合
     */
    public List<FdInvoiceCheckHistory> selectFdInvoiceCheckHistoryList(FdInvoiceCheckHistory fdInvoiceCheckHistory);

    public List<FdInvoiceCheckHistory> selectFdInvoiceCheckHistoryListByLikeTwo(FdInvoiceCheckHistory fdInvoiceCheckHistory);


    /**
     * 分页模糊查询列表
     * @return 集合
     */
    public Page selectFdInvoiceCheckHistoryListByLike(Query query);



    /**
     * 新增
     *
     * @param fdInvoiceCheckHistory 信息
     * @return 结果
     */
    public int insertFdInvoiceCheckHistory(FdInvoiceCheckHistory fdInvoiceCheckHistory);

    /**
     * 修改
     *
     * @param fdInvoiceCheckHistory 信息
     * @return 结果
     */
    public int updateFdInvoiceCheckHistory(FdInvoiceCheckHistory fdInvoiceCheckHistory);

    /**
     * 删除
     *
     * @param id ID
     * @return 结果
     */
    public int deleteFdInvoiceCheckHistoryById(Integer id);

    /**
     * 批量删除
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdInvoiceCheckHistoryByIds(Integer[] ids);

    void historyExported(FdInvoiceCheckHistory fdInvoiceCheckHistory, HttpServletResponse response) throws Exception;
}

