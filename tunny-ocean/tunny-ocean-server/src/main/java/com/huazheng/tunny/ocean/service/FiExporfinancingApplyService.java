package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.FiExporfinancingApply;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;
import java.util.Map;

/**
 * 出口融资申请表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-04-25 13:56:10
 */
public interface FiExporfinancingApplyService extends IService<FiExporfinancingApply> {
    /**
     * 查询出口融资申请表信息
     *
     * @param rowId 出口融资申请表ID
     * @return 出口融资申请表信息
     */
    public FiExporfinancingApply selectFiExporfinancingApplyById(String rowId);

    public FiExporfinancingApply selectFiExporfinancingApplyByAssetCode(String assetCode);

    /**
     * 查询出口融资申请表列表
     *
     * @param fiExporfinancingApply 出口融资申请表信息
     * @return 出口融资申请表集合
     */
    public List<FiExporfinancingApply> selectFiExporfinancingApplyList(FiExporfinancingApply fiExporfinancingApply);

    public List<FiExporfinancingApply> selectFiExporfinancingApplyExportList(FiExporfinancingApply fiExporfinancingApply);


    /**
     * 分页模糊查询出口融资申请表列表
     * @return 出口融资申请表集合
     */
    public Page selectFiExporfinancingApplyListByLike(Query query);

    public Page selectListsPage(Query query);

//    public R selectByEntCode(Map<String, Object> params);

    public Page selectExitListByPage(Query query);

    public R selectListSum(Map<String, Object> params);

    public R selectExitListBySum(Map<String, Object> params);


    /**
     * 新增出口融资申请表
     *
     * @param fiExporfinancingApply 出口融资申请表信息
     * @return 结果
     */
    public int insertFiExporfinancingApply(FiExporfinancingApply fiExporfinancingApply);

    /**
     * 修改出口融资申请表
     *
     * @param fiExporfinancingApply 出口融资申请表信息
     * @return 结果
     */
    public int updateFiExporfinancingApply(FiExporfinancingApply fiExporfinancingApply);

    public int updateFiExporfinancingApplyByAssetCode(FiExporfinancingApply fiExporfinancingApply);

    /**
     * 删除出口融资申请表
     *
     * @param rowId 出口融资申请表ID
     * @return 结果
     */
    public int deleteFiExporfinancingApplyById(String rowId);

    /**
     * 批量删除出口融资申请表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiExporfinancingApplyByIds(Integer[] rowIds);

}

