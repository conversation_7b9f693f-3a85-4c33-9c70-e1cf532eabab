package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.BillSubDetailIncomeCityMapper;
import com.huazheng.tunny.ocean.api.entity.BillSubDetailIncomeCity;
import com.huazheng.tunny.ocean.service.BillSubDetailIncomeCityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("billSubDetailIncomeCityService")
public class BillSubDetailIncomeCityServiceImpl extends ServiceImpl<BillSubDetailIncomeCityMapper, BillSubDetailIncomeCity> implements BillSubDetailIncomeCityService {

    @Autowired
    private BillSubDetailIncomeCityMapper billSubDetailIncomeCityMapper;

    /**
     * 查询应收账单（市）子账单详情信息
     *
     * @param id 应收账单（市）子账单详情ID
     * @return 应收账单（市）子账单详情信息
     */
    @Override
    public BillSubDetailIncomeCity selectBillSubDetailIncomeCityById(Integer id)
    {
        return billSubDetailIncomeCityMapper.selectBillSubDetailIncomeCityById(id);
    }

    /**
     * 查询应收账单（市）子账单详情列表
     *
     * @param billSubDetailIncomeCity 应收账单（市）子账单详情信息
     * @return 应收账单（市）子账单详情集合
     */
    @Override
    public List<BillSubDetailIncomeCity> selectBillSubDetailIncomeCityList(BillSubDetailIncomeCity billSubDetailIncomeCity)
    {
        return billSubDetailIncomeCityMapper.selectBillSubDetailIncomeCityList(billSubDetailIncomeCity);
    }


    /**
     * 分页模糊查询应收账单（市）子账单详情列表
     * @return 应收账单（市）子账单详情集合
     */
    @Override
    public Page selectBillSubDetailIncomeCityListByLike(Query query)
    {
        BillSubDetailIncomeCity billSubDetailIncomeCity =  BeanUtil.mapToBean(query.getCondition(), BillSubDetailIncomeCity.class,false);
        query.setRecords(billSubDetailIncomeCityMapper.selectBillSubDetailIncomeCityListByLike(query,billSubDetailIncomeCity));
        return query;
    }

    /**
     * 新增应收账单（市）子账单详情
     *
     * @param billSubDetailIncomeCity 应收账单（市）子账单详情信息
     * @return 结果
     */
    @Override
    public int insertBillSubDetailIncomeCity(BillSubDetailIncomeCity billSubDetailIncomeCity)
    {
        return billSubDetailIncomeCityMapper.insertBillSubDetailIncomeCity(billSubDetailIncomeCity);
    }

    /**
     * 修改应收账单（市）子账单详情
     *
     * @param billSubDetailIncomeCity 应收账单（市）子账单详情信息
     * @return 结果
     */
    @Override
    public int updateBillSubDetailIncomeCity(BillSubDetailIncomeCity billSubDetailIncomeCity)
    {
        return billSubDetailIncomeCityMapper.updateBillSubDetailIncomeCity(billSubDetailIncomeCity);
    }


    /**
     * 删除应收账单（市）子账单详情
     *
     * @param id 应收账单（市）子账单详情ID
     * @return 结果
     */
    public int deleteBillSubDetailIncomeCityById(Integer id)
    {
        return billSubDetailIncomeCityMapper.deleteBillSubDetailIncomeCityById( id);
    };


    /**
     * 批量删除应收账单（市）子账单详情对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBillSubDetailIncomeCityByIds(Integer[] ids)
    {
        return billSubDetailIncomeCityMapper.deleteBillSubDetailIncomeCityByIds( ids);
    }

}
