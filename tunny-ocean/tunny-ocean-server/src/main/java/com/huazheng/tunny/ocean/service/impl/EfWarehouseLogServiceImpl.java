package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.EfWarehouseLogMapper;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseLog;
import com.huazheng.tunny.ocean.service.EfWarehouseLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("efWarehouseLogService")
public class EfWarehouseLogServiceImpl extends ServiceImpl<EfWarehouseLogMapper, EfWarehouseLog> implements EfWarehouseLogService {

    @Autowired
    private EfWarehouseLogMapper efWarehouseLogMapper;

    public EfWarehouseLogMapper getEfWarehouseLogMapper() {
        return efWarehouseLogMapper;
    }

    public void setEfWarehouseLogMapper(EfWarehouseLogMapper efWarehouseLogMapper) {
        this.efWarehouseLogMapper = efWarehouseLogMapper;
    }

    /**
     * 查询仓单质押业务记录信息
     *
     * @param rowId 仓单质押业务记录ID
     * @return 仓单质押业务记录信息
     */
    @Override
    public EfWarehouseLog selectEfWarehouseLogById(String rowId)
    {
        return efWarehouseLogMapper.selectEfWarehouseLogById(rowId);
    }

    /**
     * 查询仓单质押业务记录列表
     *
     * @param efWarehouseLog 仓单质押业务记录信息
     * @return 仓单质押业务记录集合
     */
    @Override
    public List<EfWarehouseLog> selectEfWarehouseLogList(EfWarehouseLog efWarehouseLog)
    {
        return efWarehouseLogMapper.selectEfWarehouseLogList(efWarehouseLog);
    }


    /**
     * 分页模糊查询仓单质押业务记录列表
     * @return 仓单质押业务记录集合
     */
    @Override
    public Page selectEfWarehouseLogListByLike(Query query)
    {
        EfWarehouseLog efWarehouseLog =  BeanUtil.mapToBean(query.getCondition(), EfWarehouseLog.class,false);
        query.setRecords(efWarehouseLogMapper.selectEfWarehouseLogListByLike(query,efWarehouseLog));
        return query;
    }

    /**
     * 新增仓单质押业务记录
     *
     * @param efWarehouseLog 仓单质押业务记录信息
     * @return 结果
     */
    @Override
    public int insertEfWarehouseLog(EfWarehouseLog efWarehouseLog)
    {
        return efWarehouseLogMapper.insertEfWarehouseLog(efWarehouseLog);
    }

    /**
     * 修改仓单质押业务记录
     *
     * @param efWarehouseLog 仓单质押业务记录信息
     * @return 结果
     */
    @Override
    public int updateEfWarehouseLog(EfWarehouseLog efWarehouseLog)
    {
        return efWarehouseLogMapper.updateEfWarehouseLog(efWarehouseLog);
    }


    /**
     * 删除仓单质押业务记录
     *
     * @param rowId 仓单质押业务记录ID
     * @return 结果
     */
    public int deleteEfWarehouseLogById(String rowId)
    {
        return efWarehouseLogMapper.deleteEfWarehouseLogById( rowId);
    };


    /**
     * 批量删除仓单质押业务记录对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfWarehouseLogByIds(Integer[] rowIds)
    {
        return efWarehouseLogMapper.deleteEfWarehouseLogByIds( rowIds);
    }

}
