package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.BillPayCustomerSub;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.vo.BillDealWithCityAndCostVO;

import java.util.List;

/**
 * 应收账单（市）子账单表 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-06-11 16:57:59
 */
public interface BillPayCustomerSubService extends IService<BillPayCustomerSub> {
    /**
     * 查询应收账单（市）子账单表信息
     *
     * @param id 应收账单（市）子账单表ID
     * @return 应收账单（市）子账单表信息
     */
    public BillPayCustomerSub selectBillSubIncomeCityById(Integer id);

    /**
     * 查询应收账单（市）子账单表列表
     *
     * @param billPayCustomerSub 应收账单（市）子账单表信息
     * @return 应收账单（市）子账单表集合
     */
    public List<BillPayCustomerSub> selectBillSubIncomeCityList(BillPayCustomerSub billPayCustomerSub);


    /**
     * 分页模糊查询应收账单（市）子账单表列表
     * @return 应收账单（市）子账单表集合
     */
    public Page selectBillSubIncomeCityListByLike(Query query);



    /**
     * 新增应收账单（市）子账单表
     *
     * @param billPayCustomerSub 应收账单（市）子账单表信息
     * @return 结果
     */
    public int insertBillSubIncomeCity(BillPayCustomerSub billPayCustomerSub);

    /**
     * 修改应收账单（市）子账单表
     *
     * @param billPayCustomerSub 应收账单（市）子账单表信息
     * @return 结果
     */
    public int updateBillSubIncomeCity(BillPayCustomerSub billPayCustomerSub);

    /**
     * 删除应收账单（市）子账单表
     *
     * @param id 应收账单（市）子账单表ID
     * @return 结果
     */
    public int deleteBillSubIncomeCityById(Integer id);

    /**
     * 批量删除应收账单（市）子账单表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteBillSubIncomeCityByIds(Integer[] ids);

    List<BillDealWithCityAndCostVO>  selectFdBillSubByBillNo(String billNo,String customerName,String platformCode);

    List<String> selectBillOfCostByBillCode(String billNo);
}

