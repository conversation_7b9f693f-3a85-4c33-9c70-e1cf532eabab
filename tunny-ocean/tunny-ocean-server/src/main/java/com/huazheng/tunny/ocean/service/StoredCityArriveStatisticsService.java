package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.StoredCityArriveStatistics;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 各地市到达国家车数统计表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-10-13 09:33:59
 */
public interface StoredCityArriveStatisticsService extends IService<StoredCityArriveStatistics> {
    /**
     * 查询各地市到达国家车数统计表信息
     *
     * @param rowId 各地市到达国家车数统计表ID
     * @return 各地市到达国家车数统计表信息
     */
    public StoredCityArriveStatistics selectStoredCityArriveStatisticsById(Integer rowId);

    /**
     * 查询各地市到达国家车数统计表列表
     *
     * @param storedCityArriveStatistics 各地市到达国家车数统计表信息
     * @return 各地市到达国家车数统计表集合
     */
    public List<StoredCityArriveStatistics> selectStoredCityArriveStatisticsList(StoredCityArriveStatistics storedCityArriveStatistics);


    /**
     * 分页模糊查询各地市到达国家车数统计表列表
     * @return 各地市到达国家车数统计表集合
     */
    public Page selectStoredCityArriveStatisticsListByLike(Query query);



    /**
     * 新增各地市到达国家车数统计表
     *
     * @param storedCityArriveStatistics 各地市到达国家车数统计表信息
     * @return 结果
     */
    public int insertStoredCityArriveStatistics(StoredCityArriveStatistics storedCityArriveStatistics);

    /**
     * 修改各地市到达国家车数统计表
     *
     * @param storedCityArriveStatistics 各地市到达国家车数统计表信息
     * @return 结果
     */
    public int updateStoredCityArriveStatistics(StoredCityArriveStatistics storedCityArriveStatistics);

    /**
     * 删除各地市到达国家车数统计表
     *
     * @param rowId 各地市到达国家车数统计表ID
     * @return 结果
     */
    public int deleteStoredCityArriveStatisticsById(Integer rowId);

    /**
     * 批量删除各地市到达国家车数统计表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteStoredCityArriveStatisticsByIds(Integer[] rowIds);

}

