package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.DaysBookingplanDetail;
import com.huazheng.tunny.ocean.mapper.DaysBookingplanDetailMapper;
import com.huazheng.tunny.ocean.mapper.DaysBookingplanHeaderMapper;
import com.huazheng.tunny.ocean.api.entity.DaysBookingplanHeader;
import com.huazheng.tunny.ocean.service.DaysBookingplanHeaderService;
import org.springframework.beans.factory.annotation.Autowired;
import com.huazheng.tunny.ocean.api.enums.SysEnum;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service("daysBookingplanHeaderService")
public class DaysBookingplanHeaderServiceImpl extends ServiceImpl<DaysBookingplanHeaderMapper, DaysBookingplanHeader> implements DaysBookingplanHeaderService {

    @Autowired
    private DaysBookingplanHeaderMapper daysBookingplanHeaderMapper;

    @Autowired
    private DaysBookingplanDetailMapper daysBookingplanDetailMapper;

    @Autowired
    private SysNoConfigServiceImpl sysNoConfigServiceImpl;

    /**
     * 查询旬/周计划申请表(订舱客户提交到市平台)信息
     *
     * @param rowId 旬/周计划申请表(订舱客户提交到市平台)ID
     * @return 旬/周计划申请表(订舱客户提交到市平台)信息
     */
    @Override
    public DaysBookingplanHeader selectDaysBookingplanHeaderById(String rowId)
    {
        return daysBookingplanHeaderMapper.selectDaysBookingplanHeaderById(rowId);
    }

    /**
     * 查询旬/周计划申请表(订舱客户提交到市平台)列表
     *
     * @param daysBookingplanHeader 旬/周计划申请表(订舱客户提交到市平台)信息
     * @return 旬/周计划申请表(订舱客户提交到市平台)集合
     */
    @Override
    public List<DaysBookingplanHeader> selectDaysBookingplanHeaderList(DaysBookingplanHeader daysBookingplanHeader)
    {
        return daysBookingplanHeaderMapper.selectDaysBookingplanHeaderList(daysBookingplanHeader);
    }


    /**
     * 订舱平台旬周计划查询（列表）
     */
    @Override
    public Page selectDaysBookingplanHeaderListByLike(Query query)
    {
        DaysBookingplanHeader daysBookingplanHeader =  BeanUtil.mapToBean(query.getCondition(), DaysBookingplanHeader.class,false);
        if(StrUtil.isEmpty(daysBookingplanHeader.getPlatformCode())){
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            daysBookingplanHeader.setCustomerCode(userInfo.getPlatformCode());
        }
//        Integer c= daysBookingplanHeaderMapper.queryCount(daysBookingplanHeader);
//        if(c!=null&&c!=0){
//            query.setTotal(c);
            query.setRecords(daysBookingplanHeaderMapper.selectDaysBookingplanHeaderListByLike(query, daysBookingplanHeader));
//        }
        return query;
    }

    @Override
    public Page selectDaysBookingplanHeaderListByLikeM(Query query) {
        DaysBookingplanHeader daysBookingplanHeader =  BeanUtil.mapToBean(query.getCondition(), DaysBookingplanHeader.class,false);
        if(StrUtil.isEmpty(daysBookingplanHeader.getPlatformCode())){
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            daysBookingplanHeader.setPlatformCode(userInfo.getPlatformCode());
        }
//        Integer c= daysBookingplanHeaderMapper.queryCount1(daysBookingplanHeader);
//        if(c!=null&&c!=0){
//            query.setTotal(c);
            query.setRecords(daysBookingplanHeaderMapper.selectDaysBookingplanHeaderListByLike1(query, daysBookingplanHeader));
//        }
        return query;
    }

    /**
     * 新增旬/周计划申请表(订舱客户提交到市平台)
     *
     * @param daysBookingplanHeader 旬/周计划申请表(订舱客户提交到市平台)信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R insertDaysBookingplanHeader(DaysBookingplanHeader daysBookingplanHeader)
    {
        if(daysBookingplanHeader.getDetails()==null){
            return new R(500,Boolean.FALSE,"请检查明细信息填写");
        }
        if(daysBookingplanHeader.getPlanStatus()==null|| "".equals(daysBookingplanHeader.getPlanStatus())){
            return new R(500,Boolean.FALSE,"请检查基本信息填写");
        }
        if(daysBookingplanHeader.getPlanStartdate()==null||daysBookingplanHeader.getPlanEnddate()==null){
            return new R(500,Boolean.FALSE,"请检查基本信息填写");
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        Integer count=0;
        if(daysBookingplanHeader.getPlanCode()!=null&&!"".equals(daysBookingplanHeader.getPlanCode())){
            //有计划编码则先对原有旧的明细信息进行删除（delete_flag非"Y"数据），再对主数据和明细信息进行新增
            try {
                daysBookingplanDetailMapper.deleteDaysBookingplanDetailByPlanCode(daysBookingplanHeader.getPlanCode());
                for (DaysBookingplanDetail details : daysBookingplanHeader.getDetails()) {
                    details.setPlanCode(daysBookingplanHeader.getPlanCode());
                    details.setAddWho(usercode);
                    details.setAddWhoName(username);
                    details.setAddTime(LocalDateTime.now());
                    count=count+Integer.parseInt(details.getVehiclesNum());//计算总车数
                }
                daysBookingplanHeader.setUpdateTime(LocalDateTime.now());
                daysBookingplanHeader.setUpdateWho(usercode);
                daysBookingplanHeader.setUpdateWhoName(username);
                daysBookingplanHeader.setSum(String.valueOf(count));
                List<DaysBookingplanHeader> listTemp= new ArrayList<>();
                listTemp.add(daysBookingplanHeader);
                daysBookingplanHeaderMapper.updateDaysBookingplanHeader(listTemp);
                daysBookingplanDetailMapper.insertDaysBookingplanDetails(daysBookingplanHeader.getDetails());
            } catch (Exception e) {
                System.out.println(e.getMessage() + "@@@@@@更新班次明细失败！@@@@@@");
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return new R(500,Boolean.FALSE,"更新班次明细失败！");
            }
            return new R(200,Boolean.TRUE,"更新班次明细成功");
        }else{
            //有计划编码则直接对主数据和明细数据进行新增
            String appNo=sysNoConfigServiceImpl.genNo("JH");
            if (appNo.contains("，")) {
                return new R(500, Boolean.FALSE, appNo);
            }
            for (DaysBookingplanDetail details : daysBookingplanHeader.getDetails()) {
                details.setPlanCode(appNo);
                details.setAddWho(usercode);
                details.setAddWhoName(username);
                details.setAddTime(LocalDateTime.now());
                count=count+Integer.parseInt(details.getVehiclesNum());//计算总车数
            }
            try {
                daysBookingplanHeader.setAddTime(LocalDateTime.now());
                daysBookingplanHeader.setAddWho(usercode);
                daysBookingplanHeader.setAddWhoName(username);
                daysBookingplanHeader.setPlanCode(appNo);
                daysBookingplanHeader.setSum(String.valueOf(count));
                daysBookingplanHeader.setState(SysEnum.CUSTOMER_PLAN_STATUS_WAIT_COMMIT.getKey());
                daysBookingplanHeaderMapper.insertDaysBookingplanHeader(daysBookingplanHeader);
                daysBookingplanDetailMapper.insertDaysBookingplanDetails(daysBookingplanHeader.getDetails());
            } catch (Exception e) {
                System.out.println(e.getMessage() + "@@@@@@旬/周计划提交失败！@@@@@@");
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return new R(500,Boolean.FALSE,"计划提交失败！");
            }
            return new R(200,Boolean.TRUE,"计划保存成功！",appNo);
        }

    }

    /**
     * 删除旬/周计划申请表(订舱客户提交到市平台)
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateDaysBookingplanHeader(String[] planCode)
    {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        if(planCode.length==0){
            return new R(Boolean.FALSE,"至少选中一条计划");
        }
        List<DaysBookingplanHeader> list=new ArrayList<>();  /*组装主表list*/
        List<DaysBookingplanDetail> listDetail=new ArrayList<>(); /*组装子表list*/
        for (int i=0;i<planCode.length;i++){
            DaysBookingplanHeader daysBookingplanHeader=new DaysBookingplanHeader();
            daysBookingplanHeader.setPlanCode(planCode[i]);
            daysBookingplanHeader.setDeleteWho(usercode);
            daysBookingplanHeader.setDeleteWhoName(userInfo.getUserName());
            daysBookingplanHeader.setDeleteTime(LocalDateTime.now());
            daysBookingplanHeader.setDeleteFlag("Y");
            list.add(daysBookingplanHeader);
            DaysBookingplanDetail dbd = new DaysBookingplanDetail();
            dbd.setDeleteWho(usercode);
            dbd.setDeleteWhoName(username);
            dbd.setDeleteTime(LocalDateTime.now());
            dbd.setDeleteFlag("Y");
            dbd.setPlanCode(daysBookingplanHeader.getPlanCode());
            listDetail.add(dbd);
        }
        try {
            daysBookingplanHeaderMapper.updateDaysBookingplanHeader(list);
            daysBookingplanDetailMapper.updateDaysBookingplanDetail(listDetail);
        } catch (Exception e) {
            System.out.println(e.getMessage() + "删除旬/周计划失败！！！");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new R(500,Boolean.FALSE, "删除计划失败");
        }
        return new R(200,Boolean.TRUE,"删除计划成功");
    }

    @Override
    public int commitStatus(String planCode) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        DaysBookingplanHeader d=new DaysBookingplanHeader();
        d.setShippingCode(usercode);
        d.setShippingTime(LocalDateTime.now());
        d.setPlanCode(planCode);
        return daysBookingplanHeaderMapper.commitStatus(d);
    }


    /**
     * 删除旬/周计划申请表(订舱客户提交到市平台)
     *
     * @param rowId 旬/周计划申请表(订舱客户提交到市平台)ID
     * @return 结果
     */
    @Override
    public int deleteDaysBookingplanHeaderById(String rowId)
    {
        return daysBookingplanHeaderMapper.deleteDaysBookingplanHeaderById(rowId);
    }


    /**
     * 批量删除旬/周计划申请表(订舱客户提交到市平台)对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteDaysBookingplanHeaderByIds(Integer[] rowIds)
    {
        return daysBookingplanHeaderMapper.deleteDaysBookingplanHeaderByIds( rowIds);
    }

}
