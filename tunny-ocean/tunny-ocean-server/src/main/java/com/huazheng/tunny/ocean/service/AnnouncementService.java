package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.Announcement;

import java.util.List;

/**
 * @Description: Service
 * @Author: wx
 * @Date: 2023-05-08 15:12:44
 */
public interface AnnouncementService extends IService<Announcement> {

    /**
     * @Description: 分页
     * @Param: query
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-08 15:12:44
     */
    Page page(Query query);

    /**
     * @Description: 详情
     * @Param: id
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-08 15:12:44
     */
    R<Announcement> info(Integer id);

    /**
     * @Description: 保存
     * @Param: param
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-08 15:12:44
     */
    R save(Announcement param);

    /**
     * @Description: 修改
     * @Param: param
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-08 15:12:44
     */
    R update(Announcement param);

    /**
     * @Description: 删除
     * @Param: id
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-08 15:12:44
     */
    R delete(Integer id);

    /**
     * @Description: 列表
     * @Param: param
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-08 15:12:44
     */
    R<List<Announcement>> list(Announcement param);

    R switchType(Announcement param);

}

