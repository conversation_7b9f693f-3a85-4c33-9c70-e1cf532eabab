package com.huazheng.tunny.ocean.controller.eabillmain;

import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.eabillmain.EaBillMainDTO;
import com.huazheng.tunny.ocean.api.entity.BillBalanceMainCity;
import com.huazheng.tunny.ocean.api.entity.eabillmain.EaBillMain;
import com.huazheng.tunny.ocean.service.eabillmain.EaBillMainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 账单主表
 *
 * <AUTHOR>
 * @date 2025-06-30 18:35:54
 */
@Slf4j
@RestController
@RequestMapping("/eaBillMain")
public class EaBillMainController {

    @Autowired
    private EaBillMainService eaBillMainService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  eaBillMainService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return eaBillMainService.selectEaBillMainListByLike(new Query<>(params));
    }

    /**
     * 信息
     * @param billId
     * @return R
     */
    @GetMapping("/{billId}")
    public R info(@PathVariable("billId") Long billId) {
        EaBillMainDTO eaBillMain =eaBillMainService.selectEaBillMainById(billId);
        return new R<>(eaBillMain);
    }

    /**
     * 保存
     * @param eaBillMain
     * @return R
     */
    @PostMapping
    public R save(@RequestBody EaBillMain eaBillMain) {
        eaBillMainService.insert(eaBillMain);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 生成账单(一次)
     *
     * @param eaBillMain 生成逻辑参数
     * @return 结果
     */
    @PostMapping("/generateBill")
    public R generateBill(@RequestBody EaBillMain eaBillMain) {
        return eaBillMainService.generateBill(eaBillMain);
    }

    /**
     * 生成二次账单
     *
     * @param eaBillMain 生成逻辑参数
     * @return 结果
     */
    @PostMapping("/generateBillTwo")
    public R generateBillTwo(@RequestBody EaBillMain eaBillMain) {
        return eaBillMainService.generateBillTwo(eaBillMain);
    }

    /**
     * 删除账单
     *
     * @param eaBillMain 生成逻辑参数
     * @return 结果
     */
    @PostMapping("/delBill")
    public R delBill(@RequestBody EaBillMain eaBillMain) {
        return eaBillMainService.delBill(eaBillMain);
    }

    /**
     * 修改
     * @param eaBillMain
     * @return R
     */
    @PutMapping
    public R update(@RequestBody EaBillMain eaBillMain) {
        eaBillMainService.updateById(eaBillMain);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 结算
     * @param eaBillMain
     * @return R
     */
    @PostMapping("/settlement")
    public R settlement(@RequestBody EaBillMain eaBillMain) {
        return eaBillMainService.settlement(eaBillMain);
    }

    /**
     * 撤销结算
     * @param eaBillMain
     * @return R
     */
    @PostMapping("/revokeSettlement")
    public R revokeSettlement(@RequestBody EaBillMain eaBillMain) {
        return eaBillMainService.revokeSettlement(eaBillMain);
    }

    /**
     * 删除
     * @param billId
     * @return R
     */
    @DeleteMapping("/{billId}")
    public R delete(@PathVariable  Long billId) {
        eaBillMainService.deleteById(billId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param billIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<Long> billIds) {
        eaBillMainService.deleteBatchIds(billIds);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 导出运费核算表（市平台）
     * @Param: eaBillMain, response
     * @Return: com.huazheng.tunny.common.core.util.R
     * @Author: zhaohr
     * @Date: 2024/08/15 15:10
     **/
    @PostMapping("/exportFreightAccountCity")
    public R exportFreightAccountCity(@RequestBody EaBillMain eaBillMain, HttpServletResponse response){
        return eaBillMainService.exportFreightAccountCity(eaBillMain,response);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<EaBillMain> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = eaBillMainService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<EaBillMain> list = reader.readAll(EaBillMain.class);
        eaBillMainService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }
}
