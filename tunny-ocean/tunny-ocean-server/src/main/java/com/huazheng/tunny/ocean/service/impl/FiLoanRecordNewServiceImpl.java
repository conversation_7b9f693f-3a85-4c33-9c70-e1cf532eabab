package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.util.StringUtils;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.CustomerInfo;
import com.huazheng.tunny.ocean.api.entity.CustomerPlatformInfo;
import com.huazheng.tunny.ocean.api.entity.OperationLog;
import com.huazheng.tunny.ocean.mapper.FiLoanRecordNewMapper;
import com.huazheng.tunny.ocean.api.entity.FiLoanRecordNew;
import com.huazheng.tunny.ocean.service.CustomerPlatformInfoService;
import com.huazheng.tunny.ocean.service.FiLoanRecordNewService;
import com.huazheng.tunny.ocean.service.OperationLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import java.util.UUID;

@Service("fiLoanRecordNewService")
public class FiLoanRecordNewServiceImpl extends ServiceImpl<FiLoanRecordNewMapper, FiLoanRecordNew> implements FiLoanRecordNewService {

    @Autowired
    private FiLoanRecordNewMapper fiLoanRecordNewMapper;

    @Autowired
    private OperationLogService operationLogService;

    @Autowired
    private CustomerPlatformInfoService customerPlatformInfoService;
    /**
     * 查询企业信用贷-贷款记录（新建）信息
     *
     * @param rowId 企业信用贷-贷款记录（新建）ID
     * @return 企业信用贷-贷款记录（新建）信息
     */
    @Override
    public FiLoanRecordNew selectFiLoanRecordNewById(String rowId)
    {
        return fiLoanRecordNewMapper.selectFiLoanRecordNewById(rowId);
    }

    /**
     * 查询企业信用贷-贷款记录（新建）列表
     *
     * @param fiLoanRecordNew 企业信用贷-贷款记录（新建）信息
     * @return 企业信用贷-贷款记录（新建）集合
     */
    @Override
    public List<FiLoanRecordNew> selectFiLoanRecordNewList(FiLoanRecordNew fiLoanRecordNew)
    {
        return fiLoanRecordNewMapper.selectFiLoanRecordNewList(fiLoanRecordNew);
    }

    @Override
    public List<FiLoanRecordNew> selectFiLoanRecordNewExportList(FiLoanRecordNew fiLoanRecordNew)
    {
        if(fiLoanRecordNew.getPlatformLevel().equals("0") || fiLoanRecordNew.getPlatformLevel().equals("2")){
            CustomerPlatformInfo customerPlatformInfo=new CustomerPlatformInfo();
            if(fiLoanRecordNew.getPlatformLevel().equals("0")) {
                customerPlatformInfo.setPlatformCode(fiLoanRecordNew.getPlatformCode());
            }else if(fiLoanRecordNew.getPlatformLevel().equals("2")){
                customerPlatformInfo.setCustomerCode(fiLoanRecordNew.getPlatformCode());
            }
            List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoService.selectCustomerNoByCode(customerPlatformInfo);
            //查询社会信用代码为空的话返回为空
            if(customerPlatformInfos.size()>0){
                StringBuilder stringBuilder=new StringBuilder();
                for (CustomerPlatformInfo customerPlatform:customerPlatformInfos) {
                    stringBuilder.append(customerPlatform.getSocialUcCode()).append(",");
                }
                fiLoanRecordNew.setSocialCode(stringBuilder.toString());
            }else{
                return null;
            }
        }
        return fiLoanRecordNewMapper.selectFiLoanRecordNewExportList(fiLoanRecordNew);
    }


    /**
     * 分页模糊查询企业信用贷-贷款记录（新建）列表
     * @return 企业信用贷-贷款记录（新建）集合
     */
    @Override
    public Page selectFiLoanRecordNewListByLike(Query query)
    {
        FiLoanRecordNew fiLoanRecordNew =  BeanUtil.mapToBean(query.getCondition(), FiLoanRecordNew.class,false);
        if(fiLoanRecordNew.getPlatformLevel().equals("0") || fiLoanRecordNew.getPlatformLevel().equals("2")){
            CustomerPlatformInfo customerPlatformInfo=new CustomerPlatformInfo();
            if(fiLoanRecordNew.getPlatformLevel().equals("0")) {
                customerPlatformInfo.setPlatformCode(fiLoanRecordNew.getPlatformCode());
            }else if(fiLoanRecordNew.getPlatformLevel().equals("2")){
                customerPlatformInfo.setCustomerCode(fiLoanRecordNew.getPlatformCode());
            }
            List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoService.selectCustomerNoByCode(customerPlatformInfo);
            //查询社会信用代码为空的话返回为空
            if(customerPlatformInfos.size()>0){
                StringBuilder stringBuilder=new StringBuilder();
                for (CustomerPlatformInfo customerPlatform:customerPlatformInfos) {
                    stringBuilder.append(customerPlatform.getSocialUcCode()).append(",");
                }
                fiLoanRecordNew.setSocialCode(stringBuilder.toString());
            }else{
                return query;
            }
        }
//        if(fiLoanRecordNew !=null ){
//            if(StringUtils.isNotBlank(fiLoanRecordNew.getAmountEnd())){
//                fiLoanRecordNew.setAmountEnd(BigDecimal.valueOf(fiLoanRecordNew.getAmountEnd()).multiply(BigDecimal.valueOf(100)).setScale(2,BigDecimal.ROUND_HALF_UP).toString());
//            }
//            if(StringUtils.isNotBlank(fiLoanRecordNew.getAmountStart())){
//                fiLoanRecordNew.setAmountStart(BigDecimal.valueOf(fiLoanRecordNew.getAmountStart()).multiply(BigDecimal.valueOf(100)).setScale(2,BigDecimal.ROUND_HALF_UP).toString());
//            }
//        }
        List<FiLoanRecordNew> fiLoanRecordNews = fiLoanRecordNewMapper.selectFiLoanRecordNewListByLike(query, fiLoanRecordNew);
//        for (FiLoanRecordNew fiLoanRecordNew1:fiLoanRecordNews) {
//            if(fiLoanRecordNew1.getAmount().compareTo(BigDecimal.valueOf(0))==1) {
//                fiLoanRecordNew1.setAmount(fiLoanRecordNew1.getAmount().divide(BigDecimal.valueOf(100)).setScale(2,BigDecimal.ROUND_HALF_UP));
//            }
//        }
        query.setRecords(fiLoanRecordNews);
        return query;
    }

    @Override
    public R statisticsSum(Map<String, Object> params) {
        R r=new R();
        FiLoanRecordNew fiLoanRecordNew =  BeanUtil.mapToBean(params, FiLoanRecordNew.class,false);
        if(fiLoanRecordNew.getPlatformLevel().equals("0") || fiLoanRecordNew.getPlatformLevel().equals("2")){
            if("".equals(fiLoanRecordNew.getPlatformCode()) && fiLoanRecordNew.getPlatformCode() ==null){
                r.setCode(500);
                r.setB(false);
                r.setMsg("请传入当前登录人的账号");
                return r;
            }
            CustomerPlatformInfo customerPlatformInfo=new CustomerPlatformInfo();
            if(fiLoanRecordNew.getPlatformLevel().equals("0")) {
                customerPlatformInfo.setPlatformCode(fiLoanRecordNew.getPlatformCode());
            }else if(fiLoanRecordNew.getPlatformLevel().equals("2")){
                customerPlatformInfo.setCustomerCode(fiLoanRecordNew.getPlatformCode());
            }
            List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoService.selectCustomerNoByCode(customerPlatformInfo);
            //查询社会信用代码为空的话返回为空
            if(customerPlatformInfos.size()>0){
                StringBuilder stringBuilder=new StringBuilder();
                for (CustomerPlatformInfo customerPlatform:customerPlatformInfos) {
                    stringBuilder.append(customerPlatform.getSocialUcCode()).append(",");
                }
                fiLoanRecordNew.setSocialCode(stringBuilder.toString());
            }else{
                return r;
            }
        }
        List<FiLoanRecordNew> fiLoanRecordNews = fiLoanRecordNewMapper.selectEnterpriseStatistics(fiLoanRecordNew);
        //查询币种还款信息
//        fiLoanRecordNew.setStatePayAndOutstanding("PAY");
        List<FiLoanRecordNew> paySum = fiLoanRecordNewMapper.statisticsSum(fiLoanRecordNew);
        FiLoanRecordNew paySumZm = fiLoanRecordNewMapper.selectStatisticsPayAndRec(fiLoanRecordNew);
        //查询币种未还款信息
        fiLoanRecordNew.setStatePayAndOutstanding("OUTSTANDING,PAY");
        List<FiLoanRecordNew> outstandtingSum = fiLoanRecordNewMapper.statisticsSum(fiLoanRecordNew);
        FiLoanRecordNew recPaySumZm = fiLoanRecordNewMapper.selectStatisticsPayAndRec(fiLoanRecordNew);
        Map<String,Object> map=new HashMap<>();
        map.put("paySum",paySum);
        map.put("outstandtingSum",outstandtingSum);
        map.put("enterprise",fiLoanRecordNews.size());
        map.put("paySumZm",paySumZm.getResveredField02());
        map.put("recPaySumZm",recPaySumZm.getResveredField02());
        r.setData(map);
        r.setB(true);
        r.setCode(200);
        return r;
    }

    /**
     * 新增企业信用贷-贷款记录（新建）
     *
     * @param fiLoanRecordNew 企业信用贷-贷款记录（新建）信息
     * @return 结果
     */
    @Override
    public int insertFiLoanRecordNew(FiLoanRecordNew fiLoanRecordNew)
    {
        return fiLoanRecordNewMapper.insertFiLoanRecordNew(fiLoanRecordNew);
    }

    /**
     * 修改企业信用贷-贷款记录（新建）
     *
     * @param fiLoanRecordNew 企业信用贷-贷款记录（新建）信息
     * @return 结果
     */
    @Override
    public int updateFiLoanRecordNew(FiLoanRecordNew fiLoanRecordNew)
    {
        return fiLoanRecordNewMapper.updateFiLoanRecordNew(fiLoanRecordNew);
    }


    /**
     * 删除企业信用贷-贷款记录（新建）
     *
     * @param rowId 企业信用贷-贷款记录（新建）ID
     * @return 结果
     */
    public int deleteFiLoanRecordNewById(String rowId)
    {
        return fiLoanRecordNewMapper.deleteFiLoanRecordNewById( rowId);
    };


    /**
     * 批量删除企业信用贷-贷款记录（新建）对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiLoanRecordNewByIds(Integer[] rowIds)
    {
        return fiLoanRecordNewMapper.deleteFiLoanRecordNewByIds( rowIds);
    }

    @Override
    public R addorUpdateFiloanrecordnew(FiLoanRecordNew fiLoanRecordNew) {
        R r=new R();
        FiLoanRecordNew selectfiLoanRecordNew=new FiLoanRecordNew();
        selectfiLoanRecordNew.setDeleteFlag("N");
        selectfiLoanRecordNew.setAssetCode(fiLoanRecordNew.getAssetCode());
        List<FiLoanRecordNew> fiLoanRecordNews = fiLoanRecordNewMapper.selectFiLoanRecordNewListByLike(selectfiLoanRecordNew);
        if(StringUtils.isNotBlank(fiLoanRecordNew.getPayAmountDollar())) {
            fiLoanRecordNew.setResveredField02(BigDecimal.valueOf(Double.parseDouble(fiLoanRecordNew.getPayAmountDollar())).divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            fiLoanRecordNew.setResveredField04(fiLoanRecordNew.getPayAmountDollar());
        }
        if(StringUtils.isNotBlank(fiLoanRecordNew.getRepayAmountDollar())) {
            fiLoanRecordNew.setResveredField03(BigDecimal.valueOf(Double.parseDouble(fiLoanRecordNew.getRepayAmountDollar())).divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            fiLoanRecordNew.setResveredField05(fiLoanRecordNew.getRepayAmountDollar());
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        //插入操作日志
        OperationLog log = new OperationLog();
        log.setUuid(UUID.randomUUID().toString());
        log.setProcessCode(fiLoanRecordNew.getAssetCode());
        if(fiLoanRecordNews.size()>0){
            if(fiLoanRecordNew.getAmount().compareTo(BigDecimal.valueOf(0))==1) {
                fiLoanRecordNew.setResveredField01(fiLoanRecordNew.getAmount().toString());
                fiLoanRecordNew.setAmount(fiLoanRecordNew.getAmount().divide(BigDecimal.valueOf(100).setScale(2, BigDecimal.ROUND_HALF_UP)));
            }
            fiLoanRecordNew.setUpdateTime(LocalDateTime.now());
            fiLoanRecordNew.setUpdateWho("zc");
            fiLoanRecordNew.setUpdateWhoName("中钞");
            fiLoanRecordNewMapper.updateFiLoanRecordNew(fiLoanRecordNew);
            log.setProcessType("中超-贷款记录-修改");
            log.setOperationResult("修改完成");
            r.setMsg("修改成功");
            r.setB(Boolean.TRUE);
            r.setCode(200);
        }else{
            if(fiLoanRecordNew.getAmount().compareTo(BigDecimal.valueOf(0))==1) {
                fiLoanRecordNew.setResveredField01(fiLoanRecordNew.getAmount().toString());
                fiLoanRecordNew.setAmount(fiLoanRecordNew.getAmount().divide(BigDecimal.valueOf(100).setScale(2, BigDecimal.ROUND_HALF_UP)));
            }
            fiLoanRecordNew.setAddTime(LocalDateTime.now());
            fiLoanRecordNew.setAddWho("zc");
            fiLoanRecordNew.setAddWhoName("中钞");
            fiLoanRecordNew.setRowId(UUID.randomUUID().toString());
            fiLoanRecordNew.setDeleteFlag("N");
            fiLoanRecordNewMapper.insertFiLoanRecordNew(fiLoanRecordNew);
            log.setProcessType("中超-贷款记录-新增");
            log.setOperationResult("新增完成");
            r.setMsg("新增成功");
            r.setB(Boolean.TRUE);
            r.setCode(200);
        }
        log.setOperationCode("zc");
        log.setOperationName("中钞");
        log.setOperationTime(new Date());
        log.setOperationOpinion(JSONUtil.toJsonStr(fiLoanRecordNew));
        log.setCorInterface("/filoanrecordnew/addorUpdateFiloanrecordnew");
        log.setDeleteFlag("N");
        operationLogService.insertOperationLog(log);
        return r;
    }

}
