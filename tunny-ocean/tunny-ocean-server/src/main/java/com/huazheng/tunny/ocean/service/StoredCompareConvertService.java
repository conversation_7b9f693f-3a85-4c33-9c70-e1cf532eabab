package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.StoredCompareConvert;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 省平台-数据同比（进出口、过境、回程） 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-10-11 13:20:13
 */
public interface StoredCompareConvertService extends IService<StoredCompareConvert> {
    /**
     * 查询省平台-数据同比（进出口、过境、回程）信息
     *
     * @param rowId 省平台-数据同比（进出口、过境、回程）ID
     * @return 省平台-数据同比（进出口、过境、回程）信息
     */
    public StoredCompareConvert selectStoredCompareConvertById(Integer rowId);

    /**
     * 查询省平台-数据同比（进出口、过境、回程）列表
     *
     * @param storedCompareConvert 省平台-数据同比（进出口、过境、回程）信息
     * @return 省平台-数据同比（进出口、过境、回程）集合
     */
    public List<StoredCompareConvert> selectStoredCompareConvertList(StoredCompareConvert storedCompareConvert);


    /**
     * 分页模糊查询省平台-数据同比（进出口、过境、回程）列表
     * @return 省平台-数据同比（进出口、过境、回程）集合
     */
    public Page selectStoredCompareConvertListByLike(Query query);



    /**
     * 新增省平台-数据同比（进出口、过境、回程）
     *
     * @param storedCompareConvert 省平台-数据同比（进出口、过境、回程）信息
     * @return 结果
     */
    public int insertStoredCompareConvert(StoredCompareConvert storedCompareConvert);

    /**
     * 修改省平台-数据同比（进出口、过境、回程）
     *
     * @param storedCompareConvert 省平台-数据同比（进出口、过境、回程）信息
     * @return 结果
     */
    public int updateStoredCompareConvert(StoredCompareConvert storedCompareConvert);

    /**
     * 删除省平台-数据同比（进出口、过境、回程）
     *
     * @param rowId 省平台-数据同比（进出口、过境、回程）ID
     * @return 结果
     */
    public int deleteStoredCompareConvertById(Integer rowId);

    /**
     * 批量删除省平台-数据同比（进出口、过境、回程）
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteStoredCompareConvertByIds(Integer[] rowIds);

}

