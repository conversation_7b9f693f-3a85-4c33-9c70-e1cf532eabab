package com.huazheng.tunny.ocean.controller;



import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.FdCosdetail;
import com.huazheng.tunny.ocean.api.entity.FdCost;
import com.huazheng.tunny.ocean.api.entity.FdShippingAccount;
import com.huazheng.tunny.ocean.api.entity.WaybillHeader;
import com.huazheng.tunny.ocean.api.vo.FdBillVO;
import com.huazheng.tunny.ocean.api.vo.FdCostVO;
import com.huazheng.tunny.ocean.service.*;
import com.huazheng.tunny.ocean.util.spiltlistutil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.parameters.P;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import java.util.UUID;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * 运单费用汇总表
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:30:28
 */
@RestController
@RequestMapping("/fdcost")
@Slf4j
public class FdCostController {
    @Autowired
    private FdCostService fdCostService;
    @Autowired
    private FdCosdetailService fdCosdetailService;

    @Autowired
    private SysNoConfigService sysNoConfigService;
    @Autowired
    private FdShippingAccountService fdShippingAccountService;
    @Autowired
    private WaybillHeaderService waybillHeaderService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdCostService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询

        return fdCostService.selectFdCostListByLike(new Query<>(params));
    }

    @GetMapping("/savecostbychangebox")
    public R savecostbychangebox(@RequestParam String string) {

        //数据库字段值完整查询
        // return  fdCostService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询

        Boolean B = fdCostService.savecostbychangebox(string);
        return null;
    }


    /**
     * 列表
     *
     * @param fdCosdetail
     * @return
     */
    @PostMapping("/getcostdetailbyexchangerate")
    public List getcostdetailbyexchangerate(@RequestBody FdCosdetail fdCosdetail) {
        //数据库字段值完整查询
        // return  fdCostService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        BigDecimal newexchangeRate = fdCosdetail.getExchangeRate();
        List<FdCosdetail> fdCosdetails = fdCosdetailService.selectFdCosdetailList(fdCosdetail);
        BigDecimal oldexchangeRate = fdCosdetails.get(0).getExchangeRate();
        //计算汇率差
        for (FdCosdetail cosdetail : fdCosdetails) {
            BigDecimal subtract = newexchangeRate.multiply(cosdetail.getOriginalCurrencyAmount()).subtract(oldexchangeRate.multiply(cosdetail.getOriginalCurrencyAmount()));
            cosdetail.setLocalCurrencyAmount(subtract);
            cosdetail.setExchangeRate(newexchangeRate);
            cosdetail.setUuid(UUID.randomUUID().toString());
            cosdetail.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
            cosdetail.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
            cosdetail.setCreateTime(null);
            cosdetail.setUpdateTime(null);
            cosdetail.setCodeBbCategoriesName("发运运费");
            cosdetail.setCodeBbCategoriesCode("f_fee_type");
            cosdetail.setCodeSsCategoriesName("汇差费用");
            cosdetail.setCodeSsCategoriesCode("f_erd_fee");
        }
        fdCosdetailService.insertBatch(fdCosdetails);

        return null;
    }

    @PostMapping("/getcostdetaillistbyexchangerate")
    public List getcostdetaillistbyexchangerate(@RequestBody FdCosdetail fdCosdetail) {
        //数据库字段值完整查询
        // return  fdCostService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        BigDecimal newexchangeRate = fdCosdetail.getExchangeRate();
        List<FdCosdetail> fdCosdetails = fdCosdetailService.selectFdCosdetailListByIdList(fdCosdetail);
        //计算汇率差
        for (FdCosdetail cosdetail : fdCosdetails) {
            BigDecimal oldexchangeRate = cosdetail.getExchangeRate();
            BigDecimal subtract = newexchangeRate.multiply(cosdetail.getOriginalCurrencyAmount()).subtract(oldexchangeRate.multiply(cosdetail.getOriginalCurrencyAmount()));
            cosdetail.setLocalCurrencyAmount(subtract);
            cosdetail.setExchangeRate(newexchangeRate);
            cosdetail.setUuid(UUID.randomUUID().toString());
            cosdetail.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
            cosdetail.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
            cosdetail.setCreateTime(null);
            cosdetail.setUpdateTime(null);
            cosdetail.setBillCode(null);
            cosdetail.setBillGenerate("0");
            cosdetail.setCodeBbCategoriesName("发运运费");
            cosdetail.setCodeBbCategoriesCode("f_fee_type");
            cosdetail.setCodeSsCategoriesName("汇差费用");
            cosdetail.setCodeSsCategoriesCode("f_erd_fee");
        }
        fdCosdetailService.insertBatch(fdCosdetails);

        return null;
    }

    @GetMapping("/selectProvinceTrainsNumber")
    public List<FdCost> selectProvinceTrainsNumber(@RequestParam Map<String,Object> param){
        FdCost fdCost = BeanUtil.mapToBean(param, FdCost.class, false);
        return fdCostService.selectProvinceTrainsNumber(fdCost);
    }

    @GetMapping("/selectFdCostListAndAreaCode")
    public List<FdCost> selectFdCostListAndAreaCode(@RequestParam Map<String,Object> param){
        FdCost fdCost = BeanUtil.mapToBean(param, FdCost.class, false);
        return fdCostService.selectFdCostListAndAreaCode(fdCost);
    }

    @GetMapping("/selectLiangJiaKunBang")
    public List<FdCost> selectLiangJiaKunBang(@RequestParam Map<String,Object> param){
        FdCost fdCost = BeanUtil.mapToBean(param, FdCost.class, false);
        return fdCostService.selectLiangJiaKunBang(fdCost);
    }

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/list")
    public List list(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdCostService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        FdCost fdCost = BeanUtil.mapToBean(params, FdCost.class, false);
        boolean type = "1".equals(params.get("type"));
        List<FdCost> fdCosts = fdCostService.selectFdCostListAndAreaCode(fdCost);
        if (type) {
            ArrayList list = fdCosts.stream().collect(
                    collectingAndThen(
                            toCollection(() -> new TreeSet<>(Comparator.comparing(FdCost::getPaymentCustomerName))), ArrayList::new));
            return list;
        }
        if ("2".equals(params.get("type"))) {
            List<FdCost> collect = fdCosts.stream().filter(s -> (s.getQuantityBundledRefundYears() != null)).collect(Collectors.toList());
            return collect;
        }

        return fdCosts;
    }

    /**
     * 费用删除
     * @param fdCost
     * @return
     */
    @PostMapping("/updateCostCode")
    public R updateCostCode(@RequestBody FdCost fdCost) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        FdCosdetail fdCosdetail=new FdCosdetail();
        fdCosdetail.setCostCode(fdCost.getCostCode());
        List<FdCosdetail> fdCosdetails = fdCosdetailService.selectFdCosdetailList(fdCosdetail);
        for (FdCosdetail deatils:fdCosdetails) {
            if(!"".equals(deatils.getBillCode()) && deatils.getBillCode()!=null){
                R r=new R();
                r.setB(Boolean.FALSE);
                r.setCode(500);
                r.setMsg("不可以删除，明细已经生成账单");
                return r;
            }
        }
        fdCosdetail.setDelFlag("Y");
        fdCosdetail.setDelUsercode(userInfo.getUserName());
        fdCosdetail.setDelUserrealname(userInfo.getRealName());
        fdCosdetailService.updateFdCosdetailByCostCode(fdCosdetail);

        fdCost.setDelUsercode(userInfo.getUserName());
        fdCost.setDelUserrealname(userInfo.getRealName());
        fdCostService.updateFdCostByCostCode(fdCost);
        fdCostService.updateFdCostByCostCodeYf(fdCost);
        if("1".equals(fdCost.getPlatformLevel())){
            //省平台
            FdShippingAccount fdShippingAccount=new FdShippingAccount();
            fdShippingAccount.setProvinceShiftNo(fdCost.getProvinceTrainsNumber());
            fdShippingAccount.setUpdateWho(userInfo.getUserName());
            fdShippingAccount.setUpdateWhoName(userInfo.getRealName());
            fdShippingAccountService.updateStatus(fdShippingAccount);
        }else if("0".equals(fdCost.getPlatformLevel())){
            //市平台
            WaybillHeader waybillHeader=new WaybillHeader();
            waybillHeader.setWaybillNo(fdCost.getTransportOrderNumber());
            waybillHeader.setUpdateWho(userInfo.getUserName());
            waybillHeader.setUpdateWhoName(userInfo.getRealName());
            waybillHeaderService.updateWaybillNoByBillStatus(waybillHeader);
        }

        return new R<>(Boolean.TRUE,200);
    }


    /**
     * 信息
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Integer id) {
        FdCost fdCost =fdCostService.selectById(id);
        return new R<>(fdCost);
    }

    /**
     * 保存
     * @param fdCost
     * @return R
     */
    @PostMapping
    public R save(@RequestBody FdCost fdCost) {
        fdCostService.insert(fdCost);
        return new R<>(Boolean.TRUE);
    }
 /**
     * 运单审评审批通过后添加一条费用总计和多条费用细则
     * @param
     * @return R
     */
    @GetMapping("/savecostandcodstdetails")
    public R savecostandcodstdetails(@RequestParam String rowid) {
        Boolean b = fdCostService.savecostandcodstdetails(rowid);
        return new R<>(b);
    }

    /**
     * 根据平台编码获取下属订舱客户
     *
     * @param
     * @return R
     */
    @GetMapping("/selectcustomerbyplatformcode")
    public List selectcustomerbyplatformcode(@RequestParam Map map) {

        List<Map> maps = fdCostService.selectcustomerbyplatformcode(map);
        return maps;

    }


    /**
     * 修改汇率
     * @param fdCost
     * @return
     */
    @PostMapping("/updateOriginalExchangeRate")
    public R updateOriginalExchangeRate(@RequestBody FdCost fdCost){

       return fdCostService.updateOriginalExchangeRate(fdCost);
    }

    
 /**
     * 台账确认后生成费用
     * @param
     * @return R
     */
    @GetMapping("/savecostandcodstdetailsbyaccount")
    public R savecostandcodstdetailsbyaccount(@RequestParam String rowid) {
        Boolean b = null;

            b = fdCostService.savecostandcodstdetailsbyaccount(rowid);


        return new R<>(b);
    }

    @PostMapping("/savebyledger")
    @Transactional(rollbackFor = Exception.class)
    public R savebyledger(@RequestBody FdCostVO fdBillVO) {
        // TODO: 2021/8/24 这里默认调用的人已经把数据拆好了  这里应该不可能  最后应该还是要我们自己拆
// TODO: 2021/8/24 前提条件调用这个接口的人把数据都给我放好了
        FdCost fdCost = new FdCost();
        BeanUtils.copyProperties(fdBillVO,fdCost);
        fdCostService.insertFdCost(fdCost);
        List<FdCosdetail> fdCosdetailList = fdBillVO.getFdCosdetailList();
        // TODO: 2021/8/24 目前假设这里的数据都是正确的 但是这不可能
        List<List<FdCosdetail>> splitList = spiltlistutil.getSplitList(900, fdCosdetailList);
        splitList.stream().forEach(o -> {
            fdCosdetailService.insertBatch(o);
        })
        ;
        for (FdCosdetail fdCosdetail : fdCosdetailList) {
            // TODO: 2021/8/24 这里应该循环修改这些数据生成市平台对省平台的应付
        }
        // TODO: 2021/8/24 修改完成后插入应付数据
        //
        List<List<FdCosdetail>> splitList1 = spiltlistutil.getSplitList(900, fdCosdetailList);
        splitList1.stream().forEach(o -> {
            fdCosdetailService.insertBatch(o);
        })
        ;
        return new R(true);
    }

    /**
     * 修改
     * @param fdCost
     * @return R
     */
    @PutMapping
    public R update(@RequestBody FdCost fdCost) {
        fdCostService.updateById(fdCost);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     *
     * @param fdCostVO
     * @return R
     */
    @PostMapping("/insertFdCostVO")
    public R insertFdCostVO(@RequestBody FdCostVO fdCostVO) {

        FdCost fdCost = new FdCost();
        fdCostVO.setCostCode(sysNoConfigService.genNo("FDC"));
        BeanUtils.copyProperties(fdCostVO, fdCost);
        fdCost.setStandbyA("1");
        fdCost.setOriginalExchangeRate(null);
        fdCost.setNewExchangeRate(null);
        fdCost.setOverseasFreightOverseasFreight(null);
        fdCost.setForeignCurrency(null);

        fdCost.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
        fdCost.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
        fdCost.setUuid(UUID.randomUUID().toString());
        FdCosdetail fdCosdetail = fdCostVO.getFdCosdetail();
        BeanUtils.copyProperties(fdCostVO, fdCosdetail);
        fdCosdetail.setUuid(UUID.randomUUID().toString());
        fdCosdetail.setCustomerCode(fdCost.getPaymentCustomerCode());
        fdCosdetail.setCustomerName(fdCost.getPaymentCustomerName());
        fdCosdetail.setExchangeRate(fdCost.getOriginalExchangeRate());
        fdCosdetail.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
        fdCosdetail.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
        fdCosdetail.setLocalCurrencyAmount(fdCost.getDomesticFreight());
        fdCosdetail.setOriginalCurrencyAmount(fdCost.getDomesticFreight());
        fdCosdetail.setIncomeFlag("应收");
        fdCosdetail.setCurrency(null);
        fdCosdetail.setOriginalCurrencyAmount(null);
        fdCosdetail.setExchangeRate(null);
        fdCostService.insertFdCost(fdCost);
        fdCosdetailService.insertFdCosdetail(fdCosdetail);
        return new R<>(Boolean.TRUE);
    }



    /**
     * 删除
     * @param id
     * @return R
     */
    @DeleteMapping("/{id}")
    public R delete(@PathVariable  Integer id) {
        fdCostService.deleteById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<Integer> ids) {
        fdCostService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 保存 省平台应付费用
     */
    @PostMapping("/saveSehngCostDuolian")
    public R saveSehngCostDuolian(@RequestBody FdCostVO fdCostVO){
        FdCost fdCost = new FdCost();
        FdCost updatefdCost = new FdCost();
        FdCosdetail updatefdCosdetail=new FdCosdetail();
        updatefdCosdetail.setCostCode(fdCostVO.getCostCode());
        updatefdCost.setCostCode(fdCostVO.getCostCode());
        BeanUtils.copyProperties(fdCostVO, fdCost);
        fdCost.setUuid(UUID.randomUUID().toString());
        fdCost.setGatheringuserCode("DL");
        fdCost.setGatheringuserName("多联");
        fdCost.setPlatformLevel("2");
        fdCost.setStandbyB("1");
        fdCost.setCostCode(sysNoConfigService.genNo("FDC"));
        fdCost.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
        fdCost.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
        fdCost.setStandbyC(fdCostVO.getCostCode());
        List<FdCosdetail> fdCosdetailList = fdCostVO.getFdCosdetailList();
        BigDecimal  fDomesticFee=BigDecimal.valueOf(0);
        BigDecimal  fOverseasFee=BigDecimal.valueOf(0);
        BigDecimal  fOverseasFeeCny=BigDecimal.valueOf(0);
        for (FdCosdetail fdCosdetails:fdCosdetailList) {
            fdCosdetails.setUuid(UUID.randomUUID().toString());
            fdCosdetails.setCostCode(fdCost.getCostCode());
            fdCosdetails.setPlatformLevel("2");
            fdCosdetails.setIncomeFlag("应付");
            fdCosdetails.setStandbyA("1");
            fdCosdetails.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
            fdCosdetails.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
            //境内运费
            if("f_domestic_fee".equals(fdCosdetails.getCodeSsCategoriesCode())){
                fDomesticFee=fDomesticFee.add(fdCosdetails.getOriginalCurrencyAmount());
            }
            //境外运费
            if("f_overseas_fee".equals(fdCosdetails.getCodeSsCategoriesCode())){
                fOverseasFee=fOverseasFee.add(fdCosdetails.getOriginalCurrencyAmount());
                fOverseasFeeCny=fOverseasFeeCny.add(fdCosdetails.getLocalCurrencyAmount());
            }
        }
        fdCost.setOverseasFreightOverseasFreight(fOverseasFee);//境外运费
        fdCost.setOverseasFreightCny(fOverseasFeeCny);
        fdCost.setDomesticFreight(fDomesticFee);//境内运费
//        fdCost.setOriginalExchangeRate(fdCosdetailList.get(0).getExchangeRate());//汇率
        //修改省费用数据
        updatefdCost.setUpdateUsercode(SecurityUtils.getUserInfo().getUserName());
        updatefdCost.setUpdateUserrealname(SecurityUtils.getUserInfo().getRealName());
        fdCostService.updateFdCostCostCode(updatefdCost);
        //修改费用明细表数据
        updatefdCosdetail.setUpdateUsercode(SecurityUtils.getUserInfo().getUserName());
        updatefdCosdetail.setUpdateUserrealname(SecurityUtils.getUserInfo().getRealName());
        fdCosdetailService.updateCostDetailByCostCode(updatefdCosdetail);

        //新增多联费用
        fdCostService.insertFdCost(fdCost);
        //新增多联明细费用
        fdCosdetailService.insertFdCosdetailList(fdCosdetailList);

        return new R<>(Boolean.TRUE);
    }

/*    *//**
     *  省平台 生成应付应收费用
     *//*
    @PostMapping("/saveCostDuolian")
    public R saveCostDuolian(@RequestBody FdCostVO fdCostVO){
        FdCost fdCost = new FdCost();
        FdCost updatefdCost = new FdCost();
        FdCosdetail updatefdCosdetail=new FdCosdetail();
        updatefdCosdetail.setCostCode(fdCostVO.getCostCode());
        updatefdCost.setUuid(fdCostVO.getUuid());
        BeanUtils.copyProperties(fdCostVO, fdCost);
        fdCost.setPlatformLevel("2");
        fdCost.setStandbyB("1");
        fdCost.setUuid(UUID.randomUUID().toString());
        fdCost.setGatheringuserCode("DL");
        fdCost.setGatheringuserName("多联");
//        fdCost.setCostCode(sysNoConfigService.genNo("FDC"));
        fdCost.setPaymentCustomerCode(fdCostVO.getGatheringuserCode());
        fdCost.setPaymentCustomerName(fdCostVO.getGatheringuserName());
        fdCost.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
        fdCost.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
        fdCost.setStandbyC(fdCostVO.getCostCode());
//        List<FdCosdetail> fdCosdetailList = fdCostVO.getFdCosdetailList();
//        for (FdCosdetail fdCosdetail:fdCosdetailList) {
//            fdCosdetail.setUuid(UUID.randomUUID().toString());
//            fdCosdetail.setCostCode(fdCost.getCostCode());
//            fdCosdetail.setPlatformLevel("2");
//            fdCosdetail.setIncomeFlag("应付");
//            fdCosdetail.setStandbyA("1");
//            fdCosdetail.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
//            fdCosdetail.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
//        }
        //修改省费用数据
//        updatefdCost.setUpdateUsercode(SecurityUtils.getUserInfo().getUserName());
//        updatefdCost.setUpdateUserrealname(SecurityUtils.getUserInfo().getRealName());
//        fdCostService.updateFdCostByUuid(updatefdCost);
        //修改费用明细表数据
//        updatefdCosdetail.setUpdateUsercode(SecurityUtils.getUserInfo().getUserName());
//        updatefdCosdetail.setUpdateUserrealname(SecurityUtils.getUserInfo().getRealName());
//        fdCosdetailService.updateCostDetailByCostCode(updatefdCosdetail);

        //新增多联费用
//        fdCostService.insertFdCost(fdCost);
        //新增多联明细费用
//        fdCosdetailService.insertFdCosdetailList(fdCosdetailList);
        R booleanR = new R<>();
        booleanR.setMsg("生成应付成功");
        booleanR.setB(Boolean.TRUE);
        booleanR.setObject(fdCost);
        return booleanR;
    }*/

    /**
     * 省平台 费用编辑
     */
    @PostMapping("/updateCostDuolian")
    public R updateCostDuolian(@RequestBody FdCostVO fdCostVO){
        FdCost updatefdCost = new FdCost();
        updatefdCost.setCostCode(fdCostVO.getCostCode());
        updatefdCost.setDelFlag("N");
        updatefdCost.setDelUsercode(SecurityUtils.getUserInfo().getRealName());
        updatefdCost.setDelUserrealname(SecurityUtils.getUserInfo().getUserName());
        fdCostService.updateFdCostByCostCode(updatefdCost);
        FdCost fdCost = new FdCost();
        BeanUtils.copyProperties(fdCostVO, fdCost);
        fdCost.setCostCode(sysNoConfigService.genNo("FDC"));
        fdCost.setPlatformLevel("2");
        fdCost.setStandbyB("1");
        fdCost.setUuid(UUID.randomUUID().toString());
        //新增多联费用
        fdCostService.insertFdCost(fdCost);

        //修改费用明细
        List<FdCosdetail> fdCosdetailList = fdCostVO.getFdCosdetailList();
        if(fdCosdetailList.size()>0){
            fdCosdetailService.updateFdCosdetalSheng(fdCosdetailList);
        }
        return new R<>(Boolean.TRUE);
    }



    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<FdCost> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = fdCostService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<FdCost> list = reader.readAll(FdCost.class);
        fdCostService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 新修改账单关联台账
     * @param fdBillVO
     * @return R
     */
    @PostMapping("/updateBill")
    public R updateBill(@RequestBody FdBillVO fdBillVO) {
        fdCostService.updateBill(fdBillVO);
        return new R<>(Boolean.TRUE);
    }
}
