package com.huazheng.tunny.ocean.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.PlatformCheck;
import com.huazheng.tunny.ocean.service.PlatformCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 平台校验
 *
 * <AUTHOR>
 * @date 2024-10-24 09:56:16
 */
@Slf4j
@RestController
@RequestMapping("/platformcheck")
public class PlatformCheckController {

    @Autowired
    private PlatformCheckService platformCheckService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  platformCheckService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return platformCheckService.selectPlatformCheckListByLike(new Query<>(params));
    }

    /**
     * 信息
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Integer id) {
        PlatformCheck platformCheck =platformCheckService.selectById(id);
        return new R<>(platformCheck);
    }

    /**
     * 保存
     * @param platformCheck
     * @return R
     */
    @PostMapping
    public R save(@RequestBody PlatformCheck platformCheck) {
        platformCheckService.insert(platformCheck);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param platformCheck
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody PlatformCheck platformCheck) {
        platformCheckService.updateById(platformCheck);
        return new R<>(Boolean.TRUE);
    }

    

    /**
     * 删除
     * @param id
     * @return R
     */
    @GetMapping("/del/{id}")
    public R delete(@PathVariable  Integer id) {
        platformCheckService.deleteById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<Integer> ids) {
        platformCheckService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<PlatformCheck> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<PlatformCheck> list = platformCheckService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), PlatformCheck.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    @Transactional
    public R imported(String titleStr, MultipartFile file) throws Exception {
        
        return new R<>(Boolean.TRUE);
    }
}
