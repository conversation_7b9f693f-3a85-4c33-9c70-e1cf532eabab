package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.OperationLog;
import com.huazheng.tunny.ocean.api.entity.SysLog;

import java.util.List;

/**
 * 日志表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-08-04 11:35:56
 */
public interface OperationLogService extends IService<OperationLog> {

    /**
     * 新增日志表
     *
     * @param operationLog 日志表信息
     * @return 结果
     */
    public int insertOperationLog(OperationLog operationLog);

}

