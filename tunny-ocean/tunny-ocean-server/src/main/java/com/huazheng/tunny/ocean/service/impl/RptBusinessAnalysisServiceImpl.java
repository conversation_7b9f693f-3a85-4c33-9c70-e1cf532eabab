package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.FdBusCostDetailDTO;
import com.huazheng.tunny.ocean.api.dto.GrossProfitDeatilsDTO;
import com.huazheng.tunny.ocean.api.dto.IncomesContainerDeatilsDTO;
import com.huazheng.tunny.ocean.api.dto.IncomesDeatilsDTO;
import com.huazheng.tunny.ocean.api.entity.FdBusCostDetail;
import com.huazheng.tunny.ocean.api.entity.RptBusinessAnalysis;
import com.huazheng.tunny.ocean.api.entity.WaybillContainerInfo;
import com.huazheng.tunny.ocean.mapper.FdBusCostDetailMapper;
import com.huazheng.tunny.ocean.mapper.RptBusinessAnalysisMapper;
import com.huazheng.tunny.ocean.mapper.WaybillContainerInfoMapper;
import com.huazheng.tunny.ocean.service.RptBusinessAnalysisService;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("rptBusinessAnalysisService")
public class RptBusinessAnalysisServiceImpl extends ServiceImpl<RptBusinessAnalysisMapper, RptBusinessAnalysis> implements RptBusinessAnalysisService {

    @Autowired
    private RptBusinessAnalysisMapper rptBusinessAnalysisMapper;
    @Autowired
    private FdBusCostDetailMapper fdBusCostDetailMapper;
    @Value("${jyfx.zbwf}")
    private String zbwf;
    @Override
    public Page selectRptBusinessAnalysisLike(Query query) {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.plan_ship_time DESC,x.platform_code");
            query.setAsc(Boolean.TRUE);
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        RptBusinessAnalysis rptBusinessAnalysis = BeanUtil.mapToBean(query.getCondition(), RptBusinessAnalysis.class, false);
        if ("1".equals(userInfo.getPlatformLevel())) {
            rptBusinessAnalysis.setPlatformCode(userInfo.getPlatformCode());
        } else {
            rptBusinessAnalysis.setPlatformCodePro(userInfo.getPlatformCode());
        }
        rptBusinessAnalysis.setZbwf(zbwf);
        List<RptBusinessAnalysis> list = rptBusinessAnalysisMapper.selectRptBusinessAnalysisLike(query, rptBusinessAnalysis);
        if (CollUtil.isNotEmpty(list)) {
            for (RptBusinessAnalysis rpt : list
            ) {
                List<RptBusinessAnalysis> spaceNums = rptBusinessAnalysisMapper.selectRptBusinessAnalysisSpaceNums(rpt.getShiftId());
                if (CollUtil.isNotEmpty(spaceNums)) {
                    rpt.setSpaceNums(spaceNums.get(0).getSpaceNums());
                    rpt.setNum(spaceNums.get(0).getNum());
                }
            }
        }
        query.setRecords(list);
        return query;
    }

    @Override
    public void pageExport(RptBusinessAnalysis rptBusinessAnalysis, HttpServletResponse response) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        if ("1".equals(userInfo.getPlatformLevel())) {
            rptBusinessAnalysis.setPlatformCode(userInfo.getPlatformCode());
        } else {
            rptBusinessAnalysis.setPlatformCodePro(userInfo.getPlatformCode());
        }
        rptBusinessAnalysis.setZbwf(zbwf);
        List<RptBusinessAnalysis> list = rptBusinessAnalysisMapper.selectRptBusinessAnalysisLikeTwo(rptBusinessAnalysis);
        if (CollUtil.isNotEmpty(list)) {
            for (RptBusinessAnalysis rpt : list
            ) {
                List<RptBusinessAnalysis> spaceNums = rptBusinessAnalysisMapper.selectRptBusinessAnalysisSpaceNums(rpt.getShiftId());
                if (CollUtil.isNotEmpty(spaceNums)) {
                    rpt.setSpaceNums(spaceNums.get(0).getSpaceNums());
                    rpt.setNum(spaceNums.get(0).getNum());
                }
            }
        }
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("经营分析");
        XSSFRow row = sheet.createRow(0);
        //宽度
        for (int i = 0; i <= 11; i++) {
            sheet.setColumnWidth(i, 60 * 80);
        }
        int rowIndex = 0;
        row.setHeight((short) (10 * 50));
        //字体1
        XSSFCellStyle style = setStyle(workbook);
        XSSFCellStyle style2 = setStyle2(workbook);
        if ("1".equals(userInfo.getPlatformLevel())) {
            setTitle2(row, style);
            rowIndex++;
            setList2(list, sheet, rowIndex,style2);
        } else {
            setTitle1(row, style);
            rowIndex++;
            setList1(list, sheet, rowIndex,style2);
        }

        //写入数据流下载
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            // 使用 UTF-8 编码处理文件名，确保中文文件名在浏览器中正确显示
            String encodedFileName = URLEncoder.encode(userInfo.getPlatformName()+"--经营分析.xlsx", "UTF-8");
            encodedFileName = encodedFileName.replaceAll("\\+", "%20"); // 处理空格问题
            response.setHeader("Content-Disposition", "attachment;filename=\"" + encodedFileName + "\"");

            // 设置缓存控制
            response.addHeader("Pargam", "no-cache");
            response.addHeader("Cache-Control", "no-cache");
            ServletOutputStream out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            // 关闭writer，释放内存
            out.close();
            workbook.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setList1(List<RptBusinessAnalysis> list,XSSFSheet sheet, int rowIndex,XSSFCellStyle style) {
        SimpleDateFormat formatter  = new SimpleDateFormat("yyyy-MM-dd");
        if(CollUtil.isNotEmpty(list)){
            for (RptBusinessAnalysis rptBusinessAnalysis:list
                 ) {
                XSSFRow row = sheet.createRow(rowIndex);

                XSSFCell cell0 = row.createCell(0);
                cell0.setCellValue(rptBusinessAnalysis.getPlatformName());

                XSSFCell cell12 = row.createCell(1);
                if("1".equals(rptBusinessAnalysis.getIsParent())){
                    cell12.setCellValue("是");
                }else if("0".equals(rptBusinessAnalysis.getIsParent())){
                    cell12.setCellValue("否");
                }

                XSSFCell cell1 = row.createCell(2);
                cell1.setCellValue(rptBusinessAnalysis.getProvinceShiftNo());

                XSSFCell cell2 = row.createCell(3);
                cell2.setCellValue(rptBusinessAnalysis.getShiftName());

                XSSFCell cell3 = row.createCell(4);
                cell3.setCellValue(formatter.format(rptBusinessAnalysis.getPlanShipTime()));

                XSSFCell cell4 = row.createCell(5);
                if(rptBusinessAnalysis.getSrzj() != null){
                    cell4.setCellValue(Double.parseDouble(String.valueOf(rptBusinessAnalysis.getSrzj())));
                }else{
                    cell4.setCellValue(Double.parseDouble("0.00"));
                }
                cell4.setCellStyle(style);

                XSSFCell cell5 = row.createCell(6);
                if(rptBusinessAnalysis.getHysrzj() != null){
                    cell5.setCellValue(Double.parseDouble(String.valueOf(rptBusinessAnalysis.getHysrzj())));
                }else{
                    cell5.setCellValue(Double.parseDouble("0.00"));
                }
                cell5.setCellStyle(style);

                XSSFCell cell6 = row.createCell(7);
                if(rptBusinessAnalysis.getZczj() != null){
                    cell6.setCellValue(Double.parseDouble(String.valueOf(rptBusinessAnalysis.getZczj())));
                }else{
                    cell6.setCellValue(Double.parseDouble("0.00"));
                }
                cell6.setCellStyle(style);

                XSSFCell cell7 = row.createCell(8);
                if(rptBusinessAnalysis.getMl() != null){
                    cell7.setCellValue(Double.parseDouble(String.valueOf(rptBusinessAnalysis.getMl())));
                }else{
                    cell7.setCellValue(Double.parseDouble("0.00"));
                }
                cell7.setCellStyle(style);

                XSSFCell cell8 = row.createCell(9);
                cell8.setCellValue(rptBusinessAnalysis.getShippingLine());

                XSSFCell cell9 = row.createCell(10);
                if("G".equals(rptBusinessAnalysis.getTrip())){
                    cell9.setCellValue("去程");
                }else if("R".equals(rptBusinessAnalysis.getTrip())){
                    cell9.setCellValue("回程");
                }

                XSSFCell cell13 = row.createCell(11);
                cell13.setCellValue(rptBusinessAnalysis.getPortStation());

                XSSFCell cell10 = row.createCell(12);
                if(rptBusinessAnalysis.getSpaceNums() != null){
                    cell10.setCellValue(Double.parseDouble(String.valueOf(rptBusinessAnalysis.getSpaceNums())));
                }else{
                    cell10.setCellValue(Double.parseDouble("0.00"));
                }
                cell10.setCellStyle(style);

                XSSFCell cell11 = row.createCell(13);
                if(rptBusinessAnalysis.getNum() != null){
                    cell11.setCellValue(rptBusinessAnalysis.getNum());
                }else{
                    cell11.setCellValue(0);
                }
                cell11.setCellStyle(style);

                rowIndex++;
            }
        }
    }

    public void setList2(List<RptBusinessAnalysis> list,XSSFSheet sheet, int rowIndex,XSSFCellStyle style) {
        SimpleDateFormat formatter  = new SimpleDateFormat("yyyy-MM-dd");
        if(CollUtil.isNotEmpty(list)){
            for (RptBusinessAnalysis rptBusinessAnalysis:list
            ) {
                XSSFRow row = sheet.createRow(rowIndex);

                XSSFCell cell1 = row.createCell(0);
                cell1.setCellValue(rptBusinessAnalysis.getProvinceShiftNo());

                XSSFCell cell2 = row.createCell(1);
                cell2.setCellValue(rptBusinessAnalysis.getShiftName());

                XSSFCell cell3 = row.createCell(2);
                cell3.setCellValue(formatter.format(rptBusinessAnalysis.getPlanShipTime()));

                XSSFCell cell4 = row.createCell(3);
                if(rptBusinessAnalysis.getSrzj() != null){
                    cell4.setCellValue(Double.parseDouble(String.valueOf(rptBusinessAnalysis.getSrzj())));
                }else{
                    cell4.setCellValue(Double.parseDouble("0.00"));
                }
                cell4.setCellStyle(style);

                XSSFCell cell5 = row.createCell(4);
                if(rptBusinessAnalysis.getHysrzj() != null){
                    cell5.setCellValue(Double.parseDouble(String.valueOf(rptBusinessAnalysis.getHysrzj())));
                }else{
                    cell5.setCellValue(Double.parseDouble("0.00"));
                }
                cell5.setCellStyle(style);

                XSSFCell cell6 = row.createCell(5);
                if(rptBusinessAnalysis.getZczj() != null){
                    cell6.setCellValue(Double.parseDouble(String.valueOf(rptBusinessAnalysis.getZczj())));
                }else{
                    cell6.setCellValue(Double.parseDouble("0.00"));
                }
                cell6.setCellStyle(style);

                XSSFCell cell7 = row.createCell(6);
                if(rptBusinessAnalysis.getMl() != null){
                    cell7.setCellValue(Double.parseDouble(String.valueOf(rptBusinessAnalysis.getMl())));
                }else{
                    cell7.setCellValue(Double.parseDouble("0.00"));
                }
                cell7.setCellStyle(style);

                XSSFCell cell8 = row.createCell(7);
                cell8.setCellValue(rptBusinessAnalysis.getShippingLine());

                XSSFCell cell9 = row.createCell(8);
                if("G".equals(rptBusinessAnalysis.getTrip())){
                    cell9.setCellValue("去程");
                }else if("R".equals(rptBusinessAnalysis.getTrip())){
                    cell9.setCellValue("回程");
                }

                XSSFCell cell12 = row.createCell(9);
                cell12.setCellValue(rptBusinessAnalysis.getPortStation());


                XSSFCell cell10 = row.createCell(10);
                if(rptBusinessAnalysis.getSpaceNums() != null){
                    cell10.setCellValue(Double.parseDouble(String.valueOf(rptBusinessAnalysis.getSpaceNums())));
                }else{
                    cell10.setCellValue(Double.parseDouble("0.00"));
                }
                cell10.setCellStyle(style);

                XSSFCell cell11 = row.createCell(11);
                if(rptBusinessAnalysis.getNum() != null){
                    cell11.setCellValue(rptBusinessAnalysis.getNum());
                }else{
                    cell11.setCellValue(0);
                }
                cell11.setCellStyle(style);

                rowIndex++;
            }
        }
    }

    /**
     * 设置标题
     *
     * @Param: row, style
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/04 17:57
     **/
    public void setTitle1(XSSFRow row, XSSFCellStyle style) {
        XSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("所属平台");
        cell0.setCellStyle(style);
        XSSFCell cell1 = row.createCell(1);
        cell1.setCellValue("是否直接委托");
        cell1.setCellStyle(style);
        XSSFCell cell2 = row.createCell(2);
        cell2.setCellValue("省班列号");
        cell2.setCellStyle(style);
        XSSFCell cell3 = row.createCell(3);
        cell3.setCellValue("班列名称");
        cell3.setCellStyle(style);
        XSSFCell cell4 = row.createCell(4);
        cell4.setCellValue("发运日期");
        cell4.setCellStyle(style);
        XSSFCell cell5 = row.createCell(5);
        cell5.setCellValue("收入总计（CNY/元）");
        cell5.setCellStyle(style);
        XSSFCell cell6 = row.createCell(6);
        cell6.setCellValue("还原收入总计（CNY/元）");
        cell6.setCellStyle(style);
        XSSFCell cell7 = row.createCell(7);
        cell7.setCellValue("支出总计（CNY/元）");
        cell7.setCellStyle(style);
        XSSFCell cell8 = row.createCell(8);
        cell8.setCellValue("毛利（CNY/元）");
        cell8.setCellStyle(style);
        XSSFCell cell9 = row.createCell(9);
        cell9.setCellValue("发运线路");
        cell9.setCellStyle(style);
        XSSFCell cell10 = row.createCell(10);
        cell10.setCellValue("方向");
        cell10.setCellStyle(style);
        XSSFCell cell11 = row.createCell(11);
        cell11.setCellValue("口岸代理");
        cell11.setCellStyle(style);
        XSSFCell cell12 = row.createCell(12);
        cell12.setCellValue("舱位数");
        cell12.setCellStyle(style);
        XSSFCell cell13 = row.createCell(13);
        cell13.setCellValue("箱量");
        cell13.setCellStyle(style);
    }

    public void setTitle2(XSSFRow row, XSSFCellStyle style) {
        XSSFCell cell1 = row.createCell(0);
        cell1.setCellValue("省班列号");
        cell1.setCellStyle(style);
        XSSFCell cell2 = row.createCell(1);
        cell2.setCellValue("班列名称");
        cell2.setCellStyle(style);
        XSSFCell cell3 = row.createCell(2);
        cell3.setCellValue("发运日期");
        cell3.setCellStyle(style);
        XSSFCell cell4 = row.createCell(3);
        cell4.setCellValue("收入总计（CNY/元）");
        cell4.setCellStyle(style);
        XSSFCell cell5 = row.createCell(4);
        cell5.setCellValue("还原收入总计（CNY/元）");
        cell5.setCellStyle(style);
        XSSFCell cell6 = row.createCell(5);
        cell6.setCellValue("支出总计（CNY/元）");
        cell6.setCellStyle(style);
        XSSFCell cell7 = row.createCell(6);
        cell7.setCellValue("毛利（CNY/元）");
        cell7.setCellStyle(style);
        XSSFCell cell8 = row.createCell(7);
        cell8.setCellValue("发运线路");
        cell8.setCellStyle(style);
        XSSFCell cell9 = row.createCell(8);
        cell9.setCellValue("方向");
        cell9.setCellStyle(style);
        XSSFCell cell10 = row.createCell(9);
        cell10.setCellValue("口岸代理");
        cell10.setCellStyle(style);
        XSSFCell cell11 = row.createCell(10);
        cell11.setCellValue("舱位数");
        cell11.setCellStyle(style);
        XSSFCell cell12 = row.createCell(11);
        cell12.setCellValue("箱量");
        cell12.setCellStyle(style);
    }

    public XSSFCellStyle setStyle(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");
        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);
        return style;
    }

    public XSSFCellStyle setStyle2(XSSFWorkbook workbook) {
        XSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.RIGHT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        DataFormat format = workbook.createDataFormat();
        style.setDataFormat(format.getFormat("0.00"));
        return style;
    }

    @Override
    public List<IncomesDeatilsDTO> incomesDeatilsList(IncomesDeatilsDTO incomesDeatilsDTO) {
        return rptBusinessAnalysisMapper.incomesDeatilsList(incomesDeatilsDTO);
    }

    @Override
    public List<String> incomesContainerNumber(IncomesDeatilsDTO incomesDeatilsDTO) {
        return rptBusinessAnalysisMapper.incomesContainerNumber(incomesDeatilsDTO);
    }

    @Override
    public Map<String,Object> incomesContainerDeatilsList(IncomesContainerDeatilsDTO incomesContainerDeatilsDTO) {
        Map<String,Object> map = new HashMap<>();
        BigDecimal total = BigDecimal.ZERO;
        List<FdBusCostDetailDTO> list = rptBusinessAnalysisMapper.incomesContainerDeatilsList(incomesContainerDeatilsDTO);
        if(CollUtil.isNotEmpty(list)){
            for (FdBusCostDetailDTO fdBusCostDetailDTO:list
                 ) {
                total = total.add(fdBusCostDetailDTO.getLocalAmount());
            }
        }
        map.put("list",list);
        map.put("total",total);
        return map;
    }

    @Override
    public List<IncomesDeatilsDTO> restoreIncomesDeatilsList(IncomesDeatilsDTO incomesDeatilsDTO) {
        return rptBusinessAnalysisMapper.restoreIncomesDeatilsList(incomesDeatilsDTO);
    }

    @Override
    public Map<String,Object> restoreIncomesContainerDeatilsList(IncomesContainerDeatilsDTO incomesContainerDeatilsDTO) {
        incomesContainerDeatilsDTO.setIsRestore("1");
        Map<String,Object> map = new HashMap<>();
        BigDecimal total = BigDecimal.ZERO;
        List<FdBusCostDetailDTO> list = rptBusinessAnalysisMapper.incomesContainerDeatilsList(incomesContainerDeatilsDTO);
        if(CollUtil.isNotEmpty(list)){
            for (FdBusCostDetailDTO fdBusCostDetailDTO:list
            ) {
                total = total.add(fdBusCostDetailDTO.getLocalAmount());
            }
        }
        map.put("list",list);
        map.put("total",total);
        return map;
    }

    @Override
    public List<IncomesDeatilsDTO> expDeatilsList(IncomesDeatilsDTO incomesDeatilsDTO) {
        return rptBusinessAnalysisMapper.expDeatilsList(incomesDeatilsDTO);
    }

    @Override
    public List<FdBusCostDetailDTO> expContainerDeatilsList(IncomesContainerDeatilsDTO incomesContainerDeatilsDTO) {
        FdBusCostDetail sel = new FdBusCostDetail();
        BeanUtil.copyProperties(incomesContainerDeatilsDTO, sel);
        sel.setShiftNo(incomesContainerDeatilsDTO.getShiftNo());
        sel.setReceiveCode(incomesContainerDeatilsDTO.getCustomerNo());
        sel.setPayCode(incomesContainerDeatilsDTO.getPlatformCode());
        sel.setCostType("1");
        return fdBusCostDetailMapper.selectDetailListByLike(sel);
    }

    @Override
    public List<RptBusinessAnalysis> grossProfitContainerList(IncomesDeatilsDTO incomesDeatilsDTO) {
        return rptBusinessAnalysisMapper.grossProfitContainerList(incomesDeatilsDTO);
    }

    @Override
    public GrossProfitDeatilsDTO grossProfitContainerDetails(IncomesDeatilsDTO incomesDeatilsDTO) {
        GrossProfitDeatilsDTO dto = new GrossProfitDeatilsDTO();
        List<WaybillContainerInfo> waybillContainerInfos = rptBusinessAnalysisMapper.selectContainerInfo(incomesDeatilsDTO);
        if(CollUtil.isNotEmpty(waybillContainerInfos)){
            dto.setCustomerNo(waybillContainerInfos.get(0).getCustomerNo());
            dto.setCustomerName(waybillContainerInfos.get(0).getCustomerName());
            dto.setContainerNumber(incomesDeatilsDTO.getContainerNo());
            dto.setStationCompilation(waybillContainerInfos.get(0).getStationCompilation());
            dto.setStartStationName(waybillContainerInfos.get(0).getStartStationName());
            dto.setEndCompilation(waybillContainerInfos.get(0).getEndCompilation());
            dto.setEndStationName(waybillContainerInfos.get(0).getEndStationName());
            dto.setIdentification(waybillContainerInfos.get(0).getIdentification());
            dto.setContainerTypeCode(waybillContainerInfos.get(0).getContainerTypeCode());
            dto.setContainerOwner(waybillContainerInfos.get(0).getContainerOwner());
        }
        FdBusCostDetail sel2 = new FdBusCostDetail();
        sel2.setShiftNo(incomesDeatilsDTO.getShiftNo());
        sel2.setReceiveCode(incomesDeatilsDTO.getPlatformCode());
        sel2.setContainerNumber(incomesDeatilsDTO.getContainerNo());
        sel2.setCostType("0");
        sel2.setAuditStatus("1");
        sel2.setDeleteFlag("N");
        List<FdBusCostDetailDTO> ysList = fdBusCostDetailMapper.selectDetailListByLike(sel2);
        BigDecimal ys = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(ysList)) {
            for (FdBusCostDetailDTO fdBusCostDetailDTO : ysList
            ) {
                ys = ys.add(fdBusCostDetailDTO.getLocalAmount());
            }
            dto.setYsList(ysList);
        }
        dto.setYsAmount(ys);

        FdBusCostDetail sel3 = new FdBusCostDetail();
        sel3.setShiftNo(incomesDeatilsDTO.getShiftNo());
        sel3.setPayCode(incomesDeatilsDTO.getPlatformCode());
        sel3.setContainerNumber(incomesDeatilsDTO.getContainerNo());
        sel3.setCostType("1");
        sel3.setAuditStatus("1");
        sel3.setDeleteFlag("N");
        List<FdBusCostDetailDTO> yfList = fdBusCostDetailMapper.selectDetailListByLike(sel3);
        BigDecimal yf = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(yfList)) {
            for (FdBusCostDetailDTO fdBusCostDetailDTO : yfList
            ) {
                yf = yf.add(fdBusCostDetailDTO.getLocalAmount());
            }
            dto.setYfList(yfList);
        }
        dto.setYfAmount(yf);

        BigDecimal ml = ys.subtract(yf);
        dto.setMl(ys +" - "+ yf +" = "+ml);

        return dto;
    }
}
