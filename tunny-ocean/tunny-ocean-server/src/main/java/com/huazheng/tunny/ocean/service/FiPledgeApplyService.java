package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FiPledgeApply;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 质押申请表 服务接口层
 *
 * <AUTHOR>
 * @date 2022-11-10 10:38:33
 */
public interface FiPledgeApplyService extends IService<FiPledgeApply> {
    /**
     * 查询质押申请表信息
     *
     * @param rowId 质押申请表ID
     * @return 质押申请表信息
     */
    public FiPledgeApply selectFiPledgeApplyById(String rowId);

    /**
     * 查询质押申请表列表
     *
     * @param fiPledgeApply 质押申请表信息
     * @return 质押申请表集合
     */
    public List<FiPledgeApply> selectFiPledgeApplyList(FiPledgeApply fiPledgeApply);


    /**
     * 分页模糊查询质押申请表列表
     * @return 质押申请表集合
     */
    public Page selectFiPledgeApplyListByLike(Query query);



    /**
     * 新增质押申请表
     *
     * @param fiPledgeApply 质押申请表信息
     * @return 结果
     */
    public int insertFiPledgeApply(FiPledgeApply fiPledgeApply);

    /**
     * 修改质押申请表
     *
     * @param fiPledgeApply 质押申请表信息
     * @return 结果
     */
    public int updateFiPledgeApply(FiPledgeApply fiPledgeApply);

    /**
     * 删除质押申请表
     *
     * @param rowId 质押申请表ID
     * @return 结果
     */
    public int deleteFiPledgeApplyById(String rowId);

    /**
     * 批量删除质押申请表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiPledgeApplyByIds(Integer[] rowIds);

}

