package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.EfFinancingPledgedisposalinfoMapper;
import com.huazheng.tunny.ocean.api.entity.EfFinancingPledgedisposalinfo;
import com.huazheng.tunny.ocean.service.EfFinancingPledgedisposalinfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("efFinancingPledgedisposalinfoService")
public class EfFinancingPledgedisposalinfoServiceImpl extends ServiceImpl<EfFinancingPledgedisposalinfoMapper, EfFinancingPledgedisposalinfo> implements EfFinancingPledgedisposalinfoService {

    @Autowired
    private EfFinancingPledgedisposalinfoMapper efFinancingPledgedisposalinfoMapper;

    public EfFinancingPledgedisposalinfoMapper getEfFinancingPledgedisposalinfoMapper() {
        return efFinancingPledgedisposalinfoMapper;
    }

    public void setEfFinancingPledgedisposalinfoMapper(EfFinancingPledgedisposalinfoMapper efFinancingPledgedisposalinfoMapper) {
        this.efFinancingPledgedisposalinfoMapper = efFinancingPledgedisposalinfoMapper;
    }

    /**
     * 查询质押物处置情况信息
     *
     * @param rowId 质押物处置情况ID
     * @return 质押物处置情况信息
     */
    @Override
    public EfFinancingPledgedisposalinfo selectEfFinancingPledgedisposalinfoById(String rowId)
    {
        return efFinancingPledgedisposalinfoMapper.selectEfFinancingPledgedisposalinfoById(rowId);
    }

    /**
     * 查询质押物处置情况列表
     *
     * @param efFinancingPledgedisposalinfo 质押物处置情况信息
     * @return 质押物处置情况集合
     */
    @Override
    public List<EfFinancingPledgedisposalinfo> selectEfFinancingPledgedisposalinfoList(EfFinancingPledgedisposalinfo efFinancingPledgedisposalinfo)
    {
        return efFinancingPledgedisposalinfoMapper.selectEfFinancingPledgedisposalinfoList(efFinancingPledgedisposalinfo);
    }


    /**
     * 分页模糊查询质押物处置情况列表
     * @return 质押物处置情况集合
     */
    @Override
    public Page selectEfFinancingPledgedisposalinfoListByLike(Query query)
    {
        EfFinancingPledgedisposalinfo efFinancingPledgedisposalinfo =  BeanUtil.mapToBean(query.getCondition(), EfFinancingPledgedisposalinfo.class,false);
        query.setRecords(efFinancingPledgedisposalinfoMapper.selectEfFinancingPledgedisposalinfoListByLike(query,efFinancingPledgedisposalinfo));
        return query;
    }

    /**
     * 新增质押物处置情况
     *
     * @param efFinancingPledgedisposalinfo 质押物处置情况信息
     * @return 结果
     */
    @Override
    public int insertEfFinancingPledgedisposalinfo(EfFinancingPledgedisposalinfo efFinancingPledgedisposalinfo)
    {
        return efFinancingPledgedisposalinfoMapper.insertEfFinancingPledgedisposalinfo(efFinancingPledgedisposalinfo);
    }

    /**
     * 修改质押物处置情况
     *
     * @param efFinancingPledgedisposalinfo 质押物处置情况信息
     * @return 结果
     */
    @Override
    public int updateEfFinancingPledgedisposalinfo(EfFinancingPledgedisposalinfo efFinancingPledgedisposalinfo)
    {
        return efFinancingPledgedisposalinfoMapper.updateEfFinancingPledgedisposalinfo(efFinancingPledgedisposalinfo);
    }


    /**
     * 删除质押物处置情况
     *
     * @param rowId 质押物处置情况ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingPledgedisposalinfoById(String rowId)
    {
        return efFinancingPledgedisposalinfoMapper.deleteEfFinancingPledgedisposalinfoById( rowId);
    };


    /**
     * 批量删除质押物处置情况对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingPledgedisposalinfoByIds(Integer[] rowIds)
    {
        return efFinancingPledgedisposalinfoMapper.deleteEfFinancingPledgedisposalinfoByIds( rowIds);
    }

}
