package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FiExporfinancingInvoices;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 出口融资发票主表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-04-25 12:22:41
 */
public interface FiExporfinancingInvoicesService extends IService<FiExporfinancingInvoices> {
    /**
     * 查询出口融资发票主表信息
     *
     * @param rowId 出口融资发票主表ID
     * @return 出口融资发票主表信息
     */
    public FiExporfinancingInvoices selectFiExporfinancingInvoicesById(String rowId);

    /**
     * 查询出口融资发票主表列表
     *
     * @param fiExporfinancingInvoices 出口融资发票主表信息
     * @return 出口融资发票主表集合
     */
    public List<FiExporfinancingInvoices> selectFiExporfinancingInvoicesList(FiExporfinancingInvoices fiExporfinancingInvoices);


    /**
     * 分页模糊查询出口融资发票主表列表
     * @return 出口融资发票主表集合
     */
    public Page selectFiExporfinancingInvoicesListByLike(Query query);



    /**
     * 新增出口融资发票主表
     *
     * @param fiExporfinancingInvoices 出口融资发票主表信息
     * @return 结果
     */
    public int insertFiExporfinancingInvoices(FiExporfinancingInvoices fiExporfinancingInvoices);

    /**
     * 修改出口融资发票主表
     *
     * @param fiExporfinancingInvoices 出口融资发票主表信息
     * @return 结果
     */
    public int updateFiExporfinancingInvoices(FiExporfinancingInvoices fiExporfinancingInvoices);

    /**
     * 删除出口融资发票主表
     *
     * @param rowId 出口融资发票主表ID
     * @return 结果
     */
    public int deleteFiExporfinancingInvoicesById(String rowId);

    /**
     * 批量删除出口融资发票主表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiExporfinancingInvoicesByIds(Integer[] rowIds);

}

