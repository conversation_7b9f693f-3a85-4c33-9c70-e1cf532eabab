package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.ocean.mapper.StoredCityOpenBusinessMonthMapper;
import com.huazheng.tunny.ocean.api.entity.StoredCityOpenBusinessMonth;
import com.huazheng.tunny.ocean.service.StoredCityOpenBusinessMonthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.List;

@Service("storedCityOpenBusinessMonthService")
public class StoredCityOpenBusinessMonthServiceImpl extends ServiceImpl<StoredCityOpenBusinessMonthMapper, StoredCityOpenBusinessMonth> implements StoredCityOpenBusinessMonthService {

    @Autowired
    private StoredCityOpenBusinessMonthMapper storedCityOpenBusinessMonthMapper;

    public StoredCityOpenBusinessMonthMapper getStoredCityOpenBusinessMonthMapper() {
        return storedCityOpenBusinessMonthMapper;
    }

    public void setStoredCityOpenBusinessMonthMapper(StoredCityOpenBusinessMonthMapper storedCityOpenBusinessMonthMapper) {
        this.storedCityOpenBusinessMonthMapper = storedCityOpenBusinessMonthMapper;
    }

    /**
     * 查询信息
     *
     * @param rowId ID
     * @return 信息
     */
    @Override
    public StoredCityOpenBusinessMonth selectStoredCityOpenBusinessMonthById(Integer rowId)
    {
        return storedCityOpenBusinessMonthMapper.selectStoredCityOpenBusinessMonthById(rowId);
    }

    /**
     * 查询列表
     *
     * @param storedCityOpenBusinessMonth 信息
     * @return 集合
     */
    @Override
    public List<StoredCityOpenBusinessMonth> selectStoredCityOpenBusinessMonthList(StoredCityOpenBusinessMonth storedCityOpenBusinessMonth)
    {
        if(storedCityOpenBusinessMonth!=null && StrUtil.isEmpty(storedCityOpenBusinessMonth.getDate())){
            String date=LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
            storedCityOpenBusinessMonth.setDate(date);
        }
        String year = storedCityOpenBusinessMonth.getDate().split("-")[0];
        storedCityOpenBusinessMonth.setYear(year);
        List<StoredCityOpenBusinessMonth> list = storedCityOpenBusinessMonthMapper.selectStoredCityOpenBusinessMonthList(storedCityOpenBusinessMonth);
        StoredCityOpenBusinessMonth total = storedCityOpenBusinessMonthMapper.selectStoredCityOpenBusinessMonthListTotal(storedCityOpenBusinessMonth);
        list.add(total);
        return list;
    }


    /**
     * 分页模糊查询列表
     * @return 集合
     */
    @Override
    public Page selectStoredCityOpenBusinessMonthListByLike(Query query)
    {
        StoredCityOpenBusinessMonth storedCityOpenBusinessMonth =  BeanUtil.mapToBean(query.getCondition(), StoredCityOpenBusinessMonth.class,false);
        List<StoredCityOpenBusinessMonth> list = storedCityOpenBusinessMonthMapper.selectStoredCityOpenBusinessMonthListByLike(query, storedCityOpenBusinessMonth);
        return query;
    }

    /**
     * 新增
     *
     * @param storedCityOpenBusinessMonth 信息
     * @return 结果
     */
    @Override
    public int insertStoredCityOpenBusinessMonth(StoredCityOpenBusinessMonth storedCityOpenBusinessMonth)
    {
        return storedCityOpenBusinessMonthMapper.insertStoredCityOpenBusinessMonth(storedCityOpenBusinessMonth);
    }

    /**
     * 修改
     *
     * @param storedCityOpenBusinessMonth 信息
     * @return 结果
     */
    @Override
    public int updateStoredCityOpenBusinessMonth(StoredCityOpenBusinessMonth storedCityOpenBusinessMonth)
    {
        return storedCityOpenBusinessMonthMapper.updateStoredCityOpenBusinessMonth(storedCityOpenBusinessMonth);
    }


    /**
     * 删除
     *
     * @param rowId ID
     * @return 结果
     */
    public int deleteStoredCityOpenBusinessMonthById(Integer rowId)
    {
        return storedCityOpenBusinessMonthMapper.deleteStoredCityOpenBusinessMonthById( rowId);
    };


    /**
     * 批量删除对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteStoredCityOpenBusinessMonthByIds(Integer[] rowIds)
    {
        return storedCityOpenBusinessMonthMapper.deleteStoredCityOpenBusinessMonthByIds( rowIds);
    }

}
