package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.TimeManagementMapper;
import com.huazheng.tunny.ocean.api.entity.TimeManagement;
import com.huazheng.tunny.ocean.service.TimeManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("timeManagementService")
public class TimeManagementServiceImpl extends ServiceImpl<TimeManagementMapper, TimeManagement> implements TimeManagementService {

    @Autowired
    private TimeManagementMapper timeManagementMapper;

    public TimeManagementMapper getTimeManagementMapper() {
        return timeManagementMapper;
    }

    public void setTimeManagementMapper(TimeManagementMapper timeManagementMapper) {
        this.timeManagementMapper = timeManagementMapper;
    }

    /**
     * 查询时效管理表信息
     *
     * @param rowId 时效管理表ID
     * @return 时效管理表信息
     */
    @Override
    public TimeManagement selectTimeManagementById(String rowId)
    {
        return timeManagementMapper.selectTimeManagementById(rowId);
    }

    /**
     * 查询时效管理表列表
     *
     * @param timeManagement 时效管理表信息
     * @return 时效管理表集合
     */
    @Override
    public List<TimeManagement> selectTimeManagementList(TimeManagement timeManagement)
    {
        return timeManagementMapper.selectTimeManagementList(timeManagement);
    }


    /**
     * 分页模糊查询时效管理表列表
     * @return 时效管理表集合
     */
    @Override
    public Page selectTimeManagementListByLike(Query query)
    {
        TimeManagement timeManagement =  BeanUtil.mapToBean(query.getCondition(), TimeManagement.class,false);
        query.setRecords(timeManagementMapper.selectTimeManagementListByLike(query,timeManagement));
        return query;
    }

    /**
     * 新增时效管理表
     *
     * @param timeManagement 时效管理表信息
     * @return 结果
     */
    @Override
    public int insertTimeManagement(TimeManagement timeManagement)
    {
        return timeManagementMapper.insertTimeManagement(timeManagement);
    }

    /**
     * 修改时效管理表
     *
     * @param timeManagement 时效管理表信息
     * @return 结果
     */
    @Override
    public int updateTimeManagement(TimeManagement timeManagement)
    {
        return timeManagementMapper.updateTimeManagement(timeManagement);
    }


    /**
     * 删除时效管理表
     *
     * @param rowId 时效管理表ID
     * @return 结果
     */
    public int deleteTimeManagementById(String rowId)
    {
        return timeManagementMapper.deleteTimeManagementById( rowId);
    };


    /**
     * 批量删除时效管理表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteTimeManagementByIds(Integer[] rowIds)
    {
        return timeManagementMapper.deleteTimeManagementByIds( rowIds);
    }

}
