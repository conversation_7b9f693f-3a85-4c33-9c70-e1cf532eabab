package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.api.entity.StoredCompareConvert;
import com.huazheng.tunny.ocean.mapper.StoredCompareNatureMapper;
import com.huazheng.tunny.ocean.api.entity.StoredCompareNature;
import com.huazheng.tunny.ocean.service.StoredCompareNatureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service("storedCompareNatureService")
public class StoredCompareNatureServiceImpl extends ServiceImpl<StoredCompareNatureMapper, StoredCompareNature> implements StoredCompareNatureService {

    @Autowired
    private StoredCompareNatureMapper storedCompareNatureMapper;

    /**
     * 查询省平台-数据同比（进出口、过境、回程）---自然列信息
     *
     * @param rowId 省平台-数据同比（进出口、过境、回程）---自然列ID
     * @return 省平台-数据同比（进出口、过境、回程）---自然列信息
     */
    @Override
    public StoredCompareNature selectStoredCompareNatureById(Integer rowId)
    {
        return storedCompareNatureMapper.selectStoredCompareNatureById(rowId);
    }

    /**
     * 查询省平台-数据同比（进出口、过境、回程）---自然列列表
     *
     * @param storedCompareNature 省平台-数据同比（进出口、过境、回程）---自然列信息
     * @return 省平台-数据同比（进出口、过境、回程）---自然列集合
     */
    @Override
    public List<StoredCompareNature> selectStoredCompareNatureList(StoredCompareNature storedCompareNature)
    {
        if(storedCompareNature!=null && (storedCompareNature.getDate() ==null || "".equals(storedCompareNature.getDate()))){
            String date= LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
            storedCompareNature.setDate(date);
        }
        String year = storedCompareNature.getDate().split("-")[0];
        StoredCompareNature scn = new StoredCompareNature();
        scn.setYear(year);

        List<StoredCompareNature> allList = new ArrayList<>();
        int month = Integer.parseInt(storedCompareNature.getDate().split("-")[1]);
        for(int i = 1; i <= month; i++){
            String date = "";
            if(i > 9){
                date = year + "-" + i;
            }else{
                date = year + "-0" + i;
            }
            scn.setDate(date);
            List<StoredCompareNature> list = storedCompareNatureMapper.selectStoredCompareNatureList(scn);
            if(list != null && list.size() > 0){
                for (StoredCompareNature s:list
                ) {
                    allList.add(s);
                }
            }
        }
        return allList;
    }


    /**
     * 分页模糊查询省平台-数据同比（进出口、过境、回程）---自然列列表
     * @return 省平台-数据同比（进出口、过境、回程）---自然列集合
     */
    @Override
    public Page selectStoredCompareNatureListByLike(Query query)
    {
        StoredCompareNature storedCompareNature =  BeanUtil.mapToBean(query.getCondition(), StoredCompareNature.class,false);
        query.setRecords(storedCompareNatureMapper.selectStoredCompareNatureListByLike(query,storedCompareNature));
        return query;
    }

    /**
     * 新增省平台-数据同比（进出口、过境、回程）---自然列
     *
     * @param storedCompareNature 省平台-数据同比（进出口、过境、回程）---自然列信息
     * @return 结果
     */
    @Override
    public int insertStoredCompareNature(StoredCompareNature storedCompareNature)
    {
        return storedCompareNatureMapper.insertStoredCompareNature(storedCompareNature);
    }

    /**
     * 修改省平台-数据同比（进出口、过境、回程）---自然列
     *
     * @param storedCompareNature 省平台-数据同比（进出口、过境、回程）---自然列信息
     * @return 结果
     */
    @Override
    public int updateStoredCompareNature(StoredCompareNature storedCompareNature)
    {
        return storedCompareNatureMapper.updateStoredCompareNature(storedCompareNature);
    }


    /**
     * 删除省平台-数据同比（进出口、过境、回程）---自然列
     *
     * @param rowId 省平台-数据同比（进出口、过境、回程）---自然列ID
     * @return 结果
     */
    public int deleteStoredCompareNatureById(Integer rowId)
    {
        return storedCompareNatureMapper.deleteStoredCompareNatureById( rowId);
    };


    /**
     * 批量删除省平台-数据同比（进出口、过境、回程）---自然列对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteStoredCompareNatureByIds(Integer[] rowIds)
    {
        return storedCompareNatureMapper.deleteStoredCompareNatureByIds( rowIds);
    }

}
