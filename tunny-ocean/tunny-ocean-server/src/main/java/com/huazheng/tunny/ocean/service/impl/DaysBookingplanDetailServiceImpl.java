package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.UserInfo;
import com.huazheng.tunny.ocean.mapper.DaysBookingplanDetailMapper;
import com.huazheng.tunny.ocean.api.entity.DaysBookingplanDetail;
import com.huazheng.tunny.ocean.service.DaysBookingplanDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service("daysBookingplanDetailService")
public class DaysBookingplanDetailServiceImpl extends ServiceImpl<DaysBookingplanDetailMapper, DaysBookingplanDetail> implements DaysBookingplanDetailService {

    @Autowired
    private DaysBookingplanDetailMapper daysBookingplanDetailMapper;

    /**
     * 查询旬/周计划申请子表(订舱客户提交到市平台)信息
     *
     * @param rowId 旬/周计划申请子表(订舱客户提交到市平台)ID
     * @return 旬/周计划申请子表(订舱客户提交到市平台)信息
     */
    @Override
    public DaysBookingplanDetail selectDaysBookingplanDetailById(String rowId)
    {
        return daysBookingplanDetailMapper.selectDaysBookingplanDetailById(rowId);
    }

    /**
     * 查询旬/周计划申请子表(订舱客户提交到市平台)列表
     *
     * @param daysBookingplanDetail 旬/周计划申请子表(订舱客户提交到市平台)信息
     * @return 旬/周计划申请子表(订舱客户提交到市平台)集合
     */
    @Override
    public List<DaysBookingplanDetail> selectDaysBookingplanDetailList(DaysBookingplanDetail daysBookingplanDetail)
    {
        return daysBookingplanDetailMapper.selectDaysBookingplanDetailList(daysBookingplanDetail);
    }


    /**
     * 分页模糊查询旬/周计划申请子表(订舱客户提交到市平台)列表
     * @return 旬/周计划申请子表(订舱客户提交到市平台)集合
     */
    @Override
    public Page selectDaysBookingplanDetailListByLike(Query query)
    {
        DaysBookingplanDetail daysBookingplanDetail =  BeanUtil.mapToBean(query.getCondition(), DaysBookingplanDetail.class,false);
        Integer c= daysBookingplanDetailMapper.queryCount(daysBookingplanDetail);
        if(c!=null&&c!=0){
            query.setTotal(c);
            query.setRecords(daysBookingplanDetailMapper.selectDaysBookingplanDetailListByLike(query,daysBookingplanDetail));
        }
        return query;
    }

    /**
     * 新增旬/周计划申请子表(订舱客户提交到市平台)
     *
     * @param daysBookingplanDetail 旬/周计划申请子表(订舱客户提交到市平台)信息
     * @return 结果
     */
    @Override
    public int insertDaysBookingplanDetail(DaysBookingplanDetail daysBookingplanDetail)
    {
        return daysBookingplanDetailMapper.insertDaysBookingplanDetail(daysBookingplanDetail);
    }

    /**
     * 删除旬/周计划申请子表(订舱客户提交到市平台)
     *
     * @param daysBookingplanDetail 旬/周计划申请子表(订舱客户提交到市平台)信息
     * @return 结果
     */
    @Override
    public int updateDaysBookingplanDetail(DaysBookingplanDetail daysBookingplanDetail)
    {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        daysBookingplanDetail.setDeleteWhoName(username);
        daysBookingplanDetail.setDeleteWho(usercode);
        daysBookingplanDetail.setDeleteTime(LocalDateTime.now());
        daysBookingplanDetail.setDeleteFlag("Y");
        List<DaysBookingplanDetail> list =new ArrayList<>();
        list.add(daysBookingplanDetail);
        return daysBookingplanDetailMapper.updateDaysBookingplanDetail(list);
    }


    /**
     * 删除旬/周计划申请子表(订舱客户提交到市平台)
     *
     * @param rowId 旬/周计划申请子表(订舱客户提交到市平台)ID
     * @return 结果
     */
    @Override
    public int deleteDaysBookingplanDetailById(String rowId)
    {
        return daysBookingplanDetailMapper.deleteDaysBookingplanDetailById( rowId);
    };


    /**
     * 批量删除旬/周计划申请子表(订舱客户提交到市平台)对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteDaysBookingplanDetailByIds(Integer[] rowIds)
    {
        return daysBookingplanDetailMapper.deleteDaysBookingplanDetailByIds( rowIds);
    }

}
