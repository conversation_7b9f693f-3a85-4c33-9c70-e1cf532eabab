package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.BasChangeboxDetailMapper;
import com.huazheng.tunny.ocean.api.entity.BasChangeboxDetail;
import com.huazheng.tunny.ocean.service.BasChangeboxDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("basChangeboxDetailService")
public class BasChangeboxDetailServiceImpl extends ServiceImpl<BasChangeboxDetailMapper, BasChangeboxDetail> implements BasChangeboxDetailService {

    @Autowired
    private BasChangeboxDetailMapper basChangeboxDetailMapper;

    public BasChangeboxDetailMapper getBasChangeboxDetailMapper() {
        return basChangeboxDetailMapper;
    }

    public void setBasChangeboxDetailMapper(BasChangeboxDetailMapper basChangeboxDetailMapper) {
        this.basChangeboxDetailMapper = basChangeboxDetailMapper;
    }

    /**
     * 查询换箱申请箱号信息表信息
     *
     * @param rowId 换箱申请箱号信息表ID
     * @return 换箱申请箱号信息表信息
     */
    @Override
    public BasChangeboxDetail selectBasChangeboxDetailById(String rowId)
    {
        return basChangeboxDetailMapper.selectBasChangeboxDetailById(rowId);
    }

    /**
     * 查询换箱申请箱号信息表列表
     *
     * @param basChangeboxDetail 换箱申请箱号信息表信息
     * @return 换箱申请箱号信息表集合
     */
    @Override
    public List<BasChangeboxDetail> selectBasChangeboxDetailList(BasChangeboxDetail basChangeboxDetail)
    {
        return basChangeboxDetailMapper.selectBasChangeboxDetailList(basChangeboxDetail);
    }


    /**
     * 分页模糊查询换箱申请箱号信息表列表
     * @return 换箱申请箱号信息表集合
     */
    @Override
    public Page selectBasChangeboxDetailListByLike(Query query)
    {
        BasChangeboxDetail basChangeboxDetail =  BeanUtil.mapToBean(query.getCondition(), BasChangeboxDetail.class,false);
        query.setRecords(basChangeboxDetailMapper.selectBasChangeboxDetailListByLike(query,basChangeboxDetail));
        return query;
    }

    /**
     * 新增换箱申请箱号信息表
     *
     * @param basChangeboxDetail 换箱申请箱号信息表信息
     * @return 结果
     */
    @Override
    public int insertBasChangeboxDetail(BasChangeboxDetail basChangeboxDetail)
    {
        return basChangeboxDetailMapper.insertBasChangeboxDetail(basChangeboxDetail);
    }

    /**
     * 修改换箱申请箱号信息表
     *
     * @param basChangeboxDetail 换箱申请箱号信息表信息
     * @return 结果
     */
    @Override
    public int updateBasChangeboxDetail(BasChangeboxDetail basChangeboxDetail)
    {
        return basChangeboxDetailMapper.updateBasChangeboxDetail(basChangeboxDetail);
    }


    /**
     * 删除换箱申请箱号信息表
     *
     * @param basChangeboxDetail 换箱申请箱号信息表ID
     * @return 结果
     */
    public int deleteBasChangeboxDetailById(BasChangeboxDetail basChangeboxDetail)
    {
        return basChangeboxDetailMapper.deleteBasChangeboxDetailById( basChangeboxDetail);
    };


    /**
     * 批量删除换箱申请箱号信息表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBasChangeboxDetailByIds(Integer[] rowIds)
    {
        return basChangeboxDetailMapper.deleteBasChangeboxDetailByIds( rowIds);
    }

}
