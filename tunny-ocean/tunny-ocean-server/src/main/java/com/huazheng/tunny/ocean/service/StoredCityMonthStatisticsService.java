package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.StoredCityMonthStatistics;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 各地市汇总表-单月统计 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-09-29 16:53:39
 */
public interface StoredCityMonthStatisticsService extends IService<StoredCityMonthStatistics> {
    /**
     * 查询各地市汇总表-单月统计信息
     *
     * @param rowId 各地市汇总表-单月统计ID
     * @return 各地市汇总表-单月统计信息
     */
    public StoredCityMonthStatistics selectStoredCityMonthStatisticsById(Integer rowId);

    /**
     * 查询各地市汇总表-单月统计列表
     *
     * @param storedCityMonthStatistics 各地市汇总表-单月统计信息
     * @return 各地市汇总表-单月统计集合
     */
    public List<StoredCityMonthStatistics> selectStoredCityMonthStatisticsList(StoredCityMonthStatistics storedCityMonthStatistics);


    /**
     * 分页模糊查询各地市汇总表-单月统计列表
     * @return 各地市汇总表-单月统计集合
     */
    public Page selectStoredCityMonthStatisticsListByLike(Query query);



    /**
     * 新增各地市汇总表-单月统计
     *
     * @param storedCityMonthStatistics 各地市汇总表-单月统计信息
     * @return 结果
     */
    public int insertStoredCityMonthStatistics(StoredCityMonthStatistics storedCityMonthStatistics);

    /**
     * 修改各地市汇总表-单月统计
     *
     * @param storedCityMonthStatistics 各地市汇总表-单月统计信息
     * @return 结果
     */
    public int updateStoredCityMonthStatistics(StoredCityMonthStatistics storedCityMonthStatistics);

    /**
     * 删除各地市汇总表-单月统计
     *
     * @param rowId 各地市汇总表-单月统计ID
     * @return 结果
     */
    public int deleteStoredCityMonthStatisticsById(Integer rowId);

    /**
     * 批量删除各地市汇总表-单月统计
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteStoredCityMonthStatisticsByIds(Integer[] rowIds);

}

