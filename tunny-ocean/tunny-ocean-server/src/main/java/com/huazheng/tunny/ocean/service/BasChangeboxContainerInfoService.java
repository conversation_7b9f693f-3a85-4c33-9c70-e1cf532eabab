package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.BasChangeboxContainerInfo;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;
import java.util.Map;

/**
 * 换箱集装箱信息表 服务接口层
 *
 * <AUTHOR>
 * @date 2024-06-18 13:15:02
 */
public interface BasChangeboxContainerInfoService extends IService<BasChangeboxContainerInfo> {
    /**
     * 查询换箱集装箱信息表信息
     *
     * @param rowId 换箱集装箱信息表ID
     * @return 换箱集装箱信息表信息
     */
    public BasChangeboxContainerInfo selectBasChangeboxContainerInfoById(String rowId);

    /**
     * 查询换箱集装箱信息表列表
     *
     * @param basChangeboxContainerInfo 换箱集装箱信息表信息
     * @return 换箱集装箱信息表集合
     */
    public List<BasChangeboxContainerInfo> selectBasChangeboxContainerInfoList(BasChangeboxContainerInfo basChangeboxContainerInfo);


    /**
     * 分页模糊查询换箱集装箱信息表列表
     * @return 换箱集装箱信息表集合
     */
    public Page selectBasChangeboxContainerInfoListByLike(Query query);



    /**
     * 新增换箱集装箱信息表
     *
     * @param basChangeboxContainerInfo 换箱集装箱信息表信息
     * @return 结果
     */
    public R insertBasChangeboxContainerInfo(BasChangeboxContainerInfo basChangeboxContainerInfo);

    /**
     * 修改换箱集装箱信息表
     *
     * @param basChangeboxContainerInfo 换箱集装箱信息表信息
     * @return 结果
     */
    public R updateBasChangeboxContainerInfo(BasChangeboxContainerInfo basChangeboxContainerInfo);

    public int deleteObj(BasChangeboxContainerInfo basChangeboxContainerInfo);

    public R changeBoxnumberStatus(BasChangeboxContainerInfo basChangeboxContainerInfo);

    /**
     * 删除换箱集装箱信息表
     *
     * @param rowId 换箱集装箱信息表ID
     * @return 结果
     */
    public int deleteBasChangeboxContainerInfoById(String rowId);

    /**
     * 批量删除换箱集装箱信息表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteBasChangeboxContainerInfoByIds(Integer[] rowIds);

    /**
     * 当前运单下集装箱、货物、参与方分页信息
     */
    public List<BasChangeboxContainerInfo> selectConGoodsPantsAdd(BasChangeboxContainerInfo basChangeboxContainerInfo);

    public List<BasChangeboxContainerInfo> selectConGoodsPantsDel(BasChangeboxContainerInfo basChangeboxContainerInfo);
}

