package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.DayPlanApplyCityToPro;
import com.huazheng.tunny.ocean.api.entity.DayPlanDetailCityToPro;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 旬/周计划申请子表(市平台提交到省平台) 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-07 11:49:09
 */
public interface DayPlanDetailCityToProService extends IService<DayPlanDetailCityToPro> {
    /**
     * 查询旬/周计划申请子表(市平台提交到省平台)信息
     *
     * @param rowId 旬/周计划申请子表(市平台提交到省平台)ID
     * @return 旬/周计划申请子表(市平台提交到省平台)信息
     */
    public DayPlanDetailCityToPro selectDayPlanDetailCityToProById(String rowId);

    /**
     * 查询旬/周计划申请子表(市平台提交到省平台)列表
     *
     * @param dayPlanDetailCityToPro 旬/周计划申请子表(市平台提交到省平台)信息
     * @return 旬/周计划申请子表(市平台提交到省平台)集合
     */
    public List<DayPlanDetailCityToPro> selectDayPlanDetailCityToProList(DayPlanDetailCityToPro dayPlanDetailCityToPro);


    /**
     * 分页模糊查询旬/周计划申请子表(市平台提交到省平台)列表
     * @return 旬/周计划申请子表(市平台提交到省平台)集合
     */
    public Page selectDayPlanDetailCityToProListByLike(Query query);



    /**
     * 新增旬/周计划申请子表(市平台提交到省平台)
     *
     * @param dayPlanDetailCityToPro 旬/周计划申请子表(市平台提交到省平台)信息
     * @return 结果
     */
    public int insertDayPlanDetailCityToPro(DayPlanDetailCityToPro dayPlanDetailCityToPro);

    /**
     * 修改旬/周计划申请子表(市平台提交到省平台)
     *
     * @param dayPlanDetailCityToPro 旬/周计划申请子表(市平台提交到省平台)信息
     * @return 结果
     */
    public int updateDayPlanDetailCityToPro(DayPlanDetailCityToPro dayPlanDetailCityToPro);

    public int updateDayPlanDetailCityToProByNo(DayPlanDetailCityToPro dayPlanDetailCityToPro);

    /**
     * 删除旬/周计划申请子表(市平台提交到省平台)
     *
     * @param dayPlanDetailCityToPro 旬/周计划申请子表(市平台提交到省平台)ID
     * @return 结果
     */
    public int deleteDayPlanDetailCityToProById(DayPlanDetailCityToPro dayPlanDetailCityToPro);

    /**
     * 批量删除旬/周计划申请子表(市平台提交到省平台)
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDayPlanDetailCityToProByIds(Integer[] rowIds);

    public List<DayPlanDetailCityToPro> selectListByDate(DayPlanDetailCityToPro dayPlanDetailCityToPro);
}

