package com.huazheng.tunny.ocean.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.FdBusCostDetailDTO;
import com.huazheng.tunny.ocean.api.dto.ShiftManagementDTO;
import com.huazheng.tunny.ocean.api.entity.SupplierInfo;
import com.huazheng.tunny.ocean.service.SupplierInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 供应商管理
 *
 * <AUTHOR>
 * @date 2024-07-18 13:19:24
 */
@Slf4j
@RestController
@RequestMapping("/supplierinfo")
public class SupplierInfoController {

    @Autowired
    private SupplierInfoService supplierInfoService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page<SupplierInfo> page(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return supplierInfoService.selectSupplierInfoListByLike(new Query<>(params));
    }

    /**
     * 信息
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R<SupplierInfo> info(@PathVariable("id") Integer id) {
        SupplierInfo supplierInfo =supplierInfoService.selectSupplierInfoById(id);
        return new R<>(supplierInfo);
    }

    /**
     * 保存
     * @param supplierInfo
     * @return R
     */
    @PostMapping("/save")
    public R save(@RequestBody SupplierInfo supplierInfo) {
        return supplierInfoService.insertSupplierInfo(supplierInfo);
    }

    /**
     * 修改
     * @param supplierInfo
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody SupplierInfo supplierInfo) {
        return supplierInfoService.updateSupplierInfo(supplierInfo);
    }

    /**
     * 审核
     * @param supplierInfo
     * @return R
     */
    @PostMapping("/audit")
    public R audit(@RequestBody SupplierInfo supplierInfo) {
        return supplierInfoService.audit(supplierInfo);
    }

    /**
     * 获取收款人列表
     * @param supplierInfo
     * @return R
     */
    @PostMapping("/getReceiveList")
    public List<SupplierInfo> getReceiveList(@RequestBody SupplierInfo supplierInfo) {
        return supplierInfoService.getReceiveList(supplierInfo);
    }

    

    /**
     * 删除
     * @param id
     * @return R
     */
    @GetMapping("/del/{id}")
    public R delete(@PathVariable  Integer id) {
        return supplierInfoService.deleteSupplierInfoById(id);
    }

    /**
     * 批量删除
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<Integer> ids) {
        supplierInfoService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<SupplierInfo> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<SupplierInfo> list = supplierInfoService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), SupplierInfo.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    @Transactional
    public R imported(String titleStr, MultipartFile file) throws Exception {
        
        return new R<>(Boolean.TRUE);
    }

    /**
     *  供应商统计表
     * @param params
     * @return
     */
    @GetMapping("/supplierInfoForm")
    public Page<List<SupplierInfo>> supplierInfoForm(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return supplierInfoService.supplierInfoForm(new Query<>(params));
    }

    /**
     *  供应商统计表二级
     * @param params
     * @return
     */
    @GetMapping("/supplierInfoFormSecond")
    public Page<List<ShiftManagementDTO>> supplierInfoFormSecond(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return supplierInfoService.supplierInfoFormSecond(new Query<>(params));
    }

    /**
     *  供应商统计表三级
     * @param fdBusCostDetailDTO
     * @return
     */
    @PostMapping("/supplierInfoFormThird")
    public R<List<FdBusCostDetailDTO>> supplierInfoFormThird(@RequestBody FdBusCostDetailDTO fdBusCostDetailDTO) {
        //对象模糊查询
        List<FdBusCostDetailDTO> fdBusCostDetailDTOS = supplierInfoService.supplierInfoFormThird(fdBusCostDetailDTO);
        return new R<>(fdBusCostDetailDTOS);
    }
}
