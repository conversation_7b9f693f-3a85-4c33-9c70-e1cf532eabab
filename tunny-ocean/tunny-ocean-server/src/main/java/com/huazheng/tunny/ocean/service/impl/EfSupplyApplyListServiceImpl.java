package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.EfSupplyApplyListMapper;
import com.huazheng.tunny.ocean.api.entity.EfSupplyApplyList;
import com.huazheng.tunny.ocean.service.EfSupplyApplyListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("efSupplyApplyListService")
public class EfSupplyApplyListServiceImpl extends ServiceImpl<EfSupplyApplyListMapper, EfSupplyApplyList> implements EfSupplyApplyListService {

    @Autowired
    private EfSupplyApplyListMapper efSupplyApplyListMapper;

    public EfSupplyApplyListMapper getEfSupplyApplyListMapper() {
        return efSupplyApplyListMapper;
    }

    public void setEfSupplyApplyListMapper(EfSupplyApplyListMapper efSupplyApplyListMapper) {
        this.efSupplyApplyListMapper = efSupplyApplyListMapper;
    }

    /**
     * 查询补充质押详情表信息
     *
     * @param rowId 补充质押详情表ID
     * @return 补充质押详情表信息
     */
    @Override
    public EfSupplyApplyList selectEfSupplyApplyListById(String rowId)
    {
        return efSupplyApplyListMapper.selectEfSupplyApplyListById(rowId);
    }

    /**
     * 查询补充质押详情表列表
     *
     * @param efSupplyApplyList 补充质押详情表信息
     * @return 补充质押详情表集合
     */
    @Override
    public List<EfSupplyApplyList> selectEfSupplyApplyListList(EfSupplyApplyList efSupplyApplyList)
    {
        return efSupplyApplyListMapper.selectEfSupplyApplyListList(efSupplyApplyList);
    }


    /**
     * 分页模糊查询补充质押详情表列表
     * @return 补充质押详情表集合
     */
    @Override
    public Page selectEfSupplyApplyListListByLike(Query query)
    {
        EfSupplyApplyList efSupplyApplyList =  BeanUtil.mapToBean(query.getCondition(), EfSupplyApplyList.class,false);
        query.setRecords(efSupplyApplyListMapper.selectEfSupplyApplyListListByLike(query,efSupplyApplyList));
        return query;
    }

    /**
     * 新增补充质押详情表
     *
     * @param efSupplyApplyList 补充质押详情表信息
     * @return 结果
     */
    @Override
    public int insertEfSupplyApplyList(EfSupplyApplyList efSupplyApplyList)
    {
        return efSupplyApplyListMapper.insertEfSupplyApplyList(efSupplyApplyList);
    }

    /**
     * 修改补充质押详情表
     *
     * @param efSupplyApplyList 补充质押详情表信息
     * @return 结果
     */
    @Override
    public int updateEfSupplyApplyList(EfSupplyApplyList efSupplyApplyList)
    {
        return efSupplyApplyListMapper.updateEfSupplyApplyList(efSupplyApplyList);
    }


    /**
     * 删除补充质押详情表
     *
     * @param rowId 补充质押详情表ID
     * @return 结果
     */
    public int deleteEfSupplyApplyListById(String rowId)
    {
        return efSupplyApplyListMapper.deleteEfSupplyApplyListById( rowId);
    };


    /**
     * 批量删除补充质押详情表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfSupplyApplyListByIds(Integer[] rowIds)
    {
        return efSupplyApplyListMapper.deleteEfSupplyApplyListByIds( rowIds);
    }

}
