package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.mapper.MonthBookingplanDetailMapper;
import com.huazheng.tunny.ocean.api.entity.MonthBookingplanDetail;
import com.huazheng.tunny.ocean.service.MonthBookingplanDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.time.LocalDateTime;
import java.util.List;

@Service("monthBookingplanDetailService")
public class MonthBookingplanDetailServiceImpl extends ServiceImpl<MonthBookingplanDetailMapper, MonthBookingplanDetail> implements MonthBookingplanDetailService {

    @Autowired
    private MonthBookingplanDetailMapper monthBookingplanDetailMapper;

    public MonthBookingplanDetailMapper getMonthBookingplanDetailMapper() {
        return monthBookingplanDetailMapper;
    }

    public void setMonthBookingplanDetailMapper(MonthBookingplanDetailMapper monthBookingplanDetailMapper) {
        this.monthBookingplanDetailMapper = monthBookingplanDetailMapper;
    }

    /**
     * 查询月计划申请子表(订舱客户提交到市平台)信息
     *
     * @param rowId 月计划申请子表(订舱客户提交到市平台)ID
     * @return 月计划申请子表(订舱客户提交到市平台)信息
     */
    @Override
    public MonthBookingplanDetail selectMonthBookingplanDetailById(String rowId)
    {
        return monthBookingplanDetailMapper.selectMonthBookingplanDetailById(rowId);
    }

    /**
     * 查询月计划申请子表(订舱客户提交到市平台)列表
     *
     * @param monthBookingplanDetail 月计划申请子表(订舱客户提交到市平台)信息
     * @return 月计划申请子表(订舱客户提交到市平台)集合
     */
    @Override
    public List<MonthBookingplanDetail> selectMonthBookingplanDetailList(MonthBookingplanDetail monthBookingplanDetail)
    {
        return monthBookingplanDetailMapper.selectMonthBookingplanDetailList(monthBookingplanDetail);
    }


    /**
     * 分页模糊查询月计划申请子表(订舱客户提交到市平台)列表
     * @return 月计划申请子表(订舱客户提交到市平台)集合
     */
    @Override
    public Page selectMonthBookingplanDetailListByLike(Query query)
    {
        MonthBookingplanDetail monthBookingplanDetail =  BeanUtil.mapToBean(query.getCondition(), MonthBookingplanDetail.class,false);
        Integer c= monthBookingplanDetailMapper.queryCount(monthBookingplanDetail);
        if(c!=null&&c!=0){
            query.setTotal(c);
            query.setRecords(monthBookingplanDetailMapper.selectMonthBookingplanDetailListByLike(query, monthBookingplanDetail));
        }
        return query;
    }

    /**
     * 新增月计划申请子表(订舱客户提交到市平台)
     *
     * @param monthBookingplanDetail 月计划申请子表(订舱客户提交到市平台)信息
     * @return 结果
     */
    @Override
    public int insertMonthBookingplanDetail(MonthBookingplanDetail monthBookingplanDetail)
    {
        return monthBookingplanDetailMapper.insertMonthBookingplanDetail(monthBookingplanDetail);
    }

    /**
     * 删除月计划申请子表(订舱客户提交到市平台)
     *
     * @param monthBookingplanDetail 月计划申请子表(订舱客户提交到市平台)信息
     * @return 结果
     */
    @Override
    public int updateMonthBookingplanDetail(MonthBookingplanDetail monthBookingplanDetail)
    {
        SecruityUser userInfo= SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getUserName();
        MonthBookingplanDetail mbd = new MonthBookingplanDetail();
        mbd.setDeleteWhoName(username);
        mbd.setDeleteWho(usercode);
        mbd.setDeleteTime(LocalDateTime.now());
        mbd.setDeleteFlag("Y");
        return monthBookingplanDetailMapper.updateMonthBookingplanDetail(monthBookingplanDetail);
    }


    /**
     * 删除月计划申请子表(订舱客户提交到市平台)
     *
     * @param rowId 月计划申请子表(订舱客户提交到市平台)ID
     * @return 结果
     */
    @Override
    public int deleteMonthBookingplanDetailById(String rowId)
    {
        return monthBookingplanDetailMapper.deleteMonthBookingplanDetailById( rowId);
    };


    /**
     * 批量删除月计划申请子表(订舱客户提交到市平台)对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteMonthBookingplanDetailByIds(Integer[] rowIds)
    {
        return monthBookingplanDetailMapper.deleteMonthBookingplanDetailByIds( rowIds);
    }

}
