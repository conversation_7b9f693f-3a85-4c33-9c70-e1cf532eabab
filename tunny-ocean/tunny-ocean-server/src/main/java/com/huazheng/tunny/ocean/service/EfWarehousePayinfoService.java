package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfWarehousePayinfo;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 融资放款信息 服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-04 10:08:34
 */
public interface EfWarehousePayinfoService extends IService<EfWarehousePayinfo> {
    /**
     * 查询融资放款信息信息
     *
     * @param rowId 融资放款信息ID
     * @return 融资放款信息信息
     */
    public EfWarehousePayinfo selectEfWarehousePayinfoById(String rowId);

    /**
     * 查询融资放款信息列表
     *
     * @param efWarehousePayinfo 融资放款信息信息
     * @return 融资放款信息集合
     */
    public List<EfWarehousePayinfo> selectEfWarehousePayinfoList(EfWarehousePayinfo efWarehousePayinfo);

    public List<EfWarehousePayinfo> selectEfWarehousePaySum(EfWarehousePayinfo efWarehousePayinfo);

    /**
     * 分页模糊查询融资放款信息列表
     * @return 融资放款信息集合
     */
    public Page selectEfWarehousePayinfoListByLike(Query query);



    /**
     * 新增融资放款信息
     *
     * @param efWarehousePayinfo 融资放款信息信息
     * @return 结果
     */
    public int insertEfWarehousePayinfo(EfWarehousePayinfo efWarehousePayinfo);

    /**
     * 修改融资放款信息
     *
     * @param efWarehousePayinfo 融资放款信息信息
     * @return 结果
     */
    public int updateEfWarehousePayinfo(EfWarehousePayinfo efWarehousePayinfo);

    /**
     * 删除融资放款信息
     *
     * @param rowId 融资放款信息ID
     * @return 结果
     */
    public int deleteEfWarehousePayinfoById(String rowId);

    /**
     * 批量删除融资放款信息
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfWarehousePayinfoByIds(Integer[] rowIds);

}

