package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.BasOrg;
import com.huazheng.tunny.ocean.api.entity.CustomerInfo;
import com.huazheng.tunny.ocean.api.entity.StationPlatformRelation;
import com.huazheng.tunny.ocean.api.vo.CustomerInfoVO;
import com.huazheng.tunny.ocean.mapper.BasOrgMapper;
import com.huazheng.tunny.ocean.mapper.StationManagementMapper;
import com.huazheng.tunny.ocean.api.entity.StationManagement;
import com.huazheng.tunny.ocean.mapper.StationPlatformRelationMapper;
import com.huazheng.tunny.ocean.service.StationManagementService;
import com.huazheng.tunny.ocean.service.SysNoConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service("stationManagementService")
public class StationManagementServiceImpl extends ServiceImpl<StationManagementMapper, StationManagement> implements StationManagementService {

    @Autowired
    private StationManagementMapper stationManagementMapper;

    @Autowired
    private StationPlatformRelationMapper stationPlatformRelationMapper;

    @Autowired
    private BasOrgMapper basOrgMapper;

    @Autowired
    private SysNoConfigService sysNoConfigService;

    /**
     * 查询站点及关联机构信息
     *
     * @param stationCode 线路管信息
     */
    @Override
    public StationManagement selectPlatByStationCode(String stationCode) {
        return stationManagementMapper.selectPlatByStationCode(stationCode);
    }

    /**
     * 查询站点管理信息
     *
     * @param rowId 站点管理ID
     * @return 站点管理信息
     */
    @Override
    public StationManagement selectStationManagementById(String rowId)
    {
        return stationManagementMapper.selectStationManagementById(rowId);
    }

    /**
     * 查询站点管理列表
     *
     * @param stationManagement 站点管理信息
     * @return 站点管理集合
     */
    @Override
    public List<StationManagement> selectStationManagementList(StationManagement stationManagement)
    {
        return stationManagementMapper.selectStationManagementList(stationManagement);
    }

    @Override
    public List<StationManagement> selectStationManagementListByStationCode(StationManagement stationManagement) {
        return stationManagementMapper.selectStationManagementListByStationCode(stationManagement);
    }


    /**
     * 分页模糊查询站点管理列表
     * @return 站点管理集合
     */
    @Override
    public Page selectStationManagementListByLike(Query query)
    {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.add_time");
            query.setAsc(Boolean.FALSE);
        }
        StationManagement stationManagement =  BeanUtil.mapToBean(query.getCondition(), StationManagement.class,false);
        Long startTime = System.currentTimeMillis();
        query.setRecords(stationManagementMapper.selectStationManagementListByLike(query, stationManagement));
        return query;
    }

    /**
     * 新增站点及关联平台信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R insertStationManagement(StationManagement stationManagement)
    {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();

        StationPlatformRelation spr=new StationPlatformRelation();
        if(stationManagement.getBasOrg()!=null&&stationManagement.getBasOrg().size()!=0){
            spr.setStationCode(stationManagement.getStationCode());
        }else{
            return new R(500,Boolean.FALSE,"未维护平台信息");
        }

        List<StationPlatformRelation>listSpr =new ArrayList<>();/*组装组织（平台）信息list*/
        for (CustomerInfo org:stationManagement.getBasOrg()){
            StationPlatformRelation spr2=new StationPlatformRelation();
            spr2.setPlatformCode(org.getCustomerCode());
            spr2.setStationCode(stationManagement.getStationCode());
            spr2.setStationName(stationManagement.getStationName());
            spr2.setAddTime(LocalDateTime.now());
            spr2.setAddWho(usercode);
            spr2.setAddWhoName(username);
            listSpr.add(spr2);
        }
        try {
            stationPlatformRelationMapper.deleteRelationByCode(spr);
            stationPlatformRelationMapper.insertStationPlatformRelation(listSpr);
            if(stationManagement.getFlag()!=null&& "T".equals(stationManagement.getFlag())){
                stationManagement.setUpdateWho(usercode);
                stationManagement.setUpdateWhoName(username);
                stationManagement.setUpdateTime(LocalDateTime.now());
                List<StationManagement> list=new ArrayList<>();
                list.add(stationManagement);
                stationManagementMapper.updateStationManagement(list);
            }else{
                List list0=stationManagementMapper.selectStationManagementList(stationManagement);
                if(list0==null||list0.size()==0){}else{
                    System.out.println("@@@@@@站点信息已维护！@@@@@@");
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return new R(500,Boolean.FALSE,"站点信息已维护");
                }
                stationManagement.setAddTime(LocalDateTime.now());
                stationManagement.setAddWho(usercode);
                stationManagement.setAddWhoName(username);
                stationManagementMapper.insertStationManagement(stationManagement);
            }
        } catch (Exception e) {
            System.out.println(e.getMessage() + "@@@@@@操作失败！@@@@@@");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new R(500,Boolean.FALSE,"操作失败！");
        }
        return new R(200,Boolean.TRUE,"操作成功！");
    }

    @Override
    public int insertInfoBatch(List<StationManagement> stationManagement) {
        return stationManagementMapper.insertStationBatch(stationManagement);
    }

    /**
     * 修改站点管理
     *
     * @param stationCode 站点管理信息
     * @return 结果
     */
    @Override
    public R updateStationManagement(String[] stationCode)
    {
        if(stationCode==null||stationCode[0]==null||stationCode.length==0){
            return new R(Boolean.FALSE,"至少选中一个站点");
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        List<StationManagement> list=new ArrayList<>();
        for (int i=0;i<stationCode.length;i++){
            StationManagement temp=new StationManagement();
            temp.setStationCode(stationCode[i]);
            temp.setDeleteWho(userInfo.getUserName());
            temp.setDeleteFlag("Y");
            list.add(temp);
        }
        int flag=stationManagementMapper.updateStationManagement(list);
        if(flag==0){
            return new R(Boolean.FALSE,"删除失败");
        }else{
            return new R(Boolean.TRUE,"删除成功");
        }
    }


    /**
     * 删除站点管理
     *
     * @param rowId 站点管理ID
     * @return 结果
     */
    @Override
    public int deleteStationManagementById(String rowId)
    {
        return stationManagementMapper.deleteStationManagementById( rowId);
    };


    /**
     * 批量删除站点管理对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteStationManagementByIds(Integer[] rowIds)
    {
        return stationManagementMapper.deleteStationManagementByIds( rowIds);
    }

}
