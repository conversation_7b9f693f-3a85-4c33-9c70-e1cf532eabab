package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.BasMenu;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 菜单信息管理表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-01 09:18:58
 */
public interface BasMenuService extends IService<BasMenu> {
    /**
     * 查询菜单信息管理表信息
     *
     * @param rowId 菜单信息管理表ID
     * @return 菜单信息管理表信息
     */
    public BasMenu selectBasMenuById(String rowId);

    /**
     * 查询菜单信息管理表列表
     *
     * @param basMenu 菜单信息管理表信息
     * @return 菜单信息管理表集合
     */
    public List<BasMenu> selectBasMenuList(BasMenu basMenu);


    /**
     * 分页模糊查询菜单信息管理表列表
     * @return 菜单信息管理表集合
     */
    public Page selectBasMenuListByLike(Query query);



    /**
     * 新增菜单信息管理表
     *
     * @param basMenu 菜单信息管理表信息
     * @return 结果
     */
    public int insertBasMenu(BasMenu basMenu);

    /**
     * 修改菜单信息管理表
     *
     * @param basMenu 菜单信息管理表信息
     * @return 结果
     */
    public int updateBasMenu(BasMenu basMenu);

    /**
     * 删除菜单信息管理表
     *
     * @param rowId 菜单信息管理表ID
     * @return 结果
     */
    public int deleteBasMenuById(String rowId);

    /**
     * 批量删除菜单信息管理表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteBasMenuByIds(Integer[] rowIds);

}

