package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfDisposeIntention;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.EfReleaseInfo;

import java.util.List;

/**
 * 发布处置意向 服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-10 15:32:35
 */
public interface EfDisposeIntentionService extends IService<EfDisposeIntention> {
    /**
     * 查询发布处置意向信息
     *
     * @param rowId 发布处置意向ID
     * @return 发布处置意向信息
     */
    public EfDisposeIntention selectEfDisposeIntentionById(String rowId);

    /**
     * 查询发布处置意向列表
     *
     * @param efDisposeIntention 发布处置意向信息
     * @return 发布处置意向集合
     */
    public List<EfDisposeIntention> selectEfDisposeIntentionList(EfDisposeIntention efDisposeIntention);


    /**
     * 分页模糊查询发布处置意向列表
     * @return 发布处置意向集合
     */
    public Page selectEfDisposeIntentionListByLike(Query query);

    public Page selectEfDisposeIntentionListByLike2(Query query);

    /**
     * 新增发布处置意向
     *
     * @param efDisposeIntention 发布处置意向信息
     * @return 结果
     */
    public int insertEfDisposeIntention(EfDisposeIntention efDisposeIntention);

    /**
     * 修改发布处置意向
     *
     * @param efDisposeIntention 发布处置意向信息
     * @return 结果
     */
    public int updateEfDisposeIntention(EfDisposeIntention efDisposeIntention);

    /**
     * 删除发布处置意向
     *
     * @param rowId 发布处置意向ID
     * @return 结果
     */
    public int deleteEfDisposeIntentionById(String rowId);

    /**
     * 批量删除发布处置意向
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfDisposeIntentionByIds(Integer[] rowIds);

    String publishDisposeIntention(EfDisposeIntention efDisposeIntention);
}

