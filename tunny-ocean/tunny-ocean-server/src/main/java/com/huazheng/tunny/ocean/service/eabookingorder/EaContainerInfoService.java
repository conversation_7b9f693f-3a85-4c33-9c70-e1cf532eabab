package com.huazheng.tunny.ocean.service.eabookingorder;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.dto.eabusinessprocess.EaBusinessProcessAppendDto;
import com.huazheng.tunny.ocean.api.entity.eabookingorder.EaContainerInfo;
import com.huazheng.tunny.ocean.util.R;

import java.util.List;
import java.util.Map;

/**
 * 集装箱信息表 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2025-06-18 10:59:54
 */
public interface EaContainerInfoService extends IService<EaContainerInfo> {
    /**
     * 查询集装箱信息表信息
     *
     * @param containerId 集装箱信息表ID
     * @return 集装箱信息表信息
     */
    public EaContainerInfo selectEaContainerInfoById(Long containerId);

    /**
     * 查询集装箱信息表列表
     *
     * @param params 集装箱信息表信息
     * @return 集装箱信息表集合
     */
    public R selectEaContainerInfoList(Map<String, Object> params);


    /**
     * 分页模糊查询集装箱信息表列表
     * @return 集装箱信息表集合
     */
    public Page selectEaContainerInfoListByLike(Query query);



    /**
     * 新增集装箱信息表
     *
     * @param eaContainerInfo 集装箱信息表信息
     * @return 结果
     */
    public int insertEaContainerInfo(EaContainerInfo eaContainerInfo);

    /**
     * 修改集装箱信息表
     *
     * @param eaContainerInfo 集装箱信息表信息
     * @return 结果
     */
    public int updateEaContainerInfo(EaContainerInfo eaContainerInfo);

    /**
     * 删除集装箱信息表
     *
     * @param containerId 集装箱信息表ID
     * @return 结果
     */
    public int deleteEaContainerInfoById(Long containerId);

    /**
     * 批量删除集装箱信息表
     *
     * @param containerIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEaContainerInfoByIds(Integer[] containerIds);

    List<EaContainerInfo> selectEaContainerInfoByContainerNumber(EaBusinessProcessAppendDto eaBusinessProcessAppendDto);

    R selectEaContainerInfoLists(Map<String, Object> params);
}

