package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FiWhFinancingApplyMapper;
import com.huazheng.tunny.ocean.api.entity.FiWhFinancingApply;
import com.huazheng.tunny.ocean.service.FiWhFinancingApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fiWhFinancingApplyService")
public class FiWhFinancingApplyServiceImpl extends ServiceImpl<FiWhFinancingApplyMapper, FiWhFinancingApply> implements FiWhFinancingApplyService {

    @Autowired
    private FiWhFinancingApplyMapper fiWhFinancingApplyMapper;

    public FiWhFinancingApplyMapper getFiWhFinancingApplyMapper() {
        return fiWhFinancingApplyMapper;
    }

    public void setFiWhFinancingApplyMapper(FiWhFinancingApplyMapper fiWhFinancingApplyMapper) {
        this.fiWhFinancingApplyMapper = fiWhFinancingApplyMapper;
    }

    /**
     * 查询仓单质押融资申请表信息
     *
     * @param rowId 仓单质押融资申请表ID
     * @return 仓单质押融资申请表信息
     */
    @Override
    public FiWhFinancingApply selectFiWhFinancingApplyById(String rowId)
    {
        return fiWhFinancingApplyMapper.selectFiWhFinancingApplyById(rowId);
    }

    /**
     * 查询仓单质押融资申请表列表
     *
     * @param fiWhFinancingApply 仓单质押融资申请表信息
     * @return 仓单质押融资申请表集合
     */
    @Override
    public List<FiWhFinancingApply> selectFiWhFinancingApplyList(FiWhFinancingApply fiWhFinancingApply)
    {
        return fiWhFinancingApplyMapper.selectFiWhFinancingApplyList(fiWhFinancingApply);
    }


    /**
     * 分页模糊查询仓单质押融资申请表列表
     * @return 仓单质押融资申请表集合
     */
    @Override
    public Page selectFiWhFinancingApplyListByLike(Query query)
    {
        FiWhFinancingApply fiWhFinancingApply =  BeanUtil.mapToBean(query.getCondition(), FiWhFinancingApply.class,false);
        query.setRecords(fiWhFinancingApplyMapper.selectFiWhFinancingApplyListByLike(query,fiWhFinancingApply));
        return query;
    }

    /**
     * 新增仓单质押融资申请表
     *
     * @param fiWhFinancingApply 仓单质押融资申请表信息
     * @return 结果
     */
    @Override
    public int insertFiWhFinancingApply(FiWhFinancingApply fiWhFinancingApply)
    {
        return fiWhFinancingApplyMapper.insertFiWhFinancingApply(fiWhFinancingApply);
    }

    /**
     * 修改仓单质押融资申请表
     *
     * @param fiWhFinancingApply 仓单质押融资申请表信息
     * @return 结果
     */
    @Override
    public int updateFiWhFinancingApply(FiWhFinancingApply fiWhFinancingApply)
    {
        return fiWhFinancingApplyMapper.updateFiWhFinancingApply(fiWhFinancingApply);
    }


    /**
     * 删除仓单质押融资申请表
     *
     * @param rowId 仓单质押融资申请表ID
     * @return 结果
     */
    @Override
    public int deleteFiWhFinancingApplyById(String rowId)
    {
        return fiWhFinancingApplyMapper.deleteFiWhFinancingApplyById( rowId);
    };


    /**
     * 批量删除仓单质押融资申请表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiWhFinancingApplyByIds(Integer[] rowIds)
    {
        return fiWhFinancingApplyMapper.deleteFiWhFinancingApplyByIds( rowIds);
    }

}
