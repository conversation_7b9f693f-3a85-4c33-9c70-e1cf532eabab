package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.FdBusCostDetailDTO;
import com.huazheng.tunny.ocean.api.dto.ShiftManagementDTO;
import com.huazheng.tunny.ocean.api.entity.FdBusCostDetail;
import com.huazheng.tunny.ocean.api.entity.SupplierInfo;
import com.huazheng.tunny.ocean.api.entity.UploadRecord;
import com.huazheng.tunny.ocean.api.feign.RemoteAdminService;
import com.huazheng.tunny.ocean.api.vo.SysDictVo;
import com.huazheng.tunny.ocean.mapper.FdBusCostDetailMapper;
import com.huazheng.tunny.ocean.mapper.SupplierInfoMapper;
import com.huazheng.tunny.ocean.mapper.UploadRecordMapper;
import com.huazheng.tunny.ocean.service.SupplierInfoService;
import com.huazheng.tunny.ocean.service.SysNoConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service("supplierInfoService")
public class SupplierInfoServiceImpl extends ServiceImpl<SupplierInfoMapper, SupplierInfo> implements SupplierInfoService {

    @Autowired
    private SupplierInfoMapper supplierInfoMapper;
    @Autowired
    private SysNoConfigService sysNoConfigService;
    @Autowired
    private UploadRecordMapper uploadRecordMapper;
    @Autowired
    private RemoteAdminService remoteAdminService;
    @Autowired
    private FdBusCostDetailMapper fdBusCostDetailMapper;
    /**
     * 查询供应商管理信息
     *
     * @param id 供应商管理ID
     * @return 供应商管理信息
     */
    @Override
    public SupplierInfo selectSupplierInfoById(Integer id) {
        SupplierInfo supplierInfo = supplierInfoMapper.selectSupplierInfoById(id);
        UploadRecord sel = new UploadRecord();
        sel.setBillNo(supplierInfo.getCustomerCode());
        sel.setDeleteFlag("N");
        List<UploadRecord> uploadRecords = uploadRecordMapper.selectUploadRecordList(sel);
        if (CollUtil.isNotEmpty(uploadRecords)) {
            supplierInfo.setFiles(uploadRecords);
        }
        return supplierInfo;
    }

    /**
     * 查询供应商管理列表
     *
     * @param supplierInfo 供应商管理信息
     * @return 供应商管理集合
     */
    @Override
    public List<SupplierInfo> selectSupplierInfoList(SupplierInfo supplierInfo) {
        return supplierInfoMapper.selectSupplierInfoList(supplierInfo);
    }

    /**
     * 查询供应商管理列表
     *
     * @param supplierInfo 供应商管理信息
     * @return 供应商管理集合
     */
    @Override
    public List<SupplierInfo> getReceiveList(SupplierInfo supplierInfo) {
        supplierInfo.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        supplierInfo.setPartnerShip("normal");
        List<SupplierInfo> receiveList = supplierInfoMapper.getReceiveList(supplierInfo);
        SupplierInfo addObj = new SupplierInfo();
        addObj.setCustomerCode("ztdl");
        addObj.setCustomerName("中铁国际多式联运有限公司");
        receiveList.add(addObj);
        return receiveList;
    }


    /**
     * 分页模糊查询供应商管理列表
     *
     * @return 供应商管理集合
     */
    @Override
    public Page selectSupplierInfoListByLike(Query query) {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.add_time");
            query.setAsc(Boolean.FALSE);
        }
        SupplierInfo supplierInfo = BeanUtil.mapToBean(query.getCondition(), SupplierInfo.class, false);
        /*String data = remoteAdminService.selectDictByType2("partnerShip");
        List<SysDictVo> partnerShip = JSONUtil.toList(JSONUtil.parseArray(data), SysDictVo.class);
        String data2 = remoteAdminService.selectDictByType2("supplierType");
        List<SysDictVo> supplierType = JSONUtil.toList(JSONUtil.parseArray(data2), SysDictVo.class);*/
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        supplierInfo.setPlatformCode(userInfo.getPlatformCode());
        supplierInfo.setDeleteFlag("N");
        List<SupplierInfo> supplierInfos = supplierInfoMapper.selectSupplierInfoListByLike(query, supplierInfo);
        /*if(CollUtil.isNotEmpty(supplierInfos)){
            for (SupplierInfo sup:supplierInfos
                 ) {
                if(CollUtil.isNotEmpty(partnerShip)){
                    for (SysDictVo sysDictVo:partnerShip
                    ) {
                        if(sysDictVo.getCode().equals(sup.getPartnerShip())){
                            sup.setPartnerShipName(sysDictVo.getName());
                        }
                    }
                }
                if(CollUtil.isNotEmpty(supplierType)){
                    for (SysDictVo sysDictVo:supplierType
                    ) {
                        if(sysDictVo.getCode().equals(sup.getSupplierType())){
                            sup.setSupplierTypeName(sysDictVo.getName());
                        }
                    }
                }
            }
        }*/

        query.setRecords(supplierInfos);
        return query;
    }

    /**
     * 新增供应商管理
     *
     * @param supplierInfo 供应商管理信息
     * @return 结果
     */
    @Override
    public R insertSupplierInfo(SupplierInfo supplierInfo) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        SupplierInfo sel = new SupplierInfo();
        sel.setCustomerName(supplierInfo.getCustomerName());
        sel.setPlatformCode(userInfo.getPlatformCode());
        sel.setDeleteFlag("N");
        List<SupplierInfo> supplierInfos = supplierInfoMapper.selectSupplierInfoList(sel);
        if (CollUtil.isNotEmpty(supplierInfos)) {
            return new R<>(new Throwable("该客户名称已存在"));
        }
        supplierInfo.setPlatformCode(userInfo.getPlatformCode());
        supplierInfo.setPlatformName(userInfo.getPlatformName());
        supplierInfo.setCustomerCode(sysNoConfigService.genNo("GYS"));
        supplierInfo.setAddWho(userInfo.getUserName());
        supplierInfo.setAddWhoName(userInfo.getRealName());
        supplierInfo.setAddTime(LocalDateTime.now());
        supplierInfoMapper.insertSupplierInfo(supplierInfo);
        if (CollUtil.isNotEmpty(supplierInfo.getFiles())) {
            List<UploadRecord> files = supplierInfo.getFiles();
            for (UploadRecord uploadRecord : files
            ) {
                if(StrUtil.isBlank(uploadRecord.getRowId())){
                    uploadRecord.setResveredField01(uploadRecord.getGroupName());
                    uploadRecord.setResveredField02(uploadRecord.getDelUrl());
                    uploadRecord.setBillNo(supplierInfo.getCustomerCode());
                    uploadRecord.setAddTime(LocalDateTime.now());
                    uploadRecord.setAddWho(userInfo.getUserName());
                    uploadRecord.setAddWhoName(userInfo.getRealName());
                    uploadRecordMapper.insertUploadRecord(uploadRecord);
                }

            }
        }
        return new R<>(0, Boolean.TRUE, null, "操作成功");
    }

    /**
     * 修改供应商管理
     *
     * @param supplierInfo 供应商管理信息
     * @return 结果
     */
    @Override
    public R updateSupplierInfo(SupplierInfo supplierInfo) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        SupplierInfo sel = new SupplierInfo();
        sel.setCustomerName(supplierInfo.getCustomerName());
        sel.setPlatformCode(userInfo.getPlatformCode());
        sel.setDeleteFlag("N");
        List<SupplierInfo> supplierInfos = supplierInfoMapper.selectSupplierInfoList(sel);
        if (CollUtil.isNotEmpty(supplierInfos)) {
            for (SupplierInfo supplierInfo1 : supplierInfos) {
                if (!supplierInfo1.getCustomerName().equals(supplierInfo.getCustomerName())) {
                    return new R<>(new Throwable("该客户名称已存在"));
                }
            }
        }
        supplierInfo.setPlatformCode(userInfo.getPlatformCode());
        supplierInfo.setPlatformName(userInfo.getPlatformName());
        supplierInfo.setUpdateWho(userInfo.getUserName());
        supplierInfo.setUpdateWhoName(userInfo.getRealName());
        supplierInfo.setUpdateTime(LocalDateTime.now());
        supplierInfoMapper.updateSupplierInfo(supplierInfo);
        if (CollUtil.isNotEmpty(supplierInfo.getFiles())) {
            List<UploadRecord> files = supplierInfo.getFiles();
            for (UploadRecord uploadRecord : files
            ) {
                if (StrUtil.isBlank(uploadRecord.getRowId())) {
                    uploadRecord.setBillNo(supplierInfo.getCustomerCode());
                    uploadRecord.setResveredField01(uploadRecord.getGroupName());
                    uploadRecord.setResveredField02(uploadRecord.getDelUrl());
                    uploadRecord.setAddTime(LocalDateTime.now());
                    uploadRecord.setAddWho(userInfo.getUserName());
                    uploadRecord.setAddWhoName(userInfo.getRealName());
                    uploadRecordMapper.insertUploadRecord(uploadRecord);
                }
            }
        }
        return new R<>(0, Boolean.TRUE, null, "操作成功");
    }

    @Override
    public R audit(SupplierInfo supplierInfo) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        supplierInfo.setPlatformCode(userInfo.getPlatformCode());
        supplierInfo.setPlatformName(userInfo.getPlatformName());
        supplierInfo.setStatus("1");
        supplierInfo.setUpdateWho(userInfo.getUserName());
        supplierInfo.setUpdateWhoName(userInfo.getRealName());
        supplierInfo.setUpdateTime(LocalDateTime.now());
        supplierInfoMapper.updateSupplierInfo(supplierInfo);
        return new R<>(0, Boolean.TRUE, null, "操作成功");
    }


    /**
     * 删除供应商管理
     *
     * @param id 供应商管理ID
     * @return 结果
     */
    public R deleteSupplierInfoById(Integer id) {
        SupplierInfo info = supplierInfoMapper.selectSupplierInfoById(id);
        if(info != null){
            FdBusCostDetail sel = new FdBusCostDetail();
            sel.setReceiveCode(info.getCustomerCode());
            sel.setDeleteFlag("N");
            List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList(sel);
            if(CollUtil.isNotEmpty(detailList)){
                return new R<>(new Throwable("该供应商已存在业务流程单费用明细，不允许删除"));
            }
        }

        SecruityUser userInfo = SecurityUtils.getUserInfo();
        SupplierInfo supplierInfo = new SupplierInfo();
        supplierInfo.setId(id);
        supplierInfo.setDeleteFlag("Y");
        supplierInfo.setDeleteWho(userInfo.getUserName());
        supplierInfo.setDeleteWhoName(userInfo.getRealName());
        supplierInfo.setDeleteTime(LocalDateTime.now());
        supplierInfoMapper.updateSupplierInfo(supplierInfo);
        return new R<>(0, Boolean.TRUE, null, "操作成功");
    }

    ;


    /**
     * 批量删除供应商管理对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSupplierInfoByIds(Integer[] ids) {
        return supplierInfoMapper.deleteSupplierInfoByIds(ids);
    }


    @Override
    public Page supplierInfoForm(Query query) {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.add_time");
            query.setAsc(Boolean.FALSE);
        }
        SupplierInfo supplierInfo = BeanUtil.mapToBean(query.getCondition(), SupplierInfo.class, false);
        String data = remoteAdminService.selectDictByType2("partnerShip");
        List<SysDictVo> partnerShip = JSONUtil.toList(JSONUtil.parseArray(data), SysDictVo.class);
        String data2 = remoteAdminService.selectDictByType2("supplierType");
        List<SysDictVo> supplierType = JSONUtil.toList(JSONUtil.parseArray(data2), SysDictVo.class);
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        if("1".equals(userInfo.getPlatformLevel())){
            supplierInfo.setPlatformCode(userInfo.getPlatformCode());
        }
        supplierInfo.setDeleteFlag("N");
        List<SupplierInfo> supplierInfos = supplierInfoMapper.supplierInfoForm(query, supplierInfo);
        if(CollUtil.isNotEmpty(supplierInfos)){
            for (SupplierInfo sup:supplierInfos
            ) {
                if(CollUtil.isNotEmpty(partnerShip)){
                    for (SysDictVo sysDictVo:partnerShip
                    ) {
                        if(sysDictVo.getCode().equals(sup.getPartnerShip())){
                            sup.setPartnerShipName(sysDictVo.getName());
                        }
                    }
                }
                if(CollUtil.isNotEmpty(supplierType)){
                    for (SysDictVo sysDictVo:supplierType
                    ) {
                        if(sysDictVo.getCode().equals(sup.getSupplierType())){
                            sup.setSupplierTypeName(sysDictVo.getName());
                        }
                    }
                }
            }
        }

        query.setRecords(supplierInfos);
        return query;
    }

    @Override
    public Page supplierInfoFormSecond(Query query) {
        ShiftManagementDTO shiftManagementDTO = BeanUtil.mapToBean(query.getCondition(), ShiftManagementDTO.class, false);
        List<ShiftManagementDTO> shiftManagementDTOs = supplierInfoMapper.supplierInfoFormSecond(query, shiftManagementDTO);
        query.setRecords(shiftManagementDTOs);
        return query;
    }

    @Override
    public List<FdBusCostDetailDTO> supplierInfoFormThird(FdBusCostDetailDTO fdBusCostDetailDTO) {
        List<FdBusCostDetailDTO> fdBusCostDetailDTOs = supplierInfoMapper.supplierInfoFormThird(fdBusCostDetailDTO);
        return fdBusCostDetailDTOs;
    }
}
