package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.BookingTrackInformation;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 运踪信息表(订舱-市平台) 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-09 10:00:35
 */
public interface BookingTrackInformationService extends IService<BookingTrackInformation> {
    /**
     * 查询运踪信息表(订舱-市平台)信息
     *
     * @param rowId 运踪信息表(订舱-市平台)ID
     * @return 运踪信息表(订舱-市平台)信息
     */
    public List<BookingTrackInformation> selectBookingTrackInformationById(String rowId);

    /**
     * 查询运踪信息表(订舱-市平台)列表
     *
     * @param bookingTrackInformation 运踪信息表(订舱-市平台)信息
     * @return 运踪信息表(订舱-市平台)集合
     */
    public List<BookingTrackInformation> selectBookingTrackInformationList(BookingTrackInformation bookingTrackInformation);


    /**
     * 分页模糊查询运踪信息表(订舱-市平台)列表
     * @return 运踪信息表(订舱-市平台)集合
     */
    public Page selectBookingTrackInformationListByLike(Query query);

    /**
     * 批量新增运踪信息表(订舱-市平台)
     *
     * @param bookingTrackInformation 运踪信息表(订舱-市平台)信息
     * @return 结果
     */
    //public int insertBookingTrackInformationBatch(List<BookingTrackInformation> bookingTrackInformation);

    /**
     * 新增运踪信息表(订舱-市平台)
     *
     * @param bookingTrackInformation 运踪信息表(订舱-市平台)信息
     * @return 结果
     */
    public int insertBookingTrackInformation(BookingTrackInformation bookingTrackInformation);

    /**
     * 批量修改运踪信息表(订舱-市平台)
     *
     * @param bookingTrackInformation 运踪信息表(订舱-市平台)信息
     * @return 结果
     */
    //public int updateBookingTrackInformationBatch(List<BookingTrackInformation> bookingTrackInformation);

    /**
     * 修改运踪信息表(订舱-市平台)
     *
     * @param bookingTrackInformation 运踪信息表(订舱-市平台)信息
     * @return 结果
     */
    public int updateBookingTrackInformation(BookingTrackInformation bookingTrackInformation);

    /**
     * 删除运踪信息表(订舱-市平台)
     *
     * @param rowId 运踪信息表(订舱-市平台)ID
     * @return 结果
     */
    public int deleteBookingTrackInformationById(String rowId);

    /**
     * 批量删除运踪信息表(订舱-市平台)
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteBookingTrackInformationByIds(Integer[] rowIds);

}

