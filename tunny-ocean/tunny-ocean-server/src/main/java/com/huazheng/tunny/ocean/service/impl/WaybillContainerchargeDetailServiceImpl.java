package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.mapper.WaybillContainerchargeDetailMapper;
import com.huazheng.tunny.ocean.api.entity.WaybillContainerchargeDetail;
import com.huazheng.tunny.ocean.service.SysNoConfigService;
import com.huazheng.tunny.ocean.service.WaybillContainerchargeDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Service("waybillContainerchargeDetailService")
public class WaybillContainerchargeDetailServiceImpl extends ServiceImpl<WaybillContainerchargeDetailMapper, WaybillContainerchargeDetail> implements WaybillContainerchargeDetailService {

    @Autowired
    private WaybillContainerchargeDetailMapper waybillContainerchargeDetailMapper;

    @Autowired
    private SysNoConfigService sysNoConfigService;

    public WaybillContainerchargeDetailMapper getWaybillContainerchargeDetailMapper() {
        return waybillContainerchargeDetailMapper;
    }

    public void setWaybillContainerchargeDetailMapper(WaybillContainerchargeDetailMapper waybillContainerchargeDetailMapper) {
        this.waybillContainerchargeDetailMapper = waybillContainerchargeDetailMapper;
    }

    public SysNoConfigService getSysNoConfigService() {
        return sysNoConfigService;
    }

    public void setSysNoConfigService(SysNoConfigService sysNoConfigService) {
        this.sysNoConfigService = sysNoConfigService;
    }

    /**
     * 查询运单集装箱费用信息表信息
     *
     * @param rowId 运单集装箱费用信息表ID
     * @return 运单集装箱费用信息表信息
     */
    @Override
    public WaybillContainerchargeDetail selectWaybillContainerchargeDetailById(String rowId)
    {
        return waybillContainerchargeDetailMapper.selectWaybillContainerchargeDetailById(rowId);
    }

    /**
     * 查询运单集装箱费用信息表列表
     *
     * @param waybillContainerchargeDetail 运单集装箱费用信息表信息
     * @return 运单集装箱费用信息表集合
     */
    @Override
    public List<WaybillContainerchargeDetail> selectWaybillContainerchargeDetailList(WaybillContainerchargeDetail waybillContainerchargeDetail)
    {
        return waybillContainerchargeDetailMapper.selectWaybillContainerchargeDetailList(waybillContainerchargeDetail);
    }


    /**
     * 分页模糊查询运单集装箱费用信息表列表
     * @return 运单集装箱费用信息表集合
     */
    @Override
    public Page selectWaybillContainerchargeDetailListByLike(Query query)
    {
        WaybillContainerchargeDetail waybillContainerchargeDetail =  BeanUtil.mapToBean(query.getCondition(), WaybillContainerchargeDetail.class,false);
        query.setRecords(waybillContainerchargeDetailMapper.selectWaybillContainerchargeDetailListByLike(query,waybillContainerchargeDetail));
        return query;
    }

    @Override
    public int insertWaybillContainerchargeDetailBatch(List<WaybillContainerchargeDetail> list) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        for (WaybillContainerchargeDetail info:list) {
            info.setAddWhoName(username);
            info.setAddWho(usercode);
            info.setAddTime(new Date());
        }
        return waybillContainerchargeDetailMapper.insertWaybillContainerchargeDetailBatch(list);
    }

    /**
     * 新增运单集装箱费用信息表
     *
     * @param waybillContainerchargeDetail 运单集装箱费用信息表信息
     * @return 结果
     */
    @Override
    public int insertWaybillContainerchargeDetail(WaybillContainerchargeDetail waybillContainerchargeDetail)
    {
        return waybillContainerchargeDetailMapper.insertWaybillContainerchargeDetail(waybillContainerchargeDetail);
    }

    @Override
    public int updateWaybillContainerchargeDetailBatch(List<WaybillContainerchargeDetail> list) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        for (WaybillContainerchargeDetail info:list) {
            info.setUpdateTime(new Date());
            info.setUpdateWho(usercode);
            info.setUpdateWhoName(username);
        }
        return waybillContainerchargeDetailMapper.updateWaybillContainerchargeDetailBatch(list);
    }

    /**
     * 修改运单集装箱费用信息表
     *
     * @param waybillContainerchargeDetail 运单集装箱费用信息表信息
     * @return 结果
     */
    @Override
    public int updateWaybillContainerchargeDetail(WaybillContainerchargeDetail waybillContainerchargeDetail)
    {
        return waybillContainerchargeDetailMapper.updateWaybillContainerchargeDetail(waybillContainerchargeDetail);
    }


    /**
     * 删除运单集装箱费用信息表
     *
     * @param rowId 运单集装箱费用信息表ID
     * @return 结果
     */
    @Override
    public int deleteWaybillContainerchargeDetailById(String rowId)
    {
        return waybillContainerchargeDetailMapper.deleteWaybillContainerchargeDetailById( rowId);
    };


    /**
     * 批量删除运单集装箱费用信息表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteWaybillContainerchargeDetailByIds(Integer[] rowIds)
    {
        return waybillContainerchargeDetailMapper.deleteWaybillContainerchargeDetailByIds( rowIds);
    }

}
