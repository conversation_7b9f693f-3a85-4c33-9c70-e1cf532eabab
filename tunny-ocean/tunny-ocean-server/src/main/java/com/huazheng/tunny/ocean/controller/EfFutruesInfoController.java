package com.huazheng.tunny.ocean.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.ocean.api.entity.EfFutruesInfo;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseApply;
import com.huazheng.tunny.ocean.api.entity.OperationLog;
import com.huazheng.tunny.ocean.service.EfFutruesInfoService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.service.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 仓单期货行情
 *
 * <AUTHOR>
 * @date 2023-05-06 14:37:36
 */
@Slf4j
@RestController
@RequestMapping("/effutruesinfo")
public class EfFutruesInfoController {

    @Autowired
    private EfFutruesInfoService efFutruesInfoService;
    @Autowired
    private SignatureController signatureController;
    @Autowired
    private OperationLogService operationLogService;
    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  efFutruesInfoService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return efFutruesInfoService.selectEfFutruesInfoListByLike(new Query<>(params));
    }

    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        EfFutruesInfo efFutruesInfo =efFutruesInfoService.selectEfFutruesInfoById(rowId);
        return new R<>(efFutruesInfo);
    }

    @PostMapping("/getDetail")
    public R getDetail(@RequestBody EfFutruesInfo efFutruesInfo) {
        final EfFutruesInfo detail = efFutruesInfoService.getDetail(efFutruesInfo);
        return new R<>(detail);
    }

    /**
     * 保存
     * @param efFutruesInfo
     * @return R
     */
    @PostMapping
    public R save(@RequestBody EfFutruesInfo efFutruesInfo) {
        efFutruesInfoService.insert(efFutruesInfo);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param efFutruesInfo
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody EfFutruesInfo efFutruesInfo) {
        efFutruesInfoService.updateById(efFutruesInfo);
        return new R<>(Boolean.TRUE);
    }

    

    /**
     * 删除
     * @param rowId
     * @return R
     */
    @GetMapping("/del/{rowId}")
    public R delete(@PathVariable  String rowId) {
        efFutruesInfoService.deleteById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        efFutruesInfoService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<EfFutruesInfo> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<EfFutruesInfo> list = efFutruesInfoService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), EfFutruesInfo.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }

    @PostMapping("/syncFutruesInfo")
    public String syncFutruesInfo(@RequestBody JSONObject jsonObject) {
        String content = null;
        try {
            //调用接口
            String data = signatureController.getPost(jsonObject);
            EfFutruesInfo efFutruesInfo = JSONUtil.toBean(data, EfFutruesInfo.class);

            String content2 = String.valueOf(jsonObject.get("content"));
            OperationLog log4 = new OperationLog();
            log4.setUuid(UUID.randomUUID().toString());
            log4.setProcessType("同步期货价格");
            log4.setOperationCode("ER");
            log4.setOperationName("E融");
            log4.setOperationTime(new Date());
            log4.setOperationOpinion(content2 + data );
            operationLogService.insertOperationLog(log4);

            //插入数据
            content = efFutruesInfoService.syncFutruesInfo(efFutruesInfo);
        }catch (Exception e){
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE,e.getMessage()), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPostEf("/effutruesinfo/syncFutruesInfo", content);
        return result;
    }

}
