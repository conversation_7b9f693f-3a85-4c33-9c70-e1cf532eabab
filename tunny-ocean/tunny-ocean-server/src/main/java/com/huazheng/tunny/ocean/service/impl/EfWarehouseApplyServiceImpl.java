package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.EfFileDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.controller.SignatureController;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.EfWarehouseApplyService;
import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

@Service("efWarehouseApplyService")
public class EfWarehouseApplyServiceImpl extends ServiceImpl<EfWarehouseApplyMapper, EfWarehouseApply> implements EfWarehouseApplyService {

    @Autowired
    private EfWarehouseApplyMapper efWarehouseApplyMapper;
    @Autowired
    private EfWarehouseListMapper efWarehouseListMapper;
    @Autowired
    private SignatureController signatureController;
    @Autowired
    private EfWarehouseMapper efWarehouseMapper;
    @Autowired
    private EfWarehouseAuditinfoMapper efWarehouseAuditinfoMapper;
    @Autowired
    private EfWarehousePayinfoMapper efWarehousePayinfoMapper;
    @Autowired
    private EfWarehouseDisposeinfoMapper efWarehouseDisposeinfoMapper;
    @Autowired
    private EfWarehouseLogMapper efWarehouseLogMapper;
    @Autowired
    private EfWarehouseMarginMapper efWarehouseMarginMapper;
    @Autowired
    private EfWaybillMapper efWaybillMapper;
    @Autowired
    private EfWaybillGoodsMapper efWaybillGoodsMapper;
    @Autowired
    private EfWarehouseGoodsMapper efWarehouseGoodsMapper;

    DecimalFormat df = new DecimalFormat("#.00");
    /**
     * 查询仓单融资申请表信息
     *
     * @param rowId 仓单融资申请表ID
     * @return 仓单融资申请表信息
     */
    @Override
    public EfWarehouseInfo selectEfWarehouseApplyById(String rowId)
    {
        EfWarehouseInfo info = new EfWarehouseInfo();
        final EfWarehouseApply apply = efWarehouseApplyMapper.selectEfWarehouseApplyById(rowId);
        if(apply!=null){
            info.setAssetCode(apply.getAssetCode());
            info.setAssetState(apply.getAssetState());
            info.setApplyInfo(apply);
            final List<EfWarehouseList> efWarehouseLists = efWarehouseListMapper.selectEfWarehouseListByQlFinancingNo(apply.getQlFinancingNo());
            for (EfWarehouseList efWarehouseList:efWarehouseLists
                 ) {
                final EfWarehouse efWarehouse = efWarehouseMapper.selectEfWarehouseByWarehouseReceiptNo(efWarehouseList.getWarehouseCode());
                efWarehouseList.setEfwarehouse(efWarehouse);
            }
            final EfWarehouseAuditinfo efWarehouseAuditinfo = efWarehouseAuditinfoMapper.selectEfWarehouseAuditinfoByQlFinancingNo(apply.getQlFinancingNo());
            final EfWarehousePayinfo efWarehousePayinfo = efWarehousePayinfoMapper.selectEfWarehousePayinfoByQlFinancingNo(apply.getQlFinancingNo());
            final List<EfWarehouseMargin> efWarehouseMargins = efWarehouseMarginMapper.selectEfWarehouseMarginByQlFinancingNo(apply.getQlFinancingNo());
            final EfWarehouseDisposeinfo efWarehouseDisposeinfo = efWarehouseDisposeinfoMapper.selectEfWarehouseDisposeinfoByQlFinancingNo(apply.getQlFinancingNo());
            final List<EfWarehouseLog> efWarehouseLogs = efWarehouseLogMapper.selectEfWarehouseLogByQlFinancingNo(apply.getQlFinancingNo());
            info.setWarehouseList(efWarehouseLists);
            info.setAuditInfo(efWarehouseAuditinfo);
            info.setPayInfo(efWarehousePayinfo);
            info.setMarginInfo(efWarehouseMargins);
            info.setDisposeInfo(efWarehouseDisposeinfo);
            info.setLogInfo(efWarehouseLogs);
        }
        return info;
    }

    /**
     * 查询仓单融资申请表列表
     *
     * @param efWarehouseApply 仓单融资申请表信息
     * @return 仓单融资申请表集合
     */
    @Override
    public List<EfWarehouseApply> selectEfWarehouseApplyList(EfWarehouseApply efWarehouseApply)
    {
        return efWarehouseApplyMapper.selectEfWarehouseApplyList(efWarehouseApply);
    }


    /**
     * 分页模糊查询仓单融资申请表列表
     * @return 仓单融资申请表集合
     */
    @Override
    public Page selectEfWarehouseApplyListByLike(Query query)
    {
        EfWarehouseApply efWarehouseApply =  BeanUtil.mapToBean(query.getCondition(), EfWarehouseApply.class,false);
        efWarehouseApply.setAddWho(SecurityUtils.getUserInfo().getUserName());
        final List<String> roles = SecurityUtils.getRoles();
        if(CollUtil.isNotEmpty(roles)){
            for (String role:roles
            ) {
                if("CY_MANAGE_ADMIN".equals(role)||"CY_BIZ_ADMIN".equals(role)
                        ||"MANAGE_SUPER_ADMIN".equals(role)||"ROLE_ADMIN".equals(role)
                        ||"PR_MANAGE_ADMIN".equals(role)
                        ||"PR_BIZ_ADMIN".equals(role)
                        ||"PR_FIN_ADMIN".equals(role)
                        ||"CY_FIN_ADMIN".equals(role)||"FINANCIAL_TEMPORARY".equals(role)){
                    efWarehouseApply.setEntSocialCode(null);
                }
            }
        }

        query.setRecords(efWarehouseApplyMapper.selectEfWarehouseApplyListByLike(query,efWarehouseApply));
        return query;
    }

    @Override
    public Page pageForPlatformCode(Query query)
    {
        EfWarehouseApply efWarehouseApply =  BeanUtil.mapToBean(query.getCondition(), EfWarehouseApply.class,false);
        final List<String> roles = SecurityUtils.getRoles();
        if(CollUtil.isNotEmpty(roles)){
            for (String role:roles
                 ) {
                if("PR_FIN_ADMIN".equals(role)||"PR_BIZ_ADMIN".equals(role)||"PR_MANAGE_ADMIN".equals(role)||"MANAGE_SUPER_ADMIN".equals(role)||"ROLE_ADMIN".equals(role)){
                    efWarehouseApply.setPlatformCode(null);
                }
            }
        }
        query.setRecords(efWarehouseApplyMapper.selectEfWarehouseApplyListByLike(query,efWarehouseApply));
        return query;
    }

    @Override
    public R sumForPlatformCode(Query query)
    {
        R r = new R();
        EfWarehouseApply efWarehouseApply =  BeanUtil.mapToBean(query.getCondition(), EfWarehouseApply.class,false);
        final List<String> roles = SecurityUtils.getRoles();
        if(CollUtil.isNotEmpty(roles)){
            for (String role:roles
            ) {
                if("PR_FIN_ADMIN".equals(role)||"PR_BIZ_ADMIN".equals(role)||"PR_MANAGE_ADMIN".equals(role)||"MANAGE_SUPER_ADMIN".equals(role)||"ROLE_ADMIN".equals(role)){
                    efWarehouseApply.setPlatformCode(null);
                }
            }
        }
        Integer sum = efWarehouseApplyMapper.sumEnt(efWarehouseApply);
        List<EfWarehouseApply> paySum = efWarehouseApplyMapper.paySum(efWarehouseApply);
        Map<String,Object> map=new HashMap<>();
        map.put("paySum",paySum);
        map.put("enterprise",sum);
        r.setData(map);
        r.setB(true);
        r.setCode(200);
        return r;
    }

    /**
     * 新增仓单融资申请表
     *
     * @param efWarehouseApply 仓单融资申请表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String insertEfWarehouseApply(EfWarehouseApply efWarehouseApply)
    {
        //插入操作记录
        EfWarehouseLog log = new EfWarehouseLog();
        String remark = "";

        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(efWarehouseApply!=null){
            String qlFinancingNo = "RZ"+ System.currentTimeMillis();
            efWarehouseApply.setRowId(UUID.randomUUID().toString());
            efWarehouseApply.setQlFinancingNo(qlFinancingNo);

            Date date = new Date();
            String str = new SimpleDateFormat("yyyy-MM-dd").format(date);//再将时间转换为对应格式字符串

            efWarehouseApply.setApplyTime(str);
            final BigDecimal applyAmount = efWarehouseApply.getApplyAmountCny().multiply(BigDecimal.valueOf(100));
            efWarehouseApply.setApplyAmount(applyAmount);
            final List<EfWarehouseList> warehouseList2 = efWarehouseApply.getWarehouseList();
            if(CollUtil.isNotEmpty(warehouseList2)) {
                for (EfWarehouseList efWarehouseList : warehouseList2
                ) {
                    efWarehouseList.setQlFinancingNo(qlFinancingNo);
                }
            }
            remark = "申请金额："+efWarehouseApply.getApplyCurrency()+" "+df.format(efWarehouseApply.getApplyAmountCny());

            from = "E融平台:";
            //调用E融接口，插入申请数据
            String json = JSONUtil.parseObj(efWarehouseApply, true).toStringPretty();
            final String result = signatureController.doPostEf("/efwarehouseapply/insertEfWarehouseApply", json);
            JSONObject dataObject = JSONUtil.parseObj(result);
            flag = Boolean.valueOf(String.valueOf(dataObject.get("b")));
            msg = String.valueOf(dataObject.get("msg"));
            if(flag){
                //仓单质押申请
                //待E融审核
                efWarehouseApply.setAssetState("APPLYEF");
                efWarehouseApply.setAddTime(LocalDateTime.now());
                efWarehouseApply.setAddWho(SecurityUtils.getUserInfo().getUserName());
                efWarehouseApply.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                //仓单质押申请---仓单信息
                final List<EfWarehouseList> warehouseList = efWarehouseApply.getWarehouseList();
                if(CollUtil.isNotEmpty(warehouseList)){
                    for (EfWarehouseList efWarehouseList:warehouseList
                    ) {
                        //仓单货物信息
                        final List<EfWarehouseGoods> goodsInfoList = efWarehouseList.getGoodsInfoList();
                        if(CollUtil.isNotEmpty(goodsInfoList)){
                            for (EfWarehouseGoods goodsInfo:goodsInfoList
                                 ) {
                                //货物运单信息
                                final EfWaybill wayBillInfo = goodsInfo.getWayBillInfo();
                                if(wayBillInfo!=null){
                                    //运单箱信息
                                    final List<EfWaybillGoods> wayBillGoodsInfoList = wayBillInfo.getWayBillGoodsInfoList();
                                    if(CollUtil.isNotEmpty(wayBillGoodsInfoList)){
                                        for (EfWaybillGoods wayBillGoodsInfo:wayBillGoodsInfoList
                                             ) {
                                            wayBillGoodsInfo.setRowId(UUID.randomUUID().toString());
                                            wayBillGoodsInfo.setQlFinancingNo(qlFinancingNo);
                                            wayBillGoodsInfo.setOrderNo(wayBillInfo.getOrderNo());
                                            wayBillGoodsInfo.setWaybillNo(wayBillInfo.getWaybillNo());
                                            wayBillGoodsInfo.setWarehouseReceiptNo(efWarehouseList.getWarehouseCode());
                                            wayBillGoodsInfo.setGoodsNo(goodsInfo.getGoodsNo());
                                            wayBillGoodsInfo.setAddTime(LocalDateTime.now());
                                            wayBillGoodsInfo.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                            wayBillGoodsInfo.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                            efWaybillGoodsMapper.insertEfWaybillGoods(wayBillGoodsInfo);
                                        }
                                    }
                                    wayBillInfo.setRowId(UUID.randomUUID().toString());
                                    wayBillInfo.setQlFinancingNo(qlFinancingNo);
                                    wayBillInfo.setWarehouseReceiptNo(efWarehouseList.getWarehouseCode());
                                    wayBillInfo.setGoodsNo(goodsInfo.getGoodsNo());
                                    wayBillInfo.setAddTime(LocalDateTime.now());
                                    wayBillInfo.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                    wayBillInfo.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                    efWaybillMapper.insertEfWaybill(wayBillInfo);
                                }
                            }
                        }
                        //新增仓单融资仓单表
                        efWarehouseList.setRowId(UUID.randomUUID().toString());
                        efWarehouseList.setQlFinancingNo(qlFinancingNo);
//                        final int valuation = (int)Math.round(efWarehouseList.getValuationCny()*100);
//                        efWarehouseList.setValuation(valuation);
                        efWarehouseList.setPledgeStatus("1");
                        efWarehouseList.setAddTime(LocalDateTime.now());
                        efWarehouseList.setAddWho(SecurityUtils.getUserInfo().getUserName());
                        efWarehouseList.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                        efWarehouseListMapper.insertEfWarehouseList(efWarehouseList);

                        //改e融同步仓单表质押状态
                        EfWarehouse efWarehouse = new EfWarehouse();
                        efWarehouse.setWarehouseReceiptNo(efWarehouseList.getWarehouseCode());
                        efWarehouse.setPledgeStatus(1);
                        efWarehouse.setUpdateTime(LocalDateTime.now());
                        efWarehouse.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                        efWarehouse.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                        efWarehouseMapper.updateEfWarehouse(efWarehouse);
                    }

                }else{
                    msg = "没有接收到仓单融资仓单表数据";
                    flag = false;
                }
                //新增仓单融资申请表
                efWarehouseApplyMapper.insertEfWarehouseApply(efWarehouseApply);

                //插入操作记录
                log.setRowId(UUID.randomUUID().toString());
                log.setQlFinancingNo(qlFinancingNo);
                log.setSerialNum(String.valueOf(System.currentTimeMillis()));
                log.setControlType("融资申请");
                log.setControlTime(LocalDateTime.now());
                log.setRemark(remark);
                log.setAddTime(LocalDateTime.now());
                log.setAddWho(SecurityUtils.getUserInfo().getUserName());
                log.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                efWarehouseLogMapper.insertEfWarehouseLog(log);
            }


        }else{
            msg = "没有接收到仓单融资申请表数据";
            flag = false;
        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EfFileDTO downloadFile(EfFileDTO efFileDTO) {
        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(efFileDTO!=null){

            //调用中钞接口，插入申请数据,解析返回数据
            String json = JSONUtil.parseObj(efFileDTO, true).toStringPretty();
            final String result = signatureController.doPost("/v1/safe/warehouse/downloadFile", json);

            JSONObject resultObject = JSONUtil.parseObj(result);

            flag = Boolean.valueOf(String.valueOf(resultObject.get("b")));
            if(flag){
                from = "中钞平台:";
                msg = String.valueOf(resultObject.get("msg"));

                String detail = String.valueOf(resultObject.get("data"));
                JSONObject detailObject = JSONUtil.parseObj(detail);
                String fileData = String.valueOf(detailObject.get("fileData"));
//                byte[] bytes = Base64.getDecoder().decode(fileData);
                String fileName = String.valueOf(detailObject.get("fileName"));
                efFileDTO.setFileName(fileName);
                efFileDTO.setFileData(fileData);
                return efFileDTO;
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String downloadFileForEf(EfFileDTO efFileDTO)
    {
        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(efFileDTO!=null){
            if(StrUtil.isEmpty(efFileDTO.getQlFinancingNo())){
                flag = false;
                msg = "未接收到融资申请编号！";
            }else{
                final EfWarehouseApply efWarehouseApply = efWarehouseApplyMapper.selectEfWarehouseApplyByQlFinancingNo(efFileDTO.getQlFinancingNo());
                if(efWarehouseApply!=null){
                    efFileDTO.setAssetCode(efWarehouseApply.getAssetCode());
                }

                //调用中钞接口，插入申请数据,解析返回数据
                String json = JSONUtil.parseObj(efFileDTO, true).toStringPretty();
                final String result = signatureController.doPost("/v1/safe/warehouse/downloadFile", json);

                JSONObject resultObject = JSONUtil.parseObj(result);
//            msg = String.valueOf(resultObject.get("message"));
                flag = Boolean.valueOf(String.valueOf(resultObject.get("b")));
                msg = String.valueOf(resultObject.get("msg"));

                if(flag){
                    from = "中钞平台:";

                    String detail = String.valueOf(resultObject.get("data"));
                    JSONObject detailObject = JSONUtil.parseObj(detail);
                    String fileData = String.valueOf(detailObject.get("fileData"));
//                byte[] bytes = Base64.getDecoder().decode(fileData);
                    String fileName = String.valueOf(detailObject.get("fileName"));
                    efFileDTO.setFileName(fileName);
                    efFileDTO.setFileData(fileData);
                    return JSONUtil.parseObj(new R<>(flag,efFileDTO), false).toStringPretty();
                }
            }

        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }

    /**
     * 修改仓单融资申请表
     *
     * @param efWarehouseApply 仓单融资申请表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateEfWarehouseApply(EfWarehouseApply efWarehouseApply)
    {
        //插入操作记录
        EfWarehouseLog log = new EfWarehouseLog();
        String remark = "";

        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(efWarehouseApply!=null){
            final BigDecimal applyAmount = efWarehouseApply.getApplyAmountCny().multiply(BigDecimal.valueOf(100));
            efWarehouseApply.setApplyAmount(applyAmount);
            remark = "申请金额："+efWarehouseApply.getApplyCurrency()+" "+df.format(efWarehouseApply.getApplyAmountCny());

            from = "E融平台:";
            //调用E融接口，插入申请数据
            String json = JSONUtil.parseObj(efWarehouseApply, true).toStringPretty();
            final String result = signatureController.doPostEf("/efwarehouseapply/updateEfWarehouseApply", json);
            JSONObject dataObject = JSONUtil.parseObj(result);
            flag = Boolean.valueOf(String.valueOf(dataObject.get("b")));
            msg = String.valueOf(dataObject.get("msg"));

            if(flag){
                efWarehouseApply.setAssetState("APPLYEF");
                efWarehouseApply.setUpdateTime(LocalDateTime.now());
                efWarehouseApply.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                efWarehouseApply.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                final List<EfWarehouseList> warehouseList = efWarehouseApply.getWarehouseList();
                if(CollUtil.isNotEmpty(warehouseList)){
                    efWarehouseListMapper.deleteEfWarehouseListByQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                    for (EfWarehouseList efWarehouseList:warehouseList
                    ) {
                        //仓单货物信息
                        final List<EfWarehouseGoods> goodsInfoList = efWarehouseList.getGoodsInfoList();
                        if(CollUtil.isNotEmpty(goodsInfoList)){
                            for (EfWarehouseGoods goodsInfo:goodsInfoList
                            ) {
                                //货物运单信息
                                EfWaybill del = new EfWaybill();
                                del.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                                del.setWarehouseReceiptNo(efWarehouseList.getWarehouseCode());
                                del.setGoodsNo(goodsInfo.getGoodsNo());
                                del.setUpdateTime(LocalDateTime.now());
                                del.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                                del.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                                efWaybillMapper.deleteEfWaybills(del);
                                final EfWaybill wayBillInfo = goodsInfo.getWayBillInfo();
                                if(wayBillInfo!=null){
                                    //运单箱信息
                                    EfWaybillGoods del2 = new EfWaybillGoods();
                                    del2.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                                    del2.setWarehouseReceiptNo(efWarehouseList.getWarehouseCode());
                                    del2.setWaybillNo(wayBillInfo.getWaybillNo());
                                    del2.setOrderNo(wayBillInfo.getOrderNo());
                                    del2.setGoodsNo(goodsInfo.getGoodsNo());
                                    del2.setUpdateTime(LocalDateTime.now());
                                    del2.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                                    del2.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                                    efWaybillGoodsMapper.deleteEfWaybillGoods(del2);
                                    final List<EfWaybillGoods> wayBillGoodsInfoList = wayBillInfo.getWayBillGoodsInfoList();
                                    if(CollUtil.isNotEmpty(wayBillGoodsInfoList)){
                                        for (EfWaybillGoods wayBillGoodsInfo:wayBillGoodsInfoList
                                        ) {
                                            wayBillGoodsInfo.setRowId(UUID.randomUUID().toString());
                                            wayBillGoodsInfo.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                                            wayBillGoodsInfo.setOrderNo(wayBillInfo.getOrderNo());
                                            wayBillGoodsInfo.setWaybillNo(wayBillInfo.getWaybillNo());
                                            wayBillGoodsInfo.setWarehouseReceiptNo(efWarehouseList.getWarehouseCode());
                                            wayBillGoodsInfo.setGoodsNo(goodsInfo.getGoodsNo());
                                            wayBillGoodsInfo.setAddTime(LocalDateTime.now());
                                            wayBillGoodsInfo.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                            wayBillGoodsInfo.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                            efWaybillGoodsMapper.insertEfWaybillGoods(wayBillGoodsInfo);
                                        }
                                    }
                                    wayBillInfo.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                                    wayBillInfo.setWarehouseReceiptNo(efWarehouseList.getWarehouseCode());
                                    wayBillInfo.setGoodsNo(goodsInfo.getGoodsNo());
                                    wayBillInfo.setAddTime(LocalDateTime.now());
                                    wayBillInfo.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                    wayBillInfo.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                    efWaybillMapper.insertEfWaybill(wayBillInfo);
                                }
                            }
                        }
                        //新增仓单融资仓单表
                        efWarehouseList.setRowId(UUID.randomUUID().toString());
                        efWarehouseList.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
//                        final int valuation = (int)Math.round(efWarehouseList.getValuationCny()*100);
//                        efWarehouseList.setValuation(valuation);
                        efWarehouseList.setPledgeStatus("1");
                        efWarehouseList.setAddTime(LocalDateTime.now());
                        efWarehouseList.setAddWho(SecurityUtils.getUserInfo().getUserName());
                        efWarehouseList.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                        efWarehouseListMapper.insertEfWarehouseList(efWarehouseList);

                        //改e融同步仓单表质押状态
                        EfWarehouse efWarehouse = new EfWarehouse();
                        efWarehouse.setWarehouseReceiptNo(efWarehouseList.getWarehouseCode());
                        efWarehouse.setPledgeStatus(1);
                        efWarehouse.setUpdateTime(LocalDateTime.now());
                        efWarehouse.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                        efWarehouse.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                        efWarehouseMapper.updateEfWarehouse(efWarehouse);
                    }

                }else{
                    msg = "没有接收到仓单融资仓单表数据";
                    flag = false;
                }
                //新增仓单融资申请表
                efWarehouseApplyMapper.updateEfWarehouseApply(efWarehouseApply);

                //插入操作记录
                log.setRowId(UUID.randomUUID().toString());
                log.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                log.setSerialNum(String.valueOf(System.currentTimeMillis()));
                log.setControlType("融资申请");
                log.setControlTime(LocalDateTime.now());
                log.setRemark(remark);
                log.setAddTime(LocalDateTime.now());
                log.setAddWho(SecurityUtils.getUserInfo().getUserName());
                log.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                efWarehouseLogMapper.insertEfWarehouseLog(log);
            }
        }else{
            msg = "没有接收到仓单融资申请表数据";
            flag = false;
        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }


    /**
     * 删除仓单融资申请表
     *
     * @param rowId 仓单融资申请表ID
     * @return 结果
     */
    public int deleteEfWarehouseApplyById(String rowId)
    {
        return efWarehouseApplyMapper.deleteEfWarehouseApplyById( rowId);
    };


    /**
     * 批量删除仓单融资申请表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfWarehouseApplyByIds(Integer[] rowIds)
    {
        return efWarehouseApplyMapper.deleteEfWarehouseApplyByIds( rowIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String auditFromEf(EfWarehouseApply ef)
    {
        //插入操作记录
        EfWarehouseLog log = new EfWarehouseLog();
        String remark = "";

        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(ef!=null) {
            final EfWarehouseApply efWarehouseApply = efWarehouseApplyMapper.selectEfWarehouseApplyByQlFinancingNo(ef.getQlFinancingNo());

            if(efWarehouseApply!=null){
                EfWarehouseList list = new EfWarehouseList();
                list.setQlFinancingNo(ef.getQlFinancingNo());
                list.setDeleteFlag("N");
                final List<EfWarehouseList> efWarehouseList = efWarehouseListMapper.selectEfWarehouseListList(list);
                if(CollUtil.isNotEmpty(efWarehouseList)){
                    if("1".equals(ef.getSupplyOperate())){
                        //同意质押
                        final List<EfWarehouseList> efListOld = ef.getWarehouseList();
                        if(CollUtil.isNotEmpty(efListOld)){
                            log.setControlType("仓储审核通过");
                            for (EfWarehouseList e1:efWarehouseList
                                 ) {
                                for (EfWarehouseList e2:efListOld
                                ) {
                                    if(e1.getWarehouseCode().equals(e2.getWarehouseCode())
                                    &&e1.getWarehouseSupervisionNo().equals(e2.getWarehouseSupervisionNo())){
                                        if(e2.getValuationCny()!=null){
                                            final BigDecimal valuation = e2.getValuationCny().multiply(BigDecimal.valueOf(100));
                                            e1.setValuation(valuation);
                                            e1.setValuationCny(e2.getValuationCny());
                                            e1.setValuationCurrency(e2.getValuationCurrency());
                                        }

                                    }

                                }
                            }
                            efWarehouseApply.setWarehouseList(efWarehouseList);

                            //调用中钞接口，插入申请数据,解析返回数据


                            if(CollUtil.isNotEmpty(efWarehouseList)){
                                for (EfWarehouseList efList:efWarehouseList
                                ) {
                                    EfWarehouseGoods sel = new EfWarehouseGoods();
                                    sel.setWarehouseReceiptNo(efList.getWarehouseCode());
                                    sel.setDeleteFlag("N");
                                    final List<EfWarehouseGoods> efWarehouseGoods = efWarehouseGoodsMapper.selectEfWarehouseGoodsList(sel);

                                    final EfWarehouse efWarehouse = efWarehouseMapper.selectEfWarehouseByWarehouseReceiptNo(efList.getWarehouseCode());
                                    if(CollUtil.isNotEmpty(efWarehouseGoods)){
                                        for (EfWarehouseGoods goods:efWarehouseGoods
                                        ) {
                                            EfWaybill bill = new EfWaybill();
                                            bill.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                                            bill.setWarehouseReceiptNo(efList.getWarehouseCode());
                                            bill.setDeleteFlag("N");
                                            final List<EfWaybill> efWaybills = efWaybillMapper.selectEfWaybillList(bill);
                                            if(CollUtil.isNotEmpty(efWaybills)){
                                                for (EfWaybill  bill2:efWaybills
                                                ) {
                                                    EfWaybillGoods goods2 = new EfWaybillGoods();
                                                    goods2.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                                                    goods2.setWarehouseReceiptNo(efList.getWarehouseCode());
                                                    goods2.setWaybillNo(bill2.getWaybillNo());
                                                    goods2.setOrderNo(bill2.getOrderNo());
                                                    goods2.setGoodsNo(bill2.getGoodsNo());
                                                    goods2.setDeleteFlag("N");

                                                    final List<EfWaybillGoods> efWaybillGoods = efWaybillGoodsMapper.selectEfWaybillGoodsList(goods2);
                                                    bill2.setWayBillGoodsInfoList(efWaybillGoods);
                                                }
                                                goods.setWayBillInfo(efWaybills.get(0));

                                            }
                                            goods.setGoodsCode(goods.getGoodsNo());
                                            goods.setCategoryCode(goods.getCategoryNo());

                                            goods.setStorageQuantity(goods.getOldQuantity());
                                            goods.setStorageQuantityUnit(goods.getQuantityUnit());
                                            goods.setStockQuantity(goods.getQuantity());
                                            goods.setStockQuantityUnit(goods.getQuantityUnit());

                                            goods.setStorageWeight(goods.getOldWeight());
                                            goods.setStorageWeightUnit(goods.getWeightUnit());
                                            goods.setStockWeight(goods.getWeight());
                                            goods.setStockWeightUnit(goods.getWeightUnit());

                                            if(efWarehouse!=null){
                                                goods.setOrderPath(efWarehouse.getOrderPath());
                                            }
                                        }
                                    }
                                    efList.setGoodsInfoList(efWarehouseGoods);
                                    //调用中钞接口，同步仓单信息
                                    String json2 = JSONUtil.parseObj(efList, true).toStringPretty();
                                    final String result2 = signatureController.doPost("/v1/safe/warehouse/syncWarehouseInfo", json2);

                                    JSONObject resultObject = JSONUtil.parseObj(result2);
                                    flag = Boolean.valueOf(String.valueOf(resultObject.get("b")));
                                    if(!flag){
                                        from = "中钞平台:";
                                        msg = String.valueOf(resultObject.get("msg"));
                                        return JSONUtil.parseObj(new R<>(500,flag,null,from+msg), false).toStringPretty();
                                    }

                                }
                            }


                            String json = JSONUtil.parseObj(efWarehouseApply, true).toStringPretty();
                            final String result = signatureController.doPost("/v1/ent/warehouse/apply", json);

                            JSONObject resultObject = JSONUtil.parseObj(result);
                            flag = Boolean.valueOf(String.valueOf(resultObject.get("b")));
                            JSONObject jsonObject = JSONUtil.parseObj(resultObject.get("data"));
                            msg = String.valueOf(resultObject.get("msg"));

                            if(flag){
                                from = "中钞平台:";
                                /*String detail = String.valueOf(jsonObject.get("detail"));
                                JSONObject detailObject = JSONUtil.parseObj(detail);
                                String assetCode = String.valueOf(detailObject.get("assetCode"));*/
                                String assetCode = String.valueOf(jsonObject.get("assetCode"));

                                //更新融资申请编号
                                EfWarehouseApply apply = new EfWarehouseApply();
                                apply.setAssetCode(assetCode);
                                apply.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                                apply.setAssetState("APPLY");
                                apply.setChainState("SUCCESS");
                                efWarehouseApplyMapper.updateEfWarehouseApplyByQlFinancingNo(apply);

                        /*for (EfWarehouseList l:efWarehouseList
                             ) {
                            //改e融同步仓单表质押状态
                            EfWarehouse efWarehouse = new EfWarehouse();
                            efWarehouse.setWarehouseReceiptNo(l.getWarehouseCode());
                            efWarehouse.setPledgeStatus(1);
                            efWarehouse.setUpdateTime(LocalDateTime.now());
                            efWarehouse.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                            efWarehouse.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                            efWarehouseMapper.updateEfWarehouse(efWarehouse);
                        }*/
                                if(CollUtil.isNotEmpty(efWarehouseList)){
                                    for (EfWarehouseList e3:efWarehouseList
                                    ) {
                                        EfWarehouseList e4 = new EfWarehouseList();
                                        e4.setRowId(e3.getRowId());
                                        e4.setValuation(e3.getValuation());
                                        e4.setValuationCny(e3.getValuationCny());
                                        e4.setValuationCurrency(e3.getValuationCurrency());
                                        e4.setUpdateTime(LocalDateTime.now());
                                        e4.setUpdateWho("ER");
                                        e4.setUpdateWhoName("E融");
                                        efWarehouseListMapper.updateEfWarehouseList(e4);
                                    }
                                }

                                msg += "融资申请编号:"+assetCode;
                                remark = "提交银行审核，"+msg;
                            }else{
                                from = "中钞平台:";
                                int code = 500;
                                if(flag){
                                    code = 0;
                                }
                                return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
                            }
                        }else{
                            msg = "审批缺少仓单估值数据";
                            flag = false;
                        }
                    }else{
                        //更新融资申请状态
                        log.setControlType("仓储审核拒绝");

                        EfWarehouseApply apply = new EfWarehouseApply();
                        apply.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                        apply.setAssetState("REJECTEF");
                        efWarehouseApplyMapper.updateEfWarehouseApplyByQlFinancingNo(apply);

                        for (EfWarehouseList warehouseList:efWarehouseList
                             ) {
                            //改e融同步仓单表质押状态
                            EfWarehouse efWarehouse = new EfWarehouse();
                            efWarehouse.setWarehouseReceiptNo(warehouseList.getWarehouseCode());
                            efWarehouse.setPledgeStatus(0);
                            efWarehouse.setUpdateTime(LocalDateTime.now());
                            efWarehouse.setUpdateWho("ER");
                            efWarehouse.setUpdateWhoName("E融");
                            efWarehouseMapper.updateEfWarehouse(efWarehouse);
                        }

                    }
                    //插入操作记录
                    log.setRowId(UUID.randomUUID().toString());
                    log.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                    log.setSerialNum(String.valueOf(System.currentTimeMillis()));

                    log.setControlTime(LocalDateTime.now());
                    log.setRemark(remark);
                    log.setAddTime(LocalDateTime.now());
                    log.setAddWho("ER");
                    log.setAddWhoName("E融");
                    efWarehouseLogMapper.insertEfWarehouseLog(log);

                }else{
                    msg = "没有查询到该仓单融资仓单表数据";
                    flag = false;
                }

            }else{
                msg = "没有查询到该仓单融资申请数据";
                flag = false;
            }
        }else{
            msg = "没有接收到仓单融资审核数据";
            flag = false;
        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String syncAsset(EfWarehouseInfo efWarehouseInfo)
    {
        EfWarehouseApply ef = new EfWarehouseApply();
        //插入操作记录
        EfWarehouseLog log = new EfWarehouseLog();
        String remark = "";

        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(efWarehouseInfo!=null) {
            final EfWarehouseApply apply = efWarehouseApplyMapper.selectEfWarehouseApplyByAssetCode(efWarehouseInfo.getAssetCode());
            if(apply!=null){
                final EfWarehouseApply applyInfo = efWarehouseInfo.getApplyInfo();
                if(applyInfo!=null){
                    applyInfo.setQlFinancingNo(apply.getQlFinancingNo());
                    applyInfo.setAssetCode(apply.getAssetCode());
                    applyInfo.setAssetState(efWarehouseInfo.getAssetState());
                }else{
                    apply.setAssetState(efWarehouseInfo.getAssetState());
                    efWarehouseInfo.setApplyInfo(apply);
                }
                final EfWarehouseAuditinfo auditInfo = efWarehouseInfo.getAuditInfo();
                if(auditInfo!=null){
                    auditInfo.setQlFinancingNo(apply.getQlFinancingNo());
                    auditInfo.setAssetCode(apply.getAssetCode());
                    if(auditInfo.getExpectedLoanAmount()!=null){
                        final BigDecimal valuation = auditInfo.getExpectedLoanAmount().divide(BigDecimal.valueOf(100),2,BigDecimal.ROUND_HALF_UP);
                        auditInfo.setExpectedLoanAmountCny(valuation);
                    }
                    if(StrUtil.isNotEmpty(auditInfo.getRegulatoryAgreement())){
                        ef.setRegulatoryAgreement(auditInfo.getRegulatoryAgreement());
                    }
                    if(StrUtil.isNotEmpty(auditInfo.getAssetAgreement())){
                        ef.setAssetAgreement(auditInfo.getAssetAgreement());
                    }
                }
                final EfWarehousePayinfo payInfo = efWarehouseInfo.getPayInfo();
                if(payInfo!=null){
                    payInfo.setQlFinancingNo(apply.getQlFinancingNo());
                    payInfo.setAssetCode(apply.getAssetCode());
                    if(payInfo.getActualLoanAmount()!=null){
                        final BigDecimal valuation = payInfo.getActualLoanAmount().divide(BigDecimal.valueOf(100),2,BigDecimal.ROUND_HALF_UP);
                        payInfo.setActualLoanAmountCny(valuation);
                        if(applyInfo!=null){
                            applyInfo.setActualLoanAmountCny(valuation);
                        }else{
                            ef.setActualLoanAmountCny(valuation);
                        }
                    }
                    if(StrUtil.isNotEmpty(payInfo.getRegulatoryAgreement())){
                        ef.setRegulatoryAgreement(payInfo.getRegulatoryAgreement());
                    }
                    if(StrUtil.isNotEmpty(payInfo.getAssetAgreement())){
                        ef.setAssetAgreement(payInfo.getAssetAgreement());
                    }
                }
                final EfWarehouseDisposeinfo disposeInfo = efWarehouseInfo.getDisposeInfo();
                if(disposeInfo!=null){
                    disposeInfo.setQlFinancingNo(apply.getQlFinancingNo());
                    disposeInfo.setAssetCode(apply.getAssetCode());
                    if(disposeInfo.getCollectionAmount()!=null){
                        final BigDecimal valuation = disposeInfo.getCollectionAmount().divide(BigDecimal.valueOf(100),2,BigDecimal.ROUND_HALF_UP);
                        disposeInfo.setCollectionAmountCny(valuation);
                    }
                }

                //调用E融接口，插入数据,解析返回数据
                String json = JSONUtil.parseObj(efWarehouseInfo, true).toStringPretty();
                final String result = signatureController.doPostEf("/efwarehouseapply/syncAsset", json);

                JSONObject resultObject = JSONUtil.parseObj(result);
                from = "E融平台:";
                flag = Boolean.valueOf(String.valueOf(resultObject.get("b")));
                msg = String.valueOf(resultObject.get("msg"));

                if(flag){
                    if(applyInfo!=null){
                        applyInfo.setUpdateTime(LocalDateTime.now());
                        applyInfo.setUpdateWho("zc");
                        applyInfo.setUpdateWhoName("中钞");
                        efWarehouseApplyMapper.updateEfWarehouseApplyByQlFinancingNo(applyInfo);
                    }else{

                        ef.setQlFinancingNo(apply.getQlFinancingNo());
                        ef.setAssetState(efWarehouseInfo.getAssetState());
                        ef.setUpdateTime(LocalDateTime.now());
                        ef.setUpdateWho("zc");
                        ef.setUpdateWhoName("中钞");
                        efWarehouseApplyMapper.updateEfWarehouseApplyByQlFinancingNo(ef);
                    }
                    if("AUDIT".equals(efWarehouseInfo.getAssetState())){
                        if(auditInfo!=null){
                            log.setControlType("银行审核通过");
                            remark = remark+"预计放款金额："+auditInfo.getExpectedLoanCurrency();
                            if(auditInfo.getExpectedLoanAmountCny()!=null){
                                remark = remark + " "+df.format(auditInfo.getExpectedLoanAmountCny())+" ";
                            }else{
                                remark = remark + " 0.00 ";
                            }

                            efWarehouseAuditinfoMapper.deleteByQlFinancingNo(apply.getQlFinancingNo());
                            auditInfo.setRowId(UUID.randomUUID().toString());
                            auditInfo.setAddTime(LocalDateTime.now());
                            auditInfo.setAddWho("zc");
                            auditInfo.setAddWhoName("中钞");
                            efWarehouseAuditinfoMapper.insertEfWarehouseAuditinfo(auditInfo);
                        }
                    }
                    if("AUDIT_REJECT".equals(efWarehouseInfo.getAssetState())){
                        if(auditInfo!=null){
                            log.setControlType("银行审核拒绝");
                            remark = remark+auditInfo.getAuditNote();
                        }
                        EfWarehouseList list = new EfWarehouseList();
                        list.setQlFinancingNo(apply.getQlFinancingNo());
                        list.setDeleteFlag("N");
                        final List<EfWarehouseList> efWarehouseList = efWarehouseListMapper.selectEfWarehouseListList(list);
                        for (EfWarehouseList warehouseList:efWarehouseList
                        ) {
                            //审核拒绝，修改e融同步仓单表质押状态
                            EfWarehouse efWarehouse = new EfWarehouse();
                            efWarehouse.setWarehouseReceiptNo(warehouseList.getWarehouseCode());
                            efWarehouse.setPledgeStatus(0);
                            efWarehouse.setUpdateTime(LocalDateTime.now());
                            efWarehouse.setUpdateWho("ZC");
                            efWarehouse.setUpdateWhoName("中钞");
                            efWarehouseMapper.updateEfWarehouse(efWarehouse);
                        }
                    }
                    if("LOAN".equals(efWarehouseInfo.getAssetState())){
                        if(payInfo!=null){
                            log.setControlType("放款通过");
                            remark = remark+"实际放款金额："+payInfo.getActualLoanCurrency()+" "+df.format(payInfo.getActualLoanAmountCny())+" ";

                            efWarehousePayinfoMapper.deleteByQlFinancingNo(apply.getQlFinancingNo());
                            payInfo.setRowId(UUID.randomUUID().toString());
                            payInfo.setAddTime(LocalDateTime.now());
                            payInfo.setAddWho("zc");
                            payInfo.setAddWhoName("中钞");
                            efWarehousePayinfoMapper.insertEfWarehousePayinfo(payInfo);
                        }
                    }

                    if("LOAN_CANCEL".equals(efWarehouseInfo.getAssetState())){
                        log.setControlType("放款撤销");
                        if(payInfo!=null){
                            remark = remark+payInfo.getPayNote();
                        }
                        EfWarehouseList list = new EfWarehouseList();
                        list.setQlFinancingNo(apply.getQlFinancingNo());
                        list.setDeleteFlag("N");
                        final List<EfWarehouseList> efWarehouseList = efWarehouseListMapper.selectEfWarehouseListList(list);
                        for (EfWarehouseList warehouseList:efWarehouseList
                        ) {
                            //审核拒绝，修改e融同步仓单表质押状态
                            EfWarehouse efWarehouse = new EfWarehouse();
                            efWarehouse.setWarehouseReceiptNo(warehouseList.getWarehouseCode());
                            efWarehouse.setPledgeStatus(0);
                            efWarehouse.setUpdateTime(LocalDateTime.now());
                            efWarehouse.setUpdateWho("ZC");
                            efWarehouse.setUpdateWhoName("中钞");
                            efWarehouseMapper.updateEfWarehouse(efWarehouse);
                        }
                    }
                    if("APPLY".equals(efWarehouseInfo.getAssetState())){
                        log.setControlType("待银行审核");
                    }
                    if("PAY".equals(efWarehouseInfo.getAssetState())){
                        log.setControlType("部分解质押");
                        remark = remark+"部分解质押";
                    }
                    if("CLEAR".equals(efWarehouseInfo.getAssetState())){
                        log.setControlType("全部解质押");
                        remark = remark+"全部解质押";
                    }
                    if("ASSET_DISPOSAL_APPLY".equals(efWarehouseInfo.getAssetState())){
                        log.setControlType("资产处置登记");
                        remark = remark+"资产处置登记";
                    }
                    if("ASSET_DISPOSAL_REFUSE".equals(efWarehouseInfo.getAssetState())){
                        log.setControlType("处置复核拒绝");
                        remark = remark+"处置复核拒绝";
                    }
                    if("REPAYMENT".equals(efWarehouseInfo.getAssetState())){
                        log.setControlType("银行解质押中");
                        remark = remark+"银行解质押中";
                    }

                    if("ASSET_DISPOSAL".equals(efWarehouseInfo.getAssetState())){
                        log.setControlType("处置复核通过");
                        if(disposeInfo!=null){
                            remark = remark+"回款金额："+" "+df.format(disposeInfo.getCollectionAmountCny())+" ";

                            efWarehouseDisposeinfoMapper.deleteByQlFinancingNo(apply.getQlFinancingNo());
                            disposeInfo.setRowId(UUID.randomUUID().toString());
                            disposeInfo.setAddTime(LocalDateTime.now());
                            disposeInfo.setAddWho("zc");
                            disposeInfo.setAddWhoName("中钞");
                            efWarehouseDisposeinfoMapper.insertEfWarehouseDisposeinfo(disposeInfo);
                        }
                    }
                    //插入操作记录
                    log.setRowId(UUID.randomUUID().toString());
                    log.setQlFinancingNo(apply.getQlFinancingNo());
                    log.setSerialNum(String.valueOf(System.currentTimeMillis()));

                    log.setControlTime(LocalDateTime.now());
                    log.setRemark(remark);
                    log.setAddTime(LocalDateTime.now());
                    log.setAddWho("ZC");
                    log.setAddWhoName("中钞");
                    efWarehouseLogMapper.insertEfWarehouseLog(log);
                }
            }else{
                msg = "未查询到该申请数据";
                flag = false;
            }
        }else{
            msg = "没有接收到融资同步数据";
            flag = false;
        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String syncWarnPrice(EfWarehouseApply efWarehouseApply)
    {
        //插入操作记录
        EfWarehouseLog log = new EfWarehouseLog();
        String remark = "";

        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(efWarehouseApply!=null) {
            final EfWarehouseApply apply = efWarehouseApplyMapper.selectEfWarehouseApplyByAssetCode(efWarehouseApply.getAssetCode());
            if(apply!=null){
                efWarehouseApply.setQlFinancingNo(apply.getQlFinancingNo());
                //调用E融接口，插入数据,解析返回数据
                String json = JSONUtil.parseObj(efWarehouseApply, true).toStringPretty();
                final String result = signatureController.doPostEf("/efwarehouseapply/syncWarnPrice", json);

                JSONObject resultObject = JSONUtil.parseObj(result);
                from = "E融平台:";
                flag = Boolean.valueOf(String.valueOf(resultObject.get("b")));
                msg = String.valueOf(resultObject.get("msg"));

                if(flag){
                    if(efWarehouseApply!=null){
                        final Double valuation = Double.valueOf(Integer.parseInt(efWarehouseApply.getWarnPrice())/100);

                        remark = "估值预警线：" + df.format(valuation);
                        efWarehouseApply.setWarnPrice(valuation+"");
                        efWarehouseApply.setUpdateTime(LocalDateTime.now());
                        efWarehouseApply.setUpdateWho("zc");
                        efWarehouseApply.setUpdateWhoName("中钞");
                        efWarehouseApplyMapper.updateEfWarehouseApplyByQlFinancingNo(efWarehouseApply);
                    }
                    //插入操作记录
                    log.setRowId(UUID.randomUUID().toString());
                    log.setQlFinancingNo(apply.getQlFinancingNo());
                    log.setSerialNum(String.valueOf(System.currentTimeMillis()));
                    log.setControlType("预警估值同步");
                    log.setControlTime(LocalDateTime.now());
                    log.setRemark(remark);
                    log.setAddTime(LocalDateTime.now());
                    log.setAddWho(SecurityUtils.getUserInfo().getUserName());
                    log.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                    efWarehouseLogMapper.insertEfWarehouseLog(log);
                }
            }else{
                msg = "未查询到该申请数据";
                flag = false;
            }
        }else{
            msg = "没有接收到预警估值同步数据";
            flag = false;
        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }

}
