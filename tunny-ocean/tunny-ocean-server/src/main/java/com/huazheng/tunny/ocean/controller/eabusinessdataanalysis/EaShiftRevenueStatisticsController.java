package com.huazheng.tunny.ocean.controller.eabusinessdataanalysis;

import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.dto.eabillmain.EaFeeDTO;
import com.huazheng.tunny.ocean.api.entity.eabusinessdataanalysis.EaShiftExpenseDetail;
import com.huazheng.tunny.ocean.api.entity.eabusinessdataanalysis.EaShiftGrossProfitDetail;
import com.huazheng.tunny.ocean.api.entity.eabusinessdataanalysis.EaShiftRevenueDetail;
import com.huazheng.tunny.ocean.api.entity.eabusinessdataanalysis.EaShiftRevenueStatistics;
import com.huazheng.tunny.ocean.service.eabusinessdataanalysis.EaShiftRevenueStatisticsService;
import com.huazheng.tunny.ocean.util.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 班次收入统计控制器
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@RestController
@RequestMapping("/shift/revenue/statistics")
public class EaShiftRevenueStatisticsController {

    @Autowired
    private EaShiftRevenueStatisticsService shiftRevenueStatisticsService;

    /**
     * 分页查询班次收入统计
     *
     * @param params 查询参数
     * @return 分页结果
     */
    @GetMapping("/page")
    public R<Page<EaShiftRevenueStatistics>> page(@RequestParam Map<String, Object> params) {
        Query query = new Query(params);
        Page<EaShiftRevenueStatistics> page = shiftRevenueStatisticsService.page(query);
        return new R<>(page);
    }

    /**
     * 查询班次收入明细
     *
     * @param param 参数
     * @return 收入明细列表
     */
    @GetMapping("/incomesDetailsList")
    public R<List<EaShiftRevenueDetail>> selectIncomesDetails(EaShiftRevenueDetail param) {
        return shiftRevenueStatisticsService.selectIncomesDetails(param);
    }

    /**
     * 查询班次支出明细
     *
     * @param param 参数
     * @return 支出明细列表
     */
    @GetMapping("/expensesDetailsList")
    public R<List<EaShiftExpenseDetail>> selectExpensesDetails(EaShiftExpenseDetail param) {
        return shiftRevenueStatisticsService.selectExpensesDetails(param);
    }

    /**
     * 查询班次还原收入明细
     *
     * @param param 参数
     * @return 还原收入明细列表
     */
    @GetMapping("/restoreIncomesDetailsList")
    public R<List<EaShiftRevenueDetail>> selectRestoreIncomesDetails(EaShiftRevenueDetail param) {
        return shiftRevenueStatisticsService.selectRestoreIncomesDetails(param);
    }

    /**
     * 查询班次毛利明细
     *
     * @param param 参数
     * @return 毛利明细列表
     */
    @GetMapping("/grossProfitDetailsList")
    public R<Map<String, Object>> selectGrossProfitDetails(EaShiftGrossProfitDetail param) {
        return shiftRevenueStatisticsService.selectGrossProfitDetails(param);
    }

    /**
     * 查询班次费用明细
     *
     * @param eaFeeDTO 费用参数
     * @return 费用明细列表
     */
    @GetMapping("/feeDetailsList")
    public R<Map<String, Object>> selectFeeDetails(EaFeeDTO eaFeeDTO) {
        return shiftRevenueStatisticsService.selectFeeDetails(eaFeeDTO);
    }

    /**
     * 根据班次号、平台编码和客户编码查询箱号列表
     *
     * @param params 查询参数(shiftNo, platformCode, customerCode)
     * @return 箱号列表
     */
    @GetMapping("/containerNumbers")
    public R<List<String>> selectContainerNumbers(@RequestParam Map<String, Object> params) {
        return shiftRevenueStatisticsService.selectContainerNumbers(params);
    }

    /**
     * 箱维度毛利总计
     *
     * @param param 查询参数
     * @return R<EaShiftGrossProfitDetail>
     * <AUTHOR>
     * @since 2025/7/30 17:31
     **/
    @GetMapping("/grossProfitContainerList")
    public R<EaShiftGrossProfitDetail> grossProfitContainerList(EaShiftGrossProfitDetail param) {
        return shiftRevenueStatisticsService.grossProfitContainerList(param);
    }
}
