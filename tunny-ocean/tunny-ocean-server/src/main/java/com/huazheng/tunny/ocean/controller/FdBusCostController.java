package com.huazheng.tunny.ocean.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.FdBusCostDTO;
import com.huazheng.tunny.ocean.api.dto.FdBusCostInfoDTO;
import com.huazheng.tunny.ocean.api.dto.FdBusCostMainDTO;
import com.huazheng.tunny.ocean.api.dto.FdBusCostMainGroupDTO;
import com.huazheng.tunny.ocean.api.entity.FdBusCost;
import com.huazheng.tunny.ocean.api.vo.BpmVo;
import com.huazheng.tunny.ocean.service.FdBusCostService;
import com.huazheng.tunny.ocean.service.impl.FdBusCostExcelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 业务流程单主表
 *
 * <AUTHOR>
 * @date 2024-05-23 17:06:31
 */
@Slf4j
@RestController
@RequestMapping("/fdbuscost")
public class FdBusCostController {

    @Autowired
    private FdBusCostService fdBusCostService;

    @Autowired
    private FdBusCostExcelService fdBusCostExcelService;


    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page<FdBusCostDTO> page(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return fdBusCostService.selectFdBusCostPage(new Query<>(params));
    }

    /**
     * 信息
     *
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R<FdBusCostInfoDTO> info(@PathVariable("id") Integer id) {
        FdBusCostInfoDTO fdBusCost = fdBusCostService.selectFdBusCostById(id);
        return new R<>(fdBusCost);
    }

    /**
     * 保存
     *
     * @param fdBusCost
     * @return R
     */
    @PostMapping
    public R save(@RequestBody FdBusCost fdBusCost) {
        fdBusCostService.insertFdBusCost(fdBusCost);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 保存附件
     *
     * @param fdBusCost
     * @return R
     */
    @PostMapping("/saveFiles")
    public R saveFiles(@RequestBody FdBusCost fdBusCost) {
        return fdBusCostService.saveFiles(fdBusCost);
    }

    /**
     * 提交
     *
     * @param fdBusCost
     * @return R
     */
    @PostMapping("/commit")
    public R commit(@RequestBody FdBusCost fdBusCost) {
        return fdBusCostService.commit(fdBusCost);
    }

    /**
     * 追加费用提交
     *
     * @param fdBusCost
     * @return R
     */
    @PostMapping("/addFeesCommit")
    public R addFeesCommit(@RequestBody FdBusCost fdBusCost) {
        return fdBusCostService.addFeesCommit(fdBusCost);
    }

    /**
     * 审核同意
     *
     * @param fdBusCost
     * @return R
     */
    @PostMapping("/audit")
    public R audit(@RequestBody FdBusCost fdBusCost) {
        return fdBusCostService.audit(fdBusCost);
    }

    /**
     * 审核驳回
     *
     * @param fdBusCost
     * @return R
     */
    @PostMapping("/reject")
    public R reject(@RequestBody FdBusCost fdBusCost) {
        return fdBusCostService.reject(fdBusCost);
    }

    /**
     * 修改
     *
     * @param fdBusCost
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody FdBusCost fdBusCost) {
        fdBusCostService.updateFdBusCost(fdBusCost);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 查询审批信息
     *
     * @param fdBusCost
     * @return R
     */
    @PostMapping("/getFullNodeInfo")
    public List<BpmVo> getFullNodeInfo(@RequestBody FdBusCost fdBusCost) {
        return fdBusCostService.getFullNodeInfo(fdBusCost);
    }


    /**
     * 删除
     *
     * @param id
     * @return R
     */
    @GetMapping("/del/{id}")
    public R delete(@PathVariable Integer id) {
        fdBusCostService.deleteById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody List<Integer> ids) {
        fdBusCostService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 导出EXCEL
     *
     * @return
     */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {

        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<FdBusCost> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<FdBusCost> list = fdBusCostService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), FdBusCost.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }

    /**
     * 导出业务流程单
     *
     * @return
     */
    @PostMapping("/exportBusProcess")
    public void exportBusProcess(@RequestBody String costCode, HttpServletResponse response) throws Exception {
        fdBusCostService.exportBusProcess(costCode, response);
    }

    /**
     * 导出应收费用明细模板
     *
     * @return
     */
    @PostMapping("/exportReceiveTemplate")
    public void exportReceiveTemplate(@RequestBody FdBusCost fdBusCost, HttpServletResponse response) throws Exception {
        fdBusCostService.exportReceiveTemplate(fdBusCost, response);
    }

    /**
     * 导出应收费用明细模板(自定义模板)
     *
     * @return
     */
    @PostMapping("/exportReceiveCustomizeTemplate")
    public void exportReceiveCustomizeTemplate(@RequestBody FdBusCost fdBusCost, HttpServletResponse response) throws Exception {
        fdBusCostService.exportReceiveCustomizeTemplate(fdBusCost, response);
    }

    /**
     * 导出应付费用明细模板
     *
     * @return
     */
    @PostMapping("/exportPayTemplate")
    public void exportPayTemplate(@RequestBody FdBusCost fdBusCost, HttpServletResponse response) throws Exception {
        fdBusCostService.exportPayTemplate(fdBusCost, response);
    }

    /**
     * 导出应收费用明细模板（台账模板）
     *
     * @return
     */
    @PostMapping("/exportReceiveTzTemplate")
    public void exportReceiveTzTemplate(@RequestBody FdBusCost fdBusCost, HttpServletResponse response) throws Exception {
        fdBusCostService.exportReceiveTzTemplate(fdBusCost, response);
    }

    /**
     * 导出应收费用明细模板（济南模板）
     *
     * @return
     */
    @PostMapping("/exportReceiveJnTemplate")
    public void exportReceiveJnTemplate(@RequestBody FdBusCost fdBusCost, HttpServletResponse response) throws Exception {
        fdBusCostService.exportReceiveJnTemplate(fdBusCost, response);
    }

    /**
     * 导出应付费用明细模板（台账模板）
     *
     * @return
     */
    @PostMapping("/exportPayTzTemplate")
    public void exportPayTzTemplate(@RequestBody FdBusCost fdBusCost, HttpServletResponse response) throws Exception {
        fdBusCostService.exportPayTzTemplate(fdBusCost, response);
    }


    /**
     * 导入应收费用明细模板
     *
     * @param fdBusCost
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/importedReceive")
    public R importedReceive(@ModelAttribute FdBusCost fdBusCost, @RequestParam("file") MultipartFile file) throws Exception {
        return fdBusCostService.importedDetail(fdBusCost, file, "0");
    }

    /**
     * 导入应付费用明细模板
     *
     * @param fdBusCost
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/importedPay")
    public R importedPay(@ModelAttribute FdBusCost fdBusCost, @RequestParam("file") MultipartFile file) throws Exception {
        return fdBusCostService.importedDetail(fdBusCost, file, "1");
    }

    /**
     * 导入应收费用明细模板（台账模板）
     *
     * @param fdBusCost
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/importedReceiveTzTemplate")
    public R importedReceiveTzTemplate(@ModelAttribute FdBusCost fdBusCost, @RequestParam("file") MultipartFile file) {
        return fdBusCostService.importedDetailTzTemplate(fdBusCost, file, "0");
    }

    /**
     * 导入应收费用明细模板（济南模板）
     *
     * @param fdBusCost
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/importedReceiveJnTemplate")
    public R importedReceiveJnTemplate(@ModelAttribute FdBusCost fdBusCost, @RequestParam("file") MultipartFile file) throws Exception {
        return fdBusCostService.importedReceiveJnTemplate(fdBusCost, file, "0");
    }

    /**
     * 导入应付费用明细模板（台账模板）
     *
     * @param fdBusCost
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/importedPayTzTemplate")
    public R importedPayTzTemplate(@ModelAttribute FdBusCost fdBusCost, @RequestParam("file") MultipartFile file) throws Exception {
        return fdBusCostService.importedDetailTzTemplate(fdBusCost, file, "1");
    }

    /**
     * 导入应收费用明细模板(自定义模板)
     *
     * @param fdBusCost
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/importedReceiveCustomize")
    public R importedReceiveCustomize(@ModelAttribute FdBusCost fdBusCost, @RequestParam("file") MultipartFile file) throws Exception {
        return fdBusCostService.importedReceiveCustomize(fdBusCost, file, "0");
    }

    /**
     * 获取业务流程单
     *
     * @param fdBusCost
     * @return
     * @throws Exception
     */
    @PostMapping("/busCostMainList")
    public R<List<FdBusCostMainDTO>> busCostMainList(@RequestBody FdBusCost fdBusCost) {
        return fdBusCostService.busCostMainList(fdBusCost);
    }

    /**
     * 获取业务流程单数量
     *
     * @param fdBusCost
     * @return
     * @throws Exception
     */
    @PostMapping("/busCostMainNum")
    public R<List<FdBusCostMainDTO>> busCostMainNum(@RequestBody FdBusCost fdBusCost) {
        return fdBusCostService.busCostMainNum(fdBusCost);
    }


    /**
     * 导出业务流程单PDF
     *
     * @return
     */
    @PostMapping("/exportBusProcessToPDF")
    public void exportBusProcessToPDF(@RequestBody FdBusCost fdBusCost, HttpServletResponse response) throws Exception {
        fdBusCostService.exportBusProcessToPDF(fdBusCost, response);
    }

    /**
     * 导出业务流程单PDF2
     *
     * @return
     */
    @PostMapping("/exportBusProcessToPDF2")
    public void exportBusProcessToPDF2(@RequestBody FdBusCostMainGroupDTO fdBusCostMainGroupDTO, HttpServletResponse response) throws Exception {
        fdBusCostService.exportBusProcessToPDF2(fdBusCostMainGroupDTO, response);
    }

    /**
     * 导出业务流程单的图片
     *
     * @Param:
     * @Return:
     * @Author: Howe
     * @Date: 2024/7/19 下午3:05
     **/
    @PostMapping("/exportBusProcessToPNG")
    public R exportBusProcessToPNG(@RequestBody FdBusCostMainGroupDTO fdBusCostMainGroupDTO) {
        return R.success(fdBusCostService.exportBusProcessToPng(fdBusCostMainGroupDTO));
    }

    /**
     * 获取业务流程单-省
     *
     * @param fdBusCost
     * @return
     * @throws Exception
     */
    @PostMapping("/busCostMainListShift")
    public R<List<FdBusCostMainDTO>> busCostMainListShift(@RequestBody FdBusCost fdBusCost) {
        return fdBusCostService.busCostMainListShift(fdBusCost);
    }

    /**
     * 获取业务流程单数量-省
     *
     * @param fdBusCost
     * @return
     * @throws Exception
     */
    @PostMapping("/busCostMainNumShift")
    public R<List<FdBusCostMainDTO>> busCostMainNumShift(@RequestBody FdBusCost fdBusCost) {
        return fdBusCostService.busCostMainNumShift(fdBusCost);
    }


    /**
     * 导出业务流程单PDF-省
     *
     * @return
     */
    @PostMapping("/exportBusProcessShiftToPDF")
    public void exportBusProcessShiftToPDF(@RequestBody FdBusCost fdBusCost, HttpServletResponse response) throws Exception {
        fdBusCostService.exportBusProcessShiftToPDF(fdBusCost, response);
    }

    /**
     * 导出业务流程单PDF-省2
     *
     * @return
     */
    @PostMapping("/exportBusProcessShiftToPDF2")
    public void exportBusProcessShiftToPDF2(@RequestBody FdBusCostMainGroupDTO fdBusCostMainGroupDTO, HttpServletResponse response) throws Exception {
        fdBusCostService.exportBusProcessToPDF2(fdBusCostMainGroupDTO, response);
    }

    /**
     * 导出模板-直接导入并生成业务流程单
     *
     * @return
     */
    @PostMapping("/exportTemplateForYw")
    public void exportTemplateForYw(HttpServletResponse response) throws Exception {
        fdBusCostService.exportTemplateForYwTwo(response);
    }

    /**
     * 导出模板-多客户直接导入并生成业务流程单
     *
     * @return
     */
    @PostMapping("/exportTemplateForYwThree")
    public void exportTemplateForYwThree(HttpServletResponse response) throws Exception {
        fdBusCostService.exportTemplateForYwThree(response);
    }

    /**
     * 导入并生成业务流程单
     *
     * @return
     */
    @PostMapping("/importedTemplateForYw")
    @Transactional(rollbackFor = Exception.class)
    public R importedTemplateForYw(@RequestParam("file") MultipartFile file) throws Exception {
        return fdBusCostExcelService.createBusinessFlowBySingleCustomer(file);
    }
    @PostMapping("/importedTemplateForYwTwo")
    @Transactional(rollbackFor = Exception.class)
    public R importedTemplateForYwTwo(@RequestParam("file") MultipartFile file) throws Exception {
        return fdBusCostService.importedTemplateForYwTwo(file);
    }

    /**
     * 多客户导入并生成业务流程单
     *
     * @return
     */
    @PostMapping("/importedTemplateForYwThree")
    @Transactional(rollbackFor = Exception.class)
    public R importedTemplateForYwThree(@RequestParam("file") MultipartFile file) throws Exception {
        return fdBusCostExcelService.createBusinessFlowByMultipleCustomer(file);
    }

    /**
     * 变更汇率-省应收
     *
     * @param fdBusCost
     * @return
     * @throws Exception
     */
    @PostMapping("/changeRateProvinceReceive")
    public R changeRateProvinceReceive(@RequestBody FdBusCost fdBusCost) {
        return fdBusCostService.changeRate(fdBusCost, "provinceReceive");
    }

    /**
     * 变更汇率-省应付
     *
     * @param fdBusCost
     * @return
     * @throws Exception
     */
    @PostMapping("/changeRateProvincePay")
    public R changeRateProvincePay(@RequestBody FdBusCost fdBusCost) {
        return fdBusCostService.changeRate(fdBusCost, "provincePay");
    }

    /**
     * 变更汇率-市应收
     *
     * @param fdBusCost
     * @return
     * @throws Exception
     */
    @PostMapping("/changeRateCityReceive")
    public R changeRateCityReceive(@RequestBody FdBusCost fdBusCost) {
        return fdBusCostService.changeRate(fdBusCost, "cityReceive");
    }

    /**
     * 台账提交校验业务流程单
     *
     * @param fdBusCost
     * @return
     * @throws Exception
     */
    @PostMapping("/tzCommitCheck")
    public R tzCommitCheck(@RequestBody FdBusCost fdBusCost) {
        return fdBusCostService.tzCommitCheck(fdBusCost);
    }

}
