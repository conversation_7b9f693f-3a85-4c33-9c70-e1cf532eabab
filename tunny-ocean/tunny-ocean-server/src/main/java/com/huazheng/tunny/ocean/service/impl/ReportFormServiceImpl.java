package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.QueryMap;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.FdBusCostDetail;
import com.huazheng.tunny.ocean.api.entity.Shifmanagement;
import com.huazheng.tunny.ocean.api.entity.SubsidyManager;
import com.huazheng.tunny.ocean.mapper.FdBusCostDetailMapper;
import com.huazheng.tunny.ocean.mapper.ReportFormMapper;
import com.huazheng.tunny.ocean.mapper.SubsidyManagerMapper;
import com.huazheng.tunny.ocean.service.ReportFormService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 报表
 * @Author: 徐瑞
 * @Date: 2024-7-24 15:35:45
 */
@Service("reportFormService")
public class ReportFormServiceImpl implements ReportFormService {

    @Resource
    private ReportFormMapper reportFormMapper;
    @Autowired
    private SubsidyManagerMapper subsidyManagerMapper;
    @Autowired
    private FdBusCostDetailMapper fdBusCostDetailMapper;
    @Value("${path.windowsPath}")
    private String windowsPath;
    @Value("${path.linuxPath}")
    private String linuxPath;

    /**
     * @Description: 班列统计表(省)
     * @Author: 徐瑞
     * @Date: 2024-7-24 15:35:45
     */
    @Override
    public Page provincialTrainStatisticsForm(QueryMap query) {
        if (StrUtil.isBlank(query.getOrderByField())) {
            query.setOrderByField("x.planShipTime DESC, IF(x.provinceShiftNo IS NULL, 0, 1) DESC, x.provinceShiftNo");
            query.setAsc(Boolean.FALSE);
        }
        query.setRecords(reportFormMapper.provincialTrainStatisticsForm(query, query.getCondition()));
        return query;
    }

    @Override
    public void provincialTrainStatisticsFormExport(Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<Map<String, Object>> maps = reportFormMapper.provincialTrainStatisticsForm(params);
        String templateFileName = "provincialTrainStatisticsFormExport.xlsx";

        String osName = System.getProperties().getProperty("os.name");

        StringBuilder sb = new StringBuilder();
        if ("Linux".equals(osName)) {
            sb.append(linuxPath).append(templateFileName);
        } else {
            sb.append(windowsPath).append(templateFileName);
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 使用 UTF-8 编码处理文件名，确保中文文件名在浏览器中正确显示
        String encodedFileName = URLEncoder.encode("班列统计表(省).xlsx", "UTF-8");
        encodedFileName = encodedFileName.replaceAll("\\+", "%20"); // 处理空格问题
        response.setHeader("Content-Disposition", "attachment;filename=\"" + encodedFileName + "\"");
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");

        com.alibaba.excel.ExcelWriter excelWriter = null;
        excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(sb.toString()).build();
        WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "班列统计表(省)").build();
        if (CollUtil.isNotEmpty(maps)) {
            excelWriter.fill(maps, writeSheet1);
        }

        excelWriter.finish();
    }

    /**
     * @Description: 班列统计表(市)
     * @Author: 徐瑞
     * @Date: 2024-7-24 15:35:45
     */
    @Override
    public Page cityTrainStatisticsForm(QueryMap query) {
        if (StrUtil.isBlank(query.getOrderByField())) {
            query.setOrderByField("x.planShipTime DESC, IF(x.provinceShiftNo IS NULL, 0, 1) DESC, x.provinceShiftNo");
            query.setAsc(Boolean.FALSE);
        }
        query.setRecords(reportFormMapper.cityTrainStatisticsForm(query, query.getCondition()));
        return query;
    }

    @Override
    public void cityTrainStatisticsFormExport(Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<Map<String, Object>> maps = reportFormMapper.cityTrainStatisticsForm(params);
        String templateFileName = "cityTrainStatisticsFormExport.xlsx";
        String osName = System.getProperties().getProperty("os.name");

        StringBuilder sb = new StringBuilder();
        if ("Linux".equals(osName)) {
            sb.append(linuxPath).append(templateFileName);
        } else {
            sb.append(windowsPath).append(templateFileName);
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 使用 UTF-8 编码处理文件名，确保中文文件名在浏览器中正确显示
        String encodedFileName = URLEncoder.encode("班列统计表(市).xlsx", "UTF-8");
        encodedFileName = encodedFileName.replaceAll("\\+", "%20"); // 处理空格问题
        response.setHeader("Content-Disposition", "attachment;filename=\"" + encodedFileName + "\"");
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");

        com.alibaba.excel.ExcelWriter excelWriter = null;
        excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(sb.toString()).build();
        WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "班列统计表(市)").build();
        if (CollUtil.isNotEmpty(maps)) {
            excelWriter.fill(maps, writeSheet1);
        }

        excelWriter.finish();
    }

    /**
     * @Description: 发运明细表(省)
     * @Author: 徐瑞
     * @Date: 2024-7-24 15:35:45
     */
    @Override
    public Page provincialShippingDetailForm(QueryMap query) {
        if (StrUtil.isBlank(query.getOrderByField())) {
            query.setOrderByField("x.planShipTime DESC, IF(x.provinceShiftNo IS NULL, 0, 1) DESC, x.provinceShiftNo");
            query.setAsc(Boolean.FALSE);
        }
        List<Map<String, Object>> list = reportFormMapper.provincialShippingDetailForm(query, query.getCondition());
        if (CollUtil.isNotEmpty(list)) {
            getSubsidyManager(list, query.getCondition());
        }
        query.setRecords(list);
        return query;
    }

    @Override
    public void provincialShippingDetailFormExport(Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<Map<String, Object>> list = reportFormMapper.provincialShippingDetailForm(params);
        if (CollUtil.isNotEmpty(list)) {
            getSubsidyManager(list, params);
        }
        String templateFileName = "provincialShippingDetailFormExport.xlsx";
        String osName = System.getProperties().getProperty("os.name");

        StringBuilder sb = new StringBuilder();
        if ("Linux".equals(osName)) {
            sb.append(linuxPath).append(templateFileName);
        } else {
            sb.append(windowsPath).append(templateFileName);
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 使用 UTF-8 编码处理文件名，确保中文文件名在浏览器中正确显示
        String encodedFileName = URLEncoder.encode("发运明细表(省).xlsx", "UTF-8");
        encodedFileName = encodedFileName.replaceAll("\\+", "%20"); // 处理空格问题
        response.setHeader("Content-Disposition", "attachment;filename=\"" + encodedFileName + "\"");
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");

        com.alibaba.excel.ExcelWriter excelWriter = null;
        excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(sb.toString()).build();
        WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "发运明细表(省)").build();
        if (CollUtil.isNotEmpty(list)) {
            excelWriter.fill(list, writeSheet1);
        }

        excelWriter.finish();

    }

    private static String getProvinceCode(SecruityUser userInfo) {
        String supPlatformCode = userInfo.getSupPlatformCode();
        if (supPlatformCode.contains("-")) {
            supPlatformCode = supPlatformCode.split("-")[0];
        }
        return supPlatformCode;
    }

    /*
     * 计算补贴
     * */
    private void getSubsidyManager(List<Map<String, Object>> list, Map<String, Object> params) {
        String supPlatformCode = null;

        SecruityUser userInfo = SecurityUtils.getUserInfo();
        if ("2".equals(userInfo.getPlatformLevel())) {
            supPlatformCode = userInfo.getPlatformCode();
        } else {
            supPlatformCode = getProvinceCode(userInfo);
        }
        Shifmanagement sel = new Shifmanagement();
        sel.setDeleteFlag("N");
        //市补贴
//        List<SubsidyManager> list1 = subsidyManagerMapper.selectSubsidyManagerListByShift(sel);
        //省补贴
        sel.setPlatformCode(supPlatformCode);
        List<SubsidyManager> list2 = subsidyManagerMapper.selectSubsidyManagerListByShift(sel);

        FdBusCostDetail fdBusCostDetail = new FdBusCostDetail();
        fdBusCostDetail.setReceiveCode(supPlatformCode);
        if (params.containsKey("platformCode")) {
            fdBusCostDetail.setPayCode(String.valueOf(params.get("platformCode")));
        }
        if (params.containsKey("planShipStartDate")) {
            fdBusCostDetail.setPlanShipStartDate(String.valueOf(params.get("planShipStartDate")));
        }
        if (params.containsKey("planShipEndDate")) {
            fdBusCostDetail.setPlanShipEndDate(String.valueOf(params.get("planShipEndDate")));
        }
        //获取补贴箱号
        List<String> containerNumbers = fdBusCostDetailMapper.getSubsidyContainerNumber(fdBusCostDetail);
        if (CollUtil.isEmpty(containerNumbers)) {
            return;
        }

        for (Map<String, Object> map : list
        ) {
            String str = String.valueOf(map.get("str"));
            BigDecimal subsidyAmount = BigDecimal.ZERO;
            BigDecimal subsidyStandards = BigDecimal.ZERO;
            if (StrUtil.isNotBlank(str) && containerNumbers.contains(str)) {
                String shippingLineCode = String.valueOf(map.get("shippingLineCode"));
                String containerType = String.valueOf(map.get("containerType"));
                String trip = String.valueOf(map.get("trip"));
                Date planShipTime = Date.valueOf(String.valueOf(map.get("planShipTime")));
                Date startTime = "去程".equals(trip) || "回程".equals(trip) ? planShipTime : null;
                if (CollUtil.isNotEmpty(list2)) {
                    for (SubsidyManager subsidyManager : list2
                    ) {
                        if (checkBooleanProvincial(shippingLineCode, startTime, subsidyManager)) {
                            subsidyAmount = subsidyAmount.add(calculateSubsidyAmount(subsidyManager, containerType));
                            subsidyStandards = subsidyStandards.add(subsidyManager.getSubsidyAmount());
                            break;
                        }
                    }
                }
            }

            map.put("subsidyAmount", subsidyAmount);
            map.put("subsidyStandards", subsidyStandards);
        }
    }

    private static boolean checkBooleanProvincial(String shippingLineCode, Date startTime, SubsidyManager subsidyManager) {
        return StrUtil.isNotBlank(subsidyManager.getShippingLineCode()) && subsidyManager.getShippingLineCode().equals(shippingLineCode) && startTime.compareTo(subsidyManager.getSubsidyStartTime()) >= 0 && startTime.compareTo(subsidyManager.getSubsidyEndTime()) <= 0;
    }

    /**
     * @Description: 计算补贴金额
     */
    private BigDecimal calculateSubsidyAmount(SubsidyManager subsidyManager, String containerType) {
        if (StrUtil.isNotBlank(containerType)) {
            if (containerType.contains("20")) {
                // 20尺箱的补贴是原补贴的0.5倍
                return subsidyManager.getSubsidyAmount().multiply(BigDecimal.valueOf(0.5));
            } else if (containerType.contains("40") || containerType.contains("45")) {
                return subsidyManager.getSubsidyAmount();
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * @Description: 发运明细表(市)
     * @Author: 徐瑞
     * @Date: 2024-7-24 15:35:45
     */
    @Override
    public Page cityShippingDetailForm(QueryMap query) {
        if (StrUtil.isBlank(query.getOrderByField())) {
            query.setOrderByField("x.planShipTime DESC, IF(x.provinceShiftNo IS NULL, 0, 1) DESC, x.provinceShiftNo");
            query.setAsc(Boolean.FALSE);
        }
        List<Map<String, Object>> list = reportFormMapper.cityShippingDetailForm(query, query.getCondition());
        if (CollUtil.isNotEmpty(list)) {
            getSubsidyManager(list, query.getCondition());
        }
        query.setRecords(list);
        return query;
    }

    @Override
    public void cityShippingDetailFormExport(Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<Map<String, Object>> list = reportFormMapper.cityShippingDetailForm(params);
        if (CollUtil.isNotEmpty(list)) {
            getSubsidyManager(list, params);
        }

        String templateFileName = "cityShippingDetailFormExport.xlsx";
        String osName = System.getProperties().getProperty("os.name");

        StringBuilder sb = new StringBuilder();
        if ("Linux".equals(osName)) {
            sb.append(linuxPath).append(templateFileName);
        } else {
            sb.append(windowsPath).append(templateFileName);
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 使用 UTF-8 编码处理文件名，确保中文文件名在浏览器中正确显示
        String encodedFileName = URLEncoder.encode("发运明细表(市).xlsx", "UTF-8");
        encodedFileName = encodedFileName.replaceAll("\\+", "%20"); // 处理空格问题
        response.setHeader("Content-Disposition", "attachment;filename=\"" + encodedFileName + "\"");
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");

        com.alibaba.excel.ExcelWriter excelWriter = null;
        excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(sb.toString()).build();
        WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "发运明细表(市)").build();
        if (CollUtil.isNotEmpty(list)) {
            excelWriter.fill(list, writeSheet1);
        }

        excelWriter.finish();
    }

    @Override
    public Page billDetailForm(QueryMap query) {
        List<Map<String, Object>> list = reportFormMapper.billDetailForm(query, query.getCondition());
        query.setRecords(list);
        return query;
    }

    @Override
    public void billDetailFormExport(QueryMap query, HttpServletResponse res) throws Exception {
        List<Map<String, Object>> list = reportFormMapper.billDetailFormList(query.getCondition());
        if (CollUtil.isNotEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                list.get(i).put("rowId", i + 1);
            }
        }
        com.alibaba.excel.ExcelWriter excelWriter = null;
        String templateFileName = "billDetailFormExport.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        StringBuilder sb = new StringBuilder();
        if ("Linux".equals(osName)) {
            sb.append(linuxPath).append(templateFileName);
        } else {
            sb.append(windowsPath).append(templateFileName);
        }
        String fileName = URLEncoder.encode("发运明细表导出", "UTF-8").replaceAll("\\+", "%20");
        res.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        res.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + ".xlsx\"");

        excelWriter = EasyExcel.write(res.getOutputStream()).withTemplate(sb.toString()).build();
        WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "发运明细表").build();
        if (CollUtil.isNotEmpty(list)) {
            excelWriter.fill(list, writeSheet1);
        }
        excelWriter.finish();
    }
}
