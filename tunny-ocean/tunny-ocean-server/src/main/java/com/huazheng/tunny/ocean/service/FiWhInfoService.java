package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.FiWhInfo;

import java.util.List;

/**
 * 仓单入库申请表 服务接口层
 *
 * <AUTHOR>
 * @date 2022-11-09 14:01:58
 */
public interface FiWhInfoService extends IService<FiWhInfo> {
    /**
     * 查询仓单入库申请表信息
     *
     * @param rowId 仓单入库申请表ID
     * @return 仓单入库申请表信息
     */
    public FiWhInfo selectFiWhInfoById(String rowId);

    /**
     * 查询仓单入库申请表列表
     *
     * @param fiWhInfo 仓单入库申请表信息
     * @return 仓单入库申请表集合
     */
    public List<FiWhInfo> selectFiWhInfoList(FiWhInfo fiWhInfo);


    /**
     * 分页模糊查询仓单入库申请表列表
     * @return 仓单入库申请表集合
     */
    public Page selectFiWhInfoListByLike(Query query);



    /**
     * 新增仓单入库申请表
     *
     * @param fiWhInfo 仓单入库申请表信息
     * @return 结果
     */
    public int insertFiWhInfo(FiWhInfo fiWhInfo);

    /**
     * 修改仓单入库申请表
     *
     * @param fiWhInfo 仓单入库申请表信息
     * @return 结果
     */
    public int updateFiWhInfo(FiWhInfo fiWhInfo);

    /**
     * 删除仓单入库申请表
     *
     * @param rowId 仓单入库申请表ID
     * @return 结果
     */
    public int deleteFiWhInfoById(String rowId);

    /**
     * 批量删除仓单入库申请表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiWhInfoByIds(Integer[] rowIds);

}

