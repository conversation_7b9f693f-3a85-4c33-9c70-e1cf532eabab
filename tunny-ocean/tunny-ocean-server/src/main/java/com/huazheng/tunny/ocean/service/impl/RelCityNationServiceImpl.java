package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.RelCityNationMapper;
import com.huazheng.tunny.ocean.api.entity.RelCityNation;
import com.huazheng.tunny.ocean.service.RelCityNationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("relCityNationService")
public class RelCityNationServiceImpl extends ServiceImpl<RelCityNationMapper, RelCityNation> implements RelCityNationService {

    @Autowired
    private RelCityNationMapper relCityNationMapper;

    public RelCityNationMapper getRelCityNationMapper() {
        return relCityNationMapper;
    }

    public void setRelCityNationMapper(RelCityNationMapper relCityNationMapper) {
        this.relCityNationMapper = relCityNationMapper;
    }

    /**
     * 查询国别/城市关系表信息
     *
     * @param rowId 国别/城市关系表ID
     * @return 国别/城市关系表信息
     */
    @Override
    public RelCityNation selectRelCityNationById(String rowId)
    {
        return relCityNationMapper.selectRelCityNationById(rowId);
    }

    /**
     * 查询国别/城市关系表列表
     *
     * @param relCityNation 国别/城市关系表信息
     * @return 国别/城市关系表集合
     */
    @Override
    public List<RelCityNation> selectRelCityNationList(RelCityNation relCityNation)
    {
        return relCityNationMapper.selectRelCityNationList(relCityNation);
    }


    /**
     * 分页模糊查询国别/城市关系表列表
     * @return 国别/城市关系表集合
     */
    @Override
    public Page selectRelCityNationListByLike(Query query)
    {
        RelCityNation relCityNation =  BeanUtil.mapToBean(query.getCondition(), RelCityNation.class,false);
        query.setRecords(relCityNationMapper.selectRelCityNationListByLike(query,relCityNation));
        return query;
    }

    /**
     * 新增国别/城市关系表
     *
     * @param relCityNation 国别/城市关系表信息
     * @return 结果
     */
    @Override
    public int insertRelCityNation(RelCityNation relCityNation)
    {
        return relCityNationMapper.insertRelCityNation(relCityNation);
    }

    /**
     * 修改国别/城市关系表
     *
     * @param relCityNation 国别/城市关系表信息
     * @return 结果
     */
    @Override
    public int updateRelCityNation(RelCityNation relCityNation)
    {
        return relCityNationMapper.updateRelCityNation(relCityNation);
    }


    /**
     * 删除国别/城市关系表
     *
     * @param rowId 国别/城市关系表ID
     * @return 结果
     */
    public int deleteRelCityNationById(String rowId)
    {
        return relCityNationMapper.deleteRelCityNationById( rowId);
    };


    /**
     * 批量删除国别/城市关系表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteRelCityNationByIds(Integer[] rowIds)
    {
        return relCityNationMapper.deleteRelCityNationByIds( rowIds);
    }

}
