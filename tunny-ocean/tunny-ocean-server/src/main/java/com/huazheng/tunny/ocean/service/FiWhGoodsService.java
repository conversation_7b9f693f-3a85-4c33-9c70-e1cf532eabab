package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.FiWhGoods;

import java.util.List;

/**
 * 仓单入库货物 服务接口层
 *
 * <AUTHOR>
 * @date 2022-11-09 14:02:04
 */
public interface FiWhGoodsService extends IService<FiWhGoods> {
    /**
     * 查询仓单入库货物信息
     *
     * @param rowId 仓单入库货物ID
     * @return 仓单入库货物信息
     */
    public FiWhGoods selectFiWhGoodsById(String rowId);

    /**
     * 查询仓单入库货物列表
     *
     * @param fiWhGoods 仓单入库货物信息
     * @return 仓单入库货物集合
     */
    public List<FiWhGoods> selectFiWhGoodsList(FiWhGoods fiWhGoods);


    /**
     * 分页模糊查询仓单入库货物列表
     * @return 仓单入库货物集合
     */
    public Page selectFiWhGoodsListByLike(Query query);



    /**
     * 新增仓单入库货物
     *
     * @param fiWhGoods 仓单入库货物信息
     * @return 结果
     */
    public int insertFiWhGoods(FiWhGoods fiWhGoods);

    /**
     * 修改仓单入库货物
     *
     * @param fiWhGoods 仓单入库货物信息
     * @return 结果
     */
    public int updateFiWhGoods(FiWhGoods fiWhGoods);

    /**
     * 删除仓单入库货物
     *
     * @param rowId 仓单入库货物ID
     * @return 结果
     */
    public int deleteFiWhGoodsById(String rowId);

    public int deleteFiWhGoodsByWrCode(String wrCode);

    /**
     * 批量删除仓单入库货物
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiWhGoodsByIds(Integer[] rowIds);

}

