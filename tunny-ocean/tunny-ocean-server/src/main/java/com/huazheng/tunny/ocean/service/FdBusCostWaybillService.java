package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FdBusCostWaybill;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 业务流程单运单表 服务接口层
 *
 * <AUTHOR>
 * @date 2024-06-06 11:30:14
 */
public interface FdBusCostWaybillService extends IService<FdBusCostWaybill> {
    /**
     * 查询业务流程单运单表信息
     *
     * @param id 业务流程单运单表ID
     * @return 业务流程单运单表信息
     */
    public FdBusCostWaybill selectFdBusCostWaybillById(Integer id);

    /**
     * 查询业务流程单运单表列表
     *
     * @param fdBusCostWaybill 业务流程单运单表信息
     * @return 业务流程单运单表集合
     */
    public List<FdBusCostWaybill> selectFdBusCostWaybillList(FdBusCostWaybill fdBusCostWaybill);


    /**
     * 分页模糊查询业务流程单运单表列表
     * @return 业务流程单运单表集合
     */
    public Page selectFdBusCostWaybillListByLike(Query query);



    /**
     * 新增业务流程单运单表
     *
     * @param fdBusCostWaybill 业务流程单运单表信息
     * @return 结果
     */
    public int insertFdBusCostWaybill(FdBusCostWaybill fdBusCostWaybill);

    /**
     * 修改业务流程单运单表
     *
     * @param fdBusCostWaybill 业务流程单运单表信息
     * @return 结果
     */
    public int updateFdBusCostWaybill(FdBusCostWaybill fdBusCostWaybill);

    /**
     * 删除业务流程单运单表
     *
     * @param id 业务流程单运单表ID
     * @return 结果
     */
    public int deleteFdBusCostWaybillById(Integer id);

    /**
     * 批量删除业务流程单运单表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdBusCostWaybillByIds(Integer[] ids);

}

