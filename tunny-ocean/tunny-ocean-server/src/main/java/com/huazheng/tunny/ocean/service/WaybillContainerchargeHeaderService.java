package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.WaybillContainerchargeHeader;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 运单费用汇总表-挂账用 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-06 10:54:17
 */
public interface WaybillContainerchargeHeaderService extends IService<WaybillContainerchargeHeader> {
    /**
     * 查询运单费用汇总表-挂账用信息
     *
     * @param rowId 运单费用汇总表-挂账用ID
     * @return 运单费用汇总表-挂账用信息
     */
    public WaybillContainerchargeHeader selectWaybillContainerchargeHeaderById(String rowId);

    /**
     * 查询运单费用汇总表-挂账用列表
     *
     * @param waybillContainerchargeHeader 运单费用汇总表-挂账用信息
     * @return 运单费用汇总表-挂账用集合
     */
    public List<WaybillContainerchargeHeader> selectWaybillContainerchargeHeaderList(WaybillContainerchargeHeader waybillContainerchargeHeader);


    /**
     * 分页模糊查询运单费用汇总表-挂账用列表
     * @return 运单费用汇总表-挂账用集合
     */
    public Page selectWaybillContainerchargeHeaderListByLike(Query query);



    /**
     * 新增运单费用汇总表-挂账用
     *
     * @param waybillContainerchargeHeader 运单费用汇总表-挂账用信息
     * @return 结果
     */
    public int insertWaybillContainerchargeHeader(WaybillContainerchargeHeader waybillContainerchargeHeader);

    /**
     * 修改运单费用汇总表-挂账用
     *
     * @param waybillContainerchargeHeader 运单费用汇总表-挂账用信息
     * @return 结果
     */
    public int updateWaybillContainerchargeHeader(WaybillContainerchargeHeader waybillContainerchargeHeader);

    public int updateFee(WaybillContainerchargeHeader waybillContainerchargeHeader);
    /**
     * 删除运单费用汇总表-挂账用
     *
     * @param rowId 运单费用汇总表-挂账用ID
     * @return 结果
     */
    public int deleteWaybillContainerchargeHeaderById(String rowId);

    /**
     * 批量删除运单费用汇总表-挂账用
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteWaybillContainerchargeHeaderByIds(Integer[] rowIds);

}

