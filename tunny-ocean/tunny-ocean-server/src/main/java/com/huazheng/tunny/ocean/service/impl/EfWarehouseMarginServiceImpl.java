package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.controller.SignatureController;
import com.huazheng.tunny.ocean.mapper.EfWarehouseApplyMapper;
import com.huazheng.tunny.ocean.mapper.EfWarehouseDisposeinfoMapper;
import com.huazheng.tunny.ocean.mapper.EfWarehouseLogMapper;
import com.huazheng.tunny.ocean.mapper.EfWarehouseMarginMapper;
import com.huazheng.tunny.ocean.service.EfWarehouseMarginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service("efWarehouseMarginService")
public class EfWarehouseMarginServiceImpl extends ServiceImpl<EfWarehouseMarginMapper, EfWarehouseMargin> implements EfWarehouseMarginService {

    @Autowired
    private EfWarehouseMarginMapper efWarehouseMarginMapper;
    @Autowired
    private SignatureController signatureController;
    @Autowired
    private EfWarehouseApplyMapper efWarehouseApplyMapper;
    @Autowired
    private EfWarehouseLogMapper efWarehouseLogMapper;

    DecimalFormat df = new DecimalFormat("#.00");
    /**
     * 查询保证金信息信息
     *
     * @param rowId 保证金信息ID
     * @return 保证金信息信息
     */
    @Override
    public EfWarehouseMargin selectEfWarehouseMarginById(String rowId)
    {
        return efWarehouseMarginMapper.selectEfWarehouseMarginById(rowId);
    }

    /**
     * 查询保证金信息列表
     *
     * @param efWarehouseMargin 保证金信息信息
     * @return 保证金信息集合
     */
    @Override
    public List<EfWarehouseMargin> selectEfWarehouseMarginList(EfWarehouseMargin efWarehouseMargin)
    {
        return efWarehouseMarginMapper.selectEfWarehouseMarginList(efWarehouseMargin);
    }


    /**
     * 分页模糊查询保证金信息列表
     * @return 保证金信息集合
     */
    @Override
    public Page selectEfWarehouseMarginListByLike(Query query)
    {
        EfWarehouseMargin efWarehouseMargin =  BeanUtil.mapToBean(query.getCondition(), EfWarehouseMargin.class,false);
        query.setRecords(efWarehouseMarginMapper.selectEfWarehouseMarginListByLike(query,efWarehouseMargin));
        return query;
    }

    /**
     * 新增保证金信息
     *
     * @param efWarehouseMargin 保证金信息信息
     * @return 结果
     */
    @Override
    public int insertEfWarehouseMargin(EfWarehouseMargin efWarehouseMargin)
    {
        return efWarehouseMarginMapper.insertEfWarehouseMargin(efWarehouseMargin);
    }

    /**
     * 修改保证金信息
     *
     * @param efWarehouseMargin 保证金信息信息
     * @return 结果
     */
    @Override
    public int updateEfWarehouseMargin(EfWarehouseMargin efWarehouseMargin)
    {
        return efWarehouseMarginMapper.updateEfWarehouseMargin(efWarehouseMargin);
    }


    /**
     * 删除保证金信息
     *
     * @param rowId 保证金信息ID
     * @return 结果
     */
    public int deleteEfWarehouseMarginById(String rowId)
    {
        return efWarehouseMarginMapper.deleteEfWarehouseMarginById( rowId);
    };


    /**
     * 批量删除保证金信息对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfWarehouseMarginByIds(Integer[] rowIds)
    {
        return efWarehouseMarginMapper.deleteEfWarehouseMarginByIds( rowIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String syncMargin(EfWarehouseMargin efWarehouseMargin)
    {
        //插入操作记录
        EfWarehouseLog log = new EfWarehouseLog();
        String remark = "";

        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(efWarehouseMargin!=null) {
            final EfWarehouseApply apply = efWarehouseApplyMapper.selectEfWarehouseApplyByAssetCode(efWarehouseMargin.getAssetCode());
            if(apply!=null){
                efWarehouseMargin.setQlFinancingNo(apply.getQlFinancingNo());
                efWarehouseMargin.setAssetCode(apply.getAssetCode());
                final BigDecimal valuation = efWarehouseMargin.getSupplyMarginAmount().divide(BigDecimal.valueOf(100),2,BigDecimal.ROUND_HALF_UP);
                efWarehouseMargin.setSupplyMarginAmountCny(valuation);

                if(StrUtil.isNotEmpty(efWarehouseMargin.getSupplyMarginState())){
                    if("VAILD".equals(efWarehouseMargin.getSupplyMarginState())){
                        remark = "补充保证金金额："+efWarehouseMargin.getSupplyMarginCurrency()+" "+df.format(efWarehouseMargin.getSupplyMarginAmountCny());
                    }else if("INVALID".equals(efWarehouseMargin.getSupplyMarginState())){
                        remark = "作废保证金金额："+efWarehouseMargin.getSupplyMarginCurrency()+" "+df.format(efWarehouseMargin.getSupplyMarginAmountCny());
                    }
                }

                //调用E融接口，插入数据,解析返回数据
                String json = JSONUtil.parseObj(efWarehouseMargin, true).toStringPretty();
                final String result = signatureController.doPostEf("/efwarehousemargin/syncMargin", json);

                JSONObject resultObject = JSONUtil.parseObj(result);
                from = "E融平台:";
                flag = Boolean.valueOf(String.valueOf(resultObject.get("b")));
                msg = String.valueOf(resultObject.get("msg"));

                if(flag){
                    EfWarehouseMargin sel = new EfWarehouseMargin();
                    sel.setAssetCode(efWarehouseMargin.getAssetCode());
                    sel.setSupplyMarginCode(efWarehouseMargin.getSupplyMarginCode());
                    sel.setDeleteFlag("N");
                    final List<EfWarehouseMargin> efWarehouseMargins = efWarehouseMarginMapper.selectEfWarehouseMarginList(sel);
                    if(CollUtil.isNotEmpty(efWarehouseMargins)){
                        for (EfWarehouseMargin ef:efWarehouseMargins
                             ) {
                            efWarehouseMargin.setRowId(ef.getRowId());
                            efWarehouseMargin.setUpdateTime(LocalDateTime.now());
                            efWarehouseMargin.setUpdateWho("zc");
                            efWarehouseMargin.setUpdateWhoName("中钞");
                            efWarehouseMarginMapper.updateEfWarehouseMargin(efWarehouseMargin);
                        }

                    }else{
                        efWarehouseMargin.setRowId(UUID.randomUUID().toString());
                        efWarehouseMargin.setAddTime(LocalDateTime.now());
                        efWarehouseMargin.setAddWho("zc");
                        efWarehouseMargin.setAddWhoName("中钞");
                        efWarehouseMarginMapper.insertEfWarehouseMargin(efWarehouseMargin);
                    }
                    //插入操作记录
                    log.setRowId(UUID.randomUUID().toString());
                    log.setQlFinancingNo(apply.getQlFinancingNo());
                    log.setSerialNum(String.valueOf(System.currentTimeMillis()));
                    log.setControlType("同步保证金");
                    log.setControlTime(LocalDateTime.now());
                    log.setRemark(remark);
                    log.setAddTime(LocalDateTime.now());
                    log.setAddWho(SecurityUtils.getUserInfo().getUserName());
                    log.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                    efWarehouseLogMapper.insertEfWarehouseLog(log);
                }
            }else{
                msg = "未查询到该申请数据";
                flag = false;
            }
        }else{
            msg = "没有接受到融资同步数据";
            flag = false;
        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }

}
