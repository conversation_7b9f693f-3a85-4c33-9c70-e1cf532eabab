package com.huazheng.tunny.ocean.controller;

import com.huazheng.tunny.ocean.api.entity.EfWaybill;
import com.huazheng.tunny.ocean.service.EfWaybillService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 仓单质押关联运单
 *
 * <AUTHOR>
 * @date 2023-05-16 13:03:46
 */
@Slf4j
@RestController
@RequestMapping("/efwaybill")
public class EfWaybillController {

    @Autowired
    private EfWaybillService efWaybillService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  efWaybillService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return efWaybillService.selectEfWaybillListByLike(new Query<>(params));
    }

    @GetMapping("/selectWaybillPage")
    public Page selectWaybillPage(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  efWaybillService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return efWaybillService.selectWaybillPage(new Query<>(params));
    }

    /*
    * 仓单关联运单详情
    * */
    @PostMapping("/getWaybillInfo")
    public EfWaybill getWaybillInfo(@RequestBody EfWaybill efWaybill) {
        //数据库字段值完整查询
        // return  efWaybillService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return efWaybillService.getWaybillInfo(efWaybill);
    }

    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        EfWaybill efWaybill =efWaybillService.selectById(rowId);
        return new R<>(efWaybill);
    }

    /**
     * 保存
     * @param efWaybill
     * @return R
     */
    @PostMapping
    public R save(@RequestBody EfWaybill efWaybill) {
        efWaybillService.insert(efWaybill);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param efWaybill
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody EfWaybill efWaybill) {
        efWaybillService.updateById(efWaybill);
        return new R<>(Boolean.TRUE);
    }

    

    /**
     * 删除
     * @param rowId
     * @return R
     */
    @GetMapping("/del/{rowId}")
    public R delete(@PathVariable  String rowId) {
        efWaybillService.deleteById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        efWaybillService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<EfWaybill> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<EfWaybill> list = efWaybillService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), EfWaybill.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }



}
