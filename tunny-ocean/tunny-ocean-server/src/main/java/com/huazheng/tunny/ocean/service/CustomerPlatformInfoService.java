package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.dto.KvDTO;
import com.huazheng.tunny.ocean.api.entity.CustomerPlatformInfo;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 订舱客户签约平台子表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-01 11:52:25
 */
public interface CustomerPlatformInfoService extends IService<CustomerPlatformInfo> {
    /**
     * 查询订舱客户签约平台子表信息
     *
     * @param rowId 订舱客户签约平台子表ID
     * @return 订舱客户签约平台子表信息
     */
    public CustomerPlatformInfo selectCustomerPlatformInfoById(String rowId);

    /**
     * 查询订舱客户签约平台子表列表
     *
     * @param customerPlatformInfo 订舱客户签约平台子表信息
     * @return 订舱客户签约平台子表集合
     */
    public List<CustomerPlatformInfo> selectCustomerPlatformInfoList(CustomerPlatformInfo customerPlatformInfo);

    /**
     * 根据平台查询用户编码
     * @param customerPlatformInfo
     * @return
     */
    public List<String> selectCustomerNoByPlatform(CustomerPlatformInfo customerPlatformInfo);

    public List<CustomerPlatformInfo> selectCustomerNoByCode(CustomerPlatformInfo customerPlatformInfo);

    /**
     * 根据平台查询订舱客户编码
     * @param customerPlatformInfo
     * @return
     */
    public List<String> selectBookCustomerNoByPlatform(CustomerPlatformInfo customerPlatformInfo);

    public List<String> selectBookCustomerNoListByPlatform(CustomerPlatformInfo customerPlatformInfo);
    /**
     * 根据订舱平安查询市平台
     * @param customerPlatformInfo
     * @return
     */
    public CustomerPlatformInfo selectPlatformCodeByCustomerCode(CustomerPlatformInfo customerPlatformInfo);
    /**
     * 根据平台查询子账户
     * @param createBy
     * @return
     */
    public String selectSubByPlatform(String createBy);

    /**
     * 分页模糊查询订舱客户签约平台子表列表
     * @return 订舱客户签约平台子表集合
     */
    public Page selectCustomerPlatformInfoListByLike(Query query);

    /**
     * 根据平台编码查询客户键值对
     * @param customerPlatformInfo
     * @return
     */
    public List<KvDTO> selectKvByPlatform(CustomerPlatformInfo customerPlatformInfo);



    /**
     * 新增订舱客户签约平台子表
     *
     * @param customerPlatformInfo 订舱客户签约平台子表信息
     * @return 结果
     */
    public int insertCustomerPlatformInfo(CustomerPlatformInfo customerPlatformInfo);

    public int insertCustomerPlatformInfo1(CustomerPlatformInfo customerPlatformInfo);
    /**
     * 修改订舱客户签约平台子表
     *
     * @param customerPlatformInfo 订舱客户签约平台子表信息
     * @return 结果
     */
    public int updateCustomerPlatformInfo(CustomerPlatformInfo customerPlatformInfo);

    public int updateCustomerPlatformInfoByNo(CustomerPlatformInfo customerPlatformInfo);

    /**
     * 删除订舱客户签约平台子表
     *
     * @param rowId 订舱客户签约平台子表ID
     * @return 结果
     */
    public int deleteCustomerPlatformInfoById(String rowId);

    /**
     * 批量删除订舱客户签约平台子表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteCustomerPlatformInfoByIds(Integer[] rowIds);

}

