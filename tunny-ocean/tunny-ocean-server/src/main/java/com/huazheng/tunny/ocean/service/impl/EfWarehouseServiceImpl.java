package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseGoods;
import com.huazheng.tunny.ocean.mapper.EfWarehouseGoodsMapper;
import com.huazheng.tunny.ocean.mapper.EfWarehouseMapper;
import com.huazheng.tunny.ocean.api.entity.EfWarehouse;
import com.huazheng.tunny.ocean.service.EfWarehouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service("efWarehouseService")
public class EfWarehouseServiceImpl extends ServiceImpl<EfWarehouseMapper, EfWarehouse> implements EfWarehouseService {

    @Autowired
    private EfWarehouseMapper efWarehouseMapper;
    @Autowired
    private EfWarehouseGoodsMapper efWarehouseGoodsMapper;

    /**
     * 查询e融同步仓单表信息
     *
     * @param rowId e融同步仓单表ID
     * @return e融同步仓单表信息
     */
    @Override
    public EfWarehouse selectEfWarehouseById(String rowId)
    {
        return efWarehouseMapper.selectEfWarehouseById(rowId);
    }

    /**
     * 查询e融同步仓单表列表
     *
     * @param efWarehouse e融同步仓单表信息
     * @return e融同步仓单表集合
     */
    @Override
    public List<EfWarehouse> selectEfWarehouseList(EfWarehouse efWarehouse)
    {
        return efWarehouseMapper.selectEfWarehouseList(efWarehouse);
    }


    /**
     * 分页模糊查询e融同步仓单表列表
     * @return e融同步仓单表集合
     */
    @Override
    public Page selectEfWarehouseListByLike(Query query)
    {
        EfWarehouse efWarehouse =  BeanUtil.mapToBean(query.getCondition(), EfWarehouse.class,false);
        efWarehouse.setDeleteFlag("N");
        query.setRecords(efWarehouseMapper.selectEfWarehouseListByLike(query,efWarehouse));
        return query;
    }

    /**
     * 新增e融同步仓单表
     *
     * @param list e融同步仓单表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String insertEfWarehouse(List<EfWarehouse> list)
    {
        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(CollUtil.isNotEmpty(list)){
            for (EfWarehouse efWarehouse:list
                 ) {
                final List<EfWarehouseGoods> goodsList2 = efWarehouse.getGoodsList();
                String json = JSONUtil.toJsonStr(goodsList2);
                List<EfWarehouseGoods> goodsList = JSONUtil.toList(JSONUtil.parseArray(json), EfWarehouseGoods.class);
                if(CollUtil.isNotEmpty(goodsList)){
                    EfWarehouseGoods del = new EfWarehouseGoods();
                    del.setWarehouseReceiptNo(efWarehouse.getWarehouseReceiptNo());
                    del.setDeleteFlag("Y");
                    del.setDeleteTime(LocalDateTime.now());
                    del.setDeleteWho("eRong");
                    del.setDeleteWhoName("E融平台");
                    efWarehouseGoodsMapper.updateEfWarehouseGoodsByWarehouseReceiptNo(del);
                    for (EfWarehouseGoods goods:goodsList
                    ) {
                        goods.setRowId(UUID.randomUUID().toString());
                        goods.setWarehouseReceiptNo(efWarehouse.getWarehouseReceiptNo());
                        goods.setAddTime(LocalDateTime.now());
                        goods.setAddWho("eRong");
                        goods.setAddWhoName("E融平台");
                        efWarehouseGoodsMapper.insertEfWarehouseGoods(goods);
                    }
                }
                final EfWarehouse ef = efWarehouseMapper.selectEfWarehouseByWarehouseReceiptNo(efWarehouse.getWarehouseReceiptNo());
                if(ef!=null){
                    efWarehouse.setUpdateTime(LocalDateTime.now());
                    efWarehouse.setUpdateWho("eRong");
                    efWarehouse.setUpdateWhoName("E融平台");
                    efWarehouseMapper.updateEfWarehouse(efWarehouse);
                }else{
                    efWarehouse.setRowId(UUID.randomUUID().toString());
                    efWarehouse.setAddTime(LocalDateTime.now());
                    efWarehouse.setAddWho("eRong");
                    efWarehouse.setAddWhoName("E融平台");
                    efWarehouseMapper.insertEfWarehouse(efWarehouse);
                }
            }
        }else{
            msg = "未接收到插入数据！";
            flag = false;
        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }

    /**
     * 修改e融同步仓单表
     *
     * @param efWarehouse e融同步仓单表信息
     * @return 结果
     */
    @Override
    public int updateEfWarehouse(EfWarehouse efWarehouse)
    {
        return efWarehouseMapper.updateEfWarehouse(efWarehouse);
    }


    /**
     * 删除e融同步仓单表
     *
     * @param rowId e融同步仓单表ID
     * @return 结果
     */
    public int deleteEfWarehouseById(String rowId)
    {
        return efWarehouseMapper.deleteEfWarehouseById( rowId);
    };


    /**
     * 批量删除e融同步仓单表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfWarehouseByIds(Integer[] rowIds)
    {
        return efWarehouseMapper.deleteEfWarehouseByIds( rowIds);
    }

}
