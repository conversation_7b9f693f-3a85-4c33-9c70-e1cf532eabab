package com.huazheng.tunny.ocean.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.FdPostTransportDTO;
import com.huazheng.tunny.ocean.api.entity.FdPostTransport;
import com.huazheng.tunny.ocean.service.FdPostTransportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 后程转运表
 *
 * <AUTHOR>
 * @date 2024-08-23 14:17:30
 */
@Slf4j
@RestController
@RequestMapping("/fdposttransport")
public class FdPostTransportController {

    @Autowired
    private FdPostTransportService fdPostTransportService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdPostTransportService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdPostTransportService.selectFdPostTransportListByLike(new Query<>(params));
    }

    /**
     *  订舱列表
     * @param params
     * @return
     */
    @GetMapping("/pageCustomer")
    public Page<List<FdPostTransport>> pageCustomer(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdPostTransportService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdPostTransportService.pageCustomer(new Query<>(params));
    }

    /**
     *  市平台列表
     * @param params
     * @return
     */
    @GetMapping("/pageCity")
    public Page<List<FdPostTransport>> pageCity(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdPostTransportService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdPostTransportService.pageCity(new Query<>(params));
    }

    /**
     * 订舱详情
     * @param id
     * @return R
     */
    @GetMapping("/infoCustomer/{id}")
    public R<FdPostTransportDTO> infoCustomer(@PathVariable("id") Integer id) {
        FdPostTransportDTO fdPostTransport =fdPostTransportService.selectFdPostTransportById(id);
        return new R<>(fdPostTransport);
    }

    /**
     * 市平台详情
     * @param fdPostTransport
     * @return R
     */
    @PostMapping("/infoCity")
    public R<FdPostTransportDTO> infoCity(@RequestBody FdPostTransport fdPostTransport) {
        FdPostTransportDTO post =fdPostTransportService.infoCity(fdPostTransport);
        return new R<>(post);
    }

    /**
     * 保存
     * @param fdPostTransport
     * @return R
     */
    @PostMapping
    public R save(@RequestBody FdPostTransport fdPostTransport) {
        fdPostTransportService.insert(fdPostTransport);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 订舱保存并提交
     * @param fdPostTransport
     * @return R
     */
    @PostMapping("/updateCustomer")
    public R updateCustomer(@RequestBody FdPostTransportDTO fdPostTransport) {
        return fdPostTransportService.updateCustomer(fdPostTransport);
    }

    /**
     * 市平台保存
     * @param fdPostTransport
     * @return R
     */
    @PostMapping("/updateCity")
    public R updateCity(@RequestBody FdPostTransportDTO fdPostTransport) {
        return fdPostTransportService.updateCity(fdPostTransport);
    }

    /**
     * 市平台审核
     * @param fdPostTransport
     * @return R
     */
    @PostMapping("/auditCity")
    public R auditCity(@RequestBody FdPostTransport fdPostTransport) {
        fdPostTransportService.auditCity(fdPostTransport);
        return new R<>(Boolean.TRUE);
    }

    

    /**
     * 删除
     * @param id
     * @return R
     */
    @GetMapping("/del/{id}")
    public R delete(@PathVariable  Integer id) {
        fdPostTransportService.deleteById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<Integer> ids) {
        fdPostTransportService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出后程转运分拨信息表
    *
    * @return
    */
    @PostMapping("/fdPostTransportExported")
    public void fdPostTransportExported(@RequestBody FdPostTransport fdPostTransport, HttpServletResponse response) throws Exception {
        fdPostTransportService.fdPostTransportExported(fdPostTransport,response);
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    @Transactional
    public R imported(String titleStr, MultipartFile file) throws Exception {
        

        return new R<>(Boolean.TRUE);
    }
}
