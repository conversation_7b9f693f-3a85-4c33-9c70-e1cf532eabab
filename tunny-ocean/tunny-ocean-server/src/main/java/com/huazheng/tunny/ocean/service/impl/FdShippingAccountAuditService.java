package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.vo.FdShippingAccountVO;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.ProvinceShiftNoService;
import com.huazheng.tunny.ocean.service.SysNoConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-05-16 11:23
 */
@Service
@Slf4j
public class FdShippingAccountAuditService {

    @Autowired
    private FdShippingAccoundetailMapper fdShippingAccoundetailMapper;
    @Autowired
    private FdShippingAccountMapper fdShippingAccountMapper;
    @Autowired
    private SysNoConfigService sysNoConfigService;
    @Autowired
    private ShifmanagementMapper shifmanagementMapper;
    @Autowired
    private ProvinceShiftNoService provinceShiftNoService;
    @Autowired
    private FdBusCostDetailMapper fdBusCostDetailMapper;
    @Autowired
    private BillDealWithCityMapper billDealWithCityMapper;
    @Autowired
    private FdBusCostMapper fdBusCostMapper;
    @Autowired
    private BillSubPayCityMapper billSubPayCityMapper;
    @Autowired
    private BillPayProvinceSubMapper billPayProvinceSubMapper;
    @Autowired
    private BillPayProvinceMapper billPayProvinceMapper;

    private static class Constants {
        /**
         * 台账审核状态--驳回
         */
        static final String REJECTED_STATUS = "3";
        /**
         * 台账账单状态已生成
         */
        static final String BILL_STATUS_AUDITED = "3";
        static final String BILL_CODE_PREFIX = "FDBL";
        static final String COST_CODE_PREFIX = "FDC";
        static final String DELETE_FLAG_NO = "N";
        /**
         * 业务流程单费用审核状态
         */
        static final String AUDIT_STATUS_PENDING = "1";
        /**
         * 账单状态0待确认
         */
        static final String BILLING_STATE_INIT = "0";
        static final String STAGE_YF = "YF";
        static final String FEE_TYPE_CODE = "f_fee_type";
        static final String FEE_TYPE_NAME = "发运运费";
        static final String DOMESTIC_FEE_CODE = "jndtlyf";
        static final String DOMESTIC_FEE_NAME = "国内段包干";
        static final String OVERSEAS_FEE_CODE = "jwdtlyf";
        static final String OVERSEAS_FEE_NAME = "国外段包干";
        static final String RECEIVE_CODE_ZTDL = "ztdl";
        static final String RECEIVE_NAME_ZTDL = "中铁国际多式联运有限公司";
        static final String CURRENCY_CNY = "人民币";
        static final String CITY_PAYER_CODE = "city";
    }

    /**
     * 审核账单状态并生成相关费用信息
     *
     * @param account 台账信息
     * @return com.huazheng.tunny.common.core.util.R
     * <AUTHOR>
     * @since 2025/5/17 13:29
     */
    @Transactional(rollbackFor = Exception.class)
    public R auditOfProvincialPlatform(FdShippingAccount account) {
        try {
            // 验证台账和明细
            FdShippingAccount fdShippingAccount = validateAccount(account);
            List<FdShippingAccoundetail> accounDetailList = validateAccountDetails(fdShippingAccount, account.getStatus());

            // 查询业务流程单和班次
            List<FdBusCost> busCostList = validateBusCost(fdShippingAccount);
            //潍坊、淄博不需要校验业务流程单（MC230900000、MC230900001、MC230900002
            List<String> invalidCodes = Arrays.asList("MC230900000", "MC230900001", "MC230900002");
            String platformCode = fdShippingAccount.getPlatformCode();
            FdBusCost busCost = new FdBusCost();
            if (!invalidCodes.contains(platformCode)) {
                if (CollUtil.isEmpty(busCostList)) {
                    throw new RuntimeException("此业务流程单不存在！");
                }
                busCost = busCostList.get(0);
            } else {
                busCost.setCostCode("");
            }

            Shifmanagement shiftManagement = validateShiftManagement(fdShippingAccount);

            // 更新台账状态
            updateAccountStatus(fdShippingAccount, account.getStatus(), account.getResveredField07());
            if (Constants.REJECTED_STATUS.equals(account.getStatus())) {
                return R.success();
            }
            //省级班列号
            String provinceShiftNo;
            if (StrUtil.isBlank(shiftManagement.getProvinceShiftNo())) {
                // 生成省级班列号
                provinceShiftNo = generateProvinceShiftNo(fdShippingAccount);
            } else {
                provinceShiftNo = shiftManagement.getProvinceShiftNo();
            }


            if (StrUtil.isBlank(provinceShiftNo) || provinceShiftNo.contains("请联系系统管理员")) {
                throw new RuntimeException("生成省级班列号失败: " + provinceShiftNo);
            }

            // 更新台账明细
            fdShippingAccoundetailMapper.updateFreightByAccountCode(fdShippingAccount.getAccountCode());

            // 删除现有费用
            deleteExistingCosts(fdShippingAccount);

            String cityBillCode = sysNoConfigService.genNo(Constants.BILL_CODE_PREFIX);
            String cityBillSubCode = cityBillCode + "-001";
            String provinceBillCode = sysNoConfigService.genNo(Constants.BILL_CODE_PREFIX);
            String provinceBillSubCode = provinceBillCode + "-001";

            // 生成费用明细
            List<FdBusCostDetail> costDetails = generateCostDetails(accounDetailList, fdShippingAccount, busCost, cityBillSubCode, provinceBillSubCode);
            fdBusCostDetailMapper.batchInsertFdBusCostDetail(costDetails);

            // 插入市平台账单
            insertCityBill(fdShippingAccount, accounDetailList, costDetails, shiftManagement, busCostList, provinceShiftNo, cityBillCode, cityBillSubCode);

            // 插入省平台账单
            insertProvinceBill(fdShippingAccount, accounDetailList, busCostList, costDetails, provinceBillCode, provinceBillSubCode);

            return R.success();
        } catch (Exception e) {
            log.error("审核账单失败: {}", e.getMessage(), e);
            throw new RuntimeException("审核账单失败: " + e.getMessage());
        }
    }

    /**
     * 验证台账信息是否存在
     *
     * @param account 台账信息
     * @return com.huazheng.tunny.ocean.api.entity.FdShippingAccount
     * <AUTHOR>
     * @since 2025/5/17 14:00
     */
    private FdShippingAccount validateAccount(FdShippingAccount account) {
        return Optional.ofNullable(fdShippingAccountMapper.selectFdShippingAccountById(account.getRowId()))
                .orElseThrow(() -> new RuntimeException("此台账不存在！"));
    }

    /**
     * 验证台账明细数据是否有效
     *
     * @param fdShippingAccount 台账信息
     * @param status            审核状态
     * @return java.util.List<com.huazheng.tunny.ocean.api.entity.FdShippingAccoundetail>
     * <AUTHOR>
     * @since 2025/5/17 14:05
     */
    private List<FdShippingAccoundetail> validateAccountDetails(FdShippingAccount fdShippingAccount, String status) {
        FdShippingAccoundetail detail = new FdShippingAccoundetail();
        detail.setAccountCode(fdShippingAccount.getAccountCode());
        List<FdShippingAccoundetail> details = fdShippingAccoundetailMapper.selectFdShippingAccoundetailList(detail);
        if (CollUtil.isEmpty(details) && !Constants.REJECTED_STATUS.equals(status)) {
            throw new RuntimeException("缺失明细数据");
        }
        return details;
    }

    /**
     * 验证业务流程单是否存在
     *
     * @param fdShippingAccount 台账信息
     * @return com.huazheng.tunny.ocean.api.entity.FdBusCost
     * <AUTHOR>
     * @since 2025/5/17 14:10
     */
    private List<FdBusCost> validateBusCost(FdShippingAccount fdShippingAccount) {
        FdBusCost busCost = new FdBusCost();
        busCost.setShiftNo(fdShippingAccount.getShiftNo());
        busCost.setPlatformCode(fdShippingAccount.getPlatformCode());
        busCost.setDeleteFlag(Constants.DELETE_FLAG_NO);
        return fdBusCostMapper.selectFdBusCostList(busCost);
    }

    /**
     * 验证班次信息是否存在
     *
     * @param account 台账信息
     * @return com.huazheng.tunny.ocean.api.entity.Shifmanagement
     * <AUTHOR>
     * @since 2025/5/17 14:15
     */
    private Shifmanagement validateShiftManagement(FdShippingAccount account) {
        Shifmanagement shift = new Shifmanagement();
        shift.setShiftId(account.getShiftNo());
        shift.setPlatformCode(account.getPlatformCode());
        shift.setDeleteFlag(Constants.DELETE_FLAG_NO);
        List<Shifmanagement> shifts = shifmanagementMapper.selectShifmanagementList(shift);
        return Optional.ofNullable(CollUtil.getFirst(shifts))
                .orElseThrow(() -> new RuntimeException("缺失班次信息，请联系管理员"));
    }

    /**
     * 更新台账状态和相关信息
     *
     * @param fdShippingAccount 台账信息
     * @param status            审核状态
     * <AUTHOR>
     * @since 2025/5/17 14:20
     */
    private void updateAccountStatus(FdShippingAccount fdShippingAccount, String status, String remarks) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        fdShippingAccount.setUpdateTime(LocalDateTime.now());
        fdShippingAccount.setUpdateWho(userInfo.getUserName());
        fdShippingAccount.setUpdateWhoName(userInfo.getRealName());
        //审核意见
        fdShippingAccount.setResveredField07(remarks);
        fdShippingAccount.setResveredField09(LocalDateTime.now().toString());
        fdShippingAccount.setStatus(status);
        if (!Constants.REJECTED_STATUS.equals(status)) {
            fdShippingAccount.setBillStatus(Constants.BILL_STATUS_AUDITED);
        }
        fdShippingAccountMapper.auditStatus(fdShippingAccount);
    }

    /**
     * 生成省级班列号
     *
     * @param fdShippingAccount 台账信息
     * @return java.lang.String
     * <AUTHOR>
     * @since 2025/5/17 14:25
     */
    private String generateProvinceShiftNo(FdShippingAccount fdShippingAccount) {
        FdShippingAccountVO vo = new FdShippingAccountVO();
        BeanUtil.copyProperties(fdShippingAccount, vo);
        return provinceShiftNoService.createProvinceShiftNoTwo(vo);
    }

    /**
     * 删除市平台和省平台的现有费用
     *
     * @param fdShippingAccount 台账信息
     * <AUTHOR>
     * @since 2025/5/17 14:30
     */
    private void deleteExistingCosts(FdShippingAccount fdShippingAccount) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        FdBusCostDetail costDetail = new FdBusCostDetail();
        costDetail.setShiftNo(fdShippingAccount.getShiftNo());
        costDetail.setDeleteWho(userInfo.getRealName());

        costDetail.setPayCode(fdShippingAccount.getPlatformCode());
        fdBusCostDetailMapper.deleteByShiftNoAndPayCode(costDetail);

        costDetail.setPayCode(fdShippingAccount.getCustomerNo());
        fdBusCostDetailMapper.deleteByShiftNoAndPayCode(costDetail);
    }

    /**
     * 生成市平台和省平台的费用明细
     *
     * @param accounDetailList 台账明细列表
     * @param account          台账信息
     * @param busCost          业务流程单
     * @return java.util.List<com.huazheng.tunny.ocean.api.entity.FdBusCostDetail>
     * <AUTHOR>
     * @since 2025/5/17 14:35
     */
    private List<FdBusCostDetail> generateCostDetails(List<FdShippingAccoundetail> accounDetailList,
                                                      FdShippingAccount account,
                                                      FdBusCost busCost,
                                                      String cityBillSubCode,
                                                      String provinceBillSubCode) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();


        List<FdBusCostDetail> costDetails = new ArrayList<>();
        for (FdShippingAccoundetail detail : accounDetailList) {
            String payerCodeCity = "city";
            // 市平台 - 国内段
            FdBusCostDetail cityDomestic = createCostDetail(detail, account, busCost, userInfo, cityBillSubCode,
                    Constants.DOMESTIC_FEE_CODE, Constants.DOMESTIC_FEE_NAME, payerCodeCity);
            costDetails.add(cityDomestic);

            // 市平台 - 国外段
            FdBusCostDetail cityOverseas = createCostDetail(detail, account, busCost, userInfo, cityBillSubCode,
                    Constants.OVERSEAS_FEE_CODE, Constants.OVERSEAS_FEE_NAME, payerCodeCity);
            costDetails.add(cityOverseas);

            String payerCodeProvince = "province";
            // 省平台 - 国内段
            FdBusCostDetail provinceDomestic = createCostDetail(detail, account, busCost, userInfo, provinceBillSubCode,
                    Constants.DOMESTIC_FEE_CODE, Constants.DOMESTIC_FEE_NAME, payerCodeProvince);
            costDetails.add(provinceDomestic);

            // 省平台 - 国外段
            FdBusCostDetail provinceOverseas = createCostDetail(detail, account, busCost, userInfo, provinceBillSubCode,
                    Constants.OVERSEAS_FEE_CODE, Constants.OVERSEAS_FEE_NAME, payerCodeProvince);
            costDetails.add(provinceOverseas);
        }
        return costDetails;
    }

    /**
     * 创建费用明细对象
     *
     * @param detail      台账明细
     * @param account     台账信息
     * @param busCost     业务流程单
     * @param userInfo    当前用户信息
     * @param billSubCode 账单子编码
     * @param feeCode     费用类别代码
     * @param feeName     费用类别名称
     * @return com.huazheng.tunny.ocean.api.entity.FdBusCostDetail
     * <AUTHOR>
     * @since 2025/5/17 14:40
     */
    private FdBusCostDetail createCostDetail(FdShippingAccoundetail detail,
                                             FdShippingAccount account,
                                             FdBusCost busCost,
                                             SecruityUser userInfo,
                                             String billSubCode,
                                             String feeCode,
                                             String feeName,
                                             String payerCode) {
        FdBusCostDetail costDetail = new FdBusCostDetail();
        costDetail.setBillSubCode(billSubCode);
        costDetail.setCostType("1");
        costDetail.setContainerNumber(detail.getContainerNumber());
        costDetail.setCodeSsCategoriesCode(feeCode);
        costDetail.setCodeSsCategoriesName(feeName);

        costDetail.setShiftNo(account.getShiftNo());
        costDetail.setUpdateWho(userInfo.getUserName());
        costDetail.setUpdateWhoName(userInfo.getRealName());
        costDetail.setUpdateTime(LocalDateTime.now());
        costDetail.setCostCode(busCost.getCostCode());
        costDetail.setCodeBbCategoriesCode(Constants.FEE_TYPE_CODE);
        costDetail.setCodeBbCategoriesName(Constants.FEE_TYPE_NAME);
        if (payerCode.equals(Constants.CITY_PAYER_CODE)) {
            costDetail.setPayCode(account.getPlatformCode());
            costDetail.setPayName(account.getPlatformName());
            costDetail.setReceiveCode(account.getCustomerNo());
            costDetail.setReceiveName(account.getCustomerName());
        } else {
            costDetail.setPayCode(account.getCustomerNo());
            costDetail.setPayName(account.getCustomerName());
            costDetail.setReceiveCode(Constants.RECEIVE_CODE_ZTDL);
            costDetail.setReceiveName(Constants.RECEIVE_NAME_ZTDL);
        }
        String categoriesName = "国外段包干";
        if (categoriesName.equals(feeName)) {
            if (payerCode.equals(Constants.CITY_PAYER_CODE)) {
                costDetail.setExchangeRate(detail.getExchangeRate());
                costDetail.setExchangeRateNew(detail.getExchangeRate());
                costDetail.setLocalAmount(detail.getOverseasFreightCny());
                costDetail.setOriginalAmount(detail.getOverseasFreightOc());
            } else {
                costDetail.setExchangeRate(detail.getExchangeRate());
                costDetail.setExchangeRateNew(detail.getExchangeRate());
                costDetail.setLocalAmount(detail.getRrOverseasFreightCny());
                costDetail.setOriginalAmount(detail.getRrOverseasFreightOc());
            }
            costDetail.setCurrency(detail.getMonetaryType());

        } else {
            if (payerCode.equals(Constants.CITY_PAYER_CODE)) {
                costDetail.setExchangeRate(BigDecimal.ONE);
                costDetail.setExchangeRateNew(BigDecimal.ONE);
                costDetail.setLocalAmount(detail.getDomesticFreight());
                costDetail.setOriginalAmount(detail.getDomesticFreight());
            } else {
                costDetail.setExchangeRate(BigDecimal.ONE);
                costDetail.setExchangeRateNew(BigDecimal.ONE);
                costDetail.setLocalAmount(detail.getRrDomesticFreight());
                costDetail.setOriginalAmount(detail.getRrDomesticFreight());
            }
            costDetail.setCurrency(Constants.CURRENCY_CNY);
        }
        costDetail.setAddWho(userInfo.getUserName());
        costDetail.setAddWhoName(userInfo.getRealName());
        costDetail.setAddTime(LocalDateTime.now());
        costDetail.setAuditStatus(Constants.AUDIT_STATUS_PENDING);
        costDetail.setStatus("0");
        costDetail.setDeleteFlag("N");

        return costDetail;
    }

    /**
     * 插入市平台账单及子账单
     *
     * @param account          台账信息
     * @param accounDetailList 台账明细列表
     * @param busCostList      业务流程单
     * @param costDetails      费用明细列表
     * <AUTHOR>
     * @since 2025/5/17 14:45
     */

    private void insertCityBill(FdShippingAccount account,
                                List<FdShippingAccoundetail> accounDetailList,
                                List<FdBusCostDetail> costDetails,
                                Shifmanagement shiftManagement,
                                List<FdBusCost> busCostList,
                                String provinceShiftNo,
                                String cityBillCode,
                                String cityBillSubCode) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        //账单金额
        BigDecimal cityBillAmount = costDetails.stream()
                .filter(detail -> account.getPlatformCode().equals(detail.getPayCode()))
                .map(FdBusCostDetail::getLocalAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 市平台账单头表
        BillDealWithCity billDealWithCity = new BillDealWithCity();
        billDealWithCity.setBillCode(cityBillCode);
        billDealWithCity.setProvinceShiftNum(account.getShiftNo());
        billDealWithCity.setProvinceShiftName(account.getShiftName());
        billDealWithCity.setCustomerCode(account.getPlatformCode());
        billDealWithCity.setSourceCode(account.getCustomerNo());
        billDealWithCity.setCustomerName(account.getPlatformName());
        billDealWithCity.setSourceUnit(account.getCustomerName());
        billDealWithCity.setBoxCapacity(BigDecimal.valueOf(accounDetailList.size()));
        billDealWithCity.setPostDate(Date.from(account.getShippingTime().atZone(ZoneId.systemDefault()).toInstant()));
        billDealWithCity.setShippingLines(account.getShippingLine());
        billDealWithCity.setBillAmount(cityBillAmount);
        billDealWithCity.setDirection(account.getTrip());
        billDealWithCity.setStage(Constants.STAGE_YF);
        billDealWithCity.setCreateBy(userInfo.getRealName());
        billDealWithCity.setCreateTime(new Date());
        billDealWithCity.setProvinceTrainNum(provinceShiftNo);
        billDealWithCityMapper.insertBillDealWithCity(billDealWithCity);

        // 市平台账单子表
        BillSubPayCity billSubPayCity = new BillSubPayCity();
        billSubPayCity.setBillCode(cityBillCode);
        String costCode = busCostList.stream()
                .map(FdBusCost::getCostCode)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.joining(","));
        billSubPayCity.setCostCode(costCode);
        billSubPayCity.setBillSubCode(cityBillSubCode);
        billSubPayCity.setAccountCode(account.getAccountCode());
        billSubPayCity.setShiftName(account.getShiftName());
        billSubPayCity.setPortStation(shiftManagement.getPortStation());
        billSubPayCity.setShipmentTime(account.getShippingTime());
        billSubPayCity.setPlatformCode(account.getCustomerNo());
        billSubPayCity.setPlatformName(account.getCustomerName());
        billSubPayCity.setShiftNo(account.getShiftNo());
        billSubPayCity.setCustomerCode(account.getPlatformCode());
        billSubPayCity.setCustomerName(account.getPlatformName());
        billSubPayCity.setBoxNum(accounDetailList.size());
        billSubPayCity.setStatus("1");
        billSubPayCity.setBillingState(Constants.BILLING_STATE_INIT);
        billSubPayCity.setPlatformLevel("0");
        billSubPayCity.setAddWho(userInfo.getUserName());
        billSubPayCity.setAddWhoName(userInfo.getRealName());
        billSubPayCity.setAddTime(LocalDateTime.now());
        billDealWithCity.setProvinceTrainNum(provinceShiftNo);


        billSubPayCity.setBillAmount(cityBillAmount);
        billSubPayCityMapper.insertBillSubPayCity(billSubPayCity);
    }

    /**
     * 插入省平台账单及子账单
     *
     * @param account          台账信息
     * @param accounDetailList 台账明细列表
     * @param busCostList      业务流程单
     * @param costDetails      费用明细列表
     * <AUTHOR>
     * @since 2025/5/17 14:50
     */
    private void insertProvinceBill(FdShippingAccount account,
                                    List<FdShippingAccoundetail> accounDetailList,
                                    List<FdBusCost> busCostList,
                                    List<FdBusCostDetail> costDetails,
                                    String provinceBillCode,
                                    String provinceBillSubCode) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();


        BigDecimal provinceBillAmount = costDetails.stream()
                .filter(detail -> account.getCustomerNo().equals(detail.getPayCode()))
                .map(FdBusCostDetail::getLocalAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 省平台账单头表
        BillPayProvince billPayProvince = new BillPayProvince();
        billPayProvince.setBillCode(provinceBillCode);
        billPayProvince.setProvinceShiftNum(account.getShiftNo());
        billPayProvince.setProvinceTrainName(account.getShiftName());
        billPayProvince.setCustomerCode(account.getCustomerNo());
        billPayProvince.setSourceCode(Constants.RECEIVE_CODE_ZTDL);
        billPayProvince.setCustomerName(account.getCustomerName());
        billPayProvince.setSourceUnit(Constants.RECEIVE_NAME_ZTDL);
        billPayProvince.setSourceUnitTwo(account.getPlatformName());
        billPayProvince.setSourceUnitTwoCode(account.getPlatformCode());
        billPayProvince.setBoxCapacity(BigDecimal.valueOf(accounDetailList.size()));
        billPayProvince.setPostDate(Date.from(account.getShippingTime().atZone(ZoneId.systemDefault()).toInstant()));
        billPayProvince.setShippingLines(account.getShippingLine());
        billPayProvince.setDirection(account.getTrip());
        billPayProvince.setStage(Constants.STAGE_YF);
        billPayProvince.setCreateBy(userInfo.getRealName());
        billPayProvince.setCreateTime(new Date());
        billPayProvince.setBillAmount(provinceBillAmount);
        billPayProvinceMapper.insertBillPayProvince(billPayProvince);

        // 省平台账单子表
        BillPayProvinceSub billPayProvinceSub = new BillPayProvinceSub();
        billPayProvinceSub.setBillCode(provinceBillCode);
        billPayProvinceSub.setBillSubCode(provinceBillSubCode);
        billPayProvinceSub.setAccountCode(account.getAccountCode());
        billPayProvinceSub.setShiftName(account.getShiftName());
        billPayProvinceSub.setShipmentTime(Date.from(account.getShippingTime().atZone(ZoneId.systemDefault()).toInstant()));
        String costCode = busCostList.stream()
                .map(FdBusCost::getCostCode)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.joining(","));
        billPayProvinceSub.setCostCode(costCode);
        billPayProvinceSub.setPlatformCode(Constants.RECEIVE_CODE_ZTDL);
        billPayProvinceSub.setPlatformName(Constants.RECEIVE_NAME_ZTDL);
        billPayProvinceSub.setPlatformLevel("0");
        billPayProvinceSub.setShiftNo(account.getShiftNo());
        billPayProvinceSub.setBillingState(Constants.BILLING_STATE_INIT);
        billPayProvinceSub.setCustomerCode(account.getCustomerNo());
        billPayProvinceSub.setCustomerName(account.getCustomerName());
        billPayProvinceSub.setStatus("1");
        billPayProvinceSub.setBoxNum(accounDetailList.size());
        billPayProvinceSub.setAddWho(userInfo.getUserName());
        billPayProvinceSub.setAddWhoName(userInfo.getRealName());
        billPayProvinceSub.setAddTime(new Date());
        billPayProvinceSub.setBillAmount(provinceBillAmount);
        billPayProvinceSubMapper.insertBillPayProvinceSub(billPayProvinceSub);
    }
}