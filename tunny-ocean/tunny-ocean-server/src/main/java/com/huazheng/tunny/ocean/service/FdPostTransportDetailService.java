package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FdPostTransportDetail;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.WaybillHeader;

import java.util.List;

/**
 * 后程转运明细表 服务接口层
 *
 * <AUTHOR>
 * @date 2024-08-23 14:17:50
 */
public interface FdPostTransportDetailService extends IService<FdPostTransportDetail> {
    /**
     * 查询后程转运明细表信息
     *
     * @param id 后程转运明细表ID
     * @return 后程转运明细表信息
     */
    public FdPostTransportDetail selectFdPostTransportDetailById(Integer id);

    /**
     * 查询后程转运明细表列表
     *
     * @param fdPostTransportDetail 后程转运明细表信息
     * @return 后程转运明细表集合
     */
    public List<FdPostTransportDetail> selectFdPostTransportDetailList(FdPostTransportDetail fdPostTransportDetail);


    /**
     * 分页模糊查询后程转运明细表列表
     * @return 后程转运明细表集合
     */
    public Page selectFdPostTransportDetailListByLike(Query query);



    /**
     * 新增后程转运明细表
     *
     * @param fdPostTransportDetail 后程转运明细表信息
     * @return 结果
     */
    public int insertFdPostTransportDetail(FdPostTransportDetail fdPostTransportDetail);

    /**
     * 修改后程转运明细表
     *
     * @param fdPostTransportDetail 后程转运明细表信息
     * @return 结果
     */
    public int updateFdPostTransportDetail(FdPostTransportDetail fdPostTransportDetail);

    /**
     * 删除后程转运明细表
     *
     * @param id 后程转运明细表ID
     * @return 结果
     */
    public int deleteFdPostTransportDetailById(Integer id);

    /**
     * 批量删除后程转运明细表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdPostTransportDetailByIds(Integer[] ids);

}

