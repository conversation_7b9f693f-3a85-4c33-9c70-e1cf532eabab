package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FiExporfinancingAccept;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 出口融资受理信息表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-04-25 12:22:27
 */
public interface FiExporfinancingAcceptService extends IService<FiExporfinancingAccept> {
    /**
     * 查询出口融资受理信息表信息
     *
     * @param rowId 出口融资受理信息表ID
     * @return 出口融资受理信息表信息
     */
    public FiExporfinancingAccept selectFiExporfinancingAcceptById(String rowId);

    public FiExporfinancingAccept selectFiExporfinancingAcceptByAssetCode(FiExporfinancingAccept fiExporfinancingAccept);

    /**
     * 查询出口融资受理信息表列表
     *
     * @param fiExporfinancingAccept 出口融资受理信息表信息
     * @return 出口融资受理信息表集合
     */
    public List<FiExporfinancingAccept> selectFiExporfinancingAcceptList(FiExporfinancingAccept fiExporfinancingAccept);


    /**
     * 分页模糊查询出口融资受理信息表列表
     * @return 出口融资受理信息表集合
     */
    public Page selectFiExporfinancingAcceptListByLike(Query query);



    /**
     * 新增出口融资受理信息表
     *
     * @param fiExporfinancingAccept 出口融资受理信息表信息
     * @return 结果
     */
    public int insertFiExporfinancingAccept(FiExporfinancingAccept fiExporfinancingAccept);

    /**
     * 修改出口融资受理信息表
     *
     * @param fiExporfinancingAccept 出口融资受理信息表信息
     * @return 结果
     */
    public int updateFiExporfinancingAccept(FiExporfinancingAccept fiExporfinancingAccept);

    /**
     * 删除出口融资受理信息表
     *
     * @param rowId 出口融资受理信息表ID
     * @return 结果
     */
    public int deleteFiExporfinancingAcceptById(String rowId);

    /**
     * 批量删除出口融资受理信息表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiExporfinancingAcceptByIds(Integer[] rowIds);

}

