package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.EfFinancingRepaymentinfoMapper;
import com.huazheng.tunny.ocean.api.entity.EfFinancingRepaymentinfo;
import com.huazheng.tunny.ocean.service.EfFinancingRepaymentinfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("efFinancingRepaymentinfoService")
public class EfFinancingRepaymentinfoServiceImpl extends ServiceImpl<EfFinancingRepaymentinfoMapper, EfFinancingRepaymentinfo> implements EfFinancingRepaymentinfoService {

    @Autowired
    private EfFinancingRepaymentinfoMapper efFinancingRepaymentinfoMapper;

    public EfFinancingRepaymentinfoMapper getEfFinancingRepaymentinfoMapper() {
        return efFinancingRepaymentinfoMapper;
    }

    public void setEfFinancingRepaymentinfoMapper(EfFinancingRepaymentinfoMapper efFinancingRepaymentinfoMapper) {
        this.efFinancingRepaymentinfoMapper = efFinancingRepaymentinfoMapper;
    }

    /**
     * 查询仓单质押企业还款信息信息
     *
     * @param rowId 仓单质押企业还款信息ID
     * @return 仓单质押企业还款信息信息
     */
    @Override
    public EfFinancingRepaymentinfo selectEfFinancingRepaymentinfoById(String rowId)
    {
        return efFinancingRepaymentinfoMapper.selectEfFinancingRepaymentinfoById(rowId);
    }

    /**
     * 查询仓单质押企业还款信息列表
     *
     * @param efFinancingRepaymentinfo 仓单质押企业还款信息信息
     * @return 仓单质押企业还款信息集合
     */
    @Override
    public List<EfFinancingRepaymentinfo> selectEfFinancingRepaymentinfoList(EfFinancingRepaymentinfo efFinancingRepaymentinfo)
    {
        return efFinancingRepaymentinfoMapper.selectEfFinancingRepaymentinfoList(efFinancingRepaymentinfo);
    }


    /**
     * 分页模糊查询仓单质押企业还款信息列表
     * @return 仓单质押企业还款信息集合
     */
    @Override
    public Page selectEfFinancingRepaymentinfoListByLike(Query query)
    {
        EfFinancingRepaymentinfo efFinancingRepaymentinfo =  BeanUtil.mapToBean(query.getCondition(), EfFinancingRepaymentinfo.class,false);
        query.setRecords(efFinancingRepaymentinfoMapper.selectEfFinancingRepaymentinfoListByLike(query,efFinancingRepaymentinfo));
        return query;
    }

    /**
     * 新增仓单质押企业还款信息
     *
     * @param efFinancingRepaymentinfo 仓单质押企业还款信息信息
     * @return 结果
     */
    @Override
    public int insertEfFinancingRepaymentinfo(EfFinancingRepaymentinfo efFinancingRepaymentinfo)
    {
        return efFinancingRepaymentinfoMapper.insertEfFinancingRepaymentinfo(efFinancingRepaymentinfo);
    }

    /**
     * 修改仓单质押企业还款信息
     *
     * @param efFinancingRepaymentinfo 仓单质押企业还款信息信息
     * @return 结果
     */
    @Override
    public int updateEfFinancingRepaymentinfo(EfFinancingRepaymentinfo efFinancingRepaymentinfo)
    {
        return efFinancingRepaymentinfoMapper.updateEfFinancingRepaymentinfo(efFinancingRepaymentinfo);
    }


    /**
     * 删除仓单质押企业还款信息
     *
     * @param rowId 仓单质押企业还款信息ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingRepaymentinfoById(String rowId)
    {
        return efFinancingRepaymentinfoMapper.deleteEfFinancingRepaymentinfoById( rowId);
    };


    /**
     * 批量删除仓单质押企业还款信息对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingRepaymentinfoByIds(Integer[] rowIds)
    {
        return efFinancingRepaymentinfoMapper.deleteEfFinancingRepaymentinfoByIds( rowIds);
    }

}
