package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.FdCost;
import com.huazheng.tunny.ocean.api.vo.FdBillVO;

import java.util.List;
import java.util.Map;

/**
 * 运单费用汇总表 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:30:28
 */
public interface FdCostService extends IService<FdCost> {
    /**
     * 查询运单费用汇总表信息
     *
     * @param id 运单费用汇总表ID
     * @return 运单费用汇总表信息
     */
    public FdCost selectFdCostById(Integer id);

    /**
     * 查询运单费用汇总表列表
     *
     * @param fdCost 运单费用汇总表信息
     * @return 运单费用汇总表集合
     */
    public List<FdCost> selectFdCostList(FdCost fdCost);

    public List<FdCost> selectYsFdCost(FdCost fdCost);

    public List<FdCost> selectFdCostListAndAreaCode(FdCost fdCost);

    public List<FdCost>  selectLiangJiaKunBang(FdCost fdCost);


    /**
     * 分页模糊查询运单费用汇总表列表
     * @return 运单费用汇总表集合
     */
    public Page selectFdCostListByLike(Query query);



    /**
     * 新增运单费用汇总表
     *
     * @param fdCost 运单费用汇总表信息
     * @return 结果
     */
    public int insertFdCost(FdCost fdCost);

    /**
     * 修改运单费用汇总表
     *
     * @param fdCost 运单费用汇总表信息
     * @return 结果
     */
    public int updateFdCost(FdCost fdCost);

    /**
     * 删除运单费用汇总表
     *
     * @param id 运单费用汇总表ID
     * @return 结果
     */
    public int deleteFdCostById(Integer id);

    /**
     * 批量删除运单费用汇总表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdCostByIds(Integer[] ids);

    Boolean savecostandcodstdetails(String rowid);

    Boolean savecostandcodstdetailsbyaccount(String rowid);

    Boolean savecostandcodstdetailsbyaccount2(String rowid);

    List<Map> selectcustomerbyplatformcode(Map map);

    Boolean savecostbychangebox(String string);

    int updateFdCostCostCode(FdCost fdCost);

    /**
     * 逻辑删除
     * @param fdCost
     * @return
     */
    int updateFdCostByCostCode(FdCost fdCost);

    public R updateOriginalExchangeRate(FdCost fdCost);

    List<FdCost> selectProvinceTrainsNumber(FdCost fdCost);

    int updateFdCostByCostCodeYf(FdCost fdCost);

    public int updateFdCostByProvinceTrainsNumber(FdCost fdCost);

    void updateBill(FdBillVO fdBillVO);

}

