package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.MonthBookingplanDetail;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 月计划申请子表(订舱客户提交到市平台) 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-07 11:48:32
 */
public interface MonthBookingplanDetailService extends IService<MonthBookingplanDetail> {
    /**
     * 查询月计划申请子表(订舱客户提交到市平台)信息
     *
     * @param rowId 月计划申请子表(订舱客户提交到市平台)ID
     * @return 月计划申请子表(订舱客户提交到市平台)信息
     */
    public MonthBookingplanDetail selectMonthBookingplanDetailById(String rowId);

    /**
     * 查询月计划申请子表(订舱客户提交到市平台)列表
     *
     * @param monthBookingplanDetail 月计划申请子表(订舱客户提交到市平台)信息
     * @return 月计划申请子表(订舱客户提交到市平台)集合
     */
    public List<MonthBookingplanDetail> selectMonthBookingplanDetailList(MonthBookingplanDetail monthBookingplanDetail);


    /**
     * 分页模糊查询月计划申请子表(订舱客户提交到市平台)列表
     * @return 月计划申请子表(订舱客户提交到市平台)集合
     */
    public Page selectMonthBookingplanDetailListByLike(Query query);



    /**
     * 新增月计划申请子表(订舱客户提交到市平台)
     *
     * @param monthBookingplanDetail 月计划申请子表(订舱客户提交到市平台)信息
     * @return 结果
     */
    public int insertMonthBookingplanDetail(MonthBookingplanDetail monthBookingplanDetail);

    /**
     * 修改月计划申请子表(订舱客户提交到市平台)
     *
     * @param monthBookingplanDetail 月计划申请子表(订舱客户提交到市平台)信息
     * @return 结果
     */
    public int updateMonthBookingplanDetail(MonthBookingplanDetail monthBookingplanDetail);

    /**
     * 删除月计划申请子表(订舱客户提交到市平台)
     *
     * @param rowId 月计划申请子表(订舱客户提交到市平台)ID
     * @return 结果
     */
    public int deleteMonthBookingplanDetailById(String rowId);

    /**
     * 批量删除月计划申请子表(订舱客户提交到市平台)
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteMonthBookingplanDetailByIds(Integer[] rowIds);

}

