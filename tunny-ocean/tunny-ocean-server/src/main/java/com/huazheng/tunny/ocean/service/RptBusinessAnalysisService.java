package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.dto.FdBusCostDetailDTO;
import com.huazheng.tunny.ocean.api.dto.GrossProfitDeatilsDTO;
import com.huazheng.tunny.ocean.api.dto.IncomesContainerDeatilsDTO;
import com.huazheng.tunny.ocean.api.dto.IncomesDeatilsDTO;
import com.huazheng.tunny.ocean.api.entity.FdBusCostDetail;
import com.huazheng.tunny.ocean.api.entity.LineManagement;
import com.huazheng.tunny.ocean.api.entity.RptBusinessAnalysis;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 经营数据 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-08-04 11:35:56
 */
public interface RptBusinessAnalysisService extends IService<RptBusinessAnalysis> {

    Page selectRptBusinessAnalysisLike(Query<RptBusinessAnalysis> objectQuery);

    void pageExport(RptBusinessAnalysis rptBusinessAnalysis, HttpServletResponse response);

    List<IncomesDeatilsDTO> incomesDeatilsList(IncomesDeatilsDTO incomesDeatilsDTO);

    List<String> incomesContainerNumber(IncomesDeatilsDTO incomesDeatilsDTO);

    Map<String,Object> incomesContainerDeatilsList(IncomesContainerDeatilsDTO incomesContainerDeatilsDTO);

    List<IncomesDeatilsDTO> restoreIncomesDeatilsList(IncomesDeatilsDTO incomesDeatilsDTO);

    Map<String,Object> restoreIncomesContainerDeatilsList(IncomesContainerDeatilsDTO incomesContainerDeatilsDTO);

    List<IncomesDeatilsDTO> expDeatilsList(IncomesDeatilsDTO incomesDeatilsDTO);

    List<FdBusCostDetailDTO> expContainerDeatilsList(IncomesContainerDeatilsDTO incomesContainerDeatilsDTO);

    List<RptBusinessAnalysis> grossProfitContainerList(IncomesDeatilsDTO incomesDeatilsDTO);

    GrossProfitDeatilsDTO grossProfitContainerDetails(IncomesDeatilsDTO incomesDeatilsDTO);
}

