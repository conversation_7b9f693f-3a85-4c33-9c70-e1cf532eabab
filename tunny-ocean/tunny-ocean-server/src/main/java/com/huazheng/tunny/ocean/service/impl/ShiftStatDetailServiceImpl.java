package com.huazheng.tunny.ocean.service.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.ShiftStat;
import com.huazheng.tunny.ocean.api.entity.ShiftStatDetail;
import com.huazheng.tunny.ocean.mapper.ShiftStatDetailMapper;
import com.huazheng.tunny.ocean.service.ShiftStatDetailService;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 班次统计明细表Service
 *
 * <AUTHOR>
 * @since 2025-05-06 16:18:59
 */
@Service
public class ShiftStatDetailServiceImpl extends ServiceImpl<ShiftStatDetailMapper, ShiftStatDetail> implements ShiftStatDetailService {

    @Autowired
    private ShiftStatDetailMapper shiftStatDetailMapper;

    /**
     * 班次统计明细表分页
     *
     * @param shiftStatDetail 班次统计明细表信息
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:18:59
     */
    @Override
    public Page<ShiftStatDetail> page(ShiftStatDetail shiftStatDetail) {

        Page<ShiftStatDetail> page = new Page<>(shiftStatDetail.getPage() == null ? 1 : shiftStatDetail.getPage(),
                shiftStatDetail.getLimit() == null ? 10 : shiftStatDetail.getLimit());
        List<ShiftStatDetail> records = shiftStatDetailMapper.pageShiftStatDetail(page, shiftStatDetail);
        page.setRecords(records);

        return page;
    }

    /**
     * 班次统计明细表详情
     *
     * @param detailId 明细表详情ID
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:18:59
     */
    @Override
    public R<ShiftStatDetail> info(Integer detailId) {
        return R.success(shiftStatDetailMapper.infoShiftStatDetail(detailId));
    }

    /**
     * 班次统计明细表保存
     *
     * @param shiftStatDetail 班次统计明细
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:18:59
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<T> save(ShiftStatDetail shiftStatDetail) {
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
        shiftStatDetail.setCreateBy(secruityUser.getId());
        shiftStatDetailMapper.saveShiftStatDetail(shiftStatDetail);
        return R.success();
    }

    /**
     * 班次统计明细表修改
     *
     * @param shiftStatDetail 班次统计明细表信息
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:18:59
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R update(ShiftStatDetail shiftStatDetail) {
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
        shiftStatDetail.setUpdateBy(secruityUser.getId());
        shiftStatDetailMapper.updateShiftStatDetail(shiftStatDetail);
        return R.success();
    }

    /**
     * 班次统计明细表删除
     *
     * @param detailIds 明细详情 IDs
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:18:59
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R delete(Integer[] detailIds) {
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
        shiftStatDetailMapper.deleteShiftStatDetail(detailIds, secruityUser.getId());
        return R.success();
    }

    /**
     * 班次统计明细表列表
     *
     * @param shiftStatDetail 班次统计明细表信息
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:18:59
     */
    @Override
    public R<List<ShiftStatDetail>> list(ShiftStatDetail shiftStatDetail) {
        return R.success(shiftStatDetailMapper.listShiftStatDetail(shiftStatDetail));
    }

}
