package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.InvoiceApplicationDetail;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 开票申请子表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-16 17:56:35
 */
public interface InvoiceApplicationDetailService extends IService<InvoiceApplicationDetail> {
    /**
     * 查询开票申请子表信息
     *
     * @param rowId 开票申请子表ID
     * @return 开票申请子表信息
     */
    public InvoiceApplicationDetail selectInvoiceApplicationDetailById(String rowId);

    /**
     * 查询开票申请子表列表
     *
     * @param invoiceApplicationDetail 开票申请子表信息
     * @return 开票申请子表集合
     */
    public List<InvoiceApplicationDetail> selectInvoiceApplicationDetailList(InvoiceApplicationDetail invoiceApplicationDetail);


    /**
     * 分页模糊查询开票申请子表列表
     * @return 开票申请子表集合
     */
    public Page selectInvoiceApplicationDetailListByLike(Query query);



    /**
     * 新增开票申请子表
     *
     * @param invoiceApplicationDetail 开票申请子表信息
     * @return 结果
     */
    public int insertInvoiceApplicationDetail(InvoiceApplicationDetail invoiceApplicationDetail);

    /**
     * 修改开票申请子表
     *
     * @param invoiceApplicationDetail 开票申请子表信息
     * @return 结果
     */
    public int updateInvoiceApplicationDetail(InvoiceApplicationDetail invoiceApplicationDetail);

    /**
     * 删除开票申请子表
     *
     * @param rowId 开票申请子表ID
     * @return 结果
     */
    public int deleteInvoiceApplicationDetailById(String rowId);

    /**
     * 批量删除开票申请子表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteInvoiceApplicationDetailByIds(Integer[] rowIds);

}

