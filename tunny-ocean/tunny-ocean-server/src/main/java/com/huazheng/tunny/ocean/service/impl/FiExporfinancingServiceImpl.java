package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.mapper.FiExporfinancingApplyMapper;
import com.huazheng.tunny.ocean.mapper.FiExporfinancingMapper;
import com.huazheng.tunny.ocean.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service("fiExporfinancingService")
public class FiExporfinancingServiceImpl extends ServiceImpl<FiExporfinancingMapper, FiExporfinancing> implements FiExporfinancingService {

    @Autowired
    private FiExporfinancingMapper fiExporfinancingMapper;
    @Autowired
    private FiExporfinancingApplyMapper fiExporfinancingApplyMapper;
    @Autowired
    private FiExporfinancingInvoicesService fiExporfinancingInvoicesService;
    @Autowired
    private FiExporfinancingInvoicesManifestsService fiExporfinancingInvoicesManifestsService;
    @Autowired
    private SysAttachmentsService sysAttachmentsService;
    @Autowired
    private FiExporfinancingAcceptService fiExporfinancingAcceptService;
    @Autowired
    private FiExporfinancingPayService fiExporfinancingPayService;
    @Autowired
    private FiExporfinancingRecsService fiExporfinancingRecsService;


    /**
     * 查询出口融资主表信息
     *
     * @param rowId 出口融资主表ID
     * @return 出口融资主表信息
     */
    @Override
    public FiExporfinancing selectFiExporfinancingById(String rowId)
    {
        return fiExporfinancingMapper.selectFiExporfinancingById(rowId);
    }

    /**
     * 查询出口融资主表列表
     *
     * @param fiExporfinancing 出口融资主表信息
     * @return 出口融资主表集合
     */
    @Override
    public List<FiExporfinancing> selectFiExporfinancingList(FiExporfinancing fiExporfinancing)
    {
        return fiExporfinancingMapper.selectFiExporfinancingList(fiExporfinancing);
    }


    /**
     * 分页模糊查询出口融资主表列表
     * @return 出口融资主表集合
     */
    @Override
    public Page selectFiExporfinancingListByLike(Query query)
    {
        FiExporfinancing fiExporfinancing =  BeanUtil.mapToBean(query.getCondition(), FiExporfinancing.class,false);
        query.setRecords(fiExporfinancingMapper.selectFiExporfinancingListByLike(query,fiExporfinancing));
        return query;
    }

    @Override
    public R selectByAssetCode(Map<String, Object> params) {
        FiExporfinancing fiExporfinancing =  BeanUtil.mapToBean(params, FiExporfinancing.class,false);
        R r=new R();
        if(fiExporfinancing!=null){
            if(!"".equals(fiExporfinancing.getAssetCode()) && fiExporfinancing.getAssetCode()!=null){
                FiExporfinancingApply fiExporfinancingApply=new FiExporfinancingApply();
                fiExporfinancingApply.setAssetCode(fiExporfinancing.getAssetCode());
                //融资申请
                FiExporfinancingApply fiExporfinancingApplyByEntCode = fiExporfinancingApplyMapper.selectFiExporfinancingApplyByEntCode(fiExporfinancingApply);
                if(fiExporfinancingApplyByEntCode !=null) {
                    //应收账款明细
                    FiExporfinancingInvoices fiExporfinancingInvoices = new FiExporfinancingInvoices();
                    fiExporfinancingInvoices.setAssetCode(fiExporfinancingApply.getAssetCode());
                    fiExporfinancingInvoices.setDeleteFlag("N");
                    List<FiExporfinancingInvoices> fiExporfinancingInvoicesList = fiExporfinancingInvoicesService.selectFiExporfinancingInvoicesList(fiExporfinancingInvoices);
                    if (fiExporfinancingInvoicesList.size() > 0) {
                        BigDecimal totalMoney = BigDecimal.valueOf(0);
                        for (FiExporfinancingInvoices exporfinancingInvoices : fiExporfinancingInvoicesList) {
                            FiExporfinancingInvoicesManifests fiExporfinancingInvoicesManifests = new FiExporfinancingInvoicesManifests();
                            fiExporfinancingInvoicesManifests.setAssetCode(fiExporfinancingApply.getAssetCode());
                            fiExporfinancingInvoicesManifests.setInvoiceRowId(exporfinancingInvoices.getRowId());
                            fiExporfinancingInvoicesManifests.setDeleteFlag("N");
                            totalMoney = totalMoney.add(exporfinancingInvoices.getInvoiceAmountDecimal()).setScale(2, BigDecimal.ROUND_HALF_UP);
                            List<FiExporfinancingInvoicesManifests> fiExporfinancingInvoicesManifestsList = fiExporfinancingInvoicesManifestsService.selectFiExporfinancingInvoicesManifestsList(fiExporfinancingInvoicesManifests);
                            if (fiExporfinancingInvoicesManifestsList.size() > 0) {
                                BigDecimal manifestAmountDecimalTotal = BigDecimal.valueOf(0);
                                for (FiExporfinancingInvoicesManifests invoicesManifests : fiExporfinancingInvoicesManifestsList) {
                                    manifestAmountDecimalTotal = manifestAmountDecimalTotal.add(invoicesManifests.getManifestAmountDecimal());
                                }
                                exporfinancingInvoices.setManifestAmountDecimalTotal(manifestAmountDecimalTotal);//报关单占用总金额
                                exporfinancingInvoices.setManifests(fiExporfinancingInvoicesManifestsList);//报关单明细
                                exporfinancingInvoices.setManifestAmount(String.valueOf(fiExporfinancingInvoicesManifestsList.size()));//报关单数量
                            }
                        }
                        fiExporfinancingApplyByEntCode.setInvoiceAmount(String.valueOf(fiExporfinancingInvoicesList.size()));//发票数量
                        fiExporfinancingApplyByEntCode.setTotalMoney(totalMoney);//发票总金额
                        fiExporfinancingApplyByEntCode.setInvoices(fiExporfinancingInvoicesList);//应收账款明细

                        //附件信息
                        SysAttachments att = new SysAttachments();
                        att.setBusCode(fiExporfinancingApplyByEntCode.getRowId());
                        att.setBusType("fiexporfinancingapply");
                        att.setDeleteFlag("N");
                        List<SysAttachments> sysAttachments = sysAttachmentsService.selectSysAttachmentsList(att);
                        if(sysAttachments!= null && sysAttachments.size()>0){
                            fiExporfinancingApplyByEntCode.setAttachments(sysAttachments);
                        }

                        fiExporfinancing.setApply(fiExporfinancingApplyByEntCode);
                    }else{
                        FiExporfinancingApply fiExporfinancingApplyByEntCodeTwo=new FiExporfinancingApply();
                        fiExporfinancing.setApply(fiExporfinancingApplyByEntCodeTwo);
                    }
                    //融资受理信息
                    FiExporfinancingAccept fiExportFinancingAccept = new FiExporfinancingAccept();
                    fiExportFinancingAccept.setAssetCode(fiExporfinancingApply.getAssetCode());
                    fiExportFinancingAccept.setDeleteFlag("N");
                    FiExporfinancingAccept fiExporfinancingAccept = fiExporfinancingAcceptService.selectFiExporfinancingAcceptByAssetCode(fiExportFinancingAccept);
                    if(fiExporfinancingAccept!=null) {
                        fiExporfinancing.setAccept(fiExporfinancingAccept);
                    }else{
                        FiExporfinancingAccept fiExporfinancingAcceptTwo=new FiExporfinancingAccept();
                        fiExporfinancing.setAccept(fiExporfinancingAcceptTwo);
                    }
                    //存放还款信息
                    FiExporfinancingRec fiExporfinancingRec = new FiExporfinancingRec();
                    //查询放款信息
                    FiExporfinancingPay fiExportFinancingPay = new FiExporfinancingPay();
                    fiExportFinancingPay.setAssetCode(fiExporfinancingApply.getAssetCode());
                    fiExportFinancingPay.setDeleteFlag("N");
                    FiExporfinancingPay fiExporfinancingPay = fiExporfinancingPayService.selectFiExporfinancingPayByAssetCode(fiExportFinancingPay);
                    if (fiExporfinancingPay != null) {
                        fiExporfinancingPay.setCurrency(fiExporfinancingApplyByEntCode.getCurrency());
                        fiExporfinancingRec.setPayAmountDecimalAmount(fiExporfinancingPay.getPayAmountDecimal());
                        fiExporfinancing.setPay(fiExporfinancingPay);
                    }else{
                        FiExporfinancingPay fiExporfinancingPayTwo=new FiExporfinancingPay();
                        fiExporfinancing.setPay(fiExporfinancingPayTwo);
                    }
                    //查询还款信息
                    FiExporfinancingRecs fiExporfinancingRecs = new FiExporfinancingRecs();
                    fiExporfinancingRecs.setAssetCode(fiExporfinancingApply.getAssetCode());
                    fiExportFinancingPay.setDeleteFlag("N");
                    List<FiExporfinancingRecs> fiExporfinancingRecsList = fiExporfinancingRecsService.selectFiExporfinancingRecsList(fiExporfinancingRecs);
                    if (fiExporfinancingRecsList.size() > 0) {
                        BigDecimal recAmountDecimalAmount = BigDecimal.valueOf(0);
                        for (FiExporfinancingRecs exporfinancingRecs : fiExporfinancingRecsList) {
                            recAmountDecimalAmount = recAmountDecimalAmount.add(exporfinancingRecs.getRecAmountDecimal());
                            exporfinancingRecs.setInvoiceCurrency(fiExporfinancingApplyByEntCode.getCurrency());
                        }
                        fiExporfinancingRec.setRecAmountDecimalAmount(recAmountDecimalAmount);
                        fiExporfinancingRec.setRecs(fiExporfinancingRecsList);
                        fiExporfinancing.setRec(fiExporfinancingRec);
                    }else{
                        List<FiExporfinancingRecs> fiExporfinancingRecsListTwo=new ArrayList<>();
                        fiExporfinancingRec.setRecs(fiExporfinancingRecsListTwo);
                        fiExporfinancing.setRec(fiExporfinancingRec);
                    }
//                JSONUtil.toJsonStr(fiExporfinancing);
                    r.setData(fiExporfinancing);
                    r.setB(true);
                    r.setCode(200);
                }
            }else{
                r.setB(false);
                r.setCode(500);
                r.setMsg("请传入业务编码");
                return r;
            }
        }else{
            r.setB(false);
            r.setCode(500);
            r.setMsg("请传入业务编码");
            return r;
        }
        return r;
    }

    /**
     * 新增出口融资主表
     *
     * @param fiExporfinancing 出口融资主表信息
     * @return 结果
     */
    @Override
    public int insertFiExporfinancing(FiExporfinancing fiExporfinancing)
    {
        return fiExporfinancingMapper.insertFiExporfinancing(fiExporfinancing);
    }

    /**
     * 修改出口融资主表
     *
     * @param fiExporfinancing 出口融资主表信息
     * @return 结果
     */
    @Override
    public int updateFiExporfinancing(FiExporfinancing fiExporfinancing)
    {
        return fiExporfinancingMapper.updateFiExporfinancing(fiExporfinancing);
    }


    /**
     * 删除出口融资主表
     *
     * @param rowId 出口融资主表ID
     * @return 结果
     */
    public int deleteFiExporfinancingById(String rowId)
    {
        return fiExporfinancingMapper.deleteFiExporfinancingById( rowId);
    };


    /**
     * 批量删除出口融资主表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiExporfinancingByIds(Integer[] rowIds)
    {
        return fiExporfinancingMapper.deleteFiExporfinancingByIds( rowIds);
    }

}
