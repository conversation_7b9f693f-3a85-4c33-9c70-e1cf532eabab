package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FiExporfinancingInvoicesManifestsMapper;
import com.huazheng.tunny.ocean.api.entity.FiExporfinancingInvoicesManifests;
import com.huazheng.tunny.ocean.service.FiExporfinancingInvoicesManifestsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fiExporfinancingInvoicesManifestsService")
public class FiExporfinancingInvoicesManifestsServiceImpl extends ServiceImpl<FiExporfinancingInvoicesManifestsMapper, FiExporfinancingInvoicesManifests> implements FiExporfinancingInvoicesManifestsService {

    @Autowired
    private FiExporfinancingInvoicesManifestsMapper fiExporfinancingInvoicesManifestsMapper;

    public FiExporfinancingInvoicesManifestsMapper getFiExporfinancingInvoicesManifestsMapper() {
        return fiExporfinancingInvoicesManifestsMapper;
    }

    public void setFiExporfinancingInvoicesManifestsMapper(FiExporfinancingInvoicesManifestsMapper fiExporfinancingInvoicesManifestsMapper) {
        this.fiExporfinancingInvoicesManifestsMapper = fiExporfinancingInvoicesManifestsMapper;
    }

    /**
     * 查询出口融资发票子表信息
     *
     * @param rowId 出口融资发票子表ID
     * @return 出口融资发票子表信息
     */
    @Override
    public FiExporfinancingInvoicesManifests selectFiExporfinancingInvoicesManifestsById(String rowId)
    {
        return fiExporfinancingInvoicesManifestsMapper.selectFiExporfinancingInvoicesManifestsById(rowId);
    }

    /**
     * 查询出口融资发票子表列表
     *
     * @param fiExporfinancingInvoicesManifests 出口融资发票子表信息
     * @return 出口融资发票子表集合
     */
    @Override
    public List<FiExporfinancingInvoicesManifests> selectFiExporfinancingInvoicesManifestsList(FiExporfinancingInvoicesManifests fiExporfinancingInvoicesManifests)
    {
        return fiExporfinancingInvoicesManifestsMapper.selectFiExporfinancingInvoicesManifestsList(fiExporfinancingInvoicesManifests);
    }


    /**
     * 分页模糊查询出口融资发票子表列表
     * @return 出口融资发票子表集合
     */
    @Override
    public Page selectFiExporfinancingInvoicesManifestsListByLike(Query query)
    {
        FiExporfinancingInvoicesManifests fiExporfinancingInvoicesManifests =  BeanUtil.mapToBean(query.getCondition(), FiExporfinancingInvoicesManifests.class,false);
        query.setRecords(fiExporfinancingInvoicesManifestsMapper.selectFiExporfinancingInvoicesManifestsListByLike(query,fiExporfinancingInvoicesManifests));
        return query;
    }

    /**
     * 新增出口融资发票子表
     *
     * @param fiExporfinancingInvoicesManifests 出口融资发票子表信息
     * @return 结果
     */
    @Override
    public int insertFiExporfinancingInvoicesManifests(FiExporfinancingInvoicesManifests fiExporfinancingInvoicesManifests)
    {
        return fiExporfinancingInvoicesManifestsMapper.insertFiExporfinancingInvoicesManifests(fiExporfinancingInvoicesManifests);
    }

    /**
     * 修改出口融资发票子表
     *
     * @param fiExporfinancingInvoicesManifests 出口融资发票子表信息
     * @return 结果
     */
    @Override
    public int updateFiExporfinancingInvoicesManifests(FiExporfinancingInvoicesManifests fiExporfinancingInvoicesManifests)
    {
        return fiExporfinancingInvoicesManifestsMapper.updateFiExporfinancingInvoicesManifests(fiExporfinancingInvoicesManifests);
    }


    /**
     * 删除出口融资发票子表
     *
     * @param rowId 出口融资发票子表ID
     * @return 结果
     */
    public int deleteFiExporfinancingInvoicesManifestsById(String rowId)
    {
        return fiExporfinancingInvoicesManifestsMapper.deleteFiExporfinancingInvoicesManifestsById( rowId);
    };


    /**
     * 批量删除出口融资发票子表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiExporfinancingInvoicesManifestsByIds(Integer[] rowIds)
    {
        return fiExporfinancingInvoicesManifestsMapper.deleteFiExporfinancingInvoicesManifestsByIds( rowIds);
    }

}
