package com.huazheng.tunny.ocean.controller;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.CustomerInfo;
import com.huazheng.tunny.ocean.api.entity.FiBankAuthApply;
import com.huazheng.tunny.ocean.api.entity.FiBankAuthShow;
import com.huazheng.tunny.ocean.service.CustomerInfoService;
import com.huazheng.tunny.ocean.service.FiBankAuthApplyService;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.service.FiBankAuthShowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;


/**
 * 经营信息授权-银行申请表
 *
 * <AUTHOR> code ocean
 * @date 2022-03-02 15:06:09
 */
@RestController
@RequestMapping("/fibankauthapply")
@Slf4j
public class FiBankAuthApplyController {
    @Autowired
    private FiBankAuthApplyService fiBankAuthApplyService;
    @Autowired
    private FiBankAuthShowService fiBankAuthShowService;
    @Autowired
    private SignatureController signatureController;
    @Autowired
    private CustomerInfoService customerInfoService;
    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fiBankAuthApplyService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        params.put("entCode",SecurityUtils.getUserInfo().getUserName());
        return fiBankAuthApplyService.selectFiBankAuthApplyListByLike(new Query<>(params));
    }


    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        FiBankAuthApply fiBankAuthApply =fiBankAuthApplyService.selectFiBankAuthApplyById(rowId);
        return new R<>(fiBankAuthApply);
    }

    /**
     * 保存
     * @param jsonObject
     * @return R
     */
    @PostMapping
    public String save(@RequestBody JSONObject jsonObject) {
        String content = null;
        try {
            //调用接口
            String data = signatureController.getPost(jsonObject);
            FiBankAuthApply fiBankAuthApply = JSONUtil.toBean(data,FiBankAuthApply.class);

            CustomerInfo info = new CustomerInfo();
            info.setSocialUcCode(fiBankAuthApply.getEntSocialCode());
            info.setDeleteFlag("N");
            List<CustomerInfo> customerInfos = customerInfoService.selectCustomerInfoListBySocialUcCode2(info);

            if(customerInfos!=null && customerInfos.size()>0){
                fiBankAuthApply.setEntCode(customerInfos.get(0).getCustomerCode());
            }
            //插入数据
//        fiBankAuthApply.setRowId(UUID.randomUUID().toString());
            fiBankAuthApply.setOperationDate(LocalDateTime.now());
            fiBankAuthApply.setAuditStatus("0");
            fiBankAuthApply.setAddWho("ZC");
            fiBankAuthApply.setAddWhoName("中钞");
            fiBankAuthApply.setAddTime(LocalDateTime.now());
            fiBankAuthApplyService.insertFiBankAuthApply(fiBankAuthApply);
            content = JSONUtil.parseObj(new R<>(Boolean.TRUE,"保存成功！"), false).toStringPretty();

        }catch (Exception e){
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE,e.getMessage()), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPost("/fibankauthapply", content);
        return result;
    }

    /**
     * 保存
     * @param fiBankAuthApply
     * @return R
     */
    @PostMapping("save2")
    public R save2(@RequestBody FiBankAuthApply fiBankAuthApply) {
        //调用接口

        //插入数据
//        fiBankAuthApply.setRowId(UUID.randomUUID().toString());
        fiBankAuthApply.setOperationDate(LocalDateTime.now());
        fiBankAuthApply.setAuditStatus("0");
//        fiBankAuthApply.setAddWho(SecurityUtils.getUserInfo().getUserName());
//        fiBankAuthApply.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
        fiBankAuthApply.setAddTime(LocalDateTime.now());
        fiBankAuthApplyService.insert(fiBankAuthApply);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param fiBankAuthApply
     * @return R
     */
    @PutMapping
    public R update(@RequestBody FiBankAuthApply fiBankAuthApply) {
        fiBankAuthApplyService.updateById(fiBankAuthApply);
        return new R<>(Boolean.TRUE);
    }



    /**
     * 删除
     * @param rowId
     * @return R
     */
    @DeleteMapping("/{rowId}")
    public R delete(@PathVariable  String rowId) {
        fiBankAuthApplyService.deleteById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        fiBankAuthApplyService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<FiBankAuthApply> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = fiBankAuthApplyService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<FiBankAuthApply> list = reader.readAll(FiBankAuthApply.class);
        fiBankAuthApplyService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 银行申请处理
     * @param fiBankAuthApply
     * @return R
     */
    @PostMapping("/audit")
    public R audit(@RequestBody FiBankAuthApply fiBankAuthApply) {
        String authStatus = "";
        Boolean flag = true;
        String msg = "申请处理完成！";
        String from = "001:";
        //查询银行申请原数据
        if(fiBankAuthApply!=null){
            if(StrUtil.isNotEmpty(fiBankAuthApply.getRowId())){
                FiBankAuthApply apply = fiBankAuthApplyService.selectFiBankAuthApplyById(fiBankAuthApply.getRowId());
                if(apply==null){
                    return new R<>(Boolean.FALSE,"该银行申请不存在！");
                }

                //如果审核通过，调用接口
                if(StrUtil.isNotEmpty(fiBankAuthApply.getAuditStatus()) ){
                    FiBankAuthApply apply2 = new FiBankAuthApply();
                    apply2.setRowId(fiBankAuthApply.getRowId());
                    if("1".equals(fiBankAuthApply.getAuditStatus())){
                        apply2.setAgreeFlag("TRUE");
                        authStatus = "1";
                    }else if("5".equals(fiBankAuthApply.getAuditStatus())){
                        apply2.setAgreeFlag("FALSE");
                        authStatus = "5";
                    }
                    String content = JSONUtil.parseObj(apply2, true).toStringPretty();
                    String doPost = signatureController.doPost("/v1/ent/authorizeApplication/agree", content);
                    JSONObject result = JSONUtil.parseObj(doPost);
                    String b = String.valueOf(result.get("b"));
                    if(b!= null && !"".equals(b) && "true".equals(b)){
//                authStatus = "1";
                    }else{
                        authStatus = "0";
                        flag = false;
                        msg = String.valueOf(result.get("msg"));
                        from = "002:";
                    }
                }
                //查询是否存在对应银行展示数据
                FiBankAuthShow show = new FiBankAuthShow();
                show.setEntSocialCode(apply.getEntSocialCode());
                show.setBankTopCode(apply.getBankTopCode());
                show.setDeleteFlag("N");
                List<FiBankAuthShow> fiBankAuthShows = fiBankAuthShowService.selectFiBankAuthShowList(show);
                FiBankAuthShow newShow = new FiBankAuthShow();
                newShow.setStartTime(apply.getStartTime());
                newShow.setEndTime(apply.getEndTime());
                newShow.setOperationDate(LocalDateTime.now());
                newShow.setAuthStatus(authStatus);
//        newShow.setResveredField01(from+msg);
                if(fiBankAuthShows!=null && fiBankAuthShows.size()>0){
                    //存在，更新原数据
                    newShow.setRowId(fiBankAuthShows.get(0).getRowId());
                    newShow.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                    newShow.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                    newShow.setUpdateTime(LocalDateTime.now());
                    fiBankAuthShowService.updateFiBankAuthShow(newShow);
                }else{
                    //不存在，插入新数据
                    newShow.setRowId(UUID.randomUUID().toString());
                    newShow.setEntSocialCode(apply.getEntSocialCode());
                    newShow.setEntSocialName(apply.getEntSocialName());
                    newShow.setBankTopCode(apply.getBankTopCode());
                    newShow.setBankTopName(apply.getBankTopName());
                    newShow.setAddWho(SecurityUtils.getUserInfo().getUserName());
                    newShow.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                    newShow.setAddTime(LocalDateTime.now());
                    fiBankAuthShowService.insertFiBankAuthShow(newShow);
                }
                //更新银行申请审核状态
                FiBankAuthApply newApply = new FiBankAuthApply();
                newApply.setRowId(fiBankAuthApply.getRowId());
                newApply.setAuditStatus(authStatus);
                fiBankAuthApplyService.updateFiBankAuthApply(newApply);
            }
        }

        return new R<>(flag,from+msg);
    }
}
