package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.BasRailwaySettlement;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 中铁多联结算对象表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-08-23 15:48:45
 */
public interface BasRailwaySettlementService extends IService<BasRailwaySettlement> {
    /**
     * 查询中铁多联结算对象表信息
     *
     * @param rowId 中铁多联结算对象表ID
     * @return 中铁多联结算对象表信息
     */
    public BasRailwaySettlement selectBasRailwaySettlementById(String rowId);

    /**
     * 查询余额类型
     * @param provincialCode
     * @return
     */
    public List<String> getBanlanceType(String provincialCode);

    /**
     * 查询中铁多联结算对象表列表
     *
     * @param basRailwaySettlement 中铁多联结算对象表信息
     * @return 中铁多联结算对象表集合
     */
    public List<BasRailwaySettlement> selectBasRailwaySettlementList(BasRailwaySettlement basRailwaySettlement);


    /**
     * 分页模糊查询中铁多联结算对象表列表
     * @return 中铁多联结算对象表集合
     */
    public Page selectBasRailwaySettlementListByLike(Query query);



    /**
     * 新增中铁多联结算对象表
     *
     * @param basRailwaySettlement 中铁多联结算对象表信息
     * @return 结果
     */
    public int insertBasRailwaySettlement(BasRailwaySettlement basRailwaySettlement);

    /**
     * 修改中铁多联结算对象表
     *
     * @param basRailwaySettlement 中铁多联结算对象表信息
     * @return 结果
     */
    public int updateBasRailwaySettlement(BasRailwaySettlement basRailwaySettlement);

    /**
     * 删除中铁多联结算对象表
     *
     * @param basRailwaySettlement 中铁多联结算对象表ID
     * @return 结果
     */
    public int deleteBasRailwaySettlementById(BasRailwaySettlement basRailwaySettlement);

    /**
     * 批量删除中铁多联结算对象表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteBasRailwaySettlementByIds(Integer[] rowIds);

}

