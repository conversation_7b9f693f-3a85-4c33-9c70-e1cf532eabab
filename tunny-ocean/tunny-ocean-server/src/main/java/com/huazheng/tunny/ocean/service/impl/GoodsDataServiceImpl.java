package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.mapper.GoodsDataMapper;
import com.huazheng.tunny.ocean.api.entity.GoodsData;
import com.huazheng.tunny.ocean.service.GoodsDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service("goodsDataService")
public class GoodsDataServiceImpl extends ServiceImpl<GoodsDataMapper, GoodsData> implements GoodsDataService {

    @Autowired
    private GoodsDataMapper goodsDataMapper;

    public GoodsDataMapper getGoodsDataMapper() {
        return goodsDataMapper;
    }

    public void setGoodsDataMapper(GoodsDataMapper goodsDataMapper) {
        this.goodsDataMapper = goodsDataMapper;
    }

    /**
     * 查询货物数据信息
     *
     * @param rowId 货物数据ID
     * @return 货物数据信息
     */
    @Override
    public GoodsData selectGoodsDataById(String rowId)
    {
        return goodsDataMapper.selectGoodsDataById(rowId);
    }

    /**
     * 查询货物数据列表
     *
     * @param goodsData 货物数据信息
     * @return 货物数据集合
     */
    @Override
    public List<GoodsData> selectGoodsDataList(GoodsData goodsData)
    {
        return goodsDataMapper.selectGoodsDataList(goodsData);
    }

    @Override
    public List<GoodsData> selectDuplicate(GoodsData goodsData)
    {
        return goodsDataMapper.selectDuplicate(goodsData);
    }


    /**
     * 分页模糊查询货物数据列表
     * @return 货物数据集合
     */
    @Override
    public Page selectGoodsDataListByLike(Query query)
    {
        GoodsData goodsData =  BeanUtil.mapToBean(query.getCondition(), GoodsData.class,false);
        if(StrUtil.isEmpty(goodsData.getPlatformCode())){
            goodsData.setPlatformCode(SecurityUtils.getUserInfo().getSupPlatformCode());
        }
        goodsData.setDeleteFlag("N");
        goodsData.setAddWho(SecurityUtils.getUserInfo().getUserName());
        query.setRecords(goodsDataMapper.selectGoodsDataListByLike(query,goodsData));
        return query;
    }

    @Override
    public Page pageForCity(Query query)
    {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.add_time");
            query.setAsc(Boolean.FALSE);
        }
        GoodsData goodsData =  BeanUtil.mapToBean(query.getCondition(), GoodsData.class,false);
        goodsData.setStatus("1");
        goodsData.setDeleteFlag("N");
        query.setRecords(goodsDataMapper.selectGoodsDataListByLike(query,goodsData));
        return query;
    }

    /**
     * 新增货物数据
     *
     * @param goodsData 货物数据信息
     * @return 结果
     */
    @Override
    public int insertGoodsData(GoodsData goodsData)
    {
        goodsData.setRowId(UUID.randomUUID().toString());
        goodsData.setStatus("0");
        goodsData.setAddWho(SecurityUtils.getUserInfo().getUserName());
        goodsData.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
        goodsData.setAddTime(LocalDateTime.now());
        return goodsDataMapper.insertGoodsData(goodsData);
    }

    /**
     * 修改货物数据
     *
     * @param goodsData 货物数据信息
     * @return 结果
     */
    @Override
    public int updateGoodsData(GoodsData goodsData)
    {
        goodsData.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
        goodsData.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
        goodsData.setUpdateTime(LocalDateTime.now());
        return goodsDataMapper.updateGoodsData(goodsData);
    }

    @Override
    public int commit(GoodsData goodsData)
    {
        goodsData.setStatus("1");
        goodsData.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
        goodsData.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
        goodsData.setUpdateTime(LocalDateTime.now());
        return goodsDataMapper.updateGoodsData(goodsData);
    }

    @Override
    public void commitList(List<String> rowIds)
    {
        for (String rowId:rowIds
             ) {
            GoodsData goodsData = new GoodsData();
            goodsData.setRowId(rowId);
            goodsData.setStatus("1");
            goodsData.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
            goodsData.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
            goodsData.setUpdateTime(LocalDateTime.now());
            goodsDataMapper.updateGoodsData(goodsData);
        }
    }


    /**
     * 删除货物数据
     *
     * @param rowId 货物数据ID
     * @return 结果
     */
    public int deleteGoodsDataById(String rowId)
    {
        return goodsDataMapper.deleteGoodsDataById( rowId);
    };


    /**
     * 批量删除货物数据对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteGoodsDataByIds(Integer[] rowIds)
    {
        return goodsDataMapper.deleteGoodsDataByIds( rowIds);
    }

}
