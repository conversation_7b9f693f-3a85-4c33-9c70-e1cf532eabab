package com.huazheng.tunny.ocean.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.BalanceDetailDTO;
import com.huazheng.tunny.ocean.api.dto.BillBalanceApproveInfoDTO;
import com.huazheng.tunny.ocean.api.dto.BillBalanceMainCityDTO;
import com.huazheng.tunny.ocean.api.dto.BindingSubBillDTO;
import com.huazheng.tunny.ocean.api.entity.BillBalanceMainCity;
import com.huazheng.tunny.ocean.api.vo.BillBalanceMainCityDetailVO;
import com.huazheng.tunny.ocean.api.vo.SelectPayUserVO;
import com.huazheng.tunny.ocean.service.BillBalanceMainCityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 应收账单结算（市）
 *
 * <AUTHOR> code generator
 * @date 2024-06-15 14:15:46
 */
@Slf4j
@RestController
@RequestMapping("/billbalancemaincity")
public class BillBalanceMainCityController {

    @Autowired
    private BillBalanceMainCityService billBalanceMainCityService;

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  billBalanceMainCityService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return billBalanceMainCityService.selectBillBalanceMainCityListByLike(new Query<>(params));
    }

    /**
     * 信息
     *
     * @param id
     * @return R
     */
    @GetMapping("/info")
    public R info(@RequestParam("id") Integer id, @RequestParam("pageType") Integer pageType) {
        BillBalanceMainCityDetailVO billBalanceMainCity = billBalanceMainCityService.selectBillBalanceMainCityById(id, pageType);
        return new R<>(billBalanceMainCity);
    }

    /**
     * 保存
     *
     * @param billBalanceMainCityDTO
     * @return R
     */
    @PostMapping("/save")
    public R save(@RequestBody BillBalanceMainCityDTO billBalanceMainCityDTO) {
        return billBalanceMainCityService.insertBillBalanceMainCity(billBalanceMainCityDTO);
    }

    /**
     * 修改
     *
     * @param billBalanceMainCityDTO
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody BillBalanceMainCityDTO billBalanceMainCityDTO) {
        billBalanceMainCityService.updateBillBalanceMainCity(billBalanceMainCityDTO);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 撤销
     *
     * @return
     */
    @GetMapping("/revoke")
    public R updateRevoke(@RequestParam Integer id) {
        return billBalanceMainCityService.updateRevoke(id);
    }

    /**
     * 绑定子帐单数据接口
     *
     * @param bindingSubBillDTO
     * @return
     */
    @PostMapping("/bindingSubBill")
    public R bindingSubBill(@RequestBody BindingSubBillDTO bindingSubBillDTO) {
        return billBalanceMainCityService.bindingSubBill(bindingSubBillDTO);
    }

    /**
     * 解除绑定子帐单数据接口
     *
     * @param bindingSubBillDTO
     * @return
     */
    @PostMapping("/removeBindingSubBill")
    public R removeBindingSubBill(@RequestBody BindingSubBillDTO bindingSubBillDTO) {
        return billBalanceMainCityService.removeBindingSubBill(bindingSubBillDTO);
    }


    /**
     * 删除
     *
     * @param id
     * @return R
     */
    @GetMapping("/del/{id}")
    public R delete(@PathVariable Integer id) {
        billBalanceMainCityService.deleteById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody List<Integer> ids) {
        billBalanceMainCityService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 查询余额明细列表
     *
     * @param params
     * @return
     */
    @GetMapping("/selectBalanceDetail")
    public Page selectBalanceDetail(@RequestParam Map<String, Object> params) {
        return billBalanceMainCityService.selectBalanceDetail(new Query<>(params));
    }

    /**
     * 绑定余额明细
     * @param balanceDetailDTOS
     * @return
     */
    @PostMapping("/bindingBalance")
    public R bindingBalance(@RequestBody List<BalanceDetailDTO> balanceDetailDTOS) {
        return billBalanceMainCityService.bindingBalance(balanceDetailDTOS);
    }

    /**
     * 解除绑定余额明细
     * @param balanceDetailDTO
     * @return
     */
    @PostMapping("/removeBalance")
    public R removeBalance(@RequestBody BalanceDetailDTO balanceDetailDTO) {
        return billBalanceMainCityService.removeBalance(balanceDetailDTO);
    }

    /**
     * 修改本次抵扣金额
     * @param balanceDetailDTO
     * @return
     */
    @PostMapping("/updateBalanceAmount")
    public R updateBalanceAmount(@RequestBody BalanceDetailDTO balanceDetailDTO){
        return billBalanceMainCityService.updateBalanceAmount(balanceDetailDTO);
    }

    /**
     * 新增审批记录
     *
     * @return
     */
    @PostMapping("/saveApproveInfo")
    public R saveApproveInfo(@RequestBody BillBalanceApproveInfoDTO billBalanceApproveInfoDTO) {
        return billBalanceMainCityService.saveApproveInfo(billBalanceApproveInfoDTO);
    }

    /**
     * 逻辑删除接口
     *
     * @return
     */
    @GetMapping("/updateRemoveMain")
    public R updateRemoveMain(@RequestParam("id")Integer id, @RequestParam("pageType")Integer pageType){
        return billBalanceMainCityService.updateRemoveById(id, pageType);
    }

    /**
     * 查询应付结算单
     * @return
     */
    @GetMapping("/selectPayeeUserList")
    public List<SelectPayUserVO> selectPayeeUserList(){
        return billBalanceMainCityService.selectPayeeUserList();
    }

    /**
     * 查询应收结算单
     * @return
     */
    @GetMapping("/selectRevenueUserList")
    public List<SelectPayUserVO> selectRevenueUserList(){
        return billBalanceMainCityService.selectRevenueUserList();
    }

    /**
     * 导出EXCEL
     *
     * @return
     */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {

        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<BillBalanceMainCity> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<BillBalanceMainCity> list = billBalanceMainCityService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), BillBalanceMainCity.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }


    /**
     * 导入EXCEL
     * <p>
     *
     * @param titleStr
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/imported")
    @Transactional
    public R imported(String titleStr, MultipartFile file) throws Exception {
        
        /*List list = new ArrayList();
        try {
            EasyExcel.read(file.getInputStream(), BillBalanceMainCity.class, new EasyExcelListener<BillBalanceMainCity>() {
                //数据处理逻辑，需要实现数据校验必须重写父级的invoke方法
                @Override
                public void invoke(BillBalanceMainCity entity, AnalysisContext analysisContext) {
                    //log.info("解析到一条数据:{}", JSON.toJSONString(excelItem));
                    //数据校验逻辑
                    //String name = entity.getId();
                    //if (name.length()>10) {
                    //    throw new RuntimeException(String.format("第%s行错误，名称过长", analysisContext.readRowHolder().getRowIndex() + 1));
                    //}
                    //每读取1000条数据保存一次
                    list.add(entity);
                    if (list.size() >= 1000) {
                        saveData(list);
                        list.clear();
                    }
                }

                *//**
         * 所有数据解析完成了就会来调用，确保最后遗留的数据也存储到数据库
         *//*
                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    saveData(list);
                    list.clear();
                }

                //数据批量保存逻辑
                @Override
                public void saveData(List<BillBalanceMainCity> infoList) {
                        billBalanceMainCityService.insertBatch(infoList);
                }
                //headRowNumber()声明标题行占用行数
            }).headRowNumber(1).sheet().doRead();
        }catch (Exception e){
            e.printStackTrace();
            return new R<>(Boolean.FALSE, e.getMessage());
        }*/
        return new R<>(Boolean.TRUE);
    }

    @GetMapping("/pageByProvince")
    public Page pageByProvince(@RequestParam Map<String, Object> params) {
        return billBalanceMainCityService.pageByProvince(new Query<>(params));
    }

    /**
    * 付款管理关联应付结算单
    * */
    @GetMapping("/pageByProvinceTwo")
    public Page pageByProvinceTwo(@RequestParam Map<String, Object> params) {
        return billBalanceMainCityService.pageByProvinceTwo(new Query<>(params));
    }

    @PostMapping("/updateBillOff")
    public R updateBillOff(@RequestBody Map<String, Object> params){
        return billBalanceMainCityService.updateBillOff(params);
    }

    /**
     * 提交并核销--省平台计划
     * */
    @PostMapping("/submitWriteOff")
    public R submitWriteOff(@RequestBody BillBalanceMainCityDTO billBalanceMainCityDTO){
        return billBalanceMainCityService.submitWriteOff(billBalanceMainCityDTO);
    }

    /**
     * 导出运费核算表
     * @Param: billBalanceMainCity, response
     * @Return: com.huazheng.tunny.common.core.util.R
     * @Author: zhaohr
     * @Date: 2024/08/15 15:10
     **/
    @PostMapping("/exportFreightAccount")
    public R exportFreightAccount(@RequestBody BillBalanceMainCity billBalanceMainCity, HttpServletResponse response){
        return billBalanceMainCityService.exportFreightAccountTwo(billBalanceMainCity,response);
    }

    /**
     * 导出运费核算表（市平台）
     * @Param: billBalanceMainCity, response
     * @Return: com.huazheng.tunny.common.core.util.R
     * @Author: zhaohr
     * @Date: 2024/08/15 15:10
     **/
    @PostMapping("/exportFreightAccountCity")
    public R exportFreightAccountCity(@RequestBody BillBalanceMainCity billBalanceMainCity, HttpServletResponse response){
        return billBalanceMainCityService.exportFreightAccountCity(billBalanceMainCity,response);
    }

    /**
     * 确认并核销
     *
     * @param billBalanceMainCityDTO
     * @return R
     */
    @PostMapping("/confirmAndVerify")
    public R confirmAndVerify(@RequestBody BillBalanceMainCityDTO billBalanceMainCityDTO) {
        billBalanceMainCityService.confirmAndVerify(billBalanceMainCityDTO);
        return new R<>(Boolean.TRUE);
    }
}
