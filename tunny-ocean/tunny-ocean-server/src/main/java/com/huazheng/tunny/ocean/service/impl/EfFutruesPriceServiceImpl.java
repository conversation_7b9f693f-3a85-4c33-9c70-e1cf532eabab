package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.EfFutruesPriceMapper;
import com.huazheng.tunny.ocean.api.entity.EfFutruesPrice;
import com.huazheng.tunny.ocean.service.EfFutruesPriceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("efFutruesPriceService")
public class EfFutruesPriceServiceImpl extends ServiceImpl<EfFutruesPriceMapper, EfFutruesPrice> implements EfFutruesPriceService {

    @Autowired
    private EfFutruesPriceMapper efFutruesPriceMapper;

    public EfFutruesPriceMapper getEfFutruesPriceMapper() {
        return efFutruesPriceMapper;
    }

    public void setEfFutruesPriceMapper(EfFutruesPriceMapper efFutruesPriceMapper) {
        this.efFutruesPriceMapper = efFutruesPriceMapper;
    }

    /**
     * 查询仓单期货价格信息
     *
     * @param rowId 仓单期货价格ID
     * @return 仓单期货价格信息
     */
    @Override
    public EfFutruesPrice selectEfFutruesPriceById(String rowId)
    {
        return efFutruesPriceMapper.selectEfFutruesPriceById(rowId);
    }

    /**
     * 查询仓单期货价格列表
     *
     * @param efFutruesPrice 仓单期货价格信息
     * @return 仓单期货价格集合
     */
    @Override
    public List<EfFutruesPrice> selectEfFutruesPriceList(EfFutruesPrice efFutruesPrice)
    {
        return efFutruesPriceMapper.selectEfFutruesPriceList(efFutruesPrice);
    }


    /**
     * 分页模糊查询仓单期货价格列表
     * @return 仓单期货价格集合
     */
    @Override
    public Page selectEfFutruesPriceListByLike(Query query)
    {
        EfFutruesPrice efFutruesPrice =  BeanUtil.mapToBean(query.getCondition(), EfFutruesPrice.class,false);
        query.setRecords(efFutruesPriceMapper.selectEfFutruesPriceListByLike(query,efFutruesPrice));
        return query;
    }

    /**
     * 新增仓单期货价格
     *
     * @param efFutruesPrice 仓单期货价格信息
     * @return 结果
     */
    @Override
    public int insertEfFutruesPrice(EfFutruesPrice efFutruesPrice)
    {
        return efFutruesPriceMapper.insertEfFutruesPrice(efFutruesPrice);
    }

    /**
     * 修改仓单期货价格
     *
     * @param efFutruesPrice 仓单期货价格信息
     * @return 结果
     */
    @Override
    public int updateEfFutruesPrice(EfFutruesPrice efFutruesPrice)
    {
        return efFutruesPriceMapper.updateEfFutruesPrice(efFutruesPrice);
    }


    /**
     * 删除仓单期货价格
     *
     * @param rowId 仓单期货价格ID
     * @return 结果
     */
    public int deleteEfFutruesPriceById(String rowId)
    {
        return efFutruesPriceMapper.deleteEfFutruesPriceById( rowId);
    };


    /**
     * 批量删除仓单期货价格对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfFutruesPriceByIds(Integer[] rowIds)
    {
        return efFutruesPriceMapper.deleteEfFutruesPriceByIds( rowIds);
    }

}
