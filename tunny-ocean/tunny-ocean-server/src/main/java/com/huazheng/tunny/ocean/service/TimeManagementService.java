package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.TimeManagement;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 时效管理表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-01 09:17:24
 */
public interface TimeManagementService extends IService<TimeManagement> {
    /**
     * 查询时效管理表信息
     *
     * @param rowId 时效管理表ID
     * @return 时效管理表信息
     */
    public TimeManagement selectTimeManagementById(String rowId);

    /**
     * 查询时效管理表列表
     *
     * @param timeManagement 时效管理表信息
     * @return 时效管理表集合
     */
    public List<TimeManagement> selectTimeManagementList(TimeManagement timeManagement);


    /**
     * 分页模糊查询时效管理表列表
     * @return 时效管理表集合
     */
    public Page selectTimeManagementListByLike(Query query);



    /**
     * 新增时效管理表
     *
     * @param timeManagement 时效管理表信息
     * @return 结果
     */
    public int insertTimeManagement(TimeManagement timeManagement);

    /**
     * 修改时效管理表
     *
     * @param timeManagement 时效管理表信息
     * @return 结果
     */
    public int updateTimeManagement(TimeManagement timeManagement);

    /**
     * 删除时效管理表
     *
     * @param rowId 时效管理表ID
     * @return 结果
     */
    public int deleteTimeManagementById(String rowId);

    /**
     * 批量删除时效管理表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTimeManagementByIds(Integer[] rowIds);

}

