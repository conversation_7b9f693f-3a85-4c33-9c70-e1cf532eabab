package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfFinancingReleaseGoods;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 需要补充/解除质押的货物 服务接口层
 *
 * <AUTHOR>
 * @date 2023-02-08 15:13:37
 */
public interface EfFinancingReleaseGoodsService extends IService<EfFinancingReleaseGoods> {
    /**
     * 查询需要补充/解除质押的货物信息
     *
     * @param rowId 需要补充/解除质押的货物ID
     * @return 需要补充/解除质押的货物信息
     */
    public EfFinancingReleaseGoods selectEfFinancingReleaseGoodsById(String rowId);

    /**
     * 查询需要补充/解除质押的货物列表
     *
     * @param efFinancingReleaseGoods 需要补充/解除质押的货物信息
     * @return 需要补充/解除质押的货物集合
     */
    public List<EfFinancingReleaseGoods> selectEfFinancingReleaseGoodsList(EfFinancingReleaseGoods efFinancingReleaseGoods);


    /**
     * 分页模糊查询需要补充/解除质押的货物列表
     * @return 需要补充/解除质押的货物集合
     */
    public Page selectEfFinancingReleaseGoodsListByLike(Query query);

    public Page selectEfFinancingReleaseGoodsListByLike2(Query query);

    /**
     * 新增需要补充/解除质押的货物
     *
     * @param efFinancingReleaseGoods 需要补充/解除质押的货物信息
     * @return 结果
     */
    public int insertEfFinancingReleaseGoods(EfFinancingReleaseGoods efFinancingReleaseGoods);

    /**
     * 修改需要补充/解除质押的货物
     *
     * @param efFinancingReleaseGoods 需要补充/解除质押的货物信息
     * @return 结果
     */
    public int updateEfFinancingReleaseGoods(EfFinancingReleaseGoods efFinancingReleaseGoods);

    /**
     * 删除需要补充/解除质押的货物
     *
     * @param rowId 需要补充/解除质押的货物ID
     * @return 结果
     */
    public int deleteEfFinancingReleaseGoodsById(String rowId);

    /**
     * 批量删除需要补充/解除质押的货物
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfFinancingReleaseGoodsByIds(Integer[] rowIds);

}

