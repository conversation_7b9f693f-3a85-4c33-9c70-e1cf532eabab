package com.huazheng.tunny.ocean.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.CustomerInfo;
import com.huazheng.tunny.ocean.api.entity.CustomerPlatformInfo;
import com.huazheng.tunny.ocean.api.entity.FiBookingFinancing;
import com.huazheng.tunny.ocean.api.entity.FiWhiteList;
import com.huazheng.tunny.ocean.service.CustomerInfoService;
import com.huazheng.tunny.ocean.service.CustomerPlatformInfoService;
import com.huazheng.tunny.ocean.service.FiWhiteListService;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import java.util.UUID;

/**
 * 白名单注册
 *
 * <AUTHOR> code ocean
 * @date 2022-03-16 15:13:43
 */
@RestController
@RequestMapping("/fiwhitelist")
@Slf4j
public class FiWhiteListController {
    @Autowired
    private FiWhiteListService fiWhiteListService;
    @Autowired
    private CustomerInfoService customerInfoService;
    @Autowired
    private CustomerPlatformInfoService customerPlatformInfoService;
    @Autowired
    private SignatureController signatureController;
    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fiWhiteListService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fiWhiteListService.selectFiWhiteListListByLike(new Query<>(params));
    }

    /**
     *  市平台白名单列表
     * @param params
     * @return
     */
    @GetMapping("/selectFiWhiteListListForCity")
    public Page selectFiWhiteListListForCity(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fiWhiteListService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fiWhiteListService.selectFiWhiteListListForCity(new Query<>(params));
    }

    /**
     *  省平台白名单列表
     * @param params
     * @return
     */
    @GetMapping("/selectFiWhiteListListForProvince")
    public Page selectFiWhiteListListForProvince(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fiWhiteListService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fiWhiteListService.selectFiWhiteListListForProvince(new Query<>(params));
    }


    /**
     * 信息
     * @param entSocialCode
     * @return R
     */
    @GetMapping("/{entSocialCode}")
    public R info(@PathVariable("entSocialCode") String entSocialCode) {
        FiWhiteList fiWhiteList =fiWhiteListService.selectFiWhiteListByEntSocialCode(entSocialCode);
        return new R<>(fiWhiteList);
    }

    @GetMapping("/syncRecommendedEnt")
    public R syncRecommendedEnt(@RequestParam("entCode") String entCode,@RequestParam("operationType") String operationType) {
        R r = fiWhiteListService.syncRecommendedEnt(entCode,operationType);
        return r;
    }

    @PostMapping("/syncEntInfo")
    public R syncEntInfo(@RequestBody CustomerInfo cus) {
        R r = fiWhiteListService.syncEntInfo(cus);
        return r;
    }

    @PostMapping("/commit")
    public R commit(@RequestBody CustomerInfo cus) {
        R r = fiWhiteListService.commit(cus);
        return r;
    }

    /**
     * 保存
     * @param fiWhiteList
     * @return R
     */
    @PostMapping
    public R save(@RequestBody FiWhiteList fiWhiteList) {
        fiWhiteListService.insert(fiWhiteList);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 加入白名单
     * @param fiWhiteList
     * @return R
     */
    @PostMapping("/joinList")
    public R joinList(@RequestBody FiWhiteList fiWhiteList) {

        String msg = "加入成功！";
        Boolean flag = true;
        String from = "001:";

        CustomerInfo info = new CustomerInfo();
        info.setSocialUcCode(fiWhiteList.getEntSocialCode());
        info.setDeleteFlag("N");
        List<CustomerInfo> customerInfos = customerInfoService.selectCustomerInfoListBySocialUcCode2(info);

        if(customerInfos!=null && customerInfos.size()>0){
            if(customerInfos.get(0).getCustomerCode()!=null && !"".equals(customerInfos.get(0).getCustomerCode())){
                fiWhiteList.setEntCode(customerInfos.get(0).getCustomerCode());
            }else{
                msg = "未查询到企业客户编码！";
                flag = false;
                return new R<>(flag,from+msg);
            }

            if(customerInfos.get(0).getSocialUcCode()!=null && !"".equals(customerInfos.get(0).getSocialUcCode())){
                fiWhiteList.setEntSocialCode(customerInfos.get(0).getSocialUcCode());
            }else{
                msg = "未查询到企业客户统一社会信用编码！";
                flag = false;
                return new R<>(flag,from+msg);
            }

            fiWhiteList.setOperation("REGISTER");
            if(customerInfos.get(0).getCompanyName()!=null && !"".equals(customerInfos.get(0).getCompanyName())){
                fiWhiteList.setEntName(customerInfos.get(0).getCompanyName());
            }else{
                msg = "未查询到企业客户名称！";
                flag = false;
                return new R<>(flag,from+msg);
            }

            if(customerInfos.get(0).getResveredField03()!=null && !"".equals(customerInfos.get(0).getResveredField03())){
                fiWhiteList.setEntType(customerInfos.get(0).getResveredField03());
            }else{
                msg = "未查询到企业类型！";
                flag = false;
                return new R<>(flag,from+msg);
            }

            if(customerInfos.get(0).getOpenBank()!=null && !"".equals(customerInfos.get(0).getOpenBank())){
                fiWhiteList.setBankAccount(customerInfos.get(0).getOpenBank());
            }else{
                msg = "未查询到客户开户行！";
                flag = false;
                return new R<>(flag,from+msg);
            }

            if(customerInfos.get(0).getConnectAddress()!=null && !"".equals(customerInfos.get(0).getConnectAddress())){
                fiWhiteList.setOfficeAddress(customerInfos.get(0).getConnectAddress());
            }else{
                msg = "未查询到客户办公地址！";
                flag = false;
                return new R<>(flag,from+msg);
            }

            if(customerInfos.get(0).getBankAccount()!=null && !"".equals(customerInfos.get(0).getBankAccount())){
                fiWhiteList.setCorporateAccount(customerInfos.get(0).getBankAccount());
            }else{
                msg = "未查询到客户对公账号！";
                flag = false;
                return new R<>(flag,from+msg);
            }

//            fiWhiteList.setEntStatus("有效");

            CustomerPlatformInfo platformInfo = new CustomerPlatformInfo();
            platformInfo.setCustomerCode(customerInfos.get(0).getCustomerCode());
            platformInfo.setDeleteFlag("N");
            List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoService.selectCustomerPlatformInfoList(platformInfo);
            if(customerPlatformInfos!=null && customerPlatformInfos.size()>0){
                if(customerPlatformInfos.get(0).getContactPerson()!=null && !"".equals(customerPlatformInfos.get(0).getContactPerson())){
                    fiWhiteList.setContacts(customerPlatformInfos.get(0).getContactPerson());
                }else{
                    msg = "未查询到客户联系人！";
                    flag = false;
                    return new R<>(flag,from+msg);
                }

                if(customerPlatformInfos.get(0).getContactNo()!=null && !"".equals(customerPlatformInfos.get(0).getContactNo())){
                    fiWhiteList.setContactsPhone(customerPlatformInfos.get(0).getContactNo());
                }else{
                    msg = "未查询到客户联系电话！";
                    flag = false;
                    return new R<>(flag,from+msg);
                }

                if(customerPlatformInfos.get(0).getPictureUrl()!=null && !"".equals(customerPlatformInfos.get(0).getPictureUrl())){
                    fiWhiteList.setBusinessLicenceUrl(customerPlatformInfos.get(0).getPictureUrl());
                }else{
//                    msg = "未查询到客户营业执照！";
//                    flag = false;
//                    return new R<>(flag,from+msg);
                }

            }
            //调用接口
            String json = JSONUtil.parseObj(fiWhiteList, true).toStringPretty();
            final String result = signatureController.doPost("/v1/safe/whiteList/register", json);
            JSONObject dataObject = JSONUtil.parseObj(result);
            String b = String.valueOf(dataObject.get("b"));
            msg = String.valueOf(dataObject.get("msg"));
            String listStatus = "0";
            flag = true;
            if(!"true".equals(b)){
                listStatus = "4";
                flag = false;
                from = "002:";
            }

            FiWhiteList list = new FiWhiteList();
            if(customerPlatformInfos!=null && customerPlatformInfos.size()>0){
                fiWhiteList.setPlatformCode(customerPlatformInfos.get(0).getPlatformCodePro());
            }
            list.setEntSocialCode(fiWhiteList.getEntSocialCode());
            list.setDeleteFlag("N");
            List<FiWhiteList> fiWhiteLists = fiWhiteListService.selectFiWhiteListList(list);
            fiWhiteList.setPlatformCode(SecurityUtils.getUserInfo().getUserName());
            if(fiWhiteLists!=null && fiWhiteLists.size()>0){
                fiWhiteList.setRowId(fiWhiteLists.get(0).getRowId());
                fiWhiteList.setListStatus(listStatus);
                fiWhiteList.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                fiWhiteList.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                fiWhiteList.setUpdateTime(LocalDateTime.now());
                fiWhiteListService.updateFiWhiteList(fiWhiteList);
            }else{
                fiWhiteList.setRowId(UUID.randomUUID().toString());
                fiWhiteList.setListStatus(listStatus);
                fiWhiteList.setAddWho(SecurityUtils.getUserInfo().getUserName());
                fiWhiteList.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                fiWhiteList.setAddTime(LocalDateTime.now());
                fiWhiteListService.insertFiWhiteList(fiWhiteList);
            }
        }else{
            msg = "未查询到用户信息！";
            flag = false;
        }


        return new R<>(flag,from+msg);
    }

    /**
     * 白名单重新上链
     * @param fiWhiteList
     * @return R
     */
    @PostMapping("/reChain")
    public R reChain(@RequestBody FiWhiteList fiWhiteList) {
        String from = "001:";
        //调用接口
        String json = JSONUtil.parseObj(fiWhiteList, true).toStringPretty();
        final String result = signatureController.doPost("/v1/safe/whiteList/reChain", json);
        JSONObject dataObject = JSONUtil.parseObj(result);
        String b = String.valueOf(dataObject.get("b"));
        String msg = String.valueOf(dataObject.get("msg"));
        String listStatus = "0";
        Boolean flag = true;
        if(!"true".equals(b)){
            listStatus = "4";
            flag = false;
            from = "002:";
        }

        FiWhiteList list = new FiWhiteList();
        list.setEntSocialCode(fiWhiteList.getEntSocialCode());
        List<FiWhiteList> fiWhiteLists = fiWhiteListService.selectFiWhiteListList(list);
        final String userName = SecurityUtils.getUserInfo().getUserName();
        if(StrUtil.isNotEmpty(userName)){
            fiWhiteList.setPlatformCode(userName);
        }

        if(CollUtil.isNotEmpty(fiWhiteLists)){
            fiWhiteList.setRecommendedCertificationName(fiWhiteLists.get(0).getRecommendedCertificationName());
            fiWhiteList.setRecommendedCertificationUrl(fiWhiteLists.get(0).getRecommendedCertificationUrl());
            fiWhiteList.setRecommendationIntroduction(fiWhiteLists.get(0).getRecommendationIntroduction());
            fiWhiteList.setRowId(fiWhiteLists.get(0).getRowId());
            fiWhiteList.setListStatus(listStatus);
            fiWhiteList.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
            fiWhiteList.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
            fiWhiteList.setUpdateTime(LocalDateTime.now());
            fiWhiteListService.updateFiWhiteList(fiWhiteList);
        }else{
            fiWhiteList.setRowId(UUID.randomUUID().toString());
            fiWhiteList.setListStatus(listStatus);
            fiWhiteList.setAddWho(SecurityUtils.getUserInfo().getUserName());
            fiWhiteList.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
            fiWhiteList.setAddTime(LocalDateTime.now());
            fiWhiteListService.insertFiWhiteList(fiWhiteList);
        }

        /*if(fiWhiteLists!=null && fiWhiteLists.size()>0){
            fiWhiteList.setRowId(fiWhiteLists.get(0).getRowId());
            fiWhiteList.setListStatus(listStatus);
            fiWhiteList.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
            fiWhiteList.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
            fiWhiteList.setUpdateTime(LocalDateTime.now());
            fiWhiteListService.updateFiWhiteList(fiWhiteList);
        }else{
            fiWhiteList.setRowId(UUID.randomUUID().toString());
            fiWhiteList.setListStatus(listStatus);
            fiWhiteList.setAddWho(SecurityUtils.getUserInfo().getUserName());
            fiWhiteList.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
            fiWhiteList.setAddTime(LocalDateTime.now());
            fiWhiteListService.insertFiWhiteList(fiWhiteList);
        }*/
        return new R<>(flag,from+msg);
    }

    @PostMapping("/refresh")
    public R refresh(@RequestBody FiWhiteList fiWhiteList) {
        String from = "001:";
        //调用接口
        String json = JSONUtil.parseObj(fiWhiteList, true).toStringPretty();
        final String result = signatureController.doPost("/v1/safe/whiteList/filter", json);
        JSONObject dataObject = JSONUtil.parseObj(result);
        String b = String.valueOf(dataObject.get("b"));
        String msg = String.valueOf(dataObject.get("msg"));
        String listStatus = "0";
        Boolean flag = true;
        if(!"true".equals(b)){
            listStatus = "4";
            flag = false;
            from = "002:";
        }
        Object data = dataObject.get("data");
        JSONArray dataArray = JSONUtil.parseArray(data);
        List<FiWhiteList> fis = JSONUtil.toList(dataArray,FiWhiteList.class);
        FiWhiteList fi = fis.get(0);

        if("REGISTERED".equals(fi.getState())){
            if("SUCCESS".equals(fi.getChainState())){
                listStatus = "1";
            }else{
                listStatus = "0";
            }
        }else if("REVOKED".equals(fi.getState())){
            if("SUCCESS".equals(fi.getChainState())){
                listStatus = "3";
            }else{
                listStatus = "0";
            }
        }
        fiWhiteList.setListStatus(listStatus);
        fiWhiteList.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
        fiWhiteList.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
        fiWhiteList.setUpdateTime(LocalDateTime.now());
        fiWhiteListService.updateFiWhiteListByEntSocialCode(fiWhiteList);
        return new R<>(flag,from+msg);
    }

    /**
     * 移除白名单
     * @param fiWhiteList
     * @return R
     */
    @PostMapping("/outList")
    public R outList(@RequestBody FiWhiteList fiWhiteList) {
        String from = "001:";
        //调用接口
        fiWhiteList.setOperation("REVOKE");
        //调用接口
        String json = JSONUtil.parseObj(fiWhiteList, true).toStringPretty();
        final String result = signatureController.doPost("/v1/safe/whiteList/register", json);
        JSONObject dataObject = JSONUtil.parseObj(result);
        String b = String.valueOf(dataObject.get("b"));
        String msg = String.valueOf(dataObject.get("msg"));
        String listStatus = "3";
        Boolean flag = true;
        if(!"true".equals(b)){
            flag = false;
            listStatus = "4";
            from = "002:";
        }else{
            fiWhiteList.setListStatus(listStatus);
        }

        fiWhiteList.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
        fiWhiteList.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
        fiWhiteList.setUpdateTime(LocalDateTime.now());
        fiWhiteListService.outList(fiWhiteList);
        return new R<>(flag,from+msg);
    }

    //同步白名单数据
    @PostMapping("/updateFiWhiteListStatus")
    public String updateFiWhiteListStatus(@RequestBody JSONObject jsonObject) {
        String content = null;
        try {
            String data = signatureController.getPost(jsonObject);
            FiWhiteList fiWhiteList = JSONUtil.toBean(data, FiWhiteList.class);
            if("VALID".equals(fiWhiteList.getState())){
                if("SUCCESS".equals(fiWhiteList.getChainState())){
                    fiWhiteList.setListStatus("1");
                }else{
                    fiWhiteList.setListStatus("0");
                }
            }else if("REVOKED".equals(fiWhiteList.getState())){
                if("INVALID".equals(fiWhiteList.getChainState())){
                    fiWhiteList.setListStatus("3");
                }else{
                    fiWhiteList.setListStatus("0");
                }
            }
            fiWhiteList.setUpdateWho("ZC");
            fiWhiteList.setUpdateWhoName("中钞");
            fiWhiteList.setUpdateTime(LocalDateTime.now());
            fiWhiteListService.updateFiWhiteListByEntSocialCode(fiWhiteList);
            content = JSONUtil.parseObj(new R<>(Boolean.TRUE,"同步数据完成！"), false).toStringPretty();
        }catch (Exception e){
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE,e.getMessage()), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPost("/fiwhitelist/updateFiWhiteListStatus", content);
        return result;
    }

    /**
     * 修改
     * @param fiWhiteList
     * @return R
     */
    @PutMapping
    public R update(@RequestBody FiWhiteList fiWhiteList) {
        fiWhiteList.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
        fiWhiteList.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
        fiWhiteList.setUpdateTime(LocalDateTime.now());
        fiWhiteListService.updateFiWhiteList(fiWhiteList);
        return new R<>(Boolean.TRUE);
    }

    @PostMapping("/updateWhiteList")
    public R updateWhiteList(@RequestBody FiWhiteList fiWhiteList) {
        String from = "001:";
        FiWhiteList list = fiWhiteListService.selectFiWhiteListById(fiWhiteList.getRowId());
        list.setOperation("REGISTER");
        list.setRecommendationIntroduction(fiWhiteList.getRecommendationIntroduction());
        list.setRecommendedCertificationName(fiWhiteList.getRecommendedCertificationName());
        list.setRecommendedCertificationUrl(fiWhiteList.getRecommendedCertificationUrl());

        fiWhiteList.setEntName(list.getEntName());
        fiWhiteList.setEntSocialCode(list.getEntSocialCode());
        fiWhiteList.setOperation("REGISTER");
        //调用接口
        String json = JSONUtil.parseObj(fiWhiteList, true).toStringPretty();
        final String result = signatureController.doPost("/v1/safe/whiteList/register", json);
        JSONObject dataObject = JSONUtil.parseObj(result);
        String b = String.valueOf(dataObject.get("b"));
        String msg = String.valueOf(dataObject.get("msg"));
        String listStatus = "0";
        Boolean flag = true;
        if(!"true".equals(b)){
            listStatus = "2";
            flag = false;
            from = "002:";
        }
        list.setListStatus(listStatus);
        list.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
        list.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
        list.setUpdateTime(LocalDateTime.now());
        fiWhiteListService.updateFiWhiteList(list);
        return new R<>(flag,from+msg);
    }

    /**
     * 修改
     * @param fiWhiteList
     * @return R
     */
    @PostMapping("/updateListInfo")
    public R updateListInfo(@RequestBody FiWhiteList fiWhiteList) {
        fiWhiteList.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
        fiWhiteList.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
        fiWhiteList.setUpdateTime(LocalDateTime.now());
        fiWhiteListService.updateFiWhiteList(fiWhiteList);
        return new R<>(Boolean.TRUE);
    }



    /**
     * 删除
     * @param rowId
     * @return R
     */
    @DeleteMapping("/{rowId}")
    public R delete(@PathVariable  Integer rowId) {
        fiWhiteListService.deleteById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<Integer> rowIds) {
        fiWhiteListService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<FiWhiteList> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = fiWhiteListService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<FiWhiteList> list = reader.readAll(FiWhiteList.class);
        fiWhiteListService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }
}
