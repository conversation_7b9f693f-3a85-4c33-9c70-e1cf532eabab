package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.FdPostTransport;
import com.huazheng.tunny.ocean.api.entity.FdPostTransportDetail;
import com.huazheng.tunny.ocean.mapper.FdPostTransportDetailMapper;
import com.huazheng.tunny.ocean.mapper.FdPostTransportMapper;
import com.huazheng.tunny.ocean.service.FdPostTransportDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("fdPostTransportDetailService")
public class FdPostTransportDetailServiceImpl extends ServiceImpl<FdPostTransportDetailMapper, FdPostTransportDetail> implements FdPostTransportDetailService {

    @Autowired
    private FdPostTransportDetailMapper fdPostTransportDetailMapper;

    @Autowired
    private FdPostTransportMapper fdPostTransportMapper;


    /**
     * 查询后程转运明细表信息
     *
     * @param id 后程转运明细表ID
     * @return 后程转运明细表信息
     */
    @Override
    public FdPostTransportDetail selectFdPostTransportDetailById(Integer id) {
        return fdPostTransportDetailMapper.selectFdPostTransportDetailById(id);
    }

    /**
     * 查询后程转运明细表列表
     *
     * @param fdPostTransportDetail 后程转运明细表信息
     * @return 后程转运明细表集合
     */
    @Override
    public List<FdPostTransportDetail> selectFdPostTransportDetailList(FdPostTransportDetail fdPostTransportDetail) {
        return fdPostTransportDetailMapper.selectFdPostTransportDetailList(fdPostTransportDetail);
    }


    /**
     * 分页模糊查询后程转运明细表列表
     *
     * @return 后程转运明细表集合
     */
    @Override
    public Page selectFdPostTransportDetailListByLike(Query query) {
        FdPostTransportDetail fdPostTransportDetail = BeanUtil.mapToBean(query.getCondition(), FdPostTransportDetail.class, false);
        query.setRecords(fdPostTransportDetailMapper.selectFdPostTransportDetailListByLike(query, fdPostTransportDetail));
        return query;
    }

    /**
     * 新增后程转运明细表
     *
     * @param fdPostTransportDetail 后程转运明细表信息
     * @return 结果
     */
    @Override
    public int insertFdPostTransportDetail(FdPostTransportDetail fdPostTransportDetail) {
        return fdPostTransportDetailMapper.insertFdPostTransportDetail(fdPostTransportDetail);
    }

    /**
     * 修改后程转运明细表
     *
     * @param fdPostTransportDetail 后程转运明细表信息
     * @return 结果
     */
    @Override
    public int updateFdPostTransportDetail(FdPostTransportDetail fdPostTransportDetail) {
        return fdPostTransportDetailMapper.updateFdPostTransportDetail(fdPostTransportDetail);
    }


    /**
     * 删除后程转运明细表
     *
     * @param id 后程转运明细表ID
     * @return 结果
     */
    public int deleteFdPostTransportDetailById(Integer id) {
        return fdPostTransportDetailMapper.deleteFdPostTransportDetailById(id);
    }

    ;


    /**
     * 批量删除后程转运明细表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFdPostTransportDetailByIds(Integer[] ids) {
        return fdPostTransportDetailMapper.deleteFdPostTransportDetailByIds(ids);
    }
}
