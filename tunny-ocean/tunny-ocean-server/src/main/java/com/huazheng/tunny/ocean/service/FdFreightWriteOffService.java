package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.FdFreightAccounting;
import com.huazheng.tunny.ocean.api.entity.FdFreightWriteOff;

import java.util.List;

/**
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:29:59
 */
public interface FdFreightWriteOffService extends IService<FdFreightWriteOff> {

    public Page getFdFreightWriteOffList(Query<Object> objectQuery);

    public FdFreightWriteOff getFdFreightWriteOffListTotal(Query<Object> objectQuery);
}

