package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FdBillSubDetail;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 子账单详情 服务接口层
 *
 * <AUTHOR>
 * @date 2023-07-19 13:11:45
 */
public interface FdBillSubDetailService extends IService<FdBillSubDetail> {
    /**
     * 查询子账单详情信息
     *
     * @param uuid 子账单详情ID
     * @return 子账单详情信息
     */
    public FdBillSubDetail selectFdBillSubDetailById(String uuid);

    /**
     * 查询子账单详情列表
     *
     * @param fdBillSubDetail 子账单详情信息
     * @return 子账单详情集合
     */
    public List<FdBillSubDetail> selectFdBillSubDetailList(FdBillSubDetail fdBillSubDetail);


    /**
     * 分页模糊查询子账单详情列表
     * @return 子账单详情集合
     */
    public Page selectFdBillSubDetailListByLike(Query query);



    /**
     * 新增子账单详情
     *
     * @param fdBillSubDetail 子账单详情信息
     * @return 结果
     */
    public int insertFdBillSubDetail(FdBillSubDetail fdBillSubDetail);

    /**
     * 修改子账单详情
     *
     * @param fdBillSubDetail 子账单详情信息
     * @return 结果
     */
    public int updateFdBillSubDetail(FdBillSubDetail fdBillSubDetail);

    public int updateFdBillSubDetailByCode(FdBillSubDetail fdBillSubDetail);

    /**
     * 删除子账单详情
     *
     * @param uuid 子账单详情ID
     * @return 结果
     */
    public int deleteFdBillSubDetailById(String uuid);

    /**
     * 批量删除子账单详情
     *
     * @param uuids 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdBillSubDetailByIds(Integer[] uuids);

}

