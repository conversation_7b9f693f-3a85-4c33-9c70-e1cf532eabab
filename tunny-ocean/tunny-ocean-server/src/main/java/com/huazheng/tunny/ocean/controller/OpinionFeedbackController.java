package com.huazheng.tunny.ocean.controller;

import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.OpinionFeedback;
import com.huazheng.tunny.ocean.service.OpinionFeedbackService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 意见反馈
 *
 * <AUTHOR> code generator
 * @date 2024-06-27 18:03:32
 */
@Slf4j
@RestController
@RequestMapping("/opinionfeedback")
public class OpinionFeedbackController {

    @Autowired
    private OpinionFeedbackService opinionFeedbackService;

    @Value("${roles.superAdmin}")
    private String supperAdmin;

    @Value("${roles.normalAdmin}")
    private String normalAdmin;

    @Value("${db.database}")
    private String database;

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        // 查询当前用户是否为超级/，筛选其所属平台对应的创建人提交的意见
        List<String> roles = SecurityUtils.getRoles();
        List<String> supperAdminList = Arrays.asList(supperAdmin.split(","));
        List<String> normalAdminList = Arrays.asList(normalAdmin.split(","));
        if (roles.stream().filter(supperAdminList::contains).collect(Collectors.toList()).size() > 0) {
            // 超级管理员查所有
        } else if (roles.stream().filter(normalAdminList::contains).collect(Collectors.toList()).size() > 0) {
            // 省/市管理员根据所属平台对应的创建人提交的意见
            params.put("platformCode", SecurityUtils.getUserInfo().getPlatformCode());
        } else {
            //非管理员只查自己提交的
            params.put("addWho", SecurityUtils.getUserInfo().getUserName());
        }
        // sys_user表所在的库名
        params.put("database", database);
        return opinionFeedbackService.selectOpinionFeedbackListByLike(new Query<>(params));
    }

    /**
     * 信息
     *
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        OpinionFeedback opinionFeedback = opinionFeedbackService.selectById(rowId);
        return new R<>(opinionFeedback);
    }

    /**
     * 保存
     *
     * @param opinionFeedback
     * @return R
     */
    @PostMapping
    public R save(@RequestBody OpinionFeedback opinionFeedback) {
        opinionFeedback.setRowId(UUID.randomUUID().toString());
        opinionFeedback.setAddWho(SecurityUtils.getUserInfo().getUserName());
        opinionFeedback.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
        opinionFeedback.setAddTime(LocalDateTime.now());
        opinionFeedbackService.insertOpinionFeedback(opinionFeedback);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     *
     * @param opinionFeedback
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody OpinionFeedback opinionFeedback) {
        opinionFeedback.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
        opinionFeedback.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
        opinionFeedback.setUpdateTime(LocalDateTime.now());
        opinionFeedbackService.updateOpinionFeedback(opinionFeedback);
        return new R<>(Boolean.TRUE);
    }


    /**
     * 删除
     *
     * @param rowId
     * @return R
     */
    @GetMapping("/del/{rowId}")
    public R delete(@PathVariable String rowId) {
        OpinionFeedback opinionFeedback = opinionFeedbackService.selectOpinionFeedbackById(rowId);
        opinionFeedback.setDeleteFlag("0");
        opinionFeedback.setDeleteWho(SecurityUtils.getUserInfo().getUserName());
        opinionFeedback.setDeleteWhoName(SecurityUtils.getUserInfo().getRealName());
        opinionFeedback.setDeleteTime(LocalDateTime.now());
        opinionFeedbackService.updateOpinionFeedback(opinionFeedback);
        return new R<>(Boolean.TRUE);
    }
//
//    /**
//     * 批量删除
//     *
//     * @param rowIds
//     * @return R
//     */
//    @PostMapping("/delObjs")
//    public R delObjs(@RequestBody List<String> rowIds) {
//        opinionFeedbackService.deleteBatchIds(rowIds);
//        return new R<>(Boolean.TRUE);
//    }

}
