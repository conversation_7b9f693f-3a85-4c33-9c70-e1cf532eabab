package com.huazheng.tunny.ocean.service.eabusinessprocess;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.dto.eabusinessprocess.EaBusinessMainGroupDTO;
import com.huazheng.tunny.ocean.api.dto.eabusinessprocess.EaBusinessProcessDto;
import com.huazheng.tunny.ocean.api.entity.eaapproval.EaApprovalRecord;
import com.huazheng.tunny.ocean.api.entity.eabusinessprocess.EaBusinessProcess;
import com.huazheng.tunny.ocean.api.vo.BpmVo;
import com.huazheng.tunny.ocean.api.vo.eabusinessprocess.EaBusinessProcessVo;
import com.huazheng.tunny.ocean.util.R;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 业务流程单表 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2025-06-18 11:00:01
 */
public interface EaBusinessProcessService extends IService<EaBusinessProcess> {
    /**
     * 查询业务流程单表信息
     *
     * @param businessProcessId 业务流程单表ID
     * @return 业务流程单表信息
     */
    public EaBusinessProcess selectEaBusinessProcessById(Long businessProcessId);

    /**
     * 查询业务流程单表列表
     *
     * @param eaBusinessProcess 业务流程单表信息
     * @return 业务流程单表集合
     */
    public List<EaBusinessProcess> selectEaBusinessProcessList(EaBusinessProcess eaBusinessProcess);


    /**
     * 分页模糊查询业务流程单表列表
     * @return 业务流程单表集合
     */
    public Page selectEaBusinessProcessListByLike(Query query);



    /**
     * 新增业务流程单表
     *
     * @param eaBusinessProcess 业务流程单表信息
     * @return 结果
     */
    public int insertEaBusinessProcess(EaBusinessProcess eaBusinessProcess);

    /**
     * 修改业务流程单表
     *
     * @param eaBusinessProcess 业务流程单表信息
     * @return 结果
     */
    public int updateEaBusinessProcess(EaBusinessProcess eaBusinessProcess);

    /**
     * 删除业务流程单表
     *
     * @param businessProcessId 业务流程单表ID
     * @return 结果
     */
    public int deleteEaBusinessProcessById(Long businessProcessId);

    /**
     * 批量删除业务流程单表
     *
     * @param businessProcessIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEaBusinessProcessByIds(Integer[] businessProcessIds);

    /**
     * 查询上次业务流程单
     * @param process
     * @param trip
     * @return
     */
    EaBusinessProcess selectEaBusinessProcessOld(EaBusinessProcess process, String trip);

    EaBusinessProcessVo selectBusinessProcessList(Long businessProcessId);

    R submit(EaBusinessProcess eaBusinessProcess);

    R updateWithdrawal(EaBusinessProcess eaBusinessProcess);

    R review(EaBusinessProcessDto eaBusinessProcess);

    /**
     * 一次导入
     *
     * @param file
     * @return
     */
    R importOnceBusinessProcess(MultipartFile file)throws Exception;

    /**
     * 二次导入
     *
     * @param file
     * @return
     */
    R importSecondaryBoxNo(MultipartFile file) throws Exception;

    void updateEaBusinessProcessByOrderCode(String orderCode);

    String exportBusProcessToPDF(EaBusinessMainGroupDTO eaBusinessMainGroupDTO, HttpServletResponse response);

    int del(String orderCode);

    R updateEaBusinessProcessById(EaBusinessProcess eaBusinessProcess);


    /**
     * 查询业务流程单主数量
     * @param eaBusinessProcess
     * @return
     */
    R businessProcessMainNum(EaBusinessMainGroupDTO eaBusinessProcess);

    String exportBusProcessToPNG(EaBusinessMainGroupDTO eaBusinessProcessDto, HttpServletResponse response);

    R saveFiles(EaBusinessProcess eaBusinessProcess);

    void exportBusProcessPDF(EaBusinessMainGroupDTO eaBusinessProcessDto, HttpServletResponse response) throws Exception;


}

