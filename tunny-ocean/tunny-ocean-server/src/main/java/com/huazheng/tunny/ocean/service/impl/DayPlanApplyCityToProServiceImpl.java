package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.mapper.DayPlanApplyCityToProMapper;
import com.huazheng.tunny.ocean.api.entity.DayPlanApplyCityToPro;
import com.huazheng.tunny.ocean.service.DayPlanApplyCityToProService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("dayPlanApplyCityToProService")
public class DayPlanApplyCityToProServiceImpl extends ServiceImpl<DayPlanApplyCityToProMapper, DayPlanApplyCityToPro> implements DayPlanApplyCityToProService {

    @Autowired
    private DayPlanApplyCityToProMapper dayPlanApplyCityToProMapper;

    public DayPlanApplyCityToProMapper getDayPlanApplyCityToProMapper() {
        return dayPlanApplyCityToProMapper;
    }

    public void setDayPlanApplyCityToProMapper(DayPlanApplyCityToProMapper dayPlanApplyCityToProMapper) {
        this.dayPlanApplyCityToProMapper = dayPlanApplyCityToProMapper;
    }

    @Value("${path.CY_MANAGE_ADMIN}")
    private String cyManageAdmin;
    @Value("${path.CY_BIZ_ADMIN}")
    private String cyBizAdmin;
    @Value("${path.PR_MANAGE_ADMIN}")
    private String prManagedmin;
    @Value("${path.PR_BIZ_ADMIN}")
    private String prBizAdmin;

    /**
     * 查询旬/周计划申请表(市平台提交到省平台)信息
     *
     * @param rowId 旬/周计划申请表(市平台提交到省平台)ID
     * @return 旬/周计划申请表(市平台提交到省平台)信息
     */
    @Override
    public DayPlanApplyCityToPro selectDayPlanApplyCityToProById(String rowId)
    {
        return dayPlanApplyCityToProMapper.selectDayPlanApplyCityToProById(rowId);
    }

    /**
     * 查询旬/周计划申请表(市平台提交到省平台)列表
     *
     * @param dayPlanApplyCityToPro 旬/周计划申请表(市平台提交到省平台)信息
     * @return 旬/周计划申请表(市平台提交到省平台)集合
     */
    @Override
    public List<DayPlanApplyCityToPro> selectDayPlanApplyCityToProList(DayPlanApplyCityToPro dayPlanApplyCityToPro)
    {
        return dayPlanApplyCityToProMapper.selectDayPlanApplyCityToProList(dayPlanApplyCityToPro);
    }


    /**
     * 分页模糊查询旬/周计划申请表(市平台提交到省平台)列表
     * @return 旬/周计划申请表(市平台提交到省平台)集合
     */
    @Override
    public Page selectDayPlanApplyCityToProListByLike(Query query) {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.add_time");
            query.setAsc(Boolean.FALSE);
        }
        DayPlanApplyCityToPro dayPlanApplyCityToPro =  BeanUtil.mapToBean(query.getCondition(), DayPlanApplyCityToPro.class,false);
        List<String> roles = SecurityUtils.getRoles();
        if(roles!=null && roles.size()>0){
            for (String role: roles
            ) {
                if(cyManageAdmin.equals(role) || cyBizAdmin.equals(role)){
                    dayPlanApplyCityToPro.setQxType("cy");
                }
                if(prManagedmin.equals(role) || prBizAdmin.equals(role)){
                    dayPlanApplyCityToPro.setQxType("pr");
                }
            }
        }
        List<DayPlanApplyCityToPro> dayPlanApplyCityToPros = dayPlanApplyCityToProMapper.selectDayPlanApplyCityToProListByLike(query, dayPlanApplyCityToPro);
        query.setRecords(dayPlanApplyCityToPros);
        return query;
    }

    @Override
    public Page selectDayPlanApplyCityToProListByLike1(Query query) {
        DayPlanApplyCityToPro dayPlanApplyCityToPro =  BeanUtil.mapToBean(query.getCondition(), DayPlanApplyCityToPro.class,false);
        List<DayPlanApplyCityToPro> dayPlanApplyCityToPros = dayPlanApplyCityToProMapper.selectDayPlanApplyCityToProListByLike1(query, dayPlanApplyCityToPro);
        query.setRecords(dayPlanApplyCityToPros);
        query.setTotal(dayPlanApplyCityToProMapper.selectAllNo1(dayPlanApplyCityToPro));
        return query;
    }

    /**
     * 新增旬/周计划申请表(市平台提交到省平台)
     *
     * @param dayPlanApplyCityToPro 旬/周计划申请表(市平台提交到省平台)信息
     * @return 结果
     */
    @Override
    public int insertDayPlanApplyCityToPro(DayPlanApplyCityToPro dayPlanApplyCityToPro)
    {
        return dayPlanApplyCityToProMapper.insertDayPlanApplyCityToPro(dayPlanApplyCityToPro);
    }

    /**
     * 修改旬/周计划申请表(市平台提交到省平台)
     *
     * @param dayPlanApplyCityToPro 旬/周计划申请表(市平台提交到省平台)信息
     * @return 结果
     */
    @Override
    public int updateDayPlanApplyCityToPro(DayPlanApplyCityToPro dayPlanApplyCityToPro)
    {
        return dayPlanApplyCityToProMapper.updateDayPlanApplyCityToPro(dayPlanApplyCityToPro);
    }

    @Override
    public int updateDayPlanApplyCityToProByNo(DayPlanApplyCityToPro dayPlanApplyCityToPro)
    {
        return dayPlanApplyCityToProMapper.updateDayPlanApplyCityToProByNo(dayPlanApplyCityToPro);
    }


    /**
     * 删除旬/周计划申请表(市平台提交到省平台)
     *
     * @param dayPlanApplyCityToPro 旬/周计划申请表(市平台提交到省平台)ID
     * @return 结果
     */
    public int deleteDayPlanApplyCityToProById(DayPlanApplyCityToPro dayPlanApplyCityToPro)
    {
        return dayPlanApplyCityToProMapper.deleteDayPlanApplyCityToProById( dayPlanApplyCityToPro);
    };


    /**
     * 批量删除旬/周计划申请表(市平台提交到省平台)对象
     *
     * @return 结果
     */
    @Override
    public int deleteDayPlanApplyCityToProByIds(Integer[] rowIds)
    {
        return dayPlanApplyCityToProMapper.deleteDayPlanApplyCityToProByIds( rowIds);
    }

}
