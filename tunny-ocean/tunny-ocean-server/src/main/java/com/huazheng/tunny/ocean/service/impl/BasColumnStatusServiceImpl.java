package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.BasColumnStatusMapper;
import com.huazheng.tunny.ocean.api.entity.BasColumnStatus;
import com.huazheng.tunny.ocean.service.BasColumnStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("basColumnStatusService")
public class BasColumnStatusServiceImpl extends ServiceImpl<BasColumnStatusMapper, BasColumnStatus> implements BasColumnStatusService {

    @Autowired
    private BasColumnStatusMapper basColumnStatusMapper;

    /**
     * 查询列展示状态表信息
     *
     * @param id 列展示状态表ID
     * @return 列展示状态表信息
     */
    @Override
    public BasColumnStatus selectBasColumnStatusById(Integer id)
    {
        return basColumnStatusMapper.selectBasColumnStatusById(id);
    }

    /**
     * 查询列展示状态表列表
     *
     * @param basColumnStatus 列展示状态表信息
     * @return 列展示状态表集合
     */
    @Override
    public List<BasColumnStatus> selectBasColumnStatusList(BasColumnStatus basColumnStatus)
    {
        return basColumnStatusMapper.selectBasColumnStatusList(basColumnStatus);
    }


    /**
     * 分页模糊查询列展示状态表列表
     * @return 列展示状态表集合
     */
    @Override
    public Page selectBasColumnStatusListByLike(Query query)
    {
        BasColumnStatus basColumnStatus =  BeanUtil.mapToBean(query.getCondition(), BasColumnStatus.class,false);
        query.setRecords(basColumnStatusMapper.selectBasColumnStatusListByLike(query,basColumnStatus));
        return query;
    }

    /**
     * 新增列展示状态表
     *
     * @param basColumnStatus 列展示状态表信息
     * @return 结果
     */
    @Override
    public int insertBasColumnStatus(BasColumnStatus basColumnStatus)
    {
        return basColumnStatusMapper.insertBasColumnStatus(basColumnStatus);
    }

    /**
     * 修改列展示状态表
     *
     * @param basColumnStatus 列展示状态表信息
     * @return 结果
     */
    @Override
    public int updateBasColumnStatus(BasColumnStatus basColumnStatus)
    {
        return basColumnStatusMapper.updateBasColumnStatus(basColumnStatus);
    }


    /**
     * 删除列展示状态表
     *
     * @param id 列展示状态表ID
     * @return 结果
     */
    public int deleteBasColumnStatusById(Integer id)
    {
        return basColumnStatusMapper.deleteBasColumnStatusById( id);
    };


    /**
     * 批量删除列展示状态表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBasColumnStatusByIds(Integer[] ids)
    {
        return basColumnStatusMapper.deleteBasColumnStatusByIds( ids);
    }

}
