package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FiExporfinancingRecsMapper;
import com.huazheng.tunny.ocean.api.entity.FiExporfinancingRecs;
import com.huazheng.tunny.ocean.service.FiExporfinancingRecsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fiExporfinancingRecsService")
public class FiExporfinancingRecsServiceImpl extends ServiceImpl<FiExporfinancingRecsMapper, FiExporfinancingRecs> implements FiExporfinancingRecsService {

    @Autowired
    private FiExporfinancingRecsMapper fiExporfinancingRecsMapper;

    public FiExporfinancingRecsMapper getFiExporfinancingRecsMapper() {
        return fiExporfinancingRecsMapper;
    }

    public void setFiExporfinancingRecsMapper(FiExporfinancingRecsMapper fiExporfinancingRecsMapper) {
        this.fiExporfinancingRecsMapper = fiExporfinancingRecsMapper;
    }

    /**
     * 查询出口融资还款信息表信息
     *
     * @param rowId 出口融资还款信息表ID
     * @return 出口融资还款信息表信息
     */
    @Override
    public FiExporfinancingRecs selectFiExporfinancingRecsById(String rowId)
    {
        return fiExporfinancingRecsMapper.selectFiExporfinancingRecsById(rowId);
    }

    /**
     * 查询出口融资还款信息表列表
     *
     * @param fiExporfinancingRecs 出口融资还款信息表信息
     * @return 出口融资还款信息表集合
     */
    @Override
    public List<FiExporfinancingRecs> selectFiExporfinancingRecsList(FiExporfinancingRecs fiExporfinancingRecs)
    {
        return fiExporfinancingRecsMapper.selectFiExporfinancingRecsList(fiExporfinancingRecs);
    }


    /**
     * 分页模糊查询出口融资还款信息表列表
     * @return 出口融资还款信息表集合
     */
    @Override
    public Page selectFiExporfinancingRecsListByLike(Query query)
    {
        FiExporfinancingRecs fiExporfinancingRecs =  BeanUtil.mapToBean(query.getCondition(), FiExporfinancingRecs.class,false);
        query.setRecords(fiExporfinancingRecsMapper.selectFiExporfinancingRecsListByLike(query,fiExporfinancingRecs));
        return query;
    }

    /**
     * 新增出口融资还款信息表
     *
     * @param fiExporfinancingRecs 出口融资还款信息表信息
     * @return 结果
     */
    @Override
    public int insertFiExporfinancingRecs(FiExporfinancingRecs fiExporfinancingRecs)
    {
        return fiExporfinancingRecsMapper.insertFiExporfinancingRecs(fiExporfinancingRecs);
    }

    /**
     * 修改出口融资还款信息表
     *
     * @param fiExporfinancingRecs 出口融资还款信息表信息
     * @return 结果
     */
    @Override
    public int updateFiExporfinancingRecs(FiExporfinancingRecs fiExporfinancingRecs)
    {
        return fiExporfinancingRecsMapper.updateFiExporfinancingRecs(fiExporfinancingRecs);
    }


    /**
     * 删除出口融资还款信息表
     *
     * @param rowId 出口融资还款信息表ID
     * @return 结果
     */
    public int deleteFiExporfinancingRecsById(String rowId)
    {
        return fiExporfinancingRecsMapper.deleteFiExporfinancingRecsById( rowId);
    };


    /**
     * 批量删除出口融资还款信息表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiExporfinancingRecsByIds(Integer[] rowIds)
    {
        return fiExporfinancingRecsMapper.deleteFiExporfinancingRecsByIds( rowIds);
    }

}
