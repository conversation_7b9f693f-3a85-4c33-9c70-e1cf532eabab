package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.StationLineRelation;
import com.huazheng.tunny.ocean.api.entity.StationManagement;
import com.huazheng.tunny.ocean.mapper.LineManagementMapper;
import com.huazheng.tunny.ocean.api.entity.LineManagement;
import com.huazheng.tunny.ocean.mapper.StationLineRelationMapper;
import com.huazheng.tunny.ocean.service.LineManagementService;
import com.huazheng.tunny.ocean.service.SysNoConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service("lineManagementService")
public class LineManagementServiceImpl extends ServiceImpl<LineManagementMapper, LineManagement> implements LineManagementService {

    @Autowired
    private LineManagementMapper lineManagementMapper;

    @Autowired
    private StationLineRelationMapper stationLineRelationMapper;

    @Autowired
    private SysNoConfigService sysNoConfigService;

    /**
     * 查询线路管理信息
     *
     * @param rowId 线路管理ID
     * @return 线路管理信息
     */
    @Override
    public LineManagement selectLineManagementById(String rowId)
    {
        return lineManagementMapper.selectLineManagementById(rowId);
    }

    @Override
    public LineManagement selectLineDetialById(String rowId) {
        return lineManagementMapper.selectLineDetailById(rowId);
    }

    /**
     * 查询线路管理列表
     *
     * @param lineManagement 线路管理信息
     * @return 线路管理集合
     */
    @Override
    public List<LineManagement> selectLineManagementList(LineManagement lineManagement)
    {
        return lineManagementMapper.selectLineManagementList(lineManagement);
    }


    /**
     * 分页模糊查询线路管理列表
     * @return 线路管理集合
     */
    @Override
    public Page selectLineManagementListByLike(Query query)
    {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.add_time");
            query.setAsc(Boolean.FALSE);
        }
        LineManagement lineManagement =  BeanUtil.mapToBean(query.getCondition(), LineManagement.class,false);
//        Integer c= lineManagementMapper.queryCount(lineManagement);
//        if(c!=null&&c!=0) {
//            query.setTotal(c);
            query.setRecords(lineManagementMapper.selectLineManagementListByLike(query, lineManagement));
//        }
        return query;
    }

    /**
     * 新增线路(同时维护站点和线路关系数据)
     *
     * @param lineManagement 线路管理信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R insertLineManagement(LineManagement lineManagement)
    {
        if(lineManagement.getStations()==null){
            return new R(Boolean.FALSE,"请正确选择至少一个站点，如有疑问请联系管理员");
        }
        SecruityUser userInfo= SecurityUtils.getUserInfo();
        List<StationLineRelation> listSlr=new ArrayList<>();  /*组装当前传递站点信息listSlr*/
        List<LineManagement>listLm=new ArrayList();       /*组装当前传递线路主数据listLm*/
        String lineNo="";
        if(lineManagement.getLineCode()!=null&&!"".equals(lineManagement.getLineCode())){
            //非首次保存则先将旧的线路站点关系删除，再对线路主数据更新，对新的站点线路关系进行新增维护
            lineNo=lineManagement.getLineCode();
            StationLineRelation slr=new StationLineRelation();
            slr.setLineCode(lineNo);
            slr.setDeleteWho(userInfo.getUserName());
            slr.setDeleteWhoName(userInfo.getRealName());
            slr.setDeleteFlag("Y");
            try {
                //旧线路站点关系删除
                stationLineRelationMapper.updateStationLineRelation(slr);
                //主数据更新
                lineManagement.setUpdateTime(LocalDateTime.now());
                lineManagement.setUpdateWho(userInfo.getUserName());
                lineManagement.setUpdateWhoName(userInfo.getRealName());
                lineManagement.setLineCode(lineNo);
                listLm.add(lineManagement);
                lineManagementMapper.updateLineManagement(listLm);
                for (StationManagement item :lineManagement.getStations()){
                    StationLineRelation temp=new StationLineRelation();
                    temp.setLineCode(lineNo);
                    temp.setStationName(item.getStationName());
                    temp.setStationCode(item.getStationCode());
                    temp.setAddTime(LocalDateTime.now());
                    temp.setAddWho(userInfo.getUserName());
                    temp.setAddWhoName(userInfo.getRealName());
                    listSlr.add(temp);
                }
                //新线路站点关系新增
                stationLineRelationMapper.insertStationLineRelationBatch(listSlr);
            } catch (Exception e) {
                System.out.println(e.getMessage()+"更新线路站点信息失败！！！");
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return new R(Boolean.FALSE,"更新线路站点失败"+e.getMessage());
            }
            return new R(Boolean.TRUE,"更新线路站点成功");
        }else{
            //首次保存则先对线路主数据新增，再对新的站点线路关系进行新增
            lineNo=sysNoConfigService.genNo("LINE");
            if (lineNo.contains("，")) {
                return new R(500, Boolean.FALSE, lineNo);
            }
            lineManagement.setAddTime(LocalDateTime.now());
            lineManagement.setAddWho(userInfo.getUserName());
            lineManagement.setAddWhoName(userInfo.getRealName());
            lineManagement.setLineCode(lineNo);
            for (StationManagement item :lineManagement.getStations()){
                StationLineRelation temp=new StationLineRelation();
                temp.setLineCode(lineNo);
                temp.setStationName(item.getStationName());
                temp.setStationCode(item.getStationCode());
                temp.setAddTime(LocalDateTime.now());
                temp.setAddWho(userInfo.getUserName());
                temp.setAddWhoName(userInfo.getRealName());
                listSlr.add(temp);
            }
            try {
                //线路、线路站点关系首次新增
                lineManagementMapper.insertLineManagement(lineManagement);
                stationLineRelationMapper.insertStationLineRelationBatch(listSlr);
            } catch (Exception e) {
                System.out.println(e.getMessage()+"新增线路失败！！！");
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return new R(Boolean.FALSE,"新增线路失败"+e.getMessage());
            }
            return new R(Boolean.TRUE,"新增线路成功");
        }
    }

    /**
     * 删除线路管理
     *
     * @param lineCode 线路管理信息
     * @return 结果
     */
    @Override
    public R updateLineManagement(String[]lineCode)
    {
        if(lineCode.length==0){
            return new R(Boolean.FALSE,"至少选中一个站点");
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        List<LineManagement> list=new ArrayList<>();
        for (int i=0;i<lineCode.length;i++){
            LineManagement temp=new LineManagement();
            temp.setLineCode(lineCode[i]);
            temp.setDeleteWhoName(userInfo.getUserName());
            //temp.setDeleteWho(userInfo.getUserCode());
            temp.setDeleteFlag("Y");
            list.add(temp);
        }
        int flag = lineManagementMapper.updateLineManagement(list);
        if(flag==0){
            return new R(Boolean.FALSE,"删除失败");
        }else{
            return new R(Boolean.TRUE,"删除成功");
        }
    }


    /**
     * 删除线路管理
     *
     * @param rowId 线路管理ID
     * @return 结果
     */
    @Override
    public int deleteLineManagementById(String rowId)
    {
        return lineManagementMapper.deleteLineManagementById( rowId);
    };


    /**
     * 批量删除线路管理对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteLineManagementByIds(Integer[] rowIds)
    {
        return lineManagementMapper.deleteLineManagementByIds( rowIds);
    }

    @Override
    public List<LineManagement> selectLineManagementListByLineCode(LineManagement lineManagement)
    {
        return lineManagementMapper.selectLineManagementListByLineCode(lineManagement);
    }

}
