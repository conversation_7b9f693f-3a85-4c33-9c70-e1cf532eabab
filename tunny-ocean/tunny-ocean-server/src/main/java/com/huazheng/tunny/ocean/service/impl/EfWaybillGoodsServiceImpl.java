package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.EfWaybillGoodsMapper;
import com.huazheng.tunny.ocean.api.entity.EfWaybillGoods;
import com.huazheng.tunny.ocean.service.EfWaybillGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("efWaybillGoodsService")
public class EfWaybillGoodsServiceImpl extends ServiceImpl<EfWaybillGoodsMapper, EfWaybillGoods> implements EfWaybillGoodsService {

    @Autowired
    private EfWaybillGoodsMapper efWaybillGoodsMapper;

    public EfWaybillGoodsMapper getEfWaybillGoodsMapper() {
        return efWaybillGoodsMapper;
    }

    public void setEfWaybillGoodsMapper(EfWaybillGoodsMapper efWaybillGoodsMapper) {
        this.efWaybillGoodsMapper = efWaybillGoodsMapper;
    }

    /**
     * 查询信息
     *
     * @param rowId ID
     * @return 信息
     */
    @Override
    public EfWaybillGoods selectEfWaybillGoodsById(String rowId)
    {
        return efWaybillGoodsMapper.selectEfWaybillGoodsById(rowId);
    }

    @Override
    public List<EfWaybillGoods> selectList(String waybillNo)
    {
        return efWaybillGoodsMapper.selectList(waybillNo);
    }

    /**
     * 查询列表
     *
     * @param efWaybillGoods 信息
     * @return 集合
     */
    @Override
    public List<EfWaybillGoods> selectEfWaybillGoodsList(EfWaybillGoods efWaybillGoods)
    {
        return efWaybillGoodsMapper.selectEfWaybillGoodsList(efWaybillGoods);
    }


    /**
     * 分页模糊查询列表
     * @return 集合
     */
    @Override
    public Page selectEfWaybillGoodsListByLike(Query query)
    {
        EfWaybillGoods efWaybillGoods =  BeanUtil.mapToBean(query.getCondition(), EfWaybillGoods.class,false);
        query.setRecords(efWaybillGoodsMapper.selectEfWaybillGoodsListByLike(query,efWaybillGoods));
        return query;
    }

    /**
     * 新增
     *
     * @param efWaybillGoods 信息
     * @return 结果
     */
    @Override
    public int insertEfWaybillGoods(EfWaybillGoods efWaybillGoods)
    {
        return efWaybillGoodsMapper.insertEfWaybillGoods(efWaybillGoods);
    }

    /**
     * 修改
     *
     * @param efWaybillGoods 信息
     * @return 结果
     */
    @Override
    public int updateEfWaybillGoods(EfWaybillGoods efWaybillGoods)
    {
        return efWaybillGoodsMapper.updateEfWaybillGoods(efWaybillGoods);
    }


    /**
     * 删除
     *
     * @param rowId ID
     * @return 结果
     */
    public int deleteEfWaybillGoodsById(String rowId)
    {
        return efWaybillGoodsMapper.deleteEfWaybillGoodsById( rowId);
    };


    /**
     * 批量删除对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfWaybillGoodsByIds(Integer[] rowIds)
    {
        return efWaybillGoodsMapper.deleteEfWaybillGoodsByIds( rowIds);
    }

}
