package com.huazheng.tunny.ocean.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.CustomerInfo;
import com.huazheng.tunny.ocean.api.entity.FdRemittanceRecord;
import com.huazheng.tunny.ocean.api.entity.SysDict;
import com.huazheng.tunny.ocean.service.CustomerInfoService;
import com.huazheng.tunny.ocean.service.FdRemittanceRecordService;
import com.huazheng.tunny.ocean.service.SysDictService;
import com.huazheng.tunny.ocean.service.SysNoConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

/**
 * 收款记录表
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:30:42
 */
@RestController
@RequestMapping("/fdremittancerecord")
@Slf4j
public class FdRemittanceRecordController {
    @Autowired
    private FdRemittanceRecordService fdRemittanceRecordService;
    @Autowired
    private CustomerInfoService customerInfoService;
    @Autowired
    private SysNoConfigService noConfigService;
    @Autowired
    private SysDictService sysDictService;

    /**
     * 收款列表
     *
     * @param fdRemittanceRecord
     * @return
     */
    @PostMapping("/getList")
    public R getList(@RequestBody FdRemittanceRecord fdRemittanceRecord) {
        //数据库字段值完整查询
        // return  fdRemittanceRecordService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        fdRemittanceRecord.setDeleteFlag("N");
        List<FdRemittanceRecord> list = fdRemittanceRecordService.selectFdRemittanceRecordList(fdRemittanceRecord);
        return new R<>(200, Boolean.TRUE, list);
    }

    @GetMapping("/page2")
    public Page page2(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdRemittanceRecordService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdRemittanceRecordService.page2(new Query<>(params));
    }

    @GetMapping("/selectDetails")
    public Page selectDetails(@RequestParam Map<String, Object> params) {
        return fdRemittanceRecordService.selectDetails(new Query<>(params));
    }


    /**
     * 收款一级列表
     *
     * @param params
     * @return
     */
    @GetMapping("/RemittanceRecordListPage")
    public Page RemittanceRecordListPage(@RequestParam Map<String, Object> params) {

        //对象模糊查询
        return fdRemittanceRecordService.selectRemittanceRecordListPage(new Query<>(params));
    }

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdRemittanceRecordService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdRemittanceRecordService.selectFdRemittanceRecordListByLike(new Query<>(params));
    }

    /**
     * 根据用户code查询收款未使用明细
     *
     * @param fdRemittanceRecord
     * @return
     */
    @GetMapping("/getInfoByCustomerCode")
    public R getInfoByCustomerCode(FdRemittanceRecord fdRemittanceRecord) {
        return fdRemittanceRecordService.getInfoByCustomerCode(fdRemittanceRecord);
    }

    /**
     * 根据billCode查询收款已抵扣明细
     *
     * @param
     * @return
     */
    @GetMapping("/selectInfoByBillCode")
    public R selectInfoByBillCode(String billCode, String incomeFlag) {
        return fdRemittanceRecordService.selectInfoByBillCode(billCode, incomeFlag);
    }


    /**
     * 信息
     *
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Long id) {
        FdRemittanceRecord fdRemittanceRecord = fdRemittanceRecordService.selectFdRemittanceRecordById(id);
        return new R<>(fdRemittanceRecord);
    }

    /**
     * 保存
     *
     * @param fdRemittanceRecord
     * @return R
     */
    @PostMapping("/save")
    public R save(@RequestBody FdRemittanceRecord fdRemittanceRecord) {
        String tsIn = noConfigService.genNo("RE");
        if (tsIn.contains("请联系系统管理员")) {
            return R.error("生成收款编码出现错误!");
        }
        fdRemittanceRecord.setRemittanceRecordCode(tsIn);

        return fdRemittanceRecordService.save(fdRemittanceRecord);
    }

    /**
     * 省平台新增应收付款记录
     *
     * @param fdRemittanceRecord
     * @return R
     */
    @PostMapping("/saveTwo")
    public R saveTwo(@RequestBody FdRemittanceRecord fdRemittanceRecord) {
        String tsIn = noConfigService.genNo("RE");
        if (tsIn.contains("请联系系统管理员")) {
            return R.error("生成收款编码出现错误!");
        }
        fdRemittanceRecord.setRemittanceRecordCode(tsIn);

        return fdRemittanceRecordService.saveTwo(fdRemittanceRecord);
    }

    /**
     * 修改
     *
     * @param fdRemittanceRecord
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody FdRemittanceRecord fdRemittanceRecord) {
        if (fdRemittanceRecord.getId() == null) {
            return R.error("修改的记录的ID不能为空！");
        }
        FdRemittanceRecord record = fdRemittanceRecordService.selectFdRemittanceRecordById(fdRemittanceRecord.getId());
        if ("1".equals(record.getClaimStatus())) {
            return R.error("被领用的费用不允许编辑和作废！");
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        fdRemittanceRecord.setUpdateWho(userInfo.getUserName());
        fdRemittanceRecord.setUpdateWhoName(userInfo.getRealName());
        fdRemittanceRecord.setUpdateTime(LocalDateTime.now());
        if (fdRemittanceRecord.getId() != null) {
            fdRemittanceRecordService.updateFdRemittanceRecord(fdRemittanceRecord);
        }
        return new R<>(Boolean.TRUE);
    }


    /**
     * 删除
     *
     * @param id
     * @return R
     */
    @DeleteMapping("/{id}")
    public R delete(@PathVariable Long id) {
        fdRemittanceRecordService.deleteById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody List<Long> ids) {
        fdRemittanceRecordService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 导出EXCEL
     *
     * @return
     */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<FdRemittanceRecord> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = fdRemittanceRecordService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }


    /**
     * 导入EXCEL
     * <p>
     *
     * @param titleStr
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/imported")
    public R imported(String titleStr, MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<FdRemittanceRecord> list = reader.readAll(FdRemittanceRecord.class);
        fdRemittanceRecordService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }

    /* *
     * 导出
     * @param map
     * @param response
     * @throws IOException
     */
    @PostMapping("/exportDetails")
    public void exportDetails(@RequestBody FdRemittanceRecord param, HttpServletResponse response) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("收款详情记录");
        XSSFRow row = sheet.createRow(0);
        //宽度
        for (int i = 0; i <= 6; i++) {
            sheet.setColumnWidth(i, 90 * 90);
//            sheet.setColumnWidth(m, “列名”.getBytes().length*2*256);
        }
        row.setHeight((short) (10 * 50));
        //字体
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");

        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);//水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setBorderBottom(BorderStyle.THIN); //下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);
        XSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("客户名称");
        cell0.setCellStyle(style);
        XSSFCell cell1 = row.createCell(1);
        cell1.setCellValue("收款方式");
        cell1.setCellStyle(style);
        XSSFCell cell2 = row.createCell(2);
        cell2.setCellValue("收款金额");
        cell2.setCellStyle(style);
        XSSFCell cell3 = row.createCell(3);
        cell3.setCellValue("核销金额");
        cell3.setCellStyle(style);
        XSSFCell cell4 = row.createCell(4);
        cell4.setCellValue("收款余额");
        cell4.setCellStyle(style);
        XSSFCell cell5 = row.createCell(5);
        cell5.setCellValue("收款时间");
        cell5.setCellStyle(style);
        XSSFCell cell6 = row.createCell(6);
        cell6.setCellValue("备注");
        cell6.setCellStyle(style);
        List<FdRemittanceRecord> fdRemittanceRecords = fdRemittanceRecordService.selectDetailsList(param);


        if (CollUtil.isNotEmpty(fdRemittanceRecords)) {
            int i = 1;
            for (FdRemittanceRecord info : fdRemittanceRecords) {
                XSSFRow hssfRow = sheet.createRow(i);
                hssfRow.createCell(0).setCellValue(info.getCustomerName());
                if (info.getRemittanceName() == null) {
                    hssfRow.createCell(1).setCellValue("收款");
                } else {
                    hssfRow.createCell(1).setCellValue(info.getRemittanceName());
                }
                hssfRow.createCell(2).setCellValue(info.getRemittanceAmount() == null ? 0 : info.getRemittanceAmount().doubleValue());
                hssfRow.createCell(3).setCellValue(info.getUseMoney() == null ? 0 : info.getUseMoney().doubleValue());
                hssfRow.createCell(4).setCellValue(info.getResidualAmount() == null ? 0 : info.getResidualAmount().doubleValue());
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                hssfRow.createCell(5).setCellValue(info.getCollectionTime().format(formatter));
                hssfRow.createCell(6).setCellValue(info.getRemark());
                i++;
            }
        }

        //字体
        XSSFFont textFont = workbook.createFont();
        textFont.setFontHeightInPoints((short) 12);
        textFont.setFontName("宋体");

        XSSFCellStyle textStyle = workbook.createCellStyle();
        textStyle.setFont(textFont);
        textStyle.setAlignment(HorizontalAlignment.CENTER);//水平居中
        textStyle.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        textStyle.setBorderBottom(BorderStyle.THIN); //下边框
        textStyle.setBorderLeft(BorderStyle.THIN);//左边框
        textStyle.setBorderTop(BorderStyle.THIN);//上边框
        textStyle.setBorderRight(BorderStyle.THIN);//右边框
        textStyle.setBottomBorderColor(HSSFColor.BLACK.index);
        textStyle.setLeftBorderColor(HSSFColor.BLACK.index);
        textStyle.setRightBorderColor(HSSFColor.BLACK.index);
        textStyle.setTopBorderColor(HSSFColor.BLACK.index);

        if (CollUtil.isNotEmpty(fdRemittanceRecords)) {
            for (int i = 1; i < (fdRemittanceRecords.size() + 1); i++) {
                XSSFRow hssfRow = sheet.getRow(i);
                hssfRow.getCell(0).setCellStyle(textStyle);
                hssfRow.getCell(1).setCellStyle(textStyle);
                hssfRow.getCell(2).setCellStyle(textStyle);
                hssfRow.getCell(3).setCellStyle(textStyle);
                hssfRow.getCell(4).setCellStyle(textStyle);
                hssfRow.getCell(5).setCellStyle(textStyle);
                hssfRow.getCell(6).setCellStyle(textStyle);

            }
        }

        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setCharacterEncoding("UTF-8"); // 明确指定字符编码
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("收款详情记录", "utf-8") + ".xlsx");
        response.addHeader("Pragma", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }


    /* *
     * 导出
     * @param map
     * @param response
     * @throws IOException
     */
    @PostMapping("/exportedFile")
    public void exported(@RequestBody Map<String, Object> param, HttpServletResponse response) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("收款记录");
        XSSFRow row = sheet.createRow(0);
        //宽度
        for (int i = 0; i <= 4; i++) {
            sheet.setColumnWidth(i, 90 * 90);
//            sheet.setColumnWidth(m, “列名”.getBytes().length*2*256);
        }
        row.setHeight((short) (10 * 50));
        //字体
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");

        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);//水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setBorderBottom(BorderStyle.THIN); //下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);
        XSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("客户名称");
        cell0.setCellStyle(style);
        XSSFCell cell1 = row.createCell(1);
        cell1.setCellValue("收款方式");
        cell1.setCellStyle(style);
        XSSFCell cell2 = row.createCell(2);
        cell2.setCellValue("收款金额");
        cell2.setCellStyle(style);
        XSSFCell cell3 = row.createCell(3);
        cell3.setCellValue("核销金额");
        cell3.setCellStyle(style);
        XSSFCell cell4 = row.createCell(4);
        cell4.setCellValue("未核销余额");
        cell4.setCellStyle(style);
        XSSFCell cell5 = row.createCell(5);
        cell5.setCellValue("备注");
        cell5.setCellStyle(style);
        List<FdRemittanceRecord> fdRemittanceRecords = new ArrayList<>();
        if ("0".equals(param.get("platformFlag"))) {
            fdRemittanceRecords = fdRemittanceRecordService.selectRemittanceRecordList(param);
        } else if ("1".equals(param.get("platformFlag"))) {
            String[] addTimes = param.get("collectionTime").toString().split("-");
            param.put("addTimeStr", getFirstDayOfMonth1(Integer.valueOf(addTimes[0]), Integer.valueOf(addTimes[1])));
            param.put("addTimeEnd", getLastDayOfMonth1(Integer.valueOf(addTimes[0]), Integer.valueOf(addTimes[1])));
            fdRemittanceRecords = fdRemittanceRecordService.selectRemittanceRecordListSheng(param);
        }
        if (CollUtil.isNotEmpty(fdRemittanceRecords)) {
            int i = 1;
            for (FdRemittanceRecord info : fdRemittanceRecords) {
                XSSFRow hssfRow = sheet.createRow(i);
                if ("1".equals(param.get("platformFlag"))) {
                    hssfRow.createCell(0).setCellValue(info.getCustomerName());
                    if (info.getRemittanceType() == null) {
                        hssfRow.createCell(1).setCellValue("收款");
                    } else {
                        hssfRow.createCell(1).setCellValue(info.getRemittanceType());
                    }
                    hssfRow.createCell(2).setCellValue(info.getRemittanceAmount().doubleValue());
                    hssfRow.createCell(3).setCellValue(info.getRemittanceAmount().subtract(info.getResidualAmount()).doubleValue());
                    hssfRow.createCell(4).setCellValue(info.getResidualAmount().doubleValue());
                } else {
                    hssfRow.createCell(0).setCellValue(info.getCustomerName());
                    if (info.getRemittanceType() == null) {
                        hssfRow.createCell(1).setCellValue("收款");
                    } else {
                        hssfRow.createCell(1).setCellValue(info.getRemittanceType());
                    }
                    hssfRow.createCell(2).setCellValue(info.getRemittanceAmount().doubleValue());
                    hssfRow.createCell(3).setCellValue(info.getClearedAmount().doubleValue());
                    hssfRow.createCell(4).setCellValue(info.getResidualAmount().doubleValue());
                }
                i++;
            }
        }
        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String("收款记录.xlsx".getBytes("GB2312"), "8859_1"));
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }

    /* *
     * 导出
     * @param map
     * @param response
     * @throws IOException
     */
    @PostMapping("/xiazaiexportedFileSheng")
    public void xiazaiexportedFileSheng(@RequestBody Map<String, Object> param, HttpServletResponse response) throws IOException {
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("收款记录模板");
        HSSFRow row = sheet.createRow(0);
//        HSSFSheet sheetDrsm = workbook.createSheet("导入说明");
//        HSSFRow row1Drsm = sheetDrsm.createRow(0);
//        HSSFRow row2Drsm = sheetDrsm.createRow(1);
        //宽度
        for (int i = 0; i <= 5; i++) {
            sheet.setColumnWidth(i, 90 * 100);
//            sheet.setColumnWidth(m, “列名”.getBytes().length*2*256);
        }
//        sheetDrsm.setColumnWidth(0, 255 * 150);
//        sheetDrsm.setColumnWidth(0, 255 * 150);
        row.setHeight((short) (10 * 50));
        //字体
        HSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");

        HSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);//水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setBorderBottom(BorderStyle.THIN); //下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);


        HSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("客户编码");
        cell0.setCellStyle(style);
        HSSFCell cell1 = row.createCell(1);
        cell1.setCellValue("客户名称");
        cell1.setCellStyle(style);
        HSSFCell cell2 = row.createCell(2);
        cell2.setCellValue("收款方式(必填)");
        cell2.setCellStyle(style);
        HSSFCell cell3 = row.createCell(3);
        cell3.setCellValue("收款金额(必填)");
        cell3.setCellStyle(style);
        HSSFCell cell4 = row.createCell(4);
        cell4.setCellValue("收款时间(必填)");
        cell4.setCellStyle(style);
        HSSFCell cell5 = row.createCell(5);
        cell5.setCellValue("备注(必填)");
        cell5.setCellStyle(style);

        HSSFCellStyle styleDrsm = workbook.createCellStyle();
        styleDrsm.setFont(font);
        styleDrsm.setAlignment(HorizontalAlignment.LEFT);//水平居做
        styleDrsm.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
//        styleDrsm.setFillForegroundColor(IndexedColors.RED.getIndex());//设置单元格背景颜色
//        styleDrsm.setFillPattern(CellStyle.SOLID_FOREGROUND);//设置单元格背景颜色
        styleDrsm.setBorderBottom(BorderStyle.THIN); //下边框
        styleDrsm.setBorderLeft(BorderStyle.THIN);//左边框
        styleDrsm.setBorderTop(BorderStyle.THIN);//上边框
        styleDrsm.setBorderRight(BorderStyle.THIN);//右边框
        styleDrsm.setBottomBorderColor(HSSFColor.BLACK.index);
        styleDrsm.setLeftBorderColor(HSSFColor.BLACK.index);
        styleDrsm.setRightBorderColor(HSSFColor.BLACK.index);
        styleDrsm.setTopBorderColor(HSSFColor.BLACK.index);


//        HSSFCell cell1Drsm = row1Drsm.createCell(0);
//        cell1Drsm.setCellValue("收款方式必选，收款金额必填，备注必填");
//        cell1Drsm.setCellStyle(styleDrsm);
//        HSSFCell cell2Drsm = row2Drsm.createCell(0);
//        cell2Drsm.setCellValue("一个公司可以导入多个值，请把把该公司的客户名称和客户编码复制多行，请把没导入的公司值删除");
//        cell2Drsm.setCellStyle(styleDrsm);
//        List<FdRemittanceRecord> fdRemittanceRecords = fdRemittanceRecordService.selectRemittanceRecordList(param);
//        if (fdRemittanceRecords.size() > 0) {
//            int i = 1;
//            for (FdRemittanceRecord info : fdRemittanceRecords) {
//                HSSFRow hssfRow = sheet.createRow(i);
//
//                hssfRow.createCell(0).setCellValue(info.getCustomerNo());
//
//                hssfRow.createCell(1).setCellValue(info.getCustomerName());
//
//                hssfRow.createCell(2).setCellValue(info.getRemittanceName());
//                i++;
//            }
//        }
        //获取字典值
        SysDict sysDict = new SysDict();
        sysDict.setDictType("PAYMENT");
        List<SysDict> sysDicts = sysDictService.selectSysDictList(sysDict);
        String[] strings = new String[sysDicts.size()];
        for (int i = 0; i < sysDicts.size(); i++) {
            strings[i] = sysDicts.get(i).getDictValue();
        }

        addValidationToSheet(workbook, sheet, strings, 'C', 1, 99999);

        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String("收款记录.xlsx".getBytes("GB2312"), "8859_1"));
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }

    /**
     * 给sheet页，添加下拉列表
     *
     * @param workbook    excel文件，用于添加Name
     * @param targetSheet 级联列表所在sheet页
     * @param options     级联数据 ['百度','阿里巴巴']
     * @param column      下拉列表所在列 从'A'开始
     * @param fromRow     下拉限制开始行
     * @param endRow      下拉限制结束行
     */
    public static void addValidationToSheet(Workbook workbook, Sheet targetSheet, Object[] options, char column, int fromRow, int endRow) {
        String hiddenSheetName = "sheet" + workbook.getNumberOfSheets();
        Sheet optionsSheet = workbook.createSheet(hiddenSheetName);
        String nameName = column + "_parent";

        int rowIndex = 0;
        for (Object option : options) {
            int columnIndex = 0;
            Row row = optionsSheet.createRow(rowIndex++);
            Cell cell = row.createCell(columnIndex++);
            cell.setCellValue(option.toString());
        }

        createName(workbook, nameName, hiddenSheetName + "!$A$1:$A$" + options.length);

        DVConstraint constraint = DVConstraint.createFormulaListConstraint(nameName);
        CellRangeAddressList regions = new CellRangeAddressList(fromRow, endRow, (int) column - 'A', (int) column - 'A');
        targetSheet.addValidationData(new HSSFDataValidation(regions, constraint));
    }

    private static Name createName(Workbook workbook, String nameName, String formula) {
        Name name = workbook.createName();
        name.setNameName(nameName);
        name.setRefersToFormula(formula);
        return name;
    }

    /* *
     * 导出
     * @param map
     * @param response
     * @throws IOException
     */
    @PostMapping("/xiazaiexportedFile")
    public void xiazaiexportedFile(@RequestBody Map<String, Object> param, HttpServletResponse response) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("汇款记录模板");
        XSSFRow row = sheet.createRow(0);
        //宽度
        for (int i = 0; i <= 4; i++) {
            sheet.setColumnWidth(i, 90 * 90);
//            sheet.setColumnWidth(m, “列名”.getBytes().length*2*256);
        }
        row.setHeight((short) (10 * 50));
        //字体
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");

        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);//水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setBorderBottom(BorderStyle.THIN); //下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);
        XSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("客户编码");
        cell0.setCellStyle(style);
        XSSFCell cell1 = row.createCell(1);
        cell1.setCellValue("客户名称");
        cell1.setCellStyle(style);
        XSSFCell cell2 = row.createCell(2);
        cell2.setCellValue("收款金额");
        cell2.setCellStyle(style);
        XSSFCell cell3 = row.createCell(3);
        cell3.setCellValue("备注");
        cell3.setCellStyle(style);

        List<FdRemittanceRecord> fdRemittanceRecords = fdRemittanceRecordService.selectRemittanceRecordList(param);
        if (fdRemittanceRecords.size() > 0) {
            int i = 1;
            for (FdRemittanceRecord info : fdRemittanceRecords) {
                XSSFRow hssfRow = sheet.createRow(i);

                hssfRow.createCell(0).setCellValue(info.getCustomerNo());

                hssfRow.createCell(1).setCellValue(info.getCustomerName());
                i++;
            }
        }
        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String("汇款记录.xlsx".getBytes("GB2312"), "8859_1"));
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }

    /**
     * 获取月份的第一天
     *
     * @param year
     * @param month
     * @return
     */
    public static String getFirstDayOfMonth1(int year, int month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最小天数
        int firstDay = cal.getMinimum(Calendar.DATE);
        //设置日历中月份的最小天数
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(cal.getTime());
    }

    /**
     * 获取月份的最后一天
     *
     * @param year
     * @param month
     * @return
     */
    public static String getLastDayOfMonth1(int year, int month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DATE);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(cal.getTime());
    }

    /**
     * 导入
     */
    @PostMapping("/importFile")
    @ResponseBody
    public R importBasicData(@RequestParam MultipartFile file, FdRemittanceRecord fdRemittanceRecord) throws Exception {
        R r = new R();
        try {
            String originalFilename = file.getOriginalFilename();
            InputStream inputStream = file.getInputStream();
            // 调用工具类中方法，读取excel文件中数据
            long i = System.currentTimeMillis();
            r = readExcel(originalFilename, inputStream, fdRemittanceRecord);
            inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
            return new R<>(new Throwable(e));
        }

        return r;
    }

    /**
     * 导入
     */
    @PostMapping("/importFileSheng")
    @ResponseBody
    public R importFileSheng(@RequestParam MultipartFile file, FdRemittanceRecord fdRemittanceRecord) throws Exception {
//        List<BookingRequesdetailDTO> chainGroupOrganizations=new ArrayList<>();
        R r = new R();
        try {
            String originalFilename = file.getOriginalFilename();
            InputStream inputStream = file.getInputStream();
            // 调用工具类中方法，读取excel文件中数据
            long i = System.currentTimeMillis();
            r = readExcelSheng(originalFilename, inputStream, fdRemittanceRecord);
            inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
            return new R<>(new Throwable(e));
        }
        /*for (BookingRequesdetailDTO c:chainGroupOrganizations) {
            System.out.println(c);
        }*/
        return r;
    }

    /*
     *
     * 转为预收款
     * */
    @PostMapping("/convertToAdvance")
    public R convertToAdvance(@RequestBody FdRemittanceRecord fdRemittanceRecord) {
        fdRemittanceRecordService.convertToAdvance(fdRemittanceRecord);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 读取excel文件数据
     *
     * @param fileName
     * @param inputStream
     * @return
     * @throws Exception
     */
    public R readExcelSheng(String fileName, InputStream inputStream, FdRemittanceRecord fdRemittanceRecords) {


        ExcelReader reader = ExcelUtil.getReader(inputStream);

        // 读取所有数据到List<Map<String, Object>>对象
        List<Map<String, Object>> data = reader.readAll();
        if (data.isEmpty()) {
            return R.error("文件为空");
        }

        List<FdRemittanceRecord> list = new ArrayList<>();
        // 打印读取的数据
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        for (Map<String, Object> row : data) {
            FdRemittanceRecord fdRemittanceRecord = new FdRemittanceRecord();
            fdRemittanceRecord.setAddWho(userInfo.getUserName());//添加人编码
            fdRemittanceRecord.setAddWhoName(userInfo.getRealName());//添加人名称
            fdRemittanceRecord.setAddTime(LocalDateTime.now());//添加时间

            EntityWrapper<CustomerInfo> wrapper = new EntityWrapper<>();
            wrapper.eq("customer_code", fdRemittanceRecords.getPlatformCode());
            wrapper.eq("delete_flag", "N");
            CustomerInfo customerInfo = customerInfoService.selectOne(wrapper);
            fdRemittanceRecord.setPlatformCode(customerInfo.getCustomerCode());//平台编码
            fdRemittanceRecord.setPlatformName(customerInfo.getCompanyName());//平台名称

            //判断 平台编标识（0：市平台，1：省平台）'
            if ("1".equals(customerInfo.getCustomerFlag())) {
                fdRemittanceRecord.setPlatformFlag("1");
                fdRemittanceRecord.setCustomerFlag("0");
            } else if ("2".equals(customerInfo.getCustomerFlag())) {
                fdRemittanceRecord.setPlatformFlag("2");
                fdRemittanceRecord.setCustomerFlag("1");
            }

            String tsIn = noConfigService.genNo("RE");
            if (tsIn.contains("请联系系统管理员")) {
                return R.error("无法生成收款单号！");
            }
            fdRemittanceRecord.setRemittanceRecordCode(tsIn);//收款记录编码


            if (StrUtil.isNotBlank(row.get("收款金额(必填)").toString())) {
                fdRemittanceRecord.setRemittanceAmount(BigDecimal.valueOf(Double.parseDouble(row.get("收款金额(必填)").toString())));
                fdRemittanceRecord.setResidualAmount(fdRemittanceRecord.getRemittanceAmount());
            } else {
                return R.error("收款金额不能为空！");
            }
            if (StrUtil.isNotBlank(row.get("客户编码").toString())) {
                fdRemittanceRecord.setCustomerNo(row.get("客户编码").toString());
            } else {
                return R.error("客户编码不能为空！");
            }

            if (StrUtil.isNotBlank(row.get("客户名称").toString())) {
                fdRemittanceRecord.setCustomerName(row.get("客户名称").toString());
            } else {
                return R.error("客户名称不能为空！");
            }

            if (StrUtil.isNotBlank(row.get("收款时间(必填)").toString())) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                fdRemittanceRecord.setCollectionTime(LocalDate.parse(row.get("收款时间(必填)").toString(), formatter));
            } else {
                return R.error("收款时间不能为空！");
            }
            if (StrUtil.isNotBlank(row.get("备注(必填)").toString())) {
                fdRemittanceRecord.setRemark(row.get("备注(必填)").toString());
            } else {
                return R.error("备注不能为空！");
            }
            if (StrUtil.isNotBlank(row.get("收款方式(必填)").toString())) {
                fdRemittanceRecord.setRemittanceName(row.get("收款方式(必填)").toString());
                SysDict sysDict = new SysDict();
                sysDict.setDictType("PAYMENT");
                sysDict.setDictValue(fdRemittanceRecord.getRemittanceName());
                SysDict dict = sysDictService.selectSysDictByDictValue(sysDict);
                fdRemittanceRecord.setRemittanceType(dict.getDictCode());
            } else {
                return R.error("收款方式不能为空！");
            }
            list.add(fdRemittanceRecord);

        }

        fdRemittanceRecordService.insertFdRemittanceRecordList(list);
        return R.success();
    }

    public boolean isRowEmpty(Row row) {
        if (row != null) {
            for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
                Cell cell = row.getCell(c);
                if (cell != null) {
                    cell.setCellType(CellType.STRING);
                }
                if (cell != null && !"".equals(cell.getStringCellValue())) {
                    return false;//不是空行
                }
            }
        }
        return true;
    }

    //获取准确的文件行数
    public Sheet getAccuracyContextNum(Workbook workbook) {
        // 取第一个sheet
        Sheet sheet = workbook.getSheetAt(0);
        // 删除空行
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            // 删除空行
            if (this.isRowEmpty(row)) {
                int lastRowNum = sheet.getLastRowNum();
                if (i >= 0 && i < lastRowNum) {
                    sheet.shiftRows(i + 1, lastRowNum, -1);// 将行号为i+1一直到行号为lastRowNum的单元格全部上移一行，以便删除i行
                }
                if (i == lastRowNum) {
                    if (row != null) {
                        sheet.removeRow(row);
                    }
                }
                i--;
            }
        }
        return sheet;
    }

    /**
     * 读取excel文件数据
     *
     * @param fileName
     * @param inputStream
     * @return
     * @throws Exception
     */
    public R readExcel(String fileName, InputStream inputStream, FdRemittanceRecord fdRemittanceRecords) throws Exception {
        R r = new R();
        boolean ret = isXls(fileName);
        Workbook workbook = null;
        // 根据后缀创建不同的对象
        if (ret) {
            workbook = new HSSFWorkbook(inputStream);
        } else {
            workbook = new XSSFWorkbook(inputStream);
        }
        Sheet sheet = getAccuracyContextNum(workbook);

        List<FdRemittanceRecord> list = new ArrayList<>();
        int lastRowNum = sheet.getLastRowNum();
        if (lastRowNum > 0) {
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            for (int i = 1; i <= lastRowNum; i++) {
                FdRemittanceRecord fdRemittanceRecord = new FdRemittanceRecord();
//                    fdRemittanceRecord.setId(randomUUID());
                fdRemittanceRecord.setAddWho(userInfo.getUserName());//添加人编码
                fdRemittanceRecord.setAddWhoName(userInfo.getRealName());//添加人名称
                fdRemittanceRecord.setAddTime(LocalDateTime.now());//添加世家
                Row row = sheet.getRow(i);

                EntityWrapper<CustomerInfo> wrapper = new EntityWrapper<>();
                wrapper.eq("customer_code", fdRemittanceRecords.getPlatformCode());
                CustomerInfo customerInfo = customerInfoService.selectOne(wrapper);
                fdRemittanceRecord.setPlatformCode(customerInfo.getCustomerCode());//平台编码
                fdRemittanceRecord.setPlatformName(customerInfo.getCompanyName());//平台名称

                //判断 平台编标识（0：市平台，1：省平台）'
                if ("1".equals(customerInfo.getCustomerFlag())) {
                    fdRemittanceRecord.setPlatformFlag("0");
                } else if ("2".equals(customerInfo.getCustomerFlag())) {
                    fdRemittanceRecord.setPlatformFlag("1");
                }
                String tsIn = noConfigService.genNo("RE");
                if (tsIn.contains("请联系系统管理员")) {
                    r.setMsg(tsIn);
                    r.setStatusCode(500);
                    r.setB(false);
                }
                fdRemittanceRecord.setRemittanceRecordCode(tsIn);//收款记录编码
                if (row.getCell(0) != null) {
                    row.getCell(0).setCellType(CellType.STRING);
                    if (!"".equals(row.getCell(0).getStringCellValue()) && row.getCell(0).getStringCellValue() != null) {
                        fdRemittanceRecord.setCustomerNo(row.getCell(0).getStringCellValue());
                    } else {
                        return new R(Boolean.FALSE, "客户编码为空");
                    }
                } else {
                    return new R(Boolean.FALSE, "客户编码为空");
                }

                if (row.getCell(1) != null) {
                    row.getCell(1).setCellType(CellType.STRING);
                    if (!"".equals(row.getCell(1).getStringCellValue()) && row.getCell(1).getStringCellValue() != null) {
                        fdRemittanceRecord.setCustomerName(row.getCell(1).getStringCellValue());
                    } else {
                        return new R(Boolean.FALSE, "客户名称为空");
                    }
                } else {
                    return new R(Boolean.FALSE, "客户名称为空");
                }

                if (row.getCell(2) != null) {
                    row.getCell(2).setCellType(CellType.NUMERIC);
                    if (!Double.isNaN(row.getCell(2).getNumericCellValue())) {
                        fdRemittanceRecord.setRemittanceAmount(BigDecimal.valueOf(row.getCell(2).getNumericCellValue()));
                        fdRemittanceRecord.setResidualAmount(fdRemittanceRecord.getRemittanceAmount());
                    } else {
                        return new R(Boolean.FALSE, "收款金额为空");
                    }
                } else {
                    return new R(Boolean.FALSE, "收款金额为空");
                }

                if (row.getCell(3) != null) {
                    row.getCell(3).setCellType(CellType.STRING);
                    if (!"".equals(row.getCell(3).getStringCellValue()) && row.getCell(3).getStringCellValue() != null) {
                        fdRemittanceRecord.setRemark(row.getCell(3).getStringCellValue());
                    } else {
                        return new R(Boolean.FALSE, "备注为空");
                    }
                } else {
                    return new R(Boolean.FALSE, "备注为空");
                }
                list.add(fdRemittanceRecord);
            }
            fdRemittanceRecordService.insertFdRemittanceRecordList(list);
        } else {
            return new R(Boolean.FALSE, "表格为空");
        }
        workbook.close();
        r.setData(list);
        r.setMsg("上传成功");
        return r;
    }

    /**
     * 判断导入文件格式
     *
     * @param fileName
     * @return
     */
    public static boolean isXls(String fileName) {
        // (?i)忽略大小写
        if (fileName.matches("^.+\\.(?i)(xls)$")) {
            return true;
        } else if (fileName.matches("^.+\\.(?i)(xlsx)$")) {
            return false;
        } else {
            throw new RuntimeException("格式不对");
        }
    }


    /**
     * 收款记录认领
     *
     * @Param: fdRemittanceRecord
     * @Return:
     * @Author: Howe
     * @Date: 2024/6/17 下午5:46
     **/
    @PostMapping("/claimRemittance")
    public R claimRemittance(@RequestBody FdRemittanceRecord fdRemittanceRecord) {
        return fdRemittanceRecordService.claimRemittance(fdRemittanceRecord);
    }


    /**
     * 认领作废
     *
     * @Param: null
     * @Return:
     * @Author: Howe
     * @Date: 2024/6/22 下午2:05
     **/
    @PostMapping("/claimInvalid")
    public R claimInvalid(@RequestBody FdRemittanceRecord fdRemittanceRecord) {
        return fdRemittanceRecordService.claimInvalid(fdRemittanceRecord);
    }

    /**
     * 作废(省平台付款记录)
     *
     * @Param: null
     * @Return:
     * @Author: Howe
     * @Date: 2024/6/22 下午2:05
     **/
    @PostMapping("/claimInvalidProvince")
    public R claimInvalidProvince(@RequestBody FdRemittanceRecord fdRemittanceRecord) {
        return fdRemittanceRecordService.claimInvalidProvince(fdRemittanceRecord);
    }
}
