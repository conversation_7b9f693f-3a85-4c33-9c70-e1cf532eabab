package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.ContainerTypeCodeDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.feign.RemoteAdminService;
import com.huazheng.tunny.ocean.api.vo.ContainerInfoVO;
import com.huazheng.tunny.ocean.api.vo.SysDictVo;
import com.huazheng.tunny.ocean.controller.WaybillHeaderController;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.*;
import com.huazheng.tunny.ocean.util.NumberToCn;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

@Service("waybillContainerInfoService")
public class WaybillContainerInfoServiceImpl extends ServiceImpl<WaybillContainerInfoMapper, WaybillContainerInfo> implements WaybillContainerInfoService {

    @Autowired
    private WaybillContainerInfoMapper waybillContainerInfoMapper;

    @Autowired
    private SysDictMapper sysDictMapper;

    @Autowired
    private WaybillContainerInfoService waybillContainerInfoService;

    @Autowired
    private WaybillHeaderMapper waybillHeaderMapper;

    @Autowired
    private SysNoConfigService sysNoConfigService;

    @Autowired
    private FdBusCostMapper fdBusCostMapper;

    @Autowired
    private FdBusCostDetailMapper fdBusCostDetailMapper;
    @Autowired
    private CustomerPlatformInfoMapper customerPlatformInfoMapper;

    @Autowired
    private WaybillGoodsInfoMapper waybillGoodsInfoMapper;

    @Autowired
    private WaybillParticipantsMapper waybillParticipantsMapper;

    @Autowired
    private StationManagementMapper stationManagementMapper;

    @Autowired
    private RemoteAdminService remoteAdminService;

    @Autowired
    private ContainerTypeDataMapper containerTypeDataMapper;
    @Autowired
    private PlatformCheckMapper platformCheckMapper;
    @Autowired
    private ShifmanagementMapper shifmanagementMapper;
    @Autowired
    private WaybillParticipantsService waybillParticipantsService;
    @Autowired
    private WaybillGoodsInfoService waybillGoodsInfoService;
    @Autowired
    private FdShippingAccoundetailService fdShippingAccoundetailService;
    @Value("${db.database}")
    private String database;

    /**
     * 查询运单集装箱信息表信息
     *
     * @param rowId 运单集装箱信息表ID
     * @return 运单集装箱信息表信息
     */
    @Override
    public WaybillContainerInfo selectWaybillContainerInfoById(String rowId) {
        return waybillContainerInfoMapper.selectWaybillContainerInfoById(rowId);
    }

    /**
     * 查询运单集装箱信息表列表
     *
     * @param waybillContainerInfo 运单集装箱信息表信息
     * @return 运单集装箱信息表集合
     */
    @Override
    public List<WaybillContainerInfo> selectWaybillContainerInfoList(WaybillContainerInfo waybillContainerInfo) {
        return waybillContainerInfoMapper.selectWaybillContainerInfoList(waybillContainerInfo);
    }

    @Override
    public List<WaybillContainerInfo> selectWaybillContainerInfoList2(WaybillContainerInfo waybillContainerInfo) {
        return waybillContainerInfoMapper.selectWaybillContainerInfoList2(waybillContainerInfo);
    }


    /**
     * 分页模糊查询运单集装箱信息表列表
     *
     * @return 运单集装箱信息表集合
     */
    @Override
    public Page selectWaybillContainerInfoListByLike(Query query) {
        WaybillContainerInfo waybillContainerInfo = BeanUtil.mapToBean(query.getCondition(), WaybillContainerInfo.class, false);
        query.setRecords(waybillContainerInfoMapper.selectWaybillContainerInfoListByLike(query, waybillContainerInfo));
        return query;
    }

    @Override
    public Page selectWaybillContainerInfoAndBasChangeboxDetail(Query query) {
        WaybillContainerInfo waybillContainerInfo = BeanUtil.mapToBean(query.getCondition(), WaybillContainerInfo.class, false);
        query.setRecords(waybillContainerInfoMapper.selectWaybillContainerInfoAndBasChangeboxDetail(query, waybillContainerInfo));
        return query;
    }

    @Override
    public String selectBasChangeboxDetailForSh(String data) {
        WaybillContainerInfo info = JSONUtil.toBean(data, WaybillContainerInfo.class);
        if (info != null) {
            if (StrUtil.isEmpty(info.getWaybillNo())) {
                return JSONUtil.parseObj(new R<>(Boolean.FALSE, "未接收到订单号！"), false).toStringPretty();
            }
            if (StrUtil.isEmpty(info.getCustomerNo())) {
                return JSONUtil.parseObj(new R<>(Boolean.FALSE, "未接收到客户编码！"), false).toStringPretty();
            }
        } else {
            return JSONUtil.parseObj(new R<>(Boolean.FALSE, "未接收到请求参数！"), false).toStringPretty();
        }
        List<WaybillContainerInfo> waybillContainerInfos = waybillContainerInfoMapper.selectBasChangeboxDetailForSh(info);
        return JSONUtil.parseObj(new R<>(Boolean.TRUE, waybillContainerInfos), true).toStringPretty();
    }

    /**
     * 当前运单下集装箱、货物、参与方分页信息
     */
    @Override
    public Page selectConGoodsPantsPage(Query query) {
        WaybillContainerInfo waybillContainerInfo = BeanUtil.mapToBean(query.getCondition(), WaybillContainerInfo.class, false);
        long startTime = System.currentTimeMillis();   //获取开始时间
        List<WaybillContainerInfo> list = waybillContainerInfoMapper.selectConGoodsPantsPage(waybillContainerInfo);
        long endTime = System.currentTimeMillis(); //获取结束时间
        System.out.println("程序运行时间： " + (endTime - startTime) + "ms");
        Integer count = list.size();
        query.setTotal(count);
        query.setRecords(list);
        return query;
    }

    @Override
    public Page selectConGoodsPantsPageSh(Query query) {
        WaybillContainerInfo waybillContainerInfo = BeanUtil.mapToBean(query.getCondition(), WaybillContainerInfo.class, false);
        long startTime = System.currentTimeMillis();   //获取开始时间
        List<WaybillContainerInfo> list = waybillContainerInfoMapper.selectConGoodsPantsPageSh(waybillContainerInfo);
        long endTime = System.currentTimeMillis(); //获取结束时间
        System.out.println("程序运行时间： " + (endTime - startTime) + "ms");
        Integer count = list.size();
        query.setTotal(count);
        query.setRecords(list);
        return query;
    }

    /*
     *当前班次下集装箱详细信息分页
     */
    @Override
    public Page selectConInfoPage(Query query) {
        WaybillContainerInfo waybillContainerInfo = BeanUtil.mapToBean(query.getCondition(), WaybillContainerInfo.class, false);
        /*Integer count = waybillContainerInfoMapper.selectConInfoCount(waybillContainerInfo);
        query.setTotal(count);
        if (count!=null&&count != 0) {
            query.setRecords(waybillContainerInfoMapper.selectConInfoPage(query,waybillContainerInfo));
        }
        return query;*/
        return query.setRecords(waybillContainerInfoMapper.selectConInfoPage(query, waybillContainerInfo));
    }

    /**
     * 新增运单集装箱信息表
     *
     * @param waybillContainerInfo 运单集装箱信息表信息
     * @return 结果
     */
    @Override
    public int insertWaybillContainerInfo(WaybillContainerInfo waybillContainerInfo) {
        return waybillContainerInfoMapper.insertWaybillContainerInfo(waybillContainerInfo);
    }

    /**
     * 修改运单集装箱信息表
     *
     * @param list 运单集装箱信息表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateContainerInfoBatch(List<WaybillContainerInfo> list) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        for (WaybillContainerInfo info : list) {
            String code = info.getResveredField09();
            if (code != null && !code.equals("")) {
                List<SysDict> list1 = sysDictMapper.selectSysDictListByCode(98, code, database);
                if (list1 != null && list1.size() > 0) {
                    info.setPortAgent(list1.get(0).getDictValue());
                }
            }
            info.setUpdateWho(usercode);
            info.setUpdateWhoName(username);
            info.setUpdateTime(new Date());
            waybillContainerInfoMapper.updateContainerInfo3(info);
        }
//        return waybillContainerInfoMapper.updateContainerInfoBatch(list);
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertOrUpdateContainerInfo(List<WaybillContainerInfo> list, List<String> waybillCodes) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        if (CollUtil.isNotEmpty(waybillCodes)) {
            for (String waybillNo : waybillCodes
            ) {
                if (CollUtil.isNotEmpty(list)) {
                    for (WaybillContainerInfo info : list) {
                        String code = info.getResveredField09();
                        if (code != null && !code.equals("")) {
                            List<SysDict> list1 = sysDictMapper.selectSysDictListByCode(98, code, database);
                            if (list1 != null && list1.size() > 0) {
                                info.setPortAgent(list1.get(0).getDictValue());
                            }
                        }
                        info.setWaybillNo(waybillNo);
                        info.setUpdateWho(usercode);
                        info.setUpdateWhoName(username);
                        info.setUpdateTime(new Date());
                        waybillContainerInfoMapper.updateContainerInfo3(info);
                    }
                }
            }
        }
    }

    @Override
    public int updateContainerInfoBatchs(List<WaybillContainerInfo> list) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        for (WaybillContainerInfo waybillContainerInfo : list) {
            waybillContainerInfo.setUpdateWho(userInfo.getUserName());
            waybillContainerInfo.setUpdateWhoName(userInfo.getRealName());
            waybillContainerInfo.setUpdateTime(new Date());
            waybillContainerInfoMapper.updateContainerInfo3(waybillContainerInfo);
        }
        return 1;
    }

    @Override
    public int updateContainerInfo(WaybillContainerInfo waybillContainerInfo) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        waybillContainerInfo.setUpdateWho(usercode);
        waybillContainerInfo.setUpdateWhoName(username);
        waybillContainerInfo.setUpdateTime(new Date());
        return waybillContainerInfoMapper.updateContainerInfo(waybillContainerInfo);
    }

    @Override
    public int updateContainerInfo1(WaybillContainerInfo waybillContainerInfo) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        waybillContainerInfo.setDeleteWho(userInfo.getUserName());
        waybillContainerInfo.setDeleteWhoName(userInfo.getRealName());
        waybillContainerInfo.setDeleteTime(new Date());
        return waybillContainerInfoMapper.updateContainerInfo1(waybillContainerInfo);
    }


    /**
     * 删除运单集装箱信息表
     *
     * @param waybillContainerInfo 运单集装箱信息表ID
     * @return 结果
     */
    @Override
    public int deleteWaybillContainerInfoById(WaybillContainerInfo waybillContainerInfo) {
        return waybillContainerInfoMapper.deleteWaybillContainerInfoById(waybillContainerInfo);
    }

    /**
     * 批量删除运单集装箱信息表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteWaybillContainerInfoByIds(Integer[] rowIds) {
        return waybillContainerInfoMapper.deleteWaybillContainerInfoByIds(rowIds);
    }


    @Override
    public BigDecimal getTotalAmount(String waybillNo) {
        return waybillContainerInfoMapper.getTotalAmount(waybillNo);
    }

    @Override
    public BigDecimal getTotalAmountList(List<String> stringList) {
        return waybillContainerInfoMapper.getTotalAmountList(stringList);
    }

    @Override
    public Map<String, Object> customerBill(Query query) {
        Map<String, Object> map = new HashMap<>();
        WaybillContainerInfo waybillContainerInfo = BeanUtil.mapToBean(query.getCondition(), WaybillContainerInfo.class, false);
        List<ContainerInfoVO> containerInfoVOS = waybillContainerInfoMapper.customerBill(query, waybillContainerInfo);
        BigDecimal decimal = waybillContainerInfoMapper.customerBillCapital(waybillContainerInfo);
        map.put("capitalNum", decimal);
        map.put("capital", NumberToCn.numberToCn(decimal));
        map.put("containerInfoVOS", query.setRecords(containerInfoVOS));
        return map;
    }

    @Override
    public List<ContainerInfoVO> customerBill2(WaybillContainerInfo waybillContainerInfo) {
        return waybillContainerInfoMapper.customerBill(waybillContainerInfo);
    }

    @Override
    public BigDecimal customerBillCapital(WaybillContainerInfo waybillContainerInfo) {
        return waybillContainerInfoMapper.customerBillCapital(waybillContainerInfo);
    }

    @Override
    public List<Map<String, String>> selectNumByCountry(WaybillContainerInfo info) {
        return waybillContainerInfoMapper.selectNumByCountry(info);
    }

    @Override
    public List<Map<String, String>> selectNumFromCountry(WaybillContainerInfo info) {
        return waybillContainerInfoMapper.selectNumFromCountry(info);
    }

    @Override
    public Integer selectConInfoByCount(WaybillContainerInfo info) {
        return waybillContainerInfoMapper.selectConInfoByCount(info);
    }

    @Override
    public List<WaybillContainerInfo> selectConInfoByContainerType(WaybillContainerInfo infos) {
        return waybillContainerInfoMapper.selectConInfoByContainerType(infos);
    }

    @Override
    public List<WaybillContainerInfo> selectWaybillContainerInfoLists(WaybillContainerInfo infos) {
        return waybillContainerInfoMapper.selectWaybillContainerInfoLists(infos);
    }

    @Override
    public int updateWaybillContainerInfos(List<WaybillContainerInfo> waybillContainerInfo) {
        return waybillContainerInfoMapper.updateWaybillContainerInfos(waybillContainerInfo);
    }

    @Override
    public WaybillContainerInfo checkWaybillContainerInfo(WaybillContainerInfo info) {
        return waybillContainerInfoMapper.checkWaybillContainerInfo(info);
    }

    @Override
    public List<WaybillContainerInfo> selectWaybillInfo(WaybillContainerInfo info) {
        return waybillContainerInfoMapper.selectWaybillInfo(info);
    }

    @Override
    public String getNum(String shiftNo) {
        return waybillContainerInfoMapper.getNum(shiftNo);
    }

    @Override
    public Map<String, Object> getContainerTypeNum(WaybillContainerInfo waybillContainerInfo) {
        Map<String, Object> map = new HashMap<>();
        waybillContainerInfo.setDeleteFlag("N");
        List<ContainerTypeCodeDTO> dtos = waybillContainerInfoMapper.getContainerTypeNum(waybillContainerInfo);
        if (CollUtil.isNotEmpty(dtos)) {
            for (ContainerTypeCodeDTO dto : dtos
            ) {
                map.put(dto.getContainerTypeCode(), dto.getNum());
            }
        }
        return map;
    }

    @Override
    public R feeImported2(MultipartFile file, String waybillNo) {
        WaybillHeader wh = new WaybillHeader();
        wh.setWaybillNo(waybillNo);
        wh.setDeleteFlag("N");
        wh.setBillStatus("1");
        List<WaybillHeader> whList = waybillHeaderMapper.selectWaybillHeaderList(wh);
        BigDecimal rate = null;
        if (whList != null && whList.size() != 0) {
            rate = whList.get(0).getExchangeRate();
        } else {
            return new R(500, Boolean.FALSE, "当前运单状态非待支付不能进行费用录入操作");
        }
        Workbook workbook = null;
        try {
            InputStream is = file.getInputStream();
            //利用WorkbookFactory获取Sheet，无需判断版本
            workbook = WorkbookFactory.create(is);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            Sheet sheet2rd = getAccuracyContextNum(workbook);
            //获取行数
            int lastRowNum2rd = sheet2rd.getLastRowNum();
            //最后一行序号
            Cell serialNo = sheet2rd.getRow(lastRowNum2rd).getCell(0);
            //最后一行箱号
            Cell containerNo1 = sheet2rd.getRow(lastRowNum2rd).getCell(1);
            /*最后一行为空白行的话，最后一行下标减一*/
            if (serialNo == null && containerNo1 == null && lastRowNum2rd > 3) {
                //第一行数据下标为3，4为有两条数据
                lastRowNum2rd = lastRowNum2rd - 1;
            }

            List<FdBusCostDetail> detailList = new ArrayList<>();
            //循环校验标识
            for (int i = 1; i <= lastRowNum2rd; i++) {
                FdBusCostDetail detail = new FdBusCostDetail();
                Row row = sheet2rd.getRow(i);
                /********判断空行跳过解析*******/
                Cell cell = row.getCell(0);
                if (cell != null) {
                    cell.setCellType(CellType.STRING);
                }
                boolean blankFlag = false;
                if (cell != null && !cell.getStringCellValue().equals("")) {
                    blankFlag = false;
                } else {
                    blankFlag = true;
                }
                if (blankFlag == true) {
                    continue;
                }
                /********判断空行跳过解析 end*******/
                //箱号,以箱号为准
                String containerNo2 = row.getCell(0).getStringCellValue();
                boolean flag3 = WaybillHeaderController.verifyCntrCode(containerNo2);
                if (!flag3) {
                    return new R(500, Boolean.FALSE, "第" + (i + 1) + "行箱号填写有误，如有疑问请联系管理员");
                }
                containerNo2 = containerNo2.trim();
                detail.setContainerNumber(containerNo2);
                readExcel(row, detail);
                detailList.add(detail);
            }
            //更新订单详情
            updateContainerInfoBatch(waybillNo, detailList, rate);
            //插入业务费用信息
            insertOrUpdateBusCost(whList, detailList);

        } catch (Exception e) {
            System.out.println(e.getMessage() + "##################导入excel失败########################");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new R(500, Boolean.FALSE, "导入excel页失败，确认无误后，请联系管理员");
        }
        return new R<>(200, Boolean.TRUE, "导入完成");
    }

    /**
     * 读取Excel数据
     *
     * @Param: row, detail
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/5/27 0027 11:17
     **/
    public void readExcel(Row row, FdBusCostDetail detail) {
        //箱号
        if (row.getCell(4) != null) {
            row.getCell(4).setCellType(CellType.STRING);
        }
        if (row.getCell(5) != null) {
            row.getCell(5).setCellType(CellType.STRING);
        }
        if (row.getCell(6) != null) {
            row.getCell(6).setCellType(CellType.STRING);
        }
        if (row.getCell(7) != null) {
            row.getCell(7).setCellType(CellType.STRING);
        }
        if (row.getCell(8) != null) {
            row.getCell(8).setCellType(CellType.STRING);
        }
        //费用科目
        if (row.getCell(4) != null && !row.getCell(4).getStringCellValue().equals("")) {
            detail.setCodeSsCategoriesName(row.getCell(4).getStringCellValue());
            //大类小类
        }
        /*//应收金额
        if (row.getCell(5) != null && !row.getCell(5).getStringCellValue().equals("")) {
            detail.setReceivableAmount(BigDecimal.valueOf(Double.valueOf(row.getCell(5).getStringCellValue())));
        }
        //应收单位
        if (row.getCell(6) != null && !row.getCell(6).getStringCellValue().equals("")) {
            detail.setReceivableUnit(row.getCell(6).getStringCellValue());
        }
        //应付金额
        if (row.getCell(7) != null && !row.getCell(7).getStringCellValue().equals("")) {
            detail.setPayableAmount(BigDecimal.valueOf(Double.valueOf(row.getCell(7).getStringCellValue())));
        }
        //应付单位
        if (row.getCell(8) != null && !row.getCell(8).getStringCellValue().equals("")) {
            detail.setPayableUnit(row.getCell(8).getStringCellValue());
        }*/
    }

    /**
     * 更新订单详情
     *
     * @Param: waybillNo, detailList, rate
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/5/27 0027 11:13
     **/
    public void updateContainerInfoBatch(String waybillNo, List<FdBusCostDetail> detailList, BigDecimal rate) {
        //获取费用中所有箱号
        List<WaybillContainerInfo> listContInfo = new ArrayList<>();
        if (CollUtil.isNotEmpty(detailList)) {
            for (FdBusCostDetail detail : detailList
            ) {
                Boolean has = false;
                if (CollUtil.isNotEmpty(listContInfo)) {
                    for (WaybillContainerInfo waybillContainerInfo : listContInfo
                    ) {
                        if (waybillContainerInfo.getContainerNo().equals(detail.getContainerNumber())) {
                            has = true;
                            break;
                        }
                    }
                }
                if (has) {
                    continue;
                }
                WaybillContainerInfo contInfo = new WaybillContainerInfo();
                contInfo.setWaybillNo(waybillNo);
                contInfo.setContainerNo(detail.getContainerNumber());
                listContInfo.add(contInfo);
            }
        }

        if (CollUtil.isNotEmpty(listContInfo)) {
            for (WaybillContainerInfo contInfo : listContInfo
            ) {
                if (CollUtil.isNotEmpty(detailList)) {
                    for (FdBusCostDetail detail : detailList
                    ) {
                        if (contInfo.getContainerNo().equals(detail.getContainerNumber())) {
                            /*if ("境内费用".equals(detail.getCodeSsCategoriesName())) {
                                //境内运费
                                contInfo.setDomesticFreight(detail.getReceivableAmount());
                            } else if ("境外费用".equals(detail.getCodeSsCategoriesName())) {
                                //境外运费(原币)
                                contInfo.setOverseasFreight(detail.getReceivableAmount());
                                if (rate != null) {
                                    contInfo.setOverseasFreightRmb(rate.multiply(contInfo.getOverseasFreight()).setScale(2, BigDecimal.ROUND_HALF_UP));
                                    contInfo.setResveredField01(rate);
                                }
                            }*/
                        }
                    }
                }
            }
        }
        waybillContainerInfoService.updateContainerInfoBatch(listContInfo);
    }

    /**
     * 更新业务费用
     *
     * @Param: whList, detailList
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/5/27 11:10
     **/
    public void insertOrUpdateBusCost(List<WaybillHeader> whList, List<FdBusCostDetail> detailList) {
        FdBusCost fdBusCost = new FdBusCost();
        fdBusCost.setPlatformCode(whList.get(0).getPlatformCode());
        fdBusCost.setCustomerCode(whList.get(0).getCustomerNo());
        fdBusCost.setDeleteFlag("N");
        List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostListByLike(fdBusCost);

        CustomerPlatformInfo sel = new CustomerPlatformInfo();
        sel.setPlatformCode(whList.get(0).getPlatformCode());
        sel.setCustomerCode(whList.get(0).getCustomerNo());
        sel.setDeleteFlag("N");
        List<CustomerPlatformInfo> list = customerPlatformInfoMapper.selectCustomerPlatformInfoList(sel);
        //是否存在业务费用
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        if (CollUtil.isNotEmpty(fdBusCosts)) {
            fdBusCost.setId(fdBusCosts.get(0).getId());
            fdBusCost.setCostCode(fdBusCosts.get(0).getCostCode());
            fdBusCost.setPlatformName(whList.get(0).getPlatformName());
            fdBusCost.setPlatformLevel("0");
            fdBusCost.setCustomerName(whList.get(0).getCustomerName());
            fdBusCost.setShiftNo(whList.get(0).getShiftNo());
            fdBusCost.setUpdateWho(userInfo.getUserName());
            fdBusCost.setUpdateWhoName(userInfo.getRealName());
            fdBusCost.setUpdateTime(LocalDateTime.now());

            fdBusCost.setPrincipalName(whList.get(0).getCustomerName());
            if (CollUtil.isNotEmpty(list)) {
                fdBusCost.setContactsName(list.get(0).getContactPerson());
                fdBusCost.setContactsPhone(list.get(0).getContactNo());
            } else {
                sel.setPlatformCode(null);
                List<CustomerPlatformInfo> list2 = customerPlatformInfoMapper.selectCustomerPlatformInfoList(sel);
                if (CollUtil.isNotEmpty(list2)) {
                    fdBusCost.setContactsName(list2.get(0).getContactPerson());
                    fdBusCost.setContactsPhone(list2.get(0).getContactNo());
                }
            }

            fdBusCostMapper.updateFdBusCost(fdBusCost);
            if (CollUtil.isNotEmpty(detailList)) {
                fdBusCostDetailMapper.deleteFdBusCostDetailByCode(fdBusCosts.get(0).getCostCode());
                //新增费用明细
                for (FdBusCostDetail detail : detailList
                ) {
                    detail.setCostCode(fdBusCosts.get(0).getCostCode());
                    detail.setExchangeRateNew(detail.getExchangeRate());
                    detail.setAddWho(userInfo.getUserName());
                    detail.setAddWhoName(userInfo.getRealName());
                    detail.setAddTime(LocalDateTime.now());
                    fdBusCostDetailMapper.insertFdBusCostDetail(detail);
                }
            }
        } else {
            String costCode = sysNoConfigService.genNo("FDC");
            fdBusCost.setCostCode(costCode);
            fdBusCost.setPlatformName(whList.get(0).getPlatformName());
            fdBusCost.setPlatformLevel("0");
            fdBusCost.setCustomerName(whList.get(0).getCustomerName());
            fdBusCost.setShiftNo(whList.get(0).getShiftNo());
            fdBusCost.setAddWho(userInfo.getUserName());
            fdBusCost.setAddWhoName(userInfo.getRealName());
            fdBusCost.setAddTime(LocalDateTime.now());

            fdBusCost.setPrincipalName(whList.get(0).getCustomerName());
            if (CollUtil.isNotEmpty(list)) {
                fdBusCost.setContactsName(list.get(0).getContactPerson());
                fdBusCost.setContactsPhone(list.get(0).getContactNo());
            }

            fdBusCostMapper.insertFdBusCost(fdBusCost);
            if (CollUtil.isNotEmpty(detailList)) {
                for (FdBusCostDetail detail : detailList
                ) {
                    detail.setCostCode(costCode);
                    detail.setExchangeRateNew(detail.getExchangeRate());
                    detail.setAddWho(userInfo.getUserName());
                    detail.setAddWhoName(userInfo.getRealName());
                    detail.setAddTime(LocalDateTime.now());
                    fdBusCostDetailMapper.insertFdBusCostDetail(detail);
                }
            }
        }
    }

    /**
     * 获取准确的文件行数
     *
     * @Param: workbook
     * @Return: org.apache.poi.ss.usermodel.Sheet
     * @Author: zhaohr
     * @Date: 2024/5/27 0027 11:21
     **/
    public Sheet getAccuracyContextNum(Workbook workbook) {
        // 取第一个sheet
        Sheet sheet = workbook.getSheetAt(0);
        // 删除空行
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            // 删除空行
            if (this.isRowEmpty(row)) {
                int lastRowNum = sheet.getLastRowNum();
                if (i >= 0 && i < lastRowNum) {
                    // 将行号为i+1一直到行号为lastRowNum的单元格全部上移一行，以便删除i行
                    sheet.shiftRows(i + 1, lastRowNum, -1);
                }
                if (i == lastRowNum) {
                    if (row != null) {
                        sheet.removeRow(row);
                    }
                }
                i--;
            }
        }
        return sheet;
    }

    public boolean isRowEmpty(Row row) {
        if (row != null) {
            for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
                Cell cell = row.getCell(c);
                if (cell != null) {
                    cell.setCellType(CellType.STRING);
                }
                if (cell != null && !cell.getStringCellValue().equals("")) {
                    //不是空行
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R importFileTzTemplate(MultipartFile file, String rowId) {
        R r = new R();
        try {
            String originalFilename = file.getOriginalFilename();
            InputStream inputStream = file.getInputStream();
            // 调用工具类中方法，读取excel文件中数据
            long i = System.currentTimeMillis();
            r = readExcel2(originalFilename, inputStream, rowId);
            inputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
            return new R<>(new Throwable(e));
        }
        /*for (BookingRequesdetailDTO c:chainGroupOrganizations) {
            System.out.println(c);
        }*/
        return r;
    }

    /**
     * 根据订单号，获取所有上级订单号
     *
     * @Param: waybillHeader, waybillCodes
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/11/19 16:53
     **/
    public void getWayBillCodes(WaybillHeader waybillHeader, List<String> waybillCodes) {
        Shifmanagement shifmanagement = new Shifmanagement();
        shifmanagement.setPlatformCode(waybillHeader.getPlatformCode());
        shifmanagement.setShiftId(waybillHeader.getShiftNo());
        List<Shifmanagement> shifmanagementList = shifmanagementMapper.selectShifmanagementList(shifmanagement);
        if (CollUtil.isNotEmpty(shifmanagementList)
                && StrUtil.isNotBlank(shifmanagementList.get(0).getParentId())
                && StrUtil.isNotBlank(shifmanagementList.get(0).getSharePlatformCode())) {
            WaybillHeader wh = new WaybillHeader();
            wh.setShiftNo(waybillHeader.getShiftNo());
            wh.setPlatformCode(shifmanagementList.get(0).getSharePlatformCode());
            wh.setDeleteFlag("N");
            List<WaybillHeader> list = waybillHeaderMapper.selectWaybillHeaderList(wh);
            if (CollUtil.isNotEmpty(list)) {
                waybillCodes.add(list.get(0).getWaybillNo());
                getWayBillCodes(list.get(0), waybillCodes);
            }
        }
    }

    public R readExcel2(String fileName, InputStream inputStream, String rowId) throws Exception {
        R r = new R();
        boolean ret = isXls(fileName);
        Workbook workbook = null;
        // 根据后缀创建不同的对象
        if (ret) {
            workbook = new HSSFWorkbook(inputStream);
        } else {
            workbook = new XSSFWorkbook(inputStream);
        }
        Sheet sheet = getAccuracyContextNum(workbook);
        WaybillHeader waybillHeader = waybillHeaderMapper.selectWaybillHeaderById(rowId);
        if (waybillHeader == null) {
            return R.error("根据主键查询信息为空");
        }
        Shifmanagement selObj = new Shifmanagement();
        selObj.setShiftId(waybillHeader.getShiftNo());
        selObj.setPlatformCode(waybillHeader.getPlatformCode());
        selObj.setDeleteFlag("N");
        List<Shifmanagement> list = shifmanagementMapper.selectShifmanagementList(selObj);
        String identification = null;
        if(CollUtil.isNotEmpty(list) && StrUtil.isNotBlank(list.get(0).getIdentification())){
            identification = list.get(0).getIdentification();
        }

        List<String> waybillCodes = new ArrayList<>();
        waybillCodes.add(waybillHeader.getWaybillNo());
        getWayBillCodes(waybillHeader, waybillCodes);

        WaybillContainerInfo waybill = new WaybillContainerInfo();
        waybill.setWaybillNo(waybillHeader.getWaybillNo());
        waybill.setOrderNo(waybillHeader.getOrderNo());
        waybill.setDeleteFlag("N");
        List<WaybillContainerInfo> waybillContainerInfosWaybills = waybillContainerInfoService.selectWaybillContainerInfoLists(waybill);
        StringBuilder stringBuilder = new StringBuilder();
        for (WaybillContainerInfo waybills : waybillContainerInfosWaybills) {
            stringBuilder.append(waybills.getContainerNo()).append(",");
        }
        int lastRowNum = sheet.getLastRowNum();
        List<WaybillContainerInfo> waybillContainerInfos = new ArrayList<>();
        String str = "";
//        List<WaybillParticipants> waybillParticipantsAdd = new ArrayList<>();
        List<WaybillParticipants> waybillParticipantsUpdate = new ArrayList<>();
        List<WaybillGoodsInfo> waybillGoodsInfos = new ArrayList<>();

        ContainerTypeData sel = new ContainerTypeData();
        sel.setDeleteFlag("N");
        List<ContainerTypeData> containerTypeDataList = containerTypeDataMapper.selectContainerTypeDataList(sel);

        String countryType = remoteAdminService.selectDictByType2("country_type");
        List<SysDictVo> countryList = JSONUtil.toList(JSONUtil.parseArray(countryType), SysDictVo.class);

        List<StationManagement> stationManagements = stationManagementMapper.selectStationManagementList(null);

        String platformCode = platformCheckMapper.getPlatformCode("DDHCBCZL");

        StringBuffer sb = new StringBuffer();
        if (lastRowNum > 0) {
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            for (int i = 3; i <= lastRowNum; i++) {
                WaybillContainerInfo waybillContainerInfo = new WaybillContainerInfo();
                waybillContainerInfo.setOrderNo(waybillHeader.getOrderNo());
                waybillContainerInfo.setWaybillNo(waybillHeader.getWaybillNo());
                //收货人
                WaybillParticipants waybillParticipantsShou = new WaybillParticipants();
                waybillParticipantsShou.setParticipantsType("S");
                waybillParticipantsShou.setWaybillNo(waybillHeader.getWaybillNo());

                //发货人
                WaybillParticipants waybillParticipantsFa = new WaybillParticipants();
                waybillParticipantsFa.setParticipantsType("F");
                waybillParticipantsFa.setWaybillNo(waybillHeader.getWaybillNo());


                WaybillGoodsInfo waybillGoodsInfo = new WaybillGoodsInfo();
                waybillGoodsInfo.setWaybillNo(waybillHeader.getWaybillNo());
                waybillGoodsInfo.setAddWho(userInfo.getUserName());
                waybillGoodsInfo.setAddWhoName(userInfo.getRealName());
                waybillGoodsInfo.setAddTime(LocalDateTime.now());

                Row row = sheet.getRow(i);

                //箱号
                if (row.getCell(11) != null) {
                    row.getCell(11).setCellType(CellType.STRING);
                    if (!"".equals(row.getCell(11).getStringCellValue()) && row.getCell(11).getStringCellValue() != null) {
                        if (stringBuilder.toString().contains(row.getCell(11).getStringCellValue().replaceAll(" ", ""))) {
                            if (str.contains(row.getCell(11).getStringCellValue().replaceAll(" ", ""))) {
                                sb.append("请确认行" + (i + 1) + row.getCell(11).getStringCellValue().replaceAll(" ", "") + "此箱号是否重复");
                            } else {
                                str = str + row.getCell(11).getStringCellValue().replaceAll(" ", "") + ",";
                            }
                            waybillContainerInfo.setContainerNo(row.getCell(11).getStringCellValue().replaceAll(" ", ""));
                            waybillParticipantsShou.setContainerNo(row.getCell(11).getStringCellValue().replaceAll(" ", ""));
                            waybillGoodsInfo.setContainerNo(row.getCell(11).getStringCellValue().replaceAll(" ", ""));
                            waybillParticipantsFa.setContainerNo(row.getCell(11).getStringCellValue().replaceAll(" ", ""));
                        } else {
                            sb.append("请确认行" + (i + 1) + row.getCell(11).getStringCellValue().replaceAll(" ", "") + "此箱号是否存在");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "箱号为空");
                    }
                    //类型
                    if (row.getCell(1) != null) {
                        row.getCell(1).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(1).getStringCellValue()) && row.getCell(1).getStringCellValue() != null) {
                            if (row.getCell(1).getStringCellValue().trim().equals("出口")) {
                                waybillContainerInfo.setIdentification("E");
                            } else if (row.getCell(1).getStringCellValue().trim().equals("进口")) {
                                waybillContainerInfo.setIdentification("I");
                            } else if (row.getCell(1).getStringCellValue().trim().equals("过境")) {
                                waybillContainerInfo.setIdentification("P");
                            }
                            if(StrUtil.isNotBlank(identification) && !identification.equals(waybillContainerInfo.getIdentification())){
                                sb.append("行" + (i + 1) + "箱进出口过境类型与班列类型不符");
                            }
                        }
                        if (StrUtil.isBlank(waybillContainerInfo.getIdentification())) {
                            sb.append("请确认行" + (i + 1) + row.getCell(1).getStringCellValue().replaceAll(" ", "") + "此类型是否存在");
                        }
                    }

                    //收货人
                    if (row.getCell(3) != null) {
                        row.getCell(3).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(3).getStringCellValue()) && row.getCell(3).getStringCellValue() != null) {
                            waybillParticipantsShou.setConsignorName(row.getCell(3).getStringCellValue().trim());
                        }
                    }

                    //发站
                    if (row.getCell(4) != null) {
                        row.getCell(4).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(4).getStringCellValue()) && row.getCell(4).getStringCellValue() != null) {
                            String stationNameF = row.getCell(4).getStringCellValue().trim();
                            if (CollUtil.isNotEmpty(stationManagements)) {
                                for (StationManagement stationManagement : stationManagements
                                ) {
                                    if (stationManagement.getStationName().equals(stationNameF)) {
                                        waybillContainerInfo.setStartStationName(stationNameF);
                                        waybillContainerInfo.setDestinationName(stationNameF);
                                        waybillContainerInfo.setStationCompilation(stationManagement.getStationCode());
                                        break;
                                    }

                                }
                            }
                            if (StrUtil.isBlank(waybillContainerInfo.getStationCompilation())) {
                                sb.append("行" + (i + 1) + "发站不存在：" + stationNameF);
                            }
                        } else {
                            sb.append("行" + (i + 1) + "发站为空");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "发站为空");
                    }

                    //收货人所属国家代码
                    if (row.getCell(5) != null) {
                        row.getCell(5).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(5).getStringCellValue()) && row.getCell(5).getStringCellValue() != null) {
                            String name = row.getCell(5).getStringCellValue().trim();
                            if (CollUtil.isNotEmpty(countryList)) {
                                for (SysDictVo s : countryList
                                ) {
                                    if (s.getName().equals(name)) {
                                        waybillParticipantsShou.setCountryCode(s.getCode());
                                        waybillContainerInfo.setDestinationCountryCode(s.getCode());
                                        waybillContainerInfo.setDestinationCountryName(s.getName());
                                    }
                                }
                            }
                            if (StrUtil.isBlank(waybillParticipantsShou.getCountryCode())) {
                                sb.append("行" + (i + 1) + "目的国不存在:" + name);
                            }
                        } else {
                            sb.append("行" + (i + 1) + "目的国为空");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "目的国为空");
                    }

                    //到站
                    if (row.getCell(6) != null) {
                        row.getCell(6).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(6).getStringCellValue()) && row.getCell(6).getStringCellValue() != null) {
                            String stationNameD = row.getCell(6).getStringCellValue().trim();
                            if (CollUtil.isNotEmpty(stationManagements)) {
                                for (StationManagement stationManagement : stationManagements
                                ) {
                                    if (stationManagement.getStationName().equals(stationNameD)) {
                                        waybillContainerInfo.setEndStationName(stationNameD);
                                        waybillContainerInfo.setDestination(stationNameD);
                                        waybillContainerInfo.setEndCompilation(stationManagement.getStationCode());
                                        break;
                                    }

                                }
                            }
                            if (StrUtil.isBlank(waybillContainerInfo.getEndCompilation())) {
                                sb.append("行" + (i + 1) + "到站不存在：" + stationNameD);
                            }
                        } else {
                            sb.append("行" + (i + 1) + "到站为空");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "到站为空");
                    }

                    //口岸代理
                    if (row.getCell(7) != null) {
                        row.getCell(7).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(7).getStringCellValue()) && row.getCell(7).getStringCellValue() != null) {
                            waybillContainerInfo.setPortAgent(row.getCell(7).getStringCellValue().trim());
                        }
                    }

                    //品名
                    if (row.getCell(8) != null) {
                        row.getCell(8).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(8).getStringCellValue()) && row.getCell(8).getStringCellValue() != null) {
                            waybillGoodsInfo.setGoodsChineseName(row.getCell(8).getStringCellValue().trim());
                            waybillContainerInfo.setGoodsName(row.getCell(8).getStringCellValue().trim());
                        }
                    }

                    //箱型
                    if (row.getCell(9) != null) {
                        row.getCell(9).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(9).getStringCellValue()) && row.getCell(9).getStringCellValue() != null) {
                            String containerTypeCode = row.getCell(9).getStringCellValue().replaceAll(" ", "");
                            if (StrUtil.isNotBlank(containerTypeCode)) {
                                if ("20".equals(containerTypeCode) || "40".equals(containerTypeCode) || "45".equals(containerTypeCode)) {
                                    containerTypeCode = containerTypeCode + "GP";
                                }
                                Boolean flag = true;
                                for (ContainerTypeData data : containerTypeDataList
                                ) {
                                    if (data.getContainerTypeCode().equals(containerTypeCode)) {
                                        waybillContainerInfo.setContainerTypeCode(data.getContainerTypeCode());
                                        waybillContainerInfo.setContainerTypeName(data.getContainerTypeName());
                                        waybillContainerInfo.setContainerType(data.getContainerTypeSize());
                                        flag = false;
                                        break;
                                    }
                                }

                                if (flag) {
                                    sb.append("行" + (i + 1) + "未查询到该箱型代码：" + containerTypeCode);
                                }
                                Boolean isHas = false;
                                String oldType = "";
                                if (CollUtil.isNotEmpty(waybillContainerInfosWaybills)) {
                                    for (WaybillContainerInfo info : waybillContainerInfosWaybills
                                    ) {
                                        if (info.getContainerNo().equals(waybillContainerInfo.getContainerNo())) {
                                            oldType = info.getContainerType();
                                            if ((info.getContainerType().startsWith("2") && waybillContainerInfo.getContainerType().startsWith("2")) || (info.getContainerType().startsWith("4") && waybillContainerInfo.getContainerType().startsWith("4"))) {
                                                isHas = true;
                                                break;
                                            }
                                        }
                                    }
                                }
                                if (!isHas) {
                                    sb.append("行" + (i + 1) + "该箱号与原箱型尺寸不符-->" + waybillContainerInfo.getContainerNo() + ":" + oldType + "-->" + waybillContainerInfo.getContainerType());
                                }
                            } else {
                                sb.append("行" + (i + 1) + "箱型代码为空");
                            }
                        } else {
                            sb.append("行" + (i + 1) + "箱型代码为空");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "箱型代码为空");
                    }

                    //箱属
                    if (row.getCell(10) != null) {
                        row.getCell(10).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(10).getStringCellValue()) && row.getCell(10).getStringCellValue() != null) {
                            if (row.getCell(10).getStringCellValue().trim().equals("自备箱")) {
                                waybillContainerInfo.setContainerOwner("0");
                            } else if (row.getCell(10).getStringCellValue().trim().equals("中铁箱")) {
                                waybillContainerInfo.setContainerOwner("1");
                            }
                            if (StrUtil.isBlank(waybillContainerInfo.getContainerOwner())) {
                                sb.append("请确认行" + (i + 1) + row.getCell(10).getStringCellValue().replaceAll(" ", "") + "此箱属是否存在");
                            }
                        } else {
                            sb.append("行" + (i + 1) + "箱属为空");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "箱属为空");
                    }

                    //件数
                    if (row.getCell(12) != null) {
                        row.getCell(12).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(12).getStringCellValue()) && row.getCell(12).getStringCellValue() != null) {
                            waybillGoodsInfo.setGoodsNums(row.getCell(12).getStringCellValue().trim());
                        }
                    }

                    //货重
                    if (row.getCell(13) != null) {
                        row.getCell(13).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(13).getStringCellValue()) && row.getCell(13).getStringCellValue() != null) {
                            waybillGoodsInfo.setGoodsWeight(Float.valueOf(row.getCell(13).getStringCellValue().trim()));
                        }
                    }
                    //箱重
                    if (row.getCell(14) != null) {
                        row.getCell(14).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(14).getStringCellValue()) && row.getCell(14).getStringCellValue() != null) {
                            waybillContainerInfo.setContainerDeadWeight(Float.valueOf(row.getCell(14).getStringCellValue().trim()));
                        }
                    }

                    //是否全程
                    if (row.getCell(15) != null) {
                        row.getCell(15).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(15).getStringCellValue()) && row.getCell(15).getStringCellValue() != null) {
                            if (row.getCell(15).getStringCellValue().trim().equals("是")) {
                                waybillContainerInfo.setIsFull("1");
                            } else if (row.getCell(15).getStringCellValue().trim().equals("否")) {
                                waybillContainerInfo.setIsFull("0");
                            }
                        }
                    }
                    if (platformCode.contains(waybillHeader.getPlatformCode())) {
                        if (StrUtil.isBlank(waybillContainerInfo.getIsFull())) {
                            sb.append("第" + (i + 1) + "行是否全程不能为空");
                        }
                    }

                    //有色金属
                    if (row.getCell(16) != null) {
                        row.getCell(16).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(16).getStringCellValue()) && row.getCell(16).getStringCellValue() != null) {
                            if (row.getCell(16).getStringCellValue().trim().equals("是")) {
                                waybillContainerInfo.setNonFerrous("1");
                            } else if (row.getCell(16).getStringCellValue().trim().equals("否")) {
                                waybillContainerInfo.setNonFerrous("0");
                            }
                        }
                    }

                    //货主
                    if (row.getCell(17) != null) {
                        row.getCell(17).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(17).getStringCellValue()) && row.getCell(17).getStringCellValue() != null) {
                            String stringCellValue = row.getCell(17).getStringCellValue();
                            waybillContainerInfo.setGoodsOwner(stringCellValue);
                            if ("G".equals(waybillHeader.getTrip())) {
                                waybillParticipantsFa.setConsignorName(stringCellValue);
                            } else if ("R".equals(waybillHeader.getTrip()) && StrUtil.isBlank(waybillParticipantsShou.getConsignorName())) {
                                waybillParticipantsShou.setConsignorName(stringCellValue);
                            }
                        }
                    }

                    if (row.getCell(18) != null) {
                        try {
                            row.getCell(18).setCellType(CellType.STRING);
                            String stringCellValue = row.getCell(18).getStringCellValue();
                            if ("G".equals(waybillHeader.getTrip())) {
                                waybillParticipantsFa.setCity(stringCellValue);
                            } else if ("R".equals(waybillHeader.getTrip())) {
                                waybillParticipantsShou.setCity(stringCellValue);
                            }
                        } catch (Exception e) {
                            sb.append("行" + (i + 1) + "境内货源地异常;");
                        }
                    }

                    if (row.getCell(19) != null) {
                        try {
                            row.getCell(19).setCellType(CellType.STRING);
                            String stringCellValue = row.getCell(19).getStringCellValue();
                            waybillContainerInfo.setClearanceNumber(stringCellValue);
                        } catch (Exception e) {
                            sb.append("行" + (i + 1) + "报关单号异常;");
                        }
                    }

                    //货值
                    if (row.getCell(20) != null) {
                        row.getCell(20).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(20).getStringCellValue()) && row.getCell(20).getStringCellValue() != null) {
                            waybillContainerInfo.setGoodsValue(Float.valueOf(row.getCell(20).getStringCellValue().trim()));
                        }
                    }

                    if (row.getCell(21) != null) {
                        try {
                            row.getCell(21).setCellType(CellType.STRING);
                            String stringCellValue = row.getCell(21).getStringCellValue();
                            waybillContainerInfo.setCustomsSeal(stringCellValue);
                        } catch (Exception e) {
                            sb.append("行" + (i + 1) + "海关封异常;");
                        }
                    }

                    if (row.getCell(22) != null) {
                        try {
                            row.getCell(22).setCellType(CellType.STRING);
                            String stringCellValue = row.getCell(22).getStringCellValue();
                            waybillContainerInfo.setTrainNumber(stringCellValue);
                        } catch (Exception e) {
                            sb.append("行" + (i + 1) + "车号异常;");
                        }
                    }

                    if (row.getCell(23) != null) {
                        try {
                            row.getCell(23).setCellType(CellType.STRING);
                            String stringCellValue = row.getCell(23).getStringCellValue();
                            waybillContainerInfo.setWaybillDemandNumber(stringCellValue);
                        } catch (Exception e) {
                            sb.append("行" + (i + 1) + "运单需求号异常;");
                        }
                    }

                    if (row.getCell(24) != null) {
                        try {
                            row.getCell(24).setCellType(CellType.STRING);
                            String stringCellValue = row.getCell(24).getStringCellValue();
                            waybillContainerInfo.setWaybillLnNumber(stringCellValue);
                        } catch (Exception e) {
                            sb.append("行" + (i + 1) + "国联运单号异常;");
                        }
                    }

                }

                waybillContainerInfos.add(waybillContainerInfo);
                waybillGoodsInfos.add(waybillGoodsInfo);
                waybillParticipantsUpdate.add(waybillParticipantsFa);
                waybillParticipantsUpdate.add(waybillParticipantsShou);

            }

            workbook.close();
            if (sb.length() > 0) {
                return new R(500, Boolean.FALSE,sb.toString());
            }

            waybillContainerInfoService.insertOrUpdateContainerInfo(waybillContainerInfos, waybillCodes);

            if (CollUtil.isNotEmpty(waybillParticipantsUpdate)) {
                waybillParticipantsService.insertOrUpdateWaybillParticipants(waybillParticipantsUpdate, waybillCodes);
            }

            if (CollUtil.isNotEmpty(waybillGoodsInfos)) {
                waybillGoodsInfoService.insertOrUpdateWaybillGoodsInfo(waybillGoodsInfos, waybillCodes);
            }
            //更新台账
            Shifmanagement shifmanagement = new Shifmanagement();
            shifmanagement.setShiftId(waybillHeader.getShiftNo());
            List<Shifmanagement> shifmanagementList = shifmanagementMapper.selectShifmanagementList(shifmanagement);
            if (CollUtil.isNotEmpty(shifmanagementList)) {
                for (Shifmanagement shif : shifmanagementList
                ) {
                    if (StrUtil.isBlank(shif.getParentId())) {
                        FdShippingAccoundetail fdShippingAccoundetail = new FdShippingAccoundetail();
                        fdShippingAccoundetail.setShiftNo(shif.getShiftId());
                        fdShippingAccoundetail.setPlatformCode(shif.getPlatformCode());
                        fdShippingAccoundetailService.updateFdShippingAccoundetailByWaybill(fdShippingAccoundetail);
                        break;
                    }
                }
            }
        } else {
            return new R(500, Boolean.FALSE,"表格为空");
        }

        return R.success("上传成功");
    }

    /**
     * 判断导入文件格式
     *
     * @param fileName
     * @return
     */
    public static boolean isXls(String fileName) {
        // (?i)忽略大小写
        if (fileName.matches("^.+\\.(?i)(xls)$")) {
            return true;
        } else if (fileName.matches("^.+\\.(?i)(xlsx)$")) {
            return false;
        } else {
            throw new RuntimeException("格式不对");
        }
    }

    @Override
    public Map<String, Object> weChatList(WaybillContainerInfo waybillContainerInfo) {
        Map<String, Object> map = new HashMap<>();
        List<WaybillContainerInfo> weChatList = waybillContainerInfoMapper.weChatList(waybillContainerInfo);
        map.put("weChatList", weChatList);
        List<WaybillContainerInfo> weChatTotal = waybillContainerInfoMapper.weChatTotal(waybillContainerInfo);
        if (CollUtil.isNotEmpty(weChatTotal)) {
            StringBuilder sb = new StringBuilder();
            for (WaybillContainerInfo detail : weChatTotal) {
                String identification = detail.getIdentification();
                String num = String.valueOf(detail.getNum());

                switch (identification) {
                    case "E":
                        sb.append("出口").append(num).append(" ");
                        break;
                    case "I":
                        sb.append("进口").append(num).append(" ");
                        break;
                    case "P":
                        sb.append("过境").append(num).append(" ");
                        break;
                }
            }

            String result = sb.toString().trim();
            map.put("weChatTotal", StrUtil.isNotBlank(result) ? result.replace(" ", ",") : null);
        } else {
            map.put("weChatTotal", null);
        }
        return map;
    }
}
