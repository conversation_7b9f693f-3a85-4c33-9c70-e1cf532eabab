package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.UserInfo;

import com.huazheng.tunny.ocean.mapper.BookingTrackInformationMapper;
import com.huazheng.tunny.ocean.api.entity.BookingTrackInformation;
import com.huazheng.tunny.ocean.service.BookingTrackInformationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Service("bookingTrackInformationService")
public class BookingTrackInformationServiceImpl extends ServiceImpl<BookingTrackInformationMapper, BookingTrackInformation> implements BookingTrackInformationService {

    @Autowired
    private BookingTrackInformationMapper bookingTrackInformationMapper;

    @Autowired
    private SysNoConfigServiceImpl sysNoConfigServiceImpl;

    public BookingTrackInformationMapper getBookingTrackInformationMapper() {
        return bookingTrackInformationMapper;
    }

    public void setBookingTrackInformationMapper(BookingTrackInformationMapper bookingTrackInformationMapper) {
        this.bookingTrackInformationMapper = bookingTrackInformationMapper;
    }

    public SysNoConfigServiceImpl getSysNoConfigServiceImpl() {
        return sysNoConfigServiceImpl;
    }

    public void setSysNoConfigServiceImpl(SysNoConfigServiceImpl sysNoConfigServiceImpl) {
        this.sysNoConfigServiceImpl = sysNoConfigServiceImpl;
    }

    /**
     * 查询运踪信息表(订舱-市平台)信息
     *
     * @param rowId 运踪信息表(订舱-市平台)ID
     * @return 运踪信息表(订舱-市平台)信息
     */
    @Override
    public List<BookingTrackInformation> selectBookingTrackInformationById(String rowId)
    {
        return bookingTrackInformationMapper.selectBookingTrackInformationById(rowId);
    }

    /**
     * 查询运踪信息表(订舱-市平台)列表
     *
     * @param bookingTrackInformation 运踪信息表(订舱-市平台)信息
     * @return 运踪信息表(订舱-市平台)集合
     */
    @Override
    public List<BookingTrackInformation> selectBookingTrackInformationList(BookingTrackInformation bookingTrackInformation)
    {
        return bookingTrackInformationMapper.selectBookingTrackInformationList(bookingTrackInformation);
    }


    /**
     * 分页模糊查询运踪信息表(订舱-市平台)列表
     * @return 运踪信息表(订舱-市平台)集合
     */
    @Override
    public Page selectBookingTrackInformationListByLike(Query query)
    {
        BookingTrackInformation bookingTrackInformation =  BeanUtil.mapToBean(query.getCondition(), BookingTrackInformation.class,false);
        Integer c= bookingTrackInformationMapper.queryCount(bookingTrackInformation);
        if(c!=null&&c!=0){
            query.setTotal(c);
            query.setRecords(bookingTrackInformationMapper.selectBookingTrackInformationListByLike(query,bookingTrackInformation));
        }
        return query;
    }

    /**
     * 新增运踪信息表(订舱-市平台)
     *
     * @param bookingTrackInformation 运踪信息表(订舱-市平台)信息
     * @return 结果
     */
    @Override
    public int insertBookingTrackInformation(BookingTrackInformation bookingTrackInformation)
    {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        bookingTrackInformation.setAddTime(LocalDateTime.now());
        bookingTrackInformation.setAddWho(usercode);
        bookingTrackInformation.setAddWhoName(username);
        bookingTrackInformation.setTrackNo(sysNoConfigServiceImpl.genNo("TN"));
        return bookingTrackInformationMapper.insertBookingTrackInformation(bookingTrackInformation);
    }


    /**
     * 更新/删除运踪信息表(订舱-市平台)
     *
     * @param bookingTrackInformation 运踪信息表(订舱-市平台)信息
     * @return 结果
     */
    @Override
    public int updateBookingTrackInformation(BookingTrackInformation bookingTrackInformation)
    {
        BookingTrackInformation bti = new BookingTrackInformation();
        SecruityUser userInfo= SecurityUtils.getUserInfo();
        String del = "Y";
        if(StrUtil.isNotEmpty(bookingTrackInformation.getDeleteFlag())&&del.equals(bookingTrackInformation.getDeleteFlag())){
            bti.setDeleteWhoName(userInfo.getRealName());
            bti.setDeleteWho(userInfo.getUserName());
            bti.setDeleteTime(LocalDateTime.now());
        }else{
            bti.setUpdateWhoName(userInfo.getRealName());
            bti.setUpdateWho(userInfo.getUserName());
            bti.setUpdateTime(LocalDateTime.now());
        }
        return bookingTrackInformationMapper.updateBookingTrackInformation(bookingTrackInformation);
    }


    /**
     * 删除运踪信息表(订舱-市平台)
     *
     * @param rowId 运踪信息表(订舱-市平台)ID
     * @return 结果
     */
    @Override
    public int deleteBookingTrackInformationById(String rowId)
    {
        return bookingTrackInformationMapper.deleteBookingTrackInformationById( rowId);
    };


    /**
     * 批量删除运踪信息表(订舱-市平台)对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBookingTrackInformationByIds(Integer[] rowIds)
    {
        return bookingTrackInformationMapper.deleteBookingTrackInformationByIds( rowIds);
    }

}
