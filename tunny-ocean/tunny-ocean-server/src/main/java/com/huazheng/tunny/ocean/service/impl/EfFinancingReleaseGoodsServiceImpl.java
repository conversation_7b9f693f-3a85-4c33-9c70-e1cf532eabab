package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.EfFinancingPledgechangeinfo;
import com.huazheng.tunny.ocean.mapper.EfFinancingReleaseGoodsMapper;
import com.huazheng.tunny.ocean.api.entity.EfFinancingReleaseGoods;
import com.huazheng.tunny.ocean.service.EfFinancingReleaseGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("efFinancingReleaseGoodsService")
public class EfFinancingReleaseGoodsServiceImpl extends ServiceImpl<EfFinancingReleaseGoodsMapper, EfFinancingReleaseGoods> implements EfFinancingReleaseGoodsService {

    @Autowired
    private EfFinancingReleaseGoodsMapper efFinancingReleaseGoodsMapper;

    public EfFinancingReleaseGoodsMapper getEfFinancingReleaseGoodsMapper() {
        return efFinancingReleaseGoodsMapper;
    }

    public void setEfFinancingReleaseGoodsMapper(EfFinancingReleaseGoodsMapper efFinancingReleaseGoodsMapper) {
        this.efFinancingReleaseGoodsMapper = efFinancingReleaseGoodsMapper;
    }

    /**
     * 查询需要补充/解除质押的货物信息
     *
     * @param rowId 需要补充/解除质押的货物ID
     * @return 需要补充/解除质押的货物信息
     */
    @Override
    public EfFinancingReleaseGoods selectEfFinancingReleaseGoodsById(String rowId)
    {
        return efFinancingReleaseGoodsMapper.selectEfFinancingReleaseGoodsById(rowId);
    }

    /**
     * 查询需要补充/解除质押的货物列表
     *
     * @param efFinancingReleaseGoods 需要补充/解除质押的货物信息
     * @return 需要补充/解除质押的货物集合
     */
    @Override
    public List<EfFinancingReleaseGoods> selectEfFinancingReleaseGoodsList(EfFinancingReleaseGoods efFinancingReleaseGoods)
    {
        return efFinancingReleaseGoodsMapper.selectEfFinancingReleaseGoodsList(efFinancingReleaseGoods);
    }


    /**
     * 分页模糊查询需要补充/解除质押的货物列表
     * @return 需要补充/解除质押的货物集合
     */
    @Override
    public Page selectEfFinancingReleaseGoodsListByLike(Query query)
    {
        EfFinancingReleaseGoods efFinancingReleaseGoods =  BeanUtil.mapToBean(query.getCondition(), EfFinancingReleaseGoods.class,false);
        query.setRecords(efFinancingReleaseGoodsMapper.selectEfFinancingReleaseGoodsListByLike(query,efFinancingReleaseGoods));
        return query;
    }

    @Override
    public Page selectEfFinancingReleaseGoodsListByLike2(Query query)
    {
        EfFinancingPledgechangeinfo efFinancingPledgechangeinfo =  BeanUtil.mapToBean(query.getCondition(), EfFinancingPledgechangeinfo.class,false);
        efFinancingPledgechangeinfo.setAddWho(SecurityUtils.getUserInfo().getUserName());
        efFinancingPledgechangeinfo.setDeleteFlag("N");
        query.setRecords(efFinancingReleaseGoodsMapper.selectEfFinancingReleaseGoodsList2(query,efFinancingPledgechangeinfo));
        return query;
    }

    /**
     * 新增需要补充/解除质押的货物
     *
     * @param efFinancingReleaseGoods 需要补充/解除质押的货物信息
     * @return 结果
     */
    @Override
    public int insertEfFinancingReleaseGoods(EfFinancingReleaseGoods efFinancingReleaseGoods)
    {
        return efFinancingReleaseGoodsMapper.insertEfFinancingReleaseGoods(efFinancingReleaseGoods);
    }

    /**
     * 修改需要补充/解除质押的货物
     *
     * @param efFinancingReleaseGoods 需要补充/解除质押的货物信息
     * @return 结果
     */
    @Override
    public int updateEfFinancingReleaseGoods(EfFinancingReleaseGoods efFinancingReleaseGoods)
    {
        return efFinancingReleaseGoodsMapper.updateEfFinancingReleaseGoods(efFinancingReleaseGoods);
    }


    /**
     * 删除需要补充/解除质押的货物
     *
     * @param rowId 需要补充/解除质押的货物ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingReleaseGoodsById(String rowId)
    {
        return efFinancingReleaseGoodsMapper.deleteEfFinancingReleaseGoodsById( rowId);
    };


    /**
     * 批量删除需要补充/解除质押的货物对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingReleaseGoodsByIds(Integer[] rowIds)
    {
        return efFinancingReleaseGoodsMapper.deleteEfFinancingReleaseGoodsByIds( rowIds);
    }

}
