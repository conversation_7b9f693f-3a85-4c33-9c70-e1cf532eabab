package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.ShippingAccountMapper;
import com.huazheng.tunny.ocean.api.entity.ShippingAccount;
import com.huazheng.tunny.ocean.service.ShippingAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("shippingAccountService")
public class ShippingAccountServiceImpl extends ServiceImpl<ShippingAccountMapper, ShippingAccount> implements ShippingAccountService {

    @Autowired
    private ShippingAccountMapper shippingAccountMapper;

    public ShippingAccountMapper getShippingAccountMapper() {
        return shippingAccountMapper;
    }

    public void setShippingAccountMapper(ShippingAccountMapper shippingAccountMapper) {
        this.shippingAccountMapper = shippingAccountMapper;
    }

    /**
     * 查询发运台账表信息
     *
     * @param rowId 发运台账表ID
     * @return 发运台账表信息
     */
    @Override
    public ShippingAccount selectShippingAccountById(String rowId)
    {
        return shippingAccountMapper.selectShippingAccountById(rowId);
    }

    /**
     * 查询发运台账表列表
     *
     * @param shippingAccount 发运台账表信息
     * @return 发运台账表集合
     */
    @Override
    public List<ShippingAccount> selectShippingAccountList(ShippingAccount shippingAccount)
    {
        return shippingAccountMapper.selectShippingAccountList(shippingAccount);
    }


    /**
     * 分页模糊查询发运台账表列表
     * @return 发运台账表集合
     */
    @Override
    public Page selectShippingAccountListByLike(Query query)
    {
        ShippingAccount shippingAccount =  BeanUtil.mapToBean(query.getCondition(), ShippingAccount.class,false);
        query.setRecords(shippingAccountMapper.selectShippingAccountListByLike(query,shippingAccount));
        return query;
    }

    /**
     * 根据班次号查询下属集装箱信息分页列表
     * @return 发运台账表集合
     */
    @Override
    public Page selectShippingAccountListByShiftId(Query query) {
        ShippingAccount shippingAccount =  BeanUtil.mapToBean(query.getCondition(), ShippingAccount.class,false);
        query.setRecords(shippingAccountMapper.selectContainerInfoByShiftId(query,shippingAccount));
        return query;
    }

    /**
     * 新增发运台账表
     *
     * @param shippingAccount 发运台账表信息
     * @return 结果
     */
    @Override
    public int insertShippingAccount(ShippingAccount shippingAccount)
    {
        return shippingAccountMapper.insertShippingAccount(shippingAccount);
    }

    /**
     * 修改发运台账表
     *
     * @param shippingAccount 发运台账表信息
     * @return 结果
     */
    @Override
    public int updateShippingAccount(ShippingAccount shippingAccount)
    {
        return shippingAccountMapper.updateShippingAccount(shippingAccount);
    }


    /**
     * 删除发运台账表
     *
     * @param rowId 发运台账表ID
     * @return 结果
     */
    @Override
    public int deleteShippingAccountById(String rowId)
    {
        return shippingAccountMapper.deleteShippingAccountById( rowId);
    };


    /**
     * 批量删除发运台账表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteShippingAccountByIds(Integer[] rowIds)
    {
        return shippingAccountMapper.deleteShippingAccountByIds( rowIds);
    }

}
