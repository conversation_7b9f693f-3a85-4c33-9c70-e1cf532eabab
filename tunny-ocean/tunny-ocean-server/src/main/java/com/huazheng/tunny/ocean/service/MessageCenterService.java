package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.MessageCenter;
import com.huazheng.tunny.ocean.util.R;

import java.util.List;
import java.util.Map;

/**
 * @Description: Service
 * @Author: shaojian
 * @Date: 2022-10-09 11:31:53
 */
public interface MessageCenterService extends IService<MessageCenter> {

    /**
     * @Description: 分页
     * @Param: query
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    Page page(Query query);

    /**
     * @Description: 详情
     * @Param: id
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    R<MessageCenter> info(Integer id);

    /**
     * @Description: 保存
     * @Param: param
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    R save(MessageCenter param);

    /**
     * @Description: 修改
     * @Param: param
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    R update(MessageCenter param);

    /**
     * @Description: 删除
     * @Param: id
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    R delete(Integer id);

    /**
     * @Description: 列表
     * @Param: param
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    R<List<MessageCenter>> list(MessageCenter param);

    /**
     * @Description: 切换已读
     * @Param: param
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    R read(Map<String, Object> param);

    /**
     * @Description: 根据模块切换已读
     * @Param: param
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    R readByModule(Map<String, Object> param);

    R readByModuleV2(MessageCenter param);

    R getMyMsgNum(MessageCenter param);

    R selectBpmBacklog(Query query);

    R selectBpmProcessed(Query query);

    R addMessage(List<MessageCenter> addMessageDTOS,String psb,String businessName);

    List<Map<String, String>> selectModelByUserName(String userName);

    void pushWebSocket(Map<String,String> map);

    public void sendMiniMsgYw(String userNames, String thing4,String content, Integer id,String name);

    public void sendMiniMsgNotice(List<String> userNames, String thing4,String content, MessageCenter messageCenter);
}

