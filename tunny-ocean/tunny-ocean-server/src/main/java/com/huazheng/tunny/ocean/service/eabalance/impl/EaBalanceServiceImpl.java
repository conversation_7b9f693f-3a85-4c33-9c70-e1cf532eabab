package com.huazheng.tunny.ocean.service.eabalance.impl;

import cn.hutool.core.collection.CollUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.mapper.eabalance.EaBalanceMapper;
import com.huazheng.tunny.ocean.api.entity.eabalance.EaBalance;
import com.huazheng.tunny.ocean.service.eabalance.EaBalanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Service("eaBalanceService")
public class EaBalanceServiceImpl extends ServiceImpl<EaBalanceMapper, EaBalance> implements EaBalanceService {

    @Autowired
    private EaBalanceMapper eaBalanceMapper;

    /**
     * 查询余额表信息
     *
     * @param balanceId 余额表ID
     * @return 余额表信息
     */
    @Override
    public EaBalance selectEaBalanceById(Long balanceId) {
        return eaBalanceMapper.selectEaBalanceById(balanceId);
    }

    /**
     * 查询余额表列表
     *
     * @param eaBalance 余额表信息
     * @return 余额表集合
     */
    @Override
    public List<EaBalance> selectEaBalanceList(EaBalance eaBalance) {
        return eaBalanceMapper.selectEaBalanceList(eaBalance);
    }


    /**
     * 分页模糊查询余额表列表
     *
     * @return 余额表集合
     */
    @Override
    public Page selectEaBalanceListByLike(Query query) {
        EaBalance eaBalance = BeanUtil.mapToBean(query.getCondition(), EaBalance.class, false);
        query.setRecords(eaBalanceMapper.selectEaBalanceListByLike(query, eaBalance));
        return query;
    }

    /**
     * 新增余额表
     *
     * @param eaBalance 余额表信息
     * @return 结果
     */
    @Override
    public int insertEaBalance(EaBalance eaBalance) {
        return eaBalanceMapper.insertEaBalance(eaBalance);
    }

    /**
     * 修改余额表
     *
     * @param eaBalance 余额表信息
     * @return 结果
     */
    @Override
    public int updateEaBalance(EaBalance eaBalance) {
        return eaBalanceMapper.updateEaBalance(eaBalance);
    }


    /**
     * 删除余额表
     *
     * @param balanceId 余额表ID
     * @return 结果
     */
    @Override
    public int deleteEaBalanceById(Long balanceId) {
        return eaBalanceMapper.deleteEaBalanceById(balanceId);
    }


    /**
     * 批量删除余额表对象
     *
     * @param balanceIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEaBalanceByIds(Integer[] balanceIds) {
        return eaBalanceMapper.deleteEaBalanceByIds(balanceIds);
    }

    @Override
    public R generateBalance(EaBalance eaBalance) {
        List<EaBalance> list = eaBalanceMapper.selectCustomerInfo(eaBalance);
        if (CollUtil.isEmpty(list)) {
            return R.success();
        }
        List<EaBalance> newList = new ArrayList<>();
        for (EaBalance balance : list) {
            if ("1".equals(balance.getRemark())) {
                EaBalance newBalance = new EaBalance();
                newBalance.setPayerCode(balance.getPayerCode());
                newBalance.setPayeeCode("ztdl");
                newBalance.setDelFlag("N");
                List<EaBalance> oldList = eaBalanceMapper.selectEaBalanceList(newBalance);
                if (CollUtil.isEmpty(oldList)) {
                    newBalance.setPayerName(balance.getPayerName());
                    newBalance.setPayeeName("中铁国际多式联运有限公司");
                    newBalance.setTotalExpenditure(BigDecimal.ZERO);
                    newBalance.setTotalIncome(BigDecimal.ZERO);
                    newList.add(newBalance);
                }
            }
            balance.setTotalExpenditure(BigDecimal.ZERO);
            balance.setTotalIncome(BigDecimal.ZERO);
            newList.add(balance);
        }
        if (CollUtil.isNotEmpty(newList)) {
            for (EaBalance balance : newList) {
                eaBalanceMapper.insertEaBalance(balance);
            }
        }
        return R.success();
    }

}
