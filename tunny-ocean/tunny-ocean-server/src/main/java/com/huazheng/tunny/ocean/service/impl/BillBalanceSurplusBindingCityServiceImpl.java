package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.BillBalanceSurplusBindingCityMapper;
import com.huazheng.tunny.ocean.api.entity.BillBalanceSurplusBindingCity;
import com.huazheng.tunny.ocean.service.BillBalanceSurplusBindingCityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("billBalanceSurplusBindingCityService")
public class BillBalanceSurplusBindingCityServiceImpl extends ServiceImpl<BillBalanceSurplusBindingCityMapper, BillBalanceSurplusBindingCity> implements BillBalanceSurplusBindingCityService {

    @Autowired
    private BillBalanceSurplusBindingCityMapper billBalanceSurplusBindingCityMapper;

    /**
     * 查询应付账单结算绑定余额表（市）信息
     *
     * @param id 应付账单结算绑定余额表（市）ID
     * @return 应付账单结算绑定余额表（市）信息
     */
    @Override
    public BillBalanceSurplusBindingCity selectBillBalanceSurplusBindingCityById(Integer id)
    {
        return billBalanceSurplusBindingCityMapper.selectBillBalanceSurplusBindingCityById(id);
    }

    /**
     * 查询应付账单结算绑定余额表（市）列表
     *
     * @param billBalanceSurplusBindingCity 应付账单结算绑定余额表（市）信息
     * @return 应付账单结算绑定余额表（市）集合
     */
    @Override
    public List<BillBalanceSurplusBindingCity> selectBillBalanceSurplusBindingCityList(BillBalanceSurplusBindingCity billBalanceSurplusBindingCity)
    {
        return billBalanceSurplusBindingCityMapper.selectBillBalanceSurplusBindingCityList(billBalanceSurplusBindingCity);
    }


    /**
     * 分页模糊查询应付账单结算绑定余额表（市）列表
     * @return 应付账单结算绑定余额表（市）集合
     */
    @Override
    public Page selectBillBalanceSurplusBindingCityListByLike(Query query)
    {
        BillBalanceSurplusBindingCity billBalanceSurplusBindingCity =  BeanUtil.mapToBean(query.getCondition(), BillBalanceSurplusBindingCity.class,false);
        query.setRecords(billBalanceSurplusBindingCityMapper.selectBillBalanceSurplusBindingCityListByLike(query,billBalanceSurplusBindingCity));
        return query;
    }

    /**
     * 新增应付账单结算绑定余额表（市）
     *
     * @param billBalanceSurplusBindingCity 应付账单结算绑定余额表（市）信息
     * @return 结果
     */
    @Override
    public int insertBillBalanceSurplusBindingCity(BillBalanceSurplusBindingCity billBalanceSurplusBindingCity)
    {
        return billBalanceSurplusBindingCityMapper.insertBillBalanceSurplusBindingCity(billBalanceSurplusBindingCity);
    }

    /**
     * 修改应付账单结算绑定余额表（市）
     *
     * @param billBalanceSurplusBindingCity 应付账单结算绑定余额表（市）信息
     * @return 结果
     */
    @Override
    public int updateBillBalanceSurplusBindingCity(BillBalanceSurplusBindingCity billBalanceSurplusBindingCity)
    {
        return billBalanceSurplusBindingCityMapper.updateBillBalanceSurplusBindingCity(billBalanceSurplusBindingCity);
    }


    /**
     * 删除应付账单结算绑定余额表（市）
     *
     * @param id 应付账单结算绑定余额表（市）ID
     * @return 结果
     */
    public int deleteBillBalanceSurplusBindingCityById(Integer id)
    {
        return billBalanceSurplusBindingCityMapper.deleteBillBalanceSurplusBindingCityById( id);
    };


    /**
     * 批量删除应付账单结算绑定余额表（市）对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBillBalanceSurplusBindingCityByIds(Integer[] ids)
    {
        return billBalanceSurplusBindingCityMapper.deleteBillBalanceSurplusBindingCityByIds( ids);
    }

}
