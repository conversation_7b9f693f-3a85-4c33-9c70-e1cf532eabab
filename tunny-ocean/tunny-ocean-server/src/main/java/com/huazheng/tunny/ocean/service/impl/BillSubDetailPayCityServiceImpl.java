package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.BillSubDetailPayCityMapper;
import com.huazheng.tunny.ocean.api.entity.BillSubDetailPayCity;
import com.huazheng.tunny.ocean.service.BillSubDetailPayCityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("billSubDetailPayCityService")
public class BillSubDetailPayCityServiceImpl extends ServiceImpl<BillSubDetailPayCityMapper, BillSubDetailPayCity> implements BillSubDetailPayCityService {

    @Autowired
    private BillSubDetailPayCityMapper billSubDetailPayCityMapper;

    /**
     * 查询应付账单（市）子账单详情信息
     *
     * @param id 应付账单（市）子账单详情ID
     * @return 应付账单（市）子账单详情信息
     */
    @Override
    public BillSubDetailPayCity selectBillSubDetailPayCityById(Integer id)
    {
        return billSubDetailPayCityMapper.selectBillSubDetailPayCityById(id);
    }

    /**
     * 查询应付账单（市）子账单详情列表
     *
     * @param billSubDetailPayCity 应付账单（市）子账单详情信息
     * @return 应付账单（市）子账单详情集合
     */
    @Override
    public List<BillSubDetailPayCity> selectBillSubDetailPayCityList(BillSubDetailPayCity billSubDetailPayCity)
    {
        return billSubDetailPayCityMapper.selectBillSubDetailPayCityList(billSubDetailPayCity);
    }


    /**
     * 分页模糊查询应付账单（市）子账单详情列表
     * @return 应付账单（市）子账单详情集合
     */
    @Override
    public Page selectBillSubDetailPayCityListByLike(Query query)
    {
        BillSubDetailPayCity billSubDetailPayCity =  BeanUtil.mapToBean(query.getCondition(), BillSubDetailPayCity.class,false);
        query.setRecords(billSubDetailPayCityMapper.selectBillSubDetailPayCityListByLike(query,billSubDetailPayCity));
        return query;
    }

    /**
     * 新增应付账单（市）子账单详情
     *
     * @param billSubDetailPayCity 应付账单（市）子账单详情信息
     * @return 结果
     */
    @Override
    public int insertBillSubDetailPayCity(BillSubDetailPayCity billSubDetailPayCity)
    {
        return billSubDetailPayCityMapper.insertBillSubDetailPayCity(billSubDetailPayCity);
    }

    /**
     * 修改应付账单（市）子账单详情
     *
     * @param billSubDetailPayCity 应付账单（市）子账单详情信息
     * @return 结果
     */
    @Override
    public int updateBillSubDetailPayCity(BillSubDetailPayCity billSubDetailPayCity)
    {
        return billSubDetailPayCityMapper.updateBillSubDetailPayCity(billSubDetailPayCity);
    }


    /**
     * 删除应付账单（市）子账单详情
     *
     * @param id 应付账单（市）子账单详情ID
     * @return 结果
     */
    public int deleteBillSubDetailPayCityById(Integer id)
    {
        return billSubDetailPayCityMapper.deleteBillSubDetailPayCityById( id);
    };


    /**
     * 批量删除应付账单（市）子账单详情对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBillSubDetailPayCityByIds(Integer[] ids)
    {
        return billSubDetailPayCityMapper.deleteBillSubDetailPayCityByIds( ids);
    }

}
