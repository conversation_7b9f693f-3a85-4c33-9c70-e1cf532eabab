package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FdBusCostDetailHis;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 业务流程单历史表 服务接口层
 *
 * <AUTHOR>
 * @date 2024-06-13 20:30:20
 */
public interface FdBusCostDetailHisService extends IService<FdBusCostDetailHis> {
    /**
     * 查询业务流程单历史表信息
     *
     * @param id 业务流程单历史表ID
     * @return 业务流程单历史表信息
     */
    public FdBusCostDetailHis selectFdBusCostDetailHisById(Integer id);

    /**
     * 查询业务流程单历史表列表
     *
     * @param fdBusCostDetailHis 业务流程单历史表信息
     * @return 业务流程单历史表集合
     */
    public List<FdBusCostDetailHis> selectFdBusCostDetailHisList(FdBusCostDetailHis fdBusCostDetailHis);


    /**
     * 分页模糊查询业务流程单历史表列表
     * @return 业务流程单历史表集合
     */
    public Page selectFdBusCostDetailHisListByLike(Query query);



    /**
     * 新增业务流程单历史表
     *
     * @param fdBusCostDetailHis 业务流程单历史表信息
     * @return 结果
     */
    public int insertFdBusCostDetailHis(FdBusCostDetailHis fdBusCostDetailHis);

    /**
     * 修改业务流程单历史表
     *
     * @param fdBusCostDetailHis 业务流程单历史表信息
     * @return 结果
     */
    public int updateFdBusCostDetailHis(FdBusCostDetailHis fdBusCostDetailHis);

    /**
     * 删除业务流程单历史表
     *
     * @param id 业务流程单历史表ID
     * @return 结果
     */
    public int deleteFdBusCostDetailHisById(Integer id);

    /**
     * 批量删除业务流程单历史表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdBusCostDetailHisByIds(Integer[] ids);

}

