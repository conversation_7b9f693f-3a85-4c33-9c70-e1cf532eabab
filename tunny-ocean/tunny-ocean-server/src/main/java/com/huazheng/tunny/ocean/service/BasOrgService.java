package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.BasOrg;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 机构信息管理表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-01 10:37:15
 */
public interface BasOrgService extends IService<BasOrg> {
    /**
     * 查询机构信息管理表信息
     *
     * @param rowId 机构信息管理表ID
     * @return 机构信息管理表信息
     */
    public BasOrg selectBasOrgById(String rowId);

    /**
     * 查询机构信息管理表列表
     *
     * @param basOrg 机构信息管理表信息
     * @return 机构信息管理表集合
     */
    public List<BasOrg> selectBasOrgList(BasOrg basOrg);


    /**
     * 分页模糊查询机构信息管理表列表
     * @return 机构信息管理表集合
     */
    public Page selectBasOrgListByLike(Query query);



    /**
     * 新增机构信息管理表
     *
     * @param basOrg 机构信息管理表信息
     * @return 结果
     */
    public int insertBasOrg(BasOrg basOrg);

    /**
     * 修改机构信息管理表
     *
     * @param basOrg 机构信息管理表信息
     * @return 结果
     */
    public int updateBasOrg(BasOrg basOrg);

    /**
     * 删除机构信息管理表
     *
     * @param rowId 机构信息管理表ID
     * @return 结果
     */
    public int deleteBasOrgById(String rowId);

    /**
     * 批量删除机构信息管理表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteBasOrgByIds(Integer[] rowIds);

}

