package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.OpinionFeedback;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 意见反馈 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-06-27 18:03:32
 */
public interface OpinionFeedbackService extends IService<OpinionFeedback> {
    /**
     * 查询意见反馈信息
     *
     * @param rowId 意见反馈ID
     * @return 意见反馈信息
     */
    public OpinionFeedback selectOpinionFeedbackById(String rowId);

    /**
     * 查询意见反馈列表
     *
     * @param opinionFeedback 意见反馈信息
     * @return 意见反馈集合
     */
    public List<OpinionFeedback> selectOpinionFeedbackList(OpinionFeedback opinionFeedback);


    /**
     * 分页模糊查询意见反馈列表
     *
     * @return 意见反馈集合
     */
    public Page selectOpinionFeedbackListByLike(Query query);


    /**
     * 新增意见反馈
     *
     * @param opinionFeedback 意见反馈信息
     * @return 结果
     */
    public int insertOpinionFeedback(OpinionFeedback opinionFeedback);

    /**
     * 修改意见反馈
     *
     * @param opinionFeedback 意见反馈信息
     * @return 结果
     */
    public int updateOpinionFeedback(OpinionFeedback opinionFeedback);

    /**
     * 删除意见反馈
     *
     * @param rowId 意见反馈ID
     * @return 结果
     */
    public int deleteOpinionFeedbackById(String rowId);

    /**
     * 批量删除意见反馈
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteOpinionFeedbackByIds(Integer[] rowIds);

}

