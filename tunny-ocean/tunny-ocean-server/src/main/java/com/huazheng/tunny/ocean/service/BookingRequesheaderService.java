package com.huazheng.tunny.ocean.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.RequesheaderDTO;
import com.huazheng.tunny.ocean.api.entity.BookingRequesheader;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.SysUser;
import io.swagger.models.auth.In;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 订舱申请单主表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-02 14:38:40
 */
public interface BookingRequesheaderService extends IService<BookingRequesheader> {

    public String saveBookingAndWaybill(String data);

    public R saveBookingAndWaybillForCity(RequesheaderDTO header, String platformCode);
    /**
     * 查询订舱申请单主表信息
     *
     * @param rowId 订舱申请单主表ID
     * @return 订舱申请单主表信息
     */
    public BookingRequesheader selectBookingRequesheaderById(String rowId);

    /**
     * 查询订舱申请单主表列表
     *
     * @param bookingRequesheader 订舱申请单主表信息
     * @return 订舱申请单主表集合
     */
    public List<BookingRequesheader> selectBookingRequesheaderList(BookingRequesheader bookingRequesheader);


    /**
     * 分页模糊查询订舱申请单主表列表
     * @return 订舱申请单主表集合
     */
    public Page selectBookingRequesheaderListByLike(Query query);

    /**
     * 管理平台审核统计代办
     * @param bookingRequesheader
     * @return
     */
    public Integer selectBookingRequesheaderListByLikeCount(BookingRequesheader bookingRequesheader);



    /**
     * 新增订舱申请单主表
     *
     * @param bookingRequesheader 订舱申请单主表信息
     * @return 结果
     */
    public R insertBookingRequesheader(BookingRequesheader bookingRequesheader);

    public String saveForSh(String data);

    public String queryAuditForSh(String data);

    public int insertUser(SysUser sysUser);

    public int insertUserAndRole(SysUser sysUser);

    public int updateSysUser(SysUser sysUser);

    public List<SysUser> selectUserList(SysUser sysUser);

    /**
     * 修改订舱申请单主表
     *
     * @param bookingRequesheader 订舱申请单主表信息
     * @return 结果
     */
    public R updateBookingRequesheader(BookingRequesheader bookingRequesheader);

    public int updateBookingRequesheader1(BookingRequesheader bookingRequesheader);

    /*
     * 修改状态为待审核
     * */
    public R commitStatus(String orderNo);

    /**
     * 删除订舱申请单主表
     *
     * @param rowId 订舱申请单主表ID
     * @return 结果
     */
    public int deleteBookingRequesheaderById(String rowId);

    /**
     * 批量删除订舱申请单主表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteBookingRequesheaderByIds(Integer[] rowIds);

    /**
     * 删除用户
     *
     * @param sysUser 用户
     * @return boolean
     */
    Boolean deleteUserById(SysUser sysUser);

    public BookingRequesheader selectBookingRequesheaderByOrderNo(String orderNo);

    public R deleteAll(@RequestBody BookingRequesheader bookingRequesheader);
}

