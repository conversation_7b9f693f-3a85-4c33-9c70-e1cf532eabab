package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.BookingTrackDetailCp;
import com.huazheng.tunny.ocean.api.enums.SysEnum;
import com.huazheng.tunny.ocean.mapper.BookingTrackDetailCpMapper;
import com.huazheng.tunny.ocean.mapper.BookingTrackHeaderCpMapper;
import com.huazheng.tunny.ocean.api.entity.BookingTrackHeaderCp;
import com.huazheng.tunny.ocean.service.BookingTrackHeaderCpService;
import com.huazheng.tunny.ocean.service.SysNoConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service("bookingTrackHeaderCpService")
public class BookingTrackHeaderCpServiceImpl extends ServiceImpl<BookingTrackHeaderCpMapper, BookingTrackHeaderCp> implements BookingTrackHeaderCpService {

    @Autowired
    private BookingTrackHeaderCpMapper bookingTrackHeaderCpMapper;

    @Autowired
    private BookingTrackDetailCpMapper bookingTrackDetailCpMapper;

    @Autowired
    private SysNoConfigService sysNoConfigService;

    @Value("${path.CY_MANAGE_ADMIN}")
    private String cyManageAdmin;
    @Value("${path.CY_BIZ_ADMIN}")
    private String cyBizAdmin;
    @Value("${path.PR_MANAGE_ADMIN}")
    private String prManagedmin;
    @Value("${path.PR_BIZ_ADMIN}")
    private String prBizAdmin;

    /**
     * 查询运踪信息主表(市平台-省)信息
     *
     * @param rowId 运踪信息主表(市平台-省)ID
     * @return 运踪信息主表(市平台-省)信息
     */
    @Override
    public BookingTrackHeaderCp selectBookingTrackHeaderCpById(String rowId)
    {
        return bookingTrackHeaderCpMapper.selectBookingTrackHeaderCpById(rowId);
    }

    /**
     * 查询运踪信息主表(市平台-省)列表
     *
     * @param bookingTrackHeaderCp 运踪信息主表(市平台-省)信息
     * @return 运踪信息主表(市平台-省)集合
     */
    @Override
    public List<BookingTrackHeaderCp> selectBookingTrackHeaderCpList(BookingTrackHeaderCp bookingTrackHeaderCp)
    {
        return bookingTrackHeaderCpMapper.selectBookingTrackHeaderCpList(bookingTrackHeaderCp);
    }


    /**
     * 分页模糊查询运踪信息主表(市平台-省)列表
     * @return 运踪信息主表(市平台-省)集合
     */
    @Override
    public Page selectBookingTrackHeaderCpListByLike(Query query)
    {
        BookingTrackHeaderCp bookingTrackHeaderCp =  BeanUtil.mapToBean(query.getCondition(), BookingTrackHeaderCp.class,false);
        List<String> roles = SecurityUtils.getRoles();
        if(roles!=null && roles.size()>0){
            for (String role: roles
            ) {
                if(cyManageAdmin.equals(role) || cyBizAdmin.equals(role)){
                    bookingTrackHeaderCp.setQxType("cy");
                }
                if(prManagedmin.equals(role) || prBizAdmin.equals(role)){
                    bookingTrackHeaderCp.setQxType("pr");
                }
            }
        }
        Integer c= bookingTrackHeaderCpMapper.queryCount(bookingTrackHeaderCp);
        if(c!=null&&c!=0){
            query.setTotal(c);
            query.setRecords(bookingTrackHeaderCpMapper.selectBookingTrackHeaderCpListByLike(query, bookingTrackHeaderCp));
        }
        return query;
    }

    /**
     * 新增运踪信息主表(市平台-省)
     *
     * @param bookingTrackHeaderCp 运踪信息主表(市平台-省)信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R insertBookingTrackHeaderCp(BookingTrackHeaderCp bookingTrackHeaderCp)
    {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String trackNo = sysNoConfigService.genNo("TNCP");
        bookingTrackHeaderCp.setRowId(UUID.randomUUID().toString());
        bookingTrackHeaderCp.setTrackNo(trackNo);
        bookingTrackHeaderCp.setAddWho(userInfo.getUserName());
        bookingTrackHeaderCp.setAddWhoName(userInfo.getRealName());
        bookingTrackHeaderCp.setAddTime(LocalDateTime.now());
        bookingTrackHeaderCp.setStatus(SysEnum.TRACKINFO_CP_STATUS_WAIT_COMMITED.getKey());//保存时，设置状态未提交
        try {
            bookingTrackHeaderCpMapper.insertBookingTrackHeaderCp(bookingTrackHeaderCp);

            List<BookingTrackDetailCp> dayPlanDetailCityToProList = bookingTrackHeaderCp.getDetails();
            if (!dayPlanDetailCityToProList.isEmpty()){
                List<BookingTrackDetailCp> trackDetailCpList=new ArrayList<>();
                for (BookingTrackDetailCp bookingTrackDetailCp : dayPlanDetailCityToProList) {
                    bookingTrackDetailCp.setRowId(UUID.randomUUID().toString());
                    bookingTrackDetailCp.setTrackNo(trackNo);
                    bookingTrackDetailCp.setAddTime(LocalDateTime.now());
                    bookingTrackDetailCp.setAddWhoName(userInfo.getRealName());
                    bookingTrackDetailCp.setAddWho(userInfo.getUserName());
                    trackDetailCpList.add(bookingTrackDetailCp);
                }
                bookingTrackDetailCpMapper.insertBookingTrackDetailCpBatch(trackDetailCpList);
                return new R(200,Boolean.TRUE, "添加运踪信息（市到省）成功");
            }else{
                return new R(500,false,"请至少添加一条明细数据");
            }
        } catch (Exception e) {
            e.printStackTrace();
            e.getMessage();
            System.out.println("****************添加运踪信息（市到省）失败！！！************");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new R(500,Boolean.FALSE, "添加运踪信息（市到省）失败");
        }
    }

    /**
     * 修改运踪信息主表(市平台-省)
     *
     * @param bookingTrackHeaderCp 运踪信息主表(市平台-省)信息
     * @return 结果
     */
    @Override
    public int updateBookingTrackHeaderCp(BookingTrackHeaderCp bookingTrackHeaderCp)
    {
        return bookingTrackHeaderCpMapper.updateBookingTrackHeaderCp(bookingTrackHeaderCp);
    }

    /**
     * 删除 运踪信息主表(市平台-省)
     *
     * @param bookingTrackHeaderCp 运踪信息主表(市平台-省)信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R deleteBookingTrackHeaderCp(BookingTrackHeaderCp bookingTrackHeaderCp) {
        String trackNo= bookingTrackHeaderCp.getTrackNo();
        BookingTrackDetailCp trackDetailCp=new BookingTrackDetailCp();
        trackDetailCp.setTrackNo(trackNo);
        trackDetailCp.setDeleteTime(LocalDateTime.now());
        trackDetailCp.setDeleteWho(bookingTrackHeaderCp.getDeleteWho());
        trackDetailCp.setDeleteWhoName(bookingTrackHeaderCp.getDeleteWhoName());
        trackDetailCp.setDeleteFlag("Y");
        try {
            int flag=bookingTrackHeaderCpMapper.updateBookingTrackHeaderCp(bookingTrackHeaderCp);
            int flag1=bookingTrackDetailCpMapper.updateBookingTrackDetailCp(trackDetailCp);
            if(flag==0||flag1==0){
                System.out.println( "删除运踪信息失败！！！,未找到相应运踪信息");
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return new R(500,Boolean.FALSE, "删除运踪信息失败,未找到相应运踪信息");
            }
            return new R(200,Boolean.TRUE, "删除运踪信息成功");
        } catch (Exception e) {
            System.out.println(e.getMessage() + "删除运踪信息失败！！！");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new R(500,Boolean.FALSE, "删除运踪信息失败");
        }
    }


    /**
     * 删除运踪信息主表(市平台-省)
     *
     * @param rowId 运踪信息主表(市平台-省)ID
     * @return 结果
     */
    @Override
    public int deleteBookingTrackHeaderCpById(String rowId)
    {
        return bookingTrackHeaderCpMapper.deleteBookingTrackHeaderCpById( rowId);
    }


    /**
     * 批量删除运踪信息主表(市平台-省)对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBookingTrackHeaderCpByIds(Integer[] rowIds)
    {
        return bookingTrackHeaderCpMapper.deleteBookingTrackHeaderCpByIds( rowIds);
    }

}
