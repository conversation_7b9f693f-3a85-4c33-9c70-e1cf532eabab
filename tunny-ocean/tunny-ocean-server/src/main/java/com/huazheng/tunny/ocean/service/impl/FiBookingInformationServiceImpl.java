package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FiBookingInformationMapper;
import com.huazheng.tunny.ocean.api.entity.FiBookingInformation;
import com.huazheng.tunny.ocean.service.FiBookingInformationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fiBookingInformationService")
public class FiBookingInformationServiceImpl extends ServiceImpl<FiBookingInformationMapper, FiBookingInformation> implements FiBookingInformationService {

    @Autowired
    private FiBookingInformationMapper fiBookingInformationMapper;

    public FiBookingInformationMapper getFiBookingInformationMapper() {
        return fiBookingInformationMapper;
    }

    public void setFiBookingInformationMapper(FiBookingInformationMapper fiBookingInformationMapper) {
        this.fiBookingInformationMapper = fiBookingInformationMapper;
    }

    /**
     * 查询平台客户订舱单信息信息
     *
     * @return 订舱单信息集合
     */
    @Override
    public Page selectFiBookingInformationListByPt(Query query)
    {
        FiBookingInformation fiBookingInformation =  BeanUtil.mapToBean(query.getCondition(), FiBookingInformation.class,false);
        query.setRecords(fiBookingInformationMapper.selectFiBookingInformationListByPt(query,fiBookingInformation));
        return query;
    }
    /**
     * 查询订舱客户订舱单信息信息
     *
     * @return 订舱单信息集合
     */
    @Override
    public Page selectFiBookingInformationListByDc(Query query)
    {
        FiBookingInformation fiBookingInformation =  BeanUtil.mapToBean(query.getCondition(), FiBookingInformation.class,false);
        query.setRecords(fiBookingInformationMapper.selectFiBookingInformationListByDc(query,fiBookingInformation));
        return query;
    }

    /**
     * 查询订舱单信息信息
     *
     * @param rowId 订舱单信息ID
     * @return 订舱单信息信息
     */
    @Override
    public FiBookingInformation selectFiBookingInformationById(String rowId)
    {
        return fiBookingInformationMapper.selectFiBookingInformationById(rowId);
    }

    /**
     * 查询订舱单信息列表
     *
     * @param fiBookingInformation 订舱单信息信息
     * @return 订舱单信息集合
     */
    @Override
    public List<FiBookingInformation> selectFiBookingInformationList(FiBookingInformation fiBookingInformation)
    {
        return fiBookingInformationMapper.selectFiBookingInformationList(fiBookingInformation);
    }


    /**
     * 分页模糊查询订舱单信息列表
     * @return 订舱单信息集合
     */
    @Override
    public Page selectFiBookingInformationListByLike(Query query)
    {
        FiBookingInformation fiBookingInformation =  BeanUtil.mapToBean(query.getCondition(), FiBookingInformation.class,false);
        query.setRecords(fiBookingInformationMapper.selectFiBookingInformationListByLike(query,fiBookingInformation));
        return query;
    }

    /**
     * 新增订舱单信息
     *
     * @param fiBookingInformation 订舱单信息信息
     * @return 结果
     */
    @Override
    public int insertFiBookingInformation(FiBookingInformation fiBookingInformation)
    {
        return fiBookingInformationMapper.insertFiBookingInformation(fiBookingInformation);
    }

    /**
     * 修改订舱单信息
     *
     * @param fiBookingInformation 订舱单信息信息
     * @return 结果
     */
    @Override
    public int updateFiBookingInformation(FiBookingInformation fiBookingInformation)
    {
        return fiBookingInformationMapper.updateFiBookingInformation(fiBookingInformation);
    }

    @Override
    public int updateFiBookingInformationByAssetCode(FiBookingInformation fiBookingInformation)
    {
        return fiBookingInformationMapper.updateFiBookingInformationByAssetCode(fiBookingInformation);
    }


    /**
     * 删除订舱单信息
     *
     * @param rowId 订舱单信息ID
     * @return 结果
     */
    public int deleteFiBookingInformationById(String rowId)
    {
        return fiBookingInformationMapper.deleteFiBookingInformationById( rowId);
    }


    /**
     * 批量删除订舱单信息对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiBookingInformationByIds(Integer[] rowIds)
    {
        return fiBookingInformationMapper.deleteFiBookingInformationByIds( rowIds);
    }

}
