package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.StoredCompareWithType;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 数据同比（发运类型同比） 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-10-08 10:04:31
 */
public interface StoredCompareWithTypeService extends IService<StoredCompareWithType> {
    /**
     * 查询数据同比（发运类型同比）信息
     *
     * @param rowId 数据同比（发运类型同比）ID
     * @return 数据同比（发运类型同比）信息
     */
    public StoredCompareWithType selectStoredCompareWithTypeById(Integer rowId);

    /**
     * 查询数据同比（发运类型同比）列表
     *
     * @param storedCompareWithType 数据同比（发运类型同比）信息
     * @return 数据同比（发运类型同比）集合
     */
    public List<StoredCompareWithType> selectStoredCompareWithTypeList(StoredCompareWithType storedCompareWithType);


    /**
     * 分页模糊查询数据同比（发运类型同比）列表
     * @return 数据同比（发运类型同比）集合
     */
    public Page selectStoredCompareWithTypeListByLike(Query query);



    /**
     * 新增数据同比（发运类型同比）
     *
     * @param storedCompareWithType 数据同比（发运类型同比）信息
     * @return 结果
     */
    public int insertStoredCompareWithType(StoredCompareWithType storedCompareWithType);

    /**
     * 修改数据同比（发运类型同比）
     *
     * @param storedCompareWithType 数据同比（发运类型同比）信息
     * @return 结果
     */
    public int updateStoredCompareWithType(StoredCompareWithType storedCompareWithType);

    /**
     * 删除数据同比（发运类型同比）
     *
     * @param rowId 数据同比（发运类型同比）ID
     * @return 结果
     */
    public int deleteStoredCompareWithTypeById(Integer rowId);

    /**
     * 批量删除数据同比（发运类型同比）
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteStoredCompareWithTypeByIds(Integer[] rowIds);

}

