package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.EfWarehouseDisposeinfoMapper;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseDisposeinfo;
import com.huazheng.tunny.ocean.service.EfWarehouseDisposeinfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("efWarehouseDisposeinfoService")
public class EfWarehouseDisposeinfoServiceImpl extends ServiceImpl<EfWarehouseDisposeinfoMapper, EfWarehouseDisposeinfo> implements EfWarehouseDisposeinfoService {

    @Autowired
    private EfWarehouseDisposeinfoMapper efWarehouseDisposeinfoMapper;

    public EfWarehouseDisposeinfoMapper getEfWarehouseDisposeinfoMapper() {
        return efWarehouseDisposeinfoMapper;
    }

    public void setEfWarehouseDisposeinfoMapper(EfWarehouseDisposeinfoMapper efWarehouseDisposeinfoMapper) {
        this.efWarehouseDisposeinfoMapper = efWarehouseDisposeinfoMapper;
    }

    /**
     * 查询融资处置信息信息
     *
     * @param rowId 融资处置信息ID
     * @return 融资处置信息信息
     */
    @Override
    public EfWarehouseDisposeinfo selectEfWarehouseDisposeinfoById(String rowId)
    {
        return efWarehouseDisposeinfoMapper.selectEfWarehouseDisposeinfoById(rowId);
    }

    /**
     * 查询融资处置信息列表
     *
     * @param efWarehouseDisposeinfo 融资处置信息信息
     * @return 融资处置信息集合
     */
    @Override
    public List<EfWarehouseDisposeinfo> selectEfWarehouseDisposeinfoList(EfWarehouseDisposeinfo efWarehouseDisposeinfo)
    {
        return efWarehouseDisposeinfoMapper.selectEfWarehouseDisposeinfoList(efWarehouseDisposeinfo);
    }


    /**
     * 分页模糊查询融资处置信息列表
     * @return 融资处置信息集合
     */
    @Override
    public Page selectEfWarehouseDisposeinfoListByLike(Query query)
    {
        EfWarehouseDisposeinfo efWarehouseDisposeinfo =  BeanUtil.mapToBean(query.getCondition(), EfWarehouseDisposeinfo.class,false);
        query.setRecords(efWarehouseDisposeinfoMapper.selectEfWarehouseDisposeinfoListByLike(query,efWarehouseDisposeinfo));
        return query;
    }

    /**
     * 新增融资处置信息
     *
     * @param efWarehouseDisposeinfo 融资处置信息信息
     * @return 结果
     */
    @Override
    public int insertEfWarehouseDisposeinfo(EfWarehouseDisposeinfo efWarehouseDisposeinfo)
    {
        return efWarehouseDisposeinfoMapper.insertEfWarehouseDisposeinfo(efWarehouseDisposeinfo);
    }

    /**
     * 修改融资处置信息
     *
     * @param efWarehouseDisposeinfo 融资处置信息信息
     * @return 结果
     */
    @Override
    public int updateEfWarehouseDisposeinfo(EfWarehouseDisposeinfo efWarehouseDisposeinfo)
    {
        return efWarehouseDisposeinfoMapper.updateEfWarehouseDisposeinfo(efWarehouseDisposeinfo);
    }


    /**
     * 删除融资处置信息
     *
     * @param rowId 融资处置信息ID
     * @return 结果
     */
    public int deleteEfWarehouseDisposeinfoById(String rowId)
    {
        return efWarehouseDisposeinfoMapper.deleteEfWarehouseDisposeinfoById( rowId);
    };


    /**
     * 批量删除融资处置信息对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfWarehouseDisposeinfoByIds(Integer[] rowIds)
    {
        return efWarehouseDisposeinfoMapper.deleteEfWarehouseDisposeinfoByIds( rowIds);
    }

}
