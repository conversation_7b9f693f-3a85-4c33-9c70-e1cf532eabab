package com.huazheng.tunny.ocean.controller;

import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.SubsidyManager;
import com.huazheng.tunny.ocean.service.SubsidyManagerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 补贴管理表
 *
 * <AUTHOR> code generator
 * @date 2024-07-25 10:46:40
 */
@Slf4j
@RestController
@RequestMapping("/subsidymanager")
public class SubsidyManagerController {

    @Autowired
    private SubsidyManagerService subsidyManagerService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  subsidyManagerService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return subsidyManagerService.selectSubsidyManagerListByLike(new Query<>(params));
    }

    /**
     * 信息
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Integer id) {
        SubsidyManager subsidyManager =subsidyManagerService.selectById(id);
        return new R<>(subsidyManager);
    }

    /**
     * 保存
     * @param subsidyManager
     * @return R
     */
    @PostMapping("/save")
    public R save(@RequestBody SubsidyManager subsidyManager) {
        return subsidyManagerService.insertSubsidyManager(subsidyManager);
    }

    /**
     * 修改
     * @param subsidyManager
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody SubsidyManager subsidyManager) {
        return subsidyManagerService.updateSubsidyManager(subsidyManager);
    }



    /**
     * 删除
     * @param id
     * @return R
     */
    @GetMapping("/del")
    public R delete(@RequestParam("id") Integer id) {
        int delInt = subsidyManagerService.deleteSubsidyManagerById(id);
        if(delInt <= 0){
            return new R<>(Boolean.FALSE);
        }
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<Integer> ids) {
        subsidyManagerService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<SubsidyManager> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = subsidyManagerService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<SubsidyManager> list = reader.readAll(SubsidyManager.class);
        subsidyManagerService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }
}
