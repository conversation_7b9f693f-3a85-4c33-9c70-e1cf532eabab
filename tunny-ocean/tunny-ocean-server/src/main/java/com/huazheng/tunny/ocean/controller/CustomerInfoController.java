package com.huazheng.tunny.ocean.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.util.StringUtils;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.vo.CustomerInfoVO;
import com.huazheng.tunny.ocean.mapper.SysUserMapper;
import com.huazheng.tunny.ocean.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * 客户信息主表
 *
 * <AUTHOR> code ocean
 * @date 2021-07-01 11:52:05
 */
@RestController
@RequestMapping("/customerinfo")
@Slf4j
public class CustomerInfoController {
    @Autowired
    private CustomerInfoService customerInfoService;
    @Autowired
    private SysNoConfigService sysNoConfigService;
    @Autowired
    private CustomerPlatformInfoService customerPlatformInfoService;
    @Autowired
    private BookingRequesheaderService bookingRequesheaderService;
    @Autowired
    private SysUserRoleService sysUserRoleService;
    @Autowired
    private FiBookingFinancingService fiBookingFinancingService;
    @Autowired
    private FiLoanRecordNewService fiLoanRecordNewService;
    @Autowired
    private FiExporfinancingApplyService fiExporfinancingApplyService;
    @Autowired
    private FiExporfinancingPayService fiExporfinancingPayService;
    @Autowired
    private FiExporfinancingRecsService fiExporfinancingRecsService;
    @Autowired
    private EfWarehousePayinfoService efWarehousePayinfoService;
    @Autowired
    private SysUserMapper sysUserMapper;

    @Value("${db.database}")
    private String database;

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  customerInfoService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return customerInfoService.selectCustomerInfoListByLike(new Query<>(params));
    }

    /**
     * 获取平台下订舱用户列表
     *
     * @param customerInfo
     * @return
     */
    @PostMapping("/getCustomerByPlatformCode")
    public List<CustomerInfo> getCustomerByPlatformCode(@RequestBody CustomerInfo customerInfo) {
        return customerInfoService.getCustomerByPlatformCode(customerInfo);
    }

    /**
     * 获取平台下市平台用户列表
     *
     * @param customerInfo
     * @return
     */
    @PostMapping("/getCityByPlatformCode")
    public List<CustomerInfo> getCityByPlatformCode(@RequestBody CustomerInfo customerInfo) {
        return customerInfoService.getCityByPlatformCode(customerInfo);
    }

    @GetMapping("/getCustomerTitle")
    public R getCustomerTitle(@RequestParam String type) {
        Map<String, String> map = new HashMap<String, String>();
        CustomerInfo info = new CustomerInfo();
        info.setCustomerCode(SecurityUtils.getUserInfo().getUserName());
        List<CustomerInfo> customerInfos = customerInfoService.selectCustomerInfoListBySocialUcCode2(info);
        if (customerInfos != null && customerInfos.size() > 0) {
            map.put("companyName", customerInfos.get(0).getCompanyName());
            map.put("socialUcCode", customerInfos.get(0).getSocialUcCode());
            if (customerInfos.get(0).getAddTime() != null) {
                long between = DateUtil.between(customerInfos.get(0).getAddTime(), new Date(), DateUnit.DAY);
                if (between >= 365 * 3) {
                    map.put("starLevel", "3");
                } else if (between >= 365 * 2) {
                    map.put("starLevel", "2");
                } else if (between >= 365) {
                    map.put("starLevel", "1");
                } else if (between < 365) {
                    map.put("starLevel", "0.5");
                }
            }
        }
        BigDecimal financingAmountUsd = BigDecimal.valueOf(0);
        BigDecimal financingAmountCny = BigDecimal.valueOf(0);
        BigDecimal financingAmountEur = BigDecimal.valueOf(0);
        BigDecimal financingAmountRub = BigDecimal.valueOf(0);

        BigDecimal repaymentAmountUsd = BigDecimal.valueOf(0);
        BigDecimal repaymentAmountCny = BigDecimal.valueOf(0);
        BigDecimal repaymentAmountEur = BigDecimal.valueOf(0);
        BigDecimal repaymentAmountRub = BigDecimal.valueOf(0);

        int financingNumUsd = 0;
        int financingNumCny = 0;
        int financingNumEur = 0;
        int financingNumRub = 0;

        int repaymentNumUsd = 0;
        int repaymentNumCny = 0;
        int repaymentNumEur = 0;
        int repaymentNumRub = 0;

        if (type != null && !"".equals(type)) {
            if ("fiBookingFinancing".equals(type)) {
                FiBookingFinancing fbf = new FiBookingFinancing();
                fbf.setEntCode(SecurityUtils.getUserInfo().getUserName());
                fbf.setDeleteFlag("N");
                List<FiBookingFinancing> fiBookingFinancings = fiBookingFinancingService.selectFiBookingFinancingList(fbf);
                if (fiBookingFinancings != null && fiBookingFinancings.size() > 0) {
                    for (FiBookingFinancing f : fiBookingFinancings
                    ) {
                        if (f.getPayAmount() != null) {
                            if ("UN_REPAY".equals(f.getAssetState()) || "PAY".equals(f.getAssetState()) || "REPAY".equals(f.getAssetState())) {
                                if ("USD".equals(f.getPayCurrency())) {
                                    financingAmountUsd = financingAmountUsd.add(f.getPayAmount());
                                    financingNumUsd++;
                                } else if ("CNY".equals(f.getPayCurrency())) {
                                    financingAmountCny = financingAmountCny.add(f.getPayAmount());
                                    financingNumCny++;
                                } else if ("EUR".equals(f.getPayCurrency())) {
                                    financingAmountEur = financingAmountEur.add(f.getPayAmount());
                                    financingNumEur++;
                                } else if ("RUB".equals(f.getPayCurrency())) {
                                    financingAmountRub = financingAmountRub.add(f.getPayAmount());
                                    financingNumRub++;
                                }
                            }

                            if ("UN_REPAY".equals(f.getAssetState()) || "PAY".equals(f.getAssetState())) {
                                if ("USD".equals(f.getPayCurrency())) {
                                    repaymentAmountUsd = repaymentAmountUsd.add(f.getPayAmount());
                                    repaymentNumUsd++;
                                } else if ("CNY".equals(f.getPayCurrency())) {
                                    repaymentAmountCny = repaymentAmountCny.add(f.getPayAmount());
                                    repaymentNumCny++;
                                } else if ("EUR".equals(f.getPayCurrency())) {
                                    repaymentAmountEur = repaymentAmountEur.add(f.getPayAmount());
                                    repaymentNumEur++;
                                } else if ("RUB".equals(f.getPayCurrency())) {
                                    repaymentAmountRub = repaymentAmountRub.add(f.getPayAmount());
                                    repaymentNumRub++;
                                }
                            }
                        }
                    }
                }
            } else if ("fiLoanRecordNew".equals(type)) {
                if (customerInfos != null && customerInfos.size() > 0) {
                    FiLoanRecordNew flr = new FiLoanRecordNew();
                    flr.setEntSocialCode(customerInfos.get(0).getSocialUcCode());
                    flr.setDeleteFlag("N");
                    List<FiLoanRecordNew> fiLoanRecordNews = fiLoanRecordNewService.selectFiLoanRecordNewList(flr);
                    for (FiLoanRecordNew f : fiLoanRecordNews
                    ) {
                        if (f.getAmount() != null) {
                            if (!"CORRECTION".equals(f.getState())) {
                                if ("USD".equals(f.getCurrency())) {
                                    financingAmountUsd = financingAmountUsd.add(f.getAmount());
                                    financingNumUsd++;
                                } else if ("CNY".equals(f.getCurrency())) {
                                    financingAmountCny = financingAmountCny.add(f.getAmount());
                                    financingNumCny++;
                                } else if ("EUR".equals(f.getCurrency())) {
                                    financingAmountEur = financingAmountEur.add(f.getAmount());
                                    financingNumEur++;
                                } else if ("RUB".equals(f.getCurrency())) {
                                    financingAmountRub = financingAmountRub.add(f.getAmount());
                                    financingNumRub++;
                                }
                            }
                            if ("OUTSTANDING".equals(f.getState()) || "PAY".equals(f.getState())) {
                                if ("USD".equals(f.getCurrency())) {
                                    repaymentAmountUsd = repaymentAmountUsd.add(f.getAmount());
                                    repaymentNumUsd++;
                                } else if ("CNY".equals(f.getCurrency())) {
                                    repaymentAmountCny = repaymentAmountCny.add(f.getAmount());
                                    repaymentNumCny++;
                                } else if ("EUR".equals(f.getCurrency())) {
                                    repaymentAmountEur = repaymentAmountEur.add(f.getAmount());
                                    repaymentNumEur++;
                                } else if ("RUB".equals(f.getCurrency())) {
                                    repaymentAmountRub = repaymentAmountRub.add(f.getAmount());
                                    repaymentNumRub++;
                                }
                            }
                        }
                    }
                }
            } else if ("fiExporfinancingApply".equals(type)) {
                if (customerInfos != null && customerInfos.size() > 0) {
                    FiExporfinancingApply apply = new FiExporfinancingApply();
                    apply.setEntCode(customerInfos.get(0).getSocialUcCode().substring(8, 17));
                    apply.setDeleteFlag("N");
                    List<FiExporfinancingApply> fiExporfinancingApplies = fiExporfinancingApplyService.selectFiExporfinancingApplyList(apply);
                    if (fiExporfinancingApplies != null && fiExporfinancingApplies.size() > 0) {
                        for (FiExporfinancingApply a : fiExporfinancingApplies
                        ) {
                            if (a.getAssetCode() != null && !"".equals(a.getAssetCode())) {
                                FiExporfinancingPay pay = new FiExporfinancingPay();
                                pay.setAssetCode(a.getAssetCode());
                                pay.setDeleteFlag("N");
                                List<FiExporfinancingPay> fiExporfinancingPays = fiExporfinancingPayService.selectFiExporfinancingPayList(pay);
                                if (fiExporfinancingPays != null && fiExporfinancingPays.size() > 0) {
                                    for (FiExporfinancingPay p : fiExporfinancingPays
                                    ) {
                                        if (p.getPayAmountDecimal() != null) {
                                            if ("USD".equals(a.getCurrency())) {
                                                financingAmountUsd = financingAmountUsd.add(p.getPayAmountDecimal());
                                                financingNumUsd++;

                                                repaymentAmountUsd = repaymentAmountUsd.add(p.getPayAmountDecimal());
                                                repaymentNumUsd++;
                                            } else if ("CNY".equals(a.getCurrency())) {
                                                financingAmountCny = financingAmountCny.add(p.getPayAmountDecimal());
                                                financingNumCny++;

                                                repaymentAmountCny = repaymentAmountCny.add(p.getPayAmountDecimal());
                                                repaymentNumCny++;
                                            } else if ("EUR".equals(a.getCurrency())) {
                                                financingAmountEur = financingAmountEur.add(p.getPayAmountDecimal());
                                                financingNumEur++;

                                                repaymentAmountEur = repaymentAmountEur.add(p.getPayAmountDecimal());
                                                repaymentNumEur++;
                                            } else if ("RUB".equals(a.getCurrency())) {
                                                financingAmountRub = financingAmountRub.add(p.getPayAmountDecimal());
                                                financingNumRub++;

                                                repaymentAmountRub = repaymentAmountRub.add(p.getPayAmountDecimal());
                                                repaymentNumRub++;
                                            }
                                        }
                                    }
                                }
                                FiExporfinancingRecs recs = new FiExporfinancingRecs();
                                recs.setAssetCode(a.getAssetCode());
                                recs.setDeleteFlag("N");
                                List<FiExporfinancingRecs> fiExporfinancingRecs = fiExporfinancingRecsService.selectFiExporfinancingRecsList(recs);
                                if (fiExporfinancingRecs != null && fiExporfinancingRecs.size() > 0) {
                                    for (FiExporfinancingRecs r : fiExporfinancingRecs
                                    ) {
                                        if (r.getRecAmountDecimal() != null) {
                                            if ("CLEAR".equals(a.getAssetState())) {
                                                if ("USD".equals(a.getCurrency())) {
                                                    repaymentAmountUsd = repaymentAmountUsd.subtract(r.getRecAmountDecimal());
                                                    repaymentNumUsd--;
                                                } else if ("CNY".equals(a.getCurrency())) {
                                                    repaymentAmountCny = repaymentAmountCny.subtract(r.getRecAmountDecimal());
                                                    repaymentNumCny--;
                                                } else if ("EUR".equals(a.getCurrency())) {
                                                    repaymentAmountEur = repaymentAmountEur.subtract(r.getRecAmountDecimal());
                                                    repaymentNumEur--;
                                                } else if ("RUB".equals(a.getCurrency())) {
                                                    repaymentAmountRub = repaymentAmountRub.subtract(r.getRecAmountDecimal());
                                                    repaymentNumRub--;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } else if ("efWarehouseApply".equals(type)) {
                if (StrUtil.isNotEmpty(SecurityUtils.getUserInfo().getUserName())) {
                    EfWarehousePayinfo efWarehousePayinfo = new EfWarehousePayinfo();
                    efWarehousePayinfo.setAddWho(SecurityUtils.getUserInfo().getUserName());
                    final List<EfWarehousePayinfo> EfWarehousePayinfo = efWarehousePayinfoService.selectEfWarehousePaySum(efWarehousePayinfo);
                    if (CollUtil.isNotEmpty(EfWarehousePayinfo)) {
                        for (EfWarehousePayinfo ef : EfWarehousePayinfo
                        ) {
                            if (ef.getActualLoanAmountCny() != null) {
                                if ("CNY".equals(ef.getActualLoanCurrency())) {
                                    financingAmountCny = financingAmountCny.add(ef.getActualLoanAmountCny());
                                    financingNumCny++;
                                } else if ("EUR".equals(ef.getActualLoanCurrency())) {
                                    financingAmountEur = financingAmountEur.add(ef.getActualLoanAmountCny());
                                    financingNumEur++;
                                } else if ("RUB".equals(ef.getActualLoanCurrency())) {
                                    financingAmountRub = financingAmountRub.add(ef.getActualLoanAmountCny());
                                    financingNumRub++;
                                } else if ("USD".equals(ef.getActualLoanCurrency())) {
                                    financingAmountUsd = financingAmountUsd.add(ef.getActualLoanAmountCny());
                                    financingNumUsd++;
                                }
                            }
                        }
                    }
                }
            }

        } else {
            FiBookingFinancing fbf = new FiBookingFinancing();
            fbf.setEntCode(SecurityUtils.getUserInfo().getUserName());
            fbf.setDeleteFlag("N");
            List<FiBookingFinancing> fiBookingFinancings = fiBookingFinancingService.selectFiBookingFinancingList(fbf);
            if (fiBookingFinancings != null && fiBookingFinancings.size() > 0) {
                for (FiBookingFinancing f : fiBookingFinancings
                ) {
                    if (f.getPayAmount() != null) {
                        if ("UN_REPAY".equals(f.getAssetState()) || "PAY".equals(f.getAssetState()) || "REPAY".equals(f.getAssetState())) {
                            if ("USD".equals(f.getPayCurrency())) {
                                financingAmountUsd = financingAmountUsd.add(f.getPayAmount());
                                financingNumUsd++;
                            } else if ("CNY".equals(f.getPayCurrency())) {
                                financingAmountCny = financingAmountCny.add(f.getPayAmount());
                                financingNumCny++;
                            } else if ("EUR".equals(f.getPayCurrency())) {
                                financingAmountEur = financingAmountEur.add(f.getPayAmount());
                                financingNumEur++;
                            } else if ("RUB".equals(f.getPayCurrency())) {
                                financingAmountRub = financingAmountRub.add(f.getPayAmount());
                                financingNumRub++;
                            }
                        }

                        if ("UN_REPAY".equals(f.getAssetState()) || "PAY".equals(f.getAssetState())) {
                            if ("USD".equals(f.getPayCurrency())) {
                                repaymentAmountUsd = repaymentAmountUsd.add(f.getPayAmount());
                                repaymentNumUsd++;
                            } else if ("CNY".equals(f.getPayCurrency())) {
                                repaymentAmountCny = repaymentAmountCny.add(f.getPayAmount());
                                repaymentNumCny++;
                            } else if ("EUR".equals(f.getPayCurrency())) {
                                repaymentAmountEur = repaymentAmountEur.add(f.getPayAmount());
                                repaymentNumEur++;
                            } else if ("RUB".equals(f.getPayCurrency())) {
                                repaymentAmountRub = repaymentAmountRub.add(f.getPayAmount());
                                repaymentNumRub++;
                            }
                        }
                    }
                }
            }
            if (customerInfos != null && customerInfos.size() > 0) {
                FiLoanRecordNew flr = new FiLoanRecordNew();
                flr.setEntSocialCode(customerInfos.get(0).getSocialUcCode());
                flr.setDeleteFlag("N");
                List<FiLoanRecordNew> fiLoanRecordNews = fiLoanRecordNewService.selectFiLoanRecordNewList(flr);
                for (FiLoanRecordNew f : fiLoanRecordNews
                ) {
                    if (f.getAmount() != null) {
                        if (!"CORRECTION".equals(f.getState())) {
                            if ("USD".equals(f.getCurrency())) {
                                financingAmountUsd = financingAmountUsd.add(f.getAmount());
                                financingNumUsd++;
                            } else if ("CNY".equals(f.getCurrency())) {
                                financingAmountCny = financingAmountCny.add(f.getAmount());
                                financingNumCny++;
                            } else if ("EUR".equals(f.getCurrency())) {
                                financingAmountEur = financingAmountEur.add(f.getAmount());
                                financingNumEur++;
                            } else if ("RUB".equals(f.getCurrency())) {
                                financingAmountRub = financingAmountRub.add(f.getAmount());
                                financingNumRub++;
                            }
                        }
                        if ("OUTSTANDING".equals(f.getState()) || "PAY".equals(f.getState())) {
                            if ("USD".equals(f.getCurrency())) {
                                repaymentAmountUsd = repaymentAmountUsd.add(f.getAmount());
                                repaymentNumUsd++;
                            } else if ("CNY".equals(f.getCurrency())) {
                                repaymentAmountCny = repaymentAmountCny.add(f.getAmount());
                                repaymentNumCny++;
                            } else if ("EUR".equals(f.getCurrency())) {
                                repaymentAmountEur = repaymentAmountEur.add(f.getAmount());
                                repaymentNumEur++;
                            } else if ("RUB".equals(f.getCurrency())) {
                                repaymentAmountRub = repaymentAmountRub.add(f.getAmount());
                                repaymentNumRub++;
                            }
                        }
                    }
                }
            }
            if (customerInfos != null && customerInfos.size() > 0) {
                FiExporfinancingApply apply = new FiExporfinancingApply();
                apply.setEntCode(customerInfos.get(0).getSocialUcCode().substring(8, 17));
                apply.setDeleteFlag("N");
                List<FiExporfinancingApply> fiExporfinancingApplies = fiExporfinancingApplyService.selectFiExporfinancingApplyList(apply);
                if (fiExporfinancingApplies != null && fiExporfinancingApplies.size() > 0) {
                    for (FiExporfinancingApply a : fiExporfinancingApplies
                    ) {
                        if (a.getAssetCode() != null && !"".equals(a.getAssetCode())) {
                            FiExporfinancingPay pay = new FiExporfinancingPay();
                            pay.setAssetCode(a.getAssetCode());
                            pay.setDeleteFlag("N");
                            List<FiExporfinancingPay> fiExporfinancingPays = fiExporfinancingPayService.selectFiExporfinancingPayList(pay);
                            if (fiExporfinancingPays != null && fiExporfinancingPays.size() > 0) {
                                for (FiExporfinancingPay p : fiExporfinancingPays
                                ) {
                                    if (p.getPayAmountDecimal() != null) {
                                        if ("USD".equals(a.getCurrency())) {
                                            financingAmountUsd = financingAmountUsd.add(p.getPayAmountDecimal());
                                            financingNumUsd++;

                                            repaymentAmountUsd = repaymentAmountUsd.add(p.getPayAmountDecimal());
                                            repaymentNumUsd++;
                                        } else if ("CNY".equals(a.getCurrency())) {
                                            financingAmountCny = financingAmountCny.add(p.getPayAmountDecimal());
                                            financingNumCny++;

                                            repaymentAmountCny = repaymentAmountCny.add(p.getPayAmountDecimal());
                                            repaymentNumCny++;
                                        } else if ("EUR".equals(a.getCurrency())) {
                                            financingAmountEur = financingAmountEur.add(p.getPayAmountDecimal());
                                            financingNumEur++;

                                            repaymentAmountEur = repaymentAmountEur.add(p.getPayAmountDecimal());
                                            repaymentNumEur++;
                                        } else if ("RUB".equals(a.getCurrency())) {
                                            financingAmountRub = financingAmountRub.add(p.getPayAmountDecimal());
                                            financingNumRub++;

                                            repaymentAmountRub = repaymentAmountRub.add(p.getPayAmountDecimal());
                                            repaymentNumRub++;
                                        }
                                    }
                                }
                            }
                            FiExporfinancingRecs recs = new FiExporfinancingRecs();
                            recs.setAssetCode(a.getAssetCode());
                            recs.setDeleteFlag("N");
                            List<FiExporfinancingRecs> fiExporfinancingRecs = fiExporfinancingRecsService.selectFiExporfinancingRecsList(recs);
                            if (fiExporfinancingRecs != null && fiExporfinancingRecs.size() > 0) {
                                for (FiExporfinancingRecs r : fiExporfinancingRecs
                                ) {
                                    if (r.getRecAmountDecimal() != null) {
                                        if ("CLEAR".equals(a.getAssetState())) {
                                            if ("USD".equals(a.getCurrency())) {
                                                repaymentAmountUsd = repaymentAmountUsd.subtract(r.getRecAmountDecimal());
                                                repaymentNumUsd--;
                                            } else if ("CNY".equals(a.getCurrency())) {
                                                repaymentAmountCny = repaymentAmountCny.subtract(r.getRecAmountDecimal());
                                                repaymentNumCny--;
                                            } else if ("EUR".equals(a.getCurrency())) {
                                                repaymentAmountEur = repaymentAmountEur.subtract(r.getRecAmountDecimal());
                                                repaymentNumEur--;
                                            } else if ("RUB".equals(a.getCurrency())) {
                                                repaymentAmountRub = repaymentAmountRub.subtract(r.getRecAmountDecimal());
                                                repaymentNumRub--;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (StrUtil.isNotEmpty(SecurityUtils.getUserInfo().getUserName())) {
                EfWarehousePayinfo efWarehousePayinfo = new EfWarehousePayinfo();
                efWarehousePayinfo.setAddWho(SecurityUtils.getUserInfo().getUserName());
                final List<EfWarehousePayinfo> EfWarehousePayinfo = efWarehousePayinfoService.selectEfWarehousePaySum(efWarehousePayinfo);
                if (CollUtil.isNotEmpty(EfWarehousePayinfo)) {
                    for (EfWarehousePayinfo ef : EfWarehousePayinfo
                    ) {
                        if (ef.getActualLoanAmountCny() != null) {
                            if ("CNY".equals(ef.getActualLoanCurrency())) {
                                financingAmountCny = financingAmountCny.add(ef.getActualLoanAmountCny());
                                financingNumCny++;
                            } else if ("EUR".equals(ef.getActualLoanCurrency())) {
                                financingAmountEur = financingAmountEur.add(ef.getActualLoanAmountCny());
                                financingNumEur++;
                            } else if ("RUB".equals(ef.getActualLoanCurrency())) {
                                financingAmountRub = financingAmountRub.add(ef.getActualLoanAmountCny());
                                financingNumRub++;
                            } else if ("USD".equals(ef.getActualLoanCurrency())) {
                                financingAmountUsd = financingAmountUsd.add(ef.getActualLoanAmountCny());
                                financingNumUsd++;
                            }
                        }
                    }
                }
            }
        }

        map.put("financingAmountUsd", String.valueOf(financingAmountUsd));
        map.put("financingAmountCny", String.valueOf(financingAmountCny));
        map.put("financingAmountEur", String.valueOf(financingAmountEur));
        map.put("financingAmountRub", String.valueOf(financingAmountRub));

        map.put("repaymentAmountUsd", String.valueOf(repaymentAmountUsd));
        map.put("repaymentAmountCny", String.valueOf(repaymentAmountCny));
        map.put("repaymentAmountEur", String.valueOf(repaymentAmountEur));
        map.put("repaymentAmountRub", String.valueOf(repaymentAmountRub));

        map.put("financingNumUsd", String.valueOf(financingNumUsd));
        map.put("financingNumCny", String.valueOf(financingNumCny));
        map.put("financingNumEur", String.valueOf(financingNumEur));
        map.put("financingNumRub", String.valueOf(financingNumRub));

        map.put("repaymentNumUsd", String.valueOf(repaymentNumUsd));
        map.put("repaymentNumCny", String.valueOf(repaymentNumCny));
        map.put("repaymentNumEur", String.valueOf(repaymentNumEur));
        map.put("repaymentNumRub", String.valueOf(repaymentNumRub));
        return new R<>(200, Boolean.TRUE, map);
    }

    @GetMapping("/listXlk")
    public R listXlk(@RequestParam Map<String, Object> params) {
        return customerInfoService.listXlk(params);
    }

    @GetMapping("/list")
    public R list(CustomerInfo customerInfo) {
        customerInfo.setDeleteFlag("N");
        EntityWrapper<CustomerInfo> wrapper = new EntityWrapper<CustomerInfo>(customerInfo);
        List<CustomerInfo> customerInfos = customerInfoService.selectList(wrapper);
        return new R<>(200, Boolean.TRUE, customerInfos);
    }

    @PostMapping("/list1")
    public R list1(@RequestBody CustomerInfo customerInfo) {
        return new R(customerInfoService.selectCustomerInfoList(customerInfo));
    }

    /*
     * 信息
     * @param rowId
     * @return R
     */
    @PostMapping("/getDetail")
    public R info(@RequestBody CustomerPlatformInfo info) {
        CustomerInfo customerInfo = customerInfoService.selectCustomegenNorInfoById(info);
        return new R<>(customerInfo);
    }

    /*
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        CustomerInfo customerInfo = customerInfoService.selectCustomegenNorInfoById1(rowId);
        return new R<>(customerInfo);
    }

    @GetMapping("/listCustomerInfo")
    public R listCustomerInfo(@RequestParam Map<String, Object> map) {
        List<Map<String, Object>> mapList = customerInfoService.listCustomerInfo(map);
        return new R<>(mapList);
    }

    /*
     * 登录所用
     * @param rowId
     * @return R
     */
    @GetMapping("/userLogin")
    public R userLogin(CustomerInfo customerInfo) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        SysUser sysUser = new SysUser();
        sysUser.setUsername(customerInfo.getCustomerCode());
        sysUser.setDelFlag("1");
        sysUser.setUserFlag("1");
        List<SysUser> sysUsers = bookingRequesheaderService.selectUserList(sysUser);
        if (sysUsers != null && !sysUsers.isEmpty()) {
            //adminRemark 取值为0的话是二级子账号，则取createBy进行下方的校验
            String adminRemark = sysUsers.get(0).getAdminRemark();
            //一级账号 customerCode+platformCode确定账号有效期-customer_platform_info ，二级账号直接校验账号有效期-sys_user
            if (StringUtils.isNotEmpty(adminRemark)) {//二级账号
                customerInfo.setCustomerCode(sysUsers.get(0).getPlatformCode());
                Date passwordEndtime = sysUsers.get(0).getPasswordEndtime();
                if (passwordEndtime != null) {
                    boolean before = passwordEndtime.before(new Date());
                    if (before == true) {
                        //账号有效期<当前日期
                        sysUser.setDelFlag("0");
                        sysUser.setUserFlag("0");
                        bookingRequesheaderService.updateSysUser(sysUser);
                        return R.error("该账号已过有效期，请联系市平台人员维护！");
                    }
                }
            } else {
                //管理账号 有效期校验
                CustomerPlatformInfo customerPlatformInfo = new CustomerPlatformInfo();
                customerPlatformInfo.setCustomerCode(customerInfo.getCustomerCode());
                customerPlatformInfo.setPlatformCode(customerInfo.getPlatformCode());
                customerPlatformInfo.setDeleteFlag("N");
                customerPlatformInfo.setAuditStatus("2");
                List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoService.selectCustomerPlatformInfoList(customerPlatformInfo);
                if (customerPlatformInfos != null && !customerPlatformInfos.isEmpty()) {
                    Date accountExpireDate = customerPlatformInfos.get(0).getAccountExpireDate();
                    boolean before = accountExpireDate.before(new Date());
                    if (before == true) {
                        //账号有效期<当前日期
                        customerPlatformInfo.setDeleteFlag("Y");
                        customerPlatformInfo.setDeleteWho(userInfo.getUserName());
                        customerPlatformInfo.setDeleteWhoName(userInfo.getRealName());
                        customerPlatformInfo.setDeleteTime(new Date());
                        return R.error("该账号已过有效期，请联系市平台人员维护！");
                    }
                } else {
                    return R.error("当前账号不存在,请先注册！");
                }
            }
            String remark = sysUsers.get(0).getRemark();
            if (StrUtil.isNotEmpty(remark)) {
                if ("1".equals(remark) || "2".equals(remark)) {
                    //管理平台
                    if (!"1".equals(customerInfo.getResveredField01())) {

                        return R.error("登录账号对应平台不匹配，请确认！");
                    }
                } else {
                    //订舱平台
                    if (!"0".equals(customerInfo.getResveredField01())) {
                        return R.error("登录账号对应平台不匹配，请确认！");
                    }
                }
            } else {
                if (!"admin".equals(sysUsers.get(0).getUsername()) && !"bookadmin".equals(sysUsers.get(0).getUsername())) {
                    return R.error("用户平台标识未设置，请确认");
                }
            }

        } else {
            return R.error("当前账号不存在,请先注册！");
        }

        List<CustomerInfo> customerInfos = customerInfoService.selectCustomerInfoList1(customerInfo);
        return R.success(customerInfos);
    }


    /**
     * 查询用户平台信息
     *
     * @param customerInfo
     * @return List<CustomerInfo>
     */
    @PostMapping("/platformInfo")
    @Transactional(rollbackFor = Exception.class)
    public List<CustomerInfo> platformInfo(@RequestBody CustomerInfo customerInfo) {
        return customerInfoService.selectCustomerInfoList1(customerInfo);
    }


    /**
     * 新增省平台、市平台管理员 等账号
     *
     * @param customerInfo
     * @return R
     */
    @PostMapping("/save")
    @Transactional(rollbackFor = Exception.class)
    public R save(@RequestBody CustomerInfo customerInfo) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        SysUser sysUser = new SysUser();
        String mp = "";
        if ("1".equals(customerInfo.getCustomerFlag())) {//市平台
            mp = sysNoConfigService.genNo("MC");

            sysUser.setRemark("1");
        } else if ("2".equals(customerInfo.getCustomerFlag())) {
            mp = sysNoConfigService.genNo("MP");//省平台
            sysUser.setRemark("2");
        }
        SysUser user = new SysUser();
        user.setDelFlag("1");
        user.setUsername(mp);
        user.setDatabase(database);
        List<SysUser> userList = sysUserMapper.selectUserList(user);
        if (!userList.isEmpty()) {
            return R.error("此用户名已存在,请重新输入用户名！");
        }
        customerInfo.setCustomerCode(mp);
        customerInfo.setAddWho(userInfo.getUserName());
        customerInfo.setAddWhoName(userInfo.getRealName());
        customerInfo.setAddTime(new Date());
        customerInfo.setDeleteFlag("N");

        CustomerPlatformInfo customerPlatformInfo = new CustomerPlatformInfo();
        customerPlatformInfo.setRowId(UUID.randomUUID().toString());
        customerPlatformInfo.setAddWho(userInfo.getUserName());
        customerPlatformInfo.setAddWhoName(userInfo.getRealName());
        customerPlatformInfo.setAddTime(new Date());
        customerPlatformInfo.setDeleteFlag("N");
        customerPlatformInfo.setCustomerCode(mp);
        customerPlatformInfo.setContactPerson(customerInfo.getContactPerson());
        customerPlatformInfo.setContactNo(customerInfo.getContactTell());
        customerPlatformInfo.setEmail(customerInfo.getEmail());
        customerPlatformInfo.setRemarksCode(customerInfo.getRemarksCode());
        customerPlatformInfo.setAuditStatus("2");
        customerPlatformInfo.setAccountExpireDate(customerInfo.getAccountExpireDate());
        customerPlatformInfo.setPictureName(customerInfo.getPictureName());
        customerPlatformInfo.setPictureUrl(customerInfo.getPictureUrl());
        if ("1".equals(customerInfo.getCustomerFlag())) {//市平台

            customerPlatformInfo.setResveredField04(customerInfo.getCompanyName());
            customerPlatformInfo.setPlatformCode(mp);
            //新增市平台 有三种身份 admin、省平台操作员、省平台管理员。操作员查出它的管理员信息同步至数据库、admin空着
            SysUser sysUser1 = new SysUser();
            sysUser1.setUsername(userInfo.getUserName());
            List<SysUser> sysUsers = bookingRequesheaderService.selectUserList(sysUser1);
            String wxOpenid = sysUsers.get(0).getWxOpenid();
            if ("8".equals(wxOpenid)) {//省平台操作员
                EntityWrapper<CustomerInfo> customerWrapper = new EntityWrapper<>();
                customerWrapper.eq("customer_code", sysUsers.get(0).getCreateBy());
                CustomerInfo customerInfo1 = customerInfoService.selectOne(customerWrapper);
                customerPlatformInfo.setPlatformCodePro(customerInfo1.getCustomerCode());
                customerPlatformInfo.setResveredField03(customerInfo1.getCompanyName());
            } else {//省平台管理员 和 admin
                customerPlatformInfo.setPlatformCodePro(sysUsers.get(0).getUsername());
                customerPlatformInfo.setResveredField03(sysUsers.get(0).getUserRealname());
            }
        } else {//省平台
            customerPlatformInfo.setPlatformCodePro(mp);
            customerPlatformInfo.setResveredField03(customerInfo.getCompanyName());
        }
        customerPlatformInfoService.insertCustomerPlatformInfo1(customerPlatformInfo);
        sysUser.setUsername(mp);
        sysUser.setPassword(SecureUtil.md5("Qlh_dc@2023"));
        sysUser.setDelFlag("1");
        sysUser.setUserFlag("1");
        sysUser.setCreateTime(new Date());
        sysUser.setEmpno(mp);
        sysUser.setWxOpenid(customerInfo.getWxOpenid());
        sysUser.setQqOpenid(customerInfo.getQqOpenid());
        sysUser.setCreateBy(userInfo.getUserName());
        if (!customerInfo.getCompanyName().isEmpty()) {
            sysUser.setUserRealname(customerInfo.getCompanyName());
            // 公司名称作为平台名称
            sysUser.setPlatformName(customerInfo.getCompanyName());
        }
        if (StringUtils.isNotEmpty(mp)) {
            sysUser.setPlatformCode(mp);
        }

        bookingRequesheaderService.insertUser(sysUser);
        customerInfoService.insertCustomerInfo(customerInfo);
        return new R<>(200, Boolean.TRUE, mp);
    }

    /**
     * 客户、市平台注册
     *
     * @param customerInfo
     * @return R
     */
    @PostMapping("/cusRegister")
    public R cusRegister(@RequestBody CustomerInfoVO customerInfo) {
        return new R(customerInfoService.cusRegister(customerInfo));
    }

    /**
     * 客户注册时校验以前是否注册过
     *
     * @param code
     * @return R
     */
    @PostMapping("/isHaveMe")
    public R isHaveMe(String code) {
        CustomerInfo customerInfo = new CustomerInfo();
        customerInfo.setSocialUcCode(code);
        return new R(customerInfoService.isHaveMe(customerInfo));
    }

    /**
     * 修改
     *
     * @param customerInfo
     * @return R
     */
    @PutMapping
    public R update(@RequestBody CustomerInfo customerInfo) {
        customerInfoService.updateCustomerInfo(customerInfo);
        return new R<>(Boolean.TRUE);
    }


    /**
     * 删除
     *
     * @param customerInfo
     * @return R
     */
    @GetMapping("/delete")
    @Transactional(rollbackFor = Exception.class)
    public R delete(CustomerInfo customerInfo) {
        if (customerInfo.getCustomerCode().isEmpty()) {
            return new R<>(500, Boolean.FALSE, "请传入用户编码");
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        customerInfo.setDeleteWho(userInfo.getUserName());
        customerInfo.setDeleteFlag("Y");
        customerInfo.setDeleteTime(new Date());
        customerInfo.setDeleteWhoName(userInfo.getRealName());
        customerInfoService.deleteCustomerInfoById(customerInfo);
        CustomerPlatformInfo platInfo = new CustomerPlatformInfo();
        platInfo.setCustomerCode(customerInfo.getCustomerCode());
        platInfo.setDeleteWho(userInfo.getUserName());
        platInfo.setDeleteFlag("Y");
        platInfo.setDeleteTime(new Date());
        platInfo.setDeleteWhoName(userInfo.getRealName());
        customerPlatformInfoService.updateCustomerPlatformInfoByNo(platInfo);
        SysUser sysUser = new SysUser();
        sysUser.setUsername(customerInfo.getCustomerCode());
        List<SysUser> sysUsers = bookingRequesheaderService.selectUserList(sysUser);
        if (sysUsers != null && sysUsers.size() != 0) {
            Integer userId = sysUsers.get(0).getUserId();
            SysUser sysUser1 = new SysUser();
            sysUser1.setUserId(userId);
            Boolean isTrue = bookingRequesheaderService.deleteUserById(sysUser1);
            if (isTrue = false) {
                return new R<>(500, Boolean.FALSE, "删除主数据库源失败");
            }
        }
        return new R<>(200, Boolean.TRUE, "删除成功");
    }

    /**
     * 批量删除
     *
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody List<String> rowIds) {
        customerInfoService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 导出EXCEL
     *
     * @return
     */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<CustomerInfo> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = customerInfoService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }


    /**
     * 导入EXCEL
     * <p>
     *
     * @param titleStr
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<CustomerInfo> list = reader.readAll(CustomerInfo.class);
        customerInfoService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }

    //审核通过后，调用此接口新增角色用户关系
    @GetMapping("/addRole")
    public R addRole(CustomerInfo customerInfo) {
        SysUser sysUser = new SysUser();
        sysUser.setUsername(customerInfo.getCustomerCode());
        List<SysUser> sysUsers = bookingRequesheaderService.selectUserList(sysUser);
        if (sysUsers.isEmpty()) {
            return new R<>(500, Boolean.FALSE, "该账号没有数据信息");
        } else {
            SysUserRole sysUserRole = new SysUserRole();
            sysUserRole.setUserId(sysUsers.get(0).getUserId());
            if ("1".equals(customerInfo.getCustomerFlag())) {
                //市平台
                sysUserRole.setRoleId(2);
            } else {
                //省平台
                sysUserRole.setRoleId(7);
            }
            sysUserRoleService.insertSysUserRole(sysUserRole);
        }
        return new R<>(200, Boolean.TRUE);
    }

    @PostMapping("/updateCusInfo")
    public R updateCusInfo(@RequestBody CustomerInfo customerInfo) {
        customerInfoService.updateCustomerInfo(customerInfo);
        CustomerPlatformInfo info = new CustomerPlatformInfo();
        info.setCustomerCode(customerInfo.getCustomerCode());
        info.setPictureName(customerInfo.getPictureName());
        info.setPictureUrl(customerInfo.getPictureUrl());
        info.setPictureSize(customerInfo.getPictureSize());
        info.setContactPerson(customerInfo.getResveredField01());
        info.setContactNo(customerInfo.getResveredField02());
        customerPlatformInfoService.updateCustomerPlatformInfoByNo(info);

        return new R<>(Boolean.TRUE);
    }

    /**
     * 获取小客户列表
     *
     * @Param: customerInfo
     * @Return:
     * @Author: zhr
     * @Date: 2024/6/21 下午4:24
     **/
    @PostMapping("/getUserWithMiniPlatform")
    public R getUserWithMiniPlatform(@RequestBody CustomerInfo customerInfo) {
        return customerInfoService.getUserWithMiniPlatform(customerInfo);
    }
}
