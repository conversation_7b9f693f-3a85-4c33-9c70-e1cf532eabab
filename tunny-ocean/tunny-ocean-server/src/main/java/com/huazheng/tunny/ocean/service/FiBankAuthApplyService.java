package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FiBankAuthApply;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 经营信息授权-银行申请表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-03-02 15:06:09
 */
public interface FiBankAuthApplyService extends IService<FiBankAuthApply> {
    /**
     * 查询经营信息授权-银行申请表信息
     *
     * @param rowId 经营信息授权-银行申请表ID
     * @return 经营信息授权-银行申请表信息
     */
    public FiBankAuthApply selectFiBankAuthApplyById(String rowId);

    /**
     * 查询经营信息授权-银行申请表列表
     *
     * @param fiBankAuthApply 经营信息授权-银行申请表信息
     * @return 经营信息授权-银行申请表集合
     */
    public List<FiBankAuthApply> selectFiBankAuthApplyList(FiBankAuthApply fiBankAuthApply);


    /**
     * 分页模糊查询经营信息授权-银行申请表列表
     * @return 经营信息授权-银行申请表集合
     */
    public Page selectFiBankAuthApplyListByLike(Query query);



    /**
     * 新增经营信息授权-银行申请表
     *
     * @param fiBankAuthApply 经营信息授权-银行申请表信息
     * @return 结果
     */
    public int insertFiBankAuthApply(FiBankAuthApply fiBankAuthApply);

    /**
     * 修改经营信息授权-银行申请表
     *
     * @param fiBankAuthApply 经营信息授权-银行申请表信息
     * @return 结果
     */
    public int updateFiBankAuthApply(FiBankAuthApply fiBankAuthApply);

    /**
     * 删除经营信息授权-银行申请表
     *
     * @param rowId 经营信息授权-银行申请表ID
     * @return 结果
     */
    public int deleteFiBankAuthApplyById(String rowId);

    /**
     * 批量删除经营信息授权-银行申请表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiBankAuthApplyByIds(Integer[] rowIds);

}

