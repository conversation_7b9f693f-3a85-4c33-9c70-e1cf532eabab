package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FiPledgeIncreaseMapper;
import com.huazheng.tunny.ocean.api.entity.FiPledgeIncrease;
import com.huazheng.tunny.ocean.service.FiPledgeIncreaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fiPledgeIncreaseService")
public class FiPledgeIncreaseServiceImpl extends ServiceImpl<FiPledgeIncreaseMapper, FiPledgeIncrease> implements FiPledgeIncreaseService {

    @Autowired
    private FiPledgeIncreaseMapper fiPledgeIncreaseMapper;

    public FiPledgeIncreaseMapper getFiPledgeIncreaseMapper() {
        return fiPledgeIncreaseMapper;
    }

    public void setFiPledgeIncreaseMapper(FiPledgeIncreaseMapper fiPledgeIncreaseMapper) {
        this.fiPledgeIncreaseMapper = fiPledgeIncreaseMapper;
    }

    /**
     * 查询仓单质押补充申请表信息
     *
     * @param rowId 仓单质押补充申请表ID
     * @return 仓单质押补充申请表信息
     */
    @Override
    public FiPledgeIncrease selectFiPledgeIncreaseById(String rowId)
    {
        return fiPledgeIncreaseMapper.selectFiPledgeIncreaseById(rowId);
    }

    /**
     * 查询仓单质押补充申请表列表
     *
     * @param fiPledgeIncrease 仓单质押补充申请表信息
     * @return 仓单质押补充申请表集合
     */
    @Override
    public List<FiPledgeIncrease> selectFiPledgeIncreaseList(FiPledgeIncrease fiPledgeIncrease)
    {
        return fiPledgeIncreaseMapper.selectFiPledgeIncreaseList(fiPledgeIncrease);
    }


    /**
     * 分页模糊查询仓单质押补充申请表列表
     * @return 仓单质押补充申请表集合
     */
    @Override
    public Page selectFiPledgeIncreaseListByLike(Query query)
    {
        FiPledgeIncrease fiPledgeIncrease =  BeanUtil.mapToBean(query.getCondition(), FiPledgeIncrease.class,false);
        query.setRecords(fiPledgeIncreaseMapper.selectFiPledgeIncreaseListByLike(query,fiPledgeIncrease));
        return query;
    }

    /**
     * 新增仓单质押补充申请表
     *
     * @param fiPledgeIncrease 仓单质押补充申请表信息
     * @return 结果
     */
    @Override
    public int insertFiPledgeIncrease(FiPledgeIncrease fiPledgeIncrease)
    {
        return fiPledgeIncreaseMapper.insertFiPledgeIncrease(fiPledgeIncrease);
    }

    /**
     * 修改仓单质押补充申请表
     *
     * @param fiPledgeIncrease 仓单质押补充申请表信息
     * @return 结果
     */
    @Override
    public int updateFiPledgeIncrease(FiPledgeIncrease fiPledgeIncrease)
    {
        return fiPledgeIncreaseMapper.updateFiPledgeIncrease(fiPledgeIncrease);
    }


    /**
     * 删除仓单质押补充申请表
     *
     * @param rowId 仓单质押补充申请表ID
     * @return 结果
     */
    public int deleteFiPledgeIncreaseById(String rowId)
    {
        return fiPledgeIncreaseMapper.deleteFiPledgeIncreaseById( rowId);
    };


    /**
     * 批量删除仓单质押补充申请表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiPledgeIncreaseByIds(Integer[] rowIds)
    {
        return fiPledgeIncreaseMapper.deleteFiPledgeIncreaseByIds( rowIds);
    }

}
