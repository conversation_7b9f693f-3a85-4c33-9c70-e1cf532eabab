package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.FdShippingNumDTO;
import com.huazheng.tunny.ocean.api.entity.FdShippingAccount;
import com.huazheng.tunny.ocean.api.vo.FdShippingAccountVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * 发运台账主表 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:30:06
 */
public interface FdShippingAccountService extends IService<FdShippingAccount> {
    /**
     * 查询发运台账主表信息
     *
     * @param rowId 发运台账主表ID
     * @return 发运台账主表信息
     */
    public FdShippingAccount selectFdShippingAccountById(Long rowId);
    /**
     * 根据班次号获取班次信息
     * @param shiftNo
     * @return
     */
    public FdShippingAccount selectShippingInfo(String shiftNo);

    /**
     * 查询发运台账主表列表
     *
     * @param fdShippingAccount 发运台账主表信息
     * @return 发运台账主表集合
     */
    public List<FdShippingAccount> selectFdShippingAccountList(FdShippingAccount fdShippingAccount);


    /**
     * 分页模糊查询发运台账主表列表
     * @return 发运台账主表集合
     */
    public Page selectFdShippingAccountListByLike(Query query);

    void exportShippingAccountExcel(FdShippingAccount fdShippingAccount, HttpServletResponse response) throws IOException;

    public Integer selectFdShippingAccountListByLikeCount(FdShippingAccount fdShippingAccount);
    /**
     * 查询可生成台账List
     * @param query
     * @return
     */
    public Page selectShippingAccountList(Query query);

    public Page selectShippingAccountListNew(Query query);


    public List<FdShippingAccount> selectNotGenerateStandingBook(Map<String, Object> params);



    /**
     * 新增发运台账主表
     *
     * @param vo 发运台账主表信息
     * @return 结果
     */
    public R insertFdShippingAccount(FdShippingAccountVO vo);

    /**
     * 台账同步
     */
    public R saveSynchronousData(FdShippingAccountVO vo);

    /**
     * 修改发运台账主表
     *
     * @param vo 发运台账主表信息
     * @return 结果
     */
    public R updateFdShippingAccount(FdShippingAccountVO vo);

    public int updateFdShippingAccount2(FdShippingAccount fdShippingAccount);

    /**
     * 删除发运台账主表
     *
     * @param rowId 发运台账主表ID
     * @return 结果
     */
    public int deleteFdShippingAccountById(Long rowId);

    /**
     * 批量删除发运台账主表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdShippingAccountByIds(Integer[] rowIds);

    /**
     * 发运台账审核
     *
     * @param vo
     * @return
     */
    public int auditStatus(FdShippingAccountVO vo);

    public int auditStatus2(FdShippingAccountVO vo);

    public R auditStatus3(FdShippingAccountVO vo) throws Exception;

    public R auditStatusBatchCheck(List<FdShippingAccountVO> vos) throws Exception;

    public R auditStatusBatch(List<FdShippingAccountVO> vos) throws Exception;

    public R auditStatusWx(FdShippingAccountVO vo) throws Exception;

    public R secondAuditStatus(FdShippingAccountVO vo);

    /**
     * 省平台修改台账发运时间
     * @param fdShippingAccount 班次管理信息
     * @return 结果
     */
    public R updateShippingTime(FdShippingAccount fdShippingAccount);

    public List<FdShippingAccount> selectFdShippingAccountByCode(FdShippingAccount fdShippingAccount);

    public int updateStatus(FdShippingAccount fdShippingAccount);

    /**
     * 修改省级班次号
     * @param fdShippingAccount
     * @return
     */
    public R updateProvinceShiftNo(FdShippingAccount fdShippingAccount);

    public R updateProvinceShiftNoNew(FdShippingAccount fdShippingAccount);

    public R updateProvinceShiftNoCheck(FdShippingAccount fdShippingAccount);

    /**
     * 市平台查询历史订舱平台
     * @param param
     * @return
     */
    public R selectFiShippingTime(Map<String, Object> param) throws ParseException;


    /**
     * 市平台统计信息
     * @param param
     * @return
     */
    public R selectShiStatistics(Map<String, Object> param) throws ParseException;

    R checkTz(Long rowId);

    R importedSecond(String accountCode);

    R cancel(Long rowId);

    FdShippingAccount selectFdShippingAccountByAccountCode(String accountCode);

    R infoBatch(List<String> accountCodes);

    R selectAccountByDL(Map<String, Object> params);

    void exportTemplateForTz(HttpServletResponse response) throws IOException;

    R importedTemplateForTz(MultipartFile file) throws Exception;

    R tzCommitCheck(FdShippingAccount fdShippingAccount);

    R   selectShipNum();

    R selectShipNumDetail(FdShippingNumDTO fdShippingNumDTO);

    R selectShipNumHalfYear();

    R selectMonthCity()  throws Exception;

    R selectMonthThisCity()  throws Exception;

    void costLedgerExport(FdShippingAccount fdShippingAccount, HttpServletResponse response) throws Exception;
}

