package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.mapper.BasChangeboxCostDetailMapper;
import com.huazheng.tunny.ocean.mapper.BasChangeboxRetreatMapper;
import com.huazheng.tunny.ocean.mapper.CustomerInfoMapper;
import com.huazheng.tunny.ocean.service.BasChangeboxCostDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service("basChangeboxCostDetailService")
public class BasChangeboxCostDetailServiceImpl extends ServiceImpl<BasChangeboxCostDetailMapper, BasChangeboxCostDetail> implements BasChangeboxCostDetailService {

    @Autowired
    private BasChangeboxCostDetailMapper basChangeboxCostDetailMapper;
    @Autowired
    private BasChangeboxRetreatMapper basChangeboxRetreatMapper;
    @Autowired
    private CustomerInfoMapper customerInfoMapper;

    /**
     * 查询换箱费用表信息
     *
     * @param id 换箱费用表ID
     * @return 换箱费用表信息
     */
    @Override
    public BasChangeboxCostDetail selectBasChangeboxCostDetailById(Integer id) {
        return basChangeboxCostDetailMapper.selectBasChangeboxCostDetailById(id);
    }

    /**
     * 查询换箱费用表列表
     *
     * @param basChangeboxCostDetail 换箱费用表信息
     * @return 换箱费用表集合
     */
    @Override
    public List<BasChangeboxCostDetail> selectBasChangeboxCostDetailList(BasChangeboxCostDetail basChangeboxCostDetail) {
        return basChangeboxCostDetailMapper.selectBasChangeboxCostDetailList(basChangeboxCostDetail);
    }


    /**
     * 分页模糊查询换箱费用表列表
     *
     * @return 换箱费用表集合
     */
    @Override
    public Page selectBasChangeboxCostDetailListByLike(Query query) {
        BasChangeboxCostDetail basChangeboxCostDetail = BeanUtil.mapToBean(query.getCondition(), BasChangeboxCostDetail.class, false);
        query.setRecords(basChangeboxCostDetailMapper.selectBasChangeboxCostDetailListByLike(query, basChangeboxCostDetail));
        return query;
    }

    /**
     * 新增换箱费用表
     *
     * @param basChangeboxCostDetail 换箱费用表信息
     * @return 结果
     */
    @Override
    public int insertBasChangeboxCostDetail(BasChangeboxCostDetail basChangeboxCostDetail) {
        return basChangeboxCostDetailMapper.insertBasChangeboxCostDetail(basChangeboxCostDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCost(BasChangeboxContainerInfo basChangeboxContainerInfo) {
        BasChangeboxRetreat sel = new BasChangeboxRetreat();
        sel.setBusinessid(basChangeboxContainerInfo.getBusinessid());
        sel.setDeleteFlag("N");
        List<BasChangeboxRetreat> basChangeboxRetreats = basChangeboxRetreatMapper.selectBasChangeboxRetreatList(sel);
        if (CollUtil.isNotEmpty(basChangeboxRetreats)) {
            //删除所有数据
            basChangeboxCostDetailMapper.deleteBasChangeboxCostDetails(basChangeboxContainerInfo);

            List<BasChangeboxCostDetail> receiveCost = basChangeboxContainerInfo.getReceiveCost();
            if (CollUtil.isNotEmpty(receiveCost)) {
                for (BasChangeboxCostDetail receive : receiveCost
                ) {
                    receive.setHxAppNo(basChangeboxContainerInfo.getBusinessid());
                    receive.setContainerNumber(basChangeboxContainerInfo.getContainerNo());
                    receive.setCostType("0");
                    receive.setReceiveCode(basChangeboxRetreats.get(0).getPlatformCode());
                    receive.setReceiveName(basChangeboxRetreats.get(0).getPlatformName());
                    basChangeboxCostDetailMapper.insertBasChangeboxCostDetail(receive);
                }
            }

            List<BasChangeboxCostDetail> payCost = basChangeboxContainerInfo.getPayCost();
            if (CollUtil.isNotEmpty(payCost)) {
                List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoList(null);
                for (BasChangeboxCostDetail pay : payCost
                ) {
                    pay.setHxAppNo(basChangeboxContainerInfo.getBusinessid());
                    pay.setContainerNumber(basChangeboxContainerInfo.getContainerNo());
                    pay.setCostType("1");
                    pay.setPayCode(basChangeboxRetreats.get(0).getPlatformCode());
                    pay.setPayName(basChangeboxRetreats.get(0).getPlatformName());
                    if(CollUtil.isNotEmpty(customerInfos)){
                        for (CustomerInfo customerInfo:customerInfos
                             ) {
                            if(pay.getReceiveName().equals(customerInfo.getCustomerCode())){
                                pay.setReceiveCode(customerInfo.getCustomerCode());
                            }
                        }
                    }
                    basChangeboxCostDetailMapper.insertBasChangeboxCostDetail(pay);
                }
            }
        }
    }

    /**
     * 修改换箱费用表
     *
     * @param basChangeboxCostDetail 换箱费用表信息
     * @return 结果
     */
    @Override
    public int updateBasChangeboxCostDetail(BasChangeboxCostDetail basChangeboxCostDetail) {
        return basChangeboxCostDetailMapper.updateBasChangeboxCostDetail(basChangeboxCostDetail);
    }


    /**
     * 删除换箱费用表
     *
     * @param id 换箱费用表ID
     * @return 结果
     */
    public int deleteBasChangeboxCostDetailById(Integer id) {
        return basChangeboxCostDetailMapper.deleteBasChangeboxCostDetailById(id);
    }

    ;


    /**
     * 批量删除换箱费用表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBasChangeboxCostDetailByIds(Integer[] ids) {
        return basChangeboxCostDetailMapper.deleteBasChangeboxCostDetailByIds(ids);
    }

}
