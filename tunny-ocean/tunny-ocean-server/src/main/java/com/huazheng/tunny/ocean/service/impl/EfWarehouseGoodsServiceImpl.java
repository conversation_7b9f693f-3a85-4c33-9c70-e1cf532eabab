package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.huazheng.tunny.ocean.api.entity.EfWaybill;
import com.huazheng.tunny.ocean.api.entity.EfWaybillGoods;
import com.huazheng.tunny.ocean.mapper.EfWarehouseGoodsMapper;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseGoods;
import com.huazheng.tunny.ocean.mapper.EfWaybillGoodsMapper;
import com.huazheng.tunny.ocean.mapper.EfWaybillMapper;
import com.huazheng.tunny.ocean.service.EfWarehouseGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.ArrayList;
import java.util.List;

@Service("efWarehouseGoodsService")
public class EfWarehouseGoodsServiceImpl extends ServiceImpl<EfWarehouseGoodsMapper, EfWarehouseGoods> implements EfWarehouseGoodsService {

    @Autowired
    private EfWarehouseGoodsMapper efWarehouseGoodsMapper;
    @Autowired
    private EfWaybillMapper efWaybillMapper;
    @Autowired
    private EfWaybillGoodsMapper efWaybillGoodsMapper;
    /**
     * 查询e融同步仓单货物表信息
     *
     * @param rowId e融同步仓单货物表ID
     * @return e融同步仓单货物表信息
     */
    @Override
    public EfWarehouseGoods selectEfWarehouseGoodsById(String rowId)
    {
        return efWarehouseGoodsMapper.selectEfWarehouseGoodsById(rowId);
    }

    /**
     * 查询e融同步仓单货物表列表
     *
     * @param efWarehouseGoods e融同步仓单货物表信息
     * @return e融同步仓单货物表集合
     */
    @Override
    public List<EfWarehouseGoods> selectEfWarehouseGoodsList(EfWarehouseGoods efWarehouseGoods)
    {
        return efWarehouseGoodsMapper.selectEfWarehouseGoodsList(efWarehouseGoods);
    }


    /**
     * 分页模糊查询e融同步仓单货物表列表
     * @return e融同步仓单货物表集合
     */
    @Override
    public Page selectEfWarehouseGoodsListByLike(Query query)
    {
        EfWarehouseGoods efWarehouseGoods =  BeanUtil.mapToBean(query.getCondition(), EfWarehouseGoods.class,false);
        query.setRecords(efWarehouseGoodsMapper.selectEfWarehouseGoodsListByLike(query, efWarehouseGoods));
        return query;
    }

    @Override
    public Page pageForApply(Query query)
    {
        EfWarehouseGoods efWarehouseGoods =  BeanUtil.mapToBean(query.getCondition(), EfWarehouseGoods.class,false);
        efWarehouseGoods.setDeleteFlag("N");
        final List<EfWarehouseGoods> efWarehouseGoods1 = efWarehouseGoodsMapper.selectEfWarehouseGoodsListByLike(query, efWarehouseGoods);

        final List<EfWaybill> efWaybills = efWaybillMapper.selectEfWaybillByQlFinancingNo(efWarehouseGoods.getQlFinancingNo());
        final List<EfWaybillGoods> efWaybillGoods = efWaybillGoodsMapper.selectEfWaybillGoodsByQlFinancingNo(efWarehouseGoods.getQlFinancingNo());
        if(CollUtil.isNotEmpty(efWarehouseGoods1)){
            for (EfWarehouseGoods good:efWarehouseGoods1
            ) {
                if(CollUtil.isNotEmpty(efWaybills)){
                    for (EfWaybill bill:efWaybills
                    ) {
                        if(good.getWarehouseReceiptNo().equals(bill.getWarehouseReceiptNo())&&good.getGoodsNo().equals(bill.getGoodsNo())){
                            good.setWaybillNo(bill.getWaybillNo());
                        }
                        List<EfWaybillGoods> list = new ArrayList<>();
                        if(CollUtil.isNotEmpty(efWaybillGoods)){
                            for (EfWaybillGoods waybillGood:efWaybillGoods
                            ) {
                                if(bill.getWarehouseReceiptNo().equals(waybillGood.getWarehouseReceiptNo())
                                        &&bill.getGoodsNo().equals(waybillGood.getGoodsNo())
                                        &&bill.getOrderNo().equals(waybillGood.getOrderNo())
                                        &&bill.getWaybillNo().equals(waybillGood.getWaybillNo())){
                                    list.add(waybillGood);
                                }
                                bill.setWayBillGoodsInfoList(list);
                            }
                        }
                        if(good.getWarehouseReceiptNo().equals(bill.getWarehouseReceiptNo())
                                &&good.getGoodsNo().equals(bill.getGoodsNo())){
                            good.setWayBillInfo(bill);
                        }
                    }
                }
            }
        }

        query.setRecords(efWarehouseGoods1);
        return query;
    }
    /**
     * 新增e融同步仓单货物表
     *
     * @param efWarehouseGoods e融同步仓单货物表信息
     * @return 结果
     */
    @Override
    public int insertEfWarehouseGoods(EfWarehouseGoods efWarehouseGoods)
    {
        return efWarehouseGoodsMapper.insertEfWarehouseGoods(efWarehouseGoods);
    }

    /**
     * 修改e融同步仓单货物表
     *
     * @param efWarehouseGoods e融同步仓单货物表信息
     * @return 结果
     */
    @Override
    public int updateEfWarehouseGoods(EfWarehouseGoods efWarehouseGoods)
    {
        return efWarehouseGoodsMapper.updateEfWarehouseGoods(efWarehouseGoods);
    }


    /**
     * 删除e融同步仓单货物表
     *
     * @param rowId e融同步仓单货物表ID
     * @return 结果
     */
    public int deleteEfWarehouseGoodsById(String rowId)
    {
        return efWarehouseGoodsMapper.deleteEfWarehouseGoodsById( rowId);
    };


    /**
     * 批量删除e融同步仓单货物表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfWarehouseGoodsByIds(Integer[] rowIds)
    {
        return efWarehouseGoodsMapper.deleteEfWarehouseGoodsByIds( rowIds);
    }

}
