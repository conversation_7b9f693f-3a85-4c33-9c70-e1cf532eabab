package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.*;
import com.huazheng.tunny.ocean.api.entity.WaybillHeader;
import com.huazheng.tunny.ocean.api.entity.WaybillHeaderSh;
import com.huazheng.tunny.ocean.api.vo.IndexVO;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * 运单主表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-05 10:57:51
 */
public interface WaybillHeaderShService extends IService<WaybillHeaderSh> {

    /**
     * 分页模糊查询运单主表列表
     * @return 运单主表集合
     */
    public Page selectWaybillHeaderListByLike(Query query);

}

