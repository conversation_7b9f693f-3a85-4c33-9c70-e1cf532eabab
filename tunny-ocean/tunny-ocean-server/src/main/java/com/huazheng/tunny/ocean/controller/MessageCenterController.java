package com.huazheng.tunny.ocean.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.MessageCenter;
import com.huazheng.tunny.ocean.common.websocket.WebSocket;
import com.huazheng.tunny.ocean.service.MessageCenterService;
import com.huazheng.tunny.ocean.util.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @Description: 消息中心表
 * @Author: shaojian
 * @Date: 2022-10-09 11:31:53
 */
@RestController
@RequestMapping("/message/center")
public class MessageCenterController {

    @Autowired
    private MessageCenterService service;
    @Autowired
    private WebSocket webSocket;

    /**
     * @Description: 分页
     * @Param: params
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    @ApiOperation("分页")
    @GetMapping("/page")
    public Page<MessageCenter> page(@RequestParam Map<String, Object> params) {
        return service.page(new Query<>(params));
    }

    /**
     * @Description: 详情
     * @Param: id
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    @ApiOperation("详情")
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Integer id) {
        return service.info(id);
    }

    /**
     * @Description: 保存
     * @Param: param
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    @ApiOperation("保存")
    @PostMapping("/save")
    public R save(@RequestBody MessageCenter param) {
        return service.save(param);
    }

    /**
     * @Description: 修改
     * @Param: param
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    @ApiOperation("修改")
    @PostMapping("/renew")
    public R update(@RequestBody MessageCenter param) {
        return service.update(param);
    }

    /**
     * @Description: 删除
     * @Param: id
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    @ApiOperation("删除")
    @PostMapping("/del/{id}")
    public R delete(@PathVariable Integer id) {
        return service.delete(id);
    }

    /**
     * @Description: 列表
     * @Param: param
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    @ApiOperation("列表")
    @GetMapping("/list")
    public R list(MessageCenter param) {
        return service.list(param);
    }

    /**
     * @Description: 切换已读
     * @Param: param
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    @ApiOperation("切换已读")
    @PostMapping("/read")
    public R read(@RequestBody Map<String, Object> param) {
        return service.read(param);
    }

    /**
     * @Description: 根据模块切换已读
     * @Param: param
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */

    @ApiOperation("根据模块切换已读")
    @Deprecated
    @PostMapping("/readByModule")
    public R readByModule(@RequestBody Map<String, Object> param) {
        return service.readByModule(param);
    }

    @ApiOperation("根据模块切换已读 v2")
    @PostMapping("/readByModuleV2")
    public R readByModuleV2(@RequestBody MessageCenter param) {
        return service.readByModuleV2(param);
    }

    @ApiOperation("获取我的未读消息数量")
    @PostMapping("/getMyMsgNum")
    public R getMyMsgNum(@RequestBody MessageCenter param) {
        return service.getMyMsgNum(param);
    }

    @ApiOperation(value = "推送websocket")
    @GetMapping("/sendWebsocketToUser")
    public void sendWebsocketToUser(@RequestParam("userId") String userId, @RequestParam("content") String content) {
        webSocket.sendOneMessage(Integer.valueOf(userId), content);
    }

    @ApiOperation("/查询bpm代办")
    @GetMapping("/selectBpmBacklog")
    public R selectBpmBacklog(@RequestParam Map<String, Object> param){
        return service.selectBpmBacklog(new Query<>(param));
    }

    @GetMapping("/selectBpmProcessed")
    public R selectBpmProcessed(@RequestParam Map<String, Object> param){
        return service.selectBpmProcessed(new Query<>(param));
    }

    /*@PostMapping("/addMessage")
    public R addMessage(@RequestBody List<MessageCenter> addMessageDTO){
        return service.addMessage(addMessageDTO);
    }*/

    @GetMapping("/selectModelByUserName")
    public List<Map<String,String>> selectModelByUserName(@RequestParam("userName")String userName){
        return service.selectModelByUserName(userName);
    }

    @PostMapping("/pushWebSocket")
    public void pushWebSocket(@RequestBody Map<String,String> map){
        service.pushWebSocket(map);
    }

}
