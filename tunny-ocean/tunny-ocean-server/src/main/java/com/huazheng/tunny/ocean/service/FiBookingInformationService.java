package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FiBookingInformation;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 订舱单信息 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-03-08 09:41:46
 */
public interface FiBookingInformationService extends IService<FiBookingInformation> {
    /**
     * 查询平台客户订舱单信息信息
     *
     * @return 订舱单信息信息
     */
    public Page selectFiBookingInformationListByPt(Query query);
    /**
     * 查询订舱客户订舱单信息信息
     *
     * @return 订舱单信息信息
     */
    public Page selectFiBookingInformationListByDc(Query query);
    /**
     * 查询订舱单信息信息
     *
     * @param rowId 订舱单信息ID
     * @return 订舱单信息信息
     */
    public FiBookingInformation selectFiBookingInformationById(String rowId);

    /**
     * 查询订舱单信息列表
     *
     * @param fiBookingInformation 订舱单信息信息
     * @return 订舱单信息集合
     */
    public List<FiBookingInformation> selectFiBookingInformationList(FiBookingInformation fiBookingInformation);


    /**
     * 分页模糊查询订舱单信息列表
     * @return 订舱单信息集合
     */
    public Page selectFiBookingInformationListByLike(Query query);



    /**
     * 新增订舱单信息
     *
     * @param fiBookingInformation 订舱单信息信息
     * @return 结果
     */
    public int insertFiBookingInformation(FiBookingInformation fiBookingInformation);

    /**
     * 修改订舱单信息
     *
     * @param fiBookingInformation 订舱单信息信息
     * @return 结果
     */
    public int updateFiBookingInformation(FiBookingInformation fiBookingInformation);

    public int updateFiBookingInformationByAssetCode(FiBookingInformation fiBookingInformation);

    /**
     * 删除订舱单信息
     *
     * @param rowId 订舱单信息ID
     * @return 结果
     */
    public int deleteFiBookingInformationById(String rowId);

    /**
     * 批量删除订舱单信息
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiBookingInformationByIds(Integer[] rowIds);

}

