package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.EfFileDTO;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseApply;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 仓单融资申请表 服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-04 10:08:10
 */
public interface EfWarehouseApplyService extends IService<EfWarehouseApply> {
    /**
     * 查询仓单融资申请表信息
     *
     * @param rowId 仓单融资申请表ID
     * @return 仓单融资申请表信息
     */
    public EfWarehouseInfo selectEfWarehouseApplyById(String rowId);

    /**
     * 查询仓单融资申请表列表
     *
     * @param efWarehouseApply 仓单融资申请表信息
     * @return 仓单融资申请表集合
     */
    public List<EfWarehouseApply> selectEfWarehouseApplyList(EfWarehouseApply efWarehouseApply);


    /**
     * 分页模糊查询仓单融资申请表列表
     * @return 仓单融资申请表集合
     */
    public Page selectEfWarehouseApplyListByLike(Query query);

    public Page pageForPlatformCode(Query query);

    public R sumForPlatformCode(Query query);

    /**
     * 新增仓单融资申请表
     *
     * @param efWarehouseApply 仓单融资申请表信息
     * @return 结果
     */
    public String insertEfWarehouseApply(EfWarehouseApply efWarehouseApply);

    public EfFileDTO downloadFile(EfFileDTO efFileDTO) throws IOException;

    public String downloadFileForEf(EfFileDTO efFileDTO);
    /**
     * 修改仓单融资申请表
     *
     * @param efWarehouseApply 仓单融资申请表信息
     * @return 结果
     */
    public String updateEfWarehouseApply(EfWarehouseApply efWarehouseApply);

    /**
     * 删除仓单融资申请表
     *
     * @param rowId 仓单融资申请表ID
     * @return 结果
     */
    public int deleteEfWarehouseApplyById(String rowId);

    /**
     * 批量删除仓单融资申请表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfWarehouseApplyByIds(Integer[] rowIds);

    public String auditFromEf(EfWarehouseApply efWarehouseApply);

    public String syncAsset(EfWarehouseInfo efWarehouseInfo);

    String syncWarnPrice(EfWarehouseApply efWarehouseApply);
}

