package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.ocean.mapper.StoredCompareWithTypeMapper;
import com.huazheng.tunny.ocean.api.entity.StoredCompareWithType;
import com.huazheng.tunny.ocean.service.StoredCompareWithTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service("storedCompareWithTypeService")
public class StoredCompareWithTypeServiceImpl extends ServiceImpl<StoredCompareWithTypeMapper, StoredCompareWithType> implements StoredCompareWithTypeService {

    @Autowired
    private StoredCompareWithTypeMapper storedCompareWithTypeMapper;

    public StoredCompareWithTypeMapper getStoredCompareWithTypeMapper() {
        return storedCompareWithTypeMapper;
    }

    public void setStoredCompareWithTypeMapper(StoredCompareWithTypeMapper storedCompareWithTypeMapper) {
        this.storedCompareWithTypeMapper = storedCompareWithTypeMapper;
    }

    /**
     * 查询数据同比（发运类型同比）信息
     *
     * @param rowId 数据同比（发运类型同比）ID
     * @return 数据同比（发运类型同比）信息
     */
    @Override
    public StoredCompareWithType selectStoredCompareWithTypeById(Integer rowId)
    {
        return storedCompareWithTypeMapper.selectStoredCompareWithTypeById(rowId);
    }

    /**
     * 查询数据同比（发运类型同比）列表
     *
     * @param storedCompareWithType 数据同比（发运类型同比）信息
     * @return 数据同比（发运类型同比）集合
     */
    @Override
    public List<StoredCompareWithType> selectStoredCompareWithTypeList(StoredCompareWithType storedCompareWithType)
    {
        if(storedCompareWithType!=null&& StrUtil.isEmpty(storedCompareWithType.getDate())){
            String date= LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
            storedCompareWithType.setDate(date);
            String year = storedCompareWithType.getDate().split("-")[0];
            storedCompareWithType.setYear(year);
            String lastYear = (Integer.parseInt(year) - 1 )+"";
            storedCompareWithType.setLastYear(lastYear);
            String lastDate = lastYear + "-" + storedCompareWithType.getDate().split("-")[1];
            storedCompareWithType.setLastDate(lastDate);
        }else{
            String year = storedCompareWithType.getDate().split("-")[0];
            storedCompareWithType.setYear(year);
            String lastYear = (Integer.parseInt(year) - 1 )+"";
            storedCompareWithType.setLastYear(lastYear);
            String lastDate = lastYear + "-" + storedCompareWithType.getDate().split("-")[1];
            storedCompareWithType.setLastDate(lastDate);
        }
        List<StoredCompareWithType> list = storedCompareWithTypeMapper.selectStoredCompareWithTypeList(storedCompareWithType);
        StoredCompareWithType total = storedCompareWithTypeMapper.selectStoredCompareWithTypeListTotal(storedCompareWithType);
        list.add(total);
        return list;
    }


    /**
     * 分页模糊查询数据同比（发运类型同比）列表
     * @return 数据同比（发运类型同比）集合
     */
    @Override
    public Page selectStoredCompareWithTypeListByLike(Query query)
    {
        StoredCompareWithType storedCompareWithType =  BeanUtil.mapToBean(query.getCondition(), StoredCompareWithType.class,false);
        query.setRecords(storedCompareWithTypeMapper.selectStoredCompareWithTypeListByLike(query,storedCompareWithType));
        return query;
    }

    /**
     * 新增数据同比（发运类型同比）
     *
     * @param storedCompareWithType 数据同比（发运类型同比）信息
     * @return 结果
     */
    @Override
    public int insertStoredCompareWithType(StoredCompareWithType storedCompareWithType)
    {
        return storedCompareWithTypeMapper.insertStoredCompareWithType(storedCompareWithType);
    }

    /**
     * 修改数据同比（发运类型同比）
     *
     * @param storedCompareWithType 数据同比（发运类型同比）信息
     * @return 结果
     */
    @Override
    public int updateStoredCompareWithType(StoredCompareWithType storedCompareWithType)
    {
        return storedCompareWithTypeMapper.updateStoredCompareWithType(storedCompareWithType);
    }


    /**
     * 删除数据同比（发运类型同比）
     *
     * @param rowId 数据同比（发运类型同比）ID
     * @return 结果
     */
    public int deleteStoredCompareWithTypeById(Integer rowId)
    {
        return storedCompareWithTypeMapper.deleteStoredCompareWithTypeById( rowId);
    };


    /**
     * 批量删除数据同比（发运类型同比）对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteStoredCompareWithTypeByIds(Integer[] rowIds)
    {
        return storedCompareWithTypeMapper.deleteStoredCompareWithTypeByIds( rowIds);
    }

}
