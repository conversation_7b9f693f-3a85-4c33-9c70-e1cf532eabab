package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.util.StringUtils;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.*;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.enums.MessageBusinessEnum;
import com.huazheng.tunny.ocean.api.feign.RemoteAdminService;
import com.huazheng.tunny.ocean.api.vo.IndexVO;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service("waybillHeaderService")
public class WaybillHeaderServiceImpl extends ServiceImpl<WaybillHeaderMapper, WaybillHeader> implements WaybillHeaderService {

    @Autowired
    private WaybillHeaderMapper waybillHeaderMapper;

    @Autowired
    private WaybillContainerInfoMapper waybillContainerInfoMapper;

    @Autowired
    private WaybillGoodsInfoMapper waybillGoodsInfoMapper;

    @Autowired
    private WaybillParticipantsMapper waybillParticipantsMapper;

    @Autowired
    private BasChangeboxRetreatService basChangeboxRetreatService;

    @Autowired
    private WaybillContainerInfoService waybillContainerInfoService;

    @Autowired
    private CustomerInfoService customerInfoService;

    @Autowired
    private FiHisBookingInformationService fiHisBookingInformationService;

    @Autowired
    private PayCodeMesMapper payCodeMesMapper;

    @Autowired
    private StationManagementMapper stationMapper;

    @Autowired
    private UploadRecordMapper uploadRecordMapper;

    @Autowired
    private AudiopinionMapper audiopinionMapper;

    @Autowired
    private SysNoConfigService sysNoConfigService;

    @Autowired
    private FdBusCostMapper fdBusCostMapper;

    @Autowired
    private FdBusCostWaybillMapper fdBusCostWaybillMapper;

    @Autowired
    private ShifmanagementMapper shifmanagementMapper;

    @Autowired
    private CustomerInfoMapper customerInfoMapper;
    @Autowired
    private MessageCenterService messageCenterService;
    @Autowired
    private RemoteAdminService remoteAdminService;
    @Autowired
    private CustomerPlatformInfoMapper customerPlatformInfoMapper;
    @Autowired
    private FdPostTransportService fdPostTransportService;
    @Resource
    private FdBusCostService fdBusCostService;

    /**
     * 查询运单主表信息
     *
     * @param rowId 运单主表ID
     * @return 运单主表信息
     */
    @Override
    public WaybillHeader selectWaybillHeaderById(String rowId) {
        return waybillHeaderMapper.selectWaybillHeaderById(rowId);
    }

    /**
     * 查询运单主表列表
     *
     * @param waybillHeader 运单主表信息
     * @return 运单主表集合
     */
    @Override
    public List<WaybillHeader> selectWaybillHeaderList(WaybillHeader waybillHeader) {
        return waybillHeaderMapper.selectWaybillHeaderList(waybillHeader);
    }

    /**
     * 查询客户订舱数量
     *
     * @param indexVO
     * @return
     */
    @Override
    public String selectBookedNum(IndexVO indexVO) {
        return waybillHeaderMapper.selectBookedNum(indexVO);
    }

    @Override
    public String selectBookedNum2(IndexVO indexVO) {
        return waybillHeaderMapper.selectBookedNum2(indexVO);
    }

    /**
     * 根据年月查询客户订舱数量
     *
     * @param indexVO
     * @return
     */
    @Override
    public List<KvDTO> selectBookedNumByMonth(IndexVO indexVO) {
        return waybillHeaderMapper.selectBookedNumByMonth(indexVO);
    }

    /**
     * 分页模糊查询运单主表列表
     *
     * @return 运单主表集合
     */
    @Override
    public Page selectWaybillHeaderListByLike(Query query) {
        if (StrUtil.isBlank(query.getOrderByField())) {
            query.setOrderByField("x.add_time");
            query.setAsc(Boolean.FALSE);
        }
        WaybillHeader waybillHeader = BeanUtil.mapToBean(query.getCondition(), WaybillHeader.class, false);
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        if ("0".equals(userInfo.getPlatformLevel())) {
            waybillHeader.setCustomerNo(userInfo.getPlatformCode());
            if ("1".equals(userInfo.getDataFlag())) {
                waybillHeader.setMiniPlatform(userInfo.getMiniPlatform());
            }
        } else if ("1".equals(userInfo.getPlatformLevel())) {
            waybillHeader.setPlatformCode(userInfo.getPlatformCode());
        }

        List<WaybillHeader> waybillHeaders = waybillHeaderMapper.selectWaybillHeaderListByLike(query, waybillHeader);
        for (WaybillHeader waybill : waybillHeaders) {
            if ("1".equals(waybill.getBillStatus())) {
                //查询订单数据是否有过撤换箱
                BasChangeboxRetreat basChangeboxRetreat = new BasChangeboxRetreat();
                basChangeboxRetreat.setAppNo(waybill.getOrderNo());
                basChangeboxRetreat.setWaybillNo(waybill.getWaybillNo());
                basChangeboxRetreat.setDeleteFlag("N");
                List<BasChangeboxRetreat> basChangeboxRetreats = basChangeboxRetreatService.selectBasChangeboxRetreatList(basChangeboxRetreat);
                if (basChangeboxRetreats.size() > 0) {
                    for (BasChangeboxRetreat basChangebox : basChangeboxRetreats) {
                        if ("2".equals(basChangebox.getStatus())) {
                            waybill.setIsUpdate("1");
                            break;
                        } else {
                            waybill.setIsUpdate("0");
                            break;
                        }
                    }
                } else {
                    waybill.setIsUpdate("1");
                }
            } else {
                waybill.setIsUpdate("1");
            }
        }
        query.setRecords(waybillHeaders);
        return query;
    }

    @Override
    public Integer selectWaybillHeaderListByLikeCount(WaybillHeader waybillHeader) {
        return waybillHeaderMapper.queryCount(waybillHeader);
    }

    /**
     * 新增运单主表
     *
     * @param waybillHeader 运单主表信息
     * @return 结果
     */
    @Override
    public int insertWaybillHeader(WaybillHeader waybillHeader) {
        return waybillHeaderMapper.insertWaybillHeader(waybillHeader);
    }

    /**
     * 逻辑删除运单及下属信息
     * 注（运单主表没有批量更新，对下属子信息（集装箱、货物等）的批量更新在各自业务层实体类里）
     *
     * @param waybillHeader 运单主表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateWaybillInfos(WaybillHeader waybillHeader) {
        List<WaybillGoodsInfo> goodsList = new ArrayList<>();
        List<WaybillParticipants> pantsList = new ArrayList<>();
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        waybillHeader.setDeleteWho(userInfo.getUserName());
        waybillHeader.setDeleteWhoName(userInfo.getUserName());
        waybillHeader.setDeleteTime(new Date());
        waybillHeader.setDeleteFlag("Y");

        //组装临时运单集装箱信息实体类，用于删除运单集装箱信息，做传参封装
        WaybillContainerInfo temp3 = new WaybillContainerInfo();
        temp3.setDeleteWho(userInfo.getUserName());
        temp3.setDeleteWhoName(userInfo.getUserName());
        temp3.setDeleteTime(new Date());
        temp3.setDeleteFlag("Y");
        temp3.setWaybillNo(waybillHeader.getWaybillNo());
        //组装货物、参与方逻辑删除信息
        WaybillGoodsInfo goods = new WaybillGoodsInfo();
        WaybillParticipants pants = new WaybillParticipants();
        goods.setWaybillNo(waybillHeader.getWaybillNo());
        goods.setDeleteFlag("Y");
        goods.setDeleteWho(userInfo.getUserName());
        goods.setDeleteWhoName(userInfo.getUserName());
        goods.setDeleteTime(LocalDateTime.now());
        pants.setWaybillNo(waybillHeader.getWaybillNo());
        pants.setDeleteFlag("Y");
        pants.setDeleteWho(userInfo.getUserName());
        pants.setDeleteWhoName(userInfo.getUserName());
        pants.setDeleteTime(LocalDateTime.now());
        goodsList.add(goods);
        pantsList.add(pants);
        try {
            waybillHeaderMapper.updateWaybillHeader(waybillHeader);
            waybillContainerInfoMapper.deletelots(temp3);
            if (CollUtil.isNotEmpty(goodsList)) {
                for (WaybillGoodsInfo goodsInfo : goodsList
                ) {
                    waybillGoodsInfoMapper.updateWaybillGoodsInfo2(goodsInfo);
                }
            }

            if (CollUtil.isNotEmpty(pantsList)) {
                for (WaybillParticipants waybillParticipants : pantsList
                ) {
                    waybillParticipantsMapper.updateWaybillParticipant(waybillParticipants);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(e.getMessage() + "运单信息删除失败！！！");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new R(Boolean.FALSE, "运单信息删除失败");
        }
        return new R(Boolean.TRUE, "运单信息删除成功败");
    }

    @Override
    public int commitStatus(WaybillHeader waybillHeader) {
        WaybillHeader sel = new WaybillHeader();
        sel.setWaybillNo(waybillHeader.getWaybillNo());
        sel.setDeleteFlag("N");
        List<WaybillHeader> waybillHeaders = waybillHeaderMapper.selectWaybillHeaderList(sel);
        if (CollUtil.isNotEmpty(waybillHeaders)) {
            String content = waybillHeaders.get(0).getCustomerName() + "提交了【" + waybillHeaders.get(0).getTrainName() + "（" + waybillHeaders.get(0).getShiftNo() + "）】的补充资料，请及时登录PC端在【订舱管理-订单查询】中审核。";
            MessageCenter messageCenter = new MessageCenter();
            messageCenter.setModuleType(MessageBusinessEnum.WAYBILL_PROCESS.getKey());
            messageCenter.setModuleName(MessageBusinessEnum.WAYBILL_PROCESS.getValue());
            messageCenter.setBusinessId(waybillHeaders.get(0).getRowId());
            messageCenter.setBusinessType(MessageBusinessEnum.WAYBILL_AUDIT.getKey());
            messageCenter.setBusinessName(MessageBusinessEnum.WAYBILL_AUDIT.getValue());
            List<String> usernames = remoteAdminService.getUserNameByRoleName("业务岗", waybillHeaders.get(0).getPlatformCode());
            if (CollUtil.isNotEmpty(usernames)) {
                messageCenterService.sendMiniMsgNotice(usernames, "您有新的订单资料待审核，请及时处理。", content, messageCenter);
            }
        }
        return waybillHeaderMapper.commitStatus(waybillHeader);
    }

    @Override
    public String commitStatusForSh(String data) {
        WaybillHeader header = JSONUtil.toBean(data, WaybillHeader.class);
        if (header != null) {
            if (StrUtil.isEmpty(header.getWaybillNo())) {
                return JSONUtil.parseObj(new R<>(Boolean.FALSE, "未接收到订单号！"), false).toStringPretty();
            }
            if (StrUtil.isEmpty(header.getCustomerNo())) {
                return JSONUtil.parseObj(new R<>(Boolean.FALSE, "未接收到客户编码！"), false).toStringPretty();
            }
        } else {
            return JSONUtil.parseObj(new R<>(Boolean.FALSE, "未接收到请求参数！"), false).toStringPretty();
        }
        WaybillHeader wh = new WaybillHeader();
        wh.setWaybillNo(header.getWaybillNo());
        wh.setCustomerNo(header.getCustomerNo());
        wh.setDeleteFlag("N");
        List<WaybillHeader> waybillHeaders = waybillHeaderMapper.selectWaybillHeaderList(wh);
        if (CollUtil.isEmpty(waybillHeaders)) {
            return JSONUtil.parseObj(new R<>(Boolean.FALSE, "未查询到该订单！"), false).toStringPretty();
        }
        header.setBillStatus("3");
        waybillHeaderMapper.commitStatus(header);
        return JSONUtil.parseObj(new R<>(Boolean.TRUE, "支付成功！"), false).toStringPretty();
    }


    @Override
    public int updateWaybillHeader(WaybillHeader waybillHeader) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        waybillHeader.setUpdateTime(new Date());
        waybillHeader.setUpdateWho(usercode);
        waybillHeader.setUpdateWhoName(username);
        //修改明细表
        if (!"".equals(waybillHeader.getExchangeRate()) && waybillHeader.getExchangeRate() != null) {
            WaybillContainerInfo waybillContainerInfo = new WaybillContainerInfo();
            waybillContainerInfo.setWaybillNo(waybillHeader.getWaybillNo());
            waybillContainerInfo.setDeleteFlag("N");
            List<WaybillContainerInfo> waybillContainerInfos = waybillContainerInfoMapper.selectWaybillContainerInfoListByLike(waybillContainerInfo);
            if (waybillContainerInfos.size() > 0) {
                for (WaybillContainerInfo waybillContainer : waybillContainerInfos) {
                    waybillContainer.setResveredField01(waybillHeader.getExchangeRate());//汇率
                    waybillContainer.setMonetaryType(waybillHeader.getMonetaryType());//币种
                    waybillContainer.setOverseasFreightRmb(waybillContainer.getOverseasFreight().multiply(waybillHeader.getExchangeRate()).setScale(2, BigDecimal.ROUND_HALF_UP));//境外人民币
                    waybillContainerInfoMapper.updateContainerInfo(waybillContainer);
                }
            }
//            waybillContainerInfoMapper.updateContainerInfoBatch(waybillContainerInfos);
        }
        return waybillHeaderMapper.updateWaybillHeader(waybillHeader);
    }

    @Override
    public int updateWaybillHeaderBatch(List<WaybillHeader> waybillHeader) {
        return waybillHeaderMapper.updateWaybillHeaderBatch(waybillHeader);
    }

    /**
     * 删除运单主表
     *
     * @param rowId 运单主表ID
     * @return 结果
     */
    @Override
    public int deleteWaybillHeaderById(String rowId) {
        return waybillHeaderMapper.deleteWaybillHeaderById(rowId);
    }

    ;


    /**
     * 批量删除运单主表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteWaybillHeaderByIds(Integer[] rowIds) {
        return waybillHeaderMapper.deleteWaybillHeaderByIds(rowIds);
    }

    @Override
    public List<PlatFormDetailDTO> platformReportList(WaybillHeader waybillHeader) {

        List<PlatFormDetailDTO> platFormDetailDTOS = waybillHeaderMapper.platformReportList(waybillHeader);
        return platFormDetailDTOS;
    }

    @Override
    public List<PortDTO> monthReportList(WaybillHeader waybillHeader) {

        Map<String, Object> map = new HashMap<>();
        List<PortDTO> monthReportList = waybillHeaderMapper.monthReportList(waybillHeader);
        return monthReportList;
    }

    @Override
    public List<JiNanDTO> jiNanList2(WaybillHeader waybillHeader) {
//        List<JiNanDTO> jiNanDTOS = waybillHeaderMapper.selectJiNanList(waybillHeader);
//        return new R(200,Boolean.TRUE, jiNanDTOS);
        return waybillHeaderMapper.selectJiNanList2(waybillHeader);
    }

    @Override
    public Page jiNanList(Query query) {
//        List<JiNanDTO> jiNanDTOS = waybillHeaderMapper.selectJiNanList(waybillHeader);
//        return new R(200,Boolean.TRUE, jiNanDTOS);
        WaybillHeader waybillHeader = BeanUtil.mapToBean(query.getCondition(), WaybillHeader.class, false);
        return query.setRecords(waybillHeaderMapper.selectJiNanList(query, waybillHeader));
    }

    @Override
    public List<JiNanMonthDTO> selectCensusList(WaybillHeader waybillHeader) {
//        List<JiNanMonthDTO> jiNanMonthDTOS = waybillHeaderMapper.selectCensusList(waybillHeader);
//        return new R(200,Boolean.TRUE, jiNanMonthDTOS);
        if (waybillHeader != null && StrUtil.isEmpty(waybillHeader.getShippingTimeStart())) {
            //默认为当前年月
            waybillHeader.setShippingTimeStart(DateUtil.format(DateUtil.date(), "yyyy-MM"));
        }
        return waybillHeaderMapper.selectCensusList(waybillHeader);
    }

//    @Override
//    public List<TrainsMonthlyDTO> trainsMonthlyList(WaybillHeader waybillHeader) {
//        List<TrainsMonthlyDTO> TrainsMonthlyDTOS = waybillHeaderMapper.trainsMonthlyList(waybillHeader);
//        return TrainsMonthlyDTOS;
//    }

    @Override
    public Page selectCustomerShippingLineList(Query query) {
        WaybillHeader waybillHeader = BeanUtil.mapToBean(query.getCondition(), WaybillHeader.class, false);
        Integer c = waybillHeaderMapper.selectCustomerShippingLineListCount(waybillHeader);
        if (c != null && c != 0) {
            query.setTotal(c);
            query.setRecords(waybillHeaderMapper.selectCustomerShippingLineList(query, waybillHeader));
        }
        return query;
    }

    @Override
    public R selectFiBookingFee(Map<String, Object> params) throws ParseException {
        R r = new R();
        WaybillHeader waybillHeader = BeanUtil.mapToBean(params, WaybillHeader.class, false);
        //返回的集合
        List<Map<String, Object>> mapList = new ArrayList<>();
        if (waybillHeader != null) {
            if (StringUtils.isBlank(waybillHeader.getStartTime()) || StringUtils.isBlank(waybillHeader.getEndTime())) {
                r.setCode(500);
                r.setB(Boolean.FALSE);
                r.setMsg("请传入开始时间和结束时间");
                return r;
            }
        } else {
            r.setCode(500);
            r.setB(Boolean.FALSE);
            r.setMsg("请传入开始时间和结束时间");
            return r;
        }
        //约定时间
        String ydDate = "2022-03-01";
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date startTime = df.parse(waybillHeader.getStartTime());
        Date endTime = df.parse(waybillHeader.getEndTime());
        Date parse = df.parse(ydDate);
        CustomerInfo customerInfo = new CustomerInfo();
        customerInfo.setCustomerCode(waybillHeader.getCustomerNo());
        CustomerInfo byCustomerCode = customerInfoService.selectCustomegenNorInfoByCustomerCode(customerInfo);
        if (isEffectiveDate(parse, startTime, endTime)) {
            FiHisBookingInformation fiHisBookingInfor = new FiHisBookingInformation();
            //获取约定时间的前一天
            String beforeDay = getBeforeDay(ydDate);
            fiHisBookingInfor.setStartTime(waybillHeader.getStartTime());
            fiHisBookingInfor.setEndTime(beforeDay);
            fiHisBookingInfor.setOrgUnit(byCustomerCode.getCompanyName());
            fiHisBookingInfor.setCustomerNo(waybillHeader.getCustomerNo());
            fiHisBookingInfor.setStartTimeTwo(ydDate);
            fiHisBookingInfor.setEndTimeTwo(waybillHeader.getEndTime());
            fiHisBookingInfor.setIsGroup("1");
            List<FiHisBookingInformation> fiHisBookingInformations = fiHisBookingInformationService.selectfiHisBookingInformationDingcangList(fiHisBookingInfor);
            for (FiHisBookingInformation information : fiHisBookingInformations) {
                Map<String, Object> map = new HashMap<>();
                LocalDateTime date = information.getDate();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String format = formatter.format(date);
//                String format = formatter.format(date);
                String[] split = format.split("-");
                map.put("year", split[0]);
                map.put("month", split[1]);
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(split[0]).append("-").append(split[1]).append("-");
                FiHisBookingInformation hisBookingInformation = new FiHisBookingInformation();
                hisBookingInformation.setStartTime(stringBuilder.append("00").toString());
                hisBookingInformation.setEndTime(getLastDayOfMonth(Integer.valueOf(split[0]), Integer.valueOf(split[1])));
                hisBookingInformation.setOrgUnit(byCustomerCode.getCompanyName());
                List<FiHisBookingInformation> fiHisBookingInformationsSelect = fiHisBookingInformationService.selectfiHisBookingInformationDingcangList(hisBookingInformation);
                BigDecimal shouldCost = BigDecimal.valueOf(0);
                BigDecimal actualCost = BigDecimal.valueOf(0);
                for (FiHisBookingInformation bookingInformation : fiHisBookingInformationsSelect) {
                    shouldCost = shouldCost.add(bookingInformation.getShouldCost());
                    actualCost = actualCost.add(bookingInformation.getActualCost());
                }
                map.put("totalCost", shouldCost.setScale(2, BigDecimal.ROUND_HALF_UP));
                map.put("havePaid", actualCost.setScale(2, BigDecimal.ROUND_HALF_UP));
                if (shouldCost.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal bigDecimal = actualCost.divide(shouldCost).multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
                    map.put("payment", bigDecimal);
                } else {
                    map.put("payment", BigDecimal.valueOf(0.00));
                }

                mapList.add(map);
            }
            WaybillHeader billHeaders = new WaybillHeader();
            billHeaders.setShippingTimeStart(ydDate);
            billHeaders.setShippingTimeEnd(waybillHeader.getEndTime());
            billHeaders.setIsGroup("1");
            billHeaders.setCustomerNo(waybillHeader.getCustomerNo());
            //判断用户归属市平台还是订舱平台
            //获取区间内的所有月份
            List<WaybillHeader> waybillHeaders = waybillHeaderMapper.selectWaybillHeaderListByAddTime(billHeaders);
            for (WaybillHeader waybill : waybillHeaders) {
                Map<String, Object> map = new HashMap<>();
                Date addTime = waybill.getAddTime();
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String format = formatter.format(addTime);
                String[] split = format.split("-");
                map.put("year", split[0]);
                map.put("month", split[1]);
                //根据每个月第一天和最后一天查询出当月的所有数据
                WaybillHeader way = new WaybillHeader();
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(split[0]).append("-").append(split[1]).append("-");
                way.setShippingTimeStart(stringBuilder.append("00").toString());
                way.setShippingTimeEnd(getLastDayOfMonth(Integer.valueOf(split[0]), Integer.valueOf(split[1])));
                way.setBillStatus("Y");
                way.setCustomerNo(waybillHeader.getCustomerNo());
                //判断用户归属市平台还是订舱平台
                List<WaybillHeader> waybillHeadersList = waybillHeaderMapper.selectWaybillHeaderListByAddTime(way);
                List<String> stringList = new ArrayList<>();
                if (waybillHeadersList.size() > 0) {
                    for (WaybillHeader waybills : waybillHeadersList) {
                        stringList.add(waybills.getWaybillNo());
                    }
                    //订舱总费用
                    BigDecimal totalCost = waybillContainerInfoService.getTotalAmountList(stringList);
                    map.put("totalCost", totalCost.setScale(2, BigDecimal.ROUND_HALF_UP));


                    //查询已支付的费用
                    way.setBillStatus("N");
                    List<WaybillHeader> waybillHeadersLists = waybillHeaderMapper.selectWaybillHeaderListByAddTime(way);
                    if (waybillHeadersLists.size() > 0) {
                        List<String> stringLists = new ArrayList<>();
                        for (WaybillHeader waybills : waybillHeadersLists) {
                            stringLists.add(waybills.getWaybillNo());
                        }
                        //订舱已支付费用
                        BigDecimal havePaid = waybillContainerInfoService.getTotalAmountList(stringLists);
                        map.put("havePaid", havePaid.setScale(2, BigDecimal.ROUND_HALF_UP));
                        //计算支付率
                        BigDecimal bigDecimal = havePaid.divide(totalCost, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
                        map.put("payment", bigDecimal);
                    } else {
                        map.put("havePaid", "0");
                        map.put("payment", "0");
                    }

                } else {
                    map.put("totalCost", "0");
                    map.put("payment", "0");
                    map.put("havePaid", "0");
                }
                mapList.add(map);
            }

        } else if (parse.after(endTime) || parse.equals(endTime)) {
            FiHisBookingInformation fiHisBookingInformation = new FiHisBookingInformation();
            fiHisBookingInformation.setStartTime(waybillHeader.getStartTime());
            fiHisBookingInformation.setEndTime(waybillHeader.getEndTime());
            fiHisBookingInformation.setOrgUnit(byCustomerCode.getCompanyName());
            fiHisBookingInformation.setIsGroup("1");
            List<FiHisBookingInformation> fiHisBookingInformations = fiHisBookingInformationService.selectfiHisBookingInformationDingcangList(fiHisBookingInformation);
            for (FiHisBookingInformation information : fiHisBookingInformations) {
                Map<String, Object> map = new HashMap<>();
                LocalDateTime date = information.getDate();
//                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String format = formatter.format(date);
                String[] split = format.split("-");
                map.put("year", split[0]);
                map.put("month", split[1]);
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(split[0]).append("-").append(split[1]).append("-");
                FiHisBookingInformation hisBookingInformation = new FiHisBookingInformation();
                hisBookingInformation.setStartTime(stringBuilder.append("00").toString());
                hisBookingInformation.setEndTime(getLastDayOfMonth(Integer.valueOf(split[0]), Integer.valueOf(split[1])));
                hisBookingInformation.setOrgUnit(byCustomerCode.getCompanyName());
                List<FiHisBookingInformation> fiHisBookingInformationsSelect = fiHisBookingInformationService.selectfiHisBookingInformationDingcangList(hisBookingInformation);
                BigDecimal shouldCost = BigDecimal.valueOf(0);
                BigDecimal actualCost = BigDecimal.valueOf(0);
                for (FiHisBookingInformation bookingInformation : fiHisBookingInformationsSelect) {
                    shouldCost = shouldCost.add(bookingInformation.getShouldCost());
                    actualCost = actualCost.add(bookingInformation.getActualCost());
                }
                map.put("totalCost", shouldCost.setScale(2, BigDecimal.ROUND_HALF_UP));
                map.put("havePaid", actualCost.setScale(2, BigDecimal.ROUND_HALF_UP));
                BigDecimal bigDecimal = actualCost.divide(shouldCost).multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
                map.put("payment", bigDecimal);
                mapList.add(map);
            }
        } else if (parse.before(startTime) || parse.equals(startTime)) {
            waybillHeader.setShippingTimeStart(waybillHeader.getStartTime());
            waybillHeader.setShippingTimeEnd(waybillHeader.getEndTime());
            waybillHeader.setIsGroup("1");
            //判断用户归属市平台还是订舱平台
            //获取区间内的所有月份
            List<WaybillHeader> waybillHeaders = waybillHeaderMapper.selectWaybillHeaderListByAddTime(waybillHeader);
            for (WaybillHeader waybill : waybillHeaders) {
                Map<String, Object> map = new HashMap<>();
                Date addTime = waybill.getAddTime();
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String format = formatter.format(addTime);
                String[] split = format.split("-");
                map.put("year", split[0]);
                map.put("month", split[1]);
                //根据每个月第一天和最后一天查询出当月的所有数据
                WaybillHeader way = new WaybillHeader();
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(split[0]).append("-").append(split[1]).append("-");
                way.setShippingTimeStart(stringBuilder.append("00").toString());
                way.setShippingTimeEnd(getLastDayOfMonth(Integer.valueOf(split[0]), Integer.valueOf(split[1])));
                way.setBillStatus("Y");
                way.setCustomerNo(waybillHeader.getCustomerNo());
                //判断用户归属市平台还是订舱平台
                List<WaybillHeader> waybillHeadersList = waybillHeaderMapper.selectWaybillHeaderListByAddTime(way);
                List<String> stringList = new ArrayList<>();
                if (waybillHeadersList.size() > 0) {
                    for (WaybillHeader waybills : waybillHeadersList) {
                        stringList.add(waybills.getWaybillNo());
                    }
                    //订舱总费用
                    BigDecimal totalCost = waybillContainerInfoService.getTotalAmountList(stringList);
                    map.put("totalCost", totalCost.setScale(2, BigDecimal.ROUND_HALF_UP));


                    //查询已支付的费用
                    way.setBillStatus("N");
                    List<WaybillHeader> waybillHeadersLists = waybillHeaderMapper.selectWaybillHeaderListByAddTime(way);
                    if (waybillHeadersLists.size() > 0) {
                        List<String> stringLists = new ArrayList<>();
                        for (WaybillHeader waybills : waybillHeadersLists) {
                            stringLists.add(waybills.getWaybillNo());
                        }
                        //订舱已支付费用
                        BigDecimal havePaid = waybillContainerInfoService.getTotalAmountList(stringLists);
                        map.put("havePaid", havePaid.setScale(2, BigDecimal.ROUND_HALF_UP));
                        //计算支付率
                        BigDecimal bigDecimal = havePaid.divide(totalCost, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
                        map.put("payment", bigDecimal);
                    } else {
                        map.put("havePaid", "0");
                        map.put("payment", "0");
                    }

                } else {
                    map.put("totalCost", "0");
                    map.put("payment", "0");
                    map.put("havePaid", "0");
                }
                mapList.add(map);
            }
        }
        r.setMsg("查询成功");
        r.setB(Boolean.TRUE);
        r.setCode(200);
        r.setData(mapList);
        return r;
    }

    /**
     * 判断约定时间是否在[startTime, endTime]区间，注意时间格式要一致
     *
     * @param nowTime   约定时间
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime()
                || nowTime.getTime() == endTime.getTime()) {
            return false;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取指定日期的前一天
     *
     * @param specifiedDay
     * @return
     */
    public static String getBeforeDay(String specifiedDay) throws ParseException {
        Calendar c = Calendar.getInstance();
        Date date = null;
        date = new SimpleDateFormat("yyyy-MM-dd").parse(specifiedDay);
        c.setTime(date);
        int day = c.get(Calendar.DATE);
        c.set(Calendar.DATE, day - 1);

        String dayBefore = new SimpleDateFormat("yyyy-MM-dd").format(c.getTime());
        return dayBefore;
    }

    /**
     * 获取某月的最后一天
     */
    public static String getLastDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String lastDayOfMonth = sdf.format(cal.getTime());

        return lastDayOfMonth;
    }

    @Override
    public void updateTotalCases(String waybillNo) {
        WaybillContainerInfo info = new WaybillContainerInfo();
        info.setWaybillNo(waybillNo);
        info.setDeleteFlag("N");
        List<WaybillContainerInfo> list = waybillContainerInfoMapper.selectWaybillContainerInfoList(info);
        WaybillHeader header = new WaybillHeader();
        header.setWaybillNo(waybillNo);
        if (list != null) {
            Double t = 0D;
            header.setTotalCases(String.valueOf(list.size()));
            for (WaybillContainerInfo i : list
            ) {
                if (i.getContainerType() != null && !"".equals(i.getContainerType())) {
                    if (i.getContainerType().startsWith("2")) {
                        t += 0.5D;
                    } else if (i.getContainerType().startsWith("4")) {
                        t += 1D;
                    }
                }
            }
            header.setResveredField04(String.valueOf(t));
        } else {
            header.setTotalCases("0");
            header.setResveredField04("0.0");
        }
        waybillHeaderMapper.updateWaybillHeader(header);
    }

    @Override
    public int updateWaybillNoByBillStatus(WaybillHeader waybillHeader) {
        return waybillHeaderMapper.updateWaybillNoByBillStatus(waybillHeader);
    }

    @Override
    public int updateWaybillCheXiang(WaybillHeader waybillHeader) {
        return waybillHeaderMapper.updateWaybillCheXiang(waybillHeader);
    }

    @Override
    public Integer selectWaybillCount(WaybillHeader waybillHeader) {
        return waybillHeaderMapper.selectWaybillCount(waybillHeader);
    }

    @Override
    public List<WaybillHeader> selectShippingLine(WaybillHeader waybillHeader) {
        return waybillHeaderMapper.selectShippingLine(waybillHeader);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String queryAuditForSh(String data) {
        WaybillHeader header = JSONUtil.toBean(data, WaybillHeader.class);
        if (header != null) {
            if (StrUtil.isEmpty(header.getWaybillNo())) {
                return JSONUtil.parseObj(new R<>(Boolean.FALSE, "未接收到订单号！"), false).toStringPretty();
            }
            if (StrUtil.isEmpty(header.getCustomerNo())) {
                return JSONUtil.parseObj(new R<>(Boolean.FALSE, "未接收到用户账号！"), false).toStringPretty();
            }
            if (StrUtil.isEmpty(header.getPlatformCode())) {
                return JSONUtil.parseObj(new R<>(Boolean.FALSE, "未接收到平台编码！"), false).toStringPretty();
            }
        }
        WaybillHeader waybillHeader = waybillHeaderMapper.queryAuditForSh(header);
        if (waybillHeader != null) {
            if (StrUtil.isNotEmpty(waybillHeader.getBillStatus()) && "1".equals(waybillHeader.getBillStatus())) {
                //查询订单数据是否有过撤换箱
                BasChangeboxRetreat basChangeboxRetreat = new BasChangeboxRetreat();
                basChangeboxRetreat.setAppNo(waybillHeader.getOrderNo());
                basChangeboxRetreat.setWaybillNo(waybillHeader.getWaybillNo());
                basChangeboxRetreat.setDeleteFlag("N");
                List<BasChangeboxRetreat> basChangeboxRetreats = basChangeboxRetreatService.selectBasChangeboxRetreatList(basChangeboxRetreat);
                if (basChangeboxRetreats.size() > 0) {
                    for (BasChangeboxRetreat basChangebox : basChangeboxRetreats) {
                        if ("2".equals(basChangebox.getStatus())) {
                            waybillHeader.setIsUpdate("1");
                            break;
                        } else {
                            waybillHeader.setIsUpdate("0");
                            break;
                        }
                    }
                } else {
                    waybillHeader.setIsUpdate("1");
                }
            } else {
                waybillHeader.setIsUpdate("1");
            }
            return JSONUtil.parseObj(new R<>(Boolean.TRUE, waybillHeader), true).toStringPretty();
        } else {
            return JSONUtil.parseObj(new R<>(Boolean.FALSE, "未查询到该订单，请检查订单号！"), false).toStringPretty();
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveInfoForSh(String data) {
        WaybillHeaderDTO header = JSONUtil.toBean(data, WaybillHeaderDTO.class);
        String appNo = header.getAppNo();
        String waybillNo = header.getWaybillNo();
        WaybillHeader wh = new WaybillHeader();
        wh.setWaybillNo(waybillNo);
        wh.setBillStatus("0");
        List<WaybillHeader> list = waybillHeaderMapper.selectWaybillHeaderList(wh);
        if (list != null && list.size() != 0) {
            BasChangeboxRetreat basChangeboxRetreat = new BasChangeboxRetreat();
            basChangeboxRetreat.setAppNo(list.get(0).getOrderNo());
            basChangeboxRetreat.setWaybillNo(list.get(0).getWaybillNo());
            basChangeboxRetreat.setDeleteFlag("N");
            List<BasChangeboxRetreat> basChangeboxRetreats = basChangeboxRetreatService.selectBasChangeboxRetreatList(basChangeboxRetreat);
            if (basChangeboxRetreats.size() > 0) {
                for (BasChangeboxRetreat basChangebox : basChangeboxRetreats) {
                    if (!"2".equals(basChangebox.getStatus())) {
                        return JSONUtil.parseObj(new R<>(Boolean.FALSE, "当前运单尚未完成撤舱，不可导入表格数据！"), false).toStringPretty();
                    }
                }
            }
        } else {
            return JSONUtil.parseObj(new R<>(Boolean.FALSE, "当前运单非待补传资料状态，不可导入表格数据！"), false).toStringPretty();
        }
        WaybillContainerInfo wci = new WaybillContainerInfo();
        wci.setWaybillNo(waybillNo);
        wci.setDeleteFlag("N");
        List<WaybillContainerInfo> cnList = waybillContainerInfoMapper.selectWaybillContainerInfoList(wci);
        List<String> stringList = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder();
        for (WaybillContainerInfo containerInfo : cnList) {
            stringBuilder.append(containerInfo.getContainerNo()).append(",");
        }
        String usercode = header.getUpdateWho();
        String username = header.getUpdateWhoName();

        /*用以更新的集合*/
        /*货物参与方信息组装集合*/
        List<WaybillParticipants> listPants = new ArrayList<>();
        /*用以新增的集合*/
        /*货物参与方信息组装集合*/
        List<WaybillParticipants> listPantsSav = new ArrayList<>();

        List<PayCodeMes> listConCharge = new ArrayList<>();
        List<PayCodeMes> listConChargeDel = new ArrayList<>();

        List<WaybillGoodsInfo> goodsList = new ArrayList<>();
        List<WaybillGoodsInfo> goodsListDel = new ArrayList<>();

        List<WaybillContainerInfoShDTO> waybillContainerInfoList = header.getWaybillContainerInfoList();
        if (CollUtil.isNotEmpty(waybillContainerInfoList)) {
            //循环校验标识
            Boolean cirFlag = true;
            for (int i = 0; i < waybillContainerInfoList.size(); i++
            ) {
                WaybillContainerInfoShDTO info = waybillContainerInfoList.get(i);
                //发货人
                WaybillParticipants pants = info.getPants();
                //收货人
                WaybillParticipants pantsRec = info.getPantsRec();

                info.setWaybillNo(waybillNo);
                info.setUpdateWhoName(username);
                info.setUpdateWho(usercode);
                info.setUpdateTime(new Date());
                //组装收发货人信息
                if (pants != null) {
                    pants.setWaybillNo(waybillNo);
                }

                if (pantsRec != null) {
                    pantsRec.setWaybillNo(waybillNo);
                }

                String containerNo = info.getContainerNo();
                if (StrUtil.isEmpty(containerNo)) {
                    return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行箱号！"), false).toStringPretty();
                }

                String str = "";
                containerNo = containerNo.trim();
                str = containerNo;
                stringList.add(str);
                if (cirFlag == true) {
                    //如果excel中有大于等于一个的箱号和订舱时箱号匹配，那么校验通过cirFlag置为false,无须再次进入循环校验
                    if (stringBuilder.toString().contains(containerNo)) {
                        cirFlag = false;
//                        break;
                    } else {
                        return JSONUtil.parseObj(new R<>(Boolean.FALSE, "请检查<运单舱单信息>sheet页，箱号: " + containerNo + " 没有发现与初次订舱时箱号的匹配！"), false).toStringPretty();
                    }
                }

                boolean flag2 = verifyCntrCode(containerNo);
                if (!flag2) {
                    return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行箱号有误！"), false).toStringPretty();
                }
                if (StrUtil.isEmpty(info.getContainerType())) {
                    return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少箱型！"), false).toStringPretty();
                }
                if (info.getContainerDeadWeight() == null) {
                    return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少集装箱自重！"), false).toStringPretty();
                }
                if (StrUtil.isEmpty(info.getStationCompilation())) {
                    return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少发站站编！"), false).toStringPretty();
                } else {
                    //发站站名
                    String startName = stationMapper.selectStationMesByCode(info.getStationCompilation());
                    info.setStartStationName(startName);
                }
                if (StrUtil.isEmpty(info.getEndCompilation())) {
                    return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少到站站编！"), false).toStringPretty();
                } else {
                    //到站站名
                    String endName = stationMapper.selectStationMesByCode(info.getEndCompilation());
                    info.setEndStationName(endName);
                }
                if (StrUtil.isEmpty(info.getFrontierPortstation())) {
                    return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少国境口岸站编！"), false).toStringPretty();
                }
                if (StrUtil.isEmpty(info.getResveredField10())) {
                    return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少是否海关过境！"), false).toStringPretty();
                }
                if (StrUtil.isEmpty(info.getAbroadReachCity())) {
                    return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少境外到达城市！"), false).toStringPretty();
                }
                if (info.getGoodsValue() == null) {
                    return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少托运货物价值！"), false).toStringPretty();
                }
                if (StrUtil.isEmpty(info.getGoodsAmountTypeCode())) {
                    return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少货物金额类型代码！"), false).toStringPretty();
                }
                if (StrUtil.isEmpty(info.getFastTrain())) {
                    return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少快通准备！"), false).toStringPretty();
                } else {
                    if ("1".equals(info.getFastTrain())) {
                        if (StrUtil.isEmpty(info.getDeparturePlaceGq())) {
                            return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少出境启运地关区代码！"), false).toStringPretty();
                        }
                        if (StrUtil.isEmpty(info.getDeparturePlaceKa())) {
                            return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少出境启运地口岸代码！"), false).toStringPretty();
                        }
                    }
                }
                if (StrUtil.isEmpty(info.getBlockType())) {
                    return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少班列类别！"), false).toStringPretty();
                }

                if (pants != null) {
                    if (StrUtil.isEmpty(pants.getConsignorName())) {
                        return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少发货人名称！"), false).toStringPretty();
                    }
                    if (StrUtil.isEmpty(pants.getConsignorEnglishname()) && StrUtil.isEmpty(pants.getConsignorRussianame())) {
                        return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行中发货人英文、俄文名称须至少选填一项！"), false).toStringPretty();
                    }
                    if (StrUtil.isEmpty(pants.getCountryCode())) {
                        return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少发货人所属国家代码！"), false).toStringPretty();
                    }
                    //组装发货人
                    pants.setAppNo(appNo);
                    pants.setWaybillNo(waybillNo);
                    pants.setContainerNo(containerNo);
                    pants.setParticipantsType("F");

                    //校验新增/保存
                    List listTemp1 = waybillParticipantsMapper.selectWaybillParticipantsByWayBillNo(pants);
                    if (listTemp1 != null && listTemp1.size() != 0) {
                        //添加发货人for update
                        listPants.add(pants);
                    } else {
                        //添加发货人for save
                        listPantsSav.add(pants);
                    }
                } else {
                    return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少发货人信息！"), false).toStringPretty();
                }

                if (pantsRec != null) {
                    if (StrUtil.isEmpty(pantsRec.getConsignorName())) {
                        return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少收货人名称！"), false).toStringPretty();
                    }
                    if (StrUtil.isEmpty(pantsRec.getConsignorEnglishname()) && StrUtil.isEmpty(pantsRec.getConsignorRussianame())) {
                        return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行中收货人英文、俄文名称须至少选填一项！"), false).toStringPretty();
                    }
                    if (StrUtil.isEmpty(pantsRec.getCountryCode())) {
                        return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少收货人所属国家代码！"), false).toStringPretty();
                    }
                    //组装收货人
                    pantsRec.setAppNo(appNo);
                    pantsRec.setWaybillNo(waybillNo);
                    pantsRec.setContainerNo(containerNo);
                    pantsRec.setParticipantsType("S");
                    List listTemp2 = waybillParticipantsMapper.selectWaybillParticipantsByWayBillNo(pantsRec);
                    if (listTemp2 != null && listTemp2.size() != 0) {
                        pantsRec.setUpdateWhoName(username);
                        pantsRec.setUpdateWho(usercode);
                        pantsRec.setUpdateTime(LocalDateTime.now());
                        //添加收货人for update
                        listPants.add(pantsRec);
                    } else {
                        pantsRec.setAddTime(LocalDateTime.now());
                        pantsRec.setAddWho(usercode);
                        pantsRec.setAddWhoName(username);
                        //添加收货人for save
                        listPantsSav.add(pantsRec);
                    }
                } else {
                    return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少收货人！"), false).toStringPretty();
                }

                PayCodeMes payCodeMes = info.getPayCodeMes();
                if (payCodeMes != null) {
                    if (StrUtil.isEmpty(payCodeMes.getCarrierName())) {
                        return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少承运人简称！"), false).toStringPretty();
                    }
                    if (StrUtil.isEmpty(payCodeMes.getPayerName())) {
                        return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少支付人名称！"), false).toStringPretty();
                    }
                    if (StrUtil.isEmpty(payCodeMes.getPayerCode())) {
                        return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少支付人代码！"), false).toStringPretty();
                    }

                    PayCodeMes chargeDel = new PayCodeMes();
                    chargeDel.setWaybillNo(waybillNo);
                    chargeDel.setContainerNo(containerNo);
                    chargeDel.setDeleteFlag("Y");
                    listConChargeDel.add(chargeDel);

                    payCodeMes.setWaybillNo(waybillNo);
                    payCodeMes.setContainerNo(containerNo);
                    listConCharge.add(payCodeMes);
                } else {
                    return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少付费代码信息！"), false).toStringPretty();
                }

                WaybillGoodsInfo waybillGoodsInfo = info.getWaybillGoodsInfo();
                if (waybillGoodsInfo != null) {
                    if (StrUtil.isEmpty(waybillGoodsInfo.getGoodsCode())) {
                        return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少货物编码！"), false).toStringPretty();
                    }
                    if (StrUtil.isEmpty(waybillGoodsInfo.getGoodsChineseName())) {
                        return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少货物中文！"), false).toStringPretty();
                    }
                    if (StrUtil.isEmpty(waybillGoodsInfo.getPackageType())) {
                        return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少包装种类！"), false).toStringPretty();
                    }
                    if (StrUtil.isEmpty(waybillGoodsInfo.getGoodsNums())) {
                        return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少件数！"), false).toStringPretty();
                    }
                    if (waybillGoodsInfo.getGoodsWeight() == null) {
                        return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少货物重量！"), false).toStringPretty();
                    }
                    WaybillGoodsInfo goodsDel = new WaybillGoodsInfo();
                    goodsDel.setWaybillNo(waybillNo);
                    goodsDel.setContainerNo(containerNo);
                    goodsDel.setDeleteFlag("Y");
                    goodsListDel.add(goodsDel);

                    waybillGoodsInfo.setWaybillNo(waybillNo);
                    waybillGoodsInfo.setContainerNo(containerNo);
                    goodsList.add(waybillGoodsInfo);
                } else {
                    return JSONUtil.parseObj(new R<>(Boolean.FALSE, "第" + (i + 1) + "行缺少运单货物信息！"), false).toStringPretty();
                }
            }


            waybillContainerInfoMapper.updateContainerInfoBatchForSh(waybillContainerInfoList);

            if (listPants != null && listPants.size() != 0) {
                for (WaybillParticipants waybillParticipants : listPants
                ) {
                    waybillParticipantsMapper.updateWaybillParticipant(waybillParticipants);
                }
            }
            if (listPantsSav != null && listPantsSav.size() != 0) {
                waybillParticipantsMapper.insertWaybillParticipants(listPantsSav);
            }

            if (listConChargeDel != null && listConChargeDel.size() != 0) {
                if (CollUtil.isNotEmpty(listConChargeDel)) {
                    for (PayCodeMes payCodeMes : listConChargeDel
                    ) {
                        payCodeMesMapper.updatePayCodeMes(payCodeMes);
                    }
                }

            }
            if (listConCharge != null && listConCharge.size() != 0) {
                payCodeMesMapper.insertPayCodeMesBatch(listConCharge);
            }

            if (goodsListDel != null && goodsListDel.size() != 0) {
                if (CollUtil.isNotEmpty(goodsListDel)) {
                    for (WaybillGoodsInfo goodsInfo : goodsListDel
                    ) {
                        waybillGoodsInfoMapper.updateWaybillGoodsInfo2(goodsInfo);
                    }
                }

            }
            if (goodsList != null && goodsList.size() != 0) {
                waybillGoodsInfoMapper.insertWaybillGoodsInfo(goodsList);
            }
        } else {
            return JSONUtil.parseObj(new R<>(Boolean.FALSE, "缺少舱单信息！"), false).toStringPretty();
        }
        if (CollUtil.isNotEmpty(header.getFiles())) {
            List<UploadRecord> files = header.getFiles();
            for (UploadRecord uploadRecord : files
            ) {
                uploadRecord.setBillNo(waybillNo);
                uploadRecord.setBusiSeg("1");
                uploadRecord.setAddTime(LocalDateTime.now());
                uploadRecord.setAddWho(usercode);
                uploadRecord.setAddWhoName(username);
                uploadRecordMapper.insertUploadRecord(uploadRecord);
            }
        }
        WaybillHeader waybillHeader = new WaybillHeader();
        waybillHeader.setWaybillNo(waybillNo);
        waybillHeader.setBillStatus("1");
        waybillHeaderMapper.commitStatus(waybillHeader);
        return JSONUtil.parseObj(new R<>(Boolean.TRUE, "补充资料完成！"), false).toStringPretty();

    }

    //校验柜号是否正确
    /*
     * 1、第一部分由4位英文字母组成。前三位代码 (Owner Code) 主要说明箱主、经营人，第四位代码说明集装箱的类型。列如CBHU
     *  开头的标准集装箱是表明箱主和经营人为中远集运。
     * 2、 第二部分由6位数字组成。是箱体注册码（Registration Code），用于一个集装箱箱体持有的唯一标识。
     * 3、 第三部分为校验码（Check Digit）由前4位字母和6位数字经过校验规则运算得到，用于识别在校验时是否发生错误。即第11位数字。
     * 根据校验规则箱号的每个字母和数字都有一个运算的对应值。箱号的前10位字母和数字的对应值从0到Z对应数值为10到38，11、22、33不能对11取模数，所以要除去
     * 地址：https://blog.csdn.net/weixin_38611617/article/details/115069232
     */
    public static boolean verifyCntrCode(String strCode) {
        boolean result = true;
        strCode = strCode.trim();
        try {
            if (strCode.length() != 11) {
                return false;
            }
            if (strCode.startsWith("JSQ5") || strCode.startsWith("JSQ6")) {
                String str = strCode.substring(4);
                char[] codeChars = str.toCharArray();
                String charCode = "0123456789";
                for (int i = 0; i < 7; i++) {
                    int idx = charCode.indexOf(codeChars[i]);
                    if (idx == -1 || charCode.charAt(idx) == '?') {
                        return false;
                    }
                }
                return true;
            } else {
                char[] codeChars = strCode.toCharArray();
                String charCode = "0123456789A?BCDEFGHIJK?LMNOPQRSTU?VWXYZ";
                int num = 0;
                for (int i = 0; i < 10; i++) {
                    int idx = charCode.indexOf(codeChars[i]);
                    if (idx == -1 || charCode.charAt(idx) == '?') {
                        return false;
                    }
                    idx = (int) (idx * Math.pow(2, i));
                    num += idx;
                }
                num = (num % 11) % 10;
                result = Integer.parseInt(String.valueOf(codeChars[10])) == num;
            }

        } catch (Exception e) {
            result = false;
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R waybillExamine(WaybillHeader waybillHeader) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();

        WaybillHeader waybillHeader1 = new WaybillHeader();
        waybillHeader1.setRowId(waybillHeader.getRowId());
        waybillHeader1.setUpdateTime(new Date());
        waybillHeader1.setUpdateWho(userInfo.getUserName());
        waybillHeader1.setUpdateWhoName(userInfo.getRealName());
        if ("0".equals(waybillHeader.getAuditStatus())) {
            waybillHeader1.setAuditStatus("0");
            waybillHeader1.setBillStatus("0");
        } else {
            waybillHeader1.setAuditStatus("1");
            waybillHeader1.setBillStatus("2");
            //插入业务流程单费用表
            insertBusCost(waybillHeader1);
            //插入上级平台订单数据
            insertShareWaybill(waybillHeader);
            //插入后程转运数据
            insertPostTransport(waybillHeader1);
        }

        waybillHeaderMapper.updateWaybillHeader(waybillHeader1);
        //记录审核意见表
        Audiopinion audiopinion = new Audiopinion();
        audiopinion.setRowId(UUID.randomUUID().toString());
        audiopinion.setStatus(waybillHeader.getAuditStatus());
        audiopinion.setOrderNo(waybillHeader.getOrderNo());
        audiopinion.setWaybillNo(waybillHeader.getWaybillNo());
        audiopinion.setAuditNo(userInfo.getUserName());
        audiopinion.setAuditOpinion("YDSH");
        audiopinion.setAuditTime(LocalDateTime.now());
        audiopinion.setDeleteFlag("N");
        audiopinion.setAddTime(new Date());
        audiopinion.setAddWho(userInfo.getUserName());
        audiopinion.setAddWhoName(userInfo.getRealName());
        audiopinion.setResveredField01(waybillHeader.getPlatformCode());
        audiopinion.setResveredField02(waybillHeader.getOrderNo());
        if (!waybillHeader.getResveredField01().isEmpty()) {
            audiopinion.setResveredField03(waybillHeader.getResveredField01());
        }
        audiopinionMapper.insertAudiopinion(audiopinion);
        return new R<>(200, Boolean.TRUE, "审核成功");
    }

    @Override
    public void insertPostTransport(WaybillHeader waybillHeader) {
        WaybillHeader wh = waybillHeaderMapper.selectWaybillHeaderById(waybillHeader.getRowId());
        if (wh != null && StrUtil.isNotBlank(wh.getTrip()) && "R".equals(wh.getTrip())) {
            if (StrUtil.isNotBlank(wh.getCustomerNo()) && wh.getCustomerNo().contains("CUS")) {
                fdPostTransportService.insertPostTransport(wh);
            }
        }
    }

    /**
     * 插入上级平台订单数据
     *
     * @Param: waybillHeader
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/12 10:42
     **/
    @Override
    public void insertShareWaybill(WaybillHeader waybillHeader) {
        Shifmanagement sel = new Shifmanagement();
        sel.setShiftId(waybillHeader.getShiftNo());
        sel.setPlatformCode(waybillHeader.getPlatformCode());
        sel.setDeleteFlag("N");
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
        if (CollUtil.isEmpty(shifmanagements) || StrUtil.isEmpty(shifmanagements.get(0).getParentId())) {
            return;
        }
        //存在上级平台,获取上级平台班次
        Shifmanagement shifmanagement = shifmanagementMapper.selectShifmanagementById(shifmanagements.get(0).getParentId());
        if (shifmanagement == null) {
            return;
        }

        WaybillHeader oldHeader = waybillHeaderMapper.selectWaybillHeaderById(waybillHeader.getRowId());
        /*if(header == null || StrUtil.isEmpty(header.getWaybillNo())){
            return;
        }*/

        //新增业务流程单参数
        WaybillHeader addObj = new WaybillHeader();

        String oldCode = oldHeader.getWaybillNo();
        String waybillCode = "";
        WaybillHeader sel6 = new WaybillHeader();
        sel6.setShiftNo(shifmanagements.get(0).getShiftId());
        sel6.setCustomerNo(oldHeader.getPlatformCode());
        sel6.setPlatformCode(shifmanagement.getPlatformCode());
        sel6.setDeleteFlag("N");
        List<WaybillHeader> waybillHeaders = waybillHeaderMapper.selectWaybillHeaderList(sel6);
        if (CollUtil.isEmpty(waybillHeaders)) {
            //不存在上级平台订单，新增订单信息，否则合并内容
            BeanUtil.copyProperties(oldHeader, addObj);
            waybillCode = sysNoConfigService.genNo("YD");
            addObj.setRowId(UUID.randomUUID().toString());
            addObj.setWaybillNo(waybillCode);
            addObj.setOrderNo(null);
            addObj.setCustomerNo(oldHeader.getPlatformCode());
            addObj.setCustomerName(oldHeader.getPlatformName());
            addObj.setPlatformCode(shifmanagement.getPlatformCode());
            CustomerInfo sel2 = new CustomerInfo();
            sel2.setCustomerCode(shifmanagement.getPlatformCode());
            sel2.setDeleteFlag("N");
            List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoList(sel2);
            if (CollUtil.isNotEmpty(customerInfos)) {
                addObj.setPlatformName(customerInfos.get(0).getCompanyName());
            }
            addObj.setBillStatus("2");
            addObj.setAuditStatus("1");
            addObj.setDeleteFlag("N");
            addObj.setAddWho(SecurityUtils.getUserInfo().getUserName());
            addObj.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
            addObj.setAddTime(new Date());
            waybillHeaderMapper.insertWaybillHeader(addObj);
//            addObj.setRowId(header.getRowId());
        } else {
            waybillCode = waybillHeaders.get(0).getWaybillNo();
            addObj.setRowId(waybillHeaders.get(0).getRowId());
        }


        WaybillContainerInfo sel3 = new WaybillContainerInfo();
        sel3.setWaybillNo(oldCode);
        sel3.setDeleteFlag("N");
        List<WaybillContainerInfo> waybillContainerInfos = waybillContainerInfoMapper.selectWaybillContainerInfoList(sel3);
        if (CollUtil.isNotEmpty(waybillContainerInfos)) {
            for (WaybillContainerInfo waybillContainerInfo : waybillContainerInfos
            ) {
                waybillContainerInfo.setRowId(UUID.randomUUID().toString());
                waybillContainerInfo.setWaybillNo(waybillCode);
                waybillContainerInfo.setOrderNo(null);
                waybillContainerInfo.setDeleteFlag("N");
                waybillContainerInfo.setAddWho(SecurityUtils.getUserInfo().getUserName());
                waybillContainerInfo.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                waybillContainerInfo.setAddTime(new Date());
                waybillContainerInfoMapper.insertWaybillContainerInfo(waybillContainerInfo);
            }
        }

        WaybillGoodsInfo sel4 = new WaybillGoodsInfo();
        sel4.setWaybillNo(oldCode);
        sel4.setDeleteFlag("N");
        List<WaybillGoodsInfo> waybillGoodsInfos = waybillGoodsInfoMapper.selectWaybillGoodsInfoList(sel4);
        if (CollUtil.isNotEmpty(waybillGoodsInfos)) {
            for (WaybillGoodsInfo waybillGoodsInfo : waybillGoodsInfos
            ) {
                waybillGoodsInfo.setRowId(UUID.randomUUID().toString());
                waybillGoodsInfo.setWaybillNo(waybillCode);
                waybillGoodsInfo.setDeleteFlag("N");
                waybillGoodsInfo.setAddWho(SecurityUtils.getUserInfo().getUserName());
                waybillGoodsInfo.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                waybillGoodsInfo.setAddTime(LocalDateTime.now());
            }
            waybillGoodsInfoMapper.insertWaybillGoodsInfo(waybillGoodsInfos);
        }

        WaybillParticipants sel5 = new WaybillParticipants();
        sel5.setWaybillNo(oldCode);
        sel5.setDeleteFlag("N");
        List<WaybillParticipants> waybillParticipants = waybillParticipantsMapper.selectWaybillParticipantsListByLike(sel5);
        if (CollUtil.isNotEmpty(waybillParticipants)) {
            for (WaybillParticipants wp : waybillParticipants
            ) {
                wp.setRowId(UUID.randomUUID().toString());
                wp.setWaybillNo(waybillCode);
                wp.setDeleteFlag("N");
                wp.setAddWho(SecurityUtils.getUserInfo().getUserName());
                wp.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                wp.setAddTime(LocalDateTime.now());
            }
            waybillParticipantsMapper.insertWaybillParticipants(waybillParticipants);
        }

        //附件
        UploadRecord sel7 = new UploadRecord();
        sel7.setBillNo(oldCode);
        sel7.setDeleteFlag("N");
        List<UploadRecord> uploadRecords = uploadRecordMapper.selectUploadRecordList(sel7);
        if (CollUtil.isNotEmpty(uploadRecords)) {
            for (UploadRecord uploadRecord : uploadRecords
            ) {
                uploadRecord.setRowId(UUID.randomUUID().toString());
                uploadRecord.setBillNo(waybillCode);
                uploadRecord.setAddWho(SecurityUtils.getUserInfo().getUserName());
                uploadRecord.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                uploadRecord.setAddTime(LocalDateTime.now());
                uploadRecordMapper.insertUploadRecord(uploadRecord);
            }
        }

        //更新运单主表占用舱位数
        waybillHeaderMapper.updateResveredField04(waybillCode);
        waybillHeaderMapper.updateTotalCases(waybillCode);
        insertBusCost(addObj);
        if (shifmanagement != null && StrUtil.isNotBlank(shifmanagement.getParentId()) && !shifmanagement.getParentId().equals(shifmanagement.getRowId())) {
            insertShareWaybill(addObj);
        }
    }

    /**
     * 插入业务流程单费用表
     *
     * @Param: waybillHeader
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/04 14:31
     **/
    public void insertBusCost(WaybillHeader waybillHeader) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        WaybillHeader wh = waybillHeaderMapper.selectWaybillHeaderById(waybillHeader.getRowId());

        FdBusCost sel = new FdBusCost();
        sel.setPlatformCode(wh.getPlatformCode());
        sel.setCustomerCode(wh.getCustomerNo());
        sel.setPlatformLevel("0");
        sel.setShiftNo(wh.getShiftNo());
        sel.setDeleteFlag("N");
        List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel);
        FdBusCostWaybill fdBusCostWaybill = new FdBusCostWaybill();
        if (CollUtil.isNotEmpty(fdBusCosts)) {
            fdBusCostWaybill.setCostCode(fdBusCosts.get(0).getCostCode());
        } else {
            //主数据
            FdBusCost fdBusCost = new FdBusCost();
            fdBusCost.setCostCode(sysNoConfigService.genNo("FDC"));
            fdBusCost.setPlatformCode(wh.getPlatformCode());
            fdBusCost.setPlatformName(wh.getPlatformName());
            fdBusCost.setCustomerCode(wh.getCustomerNo());
            fdBusCost.setCustomerName(wh.getCustomerName());
            fdBusCost.setPlatformLevel("0");
            fdBusCost.setShiftNo(wh.getShiftNo());
            fdBusCost.setAuditStatus("0");
            fdBusCost.setAddWho(userInfo.getUserName());
            fdBusCost.setAddWhoName(userInfo.getRealName());
            fdBusCost.setAddTime(LocalDateTime.now());
            fdBusCost.setPrincipalName(wh.getCustomerName());

            CustomerPlatformInfo sel2 = new CustomerPlatformInfo();
            sel2.setPlatformCode(wh.getPlatformCode());
            sel2.setCustomerCode(wh.getCustomerNo());
            sel2.setDeleteFlag("N");
            List<CustomerPlatformInfo> list = customerPlatformInfoMapper.selectCustomerPlatformInfoList(sel2);
            if (CollUtil.isNotEmpty(list)) {
                fdBusCost.setContactsName(list.get(0).getContactPerson());
                fdBusCost.setContactsPhone(list.get(0).getContactNo());
            } else {
                sel2.setPlatformCode(null);
                List<CustomerPlatformInfo> list2 = customerPlatformInfoMapper.selectCustomerPlatformInfoList(sel2);
                if (CollUtil.isNotEmpty(list2)) {
                    fdBusCost.setContactsName(list2.get(0).getContactPerson());
                    fdBusCost.setContactsPhone(list2.get(0).getContactNo());
                }
            }
            fdBusCost = fdBusCostService.selectFdBusCostOld(fdBusCost, wh.getTrip());
            fdBusCostMapper.insertFdBusCost(fdBusCost);

            fdBusCostWaybill.setCostCode(fdBusCost.getCostCode());
        }

        FdBusCostWaybill sel2 = new FdBusCostWaybill();
        sel2.setCostCode(fdBusCostWaybill.getCostCode());
        sel2.setWaybillNo(wh.getWaybillNo());
        sel2.setDeleteFlag("N");
        List<FdBusCostWaybill> fdBusCostWaybills = fdBusCostWaybillMapper.selectFdBusCostWaybillList(sel2);
        if (CollUtil.isEmpty(fdBusCostWaybills)) {
            fdBusCostWaybill.setWaybillNo(wh.getWaybillNo());
            fdBusCostWaybill.setApplicationNumber(wh.getOrderNo());
            fdBusCostWaybill.setAuditStatus("0");
            fdBusCostWaybill.setAddWho(userInfo.getUserName());
            fdBusCostWaybill.setAddWhoName(userInfo.getRealName());
            fdBusCostWaybill.setAddTime(LocalDateTime.now());
            fdBusCostWaybillMapper.insertFdBusCostWaybill(fdBusCostWaybill);
        }
    }
}
