package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.BillSubPayCity;
import com.huazheng.tunny.ocean.api.vo.BillDealWithCityAndCostVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 应付账单（市）子账单表 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-06-11 16:26:08
 */
public interface BillSubPayCityService extends IService<BillSubPayCity> {
    /**
     * 查询应付账单（市）子账单表信息
     *
     * @param id 应付账单（市）子账单表ID
     * @return 应付账单（市）子账单表信息
     */
    public BillSubPayCity selectBillSubPayCityById(Integer id);

    /**
     * 查询应付账单（市）子账单表列表
     *
     * @param billSubPayCity 应付账单（市）子账单表信息
     * @return 应付账单（市）子账单表集合
     */
    public List<BillSubPayCity> selectBillSubPayCityList(BillSubPayCity billSubPayCity);


    /**
     * 分页模糊查询应付账单（市）子账单表列表
     * @return 应付账单（市）子账单表集合
     */
    public Page selectBillSubPayCityListByLike(Query query);



    /**
     * 新增应付账单（市）子账单表
     *
     * @param billSubPayCity 应付账单（市）子账单表信息
     * @return 结果
     */
    public int insertBillSubPayCity(BillSubPayCity billSubPayCity);

    /**
     * 修改应付账单（市）子账单表
     *
     * @param billSubPayCity 应付账单（市）子账单表信息
     * @return 结果
     */
    public int updateBillSubPayCity(BillSubPayCity billSubPayCity);

    /**
     * 删除应付账单（市）子账单表
     *
     * @param id 应付账单（市）子账单表ID
     * @return 结果
     */
    public int deleteBillSubPayCityById(Integer id);

    /**
     * 批量删除应付账单（市）子账单表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteBillSubPayCityByIds(Integer[] ids);

    List<BillDealWithCityAndCostVO> selectFdBillSubByBillNo( String billNo, String customerName, String platformCode);

    List<BillDealWithCityAndCostVO>  selectFdBillSubByShiftNo(String shiftNo, String platformCode);

    List<String> selectBillOfCostByBillCode(String billNo);

    String selectShiftNoBySubBillNo(List<String> billNos);
    String selectProvinceShiftNoBySubBillNo(List<String> billNos);
    String selectCustomerShiftNoBySubBillNo(List<String> billNos);

    int updateStatusBySubBill(List<String> subBillNos,String status);
    int updateProvinceStatusBySubBill(List<String> subBillNos,String status);
    int updateCustomerStatusBySubBill(List<String> subBillNos,String status);


    BigDecimal selectSumAmountByBalanceNo(String billNo);
    BigDecimal selectProvinceSumAmountByBalanceNo(String billNo);
    BigDecimal selectCustomerSumAmountByBalanceNo(String billNo);
}

