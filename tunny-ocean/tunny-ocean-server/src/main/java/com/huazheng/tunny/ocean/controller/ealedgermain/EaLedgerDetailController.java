package com.huazheng.tunny.ocean.controller.ealedgermain;

import com.huazheng.tunny.ocean.api.entity.ealedgermain.EaLedgerDetail;
import com.huazheng.tunny.ocean.api.entity.ealedgermain.EaLedgerMain;
import com.huazheng.tunny.ocean.api.vo.ealedgermain.EaLedgerTieLuFeeVo;
import com.huazheng.tunny.ocean.service.ealedgermain.EaLedgerDetailService;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Update;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 台账细表
 *
 * <AUTHOR>
 * @date 2025-07-02 15:42:19
 */
@Slf4j
@RestController
@RequestMapping("/ealedgerdetail")
public class EaLedgerDetailController {

    @Autowired
    private EaLedgerDetailService eaLedgerDetailService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  eaLedgerDetailService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return eaLedgerDetailService.selectEaLedgerDetailListByLike(new Query<>(params));
    }

    /**
     * 信息
     * @param ledgerDetailId
     * @return R
     */
    @GetMapping("/{ledgerDetailId}")
    public R info(@PathVariable("ledgerDetailId") Long ledgerDetailId) {
        EaLedgerDetail eaLedgerDetail =eaLedgerDetailService.selectById(ledgerDetailId);
        return new R<>(eaLedgerDetail);
    }

    /**
     * 信息
     * @param params
     * @return R
     */
    @GetMapping("/containerFeeList")
    public R containerFeeList(@RequestParam Map<String, Object> params) {
        return eaLedgerDetailService.containerFeeList(params);
    }

    /**
     * 信息
     * @param list
     * @return R
     */
    @PutMapping("/updateTieLuFee")
    public R updateTieLuFee(@RequestBody List<EaLedgerTieLuFeeVo> list) {
        return eaLedgerDetailService.updateTieLuFee(list);
    }


    /**
     * 保存
     * @param eaLedgerDetail
     * @return R
     */
    @PostMapping
    public R save(@RequestBody EaLedgerDetail eaLedgerDetail) {
        eaLedgerDetailService.insert(eaLedgerDetail);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param eaLedgerDetail
     * @return R
     */
    @PutMapping
    public R update(@RequestBody EaLedgerDetail eaLedgerDetail) {
        eaLedgerDetailService.updateById(eaLedgerDetail);
        return new R<>(Boolean.TRUE);
    }



    /**
     * 删除
     * @param ledgerDetailId
     * @return R
     */
    @DeleteMapping("/{ledgerDetailId}")
    public R delete(@PathVariable  Long ledgerDetailId) {
        eaLedgerDetailService.deleteById(ledgerDetailId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param ledgerDetailIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<Long> ledgerDetailIds) {
        eaLedgerDetailService.deleteBatchIds(ledgerDetailIds);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 导出台账数据为 Excel
     *
     * @param eaLedgerDetail 台账明细数据
     * @param response          Http 响应对象
     * @throws IOException IO异常
     */
    @PostMapping("/exportEaLedgerDetailExcel")
    public void exportShippingAccountExcel(@RequestBody EaLedgerDetail eaLedgerDetail, HttpServletResponse response) throws IOException {
        eaLedgerDetailService.exportEaLedgerDetailExcel(eaLedgerDetail, response);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<EaLedgerDetail> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = eaLedgerDetailService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<EaLedgerDetail> list = reader.readAll(EaLedgerDetail.class);
        eaLedgerDetailService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }
}
