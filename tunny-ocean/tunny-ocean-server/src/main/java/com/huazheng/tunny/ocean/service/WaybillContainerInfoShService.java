package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.WaybillContainerInfo;
import com.huazheng.tunny.ocean.api.entity.WaybillContainerInfoSh;
import com.huazheng.tunny.ocean.api.vo.ContainerInfoVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 运单集装箱信息表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-05 10:57:25
 */
public interface WaybillContainerInfoShService extends IService<WaybillContainerInfoSh> {

    /**
     * 当前运单下集装箱、货物、参与方分页信息
     */
    public Page selectConGoodsPantsPage(Query query);

    /**
     * 运费总金额
     */
    public BigDecimal getTotalAmount(String waybillNo) ;
}

