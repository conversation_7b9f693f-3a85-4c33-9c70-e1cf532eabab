package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.ocean.api.entity.*;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 换箱申请主表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-08-25 18:13:58
 */
public interface BasChangeboxRetreatService extends IService<BasChangeboxRetreat> {
    /**
     * 查询换箱申请主表信息
     *
     * @param rowId 换箱申请主表ID
     * @return 换箱申请主表信息
     */
    public BasChangeboxRetreat selectBasChangeboxRetreatById(String rowId);

    /**
     * 查询换箱申请主表列表
     *
     * @param basChangeboxRetreat 换箱申请主表信息
     * @return 换箱申请主表集合
     */
    public List<BasChangeboxRetreat> selectBasChangeboxRetreatList(BasChangeboxRetreat basChangeboxRetreat);


    /**
     * 分页模糊查询换箱申请主表列表
     * @return 换箱申请主表集合
     */
    public Page selectBasChangeboxRetreatListByLike(Query query);

    public Page cityPage(Query query);

    /**
     * 新增换箱申请主表
     *
     * @param basChangeboxRetreat 换箱申请主表信息
     * @return 结果
     */
    public int insertBasChangeboxRetreat(BasChangeboxRetreat basChangeboxRetreat);

    /**
     * 修改换箱申请主表
     *
     * @param basChangeboxRetreat 换箱申请主表信息
     * @return 结果
     */
    public int updateBasChangeboxRetreat(BasChangeboxRetreat basChangeboxRetreat);

    /**
     * 删除换箱申请主表
     *
     * @param rowId 换箱申请主表ID
     * @return 结果
     */
    public int deleteBasChangeboxRetreatById(String rowId);

    /**
     * 批量删除换箱申请主表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteBasChangeboxRetreatByIds(Integer[] rowIds);

    public void updateAllfreight(String businessid);

    public int updateByRowId(BasChangeboxRetreat basChangeboxRetreat);

    public R retreat2(BasChangeboxRetreat basChangeboxRetreat,String auditType);

    public R provinceRetreat(BasChangeboxRetreat basChangeboxRetreat);

    public R reject(BasChangeboxRetreat basChangeboxRetreat);

    public R saveCustomer(BasChangeboxRetreat basChangeboxRetreat,String status);

    public R saveCity(BasChangeboxRetreat basChangeboxRetreat,String status);

    public R examineNew(BasChangeboxRetreat basChangeboxRetreat);

    public R saveMainInfo(Shifmanagement shifmanagement);

    public R saveMainInfoCity(Shifmanagement shifmanagement);

    public R imported(MultipartFile file, String businessid);

    public void insertBillProvinceSub(SecruityUser userInfo, String yfBillSubCode, List<FdShippingAccount> fdShippingAccounts, List<BillPayProvinceSub> billPayProvinceSubs);

    public void exportedTz(HttpServletResponse response) throws Exception;

    public R importedTz(MultipartFile file, String businessid);
}

