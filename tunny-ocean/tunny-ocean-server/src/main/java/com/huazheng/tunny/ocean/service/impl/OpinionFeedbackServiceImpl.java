package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.OpinionFeedbackMapper;
import com.huazheng.tunny.ocean.api.entity.OpinionFeedback;
import com.huazheng.tunny.ocean.service.OpinionFeedbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("opinionFeedbackService")
public class OpinionFeedbackServiceImpl extends ServiceImpl<OpinionFeedbackMapper, OpinionFeedback> implements OpinionFeedbackService {

    @Autowired
    private OpinionFeedbackMapper opinionFeedbackMapper;

    /**
     * 查询意见反馈信息
     *
     * @param rowId 意见反馈ID
     * @return 意见反馈信息
     */
    @Override
    public OpinionFeedback selectOpinionFeedbackById(String rowId) {
        return opinionFeedbackMapper.selectOpinionFeedbackById(rowId);
    }

    /**
     * 查询意见反馈列表
     *
     * @param opinionFeedback 意见反馈信息
     * @return 意见反馈集合
     */
    @Override
    public List<OpinionFeedback> selectOpinionFeedbackList(OpinionFeedback opinionFeedback) {
        return opinionFeedbackMapper.selectOpinionFeedbackList(opinionFeedback);
    }


    /**
     * 分页模糊查询意见反馈列表
     *
     * @return 意见反馈集合
     */
    @Override
    public Page selectOpinionFeedbackListByLike(Query query) {
        OpinionFeedback opinionFeedback = BeanUtil.mapToBean(query.getCondition(), OpinionFeedback.class, false);
        query.setRecords(opinionFeedbackMapper.selectOpinionFeedbackListByLike(query, opinionFeedback));
        return query;
    }

    /**
     * 新增意见反馈
     *
     * @param opinionFeedback 意见反馈信息
     * @return 结果
     */
    @Override
    public int insertOpinionFeedback(OpinionFeedback opinionFeedback) {
        return opinionFeedbackMapper.insertOpinionFeedback(opinionFeedback);
    }

    /**
     * 修改意见反馈
     *
     * @param opinionFeedback 意见反馈信息
     * @return 结果
     */
    @Override
    public int updateOpinionFeedback(OpinionFeedback opinionFeedback) {
        return opinionFeedbackMapper.updateOpinionFeedback(opinionFeedback);
    }


    /**
     * 删除意见反馈
     *
     * @param rowId 意见反馈ID
     * @return 结果
     */
    @Override
    public int deleteOpinionFeedbackById(String rowId) {
        return opinionFeedbackMapper.deleteOpinionFeedbackById(rowId);
    }

    ;


    /**
     * 批量删除意见反馈对象
     *
     * @return 结果
     */
    @Override
    public int deleteOpinionFeedbackByIds(Integer[] rowIds) {
        return opinionFeedbackMapper.deleteOpinionFeedbackByIds(rowIds);
    }

}
