package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.RelCityNation;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 国别/城市关系表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-01 09:21:23
 */
public interface RelCityNationService extends IService<RelCityNation> {
    /**
     * 查询国别/城市关系表信息
     *
     * @param rowId 国别/城市关系表ID
     * @return 国别/城市关系表信息
     */
    public RelCityNation selectRelCityNationById(String rowId);

    /**
     * 查询国别/城市关系表列表
     *
     * @param relCityNation 国别/城市关系表信息
     * @return 国别/城市关系表集合
     */
    public List<RelCityNation> selectRelCityNationList(RelCityNation relCityNation);


    /**
     * 分页模糊查询国别/城市关系表列表
     * @return 国别/城市关系表集合
     */
    public Page selectRelCityNationListByLike(Query query);



    /**
     * 新增国别/城市关系表
     *
     * @param relCityNation 国别/城市关系表信息
     * @return 结果
     */
    public int insertRelCityNation(RelCityNation relCityNation);

    /**
     * 修改国别/城市关系表
     *
     * @param relCityNation 国别/城市关系表信息
     * @return 结果
     */
    public int updateRelCityNation(RelCityNation relCityNation);

    /**
     * 删除国别/城市关系表
     *
     * @param rowId 国别/城市关系表ID
     * @return 结果
     */
    public int deleteRelCityNationById(String rowId);

    /**
     * 批量删除国别/城市关系表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteRelCityNationByIds(Integer[] rowIds);

}

