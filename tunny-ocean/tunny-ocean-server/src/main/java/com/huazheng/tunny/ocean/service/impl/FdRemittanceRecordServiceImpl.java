package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.enums.BalanceStatusEnum;
import com.huazheng.tunny.ocean.api.vo.BillBalanceMainCityDetailVO;
import com.huazheng.tunny.ocean.api.vo.BillBalanceMainCityListVO;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.BillBalanceMainCityService;
import com.huazheng.tunny.ocean.service.FdBalanceDetailService;
import com.huazheng.tunny.ocean.service.FdRemittanceRecordService;
import com.huazheng.tunny.ocean.service.SysNoConfigService;
import com.huazheng.tunny.ocean.util.PermissionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service("fdRemittanceRecordService")
public class FdRemittanceRecordServiceImpl extends ServiceImpl<FdRemittanceRecordMapper, FdRemittanceRecord> implements FdRemittanceRecordService {

    @Autowired
    private FdRemittanceRecordMapper fdRemittanceRecordMapper;
    @Autowired
    private PermissionUtil permissionUtil;
    @Autowired
    private SysNoConfigService sysNoConfigService;
    @Autowired
    private FdTradingDetailsMapper fdTradingDetailsMapper;
    @Autowired
    private FdBalanceDetailMapper fdBalanceDetailMapper;

    @Autowired
    private BillBalanceMainCityService billBalanceMainCityService;

    @Autowired
    private FdBalanceDetailService fdBalanceDetailService;

    @Autowired
    private BillBalanceMainCityMapper billBalanceMainCityMapper;
    @Autowired
    private BillBalanceBindingCityMapper billBalanceBindingCityMapper;

    /**
     * 查询汇款记录表信息
     *
     * @param id 汇款记录表ID
     * @return 汇款记录表信息
     */
    @Override
    public FdRemittanceRecord selectFdRemittanceRecordById(Long id) {
        FdRemittanceRecord fdRemittanceRecord = fdRemittanceRecordMapper.selectFdRemittanceRecordById(id);
        //根据汇款单号单号查询认领流水
        FdTradingDetails fdTradingDetails = new FdTradingDetails();
        fdTradingDetails.setDelFlag("N");
        fdTradingDetails.setTradingStatus("1");
        fdTradingDetails.setPaymentType("2");
        fdTradingDetails.setRemittanceRecordCode(fdRemittanceRecord.getRemittanceRecordCode());
        List<FdTradingDetails> fdTradingDetailsList = fdTradingDetailsMapper.selectFdTradingDetailsList(fdTradingDetails);
        List<BillBalanceMainCity> billBalanceMainCities = new ArrayList<>();
        if (!fdTradingDetailsList.isEmpty()) {
            String balanceBillNos = fdTradingDetailsList.stream().map(FdTradingDetails::getDeductionBillCode).filter(code -> code != null && !code.isEmpty()).collect(Collectors.joining(","));
            //根据单号查询认领结算单
            BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
            billBalanceMainCity.setBalanceBillNos(balanceBillNos);
            billBalanceMainCity.setBalanceStatus(BalanceStatusEnum.VERIFIED.getKey());
            billBalanceMainCity.setDeleteFlag("N");
            billBalanceMainCities = billBalanceMainCityService.selectBillBalanceMainCityList(billBalanceMainCity);
            for (BillBalanceMainCity billBalanceMainCityListVO : billBalanceMainCities) {
                billBalanceMainCityListVO.setProvinceTrainNum(billBalanceMainCityMapper.selectProvinceShiftNoByShiftNo(billBalanceMainCityListVO.getShiftNos().split(",")));
                // 班次名称
                billBalanceMainCityListVO.setShiftNames(billBalanceMainCityMapper.selectShiftNamesByShiftNo(billBalanceMainCityListVO.getShiftNos().split(",")));
            }
        }

        if (billBalanceMainCities.isEmpty()) {
            fdRemittanceRecord.setBillBalanceMainCityList(new ArrayList<>());
            fdRemittanceRecord.setClearedAmount(BigDecimal.valueOf(0));
            fdRemittanceRecord.setResidualAmount(fdRemittanceRecord.getRemittanceAmount());
        } else {
            fdRemittanceRecord.setBillBalanceMainCityList(billBalanceMainCities);
            //计算收款余额
            BigDecimal useMoney = billBalanceMainCities.stream().map(BillBalanceMainCity::getActualAmout).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            fdRemittanceRecord.setClearedAmount(useMoney);
            fdRemittanceRecord.setResidualAmount(fdRemittanceRecord.getRemittanceAmount().subtract(useMoney));
        }
        return fdRemittanceRecord;
    }

    /**
     * 查询汇款记录表列表
     *
     * @param fdRemittanceRecord 汇款记录表信息
     * @return 汇款记录表集合
     */
    @Override
    public List<FdRemittanceRecord> selectFdRemittanceRecordList(FdRemittanceRecord fdRemittanceRecord) {
        return fdRemittanceRecordMapper.selectFdRemittanceRecordList(fdRemittanceRecord);
    }

    @Override
    public Page page2(Query query) {
        FdRemittanceRecord fdRemittanceRecord = BeanUtil.mapToBean(query.getCondition(), FdRemittanceRecord.class, false);
        query.setRecords(fdRemittanceRecordMapper.selectFdRemittanceRecordList(fdRemittanceRecord));
        return query;
    }


    /**
     * 分页模糊查询汇款记录表列表
     *
     * @return 汇款记录表集合
     */
    @Override
    public Page selectFdRemittanceRecordListByLike(Query query) {
        if (StrUtil.isBlank(query.getOrderByField())) {
            query.setOrderByField("x.claim_status, x.collection_time");
            query.setAsc(Boolean.FALSE);
        }
        FdRemittanceRecord fdRemittanceRecord = BeanUtil.mapToBean(query.getCondition(), FdRemittanceRecord.class, false);
        query.setRecords(fdRemittanceRecordMapper.selectFdRemittanceRecordListByLike(query, fdRemittanceRecord));
        setRemittanceAmount(query.getRecords());
        return query;
    }

    /**
     * 根据用户code查询汇款未使用明细
     *
     * @param fdRemittanceRecord
     * @return
     */
    @Override
    public R getInfoByCustomerCode(FdRemittanceRecord fdRemittanceRecord) {
        List<FdRemittanceRecord> records = fdRemittanceRecordMapper.selectInfoByCustomerCode(fdRemittanceRecord);
        return new R<>(200, true, records);
    }

    @Override
    public R selectInfoByBillCode(String billCode, String incomeFlag) {
        List<FdRemittanceRecord> records = fdRemittanceRecordMapper.selectInfoByBillCode(billCode, incomeFlag);
        return new R<>(200, true, records);
    }

    /**
     * 汇款一级列表
     *
     * @param query
     * @return
     */
    @Override
    public Page selectRemittanceRecordListPage(Query query) {
        FdRemittanceRecord fdRemittanceRecord = BeanUtil.mapToBean(query.getCondition(), FdRemittanceRecord.class, false);
        query.setRecords(fdRemittanceRecordMapper.selectRemittanceRecordListPage(query, fdRemittanceRecord));
        setRemittanceAmount(query.getRecords());
        return query;
    }


    /**
     * 根据收款记录汇总结算总金额
     *
     * @Param: null
     * @Return:
     * @Author: Howe
     * @Date: 2024/6/19 下午1:06
     **/
    private List<FdRemittanceRecord> setRemittanceAmount(List<FdRemittanceRecord> list) {
        for (FdRemittanceRecord fdRemittanceRecord : list) {
            //计算结算单的应付金额
            FdTradingDetails fdTradingDetails = new FdTradingDetails();
            fdTradingDetails.setDelFlag("N");
            fdTradingDetails.setTradingStatus("1");
            fdTradingDetails.setPaymentType("2");
            fdTradingDetails.setRemittanceRecordCode(fdRemittanceRecord.getRemittanceRecordCode());
            fdTradingDetails.setCustomerCode(fdRemittanceRecord.getCustomerNo());
            fdTradingDetails.setPlatformCode(fdRemittanceRecord.getPlatformCode());
            if ("1".equals(fdRemittanceRecord.getPlatformFlag())) {
                fdTradingDetails.setPlatformLevel("0");
            } else if ("2".equals(fdRemittanceRecord.getPlatformFlag()) && "1".equals(fdRemittanceRecord.getCustomerFlag())) {
                fdTradingDetails.setPlatformLevel("1");
            } else if ("2".equals(fdRemittanceRecord.getPlatformFlag()) && "2".equals(fdRemittanceRecord.getCustomerFlag())) {
                fdTradingDetails.setPlatformLevel("2");
            }
            List<FdTradingDetails> fdTradingDetailsList = fdTradingDetailsMapper.selectFdTradingDetailsList(fdTradingDetails);
            //计算收款余额中的使用余额
            fdTradingDetails.setPaymentType("3");
            List<FdTradingDetails> balanceTradingDetailsList = fdTradingDetailsMapper.selectFdTradingDetailsList(fdTradingDetails);
            fdTradingDetailsList.addAll(balanceTradingDetailsList);
            if (!fdTradingDetailsList.isEmpty()) {
                BigDecimal useMoney = fdTradingDetailsList.stream().map(FdTradingDetails::getTransactionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                fdRemittanceRecord.setClearedAmount(useMoney);
            } else {
                fdRemittanceRecord.setClearedAmount(BigDecimal.valueOf(0));
            }
            //查询收款余额中的剩余余额
            FdBalanceDetail fdBalanceDetail = new FdBalanceDetail();
            fdBalanceDetail.setPlatformCode(fdRemittanceRecord.getPlatformCode());
            fdBalanceDetail.setCustomerCode(fdRemittanceRecord.getCustomerNo());
            fdBalanceDetail.setBillCode(fdRemittanceRecord.getRemittanceRecordCode());
            if ("1".equals(fdRemittanceRecord.getPlatformCode())) {
                fdBalanceDetail.setPlatformLevel("0");
            } else if ("2".equals(fdRemittanceRecord.getPlatformFlag()) && "1".equals(fdRemittanceRecord.getCustomerFlag())) {
                fdBalanceDetail.setPlatformLevel("1");
            } else if ("2".equals(fdRemittanceRecord.getPlatformFlag()) && "2".equals(fdRemittanceRecord.getCustomerFlag())) {
                fdBalanceDetail.setPlatformLevel("2");
            }

            //查询收款余额中的被结算的余额
            fdBalanceDetail = fdBalanceDetailMapper.selectTotalAmountRemainingAmount(fdBalanceDetail);

            if (fdBalanceDetail != null) {
                //收款余额中剩余余额
                fdRemittanceRecord.setResidualAmount(fdBalanceDetail.getRemainingAmount() == null ? BigDecimal.valueOf(0) : fdBalanceDetail.getRemainingAmount());
            } else {
                fdRemittanceRecord.setResidualAmount(BigDecimal.valueOf(0));
            }
            //查询未认领的汇款余额
            FdRemittanceRecord unclaimed = new FdRemittanceRecord();
            unclaimed.setPlatformCode(fdRemittanceRecord.getPlatformCode());
            unclaimed.setCustomerNo(fdRemittanceRecord.getCustomerNo());
            unclaimed.setCustomerFlag(fdRemittanceRecord.getCustomerFlag());
            unclaimed.setPlatformFlag(fdRemittanceRecord.getPlatformFlag());
            unclaimed.setRemittanceRecordCode(fdRemittanceRecord.getRemittanceRecordCode());
            unclaimed.setClaimStatus("0");
            unclaimed.setStatus("1");
            unclaimed.setDeleteFlag("N");
            List<FdRemittanceRecord> remittanceRecordList = fdRemittanceRecordMapper.selectFdRemittanceRecordList(unclaimed);
            BigDecimal unclaimedAmount;
            if (remittanceRecordList != null && !remittanceRecordList.isEmpty()) {
                unclaimedAmount = remittanceRecordList.stream().map(FdRemittanceRecord::getResidualAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            } else {
                unclaimedAmount = BigDecimal.valueOf(0);
            }
            fdRemittanceRecord.setResidualAmount(fdRemittanceRecord.getResidualAmount().add(unclaimedAmount));

        }
        return list;
    }


    @Override
    public List<FdRemittanceRecord> selectRemittanceRecordList(Map<String, Object> param) {
        FdRemittanceRecord fdRemittanceRecord = BeanUtil.mapToBean(param, FdRemittanceRecord.class, false);
        if (StrUtil.isEmpty(fdRemittanceRecord.getPlatformCode())) {
            fdRemittanceRecord.setPlatformCode(permissionUtil.getPcPermissonCustomer(fdRemittanceRecord.getPlatformCode(), fdRemittanceRecord.getPlatformFlag()));
        }
        return fdRemittanceRecordMapper.selectRemittanceRecordList(fdRemittanceRecord);
    }

    @Override
    public List<FdRemittanceRecord> selectRemittanceRecordListSheng(Map<String, Object> param) {
        param.remove("addTime");
        FdRemittanceRecord fdRemittanceRecord = BeanUtil.mapToBean(param, FdRemittanceRecord.class, false);
        return fdRemittanceRecordMapper.selectRemittanceRecordListSheng(fdRemittanceRecord);
    }

    /**
     * 新增汇款记录表
     *
     * @param fdRemittanceRecord 汇款记录表信息
     * @return 结果
     */
    @Override
    public int insertFdRemittanceRecord(FdRemittanceRecord fdRemittanceRecord) {
        return fdRemittanceRecordMapper.insertFdRemittanceRecord(fdRemittanceRecord);
    }

    @Override
    public int convertToAdvance(FdRemittanceRecord fdRemittanceRecord) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
//        //添加流水明细--扣除回款余额
//        insertFdTradingDetails(fdRemittanceRecord, "2", fdRemittanceRecord.getResidualAmount().negate());
//        //添加流水明细--添加预收款
//        insertFdTradingDetails(fdRemittanceRecord, "3", fdRemittanceRecord.getResidualAmount());

        //添加预收款余额信息
        FdBalanceDetail fdBalanceDetail = new FdBalanceDetail();
        fdBalanceDetail.setPaymentType("3");
        fdBalanceDetail.setPlatformCode(fdRemittanceRecord.getPlatformCode());
        fdBalanceDetail.setPlatformLevel(fdRemittanceRecord.getPlatformFlag());
        fdBalanceDetail.setCustomerCode(fdRemittanceRecord.getCustomerNo());
        fdBalanceDetail.setCustomerName(fdRemittanceRecord.getCustomerName());
        fdBalanceDetail.setTotalAmount(fdRemittanceRecord.getResidualAmount());
        fdBalanceDetail.setRemainingAmount(fdRemittanceRecord.getResidualAmount());
        fdBalanceDetail.setRemarks(fdRemittanceRecord.getRemittanceRecordCode() + "转为预收款");
        fdBalanceDetail.setAddWho(userInfo.getUserName());
        fdBalanceDetail.setAddWhoName(userInfo.getRealName());
        fdBalanceDetail.setAddTime(LocalDateTime.now());
        fdBalanceDetailMapper.insertFdBalanceDetail(fdBalanceDetail);

        //修改汇款数据的金额为0
        FdRemittanceRecord upd = new FdRemittanceRecord();
        upd.setId(fdRemittanceRecord.getId());
        upd.setResidualAmount(BigDecimal.valueOf(0));
        return fdRemittanceRecordMapper.updateFdRemittanceRecord(upd);
    }

    public void insertFdTradingDetails(FdRemittanceRecord fdRemittanceRecord, String paymentType, BillBalanceMainCity billBalanceMainCity) {
        FdTradingDetails fdTradingDetails = new FdTradingDetails();
        String tsIn = sysNoConfigService.genNo("TS");
        fdTradingDetails.setUuid(UUID.randomUUID().toString());
        fdTradingDetails.setTradeSerialNumber(tsIn);
        fdTradingDetails.setPlatformCode(fdRemittanceRecord.getPlatformCode());
        fdTradingDetails.setPlatformName(fdRemittanceRecord.getPlatformName());
        fdTradingDetails.setCustomerName(fdRemittanceRecord.getCustomerName());
        fdTradingDetails.setCustomerCode(fdRemittanceRecord.getCustomerNo());
        fdTradingDetails.setTradingHours(LocalDateTime.now());
        fdTradingDetails.setPaymentType(paymentType);
        fdTradingDetails.setRemittanceRecordCode(fdRemittanceRecord.getRemittanceRecordCode());
        fdTradingDetails.setTransactionAmount(billBalanceMainCity.getActualAmout());
        fdTradingDetails.setDeductionBillCode(billBalanceMainCity.getBalanceBillNo());
        fdTradingDetails.setTradingStatus("1");
        if ("1".equals(fdRemittanceRecord.getPlatformFlag())) {
            fdTradingDetails.setPlatformLevel("0");
        } else if ("2".equals(fdRemittanceRecord.getPlatformFlag()) && "1".equals(fdRemittanceRecord.getCustomerFlag())) {
            fdTradingDetails.setPlatformLevel("1");
        } else if ("2".equals(fdRemittanceRecord.getPlatformFlag()) && "2".equals(fdRemittanceRecord.getCustomerFlag())) {
            fdTradingDetails.setPlatformLevel("2");
        }
        fdTradingDetailsMapper.insertFdTradingDetails(fdTradingDetails);
    }

    @Override
    public int insertFdRemittanceRecordList(List<FdRemittanceRecord> list) {
        return fdRemittanceRecordMapper.insertFdRemittanceRecordList(list);
    }

    /**
     * 修改汇款记录表
     *
     * @param fdRemittanceRecord 汇款记录表信息
     * @return 结果
     */
    @Override
    public int updateFdRemittanceRecord(FdRemittanceRecord fdRemittanceRecord) {
        return fdRemittanceRecordMapper.updateFdRemittanceRecord(fdRemittanceRecord);
    }

    /**
     * 根据单号更改汇款记录
     *
     * @param fdRemittanceRecord
     * @return
     */
    @Override
    public int updateByRecordCode(FdRemittanceRecord fdRemittanceRecord) {
        return fdRemittanceRecordMapper.updateByRecordCode(fdRemittanceRecord);
    }


    /**
     * 删除汇款记录表
     *
     * @param id 汇款记录表ID
     * @return 结果
     */
    @Override
    public int deleteFdRemittanceRecordById(Long id) {
        return fdRemittanceRecordMapper.deleteFdRemittanceRecordById(id);
    }


    /**
     * 批量删除汇款记录表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFdRemittanceRecordByIds(Integer[] ids) {
        return fdRemittanceRecordMapper.deleteFdRemittanceRecordByIds(ids);
    }

    @Override
    public List<FdRemittanceRecord> selectDetailsList(FdRemittanceRecord fdRemittanceRecord) {
        List<FdRemittanceRecord> list = fdRemittanceRecordMapper.selectDetailsList(fdRemittanceRecord);
        return setRemittanceAmount(list);
    }

    @Override
    public Page selectDetails(Query query) {
        FdRemittanceRecord fdRemittanceRecordVo = BeanUtil.mapToBean(query.getCondition(), FdRemittanceRecord.class, false);
        query.setRecords(fdRemittanceRecordMapper.selectDetailsList(query, fdRemittanceRecordVo));
        setRemittanceAmount(query.getRecords());
        return query;
    }

    /**
     * 省平台手动核销
     */
    public R claimRemittanceTwo(FdRemittanceRecord fdRemittanceRecord) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        //判断收款记录是否已经被认领
        FdRemittanceRecord fdRemittanceRecordBase = fdRemittanceRecordMapper.selectFdRemittanceRecordById(fdRemittanceRecord.getId());
        if (fdRemittanceRecordBase == null || "1".equals(fdRemittanceRecordBase.getClaimStatus())) {
            return R.error("此收款记录不存在或已经被认领！");
        }
        //校验结算单的状态和数量
        List<BillBalanceMainCityListVO> billBalanceMainCities = new ArrayList<>();
        if (StrUtil.isNotBlank(fdRemittanceRecord.getBalanceBillNos())) {
            BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
            billBalanceMainCity.setBalanceBillNos(fdRemittanceRecord.getBalanceBillNos());
            billBalanceMainCity.setCustomerCode(fdRemittanceRecord.getCustomerNo());
            billBalanceMainCities = billBalanceMainCityMapper.pageByProvinceTwo(billBalanceMainCity);
            //判断结算表是否存在
            String[] remittanceIds = fdRemittanceRecord.getBalanceBillNos().split(",");
            if (billBalanceMainCities.isEmpty() || remittanceIds.length != billBalanceMainCities.size()) {
                return R.error("选择的结算数据不能被结算！");
            }
        }
        //合计结算表应付金额
        BigDecimal actualAmountSum = billBalanceMainCities.stream().map(BillBalanceMainCityListVO::getActualAmout).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        //判断结算表金额是否可以被关联
        if (fdRemittanceRecord.getRemittanceAmount().compareTo(actualAmountSum) < 0) {
            return R.error("选择的结算数据金额不能大于收款金额！");
        }
        //添加认领结算单的流水
        for (BillBalanceMainCityListVO billBalanceMainCity : billBalanceMainCities) {
            BillBalanceMainCity city = new BillBalanceMainCity();
            BeanUtil.copyProperties(billBalanceMainCity, city);
            insertFdTradingDetails(fdRemittanceRecord, "2", city);
        }

        //更新结算表状态
        for (BillBalanceMainCityListVO billBalanceMainCity : billBalanceMainCities) {
            BillBalanceMainCity updObj =  new BillBalanceMainCity();
            BillBalanceMainCityDetailVO billBalanceMainCityDetailVO = billBalanceMainCityMapper.selectBillBalanceMainCityById(billBalanceMainCity.getId());
            BeanUtil.copyProperties(billBalanceMainCityDetailVO, updObj);
            //修改子表为已核销
            billBalanceBindingCityMapper.updateBillBalanceBindingByBillCode(billBalanceMainCity.getBalanceBillNo(), fdRemittanceRecord.getCustomerNo(), "1");
            //如果子表全部结算则状态改为已核销，未全部结算则为部分核销
            BillBalanceBindingCity sel = new BillBalanceBindingCity();
            sel.setBillBalanceCode(billBalanceMainCity.getBalanceBillNo());
            sel.setBalanceStatus("0");
            sel.setDeleteFlag("N");
            List<BillBalanceBindingCity> billBalanceBindingCities = billBalanceBindingCityMapper.selectBillBalanceBindingCityList(sel);
            if (CollUtil.isNotEmpty(billBalanceBindingCities)) {
                updObj.setBalanceStatus(BalanceStatusEnum.PART.getKey());
            } else {
                updObj.setBalanceStatus(BalanceStatusEnum.VERIFIED.getKey());
            }
            billBalanceMainCityMapper.updateBillBalanceMainCity(updObj);
            //修改省应付账单状态
            billBalanceMainCityService.updateBillStatusByCode(billBalanceMainCity.getBalanceBillNo(), BalanceStatusEnum.VERIFIED.getKey(), fdRemittanceRecord.getCustomerNo());
//            billBalanceMainCityMapper.updateProvinceSubBillStatusByJsCodeTwo(BalanceStatusEnum.VERIFIED.getKey(), billBalanceMainCity.getBalanceBillNo(), fdRemittanceRecord.getCustomerNo());

            FdTradingDetails fdTradingDetails = new FdTradingDetails();
            fdTradingDetails.setDeductionBillCode(billBalanceMainCity.getBalanceBillNo());
            fdTradingDetails.setCustomerCode(fdRemittanceRecord.getCustomerNo());
            fdTradingDetails.setTradingStatus("2");
            fdTradingDetails.setDelFlag("N");
            //查询结算单余额绑定的流水
            List<FdTradingDetails> fdTradingDetailsList = fdTradingDetailsMapper.selectFdTradingDetailsListTwo(fdTradingDetails);

            for (FdTradingDetails fdTrading : fdTradingDetailsList) {
                fdTrading.setTradingStatus("1");
                //把流水的结算状态改成结算
                fdTradingDetailsMapper.updateFdTradingDetailsTradingStatus(fdTrading);
                FdBalanceDetail fdBalanceDetail = fdBalanceDetailMapper.selectFdBalanceDetailListById(fdTrading.getBalanceId());
                //根据流水更新汇款余额和量价捆绑余额里面的锁定金额和剩余金额
                if (!"0".equals(fdBalanceDetail.getPaymentType())) {
                    fdBalanceDetail.setLockingAmount(fdBalanceDetail.getLockingAmount().subtract(fdTrading.getTransactionAmount()));
                    fdBalanceDetail.setRemainingAmount(fdBalanceDetail.getRemainingAmount().subtract(fdTrading.getTransactionAmount()));
                } else {
                    fdBalanceDetail.setLockingAmount(BigDecimal.ZERO);
                    fdBalanceDetail.setRemainingAmount(BigDecimal.ZERO);
                    fdBalanceDetail.setAvailableAmount(BigDecimal.ZERO);
                }
                fdBalanceDetailMapper.updateFdBalanceDetailById(fdBalanceDetail);
            }

        }
        if (fdRemittanceRecord.getRemittanceAmount().compareTo(actualAmountSum) > 0) {
            //剩余余额存入付款余额
            FdBalanceDetail fdBalanceDetail = new FdBalanceDetail();
            fdBalanceDetail.setPlatformCode(fdRemittanceRecord.getPlatformCode());
            fdBalanceDetail.setCustomerCode(fdRemittanceRecord.getCustomerNo());
            fdBalanceDetail.setCustomerName(fdRemittanceRecord.getCustomerName());
            fdBalanceDetail.setPaymentType("3");
            fdBalanceDetail.setTotalAmount(fdRemittanceRecord.getRemittanceAmount().subtract(actualAmountSum));
            fdBalanceDetail.setRemainingAmount(fdRemittanceRecord.getRemittanceAmount().subtract(actualAmountSum));
            fdBalanceDetail.setAvailableAmount(fdRemittanceRecord.getRemittanceAmount().subtract(actualAmountSum));
            fdBalanceDetail.setPlatformLevel(fdRemittanceRecord.getCustomerFlag());
            fdBalanceDetail.setAddWho(userInfo.getUserName());
            fdBalanceDetail.setAddWhoName(userInfo.getRealName());
            fdBalanceDetail.setBillCode(fdRemittanceRecord.getRemittanceRecordCode());
            fdBalanceDetail.setRemarks("收款结算余额");
            fdBalanceDetailService.insertFdBalanceDetail(fdBalanceDetail);
        }


        //更新汇款单
        fdRemittanceRecord.setClaimStatus("1");
        fdRemittanceRecord.setClaimant(userInfo.getRealName());
        fdRemittanceRecord.setClaimDate(LocalDateTime.now());
        fdRemittanceRecordMapper.updateByRecordCode(fdRemittanceRecord);
        return R.success();
    }

    /**
     * 收款记录认领
     *
     * @Param: fdRemittanceRecord
     * @Return:
     * @Author: Howe
     * @Date: 2024/6/17 下午5:46
     **/
    @Override
    @Transactional
    public R claimRemittance(FdRemittanceRecord fdRemittanceRecord) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        //判断收款记录是否已经被认领
        FdRemittanceRecord fdRemittanceRecordBase = fdRemittanceRecordMapper.selectFdRemittanceRecordById(fdRemittanceRecord.getId());
        if (fdRemittanceRecordBase == null || "1".equals(fdRemittanceRecordBase.getClaimStatus())) {
            return R.error("此收款记录不存在或已经被认领！");
        }
        //校验结算单的状态和数量
        List<BillBalanceMainCity> billBalanceMainCities = new ArrayList<>();
        if (StrUtil.isNotBlank(fdRemittanceRecord.getBalanceBillNos())) {
            BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
            billBalanceMainCity.setBalanceBillNos(fdRemittanceRecord.getBalanceBillNos());
            if (fdRemittanceRecord.getCustomerFlag() != null && "0".equals(fdRemittanceRecord.getCustomerFlag())) {
                billBalanceMainCity.setBalanceStatus(BalanceStatusEnum.WAITPAY.getKey());
            } else if (fdRemittanceRecord.getCustomerFlag() != null && "2".equals(fdRemittanceRecord.getCustomerFlag())) {
                billBalanceMainCity.setBalanceStatus(BalanceStatusEnum.WAITPAY.getKey());
            } else {
                billBalanceMainCity.setBalanceStatus(BalanceStatusEnum.VERIFICATION.getKey());
            }
            billBalanceMainCity.setDeleteFlag("N");
            billBalanceMainCities = billBalanceMainCityService.selectBillBalanceMainCityList(billBalanceMainCity);
            //判断结算表是否存在
            String[] remittanceIds = fdRemittanceRecord.getBalanceBillNos().split(",");
            if (billBalanceMainCities.isEmpty() || remittanceIds.length != billBalanceMainCities.size()) {
                return R.error("选择的结算数据不能被结算！");
            }
        }
        //合计结算表应付金额
        BigDecimal actualAmountSum = billBalanceMainCities.stream().map(BillBalanceMainCity::getActualAmout).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        //判断结算表金额是否可以被关联
        if (fdRemittanceRecord.getRemittanceAmount().compareTo(actualAmountSum) < 0) {
            return R.error("选择的结算数据金额不能大于收款金额！");
        }
        //添加认领结算单的流水
        for (BillBalanceMainCity billBalanceMainCity : billBalanceMainCities) {
            insertFdTradingDetails(fdRemittanceRecord, "2", billBalanceMainCity);
        }

        //更新结算表状态
        for (BillBalanceMainCity billBalanceMainCity : billBalanceMainCities) {
            billBalanceMainCity.setBalanceStatus(BalanceStatusEnum.VERIFIED.getKey());
            billBalanceMainCityMapper.updateBillBalanceMainCity(billBalanceMainCity);
            billBalanceMainCityService.updateBillStatusByCode(billBalanceMainCity.getBalanceBillNo(), billBalanceMainCity.getBalanceStatus(), null);

            FdTradingDetails fdTradingDetails = new FdTradingDetails();
            fdTradingDetails.setDeductionBillCode(billBalanceMainCity.getBalanceBillNo());
            fdTradingDetails.setTradingStatus("2");
            fdTradingDetails.setDelFlag("N");
            //查询结算单余额绑定的流水
            List<FdTradingDetails> fdTradingDetailsList = fdTradingDetailsMapper.selectFdTradingDetailsList(fdTradingDetails);

            for (FdTradingDetails fdTrading : fdTradingDetailsList) {
                fdTrading.setTradingStatus("1");
                //把流水的结算状态改成结算
                fdTradingDetailsMapper.updateFdTradingDetailsTradingStatus(fdTrading);
                FdBalanceDetail fdBalanceDetail = fdBalanceDetailMapper.selectFdBalanceDetailListById(fdTrading.getBalanceId());
                //根据流水更新汇款余额和量价捆绑余额里面的锁定金额和剩余金额
                if (!"0".equals(fdBalanceDetail.getPaymentType())) {
                    fdBalanceDetail.setLockingAmount(fdBalanceDetail.getLockingAmount().subtract(fdTrading.getTransactionAmount()));
                    fdBalanceDetail.setRemainingAmount(fdBalanceDetail.getRemainingAmount().subtract(fdTrading.getTransactionAmount()));
                } else {
                    fdBalanceDetail.setLockingAmount(BigDecimal.ZERO);
                    fdBalanceDetail.setRemainingAmount(BigDecimal.ZERO);
                    fdBalanceDetail.setAvailableAmount(BigDecimal.ZERO);
                }
                fdBalanceDetailMapper.updateFdBalanceDetailById(fdBalanceDetail);
            }

        }
        if (fdRemittanceRecord.getRemittanceAmount().compareTo(actualAmountSum) > 0) {
            //剩余余额存入付款余额
            FdBalanceDetail fdBalanceDetail = new FdBalanceDetail();
            fdBalanceDetail.setPlatformCode(fdRemittanceRecord.getPlatformCode());
            fdBalanceDetail.setCustomerCode(fdRemittanceRecord.getCustomerNo());
            fdBalanceDetail.setCustomerName(fdRemittanceRecord.getCustomerName());
            fdBalanceDetail.setPaymentType("3");
            fdBalanceDetail.setTotalAmount(fdRemittanceRecord.getRemittanceAmount().subtract(actualAmountSum));
            fdBalanceDetail.setRemainingAmount(fdRemittanceRecord.getRemittanceAmount().subtract(actualAmountSum));
            fdBalanceDetail.setAvailableAmount(fdRemittanceRecord.getRemittanceAmount().subtract(actualAmountSum));
            fdBalanceDetail.setPlatformLevel(fdRemittanceRecord.getCustomerFlag());
            fdBalanceDetail.setAddWho(userInfo.getUserName());
            fdBalanceDetail.setAddWhoName(userInfo.getRealName());
            fdBalanceDetail.setBillCode(fdRemittanceRecord.getRemittanceRecordCode());
            fdBalanceDetail.setRemarks("收款结算余额");
            fdBalanceDetailService.insertFdBalanceDetail(fdBalanceDetail);
        }


        //更新汇款单
        fdRemittanceRecord.setClaimStatus("1");
        fdRemittanceRecord.setClaimant(userInfo.getRealName());
        fdRemittanceRecord.setClaimDate(LocalDateTime.now());
        fdRemittanceRecordMapper.updateByRecordCode(fdRemittanceRecord);
        return R.success();
    }

    /**
     * 认领作废
     *
     * @Param: null
     * @Return:
     * @Author: Howe
     * @Date: 2024/6/22 下午2:05
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R claimInvalid(FdRemittanceRecord fdRemittanceRecord) {
        fdRemittanceRecord = fdRemittanceRecordMapper.selectFdRemittanceRecordById(fdRemittanceRecord.getId());
        if (fdRemittanceRecord == null) {
            return R.error("查不到此收款记录！");
        }
        if (!"1".equals(fdRemittanceRecord.getClaimStatus())) {
            return R.error("此收款记录未被认领！");
        }
        //查询认领后的余额是否被使用
        FdTradingDetails fdTradingDetails = new FdTradingDetails();
        fdTradingDetails.setRemittanceRecordCode(fdRemittanceRecord.getRemittanceRecordCode());
        fdTradingDetails.setPaymentType("3");
        fdTradingDetails.setDelFlag("N");
        List<FdTradingDetails> fdTradingDetailsList = fdTradingDetailsMapper.selectFdTradingDetailsList(fdTradingDetails);
        if (!fdTradingDetailsList.isEmpty()) {
            return R.error("该收款余额已被使用，不能作废！");
        }
        //查询汇款单绑定的流水
        FdTradingDetails balance = new FdTradingDetails();
        balance.setRemittanceRecordCode(fdRemittanceRecord.getRemittanceRecordCode());
        balance.setTradingStatus("1");
        balance.setPaymentType("2");
        balance.setDelFlag("N");
        List<FdTradingDetails> balanceFdTradingDetailsList = fdTradingDetailsMapper.selectFdTradingDetailsList(balance);
        for (FdTradingDetails f : balanceFdTradingDetailsList) {
            //根据流水查询结算单
            BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
            billBalanceMainCity.setBalanceBillNos(f.getDeductionBillCode());
            billBalanceMainCity.setDeleteFlag("N");
            List<BillBalanceMainCity> billBalanceMainCities = billBalanceMainCityService.selectBillBalanceMainCityList(billBalanceMainCity);
            for (BillBalanceMainCity b : billBalanceMainCities) {
                if ("1".equals(fdRemittanceRecord.getCustomerFlag())) {
                    b.setBalanceStatus(BalanceStatusEnum.VERIFICATION.getKey());
                } else {
                    b.setBalanceStatus(BalanceStatusEnum.WAITPAY.getKey());
                }
                //回退结算单的状态
                billBalanceMainCityMapper.updateBillBalanceMainCity(b);
                fdTradingDetailsMapper.deleteFdTradingDetailsById(f.getId());
                //根据结算单查询绑定余额的流水
                FdTradingDetails backBalance = new FdTradingDetails();
                backBalance.setDeductionBillCode(billBalanceMainCity.getBalanceBillNos());
                backBalance.setTradingStatus("1");
                backBalance.setDelFlag("N");
                List<FdTradingDetails> backBalanceFdTradingDetails = fdTradingDetailsMapper.selectFdTradingDetailsList(backBalance);
                for (FdTradingDetails back : backBalanceFdTradingDetails) {
                    FdBalanceDetail backBalanceDetail = fdBalanceDetailMapper.selectFdBalanceDetailListById(back.getBalanceId());
                    //增加锁定金额
                    backBalanceDetail.setLockingAmount(backBalanceDetail.getLockingAmount().add(back.getTransactionAmount()));
                    //增加剩余金额
                    backBalanceDetail.setRemainingAmount(backBalanceDetail.getRemainingAmount().add(back.getTransactionAmount()));
                    //退回流水绑定的余额
                    fdBalanceDetailMapper.updateFdBalanceDetailById(backBalanceDetail);
                    //退回流水预结算状态
                    back.setTradingStatus("2");
                    fdTradingDetailsMapper.updateFdTradingDetailsTradingStatus(back);
                }
                // 修改同步子账单状态
                billBalanceMainCityService.updateBillStatusByCode(b.getBalanceBillNo(), b.getBalanceStatus(), null);
            }
        }

        //删除收款产生的余额
        FdBalanceDetail fdBalanceDetail = new FdBalanceDetail();
        fdBalanceDetail.setDeleteFlag("N");
        fdBalanceDetail.setBillCode(fdRemittanceRecord.getRemittanceRecordCode());
        fdBalanceDetail.setPaymentType("3");
        List<FdBalanceDetail> fdBalanceDetailList = fdBalanceDetailMapper.selectFdBalanceDetailList(fdBalanceDetail);
        if (!fdBalanceDetailList.isEmpty()) {
            //汇款只能产生一条余额,删除余额
            fdBalanceDetail = fdBalanceDetailList.get(0);
            fdBalanceDetail.setDeleteFlag("Y");
            fdBalanceDetailMapper.updateFdBalanceDetailById(fdBalanceDetail);
        }

        //更新收款记录
        if ("1".equals(fdRemittanceRecord.getCustomerFlag())) {
            fdRemittanceRecord.setClaimStatus("0");
            fdRemittanceRecord.setClaimDate(null);
            fdRemittanceRecord.setClaimant(null);
        } else {
            fdRemittanceRecord.setDeleteFlag("Y");
        }
        fdRemittanceRecord.setResidualAmount(fdRemittanceRecord.getRemittanceAmount());
        fdRemittanceRecordMapper.updateFdRemittanceRecordClaimant(fdRemittanceRecord);
        return R.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R claimInvalidProvince(FdRemittanceRecord fdRemittanceRecord) {
        fdRemittanceRecord = fdRemittanceRecordMapper.selectFdRemittanceRecordById(fdRemittanceRecord.getId());
        if (fdRemittanceRecord == null) {
            return R.error("查不到此收款记录！");
        }
        if (!"1".equals(fdRemittanceRecord.getClaimStatus())) {
            return R.error("此收款记录未被认领！");
        }
        //查询认领后的余额是否被使用
        FdTradingDetails fdTradingDetails = new FdTradingDetails();
        fdTradingDetails.setRemittanceRecordCode(fdRemittanceRecord.getRemittanceRecordCode());
        fdTradingDetails.setPaymentType("3");
        fdTradingDetails.setDelFlag("N");
        List<FdTradingDetails> fdTradingDetailsList = fdTradingDetailsMapper.selectFdTradingDetailsList(fdTradingDetails);
        if (CollUtil.isNotEmpty(fdTradingDetailsList)) {
            return R.error("该收款余额已被使用，不能作废！");
        }
        //查询汇款单绑定的流水
        FdTradingDetails balance = new FdTradingDetails();
        balance.setRemittanceRecordCode(fdRemittanceRecord.getRemittanceRecordCode());
        balance.setTradingStatus("1");
        balance.setPaymentType("2");
        balance.setDelFlag("N");
        List<FdTradingDetails> balanceFdTradingDetailsList = fdTradingDetailsMapper.selectFdTradingDetailsList(balance);
        for (FdTradingDetails f : balanceFdTradingDetailsList) {
            fdTradingDetailsMapper.deleteFdTradingDetailsById(f.getId());
            //根据流水查询结算单
            BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
            billBalanceMainCity.setBalanceBillNos(f.getDeductionBillCode());
            billBalanceMainCity.setDeleteFlag("N");
            List<BillBalanceMainCity> billBalanceMainCities = billBalanceMainCityService.selectBillBalanceMainCityList(billBalanceMainCity);
            for (BillBalanceMainCity b : billBalanceMainCities) {
                b.setBalanceStatus(BalanceStatusEnum.WAITPAY.getKey());
                List<BillBalanceBindingCity> list = billBalanceBindingCityMapper.selectListByBillBalanceCode(b.getBalanceBillNo());
                if (CollUtil.isNotEmpty(list)) {
                    for (BillBalanceBindingCity city : list) {
                        if (fdRemittanceRecord.getCustomerNo().equals(city.getPlatformCode())) {
                            city.setBalanceStatus("0");
                            billBalanceBindingCityMapper.updateBillBalanceBindingCity(city);
                        } else {
                            if ("1".equals(city.getBalanceStatus())) {
                                b.setBalanceStatus(BalanceStatusEnum.PART.getKey());
                            }
                        }
                    }
                }
                //回退结算单的状态
                billBalanceMainCityMapper.updateBillBalanceMainCity(b);
                //根据结算单查询绑定余额的流水
                FdTradingDetails backBalance = new FdTradingDetails();
                backBalance.setDeductionBillCode(billBalanceMainCity.getBalanceBillNos());
                backBalance.setTradingStatus("1");
                backBalance.setDelFlag("N");
                List<FdTradingDetails> backBalanceFdTradingDetails = fdTradingDetailsMapper.selectFdTradingDetailsList(backBalance);
                for (FdTradingDetails back : backBalanceFdTradingDetails) {
                    if (back.getBalanceId() != null) {
                        FdBalanceDetail backBalanceDetail = fdBalanceDetailMapper.selectFdBalanceDetailListById(back.getBalanceId());
                        //增加锁定金额
                        backBalanceDetail.setLockingAmount(backBalanceDetail.getLockingAmount().add(back.getTransactionAmount()));
                        //增加剩余金额
                        backBalanceDetail.setRemainingAmount(backBalanceDetail.getRemainingAmount().add(back.getTransactionAmount()));
                        //退回流水绑定的余额
                        fdBalanceDetailMapper.updateFdBalanceDetailById(backBalanceDetail);
                    }
                    //退回流水预结算状态
                    back.setTradingStatus("2");
                    fdTradingDetailsMapper.updateFdTradingDetailsTradingStatus(back);
                }
                // 修改同步子账单状态
                billBalanceMainCityService.updateBillStatusByCode(b.getBalanceBillNo(), BalanceStatusEnum.WAITCHECK.getKey(), fdRemittanceRecord.getCustomerNo());
//                billBalanceMainCityMapper.updateProvinceSubBillStatusByJsCodeTwo(BalanceStatusEnum.WAITCHECK.getKey(), b.getBalanceBillNo(), fdRemittanceRecord.getCustomerNo());
            }
        }

        //删除收款产生的余额
        FdBalanceDetail fdBalanceDetail = new FdBalanceDetail();
        fdBalanceDetail.setDeleteFlag("N");
        fdBalanceDetail.setBillCode(fdRemittanceRecord.getRemittanceRecordCode());
        fdBalanceDetail.setPaymentType("3");
        List<FdBalanceDetail> fdBalanceDetailList = fdBalanceDetailMapper.selectFdBalanceDetailList(fdBalanceDetail);
        if (!fdBalanceDetailList.isEmpty()) {
            //汇款只能产生一条余额,删除余额
            fdBalanceDetail = fdBalanceDetailList.get(0);
            fdBalanceDetail.setDeleteFlag("Y");
            fdBalanceDetailMapper.updateFdBalanceDetailById(fdBalanceDetail);
        }

        //更新收款记录
        if ("1".equals(fdRemittanceRecord.getCustomerFlag())) {
            fdRemittanceRecord.setClaimStatus("0");
            fdRemittanceRecord.setClaimDate(null);
            fdRemittanceRecord.setClaimant(null);
        } else {
            fdRemittanceRecord.setDeleteFlag("Y");
        }
        fdRemittanceRecord.setResidualAmount(fdRemittanceRecord.getRemittanceAmount());
        fdRemittanceRecordMapper.updateFdRemittanceRecordClaimant(fdRemittanceRecord);
        return R.success();
    }

    /**
     * 保存接口
     *
     * @Param: fdRemittanceRecord
     * @Return:
     * @Author: Howe
     * @Date: 2024/6/25 下午6:31
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R save(FdRemittanceRecord fdRemittanceRecord) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        fdRemittanceRecord.setAddWho(userInfo.getUserName());
        fdRemittanceRecord.setAddWhoName(userInfo.getRealName());
        fdRemittanceRecord.setAddTime(LocalDateTime.now());
        if (fdRemittanceRecord.getResidualAmount() == null) {
            fdRemittanceRecord.setResidualAmount(fdRemittanceRecord.getRemittanceAmount());
        }
        this.insertFdRemittanceRecord(fdRemittanceRecord);
        if (StrUtil.isNotBlank(fdRemittanceRecord.getBalanceBillNos()) || !"1".equals(fdRemittanceRecord.getCustomerFlag())) {
            return claimRemittance(fdRemittanceRecord);
        } else {
            return R.success();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveTwo(FdRemittanceRecord fdRemittanceRecord) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        fdRemittanceRecord.setAddWho(userInfo.getUserName());
        fdRemittanceRecord.setAddWhoName(userInfo.getRealName());
        fdRemittanceRecord.setAddTime(LocalDateTime.now());
        if (fdRemittanceRecord.getResidualAmount() == null) {
            fdRemittanceRecord.setResidualAmount(fdRemittanceRecord.getRemittanceAmount());
        }
        this.insertFdRemittanceRecord(fdRemittanceRecord);
        if (StrUtil.isNotBlank(fdRemittanceRecord.getBalanceBillNos()) || "2".equals(fdRemittanceRecord.getCustomerFlag())) {
            return claimRemittanceTwo(fdRemittanceRecord);
        } else {
            return R.success();
        }
    }
}
