package com.huazheng.tunny.ocean.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.vo.FdBillVO;
import com.huazheng.tunny.ocean.mapper.*;
//import com.huazheng.tunny.ocean.mapper.WaybillHeaderMapper;
import com.huazheng.tunny.ocean.service.*;
//import com.sun.xml.internal.bind.v2.TODO;
import com.huazheng.tunny.ocean.util.spiltlistutil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

import java.util.UUID;

@Service("fdCostService")
@Transactional(rollbackFor = Exception.class)

public class FdCostServiceImpl extends ServiceImpl<FdCostMapper, FdCost> implements FdCostService {

    @Autowired
    private FdCostMapper fdCostMapper;
    @Autowired
    private FdCostService fdCostService;
    @Autowired
    private FdCosdetailService fdCosdetailService;
    @Autowired
    private WaybillHeaderService waybillHeaderService;
    @Autowired
    private SysNoConfigService sysNoConfigService;
    @Autowired
    private WaybillContainerInfoService waybillContainerInfoService;
    @Autowired
    private CustomerPlatformInfoMapper customerPlatformInfoMapper;
    @Autowired
    private FdShippingAccountService fdShippingAccountService;
    @Autowired
    private FdShippingAccoundetailService fdShippingAccoundetailService;
    @Autowired
    private BasChangeboxRetreatService basChangeboxRetreatService;
    @Autowired
    private BasChangeboxDetailService basChangeboxDetailService;
    @Autowired
    private WaybillContainerInfoMapper waybillContainerInfoMapper;
    @Autowired
    private FdBillMapper fdBillMapper;
    @Autowired
    private FdCosdetailMapper fdCosdetailMapper;
    @Autowired
    private FdShippingAccountMapper fdShippingAccountMapper;
    @Autowired
    private FdShippingAccoundetailMapper fdShippingAccoundetailMapper;
    /**
     * 查询运单费用汇总表信息
     *
     * @param id 运单费用汇总表ID
     * @return 运单费用汇总表信息
     */
    @Override
    public FdCost selectFdCostById(Integer id) {
        return fdCostMapper.selectFdCostById(id);
    }

    /**
     * 查询运单费用汇总表列表
     *
     * @param fdCost 运单费用汇总表信息
     * @return 运单费用汇总表集合
     */
    @Override
    public List<FdCost> selectFdCostList(FdCost fdCost) {
        return fdCostMapper.selectFdCostList(fdCost);
    }

    @Override
    public List<FdCost> selectYsFdCost(FdCost fdCost) {
        return fdCostMapper.selectYsFdCost(fdCost);
    }

    @Override
    public List<FdCost> selectFdCostListAndAreaCode(FdCost fdCost) {
        return fdCostMapper.selectFdCostListAndAreaCode(fdCost);
    }

    @Override
    public List<FdCost> selectLiangJiaKunBang(FdCost fdCost) {
        return fdCostMapper.selectLiangJiaKunBang(fdCost);
    }


    /**
     * 分页模糊查询运单费用汇总表列表
     *
     * @return 运单费用汇总表集合
     */
    @Override
    public Page selectFdCostListByLike(Query query) {
        FdCost fdCost = BeanUtil.mapToBean(query.getCondition(), FdCost.class, false);
        fdCost.setDelFlag("N");
        int i = fdCostMapper.selectFdCostListByLiketotal(fdCost);
        List<FdCost> fdCosts = fdCostMapper.selectFdCostListByLike(query, fdCost);
        for (FdCost fd:fdCosts) {
            FdCosdetail fdCosdetail=new FdCosdetail();
            fdCosdetail.setCostCode(fd.getCostCode());
            fdCosdetail.setDelFlag("N");
            fd.setIsUp("0");
            List<FdCosdetail> fdCosdetails = fdCosdetailService.selectFdCosdetailList(fdCosdetail);
            fdCosdetails.stream().forEach(deatils->{
                if(!"".equals(deatils.getBillCode()) && deatils.getBillCode()!=null){
                    fd.setIsUp("1");
                }
            });
        }
        query.setTotal(i);
        query.setRecords(fdCosts);
        return query;
    }

    /**
     * 新增运单费用汇总表
     *
     * @param fdCost 运单费用汇总表信息
     * @return 结果
     */
    @Override
    public int insertFdCost(FdCost fdCost) {
        return fdCostMapper.insertFdCost(fdCost);
    }

    /**
     * 修改运单费用汇总表
     *
     * @param fdCost 运单费用汇总表信息
     * @return 结果
     */
    @Override
    public int updateFdCost(FdCost fdCost) {
        return fdCostMapper.updateFdCost(fdCost);
    }


    /**
     * 删除运单费用汇总表
     *
     * @param id 运单费用汇总表ID
     * @return 结果
     */
    @Override
    public int deleteFdCostById(Integer id) {
        return fdCostMapper.deleteFdCostById(id);
    }


    /**
     * 批量删除运单费用汇总表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFdCostByIds(Integer[] ids) {
        return fdCostMapper.deleteFdCostByIds(ids);
    }

    /**
     * 调用运单等信息新增 费用主表和费用详情表
     *
     * @param
     * @return
     */
    @Override
    public Boolean savecostandcodstdetails(String rowid) {

        HashMap<String, Object> hashMap = new HashMap<>();

        FdCost fdCost = getfdcostbyWaybillHeader(rowid);
        if (fdCost == null) {
            return false;
        }
        String Invoice = "应收";
        hashMap.put("Invoice", Invoice);

        hashMap.put("codeBbCategoriesName", "发运运费");
        hashMap.put("codeBbCategoriesCode", "f_fee_type");
        hashMap.put("codeSsCategoriesName", "境内运费");
        hashMap.put("codeSsCategoriesCode", "f_domestic_fee");
        //这里是获取境内运费用的
        ArrayList<FdCosdetail> FdCosdetaillist = getcostdetailsbyfdcost(fdCost, hashMap);


        hashMap.put("codeBbCategoriesName", "发运运费");
        hashMap.put("codeBbCategoriesCode", "f_fee_type");
        hashMap.put("codeSsCategoriesName", "境外费用");
        hashMap.put("codeSsCategoriesCode", "f_overseas_fee");
        //这里是获取境外运费
        ArrayList<FdCosdetail> FdCosdetaillist1 = newgetcostdetailsbyfdcost(fdCost, hashMap);
        FdCosdetaillist.addAll(FdCosdetaillist1);
        fdCostService.insertFdCost(fdCost);
        for (FdCosdetail fdCosdetail : FdCosdetaillist) {
            fdCosdetail.setStandbyC(fdCosdetail.getCustomerName());
            fdCosdetailService.insertFdCosdetail(fdCosdetail);
        }

        return true;
    }

    @Override
    public Boolean savecostandcodstdetailsbyaccount(String rowid) {

        HashMap<String, Object> hashMap = new HashMap<>();
        FdShippingAccount fdShippingAccount = fdShippingAccountService.selectFdShippingAccountById(Long.parseLong(rowid));
        String provinceShiftNo = fdShippingAccount.getProvinceShiftNo();
        FdCost fdCost1 = new FdCost();
        fdCost1.setProvinceTrainsNumber(provinceShiftNo);
        fdCost1.setPlatformLevel("0");
        List<FdCost> fdCosts = fdCostMapper.selectFdCostList(fdCost1);
        FdCost fdCost;
        if (fdCosts.size() == 0) {
            //通过台账信息生成费用信息
            fdCost = getcostbyaccount(rowid);
        } else {

            fdCost = fdCosts.get(0);
            fdCost.setOverseasFreightOverseasFreight(fdShippingAccount.getOverseasFreightCny());
            fdCost.setOverseasFreightCny(fdShippingAccount.getOverseasFreightCny());
            //境内运费
            fdCost.setDomesticFreight(fdShippingAccount.getDomesticFreight());

        }


//撤箱
        boolean b = Unboxing(rowid);


        String Invoice = "应付";
        hashMap.put("Invoice", Invoice);

        hashMap.put("codeBbCategoriesName", "发运运费");
        hashMap.put("codeBbCategoriesCode", "f_fee_type");
        hashMap.put("codeSsCategoriesName", "境内运费");
        hashMap.put("codeSsCategoriesCode", "f_domestic_fee");
        //这个是境内费用的费用明细
        List<FdCosdetail> list = getcostdetailbyaccount(rowid, hashMap, fdCost);
        //这个是境外费用的费用明细
        hashMap.put("codeBbCategoriesName", "发运运费");
        hashMap.put("codeBbCategoriesCode", "f_fee_type");
        hashMap.put("codeSsCategoriesName", "境外费用");
        hashMap.put("codeSsCategoriesCode", "f_overseas_fee");
        List<FdCosdetail> list1 = newgetcostdetailbyaccount(rowid, hashMap, fdCost);
        list.addAll(list1);
        //计算境外费用
        BigDecimal bigDecimal=BigDecimal.valueOf(0);
        BigDecimal bigDecimalCny=BigDecimal.valueOf(0);
        for(FdCosdetail fdCosdetail:list){
            if("f_domestic_fee".equals(fdCosdetail.getCodeSsCategoriesCode())){
                fdCosdetail.setExchangeRate(BigDecimal.valueOf(1));
            }
            if("f_overseas_fee".equals(fdCosdetail.getCodeSsCategoriesCode())){
                bigDecimal=bigDecimal.add(fdCosdetail.getOriginalCurrencyAmount());
            }
            if("f_overseas_fee".equals(fdCosdetail.getCodeSsCategoriesCode())){
                bigDecimalCny=bigDecimalCny.add(fdCosdetail.getLocalCurrencyAmount());
            }
        }
        fdCost.setOverseasFreightOverseasFreight(bigDecimal);
        fdCost.setOverseasFreightCny(bigDecimalCny);
        fdCost.setStandbyB("1");//判断是否是应付账单
//        if (fdCosts.size() == 0) {
//            fdCostMapper.insertFdCost(fdCost);
//        } else {
//            fdCostMapper.updateFdCost(fdCost);
//        }
//        List<List<FdCosdetail>> splitList = spiltlistutil.getSplitList(800, list);
//        for (List<FdCosdetail> fdCosdetails : splitList) {
//            fdCosdetailService.insertBatch(fdCosdetails);
//        }

        //下面生成省平台对市平台应收
//下面的值应该是省平台
        String paymentCustomerName = fdCost.getPaymentCustomerName();
        String paymentCustomerCode = fdCost.getPaymentCustomerCode();
//下面是市平台的数据
        String platformCode = fdCost.getPlatformCode();
        String platformName = fdCost.getPlatformName();
//下面赋值
        fdCost.setPlatformName(paymentCustomerName);
        fdCost.setPlatformCode(paymentCustomerCode);
        fdCost.setPlatformLevel("1");
        fdCost.setGatheringuserName(paymentCustomerName);
        fdCost.setGatheringuserCode(paymentCustomerCode);
        fdCost.setPaymentCustomerName(platformName);
        fdCost.setPaymentCustomerCode(platformCode);
        fdCost.setCostCode(sysNoConfigService.genNo("FDC"));

        for (FdCosdetail fdCosdetail : list) {
            fdCosdetail.setCostCode(fdCost.getCostCode());
            fdCosdetail.setPlatformName(paymentCustomerName);
            fdCosdetail.setPlatformCode(paymentCustomerCode);
            fdCosdetail.setPlatformLevel("1");
            fdCosdetail.setCustomerName(platformName);
            fdCosdetail.setCustomerCode(platformCode);
            fdCosdetail.setIncomeFlag("应收");
//            fdCosdetail.setUuid(UUID.randomUUID().toString());
        }
        //下面新增
        fdCost.setStandbyB("0");
        if (fdCosts.size() == 0) {
            fdCost.setUuid(UUID.randomUUID().toString());
            fdCostMapper.insertFdCost(fdCost);
        } else {
            fdCostMapper.updateFdCost(fdCost);
        }
        List<List<FdCosdetail>> splitList1 = spiltlistutil.getSplitList(800, list);
        for (List<FdCosdetail> fdCosdetails : splitList1) {
            fdCosdetailService.insertFdCosdetailList(fdCosdetails);
        }
        return true;
    }

    /*
    *
    * 审核台账直接生成应收费用、应收账单
    *
    * 审核台账直接生成应付费用、应收账单
    *
    * */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean savecostandcodstdetailsbyaccount2(String rowid) {

        FdShippingAccount fdShippingAccount = fdShippingAccountService.selectFdShippingAccountById(Long.parseLong(rowid));
        FdShippingAccoundetail sel2 = new FdShippingAccoundetail();
        sel2.setAccountCode(fdShippingAccount.getAccountCode());
        sel2.setDeleteFlag("N");
        final List<FdShippingAccoundetail> fdShippingAccoundetails = fdShippingAccoundetailMapper.selectFdShippingAccoundetailList(sel2);


        //下面生成省平台对市平台应收----------------------------------------------------------------------------------------------------
//下面的值应该是省平台
        String paymentCustomerCode = "MP210800001";
        String paymentCustomerName = "山东高速齐鲁号欧亚班列运营有限公司";

        CustomerPlatformInfo sel = new CustomerPlatformInfo();
        sel.setCustomerCode(fdShippingAccount.getPlatformCode());
        sel.setDeleteFlag("N");
        final List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoMapper.selectCustomerPlatformInfoList(sel);
        if(CollUtil.isNotEmpty(customerPlatformInfos)){
            if(StrUtil.isNotEmpty(customerPlatformInfos.get(0).getPlatformCodePro())){
                paymentCustomerCode = customerPlatformInfos.get(0).getPlatformCodePro();
            }
        }
//下面是市平台的数据
        String platformCode = fdShippingAccount.getPlatformCode();
        String platformName = fdShippingAccount.getPlatformName();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = fdShippingAccount.getShippingTime().format(formatter);

        //生成应收费用、应收账单-------------------------------------------------------------------------------------------------------------
        //费用部分
        final String fdblYs = sysNoConfigService.genNo("FDBL");
        FdCost fdCostYs = new FdCost();
        fdCostYs.setUuid(UUID.randomUUID().toString());
        fdCostYs.setGatheringuserCode(paymentCustomerCode);
        fdCostYs.setGatheringuserName(paymentCustomerName);
        fdCostYs.setPlatformLevel("1");
        fdCostYs.setStandbyB("1");

        final String fdcYs = sysNoConfigService.genNo("FDC");
        fdCostYs.setCostCode(fdcYs);
        fdCostYs.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
        fdCostYs.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
        fdCostYs.setStandbyC(fdcYs);
        fdCostYs.setPlatformCode(paymentCustomerCode);
        fdCostYs.setPlatformName(paymentCustomerName);
        fdCostYs.setGatheringuserCode(paymentCustomerCode);
        fdCostYs.setGatheringuserName(paymentCustomerName);
        fdCostYs.setPaymentCustomerCode(platformCode);
        fdCostYs.setPaymentCustomerName(platformName);
        fdCostYs.setProvinceTrainsNumber(fdShippingAccount.getProvinceShiftNo());
        fdCostYs.setTrainNumber(fdShippingAccount.getShiftNo());
        fdCostYs.setTrainsName(fdShippingAccount.getShiftName());
        fdCostYs.setShipmentTime(formattedDate);
        fdCostYs.setShippingLines(fdShippingAccount.getShippingLine());
        fdCostYs.setDirection(fdShippingAccount.getTrip());
        fdCostYs.setOriginalExchangeRate(fdShippingAccount.getExchangeRate());
        fdCostYs.setDelFlag("N");
        fdCostYs.setStandbyB("1");
        fdCostYs.setIsUp("1");
        fdCostYs.setForeignCurrency(fdShippingAccount.getMonetaryType());

        List<FdCosdetail> fdCosdetailList2 = new ArrayList<>();
        BigDecimal  fDomesticFee2=BigDecimal.valueOf(0);
        BigDecimal  fOverseasFee2=BigDecimal.valueOf(0);
        BigDecimal  fOverseasFeeCny2=BigDecimal.valueOf(0);
        BigDecimal val2 = BigDecimal.valueOf(0);

        if(CollUtil.isNotEmpty(fdShippingAccoundetails)){

            for (FdShippingAccoundetail fdShippingAccoundetail:fdShippingAccoundetails) {
                FdCosdetail fdCosdetails = new FdCosdetail();
                fdCosdetails.setUuid(UUID.randomUUID().toString());
                fdCosdetails.setCostCode(fdCostYs.getCostCode());
                fdCosdetails.setPlatformCode(paymentCustomerCode);
                fdCosdetails.setPlatformName(paymentCustomerName);
                fdCosdetails.setCustomerName(platformName);
                fdCosdetails.setCustomerCode(platformCode);
                fdCosdetails.setPlatformLevel("1");
                fdCosdetails.setIncomeFlag("应收");
                fdCosdetails.setApplicationNumber(fdShippingAccoundetail.getApplicationNumber());
                fdCosdetails.setContainerNumber(fdShippingAccoundetail.getContainerNumber());
                fdCosdetails.setTransportOrderNumber(fdShippingAccoundetail.getTransportOrderNumber());
                fdCosdetails.setCodeBbCategoriesCode("f_fee_type");
                fdCosdetails.setCodeBbCategoriesName("发运运费");
                fdCosdetails.setCodeSsCategoriesCode("f_domestic_fee");
                fdCosdetails.setCodeSsCategoriesName("境内运费");
                fdCosdetails.setCurrency("人民币");
                fdCosdetails.setExchangeRate(BigDecimal.valueOf(1));
                fdCosdetails.setLocalCurrencyAmount(fdShippingAccoundetail.getDomesticFreight());
                fdCosdetails.setOriginalCurrencyAmount(fdShippingAccoundetail.getDomesticFreight());
                fdCosdetails.setBillCode(fdblYs);
                fdCosdetails.setBillGenerate("0");
                fdCosdetails.setStandbyA("1");
                fdCosdetails.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
                fdCosdetails.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
                fdCosdetails.setContainerTypeSize(fdShippingAccoundetail.getContainerType());
                fdCosdetails.setDestinationCountry(fdShippingAccoundetail.getDestinationCountry());
                fdCosdetailList2.add(fdCosdetails);

                FdCosdetail fdCosdetails2 = new FdCosdetail();
                fdCosdetails2.setUuid(UUID.randomUUID().toString());
                fdCosdetails2.setCostCode(fdCostYs.getCostCode());
                fdCosdetails2.setPlatformCode(paymentCustomerCode);
                fdCosdetails2.setPlatformName(paymentCustomerName);
                fdCosdetails2.setCustomerName(platformName);
                fdCosdetails2.setCustomerCode(platformCode);
                fdCosdetails2.setPlatformLevel("1");
                fdCosdetails2.setIncomeFlag("应收");
                fdCosdetails2.setApplicationNumber(fdShippingAccoundetail.getApplicationNumber());
                fdCosdetails2.setContainerNumber(fdShippingAccoundetail.getContainerNumber());
                fdCosdetails2.setTransportOrderNumber(fdShippingAccoundetail.getTransportOrderNumber());
                fdCosdetails2.setCodeBbCategoriesCode("f_fee_type");
                fdCosdetails2.setCodeBbCategoriesName("发运运费");
                fdCosdetails2.setCodeSsCategoriesCode("f_overseas_fee");
                fdCosdetails2.setCodeSsCategoriesName("境外费用");
                fdCosdetails2.setCurrency(fdShippingAccoundetail.getMonetaryType());
                fdCosdetails2.setExchangeRate(fdShippingAccoundetail.getExchangeRate());
                fdCosdetails2.setLocalCurrencyAmount(fdShippingAccoundetail.getOverseasFreightCny());
                fdCosdetails2.setOriginalCurrencyAmount(fdShippingAccoundetail.getOverseasFreightOc());
                fdCosdetails2.setBillCode(fdblYs);
                fdCosdetails2.setBillGenerate("0");
                fdCosdetails2.setStandbyA("1");
                fdCosdetails2.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
                fdCosdetails2.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
                fdCosdetails2.setContainerTypeSize(fdShippingAccoundetail.getContainerType());
                fdCosdetails2.setDestinationCountry(fdShippingAccoundetail.getDestinationCountry());
                fdCosdetailList2.add(fdCosdetails2);

                fDomesticFee2 = fDomesticFee2.add(fdShippingAccoundetail.getDomesticFreight());
                fOverseasFee2 = fOverseasFee2.add(fdShippingAccoundetail.getOverseasFreightOc());
                fOverseasFeeCny2 = fOverseasFeeCny2.add(fdShippingAccoundetail.getOverseasFreightCny());

                val2 = val2.add(fdShippingAccoundetail.getOverseasFreightCny().add(fdShippingAccoundetail.getDomesticFreight()));

            }
        }
        fdCostYs.setOverseasFreightOverseasFreight(fOverseasFee2);//境外运费
        fdCostYs.setOverseasFreightCny(fOverseasFeeCny2);
        fdCostYs.setDomesticFreight(fDomesticFee2);//境内运费

        //新增多联费用
        fdCostService.insertFdCost(fdCostYs);
        //新增多联明细费用
        fdCosdetailService.insertFdCosdetailList(fdCosdetailList2);

        //账单部分
        FdBill fdBillYs = new FdBill();
        fdBillYs.setPlatformCode(fdCostYs.getPlatformCode());
        fdBillYs.setPlatformName(fdCostYs.getPlatformName());
        fdBillYs.setPlatformLevel("1");
        fdBillYs.setProvinceTrainsNumber(fdCostYs.getProvinceTrainsNumber());
        fdBillYs.setTrainNumber(fdCostYs.getTrainNumber());
        fdBillYs.setTrainsName(fdCostYs.getTrainsName());
        fdBillYs.setShipmentTime(fdCostYs.getShipmentTime());
        fdBillYs.setShippingLines(fdCostYs.getShippingLines());
        fdBillYs.setDirection(fdCostYs.getDirection());
        fdBillYs.setStandbyA("0");
        fdBillYs.setStandbyB("0");
        fdBillYs.setStandbyC(fdCostYs.getPaymentCustomerName());
        fdBillYs.setStandbyD(fdCostYs.getPaymentCustomerCode());
        fdBillYs.setBillAmount(val2);
        fdBillYs.setAmountPayable(val2);
        fdBillYs.setStandbyF(fdCostYs.getCostCode());

        fdBillYs.setIsOffsetBalance("0");
        fdBillYs.setBillingState("0");
        fdBillYs.setUuid(UUID.randomUUID().toString());
        fdBillYs.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
        fdBillYs.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
        fdBillYs.setBillCode(fdblYs);
        fdBillMapper.insertFdBill(fdBillYs);

        //生成应付费用、应付账单-------------------------------------------------------------------------------------------------------------
        //费用部分
        final String fdblYf = sysNoConfigService.genNo("FDBL");
        FdCost fdCostYf = new FdCost();
        fdCostYf.setUuid(UUID.randomUUID().toString());
        fdCostYf.setGatheringuserCode("DL");
        fdCostYf.setGatheringuserName("多联");
        fdCostYf.setPlatformLevel("2");
        fdCostYf.setStandbyB("1");

        final String fdcYf = sysNoConfigService.genNo("FDC");
        fdCostYf.setCostCode(fdcYf);
        fdCostYf.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
        fdCostYf.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
        fdCostYf.setStandbyC(fdcYf);
        fdCostYf.setPlatformCode(paymentCustomerCode);
        fdCostYf.setPlatformName(paymentCustomerName);
        fdCostYf.setGatheringuserCode(paymentCustomerCode);
        fdCostYf.setGatheringuserName(paymentCustomerName);
        fdCostYf.setPaymentCustomerCode(platformCode);
        fdCostYf.setPaymentCustomerName(platformName);
        fdCostYf.setProvinceTrainsNumber(fdShippingAccount.getProvinceShiftNo());
        fdCostYf.setTrainNumber(fdShippingAccount.getShiftNo());
        fdCostYf.setTrainsName(fdShippingAccount.getShiftName());
        fdCostYf.setShipmentTime(formattedDate);
        fdCostYf.setShippingLines(fdShippingAccount.getShippingLine());
        fdCostYf.setDirection(fdShippingAccount.getTrip());
        fdCostYf.setOriginalExchangeRate(fdShippingAccount.getExchangeRate());
        fdCostYf.setDelFlag("N");
        fdCostYf.setStandbyB("1");
        fdCostYf.setIsUp("1");
        fdCostYf.setForeignCurrency(fdShippingAccount.getMonetaryType());

        List<FdCosdetail> fdCosdetailList3 = new ArrayList<>();
        BigDecimal  fDomesticFee3=BigDecimal.valueOf(0);
        BigDecimal  fOverseasFee3=BigDecimal.valueOf(0);
        BigDecimal  fOverseasFeeCny3=BigDecimal.valueOf(0);
        BigDecimal val3 = BigDecimal.valueOf(0);

        if(CollUtil.isNotEmpty(fdShippingAccoundetails)){

            for (FdShippingAccoundetail fdShippingAccoundetail:fdShippingAccoundetails) {
                FdCosdetail fdCosdetails = new FdCosdetail();
                fdCosdetails.setUuid(UUID.randomUUID().toString());
                fdCosdetails.setCostCode(fdCostYf.getCostCode());
                fdCosdetails.setPlatformCode(paymentCustomerCode);
                fdCosdetails.setPlatformName(paymentCustomerName);
                fdCosdetails.setCustomerName(platformName);
                fdCosdetails.setCustomerCode(platformCode);
                fdCosdetails.setPlatformLevel("2");
                fdCosdetails.setIncomeFlag("应付");
                fdCosdetails.setApplicationNumber(fdShippingAccoundetail.getApplicationNumber());
                fdCosdetails.setContainerNumber(fdShippingAccoundetail.getContainerNumber());
                fdCosdetails.setTransportOrderNumber(fdShippingAccoundetail.getTransportOrderNumber());
                fdCosdetails.setCodeBbCategoriesCode("f_fee_type");
                fdCosdetails.setCodeBbCategoriesName("发运运费");
                fdCosdetails.setCodeSsCategoriesCode("f_domestic_fee");
                fdCosdetails.setCodeSsCategoriesName("境内运费");
                fdCosdetails.setCurrency("人民币");
                fdCosdetails.setExchangeRate(BigDecimal.valueOf(1));
                fdCosdetails.setLocalCurrencyAmount(fdShippingAccoundetail.getRrDomesticFreight());
                fdCosdetails.setOriginalCurrencyAmount(fdShippingAccoundetail.getRrDomesticFreight());
                fdCosdetails.setBillCode(fdblYf);
                fdCosdetails.setBillGenerate("0");
                fdCosdetails.setStandbyA("1");
                fdCosdetails.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
                fdCosdetails.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
                fdCosdetails.setContainerTypeSize(fdShippingAccoundetail.getContainerType());
                fdCosdetails.setDestinationCountry(fdShippingAccoundetail.getDestinationCountry());
                fdCosdetailList3.add(fdCosdetails);

                FdCosdetail fdCosdetails2 = new FdCosdetail();
                fdCosdetails2.setUuid(UUID.randomUUID().toString());
                fdCosdetails2.setCostCode(fdCostYf.getCostCode());
                fdCosdetails2.setPlatformCode(paymentCustomerCode);
                fdCosdetails2.setPlatformName(paymentCustomerName);
                fdCosdetails2.setCustomerName(platformName);
                fdCosdetails2.setCustomerCode(platformCode);
                fdCosdetails2.setPlatformLevel("2");
                fdCosdetails2.setIncomeFlag("应付");
                fdCosdetails2.setApplicationNumber(fdShippingAccoundetail.getApplicationNumber());
                fdCosdetails2.setContainerNumber(fdShippingAccoundetail.getContainerNumber());
                fdCosdetails2.setTransportOrderNumber(fdShippingAccoundetail.getTransportOrderNumber());
                fdCosdetails2.setCodeBbCategoriesCode("f_fee_type");
                fdCosdetails2.setCodeBbCategoriesName("发运运费");
                fdCosdetails2.setCodeSsCategoriesCode("f_overseas_fee");
                fdCosdetails2.setCodeSsCategoriesName("境外费用");
                fdCosdetails2.setCurrency(fdShippingAccoundetail.getMonetaryType());
                fdCosdetails2.setExchangeRate(fdShippingAccoundetail.getExchangeRate());
                fdCosdetails2.setLocalCurrencyAmount(fdShippingAccoundetail.getRrOverseasFreightCny());
                fdCosdetails2.setOriginalCurrencyAmount(fdShippingAccoundetail.getRrOverseasFreightOc());
                fdCosdetails2.setBillCode(fdblYf);
                fdCosdetails2.setBillGenerate("0");
                fdCosdetails2.setStandbyA("1");
                fdCosdetails2.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
                fdCosdetails2.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
                fdCosdetails2.setContainerTypeSize(fdShippingAccoundetail.getContainerType());
                fdCosdetails2.setDestinationCountry(fdShippingAccoundetail.getDestinationCountry());
                fdCosdetailList3.add(fdCosdetails2);

                fDomesticFee3 = fDomesticFee3.add(fdShippingAccoundetail.getRrDomesticFreight());
                fOverseasFee3 = fOverseasFee3.add(fdShippingAccoundetail.getRrOverseasFreightOc());
                fOverseasFeeCny3 = fOverseasFeeCny3.add(fdShippingAccoundetail.getRrOverseasFreightCny());

                val3 = val3.add(fdShippingAccoundetail.getRrOverseasFreightCny().add(fdShippingAccoundetail.getRrDomesticFreight()));

            }
        }
        fdCostYf.setOverseasFreightOverseasFreight(fOverseasFee3);//境外运费
        fdCostYf.setOverseasFreightCny(fOverseasFeeCny3);
        fdCostYf.setDomesticFreight(fDomesticFee3);//境内运费

        //新增多联费用
        fdCostService.insertFdCost(fdCostYf);
        //新增多联明细费用
        fdCosdetailService.insertFdCosdetailList(fdCosdetailList3);

        //账单部分
        FdBill fdBillYf = new FdBill();
        fdBillYf.setPlatformCode(fdCostYf.getPlatformCode());
        fdBillYf.setPlatformName(fdCostYf.getPlatformName());
        fdBillYf.setPlatformLevel("2");
        fdBillYf.setProvinceTrainsNumber(fdCostYf.getProvinceTrainsNumber());
        fdBillYf.setTrainNumber(fdCostYf.getTrainNumber());
        fdBillYf.setTrainsName(fdCostYf.getTrainsName());
        fdBillYf.setShipmentTime(fdCostYf.getShipmentTime());
        fdBillYf.setShippingLines(fdCostYf.getShippingLines());
        fdBillYf.setDirection(fdCostYf.getDirection());
        fdBillYf.setStandbyA("0");
        fdBillYf.setStandbyB("0");
        fdBillYf.setStandbyC(fdCostYf.getPaymentCustomerName());
        fdBillYf.setStandbyD(fdCostYf.getPaymentCustomerCode());
        fdBillYf.setBillAmount(val3);
        fdBillYf.setAmountPayable(val3);
        fdBillYf.setStandbyF(fdCostYf.getCostCode());
        fdBillYf.setIsOffsetBalance("0");
        fdBillYf.setBillingState("0");
        fdBillYf.setUuid(UUID.randomUUID().toString());
        fdBillYf.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
        fdBillYf.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
        fdBillYf.setBillCode(fdblYf);
        fdBillMapper.insertFdBill(fdBillYf);

        return true;
    }

    private boolean Unboxing(String rowid) {

        FdShippingAccount fdShippingAccount = fdShippingAccountService.selectFdShippingAccountById(Long.parseLong(rowid));
        FdShippingAccoundetail fdShippingAccoundetail = new FdShippingAccoundetail();
        fdShippingAccoundetail.setAccountCode(fdShippingAccount.getAccountCode());
        List<FdShippingAccoundetail> fdShippingAccoundetails = fdShippingAccoundetailService.selectFdShippingAccoundetailList(fdShippingAccoundetail);
        ArrayList<String> strings = new ArrayList<>();
        for (FdShippingAccoundetail shippingAccoundetail : fdShippingAccoundetails) {
            if ("1".equals(shippingAccoundetail.getContainerNewestStatus())) {
                strings.add(shippingAccoundetail.getContainerNumber());
            }
        }
        if (strings.size() != 0) {
            fdCosdetailService.updateCosdetailbycontainernumber(strings, fdShippingAccount.getProvinceShiftNo(), fdShippingAccount.getShiftNo());

        }
        return true;
    }

    @Override
    public List<Map> selectcustomerbyplatformcode(Map map) {

        return fdCostMapper.selectcustomerbyplatformcode(map);

    }

    @Override
    public Boolean savecostbychangebox(String string) {

            BasChangeboxRetreat basChangeboxRetreat1 = basChangeboxRetreatService.selectBasChangeboxRetreatById(string);
            //思路 撤箱 要新增一条撤仓费用的费用明细  以及要生成原先该箱号生成过的运费相应的负数金额用来抵消
            BasChangeboxDetail basChangeboxDetail = new BasChangeboxDetail();
            basChangeboxDetail.setBusinessid(basChangeboxRetreat1.getBusinessid());
            basChangeboxDetail.setDeleteFlag("N");
            basChangeboxDetail.setBoxnumberStatus("1");

            List<BasChangeboxDetail> basChangeboxDetails = basChangeboxDetailService.selectBasChangeboxDetailList(basChangeboxDetail);
            for (BasChangeboxDetail changeboxDetail : basChangeboxDetails) {
                //这里都是撤箱的箱子，通过运单号和箱号查出来进行处理
                FdCosdetail fdCosdetail = new FdCosdetail();
                fdCosdetail.setTransportOrderNumber(basChangeboxRetreat1.getWaybillNo());
                fdCosdetail.setContainerNumber(changeboxDetail.getContainerNo());
                fdCosdetail.setDelFlag("N");
                List<FdCosdetail> fdCosdetails = fdCosdetailService.selectFdCosdetailList(fdCosdetail);
                if(CollUtil.isNotEmpty(fdCosdetails)){
                    for (FdCosdetail cosdetail : fdCosdetails) {
                        //这里是需要生成负数费用得费用明细
                        cosdetail.setUuid(UUID.randomUUID().toString());
                        if (cosdetail.getOriginalCurrencyAmount() != null) {
                            cosdetail.setOriginalCurrencyAmount(cosdetail.getOriginalCurrencyAmount().negate());
                        }
                        if (cosdetail.getLocalCurrencyAmount() != null) {
                            cosdetail.setLocalCurrencyAmount(cosdetail.getLocalCurrencyAmount().negate());
                        }
                        cosdetail.setUpdateTime(null);
                        cosdetail.setCreateTime(null);
                        cosdetail.setBillCode(null);
                        cosdetail.setBillGenerate("0");

                    }

                    //保存负数明细
                    fdCosdetailService.insertFdCosdetailList(fdCosdetails);

                    FdCosdetail fdCosdetail1 = new FdCosdetail();
                    BeanUtils.copyProperties(fdCosdetails.get(0), fdCosdetail1);
                    fdCosdetail1.setBillCode(null);
                    fdCosdetail1.setBillGenerate("0");
                    fdCosdetail1.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
                    fdCosdetail1.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());

                    fdCosdetail1.setLocalCurrencyAmount(changeboxDetail.getResveredField08());
                    fdCosdetail1.setOriginalCurrencyAmount(null);
                    fdCosdetail1.setExchangeRate(null);
                    fdCosdetail1.setUuid(UUID.randomUUID().toString());
                    fdCosdetail1.setCodeBbCategoriesName("额外费用");
                    fdCosdetail1.setCodeBbCategoriesCode("f_extra_fee_type");
                    fdCosdetail1.setCodeSsCategoriesCode("f_cancel_fee");
                    fdCosdetail1.setCodeSsCategoriesName("亏舱费");
                    fdCosdetail1.setUpdateTime(null);
                    fdCosdetail1.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
                    fdCosdetail1.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
                    fdCosdetail1.setCreateTime(null);
                    fdCosdetailService.insert(fdCosdetail1);
                }

            }

        return true;
    }

    @Override
    public int updateFdCostCostCode(FdCost fdCost) {
        return fdCostMapper.updateFdCostCostCode(fdCost);
    }

    @Override
    public int updateFdCostByCostCode(FdCost fdCost) {
        return fdCostMapper.updateFdCostByCostCode(fdCost);
    }

    @Override
    public int updateFdCostByCostCodeYf(FdCost fdCost) {
        return fdCostMapper.updateFdCostByCostCodeYf(fdCost);
    }

    @Override
    public int updateFdCostByProvinceTrainsNumber(FdCost fdCost) {
        return fdCostMapper.updateFdCostByProvinceTrainsNumber(fdCost);
    }

    @Override
    public R updateOriginalExchangeRate(FdCost fdCost) {
        if("".equals(fdCost.getCostCode()) || fdCost.getCostCode()==null){
            return new R<>(500, Boolean.FALSE, fdCost,"费用编码不能为空");
        }
        if(fdCost.getOriginalExchangeRate()==null){
            return new R<>(500, Boolean.FALSE, fdCost,"汇率不能为空");
        }
        FdCosdetail fdCosdetail=new FdCosdetail();
        fdCosdetail.setCostCode(fdCost.getCostCode());
        fdCosdetail.setCodeBbCategoriesCode("f_fee_type");
        fdCosdetail.setCodeSsCategoriesCode("f_overseas_fee");
        fdCosdetail.setDelFlag("N");
        List<FdCosdetail> fdCosdetails = fdCosdetailService.selectFdCosdetailList(fdCosdetail);
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        if(fdCosdetails.size()>0) {
            BigDecimal bigDecimal=BigDecimal.valueOf(0);
            for (FdCosdetail fdCosdeta : fdCosdetails) {
                    fdCosdeta.setExchangeRate(fdCost.getOriginalExchangeRate());
                    fdCosdeta.setLocalCurrencyAmount(fdCosdeta.getExchangeRate().multiply(fdCosdeta.getOriginalCurrencyAmount()));
                    fdCosdeta.setUpdateUsercode(userInfo.getUserName());
                    fdCosdeta.setUpdateUserrealname(userInfo.getRealName());
                    bigDecimal.add(fdCosdeta.getLocalCurrencyAmount());
            }
            fdCosdetailService.updateFdCosdetailExchangeRate(fdCosdetails);
        }
        fdCost.setUpdateUsercode(userInfo.getUserName());
        fdCost.setUpdateUserrealname(userInfo.getRealName());
        fdCostMapper.updateFdCostCostByOriginalExchangeRate(fdCost);

        if(fdCosdetails.size()>0) {
            //修改订单查询的费用
            WaybillContainerInfo waybillContainerInfo = new WaybillContainerInfo();
            waybillContainerInfo.setWaybillNo(fdCosdetails.get(0).getTransportOrderNumber());
            waybillContainerInfo.setOrderNo(fdCosdetails.get(0).getApplicationNumber());
            waybillContainerInfo.setDeleteFlag("N");
            List<WaybillContainerInfo> waybillContainerInfos = waybillContainerInfoService.selectWaybillContainerInfoLists(waybillContainerInfo);
            if(waybillContainerInfos.size()>0) {
                for(WaybillContainerInfo waybill:waybillContainerInfos){
                    waybill.setResveredField01(fdCost.getOriginalExchangeRate());
                    waybill.setOverseasFreightRmb(waybill.getOverseasFreight().multiply(fdCost.getOriginalExchangeRate()).setScale(2,BigDecimal.ROUND_HALF_UP));
                    waybill.setUpdateWho(userInfo.getUserName());
                    waybill.setUpdateWhoName(userInfo.getRealName());
                }
                waybillContainerInfoService.updateWaybillContainerInfos(waybillContainerInfos);
            }
        }

        return new R<>(200,Boolean.TRUE,"修改成功");
    }

    @Override
    public List<FdCost> selectProvinceTrainsNumber(FdCost fdCost) {
        fdCost.setDelFlag("N");
        return fdCostMapper.selectProvinceTrainsNumber(fdCost);
    }



    private List<FdCosdetail> newgetcostdetailbyaccount(String rowid, HashMap hashMap, FdCost fdCost) {


        //获取台账和台账子表信息
        FdShippingAccount fdShippingAccount = fdShippingAccountService.selectFdShippingAccountById(Long.parseLong(rowid));
        FdShippingAccoundetail fdShippingAccoundetail = new FdShippingAccoundetail();
        fdShippingAccoundetail.setAccountCode(fdShippingAccount.getAccountCode());
        fdShippingAccoundetail.setDeleteFlag("N");
        List<FdShippingAccoundetail> fdShippingAccoundetails = fdShippingAccoundetailService.selectFdShippingAccoundetailList(fdShippingAccoundetail);
        ArrayList<FdCosdetail> fdCosdetails = new ArrayList<>();
        for (FdShippingAccoundetail shippingAccoundetail : fdShippingAccoundetails) {

            //循环每个箱子
            //判断不是新增的箱子就结束掉
            if (!"0".equals(shippingAccoundetail.getContainerNewestStatus())) {
                continue;
            }

            //每个箱子都创建一条费用明细信息

            FdCosdetail fdCosdetail = new FdCosdetail();
            BeanUtils.copyProperties(fdCost, fdCosdetail);
            //客户信息
            fdCosdetail.setCustomerName(fdCost.getPaymentCustomerName());

            //uuid
            fdCosdetail.setCustomerCode(fdCost.getPaymentCustomerCode());
            fdCosdetail.setUuid(UUID.randomUUID().toString());

            //收支标识
            fdCosdetail.setIncomeFlag(hashMap.get("Invoice") + "");
            //箱号
            fdCosdetail.setContainerNumber(shippingAccoundetail.getContainerNumber());
            //费用大类编码
            fdCosdetail.setCodeBbCategoriesCode(hashMap.get("codeBbCategoriesCode") + "");
            fdCosdetail.setCodeBbCategoriesName(hashMap.get("codeBbCategoriesName") + "");
            //费用小类
            fdCosdetail.setCodeSsCategoriesCode(hashMap.get("codeSsCategoriesCode") + "");
            fdCosdetail.setCodeSsCategoriesName(hashMap.get("codeSsCategoriesName") + "");
            //币种

            fdCosdetail.setCurrency(shippingAccoundetail.getMonetaryType());
            // 汇率
            fdCosdetail.setExchangeRate(shippingAccoundetail.getExchangeRate());
            //本币金额

            fdCosdetail.setLocalCurrencyAmount(shippingAccoundetail.getOverseasFreightCny());
            //原币金额
            fdCosdetail.setOriginalCurrencyAmount(shippingAccoundetail.getOverseasFreightOc());
            //运单号
            fdCosdetail.setTransportOrderNumber(shippingAccoundetail.getTransportOrderNumber());
            //申请单号
            fdCosdetail.setApplicationNumber(shippingAccoundetail.getApplicationNumber());
            //货源组织单位
            fdCosdetail.setStandbyC(shippingAccoundetail.getOrgUnit());

            fdCosdetails.add(fdCosdetail);

        }
        return fdCosdetails;
    }

    private List<FdCosdetail> getcostdetailbyaccount(String rowid, HashMap hashMap, FdCost fdCost) {

        //获取台账和台账子表信息
        FdShippingAccount fdShippingAccount = fdShippingAccountService.selectFdShippingAccountById(Long.parseLong(rowid));
        FdShippingAccoundetail fdShippingAccoundetail = new FdShippingAccoundetail();
        fdShippingAccoundetail.setAccountCode(fdShippingAccount.getAccountCode());
        fdShippingAccoundetail.setDeleteFlag("N");
        List<FdShippingAccoundetail> fdShippingAccoundetails = fdShippingAccoundetailService.selectFdShippingAccoundetailList(fdShippingAccoundetail);
        ArrayList<FdCosdetail> fdCosdetails = new ArrayList<>();
        for (FdShippingAccoundetail shippingAccoundetail : fdShippingAccoundetails) {

            //循环每个箱子

            //判断不是新增的箱子就结束掉
            if (!"0".equals(shippingAccoundetail.getContainerNewestStatus())) {
                continue;
            }
            //每个箱子都创建一条费用明细信息

            FdCosdetail fdCosdetail = new FdCosdetail();
            BeanUtils.copyProperties(fdCost, fdCosdetail);
            //客户信息
            fdCosdetail.setCustomerName(fdCost.getPaymentCustomerName());

            //uuid
            fdCosdetail.setCustomerCode(fdCost.getPaymentCustomerCode());
            fdCosdetail.setUuid(UUID.randomUUID().toString());

            //收支标识
            fdCosdetail.setIncomeFlag(hashMap.get("Invoice") + "");
            //箱号
            fdCosdetail.setContainerNumber(shippingAccoundetail.getContainerNumber());
            //费用大类编码
            fdCosdetail.setCodeBbCategoriesCode(hashMap.get("codeBbCategoriesCode") + "");
            fdCosdetail.setCodeBbCategoriesName(hashMap.get("codeBbCategoriesName") + "");
            //费用小类
            fdCosdetail.setCodeSsCategoriesCode(hashMap.get("codeSsCategoriesCode") + "");
            fdCosdetail.setCodeSsCategoriesName(hashMap.get("codeSsCategoriesName") + "");
            //币种
            fdCosdetail.setCurrency("人民币");
            // 汇率
            fdCosdetail.setExchangeRate(shippingAccoundetail.getExchangeRate());
            //本币金额
            BigDecimal CurrencyAmount = shippingAccoundetail.getDomesticFreight();
            fdCosdetail.setLocalCurrencyAmount(CurrencyAmount);
            //原币金额
            fdCosdetail.setOriginalCurrencyAmount(CurrencyAmount);
            //运单号
            fdCosdetail.setTransportOrderNumber(shippingAccoundetail.getTransportOrderNumber());
            //申请单号
            fdCosdetail.setApplicationNumber(shippingAccoundetail.getApplicationNumber());
            //货源组织单位
            fdCosdetail.setStandbyC(shippingAccoundetail.getOrgUnit());

            fdCosdetails.add(fdCosdetail);

        }
        return fdCosdetails;
    }

    private FdCost getcostbyaccount(String rowid) {
        FdShippingAccount fdShippingAccount = fdShippingAccountService.selectFdShippingAccountById(Long.parseLong(rowid));

        FdCost fdCost = new FdCost();
        fdCost.setCostCode(sysNoConfigService.genNo("FDC"));
        fdCost.setUuid(UUID.randomUUID().toString());

        fdCost.setPlatformCode(fdShippingAccount.getPlatformCode());
        fdCost.setPlatformName(fdShippingAccount.getPlatformName());
        fdCost.setPlatformLevel("0");

        fdCost.setGatheringuserCode(fdShippingAccount.getPlatformCode());
        fdCost.setGatheringuserName(fdShippingAccount.getPlatformName());

        //付款用户信息
        fdCost.setPaymentCustomerCode(fdShippingAccount.getCustomerNo());
        fdCost.setPaymentCustomerName(fdShippingAccount.getCustomerName());

        //省级班列号
        fdCost.setProvinceTrainsNumber(fdShippingAccount.getProvinceShiftNo());
        //班次号
        fdCost.setTrainNumber(fdShippingAccount.getShiftNo());
        // 班列名称
        fdCost.setTrainsName(fdShippingAccount.getShiftName());
        //发运时间
        ZoneId zoneId = ZoneId.systemDefault();
//        LocalDateTime localDateTime = fdShippingAccount.getShippingTime();
        LocalDateTime localDateTime = fdShippingAccount.getResveredField08();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        String localTime = df.format(localDateTime);


        fdCost.setShipmentTime(localTime);
        //发运线路 0-中亚、1-中欧
        fdCost.setShippingLines(fdShippingAccount.getShippingLine());
        //方向
        fdCost.setDirection(fdShippingAccount.getTrip());
        //境外币种

        //为了获取币种且不该别人的代码

        fdCost.setForeignCurrency(fdShippingAccount.getMonetaryType());
        //通过运单号获取当前运单下所有的箱子

        //汇率
        fdCost.setOriginalExchangeRate(fdShippingAccount.getExchangeRate());
        //境外运费 原币
        fdCost.setOverseasFreightOverseasFreight(fdShippingAccount.getOverseasFreightCny());
        fdCost.setOverseasFreightCny(fdShippingAccount.getOverseasFreightCny());
        //境内运费
        fdCost.setDomesticFreight(fdShippingAccount.getDomesticFreight());
        //备注信息
        fdCost.setNoteInformation(fdShippingAccount.getRemarks());
        fdCost.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
        fdCost.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());

        return fdCost;
    }

    private ArrayList<FdCosdetail> newgetcostdetailsbyfdcost(FdCost fdCost, HashMap<String, Object> hashMap) {

        ArrayList<FdCosdetail> fdCosdetails = new ArrayList<>();

        WaybillContainerInfo waybillContainerInfo1 = new WaybillContainerInfo();
        waybillContainerInfo1.setWaybillNo(fdCost.getTransportOrderNumber());
        waybillContainerInfo1.setDeleteFlag("N");
        List<WaybillContainerInfo> waybillContainerInfos = waybillContainerInfoService.selectWaybillContainerInfoList(waybillContainerInfo1);

        //通过运单号获取当前运单下所有的箱子
        if (waybillContainerInfos.size() != 0) {

            //每个箱子都创建一条费用明细信息
            for (WaybillContainerInfo waybillContainerInfo : waybillContainerInfos) {
                FdCosdetail fdCosdetail = new FdCosdetail();
                BeanUtils.copyProperties(fdCost, fdCosdetail);
                //客户信息
                fdCosdetail.setCustomerName(fdCost.getPaymentCustomerName());

                //uuid
                fdCosdetail.setCustomerCode(fdCost.getPaymentCustomerCode());
                fdCosdetail.setUuid(UUID.randomUUID().toString());

                //收支标识
                fdCosdetail.setIncomeFlag(hashMap.get("Invoice") + "");
                //箱号
                fdCosdetail.setContainerNumber(waybillContainerInfo.getContainerNo());
                //费用大类编码
                fdCosdetail.setCodeBbCategoriesCode(hashMap.get("codeBbCategoriesCode") + "");
                fdCosdetail.setCodeBbCategoriesName(hashMap.get("codeBbCategoriesName") + "");
                //费用小类
                fdCosdetail.setCodeSsCategoriesCode(hashMap.get("codeSsCategoriesCode") + "");
                fdCosdetail.setCodeSsCategoriesName(hashMap.get("codeSsCategoriesName") + "");
                //币种
                String containerNo = waybillContainerInfo.getContainerNo();
                WaybillContainerchargeDetail waybillContainerchargeDetail = new WaybillContainerchargeDetail();
                waybillContainerchargeDetail.setContainerNo(containerNo);
                //币种
                fdCosdetail.setCurrency(fdCost.getForeignCurrency());
                // 汇率
                BigDecimal num1 = waybillContainerInfo.getResveredField01();
                BigDecimal calculation = num1;
                BigDecimal num2 = BigDecimal.valueOf(0).add(calculation);

                Float finalResults = Float.parseFloat(num2.toString());
                fdCosdetail.setExchangeRate(num2);
                //本币金额
                BigDecimal CurrencyAmount = waybillContainerInfo.getOverseasFreight().multiply(num2);
                fdCosdetail.setLocalCurrencyAmount(CurrencyAmount);
                //原币金额
                fdCosdetail.setOriginalCurrencyAmount(waybillContainerInfo.getOverseasFreight());
                System.out.println(waybillContainerInfo.getDomesticFreight());
                if (BigDecimal.valueOf(0).compareTo(waybillContainerInfo.getDomesticFreight().add(waybillContainerInfo.getOverseasFreightRmb())) != 0) {
                    fdCosdetails.add(fdCosdetail);
                }
            }
        }
        return fdCosdetails;
    }

    private ArrayList<FdCosdetail> getcostdetailsbyfdcost(FdCost fdCost, HashMap<String, Object> hashMap) {
        ArrayList<FdCosdetail> fdCosdetails = new ArrayList<>();

        WaybillContainerInfo waybillContainerInfo1 = new WaybillContainerInfo();
        waybillContainerInfo1.setWaybillNo(fdCost.getTransportOrderNumber());
        waybillContainerInfo1.setDeleteFlag("N");
        List<WaybillContainerInfo> waybillContainerInfos = waybillContainerInfoService.selectWaybillContainerInfoList(waybillContainerInfo1);

        //通过运单号获取当前运单下所有的箱子
        if (waybillContainerInfos.size() != 0) {

            //每个箱子都创建一条费用明细信息
            for (WaybillContainerInfo waybillContainerInfo : waybillContainerInfos) {
                FdCosdetail fdCosdetail = new FdCosdetail();
                BeanUtils.copyProperties(fdCost, fdCosdetail);
                //客户信息
                fdCosdetail.setCustomerName(fdCost.getPaymentCustomerName());

                //uuid
                fdCosdetail.setCustomerCode(fdCost.getPaymentCustomerCode());
                fdCosdetail.setUuid(UUID.randomUUID().toString());

                //收支标识
                fdCosdetail.setIncomeFlag(hashMap.get("Invoice") + "");
                //箱号
                fdCosdetail.setContainerNumber(waybillContainerInfo.getContainerNo());
                //费用大类编码
                fdCosdetail.setCodeBbCategoriesCode(hashMap.get("codeBbCategoriesCode") + "");
                fdCosdetail.setCodeBbCategoriesName(hashMap.get("codeBbCategoriesName") + "");
                //费用小类
                fdCosdetail.setCodeSsCategoriesCode(hashMap.get("codeSsCategoriesCode") + "");
                fdCosdetail.setCodeSsCategoriesName(hashMap.get("codeSsCategoriesName") + "");
                //币种
                String containerNo = waybillContainerInfo.getContainerNo();
                WaybillContainerchargeDetail waybillContainerchargeDetail = new WaybillContainerchargeDetail();
                waybillContainerchargeDetail.setContainerNo(containerNo);
// TODO: 2021/8/27 箱号表添加后在该
                //
//                fdCosdetail.setCurrency(monetaryType);
                // 汇率
                BigDecimal num1 = waybillContainerInfo.getResveredField01();
                BigDecimal calculation = num1;
                BigDecimal num2 = BigDecimal.valueOf(1);
                //币种
                fdCosdetail.setCurrency("人民币");
                Float finalResults = Float.parseFloat(num2.toString());
                fdCosdetail.setExchangeRate(num2);
                //本币金额
                fdCosdetail.setLocalCurrencyAmount(waybillContainerInfo.getDomesticFreight());
                //原币金额
                fdCosdetail.setOriginalCurrencyAmount(waybillContainerInfo.getDomesticFreight());
                System.out.println(waybillContainerInfo.getDomesticFreight());
                if (BigDecimal.valueOf(0).compareTo(waybillContainerInfo.getDomesticFreight().add(waybillContainerInfo.getOverseasFreightRmb())) != 0) {
                    fdCosdetails.add(fdCosdetail);
                }
            }
        }
        return fdCosdetails;
    }

    //通过运单信息生成一个费用主表信息
    private FdCost getfdcostbyWaybillHeader(String rowid) {
        WaybillHeader waybillHeader = waybillHeaderService.selectWaybillHeaderById(rowid);
        WaybillContainerInfo cn=new WaybillContainerInfo();
        if (waybillHeader == null) {
            return null;
        }else{
            /*2021/10/8之前逻辑取错了（费用管理列表中的境内运费、境外运费原币、境外运费人民币），添加查询sql,从集装箱表里再算一遍*/
            cn=waybillContainerInfoMapper.getThreeAmount(waybillHeader.getWaybillNo());
        }
        FdCost fdCost = new FdCost();
        fdCost.setCostCode(sysNoConfigService.genNo("FDC"));
        fdCost.setUuid(UUID.randomUUID().toString());

        fdCost.setPlatformCode(waybillHeader.getPlatformCode());
        fdCost.setPlatformName(waybillHeader.getPlatformName());
        fdCost.setPlatformLevel("0");

        fdCost.setGatheringuserCode(waybillHeader.getPlatformCode());
        fdCost.setGatheringuserName(waybillHeader.getPlatformName());

        //付款用户信息
        fdCost.setPaymentCustomerCode(waybillHeader.getCustomerNo());
        fdCost.setPaymentCustomerName(waybillHeader.getCustomerName());
        //申请单号
        fdCost.setApplicationNumber(waybillHeader.getOrderNo());
        //运单号
        fdCost.setTransportOrderNumber(waybillHeader.getWaybillNo());
        //省级班列号
        //班次号
        fdCost.setTrainNumber(waybillHeader.getShiftNo());
        // 班列名称
        fdCost.setTrainsName(waybillHeader.getTrainName());
        //发运时间
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = waybillHeader.getShippingTime().toInstant().atZone(zoneId).toLocalDateTime();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        String localTime = df.format(localDateTime);
        LocalDateTime ldt = LocalDateTime.parse("2017-09-28 17:07:05", df);

        fdCost.setShipmentTime(localTime);
        //发运线路 0-中亚、1-中欧
        fdCost.setShippingLines(waybillHeader.getShippingLine());
        //方向
        fdCost.setDirection(waybillHeader.getTrip());

        //为了获取币种且不该别人的代码
        HashMap<String, Object> map = new HashMap<>();

        WaybillContainerInfo waybillContainerInfo1 = new WaybillContainerInfo();
        //通过运单号查询
        waybillContainerInfo1.setWaybillNo(fdCost.getTransportOrderNumber());
        List<WaybillContainerInfo> waybillContainerInfos = waybillContainerInfoService.selectWaybillContainerInfoList(waybillContainerInfo1);
        WaybillContainerInfo waybillContainerInfo = waybillContainerInfos.get(0);
        //箱号查询
        String containerNo = waybillContainerInfo.getContainerNo();
        WaybillContainerchargeDetail waybillContainerchargeDetail = new WaybillContainerchargeDetail();
        waybillContainerchargeDetail.setContainerNo(containerNo);
        // TODO: 2021/8/27 币种从箱号表里拿
        fdCost.setForeignCurrency(waybillContainerInfo.getMonetaryType());
        //通过运单号获取当前运单下所有的箱子

        //汇率
        fdCost.setOriginalExchangeRate(waybillHeader.getExchangeRate());
        //境外运费 原币
        fdCost.setOverseasFreightOverseasFreight(cn.getOverseasFreight());
        fdCost.setOverseasFreightCny(cn.getOverseasFreightRmb());
        //境内运费
        fdCost.setDomesticFreight(cn.getDomesticFreight());
        //备注信息
        fdCost.setNoteInformation(waybillHeader.getRemarks());
        fdCost.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
        fdCost.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
        return fdCost;
    }

    @Override
    public void updateBill(FdBillVO fdBillVO) {
        final FdBill fdBill = fdBillMapper.selectFdBillById(fdBillVO.getUuid());
        final List<FdCosdetail> fdCosdetailList = fdBillVO.getFdCosdetailList();
        //更新账单对用费用
        FdCosdetail sel = new FdCosdetail();
        sel.setBillCode(fdBillVO.getBillCode());
        sel.setDelFlag("N");
        final List<FdCosdetail> fdCosdetails = fdCosdetailMapper.selectFdCosdetailList(sel);
        if(CollUtil.isNotEmpty(fdCosdetailList)){
            for (FdCosdetail fdCosdetail:fdCosdetailList
            ) {
                if(CollUtil.isNotEmpty(fdCosdetails)){
                    for (FdCosdetail old:fdCosdetailList
                    ) {
                        if(old.getContainerNumber().equals(fdCosdetail.getContainerNumber()) && old.getCodeSsCategoriesCode().equals(fdCosdetail.getCodeSsCategoriesCode())){
                            fdCosdetail.setUuid(old.getUuid());
                            fdCosdetail.setUpdateUsercode(SecurityUtils.getUserInfo().getUserName());
                            fdCosdetail.setUpdateUserrealname(SecurityUtils.getUserInfo().getRealName());
                            fdCosdetail.setUpdateTime(String.valueOf(new Date()));
                            fdCosdetailMapper.updateFdCosdetail(fdCosdetail);
                        }
                    }
                }
            }
        }
        FdCost fdCost = new FdCost();
        fdCost.setCostCode(fdBill.getStandbyF());
        fdCost.setOverseasFreightOverseasFreight(fdBillVO.getOverseasFreightOverseasFreight());
        fdCost.setOverseasFreightCny(fdBillVO.getOverseasFreightCny());
        fdCost.setDomesticFreight(fdBillVO.getDomesticFreight());
        fdCostMapper.updateFdCost(fdCost);
        //更新账单对用台账
        String applicationNumber = fdCosdetails.get(0).getApplicationNumber();
        String transportOrderNumber = fdCosdetails.get(0).getTransportOrderNumber();

        if(StrUtil.isNotEmpty(applicationNumber) && StrUtil.isNotEmpty(transportOrderNumber)){
            FdShippingAccoundetail fdShippingAccoundetail = new FdShippingAccoundetail();
            fdShippingAccoundetail.setApplicationNumber(applicationNumber);
            fdShippingAccoundetail.setTransportOrderNumber(transportOrderNumber);
            fdShippingAccoundetail.setDeleteFlag("N");
            final List<FdShippingAccoundetail> fdShippingAccoundetails = fdShippingAccoundetailMapper.selectFdShippingAccoundetailList(fdShippingAccoundetail);

            List<FdShippingAccoundetail> newList = new ArrayList<>();

            if(CollUtil.isNotEmpty(fdShippingAccoundetails)){
                final String accountCode = fdShippingAccoundetails.get(0).getAccountCode();
                BigDecimal overseasFreightOc = BigDecimal.valueOf(0);
                BigDecimal overseasFreightCny = BigDecimal.valueOf(0);
                BigDecimal domesticFreight = BigDecimal.valueOf(0);
                if(CollUtil.isNotEmpty(fdCosdetailList)){
                    for (FdCosdetail fdCosdetail:fdCosdetailList
                    ) {
                        for (FdShippingAccoundetail old:fdShippingAccoundetails
                        ) {
                            if(old.getContainerNumber().equals(fdCosdetail.getContainerNumber())){

                                FdShippingAccoundetail newFd = new FdShippingAccoundetail();
                                newFd.setAccountCode(old.getAccountCode());
                                newFd.setContainerNo(old.getContainerNo());

                                if("1".equals(fdBillVO.getPlatformLevel())){
                                    //应收
                                    if("f_domestic_fee".equals(fdCosdetail.getCodeSsCategoriesCode())){
                                        newFd.setDomesticFreight(fdCosdetail.getLocalCurrencyAmount());
                                        domesticFreight = domesticFreight.add(fdCosdetail.getLocalCurrencyAmount());
                                    }
                                    if("f_overseas_fee".equals(fdCosdetail.getCodeSsCategoriesCode())){
                                        newFd.setOverseasFreightOc(fdCosdetail.getOriginalCurrencyAmount());
                                        newFd.setOverseasFreightCny(fdCosdetail.getLocalCurrencyAmount());
                                        overseasFreightOc = overseasFreightOc.add(fdCosdetail.getOriginalCurrencyAmount());
                                        overseasFreightCny = overseasFreightCny.add(fdCosdetail.getLocalCurrencyAmount());
                                    }
                                } else if ("2".equals(fdBillVO.getPlatformLevel())) {
                                    //应付
                                    if("f_domestic_fee".equals(fdCosdetail.getCodeSsCategoriesCode())){
                                        newFd.setRrDomesticFreight(fdCosdetail.getLocalCurrencyAmount());
                                    }
                                    if("f_overseas_fee".equals(fdCosdetail.getCodeSsCategoriesCode())){
                                        newFd.setRrOverseasFreightOc(fdCosdetail.getOriginalCurrencyAmount());
                                        newFd.setRrOverseasFreightCny(fdCosdetail.getLocalCurrencyAmount());
                                    }
                                }

                                newList.add(newFd);
                            }
                        }
                    }
                }

                if ("1".equals(fdBillVO.getPlatformLevel())) {
                    FdShippingAccount fdShippingAccount = new FdShippingAccount();
                    fdShippingAccount.setAccountCode(accountCode);
                    fdShippingAccount.setOverseasFreightOc(overseasFreightOc);
                    fdShippingAccount.setOverseasFreightCny(overseasFreightCny);
                    fdShippingAccount.setDomesticFreight(domesticFreight);
                    fdShippingAccount.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                    fdShippingAccount.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                    fdShippingAccount.setUpdateTime(LocalDateTime.now());
                    fdShippingAccountMapper.updateFdShippingAccountByAccountCode(fdShippingAccount);
                }

                for (FdShippingAccoundetail newFd:newList
                ) {
                    newFd.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                    newFd.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                    newFd.setUpdateTime(LocalDateTime.now());
                    fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode(newFd);
                }
            }
        }
    }
}
