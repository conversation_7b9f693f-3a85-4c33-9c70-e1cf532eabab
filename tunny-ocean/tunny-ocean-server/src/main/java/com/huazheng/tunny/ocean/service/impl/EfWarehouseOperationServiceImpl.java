package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.EfWarehouseOperationMapper;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseOperation;
import com.huazheng.tunny.ocean.service.EfWarehouseOperationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("efWarehouseOperationService")
public class EfWarehouseOperationServiceImpl extends ServiceImpl<EfWarehouseOperationMapper, EfWarehouseOperation> implements EfWarehouseOperationService {

    @Autowired
    private EfWarehouseOperationMapper efWarehouseOperationMapper;

    public EfWarehouseOperationMapper getEfWarehouseOperationMapper() {
        return efWarehouseOperationMapper;
    }

    public void setEfWarehouseOperationMapper(EfWarehouseOperationMapper efWarehouseOperationMapper) {
        this.efWarehouseOperationMapper = efWarehouseOperationMapper;
    }

    /**
     * 查询e融同步仓单操作表信息
     *
     * @param rowId e融同步仓单操作表ID
     * @return e融同步仓单操作表信息
     */
    @Override
    public EfWarehouseOperation selectEfWarehouseOperationById(String rowId)
    {
        return efWarehouseOperationMapper.selectEfWarehouseOperationById(rowId);
    }

    /**
     * 查询e融同步仓单操作表列表
     *
     * @param efWarehouseOperation e融同步仓单操作表信息
     * @return e融同步仓单操作表集合
     */
    @Override
    public List<EfWarehouseOperation> selectEfWarehouseOperationList(EfWarehouseOperation efWarehouseOperation)
    {
        return efWarehouseOperationMapper.selectEfWarehouseOperationList(efWarehouseOperation);
    }


    /**
     * 分页模糊查询e融同步仓单操作表列表
     * @return e融同步仓单操作表集合
     */
    @Override
    public Page selectEfWarehouseOperationListByLike(Query query)
    {
        EfWarehouseOperation efWarehouseOperation =  BeanUtil.mapToBean(query.getCondition(), EfWarehouseOperation.class,false);
        query.setRecords(efWarehouseOperationMapper.selectEfWarehouseOperationListByLike(query,efWarehouseOperation));
        return query;
    }

    /**
     * 新增e融同步仓单操作表
     *
     * @param efWarehouseOperation e融同步仓单操作表信息
     * @return 结果
     */
    @Override
    public int insertEfWarehouseOperation(EfWarehouseOperation efWarehouseOperation)
    {
        return efWarehouseOperationMapper.insertEfWarehouseOperation(efWarehouseOperation);
    }

    /**
     * 修改e融同步仓单操作表
     *
     * @param efWarehouseOperation e融同步仓单操作表信息
     * @return 结果
     */
    @Override
    public int updateEfWarehouseOperation(EfWarehouseOperation efWarehouseOperation)
    {
        return efWarehouseOperationMapper.updateEfWarehouseOperation(efWarehouseOperation);
    }


    /**
     * 删除e融同步仓单操作表
     *
     * @param rowId e融同步仓单操作表ID
     * @return 结果
     */
    public int deleteEfWarehouseOperationById(String rowId)
    {
        return efWarehouseOperationMapper.deleteEfWarehouseOperationById( rowId);
    };


    /**
     * 批量删除e融同步仓单操作表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfWarehouseOperationByIds(Integer[] rowIds)
    {
        return efWarehouseOperationMapper.deleteEfWarehouseOperationByIds( rowIds);
    }

}
