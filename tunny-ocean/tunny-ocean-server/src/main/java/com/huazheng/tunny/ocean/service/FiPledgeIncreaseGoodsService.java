package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FiPledgeIncreaseGoods;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 仓单质押补充货物表 服务接口层
 *
 * <AUTHOR>
 * @date 2022-11-11 16:42:57
 */
public interface FiPledgeIncreaseGoodsService extends IService<FiPledgeIncreaseGoods> {
    /**
     * 查询仓单质押补充货物表信息
     *
     * @param rowId 仓单质押补充货物表ID
     * @return 仓单质押补充货物表信息
     */
    public FiPledgeIncreaseGoods selectFiPledgeIncreaseGoodsById(String rowId);

    /**
     * 查询仓单质押补充货物表列表
     *
     * @param fiPledgeIncreaseGoods 仓单质押补充货物表信息
     * @return 仓单质押补充货物表集合
     */
    public List<FiPledgeIncreaseGoods> selectFiPledgeIncreaseGoodsList(FiPledgeIncreaseGoods fiPledgeIncreaseGoods);


    /**
     * 分页模糊查询仓单质押补充货物表列表
     * @return 仓单质押补充货物表集合
     */
    public Page selectFiPledgeIncreaseGoodsListByLike(Query query);



    /**
     * 新增仓单质押补充货物表
     *
     * @param fiPledgeIncreaseGoods 仓单质押补充货物表信息
     * @return 结果
     */
    public int insertFiPledgeIncreaseGoods(FiPledgeIncreaseGoods fiPledgeIncreaseGoods);

    /**
     * 修改仓单质押补充货物表
     *
     * @param fiPledgeIncreaseGoods 仓单质押补充货物表信息
     * @return 结果
     */
    public int updateFiPledgeIncreaseGoods(FiPledgeIncreaseGoods fiPledgeIncreaseGoods);

    /**
     * 删除仓单质押补充货物表
     *
     * @param rowId 仓单质押补充货物表ID
     * @return 结果
     */
    public int deleteFiPledgeIncreaseGoodsById(String rowId);

    /**
     * 批量删除仓单质押补充货物表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiPledgeIncreaseGoodsByIds(Integer[] rowIds);

}

