package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.UploadRecord;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 *  服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-08-04 10:39:25
 */
public interface UploadRecordService extends IService<UploadRecord> {
    /**
     * 查询信息
     *
     * @param rowId ID
     * @return 信息
     */
    public UploadRecord selectUploadRecordById(String rowId);

    /**
     * 查询列表
     *
     * @param uploadRecord 信息
     * @return 集合
     */
    public List<UploadRecord> selectUploadRecordList(UploadRecord uploadRecord);


    /**
     * 分页模糊查询列表
     * @return 集合
     */
    public Page selectUploadRecordListByLike(Query query);



    /**
     * 新增
     *
     * @param uploadRecord 信息
     * @return 结果
     */
    public int insertUploadRecord(UploadRecord uploadRecord);

    public int saveWaybillFile(UploadRecord uploadRecord);

    /**
     * 修改
     *
     * @param uploadRecord 信息
     * @return 结果
     */
    public int updateUploadRecord(UploadRecord uploadRecord);

    public int deleteFilesForSh(UploadRecord uploadRecord);

    /**
     * 删除
     *
     * @param rowId ID
     * @return 结果
     */
    public int deleteUploadRecordById(String rowId);

    /**
     * 批量删除
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteUploadRecordByIds(Integer[] rowIds);

    String  uploadPic(MultipartFile file);

    /**
     *资料补传
     * @param uploadRecord
     * @return
     */
    int saveBookingFile(UploadRecord uploadRecord);
}

