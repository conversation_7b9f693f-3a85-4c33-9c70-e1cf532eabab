package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.LineManagement;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 线路管理 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-08 19:21:58
 */
public interface LineManagementService extends IService<LineManagement> {
    /**
     * 查询线路管理信息
     *
     * @param rowId 线路管理ID
     * @return 线路管理信息
     */
    public LineManagement selectLineManagementById(String rowId);

    /**
     * 查询线路管理信息
     *
     * @param rowId 线路管理ID
     * @return 线路管理信息
     */
    public LineManagement selectLineDetialById(String rowId);

    /**
     * 查询线路管理列表
     *
     * @param lineManagement 线路管理信息
     * @return 线路管理集合
     */
    public List<LineManagement> selectLineManagementList(LineManagement lineManagement);


    /**
     * 分页模糊查询线路管理列表
     * @return 线路管理集合
     */
    public Page selectLineManagementListByLike(Query query);



    /**
     * 新增线路管理
     *
     * @param lineManagement 线路管理信息
     * @return 结果
     */
    public R insertLineManagement(LineManagement lineManagement);

    /**
     * 修改线路管理
     *
     * @param lineCode 线路管理信息
     * @return 结果
     */
    public R updateLineManagement(String[] lineCode);

    /**
     * 删除线路管理
     *
     * @param rowId 线路管理ID
     * @return 结果
     */
    public int deleteLineManagementById(String rowId);

    /**
     * 批量删除线路管理
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteLineManagementByIds(Integer[] rowIds);

    public List<LineManagement> selectLineManagementListByLineCode(LineManagement lineManagement);

}

