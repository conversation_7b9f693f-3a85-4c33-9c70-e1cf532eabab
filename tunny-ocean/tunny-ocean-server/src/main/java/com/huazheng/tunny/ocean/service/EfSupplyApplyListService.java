package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfSupplyApplyList;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 补充质押详情表 服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-08 15:34:38
 */
public interface EfSupplyApplyListService extends IService<EfSupplyApplyList> {
    /**
     * 查询补充质押详情表信息
     *
     * @param rowId 补充质押详情表ID
     * @return 补充质押详情表信息
     */
    public EfSupplyApplyList selectEfSupplyApplyListById(String rowId);

    /**
     * 查询补充质押详情表列表
     *
     * @param efSupplyApplyList 补充质押详情表信息
     * @return 补充质押详情表集合
     */
    public List<EfSupplyApplyList> selectEfSupplyApplyListList(EfSupplyApplyList efSupplyApplyList);


    /**
     * 分页模糊查询补充质押详情表列表
     * @return 补充质押详情表集合
     */
    public Page selectEfSupplyApplyListListByLike(Query query);



    /**
     * 新增补充质押详情表
     *
     * @param efSupplyApplyList 补充质押详情表信息
     * @return 结果
     */
    public int insertEfSupplyApplyList(EfSupplyApplyList efSupplyApplyList);

    /**
     * 修改补充质押详情表
     *
     * @param efSupplyApplyList 补充质押详情表信息
     * @return 结果
     */
    public int updateEfSupplyApplyList(EfSupplyApplyList efSupplyApplyList);

    /**
     * 删除补充质押详情表
     *
     * @param rowId 补充质押详情表ID
     * @return 结果
     */
    public int deleteEfSupplyApplyListById(String rowId);

    /**
     * 批量删除补充质押详情表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfSupplyApplyListByIds(Integer[] rowIds);

}

