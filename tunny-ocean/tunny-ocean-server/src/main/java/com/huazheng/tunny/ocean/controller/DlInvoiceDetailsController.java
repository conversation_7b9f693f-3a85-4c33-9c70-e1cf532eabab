package com.huazheng.tunny.ocean.controller;

import cn.hutool.core.collection.CollUtil;
import com.huazheng.tunny.ocean.api.entity.CustomerInfo;
import com.huazheng.tunny.ocean.api.entity.DlInvoiceDetails;
import com.huazheng.tunny.ocean.api.vo.DlInvoiceDetailsVO;
import com.huazheng.tunny.ocean.service.CustomerInfoService;
import com.huazheng.tunny.ocean.service.DlInvoiceDetailsService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 多联开票明细
 *
 * <AUTHOR>
 * @date 2023-09-01 15:09:43
 */
@Slf4j
@RestController
@RequestMapping("/dlinvoicedetails")
public class DlInvoiceDetailsController {

    @Autowired
    private DlInvoiceDetailsService dlInvoiceDetailsService;
    @Autowired
    private CustomerInfoService customerInfoService;
    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return dlInvoiceDetailsService.selectDlInvoiceDetailsListByLike(new Query<>(params));
    }

    /**
     * 信息
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Integer id) {
        DlInvoiceDetails dlInvoiceDetails =dlInvoiceDetailsService.selectById(id);
        return new R<>(dlInvoiceDetails);
    }

    /**
     * 保存
     * @param dlInvoiceDetails
     * @return R
     */
    @PostMapping
    public R save(@RequestBody DlInvoiceDetails dlInvoiceDetails) {
        dlInvoiceDetailsService.insert(dlInvoiceDetails);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param dlInvoiceDetails
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody DlInvoiceDetails dlInvoiceDetails) {
        dlInvoiceDetailsService.updateById(dlInvoiceDetails);
        return new R<>(Boolean.TRUE);
    }

    

    /**
     * 删除
     * @param id
     * @return R
     */
    @GetMapping("/del/{id}")
    public R delete(@PathVariable  Integer id) {
        dlInvoiceDetailsService.deleteById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<Integer> ids) {
        dlInvoiceDetailsService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<DlInvoiceDetails> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<DlInvoiceDetails> list = dlInvoiceDetailsService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), DlInvoiceDetails.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }

    /**
     * 保存导入列表
     * @param list
     * @return R
     */
    @PostMapping("/saveImported")
    public R saveImported(@RequestBody List<DlInvoiceDetailsVO> list) {
        return dlInvoiceDetailsService.saveImported(list);
    }

    /**
     * 导入EXCEL
     *
     * @return
     */
    @PostMapping("/imported")
    @Transactional(rollbackFor = Exception.class)
    public R imported(@RequestParam("file") MultipartFile file) {

        Workbook workbook = null;
        try {
            InputStream is = file.getInputStream();
            //利用WorkbookFactory获取Sheet，无需判断版本
            workbook = WorkbookFactory.create(is);
        } catch (Exception e) {
            e.printStackTrace();
        }

        //返回list
        List<DlInvoiceDetailsVO> list = new ArrayList<>();

        CustomerInfo sel = new CustomerInfo();
        sel.setDeleteFlag("N");
        final List<CustomerInfo> customerInfos = customerInfoService.selectCustomerInfoList(sel);

        final int numberOfSheets = workbook.getNumberOfSheets();
        for (int x = 0;x < numberOfSheets;x++){
            //导入第一张sheet页
            Sheet sheet = workbook.getSheetAt(x);

            String remark ="";
            Row row0 = sheet.getRow(0);
            if(row0!=null){
                if (row0.getCell(0) != null) {
                    row0.getCell(0).setCellType(CellType.STRING);
                    remark = row0.getCell(0).getStringCellValue();

                }else{
                    continue;
                }
            }else{
                continue;
            }

            //构建返回数据
            List<DlInvoiceDetails> detailList = new ArrayList<>();

            //省级班列号
            String provinceShiftNo;
            Row row2 = sheet.getRow(2);
            if (row2.getCell(0) != null) {
                row2.getCell(0).setCellType(CellType.STRING);
                provinceShiftNo = row2.getCell(0).getStringCellValue();
            }else{
                return new R(500, Boolean.FALSE, null, "sheet页"+(x+1)+"未获取到省级班列号，无法导入！");
            }

            //获取行数
            int lastRowNum = sheet.getLastRowNum();
            for (int i = 3; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                boolean blankFlag = isRowEmpty(row);
                if (blankFlag) {
                    continue;
                }

                if (row.getCell(0) != null) {
                    row.getCell(0).setCellType(CellType.STRING);
                    final String num = row.getCell(0).getStringCellValue();
                    System.out.println(num);
                    if("小计".equals(num)){
                        break;
                    }
                }else{
                    break;
                }

                //解析导入数据
                final DlInvoiceDetails dlInvoiceDetails = getDlInvoiceDetails(remark, provinceShiftNo, row, customerInfos);
                if(StrUtil.isEmpty(dlInvoiceDetails.getContainerNumber())){
                    return new R(500, Boolean.FALSE, null, "sheet页"+(x+1)+" 序号"+(i-2)+"未获取到箱号，无法导入！");
                }
                if(StrUtil.isEmpty(dlInvoiceDetails.getShippingTime())){
                    return new R(500, Boolean.FALSE, null, "sheet页"+(x+1)+" 序号"+(i-2)+"日期格式异常，无法导入！");
                }
                //先重置状态为 2查无此信息
                dlInvoiceDetails.setStatus("2");
                detailList.add(dlInvoiceDetails);
            }

            //导入数据合并原有数据，并判断数据状态
            final DlInvoiceDetailsVO vo = getVo(provinceShiftNo, detailList);
            list.add(vo);
        }

        return new R(200, Boolean.TRUE, list, "导入完成！");
    }

    /**
     * 解析导入数据
     *
     * @return DlInvoiceDetails
     */
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    public DlInvoiceDetails getDlInvoiceDetails (String remark,String provinceShiftNo,Row row,List<CustomerInfo> customerInfos){

        DlInvoiceDetails dlInvoiceDetails = new DlInvoiceDetails();
        dlInvoiceDetails.setRemarks(remark);
        dlInvoiceDetails.setProvinceShiftNo(provinceShiftNo);

        if (row.getCell(1) != null) {
            row.getCell(1).setCellType(CellType.STRING);
            final String shippingTime = row.getCell(1).getStringCellValue();
            try {
                // 将数值解析为日期对象
                double timeValue = Double.parseDouble(shippingTime);
                java.util.Date date = DateUtil.getJavaDate(timeValue);

                // 格式化日期为指定格式
                String formattedDate = dateFormat.format(date);
                dlInvoiceDetails.setShippingTime(formattedDate);
            } catch (Exception e) {
                // 当无法解析为数值时进行异常处理
                return dlInvoiceDetails;
            }
        }
        if (row.getCell(2) != null) {
            row.getCell(2).setCellType(CellType.STRING);
            final String endStationName = row.getCell(2).getStringCellValue();
            dlInvoiceDetails.setEndStationName(endStationName);
        }
        if (row.getCell(3) != null) {
            row.getCell(3).setCellType(CellType.STRING);
            final String portStation = row.getCell(3).getStringCellValue();
            dlInvoiceDetails.setPortStation(portStation);
        }
        if (row.getCell(4) != null) {
            row.getCell(4).setCellType(CellType.STRING);
            final String customerName = row.getCell(4).getStringCellValue();
            dlInvoiceDetails.setCustomerName(customerName);
            if(CollUtil.isNotEmpty(customerInfos)){
                for (CustomerInfo customerInfo:customerInfos
                ) {
                    if(customerName.equals(customerInfo.getCompanyName())){
                        dlInvoiceDetails.setCustomerNo(customerInfo.getCustomerCode());
                    }
                }
            }
        }
        if (row.getCell(5) != null) {
            row.getCell(5).setCellType(CellType.STRING);
            final String containerNumber = row.getCell(5).getStringCellValue();
            dlInvoiceDetails.setContainerNumber(containerNumber);
        }else{
            return dlInvoiceDetails;
        }
        if (row.getCell(6) != null) {
            row.getCell(6).setCellType(CellType.STRING);
            final String containerType = row.getCell(6).getStringCellValue();
            dlInvoiceDetails.setContainerType(containerType);
        }
        if (row.getCell(7) != null) {
            row.getCell(7).setCellType(CellType.STRING);
            final String containerOwner = row.getCell(7).getStringCellValue();
            if("自备箱".equals(containerOwner)){
                dlInvoiceDetails.setContainerOwner("0");
            }else if("中铁箱".equals(containerOwner)){
                dlInvoiceDetails.setContainerOwner("1");
            }else{
                dlInvoiceDetails.setContainerOwner(containerOwner);
            }
        }
        if (row.getCell(8) != null) {
            row.getCell(8).setCellType(CellType.NUMERIC);
            final double numericCellValue = row.getCell(8).getNumericCellValue();
            if(BigDecimal.valueOf(numericCellValue).compareTo(BigDecimal.ZERO)>=0){
                dlInvoiceDetails.setRrDomesticFreight("1");
            }
        }
        if (row.getCell(9) != null) {
            row.getCell(9).setCellType(CellType.NUMERIC);
            final double numericCellValue = row.getCell(9).getNumericCellValue();
            if(BigDecimal.valueOf(numericCellValue).compareTo(BigDecimal.ZERO)>=0){
                dlInvoiceDetails.setRrOverseasFreightCny("1");
            }
        }

        return dlInvoiceDetails;
    }

    /**
     * 导入数据合并原有数据，并判断数据状态
     *
     * @return DlInvoiceDetailsVO
     */
    public DlInvoiceDetailsVO getVo(String provinceShiftNo,List<DlInvoiceDetails> detailList){
        //构建返回数据
        DlInvoiceDetailsVO vo = new DlInvoiceDetailsVO();
        vo.setProvinceShiftNo(provinceShiftNo);
        List<DlInvoiceDetails> addList = new ArrayList<>();
        //对比该省级班列号下原有数据，进行合并
        DlInvoiceDetails sel2 = new DlInvoiceDetails();
        sel2.setProvinceShiftNo(provinceShiftNo);
        sel2.setDeleteFlag("N");
        final List<DlInvoiceDetails> oldList = dlInvoiceDetailsService.selectDlInvoiceDetailsList(sel2);

        for (DlInvoiceDetails newObj:detailList
        ) {
            //判断与原表内的箱号数据是否不同
            Boolean flag = Boolean.TRUE;
            for (DlInvoiceDetails oldObj:oldList
            ) {
                if(CollUtil.isNotEmpty(oldList)){
                    if(StrUtil.isNotEmpty(oldObj.getContainerNumber())&&newObj.getContainerNumber().equals(oldObj.getContainerNumber())){
                        if(StrUtil.isNotEmpty(newObj.getRrOverseasFreightCny())){
                            oldObj.setRrOverseasFreightCny(newObj.getRrOverseasFreightCny());
                        }
                        if(StrUtil.isNotEmpty(newObj.getRrDomesticFreight())){
                            oldObj.setRrDomesticFreight(newObj.getRrDomesticFreight());
                        }
                        flag = Boolean.FALSE;
                        break;
                    }
                }
            }
            if(flag){
                //先重置状态为 2查无此信息
                newObj.setStatus("2");
                addList.add(newObj);
            }
        }
        //将不同的数据与原表内数据合并
        oldList.addAll(addList);

        List<DlInvoiceDetails> billAddList = new ArrayList<>();
        //查询该省级班列号账单中的箱数据
        final List<DlInvoiceDetails> billContainerList = dlInvoiceDetailsService.getBillContainerList(provinceShiftNo);

        //如果存在相同箱号则为1已核对，否则为0未核对
        Boolean flag = Boolean.TRUE;
        if(CollUtil.isNotEmpty(billContainerList)){
            for (DlInvoiceDetails bill:billContainerList
            ) {
                for (DlInvoiceDetails oldObj:oldList
                ) {
                    if(StrUtil.isNotEmpty(bill.getContainerNumber())&&oldObj.getContainerNumber().equals(bill.getContainerNumber())){
                        oldObj.setStatus("1");
                        flag = Boolean.FALSE;
                        break;
                    }
                }
                if(flag){
                    bill.setStatus("0");
                    billAddList.add(bill);
                }
            }
        }
        oldList.addAll(billAddList);
        if(CollUtil.isNotEmpty(oldList)){
            for (DlInvoiceDetails dlInvoiceDetails:oldList
             ) {
                dlInvoiceDetails.setUuid(UUID.randomUUID().toString());
                dlInvoiceDetails.setProvinceShiftNo(provinceShiftNo);
            }
        }

        vo.setDetailList(oldList);
        return vo;
    }

    /**
     * 功能描述: 判断是否为空行
     *
     * @param row 行对象
     * @return boolean
     * <AUTHOR> zheng
     * @date 2021/10/13
     */
    private boolean isRowEmpty(Row row) {
        if (row != null) {
            for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
                Cell cell = row.getCell(c);
                if (cell != null) {
                    cell.setCellType(CellType.STRING);
                }
                if (cell != null && !"".equals(cell.getStringCellValue())) {
                    return false;
                }
            }
        }
        return true;
    }

}
