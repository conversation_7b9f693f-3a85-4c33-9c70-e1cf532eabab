package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseGoods;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * e融同步仓单货物表 服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-04 10:08:24
 */
public interface EfWarehouseGoodsService extends IService<EfWarehouseGoods> {
    /**
     * 查询e融同步仓单货物表信息
     *
     * @param rowId e融同步仓单货物表ID
     * @return e融同步仓单货物表信息
     */
    public EfWarehouseGoods selectEfWarehouseGoodsById(String rowId);

    /**
     * 查询e融同步仓单货物表列表
     *
     * @param efWarehouseGoods e融同步仓单货物表信息
     * @return e融同步仓单货物表集合
     */
    public List<EfWarehouseGoods> selectEfWarehouseGoodsList(EfWarehouseGoods efWarehouseGoods);


    /**
     * 分页模糊查询e融同步仓单货物表列表
     * @return e融同步仓单货物表集合
     */
    public Page selectEfWarehouseGoodsListByLike(Query query);

    public Page pageForApply(Query query);



    /**
     * 新增e融同步仓单货物表
     *
     * @param efWarehouseGoods e融同步仓单货物表信息
     * @return 结果
     */
    public int insertEfWarehouseGoods(EfWarehouseGoods efWarehouseGoods);

    /**
     * 修改e融同步仓单货物表
     *
     * @param efWarehouseGoods e融同步仓单货物表信息
     * @return 结果
     */
    public int updateEfWarehouseGoods(EfWarehouseGoods efWarehouseGoods);

    /**
     * 删除e融同步仓单货物表
     *
     * @param rowId e融同步仓单货物表ID
     * @return 结果
     */
    public int deleteEfWarehouseGoodsById(String rowId);

    /**
     * 批量删除e融同步仓单货物表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfWarehouseGoodsByIds(Integer[] rowIds);

}

