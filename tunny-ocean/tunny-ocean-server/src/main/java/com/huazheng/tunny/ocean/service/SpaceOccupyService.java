package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.SpaceOccupy;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 舱位占用表(用来统计还有多少舱位可以使用) 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-08-11 11:18:32
 */
public interface SpaceOccupyService extends IService<SpaceOccupy> {
    /**
     * 查询舱位占用表(用来统计还有多少舱位可以使用)信息
     *
     * @param rowId 舱位占用表(用来统计还有多少舱位可以使用)ID
     * @return 舱位占用表(用来统计还有多少舱位可以使用)信息
     */
    public SpaceOccupy selectSpaceOccupyById(String rowId);

    /**
     * 查询舱位占用表(用来统计还有多少舱位可以使用)列表
     *
     * @param spaceOccupy 舱位占用表(用来统计还有多少舱位可以使用)信息
     * @return 舱位占用表(用来统计还有多少舱位可以使用)集合
     */
    public List<SpaceOccupy> selectSpaceOccupyList(SpaceOccupy spaceOccupy);


    /**
     * 分页模糊查询舱位占用表(用来统计还有多少舱位可以使用)列表
     * @return 舱位占用表(用来统计还有多少舱位可以使用)集合
     */
    public Page selectSpaceOccupyListByLike(Query query);

    /**
     * 依照申请单号，查出换箱、撤箱后，当前申请单下占用仓位数量，更新仓位占用数据
     */
    public void updateSpaceNums(String appNo);

    /**
     * 新增舱位占用表(用来统计还有多少舱位可以使用)
     *
     * @param spaceOccupy 舱位占用表(用来统计还有多少舱位可以使用)信息
     * @return 结果
     */
    public int insertSpaceOccupy(SpaceOccupy spaceOccupy);

    /**
     * 修改舱位占用表(用来统计还有多少舱位可以使用)
     *
     * @param spaceOccupy 舱位占用表(用来统计还有多少舱位可以使用)信息
     * @return 结果
     */
    public int updateSpaceOccupy(SpaceOccupy spaceOccupy);

    /**
     * 删除舱位占用表(用来统计还有多少舱位可以使用)
     *
     * @param rowId 舱位占用表(用来统计还有多少舱位可以使用)ID
     * @return 结果
     */
    public int deleteSpaceOccupyById(String rowId);

    /**
     * 批量删除舱位占用表(用来统计还有多少舱位可以使用)
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteSpaceOccupyByIds(Integer[] rowIds);

}

