package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseMargin;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 保证金信息 服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-06 09:15:22
 */
public interface EfWarehouseMarginService extends IService<EfWarehouseMargin> {
    /**
     * 查询保证金信息信息
     *
     * @param rowId 保证金信息ID
     * @return 保证金信息信息
     */
    public EfWarehouseMargin selectEfWarehouseMarginById(String rowId);

    /**
     * 查询保证金信息列表
     *
     * @param efWarehouseMargin 保证金信息信息
     * @return 保证金信息集合
     */
    public List<EfWarehouseMargin> selectEfWarehouseMarginList(EfWarehouseMargin efWarehouseMargin);


    /**
     * 分页模糊查询保证金信息列表
     * @return 保证金信息集合
     */
    public Page selectEfWarehouseMarginListByLike(Query query);



    /**
     * 新增保证金信息
     *
     * @param efWarehouseMargin 保证金信息信息
     * @return 结果
     */
    public int insertEfWarehouseMargin(EfWarehouseMargin efWarehouseMargin);

    /**
     * 修改保证金信息
     *
     * @param efWarehouseMargin 保证金信息信息
     * @return 结果
     */
    public int updateEfWarehouseMargin(EfWarehouseMargin efWarehouseMargin);

    /**
     * 删除保证金信息
     *
     * @param rowId 保证金信息ID
     * @return 结果
     */
    public int deleteEfWarehouseMarginById(String rowId);

    /**
     * 批量删除保证金信息
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfWarehouseMarginByIds(Integer[] rowIds);

    public String syncMargin(EfWarehouseMargin efWarehouseMargin);
}

