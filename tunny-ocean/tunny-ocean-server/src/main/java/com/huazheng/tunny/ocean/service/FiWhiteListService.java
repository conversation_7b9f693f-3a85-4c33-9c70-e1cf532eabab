package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.CustomerInfo;
import com.huazheng.tunny.ocean.api.entity.FiWhiteList;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 白名单注册 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-03-16 15:13:43
 */
public interface FiWhiteListService extends IService<FiWhiteList> {
    /**
     * 查询白名单注册信息
     *
     * @param rowId 白名单注册ID
     * @return 白名单注册信息
     */
    public FiWhiteList selectFiWhiteListById(String rowId);

    public FiWhiteList selectFiWhiteListByEntSocialCode(String rowId);

    /**
     * 查询白名单注册列表
     *
     * @param fiWhiteList 白名单注册信息
     * @return 白名单注册集合
     */
    public List<FiWhiteList> selectFiWhiteListList(FiWhiteList fiWhiteList);


    /**
     * 分页模糊查询白名单注册列表
     * @return 白名单注册集合
     */
    public Page selectFiWhiteListListByLike(Query query);

    /**
     * 分页模糊查询白名单注册列表
     * @return 白名单注册集合
     */
    public Page selectFiWhiteListListForCity(Query query);

    /**
     * 分页模糊查询白名单注册列表
     * @return 白名单注册集合
     */
    public Page selectFiWhiteListListForProvince(Query query);



    /**
     * 新增白名单注册
     *
     * @param fiWhiteList 白名单注册信息
     * @return 结果
     */
    public int insertFiWhiteList(FiWhiteList fiWhiteList);

    /**
     * 修改白名单注册
     *
     * @param fiWhiteList 白名单注册信息
     * @return 结果
     */
    public int updateFiWhiteList(FiWhiteList fiWhiteList);

    public int updateFiWhiteListByEntSocialCode(FiWhiteList fiWhiteList);

    public int outList(FiWhiteList fiWhiteList);

    /**
     * 删除白名单注册
     *
     * @param rowId 白名单注册ID
     * @return 结果
     */
    public int deleteFiWhiteListById(Integer rowId);

    /**
     * 批量删除白名单注册
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiWhiteListByIds(Integer[] rowIds);

    public R syncRecommendedEnt(String entCode, String operationType);

    public R syncEntInfo(CustomerInfo cus);

    public R commit(CustomerInfo cus);
}

