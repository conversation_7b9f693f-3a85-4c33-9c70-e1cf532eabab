package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseList;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 仓单融资仓单表 服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-04 10:08:27
 */
public interface EfWarehouseListService extends IService<EfWarehouseList> {
    /**
     * 查询仓单融资仓单表信息
     *
     * @param rowId 仓单融资仓单表ID
     * @return 仓单融资仓单表信息
     */
    public EfWarehouseList selectEfWarehouseListById(String rowId);

    /**
     * 查询仓单融资仓单表列表
     *
     * @param efWarehouseList 仓单融资仓单表信息
     * @return 仓单融资仓单表集合
     */
    public List<EfWarehouseList> selectEfWarehouseListList(EfWarehouseList efWarehouseList);


    /**
     * 分页模糊查询仓单融资仓单表列表
     * @return 仓单融资仓单表集合
     */
    public Page selectEfWarehouseListListByLike(Query query);



    /**
     * 新增仓单融资仓单表
     *
     * @param efWarehouseList 仓单融资仓单表信息
     * @return 结果
     */
    public int insertEfWarehouseList(EfWarehouseList efWarehouseList);

    /**
     * 修改仓单融资仓单表
     *
     * @param efWarehouseList 仓单融资仓单表信息
     * @return 结果
     */
    public int updateEfWarehouseList(EfWarehouseList efWarehouseList);

    public String syncWarehouseInfo(EfWarehouseList efWarehouseList);

    /**
     * 删除仓单融资仓单表
     *
     * @param rowId 仓单融资仓单表ID
     * @return 结果
     */
    public int deleteEfWarehouseListById(String rowId);

    /**
     * 批量删除仓单融资仓单表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfWarehouseListByIds(Integer[] rowIds);

    String updateMonitorVideo(EfWarehouseList efWarehouseList);
}

