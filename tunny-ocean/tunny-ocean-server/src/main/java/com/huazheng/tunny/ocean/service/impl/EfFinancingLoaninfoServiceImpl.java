package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.EfFinancingLoaninfoMapper;
import com.huazheng.tunny.ocean.api.entity.EfFinancingLoaninfo;
import com.huazheng.tunny.ocean.service.EfFinancingLoaninfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("efFinancingLoaninfoService")
public class EfFinancingLoaninfoServiceImpl extends ServiceImpl<EfFinancingLoaninfoMapper, EfFinancingLoaninfo> implements EfFinancingLoaninfoService {

    @Autowired
    private EfFinancingLoaninfoMapper efFinancingLoaninfoMapper;

    public EfFinancingLoaninfoMapper getEfFinancingLoaninfoMapper() {
        return efFinancingLoaninfoMapper;
    }

    public void setEfFinancingLoaninfoMapper(EfFinancingLoaninfoMapper efFinancingLoaninfoMapper) {
        this.efFinancingLoaninfoMapper = efFinancingLoaninfoMapper;
    }

    /**
     * 查询仓单质押银行放款信息信息
     *
     * @param rowId 仓单质押银行放款信息ID
     * @return 仓单质押银行放款信息信息
     */
    @Override
    public EfFinancingLoaninfo selectEfFinancingLoaninfoById(String rowId)
    {
        return efFinancingLoaninfoMapper.selectEfFinancingLoaninfoById(rowId);
    }

    /**
     * 查询仓单质押银行放款信息列表
     *
     * @param efFinancingLoaninfo 仓单质押银行放款信息信息
     * @return 仓单质押银行放款信息集合
     */
    @Override
    public List<EfFinancingLoaninfo> selectEfFinancingLoaninfoList(EfFinancingLoaninfo efFinancingLoaninfo)
    {
        return efFinancingLoaninfoMapper.selectEfFinancingLoaninfoList(efFinancingLoaninfo);
    }


    /**
     * 分页模糊查询仓单质押银行放款信息列表
     * @return 仓单质押银行放款信息集合
     */
    @Override
    public Page selectEfFinancingLoaninfoListByLike(Query query)
    {
        EfFinancingLoaninfo efFinancingLoaninfo =  BeanUtil.mapToBean(query.getCondition(), EfFinancingLoaninfo.class,false);
        query.setRecords(efFinancingLoaninfoMapper.selectEfFinancingLoaninfoListByLike(query,efFinancingLoaninfo));
        return query;
    }

    /**
     * 新增仓单质押银行放款信息
     *
     * @param efFinancingLoaninfo 仓单质押银行放款信息信息
     * @return 结果
     */
    @Override
    public int insertEfFinancingLoaninfo(EfFinancingLoaninfo efFinancingLoaninfo)
    {
        return efFinancingLoaninfoMapper.insertEfFinancingLoaninfo(efFinancingLoaninfo);
    }

    /**
     * 修改仓单质押银行放款信息
     *
     * @param efFinancingLoaninfo 仓单质押银行放款信息信息
     * @return 结果
     */
    @Override
    public int updateEfFinancingLoaninfo(EfFinancingLoaninfo efFinancingLoaninfo)
    {
        return efFinancingLoaninfoMapper.updateEfFinancingLoaninfo(efFinancingLoaninfo);
    }


    /**
     * 删除仓单质押银行放款信息
     *
     * @param rowId 仓单质押银行放款信息ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingLoaninfoById(String rowId)
    {
        return efFinancingLoaninfoMapper.deleteEfFinancingLoaninfoById( rowId);
    };


    /**
     * 批量删除仓单质押银行放款信息对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingLoaninfoByIds(Integer[] rowIds)
    {
        return efFinancingLoaninfoMapper.deleteEfFinancingLoaninfoByIds( rowIds);
    }

}
