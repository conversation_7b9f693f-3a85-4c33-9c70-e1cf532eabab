package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.SysUserRoleMapper;
import com.huazheng.tunny.ocean.api.entity.SysUserRole;
import com.huazheng.tunny.ocean.service.SysUserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("sysUserRoleService")
public class SysUserRoleServiceImpl extends ServiceImpl<SysUserRoleMapper, SysUserRole> implements SysUserRoleService {

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    public SysUserRoleMapper getSysUserRoleMapper() {
        return sysUserRoleMapper;
    }

    public void setSysUserRoleMapper(SysUserRoleMapper sysUserRoleMapper) {
        this.sysUserRoleMapper = sysUserRoleMapper;
    }

    @Value("${db.database}")
    private String database;

    /**
     * 查询用户角色表信息
     *
     * @param userId 用户角色表ID
     * @return 用户角色表信息
     */
    @Override
    public SysUserRole selectSysUserRoleById(Integer userId)
    {
        SysUserRole sysUserRole=new SysUserRole();
        sysUserRole.setDatabase(database);
        sysUserRole.setUserId(userId);
        return sysUserRoleMapper.selectSysUserRoleById(sysUserRole);
    }

    /**
     * 查询用户角色表列表
     *
     * @param sysUserRole 用户角色表信息
     * @return 用户角色表集合
     */
    @Override
    public List<SysUserRole> selectSysUserRoleList(SysUserRole sysUserRole)
    {
        sysUserRole.setDatabase(database);
        return sysUserRoleMapper.selectSysUserRoleList(sysUserRole);
    }


    /**
     * 分页模糊查询用户角色表列表
     * @return 用户角色表集合
     */
    @Override
    public Page selectSysUserRoleListByLike(Query query)
    {
        SysUserRole sysUserRole =  BeanUtil.mapToBean(query.getCondition(), SysUserRole.class,false);
        sysUserRole.setDatabase(database);
        query.setRecords(sysUserRoleMapper.selectSysUserRoleListByLike(query,sysUserRole));
        return query;
    }

    /**
     * 新增用户角色表
     *
     * @param sysUserRole 用户角色表信息
     * @return 结果
     */
    @Override
    public int insertSysUserRole(SysUserRole sysUserRole)
    {
        sysUserRole.setDatabase(database);
        return sysUserRoleMapper.insertSysUserRole(sysUserRole);
    }

    /**
     * 修改用户角色表
     *
     * @param sysUserRole 用户角色表信息
     * @return 结果
     */
    @Override
    public int updateSysUserRole(SysUserRole sysUserRole)
    {
        sysUserRole.setDatabase(database);
        return sysUserRoleMapper.updateSysUserRole(sysUserRole);
    }


    /**
     * 删除用户角色表
     *
     * @param userId 用户角色表ID
     * @return 结果
     */
    public int deleteSysUserRoleById(Integer userId)
    {
        SysUserRole sysUserRole =new SysUserRole();
        sysUserRole.setUserId(userId);
        sysUserRole.setDatabase(database);
        return sysUserRoleMapper.deleteSysUserRoleById(sysUserRole);
    };


    /**
     * 批量删除用户角色表对象
     *
     * @param userIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSysUserRoleByIds(Integer[] userIds)
    {
        SysUserRole sysUserRole =new SysUserRole();
        sysUserRole.setUserIds(userIds);
        sysUserRole.setDatabase(database);
        return sysUserRoleMapper.deleteSysUserRoleByIds(sysUserRole);
    }

}
