package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.DaysBookingplanDetail;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 旬/周计划申请子表(订舱客户提交到市平台) 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-07 11:47:58
 */
public interface DaysBookingplanDetailService extends IService<DaysBookingplanDetail> {
    /**
     * 查询旬/周计划申请子表(订舱客户提交到市平台)信息
     *
     * @param rowId 旬/周计划申请子表(订舱客户提交到市平台)ID
     * @return 旬/周计划申请子表(订舱客户提交到市平台)信息
     */
    public DaysBookingplanDetail selectDaysBookingplanDetailById(String rowId);

    /**
     * 查询旬/周计划申请子表(订舱客户提交到市平台)列表
     *
     * @param daysBookingplanDetail 旬/周计划申请子表(订舱客户提交到市平台)信息
     * @return 旬/周计划申请子表(订舱客户提交到市平台)集合
     */
    public List<DaysBookingplanDetail> selectDaysBookingplanDetailList(DaysBookingplanDetail daysBookingplanDetail);


    /**
     * 分页模糊查询旬/周计划申请子表(订舱客户提交到市平台)列表
     * @return 旬/周计划申请子表(订舱客户提交到市平台)集合
     */
    public Page selectDaysBookingplanDetailListByLike(Query query);



    /**
     * 新增旬/周计划申请子表(订舱客户提交到市平台)
     *
     * @param daysBookingplanDetail 旬/周计划申请子表(订舱客户提交到市平台)信息
     * @return 结果
     */
    public int insertDaysBookingplanDetail(DaysBookingplanDetail daysBookingplanDetail);

    /**
     * 修改旬/周计划申请子表(订舱客户提交到市平台)
     *
     * @param daysBookingplanDetail 旬/周计划申请子表(订舱客户提交到市平台)信息
     * @return 结果
     */
    public int updateDaysBookingplanDetail(DaysBookingplanDetail daysBookingplanDetail);

    /**
     * 删除旬/周计划申请子表(订舱客户提交到市平台)
     *
     * @param rowId 旬/周计划申请子表(订舱客户提交到市平台)ID
     * @return 结果
     */
    public int deleteDaysBookingplanDetailById(String rowId);

    /**
     * 批量删除旬/周计划申请子表(订舱客户提交到市平台)
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDaysBookingplanDetailByIds(Integer[] rowIds);

}

