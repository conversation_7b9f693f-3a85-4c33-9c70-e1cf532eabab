package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.mapper.MonthPlanHeaderCityToProMapper;
import com.huazheng.tunny.ocean.api.entity.MonthPlanHeaderCityToPro;
import com.huazheng.tunny.ocean.service.MonthPlanHeaderCityToProService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import javax.swing.plaf.PanelUI;
import java.util.List;

@Service("monthPlanHeaderCityToProService")
public class MonthPlanHeaderCityToProServiceImpl extends ServiceImpl<MonthPlanHeaderCityToProMapper, MonthPlanHeaderCityToPro> implements MonthPlanHeaderCityToProService {

    @Autowired
    private MonthPlanHeaderCityToProMapper monthPlanHeaderCityToProMapper;

    public MonthPlanHeaderCityToProMapper getMonthPlanHeaderCityToProMapper() {
        return monthPlanHeaderCityToProMapper;
    }

    public void setMonthPlanHeaderCityToProMapper(MonthPlanHeaderCityToProMapper monthPlanHeaderCityToProMapper) {
        this.monthPlanHeaderCityToProMapper = monthPlanHeaderCityToProMapper;
    }

    @Value("${path.CY_MANAGE_ADMIN}")
    private String cyManageAdmin;
    @Value("${path.CY_BIZ_ADMIN}")
    private String cyBizAdmin;
    @Value("${path.PR_MANAGE_ADMIN}")
    private String prManagedmin;
    @Value("${path.PR_BIZ_ADMIN}")
    private String prBizAdmin;

    /**
     * 查询月计划申请表(市平台提交到省平台)信息
     *
     * @param rowId 月计划申请表(市平台提交到省平台)ID
     * @return 月计划申请表(市平台提交到省平台)信息
     */
    @Override
    public MonthPlanHeaderCityToPro selectMonthPlanHeaderCityToProById(String rowId)
    {
        return monthPlanHeaderCityToProMapper.selectMonthPlanHeaderCityToProById(rowId);
    }

    /**
     * 查询月计划申请表(市平台提交到省平台)列表
     *
     * @param monthPlanHeaderCityToPro 月计划申请表(市平台提交到省平台)信息
     * @return 月计划申请表(市平台提交到省平台)集合
     */
    @Override
    public List<MonthPlanHeaderCityToPro> selectMonthPlanHeaderCityToProList(MonthPlanHeaderCityToPro monthPlanHeaderCityToPro)
    {
        return monthPlanHeaderCityToProMapper.selectMonthPlanHeaderCityToProList(monthPlanHeaderCityToPro);
    }


    /**
     * 分页模糊查询月计划申请表(市平台提交到省平台)列表
     * @return 月计划申请表(市平台提交到省平台)集合
     */
    @Override
    public Page selectMonthPlanHeaderCityToProListByLike(Query query)
    {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.add_time");
            query.setAsc(Boolean.FALSE);
        }
        MonthPlanHeaderCityToPro monthPlanHeaderCityToPro =  BeanUtil.mapToBean(query.getCondition(), MonthPlanHeaderCityToPro.class,false);
        List<String> roles = SecurityUtils.getRoles();
        if(roles!=null && roles.size()>0){
            for (String role: roles
            ) {
                if(cyManageAdmin.equals(role) || cyBizAdmin.equals(role)){
                    monthPlanHeaderCityToPro.setQxType("cy");
                }
                if(prManagedmin.equals(role) || prBizAdmin.equals(role)){
                    monthPlanHeaderCityToPro.setQxType("pr");
                }
            }
        }
        List<MonthPlanHeaderCityToPro> monthPlanHeaderCityToPros = monthPlanHeaderCityToProMapper.selectMonthPlanHeaderCityToProListByLike(query, monthPlanHeaderCityToPro);
        query.setRecords(monthPlanHeaderCityToPros);
        return query;
    }
    @Override
    public Integer selectMonthPlanHeaderCityToProListByLikeCount(MonthPlanHeaderCityToPro monthPlanHeaderCityToPro) {
        List<String> roles = SecurityUtils.getRoles();
        if(roles!=null && roles.size()>0){
            for (String role: roles
            ) {
                if(cyManageAdmin.equals(role) || cyBizAdmin.equals(role)){
                    monthPlanHeaderCityToPro.setQxType("cy");
                }
                if(prManagedmin.equals(role) || prBizAdmin.equals(role)){
                    monthPlanHeaderCityToPro.setQxType("pr");
                }
            }
        }
        return monthPlanHeaderCityToProMapper.selectAllNo(monthPlanHeaderCityToPro);
    }

    @Override
    public Page selectMonthPlanHeaderCityToProListByLike1(Query query)
    {
        MonthPlanHeaderCityToPro monthPlanHeaderCityToPro =  BeanUtil.mapToBean(query.getCondition(), MonthPlanHeaderCityToPro.class,false);
        List<MonthPlanHeaderCityToPro> monthPlanHeaderCityToPros = monthPlanHeaderCityToProMapper.selectMonthPlanHeaderCityToProListByLike1(query, monthPlanHeaderCityToPro);
        query.setRecords(monthPlanHeaderCityToPros);
        query.setTotal(monthPlanHeaderCityToProMapper.selectAllNo1(monthPlanHeaderCityToPro));
        return query;
    }

    /**
     * 新增月计划申请表(市平台提交到省平台)
     *
     * @param monthPlanHeaderCityToPro 月计划申请表(市平台提交到省平台)信息
     * @return 结果
     */
    @Override
    public int insertMonthPlanHeaderCityToPro(MonthPlanHeaderCityToPro monthPlanHeaderCityToPro)
    {
        return monthPlanHeaderCityToProMapper.insertMonthPlanHeaderCityToPro(monthPlanHeaderCityToPro);
    }

    /**
     * 修改月计划申请表(市平台提交到省平台)
     *
     * @param monthPlanHeaderCityToPro 月计划申请表(市平台提交到省平台)信息
     * @return 结果
     */
    @Override
    public int updateMonthPlanHeaderCityToPro(MonthPlanHeaderCityToPro monthPlanHeaderCityToPro)
    {
        return monthPlanHeaderCityToProMapper.updateMonthPlanHeaderCityToPro(monthPlanHeaderCityToPro);
    }

    @Override
    public int updateMonthPlanHeaderCityToProByNo(MonthPlanHeaderCityToPro monthPlanHeaderCityToPro)
    {
        return monthPlanHeaderCityToProMapper.updateMonthPlanHeaderCityToProByNo(monthPlanHeaderCityToPro);
    }
    /**
     * 删除月计划申请表(市平台提交到省平台)
     *
     * @param monthPlanHeaderCityToPro 月计划申请表(市平台提交到省平台)ID
     * @return 结果
     */
    public int deleteMonthPlanHeaderCityToProById(MonthPlanHeaderCityToPro monthPlanHeaderCityToPro)
    {
        return monthPlanHeaderCityToProMapper.deleteMonthPlanHeaderCityToProById( monthPlanHeaderCityToPro);
    };


    /**
     * 批量删除月计划申请表(市平台提交到省平台)对象
     *
     * @return 结果
     */
    @Override
    public int deleteMonthPlanHeaderCityToProByIds(Integer[] rowIds)
    {
        return monthPlanHeaderCityToProMapper.deleteMonthPlanHeaderCityToProByIds( rowIds);
    }

}
