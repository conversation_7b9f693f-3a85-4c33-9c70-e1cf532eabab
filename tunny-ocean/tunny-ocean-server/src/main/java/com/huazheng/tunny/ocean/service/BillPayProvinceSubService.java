package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.BillPayProvinceSub;
import com.huazheng.tunny.ocean.api.vo.BillDealWithCityAndCostVO;

import java.util.List;
import java.util.Map;

/**
 * 应付账单（省平台）子账单表 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-06-13 15:34:17
 */
public interface BillPayProvinceSubService extends IService<BillPayProvinceSub> {
    /**
     * 查询应付账单（省平台）子账单表信息
     *
     * @param id 应付账单（省平台）子账单表ID
     * @return 应付账单（省平台）子账单表信息
     */
    public BillPayProvinceSub selectBillPayProvinceSubById(Integer id);

    /**
     * 查询应付账单（省平台）子账单表列表
     *
     * @param billPayProvinceSub 应付账单（省平台）子账单表信息
     * @return 应付账单（省平台）子账单表集合
     */
    public List<BillPayProvinceSub> selectBillPayProvinceSubList(BillPayProvinceSub billPayProvinceSub);


    /**
     * 分页模糊查询应付账单（省平台）子账单表列表
     * @return 应付账单（省平台）子账单表集合
     */
    public Page selectBillPayProvinceSubListByLike(Query query);



    /**
     * 新增应付账单（省平台）子账单表
     *
     * @param billPayProvinceSub 应付账单（省平台）子账单表信息
     * @return 结果
     */
    public int insertBillPayProvinceSub(BillPayProvinceSub billPayProvinceSub);

    /**
     * 修改应付账单（省平台）子账单表
     *
     * @param billPayProvinceSub 应付账单（省平台）子账单表信息
     * @return 结果
     */
    public int updateBillPayProvinceSub(BillPayProvinceSub billPayProvinceSub);

    /**
     * 删除应付账单（省平台）子账单表
     *
     * @param id 应付账单（省平台）子账单表ID
     * @return 结果
     */
    public int deleteBillPayProvinceSubById(Integer id);

    /**
     * 批量删除应付账单（省平台）子账单表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteBillPayProvinceSubByIds(Integer[] ids);

    List<BillDealWithCityAndCostVO> selectFdBillSubByBillNo(String billNo,String customerName, String platformCode);

    List<BillDealWithCityAndCostVO> selectFdBillSubByShiftNo(String shiftNo, String customerCode);

    List<BillPayProvinceSub> selectBillPayProvinceSubByBillCode(String billNo);
}

