package com.huazheng.tunny.ocean.service.ealedgermain.impl;

import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.ocean.api.entity.Shifmanagement;
import com.huazheng.tunny.ocean.api.vo.FdInvoiceCostLedgerDetailsVO;
import com.huazheng.tunny.ocean.api.vo.FdInvoiceCostLedgerTitleVO;
import com.huazheng.tunny.ocean.mapper.ShifmanagementMapper;
import com.huazheng.tunny.ocean.mapper.ealedgermain.EaFdInvoiceCostLedgerMapper;
import com.huazheng.tunny.ocean.util.ExportStyleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 成本台账服务类
 *
 * <AUTHOR>
 * @since 2025-04-16 14:05
 */
@Service
@Slf4j
public class EaFdInvoiceCostLedgerService {


    @Autowired
    private ShifmanagementMapper shifmanagementMapper;

    @Autowired
    private EaFdInvoiceCostLedgerMapper costLedgerMapper;

    /**
     * 回程标识
     */
    private static final String RETURN_TRIP = "R";
    /**
     * 固定的列数
     */
    private static final int FIXED_COLUMN_COUNT = 8;

    /**
     * 导出成本台账
     *
     * @param costLedgerDetails , response
     * <AUTHOR>
     * @since 2025/4/16 下午3:18
     **/
    public void costLedgerExport(FdInvoiceCostLedgerDetailsVO costLedgerDetails, HttpServletResponse response) {
        try {
            FdInvoiceCostLedgerTitleVO titleVO = getFdInvoiceRevenueLedgerTitle(costLedgerDetails);
            List<FdInvoiceCostLedgerDetailsVO> detailList = getFdInvoiceCostLedgerDetails(costLedgerDetails);
            if (detailList.isEmpty()) {
                return;
            }
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("成本台账");
            sheet.setDefaultColumnWidth(17);
            sheet.setColumnWidth(0, 8 * 256);
            sheet.setColumnWidth(0, 8 * 256);

            CellStyle titleStyle = ExportStyleUtil.createTitleStyle(workbook);
            CellStyle detailsStyle = ExportStyleUtil.createDetailsStyleCenter(workbook);
            CellStyle titleDetailStyle = ExportStyleUtil.createTitleDetailStyle(workbook);

            int rowIndex = 0;
            Row titleRow = sheet.createRow(rowIndex);
            for (int i = 0; i < FIXED_COLUMN_COUNT; i++) {
                titleRow.createCell(i).setCellStyle(titleStyle);
            }
            titleRow.setHeightInPoints(25);
            Cell titleCell = titleRow.getCell(0);

            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy年M月d日");
            Date date = inputFormat.parse(titleVO.getPlanShipTime());
            String planShipTime = outputFormat.format(date);

            String titleText = planShipTime + titleVO.getDestinationName()
                    + titleVO.getPortStation() + titleVO.getShippingLine()
                    + ("R".equals(titleVO.getTrip()) ? "回程" : "去程") + "发运明细";

            sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, FIXED_COLUMN_COUNT - 1));
            titleCell.setCellValue(titleText);

            rowIndex++;
            Row titleRow2 = sheet.createRow(rowIndex);
            for (int i = 0; i < FIXED_COLUMN_COUNT; i++) {
                titleRow2.createCell(i).setCellStyle(titleStyle);
            }
            titleRow2.setHeightInPoints(40);
            sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 1));
            sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 4, FIXED_COLUMN_COUNT - 1));
            titleRow2.getCell(0).setCellValue("省级班列号：");
            titleRow2.getCell(2).setCellValue(titleVO.getProvinceShiftNo());
            titleRow2.getCell(3).setCellValue("客户：");
            titleRow2.getCell(4).setCellValue("山东高速齐鲁号欧亚班列运营有限公司");


            rowIndex++;
            Row titleRow3 = sheet.createRow(rowIndex);
            for (int i = 0; i < FIXED_COLUMN_COUNT; i++) {
                titleRow3.createCell(i).setCellStyle(titleStyle);
            }
            sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 1));
            sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 2, FIXED_COLUMN_COUNT - 1));
            titleRow3.getCell(0).setCellValue("发票号：");
            titleRow3.getCell(2).setCellValue("");
            titleRow3.setHeightInPoints(25);
            rowIndex++;
            String[] headers = {"序号", "国家", "口岸", "箱型", "箱属", "箱号", "境内运费", "境外运费"};
            Row headerRow = sheet.createRow(rowIndex++);
            headerRow.setHeightInPoints(25);
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(titleStyle);
            }

            for (int i = 0; i < detailList.size(); i++) {
                FdInvoiceCostLedgerDetailsVO vo = detailList.get(i);
                Row row = sheet.createRow(rowIndex++);
                row.setHeightInPoints(25);
                row.createCell(0).setCellValue(i + 1);
                row.createCell(1).setCellValue(vo.getDestinationCountry());
                row.createCell(2).setCellValue(vo.getPortStation());
                row.createCell(3).setCellValue(vo.getContainerType());
                row.createCell(4).setCellValue(vo.getContainerOwner());
                row.createCell(5).setCellValue(vo.getContainerNumber());
                row.createCell(6).setCellValue(vo.getDomesticFreightAmount().doubleValue());
                row.createCell(7).setCellValue(vo.getOverseasFreightAmount().doubleValue());
                for (int j = 0; j < FIXED_COLUMN_COUNT; j++) {
                    row.getCell(j).setCellStyle(detailsStyle);
                }
            }

            Row subtotalRow = sheet.createRow(rowIndex);
            subtotalRow.setHeightInPoints(25);
            for (int i = 0; i < FIXED_COLUMN_COUNT; i++) {
                subtotalRow.createCell(i).setCellStyle(titleDetailStyle);
            }

            BigDecimal domesticFreightAmount = detailList.stream().map(FdInvoiceCostLedgerDetailsVO::getDomesticFreightAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal overseasFreightAmount = detailList.stream().map(FdInvoiceCostLedgerDetailsVO::getOverseasFreightAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 5));
            subtotalRow.getCell(0).setCellValue("小计");
            subtotalRow.getCell(6).setCellValue(domesticFreightAmount.doubleValue());
            subtotalRow.getCell(7).setCellValue(overseasFreightAmount.doubleValue());

            rowIndex++;
            Row totalRow = sheet.createRow(rowIndex);
            totalRow.setHeightInPoints(25);
            for (int i = 0; i < FIXED_COLUMN_COUNT; i++) {
                totalRow.createCell(i).setCellStyle(titleDetailStyle);
            }
            sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 5));
            sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 6, FIXED_COLUMN_COUNT - 1));
            totalRow.getCell(0).setCellValue("合计");
            //合计：国内运费+境外运费
            totalRow.getCell(6).setCellValue(domesticFreightAmount.add(overseasFreightAmount).doubleValue());


            String fileName = URLEncoder.encode(titleVO.getProvinceShiftNo() + "-成本台账" + ".xlsx", "UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (Exception e) {
            throw new RuntimeException("导出失败: " + e.getMessage(), e);
        }
    }


    /**
     * 查询明细
     *
     * @param costLedgerDetails 成本台账
     * @return List<FdInvoiceCostLedgerDetailsVO>
     * <AUTHOR>
     * @since 2025/4/16 下午2:36
     **/
    private List<FdInvoiceCostLedgerDetailsVO> getFdInvoiceCostLedgerDetails(FdInvoiceCostLedgerDetailsVO costLedgerDetails) {
        //查询集装箱信息
        List<FdInvoiceCostLedgerDetailsVO> containerList = costLedgerMapper.selectContainers(costLedgerDetails);
        //查询境内费用
        List<FdInvoiceCostLedgerDetailsVO> domesticFreightAmount = costLedgerMapper.selectDomesticFreightAmount(costLedgerDetails);
        //查询境外费用(除境内运费外，都算境外运费--姜仁坤)
        List<FdInvoiceCostLedgerDetailsVO> overseasFreightAmount = costLedgerMapper.selectOverseasFreightAmount(costLedgerDetails);
        // 将所有费用数据转换为Map，主键是：结算单号-班次号-箱号
        Map<String, FdInvoiceCostLedgerDetailsVO> domesticFreightAmountMap = domesticFreightAmount.stream()
                .collect(Collectors.toMap(
                        fee -> fee.getShiftNo() + "-" + fee.getContainerNumber(),
                        fee -> fee));
        Map<String, FdInvoiceCostLedgerDetailsVO> overseasFreightAmountMap = overseasFreightAmount.stream()
                .collect(Collectors.toMap(
                        fee -> fee.getShiftNo() + "-" + fee.getContainerNumber(),
                        fee -> fee));

        //根据箱号遍历数据，组成新的集合fdInvoiceRevenueLedgerDetailsVOList
        for (FdInvoiceCostLedgerDetailsVO container : containerList) {
            String key = container.getShiftNo() + "-" + container.getContainerNumber();
            if (domesticFreightAmountMap.containsKey(key)) {
                container.setDomesticFreightAmount(domesticFreightAmountMap.get(key).getDomesticFreightAmount());
            } else {
                container.setDomesticFreightAmount(BigDecimal.ZERO);
            }
            if (overseasFreightAmountMap.containsKey(key)) {
                container.setOverseasFreightAmount(overseasFreightAmountMap.get(key).getOverseasFreightAmount());
            } else {
                container.setOverseasFreightAmount(BigDecimal.ZERO);
            }
        }
        return containerList;
    }


    /**
     * 查询标题
     *
     * @param costLedgerDetails 成本台账
     * @return FdInvoiceCostLedgerTitleVO
     * <AUTHOR>
     * @since 2025/4/16 下午2:17
     **/
    private FdInvoiceCostLedgerTitleVO getFdInvoiceRevenueLedgerTitle(FdInvoiceCostLedgerDetailsVO costLedgerDetails) {
        Shifmanagement sel = new Shifmanagement();
        sel.setShiftId(costLedgerDetails.getShiftNo());
        sel.setPlatformCode(costLedgerDetails.getCustomerCode());
        sel.setDeleteFlag("N");
        List<Shifmanagement> managementList = shifmanagementMapper.selectShifmanagementList(sel);
        FdInvoiceCostLedgerTitleVO fdInvoiceCostLedgerTitle = new FdInvoiceCostLedgerTitleVO();
        BeanUtil.copyProperties(managementList.get(0), fdInvoiceCostLedgerTitle);

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        if (RETURN_TRIP.equals(managementList.get(0).getTrip()) && managementList.get(0).getResveredField06() != null) {
            fdInvoiceCostLedgerTitle.setPlanShipTime(formatter.format(managementList.get(0).getResveredField06()));
        } else if (managementList.get(0).getPlanShipTime() != null) {
            fdInvoiceCostLedgerTitle.setPlanShipTime(formatter.format(managementList.get(0).getPlanShipTime()));
        }
        return fdInvoiceCostLedgerTitle;
    }

}
