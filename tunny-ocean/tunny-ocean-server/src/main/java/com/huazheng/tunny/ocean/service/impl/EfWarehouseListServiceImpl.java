package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.controller.SignatureController;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.EfWarehouseListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service("efWarehouseListService")
public class EfWarehouseListServiceImpl extends ServiceImpl<EfWarehouseListMapper, EfWarehouseList> implements EfWarehouseListService {

    @Autowired
    private EfWarehouseListMapper efWarehouseListMapper;
    @Autowired
    private EfWaybillMapper efWaybillMapper;
    @Autowired
    private EfWaybillGoodsMapper efWaybillGoodsMapper;
    @Autowired
    private SignatureController signatureController;
    @Autowired
    private EfWarehouseMapper efWarehouseMapper;
    @Autowired
    private EfSupplyApplyListMapper efSupplyApplyListMapper;
    /**
     * 查询仓单融资仓单表信息
     *
     * @param rowId 仓单融资仓单表ID
     * @return 仓单融资仓单表信息
     */
    @Override
    public EfWarehouseList selectEfWarehouseListById(String rowId)
    {
        return efWarehouseListMapper.selectEfWarehouseListById(rowId);
    }

    /**
     * 查询仓单融资仓单表列表
     *
     * @param efWarehouseList 仓单融资仓单表信息
     * @return 仓单融资仓单表集合
     */
    @Override
    public List<EfWarehouseList> selectEfWarehouseListList(EfWarehouseList efWarehouseList)
    {
        return efWarehouseListMapper.selectEfWarehouseListList(efWarehouseList);
    }


    /**
     * 分页模糊查询仓单融资仓单表列表
     * @return 仓单融资仓单表集合
     */
    @Override
    public Page selectEfWarehouseListListByLike(Query query)
    {
        EfWarehouseList efWarehouseList =  BeanUtil.mapToBean(query.getCondition(), EfWarehouseList.class,false);
        query.setRecords(efWarehouseListMapper.selectEfWarehouseListListByLike(query,efWarehouseList));
        return query;
    }

    /**
     * 新增仓单融资仓单表
     *
     * @param efWarehouseList 仓单融资仓单表信息
     * @return 结果
     */
    @Override
    public int insertEfWarehouseList(EfWarehouseList efWarehouseList)
    {
        return efWarehouseListMapper.insertEfWarehouseList(efWarehouseList);
    }

    /**
     * 修改仓单融资仓单表
     *
     * @param efWarehouseList 仓单融资仓单表信息
     * @return 结果
     */
    @Override
    public int updateEfWarehouseList(EfWarehouseList efWarehouseList)
    {

        return efWarehouseListMapper.updateEfWarehouseList(efWarehouseList);
    }

    @Override
    public String syncWarehouseInfo(EfWarehouseList efWarehouseList)
    {
        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(efWarehouseList!=null){
            //调用中钞接口，同步仓单信息
            String json = JSONUtil.parseObj(efWarehouseList, true).toStringPretty();
            final String result = signatureController.doPost("/v1/safe/warehouse/syncWarehouseInfo", json);
            JSONObject resultObject = JSONUtil.parseObj(result);

            from = "中钞平台:";
            flag = Boolean.valueOf(String.valueOf(resultObject.get("b")));
            msg = String.valueOf(resultObject.get("msg"));

            if(flag){
                flag = true;

                //仓单货物信息
                final List<EfWarehouseGoods> goodsInfoList = efWarehouseList.getGoodsInfoList();
                if(CollUtil.isNotEmpty(goodsInfoList)){
                    for (EfWarehouseGoods goodsInfo:goodsInfoList
                    ) {
                        //货物运单信息
                        EfWaybill del = new EfWaybill();
                        del.setQlFinancingNo(efWarehouseList.getQlFinancingNo());
                        del.setWarehouseReceiptNo(efWarehouseList.getWarehouseCode());
                        del.setGoodsNo(goodsInfo.getGoodsNo());
                        del.setUpdateTime(LocalDateTime.now());
                        del.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                        del.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                        efWaybillMapper.deleteEfWaybills(del);
                        final EfWaybill wayBillInfo = goodsInfo.getWayBillInfo();
                        if(wayBillInfo!=null){
                            //运单箱信息
                            EfWaybillGoods del2 = new EfWaybillGoods();
                            del2.setQlFinancingNo(efWarehouseList.getQlFinancingNo());
                            del2.setWarehouseReceiptNo(efWarehouseList.getWarehouseCode());
                            del2.setWaybillNo(wayBillInfo.getWaybillNo());
                            del2.setOrderNo(wayBillInfo.getOrderNo());
                            del2.setGoodsNo(goodsInfo.getGoodsNo());
                            del2.setUpdateTime(LocalDateTime.now());
                            del2.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                            del2.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                            efWaybillGoodsMapper.deleteEfWaybillGoods(del2);
                            final List<EfWaybillGoods> wayBillGoodsInfoList = wayBillInfo.getWayBillGoodsInfoList();
                            if(CollUtil.isNotEmpty(wayBillGoodsInfoList)){
                                for (EfWaybillGoods wayBillGoodsInfo:wayBillGoodsInfoList
                                ) {
                                    wayBillGoodsInfo.setRowId(UUID.randomUUID().toString());
                                    wayBillGoodsInfo.setOrderNo(wayBillInfo.getOrderNo());
                                    wayBillGoodsInfo.setWaybillNo(wayBillInfo.getWaybillNo());
                                    wayBillGoodsInfo.setWarehouseReceiptNo(efWarehouseList.getWarehouseCode());
                                    wayBillGoodsInfo.setGoodsNo(wayBillInfo.getGoodsNo());
                                    wayBillGoodsInfo.setAddTime(LocalDateTime.now());
                                    wayBillGoodsInfo.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                    wayBillGoodsInfo.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                    efWaybillGoodsMapper.insertEfWaybillGoods(wayBillGoodsInfo);
                                }
                            }
                            wayBillInfo.setQlFinancingNo(efWarehouseList.getQlFinancingNo());
                            wayBillInfo.setWarehouseReceiptNo(efWarehouseList.getWarehouseCode());
                            wayBillInfo.setGoodsNo(goodsInfo.getGoodsNo());
                            wayBillInfo.setAddTime(LocalDateTime.now());
                            wayBillInfo.setAddWho(SecurityUtils.getUserInfo().getUserName());
                            wayBillInfo.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                            efWaybillMapper.insertEfWaybill(wayBillInfo);
                        }
                    }
                }
            }else{
                flag = false;
            }
        }else{
            flag = false;
            msg = "没有接收到仓单关联运单数据";
        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }


    /**
     * 删除仓单融资仓单表
     *
     * @param rowId 仓单融资仓单表ID
     * @return 结果
     */
    public int deleteEfWarehouseListById(String rowId)
    {
        return efWarehouseListMapper.deleteEfWarehouseListById( rowId);
    };


    /**
     * 批量删除仓单融资仓单表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfWarehouseListByIds(Integer[] rowIds)
    {
        return efWarehouseListMapper.deleteEfWarehouseListByIds( rowIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateMonitorVideo(EfWarehouseList ef)
    {
        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(ef!=null) {
            //调用中钞接口，同步仓单信息
            String json2 = JSONUtil.parseObj(ef, true).toStringPretty();
            final String result2 = signatureController.doPost("/v1/safe/warehouse/updateMonitorVideo", json2);

            JSONObject resultObject = JSONUtil.parseObj(result2);
            flag = Boolean.valueOf(String.valueOf(resultObject.get("b")));
            if(!flag){
                from = "中钞平台:";
                msg = String.valueOf(resultObject.get("msg"));
                return JSONUtil.parseObj(new R<>(500,flag,null,from+msg), false).toStringPretty();
            }
            //更新仓单
            EfWarehouse efWarehouse = new EfWarehouse();
            efWarehouse.setWarehouseReceiptNo(ef.getWarehouseCode());
            efWarehouse.setSurveillanceVideo(ef.getSurveillanceVideo());
            efWarehouseMapper.updateEfWarehouse(efWarehouse);
            //更新质押申请列表
            EfWarehouseList efWarehouseList = new EfWarehouseList();
            efWarehouseList.setWarehouseSupervisionNo(ef.getWarehouseSupervisionNo());
            efWarehouseList.setWarehouseCode(ef.getWarehouseCode());
            efWarehouseList.setDeleteFlag("N");
            final List<EfWarehouseList> efWarehouseLists = efWarehouseListMapper.selectEfWarehouseListList(efWarehouseList);
            if(CollUtil.isNotEmpty(efWarehouseLists)){
                for (EfWarehouseList list:efWarehouseLists
                     ) {
                    EfWarehouseList efWarehouseList2 = new EfWarehouseList();
                    efWarehouseList2.setRowId(list.getRowId());
                    efWarehouseList2.setSurveillanceVideo(ef.getSurveillanceVideo());
                    efWarehouseListMapper.updateEfWarehouseList(efWarehouseList2);
                }
            }
            //更新补仓申请列表
            EfSupplyApplyList efSupplyApplyList = new EfSupplyApplyList();
            efSupplyApplyList.setWarehouseSupervisionNo(ef.getWarehouseSupervisionNo());
            efSupplyApplyList.setWarehouseCode(ef.getWarehouseCode());
            efSupplyApplyList.setDeleteFlag("N");
            final List<EfSupplyApplyList> efSupplyApplyLists = efSupplyApplyListMapper.selectEfSupplyApplyListList(efSupplyApplyList);
            if(CollUtil.isNotEmpty(efSupplyApplyLists)){
                for (EfSupplyApplyList list:efSupplyApplyLists
                     ) {
                    EfSupplyApplyList efSupplyApplyList2 = new EfSupplyApplyList();
                    efSupplyApplyList2.setRowId(list.getRowId());
                    efSupplyApplyList2.setSurveillanceVideo(ef.getSurveillanceVideo());
                    efSupplyApplyListMapper.updateEfSupplyApplyList(efSupplyApplyList2);
                }
            }

        }else{
            msg = "没有接收到视频更新数据";
            flag = false;
        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }

}
