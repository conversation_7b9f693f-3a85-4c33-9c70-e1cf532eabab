package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FiFinancingLendInfoMapper;
import com.huazheng.tunny.ocean.api.entity.FiFinancingLendInfo;
import com.huazheng.tunny.ocean.service.FiFinancingLendInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fiFinancingLendInfoService")
public class FiFinancingLendInfoServiceImpl extends ServiceImpl<FiFinancingLendInfoMapper, FiFinancingLendInfo> implements FiFinancingLendInfoService {

    @Autowired
    private FiFinancingLendInfoMapper fiFinancingLendInfoMapper;

    public FiFinancingLendInfoMapper getFiFinancingLendInfoMapper() {
        return fiFinancingLendInfoMapper;
    }

    public void setFiFinancingLendInfoMapper(FiFinancingLendInfoMapper fiFinancingLendInfoMapper) {
        this.fiFinancingLendInfoMapper = fiFinancingLendInfoMapper;
    }

    /**
     * 查询融资放款信息信息
     *
     * @param rowId 融资放款信息ID
     * @return 融资放款信息信息
     */
    @Override
    public FiFinancingLendInfo selectFiFinancingLendInfoById(String rowId)
    {
        return fiFinancingLendInfoMapper.selectFiFinancingLendInfoById(rowId);
    }

    /**
     * 查询融资放款信息列表
     *
     * @param fiFinancingLendInfo 融资放款信息信息
     * @return 融资放款信息集合
     */
    @Override
    public List<FiFinancingLendInfo> selectFiFinancingLendInfoList(FiFinancingLendInfo fiFinancingLendInfo)
    {
        return fiFinancingLendInfoMapper.selectFiFinancingLendInfoList(fiFinancingLendInfo);
    }


    /**
     * 分页模糊查询融资放款信息列表
     * @return 融资放款信息集合
     */
    @Override
    public Page selectFiFinancingLendInfoListByLike(Query query)
    {
        FiFinancingLendInfo fiFinancingLendInfo =  BeanUtil.mapToBean(query.getCondition(), FiFinancingLendInfo.class,false);
        query.setRecords(fiFinancingLendInfoMapper.selectFiFinancingLendInfoListByLike(query,fiFinancingLendInfo));
        return query;
    }

    /**
     * 新增融资放款信息
     *
     * @param fiFinancingLendInfo 融资放款信息信息
     * @return 结果
     */
    @Override
    public int insertFiFinancingLendInfo(FiFinancingLendInfo fiFinancingLendInfo)
    {
        return fiFinancingLendInfoMapper.insertFiFinancingLendInfo(fiFinancingLendInfo);
    }

    /**
     * 修改融资放款信息
     *
     * @param fiFinancingLendInfo 融资放款信息信息
     * @return 结果
     */
    @Override
    public int updateFiFinancingLendInfo(FiFinancingLendInfo fiFinancingLendInfo)
    {
        return fiFinancingLendInfoMapper.updateFiFinancingLendInfo(fiFinancingLendInfo);
    }


    /**
     * 删除融资放款信息
     *
     * @param rowId 融资放款信息ID
     * @return 结果
     */
    public int deleteFiFinancingLendInfoById(String rowId)
    {
        return fiFinancingLendInfoMapper.deleteFiFinancingLendInfoById( rowId);
    }


    /**
     * 批量删除融资放款信息对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiFinancingLendInfoByIds(Integer[] rowIds)
    {
        return fiFinancingLendInfoMapper.deleteFiFinancingLendInfoByIds( rowIds);
    }

}
