package com.huazheng.tunny.ocean.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.feign.RemoteAdminService;
import com.huazheng.tunny.ocean.api.vo.SysDictVo;
import com.huazheng.tunny.ocean.service.ContainerTypeDataService;
import com.huazheng.tunny.ocean.service.GoodsDataService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.service.StationManagementService;
import com.huazheng.tunny.ocean.util.CheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 货物数据
 *
 * <AUTHOR>
 * @date 2023-06-29 14:27:37
 */
@Slf4j
@RestController
@RequestMapping("/goodsdata")
public class GoodsDataController {

    @Autowired
    private GoodsDataService goodsDataService;
    @Autowired
    private StationManagementService stationManagementService;
    @Autowired
    private RemoteAdminService remoteAdminService;
    @Autowired
    private ContainerTypeDataService containerTypeDataService;
    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  goodsDataService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return goodsDataService.selectGoodsDataListByLike(new Query<>(params));
    }

    /**
     *  列表
     * @param params
     * @return
     */
    @GetMapping("/pageForCity")
    public Page pageForCity(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  goodsDataService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return goodsDataService.pageForCity(new Query<>(params));
    }

    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        GoodsData goodsData =goodsDataService.selectGoodsDataById(rowId);
        return new R<>(goodsData);
    }

    /**
     * 保存
     * @param goodsData
     * @return R
     */
    @PostMapping
    public R save(@RequestBody GoodsData goodsData) {
        CheckUtil checkUtil = new CheckUtil();
        boolean b = CheckUtil.verifyCntrCode(goodsData.getContainerNo());
        if(!b){
            return new R<>(500,Boolean.FALSE,null,"箱号格式错误！");
        }
        goodsDataService.insertGoodsData(goodsData);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param goodsData
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody GoodsData goodsData) {
        GoodsData sel = new GoodsData();
        sel.setRowId(goodsData.getRowId());
        sel.setContainerNo(goodsData.getContainerNo());
        sel.setArrivalDate(goodsData.getArrivalDate());
        List<GoodsData> list = goodsDataService.selectDuplicate(sel);
        if(CollUtil.isNotEmpty(list)){
            return new R<>(Boolean.FALSE,"该箱号/抵港日期已存在："+sel.getContainerNo()+"/"+sel.getArrivalDate());
        }
        goodsDataService.updateGoodsData(goodsData);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 提交
     * @param goodsData
     * @return R
     */
    @PostMapping("/commit")
    public R commit(@RequestBody GoodsData goodsData) {
        goodsDataService.commit(goodsData);
        return new R<>(Boolean.TRUE);
    }
    /**
     * 批量提交
     * @param rowIds
     * @return R
     */
    @PostMapping("/commitList")
    public R commitList(@RequestBody List<String> rowIds) {
        goodsDataService.commitList(rowIds);
        return new R<>(Boolean.TRUE);
    }

    

    /**
     * 删除
     * @param rowId
     * @return R
     */
    @GetMapping("/del/{rowId}")
    public R delete(@PathVariable  String rowId) {
        goodsDataService.deleteGoodsDataById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        goodsDataService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<GoodsData> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<GoodsData> list = goodsDataService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), GoodsData.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }

    @PostMapping("/importFile")
    @ResponseBody
    public R importFile(@RequestParam  MultipartFile file,String platformCode ) throws Exception {
//        List<BookingRequesdetailDTO> chainGroupOrganizations=new ArrayList<>();
        R r=new R();
        try {
            String originalFilename = file.getOriginalFilename();
            InputStream inputStream = file.getInputStream();
            // 调用工具类中方法，读取excel文件中数据
            long i = System.currentTimeMillis();
            r = readExcel(originalFilename, inputStream,platformCode);
            inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
            return new R<>(new Throwable(e));
        }
        return r;
    }

    public boolean isRowEmpty(Row row){
        if(row!=null){
            for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
                Cell cell = row.getCell(c);
                if(cell!=null){
                    cell.setCellType(CellType.STRING);
                }
                if (cell != null && !"".equals(cell.getStringCellValue())){
                    return false;//不是空行
                }
            }
        }
        return true;
    }

    /**
     * 读取excel文件数据
     * @param fileName
     * @param inputStream
     * @return
     * @throws Exception
     */
    public  R readExcel(String fileName, InputStream inputStream,String platformCode) throws Exception {
        CheckUtil checkUtil = new CheckUtil();
        R r = new R();
        boolean ret = isXls(fileName);
        Workbook workbook = null;
        // 根据后缀创建不同的对象
        if(ret) {
            workbook = new HSSFWorkbook(inputStream);
        }else{
            workbook = new XSSFWorkbook(inputStream);
        }
        Sheet sheet = getAccuracyContextNum(workbook);

        List<GoodsData> list = new ArrayList<>();
        int lastRowNum = sheet.getLastRowNum();
        if(lastRowNum>0) {
            StationManagement stationManagement = new StationManagement();
            stationManagement.setIsPort("1");
            stationManagement.setDeleteFlag("N");
            List<StationManagement> portStation = stationManagementService.selectStationManagementList(stationManagement);

            StationManagement stationManagement2 = new StationManagement();
            stationManagement2.setIsPort("0");
            stationManagement2.setDeleteFlag("N");
            List<StationManagement> startStation = stationManagementService.selectStationManagementList(stationManagement2);

            String data = remoteAdminService.selectDictByType2("country_type");
            List<SysDictVo> countryList = JSONUtil.toList(JSONUtil.parseArray(data), SysDictVo.class);

            ContainerTypeData sel = new ContainerTypeData();
            sel.setDeleteFlag("N");
            List<ContainerTypeData> containerTypeDataList = containerTypeDataService.selectContainerTypeDataList(sel);

            //从第二行开始读取
            for (int i = 1; i <= lastRowNum; i++) {
                GoodsData goodsData = new GoodsData();
                Row row = sheet.getRow(i);
                if (row.getCell(0) != null) {
                    row.getCell(0).setCellType(CellType.STRING);
                    if (!"".equals(row.getCell(0).getStringCellValue()) && row.getCell(0).getStringCellValue() != null) {
                        goodsData.setContainerNo(row.getCell(0).getStringCellValue().trim());//箱号
                        boolean b = CheckUtil.verifyCntrCode(goodsData.getContainerNo());
                        if(!b){
                            return new R(Boolean.FALSE, "箱号格式错误:"+goodsData.getContainerNo());
                        }
                    } else {
                        return new R(Boolean.FALSE, "箱号不能为空");
                    }
                } else {
                    return new R(Boolean.FALSE, "箱号不能为空");
                }
                if (row.getCell(1) != null) {
                    row.getCell(1).setCellType(CellType.STRING);
                    if (!"".equals(row.getCell(1).getStringCellValue()) && row.getCell(1).getStringCellValue() != null) {
//                        goodsData.setContainerType(row.getCell(1).getStringCellValue());//箱型'
                        String containerTypeCode = row.getCell(1).getStringCellValue();
                        Boolean flag = true;
                        for (ContainerTypeData containerTypeData:containerTypeDataList
                        ) {
                            if(containerTypeData.getContainerTypeCode().equals(containerTypeCode)){
                                goodsData.setContainerTypeCode(containerTypeData.getContainerTypeCode());
                                goodsData.setContainerTypeName(containerTypeData.getContainerTypeName());
                                goodsData.setContainerType(containerTypeData.getContainerTypeSize());
                                flag = false;
                                break;
                            }
                        }

                        if(flag){
                            return new R(Boolean.FALSE,"未查询到该箱型代码："+containerTypeCode);
                        }
                    } else {
                        return new R(Boolean.FALSE, "箱型代码不能为空");
                    }
                } else {
                    return new R(Boolean.FALSE, "箱型代码不能为空");
                }
                if (row.getCell(2) != null) {
                    row.getCell(2).setCellType(CellType.STRING);
                    if (!"".equals(row.getCell(2).getStringCellValue()) && row.getCell(2).getStringCellValue() != null) {
                        goodsData.setPortStation(row.getCell(2).getStringCellValue());//口岸站
                        Boolean flag = true;
                        for (StationManagement p:portStation
                             ) {
                            if(goodsData.getPortStation().equals(p.getStationName())){
                                goodsData.setPortStationCode(p.getStationCode());
                                flag = false;
                                break;
                            }
                        }
                        if(flag){
                            return new R(Boolean.FALSE, "未查询到该口岸站"+goodsData.getPortStation());
                        }
                    } else {
                        return new R(Boolean.FALSE, "口岸站不能为空");
                    }
                } else {
                    return new R(Boolean.FALSE, "口岸站不能为空");
                }
                if (row.getCell(3) != null) {
                    row.getCell(3).setCellType(CellType.STRING);
                    if (!"".equals(row.getCell(3).getStringCellValue()) && row.getCell(3).getStringCellValue() != null) {
                        goodsData.setStartStation(row.getCell(3).getStringCellValue());//始发站
                        Boolean flag = true;
                        for (StationManagement p:startStation
                        ) {
                            if(goodsData.getStartStation().equals(p.getStationName())){
                                goodsData.setStartStationCode(p.getStationCode());
                                flag = false;
                                break;
                            }
                        }
                        if(flag){
                            return new R(Boolean.FALSE, "未查询到该始发站"+goodsData.getStartStation());
                        }
                    } else {
                        return new R(Boolean.FALSE, "始发站不能为空");
                    }
                } else {
                    return new R(Boolean.FALSE, "始发站不能为空");
                }
                if (row.getCell(4) != null) {
                    row.getCell(4).setCellType(CellType.STRING);
                    if (!"".equals(row.getCell(4).getStringCellValue()) && row.getCell(4).getStringCellValue() != null) {
                        goodsData.setGoodsOrigin(row.getCell(4).getStringCellValue());//货源地
                    } else {
                        return new R(Boolean.FALSE, "货源地不能为空");
                    }
                } else {
                    return new R(Boolean.FALSE, "货源地不能为空");
                }
                if (row.getCell(5) != null) {
                    row.getCell(5).setCellType(CellType.STRING);
                    if (!"".equals(row.getCell(5).getStringCellValue()) && row.getCell(5).getStringCellValue() != null) {
                        goodsData.setDestinationCountry(row.getCell(5).getStringCellValue());//目的国
                        Boolean flag = true;
                        for (SysDictVo s:countryList
                        ) {
                            if(goodsData.getDestinationCountry().equals(s.getName())){
                                goodsData.setDestinationCountryCode(s.getCode());
                                flag = false;
                                break;
                            }
                        }
                        if(flag){
                            return new R(Boolean.FALSE, "未查询到该目的国"+goodsData.getDestinationCountry());
                        }
                    } else {
                        return new R(Boolean.FALSE, "目的国不能为空");
                    }
                } else {
                    return new R(Boolean.FALSE, "目的国不能为空");
                }
                if (row.getCell(6) != null) {
                    row.getCell(6).setCellType(CellType.STRING);
                    if (!"".equals(row.getCell(6).getStringCellValue()) && row.getCell(6).getStringCellValue() != null) {
                        goodsData.setProductName(row.getCell(6).getStringCellValue());//品名
                    } else {
                        return new R(Boolean.FALSE, "品名不能为空");
                    }
                } else {
                    return new R(Boolean.FALSE, "品名不能为空");
                }
                if (row.getCell(7) != null) {
                    row.getCell(7).setCellType(CellType.STRING);
                    if (!"".equals(row.getCell(7).getStringCellValue()) && row.getCell(7).getStringCellValue() != null) {
                        goodsData.setValueUsd(BigDecimal.valueOf(Double.parseDouble(row.getCell(7).getStringCellValue())));//货值（美元）
                    } else {
                        return new R(Boolean.FALSE, "货值（美元）不能为空");
                    }
                } else {
                    return new R(Boolean.FALSE, "货值（美元）不能为空");
                }
                if (row.getCell(8) != null) {
                    if (!"".equals(row.getCell(8).getDateCellValue()) && row.getCell(8).getDateCellValue() != null) {
                        goodsData.setArrivalDate(dateFormat(row.getCell(8).getDateCellValue()));//抵港日期
                    } else {
                        return new R(Boolean.FALSE, "抵港日期不能为空");
                    }
                } else {
                    return new R(Boolean.FALSE, "抵港日期不能为空");
                }
                goodsData.setPlatformCode(platformCode);

                //数据库查重
                GoodsData sel2 = new GoodsData();
                sel2.setContainerNo(goodsData.getContainerNo());
                sel2.setArrivalDate(goodsData.getArrivalDate());
                sel2.setPlatformCode(platformCode);
                sel2.setAddWho(SecurityUtils.getUserInfo().getUserName());
                sel2.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                sel2.setDeleteFlag("N");
                List<GoodsData> dataList = goodsDataService.selectGoodsDataList(sel2);
                if(dataList!=null && dataList.size()>0){
                    return new R(Boolean.FALSE, "该箱号/抵港日期数据已存在:"+goodsData.getContainerNo()+"/"+goodsData.getArrivalDate());
                }

                list.add(goodsData);
            }

            //导入数据查重
            for (int i = 0; i < list.size(); i++) {
                String c1 = list.get(i).getContainerNo();
                String a1 = list.get(i).getArrivalDate();
                for (int j = list.size() - 1; j > i; j--) {
                    String c2 = list.get(j).getContainerNo();
                    String a2 = list.get(j).getArrivalDate();
                    if(c1.equals(c2)&&a1.equals(a2)){
                        return new R(Boolean.FALSE, "导入箱号/抵港日期数据重复:"+c1+"/"+a1);
                    }
                }
            }

            for (GoodsData g:list
                 ) {
                goodsDataService.insertGoodsData(g);
            }
        }else {
            return new R(Boolean.FALSE,"表格不能为空");
        }
        workbook.close();
//        r.setData(list);
        r.setMsg("导入成功！");
        return r;
    }

    /**
     * 下载模板
     * @param response
     * @throws IOException
     */
    @PostMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        XSSFWorkbook workbook=new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("货物数据");
        XSSFRow row = sheet.createRow(0);
        //宽度
        for(int i=0;i<=8;i++){
            sheet.setColumnWidth(i,60*80);
        }
        row.setHeight((short) (10*50));
        //字体
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");

        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);//水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setBorderBottom(BorderStyle.THIN); //下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);

        XSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("箱号*");
        cell0.setCellStyle(style);
        XSSFCell cell1 = row.createCell(1);
        cell1.setCellValue("箱型代码*");
        cell1.setCellStyle(style);
        XSSFCell cell2 = row.createCell(2);
        cell2.setCellValue("口岸站*");
        cell2.setCellStyle(style);
        XSSFCell cell3 = row.createCell(3);
        cell3.setCellValue("发站*");
        cell3.setCellStyle(style);
        XSSFCell cell4 = row.createCell(4);
        cell4.setCellValue("货源地*");
        cell4.setCellStyle(style);
        XSSFCell cell5 = row.createCell(5);
        cell5.setCellValue("目的国*");
        cell5.setCellStyle(style);
        XSSFCell cell6 = row.createCell(6);
        cell6.setCellValue("品名*");
        cell6.setCellStyle(style);
        XSSFCell cell7 = row.createCell(7);
        cell7.setCellValue("货值*(美金)");
        cell7.setCellStyle(style);
        XSSFCell cell8 = row.createCell(8);
        cell8.setCellValue("抵港日期*");
        cell8.setCellStyle(style);

        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        String fileName = "货物数据.xlsx";
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
        response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
//        response.setHeader( "Content-Disposition", "attachment;filename=" + new String( "货物数据.xls".getBytes("GB2312"), "8859_1" ));
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();

    }

    /**
     * 下载模板
     * @param response
     * @throws IOException
     */
    @PostMapping("/exportByTitles")
    public void exportByTitles(@RequestBody GoodsData goodsData, HttpServletResponse response) throws IOException {
        int rowIndex = 0;
        XSSFWorkbook workbook=new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("货物数据");
        XSSFRow row = sheet.createRow(rowIndex);
        List<String> titles = goodsData.getTitles();
        //宽度
        for(int i=0;i<titles.size();i++){
            sheet.setColumnWidth(i,60*80);
        }
        row.setHeight((short) (10*50));
        //字体
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");

        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);//水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setBorderBottom(BorderStyle.THIN); //下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);

        for(int i=0;i<titles.size();i++){
            XSSFCell cell0 = row.createCell(i);
            cell0.setCellValue(titles.get(i));
            cell0.setCellStyle(style);
        }

        //另起一行
        rowIndex++;

        goodsData.setAddWho(SecurityUtils.getUserInfo().getUserName());
        goodsData.setDeleteFlag("N");
        goodsData.setStatus("1");
        List<GoodsData> list = goodsDataService.selectGoodsDataList(goodsData);
        if(CollUtil.isNotEmpty(list)){
            //样式
            font = workbook.createFont();
            font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
            font.setFontName("宋体");//设置字体
            style = workbook.createCellStyle();
            style.setFont(font);
            style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
            style.setAlignment(HorizontalAlignment.CENTER);// 水平
            style.setBorderTop(BorderStyle.THIN);//上边框
            style.setBorderBottom(BorderStyle.THIN);//下边框
            style.setBorderLeft(BorderStyle.THIN);//左边框
            style.setBorderRight(BorderStyle.THIN);//右边框
            for (int x = 0; x < list.size(); x++) {
                row = sheet.createRow(rowIndex);
                row.setHeightInPoints(20);//设置行高
                for(int i=0;i<titles.size();i++){
                    XSSFCell cell0 = row.createCell(i);
                    String val = "";
                    if("箱号".equals(titles.get(i))){
                        val = list.get(x).getContainerNo();
                    }
                    if("箱型代码".equals(titles.get(i))){
                        val = list.get(x).getContainerType();
                    }
                    if("箱型名称".equals(titles.get(i))){
                        val = list.get(x).getContainerType();
                    }
                    if("箱型".equals(titles.get(i))){
                        val = list.get(x).getContainerType();
                    }
                    if("口岸站".equals(titles.get(i))){
                        val = list.get(x).getPortStation();
                    }
                    if("发站".equals(titles.get(i))){
                        val = list.get(x).getStartStation();
                    }
                    if("货源地".equals(titles.get(i))){
                        val = list.get(x).getGoodsOrigin();
                    }
                    if("目的国".equals(titles.get(i))){
                        val = list.get(x).getDestinationCountry();
                    }
                    if("品名".equals(titles.get(i))){
                        val = list.get(x).getProductName();
                    }
                    if("货值(美金)".equals(titles.get(i))){
                        val = list.get(x).getValueUsd()+"";
                    }
                    if("抵港日期".equals(titles.get(i))){
                        val = list.get(x).getArrivalDate();
                    }
                    cell0.setCellValue(val);
                }

                for (Cell cellTemp : row) {
                    cellTemp.setCellStyle(style);
                }
                //另起一行
                rowIndex++;
            }
        }


        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader( "Content-Disposition", "attachment;filename=" + new String( "货物数据导出.xlsx".getBytes("GB2312"), "8859_1" ));
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();

    }

    //获取准确的文件行数
    public Sheet getAccuracyContextNum(Workbook workbook) {
        // 取第一个sheet
        Sheet sheet = workbook.getSheetAt(0);
        // 删除空行
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            // 删除空行
            if (this.isRowEmpty(row)) {
                int lastRowNum = sheet.getLastRowNum();
                if (i >= 0 && i < lastRowNum) {
                    sheet.shiftRows(i + 1, lastRowNum, -1);// 将行号为i+1一直到行号为lastRowNum的单元格全部上移一行，以便删除i行
                }
                if (i == lastRowNum) {
                    if (row != null) {
                        sheet.removeRow(row);
                    }
                }
                i--;
            }
        }
        return sheet;
    }

    /**
     * 判断导入文件格式
     * @param fileName
     * @return
     */
    public static boolean isXls(String fileName){
        // (?i)忽略大小写
        if(fileName.matches("^.+\\.(?i)(xls)$")){
            return true;
        }else if(fileName.matches("^.+\\.(?i)(xlsx)$")){
            return false;
        }else{
            throw new RuntimeException("格式不对");
        }
    }

    public static boolean isNumeric(String str) {
        String bigStr;
        try {
            bigStr = BigDecimal.valueOf(Double.parseDouble(str)).toString();
        } catch (Exception e) {
            return false;//异常 说明包含非数字。
        }
        return true;
    }

    public static boolean isValidDate(String str) {
        boolean convertSuccess=true;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        try {
            format.setLenient(false);
            format.parse(str);
        } catch (Exception e) {
            // e.printStackTrace();
            convertSuccess=false;
        }
        return convertSuccess;
    }

    public String dateFormat(Date arrivalDate) throws Exception {

        // 创建输出日期的格式
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");

        // 将日期对象格式化为字符串
        String output = outputFormat.format(arrivalDate);
        return output;
    }
}
