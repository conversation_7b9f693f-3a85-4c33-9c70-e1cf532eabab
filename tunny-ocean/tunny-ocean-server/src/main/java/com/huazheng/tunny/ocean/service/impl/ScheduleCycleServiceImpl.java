package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.DiceDTO;
import com.huazheng.tunny.ocean.api.entity.ScheduleCycle;
import com.huazheng.tunny.ocean.api.entity.StationManagement;
import com.huazheng.tunny.ocean.mapper.ScheduleCycleMapper;
import com.huazheng.tunny.ocean.service.ScheduleCycleService;
import com.huazheng.tunny.ocean.service.StationManagementService;
import com.huazheng.tunny.ocean.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description: Service
 * @Author: wx
 * @Date: 2023-05-09 15:23:07
 */
@Service
@Slf4j
public class ScheduleCycleServiceImpl extends ServiceImpl<ScheduleCycleMapper, ScheduleCycle> implements ScheduleCycleService {

    @Autowired
    private ScheduleCycleMapper scheduleCycleMapper;

    @Autowired
    private StationManagementService stationManagementService;

    /**
     * 国家字典
     */
//    private static final Integer COUNTRY_DIST = 317;
    @Value("${dict.country}")
    private String DICT_URL;

    /**
     * @Description: 分页
     * @Param: query
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-09 15:23:07
     */
    @Override
    public Page page(Query query) {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.add_time");
            query.setAsc(Boolean.FALSE);
        }
        ScheduleCycle param = BeanUtil.mapToBean(query.getCondition(), ScheduleCycle.class, false);
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
//        param.setPlatformCode(secruityUser.getUserName());
        query.setRecords(scheduleCycleMapper.pageScheduleCycle(query, param));
        return query;
    }

    /**
     * @Description: 详情
     * @Param: id
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-09 15:23:07
     */
    @Override
    public R info(String id) {
        return new R<>(scheduleCycleMapper.infoScheduleCycle(id));
    }

    /**
     * @Description: 保存
     * @Param: param
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-09 15:23:07
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R save(ScheduleCycle param) {
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
//        param.setPlatformCode(secruityUser.getUserName());
        param.setAddTime(LocalDateTime.now());
        param.setAddWho(secruityUser.getUserName());
        param.setAddWhoName(secruityUser.getRealName());
        scheduleCycleMapper.saveScheduleCycle(param);
        return new R<>();
    }

    /**
     * @Description: 修改
     * @Param: param
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-09 15:23:07
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R update(ScheduleCycle param) {
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
//        param.setPlatformCode(secruityUser.getUserName());
        param.setUpdateTime(LocalDateTime.now());
        param.setUpdateWho(secruityUser.getUserName());
        param.setUpdateWhoName(secruityUser.getRealName());
        scheduleCycleMapper.updateScheduleCycle(param);
        return new R<>();
    }

    /**
     * @Description: 删除
     * @Param: rowId
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-09 15:23:07
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R delete(String id) {
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
        ScheduleCycle scheduleCycle = this.scheduleCycleMapper.infoScheduleCycle(id);
        scheduleCycle.setDeleteFlag("Y");
        scheduleCycle.setDeleteTime(LocalDateTime.now());
        scheduleCycle.setDeleteWho(secruityUser.getUserName());
        scheduleCycle.setDeleteWhoName(secruityUser.getRealName());
        scheduleCycleMapper.updateScheduleCycle(scheduleCycle);
        return new R<>();
    }

    /**
     * @Description: 列表
     * @Param: param
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-09 15:23:07
     */
    @Override
    public R list(ScheduleCycle param) {
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
        return new R<>(scheduleCycleMapper.listScheduleCycle(param));
    }

    /**
     * @Description: 导入班期
     * @Param: param, file
     * @Return: com.huazheng.tunny.common.core.util.R
     * @Author: wx
     * @Date: 2023/5/12 15:13
     */
    @Override
    public R importExcel(ScheduleCycle param, MultipartFile file) {
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
        LocalDateTime now = LocalDateTime.now();
        // platformCode 前端传入
        if (file.isEmpty()) {
            return new R<>(1, false, null, "文件不能为空!");
        }
        try {

            List<ScheduleCycle> importList = this.readExcel(file.getOriginalFilename(), file.getInputStream());

            // 查询所有站点;
            List<StationManagement> stationManagements = stationManagementService.selectStationManagementList(new StationManagement());

            // 查询所有国家
            RequestAttributes ra = RequestContextHolder.getRequestAttributes();
            ServletRequestAttributes sra = (ServletRequestAttributes) ra;
            HttpServletRequest request = sra.getRequest();
            String authorization = request.getHeader("Authorization");

            ResponseBody responseBody = HttpUtil.get(DICT_URL, authorization);
            String json = responseBody.string();
            List<DiceDTO> countryList = JSON.parseArray(json, DiceDTO.class);

            importList.forEach(l -> {
                l.setPlatformCode(param.getPlatformCode());
                l.setAddWho(secruityUser.getUserName());
                l.setAddWhoName(secruityUser.getRealName());
                l.setAddTime(now);

                // 文件中的 始发站, 口岸, 目的国字段皆为站编, 需要查询名称存储;
                // 始发站
                stationManagements.stream()
                        .filter(f -> f.getStationCode().equals(l.getDepartureStationCode()))
                        .findFirst()
                        .ifPresent(o -> {
                            l.setDepartureStation(o.getStationName());
//                            l.setDestinationCountryCode(o.getStationCode());
                        });
                // 口岸
                stationManagements.stream()
                        .filter(f -> "1".equals(f.getIsPort()))
                        .filter(f -> f.getStationCode().equals(l.getPortStationCode()))
                        .findFirst()
                        .ifPresent(o -> {
                            l.setPortStation(o.getStationName());
//                            l.setPortStationCode(o.getStationCode());
                        });
                // 目的国
                countryList.stream()
                        .filter(f -> l.getDestinationCountryCode().equals(f.getCode()))
                        .findFirst()
                        .ifPresent(o -> {
                            l.setDestinationCountry(String.valueOf(o.getName()));
//                            l.setDestinationCountryCode(String.valueOf(o.get("code")));
                        });
            });
            // 批量保存数据;
            this.insertBatch(importList);
        } catch (Exception e) {
            log.error("文件导入异常: " + e.getMessage());
            e.printStackTrace();
        }
        return new R<>();
    }

    public List<ScheduleCycle> readExcel(String fileName, InputStream inputStream) throws IOException {
        R r = new R();
        boolean ret = isXls(fileName);
        Workbook workbook = null;
        // 根据后缀创建不同的对象
        if (ret) {
            workbook = new HSSFWorkbook(inputStream);
        } else {
            workbook = new XSSFWorkbook(inputStream);
        }
        Sheet sheet = getAccuracyContextNum(workbook);
        // 得到标题行(第二行)
        Row titleRow = sheet.getRow(1);

        int lastRowNum = sheet.getLastRowNum();

        List<ScheduleCycle> list = new ArrayList<>();

        if (lastRowNum > 0) {
            //从第二行开始读取
            for (int i = 1; i <= lastRowNum; i++) {
                ScheduleCycle scheduleCycle = new ScheduleCycle();
                Row row = sheet.getRow(i);
                // 序号
//                row.getCell(0).setCellType(CellType.STRING);
                // 日期
                Date scheduleCycleDate = row.getCell(1).getDateCellValue();
                scheduleCycle.setScheduleCycleDate(scheduleCycleDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
                // 始发站
                Double departureStationCode = row.getCell(2).getNumericCellValue();
                scheduleCycle.setDepartureStationCode(String.valueOf(departureStationCode.intValue()));
                // 口岸
                Double portStationCode = row.getCell(3).getNumericCellValue();
                scheduleCycle.setPortStationCode(String.valueOf(portStationCode.intValue()));
                // 目的国
                String destinationCountryCode = row.getCell(4).getStringCellValue();
                scheduleCycle.setDestinationCountryCode(destinationCountryCode);

                list.add(scheduleCycle);
            }
        }
        workbook.close();
        return list;
    }

    /**
     * 判断导入文件格式
     *
     * @param fileName
     * @return
     */
    public static boolean isXls(String fileName) {
        // (?i)忽略大小写
        if (fileName.matches("^.+\\.(?i)(xls)$")) {
            return true;
        } else if (fileName.matches("^.+\\.(?i)(xlsx)$")) {
            return false;
        } else {
            throw new RuntimeException("格式不对");
        }
    }

    //获取准确的文件行数
    public Sheet getAccuracyContextNum(Workbook workbook) {
        // 取第一个sheet
        Sheet sheet = workbook.getSheetAt(0);
        // 删除空行
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            // 删除空行
            if (this.isRowEmpty(row)) {
                int lastRowNum = sheet.getLastRowNum();
                if (i >= 0 && i < lastRowNum) {
                    sheet.shiftRows(i + 1, lastRowNum, -1);// 将行号为i+1一直到行号为lastRowNum的单元格全部上移一行，以便删除i行
                }
                if (i == lastRowNum) {
                    if (row != null) {
                        sheet.removeRow(row);
                    }
                }
                i--;
            }
        }
        return sheet;
    }

    public boolean isRowEmpty(Row row) {
        if (row != null) {
            for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
                Cell cell = row.getCell(c);
                if (cell != null) {
                    cell.setCellType(CellType.STRING);
                }
                if (cell != null && !"".equals(cell.getStringCellValue())) {
                    return false;//不是空行
                }
            }
        }
        return true;
    }

    /**
     * @Description: 导出模板
     * @Param: response
     * @Return: void
     * @Author: wx
     * @Date: 2023/5/15 13:23
     */
    @Override
    public void exportTemplate(HttpServletResponse response) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("Sheet1");
        sheet.setDefaultColumnWidth(20);
        OutputStream outputStream = null;
        try {
            // 创建表体
            String[] title = {"序号", "日期", "始发站", "口岸", "目的国"};
            XSSFCellStyle cellStyle = workbook.createCellStyle();
            for (int i = 0; i < 1; i++) {
                Row row = sheet.createRow(i);
                row.setHeightInPoints(20);
                for (int j = 0; j < 5; j++) {
                    Cell cell = row.createCell(j);
                    // 上边框
                    cellStyle.setBorderTop(BorderStyle.THIN);
                    // 左边框
                    cellStyle.setBorderLeft(BorderStyle.THIN);
                    // 右边框
                    cellStyle.setBorderRight(BorderStyle.THIN);
                    // 下边框
                    cellStyle.setBorderBottom(BorderStyle.THIN);
                    cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                    cellStyle.setAlignment(HorizontalAlignment.CENTER);
                    cellStyle.setLocked(false);
                    cell.setCellStyle(cellStyle);
                }
            }

            Row row = sheet.getRow(0);
            Font titleFont = workbook.createFont();
            titleFont.setBold(true);
            titleFont.setFontHeightInPoints((short) 12);
            CellStyle titleCellStyle = workbook.createCellStyle();
            titleCellStyle.cloneStyleFrom(row.getCell(0).getCellStyle());
            titleCellStyle.setFont(titleFont);
            titleCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            titleCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            titleCellStyle.setLocked(true);
            row.setHeightInPoints(25);
            for (int i = 0; i < title.length; i++) {
                Cell cell = row.getCell(i);
                cell.setCellValue(title[i]);
                cell.setCellStyle(titleCellStyle);
            }


            String fileName = "班期导入模板.xlsx";
            response.setHeader("Content-Disposition", "attachment; filename*=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setContentType("application/vnd.ms-excel");
            outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
        } catch (Exception e) {
            log.error("生成班期导入模板异常!");
            e.printStackTrace();
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("生成班期导入模板异常!");
                }
            }
        }
    }
}
