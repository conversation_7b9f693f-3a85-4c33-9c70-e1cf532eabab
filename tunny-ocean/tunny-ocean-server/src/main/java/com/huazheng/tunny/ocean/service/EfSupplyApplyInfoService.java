package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfSupplyApplyInfo;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 补充质押通知表 服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-08 15:34:32
 */
public interface EfSupplyApplyInfoService extends IService<EfSupplyApplyInfo> {
    /**
     * 查询补充质押通知表信息
     *
     * @param rowId 补充质押通知表ID
     * @return 补充质押通知表信息
     */
    public EfSupplyApplyInfo selectEfSupplyApplyInfoById(String rowId);

    /**
     * 查询补充质押通知表列表
     *
     * @param efSupplyApplyInfo 补充质押通知表信息
     * @return 补充质押通知表集合
     */
    public List<EfSupplyApplyInfo> selectEfSupplyApplyInfoList(EfSupplyApplyInfo efSupplyApplyInfo);


    /**
     * 分页模糊查询补充质押通知表列表
     * @return 补充质押通知表集合
     */
    public Page selectEfSupplyApplyInfoListByLike(Query query);



    /**
     * 新增补充质押通知表
     *
     * @param efSupplyApplyInfo 补充质押通知表信息
     * @return 结果
     */
    public int insertEfSupplyApplyInfo(EfSupplyApplyInfo efSupplyApplyInfo);

    /**
     * 修改补充质押通知表
     *
     * @param efSupplyApplyInfo 补充质押通知表信息
     * @return 结果
     */
    public String updateEfSupplyApplyInfo(EfSupplyApplyInfo efSupplyApplyInfo);

    /**
     * 删除补充质押通知表
     *
     * @param rowId 补充质押通知表ID
     * @return 结果
     */
    public int deleteEfSupplyApplyInfoById(String rowId);

    /**
     * 批量删除补充质押通知表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfSupplyApplyInfoByIds(Integer[] rowIds);

    String syncSupplyApplyInfo(EfSupplyApplyInfo efSupplyApplyInfo);

    String auditSupplyApply(EfSupplyApplyInfo efSupplyApplyInfo);

    String syncSupplyConfirmInfo(EfSupplyApplyInfo efSupplyApplyInfo);
}

