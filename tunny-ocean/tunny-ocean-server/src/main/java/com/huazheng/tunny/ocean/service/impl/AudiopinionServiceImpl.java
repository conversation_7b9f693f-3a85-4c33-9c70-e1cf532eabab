package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.AudiopinionMapper;
import com.huazheng.tunny.ocean.api.entity.Audiopinion;
import com.huazheng.tunny.ocean.service.AudiopinionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("audiopinionService")
public class AudiopinionServiceImpl extends ServiceImpl<AudiopinionMapper, Audiopinion> implements AudiopinionService {

    @Autowired
    private AudiopinionMapper audiopinionMapper;

    public AudiopinionMapper getAudiopinionMapper() {
        return audiopinionMapper;
    }

    public void setAudiopinionMapper(AudiopinionMapper audiopinionMapper) {
        this.audiopinionMapper = audiopinionMapper;
    }

    /**
     * 查询审核意见表信息
     *
     * @param rowId 审核意见表ID
     * @return 审核意见表信息
     */
    @Override
    public Audiopinion selectAudiopinionById(String rowId)
    {
        return audiopinionMapper.selectAudiopinionById(rowId);
    }

    /**
     * 查询审核意见表列表
     *
     * @param audiopinion 审核意见表信息
     * @return 审核意见表集合
     */
    @Override
    public List<Audiopinion> selectAudiopinionList(Audiopinion audiopinion)
    {
        return audiopinionMapper.selectAudiopinionList(audiopinion);
    }


    /**
     * 分页模糊查询审核意见表列表
     * @return 审核意见表集合
     */
    @Override
    public Page selectAudiopinionListByLike(Query query)
    {
        Audiopinion audiopinion =  BeanUtil.mapToBean(query.getCondition(), Audiopinion.class,false);
        query.setRecords(audiopinionMapper.selectAudiopinionListByLike(query,audiopinion));
        return query;
    }

    /**
     * 新增审核意见表
     *
     * @param audiopinion 审核意见表信息
     * @return 结果
     */
    @Override
    public int insertAudiopinion(Audiopinion audiopinion)
    {
        return audiopinionMapper.insertAudiopinion(audiopinion);
    }

    /**
     * 修改审核意见表
     *
     * @param audiopinion 审核意见表信息
     * @return 结果
     */
    @Override
    public int updateAudiopinion(Audiopinion audiopinion)
    {
        return audiopinionMapper.updateAudiopinion(audiopinion);
    }


    /**
     * 删除审核意见表
     *
     * @param rowId 审核意见表ID
     * @return 结果
     */
    public int deleteAudiopinionById(String rowId)
    {
        return audiopinionMapper.deleteAudiopinionById( rowId);
    };


    /**
     * 批量删除审核意见表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteAudiopinionByIds(Integer[] rowIds)
    {
        return audiopinionMapper.deleteAudiopinionByIds( rowIds);
    }

}
