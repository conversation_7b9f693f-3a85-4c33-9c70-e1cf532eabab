package com.huazheng.tunny.ocean.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.gson.JsonObject;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.RequesheaderDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.vo.FdCostVO;
import com.huazheng.tunny.ocean.service.*;
import com.huazheng.tunny.ocean.util.SignatureUtil;
import com.huazheng.tunny.ocean.util.spiltlistutil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;

import java.util.UUID;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * sm2
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:30:28
 */
@RestController
@RequestMapping("/signature")
@Slf4j
public class SignatureController {
    //E融公钥
    @Value("${path.EF_KEY_TEST}")
    private String efKey;
    //中钞公钥
    @Value("${path.ZC_KEY_TEST}")
    private String zcKey;
    //上合公钥
    @Value("${path.SH_KEY_TEST}")
    private String shKey;
    //上合公钥2
    @Value("${path.SH_KEY_TEST2}")
    private String shKey2;
    //华正公钥
    @Value("${path.KEY_TEST}")
    private String key;
    //华正私钥
    @Value("${path.VER_KEY_TEST}")
    private String verKey;
    @Value("${path.ENT_CODE}")
    private String entCode;
    @Value("${path.URL_TEST}")
    private String url;

    @Value("${path.EF_URL}")
    private String eFurl;

    @GetMapping("/createKey")
    public R createKey() {

        return new R<>(SignatureUtil.createKey());
    }

    //向中钞发起请求
    public String doPost(String method,String content){
        try {
            FiDoPostDetail detail = new FiDoPostDetail();
            detail.setMethod(method);
            content = SignatureUtil.crypt(zcKey, content);
            detail.setContent(content);
            String reqId = UUID.randomUUID().toString();
            detail.setReqId(reqId);
            long timestamp = System.currentTimeMillis();
            detail.setTimestamp(timestamp);
            detail.setEntCode(entCode);
            String signature = SignatureUtil.sign(verKey, (method + content + reqId + timestamp + entCode));
            detail.setSignature(signature);
            String json = JSONUtil.parseObj(detail, false).toStringPretty();
            System.out.println(json);
            String response= HttpUtil.post(url+method,json);
            JSONObject jsonObject = JSONUtil.parseObj(response);
            String o = String.valueOf(jsonObject.get("content"));
            String data = SignatureUtil.decrypt(verKey,o);
            return data;
        } catch (Exception e) {
            e.printStackTrace();
            return String.valueOf(e.getMessage());
        }
    }

    //向上合发起请求
    public String doPostSh(String method,String content){
        try {
            FiDoPostDetail detail = new FiDoPostDetail();
            detail.setMethod(method);
            content = SignatureUtil.crypt(shKey, content);
            detail.setContent(content);
            String reqId = UUID.randomUUID().toString();
            detail.setReqId(reqId);
            long timestamp = System.currentTimeMillis();
            detail.setTimestamp(timestamp);
            detail.setEntCode(entCode);
            String signature = SignatureUtil.sign(verKey, (method + content + reqId + timestamp + entCode));
            detail.setSignature(signature);
            String json = JSONUtil.parseObj(detail, false).toStringPretty();
            System.out.println(json);
            String response= HttpUtil.post(url+method,json);
            JSONObject jsonObject = JSONUtil.parseObj(response);
            String o = String.valueOf(jsonObject.get("content"));
            String data = SignatureUtil.decrypt(verKey,o);
            return data;
        } catch (Exception e) {
            e.printStackTrace();
            return String.valueOf(e.getMessage());
        }
    }

    //向E融发起请求
    public String doPostEf(String method,String content){
        try {
            FiDoPostDetail detail = new FiDoPostDetail();
            detail.setMethod(method);
            content = SignatureUtil.crypt(efKey, content);
            detail.setContent(content);
            String reqId = UUID.randomUUID().toString();
            detail.setReqId(reqId);
            long timestamp = System.currentTimeMillis();
            detail.setTimestamp(timestamp);
            detail.setEntCode(entCode);
            String signature = SignatureUtil.sign(verKey, (method + content + reqId + timestamp + entCode));
            detail.setSignature(signature);
            String json = JSONUtil.parseObj(detail, false).toStringPretty();
            System.out.println(json);
            String response= HttpUtil.post(eFurl+method,json);
            JSONObject jsonObject = JSONUtil.parseObj(response);
            String o = String.valueOf(jsonObject.get("content"));
            String data = SignatureUtil.decrypt(verKey,o);
            return data;
        } catch (Exception e) {
            e.printStackTrace();
            return String.valueOf(e.getMessage());
        }
    }

    //解析中钞JSONObject
    public String getPost(JSONObject jsonObject){
        try {
            String content = String.valueOf(jsonObject.get("content"));
            content = SignatureUtil.decrypt(verKey, content);
//            JSONObject dataObject = JSONUtil.parseObj(content);
//            String data = String.valueOf(dataObject.get("data"));
            return content;
        } catch (Exception e) {
            e.printStackTrace();
            return String.valueOf(e.getMessage());
        }
    }

    @PostMapping("/getPostStringSh")
    public String getPostStringSh(@RequestBody JSONObject jsonObject){
        try {
//            JSONObject jsonObject = JSONUtil.parseObj(json);
            String content = String.valueOf(jsonObject.get("content"));
            content = SignatureUtil.decrypt(verKey, content);
//            JSONObject dataObject = JSONUtil.parseObj(content);
//            String data = String.valueOf(dataObject.get("data"));
            return content;
        } catch (Exception e) {
            e.printStackTrace();
            return String.valueOf(e.getMessage());
        }
    }

    //返回加密
    public String returnPost(String method,String content) {
        FiDoPostDetail detail = new FiDoPostDetail();
        try {
            detail.setMethod(method);
            content = SignatureUtil.crypt(zcKey, content);
            detail.setContent(content);
            String reqId = UUID.randomUUID().toString();
            detail.setReqId(reqId);
            long timestamp = System.currentTimeMillis();
            detail.setTimestamp(timestamp);
            detail.setEntCode(entCode);
            String signature = SignatureUtil.sign(verKey, (method + content + reqId + timestamp + entCode));
            detail.setSignature(signature);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String json = JSONUtil.parseObj(detail, false).toStringPretty();
        return json;
    }

    public String returnPostSh(String method,String content) {
        FiDoPostDetail detail = new FiDoPostDetail();
        try {
            detail.setMethod(method);
            content = SignatureUtil.crypt(shKey, content);
            detail.setContent(content);
            String reqId = UUID.randomUUID().toString();
            detail.setReqId(reqId);
            long timestamp = System.currentTimeMillis();
            detail.setTimestamp(timestamp);
            detail.setEntCode(entCode);
            String signature = SignatureUtil.sign(verKey, (method + content + reqId + timestamp + entCode));
            detail.setSignature(signature);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String json = JSONUtil.parseObj(detail, false).toStringPretty();
        return json;
    }

    public String returnPostEf(String method,String content) {
        FiDoPostDetail detail = new FiDoPostDetail();
        try {
            detail.setMethod(method);
            content = SignatureUtil.crypt(efKey, content);
            detail.setContent(content);
            String reqId = UUID.randomUUID().toString();
            detail.setReqId(reqId);
            long timestamp = System.currentTimeMillis();
            detail.setTimestamp(timestamp);
            detail.setEntCode(entCode);
            String signature = SignatureUtil.sign(verKey, (method + content + reqId + timestamp + entCode));
            detail.setSignature(signature);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String json = JSONUtil.parseObj(detail, false).toStringPretty();
        return json;
    }

    public String returnPostSh2(String method,String content) {
        FiDoPostDetail detail = new FiDoPostDetail();
        try {
            detail.setMethod(method);
            //上合
            content = SignatureUtil.crypt(shKey2, content);
            //华正
//            content = SignatureUtil.crypt(key, content);
            detail.setContent(content);
            String reqId = UUID.randomUUID().toString();
            detail.setReqId(reqId);
            long timestamp = System.currentTimeMillis();
            detail.setTimestamp(timestamp);
            detail.setEntCode(entCode);
            String signature = SignatureUtil.sign(verKey, (method + content + reqId + timestamp + entCode));
            detail.setSignature(signature);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String json = JSONUtil.parseObj(detail, false).toStringPretty();
        return json;
    }

    @PostMapping("/returnPostShTest")
    public String returnPostShTest(@RequestBody Object object) {
        String content = JSONUtil.toJsonStr(object);
        JSONObject jsonObject = JSONUtil.parseObj(content);
        String m = jsonObject.getStr("method");

        FiDoPostDetail detail = new FiDoPostDetail();
        try {
            detail.setMethod(m);
            content = SignatureUtil.crypt(key, content);
            detail.setContent(content);
            String reqId = UUID.randomUUID().toString();
            detail.setReqId(reqId);
            long timestamp = System.currentTimeMillis();
            detail.setTimestamp(timestamp);
            detail.setEntCode(entCode);
            String signature = SignatureUtil.sign(verKey, (m + content + reqId + timestamp + entCode));
            detail.setSignature(signature);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String json = JSONUtil.parseObj(detail, false).toStringPretty();
        return json;
    }
    @PostMapping("/returnPostShTest2")
    public String returnPostShTest2(@RequestBody Object object) {
        String content = JSONUtil.toJsonStr(object);

        FiDoPostDetail detail = new FiDoPostDetail();
        try {
            content = SignatureUtil.crypt(key, content);
            detail.setContent(content);
            String reqId = UUID.randomUUID().toString();
            detail.setReqId(reqId);
            long timestamp = System.currentTimeMillis();
            detail.setTimestamp(timestamp);
            detail.setEntCode(entCode);
            String signature = SignatureUtil.sign(verKey, ( content + reqId + timestamp + entCode));
            detail.setSignature(signature);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String json = JSONUtil.parseObj(detail, false).toStringPretty();
        return json;
    }

    @PostMapping("/returnBookingAndWaybillTest")
    public String returnBookingAndWaybillTest(@RequestBody RequesheaderDTO header) {
        String content = JSONUtil.toJsonStr(header);
        FiDoPostDetail detail = new FiDoPostDetail();
        try {
            detail.setMethod("/bookingrequesheader/saveBookingAndWaybill");
            content = SignatureUtil.crypt(key, content);
            detail.setContent(content);
            String reqId = UUID.randomUUID().toString();
            detail.setReqId(reqId);
            long timestamp = System.currentTimeMillis();
            detail.setTimestamp(timestamp);
            detail.setEntCode(entCode);
            String signature = SignatureUtil.sign(key, ("/waybillcontainerinfo" + content + reqId + timestamp + entCode));
            detail.setSignature(signature);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String json = JSONUtil.parseObj(detail, false).toStringPretty();
        return json;
    }

    @PostMapping("/test")
    public R test() {
        String response= HttpUtil.post("http://crfront.brop.cn/qiluhao/serving/v1/ent/entInfo/warrant/filter", "{\n" +
                "    \"content\": \"94845457D5526B7A0E2A78EECE4EEC2D5BAC506076B38CE61349BD9F7285C379F41CD2D80BBA255B460E81024641A3131629C888320EC86204A7B55B7F78325D08D1BC42C73D3D480AB76400144992250202CFDD81F90AB90AE472B1E138F8A1BCF06710BC6B0C78E10D9A8E15A7970942F41A5A9F99907663717003D263859B4E79182B42A20AAB35C2EB21C9E94DD7B8B83DA02D2AE933E9\",\n" +
                "    \"entCode\": \"91370102MA3RB5UM18\",\n" +
                "    \"method\": \"/v1/ent/entInfo/warrant/filter\",\n" +
                "    \"reqId\": \"48360697-7d55-4880-ae77-2cc3273a69b8\",\n" +
                "    \"signature\": \"HCLN78e9t9TLCKXlXIspbxPnP1BhRdXpovZ5/fODxV+2bEX1ZhhUaVTkP03C+cCUX2Fa6OC6dfqHOF8cZJ9l4OI=\",\n" +
                "    \"timestamp\": 1648024200800\n" +
                "}");
        JSONObject jsonObject = JSONUtil.parseObj(response);
        String o = String.valueOf(jsonObject.get("content"));
        try {
            String data = SignatureUtil.decrypt("AA4A52C60200B3BC82BD0275B8ACA10A1727FD0BF167CC58860FF62E00AC4277",o);
            JSONObject dataObject = JSONUtil.parseObj(data);
            String dataStr = String.valueOf(dataObject.get("data"));
            JSONArray dataArray = JSONUtil.parseArray(dataStr);
            List<FiEnterpriseAuthShow> list = JSONUtil.toList(dataArray,FiEnterpriseAuthShow.class);
            return new R<>(list);
        } catch (InvalidKeyException e) {
            e.printStackTrace();
            return new R<>(String.valueOf(e.getMessage()));
        }
    }

    @PostMapping("/test2")
    public R test2(@RequestBody JSONObject jsonObject) {

        return new R<>(jsonObject);
    }


}
