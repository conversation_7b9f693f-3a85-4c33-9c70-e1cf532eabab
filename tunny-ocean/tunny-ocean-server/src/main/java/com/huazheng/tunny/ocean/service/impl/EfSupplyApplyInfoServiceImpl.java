package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.EfSupplyApplyInfoDTO;
import com.huazheng.tunny.ocean.api.dto.EfSupplyApplyListDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.controller.SignatureController;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.EfSupplyApplyInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import java.util.UUID;

@Service("efSupplyApplyInfoService")
public class EfSupplyApplyInfoServiceImpl extends ServiceImpl<EfSupplyApplyInfoMapper, EfSupplyApplyInfo> implements EfSupplyApplyInfoService {

    @Autowired
    private EfSupplyApplyInfoMapper efSupplyApplyInfoMapper;
    @Autowired
    private SignatureController signatureController;
    @Autowired
    private EfWarehouseApplyMapper efWarehouseApplyMapper;
    @Autowired
    private EfSupplyApplyListMapper efSupplyApplyListMapper;
    @Autowired
    private EfWarehouseMapper efWarehouseMapper;
    @Autowired
    private EfWarehouseListMapper efWarehouseListMapper;
    @Autowired
    private EfWaybillMapper efWaybillMapper;
    @Autowired
    private EfWaybillGoodsMapper efWaybillGoodsMapper;
    @Autowired
    private EfWarehouseGoodsMapper efWarehouseGoodsMapper;
    @Autowired
    private EfWarehouseLogMapper efWarehouseLogMapper;

    DecimalFormat df = new DecimalFormat("#.00");
    /**
     * 查询补充质押通知表信息
     *
     * @param rowId 补充质押通知表ID
     * @return 补充质押通知表信息
     */
    @Override
    public EfSupplyApplyInfo selectEfSupplyApplyInfoById(String rowId)
    {
        final EfSupplyApplyInfo efSupplyApplyInfo = efSupplyApplyInfoMapper.selectEfSupplyApplyInfoById(rowId);
        EfSupplyApplyList efSupplyApplyList = new EfSupplyApplyList();
        efSupplyApplyList.setSupplycode(efSupplyApplyInfo.getSupplyCode());
        efSupplyApplyList.setAssetCode(efSupplyApplyInfo.getAssetCode());
        efSupplyApplyList.setDeleteFlag("N");
        final List<EfSupplyApplyList> efSupplyApplyLists = efSupplyApplyListMapper.selectEfSupplyApplyListList(efSupplyApplyList);
        efSupplyApplyInfo.setSupplyapplyList(efSupplyApplyLists);

        final EfWarehouseApply efWarehouseApply = efWarehouseApplyMapper.selectEfWarehouseApplyByAssetCode(efSupplyApplyInfo.getAssetCode());
        if(efWarehouseApply!=null){
            efSupplyApplyInfo.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
        }
        return efSupplyApplyInfo;
    }

    /**
     * 查询补充质押通知表列表
     *
     * @param efSupplyApplyInfo 补充质押通知表信息
     * @return 补充质押通知表集合
     */
    @Override
    public List<EfSupplyApplyInfo> selectEfSupplyApplyInfoList(EfSupplyApplyInfo efSupplyApplyInfo)
    {
        return efSupplyApplyInfoMapper.selectEfSupplyApplyInfoList(efSupplyApplyInfo);
    }


    /**
     * 分页模糊查询补充质押通知表列表
     * @return 补充质押通知表集合
     */
    @Override
    public Page selectEfSupplyApplyInfoListByLike(Query query)
    {
        EfSupplyApplyInfo efSupplyApplyInfo =  BeanUtil.mapToBean(query.getCondition(), EfSupplyApplyInfo.class,false);
        final List<String> roles = SecurityUtils.getRoles();
        if(CollUtil.isNotEmpty(roles)){
            for (String role:roles
            ) {
                if("CY_MANAGE_ADMIN".equals(role)||"CY_BIZ_ADMIN".equals(role)
                        ||"MANAGE_SUPER_ADMIN".equals(role)||"ROLE_ADMIN".equals(role)
                        ||"CY_FIN_ADMIN".equals(role)||"FINANCIAL_TEMPORARY".equals(role)){
                    efSupplyApplyInfo.setEntSocialCode(null);
                }
            }
        }
        query.setRecords(efSupplyApplyInfoMapper.selectEfSupplyApplyInfoListByLike(query,efSupplyApplyInfo));
        return query;
    }

    /**
     * 新增补充质押通知表
     *
     * @param efSupplyApplyInfo 补充质押通知表信息
     * @return 结果
     */
    @Override
    public int insertEfSupplyApplyInfo(EfSupplyApplyInfo efSupplyApplyInfo)
    {
        return efSupplyApplyInfoMapper.insertEfSupplyApplyInfo(efSupplyApplyInfo);
    }

    /**
     * 修改补充质押通知表
     *
     * @param efSupplyApplyInfo 补充质押通知表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateEfSupplyApplyInfo(EfSupplyApplyInfo efSupplyApplyInfo)
    {
        //插入操作记录
        EfWarehouseLog log = new EfWarehouseLog();
        String remark = "";

        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(efSupplyApplyInfo!=null){
            final EfWarehouseApply efWarehouseApply = efWarehouseApplyMapper.selectEfWarehouseApplyByAssetCode(efSupplyApplyInfo.getAssetCode());
            if("CONFIRM".equals(efSupplyApplyInfo.getSupplyOperate())){
                log.setControlType("客户同意补仓");

                final List<EfSupplyApplyList> supplyapplyList = efSupplyApplyInfo.getSupplyapplyList();
                if(CollUtil.isNotEmpty(supplyapplyList)){
                    BigDecimal val = BigDecimal.valueOf(0);
                    BigDecimal valCny = BigDecimal.valueOf(0);
                    String currency = "";
                    EfWarehouseGoods goods = new EfWarehouseGoods();
                    goods.setWarehouseReceiptNo(supplyapplyList.get(0).getWarehouseCode());
                    goods.setDeleteFlag("N");
                    final List<EfWarehouseGoods> efWarehouseGoods = efWarehouseGoodsMapper.selectEfWarehouseGoodsList(goods);
                    if(CollUtil.isNotEmpty(efWarehouseGoods)){
                        for (EfWarehouseGoods good:efWarehouseGoods
                             ) {
                           if(StrUtil.isNotEmpty(good.getGoodsName())){
                               efSupplyApplyInfo.setSupplyGoodsName(good.getGoodsName());
                           }
                        }
                    }

                    List<EfSupplyApplyListDTO> list = new ArrayList<>();
                    for (EfSupplyApplyList efSupplyApplyList:supplyapplyList
                    ) {
                        /*valCny += efSupplyApplyList.getValuationCny();
                        final int valuation = (int)Math.round(efSupplyApplyList.getValuationCny()*100);
                        val += valuation;
                        efSupplyApplyList.setValuation(valuation);
                        if(!currency.contains(efSupplyApplyList.getValuationCurrency())){
                            if("".equals(currency)){
                                currency = efSupplyApplyList.getValuationCurrency();
                            }else{
                                currency = currency + "," + efSupplyApplyList.getValuationCurrency();
                            }
                        }*/

                        EfSupplyApplyListDTO efSupplyApplyListDTO = new EfSupplyApplyListDTO();
                        efSupplyApplyListDTO.setSupplyWarehouseCode(efSupplyApplyList.getWarehouseCode());
                        efSupplyApplyListDTO.setSupplyWarehouseSupervisionNo(efSupplyApplyList.getWarehouseSupervisionNo());
//                        efSupplyApplyListDTO.setSupplyGoodsValuation(valuation);
//                        efSupplyApplyListDTO.setSupplyGoodsValuationCurrency(efSupplyApplyList.getValuationCurrency());

                        list.add(efSupplyApplyListDTO);
                    }

                    EfSupplyApplyInfoDTO dto = new EfSupplyApplyInfoDTO();
                    dto.setSupplyCode(efSupplyApplyInfo.getSupplyCode());
                    dto.setAssetCode(efSupplyApplyInfo.getAssetCode());

                    LocalDate currentDate = LocalDate.now();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    String formattedDate = currentDate.format(formatter);
                    dto.setSupplyTime(formattedDate);

                    dto.setSupplyWarehouseInfoList(list);
                    dto.setSupplyOperate("CONFIRM");
                    if(efWarehouseApply!=null){
                        dto.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                    }
                    from = "E融平台:";
                    //调用E融接口，插入申请数据
                    String json = JSONUtil.parseObj(dto, true).toStringPretty();
                    final String result = signatureController.doPostEf("/efsupplyapplyinfo/updateEfSupplyApplyInfo", json);
                    JSONObject dataObject = JSONUtil.parseObj(result);
                    flag = Boolean.valueOf(String.valueOf(dataObject.get("b")));
                    msg = String.valueOf(dataObject.get("msg"));
                    if(flag){
                        efSupplyApplyInfo.setSupplyGoodsValuation(val);
                        efSupplyApplyInfo.setSupplyGoodsValuationCny(valCny);
                        efSupplyApplyInfo.setSupplyGoodsValuationCurrency(currency);

                        efSupplyApplyInfo.setSupplyTime(formattedDate);

                        //待仓储审核
                        efSupplyApplyInfo.setSupplyState("1");
                        efSupplyApplyInfo.setUpdateTime(LocalDateTime.now());
                        efSupplyApplyInfo.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                        efSupplyApplyInfo.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());


                        EfSupplyApplyList del = new EfSupplyApplyList();
                        del.setSupplycode(efSupplyApplyInfo.getAssetCode());
                        del.setAssetCode(efSupplyApplyInfo.getSupplyCode());
                        del.setDeleteFlag("Y");
                        del.setDeleteTime(LocalDateTime.now());
                        del.setDeleteWho(SecurityUtils.getUserInfo().getUserName());
                        del.setDeleteWhoName(SecurityUtils.getUserInfo().getRealName());
                        efSupplyApplyListMapper.deleteList(del);

                        for (EfSupplyApplyList efSupplyApplyList:supplyapplyList
                        ) {
                            //仓单货物信息
                            final List<EfWarehouseGoods> goodsInfoList = efSupplyApplyList.getGoodsInfoList();
                            if(CollUtil.isNotEmpty(goodsInfoList)){

                                for (EfWarehouseGoods goodsInfo:goodsInfoList
                                ) {
                                    //货物运单信息
                                    EfWaybill del3 = new EfWaybill();
                                    del3.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                                    del3.setWarehouseReceiptNo(efSupplyApplyList.getWarehouseCode());
                                    del3.setGoodsNo(goodsInfo.getGoodsNo());
                                    del3.setUpdateTime(LocalDateTime.now());
                                    del3.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                                    del3.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                                    efWaybillMapper.deleteEfWaybills(del3);
                                    final EfWaybill wayBillInfo = goodsInfo.getWayBillInfo();
                                    if(wayBillInfo!=null){
                                        //运单箱信息
                                        EfWaybillGoods del2 = new EfWaybillGoods();
                                        del2.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                                        del2.setWarehouseReceiptNo(efSupplyApplyList.getWarehouseCode());
                                        del2.setWaybillNo(wayBillInfo.getWaybillNo());
                                        del2.setOrderNo(wayBillInfo.getOrderNo());
                                        del2.setGoodsNo(goodsInfo.getGoodsNo());
                                        del2.setUpdateTime(LocalDateTime.now());
                                        del2.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                                        del2.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                                        efWaybillGoodsMapper.deleteEfWaybillGoods(del2);
                                        final List<EfWaybillGoods> wayBillGoodsInfoList = wayBillInfo.getWayBillGoodsInfoList();
                                        if(CollUtil.isNotEmpty(wayBillGoodsInfoList)){
                                            for (EfWaybillGoods wayBillGoodsInfo:wayBillGoodsInfoList
                                            ) {
                                                wayBillGoodsInfo.setRowId(UUID.randomUUID().toString());
                                                wayBillGoodsInfo.setOrderNo(wayBillInfo.getOrderNo());
                                                wayBillGoodsInfo.setWaybillNo(wayBillInfo.getWaybillNo());
                                                wayBillGoodsInfo.setWarehouseReceiptNo(efSupplyApplyList.getWarehouseCode());
                                                wayBillGoodsInfo.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                                                wayBillGoodsInfo.setGoodsNo(goodsInfo.getGoodsNo());
                                                wayBillGoodsInfo.setAddTime(LocalDateTime.now());
                                                wayBillGoodsInfo.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                                wayBillGoodsInfo.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                                efWaybillGoodsMapper.insertEfWaybillGoods(wayBillGoodsInfo);
                                            }
                                        }
                                        wayBillInfo.setRowId(UUID.randomUUID().toString());
                                        wayBillInfo.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                                        wayBillInfo.setWarehouseReceiptNo(efSupplyApplyList.getWarehouseCode());
                                        wayBillInfo.setGoodsNo(goodsInfo.getGoodsNo());
                                        wayBillInfo.setAddTime(LocalDateTime.now());
                                        wayBillInfo.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                        wayBillInfo.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                        efWaybillMapper.insertEfWaybill(wayBillInfo);
                                    }
                                }
                            }

                            efSupplyApplyList.setRowId(UUID.randomUUID().toString());
                            efSupplyApplyList.setAssetCode(efSupplyApplyInfo.getAssetCode());
                            efSupplyApplyList.setSupplycode(efSupplyApplyInfo.getSupplyCode());
                            efSupplyApplyList.setAddTime(LocalDateTime.now());
                            efSupplyApplyList.setAddWho(SecurityUtils.getUserInfo().getUserName());
                            efSupplyApplyList.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                            efSupplyApplyListMapper.insertEfSupplyApplyList(efSupplyApplyList);

                            //改e融同步仓单表质押状态
                            EfWarehouse efWarehouse = new EfWarehouse();
                            efWarehouse.setWarehouseReceiptNo(efSupplyApplyList.getWarehouseCode());
                            efWarehouse.setPledgeStatus(1);
                            efWarehouse.setUpdateTime(LocalDateTime.now());
                            efWarehouse.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                            efWarehouse.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                            efWarehouseMapper.updateEfWarehouse(efWarehouse);
                        }
                        efSupplyApplyInfoMapper.updateEfSupplyApplyInfo(efSupplyApplyInfo);


                        //插入操作记录
                        log.setRowId(UUID.randomUUID().toString());
                        log.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                        log.setSerialNum(String.valueOf(System.currentTimeMillis()));
                        log.setControlType("提交补仓申请");
                        log.setControlTime(LocalDateTime.now());
                        log.setRemark(remark);
                        log.setAddTime(LocalDateTime.now());
                        log.setAddWho(SecurityUtils.getUserInfo().getUserName());
                        log.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                        efWarehouseLogMapper.insertEfWarehouseLog(log);
                    }

                }else{
                    msg = "没有接收到补充质押详情数据";
                    flag = false;
                }
            }else{
                log.setControlType("客户拒绝补仓");

                EfSupplyApplyInfoDTO dto = new EfSupplyApplyInfoDTO();
                dto.setSupplyCode(efSupplyApplyInfo.getSupplyCode());
                dto.setAssetCode(efSupplyApplyInfo.getAssetCode());

                LocalDate currentDate = LocalDate.now();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                String formattedDate = currentDate.format(formatter);
                dto.setSupplyTime(formattedDate);
                dto.setSupplyOperate("REFUSE");
                //调用E融接口，插入申请数据
                String json = JSONUtil.parseObj(dto, true).toStringPretty();
                final String result = signatureController.doPost("/v1/ent/warehouse/supplyGoods", json);
                JSONObject resultObject = JSONUtil.parseObj(result);
                flag = Boolean.valueOf(String.valueOf(resultObject.get("b")));
                from = "中钞平台:";
                msg = String.valueOf(resultObject.get("msg"));

                if(flag){
                    //更新申请表状态
                    //用户审核拒绝补仓
                    efSupplyApplyInfo.setUpdateWho("ER");
                    efSupplyApplyInfo.setUpdateWhoName("E融");
                    efSupplyApplyInfo.setSupplyState("3");
                    efSupplyApplyInfoMapper.updateEfSupplyApplyInfo(efSupplyApplyInfo);

                    final List<EfWarehouseList> efWarehouseLists = efWarehouseListMapper.selectEfWarehouseListByQlFinancingNo(efSupplyApplyInfo.getQlFinancingNo());
                    for (EfWarehouseList warehouseList:efWarehouseLists
                    ) {
                        //同步仓单表质押状态
                        EfWarehouse efWarehouse = new EfWarehouse();
                        efWarehouse.setWarehouseReceiptNo(warehouseList.getWarehouseCode());
                        efWarehouse.setPledgeStatus(0);
                        efWarehouse.setUpdateTime(LocalDateTime.now());
                        efWarehouse.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                        efWarehouse.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                        efWarehouseMapper.updateEfWarehouse(efWarehouse);
                    }
                }
            }
            //插入操作记录
            log.setRowId(UUID.randomUUID().toString());
            log.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
            log.setSerialNum(String.valueOf(System.currentTimeMillis()));

            log.setControlTime(LocalDateTime.now());
            log.setRemark(remark);
            log.setAddTime(LocalDateTime.now());
            log.setAddWho(SecurityUtils.getUserInfo().getUserName());
            log.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
            efWarehouseLogMapper.insertEfWarehouseLog(log);

        }else {
            msg = "没有接收到补仓数据";
            flag = false;
        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }


    /**
     * 删除补充质押通知表
     *
     * @param rowId 补充质押通知表ID
     * @return 结果
     */
    public int deleteEfSupplyApplyInfoById(String rowId)
    {
        return efSupplyApplyInfoMapper.deleteEfSupplyApplyInfoById( rowId);
    };


    /**
     * 批量删除补充质押通知表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfSupplyApplyInfoByIds(Integer[] rowIds)
    {
        return efSupplyApplyInfoMapper.deleteEfSupplyApplyInfoByIds( rowIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String syncSupplyApplyInfo(EfSupplyApplyInfo efSupplyApplyInfo)
    {
        //插入操作记录
        EfWarehouseLog log = new EfWarehouseLog();
        String remark = "";

        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(efSupplyApplyInfo!=null) {
            final EfWarehouseApply apply = efWarehouseApplyMapper.selectEfWarehouseApplyByAssetCode(efSupplyApplyInfo.getAssetCode());
            if(apply!=null){
                efSupplyApplyInfo.setEntSocialCode(apply.getEntSocialCode());
                efSupplyApplyInfo.setPlatformCode(apply.getPlatformCode());
                if(efSupplyApplyInfo.getSupplyApplyValuation()!=null){
                    final BigDecimal valuationCny = efSupplyApplyInfo.getSupplyApplyValuation().divide(BigDecimal.valueOf(100),2,BigDecimal.ROUND_HALF_UP);
                    efSupplyApplyInfo.setSupplyApplyValuationCny(valuationCny);
                    remark = "申请补质押货值：" + df.format(valuationCny);
                }
                //待补仓
                efSupplyApplyInfo.setRowId(UUID.randomUUID().toString());
                efSupplyApplyInfo.setSupplyState("0");
                efSupplyApplyInfo.setAddTime(LocalDateTime.now());
                efSupplyApplyInfo.setAddWho(SecurityUtils.getUserInfo().getUserName());
                efSupplyApplyInfo.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                efSupplyApplyInfoMapper.insertEfSupplyApplyInfo(efSupplyApplyInfo);



                //插入操作记录
                log.setRowId(UUID.randomUUID().toString());
                log.setQlFinancingNo(apply.getQlFinancingNo());
                log.setSerialNum(String.valueOf(System.currentTimeMillis()));
                log.setControlType("同步补仓申请");
                log.setControlTime(LocalDateTime.now());
                log.setRemark(remark);
                log.setAddTime(LocalDateTime.now());
                log.setAddWho("ZC");
                log.setAddWhoName("中钞");
                efWarehouseLogMapper.insertEfWarehouseLog(log);
            }else{
                msg = "未查询到该申请数据";
                flag = false;
            }
        }else{
            msg = "没有接收到补充质押通知数据";
            flag = false;
        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String auditSupplyApply(EfSupplyApplyInfo efSupplyApplyInfo)
    {
        //插入操作记录
        EfWarehouseLog log = new EfWarehouseLog();
        String remark = "";

        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(efSupplyApplyInfo!=null) {
            final EfWarehouseApply efWarehouseApply1 = efWarehouseApplyMapper.selectEfWarehouseApplyByQlFinancingNo(efSupplyApplyInfo.getQlFinancingNo());
            if(efWarehouseApply1!=null){
                if(StrUtil.isNotEmpty(efWarehouseApply1.getAssetCode())){
                    efSupplyApplyInfo.setAssetCode(efWarehouseApply1.getAssetCode());
                    efSupplyApplyInfo.setDeleteFlag("N");
                    List<EfSupplyApplyInfo> infoList = efSupplyApplyInfoMapper.selectEfSupplyApplyInfoList(efSupplyApplyInfo);
                    if(CollUtil.isNotEmpty(infoList)){
                        if("CONFIRM".equals(efSupplyApplyInfo.getSupplyOperate())){
                            log.setControlType("仓储同意补仓");

                            EfSupplyApplyList efSupplyApplyList = new EfSupplyApplyList();
                            efSupplyApplyList.setSupplycode(infoList.get(0).getSupplyCode());
                            efSupplyApplyList.setAssetCode(infoList.get(0).getAssetCode());
                            efSupplyApplyList.setDeleteFlag("N");
                            final List<EfSupplyApplyList> efSupplyApplyLists = efSupplyApplyListMapper.selectEfSupplyApplyListList(efSupplyApplyList);
                            final List<EfSupplyApplyList> supplyapplyList = efSupplyApplyInfo.getSupplyapplyList();
                            if(CollUtil.isNotEmpty(efSupplyApplyLists)&&CollUtil.isNotEmpty(supplyapplyList)){
                                for (EfSupplyApplyList e1:efSupplyApplyLists
                                ) {
                                    for (EfSupplyApplyList e2:supplyapplyList
                                    ) {
                                        if(e1.getWarehouseCode().equals(e2.getWarehouseCode())
                                                &&e1.getWarehouseSupervisionNo().equals(e2.getWarehouseSupervisionNo())){
                                            if(e2.getValuationCny()!=null){
                                                final BigDecimal valuation = e2.getValuationCny().multiply(BigDecimal.valueOf(100));
                                                e1.setValuation(valuation);
                                                e1.setValuationCny(e2.getValuationCny());
                                                e1.setValuationCurrency(e2.getValuationCurrency());
                                            }
                                        }
                                    }
                                }
                            }

                            List<EfSupplyApplyListDTO> list = new ArrayList<>();
                            if(CollUtil.isNotEmpty(efSupplyApplyLists)){
                                for (EfSupplyApplyList efSupplyApplyList2:efSupplyApplyLists
                                ) {
//                            final int valuation = (int)Math.round(efSupplyApplyList2.getValuationCny()*100);
//                            efSupplyApplyList2.setValuation(valuation);

                                    EfSupplyApplyListDTO efSupplyApplyListDTO = new EfSupplyApplyListDTO();
                                    efSupplyApplyListDTO.setSupplyWarehouseCode(efSupplyApplyList2.getWarehouseCode());
                                    efSupplyApplyListDTO.setSupplyWarehouseSupervisionNo(efSupplyApplyList2.getWarehouseSupervisionNo());
                                    efSupplyApplyListDTO.setSupplyGoodsValuation(efSupplyApplyList2.getValuation());
                                    efSupplyApplyListDTO.setSupplyGoodsValuationCurrency(efSupplyApplyList2.getValuationCurrency());

                                    list.add(efSupplyApplyListDTO);
                                }

                                EfSupplyApplyInfoDTO dto = new EfSupplyApplyInfoDTO();
                                dto.setSupplyCode(infoList.get(0).getSupplyCode());
                                dto.setAssetCode(infoList.get(0).getAssetCode());
                                dto.setSupplyTime(infoList.get(0).getSupplyTime());
                                dto.setSupplyWarehouseInfoList(list);
                                dto.setSupplyOperate("CONFIRM");

                                if(CollUtil.isNotEmpty(efSupplyApplyLists)){
                                    final EfWarehouseApply efWarehouseApply = efWarehouseApplyMapper.selectEfWarehouseApplyByQlFinancingNo(efSupplyApplyInfo.getQlFinancingNo());
                                    for (EfSupplyApplyList efList:efSupplyApplyLists
                                    ) {
                                        EfWarehouseGoods sel = new EfWarehouseGoods();
                                        sel.setWarehouseReceiptNo(efList.getWarehouseCode());
                                        sel.setDeleteFlag("N");
                                        final List<EfWarehouseGoods> efWarehouseGoods = efWarehouseGoodsMapper.selectEfWarehouseGoodsList(sel);

                                        final EfWarehouse efWarehouse = efWarehouseMapper.selectEfWarehouseByWarehouseReceiptNo(efList.getWarehouseCode());
                                        if(CollUtil.isNotEmpty(efWarehouseGoods)){
                                            for (EfWarehouseGoods goods:efWarehouseGoods
                                            ) {
                                                EfWaybill bill = new EfWaybill();
                                                bill.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                                                bill.setWarehouseReceiptNo(efList.getWarehouseCode());
                                                bill.setDeleteFlag("N");
                                                final List<EfWaybill> efWaybills = efWaybillMapper.selectEfWaybillList(bill);
                                                if(CollUtil.isNotEmpty(efWaybills)){
                                                    for (EfWaybill  bill2:efWaybills
                                                    ) {
                                                        EfWaybillGoods goods2 = new EfWaybillGoods();
                                                        goods2.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                                                        goods2.setWarehouseReceiptNo(efList.getWarehouseCode());
                                                        goods2.setWaybillNo(bill2.getWaybillNo());
                                                        goods2.setOrderNo(bill2.getOrderNo());
                                                        goods2.setGoodsNo(bill2.getGoodsNo());
                                                        goods2.setDeleteFlag("N");
                                                        final List<EfWaybillGoods> efWaybillGoods = efWaybillGoodsMapper.selectEfWaybillGoodsList(goods2);
                                                        bill2.setWayBillGoodsInfoList(efWaybillGoods);
                                                    }
                                                    goods.setWayBillInfo(efWaybills.get(0));
                                                }
                                                goods.setGoodsCode(goods.getGoodsNo());
                                                goods.setCategoryCode(goods.getCategoryNo());

                                                goods.setStorageQuantity(goods.getOldQuantity());
                                                goods.setStorageQuantityUnit(goods.getQuantityUnit());
                                                goods.setStockQuantity(goods.getQuantity());
                                                goods.setStockQuantityUnit(goods.getQuantityUnit());

                                                goods.setStorageWeight(goods.getOldWeight());
                                                goods.setStorageWeightUnit(goods.getWeightUnit());
                                                goods.setStockWeight(goods.getWeight());
                                                goods.setStockWeightUnit(goods.getWeightUnit());

                                                if(efWarehouse!=null){
                                                    goods.setOrderPath(efWarehouse.getOrderPath());
                                                }
                                            }
                                        }
                                        efList.setGoodsInfoList(efWarehouseGoods);
                                        //调用中钞接口，同步仓单信息
                                        String json2 = JSONUtil.parseObj(efList, true).toStringPretty();
                                        final String result2 = signatureController.doPost("/v1/safe/warehouse/syncWarehouseInfo", json2);
                                        from = "中钞平台:";
                                        JSONObject resultObject2 = JSONUtil.parseObj(result2);
                                        flag = Boolean.valueOf(String.valueOf(resultObject2.get("b")));
                                        msg = String.valueOf(resultObject2.get("msg"));
                                        if(!flag){
                                            return JSONUtil.parseObj(new R<>(500,flag,null,from+msg), false).toStringPretty();
                                        }

                                    }
                                }

                                //调用中钞接口，插入申请数据,解析返回数据
                                String json = JSONUtil.parseObj(dto, true).toStringPretty();
                                final String result = signatureController.doPost("/v1/ent/warehouse/supplyGoods", json);

                                JSONObject resultObject = JSONUtil.parseObj(result);

                                from = "中钞平台:";
                                flag = Boolean.valueOf(String.valueOf(resultObject.get("b")));
                                msg = String.valueOf(resultObject.get("msg"));
                                if(flag){
                                    BigDecimal v1 = BigDecimal.valueOf(0);
                                    BigDecimal v2 = BigDecimal.valueOf(0);
                                    if(CollUtil.isNotEmpty(efSupplyApplyLists)){
                                        for (EfSupplyApplyList efSupplyApplyList2:efSupplyApplyLists
                                        ) {
                                            if(efSupplyApplyList2.getValuation()!=null){
                                                v2 = v2.add(efSupplyApplyList2.getValuation());
                                            }
                                            if(efSupplyApplyList2.getValuationCny()!=null){
                                                v1 = v1.add(efSupplyApplyList2.getValuationCny());
                                            }
                                            efSupplyApplyList2.setUpdateTime(LocalDateTime.now());
                                            efSupplyApplyList2.setUpdateWho("ER");
                                            efSupplyApplyList2.setUpdateWhoName("E融");
                                            efSupplyApplyListMapper.updateEfSupplyApplyList(efSupplyApplyList2);
                                        }
                                    }
                                    //更新申请表状态
                                    efSupplyApplyInfo.setRowId(infoList.get(0).getRowId());
                                    //E融审核同意补仓
                                    efSupplyApplyInfo.setSupplyGoodsValuation(v2);
                                    efSupplyApplyInfo.setSupplyGoodsValuationCny(v1);
                                    efSupplyApplyInfo.setSupplyState("2");
                                    efSupplyApplyInfo.setUpdateTime(LocalDateTime.now());
                                    efSupplyApplyInfo.setUpdateWho("ER");
                                    efSupplyApplyInfo.setUpdateWhoName("E融");
                                    efSupplyApplyInfoMapper.updateEfSupplyApplyInfo(efSupplyApplyInfo);

                                }else{
                                    flag = false;
                                    return JSONUtil.parseObj(new R<>(500,flag,null,from+msg), false).toStringPretty();
                                }
                            }else{
                                msg = "没有查询到该补仓仓单数据";
                                flag = false;
                                return JSONUtil.parseObj(new R<>(500,flag,null,from+msg), false).toStringPretty();
                            }
                        }else{
                            log.setControlType("仓储拒绝补仓");

                            //更新申请表状态
                            efSupplyApplyInfo.setRowId(infoList.get(0).getRowId());
                            //E融审核拒绝补仓
                            efSupplyApplyInfo.setUpdateWho("ER");
                            efSupplyApplyInfo.setUpdateWhoName("E融");
                            efSupplyApplyInfo.setSupplyState("3");
                            efSupplyApplyInfoMapper.updateEfSupplyApplyInfo(efSupplyApplyInfo);

                            EfSupplyApplyList supply = new EfSupplyApplyList();
                            supply.setSupplycode(infoList.get(0).getSupplyCode());
                            supply.setDeleteFlag("N");
                            final List<EfSupplyApplyList> efSupplyApplyLists = efSupplyApplyListMapper.selectEfSupplyApplyListList(supply);
                            for (EfSupplyApplyList supplyApplyList:efSupplyApplyLists
                            ) {
                                //同步仓单表质押状态
                                EfWarehouse efWarehouse = new EfWarehouse();
                                efWarehouse.setWarehouseReceiptNo(supplyApplyList.getWarehouseCode());
                                efWarehouse.setPledgeStatus(0);
                                efWarehouse.setUpdateTime(LocalDateTime.now());
                                efWarehouse.setUpdateWho("ER");
                                efWarehouse.setUpdateWhoName("E融");
                                efWarehouseMapper.updateEfWarehouse(efWarehouse);
                            }

                        }
                        //插入操作记录
                        log.setRowId(UUID.randomUUID().toString());
                        log.setQlFinancingNo(efSupplyApplyInfo.getQlFinancingNo());
                        log.setSerialNum(String.valueOf(System.currentTimeMillis()));

                        log.setControlTime(LocalDateTime.now());
                        log.setRemark(remark);
                        log.setAddTime(LocalDateTime.now());
                        log.setAddWho("ZC");
                        log.setAddWhoName("中钞");
                        efWarehouseLogMapper.insertEfWarehouseLog(log);
                    }else{
                        msg = "没有查询到该补仓申请数据";
                        flag = false;
                        return JSONUtil.parseObj(new R<>(500,flag,null,from+msg), false).toStringPretty();
                    }
                }else{
                    msg = "没有查询到该仓单融资协议编号！";
                    flag = false;
                    return JSONUtil.parseObj(new R<>(500,flag,null,from+msg), false).toStringPretty();
                }

            }else{
                msg = "没有查询到该仓单质押申请数据！";
                flag = false;
                return JSONUtil.parseObj(new R<>(500,flag,null,from+msg), false).toStringPretty();
            }


        }else{
            msg = "没有接收到仓单融资审核数据";
            flag = false;
            return JSONUtil.parseObj(new R<>(500,flag,null,from+msg), false).toStringPretty();
        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String syncSupplyConfirmInfo(EfSupplyApplyInfo efSupplyApplyInfo)
    {
        //插入操作记录
        EfWarehouseLog log = new EfWarehouseLog();
        String remark = "";

        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(efSupplyApplyInfo!=null) {
            EfSupplyApplyInfo ef = new EfSupplyApplyInfo();
            ef.setSupplyCode(efSupplyApplyInfo.getSupplyCode());
            ef.setAssetCode(efSupplyApplyInfo.getAssetCode());
            ef.setDeleteFlag("N");
            final List<EfSupplyApplyInfo> infoList = efSupplyApplyInfoMapper.selectEfSupplyApplyInfoList(ef);

            final EfWarehouseApply efWarehouseApply = efWarehouseApplyMapper.selectEfWarehouseApplyByAssetCode(efSupplyApplyInfo.getAssetCode());

            if(efWarehouseApply!=null){
                efSupplyApplyInfo.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
            }
            if(CollUtil.isNotEmpty(infoList)){

                from = "E融平台:";
                //调用E融接口，插入申请数据
                String json = JSONUtil.parseObj(efSupplyApplyInfo, true).toStringPretty();
                final String result = signatureController.doPostEf("/efsupplyapplyinfo/syncSupplyConfirmInfo", json);
                JSONObject dataObject = JSONUtil.parseObj(result);
                flag = Boolean.valueOf(String.valueOf(dataObject.get("b")));
                msg = String.valueOf(dataObject.get("msg"));

                if(flag){
                    EfSupplyApplyList efSupplyApplyList = new EfSupplyApplyList();
                    efSupplyApplyList.setSupplycode(efSupplyApplyInfo.getSupplyCode());
                    efSupplyApplyList.setAssetCode(efSupplyApplyInfo.getAssetCode());
                    efSupplyApplyList.setDeleteFlag("N");
                    final List<EfSupplyApplyList> efSupplyApplyLists = efSupplyApplyListMapper.selectEfSupplyApplyListList(efSupplyApplyList);

                    if("CONFIRM".equals(efSupplyApplyInfo.getSupplyOperate())){
                        log.setControlType("银行同意补仓");

                        efSupplyApplyInfo.setRowId(infoList.get(0).getRowId());
                        //中钞审核同意补仓
                        efSupplyApplyInfo.setSupplyState("5");
                        efSupplyApplyInfoMapper.updateEfSupplyApplyInfo(efSupplyApplyInfo);


                        if(CollUtil.isNotEmpty(efSupplyApplyLists)){
                            for (EfSupplyApplyList apply:efSupplyApplyLists
                            ) {
                                EfWarehouseList efWarehouseList = new EfWarehouseList();
                                //新增仓单融资仓单表
                                efWarehouseList.setRowId(UUID.randomUUID().toString());
                                efWarehouseList.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                                efWarehouseList.setWarehouseSupervisionNo(apply.getWarehouseSupervisionNo());
                                efWarehouseList.setWarehouseCode(apply.getWarehouseCode());
                                efWarehouseList.setValuationCny(apply.getValuationCny());
                                efWarehouseList.setValuation(apply.getValuation());
                                efWarehouseList.setValuationCurrency(apply.getValuationCurrency());
                                efWarehouseList.setSurveillanceVideo(apply.getSurveillanceVideo());
                                efWarehouseList.setApplyDate(apply.getApplyDate());
                                efWarehouseList.setFinishWhTime(apply.getFinishWhTime());
                                efWarehouseList.setCustodianEnterpriseDm(apply.getCustodianEnterpriseDm());
                                efWarehouseList.setCustodianEnterpriseName(apply.getCustodianEnterpriseName());
                                efWarehouseList.setPledgeStatus("1");
                                efWarehouseList.setIsSupply("1");
                                efWarehouseList.setSupplyCode(apply.getSupplycode());
                                efWarehouseList.setAddTime(LocalDateTime.now());
                                efWarehouseList.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                efWarehouseList.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                efWarehouseListMapper.insertEfWarehouseList(efWarehouseList);

                            }
                        }
                    }else{
                        log.setControlType("银行拒绝补仓");

                        efSupplyApplyInfo.setRowId(infoList.get(0).getRowId());
                        //中钞审核拒绝补仓
                        efSupplyApplyInfo.setSupplyState("6");
                        efSupplyApplyInfoMapper.updateEfSupplyApplyInfo(efSupplyApplyInfo);

                        //改e融同步仓单表质押状态
                        EfWarehouse efWarehouse = new EfWarehouse();
                        efWarehouse.setWarehouseReceiptNo(efSupplyApplyList.getWarehouseCode());
                        efWarehouse.setPledgeStatus(0);
                        efWarehouse.setUpdateTime(LocalDateTime.now());
                        efWarehouse.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                        efWarehouse.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                        efWarehouseMapper.updateEfWarehouse(efWarehouse);
                    }
                }
            }else{
                msg = "没有查询到该补仓申请数据";
                flag = false;
            }

            //插入操作记录
            log.setRowId(UUID.randomUUID().toString());
            log.setQlFinancingNo(efSupplyApplyInfo.getQlFinancingNo());
            log.setSerialNum(String.valueOf(System.currentTimeMillis()));

            log.setControlTime(LocalDateTime.now());
            log.setRemark(remark);
            log.setAddTime(LocalDateTime.now());
            log.setAddWho("ZC");
            log.setAddWhoName("中钞");
            efWarehouseLogMapper.insertEfWarehouseLog(log);
        }else{
            msg = "没有接收到仓单融资审核数据";
            flag = false;
        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }
}
