package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.util.StringUtils;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.vo.EnterpriseLicenseVo;
import com.huazheng.tunny.ocean.api.vo.HistoryBooking;
import com.huazheng.tunny.ocean.controller.SignatureController;
import com.huazheng.tunny.ocean.mapper.FiHisBookingInformationMapper;
import com.huazheng.tunny.ocean.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("fiHisBookingInformationService")
public class FiHisBookingInformationServiceImpl extends ServiceImpl<FiHisBookingInformationMapper, FiHisBookingInformation> implements FiHisBookingInformationService {

    @Autowired
    private FiHisBookingInformationMapper fiHisBookingInformationMapper;

    @Autowired
    private WaybillHeaderService waybillHeaderService;

    @Autowired
    private WaybillParticipantsService waybillParticipantsService;

    @Autowired
    private ShifmanagementService shifmanagementService;

    @Autowired
    private WaybillContainerInfoService waybillContainerInfoService;

    @Autowired
    private FdShippingAccountService fdShippingAccountService;

    @Autowired
    private CustomerInfoService customerInfoService;

    @Autowired
    private SignatureController signatureController;

    @Autowired
    private FiEnterpriseAuthShowService fiEnterpriseAuthShowService;
    @Autowired
    private FiBankAuthShowService fiBankAuthShowService;


    @Value("${db.database}")
    private String database;

    /**
     * 查询历史订舱信息表信息
     *
     * @param rowId 历史订舱信息表ID
     * @return 历史订舱信息表信息
     */
    @Override
    public FiHisBookingInformation selectFiHisBookingInformationById(String rowId)
    {
        return fiHisBookingInformationMapper.selectFiHisBookingInformationById(rowId);
    }

    /**
     * 查询历史订舱信息表列表
     *
     * @param fiHisBookingInformation 历史订舱信息表信息
     * @return 历史订舱信息表集合
     */
    @Override
    public List<FiHisBookingInformation> selectFiHisBookingInformationList(FiHisBookingInformation fiHisBookingInformation)
    {
        return fiHisBookingInformationMapper.selectFiHisBookingInformationList(fiHisBookingInformation);
    }

    @Override
    public List<FiHisBookingInformation> selectfiHisBookingInformationDingcangList(FiHisBookingInformation fiHisBookingInformation) {
        return fiHisBookingInformationMapper.selectfiHisBookingInformationDingcangList(fiHisBookingInformation);
    }


    /**
     * 分页模糊查询历史订舱信息表列表
     * @return 历史订舱信息表集合
     */
    @Override
    public Page selectFiHisBookingInformationListByLike(Query query)
    {
        FiHisBookingInformation fiHisBookingInformation =  BeanUtil.mapToBean(query.getCondition(), FiHisBookingInformation.class,false);
        query.setRecords(fiHisBookingInformationMapper.selectFiHisBookingInformationListByLike(query,fiHisBookingInformation));
        return query;
    }

    @Override
    public R selectStatistics(Map<String, Object> params) throws ParseException {
        FiHisBookingInformation fiHisBookingInformation =  BeanUtil.mapToBean(params, FiHisBookingInformation.class,false);

        //返回对象
        R r=new R();
        FiHisBookingInformation fiHisBooking=new FiHisBookingInformation();
        if(fiHisBookingInformation!=null) {
            //判断开始时间和结束时间是否为空
            if (StringUtils.isBlank(fiHisBookingInformation.getStartTime()) || StringUtils.isBlank(fiHisBookingInformation.getEndTime())) {
                r.setMsg("请传入开始时间和结束时间");
                r.setCode(500);
                r.setB(Boolean.FALSE);
                return r;
            }
        }else{
            r.setMsg("请传入开始时间和结束时间");
            r.setCode(500);
            r.setB(Boolean.FALSE);
            return r;
        }
//        fiHisBookingInformation.setCustomerName(fiHisBookingInformation.getCustomerNo());
        CustomerInfo customerInfo=new CustomerInfo();
        customerInfo.setCustomerCode(fiHisBookingInformation.getCustomerNo());
        CustomerInfo byCustomerCode = customerInfoService.selectCustomegenNorInfoByCustomerCode(customerInfo);
        //获取当前登录人账号
        fiHisBookingInformation.setOrgUnit(byCustomerCode.getCompanyName());
        //约定时间
        String ydDate="2022-03-01";
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date startTime = df.parse(fiHisBookingInformation.getStartTime());
        Date endTime = df.parse(fiHisBookingInformation.getEndTime());
        Date parse = df.parse(ydDate);


        if(isEffectiveDate(parse,startTime,endTime)){
            //查询目的国
            //查历史订舱信息表也差订舱信息表
            FiHisBookingInformation fiHisBookingInfor=new FiHisBookingInformation();
            //获取约定时间的前一天
            String beforeDay = getBeforeDay(ydDate);
            fiHisBookingInfor.setStartTime(fiHisBookingInformation.getStartTime());
            fiHisBookingInfor.setEndTime(beforeDay);
            fiHisBookingInfor.setOrgUnit(byCustomerCode.getCompanyName());
            fiHisBookingInfor.setCustomerNo(fiHisBookingInformation.getCustomerNo());
            fiHisBookingInfor.setStartTimeTwo(ydDate);
            fiHisBookingInfor.setEndTimeTwo(fiHisBookingInformation.getEndTime());
            fiHisBookingInfor.setDatabase(database);
            //查订舱信息表
            WaybillHeader waybillHead=new WaybillHeader();
            waybillHead.setStartTime(ydDate);
            waybillHead.setEndTime(fiHisBookingInformation.getEndTime());
            waybillHead.setCustomerNo(fiHisBookingInformation.getCustomerNo());
            //查询历史订舱的总数
            Integer byCount = fiHisBookingInformationMapper.selectByCount(fiHisBookingInfor);
            //查询总数
            Integer selectWaybillCount = waybillHeaderService.selectWaybillCount(waybillHead);
            int byCounts = byCount + selectWaybillCount;
            BigDecimal decimal = BigDecimal.valueOf(byCounts);
            if(decimal.compareTo(BigDecimal.valueOf(0))>0){
                StringBuilder stringBuilder=new StringBuilder();
                List<FiHisBookingInformation> informations = fiHisBookingInformationMapper.selectFiAndWayBillCount(fiHisBookingInfor);
                for (FiHisBookingInformation information:informations) {
                    BigDecimal multiply = BigDecimal.valueOf(Double.parseDouble(information.getRowId())).divide(decimal, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                    stringBuilder.append(information.getLine()).append(":").append(multiply).append("%").append(",");
                }
                //主要线路
                fiHisBooking.setMainExportRoutes(stringBuilder.substring(0,stringBuilder.toString().length()-1));
            }


            //主要目的国  历史订舱信息
            FiHisBookingInformation primaryEndCountry = fiHisBookingInformationMapper.selectByprimaryEndCountry(fiHisBookingInfor);

            //查询目的国  订舱信息
            WaybillParticipants waybillParticipants=new WaybillParticipants();
            waybillParticipants.setEndTime(fiHisBookingInformation.getEndTime());
            waybillParticipants.setStartTime(ydDate);
            waybillParticipants.setCustomerNo(fiHisBookingInformation.getCustomerNo());
            waybillParticipants.setDatabase(database);
            Integer waybillParticipantsByCount = waybillParticipantsService.selectWaybillParticipantsByCount(waybillParticipants);
            BigDecimal countryCount = BigDecimal.valueOf(byCount + waybillParticipantsByCount);
            if(primaryEndCountry !=null ){
                StringBuilder builder = new StringBuilder();
                if(countryCount.compareTo(BigDecimal.valueOf(0))>0){
                    BigDecimal multiply = BigDecimal.valueOf(Double.parseDouble(primaryEndCountry.getRowId())).divide(countryCount, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                    builder.append(primaryEndCountry.getEndCountry()).append(":").append(multiply).append("%");
                }
                fiHisBooking.setMainDestinationCountries(builder.toString());
            }
//            if(countryCount.compareTo(BigDecimal.valueOf(0))>0) {
//                StringBuilder builder = new StringBuilder();
//                WaybillParticipants waybillPartici = waybillParticipantsService.selectWaybillParticipantsByList(waybillParticipants);
//                if(primaryEndCountry!=null &&StringUtils.isNotBlank(primaryEndCountry.getRowId())) {
//                    if (Integer.valueOf(waybillPartici.getRowId()) > Integer.valueOf(primaryEndCountry.getRowId())) {
//                        BigDecimal multiply = BigDecimal.valueOf(Double.parseDouble(waybillPartici.getRowId())).divide(countryCount, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
//                        builder.append(waybillPartici.getCountryCode()).append(":").append(multiply).append("%");
//                    } else if (Integer.valueOf(waybillPartici.getRowId()) < Integer.valueOf(primaryEndCountry.getRowId())) {
//                        BigDecimal multiply = BigDecimal.valueOf(Double.parseDouble(primaryEndCountry.getRowId())).divide(countryCount, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
//                        builder.append(waybillPartici.getCountryCode()).append(":").append(multiply).append("%");
//                    } else if (Integer.valueOf(waybillPartici.getRowId()) == Integer.valueOf(primaryEndCountry.getRowId())) {
//                        BigDecimal multiply = BigDecimal.valueOf(Double.parseDouble(waybillPartici.getRowId())).divide(countryCount, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
//                        builder.append(waybillPartici.getCountryCode()).append(":").append(multiply).append("%");
//                    }
//                }else{
//                    BigDecimal multiply = BigDecimal.valueOf(Double.parseDouble(waybillPartici.getRowId())).divide(countryCount, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
//                    builder.append(waybillPartici.getCountryCode()).append(":").append(multiply).append("%");
//                }
//                fiHisBooking.setMainDestinationCountries(builder.toString());
//            }


            //主要下站口
            FiHisBookingInformation primaryEndStation = fiHisBookingInformationMapper.selectByPrimaryEndStation(fiHisBookingInfor);
            //查询主要下站口
            Shifmanagement shifmanagement=new Shifmanagement();
            shifmanagement.setEndTime(fiHisBookingInfor.getEndTimeTwo());

            shifmanagement.setStartTime(ydDate);
            shifmanagement.setCustomerNo(fiHisBookingInformation.getCustomerNo());
            Integer shifmanagementByCount = shifmanagementService.selectShifmanagementByCount(shifmanagement);
            BigDecimal shifManageCount=BigDecimal.valueOf(shifmanagementByCount+byCount);
            if(primaryEndStation!=null){
                StringBuilder mainExits = new StringBuilder();
                BigDecimal mainExitsMultiply = BigDecimal.valueOf(Double.parseDouble(primaryEndStation.getRowId())).divide(shifManageCount, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                mainExits.append(primaryEndStation.getEndStation()).append(":").append(mainExitsMultiply).append("%");
                fiHisBooking.setMainExits(mainExits.toString());
            }
//            if(shifManageCount.compareTo(BigDecimal.valueOf(0))>0){
//                StringBuilder mainExits = new StringBuilder();
//                Shifmanagement byPortStation = shifmanagementService.selectShifmanagementByPortStation(shifmanagement);
//                if(byPortStation !=null) {
//                    if (primaryEndStation != null && StringUtils.isNotBlank(primaryEndStation.getRowId())) {
//                        if (Integer.valueOf(byPortStation.getRowId()) > Integer.valueOf(primaryEndStation.getRowId())) {
//                            BigDecimal mainExitsMultiply = BigDecimal.valueOf(Double.parseDouble(waybillPartici.getRowId())).divide(shifManageCount, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
//                            mainExits.append(byPortStation.getDestination()).append(":").append(mainExitsMultiply).append("%");
//                        } else if (Integer.valueOf(byPortStation.getRowId()) < Integer.valueOf(primaryEndStation.getRowId())) {
//                            BigDecimal mainExitsMultiply = BigDecimal.valueOf(Double.parseDouble(primaryEndStation.getRowId())).divide(shifManageCount, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
//                            mainExits.append(byPortStation.getDestination()).append(":").append(mainExitsMultiply).append("%");
//                        } else if (Integer.valueOf(byPortStation.getRowId()) == Integer.valueOf(primaryEndStation.getRowId())) {
//                            BigDecimal mainExitsMultiply = BigDecimal.valueOf(Double.parseDouble(waybillPartici.getRowId())).divide(shifManageCount, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
//                            mainExits.append(byPortStation.getDestination()).append(":").append(mainExitsMultiply).append("%");
//                        }
//                    } else {
//                        BigDecimal mainExitsMultiply = BigDecimal.valueOf(Double.parseDouble(waybillPartici.getRowId())).divide(shifManageCount, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
//                        mainExits.append(byPortStation.getDestination()).append("尺").append(":").append(mainExitsMultiply).append("%");
//                    }
//                }
//                fiHisBooking.setMainExits(mainExits.toString());
//            }

            //运输总量
            List<FiHisBookingInformation> primaryboxType = fiHisBookingInformationMapper.selectFiHisBookingAndWayBaybillByPrimaryboxType(fiHisBookingInfor);
            if(primaryboxType.size()>0){
                StringBuilder totalTransportVolume = new StringBuilder();
                BigDecimal bigDecimals=BigDecimal.valueOf(0);
                for (FiHisBookingInformation pra:primaryboxType) {
                    String rowId = pra.getRowId();
                    if(pra.getBoxType().contains("4")){
                        rowId=BigDecimal.valueOf(Double.parseDouble(rowId)).multiply(BigDecimal.valueOf(2)).toString();
                    }
                    bigDecimals=bigDecimals.add(BigDecimal.valueOf(Double.parseDouble(rowId)));
                }
                totalTransportVolume.append(bigDecimals).append("teu").append(" 其中: ");
                for (FiHisBookingInformation informationTwo:primaryboxType) {
                    totalTransportVolume.append(informationTwo.getBoxType()).append("尺").append(informationTwo.getRowId()).append("车").append(",");
                }
                fiHisBooking.setTotalTransportVolume(totalTransportVolume.substring(0,totalTransportVolume.toString().length()-1));
            }
            r.setB(Boolean.TRUE);
            r.setData(fiHisBooking);
            r.setCode(200);
            return r;
        }else if(parse.after(endTime) || parse.equals(endTime)){
            //查历史订舱信息表
            //查询总数据
            Integer integer = fiHisBookingInformationMapper.selectByCount(fiHisBookingInformation);
            BigDecimal count=BigDecimal.valueOf(integer);
            if(count.compareTo(BigDecimal.valueOf(0))>0) {
                //主要出口线路
                List<FiHisBookingInformation> fiHisBookingInformations = fiHisBookingInformationMapper.selectStatistics(fiHisBookingInformation);
                if(fiHisBookingInformations.size()>0){
                    StringBuilder stringBuilder=new StringBuilder();
                    for (FiHisBookingInformation information : fiHisBookingInformations) {
                        BigDecimal multiply = BigDecimal.valueOf(Double.parseDouble(information.getRowId())).divide(count, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                        stringBuilder.append(information.getLine()).append(":").append(multiply).append("%").append(",");

                    }
                    fiHisBooking.setMainExportRoutes(stringBuilder.substring(0,stringBuilder.toString().length()-1));
                }
                //主要目的国
                FiHisBookingInformation primaryEndCountry = fiHisBookingInformationMapper.selectByprimaryEndCountryHis(fiHisBookingInformation);
                if(primaryEndCountry !=null ) {
                    StringBuilder endCountryStringBuilder = new StringBuilder();
                    BigDecimal endCountrymultiply = BigDecimal.valueOf(Double.parseDouble(primaryEndCountry.getRowId())).divide(count, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                    endCountryStringBuilder.append(primaryEndCountry.getEndCountry()).append(":").append(endCountrymultiply).append("%");
                    fiHisBooking.setMainDestinationCountries(endCountryStringBuilder.toString());
                }
                //主要下站口
                FiHisBookingInformation primaryEndStation = fiHisBookingInformationMapper.selectByPrimaryEndStationByHis(fiHisBookingInformation);
                if(primaryEndStation !=null ) {
                    StringBuilder endStationsb = new StringBuilder();
                    BigDecimal endStationmultiply = BigDecimal.valueOf(Double.parseDouble(primaryEndStation.getRowId())).divide(count, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                    endStationsb.append(primaryEndStation.getEndStation()).append(":").append(endStationmultiply).append("%");
                    fiHisBooking.setMainExits(endStationsb.toString());
                }
                //总运输量
                List<FiHisBookingInformation> primaryboxType = fiHisBookingInformationMapper.selectByPrimaryboxType(fiHisBookingInformation);
                if(primaryboxType.size()>0){
                    StringBuilder boxTypeBuilder=new StringBuilder();
                    BigDecimal bigDecimal=BigDecimal.valueOf(0);
                    for (FiHisBookingInformation information:primaryboxType) {
                        String rowId = information.getRowId();
                        if(information.getBoxType().contains("4")){
                            rowId=BigDecimal.valueOf(Double.parseDouble(rowId)).multiply(BigDecimal.valueOf(2)).toString();
                        }
                        bigDecimal=bigDecimal.add(BigDecimal.valueOf(Double.parseDouble(rowId)));
                    }
                    boxTypeBuilder.append(bigDecimal).append("teu").append(" 其中: ");
                    for (FiHisBookingInformation informationTwo:primaryboxType) {
                        boxTypeBuilder.append(informationTwo.getBoxType()).append("尺").append(informationTwo.getRowId()).append("车").append(",");
                    }
                    fiHisBooking.setTotalTransportVolume(boxTypeBuilder.substring(0,boxTypeBuilder.toString().length()-1));
                }
            }
            r.setB(Boolean.TRUE);
            r.setData(fiHisBooking);
            r.setCode(200);
            return r;
        }else if(parse.before(startTime) || parse.equals(startTime)){
            //查订舱信息表
            WaybillHeader waybillHeader=new WaybillHeader();
            waybillHeader.setStartTime(fiHisBookingInformation.getStartTime());
            waybillHeader.setEndTime(fiHisBookingInformation.getEndTime());
            waybillHeader.setCustomerNo(fiHisBookingInformation.getCustomerNo());
            //查询总数
            Integer selectWaybillCount = waybillHeaderService.selectWaybillCount(waybillHeader);
            BigDecimal countWaybill=BigDecimal.valueOf(selectWaybillCount);
            if(countWaybill.compareTo(BigDecimal.valueOf(0))>0){
                //查询发运线路
                List<WaybillHeader> waybillHeaders = waybillHeaderService.selectShippingLine(waybillHeader);
                if(waybillHeaders.size()>0){
                    StringBuilder listWaybillBuilder=new StringBuilder();
                    for (WaybillHeader waybill:waybillHeaders) {
                        Map<String, Object> mapWaybill = new HashMap<>();
                        mapWaybill.put(waybill.getShippingLine(),BigDecimal.valueOf(Double.parseDouble(waybill.getRowId())).divide(countWaybill,2,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)));

                        BigDecimal listWaybillmultiply = BigDecimal.valueOf(Double.parseDouble(waybill.getRowId())).divide(countWaybill, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));

                        listWaybillBuilder.append(waybill.getShippingLine()).append(":").append(listWaybillmultiply).append("%").append(",");

                    }
                    fiHisBooking.setMainExportRoutes(listWaybillBuilder.substring(0,listWaybillBuilder.length()-1));
                }
            }
            //查询目的国
            WaybillParticipants waybillParticipants=new WaybillParticipants();
            waybillParticipants.setEndTime(waybillHeader.getEndTime());
            waybillParticipants.setStartTime(waybillHeader.getStartTime());
            waybillParticipants.setCustomerNo(waybillHeader.getCustomerNo());
            waybillParticipants.setDatabase(database);
            Integer waybillParticipantsByCount = waybillParticipantsService.selectWaybillParticipantsByCount(waybillParticipants);
            BigDecimal countwaybillPartici=BigDecimal.valueOf(waybillParticipantsByCount);
            if(countwaybillPartici.compareTo(BigDecimal.valueOf(0))>0) {
                StringBuilder waybillParticiBuilder=new StringBuilder();
                WaybillParticipants waybillPartici = waybillParticipantsService.selectWaybillParticipantsByList(waybillParticipants);
                BigDecimal waybillParticiMultiply = BigDecimal.valueOf(Double.parseDouble(waybillPartici.getRowId())).divide(countwaybillPartici, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                waybillParticiBuilder.append(waybillPartici.getCountryCode()).append(":").append(waybillParticiMultiply).append("%");
                fiHisBooking.setMainDestinationCountries(waybillParticiBuilder.toString());
            }
            //查询主要下站口
            Shifmanagement shifmanagement=new Shifmanagement();
            shifmanagement.setEndTime(waybillHeader.getEndTime());
            shifmanagement.setStartTime(waybillHeader.getStartTime());
            shifmanagement.setCustomerNo(waybillHeader.getCustomerNo());
            Integer shifmanagementByCount = shifmanagementService.selectShifmanagementByCount(shifmanagement);
            BigDecimal shifManageCount=BigDecimal.valueOf(shifmanagementByCount);
            if(shifManageCount.compareTo(BigDecimal.valueOf(0))>0){

                StringBuilder portStationBuilder=new StringBuilder();
                Shifmanagement byPortStation = shifmanagementService.selectShifmanagementByPortStation(shifmanagement);
                BigDecimal portStationMultiply = BigDecimal.valueOf(Double.parseDouble(byPortStation.getRowId())).divide(shifManageCount, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                portStationBuilder.append(byPortStation.getDestination()).append(":").append(portStationMultiply).append("%");
                fiHisBooking.setMainExits(portStationBuilder.toString());

            }

            //查询运输总量
            WaybillContainerInfo waybillContainerInfo=new WaybillContainerInfo();
            waybillContainerInfo.setStartTime(fiHisBookingInformation.getStartTime());
            waybillContainerInfo.setEndTime(fiHisBookingInformation.getEndTime());
            waybillContainerInfo.setCustomerNo(fiHisBookingInformation.getCustomerNo());
            Integer conInfoByCount = waybillContainerInfoService.selectConInfoByCount(waybillContainerInfo);
            BigDecimal infoByCount=BigDecimal.valueOf(conInfoByCount);
            if(infoByCount.compareTo(BigDecimal.valueOf(0))>0){
                StringBuilder infoBuidler=new StringBuilder();
                BigDecimal bigDecimalS=BigDecimal.valueOf(0);
                List<WaybillContainerInfo> waybillContainerInfos = waybillContainerInfoService.selectConInfoByContainerType(waybillContainerInfo);
                for (WaybillContainerInfo waybillCont:waybillContainerInfos) {
                    String rowId = waybillCont.getRowId();
                    if(waybillCont.getContainerType().contains("4")){
                        rowId=BigDecimal.valueOf(Double.parseDouble(rowId)).multiply(BigDecimal.valueOf(2)).toString();
                    }
                    bigDecimalS=bigDecimalS.add(BigDecimal.valueOf(Double.parseDouble(rowId)));
                }
                infoBuidler.append(bigDecimalS).append("teu").append(" 其中: ");
                for (WaybillContainerInfo waybillContTwo:waybillContainerInfos) {
                    infoBuidler.append(waybillContTwo.getContainerType()).append("尺").append(waybillContTwo.getRowId()).append("车").append(",");
                }

                fiHisBooking.setTotalTransportVolume(infoBuidler.substring(0,infoBuidler.length()-1));
            }
            r.setB(Boolean.TRUE);
            r.setCode(200);
            r.setData(fiHisBooking);
            return r;
        }

        return r;
    }

    /**
     * 获取指定日期的前一天
     * @param specifiedDay
     * @return
     */
    public static String getBeforeDay(String specifiedDay) throws ParseException {
        Calendar c = Calendar.getInstance();
        Date date = null;
        date = new SimpleDateFormat("yyyy-MM-dd").parse(specifiedDay);
        c.setTime(date);
        int day = c.get(Calendar.DATE);
        c.set(Calendar.DATE, day - 1);

        String dayBefore = new SimpleDateFormat("yyyy-MM-dd").format(c.getTime());
        return dayBefore;
    }

    /**
     * 判断约定时间是否在[startTime, endTime]区间，注意时间格式要一致
     *
     * @param nowTime  约定时间
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime()
                || nowTime.getTime() == endTime.getTime()) {
            return false;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 新增历史订舱信息表
     *
     * @param fiHisBookingInformation 历史订舱信息表信息
     * @return 结果
     */
    @Override
    public int insertFiHisBookingInformation(FiHisBookingInformation fiHisBookingInformation)
    {
        return fiHisBookingInformationMapper.insertFiHisBookingInformation(fiHisBookingInformation);
    }

    /**
     * 修改历史订舱信息表
     *
     * @param fiHisBookingInformation 历史订舱信息表信息
     * @return 结果
     */
    @Override
    public int updateFiHisBookingInformation(FiHisBookingInformation fiHisBookingInformation)
    {
        return fiHisBookingInformationMapper.updateFiHisBookingInformation(fiHisBookingInformation);
    }


    /**
     * 删除历史订舱信息表
     *
     * @param rowId 历史订舱信息表ID
     * @return 结果
     */
    public int deleteFiHisBookingInformationById(String rowId)
    {
        return fiHisBookingInformationMapper.deleteFiHisBookingInformationById( rowId);
    }


    /**
     * 批量删除历史订舱信息表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiHisBookingInformationByIds(Integer[] rowIds)
    {
        return fiHisBookingInformationMapper.deleteFiHisBookingInformationByIds( rowIds);
    }

    @Override
    public String selectEnterpriseLicense(JSONObject jsonObject) throws ParseException {
        String data = signatureController.getPost(jsonObject);
        //获取入参
        CustomerPlatformInfo customerPlatformInfo = JSONUtil.toBean(data, CustomerPlatformInfo.class);
        //创建返回对象
        R r=new R();
        EnterpriseLicenseVo enterpriseLicenseVo=new EnterpriseLicenseVo();
        String content = null;
        if(StringUtils.isBlank(customerPlatformInfo.getEntSocialCode())){
            r.setB(Boolean.FALSE);
            r.setData(500);
            r.setMsg("企业统一社会信用代码为空");
            content = JSONUtil.parseObj(r, true).toStringPretty();
            String result = signatureController.returnPost("/fihisbookinginformation/selectEnterpriseLicense", content);
            return result;
        }else{
            customerPlatformInfo.setSocialUcCode(customerPlatformInfo.getEntSocialCode());
        }
        FiEnterpriseAuthShow fiEnterpriseAuthShow=new FiEnterpriseAuthShow();
        fiEnterpriseAuthShow.setEntSocialCode(customerPlatformInfo.getEntSocialCode());
        fiEnterpriseAuthShow.setBankTopCode(customerPlatformInfo.getBankCode());
        List<FiEnterpriseAuthShow> fiEnterpriseAuthShows = fiEnterpriseAuthShowService.selectFiEnterpriseAuthShowByEntSocialCode(fiEnterpriseAuthShow);
        FiBankAuthShow fiBankAuthShow=new FiBankAuthShow();
        fiBankAuthShow.setEntSocialCode(customerPlatformInfo.getEntSocialCode());
        fiBankAuthShow.setBankTopCode(customerPlatformInfo.getBankCode());
        List<FiBankAuthShow> fiBankAuthShows = fiBankAuthShowService.selectFiBankAuthShowByEntSocialCode(fiBankAuthShow);
        if(fiEnterpriseAuthShows.size()<=0 && fiBankAuthShows.size()<=0){
            r.setB(Boolean.FALSE);
            r.setData(500);
            r.setMsg("未获得授权");
            content = JSONUtil.parseObj(r, true).toStringPretty();
            String result = signatureController.returnPost("/fihisbookinginformation/selectEnterpriseLicense", content);
            return result;
        }
        //查询基本信息
        CustomerInfo info = customerInfoService.selectCustomegenNorInfoById(customerPlatformInfo);
        if(info!=null) {
            enterpriseLicenseVo.setEntSocialCode(isNotNull(info.getSocialUcCode()));//统一社会信用代码
            enterpriseLicenseVo.setEntName(isNotNull(info.getCompanyName()));//公司名称
            enterpriseLicenseVo.setEntType(isNotNull(info.getForwarderUser()));//企业类型
            enterpriseLicenseVo.setContacts(isNotNull(info.getResveredField02()));//联系电话
            enterpriseLicenseVo.setBankAccount(isNotNull(info.getOpenBank()));//开户行
            enterpriseLicenseVo.setOfficeAddress(isNotNull(info.getConnectAddress()));//联系地址
//            enterpriseLicenseVo.setEntStatus();//企业状态
            enterpriseLicenseVo.setContactsPhone(isNotNull(info.getResveredField01()));//联系人电话
            enterpriseLicenseVo.setCorporateAccount(isNotNull(info.getBankAccount()));//对公账号
            enterpriseLicenseVo.setBusinessLicenceUrl(isNotNull(info.getPictureUrl()));//营业执照URL
            HistoryBooking historyBooking=new HistoryBooking();
            //判断是订舱客户：0 还是市平台：1
            if(info.getCustomerFlag().equals("0")){

                Map<String,Object> map=new HashMap<>();
                map.put("isUser","0");
                map.put("customerNo",info.getCustomerCode());
                if(StringUtils.isNotBlank(customerPlatformInfo.getStartTime())){
                    //开始时间加上当月的第一天
                    String startTime = customerPlatformInfo.getStartTime();
                    StringBuilder stringBuilder=new StringBuilder();
                    stringBuilder.append(startTime).append("-").append("01");
                    map.put("startTime",stringBuilder.toString());
                }else{
                    map.put("startTime",lastYear());
                }
                if(StringUtils.isNotBlank(customerPlatformInfo.getEndTime())){
                    //结束时间加上当月的最后一天
                    String[] split = customerPlatformInfo.getEndTime().split("-");
                    String lastDayOfMonth = getLastDayOfMonth(Integer.valueOf(split[0]), Integer.valueOf(split[1]));
                    map.put("endTime",lastDayOfMonth);
                }else{
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                    String[] split = format.format(new Date()).toString().split("-");
                    map.put("endTime",getLastDayOfMonth(Integer.valueOf(split[0]), Integer.valueOf(split[1])));
                }
                //查询历史订舱信息
                R fiBookingFee = waybillHeaderService.selectFiBookingFee(map);
                String[] startTimes = map.get("startTime").toString().split("-");
                String[] endTimes = map.get("endTime").toString().split("-");
                StringBuilder startString=new StringBuilder();
                StringBuilder endString=new StringBuilder();
                //统计起止时间
                historyBooking.setStatisticsStartTime(startString.append(startTimes[0]).append("-").append(startTimes[1]).toString());
                historyBooking.setStatisticsEndTime(endString.append(endTimes[0]).append("-").append(endTimes[1]).toString());

                if(fiBookingFee.getCode()==200) {
                    List<Map<String, Object>> dataMap = (List<Map<String, Object>>) fiBookingFee.getData();
                    if (dataMap.size() > 0) {
                        List<Map<String, Object>> mapList = new ArrayList<>();
                        for (Map<String, Object> stringObjectMap : dataMap) {
                            Map<String, Object> objectMap = new HashMap<>();
                            StringBuilder stringb = new StringBuilder();
                            stringb.append(stringObjectMap.get("year")).append("-").append(stringObjectMap.get("month"));
                            objectMap.put("date", stringb);//日期
                            objectMap.put("bookingFee",Long.valueOf(BigDecimal.valueOf(Double.parseDouble(String.valueOf(stringObjectMap.get("totalCost")))).multiply(BigDecimal.valueOf(100)).setScale(0).toString()));//订舱总费用
                            objectMap.put("paidFee", Long.valueOf(BigDecimal.valueOf(Double.parseDouble(String.valueOf(stringObjectMap.get("havePaid")))).multiply(BigDecimal.valueOf(100)).setScale(0).toString()));//已支付费用
                            if(stringObjectMap.get("payment").toString().equals("0.00")){
                                objectMap.put("paymentRat","0");//支付率
                            }else{
                                objectMap.put("paymentRat", stringObjectMap.get("payment").toString());//支付率
                            }

                            mapList.add(objectMap);
                        }
                        historyBooking.setBookingList(mapList);
                    }
                }
                //查询统计信息
                R statistics = selectStatistics(map);
                if(statistics.getCode()==200) {
                    FiHisBookingInformation information = (FiHisBookingInformation) statistics.getData();
                    historyBooking.setMainExits(information.getMainExits());
                    historyBooking.setMainDestinationCountries(information.getMainDestinationCountries());
                    historyBooking.setMainExportRoutes(information.getMainExportRoutes());
                    historyBooking.setTotalTransportQuantities(information.getTotalTransportVolume());
                }
                enterpriseLicenseVo.setHistoryBooking(historyBooking);
                r.setData(enterpriseLicenseVo);
                r.setCode(200);
                r.setB(Boolean.TRUE);
            }else if(info.getCustomerFlag().equals("1")){

                Map<String,Object> map=new HashMap<>();
                map.put("customerNo",info.getCustomerCode());
                if(StringUtils.isNotBlank(customerPlatformInfo.getStartTime())){
                    //开始时间加上当月的第一天
                    String startTime = customerPlatformInfo.getStartTime();
                    StringBuilder stringBuilder=new StringBuilder();
                    stringBuilder.append(startTime).append("-").append("01");
                    map.put("startTime",stringBuilder.toString());
                }else{
                    map.put("startTime",lastYear());
                }
                if(StringUtils.isNotBlank(customerPlatformInfo.getEndTime())){
                    //结束时间加上当月的最后一天
                    String[] split = customerPlatformInfo.getEndTime().split("-");
                    String lastDayOfMonth = getLastDayOfMonth(Integer.valueOf(split[0]), Integer.valueOf(split[1]));
                    map.put("endTime",lastDayOfMonth);
                }else{
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//                    map.put("endTime",format.format(new Date()));
                    String[] split = format.format(new Date()).toString().split("-");
                    map.put("endTime",getLastDayOfMonth(Integer.valueOf(split[0]), Integer.valueOf(split[1])));
                }

                R shiShippingTime = fdShippingAccountService.selectFiShippingTime(map);
                if(shiShippingTime.getCode()==200){
                    List<Map<String, Object>> dataMap = (List<Map<String, Object>>) shiShippingTime.getData();
                    String[] startTimes = map.get("startTime").toString().split("-");
                    String[] endTimes = map.get("endTime").toString().split("-");
                    StringBuilder startString=new StringBuilder();
                    StringBuilder endString=new StringBuilder();
                    //统计起止时间
                    historyBooking.setStatisticsStartTime(startString.append(startTimes[0]).append("-").append(startTimes[1]).toString());
                    historyBooking.setStatisticsEndTime(endString.append(endTimes[0]).append("-").append(endTimes[1]).toString());
                    if(dataMap.size()>0){
                       List<Map<String, Object>> mapList = new ArrayList<>();
                       for (Map<String, Object> stringObjectMap : dataMap) {
                                Map<String, Object> objectMap = new HashMap<>();
                                StringBuilder stringb = new StringBuilder();
                                stringb.append(stringObjectMap.get("year")).append("-").append(stringObjectMap.get("month"));
                                objectMap.put("date", stringb);//日期
                                objectMap.put("bookingFee",Long.valueOf(BigDecimal.valueOf(Double.parseDouble(String.valueOf(stringObjectMap.get("totalCost")))).multiply(BigDecimal.valueOf(100)).setScale(0).toString()));//订舱总费用
                                objectMap.put("paidFee", Long.valueOf(BigDecimal.valueOf(Double.parseDouble(String.valueOf(stringObjectMap.get("havePaid")))).multiply(BigDecimal.valueOf(100)).setScale(0).toString()));//已支付费用
                               if(stringObjectMap.get("payment").toString().equals("0.00")){
                                   objectMap.put("paymentRat","0");//支付率
                               }else{
                                   objectMap.put("paymentRat", stringObjectMap.get("payment").toString());//支付率
                               }
                                mapList.add(objectMap);
                            }
                        historyBooking.setBookingList(mapList);
                    }
                }

                //查询统计信息
                R selectShiStatistics = fdShippingAccountService.selectShiStatistics(map);
                if(selectShiStatistics.getCode()==200){
                    FiHisBookingInformation information = (FiHisBookingInformation) selectShiStatistics.getData();
                    historyBooking.setMainExits(information.getMainExits());
                    historyBooking.setMainDestinationCountries(information.getMainDestinationCountries());
                    historyBooking.setMainExportRoutes(information.getMainExportRoutes());
                    historyBooking.setTotalTransportQuantities(information.getTotalTransportVolume());
                }
                enterpriseLicenseVo.setHistoryBooking(historyBooking);
                r.setData(enterpriseLicenseVo);
                r.setCode(200);
                r.setB(Boolean.TRUE);
            }

        }else{
            r.setCode(500);
            r.setB(Boolean.FALSE);
            r.setMsg("企业统一社会信用代码查询不到用户");
            content = JSONUtil.parseObj(r, true).toStringPretty();
            String result = signatureController.returnPost("/fihisbookinginformation/selectEnterpriseLicense", content);
            return result;
        }
        content = JSONUtil.parseObj(r, true).toStringPretty();
        System.out.println(content);
        //调用接口
        String result = signatureController.returnPost("/fihisbookinginformation/selectEnterpriseLicense", content);
        return result;
    }

    //判断是否为空
    public String isNotNull(Object object){
        if(!"".equals(object) && object!=null){
            return object.toString();
        }
        return "";
    }

    /**
     * 获取某月的最后一天
     *
     */
    public static String getLastDayOfMonth(int year,int month)
    {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR,year);
        //设置月份
        cal.set(Calendar.MONTH, month-1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String lastDayOfMonth = sdf.format(cal.getTime());

        return lastDayOfMonth;
    }

    /**
     * 获取过去一年的时间
     */
    public static String lastYear(){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        Calendar calendar = Calendar.getInstance();

        calendar.setTime(new Date());

        calendar.add(Calendar.YEAR, -1);

        Date y = calendar.getTime();
        String format1 = format.format(y);
        String[] split = format1.split("-");
        StringBuilder stringBuilder=new StringBuilder();
        stringBuilder.append(split[0]).append("-").append(split[1]).append("-").append("01");

        return stringBuilder.toString();
    }

}
