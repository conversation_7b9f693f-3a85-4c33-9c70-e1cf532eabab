package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.util.StringUtils;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.mapper.FiExporfinancingApplyMapper;
import com.huazheng.tunny.ocean.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("fiExporfinancingApplyService")
public class FiExporfinancingApplyServiceImpl extends ServiceImpl<FiExporfinancingApplyMapper, FiExporfinancingApply> implements FiExporfinancingApplyService {

    @Autowired
    private FiExporfinancingApplyMapper fiExporfinancingApplyMapper;
    @Autowired
    private CustomerPlatformInfoService customerPlatformInfoService;
    @Autowired
    private FiExporfinancingInvoicesService fiExporfinancingInvoicesService;
    @Autowired
    private FiExporfinancingInvoicesManifestsService fiExporfinancingInvoicesManifestsService;
    @Autowired
    private SysAttachmentsService sysAttachmentsService;
    @Autowired
    private FiExporfinancingAcceptService fiExporfinancingAcceptService;
    @Autowired
    private FiExporfinancingPayService fiExporfinancingPayService;
    @Autowired
    private FiExporfinancingRecsService fiExporfinancingRecsService;

    /**
     * 查询出口融资申请表信息
     *
     * @param rowId 出口融资申请表ID
     * @return 出口融资申请表信息
     */
    @Override
    public FiExporfinancingApply selectFiExporfinancingApplyById(String rowId)
    {
        return fiExporfinancingApplyMapper.selectFiExporfinancingApplyById(rowId);
    }

    @Override
    public FiExporfinancingApply selectFiExporfinancingApplyByAssetCode(String assetCode)
    {
        return fiExporfinancingApplyMapper.selectFiExporfinancingApplyByAssetCode(assetCode);
    }

    /**
     * 查询出口融资申请表列表
     *
     * @param fiExporfinancingApply 出口融资申请表信息
     * @return 出口融资申请表集合
     */
    @Override
    public List<FiExporfinancingApply> selectFiExporfinancingApplyList(FiExporfinancingApply fiExporfinancingApply)
    {
        return fiExporfinancingApplyMapper.selectFiExporfinancingApplyList(fiExporfinancingApply);
    }

    @Override
    public List<FiExporfinancingApply> selectFiExporfinancingApplyExportList(FiExporfinancingApply fiExporfinancingApply)
    {
        return fiExporfinancingApplyMapper.selectFiExporfinancingApplyExportList(fiExporfinancingApply);
    }


    /**
     * 分页模糊查询出口融资申请表列表
     * @return 出口融资申请表集合
     */
    @Override
    public Page selectFiExporfinancingApplyListByLike(Query query)
    {
        FiExporfinancingApply fiExporfinancingApply =  BeanUtil.mapToBean(query.getCondition(), FiExporfinancingApply.class,false);
        query.setRecords(fiExporfinancingApplyMapper.selectFiExporfinancingApplyListByLike(query,fiExporfinancingApply));
        return query;
    }

    @Override
    public Page selectListsPage(Query query) {
        FiExporfinancingApply fiExporfinancingApply =  BeanUtil.mapToBean(query.getCondition(), FiExporfinancingApply.class,false);
        if(fiExporfinancingApply!=null){
            if(!"".equals(fiExporfinancingApply.getPlatformLevel()) && fiExporfinancingApply.getPlatformLevel()!=null){
                if(!"2".equals(fiExporfinancingApply.getPlatformLevel())) {//省平台查询所有不添加全选
                    if(!"".equals(fiExporfinancingApply.getCustomerCode()) && fiExporfinancingApply.getCustomerCode()!=null) {
                        CustomerPlatformInfo customerPlatformInfo = new CustomerPlatformInfo();
                        if ("1".equals(fiExporfinancingApply.getPlatformLevel())) {//查询所有子级的数据
                            customerPlatformInfo.setPlatformCode(fiExporfinancingApply.getCustomerCode());
                        } else if ("0".equals(fiExporfinancingApply.getPlatformLevel())) {//查询本级的数据
                            customerPlatformInfo.setCustomerCode(fiExporfinancingApply.getCustomerCode());
                        }
                        List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoService.selectCustomerNoByCode(customerPlatformInfo);
                        //查询社会信用代码为空的话返回为空
                        if (customerPlatformInfos.size() > 0) {
                            StringBuilder stringBuilder = new StringBuilder();
                            for (CustomerPlatformInfo customerPlatform : customerPlatformInfos) {
                                String substring = customerPlatform.getSocialUcCode().substring(8, 17);
                                stringBuilder.append(substring).append(",");
                            }
                            fiExporfinancingApply.setEntCodes(stringBuilder.toString());
                        } else {
                            return query;
                        }
                    }else{
                        return query;
                    }
                }
                query.setRecords(fiExporfinancingApplyMapper.selectListsPage(query, fiExporfinancingApply));
            }
        }
        return query;
    }

//    @Override
//    public R selectByEntCode(Map<String, Object> params) {
//        FiExporfinancingApply fiExporfinancingApply =  BeanUtil.mapToBean(params, FiExporfinancingApply.class,false);
//        R r=new R();
//        if(fiExporfinancingApply!=null){
//            if(!"".equals(fiExporfinancingApply.getAssetCode()) && fiExporfinancingApply.getAssetCode()!=null){
//                //融资申请
//                FiExporfinancingApply fiExporfinancingApplyByEntCode = fiExporfinancingApplyMapper.selectFiExporfinancingApplyByEntCode(fiExporfinancingApply);
//                //应收账款明细
//                FiExporfinancingInvoices fiExporfinancingInvoices=new FiExporfinancingInvoices();
//                fiExporfinancingInvoices.setAssetCode(fiExporfinancingApply.getAssetCode());
//                fiExporfinancingInvoices.setDeleteFlag("N");
//                List<FiExporfinancingInvoices> fiExporfinancingInvoicesList = fiExporfinancingInvoicesService.selectFiExporfinancingInvoicesList(fiExporfinancingInvoices);
//                if(fiExporfinancingInvoicesList.size()>0){
//                    BigDecimal totalMoney=BigDecimal.valueOf(0);
//                    for (FiExporfinancingInvoices exporfinancingInvoices:fiExporfinancingInvoicesList) {
//                        FiExporfinancingInvoicesManifests fiExporfinancingInvoicesManifests=new FiExporfinancingInvoicesManifests();
//                        fiExporfinancingInvoicesManifests.setAssetCode(fiExporfinancingApply.getAssetCode());
//                        fiExporfinancingInvoicesManifests.setInvoiceRowId(exporfinancingInvoices.getRowId());
//                        fiExporfinancingInvoicesManifests.setDeleteFlag("N");
//                        totalMoney=totalMoney.add(exporfinancingInvoices.getInvoiceAmountDecimal()).setScale(2,BigDecimal.ROUND_HALF_UP);
//                        List<FiExporfinancingInvoicesManifests> fiExporfinancingInvoicesManifestsList = fiExporfinancingInvoicesManifestsService.selectFiExporfinancingInvoicesManifestsList(fiExporfinancingInvoicesManifests);
//                        if(fiExporfinancingInvoicesManifestsList.size()>0){
//                            BigDecimal manifestAmountDecimalTotal=BigDecimal.valueOf(0);
//                            for (FiExporfinancingInvoicesManifests invoicesManifests:fiExporfinancingInvoicesManifestsList) {
//                                manifestAmountDecimalTotal=manifestAmountDecimalTotal.add(invoicesManifests.getManifestAmountDecimal());
//                            }
//                            exporfinancingInvoices.setManifestAmountDecimalTotal(manifestAmountDecimalTotal);//报关单占用总金额
//                            exporfinancingInvoices.setManifests(fiExporfinancingInvoicesManifestsList);//报关单明细
//                            exporfinancingInvoices.setManifestAmount(String.valueOf(fiExporfinancingInvoicesManifestsList.size()));//报关单数量
//                        }
//                    }
//                    fiExporfinancingApplyByEntCode.setInvoiceAmount(String.valueOf(fiExporfinancingInvoicesList.size()));//发票数量
//                    fiExporfinancingApplyByEntCode.setTotalMoney(totalMoney);//发票总金额
//                    fiExporfinancingApplyByEntCode.setInvoices(fiExporfinancingInvoicesList);//应收账款明细
//                }
//                //融资受理信息
//                FiExporfinancingAccept fiExportFinancingAccept=new FiExporfinancingAccept();
//                fiExportFinancingAccept.setAssetCode(fiExporfinancingApply.getAssetCode());
//                fiExportFinancingAccept.setDeleteFlag("N");
//                FiExporfinancingAccept fiExporfinancingAccept = fiExporfinancingAcceptService.selectFiExporfinancingAcceptByAssetCode(fiExportFinancingAccept);
//                fiExporfinancingApplyByEntCode.setFiExporfinancingAccept(fiExporfinancingAccept);
//                //查询放款信息
//                FiExporfinancingPay fiExportFinancingPay=new FiExporfinancingPay();
//                fiExportFinancingPay.setAssetCode(fiExporfinancingApply.getAssetCode());
//                fiExportFinancingPay.setDeleteFlag("N");
//                FiExporfinancingPay fiExporfinancingPay = fiExporfinancingPayService.selectFiExporfinancingPayByAssetCode(fiExportFinancingPay);
//                fiExporfinancingPay.setCurrency(fiExporfinancingApplyByEntCode.getCurrency());
//                fiExporfinancingApplyByEntCode.setFiExportFinancingPay(fiExporfinancingPay);
//                fiExporfinancingApplyByEntCode.setPayAmountDecimalAmount(fiExporfinancingPay.getPayAmountDecimal());
//                //查询还款信息
//                FiExporfinancingRecs fiExporfinancingRecs=new FiExporfinancingRecs();
//                fiExporfinancingRecs.setAssetCode(fiExporfinancingApply.getAssetCode());
//                fiExportFinancingPay.setDeleteFlag("N");
//                List<FiExporfinancingRecs> fiExporfinancingRecsList = fiExporfinancingRecsService.selectFiExporfinancingRecsList(fiExporfinancingRecs);
//                if(fiExporfinancingRecsList.size()>0){
//                    BigDecimal recAmountDecimalAmount=BigDecimal.valueOf(0);
//                    for (FiExporfinancingRecs exporfinancingRecs:fiExporfinancingRecsList) {
//                        recAmountDecimalAmount=recAmountDecimalAmount.add(exporfinancingRecs.getRecAmountDecimal());
//                        exporfinancingRecs.setInvoiceCurrency(fiExporfinancingApplyByEntCode.getCurrency());
//                    }
//                    fiExporfinancingApplyByEntCode.setRecAmountDecimalAmount(recAmountDecimalAmount);
//                    fiExporfinancingApplyByEntCode.setFiExporfinancingRecsList(fiExporfinancingRecsList);
//                }
//                r.setData(fiExporfinancingApplyByEntCode);
//                r.setB(true);
//                r.setCode(200);
//            }else{
//                r.setB(false);
//                r.setCode(500);
//                r.setMsg("请传入业务编码");
//                return r;
//            }
//        }else{
//            r.setB(false);
//            r.setCode(500);
//            r.setMsg("请传入业务编码");
//            return r;
//        }
//        return r;
//    }

    @Override
    public Page selectExitListByPage(Query query) {
        FiExporfinancingApply fiExporfinancingApply =  BeanUtil.mapToBean(query.getCondition(), FiExporfinancingApply.class,false);
        if(fiExporfinancingApply!=null) {
            if (!"".equals(fiExporfinancingApply.getPlatformLevel()) && fiExporfinancingApply.getPlatformLevel() != null) {
                if(!"2".equals(fiExporfinancingApply.getPlatformLevel())) {//省平台查询所有不添加全选
                    CustomerPlatformInfo customerPlatformInfo = new CustomerPlatformInfo();
                    if(!"".equals(fiExporfinancingApply.getCustomerCode()) && fiExporfinancingApply.getCustomerCode()!=null)
                    {
                        if ("1".equals(fiExporfinancingApply.getPlatformLevel())) {//查询所有子级的数据
                            customerPlatformInfo.setPlatformCode(fiExporfinancingApply.getCustomerCode());
                        } else if ("0".equals(fiExporfinancingApply.getPlatformLevel())) {//查询本级的数据
                            customerPlatformInfo.setCustomerCode(fiExporfinancingApply.getCustomerCode());
                        }
                        List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoService.selectCustomerNoByCode(customerPlatformInfo);

                        //判断查询社会信用代码为空的话返回是否为空
                        if (customerPlatformInfos.size() > 0) {
                            StringBuilder stringBuilder = new StringBuilder();
                            for (CustomerPlatformInfo customerPlatform : customerPlatformInfos) {
                                String substring = customerPlatform.getSocialUcCode().substring(8,17);
                                stringBuilder.append(substring).append(",");
                            }
                            fiExporfinancingApply.setEntCodes(stringBuilder.toString());
                        } else {
                            return query;
                        }
                    }else{
                        return query;
                    }
                }
                query.setRecords(fiExporfinancingApplyMapper.selectExitListByPage(query, fiExporfinancingApply));
            }
        }
        return query;
    }

    @Override
    public R selectListSum(Map<String, Object> params) {
        FiExporfinancingApply fiExporfinancingApply =  BeanUtil.mapToBean(params, FiExporfinancingApply.class,false);
        R r=new R();
        if(fiExporfinancingApply!=null) {
            if (StringUtils.isNotBlank(fiExporfinancingApply.getPlatformLevel())) {
                if (!"2".equals(fiExporfinancingApply.getPlatformLevel())) {//省平台查询所有不添加全选
                    CustomerPlatformInfo customerPlatformInfo = new CustomerPlatformInfo();
                    if (StringUtils.isNotBlank(fiExporfinancingApply.getCustomerCode())) {
                        if ("1".equals(fiExporfinancingApply.getPlatformLevel())) {//查询所有子级的数据
                            customerPlatformInfo.setPlatformCode(fiExporfinancingApply.getCustomerCode());
                        } else if ("0".equals(fiExporfinancingApply.getPlatformLevel())) {//查询本级的数据
                            customerPlatformInfo.setCustomerCode(fiExporfinancingApply.getCustomerCode());
                        }
                        List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoService.selectCustomerNoByCode(customerPlatformInfo);

                        //判断查询社会信用代码为空的话返回是否为空
                        if (customerPlatformInfos.size() > 0) {
                            StringBuilder stringBuilder = new StringBuilder();
                            for (CustomerPlatformInfo customerPlatform : customerPlatformInfos) {
                                String substring = customerPlatform.getSocialUcCode().substring(8, 17);
                                stringBuilder.append(substring).append(",");
                            }
                            fiExporfinancingApply.setEntCodes(stringBuilder.toString());
                        } else {
                            r.setB(false);
                            r.setMsg("查询统一信用代码为空");
                            r.setCode(500);
                            return r;
                        }
                    } else {
                        r.setB(false);
                        r.setMsg("请传入当前登录人账号 ");
                        r.setCode(500);
                        return r;
                    }
                }
                List<FiExporfinancingApply> fiExporfinancingApplies = fiExporfinancingApplyMapper.selectGroupEntSocialName(fiExporfinancingApply);

                fiExporfinancingApply.setAssetStatePayAndRec("PAY");
                List<FiExporfinancingApply> paySum = fiExporfinancingApplyMapper.selectfiExporfinancingApplyPayAndRecSum(fiExporfinancingApply);
                fiExporfinancingApply.setAssetStatePayAndRec("REC");
                List<FiExporfinancingApply> recSum = fiExporfinancingApplyMapper.selectfiExporfinancingApplyPayAndRecSum(fiExporfinancingApply);
                Map<String,Object> map=new HashMap<>();
                map.put("paySum",paySum);
                map.put("recSum",recSum);
                map.put("enterprise",fiExporfinancingApplies.size());
                r.setData(map);
                r.setB(true);
                r.setCode(200);
                return r;
            }else{
                r.setB(false);
                r.setMsg("请传入platformLever");
                r.setCode(500);
                return r;
            }
        }
        return r;
    }

    @Override
    public R selectExitListBySum(Map<String, Object> params) {
        FiExporfinancingApply fiExporfinancingApply =  BeanUtil.mapToBean(params, FiExporfinancingApply.class,false);
        R r=new R();
        if(fiExporfinancingApply!=null) {
            if (StringUtils.isNotBlank(fiExporfinancingApply.getPlatformLevel())) {
                if(!"2".equals(fiExporfinancingApply.getPlatformLevel())) {//省平台查询所有不添加全选
                    CustomerPlatformInfo customerPlatformInfo = new CustomerPlatformInfo();
                    if(StringUtils.isNotBlank(fiExporfinancingApply.getCustomerCode()))
                    {
                        if ("1".equals(fiExporfinancingApply.getPlatformLevel())) {//查询所有子级的数据
                            customerPlatformInfo.setPlatformCode(fiExporfinancingApply.getCustomerCode());
                        } else if ("0".equals(fiExporfinancingApply.getPlatformLevel())) {//查询本级的数据
                            customerPlatformInfo.setCustomerCode(fiExporfinancingApply.getCustomerCode());
                        }
                        List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoService.selectCustomerNoByCode(customerPlatformInfo);

                        //判断查询社会信用代码为空的话返回是否为空
                        if (customerPlatformInfos.size() > 0) {
                            StringBuilder stringBuilder = new StringBuilder();
                            for (CustomerPlatformInfo customerPlatform : customerPlatformInfos) {
                                String substring = customerPlatform.getSocialUcCode().substring(8,17);
                                stringBuilder.append(substring).append(",");
                            }
                            fiExporfinancingApply.setEntCodes(stringBuilder.toString());
                        } else {
                            r.setB(false);
                            r.setMsg("查询统一信用代码为空");
                            r.setCode(500);
                            return r;
                        }
                    }else{
                        r.setB(false);
                        r.setMsg("请传入当前登录人账号");
                        r.setCode(500);
                        return r;
                    }
                }
                List<FiExporfinancingApply> fiExporfinancingApplies = fiExporfinancingApplyMapper.selectExitListBySum(fiExporfinancingApply);

                fiExporfinancingApply.setAssetStatePayAndRec("PAY,REC,CLEAR");
                List<FiExporfinancingApply> paySum = fiExporfinancingApplyMapper.selectExitListPayAndRecSum(fiExporfinancingApply);
                fiExporfinancingApply.setAssetStatePayAndRec("REC,PAY");
                List<FiExporfinancingApply> recSum = fiExporfinancingApplyMapper.selectExitListPayAndRecSum(fiExporfinancingApply);
                Map<String,Object> map=new HashMap<>();
                map.put("paySum",paySum);
                map.put("recSum",recSum);
                map.put("enterprise",fiExporfinancingApplies.size());
                r.setData(map);
                r.setB(true);
                r.setCode(200);
                return r;
            }else{
                r.setB(false);
                r.setMsg("请传入platformLever");
                r.setCode(500);
                return r;
            }
        }
        return r;
    }


    /**
     * 新增出口融资申请表
     *
     * @param fiExporfinancingApply 出口融资申请表信息
     * @return 结果
     */
    @Override
    public int insertFiExporfinancingApply(FiExporfinancingApply fiExporfinancingApply)
    {
        return fiExporfinancingApplyMapper.insertFiExporfinancingApply(fiExporfinancingApply);
    }

    /**
     * 修改出口融资申请表
     *
     * @param fiExporfinancingApply 出口融资申请表信息
     * @return 结果
     */
    @Override
    public int updateFiExporfinancingApply(FiExporfinancingApply fiExporfinancingApply)
    {
        return fiExporfinancingApplyMapper.updateFiExporfinancingApply(fiExporfinancingApply);
    }

    @Override
    public int updateFiExporfinancingApplyByAssetCode(FiExporfinancingApply fiExporfinancingApply)
    {
        return fiExporfinancingApplyMapper.updateFiExporfinancingApplyByAssetCode(fiExporfinancingApply);
    }


    /**
     * 删除出口融资申请表
     *
     * @param rowId 出口融资申请表ID
     * @return 结果
     */
    @Override
    public int deleteFiExporfinancingApplyById(String rowId)
    {
        return fiExporfinancingApplyMapper.deleteFiExporfinancingApplyById( rowId);
    };


    /**
     * 批量删除出口融资申请表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiExporfinancingApplyByIds(Integer[] rowIds)
    {
        return fiExporfinancingApplyMapper.deleteFiExporfinancingApplyByIds( rowIds);
    }

}
