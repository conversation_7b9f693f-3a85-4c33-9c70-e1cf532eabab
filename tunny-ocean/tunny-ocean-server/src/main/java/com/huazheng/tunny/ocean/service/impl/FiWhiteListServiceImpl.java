package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.CustomerInfo;
import com.huazheng.tunny.ocean.api.entity.CustomerPlatformInfo;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseOperation;
import com.huazheng.tunny.ocean.api.vo.EntVo;
import com.huazheng.tunny.ocean.controller.SignatureController;
import com.huazheng.tunny.ocean.mapper.CustomerInfoMapper;
import com.huazheng.tunny.ocean.mapper.CustomerPlatformInfoMapper;
import com.huazheng.tunny.ocean.mapper.FiWhiteListMapper;
import com.huazheng.tunny.ocean.api.entity.FiWhiteList;
import com.huazheng.tunny.ocean.service.FiWhiteListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service("fiWhiteListService")
public class FiWhiteListServiceImpl extends ServiceImpl<FiWhiteListMapper, FiWhiteList> implements FiWhiteListService {

    @Autowired
    private FiWhiteListMapper fiWhiteListMapper;
    @Autowired
    private CustomerInfoMapper customerInfoMapper;
    @Autowired
    private CustomerPlatformInfoMapper customerPlatformInfoMapper;
    @Autowired
    private SignatureController signatureController;

    /**
     * 查询白名单注册信息
     *
     * @param rowId 白名单注册ID
     * @return 白名单注册信息
     */
    @Override
    public FiWhiteList selectFiWhiteListById(String rowId)
    {
        return fiWhiteListMapper.selectFiWhiteListById(rowId);
    }

    @Override
    public FiWhiteList selectFiWhiteListByEntSocialCode(String rowId)
    {
        return fiWhiteListMapper.selectFiWhiteListByEntSocialCode(rowId);
    }

    /**
     * 查询白名单注册列表
     *
     * @param fiWhiteList 白名单注册信息
     * @return 白名单注册集合
     */
    @Override
    public List<FiWhiteList> selectFiWhiteListList(FiWhiteList fiWhiteList)
    {
        return fiWhiteListMapper.selectFiWhiteListList(fiWhiteList);
    }


    /**
     * 分页模糊查询白名单注册列表
     * @return 白名单注册集合
     */
    @Override
    public Page selectFiWhiteListListByLike(Query query)
    {
        FiWhiteList fiWhiteList =  BeanUtil.mapToBean(query.getCondition(), FiWhiteList.class,false);
        query.setRecords(fiWhiteListMapper.selectFiWhiteListListByLike(query,fiWhiteList));
        return query;
    }

    /**
     * 分页模糊查询白名单注册列表
     * @return 白名单注册集合
     */
    @Override
    public Page selectFiWhiteListListForCity(Query query)
    {
        FiWhiteList fiWhiteList =  BeanUtil.mapToBean(query.getCondition(), FiWhiteList.class,false);
        query.setRecords(fiWhiteListMapper.selectFiWhiteListListForCity(query,fiWhiteList));
        return query;
    }

    /**
     * 分页模糊查询白名单注册列表
     * @return 白名单注册集合
     */
    @Override
    public Page selectFiWhiteListListForProvince(Query query)
    {
        FiWhiteList fiWhiteList =  BeanUtil.mapToBean(query.getCondition(), FiWhiteList.class,false);
        query.setRecords(fiWhiteListMapper.selectFiWhiteListListForProvince(query,fiWhiteList));
        return query;
    }

    /**
     * 新增白名单注册
     *
     * @param fiWhiteList 白名单注册信息
     * @return 结果
     */
    @Override
    public int insertFiWhiteList(FiWhiteList fiWhiteList)
    {
        return fiWhiteListMapper.insertFiWhiteList(fiWhiteList);
    }

    /**
     * 修改白名单注册
     *
     * @param fiWhiteList 白名单注册信息
     * @return 结果
     */
    @Override
    public int updateFiWhiteList(FiWhiteList fiWhiteList)
    {
        return fiWhiteListMapper.updateFiWhiteList(fiWhiteList);
    }

    @Override
    public int updateFiWhiteListByEntSocialCode(FiWhiteList fiWhiteList)
    {
        return fiWhiteListMapper.updateFiWhiteListByEntSocialCode(fiWhiteList);
    }

    /**
     * 修改白名单注册
     *
     * @param fiWhiteList 白名单注册信息
     * @return 结果
     */
    @Override
    public int outList(FiWhiteList fiWhiteList)
    {
        return fiWhiteListMapper.outList(fiWhiteList);
    }


    /**
     * 删除白名单注册
     *
     * @param rowId 白名单注册ID
     * @return 结果
     */
    @Override
    public int deleteFiWhiteListById(Integer rowId)
    {
        return fiWhiteListMapper.deleteFiWhiteListById( rowId);
    };


    /**
     * 批量删除白名单注册对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiWhiteListByIds(Integer[] rowIds)
    {
        return fiWhiteListMapper.deleteFiWhiteListByIds( rowIds);
    }

    @Override
    public R syncRecommendedEnt(String entCode, String operationType)
    {
        String msg = "操作成功！";
        Boolean flag = true;
        String from = "齐鲁号:";

        CustomerInfo info = new CustomerInfo();
        info.setSocialUcCode(entCode);
        info.setDeleteFlag("N");
        List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoListBySocialUcCode2(info);
        if(CollUtil.isNotEmpty(customerInfos)){
            EntVo entVo = new EntVo();
            entVo.setEntCode(customerInfos.get(0).getSocialUcCode());
            entVo.setEntName(customerInfos.get(0).getCompanyName());
            entVo.setEntAbbreviation(customerInfos.get(0).getCompanyShortName());
            entVo.setEntType(customerInfos.get(0).getResveredField03());
            entVo.setContactAddr(customerInfos.get(0).getConnectAddress());

            EfWarehouseOperation operation = new EfWarehouseOperation();
            operation.setOperationType(operationType);
            operation.setOperationTime(String.valueOf(LocalDateTime.now()));
            entVo.setOperationInfo(operation);

            //调用中钞接口，插入申请数据
            String json = JSONUtil.parseObj(entVo, true).toStringPretty();
            final String result = signatureController.doPost("/syncRecommendedEnt", json);
            JSONObject dataObject = JSONUtil.parseObj(result);
            String code = String.valueOf(dataObject.get("code"));
            if(!"SUCCESS".equals(code)){
                from = "跨境平台:";
                msg = dataObject.get("msg") +" 错误代码："+code;
                flag = false;
                return new R<>(flag,from+msg);
            }

            //更新用户状态
            CustomerInfo info1 = new CustomerInfo();
            info1.setSocialUcCode(entCode);
            if("UPDATE_ENT".equals(operationType)){
                info1.setResveredField06("1");
            }else if("DELETE_ENT".equals(operationType)){
                info1.setResveredField06("0");
            }
            customerInfoMapper.updateCustomerInfoBySocialUcCode(info1);
        }else{
            msg = "没有查询到该用户信息！";
            flag = false;
        }

        return new R<>(flag,from+msg);
    }

    @Override
    public R syncEntInfo(CustomerInfo cus)
    {
        String msg = "操作成功！";
        Boolean flag = true;
        String from = "齐鲁号:";

        CustomerInfo info = new CustomerInfo();
        info.setSocialUcCode(cus.getSocialUcCode());
        info.setDeleteFlag("N");
        List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoListBySocialUcCode2(info);
        if(CollUtil.isNotEmpty(customerInfos)){
            CustomerPlatformInfo customerPlatformInfo  = new CustomerPlatformInfo();
            customerPlatformInfo.setCustomerCode(customerInfos.get(0).getCustomerCode());
            customerPlatformInfo.setPlatformCode(cus.getPlatformCode());
            CustomerPlatformInfo customerPlatformInfo1 = customerPlatformInfoMapper.selectPlatformCodeByCustomerCode(customerPlatformInfo);
            if(customerPlatformInfo1==null){
                CustomerPlatformInfo customerPlatformInfo2  = new CustomerPlatformInfo();
                customerPlatformInfo2.setCustomerCode(customerInfos.get(0).getCustomerCode());
                final List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoMapper.selectCustomerPlatformInfoListByLike(customerPlatformInfo2);
                if(CollUtil.isNotEmpty(customerPlatformInfos)){
                    customerPlatformInfo1 = customerPlatformInfos.get(0);
                }else{
                    flag = false;
                    msg = "用户信息不完善，无法同步！";
                    return new R<>(flag,from+msg);
                }
            }

            //更新用户状态
            CustomerInfo info1 = new CustomerInfo();
            info1.setSocialUcCode(cus.getSocialUcCode());
            /*if("UPDATE_ENT".equals(operationType)){
                info1.setResveredField06("1");
            }else if("DELETE_ENT".equals(operationType)){
                info1.setResveredField06("0");
            }*/
            info1.setIsEf("1");
            customerInfoMapper.updateCustomerInfoBySocialUcCode(info1);
            final FiWhiteList fiWhiteList1 = fiWhiteListMapper.selectFiWhiteListByEntSocialCode(customerInfos.get(0).getSocialUcCode());
            if(fiWhiteList1!=null){
                FiWhiteList fiWhiteList = new FiWhiteList();
                fiWhiteList.setRowId(fiWhiteList1.getRowId());
                fiWhiteList.setIsEf("1");
                fiWhiteList.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                fiWhiteList.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                fiWhiteList.setUpdateTime(LocalDateTime.now());
                fiWhiteListMapper.updateFiWhiteList(fiWhiteList);
            }else{
                FiWhiteList fiWhiteList = new FiWhiteList();
                fiWhiteList.setRowId(UUID.randomUUID().toString());
                fiWhiteList.setEntSocialCode(customerInfos.get(0).getSocialUcCode());
                fiWhiteList.setEntCode(customerInfos.get(0).getCustomerCode());
                fiWhiteList.setEntName(customerInfos.get(0).getCompanyName());
                fiWhiteList.setEntType(customerInfos.get(0).getResveredField03());
                fiWhiteList.setContacts(customerPlatformInfo1.getContactPerson());
                fiWhiteList.setBankAccount(customerInfos.get(0).getOpenBank());
                fiWhiteList.setOfficeAddress(customerInfos.get(0).getConnectAddress());
                fiWhiteList.setContactsPhone(customerPlatformInfo1.getContactNo());
                fiWhiteList.setCorporateAccount(customerInfos.get(0).getBankAccount());
                fiWhiteList.setBusinessLicenceUrl(customerPlatformInfo1.getPictureUrl());
                fiWhiteList.setPlatformCode(customerPlatformInfo1.getPlatformCode());
                fiWhiteList.setIsEf("1");
                fiWhiteList.setAddWho(SecurityUtils.getUserInfo().getUserName());
                fiWhiteList.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                fiWhiteList.setAddTime(LocalDateTime.now());
                fiWhiteListMapper.insertFiWhiteList(fiWhiteList);
            }

        }else{
            msg = "没有查询到该用户信息！";
            flag = false;
        }

        return new R<>(0,flag,null,from+msg);
    }

    @Override
    public R commit(CustomerInfo cus)
    {
        String msg = "操作成功！";
        Boolean flag = true;
        String from = "齐鲁号:";

        CustomerInfo info = new CustomerInfo();
        info.setSocialUcCode(cus.getSocialUcCode());
        info.setDeleteFlag("N");
        List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoListBySocialUcCode2(info);
        final FiWhiteList fiWhiteList1 = fiWhiteListMapper.selectFiWhiteListByEntSocialCode(customerInfos.get(0).getSocialUcCode());
        if(CollUtil.isNotEmpty(customerInfos)){
            CustomerPlatformInfo customerPlatformInfo  = new CustomerPlatformInfo();
            customerPlatformInfo.setCustomerCode(customerInfos.get(0).getCustomerCode());
            if(fiWhiteList1!=null){
                if(StrUtil.isNotEmpty(fiWhiteList1.getPlatformCode())){
                    customerPlatformInfo.setPlatformCode(fiWhiteList1.getPlatformCode());
                }
            }

            CustomerPlatformInfo customerPlatformInfo1 = customerPlatformInfoMapper.selectPlatformCodeByCustomerCode(customerPlatformInfo);
            if(customerPlatformInfo1==null){
                CustomerPlatformInfo customerPlatformInfo2  = new CustomerPlatformInfo();
                customerPlatformInfo2.setCustomerCode(customerInfos.get(0).getCustomerCode());
                final List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoMapper.selectCustomerPlatformInfoListByLike(customerPlatformInfo2);
                if(CollUtil.isNotEmpty(customerPlatformInfos)){
                    customerPlatformInfo1 = customerPlatformInfos.get(0);
                }else{
                    flag = false;
                    msg = "用户信息不完善，无法同步！";
                    return new R<>(flag,from+msg);
                }
            }

            EntVo entVo = new EntVo();
            entVo.setEntSocialCode(customerInfos.get(0).getSocialUcCode());
            entVo.setEntName(customerInfos.get(0).getCompanyName());
            entVo.setEntShortName(customerInfos.get(0).getCompanyShortName());
            entVo.setEntType(customerInfos.get(0).getResveredField03());
            entVo.setOwnershipType("");
            entVo.setBusinessTerm("");
            entVo.setRegistrationDate("");
            entVo.setRegistrationAddress("");
            entVo.setContactAddress(customerInfos.get(0).getConnectAddress());
            entVo.setRegisteredCapitalCurrency("");
            entVo.setRegisteredCapital(customerInfos.get(0).getResveredField04());
            entVo.setShareholdersNum("");

            entVo.setLegalPersonIdentificationType("");
            entVo.setLegalPersonIdentificationNo("");

            entVo.setLegalPersonEmail("");
            entVo.setBusinessScope("");
            entVo.setLegalPersonIdentificationPhotoFront("");
            entVo.setLegalPersonIdentificationPhotoBack("");
            if(customerPlatformInfo1!=null){
                entVo.setLegalPerson(customerPlatformInfo1.getContactPerson());
                entVo.setLegalPersonContactPhone(customerPlatformInfo1.getContactNo());
                String pictureUrl = customerPlatformInfo1.getPictureUrl();
                /*if(pictureUrl.contains("*************:10135")){
                    pictureUrl = pictureUrl.replace("*************:10135","*************:10163");
                }*/
                entVo.setBussinessLicense(pictureUrl);
                entVo.setEntContactPerson(customerPlatformInfo1.getContactPerson());
                entVo.setEntContactPhone(customerPlatformInfo1.getContactNo());
            }

            /*EfWarehouseOperation operation = new EfWarehouseOperation();
            operation.setOperationType(operationType);
            operation.setOperationTime(String.valueOf(LocalDateTime.now()));
            entVo.setOperationInfo(operation);*/

            //调用E融接口，插入申请数据
            String jsonEf = JSONUtil.parseObj(entVo, true).toStringPretty();
            final String resultEf = signatureController.doPostEf("/efwarehouseapply/syncEntInfo", jsonEf);
            JSONObject dataObjectEf = JSONUtil.parseObj(resultEf);
//            String codeEf = String.valueOf(dataObjectEf.get("code"));
            flag = Boolean.valueOf(String.valueOf(dataObjectEf.get("b")));
            if(!flag){
                from = "E融平台:";
                msg = dataObjectEf.get("msg")+"";
                return new R<>(flag,from+msg);
            }


            //调用中钞接口，插入申请数据
            String json = JSONUtil.parseObj(entVo, true).toStringPretty();
            final String result = signatureController.doPost("/v1/safe/warehouse/syncEntInfo", json);
            JSONObject dataObject = JSONUtil.parseObj(result);
//            String code = String.valueOf(dataObject.get("code"));
            flag = Boolean.valueOf(String.valueOf(dataObject.get("b")));
            if(!flag){
                from = "中钞平台:";
                msg = dataObject.get("msg") +"";
                return new R<>(flag,from+msg);
            }

            //更新用户状态
            CustomerInfo info1 = new CustomerInfo();
            info1.setSocialUcCode(cus.getSocialUcCode());
            info1.setIsEf("2");
            customerInfoMapper.updateCustomerInfoBySocialUcCode(info1);

            if(fiWhiteList1!=null){
                FiWhiteList fiWhiteList = new FiWhiteList();
                fiWhiteList.setRowId(fiWhiteList1.getRowId());
                fiWhiteList.setIsEf("2");
                fiWhiteList.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                fiWhiteList.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                fiWhiteList.setUpdateTime(LocalDateTime.now());
                fiWhiteListMapper.updateFiWhiteList(fiWhiteList);
            }
            /*else{
                FiWhiteList fiWhiteList = new FiWhiteList();
                fiWhiteList.setRowId(UUID.randomUUID().toString());
                fiWhiteList.setEntSocialCode(customerInfos.get(0).getSocialUcCode());
                fiWhiteList.setEntCode(customerInfos.get(0).getCustomerCode());
                fiWhiteList.setEntName(customerInfos.get(0).getCompanyName());
                fiWhiteList.setEntType(customerInfos.get(0).getResveredField03());
                fiWhiteList.setContacts(customerPlatformInfo1.getContactPerson());
                fiWhiteList.setBankAccount(customerInfos.get(0).getOpenBank());
                fiWhiteList.setOfficeAddress(customerInfos.get(0).getConnectAddress());
                fiWhiteList.setContactsPhone(customerPlatformInfo1.getContactNo());
                fiWhiteList.setCorporateAccount(customerInfos.get(0).getBankAccount());
                fiWhiteList.setBusinessLicenceUrl(customerPlatformInfo1.getPictureUrl());
                fiWhiteList.setPlatformCode(customerPlatformInfo1.getPlatformCode());
                fiWhiteList.setIsEf("2");
                fiWhiteList.setAddWho(SecurityUtils.getUserInfo().getUserName());
                fiWhiteList.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                fiWhiteList.setAddTime(LocalDateTime.now());
                fiWhiteListMapper.insertFiWhiteList(fiWhiteList);
            }*/

        }else{
            msg = "没有查询到该用户信息！";
            flag = false;
        }

        return new R<>(0,flag,null,from+msg);
    }
}
