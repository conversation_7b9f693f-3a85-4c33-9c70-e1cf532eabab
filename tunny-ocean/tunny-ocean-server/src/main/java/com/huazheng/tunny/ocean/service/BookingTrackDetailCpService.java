package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.BookingTrackDetailCp;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 运踪信息子表(市平台-省) 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-08-12 18:13:35
 */
public interface BookingTrackDetailCpService extends IService<BookingTrackDetailCp> {
    /**
     * 查询运踪信息子表(市平台-省)信息
     *
     * @param rowId 运踪信息子表(市平台-省)ID
     * @return 运踪信息子表(市平台-省)信息
     */
    public BookingTrackDetailCp selectBookingTrackDetailCpById(String rowId);

    /**
     * 查询运踪信息子表(市平台-省)列表
     *
     * @param bookingTrackDetailCp 运踪信息子表(市平台-省)信息
     * @return 运踪信息子表(市平台-省)集合
     */
    public List<BookingTrackDetailCp> selectBookingTrackDetailCpList(BookingTrackDetailCp bookingTrackDetailCp);


    /**
     * 分页模糊查询运踪信息子表(市平台-省)列表
     * @return 运踪信息子表(市平台-省)集合
     */
    public Page selectBookingTrackDetailCpListByLike(Query query);



    /**
     * 新增运踪信息子表(市平台-省)
     *
     * @param bookingTrackDetailCp 运踪信息子表(市平台-省)信息
     * @return 结果
     */
    public int insertBookingTrackDetailCp(BookingTrackDetailCp bookingTrackDetailCp);

    /**
     * 修改运踪信息子表(市平台-省)
     *
     * @param bookingTrackDetailCp 运踪信息子表(市平台-省)信息
     * @return 结果
     */
    public int updateBookingTrackDetailCp(BookingTrackDetailCp bookingTrackDetailCp);

    /**
     * 删除运踪信息子表(市平台-省)
     *
     * @param rowId 运踪信息子表(市平台-省)ID
     * @return 结果
     */
    public int deleteBookingTrackDetailCpById(String rowId);

    /**
     * 批量删除运踪信息子表(市平台-省)
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteBookingTrackDetailCpByIds(Integer[] rowIds);

}

