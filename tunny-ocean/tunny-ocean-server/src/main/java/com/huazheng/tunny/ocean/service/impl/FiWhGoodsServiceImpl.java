package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.api.entity.FiWhGoods;
import com.huazheng.tunny.ocean.mapper.FiWhGoodsMapper;
import com.huazheng.tunny.ocean.service.FiWhGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fiWhGoodsService")
public class FiWhGoodsServiceImpl extends ServiceImpl<FiWhGoodsMapper, FiWhGoods> implements FiWhGoodsService {

    @Autowired
    private FiWhGoodsMapper fiWhGoodsMapper;

    public FiWhGoodsMapper getFiWhGoodsMapper() {
        return fiWhGoodsMapper;
    }

    public void setFiWhGoodsMapper(FiWhGoodsMapper fiWhGoodsMapper) {
        this.fiWhGoodsMapper = fiWhGoodsMapper;
    }

    /**
     * 查询仓单入库货物信息
     *
     * @param rowId 仓单入库货物ID
     * @return 仓单入库货物信息
     */
    @Override
    public FiWhGoods selectFiWhGoodsById(String rowId)
    {
        return fiWhGoodsMapper.selectFiWhGoodsById(rowId);
    }

    /**
     * 查询仓单入库货物列表
     *
     * @param fiWhGoods 仓单入库货物信息
     * @return 仓单入库货物集合
     */
    @Override
    public List<FiWhGoods> selectFiWhGoodsList(FiWhGoods fiWhGoods)
    {
        return fiWhGoodsMapper.selectFiWhGoodsList(fiWhGoods);
    }


    /**
     * 分页模糊查询仓单入库货物列表
     * @return 仓单入库货物集合
     */
    @Override
    public Page selectFiWhGoodsListByLike(Query query)
    {
        FiWhGoods fiWhGoods =  BeanUtil.mapToBean(query.getCondition(), FiWhGoods.class,false);
        query.setRecords(fiWhGoodsMapper.selectFiWhGoodsListByLike(query,fiWhGoods));
        return query;
    }

    /**
     * 新增仓单入库货物
     *
     * @param fiWhGoods 仓单入库货物信息
     * @return 结果
     */
    @Override
    public int insertFiWhGoods(FiWhGoods fiWhGoods)
    {
        return fiWhGoodsMapper.insertFiWhGoods(fiWhGoods);
    }

    /**
     * 修改仓单入库货物
     *
     * @param fiWhGoods 仓单入库货物信息
     * @return 结果
     */
    @Override
    public int updateFiWhGoods(FiWhGoods fiWhGoods)
    {
        return fiWhGoodsMapper.updateFiWhGoods(fiWhGoods);
    }


    /**
     * 删除仓单入库货物
     *
     * @param rowId 仓单入库货物ID
     * @return 结果
     */
    @Override
    public int deleteFiWhGoodsById(String rowId)
    {
        return fiWhGoodsMapper.deleteFiWhGoodsById( rowId);
    };

    @Override
    public int deleteFiWhGoodsByWrCode(String wrCode)
    {
        return fiWhGoodsMapper.deleteFiWhGoodsByWrCode( wrCode);
    };


    /**
     * 批量删除仓单入库货物对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiWhGoodsByIds(Integer[] rowIds)
    {
        return fiWhGoodsMapper.deleteFiWhGoodsByIds( rowIds);
    }

}
