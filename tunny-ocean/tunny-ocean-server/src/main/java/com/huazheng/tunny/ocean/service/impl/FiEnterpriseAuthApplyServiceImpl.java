package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FiEnterpriseAuthApplyMapper;
import com.huazheng.tunny.ocean.api.entity.FiEnterpriseAuthApply;
import com.huazheng.tunny.ocean.service.FiEnterpriseAuthApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fiEnterpriseAuthApplyService")
public class FiEnterpriseAuthApplyServiceImpl extends ServiceImpl<FiEnterpriseAuthApplyMapper, FiEnterpriseAuthApply> implements FiEnterpriseAuthApplyService {

    @Autowired
    private FiEnterpriseAuthApplyMapper fiEnterpriseAuthApplyMapper;

    public FiEnterpriseAuthApplyMapper getFiEnterpriseAuthApplyMapper() {
        return fiEnterpriseAuthApplyMapper;
    }

    public void setFiEnterpriseAuthApplyMapper(FiEnterpriseAuthApplyMapper fiEnterpriseAuthApplyMapper) {
        this.fiEnterpriseAuthApplyMapper = fiEnterpriseAuthApplyMapper;
    }

    /**
     * 查询经营信息授权-企业授权申请表信息
     *
     * @param rowId 经营信息授权-企业授权申请表ID
     * @return 经营信息授权-企业授权申请表信息
     */
    @Override
    public FiEnterpriseAuthApply selectFiEnterpriseAuthApplyById(String rowId)
    {
        return fiEnterpriseAuthApplyMapper.selectFiEnterpriseAuthApplyById(rowId);
    }

    /**
     * 查询经营信息授权-企业授权申请表列表
     *
     * @param fiEnterpriseAuthApply 经营信息授权-企业授权申请表信息
     * @return 经营信息授权-企业授权申请表集合
     */
    @Override
    public List<FiEnterpriseAuthApply> selectFiEnterpriseAuthApplyList(FiEnterpriseAuthApply fiEnterpriseAuthApply)
    {
        return fiEnterpriseAuthApplyMapper.selectFiEnterpriseAuthApplyList(fiEnterpriseAuthApply);
    }


    /**
     * 分页模糊查询经营信息授权-企业授权申请表列表
     * @return 经营信息授权-企业授权申请表集合
     */
    @Override
    public Page selectFiEnterpriseAuthApplyListByLike(Query query)
    {
        FiEnterpriseAuthApply fiEnterpriseAuthApply =  BeanUtil.mapToBean(query.getCondition(), FiEnterpriseAuthApply.class,false);
        query.setRecords(fiEnterpriseAuthApplyMapper.selectFiEnterpriseAuthApplyListByLike(query,fiEnterpriseAuthApply));
        return query;
    }

    /**
     * 新增经营信息授权-企业授权申请表
     *
     * @param fiEnterpriseAuthApply 经营信息授权-企业授权申请表信息
     * @return 结果
     */
    @Override
    public int insertFiEnterpriseAuthApply(FiEnterpriseAuthApply fiEnterpriseAuthApply)
    {
        return fiEnterpriseAuthApplyMapper.insertFiEnterpriseAuthApply(fiEnterpriseAuthApply);
    }

    /**
     * 修改经营信息授权-企业授权申请表
     *
     * @param fiEnterpriseAuthApply 经营信息授权-企业授权申请表信息
     * @return 结果
     */
    @Override
    public int updateFiEnterpriseAuthApply(FiEnterpriseAuthApply fiEnterpriseAuthApply)
    {
        return fiEnterpriseAuthApplyMapper.updateFiEnterpriseAuthApply(fiEnterpriseAuthApply);
    }


    /**
     * 删除经营信息授权-企业授权申请表
     *
     * @param rowId 经营信息授权-企业授权申请表ID
     * @return 结果
     */
    public int deleteFiEnterpriseAuthApplyById(String rowId)
    {
        return fiEnterpriseAuthApplyMapper.deleteFiEnterpriseAuthApplyById( rowId);
    }


    /**
     * 批量删除经营信息授权-企业授权申请表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiEnterpriseAuthApplyByIds(Integer[] rowIds)
    {
        return fiEnterpriseAuthApplyMapper.deleteFiEnterpriseAuthApplyByIds( rowIds);
    }

}
