package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.FiHisFinancingInfo;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;
import java.util.Map;

/**
 * 历史融资信息 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-03-08 09:42:47
 */
public interface FiHisFinancingInfoService extends IService<FiHisFinancingInfo> {
    /**
     * 查询历史融资信息信息
     *
     * @param rowId 历史融资信息ID
     * @return 历史融资信息信息
     */
    public FiHisFinancingInfo selectFiHisFinancingInfoById(String rowId);

    /**
     * 查询历史融资信息列表
     *
     * @param fiHisFinancingInfo 历史融资信息信息
     * @return 历史融资信息集合
     */
    public List<FiHisFinancingInfo> selectFiHisFinancingInfoList(FiHisFinancingInfo fiHisFinancingInfo);


    /**
     * 分页模糊查询历史融资信息列表
     * @return 历史融资信息集合
     */
    public Page selectFiHisFinancingInfoListByLike(Query query);

    public R selectFiHisFinancing(Map<String, Object> params);



    /**
     * 新增历史融资信息
     *
     * @param fiHisFinancingInfo 历史融资信息信息
     * @return 结果
     */
    public int insertFiHisFinancingInfo(FiHisFinancingInfo fiHisFinancingInfo);

    /**
     * 修改历史融资信息
     *
     * @param fiHisFinancingInfo 历史融资信息信息
     * @return 结果
     */
    public int updateFiHisFinancingInfo(FiHisFinancingInfo fiHisFinancingInfo);

    /**
     * 删除历史融资信息
     *
     * @param rowId 历史融资信息ID
     * @return 结果
     */
    public int deleteFiHisFinancingInfoById(String rowId);

    /**
     * 批量删除历史融资信息
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiHisFinancingInfoByIds(Integer[] rowIds);

}

