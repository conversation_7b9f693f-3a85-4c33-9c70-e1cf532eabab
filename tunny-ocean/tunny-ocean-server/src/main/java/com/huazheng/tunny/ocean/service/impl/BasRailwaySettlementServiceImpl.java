package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.BasRailwaySettlementMapper;
import com.huazheng.tunny.ocean.api.entity.BasRailwaySettlement;
import com.huazheng.tunny.ocean.service.BasRailwaySettlementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("basRailwaySettlementService")
public class BasRailwaySettlementServiceImpl extends ServiceImpl<BasRailwaySettlementMapper, BasRailwaySettlement> implements BasRailwaySettlementService {

    @Autowired
    private BasRailwaySettlementMapper basRailwaySettlementMapper;

    public BasRailwaySettlementMapper getBasRailwaySettlementMapper() {
        return basRailwaySettlementMapper;
    }

    public void setBasRailwaySettlementMapper(BasRailwaySettlementMapper basRailwaySettlementMapper) {
        this.basRailwaySettlementMapper = basRailwaySettlementMapper;
    }

    /**
     * 查询中铁多联结算对象表信息
     *
     * @param rowId 中铁多联结算对象表ID
     * @return 中铁多联结算对象表信息
     */
    @Override
    public BasRailwaySettlement selectBasRailwaySettlementById(String rowId)
    {
        return basRailwaySettlementMapper.selectBasRailwaySettlementById(rowId);
    }

    /**
     * 查询余额类型
     * @param provincialCode
     * @return
     */
    @Override
    public List<String> getBanlanceType(String provincialCode){
        return basRailwaySettlementMapper.getBanlanceType(provincialCode);
    }

    /**
     * 查询中铁多联结算对象表列表
     *
     * @param basRailwaySettlement 中铁多联结算对象表信息
     * @return 中铁多联结算对象表集合
     */
    @Override
    public List<BasRailwaySettlement> selectBasRailwaySettlementList(BasRailwaySettlement basRailwaySettlement)
    {
        return basRailwaySettlementMapper.selectBasRailwaySettlementList(basRailwaySettlement);
    }


    /**
     * 分页模糊查询中铁多联结算对象表列表
     * @return 中铁多联结算对象表集合
     */
    @Override
    public Page selectBasRailwaySettlementListByLike(Query query)
    {
        BasRailwaySettlement basRailwaySettlement =  BeanUtil.mapToBean(query.getCondition(), BasRailwaySettlement.class,false);
        basRailwaySettlement.setDeleteFlag("N");
        query.setRecords(basRailwaySettlementMapper.selectBasRailwaySettlementListByLike(query,basRailwaySettlement));
        return query;
    }

    /**
     * 新增中铁多联结算对象表
     *
     * @param basRailwaySettlement 中铁多联结算对象表信息
     * @return 结果
     */
    @Override
    public int insertBasRailwaySettlement(BasRailwaySettlement basRailwaySettlement)
    {
        return basRailwaySettlementMapper.insertBasRailwaySettlement(basRailwaySettlement);
    }

    /**
     * 修改中铁多联结算对象表
     *
     * @param basRailwaySettlement 中铁多联结算对象表信息
     * @return 结果
     */
    @Override
    public int updateBasRailwaySettlement(BasRailwaySettlement basRailwaySettlement)
    {
        return basRailwaySettlementMapper.updateBasRailwaySettlement(basRailwaySettlement);
    }


    /**
     * 删除中铁多联结算对象表
     *
     * @param basRailwaySettlement 中铁多联结算对象表ID
     * @return 结果
     */
    public int deleteBasRailwaySettlementById(BasRailwaySettlement basRailwaySettlement)
    {
        return basRailwaySettlementMapper.deleteBasRailwaySettlementById( basRailwaySettlement);
    };


    /**
     * 批量删除中铁多联结算对象表对象
     *
     * @param  rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBasRailwaySettlementByIds(Integer[] rowIds)
    {
        return basRailwaySettlementMapper.deleteBasRailwaySettlementByIds( rowIds);
    }

}
