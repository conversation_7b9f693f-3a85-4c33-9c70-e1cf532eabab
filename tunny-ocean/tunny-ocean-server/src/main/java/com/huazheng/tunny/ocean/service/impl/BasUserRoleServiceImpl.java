package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.BasUserRoleMapper;
import com.huazheng.tunny.ocean.api.entity.BasUserRole;
import com.huazheng.tunny.ocean.service.BasUserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("basUserRoleService")
public class BasUserRoleServiceImpl extends ServiceImpl<BasUserRoleMapper, BasUserRole> implements BasUserRoleService {

    @Autowired
    private BasUserRoleMapper basUserRoleMapper;

    public BasUserRoleMapper getBasUserRoleMapper() {
        return basUserRoleMapper;
    }

    public void setBasUserRoleMapper(BasUserRoleMapper basUserRoleMapper) {
        this.basUserRoleMapper = basUserRoleMapper;
    }

    /**
     * 查询用户角色关联表信息
     *
     * @param rowId 用户角色关联表ID
     * @return 用户角色关联表信息
     */
    @Override
    public BasUserRole selectBasUserRoleById(String rowId)
    {
        return basUserRoleMapper.selectBasUserRoleById(rowId);
    }

    /**
     * 查询用户角色关联表列表
     *
     * @param basUserRole 用户角色关联表信息
     * @return 用户角色关联表集合
     */
    @Override
    public List<BasUserRole> selectBasUserRoleList(BasUserRole basUserRole)
    {
        return basUserRoleMapper.selectBasUserRoleList(basUserRole);
    }


    /**
     * 分页模糊查询用户角色关联表列表
     * @return 用户角色关联表集合
     */
    @Override
    public Page selectBasUserRoleListByLike(Query query)
    {
        BasUserRole basUserRole =  BeanUtil.mapToBean(query.getCondition(), BasUserRole.class,false);
        query.setRecords(basUserRoleMapper.selectBasUserRoleListByLike(query,basUserRole));
        return query;
    }

    /**
     * 新增用户角色关联表
     *
     * @param basUserRole 用户角色关联表信息
     * @return 结果
     */
    @Override
    public int insertBasUserRole(BasUserRole basUserRole)
    {
        return basUserRoleMapper.insertBasUserRole(basUserRole);
    }

    /**
     * 修改用户角色关联表
     *
     * @param basUserRole 用户角色关联表信息
     * @return 结果
     */
    @Override
    public int updateBasUserRole(BasUserRole basUserRole)
    {
        return basUserRoleMapper.updateBasUserRole(basUserRole);
    }


    /**
     * 删除用户角色关联表
     *
     * @param rowId 用户角色关联表ID
     * @return 结果
     */
    public int deleteBasUserRoleById(String rowId)
    {
        return basUserRoleMapper.deleteBasUserRoleById( rowId);
    };


    /**
     * 批量删除用户角色关联表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBasUserRoleByIds(Integer[] rowIds)
    {
        return basUserRoleMapper.deleteBasUserRoleByIds( rowIds);
    }

}
