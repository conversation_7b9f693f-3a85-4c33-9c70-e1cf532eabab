package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.EfDisposeIntentionListMapper;
import com.huazheng.tunny.ocean.api.entity.EfDisposeIntentionList;
import com.huazheng.tunny.ocean.service.EfDisposeIntentionListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("efDisposeIntentionListService")
public class EfDisposeIntentionListServiceImpl extends ServiceImpl<EfDisposeIntentionListMapper, EfDisposeIntentionList> implements EfDisposeIntentionListService {

    @Autowired
    private EfDisposeIntentionListMapper efDisposeIntentionListMapper;

    public EfDisposeIntentionListMapper getEfDisposeIntentionListMapper() {
        return efDisposeIntentionListMapper;
    }

    public void setEfDisposeIntentionListMapper(EfDisposeIntentionListMapper efDisposeIntentionListMapper) {
        this.efDisposeIntentionListMapper = efDisposeIntentionListMapper;
    }

    /**
     * 查询处置仓单编码信息
     *
     * @param rowId 处置仓单编码ID
     * @return 处置仓单编码信息
     */
    @Override
    public EfDisposeIntentionList selectEfDisposeIntentionListById(String rowId)
    {
        return efDisposeIntentionListMapper.selectEfDisposeIntentionListById(rowId);
    }

    /**
     * 查询处置仓单编码列表
     *
     * @param efDisposeIntentionList 处置仓单编码信息
     * @return 处置仓单编码集合
     */
    @Override
    public List<EfDisposeIntentionList> selectEfDisposeIntentionListList(EfDisposeIntentionList efDisposeIntentionList)
    {
        return efDisposeIntentionListMapper.selectEfDisposeIntentionListList(efDisposeIntentionList);
    }


    /**
     * 分页模糊查询处置仓单编码列表
     * @return 处置仓单编码集合
     */
    @Override
    public Page selectEfDisposeIntentionListListByLike(Query query)
    {
        EfDisposeIntentionList efDisposeIntentionList =  BeanUtil.mapToBean(query.getCondition(), EfDisposeIntentionList.class,false);
        query.setRecords(efDisposeIntentionListMapper.selectEfDisposeIntentionListListByLike(query,efDisposeIntentionList));
        return query;
    }

    /**
     * 新增处置仓单编码
     *
     * @param efDisposeIntentionList 处置仓单编码信息
     * @return 结果
     */
    @Override
    public int insertEfDisposeIntentionList(EfDisposeIntentionList efDisposeIntentionList)
    {
        return efDisposeIntentionListMapper.insertEfDisposeIntentionList(efDisposeIntentionList);
    }

    /**
     * 修改处置仓单编码
     *
     * @param efDisposeIntentionList 处置仓单编码信息
     * @return 结果
     */
    @Override
    public int updateEfDisposeIntentionList(EfDisposeIntentionList efDisposeIntentionList)
    {
        return efDisposeIntentionListMapper.updateEfDisposeIntentionList(efDisposeIntentionList);
    }


    /**
     * 删除处置仓单编码
     *
     * @param rowId 处置仓单编码ID
     * @return 结果
     */
    public int deleteEfDisposeIntentionListById(String rowId)
    {
        return efDisposeIntentionListMapper.deleteEfDisposeIntentionListById( rowId);
    };


    /**
     * 批量删除处置仓单编码对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfDisposeIntentionListByIds(Integer[] rowIds)
    {
        return efDisposeIntentionListMapper.deleteEfDisposeIntentionListByIds( rowIds);
    }

}
