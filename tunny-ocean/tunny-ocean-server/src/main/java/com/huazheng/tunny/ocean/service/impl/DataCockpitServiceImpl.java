package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.RptBusinessAnalysis;
import com.huazheng.tunny.ocean.api.vo.DataCockpitLineChartVO;
import com.huazheng.tunny.ocean.api.vo.DataCockpitTitleNumBerVO;
import com.huazheng.tunny.ocean.mapper.DataCockpitMapper;
import com.huazheng.tunny.ocean.service.DataCockpitService;
import com.huazheng.tunny.tools.collection.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2024/7/30 13:31
 */
@Service("dataCockpitServiceImpl")
public class DataCockpitServiceImpl implements DataCockpitService {

    @Autowired
    private DataCockpitMapper dataCockpitMapper;

    @Override
    public DataCockpitTitleNumBerVO selectDataCockpitTitleNumber(String startTime,
                                                                 String endTime) {

        // 获取当前登陆人信息
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String platformLevel = userInfo.getPlatformLevel();
        String platformCode = userInfo.getPlatformCode();

        RptBusinessAnalysis rptBusinessAnalysis = new RptBusinessAnalysis();

        rptBusinessAnalysis.setStartTime(startTime);
        rptBusinessAnalysis.setEndTime(endTime);

        List<String> platformCodes = new ArrayList<>();
        platformCodes.add("MC210800001"); // 青岛分
        platformCodes.add("MC210800002"); // 临沂分
        platformCodes.add("MC230700000"); // 投资发展
        platformCodes.add("MC230900001"); // 陆港
        platformCodes.add("MC211000001"); // 国储
        platformCodes.add("MC210800003"); // 烟台分
        platformCodes.add("MC240600000"); // 中铁

        if("1".equals(platformLevel)){
            rptBusinessAnalysis.setPlatformCode(platformCode);
        }else {
            rptBusinessAnalysis.setPlatformCodes(platformCodes);
            rptBusinessAnalysis.setPlatformCodePro(platformCode);
        }

        DataCockpitTitleNumBerVO dataCockpitTitleNumBerVO = dataCockpitMapper.selectDataCockpitTitleNumber(rptBusinessAnalysis);
        return dataCockpitTitleNumBerVO;
    }

    /**
     * 获取收入总计折线图
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public DataCockpitLineChartVO selectIncomeSumLineChart(String startTime, String endTime) {
        // 获取当前登陆人信息
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String platformCode = userInfo.getPlatformCode();

        RptBusinessAnalysis rptBusinessAnalysis = new RptBusinessAnalysis();

        rptBusinessAnalysis.setStartTime(startTime);
        rptBusinessAnalysis.setEndTime(endTime);


        rptBusinessAnalysis.setPlatformCodePro(platformCode);
        List<String> platformCodes = new ArrayList<>();
        platformCodes.add("MC210800001"); // 青岛分
        platformCodes.add("MC210800002"); // 临沂分
        platformCodes.add("MC230700000"); // 投资发展
        platformCodes.add("MC230900001"); // 陆港
        platformCodes.add("MC211000001"); // 国储
        platformCodes.add("MC210800003"); // 烟台分
        platformCodes.add("MC240600000"); // 中铁
        rptBusinessAnalysis.setPlatformCodes(platformCodes);

        DataCockpitLineChartVO dataCockpitLineChartVO = new DataCockpitLineChartVO();

        List<DataCockpitTitleNumBerVO> dataCockpitTitleNumBerVOS = dataCockpitMapper.selectDataCockpitTitleNumberByGroupPlatform(rptBusinessAnalysis);
        if (!CollectionUtil.isEmpty(dataCockpitTitleNumBerVOS)) {
            List<String> x = new ArrayList<>();
            List<BigDecimal> y = new ArrayList<>();
            for(DataCockpitTitleNumBerVO dataCockpitTitleNumBerVO : dataCockpitTitleNumBerVOS){
                if("MC210800001".equals(dataCockpitTitleNumBerVO.getPlatformCode())){
                    x.add("青岛分");
                }
                if("MC210800002".equals(dataCockpitTitleNumBerVO.getPlatformCode())){
                    x.add("临沂分");
                }
                if("MC230700000".equals(dataCockpitTitleNumBerVO.getPlatformCode())){
                    x.add("投资发展");
                }
                if("MC230900001".equals(dataCockpitTitleNumBerVO.getPlatformCode())){
                    x.add("陆港");
                }
                if("MC211000001".equals(dataCockpitTitleNumBerVO.getPlatformCode())){
                    x.add("国储");
                }
                if("MC210800003".equals(dataCockpitTitleNumBerVO.getPlatformCode())){
                    x.add("烟台分");
                }
                if("MC240600000".equals(dataCockpitTitleNumBerVO.getPlatformCode())){
                    x.add("中铁");
                }

                y.add(dataCockpitTitleNumBerVO.getIncomeSum());
            }
            dataCockpitLineChartVO.setDataListByX(x);
            dataCockpitLineChartVO.setDataListByY(y);
        }
        return dataCockpitLineChartVO;
    }

    /**
     * 获取各市单车平均收入
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public DataCockpitLineChartVO selectCityUnitTrainSum(String startTime, String endTime) {
        // 获取当前登陆人信息
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String platformCode = userInfo.getPlatformCode();

        RptBusinessAnalysis rptBusinessAnalysis = new RptBusinessAnalysis();

        rptBusinessAnalysis.setStartTime(startTime);
        rptBusinessAnalysis.setEndTime(endTime);


        rptBusinessAnalysis.setPlatformCodePro(platformCode);
        List<String> platformCodes = new ArrayList<>();
        platformCodes.add("MC210800001"); // 青岛分
        platformCodes.add("MC210800002"); // 临沂分
        platformCodes.add("MC230700000"); // 投资发展
        platformCodes.add("MC230900001"); // 陆港
        platformCodes.add("MC211000001"); // 国储
        platformCodes.add("MC210800003"); // 烟台分
        platformCodes.add("MC240600000"); // 中铁
        rptBusinessAnalysis.setPlatformCodes(platformCodes);

        DataCockpitLineChartVO dataCockpitLineChartVO = new DataCockpitLineChartVO();

        // 查询出车数
        List<DataCockpitTitleNumBerVO> dataCockpitTitleNumBerVOS = dataCockpitMapper.selectCityUnitTrainSum(rptBusinessAnalysis);

        // 查询出总收入
        List<DataCockpitTitleNumBerVO> dataCockpitNumBerVOS = dataCockpitMapper.selectDataCockpitTitleNumberByGroupPlatform(rptBusinessAnalysis);


        if(CollectionUtil.isNotEmpty(dataCockpitTitleNumBerVOS) && CollectionUtil.isNotEmpty(dataCockpitNumBerVOS)){
            Map<String, BigDecimal> trainMap = dataCockpitTitleNumBerVOS.stream().collect(Collectors.toMap(DataCockpitTitleNumBerVO::getPlatformCode, DataCockpitTitleNumBerVO::getTrainNum));
            Map<String, BigDecimal> incomeSumMap = dataCockpitNumBerVOS.stream().collect(Collectors.toMap(DataCockpitTitleNumBerVO::getPlatformCode, DataCockpitTitleNumBerVO::getIncomeSum));
            List<String> x = new ArrayList<>();
            List<BigDecimal> y = new ArrayList<>();
            // 算出平均收入
            for(String key : incomeSumMap.keySet()){
                if("MC210800001".equals(key)){
                    x.add("青岛分");
                }
                if("MC210800002".equals(key)){
                    x.add("临沂分");
                }
                if("MC230700000".equals(key)){
                    x.add("投资发展");
                }
                if("MC230900001".equals(key)){
                    x.add("陆港");
                }
                if("MC211000001".equals(key)){
                    x.add("国储");
                }
                if("MC210800003".equals(key)){
                    x.add("烟台分");
                }
                if("MC240600000".equals(key)){
                    x.add("中铁");
                }
                BigDecimal trainBigDecimal = trainMap.get(key);
                if(!ObjectUtils.isEmpty(trainBigDecimal)){

                    BigDecimal incomeBigDecimal = incomeSumMap.get(key);
                    if(!ObjectUtils.isEmpty(incomeBigDecimal)){
                        y.add(incomeBigDecimal.divide(trainBigDecimal, 2, RoundingMode.HALF_UP));
                    }
                }
            }
            dataCockpitLineChartVO.setDataListByX(x);
            dataCockpitLineChartVO.setDataListByY(y);
        }
        return dataCockpitLineChartVO;
    }

    /**
     * 获取各市单车平均收入 （市明细）
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public DataCockpitLineChartVO selectCityUnitTrainDetail(String startTime, String endTime) {
        // 获取当前登陆人信息
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String platformCode = userInfo.getPlatformCode();

        RptBusinessAnalysis rptBusinessAnalysis = new RptBusinessAnalysis();

        rptBusinessAnalysis.setStartTime(startTime);
        rptBusinessAnalysis.setEndTime(endTime);


        rptBusinessAnalysis.setPlatformCodePro(platformCode);
        List<String> platformCodes = new ArrayList<>();
        platformCodes.add("MC210800001"); // 青岛分
        platformCodes.add("MC210800002"); // 临沂分
        platformCodes.add("MC230700000"); // 投资发展
        platformCodes.add("MC230900001"); // 陆港
        platformCodes.add("MC211000001"); // 国储
        platformCodes.add("MC210800003"); // 烟台分
        platformCodes.add("MC240600000"); // 中铁
        rptBusinessAnalysis.setPlatformCodes(platformCodes);

        // 返回体
        DataCockpitLineChartVO dataCockpitLineChartVO = new DataCockpitLineChartVO();

        // 查询出各个进出口状态的收入总数
        // 出入过境总收入
        List<DataCockpitTitleNumBerVO> dataCockpitNumBerVOS = dataCockpitMapper.selectDataCockpitRansitByGroupPlatform(rptBusinessAnalysis);
        if(CollectionUtil.isNotEmpty(dataCockpitNumBerVOS)){
            List<String> x = new ArrayList<>();
            Map<String,List<BigDecimal>> y = new HashMap<>();
            y.put("I",new ArrayList<>());
            y.put("E",new ArrayList<>());
            y.put("P",new ArrayList<>());
            for(String key : platformCodes){
                if("MC210800001".equals(key)){
                    x.add("青岛分");
                }
                if("MC210800002".equals(key)){
                    x.add("临沂分");
                }
                if("MC230700000".equals(key)){
                    x.add("投资发展");
                }
                if("MC230900001".equals(key)){
                    x.add("陆港");
                }
                if("MC211000001".equals(key)){
                    x.add("国储");
                }
                if("MC210800003".equals(key)){
                    x.add("烟台分");
                }
                if("MC240600000".equals(key)){
                    x.add("中铁");
                }
                BigDecimal I = new BigDecimal("0");
                BigDecimal E = new BigDecimal("0");
                BigDecimal P = new BigDecimal("0");

                for(DataCockpitTitleNumBerVO dataCockpitTitleNumBerVO :dataCockpitNumBerVOS){
                    if(key.equals(dataCockpitTitleNumBerVO.getPlatformCode())){
                        if("I".equals(dataCockpitTitleNumBerVO.getRansit())){
                            I = dataCockpitTitleNumBerVO.getIncomeSum();
                        }
                        if("E".equals(dataCockpitTitleNumBerVO.getRansit())){
                            E = dataCockpitTitleNumBerVO.getIncomeSum();
                        }
                        if("P".equals(dataCockpitTitleNumBerVO.getRansit())){
                            P = dataCockpitTitleNumBerVO.getIncomeSum();
                        }
                    }
                }
                List<BigDecimal> iList = y.get("I");
                iList.add(I);
                y.put("I",iList);
                List<BigDecimal> eList = y.get("E");
                eList.add(E);
                y.put("E",eList);
                List<BigDecimal> pList = y.get("P");
                pList.add(P);
                y.put("P",pList);
            }
            dataCockpitLineChartVO.setDataMapByY(y);
            dataCockpitLineChartVO.setDataListByX(x);
        }
        return dataCockpitLineChartVO;
    }
}
