package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.ocean.api.entity.Announcement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.ocean.mapper.AnnouncementMapper;
import com.huazheng.tunny.ocean.service.AnnouncementService;
import org.springframework.beans.factory.annotation.Autowired;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * @Description: Service
 * @Author: wx
 * @Date: 2023-05-08 15:12:44
 */
@Slf4j
@Service
public class AnnouncementServiceImpl extends ServiceImpl<AnnouncementMapper, Announcement> implements AnnouncementService {

    @Autowired
    private AnnouncementMapper announcementMapper;

    /**
     * @Description: 分页
     * @Param: query
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-08 15:12:44
     */
    @Override
    public Page page(Query query) {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.add_time");
            query.setAsc(Boolean.FALSE);
        }
        Announcement param = BeanUtil.mapToBean(query.getCondition(), Announcement.class, false);
        query.setRecords(announcementMapper.pageAnnouncement(query, param));
        return query;
    }

    /**
     * @Description: 详情
     * @Param: id
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-08 15:12:44
     */
    @Override
    public R info(Integer id) {
        return new R<>(announcementMapper.infoAnnouncement(id));
    }

    /**
     * @Description: 保存
     * @Param: param
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-08 15:12:44
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R save(Announcement param) {
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
//        param.setPlatformCode(secruityUser.getUserName());
        param.setAddTime(LocalDateTime.now());
        param.setAddWho(secruityUser.getUserName());
        param.setAddWhoName(secruityUser.getRealName());
        // 默认禁用 改为前端传入
//        param.setSwitchType("1");
        if ("0".equals(param.getSwitchType())) {
            param.setPublishTime(LocalDateTime.now());
        }
        announcementMapper.saveAnnouncement(param);
        return new R<>();
    }

    /**
     * @Description: 修改
     * @Param: param
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-08 15:12:44
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R update(Announcement param) {
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
//        param.setPlatformCode(secruityUser.getUserName());
        param.setUpdateTime(LocalDateTime.now());
        param.setUpdateWho(secruityUser.getUserName());
        param.setUpdateWhoName(secruityUser.getRealName());
        announcementMapper.updateAnnouncement(param);
        return new R<>();
    }

    /**
     * @Description: 删除
     * @Param: id
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-08 15:12:44
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R delete(Integer id) {
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
//        Announcement announcement = this.announcementMapper.selectById(id);
        Announcement announcement = this.announcementMapper.infoAnnouncement(id);
        announcement.setDeleteFlag("Y");
        announcement.setDeleteTime(LocalDateTime.now());
        announcement.setDeleteWho(secruityUser.getUserName());
        announcement.setDeleteWhoName(secruityUser.getRealName());
        announcementMapper.updateAnnouncement(announcement);
        return new R<>();
    }

    /**
     * @Description: 列表
     * @Param: param
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-08 15:12:44
     */
    @Override
    public R list(Announcement param) {
        // 查询启用公告
        param.setSwitchType("0");
        return new R<>(announcementMapper.listAnnouncement(param));
    }

    /**
     * @Description: 启用禁用
     * @Param: param
     * @Return: com.huazheng.tunny.common.core.util.R
     * @Author: wx
     * @Date: 2023/5/8 16:45
     */
    @Override
    public R switchType(Announcement param) {
        LocalDateTime now = LocalDateTime.now();
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
        Announcement announcement = this.announcementMapper.infoAnnouncement(param.getId());
        announcement.setUpdateTime(now);
        announcement.setUpdateWho(secruityUser.getUserName());
        announcement.setUpdateWhoName(secruityUser.getRealName());
        announcement.setSwitchType(param.getSwitchType());

        if ("0".equals(announcement.getSwitchType())) {
            announcement.setPublishTime(now);
        }

        announcementMapper.updateAnnouncement(announcement);
        return new R();
    }

}
