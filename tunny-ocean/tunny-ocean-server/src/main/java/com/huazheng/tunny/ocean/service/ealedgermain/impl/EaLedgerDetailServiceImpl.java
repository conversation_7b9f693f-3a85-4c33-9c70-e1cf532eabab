package com.huazheng.tunny.ocean.service.ealedgermain.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.Shifmanagement;
import com.huazheng.tunny.ocean.api.entity.eabillmain.EaBillMain;
import com.huazheng.tunny.ocean.api.entity.eabillmain.EaFee;
import com.huazheng.tunny.ocean.api.entity.eabookingorder.EaContainerSummary;
import com.huazheng.tunny.ocean.api.entity.ealedgermain.EaLedgerMain;
import com.huazheng.tunny.ocean.api.enums.BillStageEnum;
import com.huazheng.tunny.ocean.api.enums.LedgerAuditStatus;
import com.huazheng.tunny.ocean.api.enums.PlatformLevelEnum;
import com.huazheng.tunny.ocean.api.util.SysCommon;
import com.huazheng.tunny.ocean.api.vo.ealedgermain.EaLedgerTieLuFeeVo;
import com.huazheng.tunny.ocean.mapper.ShifmanagementMapper;
import com.huazheng.tunny.ocean.mapper.eabillmain.EaBillMainMapper;
import com.huazheng.tunny.ocean.mapper.eabillmain.EaFeeMapper;
import com.huazheng.tunny.ocean.mapper.eabookingorder.EaContainerSummaryMapper;
import com.huazheng.tunny.ocean.mapper.ealedgermain.EaLedgerDetailMapper;
import com.huazheng.tunny.ocean.api.entity.ealedgermain.EaLedgerDetail;
import com.huazheng.tunny.ocean.mapper.ealedgermain.EaLedgerMainMapper;
import com.huazheng.tunny.ocean.service.eabillmain.EaBillMainService;
import com.huazheng.tunny.ocean.service.ealedgermain.EaLedgerDetailService;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service("eaLedgerDetailService")
public class EaLedgerDetailServiceImpl extends ServiceImpl<EaLedgerDetailMapper, EaLedgerDetail> implements EaLedgerDetailService {

    @Autowired
    private EaLedgerDetailMapper eaLedgerDetailMapper;
    @Autowired
    private EaLedgerMainMapper eaLedgerMainMapper;
    @Autowired
    private EaFeeMapper eaFeeMapper;
    @Autowired
    private EaContainerSummaryMapper eaContainerSummaryMapper;
    @Autowired
    private ShifmanagementMapper shifmanagementMapper;
    @Autowired
    private EaBillMainService eaBillMainService;

    /**
     * 查询台账细表信息
     *
     * @param ledgerDetailId 台账细表ID
     * @return 台账细表信息
     */
    @Override
    public EaLedgerDetail selectEaLedgerDetailById(Long ledgerDetailId) {
        return eaLedgerDetailMapper.selectEaLedgerDetailById(ledgerDetailId);
    }

    /**
     * 查询台账细表列表
     *
     * @param eaLedgerDetail 台账细表信息
     * @return 台账细表集合
     */
    @Override
    public List<EaLedgerDetail> selectEaLedgerDetailList(EaLedgerDetail eaLedgerDetail) {
        return eaLedgerDetailMapper.selectEaLedgerDetailList(eaLedgerDetail);
    }


    /**
     * 分页模糊查询台账细表列表
     *
     * @return 台账细表集合
     */
    @Override
    public Page selectEaLedgerDetailListByLike(Query query) {
        EaLedgerDetail eaLedgerDetail = BeanUtil.mapToBean(query.getCondition(), EaLedgerDetail.class, false);
        query.setRecords(eaLedgerDetailMapper.selectEaLedgerDetailListByLike(query, eaLedgerDetail));
        return query;
    }

    /**
     * 新增台账细表
     *
     * @param eaLedgerDetail 台账细表信息
     * @return 结果
     */
    @Override
    public int insertEaLedgerDetail(EaLedgerDetail eaLedgerDetail) {
        return eaLedgerDetailMapper.insertEaLedgerDetail(eaLedgerDetail);
    }

    /**
     * 修改台账细表
     *
     * @param eaLedgerDetail 台账细表信息
     * @return 结果
     */
    @Override
    public int updateEaLedgerDetail(EaLedgerDetail eaLedgerDetail) {
        return eaLedgerDetailMapper.updateEaLedgerDetail(eaLedgerDetail);
    }


    /**
     * 删除台账细表
     *
     * @param ledgerDetailId 台账细表ID
     * @return 结果
     */
    @Override
    public int deleteEaLedgerDetailById(Long ledgerDetailId) {
        return eaLedgerDetailMapper.deleteEaLedgerDetailById(ledgerDetailId);
    }


    /**
     * 批量删除台账细表对象
     *
     * @param ledgerDetailIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEaLedgerDetailByIds(Integer[] ledgerDetailIds) {
        return eaLedgerDetailMapper.deleteEaLedgerDetailByIds(ledgerDetailIds);
    }

    @Override
    public R containerFeeList(Map<String, Object> params) {
        if (params == null || params.get("shiftNo") == null) {
            return R.error("请传入班次号");
        }
        List<EaLedgerTieLuFeeVo> mapList = eaContainerSummaryMapper.selectEaContainerSummaryMapperByLikeGroup(params.get("shiftNo").toString());
        return R.success(mapList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateTieLuFee(List<EaLedgerTieLuFeeVo> list) {
        //铁路境内运费
        BigDecimal payableRailwayDomesticFee = BigDecimal.ZERO;
        //铁路境外运费
        BigDecimal payableRailwayForeignFee = BigDecimal.ZERO;
        //铁路境外原币
        BigDecimal payableRailwayForeignOriginal = BigDecimal.ZERO;
        List<EaContainerSummary> summaryList = new ArrayList<>();
        List<EaFee> feeList = new ArrayList<>();

        //台账明细-铁路费用
        for (EaLedgerTieLuFeeVo eaLedgerTieLuFeeVo : list) {
            payableRailwayDomesticFee = payableRailwayDomesticFee.add(eaLedgerTieLuFeeVo.getPayableRailwayDomesticFee().multiply(new BigDecimal(eaLedgerTieLuFeeVo.getContainerNum())));
            payableRailwayForeignFee = payableRailwayForeignFee.add(eaLedgerTieLuFeeVo.getPayableRailwayForeignFee().multiply(new BigDecimal(eaLedgerTieLuFeeVo.getContainerNum())));
            payableRailwayForeignOriginal = payableRailwayForeignOriginal.add(eaLedgerTieLuFeeVo.getPayableRailwayForeignOriginal().multiply(new BigDecimal(eaLedgerTieLuFeeVo.getContainerNum())));

            this.generateEaContainerSummary(summaryList, eaLedgerTieLuFeeVo);

            this.generateEaFee(feeList, eaLedgerTieLuFeeVo, SysCommon.DOMESTIC_FEE_CODE);
            this.generateEaFee(feeList, eaLedgerTieLuFeeVo, SysCommon.OVERSEAS_FEE_CODE);
        }
        EaLedgerMain main = new EaLedgerMain();
        main.setShiftNo(list.get(0).getShiftNo());
        main.setDelFlag(SysCommon.DELETE_FLAG_NO);
        main = eaLedgerMainMapper.selectOne(main);
        main.setPayableRailwayDomesticFee(payableRailwayDomesticFee);
        main.setPayableRailwayForeignFee(payableRailwayForeignFee);
        main.setPayableRailwayForeignOriginal(payableRailwayForeignOriginal);


        // 循环修改站台账明细
        for (EaLedgerTieLuFeeVo eaLedgerTieLuFeeVo : list) {
            eaLedgerDetailMapper.updateTieLuFee(eaLedgerTieLuFeeVo);
        }

        eaLedgerMainMapper.updateTieLuFee(main);

        // 循环修改站台账明细
        for (EaContainerSummary summary : summaryList) {
            eaContainerSummaryMapper.updateTieLuFee(summary);
        }

        // 循环修改站台账明细
        for (EaFee eaFee : feeList) {
            eaFeeMapper.updateTieLuFee(eaFee);
        }

        EaBillMain eaBillMain = new EaBillMain();
        eaBillMain.setShiftNo(main.getShiftNo());
        eaBillMain.setPayeeCode(SysCommon.RECEIVE_CODE_ZTDL);
        if (BillStageEnum.ONE.getKey().equals(main.getLedgerStage())) {
            eaBillMainService.generateBill(eaBillMain);
        } else {
            eaBillMain.setPlatformCode(main.getPlatformCode());
            eaBillMain.setPlatformName(main.getPlatformName());
            eaBillMainService.generateBillTwo(eaBillMain);
        }
        return R.success("修改成功");
    }

    @Override
    public void exportEaLedgerDetailExcel(EaLedgerDetail eaLedgerDetail, HttpServletResponse response) {
        if (eaLedgerDetail == null || StrUtil.isBlank(eaLedgerDetail.getShiftNo())) {
            return;
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        EaLedgerMain eaLedgerMain = eaLedgerMainMapper.selectEaLedgerMainByShiftNo(eaLedgerDetail.getShiftNo());
        if (eaLedgerMain == null) {
            return;
        }
        List<EaLedgerDetail> list = eaLedgerDetailMapper.selectEaLedgerDetailList(eaLedgerDetail);
        OutputStream out = null;
        try {
            Shifmanagement sel2 = new Shifmanagement();
            sel2.setShiftId(eaLedgerDetail.getShiftNo());
            sel2.setPlatformCode(eaLedgerMain.getPlatformCode());
            sel2.setDeleteFlag("N");
            List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel2);

            //创建sheet页
            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet("台账详情");
            //宽度
            for (int i = 0; i < 32; i++) {
                sheet.setColumnWidth(i, 60 * 80);
            }

            //字体
            XSSFFont font = workbook.createFont();
            font.setBold(true);
            font.setFontHeightInPoints((short) 14);
            font.setFontName("宋体");

            //字体
            XSSFFont font1 = workbook.createFont();
            font1.setBold(false);
            font1.setFontHeightInPoints((short) 12);
            font1.setFontName("宋体");

            XSSFCellStyle style = workbook.createCellStyle();
            style.setFont(font);
            style.setAlignment(HorizontalAlignment.CENTER);//水平居中
            style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
            style.setBorderBottom(BorderStyle.THIN); //下边框
            style.setBorderLeft(BorderStyle.THIN);//左边框
            style.setBorderTop(BorderStyle.THIN);//上边框
            style.setBorderRight(BorderStyle.THIN);//右边框
            style.setBottomBorderColor(HSSFColor.BLACK.index);
            style.setLeftBorderColor(HSSFColor.BLACK.index);
            style.setRightBorderColor(HSSFColor.BLACK.index);
            style.setTopBorderColor(HSSFColor.BLACK.index);

            XSSFCellStyle style1 = workbook.createCellStyle();
            style1.setFont(font1);
            style1.setAlignment(HorizontalAlignment.CENTER);//水平居中
            style1.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
            style1.setBorderBottom(BorderStyle.THIN); //下边框
            style1.setBorderLeft(BorderStyle.THIN);//左边框
            style1.setBorderTop(BorderStyle.THIN);//上边框
            style1.setBorderRight(BorderStyle.THIN);//右边框
            style1.setBottomBorderColor(HSSFColor.BLACK.index);
            style1.setLeftBorderColor(HSSFColor.BLACK.index);
            style1.setRightBorderColor(HSSFColor.BLACK.index);
            style1.setTopBorderColor(HSSFColor.BLACK.index);

            //第一行表头
            int rowIndex = 0;
            XSSFRow row1 = sheet.createRow(rowIndex);
            //班列号
            XSSFCell cell10 = row1.createCell(0);
            cell10.setCellValue("班列号");
            cell10.setCellStyle(style);
            XSSFCell cell11 = row1.createCell(1);
            cell11.setCellValue(shifmanagements.get(0).getShiftId());
            cell11.setCellStyle(style1);
            //班列名称
            XSSFCell cell12 = row1.createCell(2);
            cell12.setCellValue("班列名称");
            cell12.setCellStyle(style);
            XSSFCell cell13 = row1.createCell(3);
            cell13.setCellValue(shifmanagements.get(0).getShiftName());
            cell13.setCellStyle(style1);
            //省级班列单号
            XSSFCell cell14 = row1.createCell(4);
            cell14.setCellValue("省级班列单号");
            cell14.setCellStyle(style);
            XSSFCell cell15 = row1.createCell(5);
            if (CollUtil.isNotEmpty(shifmanagements)) {
                cell15.setCellValue(shifmanagements.get(0).getProvinceShiftNo());
            } else {
                cell15.setCellValue("");
            }
            cell15.setCellStyle(style1);
            //方向
            XSSFCell cell16 = row1.createCell(6);
            cell16.setCellValue("方向");
            cell16.setCellStyle(style);
            XSSFCell cell17 = row1.createCell(7);
            if ("G".equals(shifmanagements.get(0).getTrip())) {
                cell17.setCellValue("去程");
            } else if ("R".equalsIgnoreCase(shifmanagements.get(0).getTrip())) {
                cell17.setCellValue("回程");
            } else {
                cell17.setCellValue("");
            }
            cell17.setCellStyle(style1);
            //线路
            XSSFCell cell18 = row1.createCell(8);
            cell18.setCellValue("线路");
            cell18.setCellStyle(style);
            XSSFCell cell19 = row1.createCell(9);
            cell19.setCellValue(shifmanagements.get(0).getShippingLine());
            cell19.setCellStyle(style1);
            //发运城市
            XSSFCell cell110 = row1.createCell(10);
            cell110.setCellValue("发运城市");
            cell110.setCellStyle(style);
            XSSFCell cell111 = row1.createCell(11);
            cell111.setCellValue(shifmanagements.get(0).getDestinationName());
            cell111.setCellStyle(style1);
            //发运日期
            XSSFCell cell112 = row1.createCell(12);
            cell112.setCellValue("发运日期");
            cell112.setCellStyle(style);
            XSSFCell cell113 = row1.createCell(13);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String formattedDate = dateFormat.format(shifmanagements.get(0).getPlanShipTime());
            cell113.setCellValue(formattedDate);
            cell113.setCellStyle(style1);
            //类型
            XSSFCell cell114 = row1.createCell(14);
            cell114.setCellValue("类型");
            cell114.setCellStyle(style);
            XSSFCell cell115 = row1.createCell(15);
            String trainType = shifmanagements.get(0).getTrainType();
            if ("0".equals(trainType)) {
                trainType = "散列";
            } else {
                trainType = "整列";
            }
            cell115.setCellValue(trainType);
            cell115.setCellStyle(style1);
            //口岸站
            XSSFCell cell116 = row1.createCell(16);
            cell116.setCellValue("口岸站");
            cell116.setCellStyle(style);
            XSSFCell cell117 = row1.createCell(17);
            cell117.setCellValue(shifmanagements.get(0).getPortStation());
            cell117.setCellStyle(style1);
            //集结站
            XSSFCell cell118 = row1.createCell(18);
            cell118.setCellValue("集结站");
            cell118.setCellStyle(style);
            XSSFCell cell119 = row1.createCell(19);
            cell119.setCellValue(shifmanagements.get(0).getDestinationName());
            cell119.setCellStyle(style1);
            //列备注
            XSSFCell cell120 = row1.createCell(20);
            cell120.setCellValue("列备注");
            cell120.setCellStyle(style);
            XSSFCell cell121 = row1.createCell(21);
            cell121.setCellValue(shifmanagements.get(0).getShiftName());
            cell121.setCellStyle(style1);

            //另起一行
            rowIndex++;
            //第二行表头 构建复杂表头
            XSSFRow row2 = sheet.createRow(rowIndex);
            String[] title = new String[]{"序号", "箱号", "箱属", "箱型尺寸", "类型", "收货人", "发站", "目的国", "到站", "口岸代理", "品名", "件数", "货重（kgs）", "箱重（kgs）", "是否全程", "有色金属", "货源组织单位", "货主", "境内货源地/目的地", "报关单号", "货值（美金）", "海关封", "车号", "订单需求号", "国联订单号", "应收境内运费", "应收境外运费（CNY）", "应收境外运费（原币）", "应收总金额", "应付境内运费", "应付境外运费（CNY）", "应付境外运费（原币）", "应付运费总金额", "补贴标准", "补贴金额", "箱备注"};
            if (userInfo.getPlatformCode().startsWith("MP")) {
                title = new String[]{"序号", "箱号", "箱属", "箱型尺寸", "类型", "收货人", "发站", "目的国", "到站", "口岸代理", "品名", "件数", "货重（kgs）", "箱重（kgs）", "是否全程", "有色金属", "货源组织单位", "货主", "境内货源地/目的地", "报关单号", "货值（美金）", "海关封", "车号", "订单需求号", "国联订单号", "境内运费", "境外运费（CNY）", "境外运费（原币）", "总金额", "铁路境内运费", "铁路境外运费（CNY）", "铁路境外运费（原币）", "铁路运费总金额", "补贴标准", "补贴金额", "箱备注"};
            }
            List<String> titles = Arrays.asList(title);
            if (CollUtil.isNotEmpty(titles)) {
                int a1 = 0;
                int b1 = 0;
                int c1 = 0;
                int d1 = 0;
                int e1 = 0;
                int a2 = 0;
                int b2 = 0;
                int c2 = 0;
                int d2 = 0;
                Boolean a = true;
                Boolean b = true;
                Boolean c = true;
                Boolean d = true;

                for (int i = 0; i < titles.size(); i++) {
                    if ("序号".equals(titles.get(i))
                            || "类型".equals(titles.get(i))
                            || "货源组织单位".equals(titles.get(i))
                            || "收货人".equals(titles.get(i))
                            || "发站".equals(titles.get(i))
                            || "目的国".equals(titles.get(i))
                            || "到站".equals(titles.get(i))
                            || "口岸代理".equals(titles.get(i))
                            || "品名".equals(titles.get(i))
                            || "箱型尺寸".equals(titles.get(i))
                            || "箱属".equals(titles.get(i))
                            || "箱号".equals(titles.get(i))
                            || "箱型代码".equals(titles.get(i))
                            || "箱型名称".equals(titles.get(i))
                            || "件数".equals(titles.get(i))
                            || "货重（kgs）".equals(titles.get(i))
                            || "箱重（kgs）".equals(titles.get(i))
                            || "是否全程".equals(titles.get(i))
                            || "有色金属".equals(titles.get(i))
                    ) {
                        if (a) {
                            a1 = i;
                            a = false;
                        }
                        a2 = i;
                    }

                    if ("货主".equals(titles.get(i))
                            || "境内货源地/目的地".equals(titles.get(i))
                            || "报关单号".equals(titles.get(i))
                            || "货值（美金）".equals(titles.get(i))
                            || "海关封".equals(titles.get(i))
                            || "车号".equals(titles.get(i))
                            || "订单需求号".equals(titles.get(i))
                            || "国联订单号".equals(titles.get(i))
                    ) {
                        if (b) {
                            b1 = i;
                            b = false;
                        }
                        b2 = i;
                    }

                    if ("境内运费".equals(titles.get(i))
                            || "境外运费（CNY）".equals(titles.get(i))
                            || "境外运费（原币）".equals(titles.get(i))
                            || "总金额".equals(titles.get(i))
                            || "铁路境内运费".equals(titles.get(i))
                            || "铁路境外运费（CNY）".equals(titles.get(i))
                            || "铁路境外运费（原币）".equals(titles.get(i))
                            || "铁路运费总金额".equals(titles.get(i))
                            || "应收境内运费".equals(titles.get(i))
                            || "应收境外运费（CNY）".equals(titles.get(i))
                            || "应收境外运费（原币）".equals(titles.get(i))
                            || "应收总金额".equals(titles.get(i))
                            || "应付境内运费".equals(titles.get(i))
                            || "应付境外运费（CNY）".equals(titles.get(i))
                            || "应付境外运费（原币）".equals(titles.get(i))
                            || "应付总金额".equals(titles.get(i))
                    ) {
                        if (c) {
                            c1 = i;
                            c = false;
                        }
                        c2 = i;
                    }

                    if ("补贴标准".equals(titles.get(i))
                            || "补贴金额".equals(titles.get(i))
                    ) {
                        if (d) {
                            d1 = i;
                            d = false;
                        }
                        d1 = i;
                    }

                    if ("箱备注".equals(titles.get(i))
                    ) {
                        e1 = i;
                    }
                }
                if (a2 != 0) {
                    XSSFCell cella = row2.createCell(a1);
                    cella.setCellValue("箱基本信息");
                    cella.setCellStyle(style);
                }
                if (b2 != 0) {
                    XSSFCell cellb = row2.createCell(b1);
                    cellb.setCellValue("箱补充信息");
                    cellb.setCellStyle(style);
                }
                if (c2 != 0) {
                    XSSFCell cellc = row2.createCell(c1);
                    cellc.setCellValue("运费信息");
                    cellc.setCellStyle(style);
                }
                if (d2 != 0) {
                    XSSFCell celld = row2.createCell(d1);
                    celld.setCellValue("补贴信息");
                    celld.setCellStyle(style);
                }
                if (e1 != 0) {
                    XSSFCell celle = row2.createCell(e1);
                    celle.setCellValue("备注");
                    celle.setCellStyle(style);
                }

                if (a2 != 0 && a1 != a2) {
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, a1, a2));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                }
                if (b2 != 0 && b1 != b2) {
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, b1, b2));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                }
                if (c2 != 0 && c1 != c2) {
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, c1, c2));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                }
                if (d2 != 0 && d1 != d2) {
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, d1, d2));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                }

                for (int i = 0; i < titles.size(); i++) {
                    if (i != a1 && i != b1 && i != c1 && i != d1 && i != e1) {
                        XSSFCell cella = row2.createCell(a1);
                        cella.setCellValue("");
                        cella.setCellStyle(style);
                    }
                }
                rowIndex++;

                XSSFRow row = sheet.createRow(rowIndex);

                for (int i = 0; i < titles.size(); i++) {
                    XSSFCell cell0 = row.createCell(i);
                    cell0.setCellValue(titles.get(i));
                    cell0.setCellStyle(style);
                }

                //另起一行
                rowIndex++;

                if (CollUtil.isNotEmpty(list)) {
                    //样式
                    font = workbook.createFont();
                    font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
                    font.setFontName("宋体");//设置字体
                    style = workbook.createCellStyle();
                    style.setFont(font);
                    style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
                    style.setAlignment(HorizontalAlignment.CENTER);// 水平
                    style.setBorderTop(BorderStyle.THIN);//上边框
                    style.setBorderBottom(BorderStyle.THIN);//下边框
                    style.setBorderLeft(BorderStyle.THIN);//左边框
                    style.setBorderRight(BorderStyle.THIN);//右边框
                    for (int x = 0; x < list.size(); x++) {
                        row = sheet.createRow(rowIndex);
                        row.setHeightInPoints(20);//设置行高
                        for (int i = 0; i < titles.size(); i++) {
                            XSSFCell cell0 = row.createCell(i);
                            String val = "";
                            if ("序号".equals(titles.get(i))) {
                                val = (x + 1) + "";
                            }
                            if ("类型".equals(titles.get(i))) {
                                if ("E".equals(list.get(x).getTradeType())) {
                                    val = "出口";//类型
                                } else if ("P".equals(list.get(x).getTradeType())) {
                                    val = "过境";//类型
                                } else if ("I".equals(list.get(x).getTradeType())) {
                                    val = "进口";//类型
                                }
                            }
                            if ("货源组织单位".equals(titles.get(i))) {
                                val = list.get(x).getPlatformName();
                            }
                            if ("收货人".equals(titles.get(i))) {
                                val = list.get(x).getConsignee();
                            }
                            if ("发站".equals(titles.get(i))) {
                                val = list.get(x).getDepartureStation();
                            }
                            if ("目的国".equals(titles.get(i))) {
                                val = list.get(x).getDestinationCountry();
                            }
                            if ("到站".equals(titles.get(i))) {
                                val = list.get(x).getArrivalStation();
                            }
                            if ("口岸代理".equals(titles.get(i))) {
                                val = list.get(x).getPortAgent();
                            }
                            if ("品名".equals(titles.get(i))) {
                                val = list.get(x).getGoodsName();
                            }
                            if ("箱型尺寸".equals(titles.get(i))) {
                                val = list.get(x).getContainerSize();
                            }
                            if ("箱属".equals(titles.get(i))) {
                                val = list.get(x).getContainerOwner().equals("ZBX") ? "自备箱" : "中铁箱";
                            }
                            if ("箱型代码".equals(titles.get(i))) {
                                val = list.get(x).getContainerTypeCode();
                            }
                            if ("箱型名称".equals(titles.get(i))) {
                                val = list.get(x).getContainerTypeName();
                            }
                            if ("箱号".equals(titles.get(i))) {
                                val = list.get(x).getContainerNumber();
                            }
                            if ("件数".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getPieceCount());
                            }
                            if ("货重（kgs）".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getCargoWeight());
                            }
                            if ("箱重（kgs）".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getContainerWeight());
                            }
                            if ("是否全程".equals(titles.get(i))) {
                                val = list.get(x).getIsFullJourney().equals("Y") ? "是" : "否";
                            }
                            if ("有色金属".equals(titles.get(i))) {
                                val = list.get(x).getHasNonferrousMetal().equals("Y") ? "是" : "否";
                            }
                            if ("货主".equals(titles.get(i))) {
                                val = list.get(x).getShipper();
                            }
                            if ("境内货源地/目的地".equals(titles.get(i))) {
                                val = list.get(x).getDomesticOriginDestination();
                            }
                            if ("报关单号".equals(titles.get(i))) {
                                val = list.get(x).getCustomsDeclarationNumber();
                            }
                            if ("货值（美金）".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getCargoValueUsd());
                            }
                            if ("海关封".equals(titles.get(i))) {
                                val = list.get(x).getCustomsSeal();
                            }
                            if ("车号".equals(titles.get(i))) {
                                val = list.get(x).getVehicleNumber();
                            }
                            if ("订单需求号".equals(titles.get(i))) {
                                val = list.get(x).getOrderDemandNumber();
                            }
                            if ("国联订单号".equals(titles.get(i))) {
                                val = list.get(x).getGuolianOrderNumber();
                            }
                            if ("应收境内运费".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getReceivableDomesticFee());
                            }
                            if ("应收境外运费（CNY）".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getReceivableForeignFee());
                            }
                            if ("应收境外运费（原币）".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getReceivableForeignOriginal());
                            }
                            if ("应收总金额".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getReceivableTotalFee());
                            }
                            if ("应付境内运费".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getPayableDomesticFee());
                            }
                            if ("应付境外运费（CNY）".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getPayableForeignFee());
                            }
                            if ("应付境外运费（原币）".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getPayableForeignOriginal());
                            }
                            if ("应付总金额".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getPayableTotalFee());
                            }
                            if ("境内运费".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getPayableDomesticFee());
                            }
                            if ("境外运费（CNY）".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getPayableForeignFee());
                            }
                            if ("境外运费（原币）".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getPayableForeignOriginal());
                            }
                            if ("总金额".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getPayableTotalFee());
                            }
                            if ("铁路境内运费".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getPayableRailwayDomesticFee());
                            }
                            if ("铁路境外运费（CNY）".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getPayableRailwayForeignFee());
                            }
                            if ("铁路境外运费（原币）".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getPayableRailwayForeignOriginal());
                            }
                            if ("铁路运费总金额".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getPayableRailwayTotalFee());
                            }
                            if ("补贴标准".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getSubsidyStandard());
                            }
                            if ("补贴金额".equals(titles.get(i))) {
                                val = String.valueOf(list.get(x).getSubsidyAmount());
                            }
                            if ("箱备注".equals(titles.get(i))) {
                                val = list.get(x).getContainerRemark();
                            }
                            cell0.setCellValue(val);
                        }

                        for (Cell cellTemp : row) {
                            cellTemp.setCellStyle(style);
                        }
                        //另起一行
                        rowIndex++;
                    }
                }
            }
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            String fileName = URLEncoder.encode("台账明细导出.xlsx", "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            out.close();
            workbook.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    private void generateEaContainerSummary(List<EaContainerSummary> summaryList, EaLedgerTieLuFeeVo eaLedgerTieLuFeeVo) {
        EaContainerSummary summary = new EaContainerSummary();
        summary.setShiftNo(eaLedgerTieLuFeeVo.getShiftNo());
        summary.setPayeeCode(PlatformLevelEnum.TIELU_CODE.getKey());
        summary.setContainerSize(eaLedgerTieLuFeeVo.getContainerSize());
        summary.setDepartureStation(eaLedgerTieLuFeeVo.getDepartureStation());
        summary.setArrivalStation(eaLedgerTieLuFeeVo.getArrivalStation());
        summary.setIsFullJourney(eaLedgerTieLuFeeVo.getIsFullJourney());
        summary.setFeeCategoryCode(SysCommon.FEE_TYPE_CODE);
        summary.setDomesticFlatRate(eaLedgerTieLuFeeVo.getPayableRailwayDomesticFee());
        summary.setForeignFlatRateOriginal(eaLedgerTieLuFeeVo.getPayableRailwayForeignOriginal());
        summary.setForeignFlatRateConverted(eaLedgerTieLuFeeVo.getPayableRailwayForeignFee());
        summaryList.add(summary);
    }

    private void generateEaFee(List<EaFee> feeList, EaLedgerTieLuFeeVo eaLedgerTieLuFeeVo, String type) {
        EaFee fee = new EaFee();
        fee.setShiftNo(eaLedgerTieLuFeeVo.getShiftNo());
        fee.setPayeeCode(PlatformLevelEnum.TIELU_CODE.getKey());
        fee.setContainerSize(eaLedgerTieLuFeeVo.getContainerSize());
        fee.setDepartureStation(eaLedgerTieLuFeeVo.getDepartureStation());
        fee.setArrivalStation(eaLedgerTieLuFeeVo.getArrivalStation());
        fee.setIsFullJourney(eaLedgerTieLuFeeVo.getIsFullJourney());
        fee.setFeeCategoryCode(SysCommon.FEE_TYPE_CODE);
        fee.setFeeSubcategoryCode(type);
        if (SysCommon.DOMESTIC_FEE_CODE.equals(type)) {
            fee.setOriginalAmount(eaLedgerTieLuFeeVo.getPayableRailwayDomesticFee());
        } else {
            fee.setOriginalAmount(eaLedgerTieLuFeeVo.getPayableRailwayForeignOriginal());
        }
        feeList.add(fee);
    }

}
