package com.huazheng.tunny.ocean.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.ContainerTypeData;
import com.huazheng.tunny.ocean.api.entity.GoodsData;
import com.huazheng.tunny.ocean.api.entity.StationManagement;
import com.huazheng.tunny.ocean.api.vo.SysDictVo;
import com.huazheng.tunny.ocean.service.ContainerTypeDataService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.util.CheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 箱型数据管理表
 *
 * <AUTHOR>
 * @date 2023-07-03 09:43:51
 */
@Slf4j
@RestController
@RequestMapping("/containertypedata")
public class ContainerTypeDataController {

    @Autowired
    private ContainerTypeDataService containerTypeDataService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  containerTypeDataService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return containerTypeDataService.selectContainerTypeDataListByLike(new Query<>(params));
    }

    @PostMapping("/list")
    public R list(@RequestBody ContainerTypeData containerTypeData) {
        final List<ContainerTypeData> list = containerTypeDataService.selectContainerTypeDataListByLike(containerTypeData);
        return new R<>(list);
    }

    /**
     * 尺寸下拉列表
     * @param containerTypeData
     * @return
     */
    @GetMapping("/sizeList")
    public R sizeList(@RequestParam Map<String, Object> params) {
         List<ContainerTypeData> list = containerTypeDataService.selectSizeList(params);
        return new R<>(list);
    }

    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        ContainerTypeData containerTypeData =containerTypeDataService.selectContainerTypeDataById(rowId);
        return new R<>(containerTypeData);
    }

    /**
     * 保存
     * @param containerTypeData
     * @return R
     */
    @PostMapping
    public R save(@RequestBody ContainerTypeData containerTypeData) {
        ContainerTypeData sel = new ContainerTypeData();
        sel.setContainerTypeCode(containerTypeData.getContainerTypeCode());
        sel.setDeleteFlag("N");
        final List<ContainerTypeData> list = containerTypeDataService.selectContainerTypeDataList(sel);
        if(CollUtil.isNotEmpty(list)){
            return new R<>(500,Boolean.FALSE,null,"该箱型代码已存在："+sel.getContainerTypeCode());
        }
        containerTypeDataService.insertContainerTypeData(containerTypeData);
        return new R<>(200,Boolean.TRUE,null,"新增完成！");
    }

    /**
     * 修改
     * @param containerTypeData
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody ContainerTypeData containerTypeData) {
        ContainerTypeData sel = new ContainerTypeData();
        sel.setRowId(containerTypeData.getRowId());
        sel.setContainerTypeCode(containerTypeData.getContainerTypeCode());
        final List<ContainerTypeData> list = containerTypeDataService.selectDuplicate(sel);
        if(CollUtil.isNotEmpty(list)){
            return new R<>(500,Boolean.FALSE,null,"该箱型代码已存在："+sel.getContainerTypeCode());
        }
        containerTypeDataService.updateContainerTypeData(containerTypeData);
        return new R<>(200,Boolean.TRUE,null,"修改完成！");
    }

    

    /**
     * 删除
     * @param rowId
     * @return R
     */
    @GetMapping("/del/{rowId}")
    public R delete(@PathVariable  String rowId) {
        containerTypeDataService.deleteContainerTypeDataById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        containerTypeDataService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<ContainerTypeData> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<ContainerTypeData> list = containerTypeDataService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), ContainerTypeData.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }

    @PostMapping("/importFile")
    @ResponseBody
    public R importFile(@RequestParam  MultipartFile file ) throws Exception {
        R r;
        try {
            String originalFilename = file.getOriginalFilename();
            InputStream inputStream = file.getInputStream();
            // 调用工具类中方法，读取excel文件中数据
            r = readExcel(originalFilename, inputStream);
            inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
            return new R<>(new Throwable(e));
        }
        return r;
    }

    public boolean isRowEmpty(Row row){
        if(row!=null){
            for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
                Cell cell = row.getCell(c);
                if(cell!=null){
                    cell.setCellType(CellType.STRING);
                }
                if (cell != null && !"".equals(cell.getStringCellValue())){
                    return false;//不是空行
                }
            }
        }
        return true;
    }

    /**
     * 读取excel文件数据
     * @param fileName
     * @param inputStream
     * @return
     * @throws Exception
     */
    public  R readExcel(String fileName, InputStream inputStream) throws Exception {
        CheckUtil checkUtil = new CheckUtil();
        R r = new R();
        boolean ret = isXls(fileName);
        Workbook workbook = null;
        // 根据后缀创建不同的对象
        if(ret) {
            workbook = new HSSFWorkbook(inputStream);
        }else{
            workbook = new XSSFWorkbook(inputStream);
        }
        Sheet sheet = getAccuracyContextNum(workbook);

        List<ContainerTypeData> list = new ArrayList<>();
        int lastRowNum = sheet.getLastRowNum();
        if(lastRowNum>0) {
            //从第二行开始读取
            for (int i = 1; i <= lastRowNum; i++) {
                ContainerTypeData containerTypeData = new ContainerTypeData();
                Row row = sheet.getRow(i);
                if (row.getCell(0) != null) {
                    row.getCell(0).setCellType(CellType.STRING);
                    if (!"".equals(row.getCell(0).getStringCellValue()) && row.getCell(0).getStringCellValue() != null) {
                        containerTypeData.setContainerTypeCode(row.getCell(0).getStringCellValue());//箱型代码
                    } else {
                        return new R(Boolean.FALSE, "箱型代码不能为空");
                    }
                } else {
                    return new R(Boolean.FALSE, "箱型代码不能为空");
                }

                if (row.getCell(1) != null) {
                    row.getCell(1).setCellType(CellType.STRING);
                    if (!"".equals(row.getCell(1).getStringCellValue()) && row.getCell(1).getStringCellValue() != null) {
                        containerTypeData.setContainerTypeName(row.getCell(1).getStringCellValue());//箱型名称
                    } else {
                        return new R(Boolean.FALSE, "箱型名称不能为空");
                    }
                } else {
                    return new R(Boolean.FALSE, "箱型名称不能为空");
                }

                if (row.getCell(2) != null) {
                    row.getCell(2).setCellType(CellType.STRING);
                    if (!"".equals(row.getCell(2).getStringCellValue()) && row.getCell(2).getStringCellValue() != null) {
                        if("20".equals(row.getCell(2).getStringCellValue())||"40".equals(row.getCell(2).getStringCellValue())||"45".equals(row.getCell(2).getStringCellValue())){
                            containerTypeData.setContainerTypeSize(row.getCell(2).getStringCellValue());//箱型尺寸
                        }else{
                            return new R(Boolean.FALSE, "箱型尺寸必须为：20/40/45");
                        }

                    } else {
                        return new R(Boolean.FALSE, "箱型尺寸不能为空");
                    }
                } else {
                    return new R(Boolean.FALSE, "箱型尺寸不能为空");
                }

                if (row.getCell(3) != null) {
                    row.getCell(3).setCellType(CellType.STRING);
                    if (!"".equals(row.getCell(3).getStringCellValue()) && row.getCell(3).getStringCellValue() != null) {
                        containerTypeData.setNationalCode(row.getCell(3).getStringCellValue());//国标代码
                    } else {
                        return new R(Boolean.FALSE, "国标代码不能为空");
                    }
                } else {
                    return new R(Boolean.FALSE, "国标代码不能为空");
                }

                if (row.getCell(4) != null) {
                    row.getCell(4).setCellType(CellType.STRING);
                    if (!"".equals(row.getCell(4).getStringCellValue()) && row.getCell(4).getStringCellValue() != null) {
                        if("是".equals(row.getCell(4).getStringCellValue())){
                            containerTypeData.setIsRef("1");//是否冷藏箱
                        }else if("否".equals(row.getCell(4).getStringCellValue())){
                            containerTypeData.setIsRef("0");//是否冷藏箱
                        }else{
                            return new R(Boolean.FALSE, "是否冷藏箱必须为：是/否");
                        }
                    } else {
                        return new R(Boolean.FALSE, "是否冷藏箱不能为空");
                    }
                } else {
                    return new R(Boolean.FALSE, "是否冷藏箱不能为空");
                }
                list.add(containerTypeData);
            }

            //导入数据查重
            for (int i = 0; i < list.size(); i++) {
                String c1 = list.get(i).getContainerTypeCode();
                String a1 = list.get(i).getNationalCode();
                for (int j = list.size() - 1; j > i; j--) {
                    String c2 = list.get(j).getContainerTypeCode();
                    String a2 = list.get(j).getNationalCode();
                    if(c1.equals(c2)){
                        return new R(Boolean.FALSE, "导入箱型代码重复:"+c1);
                    }
                    /*if(a1.equals(a2)){
                        return new R(Boolean.FALSE, "导入国标代码重复:"+a1);
                    }*/
                }
            }

            //数据库查重
            ContainerTypeData sel = new ContainerTypeData();
            sel.setDeleteFlag("N");
            final List<ContainerTypeData> dataList = containerTypeDataService.selectContainerTypeDataList(sel);
            if(CollUtil.isNotEmpty(dataList)){
                for (ContainerTypeData g:list
                ) {
                    for (ContainerTypeData d:dataList
                    ) {
                        if(g.getContainerTypeCode().equals(d.getContainerTypeCode())){
                            return new R(Boolean.FALSE, "该箱型代码已存在:"+g.getContainerTypeCode());
                        }
                        /*if(g.getNationalCode().equals(d.getNationalCode())){
                            return new R(Boolean.FALSE, "该国标代码已存在:"+g.getNationalCode());
                        }*/
                    }
                }
            }
            for (ContainerTypeData g:list
            ) {
                containerTypeDataService.insertContainerTypeData(g);
            }
        }else {
            return new R(Boolean.FALSE,"表格不能为空");
        }
        workbook.close();
//        r.setData(list);
        r.setMsg("导入成功！");
        return r;
    }

    //获取准确的文件行数
    public Sheet getAccuracyContextNum(Workbook workbook) {
        // 取第一个sheet
        Sheet sheet = workbook.getSheetAt(0);
        // 删除空行
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            // 删除空行
            if (this.isRowEmpty(row)) {
                int lastRowNum = sheet.getLastRowNum();
                if (i >= 0 && i < lastRowNum) {
                    sheet.shiftRows(i + 1, lastRowNum, -1);// 将行号为i+1一直到行号为lastRowNum的单元格全部上移一行，以便删除i行
                }
                if (i == lastRowNum) {
                    if (row != null) {
                        sheet.removeRow(row);
                    }
                }
                i--;
            }
        }
        return sheet;
    }

    /**
     * 判断导入文件格式
     * @param fileName
     * @return
     */
    public static boolean isXls(String fileName){
        // (?i)忽略大小写
        if(fileName.matches("^.+\\.(?i)(xls)$")){
            return true;
        }else if(fileName.matches("^.+\\.(?i)(xlsx)$")){
            return false;
        }else{
            throw new RuntimeException("格式不对");
        }
    }


    /**
     * 下载模板
     * @param response
     * @throws IOException
     */
    @PostMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        XSSFWorkbook workbook=new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("箱型管理");
        XSSFRow row = sheet.createRow(0);
        //宽度
        for(int i=0;i<=4;i++){
            sheet.setColumnWidth(i,60*80);
        }
        row.setHeight((short) (10*50));
        //字体
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");

        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);//水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setBorderBottom(BorderStyle.THIN); //下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);

        XSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("箱型代码*");
        cell0.setCellStyle(style);
        XSSFCell cell1 = row.createCell(1);
        cell1.setCellValue("箱型名称*");
        cell1.setCellStyle(style);
        XSSFCell cell2 = row.createCell(2);
        cell2.setCellValue("箱型尺寸*");
        cell2.setCellStyle(style);
        XSSFCell cell3 = row.createCell(3);
        cell3.setCellValue("国标代码*");
        cell3.setCellStyle(style);
        XSSFCell cell4 = row.createCell(4);
        cell4.setCellValue("是否冷藏箱*(是/否)");
        cell4.setCellStyle(style);

        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        String fileName = "箱型管理.xlsx";
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
        response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();

    }
}
