package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.WaybillParticipants;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import io.swagger.models.auth.In;

import java.util.List;

/**
 * 运单参与方信息表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-05 10:58:23
 */
public interface WaybillParticipantsService extends IService<WaybillParticipants> {
    /**
     * 查询运单参与方信息表信息
     *
     * @param rowId 运单参与方信息表ID
     * @return 运单参与方信息表信息
     */
    public WaybillParticipants selectWaybillParticipantsById(String rowId);

    /**
     * 查询运单参与方信息表列表
     *
     * @param waybillParticipants 运单参与方信息表信息
     * @return 运单参与方信息表集合
     */
    public List<WaybillParticipants> selectWaybillParticipantsList(WaybillParticipants waybillParticipants);

    public List<WaybillParticipants> selectWaybillParticipantsByWayBillNo(WaybillParticipants waybillParticipants);

    /**
     * 分页模糊查询运单参与方信息表列表
     * @return 运单参与方信息表集合
     */
    public Page selectWaybillParticipantsListByLike(Query query);



    /**
     * 新增运单参与方信息表
     *
     * @param waybillParticipants 运单参与方信息表信息
     * @return 结果
     */
    public int insertWaybillParticipants(List<WaybillParticipants> waybillParticipants);

    /**
     * 修改运单参与方信息表
     *
     * @param waybillParticipants 运单参与方信息表信息
     * @return 结果
     */
    public int updateWaybillParticipants(List<WaybillParticipants> waybillParticipants);

    public void insertOrUpdateWaybillParticipants(List<WaybillParticipants> waybillParticipants, List<String> waybillCodes);

    /**
     * 删除运单参与方信息表
     *
     * @param rowId 运单参与方信息表ID
     * @return 结果
     */
    public int deleteWaybillParticipantsById(String rowId);

    /**
     * 批量删除运单参与方信息表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteWaybillParticipantsByIds(Integer[] rowIds);

    public Integer selectWaybillParticipantsByCount(WaybillParticipants waybillParticipants);

    public WaybillParticipants selectWaybillParticipantsByList(WaybillParticipants waybillParticipants);

    public int updateWaybillParticipantsByWayBillNo(WaybillParticipants waybillParticipants);

    public int deleteWaybillParticipantsByContainerNo(WaybillParticipants waybillParticipants);
}

