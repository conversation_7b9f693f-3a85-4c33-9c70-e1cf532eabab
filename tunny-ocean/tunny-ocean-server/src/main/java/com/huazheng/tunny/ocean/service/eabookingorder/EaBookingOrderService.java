package com.huazheng.tunny.ocean.service.eabookingorder;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.dto.eaboolingorder.EaBookingOrderDto;
import com.huazheng.tunny.ocean.api.entity.eabookingorder.EaBookingOrder;
import com.huazheng.tunny.ocean.api.vo.eaboolingorder.EaBookingOrderVo;
import com.huazheng.tunny.ocean.util.R;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订舱订单表 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2025-06-16 15:23:50
 */
public interface EaBookingOrderService extends IService<EaBookingOrder> {
    /**
     * 查询订舱订单表信息
     *
     * @param orderId 订舱订单表ID
     * @return 订舱订单表信息
     */
    public R selectEaBookingOrderById(Long orderId);

    /**
     * 查询订舱订单表列表
     *
     * @param eaBookingOrder 订舱订单表信息
     * @return 订舱订单表集合
     */
    public List<EaBookingOrder> selectEaBookingOrderList(EaBookingOrder eaBookingOrder);


    /**
     * 分页模糊查询订舱订单表列表
     * @return 订舱订单表集合
     */
    public Page selectEaBookingOrderListByLike(Query query);



    /**
     * 新增订舱订单表
     *
     * @param eaBookingOrder 订舱订单表信息
     * @return 结果
     */
    public int insertEaBookingOrder(EaBookingOrder eaBookingOrder);

    /**
     * 修改订舱订单表
     *
     * @param eaBookingOrder 订舱订单表信息
     * @return 结果
     */
    public int updateEaBookingOrder(EaBookingOrder eaBookingOrder);

    /**
     * 删除订舱订单表
     *
     * @param orderId 订舱订单表ID
     * @return 结果
     */
    public int deleteEaBookingOrderById(Long orderId);

    /**
     * 批量删除订舱订单表
     *
     * @param orderIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEaBookingOrderByIds(Integer[] orderIds);

    R save(EaBookingOrder eaBookingOrder);

    R updateEaBookingOrderById(EaBookingOrder eaBookingOrder);

    /**
     * 审批
     *
     * @param eaBookingOrder
     * @return
     */
    R updateEaBookingOrderReview(EaBookingOrderDto eaBookingOrder);

    R updateEaBookingOrderWithdrawal(EaBookingOrderDto eaBookingOrder);

    /**
     * 详情
     *
     * @param orderId
     * @return
     */
    R selectEaBookingOrderByOrderId(Long orderId);

    /**
     * 提交订单
     *
     * @param eaBookingOrder
     * @return
     */
    R updateEaBookingOrderSubmitOrder(EaBookingOrderDto eaBookingOrder);

    List<EaBookingOrderVo> selectEaBookingOrderListByOrderCode(String orderCode);

    List<EaBookingOrder> selectEaBookingOrderByShiftNoAndPlatformCode(EaBookingOrder requesHeader);

    int insertBusCost(EaBookingOrder requesHeader);

    /**
     * 查询是否已订
     *
     * @param shiftNo
     * @return
     */
    R selectIsHaveBooked(String shiftNo);

    /**
     * 删除
     * @param orderIds
     * @return
     */
    R del(List<Long> orderIds);

    BigDecimal getBoxVolume(String size, Integer containerQuantity);
}

