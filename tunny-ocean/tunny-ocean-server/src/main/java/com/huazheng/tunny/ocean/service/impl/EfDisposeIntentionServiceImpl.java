package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.controller.SignatureController;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.EfDisposeIntentionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import java.util.UUID;

@Service("efDisposeIntentionService")
public class EfDisposeIntentionServiceImpl extends ServiceImpl<EfDisposeIntentionMapper, EfDisposeIntention> implements EfDisposeIntentionService {

    @Autowired
    private EfDisposeIntentionMapper efDisposeIntentionMapper;
    @Autowired
    private SignatureController signatureController;
    @Autowired
    private EfWarehouseApplyMapper efWarehouseApplyMapper;
    @Autowired
    private EfDisposeIntentionListMapper efDisposeIntentionListMapper;
    @Autowired
    private EfWarehouseLogMapper efWarehouseLogMapper;
    @Autowired
    private EfWarehouseListMapper efWarehouseListMapper;
    @Autowired
    private EfWarehouseGoodsMapper efWarehouseGoodsMapper;

    /**
     * 查询发布处置意向信息
     *
     * @param rowId 发布处置意向ID
     * @return 发布处置意向信息
     */
    @Override
    public EfDisposeIntention selectEfDisposeIntentionById(String rowId)
    {
        final EfDisposeIntention efDisposeIntention = efDisposeIntentionMapper.selectEfDisposeIntentionById(rowId);
        EfDisposeIntentionList ef = new EfDisposeIntentionList();
        ef.setAssetCode(efDisposeIntention.getAssetCode());
        ef.setDeleteFlag("N");
        final List<EfDisposeIntentionList> efDisposeIntentionLists = efDisposeIntentionListMapper.selectEfDisposeIntentionListList(ef);
        efDisposeIntention.setWarehouseList(efDisposeIntentionLists);
        return efDisposeIntention;
    }

    /**
     * 查询发布处置意向列表
     *
     * @param efDisposeIntention 发布处置意向信息
     * @return 发布处置意向集合
     */
    @Override
    public List<EfDisposeIntention> selectEfDisposeIntentionList(EfDisposeIntention efDisposeIntention)
    {
        return efDisposeIntentionMapper.selectEfDisposeIntentionList(efDisposeIntention);
    }


    /**
     * 分页模糊查询发布处置意向列表
     * @return 发布处置意向集合
     */
    @Override
    public Page selectEfDisposeIntentionListByLike(Query query)
    {
        EfDisposeIntention efDisposeIntention =  BeanUtil.mapToBean(query.getCondition(), EfDisposeIntention.class,false);
        query.setRecords(efDisposeIntentionMapper.selectEfDisposeIntentionListByLike(query,efDisposeIntention));
        return query;
    }

    @Override
    public Page selectEfDisposeIntentionListByLike2(Query query)
    {
        EfDisposeIntention efDisposeIntention =  BeanUtil.mapToBean(query.getCondition(), EfDisposeIntention.class,false);
        query.setRecords(efDisposeIntentionMapper.selectEfDisposeIntentionListByLike2(query,efDisposeIntention));
        return query;
    }

    /**
     * 新增发布处置意向
     *
     * @param efDisposeIntention 发布处置意向信息
     * @return 结果
     */
    @Override
    public int insertEfDisposeIntention(EfDisposeIntention efDisposeIntention)
    {
        return efDisposeIntentionMapper.insertEfDisposeIntention(efDisposeIntention);
    }

    /**
     * 修改发布处置意向
     *
     * @param efDisposeIntention 发布处置意向信息
     * @return 结果
     */
    @Override
    public int updateEfDisposeIntention(EfDisposeIntention efDisposeIntention)
    {
        return efDisposeIntentionMapper.updateEfDisposeIntention(efDisposeIntention);
    }


    /**
     * 删除发布处置意向
     *
     * @param rowId 发布处置意向ID
     * @return 结果
     */
    public int deleteEfDisposeIntentionById(String rowId)
    {
        return efDisposeIntentionMapper.deleteEfDisposeIntentionById( rowId);
    };


    /**
     * 批量删除发布处置意向对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfDisposeIntentionByIds(Integer[] rowIds)
    {
        return efDisposeIntentionMapper.deleteEfDisposeIntentionByIds( rowIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String publishDisposeIntention(EfDisposeIntention efDisposeIntention)
    {
        //插入操作记录
        EfWarehouseLog log = new EfWarehouseLog();
        String remark = "";

        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(efDisposeIntention!=null) {
            final EfWarehouseApply efWarehouseApply = efWarehouseApplyMapper.selectEfWarehouseApplyByAssetCode(efDisposeIntention.getAssetCode());
            if(efWarehouseApply!=null){
                efDisposeIntention.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                from = "E融平台:";
                //调用E融接口，插入申请数据
                String json = JSONUtil.parseObj(efDisposeIntention, true).toStringPretty();
                final String result = signatureController.doPostEf("/efdisposeintention/publishDisposeIntention", json);
                JSONObject dataObject = JSONUtil.parseObj(result);
                flag = Boolean.valueOf(String.valueOf(dataObject.get("b")));
                msg = String.valueOf(dataObject.get("msg"));
                if(flag){
                    efDisposeIntention.setRowId(UUID.randomUUID().toString());
                    efDisposeIntention.setEntSocialCode(efWarehouseApply.getEntSocialCode());
                    efDisposeIntention.setPlatformCode(efWarehouseApply.getPlatformCode());
                    efDisposeIntention.setAddTime(LocalDateTime.now());
                    efDisposeIntention.setAddWho("ZC");
                    efDisposeIntention.setAddWhoName("中钞");
                    efDisposeIntentionMapper.insertEfDisposeIntention(efDisposeIntention);
                    final List<EfDisposeIntentionList> warehouseList = efDisposeIntention.getWarehouseList();
                    if(CollUtil.isNotEmpty(warehouseList)){
                        remark = "处置仓单编码：";
                        final List<EfWarehouseList> efWarehouseLists = efWarehouseListMapper.selectEfWarehouseListByQlFinancingNo(efWarehouseApply.getQlFinancingNo());

                        for (EfDisposeIntentionList efDisposeIntentionList:warehouseList
                        ) {
                            if(CollUtil.isNotEmpty(efWarehouseLists)){
                                for (EfWarehouseList efWarehouseList:efWarehouseLists
                                     ) {
                                    if(efWarehouseList.getWarehouseSupervisionNo().equals(efDisposeIntentionList.getWarehouseSupervisionNo())
                                    &&efWarehouseList.getWarehouseCode().equals(efDisposeIntentionList.getWarehouseCode())){
                                        efDisposeIntentionList.setValuation(efWarehouseList.getValuation());
                                        efDisposeIntentionList.setValuationCny(efWarehouseList.getValuationCny());
                                        efDisposeIntentionList.setValuationCurrency(efWarehouseList.getValuationCurrency());
                                        efDisposeIntentionList.setSurveillanceVideo(efWarehouseList.getSurveillanceVideo());

                                        EfWarehouseGoods goods = new EfWarehouseGoods();
                                        goods.setWarehouseReceiptNo(efDisposeIntentionList.getWarehouseCode());
                                        goods.setDeleteFlag("N");
                                        final List<EfWarehouseGoods> efWarehouseGoods = efWarehouseGoodsMapper.selectEfWarehouseGoodsList(goods);
                                        if(CollUtil.isNotEmpty(efWarehouseGoods)){
                                            for (EfWarehouseGoods g:efWarehouseGoods
                                                 ) {
                                               if(StrUtil.isNotEmpty(g.getCategoryName())){
                                                   efDisposeIntentionList.setCategoryName(g.getCategoryName());
                                                   break;
                                               }
                                            }
                                        }
                                    }
                                }
                            }
                            remark += efDisposeIntentionList.getWarehouseCode() +" ";
                            efDisposeIntentionList.setRowId(UUID.randomUUID().toString());
                            efDisposeIntentionList.setAssetCode(efDisposeIntention.getAssetCode());
                            efDisposeIntentionList.setStatus("0");
                            efDisposeIntentionList.setAddTime(LocalDateTime.now());
                            efDisposeIntentionList.setAddWho("ZC");
                            efDisposeIntentionList.setAddWhoName("中钞");
                            efDisposeIntentionListMapper.insertEfDisposeIntentionList(efDisposeIntentionList);
                        }
                    }

                    //插入操作记录
                    log.setRowId(UUID.randomUUID().toString());
                    log.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                    log.setSerialNum(String.valueOf(System.currentTimeMillis()));
                    log.setControlType("同步处置信息");
                    log.setControlTime(LocalDateTime.now());
                    log.setRemark(remark);
                    log.setAddTime(LocalDateTime.now());
                    log.setAddWho("ZC");
                    log.setAddWhoName("中钞");
                    efWarehouseLogMapper.insertEfWarehouseLog(log);
                }
            }else{
                msg = "未查询到该仓单数据";
                flag = false;
            }
        }else{
            msg = "没有接收到仓单融资审核数据";
            flag = false;
        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }

}
