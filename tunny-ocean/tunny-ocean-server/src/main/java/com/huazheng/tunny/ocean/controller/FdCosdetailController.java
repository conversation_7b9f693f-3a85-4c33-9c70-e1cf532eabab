package com.huazheng.tunny.ocean.controller;


import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.util.StringUtils;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.ContainerTypeData;
import com.huazheng.tunny.ocean.api.entity.FdBill;
import com.huazheng.tunny.ocean.api.entity.FdCosdetail;
import com.huazheng.tunny.ocean.api.entity.FdCost;
import com.huazheng.tunny.ocean.api.vo.FdBillVO;
import com.huazheng.tunny.ocean.api.vo.FdCosdetailShengYfVo;
import com.huazheng.tunny.ocean.api.vo.FdCosdetailvo;
import com.huazheng.tunny.ocean.mapper.FdCosdetailMapper;
import com.huazheng.tunny.ocean.mapper.FdCostMapper;
import com.huazheng.tunny.ocean.service.ContainerTypeDataService;
import com.huazheng.tunny.ocean.service.FdCosdetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import java.util.UUID;

/**
 * 运单费用明细表
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:30:20
 */
@RestController
@RequestMapping("/fdcosdetail")
@Slf4j
public class FdCosdetailController {
    @Autowired
    private FdCosdetailMapper fdCosdetailMapper;
    @Autowired
    private FdCosdetailService fdCosdetailService;
    @Autowired
    private FdCostMapper fdCostMapper;
    @Autowired
    private ContainerTypeDataService containerTypeDataService;
    @Value("${db.database}")
    private String database;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdCosdetailService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdCosdetailService.selectFdCosdetailListByLike(new Query<>(params));
    }


    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/costdetailpage")
    public Page costpage(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
//         return  fdCosdetailService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdCosdetailService.costdetaillist(new Query<>(params));
    }

    @PostMapping("/costdetailpagenew")
    public R costpagenew(@RequestBody FdCosdetail fdCosdetail) {
        //数据库字段值完整查询
//         return  fdCosdetailService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return new R<>(fdCosdetailService.costdetaillistnew(fdCosdetail));
    }

    /**
     *  省平台费用详情列表
     * @param params
     * @return
     */
    @GetMapping("/selectShengListpage")
    public Page selectShengListpage(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
//         return  fdCosdetailService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdCosdetailService.selectShengListpage(new Query<>(params));
    }

    @PostMapping("/selectShengList")
    public R selectShengList(@RequestBody FdCosdetail fdCosdetail) {
        //数据库字段值完整查询
//         return  fdCosdetailService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        final List list = fdCosdetailService.selectShengList(fdCosdetail);
        return new R<>(list);
    }

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/amountpage")
    public Page amountpage(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
//         return  fdCosdetailService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdCosdetailService.amountpage(new Query<>(params));
    }


    /**
     * 信息
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Integer id) {
        FdCosdetail fdCosdetail =fdCosdetailService.selectById(id);
        return new R<>(fdCosdetail);
    } 

    @GetMapping("/selectcontainerNumberbycostcode")
    public R selectcontainerNumberbycostcode(@RequestParam String costCode) {
        List string =fdCosdetailService.selectcontainerNumberbycostcode(costCode);
        return new R<>(string);
    }

    /**
     * 保存
     * @param fdCosdetail
     * @return R
     */
    @PostMapping("/save")
    public R save(@RequestBody FdCosdetail fdCosdetail) {
        fdCosdetail.setUuid(UUID.randomUUID().toString());
        fdCosdetailService.insertFdCosdetail(fdCosdetail);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 台账二次确认后撤箱
     *
     * @param fdCosdetail
     * @return R
     */

  

    /**
     * 修改
     * @param fdCosdetail
     * @return R
     */
    @PutMapping
    public R update(@RequestBody FdCosdetail fdCosdetail) {
        fdCosdetailService.updateById(fdCosdetail);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 省平台费用编辑接口
     */
    @PostMapping("/updateFdCosdetalSheng")
    public R updateFdCosdetalSheng(List<FdCosdetail> list){
        if(list.size()>0){
            if(fdCosdetailService.updateFdCosdetalSheng(list)>0){
                return new R<>(Boolean.TRUE);
            }
        }else{
            return new R<>(Boolean.FALSE,"数据为空，请传入数据");
        }
        return new R<>(Boolean.FALSE);
    }

    /**
     * 删除
     *
     * @param fdCosdetails
     * @return R
     */
    @PostMapping("/del")
    public R delete(@RequestBody FdCosdetail fdCosdetails) {
        fdCosdetails.setDelFlag("N");
        FdCosdetail fdCosdetail = fdCosdetailService.selectFdCosdetailByuuId(fdCosdetails);
        if ("1".equals(fdCosdetail.getBillGenerate())) {
            return new R<>(Boolean.FALSE,"账单已生成，无法作废");
        }
        Boolean b = fdCosdetailService.deleteFdCosdetailByuuId(fdCosdetails);
        return new R<>(Boolean.TRUE,"费用作废成功");
    }

    /**
     * 批量删除
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<Integer> ids) {
        fdCosdetailService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<FdCosdetail> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = fdCosdetailService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<FdCosdetail> list = reader.readAll(FdCosdetail.class);
        fdCosdetailService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 导入
     */
    @PostMapping("/importFile")
    @ResponseBody
    public R importBasicData(@RequestParam  MultipartFile file ,String platformLevel ) throws Exception {
//        List<BookingRequesdetailDTO> chainGroupOrganizations=new ArrayList<>();
        R r=new R();
        try {
            String originalFilename = file.getOriginalFilename();
            InputStream inputStream = file.getInputStream();
            // 调用工具类中方法，读取excel文件中数据
            long i = System.currentTimeMillis();
            r = readExcel(originalFilename, inputStream,platformLevel);
            inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
            return new R<>(new Throwable(e));
        }
        /*for (BookingRequesdetailDTO c:chainGroupOrganizations) {
            System.out.println(c);
        }*/
        return r;
    }

    public boolean isRowEmpty(Row row){
        if(row!=null){
            for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
                Cell cell = row.getCell(c);
                if(cell!=null){
                    cell.setCellType(CellType.STRING);
                }
                if (cell != null && !"".equals(cell.getStringCellValue())){
                    return false;//不是空行
                }
            }
        }
        return true;
    }

    //获取准确的文件行数
    public Sheet getAccuracyContextNum(Workbook workbook) {
        // 取第一个sheet
        Sheet sheet = workbook.getSheetAt(0);
        // 删除空行
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            // 删除空行
            if (this.isRowEmpty(row)) {
                int lastRowNum = sheet.getLastRowNum();
                if (i >= 0 && i < lastRowNum) {
                    sheet.shiftRows(i + 1, lastRowNum, -1);// 将行号为i+1一直到行号为lastRowNum的单元格全部上移一行，以便删除i行
                }
                if (i == lastRowNum) {
                    if (row != null) {
                        sheet.removeRow(row);
                    }
                }
                i--;
            }
        }
        return sheet;
    }

    /**
     * 读取excel文件数据
     * @param fileName
     * @param inputStream
     * @return
     * @throws Exception
     */
    public  R readExcel(String fileName, InputStream inputStream ,String platformLevel) throws Exception {
        R r = new R();
        boolean ret = isXls(fileName);
        Workbook workbook = null;
        // 根据后缀创建不同的对象
        if(ret) {
            workbook = new HSSFWorkbook(inputStream);
        }else{
            workbook = new XSSFWorkbook(inputStream);
        }
        Sheet sheet = getAccuracyContextNum(workbook);

        List<FdCosdetail> list = new ArrayList<>();
        int lastRowNum = sheet.getLastRowNum();
        if(lastRowNum>0) {
            // 得到标题行(第二行)
            Row titleRow = sheet.getRow(1);
            if (titleRow.getCell(0) != null) {
                titleRow.getCell(0).setCellType(CellType.STRING);
                if (titleRow.getCell(0).getStringCellValue() != null && !"".equals(titleRow.getCell(0).getStringCellValue())) {
                    String cellValue = titleRow.getCell(0).getStringCellValue();//获取费用编码
                    FdCost fdCost = fdCostMapper.selectFdCostListByCostCode(cellValue);//根据费用编码查询基本信息
                    //明细信息
                    List<FdCosdetail> fdCosdetails = fdCosdetailMapper.selectConstDeatilByCostCode(cellValue);
                    if(fdCost==null){
                        return new R(Boolean.FALSE,"费用编码查询不到数据");
                    }
                    //从第二行开始读取
                    for (int i = 1; i <= lastRowNum; i++) {
                        FdCosdetail fdCosdetail = new FdCosdetail();
                        Row row = sheet.getRow(i);
                        fdCosdetail.setCostCode(cellValue);//费用编码
                        fdCosdetail.setUuid(UUID.randomUUID().toString());//主键id
                        fdCosdetail.setPlatformCode(fdCost.getPlatformCode());//平台编码
                        fdCosdetail.setPlatformName(fdCost.getPlatformName());//平台名称
                        fdCosdetail.setPlatformLevel(platformLevel);//平台层级 0市平台 1为省平台
                        fdCosdetail.setApplicationNumber(fdCost.getApplicationNumber());//申请单号
                        fdCosdetail.setTransportOrderNumber(fdCost.getTransportOrderNumber());//运单号
                        fdCosdetail.setCustomerCode(fdCost.getPaymentCustomerCode());//客户编码
                        fdCosdetail.setCustomerName(fdCost.getPaymentCustomerName());//客户名称
                        fdCosdetail.setStandbyA(fdCost.getStandbyB());
                        if (row.getCell(1) != null) {
                            row.getCell(1).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(1).getStringCellValue()) && row.getCell(1).getStringCellValue() != null) {
                                fdCosdetail.setContainerNumber(row.getCell(1).getStringCellValue().trim());//集装箱号
                            } else {
                                return new R(Boolean.FALSE, "箱号为空");
                            }
                        } else {
                            return new R(Boolean.FALSE, "箱号为空");
                        }

                        if (row.getCell(2) != null && row.getCell(3) != null) {
                            row.getCell(2).setCellType(CellType.STRING);
                            row.getCell(3).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(2).getStringCellValue()) && row.getCell(2).getStringCellValue() != null &&
                                    !"".equals(row.getCell(3).getStringCellValue()) && row.getCell(3).getStringCellValue() != null) {

                                Map<String, String> codeBbCategoriesCode = isCodeBbCategoriesCode(row.getCell(2).getStringCellValue(), row.getCell(3).getStringCellValue());
                                fdCosdetail.setCodeBbCategoriesCode(codeBbCategoriesCode.get("bCode"));//费用大类编码
                                fdCosdetail.setCodeBbCategoriesName(row.getCell(2).getStringCellValue());//费用大类名称

                                fdCosdetail.setCodeSsCategoriesCode(codeBbCategoriesCode.get("sCode"));//费用小类编码
                                fdCosdetail.setCodeSsCategoriesName(row.getCell(3).getStringCellValue());//费用小类名称
                            } else {
                                return new R(Boolean.FALSE, "费用大类(小类)名称为空");
                            }
                        } else {
                            return new R(Boolean.FALSE, "费用大类(小类)名称为空");
                        }

                        if (row.getCell(4) != null) {
                            row.getCell(4).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(4).getStringCellValue()) && row.getCell(4).getStringCellValue() != null) {
                                fdCosdetail.setCurrency(row.getCell(4).getStringCellValue());//币种
                            } else {
                                return new R(Boolean.FALSE, "币种为空");
                            }
                        } else {
                            return new R(Boolean.FALSE, "币种为空");
                        }
                        if (row.getCell(5) != null && row.getCell(6) != null) {
                            row.getCell(5).setCellType(CellType.NUMERIC);
                            row.getCell(6).setCellType(CellType.NUMERIC);
                            if (!"".equals(row.getCell(5).getStringCellValue()) && !"".equals(row.getCell(6).getStringCellValue())
                                    && row.getCell(5).getStringCellValue() != null && row.getCell(6).getStringCellValue() != null) {
                                fdCosdetail.setExchangeRate(BigDecimal.valueOf(row.getCell(5).getNumericCellValue()));//汇率
                                fdCosdetail.setOriginalCurrencyAmount(BigDecimal.valueOf(row.getCell(6).getNumericCellValue()));//原币金额  原币就是币种里的那个
                                fdCosdetail.setLocalCurrencyAmount(fdCosdetail.getExchangeRate().multiply(fdCosdetail.getOriginalCurrencyAmount()));//本币金额 一般都是指人民币 =汇率*原币金额
                            } else {
                                return new R(Boolean.FALSE, "汇率(原始金额)为空");
                            }
                        } else {
                            return new R(Boolean.FALSE, "汇率(原始金额)为空");
                        }
                        fdCosdetail.setNoteInformation(fdCost.getNoteInformation());//备注信息
                        fdCosdetail.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());//创建人
                        fdCosdetail.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());//创建人编码
                        fdCosdetail.setIncomeFlag("应收");//收支标识
                        //申请单号
                        fdCosdetail.setApplicationNumber(fdCosdetails.get(0).getApplicationNumber());
                        //运单号
                        fdCosdetail.setTransportOrderNumber(fdCosdetails.get(0).getTransportOrderNumber());
                        //账单编码
                        fdCosdetail.setBillCode(fdCosdetails.get(0).getBillCode());
                        list.add(fdCosdetail);

                    }

                    if("0".equals(platformLevel)) {
                        //批量新增
                        fdCosdetailMapper.insertFdCosdetailList(list);
                    }

                }
            } else {
                return new R(Boolean.FALSE, "费用编码不能为空");
            }
        }else {
            return new R(Boolean.FALSE,"表格为空");
        }
        workbook.close();
        r.setData(list);
        r.setMsg("上传成功");
        return r;
    }

    /**
     * 导入
     */
    @PostMapping("/importFileShengYf")
    @ResponseBody
    public R importFileShengYf(@RequestParam  MultipartFile file) throws Exception {
//        List<BookingRequesdetailDTO> chainGroupOrganizations=new ArrayList<>();
        R r=new R();
        try {
            String originalFilename = file.getOriginalFilename();
            InputStream inputStream = file.getInputStream();
            // 调用工具类中方法，读取excel文件中数据
            long i = System.currentTimeMillis();
            r = readExcelShengYf(originalFilename, inputStream);
            inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
            return new R<>(new Throwable(e));
        }
        /*for (BookingRequesdetailDTO c:chainGroupOrganizations) {
            System.out.println(c);
        }*/
        return r;
    }

    /**
     * 读取excel文件数据
     * @param fileName
     * @param inputStream
     * @return
     * @throws Exception
     */
    public  R readExcelShengYf(String fileName, InputStream inputStream) throws Exception {
        R r = new R();
        boolean ret = isXls(fileName);
        Workbook workbook = null;
        // 根据后缀创建不同的对象
        if(ret) {
            workbook = new HSSFWorkbook(inputStream);
        }else{
            workbook = new XSSFWorkbook(inputStream);
        }
//        workbook = new HSSFWorkbook(inputStream);
        Sheet sheet = getAccuracyContextNum(workbook);
        FdCosdetailShengYfVo fdCosdetailShengYfVo=new FdCosdetailShengYfVo();
        List<FdCosdetailvo> list = new ArrayList<>();
        int lastRowNum = sheet.getLastRowNum();

        ContainerTypeData sel = new ContainerTypeData();
        sel.setDeleteFlag("N");
        final List<ContainerTypeData> containerTypeDataList = containerTypeDataService.selectContainerTypeDataList(sel);

        if(lastRowNum>0) {
            // 得到标题行(第二行)
            Row titleRow = sheet.getRow(1);
            if (titleRow.getCell(0) != null) {
                titleRow.getCell(0).setCellType(CellType.STRING);
                if (titleRow.getCell(0).getStringCellValue() != null && !"".equals(titleRow.getCell(0).getStringCellValue())) {
                    String cellValue = titleRow.getCell(0).getStringCellValue();//获取费用编码
                    FdCost fdCost = fdCostMapper.selectFdCostListByCostCode(cellValue);//根据费用编码查询基本信息
                    //明细信息
                    List<FdCosdetail> fdCosdetails = fdCosdetailMapper.selectConstDeatilByCostCode(cellValue);
                    if(fdCost==null){
                        return new R(Boolean.FALSE,"费用编码查询不到数据");
                    }
                    //从第二行开始读取
                    //读取单元格顺序是后改的不必在意
                    for (int i = 1; i <= lastRowNum; i++) {
                        FdCosdetailvo fdCosdetail = new FdCosdetailvo();
                        Row row = sheet.getRow(i);
                        fdCosdetail.setCostCode(cellValue);//费用编码
                        fdCosdetail.setUuid(UUID.randomUUID().toString());//主键id
                        fdCosdetail.setPlatformCode(fdCost.getPlatformCode());//平台编码
                        fdCosdetail.setPlatformName(fdCost.getPlatformName());//平台名称
                        fdCosdetail.setPlatformLevel("2");//平台层级 0市平台 1为省平台  2多联
                        fdCosdetail.setApplicationNumber(fdCost.getApplicationNumber());//申请单号
                        fdCosdetail.setTransportOrderNumber(fdCost.getTransportOrderNumber());//运单号
                        fdCosdetail.setCustomerCode(fdCost.getPaymentCustomerCode());//客户编码
                        fdCosdetail.setCustomerName(fdCost.getPaymentCustomerName());//客户名称
                        fdCosdetail.setStandbyA(fdCost.getStandbyB());
                        if (row.getCell(1) != null) {
                            row.getCell(1).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(1).getStringCellValue()) && row.getCell(1).getStringCellValue() != null) {
                                fdCosdetail.setContainerNumber(row.getCell(1).getStringCellValue().trim());//集装箱号
                            } else {
                                return new R(Boolean.FALSE, "箱号为空");
                            }
                        } else {
                            return new R(Boolean.FALSE, "箱号为空");
                        }
                        //箱属
                        if(row.getCell(2)!=null){
                            row.getCell(2).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(2).getStringCellValue())) {
                                if("中铁箱".equals(row.getCell(2).getStringCellValue())) {
                                    fdCosdetail.setContainerOwner("1");
                                }else if("自备箱".equals(row.getCell(2).getStringCellValue())){
                                    fdCosdetail.setContainerOwner("0");
                                }
                            }
                        }

                        //箱型
                        if(row.getCell(3)!=null){
                            row.getCell(3).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(3).getStringCellValue())) {
//                                fdCosdetail.setContainerType(row.getCell(3).getStringCellValue());
                                final String containerTypeCode = row.getCell(3).getStringCellValue();
                                Boolean flag = true;
                                for (ContainerTypeData data:containerTypeDataList
                                ) {
                                    if(data.getContainerTypeCode().equals(containerTypeCode)){
                                        fdCosdetail.setContainerTypeCode(data.getContainerTypeCode());
                                        fdCosdetail.setContainerTypeName(data.getContainerTypeName());
                                        fdCosdetail.setContainerType(data.getContainerTypeSize());
                                        flag = false;
                                        break;
                                    }
                                }

                                if(flag){
                                    return new R(Boolean.FALSE,"未查询到该箱型代码："+containerTypeCode);
                                }
                            }
                        }

                        //品名
                        if(row.getCell(4)!=null){
                            row.getCell(4).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(4).getStringCellValue())) {
                                fdCosdetail.setGoodsName(row.getCell(4).getStringCellValue());
                            }
                        }


                        //类型
                        if(row.getCell(5)!=null){
                            row.getCell(5).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(5).getStringCellValue())) {
                                if("过境".equals(row.getCell(5).getStringCellValue())) {
                                    fdCosdetail.setIdentification("P");
                                }else if("进口".equals(row.getCell(5).getStringCellValue())){
                                    fdCosdetail.setIdentification("I");
                                }else if("出口".equals(row.getCell(5).getStringCellValue())){
                                    fdCosdetail.setIdentification("E");
                                }
                            }
                        }
                        if (row.getCell(6) != null && row.getCell(7) != null) {
                            row.getCell(6).setCellType(CellType.STRING);
                            row.getCell(7).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(6).getStringCellValue()) && row.getCell(6).getStringCellValue() != null &&
                                    !"".equals(row.getCell(7).getStringCellValue()) && row.getCell(7).getStringCellValue() != null) {

                                Map<String, String> codeBbCategoriesCode = isCodeBbCategoriesCode(row.getCell(6).getStringCellValue(), row.getCell(7).getStringCellValue());
                                fdCosdetail.setCodeBbCategoriesCode(codeBbCategoriesCode.get("bCode"));//费用大类编码
                                fdCosdetail.setCodeBbCategoriesName(row.getCell(6).getStringCellValue());//费用大类名称

                                fdCosdetail.setCodeSsCategoriesCode(codeBbCategoriesCode.get("sCode"));//费用小类编码
                                fdCosdetail.setCodeSsCategoriesName(row.getCell(7).getStringCellValue());//费用小类名称
                            } else {
                                return new R(Boolean.FALSE, "费用大类(小类)名称为空");
                            }
                        } else {
                            return new R(Boolean.FALSE, "费用大类(小类)名称为空");
                        }

                        if (row.getCell(9) != null && row.getCell(10) != null) {
                            row.getCell(9).setCellType(CellType.NUMERIC);
                            row.getCell(10).setCellType(CellType.NUMERIC);
                            if (!"".equals(row.getCell(9).getStringCellValue()) && !"".equals(row.getCell(10).getStringCellValue())
                                    && row.getCell(9).getStringCellValue() != null && row.getCell(10).getStringCellValue() != null) {
                                fdCosdetail.setExchangeRate(BigDecimal.valueOf(row.getCell(9).getNumericCellValue()));//汇率
                                fdCosdetail.setLocalCurrencyAmount(BigDecimal.valueOf(row.getCell(10).getNumericCellValue()));//人民币金额
                                fdCosdetail.setOriginalCurrencyAmount(fdCosdetail.getLocalCurrencyAmount().divide(fdCosdetail.getExchangeRate(),2,BigDecimal.ROUND_HALF_UP));//本币金额 一般都是指人民币 =汇率*原币金额
                                if(BigDecimal.valueOf(row.getCell(10).getNumericCellValue()).compareTo(BigDecimal.valueOf(0))!=0){
                                    if (row.getCell(8) != null) {
                                        row.getCell(8).setCellType(CellType.STRING);
                                        if (!"".equals(row.getCell(8).getStringCellValue()) && row.getCell(8).getStringCellValue() != null) {
                                            fdCosdetail.setCurrency(row.getCell(8).getStringCellValue());//币种
                                        } else {
                                            return new R(Boolean.FALSE, "币种为空");
                                        }
                                    } else {
                                        return new R(Boolean.FALSE, "币种为空");
                                    }
                                }

                            } else {
                                return new R(Boolean.FALSE, "汇率(原始金额)为空");
                            }
                        } else {
                            return new R(Boolean.FALSE, "汇率(原始金额)为空");
                        }
                        if(row.getCell(11)!=null){
                            row.getCell(11).setCellType(CellType.STRING);
                            fdCosdetail.setStandbyC(row.getCell(11).getStringCellValue());
                        }
                        fdCosdetail.setNoteInformation(fdCost.getNoteInformation());//备注信息
                        fdCosdetail.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());//创建人
                        fdCosdetail.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());//创建人编码
                        fdCosdetail.setIncomeFlag("应收");//收支标识
                        //申请单号
                        fdCosdetail.setApplicationNumber(fdCosdetails.get(0).getApplicationNumber());
                        //运单号
                        fdCosdetail.setTransportOrderNumber(fdCosdetails.get(0).getTransportOrderNumber());
                        //账单编码
                        fdCosdetail.setBillCode(fdCosdetails.get(0).getBillCode());


                        FdCosdetail fdCosdetailByCountryname=new FdCosdetail();
                        fdCosdetailByCountryname.setCostCode(fdCosdetail.getCostCode());
                        fdCosdetailByCountryname.setContainerNumber(fdCosdetail.getContainerNumber());
                        fdCosdetailByCountryname.setCodeBbCategoriesCode(fdCosdetail.getCodeBbCategoriesCode());
                        fdCosdetailByCountryname.setCodeSsCategoriesCode(fdCosdetail.getCodeSsCategoriesCode());
                        List<FdCosdetail> fdCosdetails1 = fdCosdetailService.costdetaillist2(fdCosdetailByCountryname);
                        if(fdCosdetails1.size()>0){
                            //收货人
                            if(StringUtils.isNotBlank(fdCosdetails1.get(0).getConsignorName())){
                                fdCosdetail.setConsignorName(fdCosdetails1.get(0).getConsignorName());
                            }
                            //目的国
                            if(StringUtils.isNotBlank(fdCosdetails1.get(0).getCountryname())){
                                fdCosdetail.setCountryname(fdCosdetails1.get(0).getCountryname());
                            }
                            //发站
                            if(StringUtils.isNotBlank(fdCosdetails1.get(0).getDestinationName())){
                                fdCosdetail.setDestinationName(fdCosdetails1.get(0).getDestinationName());
                            }
                            //到站
                            if(StringUtils.isNotBlank(fdCosdetails1.get(0).getDestination())){
                                fdCosdetail.setDestination(fdCosdetails1.get(0).getDestination());
                            }
                        }
                        list.add(fdCosdetail);

                    }
                    BigDecimal jnyf=BigDecimal.valueOf(0);
                    BigDecimal jwfy=BigDecimal.valueOf(0);
                    for (FdCosdetailvo fdCosdetailvo:list) {
                        if("f_domestic_fee".equals(fdCosdetailvo.getCodeSsCategoriesCode())){
                            jnyf=jnyf.add(fdCosdetailvo.getLocalCurrencyAmount());
                        }else if("f_overseas_fee".equals(fdCosdetailvo.getCodeSsCategoriesCode())){
                            jwfy=jwfy.add(fdCosdetailvo.getLocalCurrencyAmount());
                        }
                    }
                    fdCosdetailShengYfVo.setList(list);
                    fdCosdetailShengYfVo.setJnyf(jnyf);
                    fdCosdetailShengYfVo.setJwyf(jwfy);
                }
            } else {
                return new R(Boolean.FALSE, "费用编码不能为空");
            }
        }else {
            return new R(Boolean.FALSE,"表格为空");
        }
        workbook.close();
        r.setData(fdCosdetailShengYfVo);
        r.setMsg("上传成功");
        return r;
    }
    /**
     * 根据名称判断大类编码
     */
    public static Map<String,String> isCodeBbCategoriesCode(String bName,String sName){
        Map<String,String> map=new HashMap<>();
        String bCode="";//大类编码
        String sCode="";//小类编码
        if("量价捆绑退费".equals(bName)){
            bCode="f_ljkb_fee_type";
            if("量价捆绑退费".equals(sName)){
                sCode="f_ljkb_fee";
            }
        }else if("发运运费".equals(bName)){
            bCode="f_fee_type";
            if("汇差费用".equals(sName)){
                sCode="f_erd_fee";
            }else if("境内运费".equals(sName)){
                sCode="f_domestic_fee";
            }else if("境外运费".equals(sName) || "境外费用".equals(sName)){
                sCode="f_overseas_fee";
            }
        }else if("额外费用".equals(bName)){
            bCode="f_extra_fee_type";
            if("罚金".equals(sName)){
                sCode="f_fine_fee";
            }else if("滞留金".equals(sName)){
                sCode="f_stranded_fee";
            }else if("报关费".equals(sName)){
                sCode="f_bg_fee";
            }else if("场站操作费".equals(sName)){
                sCode="f_czcz_fee";
            }else if("海运费".equals(sName)){
                sCode="f_sf_fee";
            }else if("代理服务费".equals(sName)){
                sCode="f_agent_fee";
            }else if("拖车费".equals(sName)){
                sCode="f_truck_fee";
            }else if("亏舱费".equals(sName)){
                sCode="f_cancel_fee";
            }

        }
        map.put("bCode",bCode);
        map.put("sCode",sCode);
        return map;
    }


    /**
     * 判断导入文件格式
     * @param fileName
     * @return
     */
    public static boolean isXls(String fileName){
        // (?i)忽略大小写
        if(fileName.matches("^.+\\.(?i)(xls)$")){
            return true;
        }else if(fileName.matches("^.+\\.(?i)(xlsx)$")){
            return false;
        }else{
            throw new RuntimeException("格式不对");
        }
    }
    /*
     * 导入省平台数据
     */
    @PostMapping("/importFileFdCosdetailSheng")
    public R importFileFdCosdetailSheng(@RequestParam  MultipartFile file) throws Exception {
        R r=new R();
        try {
            String originalFilename = file.getOriginalFilename();
            InputStream inputStream = file.getInputStream();
            // 调用工具类中方法，读取excel文件中数据
            long i = System.currentTimeMillis();
            r = readExcels(originalFilename, inputStream);
            inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
            return new R<>(new Throwable(e));
        }
        return r;
    }

    /**
     * 读取excel文件数据
     * @param fileName
     * @param inputStream
     * @return
     * @throws Exception
     */
    public  R readExcels(String fileName, InputStream inputStream) throws Exception {
        R r = new R();
        boolean ret = isXls(fileName);
        Workbook workbook = null;
        // 根据后缀创建不同的对象
         if(ret) {
            workbook = new HSSFWorkbook(inputStream);
        }else{
            workbook = new XSSFWorkbook(inputStream);
        }
        Sheet sheet = getAccuracyContextNum(workbook);

        List<FdCosdetail> list = new ArrayList<>();
        int lastRowNum = sheet.getLastRowNum();
        if(lastRowNum>0) {
            //从第二行开始读取
            for (int i = 1; i <= lastRowNum; i++) {
                FdCosdetailvo fdCosdetail = new FdCosdetailvo();
                Row row = sheet.getRow(i);
                if(row.getCell(1)!= null && row.getCell(2)!=null && row.getCell(4)!=null && row.getCell(5)!=null){
                    row.getCell(1).setCellType(CellType.STRING);
                    row.getCell(2).setCellType(CellType.STRING);
                    row.getCell(4).setCellType(CellType.STRING);
                    row.getCell(5).setCellType(CellType.STRING);
                    fdCosdetail.setCostCode(row.getCell(1).getStringCellValue());
                    fdCosdetail.setContainerNumber(row.getCell(2).getStringCellValue());
                    Map<String, String> codeBbCategoriesCode = isCodeBbCategoriesCode(row.getCell(4).getStringCellValue(), row.getCell(5).getStringCellValue());
                    fdCosdetail.setCodeBbCategoriesCode(codeBbCategoriesCode.get("bCode"));
                    fdCosdetail.setCodeSsCategoriesCode(codeBbCategoriesCode.get("sCode"));
                    FdCosdetail fdCosde = fdCosdetailService.selectFdCosdetailByCostCodeAndXh(fdCosdetail);
                    fdCosdetail.setCodeBbCategoriesName(row.getCell(4).getStringCellValue());
                    fdCosdetail.setCodeSsCategoriesName(row.getCell(5).getStringCellValue());
                    if(row.getCell(6) !=null && row.getCell(7) !=null && row.getCell(8)!=null){
                        row.getCell(6).setCellType(CellType.STRING);
                        row.getCell(7).setCellType(CellType.NUMERIC);
                        row.getCell(8).setCellType(CellType.NUMERIC);
                        fdCosdetail.setCurrency(row.getCell(6).getStringCellValue());
                        fdCosdetail.setExchangeRate(BigDecimal.valueOf(row.getCell(7).getNumericCellValue()));
                        fdCosdetail.setOriginalCurrencyAmount(BigDecimal.valueOf(row.getCell(8).getNumericCellValue()));
                        fdCosdetail.setLocalCurrencyAmount(fdCosdetail.getExchangeRate().multiply(fdCosdetail.getOriginalCurrencyAmount()));
                    }
                    fdCosdetail.setPlatformCode(fdCosde.getPlatformCode());
                    fdCosdetail.setPlatformName(fdCosde.getPlatformName());
                    fdCosdetail.setPlatformLevel(fdCosde.getPlatformLevel());
                    fdCosdetail.setCustomerName(fdCosde.getCustomerName());
                    fdCosdetail.setCustomerCode(fdCosde.getCustomerCode());
                    fdCosdetail.setIncomeFlag(fdCosde.getIncomeFlag());
                    fdCosdetail.setApplicationNumber(fdCosde.getApplicationNumber());
                    fdCosdetail.setTransportOrderNumber(fdCosde.getTransportOrderNumber());
                    fdCosdetail.setActualTrainNumber(fdCosde.getActualTrainNumber());
                    fdCosdetail.setNoteInformation(fdCosde.getNoteInformation());
                    fdCosdetail.setDelFlag("N");
                    fdCosdetail.setCreateTime(LocalDateTime.now().toString());
                    fdCosdetail.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
                    fdCosdetail.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
                    fdCosdetail.setBillCode(fdCosde.getBillCode());
                    fdCosdetail.setBillGenerate(fdCosde.getBillGenerate());
                    fdCosdetail.setInvoice(fdCosde.getInvoice());
                    fdCosdetail.setInvoiceApplicationNumber(fdCosde.getInvoiceApplicationNumber());
                    fdCosdetail.setStandbyA(fdCosde.getStandbyA());
                    list.add(fdCosdetail);
                }
            }
        }else{
            return new R(Boolean.FALSE,"表格为空");
        }
        workbook.close();
        r.setData(list);
        r.setMsg("上传成功");
        return r;
    }



    /**
     * 省平台费用导出
     */
    @PostMapping("/exportedFileSheng")
    public void exportedFileSheng(@RequestBody Map<String,Object> map,HttpServletResponse response) throws IOException {
        XSSFWorkbook workbook=new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("费用明细");
        XSSFRow row = sheet.createRow(0);
        //宽度
        for(int i=0;i<=8;i++){
            sheet.setColumnWidth(i,60*80);
        }

        row.setHeight((short) (10*50));
        //字体
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");

        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);//水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setBorderBottom(BorderStyle.THIN); //下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);

        XSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("序号");
        cell0.setCellStyle(style);
        XSSFCell cell1 = row.createCell(1);
        cell1.setCellValue("费用编码");
        cell1.setCellStyle(style);
        XSSFCell cell2 = row.createCell(2);
        cell2.setCellValue("箱号");
        cell2.setCellStyle(style);
        XSSFCell cell7 = row.createCell(3);
        cell7.setCellValue("货源组织单位");
        cell7.setCellStyle(style);
        XSSFCell cell8 = row.createCell(4);
        cell8.setCellValue("费用大类名称");
        cell8.setCellStyle(style);
        XSSFCell cell9 = row.createCell(5);
        cell9.setCellValue("费用小类名称");
        cell9.setCellStyle(style);
        XSSFCell cell10 = row.createCell(6);
        cell10.setCellValue("币种");
        cell10.setCellStyle(style);
        XSSFCell cell11 = row.createCell(7);
        cell11.setCellValue("汇率");
        cell11.setCellStyle(style);
        XSSFCell cell12 = row.createCell(8);
        cell12.setCellValue("原币金额");
        cell12.setCellStyle(style);
//        HSSFCell cell13 = row.createCell(9);
//        cell13.setCellValue("本币金额");
//        cell13.setCellStyle(style);
        FdCosdetail fdCosdetail=new FdCosdetail();
        fdCosdetail.setDelFlag("N");
        fdCosdetail.setCostCode(map.get("costCode").toString());
        fdCosdetail.setDatabase(database);
        List<FdCosdetailvo> newcostdetaillist = fdCosdetailMapper.newcostdetaillist(fdCosdetail);
        int j=1;
        for (FdCosdetailvo fdCosdetailvo:newcostdetaillist) {
            XSSFRow rows = sheet.createRow(j);
            rows.createCell(0).setCellValue(j);
            rows.createCell(1).setCellValue(fdCosdetailvo.getCostCode());
            rows.createCell(2).setCellValue(fdCosdetailvo.getContainerNumber());
            rows.createCell(3).setCellValue(fdCosdetailvo.getCreateUserrealname());
            rows.createCell(4).setCellValue(fdCosdetailvo.getCodeBbCategoriesName());
            rows.createCell(5).setCellValue(fdCosdetailvo.getCodeSsCategoriesName());
            rows.createCell(6).setCellValue(fdCosdetailvo.getCurrency());
            rows.createCell(7).setCellValue(fdCosdetailvo.getExchangeRate().toString());
            rows.createCell(8).setCellValue(fdCosdetailvo.getOriginalCurrencyAmount().toString());
//            rows.createCell(9).setCellValue(fdCosdetailvo.getLocalCurrencyAmount().toString());
            j++;
        }
        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader( "Content-Disposition", "attachment;filename=" + new String( "费用管理.xls".getBytes("GB2312"), "8859_1" ));
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();

    }


    /**
     * 导出
     * @param map
     * @param response
     * @throws IOException
     */
    @PostMapping("/exportedFile")
    public  void exported(@RequestBody Map<String,Object> map,HttpServletResponse response) throws IOException {
        HSSFWorkbook workbook=new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("费用管理");
        HSSFSheet sheetSm = workbook.createSheet("导入说明");
        HSSFRow row = sheet.createRow(0);
        HSSFRow smRow = sheetSm.createRow(0);
        //宽度
        for(int i=0;i<=6;i++){
            sheet.setColumnWidth(i,60*80);
        }
        row.setHeight((short) (10*50));
        smRow.setHeight((short) (10*50));
        sheetSm.setColumnWidth(0,80*200);
        //字体
        HSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");
        HSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);//水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setBorderBottom(BorderStyle.THIN); //下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);
        HSSFCell smRowCell = smRow.createCell(0);
        smRowCell.setCellValue("如果币种是人民币汇率必须填写1");
        smRowCell.setCellStyle(style);

        HSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("费用编码");
        cell0.setCellStyle(style);
        HSSFCell cell1 = row.createCell(1);
        cell1.setCellValue("箱号");
        cell1.setCellStyle(style);
        HSSFCell cell2 = row.createCell(2);
        cell2.setCellValue("费用大类名称(必填)");
        cell2.setCellStyle(style);
        HSSFCell cell3 = row.createCell(3);
        cell3.setCellValue("费用小类名称(必填)");
        cell3.setCellStyle(style);
        HSSFCell cell4 = row.createCell(4);
        cell4.setCellValue("币种(必填)");
        cell4.setCellStyle(style);
        HSSFCell cell5 = row.createCell(5);
        cell5.setCellValue("汇率(必填)");
        cell5.setCellStyle(style);
        HSSFCell cell6 = row.createCell(6);
        cell6.setCellValue("原始金额(必填)");
        cell6.setCellStyle(style);


        String fybm = String.valueOf(map.get("fybm"));
        List<String> xh = (List<String>) map.get("xh");

        //2021-12-03 对箱号进行排序
        Collections.sort(xh, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                // 返回相反的compare
                return o2.compareTo(o1);
            }
        });

        int j=1;
        for (String xhs:xh){
            HSSFRow irow = sheet.createRow(j);
            irow.createCell(0).setCellValue(fybm);
            irow.createCell(1).setCellValue(xhs);
            j++;
        }
        //demo 级联下拉列表
        Map<String, List<String>> data = new HashMap<>();
        data.put("量价捆绑退费", Arrays.asList("量价捆绑退费"));
        data.put("发运运费", Arrays.asList("汇费费用", "境内运费", "境外运费"));
        data.put("额外费用", Arrays.asList("罚金", "滞留金", "报关费","场站操作费","海运费","代理服务费","拖车费","亏舱费"));
        addValidationToSheet(workbook, sheet, data, 'C', 'D', 1, 99999);

        //demo 单独下拉列表
        addValidationToSheet(workbook, sheet, new String[]{"人民币", "美元","欧元","英镑"}, 'E', 1, 99999);

        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader( "Content-Disposition", "attachment;filename=" + new String( "费用管理.xlsx".getBytes("GB2312"), "8859_1" ));
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }



    /**
     * 导出
     * @param map
     * @param response
     * @throws IOException
     */
    @PostMapping("/exportedFileShengYf")
    public  void exportedFileShengYf(@RequestBody Map<String,Object> map,HttpServletResponse response) throws IOException {
        HSSFWorkbook workbook=new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("费用管理");
        HSSFSheet sheetSm = workbook.createSheet("导入说明");
        HSSFRow row = sheet.createRow(0);
        HSSFRow smRow = sheetSm.createRow(0);
        //宽度
        for(int i=0;i<=10;i++){
            sheet.setColumnWidth(i,60*80);
        }
        row.setHeight((short) (10*50));
        smRow.setHeight((short) (10*50));
        sheetSm.setColumnWidth(0,80*200);
        //字体
        HSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");
        HSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);//水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setBorderBottom(BorderStyle.THIN); //下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);
        HSSFCell smRowCell = smRow.createCell(0);
        smRowCell.setCellValue("如果币种是人民币汇率必须填写1");
        smRowCell.setCellStyle(style);

        HSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("费用编码");
        cell0.setCellStyle(style);
        HSSFCell cell1 = row.createCell(1);
        cell1.setCellValue("箱号");
        cell1.setCellStyle(style);
        HSSFCell cell2 = row.createCell(2);
        cell2.setCellValue("箱属");
        cell2.setCellStyle(style);
        HSSFCell cell3 = row.createCell(3);
        cell3.setCellValue("箱型");
        cell3.setCellStyle(style);
        HSSFCell cell4 = row.createCell(4);
        cell4.setCellValue("品名");
        cell4.setCellStyle(style);
        HSSFCell cell5 = row.createCell(5);
        cell5.setCellValue("类型");
        cell5.setCellStyle(style);
        HSSFCell cell6 = row.createCell(6);
        cell6.setCellValue("费用大类名称");
        cell6.setCellStyle(style);
        HSSFCell cell7 = row.createCell(7);
        cell7.setCellValue("费用小类名称");
        cell7.setCellStyle(style);
        HSSFCell cell8 = row.createCell(8);
        cell8.setCellValue("币种");
        cell8.setCellStyle(style);
        HSSFCell cell9 = row.createCell(9);
        cell9.setCellValue("汇率");
        cell9.setCellStyle(style);
        HSSFCell cell10 = row.createCell(10);
        cell10.setCellValue("人民币金额");
        cell10.setCellStyle(style);
        HSSFCell cell11 = row.createCell(11);
        cell11.setCellValue("货源组织单位");
        cell11.setCellStyle(style);


        String fybm = String.valueOf(map.get("fybm"));
        FdCosdetail fdCosdetail=new FdCosdetail();
        fdCosdetail.setCostCode(fybm);
        List<FdCosdetailvo> fdCosdetails = fdCosdetailService.costdetaillistYf(fdCosdetail);

        //2021-12-03 对箱号进行排序
        Collections.sort(fdCosdetails, new Comparator<FdCosdetailvo>() {
            @Override
            public int compare(FdCosdetailvo o1, FdCosdetailvo o2) {
                //升序
                return o1.getContainerNumber().compareTo(o2.getContainerNumber());
            }
        });

        int j=1;
        for (FdCosdetailvo fdCostVo:fdCosdetails){
            HSSFRow irow = sheet.createRow(j);
            //费用编码
            irow.createCell(0).setCellValue(fybm);
            //箱号
            irow.createCell(1).setCellValue(fdCostVo.getContainerNumber());
            //箱属
            if(fdCostVo.getContainerOwner()!=null){
                if("0".equals(fdCostVo.getContainerOwner())){
                    irow.createCell(2).setCellValue("自备箱");
                }else if("1".equals(fdCostVo.getContainerOwner())){
                    irow.createCell(2).setCellValue("中铁箱");
                }
            }
            //箱型
            if(fdCostVo.getContainerType()!=null){
                irow.createCell(3).setCellValue(fdCostVo.getContainerType());
            }
            //品名
            if(fdCostVo.getGoodsName()!=null){
                irow.createCell(4).setCellValue(fdCostVo.getGoodsName());
            }
            //类型
            if(fdCostVo.getIdentification()!=null){
                if("I".equals(fdCostVo.getIdentification())){
                    irow.createCell(5).setCellValue("进口");
                }else if("P".equals(fdCostVo.getIdentification())){
                    irow.createCell(5).setCellValue("过境");
                }else if("E".equals(fdCostVo.getIdentification())){
                    irow.createCell(5).setCellValue("出口");
                }
            }
            //费用大类名称
            if(fdCostVo.getCodeBbCategoriesName()!=null){
                irow.createCell(6).setCellValue(fdCostVo.getCodeBbCategoriesName());
            }
            //费用小类名称
            if(fdCostVo.getCodeSsCategoriesName()!=null){
                irow.createCell(7).setCellValue(fdCostVo.getCodeSsCategoriesName());
            }
            //币种
            if(fdCostVo.getCurrency()!=null){
                irow.createCell(8).setCellValue(fdCostVo.getCurrency());
            }
            //汇率
            if(fdCostVo.getExchangeRate()!=null){
                irow.createCell(9).setCellValue(fdCostVo.getExchangeRate().toString());
            }
            //人民币金额
            if(fdCostVo.getOriginalCurrencyAmount()!=null){
                irow.createCell(10).setCellValue(fdCostVo.getLocalCurrencyAmount().toString());
            }
            if(fdCostVo.getStandbyC()!=null){
                irow.createCell(11).setCellValue(fdCostVo.getStandbyC().toString());
            }
            j++;
        }
        //demo 级联下拉列表
        Map<String, List<String>> data = new HashMap<>();
        data.put("量价捆绑退费", Arrays.asList("量价捆绑退费"));
        data.put("发运运费", Arrays.asList("汇费费用", "境内运费", "境外运费"));
        data.put("额外费用", Arrays.asList("罚金", "滞留金", "报关费","场站操作费","海运费","代理服务费","拖车费","亏舱费"));
        addValidationToSheet(workbook, sheet, data, 'G', 'H', 1, 99999);

        //demo 单独下拉列表
        addValidationToSheet(workbook, sheet, new String[]{"人民币", "美元","欧元","英镑","无"}, 'I', 1, 99999);

        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader( "Content-Disposition", "attachment;filename=" + new String( "费用管理.xlsx".getBytes("GB2312"), "8859_1" ));
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }
    /**
     * 给sheet页  添加级联下拉列表
     *
     * @param workbook    excel
     * @param targetSheet sheet页
     * @param options     要添加的下拉列表内容  ， keys 是下拉列表1中的内容，每个Map.Entry.Value 是对应的级联下拉列表内容
     * @param keyColumn   下拉列表1位置
     * @param valueColumn 级联下拉列表位置
     * @param fromRow     级联限制开始行
     * @param endRow      级联限制结束行
     */
    public static void addValidationToSheet(Workbook workbook, Sheet targetSheet, Map<String, List<String>> options, char keyColumn, char valueColumn, int fromRow, int endRow) {
        String hiddenSheetName = "sheet" + workbook.getNumberOfSheets();
        Sheet hiddenSheet = workbook.createSheet(hiddenSheetName);
        List<String> firstLevelItems = new ArrayList<>();

        int rowIndex = 0;
        for (Map.Entry<String, List<String>> entry : options.entrySet()) {
            String parent = formatNameName(entry.getKey());
            firstLevelItems.add(parent);
            List<String> children = entry.getValue();

            int columnIndex = 0;
            Row row = hiddenSheet.createRow(rowIndex++);
            Cell cell = null;

            for (String child : children) {
                cell = row.createCell(columnIndex++);
                cell.setCellValue(child);
            }

            char lastChildrenColumn = (char) ((int) 'A' + children.size() - 1);
            createName(workbook, parent, String.format(hiddenSheetName + "!$A$%s:$%s$%s", rowIndex, lastChildrenColumn, rowIndex));

            DVConstraint constraint = DVConstraint.createFormulaListConstraint("INDIRECT($" + keyColumn + "1)");
            CellRangeAddressList regions = new CellRangeAddressList(fromRow, endRow, valueColumn - 'A', valueColumn - 'A');
            targetSheet.addValidationData(new HSSFDataValidation(regions, constraint));
        }

        addValidationToSheet(workbook, targetSheet, firstLevelItems.toArray(), keyColumn, fromRow, endRow);

    }

    /**
     * 给sheet页，添加下拉列表
     *
     * @param workbook    excel文件，用于添加Name
     * @param targetSheet 级联列表所在sheet页
     * @param options     级联数据 ['百度','阿里巴巴']
     * @param column      下拉列表所在列 从'A'开始
     * @param fromRow     下拉限制开始行
     * @param endRow      下拉限制结束行
     */
    public static void addValidationToSheet(Workbook workbook, Sheet targetSheet, Object[] options, char column, int fromRow, int endRow) {
        String hiddenSheetName = "sheet" + workbook.getNumberOfSheets();
        Sheet optionsSheet = workbook.createSheet(hiddenSheetName);
        String nameName = column + "_parent";

        int rowIndex = 0;
        for (Object option : options) {
            int columnIndex = 0;
            Row row = optionsSheet.createRow(rowIndex++);
            Cell cell = row.createCell(columnIndex++);
            cell.setCellValue(option.toString());
        }

        createName(workbook, nameName, hiddenSheetName + "!$A$1:$A$" + options.length);

        DVConstraint constraint = DVConstraint.createFormulaListConstraint(nameName);
        CellRangeAddressList regions = new CellRangeAddressList(fromRow, endRow, (int) column - 'A', (int) column - 'A');
        targetSheet.addValidationData(new HSSFDataValidation(regions, constraint));
    }

    /**
     * 不可数字开头
     *
     * @param name
     * @return
     */
    static String formatNameName(String name) {
        name = name.replaceAll(" ", "").replaceAll("-", "_").replaceAll(":", ".");
        if (Character.isDigit(name.charAt(0))) {
            name = "_" + name;
        }

        return name;
    }

    private static Name createName(Workbook workbook, String nameName, String formula) {
        Name name = workbook.createName();
        name.setNameName(nameName);
        name.setRefersToFormula(formula);
        return name;
    }
}
