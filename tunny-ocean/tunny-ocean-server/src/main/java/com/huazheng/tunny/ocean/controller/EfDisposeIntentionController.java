package com.huazheng.tunny.ocean.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.ocean.api.entity.EfDisposeIntention;
import com.huazheng.tunny.ocean.api.entity.EfReleaseInfo;
import com.huazheng.tunny.ocean.api.entity.OperationLog;
import com.huazheng.tunny.ocean.service.EfDisposeIntentionService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.service.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 发布处置意向
 *
 * <AUTHOR>
 * @date 2023-05-10 15:32:35
 */
@Slf4j
@RestController
@RequestMapping("/efdisposeintention")
public class EfDisposeIntentionController {

    @Autowired
    private EfDisposeIntentionService efDisposeIntentionService;
    @Autowired
    private SignatureController signatureController;
    @Autowired
    private OperationLogService operationLogService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  efDisposeIntentionService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return efDisposeIntentionService.selectEfDisposeIntentionListByLike2(new Query<>(params));
    }

    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        EfDisposeIntention efDisposeIntention =efDisposeIntentionService.selectEfDisposeIntentionById(rowId);
        return new R<>(efDisposeIntention);
    }

    /**
     * 保存
     * @param efDisposeIntention
     * @return R
     */
    @PostMapping
    public R save(@RequestBody EfDisposeIntention efDisposeIntention) {
        efDisposeIntentionService.insert(efDisposeIntention);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param efDisposeIntention
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody EfDisposeIntention efDisposeIntention) {
        efDisposeIntentionService.updateById(efDisposeIntention);
        return new R<>(Boolean.TRUE);
    }

    

    /**
     * 删除
     * @param rowId
     * @return R
     */
    @GetMapping("/del/{rowId}")
    public R delete(@PathVariable  String rowId) {
        efDisposeIntentionService.deleteById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        efDisposeIntentionService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<EfDisposeIntention> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<EfDisposeIntention> list = efDisposeIntentionService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), EfDisposeIntention.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }



    @PostMapping("/publishDisposeIntention")
    public String publishDisposeIntention(@RequestBody JSONObject jsonObject) {
        String content = null;
        try {
            //调用接口
            String data = signatureController.getPost(jsonObject);
            EfDisposeIntention efDisposeIntention = JSONUtil.toBean(data, EfDisposeIntention.class);

            String content2 = String.valueOf(jsonObject.get("content"));
            OperationLog log4 = new OperationLog();
            log4.setUuid(UUID.randomUUID().toString());
            log4.setProcessType("处置信息同步");
            log4.setOperationCode("ZC");
            log4.setOperationName("中钞");
            log4.setOperationTime(new Date());
            log4.setOperationOpinion(content2 + data );
            operationLogService.insertOperationLog(log4);

            //插入数据
            content = efDisposeIntentionService.publishDisposeIntention(efDisposeIntention);
        }catch (Exception e){
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE,e.getMessage()), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPost("/v1/ent/warehouse/publishDisposeIntention", content);
        return result;
    }
}
