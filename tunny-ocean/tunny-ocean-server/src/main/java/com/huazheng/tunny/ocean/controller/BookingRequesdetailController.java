package com.huazheng.tunny.ocean.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.ExcelObjectCheckerUtil;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.BookingRequesdetailDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.service.BookingRequesdetailService;
import com.huazheng.tunny.ocean.service.ContainerTypeDataService;
import com.huazheng.tunny.ocean.service.ShifmanagementService;
import com.huazheng.tunny.ocean.service.StationManagementService;
import com.huazheng.tunny.ocean.util.CheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 订舱申请单子表
 *
 * <AUTHOR> code ocean
 * @date 2021-07-02 14:38:08
 */
@RestController
@RequestMapping("/bookingrequesdetail")
@Slf4j
public class BookingRequesdetailController {
    @Autowired
    private BookingRequesdetailService bookingRequesdetailService;

    @Autowired
    private ContainerTypeDataService containerTypeDataService;
    @Autowired
    private StationManagementService stationManagementService;
    @Autowired
    private ShifmanagementService shifmanagementService;

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //分页模糊查询
        return bookingRequesdetailService.selectBookingRequesdetailListByLike(new Query<>(params));
    }

    /**
     * 小程序列表
     *
     * @param bookingRequesdetail
     * @return
     */
    @PostMapping("/weChatList")
    public Map<String, Object> weChatList(@RequestBody BookingRequesdetail bookingRequesdetail) {
        //分页模糊查询
        return bookingRequesdetailService.weChatList(bookingRequesdetail);
    }

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/list")
    public R list(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return new R(bookingRequesdetailService.selectBookingRequesdetailListByLike(new Query<>(params)));
    }


    /**
     * 信息
     *
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        BookingRequesdetail bookingRequesdetail = bookingRequesdetailService.selectById(rowId);
        return new R<>(bookingRequesdetail);
    }

    /**
     * 保存
     *
     * @param bookingRequesdetail
     * @return R
     */
    @PostMapping
    public R save(@RequestBody BookingRequesdetail bookingRequesdetail) {
        bookingRequesdetailService.insert(bookingRequesdetail);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改/删除集装箱信息
     *
     * @param bookingRequesdetail
     * @return R
     */
    @PutMapping
    public R update(@RequestBody BookingRequesdetail bookingRequesdetail) {
        return new R(bookingRequesdetailService.updateBookingRequesdetail(bookingRequesdetail));
    }


    /**
     * 逻辑删除
     */
    @PostMapping("/updateDeleteFlag")
    public R updateDeleteFlag(@RequestBody BookingRequesheader bookingRequesheader) {
        R r = new R();
        bookingRequesdetailService.updateBookingRequesdetails(bookingRequesheader);
        r.setB(Boolean.TRUE);
        r.setMsg("删除成功");
        r.setCode(200);
        return r;
    }


    /**
     * 删除
     *
     * @param rowId
     * @return R
     */
    @DeleteMapping("/{rowId}")
    public R delete(@PathVariable String rowId) {
        bookingRequesdetailService.deleteById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     *
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody List<String> rowIds) {
        bookingRequesdetailService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 导出EXCEL
     *
     * @return
     */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<BookingRequesdetail> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = bookingRequesdetailService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }


    /**
     * 导入EXCEL
     * <p>
     *
     * @param titleStr
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        boolean isLargeNumberOfObjects = ExcelObjectCheckerUtil.containsLargeNumberOfObjects(file.getInputStream(), 0);
        if (isLargeNumberOfObjects) {
            return new R<>(new Throwable("文件中存在未知对象文件，请使用快捷键<Ctrl+G或F5>，进行定位，删除对象后再进行提交！"));
        }

        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<BookingRequesdetail> list = reader.readAll(BookingRequesdetail.class);
        bookingRequesdetailService.insertBatch(list);
        return R.success();
    }

    @PostMapping("/importFile")
    @ResponseBody
    public R importBasicData(@RequestParam MultipartFile file, String shiftNo, String platformCode) throws Exception {
//        List<BookingRequesdetailDTO> chainGroupOrganizations=new ArrayList<>();

        boolean isLargeNumberOfObjects = ExcelObjectCheckerUtil.containsLargeNumberOfObjects(file.getInputStream(), 0);
        if (isLargeNumberOfObjects) {
            return new R<>(new Throwable("文件中存在未知对象文件，请使用快捷键<Ctrl+G或F5>，进行定位，删除对象后再进行提交！"));
        }
        R r = new R();
        try {
            String originalFilename = file.getOriginalFilename();
            InputStream inputStream = file.getInputStream();
            // 调用工具类中方法，读取excel文件中数据
            long i = System.currentTimeMillis();
            r = readExcel(originalFilename, inputStream, shiftNo, platformCode);
            inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
            return new R<>(new Throwable(e));
        }
        /*for (BookingRequesdetailDTO c:chainGroupOrganizations) {
            System.out.println(c);
        }*/
        return r;
    }


    /**
     * 导入订舱申请详情（台账模板）
     *
     * @Param: file, shiftNo
     * @Return: com.huazheng.tunny.common.core.util.R
     * @Author: zhaohr
     * @Date: 2024/10/29 14:58
     **/
    @PostMapping("/importTzTemplate")
    @ResponseBody
    public R importTzTemplate(@RequestParam MultipartFile file, String shiftNo, String platformCode) throws Exception {
        return bookingRequesdetailService.importTzTemplate(file, shiftNo, platformCode);
    }

    public boolean isRowEmpty(Row row) {
        if (row != null) {
            for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
                Cell cell = row.getCell(c);
                if (cell != null) {
                    cell.setCellType(CellType.STRING);
                }
                if (cell != null && !"".equals(cell.getStringCellValue())) {
                    return false;
                }
            }
        }
        return true;
    }

    //获取准确的文件行数
    public Sheet getAccuracyContextNum(Workbook workbook) {
        // 取第一个sheet
        Sheet sheet = workbook.getSheetAt(0);
        // 删除空行
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            // 删除空行
            if (this.isRowEmpty(row)) {
                int lastRowNum = sheet.getLastRowNum();
                if (i >= 0 && i < lastRowNum) {
                    // 将行号为i+1一直到行号为lastRowNum的单元格全部上移一行，以便删除i行
                    sheet.shiftRows(i + 1, lastRowNum, -1);
                }
                if (i == lastRowNum) {
                    if (row != null) {
                        sheet.removeRow(row);
                    }
                }
                i--;
            }
        }
        return sheet;
    }

    /**
     * 读取excel文件数据
     *
     * @param fileName
     * @param inputStream
     * @return
     * @throws Exception
     */
    public R readExcel(String fileName, InputStream inputStream, String shiftNo, String platformCode) throws Exception {
        List<BookingRequesdetailDTO> list = new ArrayList<>();
        ExcelReader reader = ExcelUtil.getReader(inputStream);
        // 读取所有数据到List<Map<String, Object>>对象
        List<Map<String, Object>> data = reader.readAll();
        if (CollUtil.isNotEmpty(data)) {
            String identification = null;
            Shifmanagement sel2 = new Shifmanagement();
            sel2.setShiftId(shiftNo);
            sel2.setPlatformCode(platformCode);
            sel2.setDeleteFlag("N");
            List<Shifmanagement> shifmanagements = shifmanagementService.selectShifmanagementList(sel2);
            if (CollUtil.isNotEmpty(shifmanagements) && StrUtil.isNotBlank(shifmanagements.get(0).getIdentification())) {
                identification = shifmanagements.get(0).getIdentification();
            }
            List<String> stringList = new ArrayList<>();
            List<String> strings = bookingRequesdetailService.selectBookingRequesdetailByShiftNo(shiftNo);
            if (strings.size() > 0) {
                stringList.addAll(strings);
            }
            ContainerTypeData sel = new ContainerTypeData();
            sel.setDeleteFlag("N");
            List<ContainerTypeData> containerTypeDataList = containerTypeDataService.selectContainerTypeDataList(sel);

            StationManagement sel3 = new StationManagement();
            sel3.setDeleteFlag("N");
            List<StationManagement> stationManagements = stationManagementService.selectStationManagementList(sel3);
            for (int i = 0; i < data.size(); i++) {
                BookingRequesdetailDTO bookingRequesdetailDTO = new BookingRequesdetailDTO();
                Map<String, Object> map = data.get(i);
                if (map.get("箱号(必填)") != null) {
                    String containerNo = String.valueOf(map.get("箱号(必填)"));
                    containerNo = containerNo.trim();
                    if (CheckUtil.verifyCntrCode(containerNo)) {
                        if (!stringList.contains(containerNo)) {
                            if (containerNo.contains("\n")) {
                                containerNo.replace("\n", "");
                            }
                            if (containerNo.contains(" ")) {
                                containerNo.replace(" ", "");
                            }
                            containerNo = containerNo.trim();
                            bookingRequesdetailDTO.setContainerNo(containerNo);
                            stringList.add(containerNo);
                        } else {
                            return R.error(containerNo + "箱号重复,请查看本次箱号或者班次号下所有的订舱申请的箱号");

                        }
                    } else {
                        return R.error(containerNo + "箱号规则不对");
                    }
                } else {

                    return R.error("箱号不能为空");
                }

                if (map.get("进/出口类型(必填)") != null) {
                    String resveredField01 = String.valueOf(map.get("进/出口类型(必填)"));
                    if (StrUtil.isNotBlank(resveredField01)) {
                        if ("出口".equals(resveredField01)) {
                            bookingRequesdetailDTO.setResveredField01("E");
                        } else if ("进口".equals(resveredField01)) {
                            bookingRequesdetailDTO.setResveredField01("I");
                        } else if ("过境".equals(resveredField01)) {
                            bookingRequesdetailDTO.setResveredField01("P");
                        }
                        if (StrUtil.isNotBlank(identification) && !identification.equals(bookingRequesdetailDTO.getResveredField01())) {

                            return R.error("箱进出口过境类型与班列类型不符");
                        }
                    } else {
                        return R.error("进/出口类型为空");
                    }
                } else {
                    return R.error("进/出口类型为空");
                }


                if (map.get("箱属(必填)") != null) {
                    String box = String.valueOf(map.get("箱属(必填)")).trim();
                    if (StrUtil.isNotBlank(box)) {
                        if ("自备箱".equals(box)) {
                            bookingRequesdetailDTO.setBox("0");
                        } else if ("中铁箱".equals(box)) {
                            bookingRequesdetailDTO.setBox("1");
                        } else {
                            bookingRequesdetailDTO.setBox(box);
                        }
                    } else {
                        return R.error("箱属为空");
                    }
                } else {
                    return R.error("箱属为空");
                }


                if (map.get("箱型(必填)") != null) {
                    String containerTypeCode = String.valueOf(map.get("箱型(必填)"));
                    if (StrUtil.isNotBlank(containerTypeCode)) {
                        Boolean flag = true;
                        for (ContainerTypeData containerTypeData : containerTypeDataList
                        ) {
                            if (containerTypeData.getContainerTypeCode().equals(containerTypeCode)) {
                                bookingRequesdetailDTO.setContainerTypeCode(containerTypeData.getContainerTypeCode());
                                bookingRequesdetailDTO.setContainerTypeName(containerTypeData.getContainerTypeName());
                                bookingRequesdetailDTO.setContainerType(containerTypeData.getContainerTypeSize());
                                flag = false;
                                break;
                            }
                        }

                        if (flag) {
                            return R.error("未查询到该箱型代码：" + containerTypeCode);
                        }
                    } else {
                        return R.error("箱型代码为空");
                    }
                } else {
                    return R.error("箱型代码为空");
                }

                if (map.get("自重(kg)") != null) {
                    String deadWeight = String.valueOf(map.get("自重(kg)"));
                    if (StrUtil.isNotBlank(deadWeight)) {
                        bookingRequesdetailDTO.setDeadWeight(Float.valueOf(deadWeight));
                    } else {
                        bookingRequesdetailDTO.setDeadWeight(new Float(0.00));
                    }
                } else {
                    bookingRequesdetailDTO.setDeadWeight(new Float(0.00));
                }

                if (map.get("毛重(kg)") != null) {
                    String grossWeight = String.valueOf(map.get("毛重(kg)"));
                    if (StrUtil.isNotBlank(grossWeight)) {
                        bookingRequesdetailDTO.setGrossWeight(Float.valueOf(grossWeight));
                    } else {
                        bookingRequesdetailDTO.setGrossWeight(new Float(0.00));
                    }
                } else {
                    bookingRequesdetailDTO.setGrossWeight(new Float(0.00));
                }

                if (map.get("净重(kg)") != null) {
                    String netWeight = String.valueOf(map.get("净重(kg)"));
                    if (StrUtil.isNotBlank(netWeight)) {
                        bookingRequesdetailDTO.setNetWeight(Float.valueOf(netWeight));
                    } else {
                        bookingRequesdetailDTO.setNetWeight(new Float(0.00));
                    }
                } else {
                    bookingRequesdetailDTO.setNetWeight(new Float(0.00));
                }

                if (map.get("货名") != null) {
                    String goodsName = String.valueOf(map.get("货名"));
                    if (StrUtil.isNotBlank(goodsName)) {
                        bookingRequesdetailDTO.setGoodsName(goodsName);
                    } else {
                        bookingRequesdetailDTO.setGoodsName("");
                    }
                } else {
                    bookingRequesdetailDTO.setGoodsName("");
                }

                if (map.get("发站") != null) {
                    String destinationName = String.valueOf(map.get("发站"));
                    if (StrUtil.isNotBlank(destinationName)) {
                        if (CollUtil.isNotEmpty(stationManagements)) {
                            for (StationManagement stationManagement : stationManagements
                            ) {
                                if (stationManagement.getStationName().equals(destinationName)) {
                                    bookingRequesdetailDTO.setDestinationName(destinationName);
                                    break;
                                }
                            }
                            if (StrUtil.isBlank(bookingRequesdetailDTO.getDestinationName())) {
                                return R.error("发站" + destinationName + "不存在");
                            }
                        }
                        if (map.get("局属") != null) {
                            String bureauSubordinate = String.valueOf(map.get("局属"));
                            if (StrUtil.isNotBlank(bureauSubordinate)) {
                                if (CollUtil.isNotEmpty(stationManagements)) {
                                    for (StationManagement stationManagement : stationManagements
                                    ) {
                                        if (stationManagement.getStationName().equals(destinationName) && stationManagement.getBureau().equals(bureauSubordinate)) {
                                            bookingRequesdetailDTO.setBureauSubordinate(bureauSubordinate);
                                            break;
                                        }
                                    }
                                    if (StrUtil.isBlank(bookingRequesdetailDTO.getDestinationName()) || StrUtil.isBlank(bookingRequesdetailDTO.getBureauSubordinate())) {
                                        return R.error("发站" + destinationName + "与局属" + bureauSubordinate + "不符");
                                    }

                                }
                            } else {
                                bookingRequesdetailDTO.setBureauSubordinate("");
                            }
                        } else {
                            bookingRequesdetailDTO.setBureauSubordinate("");
                        }
                    } else {
                        bookingRequesdetailDTO.setDestinationName("");
                        bookingRequesdetailDTO.setBureauSubordinate("");
                    }
                } else {
                    bookingRequesdetailDTO.setDestinationName("");
                    bookingRequesdetailDTO.setBureauSubordinate("");
                }

                if (map.get("到达站") != null) {
                    String destination = String.valueOf(map.get("到达站"));
                    if (StrUtil.isNotBlank(destination)) {
                        if (CollUtil.isNotEmpty(stationManagements)) {
                            for (StationManagement stationManagement : stationManagements
                            ) {
                                if (stationManagement.getStationName().equals(destination)) {
                                    bookingRequesdetailDTO.setDestination(destination);
                                    break;
                                }
                            }
                            if (StrUtil.isBlank(bookingRequesdetailDTO.getDestination())) {

                                return R.error("到达站" + destination + "不存在");
                            }
                        }
                    } else {
                        bookingRequesdetailDTO.setDestination("");
                    }
                } else {
                    bookingRequesdetailDTO.setDestination("");
                }

                if (map.get("口岸站") != null) {
                    String portStation = String.valueOf(map.get("口岸站"));
                    if (StrUtil.isNotBlank(portStation)) {
                        if (CollUtil.isNotEmpty(stationManagements)) {
                            for (StationManagement stationManagement : stationManagements
                            ) {
                                if (stationManagement.getStationName().equals(portStation) && "1".equals(stationManagement.getIsPort())) {
                                    bookingRequesdetailDTO.setPortStation(portStation);
                                    break;
                                }
                            }
                            if (StrUtil.isBlank(bookingRequesdetailDTO.getPortStation())) {
                                return R.error("口岸站" + portStation + "不存在");
                            }
                        }
                    } else {
                        bookingRequesdetailDTO.setPortStation("");
                    }
                } else {
                    bookingRequesdetailDTO.setPortStation("");
                }

                list.add(bookingRequesdetailDTO);
            }
        }

        return R.success(list);
    }

    /**
     * 判断导入文件格式
     *
     * @param fileName
     * @return
     */
    public static boolean isXls(String fileName) {
        // (?i)忽略大小写
        if (fileName.matches("^.+\\.(?i)(xls)$")) {
            return true;
        } else if (fileName.matches("^.+\\.(?i)(xlsx)$")) {
            return false;
        } else {
            throw new RuntimeException("格式不对");
        }
    }

    /*
     * 1、第一部分由4位英文字母组成。前三位代码 (Owner Code) 主要说明箱主、经营人，第四位代码说明集装箱的类型。列如CBHU
     *  开头的标准集装箱是表明箱主和经营人为中远集运。
     * 2、 第二部分由6位数字组成。是箱体注册码（Registration Code），用于一个集装箱箱体持有的唯一标识。
     * 3、 第三部分为校验码（Check Digit）由前4位字母和6位数字经过校验规则运算得到，用于识别在校验时是否发生错误。即第11位数字。
     * 根据校验规则箱号的每个字母和数字都有一个运算的对应值。箱号的前10位字母和数字的对应值从0到Z对应数值为10到38，11、22、33不能对11取模数，所以要除去
     * 地址：https://blog.csdn.net/weixin_38611617/article/details/115069232
     */
    public static boolean verifyCntrCode(String strCode) {
        boolean result = true;
        strCode = strCode.trim();
        try {
            if (strCode.length() != 11) {
                return false;
            }
            if (strCode.startsWith("JSQ5") || strCode.startsWith("JSQ6")) {
                String str = strCode.substring(4);
                char[] codeChars = str.toCharArray();
                String charCode = "0123456789";
                for (int i = 0; i < 7; i++) {
                    int idx = charCode.indexOf(codeChars[i]);
                    if (idx == -1 || charCode.charAt(idx) == '?') {
                        return false;
                    }
                }
                return true;
            } else {
                char[] codeChars = strCode.toCharArray();
                String charCode = "0123456789A?BCDEFGHIJK?LMNOPQRSTU?VWXYZ";
                int num = 0;
                for (int i = 0; i < 10; i++) {
                    int idx = charCode.indexOf(codeChars[i]);
                    if (idx == -1 || charCode.charAt(idx) == '?') {
                        return false;
                    }
                    idx = (int) (idx * Math.pow(2, i));
                    num += idx;
                }
                num = (num % 11) % 10;
                result = Integer.parseInt(String.valueOf(codeChars[10])) == num;
            }

        } catch (Exception e) {
            result = false;
        }
        return result;
    }
}
