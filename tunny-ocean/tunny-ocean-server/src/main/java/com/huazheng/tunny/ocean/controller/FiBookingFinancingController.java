package com.huazheng.tunny.ocean.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.vo.FiBookingInformationDetailVO;
import com.huazheng.tunny.ocean.service.*;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import java.util.UUID;

/**
 * 订舱单融资
 *
 * <AUTHOR> code ocean
 * @date 2022-03-08 09:41:35
 */
@RestController
@RequestMapping("/fibookingfinancing")
@Slf4j
public class FiBookingFinancingController {
    @Autowired
    private FiBookingFinancingService fiBookingFinancingService;
    @Autowired
    private SysNoConfigService sysNoConfigService;
    @Autowired
    private FiBookingInformationService fiBookingInformationService;
    @Autowired
    private SignatureController signatureController;
    @Autowired
    private CustomerInfoService customerInfoService;
    @Autowired
    private OperationLogService operationLogService;

    @Value("${path.windowsPath}")
    private String windowsPath;
    @Value("${path.linuxPath}")
    private String linuxPath;
    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fiBookingFinancingService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        params.put("entCode",SecurityUtils.getUserInfo().getUserName());
        return fiBookingFinancingService.selectFiBookingFinancingListByLike(new Query<>(params));
    }

    @GetMapping("/selectFiBookingFinancingPage")
    public Page selectFiBookingFinancingPage(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return fiBookingFinancingService.selectFiBookingFinancingPage(new Query<>(params));
    }

    @GetMapping("/selectFiBookingFinancingSum")
    public R selectFiBookingFinancingSum(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return fiBookingFinancingService.selectFiBookingFinancingSum(params);
    }

    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        FiBookingFinancing fiBookingFinancing =fiBookingFinancingService.selectFiBookingFinancingById(rowId);
        if(fiBookingFinancing!=null && fiBookingFinancing.getAssetCode()!=null && !"".equals(fiBookingFinancing.getAssetCode())){
            FiBookingInformation info = new FiBookingInformation();
            info.setAssetCode(fiBookingFinancing.getAssetCode());
            info.setDeleteFlag("N");
            List<FiBookingInformation> informationList = fiBookingInformationService.selectFiBookingInformationList(info);
            fiBookingFinancing.setBookingList(informationList);
        }
        return new R<>(fiBookingFinancing);
    }

    /**
     * 保存
     * @param fiBookingFinancing
     * @return R
     */
    @PostMapping
    public R save(@RequestBody FiBookingFinancing fiBookingFinancing) {
        String assetCode = "";
        String chainState = "PENDING";
        Boolean flag = true;
        String msg = "success";
        String from = "001:";
        CustomerInfo customerInfo = new CustomerInfo();
        customerInfo.setCustomerCode(fiBookingFinancing.getEntCode());
        customerInfo.setDeleteFlag("N");
        List<CustomerInfo> customerInfos = customerInfoService.selectCustomerInfoList1(customerInfo);
        if(customerInfos!= null && customerInfos.size()>0){
            fiBookingFinancing.setEntSocialCode(customerInfos.get(0).getSocialUcCode());
        }
        List<FiBookingInformation> infoList = fiBookingFinancing.getBookingList();
        if(infoList!=null && infoList.size()>0){
            for (FiBookingInformation info:infoList
             ) {
                if(info.getTotalBookingAmount()!=null){
                    info.setTotalAmount((info.getTotalBookingAmount().multiply(BigDecimal.valueOf(100))).longValue());
                }else{
                    info.setTotalAmount(0L);
                }
                if(info.getOccupiedAmountThis()!=null){
                    info.setUseAmount((info.getOccupiedAmountThis().multiply(BigDecimal.valueOf(100))).longValue());
                }else{
                    info.setUseAmount(0L);
                }
            }
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String datef = sdf.format(date);
            fiBookingFinancing.setApplyDate(datef);
            if(fiBookingFinancing.getApplyAmountB()!=null){
                fiBookingFinancing.setApplyAmount((fiBookingFinancing.getApplyAmountB().multiply(BigDecimal.valueOf(100))).longValue());
            }
            //接口调用
            String json = JSONUtil.parseObj(fiBookingFinancing, true).toStringPretty();
            final String result = signatureController.doPost("/v1/ent/booking/apply", json);
            JSONObject dataObject = JSONUtil.parseObj(result);
            String b = String.valueOf(dataObject.get("b"));
            msg = String.valueOf(dataObject.get("msg"));
            JSONObject jsonObject = JSONUtil.parseObj(dataObject.get("data"));
            if(jsonObject.containsKey("assetCode")){
                assetCode  = String.valueOf(jsonObject.get("assetCode"));
            }

            if(!"true".equals(b)){
                from = "002:";
                if(assetCode==null || "".equals(assetCode) || "null".equals(assetCode)){
                    return new R<>(Boolean.FALSE,from+msg);
                }
                chainState = "FAIL";
                flag = false;
            }
        }else{
            return new R<>(Boolean.FALSE,"001:未获取到订舱单信息！");
        }
        fiBookingFinancing.setRowId(UUID.randomUUID().toString());
        fiBookingFinancing.setAssetCode(assetCode);
        fiBookingFinancing.setAssetState("APPLY");
        fiBookingFinancing.setChainState(chainState);
        fiBookingFinancing.setAddWho(SecurityUtils.getUserInfo().getUserName());
        fiBookingFinancing.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
        fiBookingFinancing.setAddTime(LocalDateTime.now());
        for (FiBookingInformation info:infoList
             ) {
            info.setRowId(UUID.randomUUID().toString());
            info.setAssetCode(assetCode);
            info.setOccupiedAmount(info.getOccupiedAmountThis());
            info.setAddWho(SecurityUtils.getUserInfo().getUserName());
            info.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
            info.setAddTime(LocalDateTime.now());
            fiBookingInformationService.insertFiBookingInformation(info);
        }
        fiBookingFinancingService.insertFiBookingFinancing(fiBookingFinancing);
        return new R<>(flag,from+msg);
    }

    /**
     * 保存
     * @param fiBookingFinancing
     * @return R
     */
    @PostMapping("/retry")
    public R retry(@RequestBody FiBookingFinancing fiBookingFinancing) {
        String chainState = "PENDING";
        Boolean flag = true;
        String msg = "success";
        String from = "001:";
        //接口调用
        String json = JSONUtil.parseObj(fiBookingFinancing, true).toStringPretty();
        final String result = signatureController.doPost("/v1/ent/booking/retry", json);
        JSONObject dataObject = JSONUtil.parseObj(result);
        String b = String.valueOf(dataObject.get("b"));
        msg = String.valueOf(dataObject.get("msg"));
        if(!"true".equals(b)){
            chainState = "FAIL";
            flag = false;
            from = "002:";
        }
        fiBookingFinancing.setAssetState("APPLY");
        fiBookingFinancing.setChainState(chainState);
        fiBookingFinancing.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
        fiBookingFinancing.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
        fiBookingFinancing.setUpdateTime(LocalDateTime.now());
        fiBookingFinancingService.updateFiBookingFinancingInfo(fiBookingFinancing);
        return new R<>(flag,from+msg);
    }

    //舱单融资情况同步
    @PostMapping("/updateFiBookingFinancingInfo")
    public String updateFiBookingFinancingInfo(@RequestBody JSONObject jsonObject) {
        String content = null;
        try {
            String data = signatureController.getPost(jsonObject);

            /*String content2 = String.valueOf(jsonObject.get("content"));
            OperationLog log4 = new OperationLog();
            log4.setUuid(UUID.randomUUID().toString());
            log4.setProcessType("舱单融资情况同步");
            log4.setOperationCode("zc");
            log4.setOperationName("中钞");
            log4.setOperationTime(new Date());
            log4.setOperationOpinion(content2 + data );
            operationLogService.insertOperationLog(log4);*/

            FiBookingFinancing fiBookingFinancing = JSONUtil.toBean(data, FiBookingFinancing.class);
            FiBookingFinancing fiBookingFinancing1 = fiBookingFinancingService.selectFiBookingFinancingByAssetCode(fiBookingFinancing.getAssetCode());
            if(fiBookingFinancing1!=null){
                if(fiBookingFinancing.getPayAmount()!=null){
                    fiBookingFinancing.setPayAmount(fiBookingFinancing.getPayAmount().divide(BigDecimal.valueOf(100)));
                }
                if(fiBookingFinancing.getPayAmountDollar()!=null){
                    fiBookingFinancing.setPayAmountDollar(fiBookingFinancing.getPayAmountDollar().divide(BigDecimal.valueOf(100)));
                }
                fiBookingFinancing.setUpdateWho("zc");
                fiBookingFinancing.setUpdateWhoName("中钞");
                fiBookingFinancing.setUpdateTime(LocalDateTime.now());

                List<FiBookingInformation> infoList = fiBookingFinancing.getBookingList();
                if(infoList!=null && infoList.size()>0){
                    for (FiBookingInformation info:infoList
                    ) {
                        info.setRowId(UUID.randomUUID().toString());
                        info.setAssetCode(fiBookingFinancing.getAssetCode());
                        if(info.getTotalAmount()!=null){//订舱单总金额
                            info.setTotalBookingAmount((BigDecimal.valueOf(info.getTotalAmount())).divide(BigDecimal.valueOf(100)));
                        }
                        if(info.getUsedAmount()!=null){//已融资金额
                            info.setAmountFinanced((BigDecimal.valueOf(info.getUsedAmount())).divide(BigDecimal.valueOf(100)));
                        }
                        if(info.getUsingAmount()!=null){//在途占用金额
                            info.setOccupiedAmount((BigDecimal.valueOf(info.getUsingAmount())).divide(BigDecimal.valueOf(100)));
                        }
                        if(info.getAvailableAmount()!=null){//可用金额
                            info.setAvailableAmountB((BigDecimal.valueOf(info.getAvailableAmount())).divide(BigDecimal.valueOf(100)));
                        }
                        if(info.getCurrentUseAmount()!=null){//本次占用金额
                            info.setOccupiedAmountThis((BigDecimal.valueOf(info.getCurrentUseAmount())).divide(BigDecimal.valueOf(100)));
                        }
                        info.setUpdateWho("zc");
                        info.setUpdateWhoName("中钞");
                        info.setUpdateTime(LocalDateTime.now());
                        fiBookingInformationService.updateFiBookingInformationByAssetCode(info);
                    }
                }

                fiBookingFinancingService.updateFiBookingFinancingInfo(fiBookingFinancing);
            }else{
                if(fiBookingFinancing.getPayAmount()!=null){
                    fiBookingFinancing.setPayAmount(fiBookingFinancing.getPayAmount().divide(BigDecimal.valueOf(100)));
                }
                if(fiBookingFinancing.getApplyAmount()!=null){
                    fiBookingFinancing.setApplyAmountB((BigDecimal.valueOf(fiBookingFinancing.getApplyAmount())).divide(BigDecimal.valueOf(100)));
                }
                fiBookingFinancing.setAddWho("zc");
                fiBookingFinancing.setAddWhoName("中钞");
                fiBookingFinancing.setAddTime(LocalDateTime.now());
                List<FiBookingInformation> infoList = fiBookingFinancing.getBookingList();
                if(infoList!=null && infoList.size()>0){
                    for (FiBookingInformation info:infoList
                    ) {
                        info.setRowId(UUID.randomUUID().toString());
                        info.setAssetCode(fiBookingFinancing.getAssetCode());
                        if(info.getTotalAmount()!=null){//订舱单总金额
                            info.setTotalBookingAmount((BigDecimal.valueOf(info.getTotalAmount())).divide(BigDecimal.valueOf(100)));
                        }
                        if(info.getUsedAmount()!=null){//已融资金额
                            info.setAmountFinanced((BigDecimal.valueOf(info.getUsedAmount())).divide(BigDecimal.valueOf(100)));
                        }
                        if(info.getUsingAmount()!=null){//在途占用金额
                            info.setOccupiedAmount((BigDecimal.valueOf(info.getUsingAmount())).divide(BigDecimal.valueOf(100)));
                        }
                        if(info.getAvailableAmount()!=null){//可用金额
                            info.setAvailableAmountB((BigDecimal.valueOf(info.getAvailableAmount())).divide(BigDecimal.valueOf(100)));
                        }
                        if(info.getCurrentUseAmount()!=null){//本次占用金额
                            info.setOccupiedAmountThis((BigDecimal.valueOf(info.getCurrentUseAmount())).divide(BigDecimal.valueOf(100)));
                        }
                        info.setAddWho("zc");
                        info.setAddWhoName("中钞");
                        info.setAddTime(LocalDateTime.now());
                        fiBookingInformationService.insertFiBookingInformation(info);
                    }
                }
                CustomerInfo c = new CustomerInfo();
                c.setSocialUcCode(fiBookingFinancing.getEntSocialCode());
                List<CustomerInfo> cs = customerInfoService.selectCustomerInfoListBySocialUcCode2(c);
                if(cs != null && cs.size() > 0){
                    fiBookingFinancing.setEntCode(cs.get(0).getCustomerCode());
                }
                fiBookingFinancing.setRowId(UUID.randomUUID().toString());
                fiBookingFinancingService.insertFiBookingFinancing(fiBookingFinancing);
            }

            content = JSONUtil.parseObj(new R<>(Boolean.TRUE,"同步数据完成！"), false).toStringPretty();
        }catch (Exception e){
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE,e.getMessage()), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPost("/fibookingfinancing/updateFiBookingFinancingInfo", content);
        return result;
    }

    /**
     * 舱单融资情况同步
     * @param fiBookingFinancing
     * @return R
     */
    @PostMapping("/updateFiBookingFinancingInfo2")
    public R updateFiBookingFinancingInfo2(@RequestBody FiBookingFinancing fiBookingFinancing) {
        fiBookingFinancing.setUpdateWho("zc");
        fiBookingFinancing.setUpdateWhoName("中钞");
        fiBookingFinancing.setUpdateTime(LocalDateTime.now());
        fiBookingFinancingService.updateFiBookingFinancingInfo(fiBookingFinancing);
        return new R<>(Boolean.TRUE,"同步成功！");
    }

    /**
     * 修改
     * @param fiBookingFinancing
     * @return R
     */
    @PutMapping
    public R update(@RequestBody FiBookingFinancing fiBookingFinancing) {
        fiBookingFinancingService.updateById(fiBookingFinancing);
        return new R<>(Boolean.TRUE);
    }



    /**
     * 删除
     * @param rowId
     * @return R
     */
    @DeleteMapping("/{rowId}")
    public R delete(@PathVariable  String rowId) {
        fiBookingFinancingService.deleteById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        fiBookingFinancingService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<FiBookingFinancing> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = fiBookingFinancingService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<FiBookingFinancing> list = reader.readAll(FiBookingFinancing.class);
        fiBookingFinancingService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }

    @GetMapping("/fiBookingFinancingExport")
    public void fiBookingFinancingExport(FiBookingFinancing fiBookingFinancing, HttpServletResponse res) throws Exception{
        List<FiBookingFinancing> list = fiBookingFinancingService.selectFiBookingFinancingExportList(fiBookingFinancing);
        if(CollUtil.isNotEmpty(list)){
            for(int i = 0;i<list.size();i++){
                list.get(i).setRowId(i+1+"");
                if(list.get(i).getAssetState()!=null && !"".equals(list.get(i).getAssetState())){
                    if("APPLY".equals(list.get(i).getAssetState())){
                        list.get(i).setAssetState("融资申请");
                    }else if("APPLY-USE".equals(list.get(i).getAssetState())){
                        list.get(i).setAssetState("融资申请-舱单占用上链");
                    }else if("APPLY_USE".equals(list.get(i).getAssetState())){
                        list.get(i).setAssetState("融资申请-舱单占用上链");
                    }else if("ACCEPT".equals(list.get(i).getAssetState())){
                        list.get(i).setAssetState("融资受理");
                    }else if("REJECT".equals(list.get(i).getAssetState())){
                        list.get(i).setAssetState("受理拒绝");
                    }else if("PAY".equals(list.get(i).getAssetState())){
                        list.get(i).setAssetState("融资放款");
                    }else if("CANCEL".equals(list.get(i).getAssetState())){
                        list.get(i).setAssetState("放款撤销");
                    }else if("REPAY".equals(list.get(i).getAssetState())){
                        list.get(i).setAssetState("已还款");
                    }else if("UN-REPAY".equals(list.get(i).getAssetState())){
                        list.get(i).setAssetState("未还款");
                    }else if("UN_REPAY".equals(list.get(i).getAssetState())){
                        list.get(i).setAssetState("未还款");
                    }

                    if("SUCCESS".equals(list.get(i).getChainState())){
                        list.get(i).setChainState("上链成功");
                    }else if("PENDING".equals(list.get(i).getChainState())){
                        list.get(i).setChainState("上链中");
                    }else if("FAIL".equals(list.get(i).getChainState())){
                        list.get(i).setChainState("上链失败");
                    }
                }
            }
        }

        com.alibaba.excel.ExcelWriter excelWriter=null;
        String templateFileName = "fiBookingFinancingExport.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)){
            templateFileName = linuxPath + templateFileName;
        }else{
            templateFileName = windowsPath + templateFileName;
        }
        String fileName = new String("运费融资列表导出.xlsx".getBytes("utf-8"), "iso-8859-1");
        //文件格式xlsx
        res.setHeader("Content-Type","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        res.setHeader( "Content-Disposition", "attachment;filename=" + fileName);
        res.addHeader("Pargam", "no-cache");
        res.addHeader("Cache-Control", "no-cache");
        excelWriter = EasyExcel.write(res.getOutputStream()).withTemplate(templateFileName).build();
        WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "Sheet1").build();

        if(CollUtil.isNotEmpty(list)){
            excelWriter.fill(list, writeSheet1);
        }
        excelWriter.finish();
    }
}
