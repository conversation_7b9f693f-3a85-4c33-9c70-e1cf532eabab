package com.huazheng.tunny.ocean.controller;


import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huazheng.tunny.common.core.util.StringUtils;
import com.huazheng.tunny.ocean.api.entity.FiHisFinancingInfo;
import com.huazheng.tunny.ocean.api.vo.FiHisFinancingVo;
import com.huazheng.tunny.ocean.api.vo.FiHisVo;
import com.huazheng.tunny.ocean.service.FiHisFinancingInfoService;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 历史融资信息
 *
 * <AUTHOR> code ocean
 * @date 2022-03-08 09:42:47
 */
@RestController
@RequestMapping("/fihisfinancinginfo")
@Slf4j
public class FiHisFinancingInfoController {
    @Autowired
    private FiHisFinancingInfoService fiHisFinancingInfoService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fiHisFinancingInfoService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fiHisFinancingInfoService.selectFiHisFinancingInfoListByLike(new Query<>(params));
    }



    /**
     *  列表
     * @param params
     * @return
     */
    @GetMapping("/selectFiHisFinancing")
    public R selectFiHisFinancing(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fiHisFinancingInfoService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fiHisFinancingInfoService.selectFiHisFinancing(params);
    }
    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        FiHisFinancingInfo fiHisFinancingInfo =fiHisFinancingInfoService.selectById(rowId);
        return new R<>(fiHisFinancingInfo);
    }

    /**
     * 保存
     * @param fiHisFinancingInfo
     * @return R
     */
    @PostMapping
    public R save(@RequestBody FiHisFinancingInfo fiHisFinancingInfo) {
        fiHisFinancingInfoService.insert(fiHisFinancingInfo);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param fiHisFinancingInfo
     * @return R
     */
    @PutMapping
    public R update(@RequestBody FiHisFinancingInfo fiHisFinancingInfo) {
        fiHisFinancingInfoService.updateById(fiHisFinancingInfo);
        return new R<>(Boolean.TRUE);
    }



    /**
     * 删除
     * @param rowId
     * @return R
     */
    @DeleteMapping("/{rowId}")
    public R delete(@PathVariable  String rowId) {
        fiHisFinancingInfoService.deleteById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        fiHisFinancingInfoService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<FiHisFinancingInfo> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = fiHisFinancingInfoService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }


    /**
     * 导出接口
     * @param map
     * @param response
     * @throws IOException
     */
    @PostMapping("/exportedFileFiHisFinancing")
    public void exportedFile(@RequestBody Map<String,Object> map,HttpServletResponse response) throws IOException {
        XSSFWorkbook workbook=new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("历史融资信息");
        XSSFRow row = sheet.createRow(0);
        //宽度
        for(int i=0;i<=17;i++){
            sheet.setColumnWidth(i,60*80);
        }
        row.setHeight((short) (10*50));
        //字体
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");

        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);//水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setBorderBottom(BorderStyle.THIN); //下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);


        XSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("序号");
        cell0.setCellStyle(style);
        XSSFCell cell1 = row.createCell(1);
        cell1.setCellValue("融资业务编号");
        cell1.setCellStyle(style);
        XSSFCell cell2 = row.createCell(2);
        cell2.setCellValue("融资类别");
        cell2.setCellStyle(style);
        XSSFCell cell3 = row.createCell(3);
        cell3.setCellValue("业务办理银行");
        cell3.setCellStyle(style);
        XSSFCell cell4 = row.createCell(4);
        cell4.setCellValue("放款日");
        cell4.setCellStyle(style);
        XSSFCell cell5 = row.createCell(5);
        cell5.setCellValue("放款币种");
        cell5.setCellStyle(style);
        XSSFCell cell6 = row.createCell(6);
        cell6.setCellValue("放款金额");
        cell6.setCellStyle(style);
        XSSFCell cell7 = row.createCell(7);
        cell7.setCellValue("还款日");
        cell7.setCellStyle(style);
        XSSFCell cell8 = row.createCell(8);
        cell8.setCellValue("还款金额");
        cell8.setCellStyle(style);
        XSSFCell cell9 = row.createCell(9);
        cell9.setCellValue("融资状态");
        cell9.setCellStyle(style);


        R r = this.selectFiHisFinancing(map);

        int i=1;
        ObjectMapper objectMapper=new ObjectMapper();
        FiHisFinancingVo fiHisFinancingVo = objectMapper.convertValue(r.getData(), FiHisFinancingVo.class);
        List<FiHisVo> assetList = fiHisFinancingVo.getAssetList();
        if(assetList.size()>0){
            for (FiHisVo lists:assetList) {
                XSSFRow rows = sheet.createRow(i);
                rows.createCell(0).setCellValue(i);
                if(StringUtils.isNotBlank(lists.getAssetCode())){
                    rows.createCell(1).setCellValue(lists.getAssetCode());
                }
                if(StringUtils.isNotBlank(lists.getAssetTypeName())){
                    rows.createCell(2).setCellValue(lists.getAssetTypeName());
                }
                if(StringUtils.isNotBlank(lists.getBusinessBankName())){
                    rows.createCell(3).setCellValue(lists.getBusinessBankName());
                }
                if(StringUtils.isNotBlank(lists.getPayTime())){
                    rows.createCell(4).setCellValue(lists.getPayTime());
                }
                if(StringUtils.isNotBlank(lists.getPayCurrency())){
                    rows.createCell(5).setCellValue(lists.getPayCurrency());
                }
                if(StringUtils.isNotBlank(lists.getPayAmount())){
                    rows.createCell(6).setCellValue(lists.getPayAmount());
                }
                if(StringUtils.isNotBlank(lists.getRepayTime())){
                    rows.createCell(7).setCellValue(lists.getRepayTime());
                }
                if(StringUtils.isNotBlank(lists.getRepayAmount())){
                    rows.createCell(8).setCellValue(lists.getRepayAmount());
                }
                if(StringUtils.isNotBlank(lists.getAssetStateName())){
                    rows.createCell(9).setCellValue(lists.getAssetStateName());
                }
                i++;
            }
        }

        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader( "Content-Disposition", "attachment;filename=" + new String( "历史融资信息.xls".getBytes("GB2312"), "8859_1" ));
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }
    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<FiHisFinancingInfo> list = reader.readAll(FiHisFinancingInfo.class);
        fiHisFinancingInfoService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }

}
