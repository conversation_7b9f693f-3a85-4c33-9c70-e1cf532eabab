package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.api.entity.FiEnterpriseAuthShow;
import com.huazheng.tunny.ocean.mapper.FiBankAuthShowMapper;
import com.huazheng.tunny.ocean.api.entity.FiBankAuthShow;
import com.huazheng.tunny.ocean.service.FiBankAuthShowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fiBankAuthShowService")
public class FiBankAuthShowServiceImpl extends ServiceImpl<FiBankAuthShowMapper, FiBankAuthShow> implements FiBankAuthShowService {

    @Autowired
    private FiBankAuthShowMapper fiBankAuthShowMapper;

    public FiBankAuthShowMapper getFiBankAuthShowMapper() {
        return fiBankAuthShowMapper;
    }

    public void setFiBankAuthShowMapper(FiBankAuthShowMapper fiBankAuthShowMapper) {
        this.fiBankAuthShowMapper = fiBankAuthShowMapper;
    }

    /**
     * 查询经营信息授权-银行展示表信息
     *
     * @param rowId 经营信息授权-银行展示表ID
     * @return 经营信息授权-银行展示表信息
     */
    @Override
    public FiBankAuthShow selectFiBankAuthShowById(String rowId)
    {
        return fiBankAuthShowMapper.selectFiBankAuthShowById(rowId);
    }

    /**
     * 查询经营信息授权-银行展示表列表
     *
     * @param fiBankAuthShow 经营信息授权-银行展示表信息
     * @return 经营信息授权-银行展示表集合
     */
    @Override
    public List<FiBankAuthShow> selectFiBankAuthShowList(FiBankAuthShow fiBankAuthShow)
    {
        return fiBankAuthShowMapper.selectFiBankAuthShowList(fiBankAuthShow);
    }

    @Override
    public List<FiBankAuthShow> selectFiBankAuthShowByEntSocialCode(FiBankAuthShow fiBankAuthShow) {
        return fiBankAuthShowMapper.selectFiBankAuthShowByEntSocialCode(fiBankAuthShow);
    }


    /**
     * 分页模糊查询经营信息授权-银行展示表列表
     * @return 经营信息授权-银行展示表集合
     */
    @Override
    public Page selectFiBankAuthShowListByLike(Query query)
    {
        FiBankAuthShow fiBankAuthShow =  BeanUtil.mapToBean(query.getCondition(), FiBankAuthShow.class,false);
        query.setRecords(fiBankAuthShowMapper.selectFiBankAuthShowListByLike(query,fiBankAuthShow));
        return query;
    }

    /**
     * 分页模糊查询经营信息授权-银行展示表列表-市平台
     * @return 经营信息授权-银行展示表集合-市平台
     */
    @Override
    public Page selectFiBankAuthShowListForCity(Query query)
    {
        FiBankAuthShow fiBankAuthShow =  BeanUtil.mapToBean(query.getCondition(), FiBankAuthShow.class,false);
        query.setRecords(fiBankAuthShowMapper.selectFiBankAuthShowListForCity(query,fiBankAuthShow));
        return query;
    }

    /**
     * 分页模糊查询经营信息授权-银行展示表列表-省平台
     * @return 经营信息授权-银行展示表集合-省平台
     */
    @Override
    public Page selectFiBankAuthShowListForProvince(Query query)
    {
        FiBankAuthShow fiBankAuthShow =  BeanUtil.mapToBean(query.getCondition(), FiBankAuthShow.class,false);
        query.setRecords(fiBankAuthShowMapper.selectFiBankAuthShowListForProvince(query,fiBankAuthShow));
        return query;
    }

    /**
     * 新增经营信息授权-银行展示表
     *
     * @param fiBankAuthShow 经营信息授权-银行展示表信息
     * @return 结果
     */
    @Override
    public int insertFiBankAuthShow(FiBankAuthShow fiBankAuthShow)
    {
        return fiBankAuthShowMapper.insertFiBankAuthShow(fiBankAuthShow);
    }

    /**
     * 修改经营信息授权-银行展示表
     *
     * @param fiBankAuthShow 经营信息授权-银行展示表信息
     * @return 结果
     */
    @Override
    public int updateFiBankAuthShow(FiBankAuthShow fiBankAuthShow)
    {
        return fiBankAuthShowMapper.updateFiBankAuthShow(fiBankAuthShow);
    }


    /**
     * 删除经营信息授权-银行展示表
     *
     * @param rowId 经营信息授权-银行展示表ID
     * @return 结果
     */
    public int deleteFiBankAuthShowById(String rowId)
    {
        return fiBankAuthShowMapper.deleteFiBankAuthShowById( rowId);
    };


    /**
     * 批量删除经营信息授权-银行展示表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiBankAuthShowByIds(Integer[] rowIds)
    {
        return fiBankAuthShowMapper.deleteFiBankAuthShowByIds( rowIds);
    }

    @Override
    public void cancelAuth(FiBankAuthShow fiBankAuthShow){
        fiBankAuthShowMapper.cancelAuth(fiBankAuthShow);
    }


}
