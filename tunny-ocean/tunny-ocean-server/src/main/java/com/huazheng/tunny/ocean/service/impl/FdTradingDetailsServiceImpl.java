package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.FdAggregateBalanceDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.vo.FdBillVO;
import com.huazheng.tunny.ocean.api.vo.Remittance;
import com.huazheng.tunny.ocean.controller.FdRemittanceRecordController;
import com.huazheng.tunny.ocean.mapper.FdBalanceDetailMapper;
import com.huazheng.tunny.ocean.mapper.FdRemittanceRecordMapper;
import com.huazheng.tunny.ocean.mapper.FdTradingDetailsMapper;
import com.huazheng.tunny.ocean.service.CustomerInfoService;
import com.huazheng.tunny.ocean.service.FdBillService;
import com.huazheng.tunny.ocean.service.FdTradingDetailsService;
import com.huazheng.tunny.ocean.service.SysNoConfigService;
import com.huazheng.tunny.ocean.util.PermissionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service("fdTradingDetailsService")
public class FdTradingDetailsServiceImpl extends ServiceImpl<FdTradingDetailsMapper, FdTradingDetails> implements FdTradingDetailsService {

    @Autowired
    private FdTradingDetailsMapper fdTradingDetailsMapper;
    @Autowired
    private CustomerInfoService customerInfoService;
    @Autowired
    private SysNoConfigService noConfigService;
    @Autowired
    private FdBillService fdBillService;
    @Autowired
    private FdRemittanceRecordController fdRemittanceRecordController;
    @Autowired
    private FdRemittanceRecordMapper fdRemittanceRecordMapper;
    @Autowired
    private PermissionUtil permissionUtil;
    @Autowired
    private FdTradingDetailsService fdTradingDetailsService;
    @Autowired
    private FdBalanceDetailMapper fdBalanceDetailMapper;


    /**
     *  查询客户余额流水列表
     * @param fdTradingDetails
     * @return
     */
    @Override
    public List<FdTradingDetails> getList(FdTradingDetails fdTradingDetails){
        return fdTradingDetailsMapper.selectFdTradingDetailsList(fdTradingDetails);
    }

    @Override
    public List<FdTradingDetails> getRemittanceList(FdTradingDetails fdTradingDetails){
        return fdTradingDetailsMapper.getRemittanceList(fdTradingDetails);
    }
    /**
     * 查询交易明细表信息
     *
     * @param id 交易明细表ID
     * @return 交易明细表信息
     */
    @Override
    public FdTradingDetails selectFdTradingDetailsById(Integer id)
    {
        return fdTradingDetailsMapper.selectFdTradingDetailsById(id);
    }

    /**
     * 查询交易明细表列表
     *
     * @param fdTradingDetails 交易明细表信息
     * @return 交易明细表集合
     */
    @Override
    public List<FdTradingDetails> selectFdTradingDetailsList(FdTradingDetails fdTradingDetails)
    {
        if(StrUtil.isNotEmpty(fdTradingDetails.getPaymentType())&&"2".equals(fdTradingDetails.getPaymentType())){
            return fdTradingDetailsMapper.selectFdTradingDetailsList3(fdTradingDetails);
        }else{
            return fdTradingDetailsMapper.selectFdTradingDetailsList(fdTradingDetails);
        }

    }

    @Override
    public List<FdTradingDetails> selectFdTradingDetailsList2(FdTradingDetails fdTradingDetails)
    {
        return fdTradingDetailsMapper.selectFdTradingDetailsList2(fdTradingDetails);
    }


    /**
     * 分页模糊查询交易明细表列表
     * @return 交易明细表集合
     */
    @Override
    public Page selectFdTradingDetailsListByLike(Query query)
    {
        FdTradingDetails fdTradingDetails =  BeanUtil.mapToBean(query.getCondition(), FdTradingDetails.class,false);
//        if (StrUtil.isNotEmpty(fdTradingDetails.getPlatformCode())) {
//            fdTradingDetails.setPlatformCode(permissionUtil.getPCPermisson(fdTradingDetails.getPlatformCode(), fdTradingDetails.getPlatformLevel()));
//            fdTradingDetails.setPlatformLevel(null);
//            fdTradingDetails.setPlatformName(null);
//        }
        query.setRecords(fdTradingDetailsMapper.selectFdTradingDetailsListByLike(query,fdTradingDetails));
        return query;
    }

    @Override
    public R insertFdTradingDetailsShi(FdTradingDetails fdTradingDetails) throws Exception {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String tsIn = noConfigService.genNo("TS");
        if (tsIn.contains("请联系系统管理员")){
            throw new Exception(tsIn);
        }
        fdTradingDetails.setTradeSerialNumber(tsIn);
        fdTradingDetails.setUuid(UUID.randomUUID().toString());
        fdTradingDetails.setPlatformLevel("0");
        fdTradingDetails.setIncomeFlag("1");
        fdTradingDetails.setIsAdd(fdTradingDetails.getIsAdd());
        if(StrUtil.isNotEmpty(fdTradingDetails.getPaymentType())&&"1".equals(fdTradingDetails.getPaymentType())&&StrUtil.isNotEmpty(fdTradingDetails.getIsAdd())&&"1".equals(fdTradingDetails.getIsAdd())){
            fdTradingDetails.setTradingStatus("2");
        }else{
            fdTradingDetails.setTradingStatus("1");
        }
        fdTradingDetails.setTradingHours(LocalDateTime.now());
        fdTradingDetails.setCreateUsercode(userInfo.getUserName());
        fdTradingDetails.setCreateUserrealname(userInfo.getRealName());
        fdTradingDetails.setTransactionAmount(fdTradingDetails.getTransactionAmount());
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if(fdTradingDetails.getShipmentTimeStr()!=null && !"".equals(fdTradingDetails.getShipmentTimeStr())){
            fdTradingDetails.setShipmentTime(LocalDateTime.parse(fdTradingDetails.getShipmentTimeStr() + " 00:00:00", df));
        }
        fdTradingDetailsMapper.insertFdTradingDetails(fdTradingDetails);
        return new R<>(Boolean.TRUE,"新增成功");
    }

    /**
     * 查询客户余额一级List
     * @param query
     * @return
     */
    @Override
    public Page selectAggregateBalanceList(Query query){
        FdAggregateBalanceDTO fdAggregateBalanceDTO =  BeanUtil.mapToBean(query.getCondition(), FdAggregateBalanceDTO.class,false);
        if (StrUtil.isNotEmpty(fdAggregateBalanceDTO.getPlatformCode())) {
            fdAggregateBalanceDTO.setKhbm(fdAggregateBalanceDTO.getPlatformCode());
            fdAggregateBalanceDTO.setPlatformCode(permissionUtil.getPcPermissonCustomer(fdAggregateBalanceDTO.getPlatformCode(), fdAggregateBalanceDTO.getPlatformLevel()));
            fdAggregateBalanceDTO.setPlatformName(null);
            fdAggregateBalanceDTO.setPlatformLevel(null);
        }
        query.setRecords(fdTradingDetailsMapper.selectAggregateBalanceList(query,fdAggregateBalanceDTO));
        return query;
    }

    @Override
    public Page selectAggregateBalanceList2(Query query){
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        FdAggregateBalanceDTO fdAggregateBalanceDTO =  BeanUtil.mapToBean(query.getCondition(), FdAggregateBalanceDTO.class,false);
        /*if (StrUtil.isNotEmpty(fdAggregateBalanceDTO.getPlatformCode())) {
            fdAggregateBalanceDTO.setCustomerCode(permissionUtil.getPcPermissonCustomer(fdAggregateBalanceDTO.getPlatformCode(), fdAggregateBalanceDTO.getPlatformLevel()));
            fdAggregateBalanceDTO.setPlatformCode(fdAggregateBalanceDTO.getPlatformCode());
            fdAggregateBalanceDTO.setPlatformName(null);
            fdAggregateBalanceDTO.setPlatformLevel(fdAggregateBalanceDTO.getPlatformLevel());
        }*/
        query.setRecords(fdTradingDetailsMapper.selectAggregateBalanceList2(query,fdAggregateBalanceDTO));
        return query;
    }

    @Override
    public Page selectAggregateBalanceList3(Query query){
        FdAggregateBalanceDTO fdAggregateBalanceDTO =  BeanUtil.mapToBean(query.getCondition(), FdAggregateBalanceDTO.class,false);
        query.setRecords(fdTradingDetailsMapper.selectAggregateBalanceList3(query,fdAggregateBalanceDTO));
        return query;
    }

    @Override
    public Page selectProvinceInRailway(Query query){
        FdAggregateBalanceDTO fdAggregateBalanceDTO =  BeanUtil.mapToBean(query.getCondition(), FdAggregateBalanceDTO.class,false);
        if (StrUtil.isNotEmpty(fdAggregateBalanceDTO.getPlatformCode())) {
            fdAggregateBalanceDTO.setCustomerCode(permissionUtil.getPcPermissonCustomer(fdAggregateBalanceDTO.getPlatformCode(), fdAggregateBalanceDTO.getPlatformLevel()));
            fdAggregateBalanceDTO.setPlatformCode(fdAggregateBalanceDTO.getPlatformCode());
            fdAggregateBalanceDTO.setPlatformName(null);
            fdAggregateBalanceDTO.setPlatformLevel(fdAggregateBalanceDTO.getPlatformLevel());
        }
        query.setRecords(fdTradingDetailsMapper.selectProvinceInRailway(query,fdAggregateBalanceDTO));
        return query;
    }

    @Override
    public Page selectCityInProvince(Query query){
        FdAggregateBalanceDTO fdAggregateBalanceDTO =  BeanUtil.mapToBean(query.getCondition(), FdAggregateBalanceDTO.class,false);
        if (StrUtil.isNotEmpty(fdAggregateBalanceDTO.getPlatformCode())) {
            fdAggregateBalanceDTO.setCustomerCode(permissionUtil.getPermissonCustomer(fdAggregateBalanceDTO.getPlatformCode(), "2"));
            fdAggregateBalanceDTO.setPlatformCode(fdAggregateBalanceDTO.getPlatformCode());
            fdAggregateBalanceDTO.setPlatformName(null);
            fdAggregateBalanceDTO.setPlatformLevel(fdAggregateBalanceDTO.getPlatformLevel());
        }
        query.setRecords(fdTradingDetailsMapper.selectCityInProvince(query,fdAggregateBalanceDTO));
        return query;
    }

    @Override
    public List<FdAggregateBalanceDTO> selectAggregateBalanceListSub(FdAggregateBalanceDTO fdAggregateBalanceDTO){
        return fdTradingDetailsMapper.selectAggregateBalanceListSub(fdAggregateBalanceDTO);
    }

    /**
     * 保存
     * @param fdTradingDetails
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R insertTrandingDetails(FdTradingDetails fdTradingDetails) throws Exception {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        fdTradingDetails.setCreateUsercode(userInfo.getUserName());
        fdTradingDetails.setCreateUserrealname(userInfo.getRealName());
        EntityWrapper<CustomerInfo> wrapper = new EntityWrapper<>();
        wrapper.eq("customer_code", fdTradingDetails.getPlatformCode());
        CustomerInfo customerInfo = customerInfoService.selectOne(wrapper);
        if (customerInfo == null){
            return new R<>(500, false, null, "暂无该用户");
        }
        fdTradingDetails.setPlatformCode(customerInfo.getCustomerCode());
        fdTradingDetails.setPlatformName(customerInfo.getCompanyName());
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if(fdTradingDetails.getShipmentTimeStr()!=null && !"".equals(fdTradingDetails.getShipmentTimeStr())){
            fdTradingDetails.setShipmentTime(LocalDateTime.parse(fdTradingDetails.getShipmentTimeStr() + " 00:00:00", df));
        }
        /*if ("1".equals(customerInfo.getCustomerFlag())){
            fdTradingDetails.setPlatformLevel("0");
        }else if ("2".equals(customerInfo.getCustomerFlag())){
            fdTradingDetails.setPlatformLevel("1");
        }*/
//        fdTradingDetails.setTradingHours(DateUtil.now());
        fdTradingDetails.setTradingStatus("2");
        //编辑第一条数据
        String tsIn = noConfigService.genNo("TS");
        if (tsIn.contains("请联系系统管理员")){
            throw new Exception(tsIn);
        }
        fdTradingDetails.setTradeSerialNumber(tsIn);
        int amountFlag =0;
        //2021加空指针校验
        /*if(fdTradingDetails.getTransactionAmount()!=null){
            amountFlag = fdTradingDetails.getTransactionAmount().compareTo(BigDecimal.valueOf(0));
            amountFlag = 0-amountFlag;
        }*/
        //2021加空指针校验 end
        /*if (amountFlag < 0){
            fdTradingDetails.setIsAdd("0");
            fdTradingDetails.setIncomeFlag("1");
        }else{
            fdTradingDetails.setIsAdd("1");
            fdTradingDetails.setIncomeFlag("0");
        }*/
        if(fdTradingDetails.getTransactionAmount()!=null){
            if(fdTradingDetails.getTransactionAmount().compareTo(BigDecimal.valueOf(0))==1){
                fdTradingDetails.setIsAdd("1");
                fdTradingDetails.setIncomeFlag("0");
            }else{
                fdTradingDetails.setIsAdd("0");
                fdTradingDetails.setIncomeFlag("1");
            }
        }
//        fdTradingDetails.setTransactionAmount(fdTradingDetails.getTransactionAmount().negate());
        insert(fdTradingDetails);

        //编辑第二条数据
        /*String tsOut = noConfigService.genNo("TS");
        if (tsIn.contains("请联系系统管理员")){
            throw new Exception(tsOut);
        }
        fdTradingDetails.setTradeSerialNumber(tsOut);
        fdTradingDetails.setCustomerCode(customerInfo.getCustomerCode());
        fdTradingDetails.setCustomerName(customerInfo.getCompanyName());
        *//*if (amountFlag < 0){
            fdTradingDetails.setIsAdd("1");
            fdTradingDetails.setIncomeFlag("0");
        }else{
            fdTradingDetails.setIsAdd("0");
            fdTradingDetails.setIncomeFlag("1");
        }*//*
        //2021加空指针校验
        if(fdTradingDetails.getTransactionAmount()!=null){
            fdTradingDetails.setTransactionAmount(fdTradingDetails.getTransactionAmount().negate());
            if((fdTradingDetails.getTransactionAmount()).compareTo(BigDecimal.valueOf(0))==1){
                fdTradingDetails.setIsAdd("1");
                fdTradingDetails.setIncomeFlag("0");
            }else{
                fdTradingDetails.setIsAdd("0");
                fdTradingDetails.setIncomeFlag("1");
            }
        }
        //2021加空指针校验 end
        insert(fdTradingDetails);*/

        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param fdTradingDetails
     * @return R
     */
    @Override
    public R update(FdTradingDetails fdTradingDetails){
        R r = new R();
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        fdTradingDetails.setUpdateUsercode(userInfo.getUserName());
        fdTradingDetails.setUpdateUserrealname(userInfo.getRealName());

        if (fdTradingDetails.getId() != null) {
            updateById(fdTradingDetails);
        }
        if(StrUtil.isNotEmpty(fdTradingDetails.getDeductionBillCode())){
            EntityWrapper<FdBill> billWrapper = new EntityWrapper<>();
            billWrapper.eq("bill_code", fdTradingDetails.getDeductionBillCode());
            FdBill fdBill = fdBillService.selectOne(billWrapper);
            if (fdBill != null && !"0".equals(fdBill.getBillingState())){
                r.setMsg("该账单已确定，请退回后再进行作废");
                r.setStatusCode(500);
                r.setB(false);
                return r;
            }
            //若作废汇款，增加相关汇款记录的汇款
            if ("0".equals(fdTradingDetails.getTradingStatus())){
                EntityWrapper<FdTradingDetails> wrapper = new EntityWrapper<>();
                wrapper.eq("bill_code", fdTradingDetails.getDeductionBillCode());

                wrapper.eq("payment_type", "2");
                List<FdTradingDetails> list = selectList(wrapper);
                for (FdTradingDetails dto : list){
                    FdRemittanceRecord fdRemittanceRecord = new FdRemittanceRecord();
                    fdRemittanceRecord.setRemittanceRecordCode(dto.getRemittanceRecordCode());
                    fdRemittanceRecord.setResidualAmount(dto.getTransactionAmount());
                    fdRemittanceRecordController.update(fdRemittanceRecord);
                }
            }

//            EntityWrapper<FdTradingDetails> wrapper = new EntityWrapper<>();
//            wrapper.eq("bill_code", fdTradingDetails.getBillCode());
//            update(fdTradingDetails, wrapper);
            fdTradingDetailsMapper.updateFdTradingDetailsByBillCode(fdTradingDetails);
        }
        return new R<>(Boolean.TRUE);
    }

    @Override
    public R updateNew(FdTradingDetails fdTradingDetails){
        String platformLevel = fdTradingDetails.getPlatformLevel();
        fdTradingDetails.setPlatformLevel(null);
        FdTradingDetails details = fdTradingDetailsMapper.selectFdTradingDetailsById(fdTradingDetails.getId());
        if(details!=null){
            if(platformLevel!=null && !"".equals(platformLevel)){
                if("1".equals(platformLevel)){
                    if(details.getDeductionBillCode() == null || "".equals(details.getDeductionBillCode())){
                        FdTradingDetails ftd = new FdTradingDetails();
                        ftd.setId(fdTradingDetails.getId());
                        ftd.setPlatformCode(details.getPlatformCode());
                        ftd.setCustomerCode(details.getCustomerCode());
                        ftd.setDelFlag("N");
                        BigDecimal bigDecimal = fdTradingDetailsMapper.selectTotalTransactionAmount(ftd);
                        if(bigDecimal ==null || bigDecimal.compareTo(BigDecimal.valueOf(0))>=0){
                            SecruityUser userInfo = SecurityUtils.getUserInfo();
//                            fdTradingDetails.setDelFlag("Y");
                            fdTradingDetails.setUpdateUsercode(userInfo.getUserName());
                            fdTradingDetails.setUpdateUserrealname(userInfo.getRealName());
                            if (fdTradingDetails.getId() != null) {
                                updateById(fdTradingDetails);
                            }
                        }else{
                            return new R<>(Boolean.FALSE,"余额已抵扣，不能作废！");
                        }
                    }
                }else if("2".equals(platformLevel)){
                    if(details.getDeductionBillCode() == null || "".equals(details.getDeductionBillCode())){
                        FdBalanceDetail fdBalanceDetail = new FdBalanceDetail();
                        fdBalanceDetail.setId(details.getBalanceId());
                        fdBalanceDetail.setDeleteFlag("N");
                        List<FdBalanceDetail> fdBalanceDetails = fdBalanceDetailMapper.selectFdBalanceDetailListByShiftId(fdBalanceDetail);
                        if(fdBalanceDetails!=null && fdBalanceDetails.size()>0){
                            return new R<>(Boolean.FALSE,"余额已抵扣，不能作废！");
                        }else{
                            //余额抵扣
                            SecruityUser userInfo = SecurityUtils.getUserInfo();
//                        fdTradingDetails.setDelFlag("Y");
                            fdTradingDetails.setUpdateUsercode(userInfo.getUserName());
                            fdTradingDetails.setUpdateUserrealname(userInfo.getRealName());
                            if (fdTradingDetails.getId() != null) {
                                updateById(fdTradingDetails);
                            }
                            if(details.getBalanceId()!=null){
                                FdBalanceDetail fbd = new FdBalanceDetail();
                                fbd.setId(details.getBalanceId());
                                fbd.setDeleteFlag("Y");
                                fdBalanceDetailMapper.updateFdBalanceDetailById(fbd);
                            }
                        }


                    }else if(details.getDeductionBillCode() != null && !"".equals(details.getDeductionBillCode())){
                        //账单抵扣
                        if(details.getTradingStatus()!=null && !"".equals(details.getTradingStatus())){
                            if("2".equals(details.getTradingStatus())){
                                //预结算
                                FdBill fb = new FdBill();
                                fb.setBillCode(details.getDeductionBillCode());
                                fb.setDelFlag("N");
                                List<FdBill> fdBills = fdBillService.selectFdBillList(fb);
                                if(fdBills!=null && fdBills.size()>0){
                                    SecruityUser userInfo = SecurityUtils.getUserInfo();
//                                    fdTradingDetails.setDelFlag("Y");
                                    fdTradingDetails.setUpdateUsercode(userInfo.getUserName());
                                    fdTradingDetails.setUpdateUserrealname(userInfo.getRealName());
                                    if (fdTradingDetails.getId() != null) {
                                        updateById(fdTradingDetails);
                                    }
                                    FdBalanceDetail detail = fdBalanceDetailMapper.selectById(details.getBalanceId());
                                    if(detail!=null){
                                        FdBalanceDetail fbd = new FdBalanceDetail();
                                        fbd.setId(detail.getId());
                                        fbd.setRemainingAmount(detail.getRemainingAmount().subtract(details.getTransactionAmount()));
                                        fdBalanceDetailMapper.updateRemainingAmountById(fbd);
                                    }
                                    FdBill bill = new FdBill();
                                    bill.setUuid(fdBills.get(0).getUuid());
                                    bill.setOffsetBalance(fdBills.get(0).getOffsetBalance().subtract(details.getTransactionAmount().negate()));
                                    bill.setAmountPayable(fdBills.get(0).getAmountPayable().add(details.getTransactionAmount()));
                                    bill.setBillingState("0");
                                    fdBillService.updateFdBill(bill);

//                                fdTradingDetails.setDelFlag("Y");
                                    fdTradingDetails.setUpdateUsercode(userInfo.getUserName());
                                    fdTradingDetails.setUpdateUserrealname(userInfo.getRealName());
                                    if (fdTradingDetails.getId() != null) {
                                        updateById(fdTradingDetails);
                                    }
                                    fdTradingDetails.setTradingStatus("0");
                                    fdTradingDetails.setDeductionBillCode(details.getDeductionBillCode());
                                    fdTradingDetailsMapper.updateFdTradingDetails2(fdTradingDetails);
                                }


                            }else if("1".equals(details.getTradingStatus())){
                                //已结算
                                SecruityUser userInfo = SecurityUtils.getUserInfo();
//                                fdTradingDetails.setDelFlag("Y");
                                fdTradingDetails.setUpdateUsercode(userInfo.getUserName());
                                fdTradingDetails.setUpdateUserrealname(userInfo.getRealName());
                                if (fdTradingDetails.getId() != null) {
                                    updateById(fdTradingDetails);
                                }
                                fdTradingDetails.setTradingStatus("2");
                                fdTradingDetails.setDeductionBillCode(details.getDeductionBillCode());
                                fdTradingDetailsMapper.updateFdTradingDetails2(fdTradingDetails);

                                FdRemittanceRecord frr = new FdRemittanceRecord();
                                frr.setRemittanceRecordCode(details.getRemittanceRecordCode());
                                List<FdRemittanceRecord> fdRemittanceRecords = fdRemittanceRecordMapper.selectFdRemittanceRecordList(frr);
                                if(fdRemittanceRecords!=null && fdRemittanceRecords.size()>0){
                                    FdRemittanceRecord r = new FdRemittanceRecord();
                                    r.setId(fdRemittanceRecords.get(0).getId());
                                    r.setResidualAmount(fdRemittanceRecords.get(0).getResidualAmount().subtract(details.getTransactionAmount()));
                                    fdRemittanceRecordMapper.updateFdRemittanceRecord(r);
                                }
                                FdBill fb = new FdBill();
                                fb.setBillCode(details.getDeductionBillCode());
                                fb.setDelFlag("N");
                                List<FdBill> fdBills = fdBillService.selectFdBillList(fb);
                                if(fdBills!=null && fdBills.size()>0){
                                    FdBill bill = new FdBill();
                                    bill.setUuid(fdBills.get(0).getUuid());
                                    bill.setOffsetBalance(fdBills.get(0).getOffsetBalance().subtract(details.getTransactionAmount().negate()));
                                    bill.setAmountPayable(fdBills.get(0).getAmountPayable().subtract(details.getTransactionAmount()));
                                    bill.setBillingState("1");
                                    fdBillService.updateFdBill(bill);
                                }

                                if(details.getBalanceId()!=null){
                                    FdBalanceDetail fbd = new FdBalanceDetail();
                                    fbd.setId(details.getBalanceId());
                                    fbd.setDeleteFlag("Y");
                                    fdBalanceDetailMapper.updateFdBalanceDetailById(fbd);
                                }
                            }
                        }
                    }
                }
            }
        }
        return new R<>(Boolean.TRUE);
    }

    @Override
    public R updateFdTradingDetailsById(FdTradingDetails fdTradingDetails){
        R r = new R();
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        fdTradingDetails.setUpdateUsercode(userInfo.getUserName());
        fdTradingDetails.setUpdateUserrealname(userInfo.getRealName());
        fdTradingDetailsMapper.updateFdTradingDetailsById(fdTradingDetails);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 根据客户编码查询余额
     * @param fdTradingDetails
     * @return
     */
    @Override
    public R selectBalanceByCode(FdTradingDetails fdTradingDetails){
        List<FdTradingDetails> list = fdTradingDetailsMapper.selectBalanceByCode(fdTradingDetails);
        return new R<>(list);
    }

    /**
     * 新增交易明细表
     *
     * @param fdTradingDetails 交易明细表信息
     * @return 结果
     */
    @Override
    public int insertFdTradingDetails(FdTradingDetails fdTradingDetails)
    {
        return fdTradingDetailsMapper.insertFdTradingDetails(fdTradingDetails);
    }

    /**
     * 修改交易明细表
     *
     * @param fdTradingDetails 交易明细表信息
     * @return 结果
     */
    @Override
    public int updateFdTradingDetails(FdTradingDetails fdTradingDetails)
    {
        return fdTradingDetailsMapper.updateFdTradingDetails(fdTradingDetails);
    }

    @Override
    public int deleteFdTradingDetailsByBillCode(FdTradingDetails fdTradingDetails)
    {
        return fdTradingDetailsMapper.deleteFdTradingDetailsByBillCode(fdTradingDetails);
    }


    /**
     * 删除交易明细表
     *
     * @param id 交易明细表ID
     * @return 结果
     */
    @Override
    public int deleteFdTradingDetailsById(Integer id)
    {
        return fdTradingDetailsMapper.deleteFdTradingDetailsById( id);
    }


    /**
     * 批量删除交易明细表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFdTradingDetailsByIds(Integer[] ids)
    {
        return fdTradingDetailsMapper.deleteFdTradingDetailsByIds( ids);
    }

    @Override
    public void invalidbybillcode(String billCode) {

      fdTradingDetailsMapper.invalidbybillcode( billCode);
    }

    @Override
    public Boolean insertFdTradingDetailsbyRemittance(FdBillVO fdBillVO) {
        if(fdBillVO.getAmountPayable().compareTo(BigDecimal.valueOf(0)) < 0){
            BigDecimal negate = fdBillVO.getAmountPayable().negate();
            if(negate !=null){
                String billCode = fdBillVO.getBillCode();
                List<Map<String,Object>> list = fdBillService.getInfo(billCode);
                String remarks=null;
                if(CollUtil.isNotEmpty(list)) {
                     remarks = "" + list.get(0).get("customer_name") + list.get(0).get("plan_ship_time") + list.get(0).get("port_station") + "，省级班列号："
                            + list.get(0).get("province_trains_number") + ",与" + fdBillVO.getShUnitName() + "确认境外汇率为" + list.get(0).get("exchange_rate") + "，产生境外费用余额"
                            + negate + "元，本次抵扣。";
                }
                FdBalanceDetail fdBalanceDetail = new FdBalanceDetail();
                fdBalanceDetail.setPlatformCode(fdBillVO.getPlatformCode());
                fdBalanceDetail.setCustomerCode(fdBillVO.getStandbyD());
                fdBalanceDetail.setCustomerName(fdBillVO.getStandbyC());
                fdBalanceDetail.setShiftId(fdBillVO.getProvinceTrainsNumber());
                fdBalanceDetail.setShiftNo(fdBillVO.getProvinceTrainsNumber());
                fdBalanceDetail.setShiftName(fdBillVO.getTrainsName());
//            fdBalanceDetail.setCodeBCategoriesCode();
//            fdBalanceDetail.setCodeBCategoriesName();
//            fdBalanceDetail.setCodeSCategoriesCode();
//            fdBalanceDetail.setCodeSCategoriesName();
                fdBalanceDetail.setShUnitCode(fdBillVO.getShUnitCode());
                fdBalanceDetail.setShUnitName(fdBillVO.getShUnitName());
                //判断是量价捆绑还是业务余额
                if(!"1".equals(fdBillVO.getStandbyE())){
                    fdBalanceDetail.setPaymentType("0");
                }else{
                  fdBalanceDetail.setPaymentType("1");
                }
                fdBalanceDetail.setTotalAmount(negate);
                fdBalanceDetail.setRemainingAmount(negate);
                fdBalanceDetail.setRemarks(remarks);
                //市平台不添加余额明细
                if(!"0".equals(fdBillVO.getPlatformLevel())) {
                    int i = fdBalanceDetailMapper.insertFdBalanceDetail(fdBalanceDetail);
                }

                FdTradingDetails fdTradingDetails = new FdTradingDetails();
                fdTradingDetails.setBalanceId(fdBalanceDetail.getId());
                String shipmentTime = fdBillVO.getShipmentTime();
                fdBillVO.setShipmentTime(null);
                BeanUtil.copyProperties(fdBillVO, fdTradingDetails);
                if(shipmentTime!=null &&!"".equals(shipmentTime)){
                    DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    LocalDateTime localDateTime = LocalDateTime.parse(shipmentTime, dtf);
                    fdTradingDetails.setShipmentTime(localDateTime);
                    if(shipmentTime.contains(" 00:00:00")){
                        String str = shipmentTime.replace(" 00:00:00", "");
                        fdTradingDetails.setShipmentTimeStr(str);
                    }
                }
                fdTradingDetails.setCustomerName(fdBillVO.getStandbyC());
                fdTradingDetails.setCustomerCode(fdBillVO.getStandbyD());
                //判断是量价捆绑还是业务余额
                if(!"1".equals(fdBillVO.getStandbyE())){
                    fdTradingDetails.setPaymentType("0");
                }else{
                    fdTradingDetails.setPaymentType("1");
                }
                fdTradingDetails.setIsAdd("0");
                fdTradingDetails.setTradingStatus("1");
                fdTradingDetails.setShiftId(fdBillVO.getProvinceTrainsNumber());
                fdTradingDetails.setShiftName(fdBillVO.getTrainsName());
                fdTradingDetails.setTransactionAmount(negate);
                fdTradingDetails.setDeductionBillCode(fdBillVO.getBillCode());
                try {
                    fdTradingDetailsService.insertTrandingDetails(fdTradingDetails);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }else{
            List<Remittance> remittances = fdBillVO.getRemittances();
            if (remittances != null) {

                for (Remittance remittance : remittances) {
                    if(remittance.getRemittanceUsed()!=null){
                        FdTradingDetails fdTradingDetails = new FdTradingDetails();
                        String shipmentTime = fdBillVO.getShipmentTime();
                        fdBillVO.setShipmentTime(null);
                        BeanUtil.copyProperties(fdBillVO, fdTradingDetails);
                        if(shipmentTime!=null &&!"".equals(shipmentTime)){
                            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            LocalDateTime localDateTime = LocalDateTime.parse(shipmentTime, dtf);
                            fdTradingDetails.setShipmentTime(localDateTime);
                            if(shipmentTime.contains(" 00:00:00")){
                                String str = shipmentTime.replace(" 00:00:00", "");
                                fdTradingDetails.setShipmentTimeStr(str);
                            }
                        }

                        fdTradingDetails.setCustomerName(fdBillVO.getStandbyC());
                        fdTradingDetails.setCustomerCode(fdBillVO.getStandbyD());
                        fdTradingDetails.setDeductionBillCode(fdBillVO.getBillCode());
                        fdTradingDetails.setPaymentType("2");
                        fdTradingDetails.setRemittanceRecordCode(remittance.getRemittancecode());
                        fdTradingDetails.setTransactionAmount(remittance.getRemittanceUsed().negate());
                        fdTradingDetails.setTradingStatus("1");
                        //记录交易明细
//            insertFdTradingDetails(fdTradingDetails);
                        //修改汇款数据的金额
                        FdRemittanceRecord fdRemittanceRecord = new FdRemittanceRecord();
                        fdRemittanceRecord.setRemittanceRecordCode(remittance.getRemittancecode());
                        fdRemittanceRecord.setResidualAmount(remittance.getRemittancesurplus());
                        fdRemittanceRecordController.update(fdRemittanceRecord);
                        try {
                            fdTradingDetailsService.insertTrandingDetails(fdTradingDetails);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
            /*FdTradingDetails fdTradingDetails = new FdTradingDetails();
            fdTradingDetails.setBillCode(fdBillVO.getBillCode());
            fdTradingDetails.setTradingStatus("1");
            fdTradingDetails.setUpdateUsercode(SecurityUtils.getUserInfo().getUserName());
            fdTradingDetails.setUpdateUserrealname(SecurityUtils.getUserInfo().getRealName());
            fdTradingDetailsMapper.updateFdTradingDetailsByBillCode(fdTradingDetails);*/
//            fdTradingDetailsService.update(fdTradingDetails);

        }
        return true;
    }

    @Override
    public Boolean insertFdTradingDetailsbyRemittance2(FdBillVO fdBillVO) {
        List<Remittance> remittances = fdBillVO.getRemittances();
        if (CollUtil.isNotEmpty(remittances)) {
            for (Remittance remittance : remittances) {
                if(remittance.getRemittanceUsed()!=null){
                    FdTradingDetails fdTradingDetails = new FdTradingDetails();
                    String shipmentTime = fdBillVO.getShipmentTime();
                    fdBillVO.setShipmentTime(null);
                    BeanUtil.copyProperties(fdBillVO, fdTradingDetails);
                    if(shipmentTime!=null &&!"".equals(shipmentTime)){
                        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        LocalDateTime localDateTime = LocalDateTime.parse(shipmentTime, dtf);
                        fdTradingDetails.setShipmentTime(localDateTime);
                        if(shipmentTime.contains(" 00:00:00")){
                            String str = shipmentTime.replace(" 00:00:00", "");
                            fdTradingDetails.setShipmentTimeStr(str);
                        }
                    }

                    fdTradingDetails.setCustomerName(fdBillVO.getStandbyC());
                    fdTradingDetails.setCustomerCode(fdBillVO.getStandbyD());
                    fdTradingDetails.setBalanceType(fdBillVO.getPaymentType());
                    fdTradingDetails.setDeductionBillCode(fdBillVO.getBillCode());
                    fdTradingDetails.setPaymentType("2");
                    fdTradingDetails.setRemittanceRecordCode(remittance.getRemittancecode());
                    fdTradingDetails.setTransactionAmount(remittance.getRemittanceUsed().negate());
                    fdTradingDetails.setTradingStatus("1");
                    fdTradingDetails.setProvinceShiftNo(fdBillVO.getProvinceTrainsNumber());
                    //记录交易明细
//            insertFdTradingDetails(fdTradingDetails);
                    //修改汇款数据的金额
                    FdRemittanceRecord fdRemittanceRecord = new FdRemittanceRecord();
                    fdRemittanceRecord.setRemittanceRecordCode(remittance.getRemittancecode());
                    fdRemittanceRecord.setResidualAmount(remittance.getRemittancesurplus());
                    fdRemittanceRecordController.update(fdRemittanceRecord);
                    try {
                        fdTradingDetailsService.insertTrandingDetails(fdTradingDetails);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        /*FdTradingDetails fdTradingDetails = new FdTradingDetails();
        fdTradingDetails.setBillCode(fdBillVO.getBillCode());
        fdTradingDetails.setTradingStatus("1");
        fdTradingDetails.setUpdateUsercode(SecurityUtils.getUserInfo().getUserName());
        fdTradingDetails.setUpdateUserrealname(SecurityUtils.getUserInfo().getRealName());
        fdTradingDetailsMapper.updateFdTradingDetailsByBillCode(fdTradingDetails);*/
        return true;
    }

    @Override
    public List<FdTradingDetails> selectHnrbList(FdTradingDetails fdTradingDetails) {
        return fdTradingDetailsMapper.selectHnrbList(fdTradingDetails);
    }

}
