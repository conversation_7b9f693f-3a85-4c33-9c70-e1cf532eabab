package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.AddCostByBillDTO;
import com.huazheng.tunny.ocean.api.dto.AddCostByBillListDTO;
import com.huazheng.tunny.ocean.api.dto.BillCostDetailDTO;
import com.huazheng.tunny.ocean.api.dto.ExchangeRateCalculationDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.enums.BalanceStatusEnum;
import com.huazheng.tunny.ocean.api.vo.*;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("billDealWithCityService")
public class BillDealWithCityServiceImpl extends ServiceImpl<BillDealWithCityMapper, BillDealWithCity> implements BillDealWithCityService {

    @Autowired
    private BillDealWithCityMapper billDealWithCityMapper;

    @Autowired
    private BillSubPayCityService billSubPayCityService;

    @Autowired
    private BillPayCustomerSubService billPayCustomerSubService;

    @Autowired
    private BillPayCustomerService billPayCustomerService;
    @Autowired
    private BillSubPayCityMapper billSubPayCityMapper;
    @Autowired
    private BillPayProvinceSubMapper billPayProvinceSubMapper;

    @Value("${db.database}")
    private String database;

    @Autowired
    private FdBusCostMapper fdBusCostMapper;

    @Autowired
    private FdBusCostDetailService busCostDetailService;

    @Autowired
    private BillPayProvinceMapper billPayProvinceMapper;

    @Autowired
    private BillPayProvinceSubService billPayProvinceSubService;

    @Autowired
    private SysNoConfigService sysNoConfigService;

    @Autowired
    private BillBalanceMainCityService billBalanceMainCityService;

    @Autowired
    private BillBalanceBindingCityService billBalanceBindingCityService;

    @Autowired
    private FdBalanceDetailService fdBalanceDetailService;

    @Autowired
    private ShifmanagementMapper shifmanagementMapper;
    /**
     * 查询应付账单（市）信息
     *
     * @param params
     * @return 应付账单（市）信息
     */
    @Override
    public R selectBillDealWithCityById(Map<String, Object> params) {
        R r = new R();
        Integer id = Integer.valueOf(String.valueOf(params.get("id")));
        Object customerNameOb = params.get("customerName");
        Integer pageType = Integer.valueOf(String.valueOf(params.get("pageType")));
        String customerName = "";
        if (!ObjectUtils.isEmpty(customerNameOb)) {
            customerName = String.valueOf(params.get("customerName"));
        }
        // 获取当前登录人信息
        SecruityUser userInfo = SecurityUtils.getUserInfo();

        String platformCode = userInfo.getPlatformCode();
        String platformLevel = userInfo.getPlatformLevel();

        if(pageType.equals(1)){
            // 判断一下时候是市对市的应付账单
            BillDealWithCityVO billDealWithCityVO = billDealWithCityMapper.selectBillDealWithCityToCityById(id,platformCode);
            if(ObjectUtil.isNotNull(billDealWithCityVO)){
                List<BillDealWithCityAndCostVO> cityAndCostVOS = billPayCustomerSubService.selectFdBillSubByBillNo(billDealWithCityVO.getBillCode(), customerName, platformCode);
                if (!CollectionUtils.isEmpty(cityAndCostVOS)) {
                    billDealWithCityVO.setList(cityAndCostVOS);
                    r.setData(billDealWithCityVO);
                }
                return r;
            }
        }
        BillDealWithCityVO billDealWithCityVO = billDealWithCityMapper.selectBillDealWithCityById(id,platformCode,platformLevel);
        if (ObjectUtils.isEmpty(billDealWithCityVO)) {
            r.setCode(500);
            r.setMsg("查询出错");
            return r;
        }
        // 查询出子账单信息
        List<BillDealWithCityAndCostVO> cityAndCostVOS = billSubPayCityService.selectFdBillSubByBillNo(billDealWithCityVO.getBillCode(), customerName, platformCode);
        if (!CollectionUtils.isEmpty(cityAndCostVOS)) {
            billDealWithCityVO.setList(cityAndCostVOS);
            r.setData(billDealWithCityVO);
        }

        return r;
    }

    /**
     * 根据账单Id获取费用明细
     *
     * @param query
     * @return
     */
    @Override
    public Map<String, Object> selectCostInfoByBillId(Query query) {

        BillCostDetailDTO billCostDetailDTO = BeanUtil.mapToBean(query.getCondition(), BillCostDetailDTO.class, false);
        // 获取当前登录人信息
        SecruityUser userInfo = SecurityUtils.getUserInfo();

        String platformCode = userInfo.getPlatformCode();
        String platformLevel = userInfo.getPlatformLevel();
        Map<String, Object> map = new HashMap<>();
        /*应付*/
        if(billCostDetailDTO.getPageType().equals(1)){
            BillDealWithCityVO billDealWithCityVO = billDealWithCityMapper.selectBillDealWithCityToCityById(billCostDetailDTO.getId(),platformCode);
            if(!ObjectUtils.isEmpty(billDealWithCityVO)){
                Shifmanagement shifmanagement = shifmanagementMapper.selectShifmanagementByPlatCodeAndShiftNo(platformCode, billDealWithCityVO.getProvinceShiftNum());
                if(ObjectUtils.isEmpty(shifmanagement)){
                    log.error("BillDealWithCityServiceImpl类：查询应付账单费用明细查询班次报错！");
                    return map;
                }
                if(!ObjectUtils.isEmpty(billDealWithCityVO)){
                    billCostDetailDTO.setShiftNo(billDealWithCityVO.getProvinceShiftNum());
                    billCostDetailDTO.setDbSource(database);
                    billCostDetailDTO.setBillCode(billDealWithCityVO.getBillCode());
                    List<BillCostDetailVO> billCostDetailVOS = new ArrayList<>();
                    if(StringUtils.isEmpty(shifmanagement.getParentId())){
                        billCostDetailVOS = billPayCustomerService.selectCostInfoByCityCostCode(query, billCostDetailDTO);
                    }else {
                        billCostDetailVOS = billPayCustomerService.selectCostInfoByOrderInfo(query, billCostDetailDTO);
                    }

                    if(!CollectionUtils.isEmpty(billCostDetailVOS)){
                        Double ybJe = billCostDetailVOS.stream().mapToDouble(b -> {
                            try {
                                return Double.parseDouble(String.valueOf(b.getOriginalAmount()));
                            }catch (NumberFormatException e){
                                return 0.0;
                            }
                        }).sum();

                        Double bbJe = billCostDetailVOS.stream().mapToDouble(b -> {
                            try {
                                return Double.parseDouble(String.valueOf(b.getLocalAmount()));
                            }catch (NumberFormatException e){
                                return 0.0;
                            }
                        }).sum();
                        query.setRecords(billCostDetailVOS);
                        map.put("ybJe",ybJe);
                        map.put("bbJe",bbJe);
                        map.put("data",query);
                    }
                    return map;
                }
            }
        }
        BillDealWithCityVO billDealWithCityVO = billDealWithCityMapper.selectBillDealWithCityById(billCostDetailDTO.getId(),platformCode,platformLevel);
        if(!ObjectUtils.isEmpty(billDealWithCityVO)){

            Shifmanagement shifmanagement = new Shifmanagement();
            if(platformLevel.equals("1")){
                shifmanagement = shifmanagementMapper.selectShifmanagementByPlatCodeAndShiftNo(platformCode, billDealWithCityVO.getProvinceShiftNum());
                if(ObjectUtils.isEmpty(shifmanagement)){
                    log.error("BillDealWithCityServiceImpl类：查询应付账单费用明细查询班次报错！");
                    return map;
                }
            }

            if (!ObjectUtils.isEmpty(billDealWithCityVO)) {
                billCostDetailDTO.setShiftNo(billDealWithCityVO.getProvinceShiftNum());
                billCostDetailDTO.setDbSource(database);
                billCostDetailDTO.setBillCode(billDealWithCityVO.getBillCode());
                List<BillCostDetailVO> billCostDetailVOS = new ArrayList<>();
                if(platformLevel.equals("1")){
                    if(StringUtils.isEmpty(shifmanagement.getParentId())) {
                        billCostDetailVOS = billDealWithCityMapper.selectCostInfoByCostCode(query, billCostDetailDTO);
                    } else {
                        billCostDetailVOS = billDealWithCityMapper.selectCostInfoByOrderInfo(query, billCostDetailDTO);
                    }
                }else{
                    billCostDetailVOS = billDealWithCityMapper.selectCostInfoByCostCode(query, billCostDetailDTO);
                }

                if (!CollectionUtils.isEmpty(billCostDetailVOS)) {
                    Double ybJe = billCostDetailVOS.stream().mapToDouble(b -> {
                        try {
                            return Double.parseDouble(String.valueOf(b.getOriginalAmount()));
                        } catch (NumberFormatException e) {
                            return 0.0;
                        }
                    }).sum();

                    Double bbJe = billCostDetailVOS.stream().mapToDouble(b -> {
                        try {
                            return Double.parseDouble(String.valueOf(b.getLocalAmount()));
                        } catch (NumberFormatException e) {
                            return 0.0;
                        }
                    }).sum();
                    query.setRecords(billCostDetailVOS);
                    map.put("ybJe", ybJe);
                    map.put("bbJe", bbJe);
                    map.put("data", query);
                }
            }
        }

        return map;
    }

    /**
     * 转实收接口
     * @param id
     * @param pageType
     * @return
     */
    @Override
    public R updateYfOfSf(Integer id, Integer pageType) {
        R r = new R();
        // 获取当前登录人信息
        SecruityUser userInfo = SecurityUtils.getUserInfo();

        String platformCode = userInfo.getPlatformCode();
        String platformLevel = userInfo.getPlatformLevel();
        if(platformLevel.equals("2") && pageType.equals(0)){
            billDealWithCityMapper.updateCityBill(id);
        }else if(platformLevel.equals("2") && pageType.equals(1)){
            billDealWithCityMapper.updateProvinceBill(id);
        }else if(platformLevel.equals("1") && pageType.equals(0)){
            billDealWithCityMapper.updateCustomerBill(id);
        }
        return r; 
    }

    /**
     * 省对市追加费用接口
     * @param addCostByBillDTO
     * @return
     */
    @Transactional(isolation = Isolation.READ_COMMITTED,rollbackFor = Exception.class)
    @Override
    public R addCostByBill(AddCostByBillDTO addCostByBillDTO) {
        List<AddCostByBillListDTO> addList = new ArrayList<>();
        //去除出金额是0的数据
        for (AddCostByBillListDTO addCostByBillListDTO : addCostByBillDTO.getAddList()){
            if (addCostByBillListDTO.getLocalAmount() != null && addCostByBillListDTO.getLocalAmount().compareTo(BigDecimal.ZERO) != 0) {
                addList.add(addCostByBillListDTO);
            }
        }
        addCostByBillDTO.setAddList(addList);
        if (CollectionUtils.isEmpty(addCostByBillDTO.getAddList())){
            return R.error("追加费用不能为0");
        }
        R r = new R();
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            // 查询出账单数据
            // 主账单Id
            Integer id = addCostByBillDTO.getId();
            Integer pageType = addCostByBillDTO.getPageType();
            // 获取当前登录人信息
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String platformCode = userInfo.getPlatformCode();
            String platformLevel = userInfo.getPlatformLevel();
            String platformName = userInfo.getPlatformName();

            String shiftNo = "";
            String billCode = "";
            int listSize = 0;
            // 应收
            if(pageType.equals(0)){
                // 查询市应付账单数据
                BillDealWithCityVO billDealWithCityVO = billDealWithCityMapper.selectBillDealWithCityById(id, platformCode, platformLevel);
                if(ObjectUtils.isEmpty(billDealWithCityVO)){
                    r.setCode(500);
                    r.setMsg("未查询出账单主数据");
                    return r;
                }
                List<BillSubPayCity> billSubPayCities = billDealWithCityMapper.selectBillSubByBillCode(billDealWithCityVO.getBillCode());
                if(CollectionUtil.isEmpty(billSubPayCities)){
                    r.setCode(500);
                    r.setMsg("未查询出账单数据");
                    return r;
                }
                shiftNo = billDealWithCityVO.getProvinceShiftNum();
                billCode = billDealWithCityVO.getBillCode();
                listSize = billSubPayCities.size();
                if(listSize == 0){
                    r.setCode(500);
                    r.setMsg("未查询出账单数据");
                    return r;
                }
                // 将数据封装
                // 班列信息
                Shifmanagement shifmanagement = billDealWithCityMapper.selectShiftInfo(shiftNo, platformCode);
                // 费用流程单
                //billDealWithCityMapper.selectFdBusCodeBy
                // 查询出发运台账信息
                FdShippingAccount fdShippingAccount = billDealWithCityMapper.selectAccountByShiftNoAndPlatformCode(shiftNo, platformCode);
                // 生成子帐单数据
                BillSubPayCity billSubPayCity = new BillSubPayCity();
                billSubPayCity.setBillCode(billCode);
                String billSubCode = billCode +"-"+ String.format("%03d", listSize+1);
                billSubPayCity.setBillSubCode(billSubCode);
                //billSubPayCity.setWaybillNo();
                FdBusCost sel2 = new FdBusCost();
                sel2.setShiftNo(shiftNo);
                sel2.setPlatformCode(platformCode);
                sel2.setDeleteFlag("N");
                List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel2);
                if (CollUtil.isNotEmpty(fdBusCosts)) {
                    billSubPayCity.setCostCode(fdBusCosts.get(0).getCostCode());
                }
                billSubPayCity.setAccountCode(fdShippingAccount.getAccountCode());
                billSubPayCity.setPlatformCode(billSubPayCities.get(0).getPlatformCode());
                billSubPayCity.setPlatformName(billSubPayCities.get(0).getPlatformName());
                billSubPayCity.setPlatformLevel(billSubPayCities.get(0).getPlatformLevel());
                billSubPayCity.setProvinceShiftNo(shifmanagement.getProvinceShiftNo());
                billSubPayCity.setShiftNo(shifmanagement.getShiftId());
                billSubPayCity.setShiftName(shifmanagement.getShiftName());
                billSubPayCity.setShipmentTime(LocalDateTime.ofInstant(shifmanagement.getPlanShipTime().toInstant(), ZoneId.systemDefault()));
                BigDecimal reduce = addCostByBillDTO.getAddList().stream().map(AddCostByBillListDTO::getLocalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

                billSubPayCity.setBillAmount(reduce);
                billSubPayCity.setBillingState("0");
                billSubPayCity.setAddWho(platformCode);
                billSubPayCity.setAddWhoName(platformName);
                billSubPayCity.setAddTime(LocalDateTime.now());
                billSubPayCity.setCustomerCode(billSubPayCities.get(0).getCustomerCode());
                billSubPayCity.setCustomerName(billSubPayCities.get(0).getCustomerName());
                billSubPayCity.setPortStation(shifmanagement.getPortStation());
                billSubPayCity.setStatus("1");
                billSubPayCity.setBoxNum(addCostByBillDTO.getAddList().size());
                //billSubPayCity.setCostCode(costCode);
                // 增加业务逻辑 如果金额为负数的情况下以及账单已经结算将当前增加的账单直接结算
                if(reduce.compareTo(new BigDecimal("0")) < 0){
                    // 查看001的账单是否为已结算
                    Optional<BillSubPayCity> first = billSubPayCities.stream().filter(billSubPayCity1 -> billSubPayCity1.getBillSubCode().contains("001"))
                            .filter(b -> b.getBillingState().equals(BalanceStatusEnum.VERIFIED.getKey()) || b.getBillingState().equals(BalanceStatusEnum.VERIFICATION.getKey())).findFirst();
                    if(first.isPresent()){
                        // 如果查询出数据进行后续业务操作
                        billSubPayCity.setBillingState(BalanceStatusEnum.VERIFIED.getKey());
                        // 结算单名称
                        String balanceName = addCostByBillDTO.getAddList().stream().map(AddCostByBillListDTO::getCodeSsCategoriesName).distinct().collect(Collectors.joining(","));
                        String balanceCode = sysNoConfigService.genNo("JS");
                        // 生成结算单数据
                        BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
                        billBalanceMainCity.setBalanceBillNo(balanceCode);
                        billBalanceMainCity.setBalanceBillName(balanceName);
                        billBalanceMainCity.setShiftNos(shifmanagement.getShiftId());
                        billBalanceMainCity.setShiftNames(shifmanagement.getShiftName());
                        billBalanceMainCity.setPostDate(simpleDateFormat.format(shifmanagement.getPlanShipTime()));
                        billBalanceMainCity.setCustomerCode(billSubPayCity.getCustomerCode());
                        billBalanceMainCity.setCustomerName(billSubPayCity.getCustomerName());
                        billBalanceMainCity.setBalanceStatus(BalanceStatusEnum.VERIFIED.getKey());
                        billBalanceMainCity.setBillAmount(reduce);
                        billBalanceMainCity.setDeductionAmout(new BigDecimal("0"));
                        billBalanceMainCity.setActualAmout(new BigDecimal("0"));
                        billBalanceMainCity.setCreateBy(platformName);
                        billBalanceMainCity.setCreateTime(new Date());
                        billBalanceMainCity.setDeleteFlag("N");
                        billBalanceMainCity.setPlatformCode(billSubPayCity.getPlatformCode());
                        billBalanceMainCity.setPlatformName(billSubPayCity.getPlatformName());
                        billBalanceMainCity.setPlatformLevel(billSubPayCity.getPlatformLevel());
                        billBalanceMainCity.setProvinceTrainNum(shifmanagement.getProvinceShiftNo());
                        billBalanceMainCityService.insert(billBalanceMainCity);
                        // 关联表
                        BillBalanceBindingCity billBalanceBindingCity = new BillBalanceBindingCity();
                        billBalanceBindingCity.setBillBalanceCode(balanceCode);
                        billBalanceBindingCity.setBillSubCode(billSubPayCity.getBillSubCode());
                        billBalanceBindingCity.setCreateBy(platformName);
                        billBalanceBindingCity.setCreateTime(new Date());
                        billBalanceBindingCity.setDeleteFlag("N");
                        billBalanceBindingCityService.insert(billBalanceBindingCity);
                        // 插入余额数据
                        // 市在省余额
                        FdBalanceDetail fdBalanceDetail = new FdBalanceDetail();
                        fdBalanceDetail.setShiftId(shifmanagement.getShiftId());
                        fdBalanceDetail.setShiftNo(shifmanagement.getShiftId());
                        fdBalanceDetail.setPlatformCode(platformCode);
                        fdBalanceDetail.setCustomerCode(billSubPayCity.getCustomerCode());
                        fdBalanceDetail.setCustomerName(billSubPayCity.getCustomerName());
                        fdBalanceDetail.setPaymentType("0");
                        fdBalanceDetail.setTotalAmount(reduce.abs());
                        fdBalanceDetail.setRemainingAmount(reduce.abs());
                        fdBalanceDetail.setRemarks(balanceName);
                        fdBalanceDetail.setBillCode(balanceCode);
                        fdBalanceDetail.setCentralLineCode(shifmanagement.getShippingLineCode());
                        fdBalanceDetail.setCentralLineName(shifmanagement.getShippingLine());
                        fdBalanceDetail.setPlatformLevel("1");
                        fdBalanceDetail.setAddTime(LocalDateTime.now());
                        fdBalanceDetail.setLockingAmount(new BigDecimal("0"));
                        fdBalanceDetail.setAvailableAmount(reduce.abs());
                        fdBalanceDetailService.insert(fdBalanceDetail);
                    }
                }


                    // 费用明细增加
                List<FdBusCostDetail> fdBusCostDetails = new ArrayList<>();
                for(AddCostByBillListDTO addCostByBillListDTO : addCostByBillDTO.getAddList()){
                    // 查询子帐单001的CostCode
                    List<BillSubPayCity> subPayCities = billSubPayCities.stream().filter(billSubPayCity1 -> billSubPayCity1.getBillSubCode().contains("001")).collect(Collectors.toList());
                    String costCode = fdBusCostMapper.selectCostCode(subPayCities.get(0).getBillSubCode(), addCostByBillListDTO.getBoxNum(), subPayCities.get(0).getShiftNo());
                    // TODO 等待添加新汇率
                    FdBusCostDetail fdBusCostDetail = new FdBusCostDetail();
                    fdBusCostDetail.setCostCode(costCode);
                    fdBusCostDetail.setCostType("1");
                    fdBusCostDetail.setContainerNumber(addCostByBillListDTO.getBoxNum());
                    fdBusCostDetail.setCodeBbCategoriesCode(addCostByBillListDTO.getCodeBbCategoriesCode());
                    fdBusCostDetail.setCodeBbCategoriesName(addCostByBillListDTO.getCodeBbCategoriesName());
                    fdBusCostDetail.setCodeSsCategoriesCode(addCostByBillListDTO.getCodeSsCategoriesCode());
                    fdBusCostDetail.setCodeSsCategoriesName(addCostByBillListDTO.getCodeSsCategoriesName());
                    fdBusCostDetail.setReceiveCode(platformCode);
                    fdBusCostDetail.setReceiveName(platformName);
                    fdBusCostDetail.setPayCode(fdShippingAccount.getPlatformCode());
                    fdBusCostDetail.setPayName(fdShippingAccount.getPlatformName());
                    fdBusCostDetail.setCurrency(addCostByBillListDTO.getCurrency());
                    fdBusCostDetail.setExchangeRate(addCostByBillListDTO.getExchangeRate());
                    fdBusCostDetail.setExchangeRateNew(addCostByBillListDTO.getExchangeRate());
                    fdBusCostDetail.setOriginalAmount(addCostByBillListDTO.getOriginalAmount());
                    fdBusCostDetail.setLocalAmount(addCostByBillListDTO.getLocalAmount());
                    fdBusCostDetail.setAuditStatus("1");
                    fdBusCostDetail.setAddWho(platformCode);
                    fdBusCostDetail.setAddWhoName(platformName);
                    fdBusCostDetail.setAddTime(LocalDateTime.now());
                    fdBusCostDetail.setShiftNo(shifmanagement.getShiftId());
                    fdBusCostDetail.setBillSubCode(billSubCode);


                    // 修改原有箱费用汇率；
                    List<FdBusCostDetail> fdBusCostDetailsCon = busCostDetailService.selectFdExchangeRateByCostInfo(fdBusCostDetail.getContainerNumber(),
                            fdBusCostDetail.getCodeBbCategoriesCode(),
                            fdBusCostDetail.getCodeSsCategoriesCode(),
                            billDealWithCityVO.getBillCode());

                    if(!CollectionUtils.isEmpty(fdBusCostDetailsCon)){
                        for(FdBusCostDetail fdBus : fdBusCostDetailsCon){
                            fdBus.setExchangeRateNew(addCostByBillListDTO.getExchangeRate());
                        }
                        FdBusCostDetail fdBusCostDetail1 = fdBusCostDetailsCon.get(0);
                        fdBusCostDetail.setRemark("原汇率为："+fdBusCostDetail1.getExchangeRate()+",原币金额为："+fdBusCostDetail1.getOriginalAmount());
                        busCostDetailService.updateBatchById(fdBusCostDetailsCon);
                    }

                    // 判断根据班次和箱号修改台账信息
                    if(fdBusCostDetail.getCodeBbCategoriesCode().equals("f_fee_type")){
                        billDealWithCityMapper.updateAccData(fdShippingAccount.getAccountCode(),fdBusCostDetail.getContainerNumber(),fdBusCostDetail.getExchangeRate(),fdBusCostDetail.getLocalAmount());
                    }
                    fdBusCostDetails.add(fdBusCostDetail);
                }
//                billSubPayCityService.insert(billSubPayCity);
                billSubPayCityMapper.insertBillSubPayCity(billSubPayCity);
                busCostDetailService.insertBatch(fdBusCostDetails);
                billDealWithCityMapper.updateBillAmountByBillCode(billSubPayCity.getBillCode());
            }

            if(pageType.equals(1)){
                BillPayProvinceVO billPayProvinceVO = billPayProvinceMapper.selectBillPayProvinceById(id);
                if(ObjectUtils.isEmpty(billPayProvinceVO)){
                    r.setCode(500);
                    r.setMsg("未查询出账单主数据");
                    return r;
                }
                List<BillPayProvinceSub> billPayProvinceSubs = billPayProvinceSubService.selectBillPayProvinceSubByBillCode(billPayProvinceVO.getBillCode());
                if(CollectionUtil.isEmpty(billPayProvinceSubs)){
                    r.setCode(500);
                    r.setMsg("未查询出账单数据");
                    return r;
                }
                shiftNo = billPayProvinceVO.getProvinceShiftNum();
                billCode = billPayProvinceVO.getBillCode();
                listSize = billPayProvinceSubs.size();
                //costCode = billPayProvinceSubs.get(0).getCostCode();

                if(listSize == 0){
                    r.setCode(500);
                    r.setMsg("未查询出账单数据");
                    return r;
                }

                // 将数据封装
                // 班列信息
                Shifmanagement shifmanagement = billDealWithCityMapper.selectShiftInfo(shiftNo, platformCode);
                // 费用流程单
                //billDealWithCityMapper.selectFdBusCodeBy
                // 查询出发运台账信息
                FdShippingAccount fdShippingAccount = billDealWithCityMapper.selectAccountByShiftNoAndPlatformCode(shiftNo, platformCode);
                // 生成子帐单数据
                BillPayProvinceSub billPayProvinceSub = new BillPayProvinceSub();
                billPayProvinceSub.setBillCode(billCode);
                String billSubCode = billCode +"-"+ String.format("%03d", listSize+1);
                billPayProvinceSub.setBillSubCode(billSubCode);
                //billSubPayCity.setWaybillNo();
                FdBusCost sel2 = new FdBusCost();
                sel2.setShiftNo(shiftNo);
                sel2.setPlatformCode(platformCode);
                sel2.setDeleteFlag("N");
                List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel2);
                if (CollUtil.isNotEmpty(fdBusCosts)) {
                    billPayProvinceSub.setCostCode(fdBusCosts.get(0).getCostCode());
                }
                billPayProvinceSub.setAccountCode(fdShippingAccount.getAccountCode());
                billPayProvinceSub.setPlatformCode(billPayProvinceSubs.get(0).getPlatformCode());
                billPayProvinceSub.setPlatformName(billPayProvinceSubs.get(0).getPlatformName());
                billPayProvinceSub.setPlatformLevel(billPayProvinceSubs.get(0).getPlatformLevel());
                billPayProvinceSub.setProvinceShiftNo(shifmanagement.getProvinceShiftNo());
                billPayProvinceSub.setShiftNo(shifmanagement.getShiftId());
                billPayProvinceSub.setShiftName(shifmanagement.getShiftName());

                BigDecimal reduce = addCostByBillDTO.getAddList().stream().map(AddCostByBillListDTO::getLocalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                billPayProvinceSub.setShipmentTime(shifmanagement.getPlanShipTime());
                billPayProvinceSub.setBillAmount(reduce);
                billPayProvinceSub.setBillingState("0");
                billPayProvinceSub.setAddWho(platformCode);
                billPayProvinceSub.setAddWhoName(platformName);
                billPayProvinceSub.setAddTime(new Date());
                billPayProvinceSub.setCustomerCode(billPayProvinceSubs.get(0).getCustomerCode());
                billPayProvinceSub.setCustomerName(billPayProvinceSubs.get(0).getCustomerName());
                billPayProvinceSub.setPortStation(shifmanagement.getPortStation());
                billPayProvinceSub.setStatus("1");
                billPayProvinceSub.setBoxNum(addCostByBillDTO.getAddList().size());
                //billPayProvinceSub.setCostCode(costCode);

                // 增加业务逻辑 如果金额为负数的情况下以及账单已经结算将当前增加的账单直接结算
                if(reduce.compareTo(new BigDecimal("0")) < 0){
                    // 查看001的账单是否为已结算
                    Optional<BillPayProvinceSub> first = billPayProvinceSubs.stream().filter(billSubPayCity1 -> billSubPayCity1.getBillSubCode().contains("001"))
                            .filter(b -> b.getBillingState().equals(BalanceStatusEnum.VERIFIED.getKey()) || b.getBillingState().equals(BalanceStatusEnum.VERIFICATION.getKey())).findFirst();
                    if(first.isPresent()){
                        // 如果查询出数据进行后续业务操作
                        billPayProvinceSub.setBillingState(BalanceStatusEnum.VERIFIED.getKey());
                        // 结算单名称
                        String balanceName = addCostByBillDTO.getAddList().stream().map(AddCostByBillListDTO::getCodeSsCategoriesName).distinct().collect(Collectors.joining(","));
                        String balanceCode = sysNoConfigService.genNo("JS");
                        // 生成结算单数据
                        BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
                        billBalanceMainCity.setBalanceBillNo(balanceCode);
                        billBalanceMainCity.setBalanceBillName(balanceName);
                        billBalanceMainCity.setShiftNos(shifmanagement.getShiftId());
                        billBalanceMainCity.setShiftNames(shifmanagement.getShiftName());
                        billBalanceMainCity.setPostDate(simpleDateFormat.format(shifmanagement.getPlanShipTime()));
                        billBalanceMainCity.setCustomerCode(billPayProvinceSub.getCustomerCode());
                        billBalanceMainCity.setCustomerName(billPayProvinceSub.getCustomerName());
                        billBalanceMainCity.setBalanceStatus(BalanceStatusEnum.VERIFIED.getKey());
                        billBalanceMainCity.setBillAmount(reduce);
                        billBalanceMainCity.setDeductionAmout(new BigDecimal("0"));
                        billBalanceMainCity.setActualAmout(new BigDecimal("0"));
                        billBalanceMainCity.setCreateBy(platformName);
                        billBalanceMainCity.setCreateTime(new Date());
                        billBalanceMainCity.setDeleteFlag("N");
                        billBalanceMainCity.setPlatformCode(billPayProvinceSub.getPlatformCode());
                        billBalanceMainCity.setPlatformName(billPayProvinceSub.getPlatformName());
                        billBalanceMainCity.setPlatformLevel(billPayProvinceSub.getPlatformLevel());
                        billBalanceMainCity.setProvinceTrainNum(shifmanagement.getProvinceShiftNo());
                        billBalanceMainCityService.insert(billBalanceMainCity);
                        // 关联表
                        BillBalanceBindingCity billBalanceBindingCity = new BillBalanceBindingCity();
                        billBalanceBindingCity.setBillBalanceCode(balanceCode);
                        billBalanceBindingCity.setBillSubCode(billPayProvinceSub.getBillSubCode());
                        billBalanceBindingCity.setCreateBy(platformName);
                        billBalanceBindingCity.setCreateTime(new Date());
                        billBalanceBindingCity.setDeleteFlag("N");
                        billBalanceBindingCityService.insert(billBalanceBindingCity);
                        // 插入余额数据
                        // 市在省余额
                        FdBalanceDetail fdBalanceDetail = new FdBalanceDetail();
                        fdBalanceDetail.setPlatformCode(platformCode);
                        fdBalanceDetail.setCustomerCode(shifmanagement.getPlatformCode());
                        fdBalanceDetail.setCustomerName(shifmanagement.getResveredField02());
                        fdBalanceDetail.setPaymentType("0");
                        fdBalanceDetail.setTotalAmount(reduce.abs());
                        fdBalanceDetail.setRemainingAmount(reduce.abs());
                        fdBalanceDetail.setRemarks(balanceName);
                        fdBalanceDetail.setBillCode(balanceCode);
                        fdBalanceDetail.setCentralLineCode(shifmanagement.getShippingLineCode());
                        fdBalanceDetail.setCentralLineName(shifmanagement.getShippingLine());
                        fdBalanceDetail.setPlatformLevel("2");
                        fdBalanceDetail.setAddTime(LocalDateTime.now());
                        fdBalanceDetail.setLockingAmount(new BigDecimal("0"));
                        fdBalanceDetail.setAvailableAmount(reduce.abs());
                        fdBalanceDetailService.insert(fdBalanceDetail);
                    }
                }
                // 费用明细增加
                List<FdBusCostDetail> fdBusCostDetails = new ArrayList<>();
                for(AddCostByBillListDTO addCostByBillListDTO : addCostByBillDTO.getAddList()){
                    FdBusCostDetail fdBusCostDetail = new FdBusCostDetail();

                    fdBusCostDetail.setContainerNumber(addCostByBillListDTO.getBoxNum());
                    fdBusCostDetail.setCodeBbCategoriesCode(addCostByBillListDTO.getCodeBbCategoriesCode());
                    fdBusCostDetail.setCodeBbCategoriesName(addCostByBillListDTO.getCodeBbCategoriesName());
                    fdBusCostDetail.setCodeSsCategoriesCode(addCostByBillListDTO.getCodeSsCategoriesCode());
                    fdBusCostDetail.setCodeSsCategoriesName(addCostByBillListDTO.getCodeSsCategoriesName());
                    fdBusCostDetail.setReceiveCode("ztdl");
                    fdBusCostDetail.setReceiveName("中铁国际多式联运有限公司");
                    fdBusCostDetail.setPayCode(fdShippingAccount.getCustomerNo());
                    fdBusCostDetail.setPayName(fdShippingAccount.getCustomerName());
                    fdBusCostDetail.setCurrency(addCostByBillListDTO.getCurrency());
                    fdBusCostDetail.setExchangeRate(addCostByBillListDTO.getExchangeRate());
                    fdBusCostDetail.setOriginalAmount(addCostByBillListDTO.getOriginalAmount());
                    fdBusCostDetail.setLocalAmount(addCostByBillListDTO.getLocalAmount());
                    fdBusCostDetail.setAuditStatus("1");
                    fdBusCostDetail.setAddWho(platformCode);
                    fdBusCostDetail.setAddWhoName(platformName);
                    fdBusCostDetail.setAddTime(LocalDateTime.now());
                    fdBusCostDetail.setShiftNo(shifmanagement.getShiftId());
                    fdBusCostDetail.setBillSubCode(billSubCode);

                    // 修改原有箱费用汇率；
                    List<FdBusCostDetail> fdBusCostDetailsCon = busCostDetailService.selectFdExchangeRateByCostInfo(fdBusCostDetail.getContainerNumber(),
                            fdBusCostDetail.getCodeBbCategoriesCode(),
                            fdBusCostDetail.getCodeSsCategoriesCode(),
                            billPayProvinceVO.getBillCode());

                    if(!CollectionUtils.isEmpty(fdBusCostDetailsCon)){
                        for(FdBusCostDetail fdBus : fdBusCostDetailsCon){
                            fdBus.setExchangeRateNew(addCostByBillListDTO.getExchangeRate());
                        }
                        FdBusCostDetail fdBusCostDetail1 = fdBusCostDetailsCon.get(0);
                        fdBusCostDetail.setRemark("原汇率为："+fdBusCostDetail1.getExchangeRate()+",原币金额为："+fdBusCostDetail1.getOriginalAmount());
                        busCostDetailService.updateBatchById(fdBusCostDetailsCon);
                    }


                    fdBusCostDetails.add(fdBusCostDetail);

                    // 判断根据班次和箱号修改台账信息
                    if(fdBusCostDetail.getCodeBbCategoriesCode().equals("f_fee_type")){
                        billDealWithCityMapper.updateAccDataForPay(fdShippingAccount.getAccountCode(),fdBusCostDetail.getContainerNumber(),fdBusCostDetail.getLocalAmount());
                    }
                }
//                billPayProvinceSubService.insert(billPayProvinceSub);
                billPayProvinceSubMapper.insertBillPayProvinceSub(billPayProvinceSub);
                busCostDetailService.insertBatch(fdBusCostDetails);
                billPayProvinceMapper.updateBillAmountByBillCode(billPayProvinceSub.getBillCode());
            }

            return r;
        }catch (Exception e){
            e.printStackTrace();
            r.setMsg("未查询出账单数据");
            r.setCode(500);
            return r;
        }
    }

    /**
     * 查询应付账单（市）列表
     *
     * @param billDealWithCity 应付账单（市）信息
     * @return 应付账单（市）集合
     */
    @Override
    public List<BillDealWithCity> selectBillDealWithCityList(BillDealWithCity billDealWithCity) {
        return billDealWithCityMapper.selectBillDealWithCityList(billDealWithCity);
    }


    /**
     * 分页模糊查询应付账单（市）列表
     *
     * @return 应付账单（市）集合
     */
    @Override
    public Page selectBillDealWithCityListByLike(Query query) {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.sub_billing_status");
            query.setAsc(Boolean.TRUE);
        }
        BillDealWithCity billDealWithCity = BeanUtil.mapToBean(query.getCondition(), BillDealWithCity.class, false);
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        billDealWithCity.setPlatformCode(userInfo.getPlatformCode());
        billDealWithCity.setPlatformLevel(userInfo.getPlatformLevel());
        query.setRecords(billDealWithCityMapper.selectBillDealWithCityListByLike(query, billDealWithCity));
        return query;
    }

    /**
     * 查询所有子帐单
     *
     * @param query
     * @return
     */
    @Override
    public Page pageSubBill(Query query) {
        BillDealWithCity billDealWithCity = BeanUtil.mapToBean(query.getCondition(), BillDealWithCity.class, false);
        // 获取当前登录人信息
        SecruityUser userInfo = SecurityUtils.getUserInfo();

        String platformCode = userInfo.getPlatformCode();
        String platformLevel = userInfo.getPlatformLevel();


        List<BalanceSubBillVO> balanceSubBillVOS = new ArrayList<>();
        // 应收
        if (platformLevel.equals("1") && billDealWithCity.getPageType() == 0) {
            billDealWithCity.setPlatformCode(platformCode);
            balanceSubBillVOS = billDealWithCityMapper.pageSubBillCustomer(query, billDealWithCity);
        }
        // 应付
        if (platformLevel.equals("1") && billDealWithCity.getPageType() == 1) {
            billDealWithCity.setCustomerCode(platformCode);
            balanceSubBillVOS = billDealWithCityMapper.pageSubBill(query, billDealWithCity);
        }
        if(platformLevel.equals("2") && billDealWithCity.getPageType() == 1){
            billDealWithCity.setCustomerCode(platformCode);
            balanceSubBillVOS = billDealWithCityMapper.pageSubBillProvince(query, billDealWithCity);
        }

        if (CollectionUtil.isNotEmpty(balanceSubBillVOS) && !balanceSubBillVOS.stream().anyMatch(Objects::isNull)) {
            for(BalanceSubBillVO balanceSubBillVO : balanceSubBillVOS){
                String codeSsCategoriesNameByBillSubCode = billDealWithCityMapper.getCodeSsCategoriesNameByBillSubCode(balanceSubBillVO.getBillCode());
                if(StringUtils.isNotEmpty(codeSsCategoriesNameByBillSubCode)){
                    balanceSubBillVO.setProductNames(codeSsCategoriesNameByBillSubCode);
                }
            }

            query.setRecords(balanceSubBillVOS);
        } else {
            query.setRecords(new ArrayList());
        }
        return query;
    }

    /**
     * 查询平台涉及的所有客户账单
     *
     * @param params
     * @return
     */
    @Override
    public List<BalanceSubBillVO> selectCustomerList(Map<String, Object> params) {
        BillDealWithCity billDealWithCity = BeanUtil.mapToBean(params, BillDealWithCity.class, false);
        return billDealWithCityMapper.selectCustomerList(billDealWithCity);
    }

    /**
     * 新增应付账单（市）
     *
     * @param billDealWithCity 应付账单（市）信息
     * @return 结果
     */
    @Override
    public int insertBillDealWithCity(BillDealWithCity billDealWithCity) {
        return billDealWithCityMapper.insertBillDealWithCity(billDealWithCity);
    }

    /**
     * 修改应付账单（市）
     *
     * @param billDealWithCity 应付账单（市）信息
     * @return 结果
     */
    @Override
    public int updateBillDealWithCity(BillDealWithCity billDealWithCity) {
        return billDealWithCityMapper.updateBillDealWithCity(billDealWithCity);
    }


    /**
     * 删除应付账单（市）
     *
     * @param id 应付账单（市）ID
     * @return 结果
     */
    public int deleteBillDealWithCityById(Integer id) {
        return billDealWithCityMapper.deleteBillDealWithCityById(id);
    }

    ;


    /**
     * 批量删除应付账单（市）对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBillDealWithCityByIds(Integer[] ids) {
        return billDealWithCityMapper.deleteBillDealWithCityByIds(ids);
    }

    /**
     * 根据账单id获取账单下所有费用
     * @param id
     * @return
     */
    @Override
    public R getCostByBillId(Integer id, Integer pageType) {
        R r = new R();
        // 获取当前登录人信息
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String platformCode = userInfo.getPlatformCode();
        String platformLevel = userInfo.getPlatformLevel();

        BillCostDetailDTO billCostDetailDTO = new BillCostDetailDTO();
        billCostDetailDTO.setCodeSsCategoriesName("国外段包干");
        // 应收
        if(pageType.equals(0)){
            BillDealWithCityVO billDealWithCityVO = billDealWithCityMapper.selectBillDealWithCityById(id, platformCode, platformLevel);
            if(!ObjectUtils.isEmpty(billDealWithCityVO)){
                billCostDetailDTO.setShiftNo(billDealWithCityVO.getProvinceShiftNum());
                billCostDetailDTO.setBillCode(billDealWithCityVO.getBillCode());
                List<BillCostDetailVO> billCostDetailVOS = billDealWithCityMapper.selectCostInfoByCostCode(billCostDetailDTO);
                if(CollectionUtil.isNotEmpty(billCostDetailVOS)){
                    List<BillCostDetailVO> billCostDetailVOS1 = dedupeAndSumAmounts(billCostDetailVOS);
                    r.setData(billCostDetailVOS1);
                }
            }
        }
        if(pageType.equals(1)){
            BillPayProvinceVO billPayProvinceVO = billPayProvinceMapper.selectBillPayProvinceById(id);
            if(!ObjectUtils.isEmpty(billPayProvinceVO)){
                billCostDetailDTO.setShiftNo(billPayProvinceVO.getProvinceShiftNum());
                billCostDetailDTO.setBillCode(billPayProvinceVO.getBillCode());
                List<BillCostDetailVO> billCostDetailVOS = billPayProvinceMapper.selectCostInfoByCostCode(billCostDetailDTO);
                if(CollectionUtil.isNotEmpty(billCostDetailVOS)){
                    List<BillCostDetailVO> billCostDetailVOS1 = dedupeAndSumAmounts(billCostDetailVOS);
                    r.setData(billCostDetailVOS1);
                }
            }
        }
        return r;
    }

    /**
     * 汇率计算
     * @param exchangeRateCalculationDTO
     * @return
     */
    @Override
    public R addCalculation(ExchangeRateCalculationDTO exchangeRateCalculationDTO) {
        R r = new R();
        // 原数据
        List<BillCostDetailVO> billCostDetailVOList = exchangeRateCalculationDTO.getBillCostDetailVOList();
        List<BillCostDetailVO> requestList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(billCostDetailVOList)){
            for(BillCostDetailVO billCostDetailVO : billCostDetailVOList){
                // 计算
                // 汇率差
                BigDecimal hlCha = exchangeRateCalculationDTO.getExchangeRate().subtract(billCostDetailVO.getExchangeRate());

                // 计算后的本币金额
                BigDecimal originalAmount = billCostDetailVO.getOriginalAmount().multiply(hlCha);

                billCostDetailVO.setExchangeRate(hlCha.setScale(4, RoundingMode.HALF_UP));
                billCostDetailVO.setLocalAmount(originalAmount.setScale(2, RoundingMode.HALF_UP));
                billCostDetailVO.setOriginalAmount(BigDecimal.ZERO);
                requestList.add(billCostDetailVO);
            }
        }
        r.setData(requestList);
        return r;
    }


    public static List<BillCostDetailVO> dedupeAndSumAmounts(List<BillCostDetailVO> transactions) {
        Map<String, BillCostDetailVO> dedupeMap = new HashMap<>();

        for (BillCostDetailVO transaction : transactions) {
            String id = transaction.getContainerNumber();
            if (dedupeMap.containsKey(id)) {
                // 如果Map中已经存在该id，累加金额
                dedupeMap.get(id).setLocalAmount(dedupeMap.get(id).getLocalAmount().add(transaction.getLocalAmount()));
            } else {
                // 如果Map中不存在该id，添加新的对象
                dedupeMap.put(id, transaction);
            }
        }

        // 将Map的值转换为列表
        return new ArrayList<>(dedupeMap.values());
    }
}
