package com.huazheng.tunny.ocean.service.eabusinessdataanalysis;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.dto.eabillmain.EaFeeDTO;
import com.huazheng.tunny.ocean.api.entity.eabillmain.EaFee;
import com.huazheng.tunny.ocean.api.entity.eabusinessdataanalysis.EaShiftExpenseDetail;
import com.huazheng.tunny.ocean.api.entity.eabusinessdataanalysis.EaShiftGrossProfitDetail;
import com.huazheng.tunny.ocean.api.entity.eabusinessdataanalysis.EaShiftRevenueDetail;
import com.huazheng.tunny.ocean.api.entity.eabusinessdataanalysis.EaShiftRevenueStatistics;
import com.huazheng.tunny.ocean.mapper.eabillmain.EaFeeMapper;
import com.huazheng.tunny.ocean.mapper.eabusinessdataanalysis.EaShiftRevenueStatisticsMapper;
import com.huazheng.tunny.ocean.util.R;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 班次收入统计服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
public class EaShiftRevenueStatisticsService {

    @Resource
    private EaShiftRevenueStatisticsMapper shiftRevenueStatisticsMapper;

    @Resource
    private EaFeeMapper eaFeeMapper;

    /**
     * 分页查询班次收入统计
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public Page<EaShiftRevenueStatistics> page(Query query) {
        EaShiftRevenueStatistics shiftRevenueStatistics = BeanUtil.mapToBean(query.getCondition(), EaShiftRevenueStatistics.class, false);
        query.setRecords(shiftRevenueStatisticsMapper.selectShiftRevenueStatisticsPage(query, shiftRevenueStatistics));
        return query;
    }

    /**
     * 查询班次收入明细
     *
     * @param param 查询参数
     * @return 收入明细数据
     */
    public R<List<EaShiftRevenueDetail>> selectIncomesDetails(EaShiftRevenueDetail param) {
        List<EaShiftRevenueDetail> list = shiftRevenueStatisticsMapper.selectIncomesDetails(param);
        return R.success(list);
    }

    /**
     * 查询班次支出明细
     *
     * @param param 查询参数
     * @return 支出明细数据
     */
    public R<List<EaShiftExpenseDetail>> selectExpensesDetails(EaShiftExpenseDetail param) {
        List<EaShiftExpenseDetail> list = shiftRevenueStatisticsMapper.selectExpensesDetails(param);
        return R.success(list);
    }

    /**
     * 查询班次还原收入明细
     *
     * @param param 查询参数
     * @return 还原收入明细数据
     */
    public R<List<EaShiftRevenueDetail>> selectRestoreIncomesDetails(EaShiftRevenueDetail param) {
        List<EaShiftRevenueDetail> list = shiftRevenueStatisticsMapper.selectRestoreIncomesDetails(param);
        return R.success(list);
    }

    /**
     * 查询班次毛利明细
     *
     * @param param 查询参数
     * @return 毛利明细数据
     */
    public R<Map<String, Object>> selectGrossProfitDetails(EaShiftGrossProfitDetail param) {
        List<EaShiftGrossProfitDetail> list = shiftRevenueStatisticsMapper.selectGrossProfitDetails(param);
        //统计收入总计
        BigDecimal totalIncome = list.stream().map(EaShiftGrossProfitDetail::getRevenue).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //统计支出总计
        BigDecimal totalExpense = list.stream().map(EaShiftGrossProfitDetail::getExpenses).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //统计毛利总计
        BigDecimal totalGrossProfit = totalIncome.subtract(totalExpense);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("totalIncome", totalIncome);
        resultMap.put("totalExpense", totalExpense);
        resultMap.put("totalGrossProfit", totalGrossProfit);
        resultMap.put("list", list);
        return R.success(resultMap);
    }

    /**
     * 根据班次号、平台编码和客户编码查询箱号列表
     *
     * @param param 查询参数
     * @return 箱号列表
     */
    public R<List<String>> selectContainerNumbers(Map<String, Object> param) {
        List<String> list = shiftRevenueStatisticsMapper.selectContainerNumbers(param);
        return R.success(list);
    }


    /**
     * 查询费用明细
     *
     * @param eaFeeDTO 查询参数
     * @return R<Map < String, Object>>
     * <AUTHOR>
     * @since 2025/7/30 16:15
     **/
    public R<Map<String, Object>> selectFeeDetails(EaFeeDTO eaFeeDTO) {
        List<EaFee> list = eaFeeMapper.selectEaFeeListByLike(eaFeeDTO);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("list", list);
        if (CollUtil.isEmpty(list)) {
            resultMap.put("total", 0);
        } else {
            BigDecimal totalAmount = list.stream().map(EaFee::getLocalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            resultMap.put("total", totalAmount);
        }
        return R.success(resultMap);
    }


    /**
     * 箱维度毛利总计
     *
     * @param param 查询参数
     * @return R<EaShiftGrossProfitDetail>
     * <AUTHOR>
     * @since 2025/7/30 17:31
     **/
    public R<EaShiftGrossProfitDetail> grossProfitContainerList(EaShiftGrossProfitDetail param) {
        List<EaShiftGrossProfitDetail> list = shiftRevenueStatisticsMapper.selectGrossProfitDetails(param);
        EaShiftGrossProfitDetail grossProfitTotal = new EaShiftGrossProfitDetail();
        if (CollUtil.isNotEmpty(list)) {
            grossProfitTotal = list.get(0);
            //查询支出费用总计
            EaFeeDTO eaFeeDTO = new EaFeeDTO();
            eaFeeDTO.setShiftNo(param.getShiftNo());
            eaFeeDTO.setPayerCode(param.getPlatformCode());
            eaFeeDTO.setContainerNumber(param.getContainerNumber());
            List<EaFee> expensesList = eaFeeMapper.selectEaFeeListByLike(eaFeeDTO);
            grossProfitTotal.setExpensesDetails(expensesList);

            //查询收入费用总计
            eaFeeDTO.setPayerCode(param.getPlatformCode());
            List<EaFee> revenueList = eaFeeMapper.selectEaFeeListByLike(eaFeeDTO);
            grossProfitTotal.setRevenueDetails(revenueList);
        }

        return R.success(grossProfitTotal);
    }
}
