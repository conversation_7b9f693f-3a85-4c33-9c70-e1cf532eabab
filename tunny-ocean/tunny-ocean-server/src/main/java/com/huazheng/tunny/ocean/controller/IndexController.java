package com.huazheng.tunny.ocean.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.KvDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.vo.IndexVO;
import com.huazheng.tunny.ocean.mapper.MonthPlanHeaderCityToProMapper;
import com.huazheng.tunny.ocean.service.*;
import com.huazheng.tunny.ocean.util.PermissionUtil;
import com.huazheng.tunny.ocean.util.XAxisUtil;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 首页
 *
 * <AUTHOR>
 * @create 2021-09-08 10:09
 **/
@RestController
@RequestMapping("/indexController")
@Slf4j
public class IndexController {
    @Autowired
    private WaybillHeaderService waybillHeaderService;
    @Autowired
    private FdCosdetailService fdCosdetailService;
    @Autowired
    private FdShippingAccoundetailService fdShippingAccoundetailService;
    @Autowired
    private FdShippingAccountService fdShippingAccountService;
    @Autowired
    private MonthPlanHeaderCityToProService monthPlanHeaderCityToProService;
    @Autowired
    private FdInvoiceApplicationService fdInvoiceApplicationService;
    @Autowired
    private FdBillService fdBillService;
    @Autowired
    private PermissionUtil permissionUtil;
    @Autowired
    private ShifmanagementService shifmanagementService;
    @Autowired
    private BookingRequesheaderService bookingRequesheaderService;
    @Autowired
    private CustomerPlatformInfoService customerPlatformInfoService;
    @Autowired
    private BillBalanceMainCityService billBalanceMainCityService;
    @Autowired
    private FdBusCostService fdBusCostService;

    @Value("${roles.provincePlan}")
    private String provincePlan;
    @Value("${roles.provinceCu}")
    private String provinceCu;
    @Value("${roles.provinceFin}")
    private String provinceFin;
    @Value("${roles.provinceFinance}")
    private String provinceFinance;

    /**
     * 查询首页信息
     *
     * @param indexVO
     * @return
     */
    @GetMapping("/getIndexInfo")
    public R getIndexInfo(IndexVO indexVO) {
        HashMap<String, Object> map = new HashMap<>();
        /*SecruityUser userInfo = SecurityUtils.getUserInfo();
        if("0".equals(userInfo.getPlatformLevel())){
            indexVO.setCustomerCode(userInfo.getPlatformCode());
        }else if("1".equals(userInfo.getPlatformLevel())){
            indexVO.setPlatformCode(userInfo.getPlatformCode());
        }*/
        //累计订舱数量
        String bookedSum = waybillHeaderService.selectBookedNum2(indexVO);
        map.put("bookedSum", StrUtil.isNotEmpty(bookedSum) ? bookedSum : "0");

        //累计支付运费
        String payedAmount = fdCosdetailService.selectPayedAmount2(indexVO);
        map.put("payedAmount", StrUtil.isNotEmpty(payedAmount) ? payedAmount : "0");

        Integer unpaidCount = fdCosdetailService.selectNotPayedCount(indexVO);
        map.put("unpaidCount", unpaidCount != null ? unpaidCount : 0);

        /*String customerCodes = permissionUtil.getPermissonCustomer(indexVO.getCustomerCode(), indexVO.getCustomerFlag());
        if (StrUtil.isNotEmpty(customerCodes)) {

            //平台权限
            *//*if ("1".equals(indexVO.getCustomerFlag())){
                indexVO.setPlatformCode(indexVO.getCustomerCode());
            }*//*
            //若登录账号为省平台，则取数从台账表中取
            *//*if ("2".equals(indexVO.getCustomerFlag())){
                FdShippingAccoundetail accoundetail = fdShippingAccoundetailService.selectProvinceIndexInfo(indexVO.getCustomerCode());
                map.put("bookedSum", StrUtil.isNotEmpty(accoundetail.getResveredField07()) ? accoundetail.getResveredField07() : "0");
                map.put("payedAmount", accoundetail.getShippingFreight() != null ? accoundetail.getShippingFreight() : "0");
                //待确认台账数量
                EntityWrapper<FdShippingAccount> accountWrapper = new EntityWrapper<>();
                accountWrapper.eq("customer_no", indexVO.getCustomerCode());
                accountWrapper.eq("status", "1");
                accountWrapper.eq("delete_flag", "N");
                int i = fdShippingAccountService.selectCount(accountWrapper);
                map.put("unpaidCount", i);
                return new R(200, true, map, "获取首页信息成功");
            }*//*


//            indexVO.setCustomerCode(customerCodes);
            if (StrUtil.isNotEmpty(indexVO.getCustomerFlag())&&"2".equals(indexVO.getCustomerFlag())){
                indexVO.setCustomerCode(null);
            }
            //累计订舱数量
            String bookedSum = waybillHeaderService.selectBookedNum2(indexVO);
            map.put("bookedSum", StrUtil.isNotEmpty(bookedSum) ? bookedSum : "0");

            //累计支付运费
            String payedAmount = fdCosdetailService.selectPayedAmount2(indexVO);
            map.put("payedAmount", StrUtil.isNotEmpty(payedAmount) ? payedAmount : "0");

            //待支付运单数量
            *//*EntityWrapper<WaybillHeader> headerWrapper = new EntityWrapper<>();
            headerWrapper.eq("bill_status", "2");
            headerWrapper.eq("delete_flag", "N");
            headerWrapper.in("customer_no", customerCodes);
            if (StrUtil.isNotEmpty(indexVO.getPlatformCode())) {
                headerWrapper.in("platform_code", indexVO.getPlatformCode());
            }
            int unpaidCount = waybillHeaderService.selectCount(headerWrapper);*//*
            Integer unpaidCount = fdCosdetailService.selectNotPayedCount(indexVO);
            map.put("unpaidCount", unpaidCount);

        }*/

        return new R(200, true, map, "获取首页信息成功");
    }


    /**
     * 订舱数量统计图
     *
     * @param indexVO
     * @return
     */
    @GetMapping("/getBookedCount")
    public R getBookedCount(IndexVO indexVO) throws ParseException {
        HashMap<String, Object> map = new HashMap<>();
        List<String> customerCodes = permissionUtil.getPermissonCustomerList(indexVO.getCustomerCode(), indexVO.getCustomerFlag());
        if (CollUtil.isNotEmpty(customerCodes)) {
            //平台权限
            if ("1".equals(indexVO.getCustomerFlag())){
                indexVO.setPlatformCode(indexVO.getCustomerCode());
                indexVO.setCustomerCodeList(customerCodes);
            }
            if ("2".equals(indexVO.getCustomerFlag())){
                String pcPermisson = permissionUtil.getPcPermisson(indexVO.getCustomerCode(), "1");
                if(StrUtil.isNotBlank(pcPermisson)){
                    pcPermisson = pcPermisson.replace(",", "_");
                }
                indexVO.setPlatformCode(pcPermisson);

            }
            List<KvDTO> kvdtos = waybillHeaderService.selectBookedNumByMonth(indexVO);
            String dateStr = indexVO.getDateSearch() + "-01";
            DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(format.parse(dateStr));
            List<String> xaxis = XAxisUtil.getNumericalAxis(calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            ArrayList<Double> dataList = new ArrayList<>();
            for (String date : xaxis) {
                double data = 0;
                for (KvDTO dto : kvdtos) {
                    if (date.equals(dto.getKeyString())) {
                        data = Double.parseDouble(dto.getValueString());
                        break;
                    }
                }
                dataList.add(data);
            }
            map.put("xAxis", xaxis);
            map.put("data", dataList);
        }

        return new R(200, true, map, "获取订舱数量统计图成功");
    }


    /**
     * 待办统计接口----省平台
     * @param indexVO
     * @return
     */
    @GetMapping("/getStatisticsCommissionSheng")
    public R getStatisticsCommissionSheng(IndexVO indexVO){
        Map<String,Object> map=new HashMap<>();
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        List<String> roles = SecurityUtils.getRoles();
        //台账确认
        FdShippingAccount fdShippingAccount = new FdShippingAccount();
        fdShippingAccount.setPlatformFlag(indexVO.getCustomerFlag());
        fdShippingAccount.setCustomerNo(userInfo.getPlatformCode());
        fdShippingAccount.setStatus("1");
        map.put("tzqr", ifnull(fdShippingAccountService.selectFdShippingAccountListByLikeCount(fdShippingAccount)));

        //开票申请
        FdInvoiceApplication fdInvoiceApplication = new FdInvoiceApplication();
        fdInvoiceApplication.setStatus("1");
        fdInvoiceApplication.setPlatformFlag("1");
        fdInvoiceApplication.setPlatformCode(userInfo.getPlatformCode());
        map.put("kpsq", ifnull(fdInvoiceApplicationService.selectFdInvoiceApplicationListByLikeCount(fdInvoiceApplication)));

        map.put("ysjs","");
        map.put("yfjs","");
                //结算
        if(roles.size()>0){
            for (String role: roles) {
                //计划岗
                if(provincePlan.equals(role) ) {
                    BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
                    billBalanceMainCity.setBalanceStatus("WAITCHECK");
                    billBalanceMainCity.setPageType(0);
                    map.put("ysjs", ifnull(billBalanceMainCityService.selectBillBalanceMainCityCount(billBalanceMainCity)));

                    BillBalanceMainCity billBalanceMainCity2 = new BillBalanceMainCity();
                    billBalanceMainCity2.setBalanceStatus("NOTSUB");
                    billBalanceMainCity2.setPageType(1);
                    map.put("yfjs", ifnull(billBalanceMainCityService.selectBillBalanceMainCityCount(billBalanceMainCity2)));
                }//客服岗
                if(provinceCu.equals(role) ) {
                    BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
                    billBalanceMainCity.setAntiBalanceStatus("VERIFIED");
                    billBalanceMainCity.setPageType(0);
                    map.put("ysjs", ifnull(billBalanceMainCityService.selectBillBalanceMainCityCount(billBalanceMainCity)));

                    BillBalanceMainCity billBalanceMainCity2 = new BillBalanceMainCity();
                    billBalanceMainCity2.setBalanceStatus("WAITCHECK");
                    billBalanceMainCity2.setPageType(1);
                    map.put("yfjs", ifnull(billBalanceMainCityService.selectBillBalanceMainCityCount(billBalanceMainCity2)));
                }
                //财务岗
                if(provinceFin.equals(role) || provinceFinance.equals(role)){
                    BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
                    billBalanceMainCity.setAntiBalanceStatus("VERIFIED");
                    billBalanceMainCity.setPageType(0);
                    map.put("ysjs", ifnull(billBalanceMainCityService.selectBillBalanceMainCityCount(billBalanceMainCity)));

                    BillBalanceMainCity billBalanceMainCity2 = new BillBalanceMainCity();
                    billBalanceMainCity.setAntiBalanceStatus("VERIFIED");
                    billBalanceMainCity2.setPageType(1);
                    map.put("yfjs", ifnull(billBalanceMainCityService.selectBillBalanceMainCityCount(billBalanceMainCity2)));
                }
            }
        }
        return new R(200, true, map,"获取待办信息");
    }

    /**
     * 待办统计接口----市平台
     * @param indexVO
     * @return
     */
    @GetMapping("/getStatisticsCommissionShi")
    public R getStatisticsCommissionShi(IndexVO indexVO) {
        Map<String,Object> map=new HashMap<>();
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        List<String> roles = SecurityUtils.getRoles();

        //班次发布
        Shifmanagement shifmanagement = new Shifmanagement();
        shifmanagement.setReleaseStatus("0");
        shifmanagement.setPlatformCode(userInfo.getPlatformCode());
        map.put("bcfb", ifnull(shifmanagementService.selectShifmanagementListByLikeCount(shifmanagement)));

        //订舱审核
        BookingRequesheader bookingRequesheader = new BookingRequesheader();
        bookingRequesheader.setDocumentStatus("1");
        bookingRequesheader.setResveredField01("1");
        bookingRequesheader.setResveredField02(userInfo.getPlatformCode());
        map.put("dcsh", ifnull(bookingRequesheaderService.selectBookingRequesheaderListByLikeCount(bookingRequesheader)));

        //资料审核
        WaybillHeader waybillHeader = new WaybillHeader();
        //目前查询的是待审核的数据
        waybillHeader.setBillStatus("1");
        //暂不确定值是否正确
        waybillHeader.setPlatformCode(userInfo.getPlatformCode());
        map.put("zlsh", ifnull(waybillHeaderService.selectWaybillHeaderListByLikeCount(waybillHeader)));

        //业务流程单
        map.put("ywlcd", "");
        if(roles.size()>0) {
            for (String role:roles) {
                if(role.contains("DepartmentManager") ||role.contains("BusinessAccounting")) {
                    FdBusCost sel  = new FdBusCost();
                    sel.setPlatformCode(userInfo.getPlatformCode());
                    sel.setNextAuditCode(userInfo.getUserName());
                    sel.setAuditStatus("1");
                    List<FdBusCost> list = fdBusCostService.selectFdBusCostListsByLike(sel);
                    map.put("ywlcd", ifnull(list.size()));
                }
            }
        }

        //市平台应收账单结算除已核销之外的
        BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
        billBalanceMainCity.setAntiBalanceStatus("VERIFIED");
        billBalanceMainCity.setPageType(0);
        map.put("yszdjs", ifnull(billBalanceMainCityService.selectBillBalanceMainCityCount(billBalanceMainCity)));

        //台账管理
        FdShippingAccount fdShippingAccount = new FdShippingAccount();
        fdShippingAccount.setPlatformCode(userInfo.getPlatformCode());
        fdShippingAccount.setPlatformFlag("0");
        fdShippingAccount.setStatusList("0,3");
        map.put("tzgl", ifnull(fdShippingAccountService.selectFdShippingAccountListByLikeCount(fdShippingAccount)));

        //市平台应付账单结算除已核销之外的
        BillBalanceMainCity billBalanceMainCityTwo = new BillBalanceMainCity();
        billBalanceMainCityTwo.setAntiBalanceStatus("VERIFIED");
        billBalanceMainCityTwo.setPageType(1);
        map.put("yfzdjs", ifnull(billBalanceMainCityService.selectBillBalanceMainCityCount(billBalanceMainCityTwo)));

        return new R(200, true, map,"获取待办信息");
    }



    /**
     * 待办统计接口----订舱平台
     * @param indexVO
     * @return
     */
    @GetMapping("/getStatisticsCommissionDc")
    public R getStatisticsCommissionDc(IndexVO indexVO) {
        Map<String,Object> map=new HashMap<>();
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        List<String> roles = SecurityUtils.getRoles();
        //根据订舱平台查询市平台编码
        CustomerPlatformInfo customerPlatformInfo=new CustomerPlatformInfo();
        customerPlatformInfo.setCustomerCode(userInfo.getUserName());
//        CustomerPlatformInfo platformInfo = customerPlatformInfoService.selectPlatformCodeByCustomerCode(customerPlatformInfo);
//        if(platformInfo!=null) {

            //补传资料
            WaybillHeader waybillHeader = new WaybillHeader();
            waybillHeader.setBillStatus("0");
            waybillHeader.setCustomerNo(userInfo.getPlatformCode());
            //暂不确定
//            waybillHeader.setPlatformCode(indexVO.getPlatformCode());
            map.put("bczl", ifnull(waybillHeaderService.selectWaybillHeaderListByLikeCount(waybillHeader)));

        //市平台应付账单结算除已核销之外的
        BillBalanceMainCity billBalanceMainCityTwo = new BillBalanceMainCity();
        billBalanceMainCityTwo.setAntiBalanceStatus("VERIFIED");
        billBalanceMainCityTwo.setPageType(1);
        map.put("zdjs", ifnull(billBalanceMainCityService.selectBillBalanceMainCityCount(billBalanceMainCityTwo)));
        return new R(200, true, map,"获取待办信息");
    }

    public Object ifnull(Integer integer){
        if(integer==0){
            return "";
        }
        return integer;
    }

}
