package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FiPledgeIncreaseGoodsMapper;
import com.huazheng.tunny.ocean.api.entity.FiPledgeIncreaseGoods;
import com.huazheng.tunny.ocean.service.FiPledgeIncreaseGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fiPledgeIncreaseGoodsService")
public class FiPledgeIncreaseGoodsServiceImpl extends ServiceImpl<FiPledgeIncreaseGoodsMapper, FiPledgeIncreaseGoods> implements FiPledgeIncreaseGoodsService {

    @Autowired
    private FiPledgeIncreaseGoodsMapper fiPledgeIncreaseGoodsMapper;

    public FiPledgeIncreaseGoodsMapper getFiPledgeIncreaseGoodsMapper() {
        return fiPledgeIncreaseGoodsMapper;
    }

    public void setFiPledgeIncreaseGoodsMapper(FiPledgeIncreaseGoodsMapper fiPledgeIncreaseGoodsMapper) {
        this.fiPledgeIncreaseGoodsMapper = fiPledgeIncreaseGoodsMapper;
    }

    /**
     * 查询仓单质押补充货物表信息
     *
     * @param rowId 仓单质押补充货物表ID
     * @return 仓单质押补充货物表信息
     */
    @Override
    public FiPledgeIncreaseGoods selectFiPledgeIncreaseGoodsById(String rowId)
    {
        return fiPledgeIncreaseGoodsMapper.selectFiPledgeIncreaseGoodsById(rowId);
    }

    /**
     * 查询仓单质押补充货物表列表
     *
     * @param fiPledgeIncreaseGoods 仓单质押补充货物表信息
     * @return 仓单质押补充货物表集合
     */
    @Override
    public List<FiPledgeIncreaseGoods> selectFiPledgeIncreaseGoodsList(FiPledgeIncreaseGoods fiPledgeIncreaseGoods)
    {
        return fiPledgeIncreaseGoodsMapper.selectFiPledgeIncreaseGoodsList(fiPledgeIncreaseGoods);
    }


    /**
     * 分页模糊查询仓单质押补充货物表列表
     * @return 仓单质押补充货物表集合
     */
    @Override
    public Page selectFiPledgeIncreaseGoodsListByLike(Query query)
    {
        FiPledgeIncreaseGoods fiPledgeIncreaseGoods =  BeanUtil.mapToBean(query.getCondition(), FiPledgeIncreaseGoods.class,false);
        query.setRecords(fiPledgeIncreaseGoodsMapper.selectFiPledgeIncreaseGoodsListByLike(query,fiPledgeIncreaseGoods));
        return query;
    }

    /**
     * 新增仓单质押补充货物表
     *
     * @param fiPledgeIncreaseGoods 仓单质押补充货物表信息
     * @return 结果
     */
    @Override
    public int insertFiPledgeIncreaseGoods(FiPledgeIncreaseGoods fiPledgeIncreaseGoods)
    {
        return fiPledgeIncreaseGoodsMapper.insertFiPledgeIncreaseGoods(fiPledgeIncreaseGoods);
    }

    /**
     * 修改仓单质押补充货物表
     *
     * @param fiPledgeIncreaseGoods 仓单质押补充货物表信息
     * @return 结果
     */
    @Override
    public int updateFiPledgeIncreaseGoods(FiPledgeIncreaseGoods fiPledgeIncreaseGoods)
    {
        return fiPledgeIncreaseGoodsMapper.updateFiPledgeIncreaseGoods(fiPledgeIncreaseGoods);
    }


    /**
     * 删除仓单质押补充货物表
     *
     * @param rowId 仓单质押补充货物表ID
     * @return 结果
     */
    @Override
    public int deleteFiPledgeIncreaseGoodsById(String rowId)
    {
        return fiPledgeIncreaseGoodsMapper.deleteFiPledgeIncreaseGoodsById( rowId);
    };


    /**
     * 批量删除仓单质押补充货物表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiPledgeIncreaseGoodsByIds(Integer[] rowIds)
    {
        return fiPledgeIncreaseGoodsMapper.deleteFiPledgeIncreaseGoodsByIds( rowIds);
    }

}
