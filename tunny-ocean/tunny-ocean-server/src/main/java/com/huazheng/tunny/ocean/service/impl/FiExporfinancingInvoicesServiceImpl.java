package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FiExporfinancingInvoicesMapper;
import com.huazheng.tunny.ocean.api.entity.FiExporfinancingInvoices;
import com.huazheng.tunny.ocean.service.FiExporfinancingInvoicesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fiExporfinancingInvoicesService")
public class FiExporfinancingInvoicesServiceImpl extends ServiceImpl<FiExporfinancingInvoicesMapper, FiExporfinancingInvoices> implements FiExporfinancingInvoicesService {

    @Autowired
    private FiExporfinancingInvoicesMapper fiExporfinancingInvoicesMapper;

    public FiExporfinancingInvoicesMapper getFiExporfinancingInvoicesMapper() {
        return fiExporfinancingInvoicesMapper;
    }

    public void setFiExporfinancingInvoicesMapper(FiExporfinancingInvoicesMapper fiExporfinancingInvoicesMapper) {
        this.fiExporfinancingInvoicesMapper = fiExporfinancingInvoicesMapper;
    }

    /**
     * 查询出口融资发票主表信息
     *
     * @param rowId 出口融资发票主表ID
     * @return 出口融资发票主表信息
     */
    @Override
    public FiExporfinancingInvoices selectFiExporfinancingInvoicesById(String rowId)
    {
        return fiExporfinancingInvoicesMapper.selectFiExporfinancingInvoicesById(rowId);
    }

    /**
     * 查询出口融资发票主表列表
     *
     * @param fiExporfinancingInvoices 出口融资发票主表信息
     * @return 出口融资发票主表集合
     */
    @Override
    public List<FiExporfinancingInvoices> selectFiExporfinancingInvoicesList(FiExporfinancingInvoices fiExporfinancingInvoices)
    {
        return fiExporfinancingInvoicesMapper.selectFiExporfinancingInvoicesList(fiExporfinancingInvoices);
    }


    /**
     * 分页模糊查询出口融资发票主表列表
     * @return 出口融资发票主表集合
     */
    @Override
    public Page selectFiExporfinancingInvoicesListByLike(Query query)
    {
        FiExporfinancingInvoices fiExporfinancingInvoices =  BeanUtil.mapToBean(query.getCondition(), FiExporfinancingInvoices.class,false);
        query.setRecords(fiExporfinancingInvoicesMapper.selectFiExporfinancingInvoicesListByLike(query,fiExporfinancingInvoices));
        return query;
    }

    /**
     * 新增出口融资发票主表
     *
     * @param fiExporfinancingInvoices 出口融资发票主表信息
     * @return 结果
     */
    @Override
    public int insertFiExporfinancingInvoices(FiExporfinancingInvoices fiExporfinancingInvoices)
    {
        return fiExporfinancingInvoicesMapper.insertFiExporfinancingInvoices(fiExporfinancingInvoices);
    }

    /**
     * 修改出口融资发票主表
     *
     * @param fiExporfinancingInvoices 出口融资发票主表信息
     * @return 结果
     */
    @Override
    public int updateFiExporfinancingInvoices(FiExporfinancingInvoices fiExporfinancingInvoices)
    {
        return fiExporfinancingInvoicesMapper.updateFiExporfinancingInvoices(fiExporfinancingInvoices);
    }


    /**
     * 删除出口融资发票主表
     *
     * @param rowId 出口融资发票主表ID
     * @return 结果
     */
    public int deleteFiExporfinancingInvoicesById(String rowId)
    {
        return fiExporfinancingInvoicesMapper.deleteFiExporfinancingInvoicesById( rowId);
    };


    /**
     * 批量删除出口融资发票主表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiExporfinancingInvoicesByIds(Integer[] rowIds)
    {
        return fiExporfinancingInvoicesMapper.deleteFiExporfinancingInvoicesByIds( rowIds);
    }

}
