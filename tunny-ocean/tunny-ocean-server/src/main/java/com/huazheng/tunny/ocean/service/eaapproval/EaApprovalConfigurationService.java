package com.huazheng.tunny.ocean.service.eaapproval;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.eaapproval.EaApprovalConfiguration;

import java.util.List;

/**
 * 审批配置表 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2025-06-17 10:02:12
 */
public interface EaApprovalConfigurationService extends IService<EaApprovalConfiguration> {
    /**
     * 查询审批配置表信息
     *
     * @param id 审批配置表ID
     * @return 审批配置表信息
     */
    public EaApprovalConfiguration selectEaApprovalConfigurationById(Long id);

    /**
     * 查询审批配置表列表
     *
     * @param eaApprovalConfiguration 审批配置表信息
     * @return 审批配置表集合
     */
    public List<EaApprovalConfiguration> selectEaApprovalConfigurationList(EaApprovalConfiguration eaApprovalConfiguration);


    /**
     * 分页模糊查询审批配置表列表
     * @return 审批配置表集合
     */
    public Page selectEaApprovalConfigurationListByLike(Query query);



    /**
     * 新增审批配置表
     *
     * @param eaApprovalConfiguration 审批配置表信息
     * @return 结果
     */
    public int insertEaApprovalConfiguration(EaApprovalConfiguration eaApprovalConfiguration);

    /**
     * 修改审批配置表
     *
     * @param eaApprovalConfiguration 审批配置表信息
     * @return 结果
     */
    public int updateEaApprovalConfiguration(EaApprovalConfiguration eaApprovalConfiguration);

    /**
     * 删除审批配置表
     *
     * @param id 审批配置表ID
     * @return 结果
     */
    public int deleteEaApprovalConfigurationById(Long id);

    /**
     * 批量删除审批配置表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteEaApprovalConfigurationByIds(Integer[] ids);

    List<EaApprovalConfiguration> selectEaApprovalConfigurationListByDefkey(String key);

}

