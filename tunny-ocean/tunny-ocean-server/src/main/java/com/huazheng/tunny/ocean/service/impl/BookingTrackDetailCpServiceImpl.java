package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.BookingTrackDetailCpMapper;
import com.huazheng.tunny.ocean.api.entity.BookingTrackDetailCp;
import com.huazheng.tunny.ocean.service.BookingTrackDetailCpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("bookingTrackDetailCpService")
public class BookingTrackDetailCpServiceImpl extends ServiceImpl<BookingTrackDetailCpMapper, BookingTrackDetailCp> implements BookingTrackDetailCpService {

    @Autowired
    private BookingTrackDetailCpMapper bookingTrackDetailCpMapper;

    public BookingTrackDetailCpMapper getBookingTrackDetailCpMapper() {
        return bookingTrackDetailCpMapper;
    }

    public void setBookingTrackDetailCpMapper(BookingTrackDetailCpMapper bookingTrackDetailCpMapper) {
        this.bookingTrackDetailCpMapper = bookingTrackDetailCpMapper;
    }

    /**
     * 查询运踪信息子表(市平台-省)信息
     *
     * @param rowId 运踪信息子表(市平台-省)ID
     * @return 运踪信息子表(市平台-省)信息
     */
    @Override
    public BookingTrackDetailCp selectBookingTrackDetailCpById(String rowId)
    {
        return bookingTrackDetailCpMapper.selectBookingTrackDetailCpById(rowId);
    }

    /**
     * 查询运踪信息子表(市平台-省)列表
     *
     * @param bookingTrackDetailCp 运踪信息子表(市平台-省)信息
     * @return 运踪信息子表(市平台-省)集合
     */
    @Override
    public List<BookingTrackDetailCp> selectBookingTrackDetailCpList(BookingTrackDetailCp bookingTrackDetailCp)
    {
        return bookingTrackDetailCpMapper.selectBookingTrackDetailCpList(bookingTrackDetailCp);
    }


    /**
     * 分页模糊查询运踪信息子表(市平台-省)列表
     * @return 运踪信息子表(市平台-省)集合
     */
    @Override
    public Page selectBookingTrackDetailCpListByLike(Query query)
    {
        BookingTrackDetailCp bookingTrackDetailCp =  BeanUtil.mapToBean(query.getCondition(), BookingTrackDetailCp.class,false);
        Integer c= bookingTrackDetailCpMapper.queryCount(bookingTrackDetailCp);
        if(c!=null&&c!=0){
            query.setTotal(c);
            query.setRecords(bookingTrackDetailCpMapper.selectBookingTrackDetailCpListByLike(query, bookingTrackDetailCp));
        }
        return query;
    }

    /**
     * 新增运踪信息子表(市平台-省)
     *
     * @param bookingTrackDetailCp 运踪信息子表(市平台-省)信息
     * @return 结果
     */
    @Override
    public int insertBookingTrackDetailCp(BookingTrackDetailCp bookingTrackDetailCp)
    {
        return bookingTrackDetailCpMapper.insertBookingTrackDetailCp(bookingTrackDetailCp);
    }

    /**
     * 修改运踪信息子表(市平台-省)
     *
     * @param bookingTrackDetailCp 运踪信息子表(市平台-省)信息
     * @return 结果
     */
    @Override
    public int updateBookingTrackDetailCp(BookingTrackDetailCp bookingTrackDetailCp)
    {
        return bookingTrackDetailCpMapper.updateBookingTrackDetailCp(bookingTrackDetailCp);
    }


    /**
     * 删除运踪信息子表(市平台-省)
     *
     * @param rowId 运踪信息子表(市平台-省)ID
     * @return 结果
     */
    @Override
    public int deleteBookingTrackDetailCpById(String rowId)
    {
        return bookingTrackDetailCpMapper.deleteBookingTrackDetailCpById( rowId);
    };


    /**
     * 批量删除运踪信息子表(市平台-省)对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBookingTrackDetailCpByIds(Integer[] rowIds)
    {
        return bookingTrackDetailCpMapper.deleteBookingTrackDetailCpByIds( rowIds);
    }

}
