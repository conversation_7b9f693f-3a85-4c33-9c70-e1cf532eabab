package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.FiLoanRecord;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.text.ParseException;
import java.util.List;

/**
 * 企业信用贷-贷款记录 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-03-02 15:12:05
 */
public interface FiLoanRecordService extends IService<FiLoanRecord> {
    /**
     * 查询企业信用贷-贷款记录信息
     *
     * @param rowId 企业信用贷-贷款记录ID
     * @return 企业信用贷-贷款记录信息
     */
    public FiLoanRecord selectFiLoanRecordById(String rowId);

    /**
     * 查询企业信用贷-贷款记录列表
     *
     * @param fiLoanRecord 企业信用贷-贷款记录信息
     * @return 企业信用贷-贷款记录集合
     */
    public List<FiLoanRecord> selectFiLoanRecordList(FiLoanRecord fiLoanRecord);


    /**
     * 分页模糊查询企业信用贷-贷款记录列表
     * @return 企业信用贷-贷款记录集合
     */
    public Page selectFiLoanRecordListByLike(Query query);



    /**
     * 新增企业信用贷-贷款记录
     *
     * @param fiLoanRecord 企业信用贷-贷款记录信息
     * @return 结果
     */
    public int insertFiLoanRecord(FiLoanRecord fiLoanRecord);

    /**
     * 修改企业信用贷-贷款记录
     *
     * @param fiLoanRecord 企业信用贷-贷款记录信息
     * @return 结果
     */
    public int updateFiLoanRecord(FiLoanRecord fiLoanRecord);

    /**
     * 删除企业信用贷-贷款记录
     *
     * @param rowId 企业信用贷-贷款记录ID
     * @return 结果
     */
    public int deleteFiLoanRecordById(String rowId);

    /**
     * 批量删除企业信用贷-贷款记录
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiLoanRecordByIds(Integer[] rowIds);

    /**
     * 新增或修改
     */
    public R addorUpdateFiLoanRecord(FiLoanRecord fiLoanRecord);

}

