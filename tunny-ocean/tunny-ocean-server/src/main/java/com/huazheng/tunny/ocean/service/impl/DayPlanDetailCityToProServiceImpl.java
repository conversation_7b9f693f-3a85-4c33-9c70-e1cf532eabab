package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.api.entity.DayPlanApplyCityToPro;
import com.huazheng.tunny.ocean.mapper.DayPlanDetailCityToProMapper;
import com.huazheng.tunny.ocean.api.entity.DayPlanDetailCityToPro;
import com.huazheng.tunny.ocean.service.DayPlanDetailCityToProService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("dayPlanDetailCityToProService")
public class DayPlanDetailCityToProServiceImpl extends ServiceImpl<DayPlanDetailCityToProMapper, DayPlanDetailCityToPro> implements DayPlanDetailCityToProService {

    @Autowired
    private DayPlanDetailCityToProMapper dayPlanDetailCityToProMapper;

    public DayPlanDetailCityToProMapper getDayPlanDetailCityToProMapper() {
        return dayPlanDetailCityToProMapper;
    }

    public void setDayPlanDetailCityToProMapper(DayPlanDetailCityToProMapper dayPlanDetailCityToProMapper) {
        this.dayPlanDetailCityToProMapper = dayPlanDetailCityToProMapper;
    }

    /**
     * 查询旬/周计划申请子表(市平台提交到省平台)信息
     *
     * @param rowId 旬/周计划申请子表(市平台提交到省平台)ID
     * @return 旬/周计划申请子表(市平台提交到省平台)信息
     */
    @Override
    public DayPlanDetailCityToPro selectDayPlanDetailCityToProById(String rowId)
    {
        return dayPlanDetailCityToProMapper.selectDayPlanDetailCityToProById(rowId);
    }

    /**
     * 查询旬/周计划申请子表(市平台提交到省平台)列表
     *
     * @param dayPlanDetailCityToPro 旬/周计划申请子表(市平台提交到省平台)信息
     * @return 旬/周计划申请子表(市平台提交到省平台)集合
     */
    @Override
    public List<DayPlanDetailCityToPro> selectDayPlanDetailCityToProList(DayPlanDetailCityToPro dayPlanDetailCityToPro)
    {
        return dayPlanDetailCityToProMapper.selectDayPlanDetailCityToProList(dayPlanDetailCityToPro);
    }


    /**
     * 分页模糊查询旬/周计划申请子表(市平台提交到省平台)列表
     * @return 旬/周计划申请子表(市平台提交到省平台)集合
     */
    @Override
    public Page selectDayPlanDetailCityToProListByLike(Query query)
    {
        DayPlanDetailCityToPro dayPlanDetailCityToPro =  BeanUtil.mapToBean(query.getCondition(), DayPlanDetailCityToPro.class,false);
        List<DayPlanDetailCityToPro> dayPlanDetailCityToPros = dayPlanDetailCityToProMapper.selectDayPlanDetailCityToProListByLike(query, dayPlanDetailCityToPro);
        query.setRecords(dayPlanDetailCityToPros);
        query.setTotal(dayPlanDetailCityToProMapper.selectAllNo(dayPlanDetailCityToPro));
        return query;
    }

    /**
     * 新增旬/周计划申请子表(市平台提交到省平台)
     *
     * @param dayPlanDetailCityToPro 旬/周计划申请子表(市平台提交到省平台)信息
     * @return 结果
     */
    @Override
    public int insertDayPlanDetailCityToPro(DayPlanDetailCityToPro dayPlanDetailCityToPro)
    {
        return dayPlanDetailCityToProMapper.insertDayPlanDetailCityToPro(dayPlanDetailCityToPro);
    }

    /**
     * 修改旬/周计划申请子表(市平台提交到省平台)
     *
     * @param dayPlanDetailCityToPro 旬/周计划申请子表(市平台提交到省平台)信息
     * @return 结果
     */
    @Override
    public int updateDayPlanDetailCityToPro(DayPlanDetailCityToPro dayPlanDetailCityToPro)
    {
        return dayPlanDetailCityToProMapper.updateDayPlanDetailCityToPro(dayPlanDetailCityToPro);
    }

    @Override
    public int updateDayPlanDetailCityToProByNo(DayPlanDetailCityToPro dayPlanDetailCityToPro)
    {
        return dayPlanDetailCityToProMapper.updateDayPlanDetailCityToProByNo(dayPlanDetailCityToPro);
    }
    /**
     * 删除旬/周计划申请子表(市平台提交到省平台)
     *
     * @param dayPlanDetailCityToPro 旬/周计划申请子表(市平台提交到省平台)ID
     * @return 结果
     */
    public int deleteDayPlanDetailCityToProById(DayPlanDetailCityToPro dayPlanDetailCityToPro)
    {
        return dayPlanDetailCityToProMapper.deleteDayPlanDetailCityToProById( dayPlanDetailCityToPro);
    };


    /**
     * 批量删除旬/周计划申请子表(市平台提交到省平台)对象
     *
     * @return 结果
     */
    @Override
    public int deleteDayPlanDetailCityToProByIds(Integer[] rowIds)
    {
        return dayPlanDetailCityToProMapper.deleteDayPlanDetailCityToProByIds( rowIds);
    }

    @Override
    public List<DayPlanDetailCityToPro> selectListByDate(DayPlanDetailCityToPro dayPlanDetailCityToPro){
        return dayPlanDetailCityToProMapper.selectListByDate(dayPlanDetailCityToPro);
    }
}
