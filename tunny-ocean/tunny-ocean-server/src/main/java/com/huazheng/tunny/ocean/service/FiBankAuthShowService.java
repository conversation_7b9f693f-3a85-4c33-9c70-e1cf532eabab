package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FiBankAuthShow;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 经营信息授权-银行展示表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-03-02 15:09:47
 */
public interface FiBankAuthShowService extends IService<FiBankAuthShow> {
    /**
     * 查询经营信息授权-银行展示表信息
     *
     * @param rowId 经营信息授权-银行展示表ID
     * @return 经营信息授权-银行展示表信息
     */
    public FiBankAuthShow selectFiBankAuthShowById(String rowId);

    /**
     * 查询经营信息授权-银行展示表列表
     *
     * @param fiBankAuthShow 经营信息授权-银行展示表信息
     * @return 经营信息授权-银行展示表集合
     */
    public List<FiBankAuthShow> selectFiBankAuthShowList(FiBankAuthShow fiBankAuthShow);

    public List<FiBankAuthShow> selectFiBankAuthShowByEntSocialCode(FiBankAuthShow fiBankAuthShow);
    /**
     * 分页模糊查询经营信息授权-银行展示表列表
     * @return 经营信息授权-银行展示表集合
     */
    public Page selectFiBankAuthShowListByLike(Query query);

    /**
     * 分页模糊查询经营信息授权-银行展示表列表-市平台
     * @return 经营信息授权-银行展示表集合-市平台
     */
    public Page selectFiBankAuthShowListForCity(Query query);

    /**
     * 分页模糊查询经营信息授权-银行展示表列表-省平台
     * @return 经营信息授权-银行展示表集合-省平台
     */
    public Page selectFiBankAuthShowListForProvince(Query query);



    /**
     * 新增经营信息授权-银行展示表
     *
     * @param fiBankAuthShow 经营信息授权-银行展示表信息
     * @return 结果
     */
    public int insertFiBankAuthShow(FiBankAuthShow fiBankAuthShow);

    /**
     * 修改经营信息授权-银行展示表
     *
     * @param fiBankAuthShow 经营信息授权-银行展示表信息
     * @return 结果
     */
    public int updateFiBankAuthShow(FiBankAuthShow fiBankAuthShow);

    /**
     * 删除经营信息授权-银行展示表
     *
     * @param rowId 经营信息授权-银行展示表ID
     * @return 结果
     */
    public int deleteFiBankAuthShowById(String rowId);

    /**
     * 批量删除经营信息授权-银行展示表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiBankAuthShowByIds(Integer[] rowIds);

    public void cancelAuth(FiBankAuthShow fiBankAuthShow);
}

