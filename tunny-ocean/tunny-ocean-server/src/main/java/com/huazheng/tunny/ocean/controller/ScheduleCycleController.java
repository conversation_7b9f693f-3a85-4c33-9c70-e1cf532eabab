package com.huazheng.tunny.ocean.controller;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.ScheduleCycle;
import com.huazheng.tunny.ocean.service.ScheduleCycleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * @Description: 班期表
 * @Author: wx
 * @Date: 2023-05-09 15:23:07
 */
@RestController
@RequestMapping("/schedule/cycle")
public class ScheduleCycleController {

    @Autowired
    private ScheduleCycleService service;

    /**
     * @Description: 分页
     * @Param: params
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-09 15:23:07
     */
    @GetMapping("/page")
    public R page(@RequestParam Map<String, Object> params) {
        return new R<>(service.page(new Query<>(params)));
    }

    /**
     * @Description: 详情
     * @Param: id
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-09 15:23:07
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") String id) {
        return service.info(id);
    }

    /**
     * @Description: 保存
     * @Param: param
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-09 15:23:07
     */
    @PostMapping("/save")
    public R save(@RequestBody ScheduleCycle param) {
        return service.save(param);
    }

    /**
     * @Description: 修改
     * @Param: param
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-09 15:23:07
     */
    @PostMapping("/renew")
    public R update(@RequestBody ScheduleCycle param) {
        return service.update(param);
    }

    /**
     * @Description: 删除
     * @Param: rowId
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-09 15:23:07
     */
    @PostMapping("/del/{id}")
    public R delete(@PathVariable String id) {
        return service.delete(id);
    }

    /**
     * @Description: 列表
     * @Param: param
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-09 15:23:07
     */
    @GetMapping("/list")
    public R list(ScheduleCycle param) {
        return service.list(param);
    }

    /**
     * @Description: 导入班期
     * @Param:
     * @Return: com.huazheng.tunny.common.core.util.R
     * @Author: wx
     * @Date: 2023/5/12 15:09
     */
    @PostMapping("/importExcel")
    public R importExcel(ScheduleCycle param, MultipartFile file) {
        return service.importExcel(param, file);
    }

    /**
     * @Description: 导出模板
     * @Param:
     * @Return: com.huazheng.tunny.common.core.util.R
     * @Author: wx
     * @Date: 2023/5/15 13:18
     */
    @GetMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        service.exportTemplate(response);
    }

}
