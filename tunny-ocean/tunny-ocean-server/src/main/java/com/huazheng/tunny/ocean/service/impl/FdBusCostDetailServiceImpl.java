package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.FdBusCostContainerTypeDTO;
import com.huazheng.tunny.ocean.api.dto.FdBusCostDetailDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.FdBusCostDetailService;
import com.huazheng.tunny.ocean.util.CheckUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("fdBusCostDetailService")
public class FdBusCostDetailServiceImpl extends ServiceImpl<FdBusCostDetailMapper, FdBusCostDetail> implements FdBusCostDetailService {
    @Autowired
    private FdBusCostDetailMapper fdBusCostDetailMapper;
    @Autowired
    private WaybillContainerInfoMapper waybillContainerInfoMapper;
    @Autowired
    private FdBusCostMapper fdBusCostMapper;
    @Autowired
    private FdShippingAccountMapper fdShippingAccountMapper;
    @Autowired
    private SupplierInfoMapper supplierInfoMapper;
    @Autowired
    private ShifmanagementMapper shifmanagementMapper;
    @Autowired
    private SubsidyManagerMapper subsidyManagerMapper;
    /**
     * 查询业务流程单子表信息
     *
     * @param id 业务流程单子表ID
     * @return 业务流程单子表信息
     */
    @Override
    public FdBusCostDetail selectFdBusCostDetailById(Integer id) {
        return fdBusCostDetailMapper.selectFdBusCostDetailById(id);
    }

    /**
     * 查询业务流程单子表列表
     *
     * @param fdBusCostDetail 业务流程单子表信息
     * @return 业务流程单子表集合
     */
    @Override
    public List<FdBusCostDetail> selectFdBusCostDetailList(FdBusCostDetail fdBusCostDetail) {
        return fdBusCostDetailMapper.selectFdBusCostDetailList(fdBusCostDetail);
    }


    /**
     * 分页模糊查询业务流程单子表列表
     *
     * @return 业务流程单子表集合
     */
    @Override
    public Page selectFdBusCostDetailListByLike(Query query) {
        FdBusCostDetail fdBusCostDetail = BeanUtil.mapToBean(query.getCondition(), FdBusCostDetail.class, false);
        query.setRecords(fdBusCostDetailMapper.selectFdBusCostDetailListByLike(query, fdBusCostDetail));
        return query;
    }

    @Override
    public Map reveivePage(Query query) {
        Map<String, Object> map = new HashMap<>();
        FdBusCostDetail fdBusCostDetail = BeanUtil.mapToBean(query.getCondition(), FdBusCostDetail.class, false);
        fdBusCostDetail.setCostType("0");
        fdBusCostDetail.setDeleteFlag("N");
        List<FdBusCostDetailDTO> fdBusCostDetailDTOS = fdBusCostDetailMapper.selectDetailListByLike(fdBusCostDetail);
        BigDecimal totalAmount = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(fdBusCostDetailDTOS)) {
            for (FdBusCostDetailDTO fdBusCostDetailDTO : fdBusCostDetailDTOS
            ) {
                totalAmount = totalAmount.add(fdBusCostDetailDTO.getLocalAmount());
            }
            waybillInfo(fdBusCostDetail, fdBusCostDetailDTOS);
        }
        map.put("list", fdBusCostDetailDTOS);
        map.put("totalAmount", totalAmount);
        return map;
    }

    private void waybillInfo(FdBusCostDetail fdBusCostDetail, List<FdBusCostDetailDTO> fdBusCostDetailDTOS) {
        //箱信息
        List<FdBusCostDetailDTO> list = fdBusCostDetailMapper.selectWaybillList(fdBusCostDetail);
        if(CollUtil.isNotEmpty(list)){
            for (FdBusCostDetailDTO fdBusCostDetailDTO : fdBusCostDetailDTOS
            ) {
                for (FdBusCostDetailDTO waybill:list
                ) {
                    if(fdBusCostDetailDTO.getContainerNumber().equals(waybill.getContainerNumber())){
                        fdBusCostDetailDTO.setContainerTypeCode(waybill.getContainerTypeCode());
                        fdBusCostDetailDTO.setDestinationName(waybill.getDestinationName());
                        fdBusCostDetailDTO.setDestination(waybill.getDestination());
                        fdBusCostDetailDTO.setStartStationName(waybill.getStartStationName());
                        fdBusCostDetailDTO.setEndStationName(waybill.getEndStationName());
                        fdBusCostDetailDTO.setStationCompilation(waybill.getStationCompilation());
                        fdBusCostDetailDTO.setEndCompilation(waybill.getEndCompilation());
                        fdBusCostDetailDTO.setIdentification(waybill.getIdentification());
                        fdBusCostDetailDTO.setContainerOwner(waybill.getContainerOwner());
                        break;
                    }
                }
            }
        }
    }

    @Override
    public Map payPage(Query query) {
        Map<String, Object> map = new HashMap<>();
        FdBusCostDetail fdBusCostDetail = BeanUtil.mapToBean(query.getCondition(), FdBusCostDetail.class, false);
        fdBusCostDetail.setCostType("1");
        fdBusCostDetail.setDeleteFlag("N");
        List<FdBusCostDetailDTO> fdBusCostDetailDTOS = fdBusCostDetailMapper.selectDetailListByLike(fdBusCostDetail);
        BigDecimal totalAmount = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(fdBusCostDetailDTOS)) {
            for (FdBusCostDetailDTO fdBusCostDetailDTO : fdBusCostDetailDTOS
            ) {
                totalAmount = totalAmount.add(fdBusCostDetailDTO.getLocalAmount());
            }
            waybillInfo(fdBusCostDetail, fdBusCostDetailDTOS);
        }
        map.put("list", fdBusCostDetailDTOS);
        map.put("totalAmount", totalAmount);
        return map;
    }

    /**
     * 新增业务流程单子表
     *
     * @param fdBusCostDetail 业务流程单子表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertFdBusCostDetail(FdBusCostDetail fdBusCostDetail) {
        fdBusCostDetail.setExchangeRateNew(fdBusCostDetail.getExchangeRate());
        return fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveReceive(FdBusCostDetail fdBusCostDetail) {
        boolean b = CheckUtil.verifyCntrCode(fdBusCostDetail.getContainerNumber());
        if (!b) {
            return new R<>(new Throwable("箱号格式错误！"));
        }
        FdBusCost sel = new FdBusCost();
        sel.setCostCode(fdBusCostDetail.getCostCode());
        sel.setDeleteFlag("N");
        List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel);
        if (CollUtil.isNotEmpty(fdBusCosts)) {
            fdBusCostDetail.setShiftNo(fdBusCosts.get(0).getShiftNo());
            fdBusCostDetail.setPayCode(fdBusCosts.get(0).getCustomerCode());
            fdBusCostDetail.setPayName(fdBusCosts.get(0).getCustomerName());
            fdBusCostDetail.setReceiveCode(fdBusCosts.get(0).getPlatformCode());
            fdBusCostDetail.setReceiveName(fdBusCosts.get(0).getPlatformName());
        }
        fdBusCostDetail.setRemark("追加费用");
        fdBusCostDetail.setExchangeRateNew(fdBusCostDetail.getExchangeRate());
        fdBusCostDetail.setCostType("0");
        fdBusCostDetail.setAuditStatus("0");
        fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
        return new R<>(0, true, null, "操作成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveReceiveList(FdBusCostDetail fdBusCostDetail) {
        FdBusCost sel = new FdBusCost();
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        if (CollUtil.isNotEmpty(fdBusCostDetail.getContainerNumberList())) {
            sel.setCostCode(fdBusCostDetail.getCostCode());
            sel.setDeleteFlag("N");
            List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel);
            for (String containerNumber : fdBusCostDetail.getContainerNumberList()
            ) {
                boolean b = CheckUtil.verifyCntrCode(containerNumber);
                if (!b) {
                    return new R<>(new Throwable("箱号格式错误！"));
                }
                if (CollUtil.isNotEmpty(fdBusCosts)) {
                    fdBusCostDetail.setShiftNo(fdBusCosts.get(0).getShiftNo());
                    fdBusCostDetail.setPayCode(fdBusCosts.get(0).getCustomerCode());
                    fdBusCostDetail.setPayName(fdBusCosts.get(0).getCustomerName());
                    fdBusCostDetail.setReceiveCode(fdBusCosts.get(0).getPlatformCode());
                    fdBusCostDetail.setReceiveName(fdBusCosts.get(0).getPlatformName());
                }
                fdBusCostDetail.setRemark("追加费用");
                fdBusCostDetail.setContainerNumber(containerNumber);
                fdBusCostDetail.setExchangeRateNew(fdBusCostDetail.getExchangeRate());
                fdBusCostDetail.setCostType("0");
                fdBusCostDetail.setAuditStatus("0");
                fdBusCostDetail.setAddWho(userInfo.getUserName());
                fdBusCostDetail.setAddWhoName(userInfo.getRealName());
                fdBusCostDetail.setAddTime(LocalDateTime.now());
                fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
            }
        }
        return new R<>(0, true, null, "操作成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveList(FdBusCost fdBusCost) {
        if (CollUtil.isNotEmpty(fdBusCost.getDetailList())) {
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            for (FdBusCostDetail fdBusCostDetail : fdBusCost.getDetailList()
            ) {
                fdBusCostDetail.setAddWho(userInfo.getUserName());
                fdBusCostDetail.setAddWhoName(userInfo.getRealName());
                fdBusCostDetail.setAddTime(LocalDateTime.now());
                fdBusCostDetail.setUpdateWho(userInfo.getUserName());
                fdBusCostDetail.setUpdateWhoName(userInfo.getRealName());
                fdBusCostDetail.setUpdateTime(LocalDateTime.now());
                fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
            }
        }
        return R.success("操作成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R savePay(FdBusCostDetail fdBusCostDetail) {
        fdBusCostDetail.setCostType("1");
        fdBusCostDetail.setAuditStatus("0");
        fdBusCostDetail.setRemark("追加费用");
        fdBusCostDetail.setExchangeRateNew(fdBusCostDetail.getExchangeRate());
        FdBusCost sel = new FdBusCost();
        sel.setCostCode(fdBusCostDetail.getCostCode());
        sel.setDeleteFlag("N");
        List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel);
        if (CollUtil.isNotEmpty(fdBusCosts)) {
            fdBusCostDetail.setShiftNo(fdBusCosts.get(0).getShiftNo());
            fdBusCostDetail.setPayCode(fdBusCosts.get(0).getPlatformCode());
            fdBusCostDetail.setPayName(fdBusCosts.get(0).getPlatformName());
        }
        SupplierInfo sel4 = new SupplierInfo();
        sel4.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        sel4.setPartnerShip("normal");
        List<SupplierInfo> supplierInfos = supplierInfoMapper.getReceiveList(sel4);

        if (CollUtil.isNotEmpty(supplierInfos)) {
            for (SupplierInfo info : supplierInfos
            ) {
                if (fdBusCostDetail.getReceiveName().equals(info.getCustomerName())) {
                    fdBusCostDetail.setReceiveCode(info.getCustomerCode());
                }
            }
        }
        boolean b = CheckUtil.verifyCntrCode(fdBusCostDetail.getContainerNumber());
        if (!b) {
            return new R<>(new Throwable("箱号格式错误！"));
        }
        if (SecurityUtils.getUserInfo().getSupPlatformCode().contains(fdBusCostDetail.getReceiveCode())) {
            FdShippingAccount sel2 = new FdShippingAccount();
            sel2.setShiftNo(fdBusCostDetail.getShiftNo());
            sel2.setPlatformCode(fdBusCostDetail.getPayCode());
            sel2.setDeleteFlag("N");
            List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountList(sel2);
            if (CollUtil.isEmpty(fdShippingAccounts) || "0".equals(fdShippingAccounts.get(0).getStatus()) || "3".equals(fdShippingAccounts.get(0).getStatus())) {
                fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
                return new R<>(0, true, null, "操作成功");
            } else {
                return new R<>(new Throwable("台账已提交审核，无法追加省平台费用！"));
            }
        } else {
            fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
            return new R<>(0, true, null, "操作成功");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R savePayList(FdBusCostDetail fdBusCostDetail) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        SupplierInfo sel4 = new SupplierInfo();
        sel4.setPlatformCode(userInfo.getPlatformCode());
        sel4.setPartnerShip("normal");
        List<SupplierInfo> supplierInfos = supplierInfoMapper.getReceiveList(sel4);
        if (CollUtil.isNotEmpty(fdBusCostDetail.getContainerNumberList())) {
            FdBusCost sel = new FdBusCost();
            sel.setCostCode(fdBusCostDetail.getCostCode());
            sel.setDeleteFlag("N");
            List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel);

            FdShippingAccount sel2 = new FdShippingAccount();
            sel2.setShiftNo(fdBusCosts.get(0).getShiftNo());
            sel2.setPlatformCode(fdBusCosts.get(0).getPlatformCode());
            sel2.setDeleteFlag("N");
            List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountList(sel2);
            for (String containerNumber : fdBusCostDetail.getContainerNumberList()
            ) {
                boolean b = CheckUtil.verifyCntrCode(containerNumber);
                if (!b) {
                    return new R<>(new Throwable("箱号格式错误！"));
                }
                fdBusCostDetail.setContainerNumber(containerNumber);
                fdBusCostDetail.setCostType("1");
                fdBusCostDetail.setAuditStatus("0");
                if (CollUtil.isNotEmpty(fdBusCosts)) {
                    fdBusCostDetail.setShiftNo(fdBusCosts.get(0).getShiftNo());
                    fdBusCostDetail.setPayCode(fdBusCosts.get(0).getPlatformCode());
                    fdBusCostDetail.setPayName(fdBusCosts.get(0).getPlatformName());
                    if (CollUtil.isNotEmpty(supplierInfos)) {
                        for (SupplierInfo info : supplierInfos
                        ) {
                            if (fdBusCostDetail.getReceiveName().equals(info.getCustomerName())) {
                                fdBusCostDetail.setReceiveCode(info.getCustomerCode());
                            }
                        }
                    }
                }
                fdBusCostDetail.setRemark("追加费用");
                fdBusCostDetail.setExchangeRateNew(fdBusCostDetail.getExchangeRate());
                fdBusCostDetail.setAddWho(userInfo.getUserName());
                fdBusCostDetail.setAddWhoName(userInfo.getRealName());
                fdBusCostDetail.setAddTime(LocalDateTime.now());
                if (SecurityUtils.getUserInfo().getSupPlatformCode().contains(fdBusCostDetail.getReceiveCode())) {
                    if (CollUtil.isEmpty(fdShippingAccounts) || "0".equals(fdShippingAccounts.get(0).getStatus()) || "3".equals(fdShippingAccounts.get(0).getStatus())) {
                        fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
                    } else {
                        return new R<>(new Throwable("台账已提交审核，无法追加省平台费用！"));
                    }
                } else {
                    fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
                }
            }
        }
        return new R<>(0, true, null, "操作成功");
    }

    /**
     * 修改业务流程单子表
     *
     * @param fdBusCostDetail 业务流程单子表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateFdBusCostDetail(FdBusCostDetail fdBusCostDetail) {
        return fdBusCostDetailMapper.updateFdBusCostDetail(fdBusCostDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateReceive(FdBusCostDetail fdBusCostDetail) {
        return fdBusCostDetailMapper.updateFdBusCostDetail(fdBusCostDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updatePay(FdBusCostDetail fdBusCostDetail) {
        SupplierInfo sel4 = new SupplierInfo();
        sel4.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        sel4.setPartnerShip("normal");
        List<SupplierInfo> supplierInfos = supplierInfoMapper.getReceiveList(sel4);
        if (CollUtil.isNotEmpty(supplierInfos)) {
            for (SupplierInfo info : supplierInfos
            ) {
                if (fdBusCostDetail.getReceiveName().equals(info.getCustomerName())) {
                    fdBusCostDetail.setReceiveCode(info.getCustomerCode());
                }
            }
        }
        return fdBusCostDetailMapper.updateFdBusCostDetail(fdBusCostDetail);
    }


    /**
     * 删除业务流程单子表
     *
     * @param id 业务流程单子表ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteFdBusCostDetailById(Integer id) {
        FdBusCostDetail delObj = new FdBusCostDetail();
        delObj.setId(id);
        delObj.setDeleteFlag("Y");
        delObj.setDeleteWho(SecurityUtils.getUserInfo().getUserName());
        delObj.setDeleteWhoName(SecurityUtils.getUserInfo().getRealName());
        delObj.setDeleteTime(LocalDateTime.now());
        return fdBusCostDetailMapper.updateFdBusCostDetail(delObj);
    }

    ;


    /**
     * 批量删除业务流程单子表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R deleteFdBusCostDetailByIds(Integer[] ids) {
        if (ids != null && ids.length > 0) {
            for (Integer id : ids
            ) {
                FdBusCostDetail delObj = new FdBusCostDetail();
                delObj.setId(id);
                delObj.setDeleteFlag("Y");
                delObj.setDeleteWho(SecurityUtils.getUserInfo().getUserName());
                delObj.setDeleteWhoName(SecurityUtils.getUserInfo().getRealName());
                delObj.setDeleteTime(LocalDateTime.now());
                fdBusCostDetailMapper.updateFdBusCostDetail(delObj);
            }

        }
        return new R<>(0, Boolean.TRUE, null, "导入成功");
    }

    @Override
    public List<WaybillContainerInfo> selectListWithBusCost(FdBusCostDetail fdBusCostDetail) {
        return waybillContainerInfoMapper.selectListWithBusCost(fdBusCostDetail);
    }

    @Override
    public List<WaybillContainerInfo> selectListWithBusCostDel(FdBusCostDetail fdBusCostDetail) {
        return waybillContainerInfoMapper.selectListWithBusCostDel(fdBusCostDetail);
    }

    @Override
    public List<WaybillContainerInfo> selectContainerTypeCode(FdBusCostDetail fdBusCostDetail) {
        return waybillContainerInfoMapper.selectContainerTypeCode(fdBusCostDetail);
    }

    @Override
    public List<WaybillContainerInfo> selectEndCompilation(FdBusCostDetail fdBusCostDetail) {
        return waybillContainerInfoMapper.selectEndCompilation(fdBusCostDetail);
    }

    @Override
    public List<FdBusCostDetail> selectFdExchangeRateByCostInfo(String containerNumber, String bbCode, String ssCode, String billCode) {
        return fdBusCostDetailMapper.selectFdExchangeRateByCostInfo(containerNumber, bbCode, ssCode, billCode);
    }

    @Override
    public List<FdBusCostDetailDTO> selectFdBusCostDetailFromLower(FdBusCostDetailDTO fdBusCostDetail) {
        FdBusCost fdBusCost = fdBusCostMapper.selectFdBusCostByCostCode(fdBusCostDetail.getCostCode());
        if (fdBusCost != null) {
            fdBusCostDetail.setPayCode(fdBusCost.getCustomerCode());
        }
        fdBusCostDetail.setReceiveCode(SecurityUtils.getUserInfo().getPlatformCode());
        return fdBusCostDetailMapper.selectFdBusCostDetailFromLower(fdBusCostDetail);
    }

    @Override
    public Map<String, Object> weChatList(FdBusCostDetail fdBusCostDetail) {
        Map<String,Object> map = new HashMap<>();
        BigDecimal ysAmount = BigDecimal.ZERO;
        BigDecimal yfAmount = BigDecimal.ZERO;
        BigDecimal btqml = BigDecimal.ZERO;
        BigDecimal subsidyAmount = BigDecimal.ZERO;
        BigDecimal bthml = BigDecimal.ZERO;
        FdBusCost fdBusCost = fdBusCostMapper.selectFdBusCostByCostCode(fdBusCostDetail.getCostCode());
        if (fdBusCost != null) {
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            Shifmanagement shifmanagement = shifmanagementMapper.selectShifmanagementByPlatCodeAndShiftNo(userInfo.getPlatformCode(), fdBusCost.getShiftNo());
            Shifmanagement sel = new Shifmanagement();
            sel.setShippingLineCode(shifmanagement.getShippingLineCode());
            sel.setTrip(shifmanagement.getTrip());
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            if ("G".equals(shifmanagement.getTrip())) {
                sel.setStartTime(formatter.format(shifmanagement.getPlanShipTime()));
            } else if ("R".equals(shifmanagement.getTrip())) {
                sel.setStartTime(formatter.format(shifmanagement.getPlanShipTime()));
            }
            //应收
            FdBusCostDetail ys = new FdBusCostDetail();
            ys.setCostCode(fdBusCost.getCostCode());
            ys.setCostType("0");
            ysAmount = fdBusCostDetailMapper.getLocalAmountByCostCodeTwo(ys);
            //应付
            FdBusCostDetail yf = new FdBusCostDetail();
            yf.setCostCode(fdBusCost.getCostCode());
            yf.setCostType("1");
            yfAmount = fdBusCostDetailMapper.getLocalAmountByCostCodeTwo(yf);
            //补贴前毛利
            btqml = ysAmount.subtract(yfAmount);
            //补贴金额
            if ("1".equals(userInfo.getPlatformLevel())) {
                String supPlatformCode = userInfo.getSupPlatformCode();
                if (supPlatformCode.contains("-")) {
                    supPlatformCode = supPlatformCode.split("-")[0];
                }
                sel.setPlatformCode(supPlatformCode);
            } else {
                sel.setPlatformCode(userInfo.getPlatformCode());
            }
            List<FdBusCostContainerTypeDTO> fdBusCostContainerTypeDTOS = fdBusCostDetailMapper.weChatTotal(fdBusCost.getCostCode(),sel.getPlatformCode());
            if (StrUtil.isNotBlank(sel.getStartTime())) {
                //市补贴
                /*if (StrUtil.isNotBlank(fdBusCost.getPlatformCode())) {
                    sel.setPlatformCode(userInfo.getPlatformCode());
                    List<SubsidyManager> list = subsidyManagerMapper.selectSubsidyManagerListByShift(sel);
                    if (CollUtil.isNotEmpty(list)) {
                        for (FdBusCostContainerTypeDTO dto : fdBusCostContainerTypeDTOS
                        ) {
                            if ("20".equals(dto.getContainerType())) {
                                Double num = 0.5;
                                subsidyAmount = subsidyAmount.add(list.get(0).getSubsidyAmount().multiply(BigDecimal.valueOf(num)).multiply(BigDecimal.valueOf(dto.getNum())));
                            } else if ("40".equals(dto.getContainerType()) || "45".equals(dto.getContainerType())) {
                                subsidyAmount = subsidyAmount.add(list.get(0).getSubsidyAmount().multiply(BigDecimal.valueOf(dto.getNum())));
                            }
                        }
                    }
                }*/
                //省补贴
                List<SubsidyManager> list = subsidyManagerMapper.selectSubsidyManagerListByShift(sel);
                if (CollUtil.isNotEmpty(list)) {
                    for (FdBusCostContainerTypeDTO dto : fdBusCostContainerTypeDTOS
                    ) {
                        if ("20".equals(dto.getContainerType())) {
                            Double num = 0.5;
                            subsidyAmount = subsidyAmount.add(list.get(0).getSubsidyAmount().multiply(BigDecimal.valueOf(num)).multiply(BigDecimal.valueOf(dto.getNum())));
                        } else if ("40".equals(dto.getContainerType()) || "45".equals(dto.getContainerType())) {
                            subsidyAmount = subsidyAmount.add(list.get(0).getSubsidyAmount().multiply(BigDecimal.valueOf(dto.getNum())));
                        }
                    }
                }
            }
            //补贴后毛利
            bthml = btqml.add(subsidyAmount);
        }
        map.put("ysAmount",ysAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        map.put("yfAmount",yfAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        map.put("btqml",btqml.setScale(2, BigDecimal.ROUND_HALF_UP));
        map.put("subsidyAmount",subsidyAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        map.put("bthml",bthml.setScale(2, BigDecimal.ROUND_HALF_UP));
        return map;
    }

}
