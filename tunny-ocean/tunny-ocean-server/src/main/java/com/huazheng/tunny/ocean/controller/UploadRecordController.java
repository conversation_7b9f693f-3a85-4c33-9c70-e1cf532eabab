package com.huazheng.tunny.ocean.controller;


import com.huazheng.tunny.ocean.api.entity.UploadRecord;
import com.huazheng.tunny.ocean.service.UploadRecordService;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> code ocean
 * @date 2021-08-04 10:39:25
 */
@RestController
@RequestMapping("/uploadrecord")
@Slf4j
public class UploadRecordController {
    @Autowired
    private UploadRecordService uploadRecordService;
    @Autowired
    private SignatureController signatureController;
    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  uploadRecordService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return uploadRecordService.selectUploadRecordListByLike(new Query<>(params));
    }

    /**
     *  列表
     * @param uploadRecord
     * @return
     */
    @PostMapping("/list")
    public List list(@RequestBody UploadRecord uploadRecord) {
        //对象查询
        return uploadRecordService.selectUploadRecordList(uploadRecord);
    }


    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        UploadRecord uploadRecord =uploadRecordService.selectById(rowId);
        return new R<>(uploadRecord);
    }

    /**
     * 保存
     * @param uploadRecord
     * @return R
     */
    @PostMapping
    public R save(@RequestBody UploadRecord uploadRecord) {
        int flag= 0;
        if("1".equals(uploadRecord.getType())){
            flag=uploadRecordService.saveWaybillFile(uploadRecord);
        }else{
            flag = uploadRecordService.insertUploadRecord(uploadRecord);
        }

        if(flag!=0){
            return new R<>(Boolean.TRUE,"文件信息保存成功");
        }else{
            return new R<>(Boolean.FALSE,"文件信息保存失败");
        }
    }
    /**
     * 保存附件
     * @param uploadRecord
     * @return R
     */
    @PostMapping("/saveBookingFile")
    public R saveBookingFile(@RequestBody UploadRecord uploadRecord) {
        int flag= 0;
        if("1".equals(uploadRecord.getType())){
            flag=uploadRecordService.saveBookingFile(uploadRecord);
        }else{
            flag = uploadRecordService.insertUploadRecord(uploadRecord);
        }

        if(flag!=0){
            return new R<>(Boolean.TRUE,"文件信息保存成功");
        }else{
            return new R<>(Boolean.FALSE,"文件信息保存失败");
        }
    }

    /**
     * 共享订单附件保存
     * @param uploadRecord
     * @return R
     */
    @PostMapping("/saveWaybillFile")
    public R saveWaybillFile(@RequestBody UploadRecord uploadRecord) {
        int flag=uploadRecordService.saveWaybillFile(uploadRecord);
        if(flag!=0){
            return new R<>(Boolean.TRUE,"文件信息保存成功");
        }else{
            return new R<>(Boolean.FALSE,"文件信息保存失败");
        }
    }

    /**
     * 修改
     * @param uploadRecord
     * @return R
     */
    @PutMapping
    public R update(@RequestBody UploadRecord uploadRecord) {
        uploadRecordService.updateById(uploadRecord);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param uploadRecord
     * @return R
     */
    @PutMapping("/deleteFiles")
    public R deleteFiles(@RequestBody UploadRecord uploadRecord) {
        int flag=uploadRecordService.updateUploadRecord(uploadRecord);
        if(flag!=0){
            return new R<>(Boolean.TRUE,"文件信息删除成功");
        }else{
            return new R<>(Boolean.FALSE,"文件信息删除失败");
        }
    }

    @PostMapping("/deleteFilesForSh")
    public String deleteFilesForSh(@RequestBody UploadRecord uploadRecord) {
        int flag=uploadRecordService.deleteFilesForSh(uploadRecord);
        String content = null;
        if(flag!=0){
            content = JSONUtil.parseObj(new R<>(Boolean.TRUE,"文件信息删除成功"), false).toStringPretty();

        }else{
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE,"文件信息删除失败"), false).toStringPretty();
        }
        return signatureController.returnPostSh2("/uploadrecord/deleteFilesForSh", content);
    }

    @PostMapping("/uploadPicAndNameForSh")
    public String uploadPicAndNameForSh(@RequestParam(required = false) MultipartFile file, HttpServletResponse response) throws IOException {
        String map = uploadRecordService.uploadPic(file);
        return signatureController.returnPostSh2("/uploadrecord/uploadPicAndNameForSh", map);
    }

}
