package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.SysDict;
import com.huazheng.tunny.ocean.api.entity.WaybillContainerInfo;
import com.huazheng.tunny.ocean.api.entity.WaybillContainerInfoSh;
import com.huazheng.tunny.ocean.api.vo.ContainerInfoVO;
import com.huazheng.tunny.ocean.mapper.SysDictMapper;
import com.huazheng.tunny.ocean.mapper.WaybillContainerInfoMapper;
import com.huazheng.tunny.ocean.mapper.WaybillContainerInfoShMapper;
import com.huazheng.tunny.ocean.service.WaybillContainerInfoService;
import com.huazheng.tunny.ocean.service.WaybillContainerInfoShService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service("waybillContainerInfoShService")
public class WaybillContainerInfoShServiceImpl extends ServiceImpl<WaybillContainerInfoShMapper, WaybillContainerInfoSh> implements WaybillContainerInfoShService {

    @Autowired
    private WaybillContainerInfoShMapper waybillContainerInfoShMapper;

    public WaybillContainerInfoShMapper getWaybillContainerInfoShMapper() {
        return waybillContainerInfoShMapper;
    }

    public void setWaybillContainerInfoShMapper(WaybillContainerInfoShMapper waybillContainerInfoShMapper) {
        this.waybillContainerInfoShMapper = waybillContainerInfoShMapper;
    }

    /**
     * 当前运单下集装箱、货物、参与方分页信息
     */
    @Override
    public Page selectConGoodsPantsPage(Query query)
    {
        WaybillContainerInfoSh waybillContainerInfoSh =  BeanUtil.mapToBean(query.getCondition(), WaybillContainerInfoSh.class,false);
        //获取开始时间
        long startTime=System.currentTimeMillis();
        List<WaybillContainerInfoSh> list= waybillContainerInfoShMapper.selectConGoodsPantsPage(waybillContainerInfoSh);
        //获取结束时间
        long endTime=System.currentTimeMillis();
        System.out.println("程序运行时间： "+(endTime-startTime)+"ms");
        Integer count = list.size();
        query.setTotal(count);
        query.setRecords(list);
        return query;
    }

    @Override
    public BigDecimal getTotalAmount(String waybillNo) {
        return waybillContainerInfoShMapper.getTotalAmount(waybillNo);
    }

}
