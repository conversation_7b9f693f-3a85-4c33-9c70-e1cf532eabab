package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.AccountingCostDTO;
import com.huazheng.tunny.ocean.api.dto.FdShippingAccoundetailDTO;
import com.huazheng.tunny.ocean.api.dto.FdShippingAccountDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.feign.RemoteAdminService;
import com.huazheng.tunny.ocean.api.vo.SysDictVo;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.ContainerTypeDataService;
import com.huazheng.tunny.ocean.service.FdShippingAccoundetailService;
import com.huazheng.tunny.ocean.service.FdShippingAccountService;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

@Service("fdShippingAccoundetailService")
public class FdShippingAccoundetailServiceImpl extends ServiceImpl<FdShippingAccoundetailMapper, FdShippingAccoundetail> implements FdShippingAccoundetailService {

    @Autowired
    private FdShippingAccoundetailMapper fdShippingAccoundetailMapper;
    @Autowired
    private FdShippingAccountService fdShippingAccountService;
    @Autowired
    private ContainerTypeDataService containerTypeDataService;
    @Autowired
    private WaybillContainerInfoMapper waybillContainerInfoMapper;
    @Autowired
    private ShifmanagementMapper shifmanagementMapper;
    @Autowired
    private SubsidyManagerMapper subsidyManagerMapper;
    @Autowired
    private FdShippingAccountMapper fdShippingAccountMapper;
    @Autowired
    private StationManagementMapper stationManagementMapper;
    @Autowired
    private PlatformCheckMapper platformCheckMapper;
    @Autowired
    private WaybillParticipantsMapper waybillParticipantsMapper;
    @Autowired
    private FdBusCostMapper fdBusCostMapper;
    @Autowired
    private RemoteAdminService remoteAdminService;
    @Value("${db.database}")
    private String database;

    @Value("${path.windowsPath}")
    private String windowsPath;
    @Value("${path.linuxPath}")
    private String linuxPath;

    /**
     * 查询发运台账子表信息
     *
     * @param rowId 发运台账子表ID
     * @return 发运台账子表信息
     */
    @Override
    public FdShippingAccoundetail selectFdShippingAccoundetailById(String rowId) {
        return fdShippingAccoundetailMapper.selectFdShippingAccoundetailById(rowId);
    }

    /**
     * 查询发运台账子表列表
     *
     * @param fdShippingAccoundetail 发运台账子表信息
     * @return 发运台账子表集合
     */
    @Override
    public List<FdShippingAccoundetail> selectFdShippingAccoundetailList(FdShippingAccoundetail fdShippingAccoundetail) {
        return fdShippingAccoundetailMapper.selectFdShippingAccoundetailList(fdShippingAccoundetail);
    }

    /**
     * 查询省平台首页数据
     *
     * @param customerNo
     * @return
     */
    @Override
    public FdShippingAccoundetail selectProvinceIndexInfo(String customerNo) {
        return fdShippingAccoundetailMapper.selectProvinceIndexInfo(customerNo);
    }


    /**
     * 分页模糊查询发运台账子表列表
     *
     * @return 发运台账子表集合
     */
    @Override
    public Page selectFdShippingAccoundetailListByLike(Query query) {
        FdShippingAccoundetail fdShippingAccoundetail = BeanUtil.mapToBean(query.getCondition(), FdShippingAccoundetail.class, false);
        query.setRecords(fdShippingAccoundetailMapper.selectFdShippingAccoundetailListByLike(query, fdShippingAccoundetail));
        return query;
    }

    @Override
    public List<FdShippingAccoundetail> list(FdShippingAccoundetail fdShippingAccoundetail) {
        List<FdShippingAccoundetail> fdShippingAccoundetails = fdShippingAccoundetailMapper.selectFdShippingAccoundetailListByLike2(fdShippingAccoundetail);
        if (CollUtil.isNotEmpty(fdShippingAccoundetails)) {
            FdShippingAccount sel2 = new FdShippingAccount();
            sel2.setAccountCode(fdShippingAccoundetail.getAccountCode());
            sel2.setDeleteFlag("N");
            List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountList(sel2);
            if (CollUtil.isNotEmpty(fdShippingAccounts)) {
                Shifmanagement sel3 = new Shifmanagement();
                sel3.setShiftId(fdShippingAccounts.get(0).getShiftNo());
                sel3.setPlatformCode(fdShippingAccounts.get(0).getPlatformCode());
                sel3.setDeleteFlag("N");
                List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel3);
                if (CollUtil.isNotEmpty(shifmanagements) && StrUtil.isNotBlank(shifmanagements.get(0).getShippingLineCode()) && StrUtil.isNotBlank(shifmanagements.get(0).getTrip())) {
                    Shifmanagement sel = new Shifmanagement();
                    sel.setShippingLineCode(shifmanagements.get(0).getShippingLineCode());
                    sel.setTrip(shifmanagements.get(0).getTrip());
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                    if ("G".equals(shifmanagements.get(0).getTrip())) {
                        sel.setStartTime(formatter.format(shifmanagements.get(0).getPlanShipTime()));
                    } else if ("R".equals(shifmanagements.get(0).getTrip())) {
                        sel.setStartTime(formatter.format(shifmanagements.get(0).getPlanShipTime()));
                    }

                    //市补贴
                    /*sel.setPlatformCode(fdShippingAccounts.get(0).getPlatformCode());
                    List<SubsidyManager> list = subsidyManagerMapper.selectSubsidyManagerListByShift(sel);*/

                    //省补贴
                    sel.setPlatformCode(fdShippingAccounts.get(0).getCustomerNo());
                    List<SubsidyManager> list2 = subsidyManagerMapper.selectSubsidyManagerListByShift(sel);
                    List<String> containerNumbers = fdBusCostMapper.selectSubsidyContainerNumber(fdShippingAccounts.get(0).getShiftNo(), fdShippingAccounts.get(0).getPlatformCode(), fdShippingAccounts.get(0).getPlatformCode(), fdShippingAccounts.get(0).getCustomerNo());

                    for (FdShippingAccoundetail detail : fdShippingAccoundetails) {
                        BigDecimal subsidyAmount = BigDecimal.ZERO;
                        BigDecimal subsidyStandards = BigDecimal.ZERO;
                        if ("N".equals(detail.getDeleteFlag())) {
                            /*if (CollUtil.isNotEmpty(list)) {
                                if ("20".equals(detail.getContainerType())) {
                                    Double num = 0.5;
                                    subsidyAmount = subsidyAmount.add(list.get(0).getSubsidyAmount().multiply(BigDecimal.valueOf(num)));
                                } else if ("40".equals(detail.getContainerType()) || "45".equals(detail.getContainerType())) {
                                    subsidyAmount = subsidyAmount.add(list.get(0).getSubsidyAmount());
                                }
                                subsidyStandards = subsidyStandards.add(list.get(0).getSubsidyAmount());
                            }*/
                            if (CollUtil.isNotEmpty(list2) && CollUtil.isNotEmpty(containerNumbers) && containerNumbers.contains(detail.getContainerNumber())) {
                                if ("20".equals(detail.getContainerType())) {
                                    Double num = 0.5;
                                    subsidyAmount = subsidyAmount.add(list2.get(0).getSubsidyAmount().multiply(BigDecimal.valueOf(num)));
                                } else if ("40".equals(detail.getContainerType()) || "45".equals(detail.getContainerType())) {
                                    subsidyAmount = subsidyAmount.add(list2.get(0).getSubsidyAmount());
                                }
                                subsidyStandards = subsidyStandards.add(list2.get(0).getSubsidyAmount());
                            }
                        }
                        detail.setSubsidyAmount(subsidyAmount);
                        detail.setSubsidyStandards(String.valueOf(subsidyStandards));
                    }
                }
            }
        }

        return fdShippingAccoundetails;
    }

    @Override
    public List<FdShippingAccoundetail> listWithCancel(FdShippingAccoundetail fdShippingAccoundetail) {
        List<FdShippingAccoundetail> fdShippingAccoundetails = fdShippingAccoundetailMapper.listWithCancel(fdShippingAccoundetail);
        if (CollUtil.isNotEmpty(fdShippingAccoundetails)) {
            FdShippingAccount sel2 = new FdShippingAccount();
            sel2.setAccountCode(fdShippingAccoundetail.getAccountCode());
            sel2.setDeleteFlag("N");
            List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountList(sel2);
            if (CollUtil.isNotEmpty(fdShippingAccounts)) {
                Shifmanagement sel3 = new Shifmanagement();
                sel3.setShiftId(fdShippingAccounts.get(0).getShiftNo());
                sel3.setPlatformCode(fdShippingAccounts.get(0).getPlatformCode());
                sel3.setDeleteFlag("N");
                List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel3);
                if (CollUtil.isNotEmpty(shifmanagements) && StrUtil.isNotBlank(shifmanagements.get(0).getShippingLineCode()) && StrUtil.isNotBlank(shifmanagements.get(0).getTrip())) {
                    Shifmanagement sel = new Shifmanagement();
                    sel.setShippingLineCode(shifmanagements.get(0).getShippingLineCode());
                    sel.setTrip(shifmanagements.get(0).getTrip());
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                    if ("G".equals(shifmanagements.get(0).getTrip())) {
                        sel.setStartTime(formatter.format(shifmanagements.get(0).getPlanShipTime()));
                    } else if ("R".equals(shifmanagements.get(0).getTrip())) {
                        sel.setStartTime(formatter.format(shifmanagements.get(0).getPlanShipTime()));
                    }

                    //市补贴
                    /*sel.setPlatformCode(fdShippingAccounts.get(0).getPlatformCode());
                    List<SubsidyManager> list = subsidyManagerMapper.selectSubsidyManagerListByShift(sel);*/

                    //省补贴
                    sel.setPlatformCode(fdShippingAccounts.get(0).getCustomerNo());
                    List<SubsidyManager> list2 = subsidyManagerMapper.selectSubsidyManagerListByShift(sel);

                    List<String> containerNumbers = fdBusCostMapper.selectSubsidyContainerNumber(fdShippingAccounts.get(0).getShiftNo(), fdShippingAccounts.get(0).getPlatformCode(), fdShippingAccounts.get(0).getPlatformCode(), fdShippingAccounts.get(0).getCustomerNo());
                    for (FdShippingAccoundetail detail : fdShippingAccoundetails) {
                        BigDecimal subsidyAmount = BigDecimal.ZERO;
                        BigDecimal subsidyStandards = BigDecimal.ZERO;
                        if ("N".equals(detail.getDeleteFlag())) {
                            /*if (CollUtil.isNotEmpty(list)) {
                                if ("20".equals(detail.getContainerType())) {
                                    Double num = 0.5;
                                    subsidyAmount = subsidyAmount.add(list.get(0).getSubsidyAmount().multiply(BigDecimal.valueOf(num)));
                                } else if ("40".equals(detail.getContainerType()) || "45".equals(detail.getContainerType())) {
                                    subsidyAmount = subsidyAmount.add(list.get(0).getSubsidyAmount());
                                }
                                subsidyStandards = subsidyStandards.add(list.get(0).getSubsidyAmount());
                            }*/
                            if (CollUtil.isNotEmpty(list2) && CollUtil.isNotEmpty(containerNumbers) && containerNumbers.contains(detail.getContainerNumber())) {
                                if ("20".equals(detail.getContainerType())) {
                                    Double num = 0.5;
                                    subsidyAmount = subsidyAmount.add(list2.get(0).getSubsidyAmount().multiply(BigDecimal.valueOf(num)));
                                } else if ("40".equals(detail.getContainerType()) || "45".equals(detail.getContainerType())) {
                                    subsidyAmount = subsidyAmount.add(list2.get(0).getSubsidyAmount());
                                }
                                subsidyStandards = subsidyStandards.add(list2.get(0).getSubsidyAmount());
                            }
                        }
                        detail.setSubsidyAmount(subsidyAmount);
                        detail.setSubsidyStandards(String.valueOf(subsidyStandards));
                    }
                }
            }
        }

        return fdShippingAccoundetails;
    }

    /**
     * 台账明细刷新接口
     *
     * @param fdShippingAccoundetail
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R refreshAccoundetailList(FdShippingAccoundetail fdShippingAccoundetail) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        //查询该台账的信息
        EntityWrapper<FdShippingAccount> accountWrapper = new EntityWrapper<>();
        accountWrapper.eq("account_code", fdShippingAccoundetail.getAccountCode());
        FdShippingAccount account = fdShippingAccountService.selectOne(accountWrapper);

        //查询台账明细
        EntityWrapper<FdShippingAccoundetail> detailWrapper = new EntityWrapper<>();
        detailWrapper.eq("account_code", fdShippingAccoundetail.getAccountCode());
        detailWrapper.eq("delete_flag", "N");
        List<FdShippingAccoundetail> detailList = selectList(detailWrapper);
        //如果从未提交过，进行刷新操作
        if ("0".equals(account.getSubmitFlag())) {
            //查询班列号所有的可生成的台账明细
            FdShippingAccoundetail shippingAccoundetail = new FdShippingAccoundetail();
            shippingAccoundetail.setShiftNo(account.getShiftNo());
            List<FdShippingAccoundetail> containerInfoList = fdShippingAccoundetailMapper.selectContainerInfoList(shippingAccoundetail);

            //对比、并进行相关操作
            List<Integer> containerRemoveList = new ArrayList<>();
            StringBuilder detailRemove = new StringBuilder();
            for (FdShippingAccoundetail detail : detailList) {
                Boolean flag = false;
                for (int j = 0; j < containerInfoList.size(); j++) {
                    FdShippingAccoundetail container = containerInfoList.get(j);
                    if (detail.getContainerNumber().equals(container.getContainerNumber())) {
                        containerRemoveList.add(j);
                        flag = true;
                        break;
                    }
                }
                if (flag) {
                    continue;
                } else {
                    detailRemove.append(detail.getRowId() + ",");
                }
            }
            //进行移除、新增操作
            Collections.sort(containerRemoveList, Collections.reverseOrder());
            for (int containerRemove : containerRemoveList) {
                containerInfoList.remove(containerRemove);
            }
            for (FdShippingAccoundetail containerInfo : containerInfoList) {
                containerInfo.setAddTime(LocalDateTime.now());
                containerInfo.setAddWho(userInfo.getUserName());
                containerInfo.setAddWhoName(userInfo.getRealName());
                containerInfo.setContainerNewestStatus("1");
            }
            insertBatch(containerInfoList);
            if (detailRemove.length() > 0) {
                EntityWrapper<FdShippingAccoundetail> wrapper = new EntityWrapper<>();
                wrapper.in("row_id", detailRemove.substring(0, detailRemove.length() - 1));
                FdShippingAccoundetail accoundetail = new FdShippingAccoundetail();
                accoundetail.setDeleteFlag("Y");
                accoundetail.setUpdateTime(LocalDateTime.now());
                accoundetail.setUpdateWho(userInfo.getUserName());
                accoundetail.setUpdateWhoName(userInfo.getRealName());
                update(accoundetail, wrapper);
            }
            /*//修改主表数据
            List<FdShippingAccoundetail> accoundetailList = selectList(detailWrapper);
            //境外运费(原币)
            BigDecimal freightOc = BigDecimal.valueOf(0);
            //境外运费(人民币)
            BigDecimal freightCny = BigDecimal.valueOf(0);
            //境内运费
            BigDecimal freight = BigDecimal.valueOf(0);
            for (FdShippingAccoundetail dto : accoundetailList){
                if (dto.getOverseasFreightOc() != null){
                    freightOc = freightOc.add(dto.getOverseasFreightOc());
                }
                if (dto.getOverseasFreightCny() != null){
                    freightCny = freightCny.add(dto.getOverseasFreightCny());
                }
                if (dto.getDomesticFreight() != null){
                    freight = freight.add(dto.getDomesticFreight());
                }
            }
            account.setOverseasFreightOc(freightOc);
            account.setOverseasFreightCny(freightCny);
            account.setDomesticFreight(freight);
            account.setAmount(freightCny.add(freight));
            account.setUpdateTime(LocalDateTime.now());
            account.setUpdateWho(userInfo.getUserName());
            account.setUpdateWhoName(userInfo.getRealName());
            fdShippingAccountService.updateById(account);*/
            return new R(200, true, null, "刷新成功");
        }

        //返回
        return new R(500, false, null, "该省级班列号已经提交过，不能进行刷新操作");
    }

    /**
     * 导入文件
     *
     * @param listob
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R importFile(List<List<Object>> listob) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String provinceShiftNo = "";
        FdShippingAccount fdShippingAccount = new FdShippingAccount();
        List<FdShippingAccoundetail> detailList = new ArrayList<>();
        Map<String, String> map = new HashMap<>();

        ContainerTypeData sel = new ContainerTypeData();
        sel.setDeleteFlag("N");
        List<ContainerTypeData> containerTypeDataList = containerTypeDataService.selectContainerTypeDataList(sel);

        for (int i = 0; i < listob.size(); i++) {
            List<Object> objects = listob.get(i);
            //判断省级班列号是否存在
            if (i == 0) {
                provinceShiftNo = objects.get(16).toString();
                EntityWrapper<FdShippingAccount> accountWrapper = new EntityWrapper<>();
                accountWrapper.eq("province_shift_no", provinceShiftNo);
//                accountWrapper.ne("status", "0");
                fdShippingAccount = fdShippingAccountService.selectOne(accountWrapper);
                if (fdShippingAccount == null) {
                    return new R(500, false, null, "该省级班列号未提交或不存在");
                }
                EntityWrapper<FdShippingAccoundetail> detailWrapper = new EntityWrapper<>();
                detailWrapper.eq("account_code", fdShippingAccount.getAccountCode());
                detailWrapper.eq("delete_flag", "N");
                detailWrapper.andNew().isNull("container_status").or().eq("container_status", "0");
                detailList = fdShippingAccoundetailMapper.selectList(detailWrapper);
                //更改明细最新集装箱状态为null
                EntityWrapper<FdShippingAccoundetail> wrapper = new EntityWrapper<>();
                wrapper.eq("account_code", fdShippingAccount.getAccountCode());
                FdShippingAccoundetail accoundetail = new FdShippingAccoundetail();
                accoundetail.setContainerNewestStatus(null);
                fdShippingAccoundetailMapper.update(accoundetail, wrapper);
                for (FdShippingAccoundetail detail : detailList) {
                    map.put(detail.getContainerNumber(), detail.getContainerNumber());
                }
                continue;
            }
            if (i == 1 || i == 2) {
                continue;
            }
            String containerNumber = objects.get(10).toString();
            Boolean flag = false;

            for (FdShippingAccoundetail detail : detailList) {
                if (detail.getContainerNumber().equals(containerNumber)) {
                    flag = true;
                    map.remove(detail.getContainerNumber());
                    break;
                }
            }
            //若台账表中不存在，则新增
            if (flag) {
                continue;
            } else {

                FdShippingAccoundetail accoundetail = new FdShippingAccoundetail();
                accoundetail.setAccountCode(fdShippingAccount.getAccountCode());
                accoundetail.setCustomerNo(null);
                accoundetail.setCustomerName(null);
                accoundetail.setApplicationNumber(null);
                accoundetail.setTransportOrderNumber(null);
                accoundetail.setContainerNumber(objects.get(10).toString());
                accoundetail.setClearanceNumber(objects.get(18).toString());
                if (StrUtil.isNotEmpty(objects.get(19).toString())) {
                    accoundetail.setValueUsd(BigDecimal.valueOf(Double.valueOf(String.valueOf(objects.get(19)))));
                }
                accoundetail.setCustomsSeal(objects.get(20).toString());
                accoundetail.setTrainNumber(objects.get(21).toString());
                accoundetail.setWaybillDemandNumber(objects.get(22).toString());
                accoundetail.setWaybillLnNumber(objects.get(23).toString());
                accoundetail.setSubsidyStandards(objects.get(29).toString());
                accoundetail.setDomesticFreight(BigDecimal.valueOf((Double.valueOf(String.valueOf(objects.get(24))))));
                if (StrUtil.isNotEmpty(objects.get(30).toString())) {
                    accoundetail.setSubsidyAmount(BigDecimal.valueOf((Double.valueOf(String.valueOf(objects.get(30))))));
                }
                accoundetail.setMonetaryType(objects.get(26).toString());
                if (StrUtil.isNotEmpty(objects.get(27).toString())) {
                    accoundetail.setExchangeRate(BigDecimal.valueOf((Double.valueOf(String.valueOf(objects.get(27))))));
                }
                if (StrUtil.isNotEmpty(objects.get(25).toString())) {
                    accoundetail.setOverseasFreightOc(BigDecimal.valueOf((Double.valueOf(String.valueOf(objects.get(25))))));
                }
                if (StrUtil.isNotEmpty(objects.get(28).toString())) {
                    accoundetail.setOverseasFreightCny(BigDecimal.valueOf((Double.valueOf(String.valueOf(objects.get(28))))));
                    accoundetail.setShippingFreight(accoundetail.getOverseasFreightCny().add(accoundetail.getDomesticFreight()));
                }
                accoundetail.setContainerStatus("0");
                accoundetail.setContainerNewestStatus("0");
                accoundetail.setRemarks(objects.get(29).toString());
                accoundetail.setAddWho(userInfo.getUserName());
                accoundetail.setAddWhoName(userInfo.getRealName());
                accoundetail.setAddTime(LocalDateTime.now());
                accoundetail.setIsRansit(objects.get(0).toString());
                accoundetail.setOrgUnit(objects.get(1).toString());
                accoundetail.setDestinationName(objects.get(3).toString());
                accoundetail.setDestination(objects.get(5).toString());
                accoundetail.setContainerNo(objects.get(10).toString());
                accoundetail.setGoodsName(objects.get(7).toString());
                accoundetail.setGoodsNums(Double.valueOf(objects.get(11).toString()).intValue());
                accoundetail.setGoodsWeight(new Double(objects.get(12).toString()));
                accoundetail.setContainerWeight(objects.get(13).toString());
                accoundetail.setContainerOwner("自备箱".equals(objects.get(9).toString()) ? "0" : "1");
//                accoundetail.setContainerType(objects.get(8).toString());
                String containerTypeCode = objects.get(8).toString();
                Boolean flag2 = true;
                for (ContainerTypeData data : containerTypeDataList) {
                    if (data.getContainerTypeCode().equals(containerTypeCode)) {
                        accoundetail.setContainerTypeCode(data.getContainerTypeCode());
                        accoundetail.setContainerTypeName(data.getContainerTypeName());
                        accoundetail.setContainerType(data.getContainerTypeSize());
                        flag2 = false;
                        break;
                    }
                }

                if (flag2) {
                    return new R(Boolean.FALSE, "未查询到该箱型代码：" + containerTypeCode);
                }

                accoundetail.setDestination(objects.get(5).toString());
                fdShippingAccoundetailMapper.insert(accoundetail);
            }

        }
        //若表格中不存在，则撤箱
        StringBuilder sb = new StringBuilder();
        for (String key : map.keySet()) {
            sb.append(key + ",");
        }
        if (sb != null && sb.length() > 0) {
            EntityWrapper<FdShippingAccoundetail> wrapper = new EntityWrapper<>();
            wrapper.in("container_number", sb.substring(0, sb.length() - 1));
            wrapper.eq("account_code", fdShippingAccount.getAccountCode());
            FdShippingAccoundetail accoundetail = new FdShippingAccoundetail();
            accoundetail.setContainerStatus("1");
            accoundetail.setContainerNewestStatus("1");
            accoundetail.setUpdateWho(userInfo.getUserName());
            accoundetail.setUpdateWhoName(userInfo.getRealName());
            accoundetail.setUpdateTime(LocalDateTime.now());
            update(accoundetail, wrapper);
        }

        //更改主表中相关字段
        EntityWrapper<FdShippingAccoundetail> detailWrapper = new EntityWrapper<>();
        detailWrapper.eq("account_code", fdShippingAccount.getAccountCode());
        detailWrapper.eq("delete_flag", "N");
        detailWrapper.andNew().isNull("container_status").or().eq("container_status", "0");
        List<FdShippingAccoundetail> accoundetailList = fdShippingAccoundetailMapper.selectList(detailWrapper);
        //境外运费(原币)
        BigDecimal freightOc = BigDecimal.valueOf(0);
        //境外运费(人民币)
        BigDecimal freightCny = BigDecimal.valueOf(0);
        //境内运费
        BigDecimal freight = BigDecimal.valueOf(0);
        for (FdShippingAccoundetail dto : accoundetailList) {
            if (dto.getOverseasFreightOc() != null) {
                freightOc = freightOc.add(dto.getOverseasFreightOc());
            }
            if (dto.getOverseasFreightCny() != null) {
                freightCny = freightCny.add(dto.getOverseasFreightCny());
            }
            if (dto.getDomesticFreight() != null) {
                freight = freight.add(dto.getDomesticFreight());
            }
        }
        fdShippingAccount.setOverseasFreightOc(freightOc);
        fdShippingAccount.setOverseasFreightCny(freightCny);
        fdShippingAccount.setDomesticFreight(freight);
        fdShippingAccount.setAmount(freightCny.add(freight));
        fdShippingAccount.setUpdateWho(userInfo.getUserName());
        fdShippingAccount.setUpdateWhoName(userInfo.getRealName());
        fdShippingAccount.setUpdateTime(LocalDateTime.now());
        fdShippingAccount.setStatus("0");
        fdShippingAccountService.updateById(fdShippingAccount);


        return new R(200, true, null, "导入成功");
    }

    /**
     * 根据班列查询箱号信息
     *
     * @param query
     * @return
     */
    @Override
    public Page containerInfoPage(Query query) {
        FdShippingAccoundetail fdShippingAccoundetail = BeanUtil.mapToBean(query.getCondition(), FdShippingAccoundetail.class, false);
        fdShippingAccoundetail.setDatabase(database);
        query.setRecords(fdShippingAccoundetailMapper.containerInfoPage(query, fdShippingAccoundetail));
        return query;
    }

    @Override
    public List containerInfoList(FdShippingAccoundetail fdShippingAccoundetail) {
        fdShippingAccoundetail.setDatabase(database);
        return fdShippingAccoundetailMapper.containerInfoList(fdShippingAccoundetail);
    }

    @Override
    public List containerInfoListNew(FdShippingAccoundetail fdShippingAccoundetail) {
        fdShippingAccoundetail.setDatabase(database);
        fdShippingAccoundetail.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        List<FdShippingAccoundetail> list1 = fdShippingAccoundetailMapper.containerInfoListNew1(fdShippingAccoundetail);
        List<FdShippingAccoundetail> list2 = fdShippingAccoundetailMapper.containerInfoListNew2(fdShippingAccoundetail);
        if (CollUtil.isNotEmpty(list1) && CollUtil.isNotEmpty(list2)) {
            for (FdShippingAccoundetail f1 : list1) {
                for (FdShippingAccoundetail f2 : list2) {
                    if (f1.getContainerNumber().equals(f2.getContainerNumber())) {
                        f1.setTransportOrderNumber(f2.getTransportOrderNumber());
                        f1.setIsRansit(f2.getIsRansit());
                        f1.setApplicationNumber(f2.getApplicationNumber());
                        f1.setGoodsOwner(f2.getGoodsOwner());
                        f1.setDestinationName(f2.getDestinationName());
                        f1.setDestination(f2.getDestination());
                        f1.setGoodsName(f2.getGoodsName());
                        f1.setGoodsNums(f2.getGoodsNums());
                        f1.setGoodsWeight(f2.getGoodsWeight());
                        f1.setValueUsd(f2.getValueUsd());
                        f1.setContainerTypeCode(f2.getContainerTypeCode());
                        f1.setContainerTypeName(f2.getContainerTypeName());
                        f1.setContainerType(f2.getContainerType());
                        f1.setContainerOwner(f2.getContainerOwner());
                        f1.setContainerWeight(f2.getContainerWeight());
                        f1.setConsignorName(f2.getConsignorName());
                        f1.setDestinationCountry(f2.getDestinationCountry());
                        f1.setDestinationCountryCode(f2.getDestinationCountryCode());
                        f1.setPortAgent(f2.getPortAgent());
                        f1.setGoodsOrigin(f2.getGoodsOrigin());
                        f1.setIsFull(f2.getIsFull());
                        f1.setNonFerrous(f2.getNonFerrous());

                        f1.setClearanceNumber(f2.getClearanceNumber());
                        f1.setCustomsSeal(f2.getCustomsSeal());
                        f1.setTrainNumber(f2.getTrainNumber());
                        f1.setWaybillDemandNumber(f2.getWaybillDemandNumber());
                        f1.setWaybillLnNumber(f2.getWaybillLnNumber());
                        break;
                    }
                }
            }
        }
        Shifmanagement sel2 = new Shifmanagement();
        sel2.setShiftId(fdShippingAccoundetail.getShiftNo());
        sel2.setPlatformCode(fdShippingAccoundetail.getPlatformCode());
        sel2.setDeleteFlag("N");
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel2);
        if (CollUtil.isNotEmpty(shifmanagements)) {
            Shifmanagement sel = new Shifmanagement();
            sel.setShippingLineCode(shifmanagements.get(0).getShippingLineCode());
            sel.setTrip(shifmanagements.get(0).getTrip());
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            if ("G".equals(shifmanagements.get(0).getTrip())) {
                sel.setStartTime(formatter.format(shifmanagements.get(0).getPlanShipTime()));
            } else if ("R".equals(shifmanagements.get(0).getTrip())) {
                sel.setStartTime(formatter.format(shifmanagements.get(0).getPlanShipTime()));
            }
            //市补贴
            /*sel.setPlatformCode(fdShippingAccoundetail.getPlatformCode());
            List<SubsidyManager> city = subsidyManagerMapper.selectSubsidyManagerListByShift(sel);*/

            SecruityUser userInfo = SecurityUtils.getUserInfo();
            if ("2".equals(userInfo.getPlatformLevel())) {
                sel.setPlatformCode(userInfo.getPlatformCode());
            } else {
                String supPlatformCode = getProvinceCode(userInfo);
                sel.setPlatformCode(supPlatformCode);
            }
            //省补贴
            List<SubsidyManager> province = subsidyManagerMapper.selectSubsidyManagerListByShift(sel);
            if (CollUtil.isNotEmpty(list1)) {
                String supPlatformCode = getProvinceCode(userInfo);
                List<String> containerNumbers = fdBusCostMapper.selectSubsidyContainerNumber(fdShippingAccoundetail.getShiftNo(), fdShippingAccoundetail.getPlatformCode(), fdShippingAccoundetail.getPlatformCode(), supPlatformCode);
                for (FdShippingAccoundetail f1 : list1) {
                    BigDecimal subsidyAmount = BigDecimal.ZERO;
                    BigDecimal subsidyStandards = BigDecimal.ZERO;
                    /*if(CollUtil.isNotEmpty(city)){
                        if ("20".equals(f1.getContainerType())) {
                            Double num = 0.5;
                            subsidyAmount = subsidyAmount.add(city.get(0).getSubsidyAmount().multiply(BigDecimal.valueOf(num)));
                        } else if ("40".equals(f1.getContainerType()) || "45".equals(f1.getContainerType())) {
                            subsidyAmount = subsidyAmount.add(city.get(0).getSubsidyAmount());
                        }
                        subsidyStandards = subsidyStandards.add(city.get(0).getSubsidyAmount());
                    }*/
                    if (CollUtil.isNotEmpty(province) && CollUtil.isNotEmpty(containerNumbers) && containerNumbers.contains(f1.getContainerNumber())) {
                        if ("20".equals(f1.getContainerType())) {
                            Double num = 0.5;
                            subsidyAmount = subsidyAmount.add(province.get(0).getSubsidyAmount().multiply(BigDecimal.valueOf(num)));
                        } else if ("40".equals(f1.getContainerType()) || "45".equals(f1.getContainerType())) {
                            subsidyAmount = subsidyAmount.add(province.get(0).getSubsidyAmount());
                        }
                        subsidyStandards = subsidyStandards.add(province.get(0).getSubsidyAmount());
                    }
                    subsidyAmount = subsidyAmount.setScale(2, RoundingMode.HALF_UP);
                    f1.setSubsidyAmount(subsidyAmount);
                    f1.setSubsidyStandards(String.valueOf(subsidyStandards));
                }
            }
        }

        return list1;
    }

    private static String getProvinceCode(SecruityUser userInfo) {
        String supPlatformCode = userInfo.getSupPlatformCode();
        if (supPlatformCode.contains("-")) {
            supPlatformCode = supPlatformCode.split("-")[0];
        }
        return supPlatformCode;
    }

    @Override
    public List containerInfoForTz(FdShippingAccoundetail fdShippingAccoundetail) {
        fdShippingAccoundetail.setDatabase(database);
        return fdShippingAccoundetailMapper.containerInfoForTz(fdShippingAccoundetail);
    }

    @Override
    public R selectContainerInfoLists(Map<String, Object> params) {
        R r = new R();
        FdShippingAccoundetail fdShippingAccoundetail = BeanUtil.mapToBean(params, FdShippingAccoundetail.class, false);
        fdShippingAccoundetail.setDatabase(database);
        List<FdShippingAccoundetail> fdShippingAccoundetails = fdShippingAccoundetailMapper.selectContainerInfoLists(fdShippingAccoundetail);
        r.setData(fdShippingAccoundetails);
        r.setB(Boolean.TRUE);
        return r;
    }

    /**
     * 班列发运明细表（导出）
     *
     * @param params
     * @return
     */
    @Override
    public List<FdShippingAccoundetailDTO> selectShippingDetailExport(Map<String, Object> params) {
        return fdShippingAccoundetailMapper.selectShippingDetailExport(params);
    }

    @Override
    public List<FdShippingAccoundetailDTO> selectShippingDetailExport2(String shiftNo, String billBalanceCode) {
        return fdShippingAccoundetailMapper.selectShippingDetailExport2(shiftNo, billBalanceCode);
    }

    @Override
    public Page selectFreightAccountingList(Query query) {
        FdShippingAccoundetailDTO fdShippingAccoundetailDTO = BeanUtil.mapToBean(query.getCondition(), FdShippingAccoundetailDTO.class, false);
        query.setRecords(fdShippingAccoundetailMapper.selectFreightAccountingList(query, fdShippingAccoundetailDTO));
//        query.setTotal(fdShippingAccoundetailMapper.selectFreightAccountingListCount(fdShippingAccoundetailDTO));
        return query;
    }

    @Override
    public List<FdShippingAccoundetailDTO> selectFreightAccountingListExport(FdShippingAccoundetailDTO fdShippingAccoundetailDTO) {
        return fdShippingAccoundetailMapper.selectFreightAccountingListExport(fdShippingAccoundetailDTO);
    }

    /**
     * 省平台-运费核算表（导出）
     *
     * @param provinceShiftNo
     * @return
     */
    @Override
    public R selectAccountingCostInfo(String provinceShiftNo) {
        List<FdShippingAccoundetailDTO> dtoList = fdShippingAccoundetailMapper.selectAccountingCostInfo(provinceShiftNo);
        AccountingCostDTO accountingCostDTO = new AccountingCostDTO();
        Map<String, Object> map = new HashMap<>();
        if (dtoList != null && dtoList.size() > 0) {
            BigDecimal overseasFreightSum = BigDecimal.valueOf(0);
            BigDecimal domesticFreightSum = BigDecimal.valueOf(0);
            for (FdShippingAccoundetailDTO dto : dtoList) {
                if (dto.getOverseasFreightCny() != null) {
                    overseasFreightSum = overseasFreightSum.add(dto.getOverseasFreightCny());
                }
                if (dto.getDomesticFreight() != null) {
                    domesticFreightSum = domesticFreightSum.add(dto.getDomesticFreight());
                }

            }
            accountingCostDTO.setProvinceShiftNo(provinceShiftNo);
            accountingCostDTO.setShippingTime(dtoList.get(0).getShippingTime());
            accountingCostDTO.setProvinceShiftNo(dtoList.get(0).getProvinceShiftNo());
            accountingCostDTO.setOverseasFreightSum(overseasFreightSum);
            accountingCostDTO.setDomesticFreightSum(domesticFreightSum);
            accountingCostDTO.setAmountPayable(overseasFreightSum.add(domesticFreightSum));
        }
        map.put("accountingCostDTO", accountingCostDTO);
        map.put("list", dtoList);
        return new R(200, true, map);
    }

    /**
     * 省平台-运费核算表附表（导出）
     *
     * @param params
     * @return
     */
    @Override
    public List<FdShippingAccoundetailDTO> selectFreightAccountingExport(Map<String, Object> params) {
        return fdShippingAccoundetailMapper.selectFreightAccountingExport(params);
    }

    /**
     * 新增发运台账子表
     *
     * @param fdShippingAccoundetail 发运台账子表信息
     * @return 结果
     */
    @Override
    public int insertFdShippingAccoundetail(FdShippingAccoundetail fdShippingAccoundetail) {
        return fdShippingAccoundetailMapper.insertFdShippingAccoundetail(fdShippingAccoundetail);
    }

    /**
     * 修改发运台账子表
     *
     * @param fdShippingAccoundetail 发运台账子表信息
     * @return 结果
     */
    @Override
    public int updateFdShippingAccoundetail(FdShippingAccoundetail fdShippingAccoundetail) {
        return fdShippingAccoundetailMapper.updateFdShippingAccoundetail(fdShippingAccoundetail);
    }

    @Override
    public int updateFdShippingAccoundetailList(List<FdShippingAccoundetail> fdShippingAccoundetail) {
        return fdShippingAccoundetailMapper.updateFdShippingAccoundetailList(fdShippingAccoundetail);
    }

    @Override
    public int updateFdShippingAccoundetailList2(List<FdShippingAccoundetail> fdShippingAccoundetail) {
        for (FdShippingAccoundetail detail : fdShippingAccoundetail) {
            fdShippingAccoundetailMapper.updateFdShippingAccoundetailOne(detail);
        }
        return 0;
    }


    /**
     * 删除发运台账子表
     *
     * @param rowId 发运台账子表ID
     * @return 结果
     */
    @Override
    public int deleteFdShippingAccoundetailById(String rowId) {
        return fdShippingAccoundetailMapper.deleteFdShippingAccoundetailById(rowId);
    }


    /**
     * 批量删除发运台账子表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFdShippingAccoundetailByIds(Integer[] rowIds) {
        return fdShippingAccoundetailMapper.deleteFdShippingAccoundetailByIds(rowIds);
    }

    @Override
    public List<FdShippingAccountDTO> selectShippingDetailAccountExport(Map<String, Object> params) {
        return fdShippingAccoundetailMapper.selectShippingDetailAccountExport(params);
    }

    public FdShippingAccoundetail selectAccountCodeAndContainerNumber(FdShippingAccoundetail fdShippingAccoundetail) {
        return fdShippingAccoundetailMapper.selectAccountCodeAndContainerNumber(fdShippingAccoundetail);
    }

    @Override
    public List<FdShippingAccoundetail> selectFdShippingAccoundetailListByCode(FdShippingAccoundetail fdShippingAccoundetail) {
        return fdShippingAccoundetailMapper.selectFdShippingAccoundetailListByCode(fdShippingAccoundetail);
    }

    @Override
    public int updateFdShippingAccoundetailByAccountCode(FdShippingAccoundetail fdShippingAccoundetail) {
        return fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode(fdShippingAccoundetail);
    }

    @Override
    public void exportCityTemplate(FdShippingAccoundetail detail, HttpServletResponse response) throws Exception {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("市平台台账明细");
        XSSFRow row = sheet.createRow(0);
        //宽度
        for (int i = 0; i <= 14; i++) {
            sheet.setColumnWidth(i, 60 * 80);
        }

        row.setHeight((short) (10 * 50));
        //字体1
        XSSFCellStyle style = setStyle(workbook);
        setTitle(row, style, sheet);
        int rowIndex = 2;

        setList(detail, sheet, rowIndex);
        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String(("市平台台账明细" + detail.getAccountCode() + ".xlsx").getBytes("GB2312"), "8859_1"));
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }

    @Override
    public void exportCityTemplateTwo(FdShippingAccoundetail fdShippingAccoundetail, HttpServletResponse response) throws Exception {
        String templateFileName = "exportCityTemplateTwo.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)) {
            templateFileName = linuxPath + templateFileName;
        } else {
            templateFileName = windowsPath + templateFileName;
        }
        InputStream templateFile = new FileInputStream(templateFileName);
        XSSFWorkbook workbook = new XSSFWorkbook(templateFile);
        XSSFSheet sheet = workbook.getSheetAt(0);

        setTitleTwo(fdShippingAccoundetail, sheet);

        setListTwo(fdShippingAccoundetail, sheet);

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String(("市平台台账明细" + fdShippingAccoundetail.getAccountCode() + ".xlsx").getBytes("GB2312"), "8859_1"));
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }

    private void setListTwo(FdShippingAccoundetail fdShippingAccoundetail, XSSFSheet sheet) {
        List<FdShippingAccoundetail> fdShippingAccoundetails = null;
        if (StrUtil.isNotBlank(fdShippingAccoundetail.getAccountCode())) {
            fdShippingAccoundetail.setDeleteFlag("N");
            fdShippingAccoundetails = fdShippingAccoundetailMapper.selectFdShippingAccoundetailList(fdShippingAccoundetail);
            if (CollUtil.isNotEmpty(fdShippingAccoundetails)) {
                Long i = 1L;
                for (FdShippingAccoundetail f1 : fdShippingAccoundetails) {
                    f1.setRowId(i);
                    f1.setMonetaryType("人民币");
                    i++;
                    if (StrUtil.isNotEmpty(f1.getIsFull())) {
                        if ("0".equals(f1.getIsFull())) {
                            f1.setIsFull("否");
                        } else if ("1".equals(f1.getIsFull())) {
                            f1.setIsFull("是");
                        }
                    } else {
                        f1.setIsFull("是");
                    }
                    if (StrUtil.isNotEmpty(f1.getNonFerrous())) {
                        if ("0".equals(f1.getNonFerrous())) {
                            f1.setNonFerrous("否");
                        } else if ("1".equals(f1.getNonFerrous())) {
                            f1.setNonFerrous("是");
                        }
                    } else {
                        f1.setNonFerrous("否");
                    }
                    if (StrUtil.isNotEmpty(f1.getIsRansit())) {
                        if ("E".equals(f1.getIsRansit())) {
                            f1.setIsRansit("出口");
                        } else if ("I".equals(f1.getIsRansit())) {
                            f1.setIsRansit("进口");
                        } else if ("P".equals(f1.getIsRansit())) {
                            f1.setIsRansit("过境");
                        }
                    }
                    if (StrUtil.isNotEmpty(f1.getContainerOwner())) {
                        if ("0".equals(f1.getContainerOwner())) {
                            f1.setContainerOwner("自备箱");
                        } else if ("1".equals(f1.getContainerOwner())) {
                            f1.setContainerOwner("中铁箱");
                        }
                    }
                }
            }
        } else {
            fdShippingAccoundetail.setDatabase(database);
            fdShippingAccoundetail.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
            fdShippingAccoundetails = fdShippingAccoundetailMapper.containerInfoListNew1(fdShippingAccoundetail);
            List<FdShippingAccoundetail> list2 = fdShippingAccoundetailMapper.containerInfoListNew2(fdShippingAccoundetail);
            if (CollUtil.isNotEmpty(fdShippingAccoundetails)) {
                Long i = 1L;
                for (FdShippingAccoundetail f1 : fdShippingAccoundetails) {
                    f1.setRowId(i);
                    f1.setMonetaryType("人民币");
                    i++;
                    if (StrUtil.isNotEmpty(f1.getIsFull())) {
                        if ("0".equals(f1.getIsFull())) {
                            f1.setIsFull("否");
                        } else if ("1".equals(f1.getIsFull())) {
                            f1.setIsFull("是");
                        }
                    } else {
                        f1.setIsFull("是");
                    }
                    if (StrUtil.isNotEmpty(f1.getNonFerrous())) {
                        if ("0".equals(f1.getNonFerrous())) {
                            f1.setNonFerrous("否");
                        } else if ("1".equals(f1.getNonFerrous())) {
                            f1.setNonFerrous("是");
                        }
                    } else {
                        f1.setNonFerrous("否");
                    }
                    if (CollUtil.isNotEmpty(list2)) {
                        for (FdShippingAccoundetail f2 : list2) {
                            if (f1.getContainerNumber().equals(f2.getContainerNumber())) {
                                f1.setTransportOrderNumber(f2.getTransportOrderNumber());
                                if (StrUtil.isNotEmpty(f2.getIsRansit())) {
                                    if ("E".equals(f2.getIsRansit())) {
                                        f1.setIsRansit("出口");
                                    } else if ("I".equals(f2.getIsRansit())) {
                                        f1.setIsRansit("进口");
                                    } else if ("P".equals(f2.getIsRansit())) {
                                        f1.setIsRansit("过境");
                                    }
                                }
                                f1.setApplicationNumber(f2.getApplicationNumber());
                                f1.setGoodsOrigin(f2.getGoodsOrigin());
                                f1.setGoodsOwner(f2.getGoodsOwner());
                                f1.setDestinationName(f2.getDestinationName());
                                f1.setDestination(f2.getDestination());
                                f1.setGoodsName(f2.getGoodsName());
                                f1.setGoodsNums(f2.getGoodsNums());
                                f1.setGoodsWeight(f2.getGoodsWeight());
                                f1.setContainerTypeCode(f2.getContainerTypeCode());
                                f1.setContainerTypeName(f2.getContainerTypeName());
                                f1.setContainerType(f2.getContainerType());
                                if (StrUtil.isNotEmpty(f2.getContainerOwner())) {
                                    if ("0".equals(f2.getContainerOwner())) {
                                        f1.setContainerOwner("自备箱");
                                    } else if ("1".equals(f2.getContainerOwner())) {
                                        f1.setContainerOwner("中铁箱");
                                    }
                                }
                                f1.setContainerWeight(f2.getContainerWeight());
                                f1.setConsignorName(f2.getConsignorName());
                                f1.setDestinationCountry(f2.getDestinationCountry());
                                f1.setPortAgent(f2.getPortAgent());

                                f1.setClearanceNumber(f2.getClearanceNumber());
                                f1.setCustomsSeal(f2.getCustomsSeal());
                                f1.setTrainNumber(f2.getTrainNumber());
                                f1.setWaybillDemandNumber(f2.getWaybillDemandNumber());
                                f1.setWaybillLnNumber(f2.getWaybillLnNumber());
                                break;
                            }
                        }
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(fdShippingAccoundetails)) {
            int rowIndex = 3;
            for (FdShippingAccoundetail detail : fdShippingAccoundetails) {
                XSSFRow row = sheet.createRow(rowIndex);
                XSSFCell cell0 = row.createCell(0);
                cell0.setCellValue(rowIndex - 2);
                XSSFCell cell1 = row.createCell(11);
                cell1.setCellValue(detail.getContainerNumber());

                XSSFCell cell2 = row.createCell(1);
                cell2.setCellValue(StrUtil.isNotEmpty(detail.getIsRansit()) ? detail.getIsRansit() : "");

                XSSFCell cell3 = row.createCell(2);
                cell3.setCellValue(StrUtil.isNotEmpty(detail.getOrgUnit()) ? detail.getOrgUnit() : "");

                XSSFCell cell4 = row.createCell(3);
                cell4.setCellValue(StrUtil.isNotEmpty(detail.getConsignorName()) ? detail.getConsignorName() : "");
                XSSFCell cell5 = row.createCell(4);
                cell5.setCellValue(StrUtil.isNotEmpty(detail.getDestinationName()) ? detail.getDestinationName() : "");
                XSSFCell cell6 = row.createCell(5);
                cell6.setCellValue(StrUtil.isNotEmpty(detail.getDestinationCountry()) ? detail.getDestinationCountry() : "");
                XSSFCell cell7 = row.createCell(6);
                cell7.setCellValue(StrUtil.isNotEmpty(detail.getDestination()) ? detail.getDestination() : "");
                XSSFCell cell8 = row.createCell(7);
                cell8.setCellValue(StrUtil.isNotEmpty(detail.getPortAgent()) ? detail.getPortAgent() : "");
                XSSFCell cell9 = row.createCell(8);
                cell9.setCellValue(StrUtil.isNotEmpty(detail.getGoodsName()) ? detail.getGoodsName() : "");
                XSSFCell cell10 = row.createCell(10);
                cell10.setCellValue(StrUtil.isNotEmpty(detail.getContainerOwner()) ? detail.getContainerOwner() : "");
                XSSFCell cell11 = row.createCell(9);
                cell11.setCellValue(StrUtil.isNotEmpty(detail.getContainerTypeCode()) ? detail.getContainerTypeCode() : "");

                XSSFCell cell12 = row.createCell(12);
                cell12.setCellValue(detail.getGoodsNums() != null ? detail.getGoodsNums().doubleValue() : 0);
                XSSFCell cell13 = row.createCell(13);
                cell13.setCellValue(detail.getGoodsWeight() != null ? detail.getGoodsWeight() : 0);
                XSSFCell cell14 = row.createCell(14);
                cell14.setCellValue(detail.getContainerWeight() != null ? Double.parseDouble(detail.getContainerWeight()) : 0);
                XSSFCell cell15 = row.createCell(15);
                cell15.setCellValue(StrUtil.isNotEmpty(detail.getIsFull()) ? detail.getIsFull() : "");
                XSSFCell cell16 = row.createCell(16);
                cell16.setCellValue(StrUtil.isNotEmpty(detail.getNonFerrous()) ? detail.getNonFerrous() : "");

                XSSFCell cell17 = row.createCell(17);
                cell17.setCellValue(StrUtil.isNotEmpty(detail.getGoodsOwner()) ? detail.getGoodsOwner() : "");
                XSSFCell cell18 = row.createCell(18);
                cell18.setCellValue(StrUtil.isNotEmpty(detail.getGoodsOrigin()) ? detail.getGoodsOrigin() : "");
                XSSFCell cell19 = row.createCell(19);
                cell19.setCellValue(StrUtil.isNotEmpty(detail.getClearanceNumber()) ? detail.getClearanceNumber() : "");
                XSSFCell cell20 = row.createCell(20);
                if (detail.getValueUsd() != null) {
                    cell20.setCellValue(detail.getValueUsd().doubleValue());
                } else {
                    cell20.setCellValue(0.00);
                }

                XSSFCell cell21 = row.createCell(21);
                cell21.setCellValue(StrUtil.isNotEmpty(detail.getCustomsSeal()) ? detail.getCustomsSeal() : "");
                XSSFCell cell22 = row.createCell(22);
                cell22.setCellValue(StrUtil.isNotEmpty(detail.getTrainNumber()) ? detail.getTrainNumber() : "");
                XSSFCell cell23 = row.createCell(23);
                cell23.setCellValue(StrUtil.isNotEmpty(detail.getWaybillDemandNumber()) ? detail.getWaybillDemandNumber() : "");
                XSSFCell cell24 = row.createCell(24);
                cell24.setCellValue(StrUtil.isNotEmpty(detail.getWaybillLnNumber()) ? detail.getWaybillLnNumber() : "");

                XSSFCell cell25 = row.createCell(25);
                cell25.setCellValue(detail.getDomesticFreight() != null ? detail.getDomesticFreight().doubleValue() : 0.00);
                XSSFCell cell26 = row.createCell(26);
                cell26.setCellValue(detail.getOverseasFreightCny() != null ? detail.getOverseasFreightCny().doubleValue() : 0.00);
                XSSFCell cell27 = row.createCell(27);
                cell27.setCellValue(StrUtil.isNotEmpty(detail.getMonetaryType()) ? detail.getMonetaryType() : "");

                XSSFCell cell32 = row.createCell(30);
                cell32.setCellValue(StrUtil.isNotEmpty(detail.getRemarks()) ? detail.getRemarks() : "");
                rowIndex++;
            }
        }
    }

    private void setTitleTwo(FdShippingAccoundetail fdShippingAccoundetail, XSSFSheet sheet) {
        XSSFRow row0 = sheet.getRow(0);
        String platformCode = null;
        String shiftNo = null;
        if (StrUtil.isNotBlank(fdShippingAccoundetail.getAccountCode())) {
            FdShippingAccount fdShippingAccount = fdShippingAccountMapper.selectFdShippingAccountByAccountCode(fdShippingAccoundetail.getAccountCode());
            if (fdShippingAccount != null) {
                platformCode = fdShippingAccount.getPlatformCode();
                shiftNo = fdShippingAccount.getShiftNo();
            }
        } else {
            platformCode = SecurityUtils.getUserInfo().getPlatformCode();
            shiftNo = fdShippingAccoundetail.getShiftNo();
        }

        Shifmanagement shifmanagement = shifmanagementMapper.selectShifmanagementByPlatCodeAndShiftNo(platformCode, shiftNo);
        if (shifmanagement != null) {
            XSSFCell cell01 = row0.createCell(1);
            cell01.setCellValue(shifmanagement.getShiftName());

            XSSFCell cell03 = row0.createCell(3);
            if ("G".equals(shifmanagement.getTrip())) {
                cell03.setCellValue("去程");
            } else if ("R".equals(shifmanagement.getTrip())) {
                cell03.setCellValue("回程");
            }

            XSSFCell cell05 = row0.createCell(5);
            cell05.setCellValue(shifmanagement.getShippingLine());

            XSSFCell cell09 = row0.createCell(9);
            if (shifmanagement.getPlanShipTime() != null) {
                SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
                cell09.setCellValue(df.format(shifmanagement.getPlanShipTime()));
            }

            XSSFCell cell011 = row0.createCell(11);
            if ("0".equals(shifmanagement.getTrainType())) {
                cell011.setCellValue("散列");
            } else if ("1".equals(shifmanagement.getTrainType())) {
                cell011.setCellValue("整列");
            }

            XSSFCell cell013 = row0.createCell(13);
            cell013.setCellValue(shifmanagement.getPortStation());

            XSSFCell cell07 = row0.createCell(7);
            cell07.setCellValue(shifmanagement.getDestinationName());

            XSSFCell cell017 = row0.createCell(17);
            cell017.setCellValue(shifmanagement.getProvinceShiftNo());

            if (StrUtil.isNotBlank(shifmanagement.getDestinationCode())) {
                StationManagement stationManagement = stationManagementMapper.selectPlatByStationCode(shifmanagement.getDestinationCode());
                if (stationManagement != null) {
                    XSSFCell cell015 = row0.createCell(15);
                    cell015.setCellValue(stationManagement.getCity());
                }
            }
        }
    }

    private void setList(FdShippingAccoundetail fdShippingAccoundetail, XSSFSheet sheet, int rowIndex) {
        List<FdShippingAccoundetail> fdShippingAccoundetails = null;
        if (StrUtil.isNotEmpty(fdShippingAccoundetail.getAccountCode())) {
            fdShippingAccoundetail.setDeleteFlag("N");
            fdShippingAccoundetails = fdShippingAccoundetailMapper.selectFdShippingAccoundetailList(fdShippingAccoundetail);
        } else {
            fdShippingAccoundetail.setDatabase(database);
            fdShippingAccoundetail.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
            fdShippingAccoundetails = fdShippingAccoundetailMapper.containerInfoListNew1(fdShippingAccoundetail);
            List<FdShippingAccoundetail> list2 = fdShippingAccoundetailMapper.containerInfoListNew2(fdShippingAccoundetail);
            if (CollUtil.isNotEmpty(fdShippingAccoundetails)) {
                if (CollUtil.isNotEmpty(list2)) {
                    for (FdShippingAccoundetail f1 : fdShippingAccoundetails) {
                        for (FdShippingAccoundetail f2 : list2) {
                            if (f1.getContainerNumber().equals(f2.getContainerNumber())) {
                                f1.setTransportOrderNumber(f2.getTransportOrderNumber());
                                f1.setIsRansit(f2.getIsRansit());
                                f1.setApplicationNumber(f2.getApplicationNumber());
                                f1.setGoodsOwner(f2.getGoodsOwner());
                                f1.setDestinationName(f2.getDestinationName());
                                f1.setDestination(f2.getDestination());
                                f1.setGoodsName(f2.getGoodsName());
                                f1.setGoodsNums(f2.getGoodsNums());
                                f1.setGoodsWeight(f2.getGoodsWeight());
                                f1.setContainerTypeCode(f2.getContainerTypeCode());
                                f1.setContainerTypeName(f2.getContainerTypeName());
                                f1.setContainerType(f2.getContainerType());
                                f1.setContainerOwner(f2.getContainerOwner());
                                f1.setContainerWeight(f2.getContainerWeight());
                                f1.setConsignorName(f2.getConsignorName());
                                f1.setDestinationCountry(f2.getDestinationCountry());
                                f1.setDestinationCountryCode(f2.getDestinationCountryCode());
                                f1.setPortAgent(f2.getPortAgent());

                                f1.setClearanceNumber(f2.getClearanceNumber());
                                f1.setCustomsSeal(f2.getCustomsSeal());
                                f1.setTrainNumber(f2.getTrainNumber());
                                f1.setWaybillDemandNumber(f2.getWaybillDemandNumber());
                                f1.setWaybillLnNumber(f2.getWaybillLnNumber());
                                break;
                            }
                        }
                    }
                }
            }
        }

        if (CollUtil.isNotEmpty(fdShippingAccoundetails)) {
            for (FdShippingAccoundetail detail : fdShippingAccoundetails) {
                XSSFRow row = sheet.createRow(rowIndex);
                XSSFCell cell0 = row.createCell(0);
                cell0.setCellValue(rowIndex - 1);
                XSSFCell cell1 = row.createCell(1);
                cell1.setCellValue(detail.getContainerNumber());
                XSSFCell cell2 = row.createCell(2);
                if (StrUtil.isNotEmpty(detail.getIsRansit())) {
                    if ("E".equals(detail.getIsRansit())) {
                        cell2.setCellValue("出口");
                    } else if ("I".equals(detail.getIsRansit())) {
                        cell2.setCellValue("进口");
                    } else if ("P".equals(detail.getIsRansit())) {
                        cell2.setCellValue("过境");
                    }
                }
                XSSFCell cell3 = row.createCell(3);
                cell3.setCellValue(StrUtil.isNotEmpty(detail.getOrgUnit()) ? detail.getOrgUnit() : "");
                XSSFCell cell4 = row.createCell(4);
                cell4.setCellValue(StrUtil.isNotEmpty(detail.getConsignorName()) ? detail.getConsignorName() : "");
                XSSFCell cell5 = row.createCell(5);
                cell5.setCellValue(StrUtil.isNotEmpty(detail.getDestinationName()) ? detail.getDestinationName() : "");
                XSSFCell cell6 = row.createCell(6);
                cell6.setCellValue(StrUtil.isNotEmpty(detail.getDestinationCountry()) ? detail.getDestinationCountry() : "");
                XSSFCell cell7 = row.createCell(7);
                cell7.setCellValue(StrUtil.isNotEmpty(detail.getDestination()) ? detail.getDestination() : "");
                XSSFCell cell8 = row.createCell(8);
                cell8.setCellValue(StrUtil.isNotEmpty(detail.getPortAgent()) ? detail.getPortAgent() : "");
                XSSFCell cell9 = row.createCell(9);
                cell9.setCellValue(StrUtil.isNotEmpty(detail.getGoodsName()) ? detail.getGoodsName() : "");
                XSSFCell cell10 = row.createCell(10);
                if (StrUtil.isNotEmpty(detail.getContainerOwner())) {
                    if ("0".equals(detail.getContainerOwner())) {
                        cell10.setCellValue("自备箱");
                    } else if ("1".equals(detail.getContainerOwner())) {
                        cell10.setCellValue("中铁箱");
                    }
                }
                XSSFCell cell11 = row.createCell(11);
                cell11.setCellValue(StrUtil.isNotEmpty(detail.getContainerTypeCode()) ? detail.getContainerTypeCode() : "");
                XSSFCell cell12 = row.createCell(12);
                cell12.setCellValue(detail.getGoodsNums() != null ? detail.getGoodsNums().doubleValue() : 0);
                XSSFCell cell13 = row.createCell(13);
                cell13.setCellValue(detail.getGoodsWeight() != null ? detail.getGoodsWeight().doubleValue() : 0);
                XSSFCell cell14 = row.createCell(14);
                cell14.setCellValue(StrUtil.isNotEmpty(detail.getContainerWeight()) ? Double.valueOf(detail.getContainerWeight()) : 0);
                XSSFCell cell15 = row.createCell(15);
                if (StrUtil.isNotEmpty(detail.getIsFull())) {
                    if ("0".equals(detail.getIsFull())) {
                        cell15.setCellValue("否");
                    } else if ("1".equals(detail.getIsFull())) {
                        cell15.setCellValue("是");
                    }
                } else {
                    cell15.setCellValue("是");
                }
                XSSFCell cell16 = row.createCell(16);
                if (StrUtil.isNotEmpty(detail.getNonFerrous())) {
                    if ("0".equals(detail.getNonFerrous())) {
                        cell16.setCellValue("否");
                    } else if ("1".equals(detail.getNonFerrous())) {
                        cell16.setCellValue("是");
                    }
                } else {
                    cell16.setCellValue("否");
                }

                XSSFCell cell17 = row.createCell(17);
                cell17.setCellValue(StrUtil.isNotEmpty(detail.getGoodsOwner()) ? detail.getGoodsOwner() : "");
                XSSFCell cell18 = row.createCell(18);
                cell18.setCellValue(StrUtil.isNotEmpty(detail.getGoodsOrigin()) ? detail.getGoodsOrigin() : "");
                XSSFCell cell19 = row.createCell(19);
                cell19.setCellValue(StrUtil.isNotEmpty(detail.getClearanceNumber()) ? detail.getClearanceNumber() : "");
                XSSFCell cell20 = row.createCell(20);
                if (detail.getValueUsd() != null) {
                    cell20.setCellValue(detail.getValueUsd().doubleValue());
                } else {
                    cell20.setCellValue(0);
                }

                XSSFCell cell21 = row.createCell(21);
                cell21.setCellValue(StrUtil.isNotEmpty(detail.getCustomsSeal()) ? detail.getCustomsSeal() : "");
                XSSFCell cell22 = row.createCell(22);
                cell22.setCellValue(StrUtil.isNotEmpty(detail.getTrainNumber()) ? detail.getTrainNumber() : "");
                XSSFCell cell23 = row.createCell(23);
                cell23.setCellValue(StrUtil.isNotEmpty(detail.getWaybillDemandNumber()) ? detail.getWaybillDemandNumber() : "");
                XSSFCell cell24 = row.createCell(24);
                cell24.setCellValue(StrUtil.isNotEmpty(detail.getWaybillLnNumber()) ? detail.getWaybillLnNumber() : "");

                XSSFCell cell25 = row.createCell(25);
                cell25.setCellValue(detail.getDomesticFreight() != null ? detail.getDomesticFreight().doubleValue() : 0.00);
                XSSFCell cell26 = row.createCell(26);
                cell26.setCellValue(detail.getOverseasFreightCny() != null ? detail.getOverseasFreightCny().doubleValue() : 0.00);
                XSSFCell cell27 = row.createCell(27);
                cell27.setCellValue(detail.getOverseasFreightOc() != null ? detail.getOverseasFreightOc().doubleValue() : 0.00);
                XSSFCell cell28 = row.createCell(28);
                cell28.setCellValue(detail.getExchangeRate() != null ? detail.getExchangeRate().doubleValue() : 1.00);
                XSSFCell cell29 = row.createCell(29);
                cell29.setCellValue(detail.getMonetaryType());

                XSSFCell cell30 = row.createCell(30);
                cell30.setCellValue(StrUtil.isNotEmpty(detail.getSubsidyStandards()) ? detail.getSubsidyStandards() : "");
                XSSFCell cell31 = row.createCell(31);
                if (detail.getSubsidyAmount() != null) {
                    cell31.setCellValue(detail.getSubsidyAmount().doubleValue());
                } else {
                    cell31.setCellValue(0.00);
                }
                XSSFCell cell32 = row.createCell(32);
                cell32.setCellValue(StrUtil.isNotEmpty(detail.getRemarks()) ? detail.getRemarks() : "");
                rowIndex++;
            }
        }
    }

    /**
     * 设置标题
     *
     * @Param: row, style
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/04 17:57
     **/
    public void setTitle(XSSFRow row, XSSFCellStyle style, XSSFSheet sheet) {
        XSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("序号");
        cell0.setCellStyle(style);
        XSSFCell cell1 = row.createCell(1);
        cell1.setCellValue("箱号*");
        cell1.setCellStyle(style);
        XSSFCell cell2 = row.createCell(2);
        cell2.setCellValue("类型*");
        cell2.setCellStyle(style);
        XSSFCell cell3 = row.createCell(3);
        cell3.setCellValue("货源组织单位*");
        cell3.setCellStyle(style);
        XSSFCell cell4 = row.createCell(4);
        cell4.setCellValue("收货人*");
        cell4.setCellStyle(style);
        XSSFCell cell5 = row.createCell(5);
        cell5.setCellValue("发站*");
        cell5.setCellStyle(style);
        XSSFCell cell6 = row.createCell(6);
        cell6.setCellValue("目的国*");
        cell6.setCellStyle(style);
        XSSFCell cell7 = row.createCell(7);
        cell7.setCellValue("到站*");
        cell7.setCellStyle(style);
        XSSFCell cell8 = row.createCell(8);
        cell8.setCellValue("口岸代理*");
        cell8.setCellStyle(style);
        XSSFCell cell9 = row.createCell(9);
        cell9.setCellValue("品名*");
        cell9.setCellStyle(style);
        XSSFCell cell10 = row.createCell(10);
        cell10.setCellValue("箱属*");
        cell10.setCellStyle(style);
        XSSFCell cell11 = row.createCell(11);
        cell11.setCellValue("箱型代码*");
        cell11.setCellStyle(style);
        XSSFCell cell12 = row.createCell(12);
        cell12.setCellValue("件数*");
        cell12.setCellStyle(style);
        XSSFCell cell13 = row.createCell(13);
        cell13.setCellValue("货重*");
        cell13.setCellStyle(style);
        XSSFCell cell14 = row.createCell(14);
        cell14.setCellValue("箱重*");
        cell14.setCellStyle(style);
        XSSFCell cell15 = row.createCell(15);
        cell15.setCellValue("是否全程*");
        cell15.setCellStyle(style);
        XSSFCell cell16 = row.createCell(16);
        cell16.setCellValue("有色金属(是/否)*");
        cell16.setCellStyle(style);
        XSSFCell cell17 = row.createCell(17);
        cell17.setCellValue("箱补充信息");
        cell17.setCellStyle(style);
        XSSFCell cell18 = row.createCell(18);
        cell18.setCellValue("");
        cell18.setCellStyle(style);
        XSSFCell cell19 = row.createCell(19);
        cell19.setCellValue("");
        cell19.setCellStyle(style);
        XSSFCell cell20 = row.createCell(20);
        cell20.setCellValue("");
        cell20.setCellStyle(style);
        XSSFCell cell21 = row.createCell(21);
        cell21.setCellValue("");
        cell21.setCellStyle(style);
        XSSFCell cell22 = row.createCell(22);
        cell22.setCellValue("");
        cell22.setCellStyle(style);
        XSSFCell cell23 = row.createCell(23);
        cell23.setCellValue("");
        cell23.setCellStyle(style);
        XSSFCell cell24 = row.createCell(24);
        cell24.setCellValue("");
        cell24.setCellStyle(style);
        XSSFCell cell25 = row.createCell(25);
        cell25.setCellValue("运费信息");
        cell25.setCellStyle(style);
        XSSFCell cell26 = row.createCell(26);
        cell26.setCellValue("");
        cell26.setCellStyle(style);
        XSSFCell cell27 = row.createCell(27);
        cell27.setCellValue("");
        cell27.setCellStyle(style);
        XSSFCell cell28 = row.createCell(28);
        cell28.setCellValue("");
        cell28.setCellStyle(style);
        XSSFCell cell29 = row.createCell(29);
        cell29.setCellValue("");
        cell29.setCellStyle(style);
        XSSFCell cell30 = row.createCell(30);
        cell30.setCellValue("补贴信息");
        cell30.setCellStyle(style);
        XSSFCell cell31 = row.createCell(31);
        cell31.setCellValue("");
        cell31.setCellStyle(style);
        XSSFCell cell32 = row.createCell(32);
        cell32.setCellValue("备注");
        cell32.setCellStyle(style);

        XSSFRow row2 = sheet.createRow(1);
        XSSFCell cell220 = row2.createCell(0);
        cell220.setCellValue("");
        cell220.setCellStyle(style);
        XSSFCell cell221 = row2.createCell(1);
        cell221.setCellValue("");
        cell221.setCellStyle(style);
        XSSFCell cell222 = row2.createCell(2);
        cell222.setCellValue("");
        cell222.setCellStyle(style);
        XSSFCell cell223 = row2.createCell(3);
        cell223.setCellValue("");
        cell223.setCellStyle(style);
        XSSFCell cell224 = row2.createCell(4);
        cell224.setCellValue("");
        cell224.setCellStyle(style);
        XSSFCell cell225 = row2.createCell(5);
        cell225.setCellValue("");
        cell225.setCellStyle(style);
        XSSFCell cell226 = row2.createCell(6);
        cell226.setCellValue("");
        cell226.setCellStyle(style);
        XSSFCell cell227 = row2.createCell(7);
        cell227.setCellValue("");
        cell227.setCellStyle(style);
        XSSFCell cell228 = row2.createCell(8);
        cell228.setCellValue("");
        cell228.setCellStyle(style);
        XSSFCell cell229 = row2.createCell(9);
        cell229.setCellValue("");
        cell229.setCellStyle(style);
        XSSFCell cell2210 = row2.createCell(10);
        cell2210.setCellValue("");
        cell2210.setCellStyle(style);
        XSSFCell cell2211 = row2.createCell(11);
        cell2211.setCellValue("");
        cell2211.setCellStyle(style);
        XSSFCell cell2212 = row2.createCell(12);
        cell2212.setCellValue("");
        cell2212.setCellStyle(style);
        XSSFCell cell2213 = row2.createCell(13);
        cell2213.setCellValue("");
        cell2213.setCellStyle(style);
        XSSFCell cell2214 = row2.createCell(14);
        cell2214.setCellValue("");
        cell2214.setCellStyle(style);
        XSSFCell cell2215 = row2.createCell(15);
        cell2215.setCellValue("");
        cell2215.setCellStyle(style);
        XSSFCell cell2216 = row2.createCell(16);
        cell2216.setCellValue("");
        cell2216.setCellStyle(style);
        XSSFCell cell2217 = row2.createCell(17);
        cell2217.setCellValue("货主");
        cell2217.setCellStyle(style);
        XSSFCell cell2218 = row2.createCell(18);
        cell2218.setCellValue("境内货源地/目的地");
        cell2218.setCellStyle(style);
        XSSFCell cell2219 = row2.createCell(19);
        cell2219.setCellValue("报关单号");
        cell2219.setCellStyle(style);
        XSSFCell cell2220 = row2.createCell(20);
        cell2220.setCellValue("货值(美金)");
        cell2220.setCellStyle(style);
        XSSFCell cell2221 = row2.createCell(21);
        cell2221.setCellValue("海关封");
        cell2221.setCellStyle(style);
        XSSFCell cell2222 = row2.createCell(22);
        cell2222.setCellValue("车号");
        cell2222.setCellStyle(style);
        XSSFCell cell2223 = row2.createCell(23);
        cell2223.setCellValue("订单需求号");
        cell2223.setCellStyle(style);
        XSSFCell cell2224 = row2.createCell(24);
        cell2224.setCellValue("国联订单号");
        cell2224.setCellStyle(style);
        XSSFCell cell2225 = row2.createCell(25);
        cell2225.setCellValue("境内运费(人民币)");
        cell2225.setCellStyle(style);
        XSSFCell cell2226 = row2.createCell(26);
        cell2226.setCellValue("境外运费(人民币)");
        cell2226.setCellStyle(style);
        XSSFCell cell2227 = row2.createCell(27);
        cell2227.setCellValue("境外运费(原币)");
        cell2227.setCellStyle(style);
        XSSFCell cell2228 = row2.createCell(28);
        cell2228.setCellValue("境外汇率");
        cell2228.setCellStyle(style);
        XSSFCell cell2229 = row2.createCell(29);
        cell2229.setCellValue("境外币种");
        cell2229.setCellStyle(style);
        XSSFCell cell2230 = row2.createCell(30);
        cell2230.setCellValue("补贴标准");
        cell2230.setCellStyle(style);
        XSSFCell cell2231 = row2.createCell(31);
        cell2231.setCellValue("补贴金额");
        cell2231.setCellStyle(style);
        XSSFCell cell2232 = row2.createCell(32);
        cell2232.setCellValue("箱备注");
        cell2232.setCellStyle(style);
        //合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 1, 1));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 2, 2));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 3, 3));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 4, 4));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 5, 5));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 6, 6));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 7, 7));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 8, 8));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 9, 9));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 10, 10));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 11, 11));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 12, 12));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 13, 13));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 14, 14));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 15, 15));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 16, 16));

        sheet.addMergedRegion(new CellRangeAddress(0, 0, 17, 24));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 25, 29));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 30, 31));
    }

    public XSSFCellStyle setStyle(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");
        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);
        return style;
    }

    public R importBasicData2(MultipartFile file, String accountCode, String shiftNo) {
        R r = new R();
        try {
            String originalFilename = file.getOriginalFilename();
            InputStream inputStream = file.getInputStream();
            // 调用工具类中方法，读取excel文件中数据
            long i = System.currentTimeMillis();
            r = readExcel(originalFilename, inputStream, accountCode, shiftNo);
            inputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
            return new R<>(new Throwable(e));
        }
        return r;
    }

    public R importBasicDataTwo(MultipartFile file, String accountCode, String shiftNo) {
        R r = new R();
        try {
            String originalFilename = file.getOriginalFilename();
            InputStream inputStream = file.getInputStream();
            // 调用工具类中方法，读取excel文件中数据
            long i = System.currentTimeMillis();
            r = readExcelTwo(originalFilename, inputStream, accountCode, shiftNo);
            inputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
            return new R<>(new Throwable(e));
        }
        return r;
    }

    /**
     * 读取excel文件数据
     *
     * @param fileName
     * @param inputStream
     * @return
     * @throws Exception
     */
    public R readExcel(String fileName, InputStream inputStream, String accountCode, String shiftNo) throws Exception {
        R r = new R();
        boolean ret = isXls(fileName);
        Workbook workbook = null;
        // 根据后缀创建不同的对象
        if (ret) {
            workbook = new HSSFWorkbook(inputStream);
        } else {
            workbook = new XSSFWorkbook(inputStream);
        }
        Sheet sheet = getAccuracyContextNum(workbook);
        List<FdShippingAccoundetail> list = new ArrayList<>();
        int lastRowNum = sheet.getLastRowNum();

        ContainerTypeData sel = new ContainerTypeData();
        sel.setDeleteFlag("N");
        List<ContainerTypeData> containerTypeDataList = containerTypeDataService.selectContainerTypeDataList(sel);
        String countryListData = remoteAdminService.selectDictByType2("country_type");
        List<SysDictVo> countryList = JSONUtil.toList(JSONUtil.parseArray(countryListData), SysDictVo.class);

        String platformCode = platformCheckMapper.getPlatformCode("DDHCBCZL");

        if (lastRowNum > 0) {
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            for (int i = 2; i <= lastRowNum; i++) {
                FdShippingAccoundetail fdShippingAccoundetail = new FdShippingAccoundetail();
                Row row = sheet.getRow(i);
                //箱号
                if (row.getCell(1) != null) {
                    row.getCell(1).setCellType(CellType.STRING);
                    if (!"".equals(row.getCell(1).getStringCellValue()) && row.getCell(1).getStringCellValue() != null) {
                        fdShippingAccoundetail.setContainerNumber(row.getCell(1).getStringCellValue().replaceAll(" ", ""));
                    } else {
                        return R.error("箱号为空");
                    }
                    //类型
                    if (row.getCell(2) != null) {
                        row.getCell(2).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(2).getStringCellValue()) && row.getCell(2).getStringCellValue() != null) {
                            if (row.getCell(2).getStringCellValue().equals("出口")) {
                                fdShippingAccoundetail.setIsRansit("E");
                            } else if (row.getCell(2).getStringCellValue().equals("进口")) {
                                fdShippingAccoundetail.setIsRansit("I");
                            } else if (row.getCell(2).getStringCellValue().equals("过境")) {
                                fdShippingAccoundetail.setIsRansit("P");
                            }
                        } else {
                            return R.error("类型为空");
                        }
                    } else {
                        return R.error("类型为空");
                    }
                    //货源组织单位
                    if (row.getCell(3) != null) {
                        row.getCell(3).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(3).getStringCellValue()) && row.getCell(3).getStringCellValue() != null) {
                            fdShippingAccoundetail.setOrgUnit(row.getCell(3).getStringCellValue().replaceAll(" ", ""));
                        } else {
                            return R.error("货源组织单位为空");
                        }
                    } else {
                        return R.error("货源组织单位为空");
                    }

                    //收货人
                    if (row.getCell(4) != null) {
                        row.getCell(4).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(4).getStringCellValue()) && row.getCell(4).getStringCellValue() != null) {
                            fdShippingAccoundetail.setConsignorName(row.getCell(4).getStringCellValue().replaceAll(" ", ""));
                        } else {
                            return R.error("收货人为空");
                        }
                    } else {
                        return R.error("收货人为空");
                    }

                    //发站
                    if (row.getCell(5) != null) {
                        row.getCell(5).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(5).getStringCellValue()) && row.getCell(5).getStringCellValue() != null) {
                            fdShippingAccoundetail.setDestinationName(row.getCell(5).getStringCellValue().replaceAll(" ", ""));
                        } else {
                            return R.error("发站为空");
                        }
                    } else {
                        return R.error("发站为空");
                    }

                    //目的国
                    if (row.getCell(6) != null) {
                        row.getCell(6).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(6).getStringCellValue()) && row.getCell(6).getStringCellValue() != null) {
                            fdShippingAccoundetail.setDestinationCountry(row.getCell(6).getStringCellValue().replaceAll(" ", ""));
                            String destinationCountryCode = Optional.ofNullable(countryList)
                                    .flatMap(l -> l.stream()
                                            .filter(item -> fdShippingAccoundetail.getDestinationCountry() != null && fdShippingAccoundetail.getDestinationCountry().equals(item.getName()))
                                            .map(SysDictVo::getCode)
                                            .findFirst())
                                    .orElse(null);
                            ;
                            fdShippingAccoundetail.setDestinationCountryCode(destinationCountryCode);
                        } else {
                            return R.error("目的国为空");
                        }
                    } else {
                        return R.error("目的国为空");
                    }

                    //到站
                    if (row.getCell(7) != null) {
                        row.getCell(7).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(7).getStringCellValue()) && row.getCell(7).getStringCellValue() != null) {
                            fdShippingAccoundetail.setDestination(row.getCell(7).getStringCellValue().replaceAll(" ", ""));
                        } else {
                            return R.error("到站为空");
                        }
                    } else {
                        return R.error("到站为空");
                    }

                    //口岸代理
                    if (row.getCell(8) != null) {
                        row.getCell(8).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(8).getStringCellValue()) && row.getCell(8).getStringCellValue() != null) {
                            fdShippingAccoundetail.setPortAgent(row.getCell(8).getStringCellValue().replaceAll(" ", ""));
                        }
                        /*else {
                            return R.error( "口岸代理为空");
                        }*/
                    }
                    /*else {
                        return R.error( "口岸代理为空");
                    }*/

                    //品名
                    if (row.getCell(9) != null) {
                        row.getCell(9).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(9).getStringCellValue()) && row.getCell(9).getStringCellValue() != null) {
                            fdShippingAccoundetail.setGoodsName(row.getCell(9).getStringCellValue().replaceAll(" ", ""));
                        } else {
                            return R.error("品名为空");
                        }
                    } else {
                        return R.error("品名为空");
                    }

                    //箱属
                    if (row.getCell(10) != null) {
                        row.getCell(10).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(10).getStringCellValue()) && row.getCell(10).getStringCellValue() != null) {
                            if (row.getCell(10).getStringCellValue().equals("自备箱")) {
                                fdShippingAccoundetail.setContainerOwner("0");
                            } else if (row.getCell(10).getStringCellValue().equals("中铁箱")) {
                                fdShippingAccoundetail.setContainerOwner("1");
                            }
                        } else {
                            return R.error("箱属为空");
                        }
                    } else {
                        return R.error("箱属为空");
                    }

                    //箱型
                    if (row.getCell(11) != null) {
                        row.getCell(11).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(11).getStringCellValue()) && row.getCell(11).getStringCellValue() != null) {
                            String containerTypeCode = row.getCell(11).getStringCellValue().replaceAll(" ", "");
                            Boolean flag = true;
                            for (ContainerTypeData data : containerTypeDataList) {
                                if (data.getContainerTypeCode().equals(containerTypeCode)) {
                                    fdShippingAccoundetail.setContainerTypeCode(data.getContainerTypeCode());
                                    fdShippingAccoundetail.setContainerTypeName(data.getContainerTypeName());
                                    fdShippingAccoundetail.setContainerType(data.getContainerTypeSize());
                                    flag = false;
                                    break;
                                }
                            }

                            if (flag) {
                                return R.error("未查询到该箱型代码：" + containerTypeCode);
                            }
                        } else {
                            return R.error("箱型代码为空");
                        }
                    } else {
                        return R.error("箱型代码为空");
                    }

                    //件数
                    if (row.getCell(12) != null) {
                        row.getCell(12).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(12).getStringCellValue()) && row.getCell(12).getStringCellValue() != null) {
                            double doubleValue = Double.parseDouble(row.getCell(12).getStringCellValue().replaceAll(" ", ""));
                            int intValue = (int) Math.floor(doubleValue);
                            fdShippingAccoundetail.setGoodsNums(intValue);
                        } else {
                            return R.error("件数为空");
                        }
                    } else {
                        return R.error("件数为空");
                    }

                    //货重
                    if (row.getCell(13) != null) {
                        row.getCell(13).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(13).getStringCellValue()) && row.getCell(13).getStringCellValue() != null) {
                            fdShippingAccoundetail.setGoodsWeight(Double.valueOf(row.getCell(13).getStringCellValue().replaceAll(" ", "")));
                        } else {
                            return R.error("货重为空");
                        }
                    } else {
                        return R.error("货重为空");
                    }

                    //箱重
                    if (row.getCell(14) != null) {
                        row.getCell(14).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(14).getStringCellValue()) && row.getCell(14).getStringCellValue() != null) {
                            fdShippingAccoundetail.setContainerWeight(row.getCell(14).getStringCellValue().replaceAll(" ", ""));
                        } else {
                            return R.error("箱重为空");
                        }
                    } else {
                        return R.error("箱重为空");
                    }

                    //是否全程
                    if (row.getCell(15) != null) {
                        row.getCell(15).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(15).getStringCellValue()) && row.getCell(15).getStringCellValue() != null) {
                            if (row.getCell(15).getStringCellValue().equals("否")) {
                                fdShippingAccoundetail.setIsFull("0");
                            } else if (row.getCell(15).getStringCellValue().equals("是")) {
                                fdShippingAccoundetail.setIsFull("1");
                            }
                        }
                    }
                    if (platformCode.contains(SecurityUtils.getUserInfo().getPlatformCode())) {
                        if (StrUtil.isBlank(fdShippingAccoundetail.getIsFull())) {
                            return R.error("是否全程为空");
                        }
                    }

                    //有色金属
                    if (row.getCell(16) != null) {
                        row.getCell(16).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(16).getStringCellValue()) && row.getCell(16).getStringCellValue() != null) {
                            if (row.getCell(16).getStringCellValue().equals("否")) {
                                fdShippingAccoundetail.setNonFerrous("0");
                            } else if (row.getCell(16).getStringCellValue().equals("是")) {
                                fdShippingAccoundetail.setNonFerrous("1");
                            }
                        } else {
                            return R.error("有色金属为空");
                        }
                    } else {
                        return R.error("有色金属为空");
                    }

                    //货主
                    if (row.getCell(17) != null) {
                        row.getCell(17).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(17).getStringCellValue()) && row.getCell(17).getStringCellValue() != null) {
                            fdShippingAccoundetail.setGoodsOwner(row.getCell(17).getStringCellValue().replaceAll(" ", ""));
                        }
                    }

                    //境内货源地/目的地
                    if (row.getCell(18) != null) {
                        row.getCell(18).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(18).getStringCellValue()) && row.getCell(18).getStringCellValue() != null) {
                            fdShippingAccoundetail.setGoodsOrigin(row.getCell(18).getStringCellValue().replaceAll(" ", ""));
                        }
                    }

                    //报关单号
                    if (row.getCell(19) != null) {
                        row.getCell(19).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(19).getStringCellValue()) && row.getCell(19).getStringCellValue() != null) {
                            fdShippingAccoundetail.setClearanceNumber(row.getCell(19).getStringCellValue().replaceAll(" ", ""));
                        }
                    }
                    //货值(美金)
                    if (row.getCell(20) != null) {
                        row.getCell(20).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(20).getStringCellValue())) {
                            try {
                                String value = row.getCell(20).getStringCellValue();
                                BigDecimal bigDecimal = BigDecimal.valueOf(Double.parseDouble(value));
                                bigDecimal.setScale(2, RoundingMode.HALF_UP);
                                fdShippingAccoundetail.setValueUsd(bigDecimal);
                            } catch (Exception e) {
                                return R.error("货值(美金)数值异常，请检查数据！" + e.getMessage());
                            }
                        }
                    }

                    //海关封
                    if (row.getCell(21) != null) {
                        row.getCell(21).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(21).getStringCellValue()) && row.getCell(21).getStringCellValue() != null) {
                            fdShippingAccoundetail.setCustomsSeal(row.getCell(21).getStringCellValue().replaceAll(" ", ""));
                        }
                    }

                    //车号
                    if (row.getCell(22) != null) {
                        row.getCell(22).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(22).getStringCellValue()) && row.getCell(22).getStringCellValue() != null) {
                            fdShippingAccoundetail.setTrainNumber(row.getCell(22).getStringCellValue().replaceAll(" ", ""));
                        }
                    }

                    //订单需求号
                    if (row.getCell(23) != null) {
                        row.getCell(23).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(23).getStringCellValue()) && row.getCell(23).getStringCellValue() != null) {
                            fdShippingAccoundetail.setWaybillDemandNumber(row.getCell(23).getStringCellValue().replaceAll(" ", ""));
                        }
                    }

                    //国联订单号
                    if (row.getCell(24) != null) {
                        row.getCell(24).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(24).getStringCellValue()) && row.getCell(24).getStringCellValue() != null) {
                            fdShippingAccoundetail.setWaybillLnNumber(row.getCell(24).getStringCellValue().replaceAll(" ", ""));
                        }
                    }

                    if (row.getCell(25) != null) {
                        row.getCell(25).setCellType(CellType.STRING);
                        try {
                            String value = row.getCell(25).getStringCellValue();
                            BigDecimal bigDecimal = BigDecimal.valueOf(Double.parseDouble(value));
                            bigDecimal.setScale(2, RoundingMode.HALF_UP);
                            fdShippingAccoundetail.setDomesticFreight(bigDecimal);
                        } catch (Exception e) {
                            return R.error("境内运费(人民币)数值异常，请检查数据！" + e.getMessage());
                        }
                    }

                    //境外运费(人民币)
                    if (row.getCell(26) != null) {
                        row.getCell(26).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(26).getStringCellValue())) {
                            try {
                                String value = row.getCell(26).getStringCellValue();
                                BigDecimal bigDecimal = BigDecimal.valueOf(Double.parseDouble(value));
                                bigDecimal.setScale(2, RoundingMode.HALF_UP);
                                fdShippingAccoundetail.setOverseasFreightCny(bigDecimal);
                            } catch (Exception e) {
                                return R.error("境外运费(人民币)数值异常，请检查数据！" + e.getMessage());
                            }
                        }


                    }

                    if (row.getCell(27) != null) {
                        row.getCell(27).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(27).getStringCellValue())) {
                            try {
                                String value = row.getCell(27).getStringCellValue();
                                BigDecimal bigDecimal = BigDecimal.valueOf(Double.parseDouble(value));
                                bigDecimal.setScale(2, RoundingMode.HALF_UP);
                                fdShippingAccoundetail.setOverseasFreightOc(bigDecimal);
                            } catch (Exception e) {
                                return R.error("境外运费(原币)数值异常，请检查数据！" + e.getMessage());
                            }
                        }
                    }

                    if (row.getCell(28) != null) {
                        row.getCell(28).setCellType(CellType.STRING);
                        try {
                            String value = row.getCell(28).getStringCellValue();
                            BigDecimal bigDecimal = BigDecimal.valueOf(Double.parseDouble(value));
                            bigDecimal.setScale(4, RoundingMode.HALF_UP);
                            fdShippingAccoundetail.setExchangeRate(bigDecimal);
                        } catch (Exception e) {
                            return R.error("境外汇率数值异常，请检查数据！" + e.getMessage());
                        }

                    }

                    //境外币种
                    if (row.getCell(29) != null) {
                        row.getCell(29).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(29).getStringCellValue()) && row.getCell(29).getStringCellValue() != null) {
                            fdShippingAccoundetail.setMonetaryType(row.getCell(29).getStringCellValue().replaceAll(" ", ""));
                        }
                    }

                    //补贴标准
                    /*if(row.getCell(30)!=null) {
                        row.getCell(30).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(30).getStringCellValue()) && row.getCell(30).getStringCellValue() != null) {
                            fdShippingAccoundetail.setSubsidyStandards(row.getCell(30).getStringCellValue().replaceAll(" ",""));
                        }
                    }*/

                    //补贴金额
                    /*if(row.getCell(31)!=null) {
                        row.getCell(31).setCellType(CellType.STRING);
                        try {
                            String value = row.getCell(31).getStringCellValue();
                            BigDecimal bigDecimal = BigDecimal.valueOf(Double.parseDouble(value));
                            bigDecimal.setScale(2, RoundingMode.HALF_UP);
                            fdShippingAccoundetail.setSubsidyAmount(bigDecimal);
                        }catch (Exception e){
                            return R.error( "补贴金额数值异常，请检查数据！"+e.getMessage());
                        }
                    }*/

                    //箱备注
                    if (row.getCell(32) != null) {
                        row.getCell(32).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(32).getStringCellValue()) && row.getCell(32).getStringCellValue() != null) {
                            fdShippingAccoundetail.setRemarks(row.getCell(32).getStringCellValue().replaceAll(" ", ""));
                        }
                    }
                    fdShippingAccoundetail.setDeleteFlag("N");
                    fdShippingAccoundetail.setAddTime(LocalDateTime.now());
                    fdShippingAccoundetail.setAddWho(userInfo.getUserName());
                    fdShippingAccoundetail.setAddWhoName(userInfo.getRealName());
                    fdShippingAccoundetail.setContainerNewestStatus("0");
                    if (StrUtil.isNotEmpty(accountCode) && !accountCode.equals("undefined")) {
                        fdShippingAccoundetail.setAccountCode(accountCode);
                        FdShippingAccoundetail fdShipping = fdShippingAccoundetailMapper.selectAccountCodeAndContainerNumber(fdShippingAccoundetail);
                        if (fdShipping != null) {
//                            fdShippingAccoundetail.setExchangeRate(fdShipping.getExchangeRate());
                            fdShippingAccoundetail.setRowId(fdShipping.getRowId());
                            fdShippingAccoundetail.setCustomerNo(fdShipping.getCustomerNo());
                            fdShippingAccoundetail.setCustomerName(fdShipping.getCustomerName());
                            fdShippingAccoundetail.setApplicationNumber(fdShipping.getApplicationNumber());
                            fdShippingAccoundetail.setTransportOrderNumber(fdShipping.getTransportOrderNumber());
                        } else {
                            return R.error("请查看箱" + fdShippingAccoundetail.getContainerNumber() + "是否存在");
                        }
                    } else {
                        WaybillContainerInfo waybillContainerInfo = new WaybillContainerInfo();
                        waybillContainerInfo.setShiftNo(shiftNo);
                        waybillContainerInfo.setContainerNo(fdShippingAccoundetail.getContainerNumber());
                        WaybillContainerInfo waybillContainerInfo1 = waybillContainerInfoMapper.selectOrderNoAndWaybillNoByContainerNo(waybillContainerInfo);
                        if (waybillContainerInfo1 != null) {
                            fdShippingAccoundetail.setApplicationNumber(waybillContainerInfo1.getOrderNo());
                            fdShippingAccoundetail.setTransportOrderNumber(waybillContainerInfo1.getWaybillNo());
                        } else {
                            return R.error("根据箱号查询申请单号和运单号为空");
                        }
                    }
                    list.add(fdShippingAccoundetail);
                }
            }
        } else {
            return R.error("表格为空");
        }
        workbook.close();
        r.setData(list);
        r.setMsg("上传成功");
        r.setCode(0);
        return r;
    }

    public R readExcelTwo(String fileName, InputStream inputStream, String accountCode, String shiftNo) throws Exception {
        R r = new R();
        boolean ret = isXls(fileName);
        Workbook workbook = null;
        // 根据后缀创建不同的对象
        if (ret) {
            workbook = new HSSFWorkbook(inputStream);
        } else {
            workbook = new XSSFWorkbook(inputStream);
        }
        Sheet sheet = getAccuracyContextNum(workbook);
        List<FdShippingAccoundetail> list = new ArrayList<>();
        int lastRowNum = sheet.getLastRowNum();

        ContainerTypeData sel = new ContainerTypeData();
        sel.setDeleteFlag("N");
        List<ContainerTypeData> containerTypeDataList = containerTypeDataService.selectContainerTypeDataList(sel);
        String countryListData = remoteAdminService.selectDictByType2("country_type");
        List<SysDictVo> countryList = JSONUtil.toList(JSONUtil.parseArray(countryListData), SysDictVo.class);

        String platformCode = platformCheckMapper.getPlatformCode("DDHCBCZL");

        StringBuffer sb = new StringBuffer();
        if (lastRowNum > 0) {
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            for (int i = 3; i <= lastRowNum; i++) {
                FdShippingAccoundetail fdShippingAccoundetail = new FdShippingAccoundetail();
                Row row = sheet.getRow(i);
                //箱号
                if (row.getCell(11) != null) {
//                    row.getCell(11).setCellType(CellType.STRING);
                    if (StrUtil.isNotBlank(row.getCell(11).getStringCellValue())) {
                        fdShippingAccoundetail.setContainerNumber(row.getCell(11).getStringCellValue().replaceAll(" ", ""));
                    } else {
                        sb.append("第" + (i + 1) + "行箱号不能为空");
                    }
                    //类型
                    if (row.getCell(1) != null) {
//                        row.getCell(1).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(1).getStringCellValue())) {
                            if (row.getCell(1).getStringCellValue().equals("出口")) {
                                fdShippingAccoundetail.setIsRansit("E");
                            } else if (row.getCell(1).getStringCellValue().equals("进口")) {
                                fdShippingAccoundetail.setIsRansit("I");
                            } else if (row.getCell(1).getStringCellValue().equals("过境")) {
                                fdShippingAccoundetail.setIsRansit("P");
                            }
                        } else {
                            sb.append("第" + (i + 1) + "行类型不能为空");
                        }
                    } else {
                        sb.append("第" + (i + 1) + "行类型不能为空");
                    }
                    //货源组织单位
                    if (row.getCell(2) != null) {
//                        row.getCell(2).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(2).getStringCellValue())) {
                            fdShippingAccoundetail.setOrgUnit(row.getCell(2).getStringCellValue().replaceAll(" ", ""));
                        } else {
                            sb.append("第" + (i + 1) + "行货源组织单位不能为空");
                        }
                    } else {
                        sb.append("第" + (i + 1) + "行货源组织单位不能为空");
                    }

                    //收货人
                    if (row.getCell(3) != null) {
//                        row.getCell(3).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(3).getStringCellValue())) {
                            fdShippingAccoundetail.setConsignorName(row.getCell(3).getStringCellValue().replaceAll(" ", ""));
                        } else {
                            sb.append("第" + (i + 1) + "行收货人不能为空");
                        }
                    } else {
                        sb.append("第" + (i + 1) + "行收货人不能为空");
                    }

                    //发站
                    if (row.getCell(4) != null) {
//                        row.getCell(4).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(4).getStringCellValue())) {
                            fdShippingAccoundetail.setDestinationName(row.getCell(4).getStringCellValue().replaceAll(" ", ""));
                        } else {
                            sb.append("第" + (i + 1) + "行发站不能为空");
                        }
                    } else {
                        sb.append("第" + (i + 1) + "行发站不能为空");
                    }

                    //目的国
                    if (row.getCell(5) != null) {
//                        row.getCell(5).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(5).getStringCellValue())) {
                            fdShippingAccoundetail.setDestinationCountry(row.getCell(5).getStringCellValue().replaceAll(" ", ""));
                            String destinationCountryCode = Optional.ofNullable(countryList)
                                    .flatMap(l -> l.stream()
                                            .filter(item -> fdShippingAccoundetail.getDestinationCountry() != null && fdShippingAccoundetail.getDestinationCountry().equals(item.getName()))
                                            .map(SysDictVo::getCode)
                                            .findFirst())
                                    .orElse(null);
                            ;
                            fdShippingAccoundetail.setDestinationCountryCode(destinationCountryCode);
                        } else {
                            sb.append("第" + (i + 1) + "行目的国不能为空");
                        }
                    } else {
                        sb.append("第" + (i + 1) + "行目的国不能为空");
                    }

                    //到站
                    if (row.getCell(6) != null) {
//                        row.getCell(6).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(6).getStringCellValue())) {
                            fdShippingAccoundetail.setDestination(row.getCell(6).getStringCellValue().replaceAll(" ", ""));
                        } else {
                            sb.append("第" + (i + 1) + "行到站不能为空");
                        }
                    } else {
                        sb.append("第" + (i + 1) + "行到站不能为空");
                    }

                    //口岸代理
                    if (row.getCell(7) != null) {
//                        row.getCell(7).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(7).getStringCellValue())) {
                            fdShippingAccoundetail.setPortAgent(row.getCell(7).getStringCellValue().replaceAll(" ", ""));
                        }
                        /*else {
                            return new R(Boolean.FALSE, "口岸代理为空");
                        }*/
                    }
                    /*else {
                        return new R(Boolean.FALSE, "口岸代理为空");
                    }*/

                    //品名
                    if (row.getCell(8) != null) {
//                        row.getCell(8).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(8).getStringCellValue())) {
                            fdShippingAccoundetail.setGoodsName(row.getCell(8).getStringCellValue().replaceAll(" ", ""));
                        } else {
                            sb.append("第" + (i + 1) + "行品名不能为空");
                        }
                    } else {
                        sb.append("第" + (i + 1) + "行品名不能为空");
                    }

                    //箱属
                    if (row.getCell(10) != null) {
//                        row.getCell(10).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(10).getStringCellValue())) {
                            if (row.getCell(10).getStringCellValue().equals("自备箱")) {
                                fdShippingAccoundetail.setContainerOwner("0");
                            } else if (row.getCell(10).getStringCellValue().equals("中铁箱")) {
                                fdShippingAccoundetail.setContainerOwner("1");
                            }
                        } else {
                            sb.append("第" + (i + 1) + "行箱属不能为空");
                        }
                    } else {
                        sb.append("第" + (i + 1) + "行箱属不能为空");
                    }

                    //箱型
                    if (row.getCell(9) != null) {
                        row.getCell(9).setCellType(CellType.STRING);
                        String value = row.getCell(9).toString();
                        if (StrUtil.isNotBlank(value)) {
                            String containerTypeCode = value.replaceAll(" ", "");
                            if ("20".equals(containerTypeCode) || "40".equals(containerTypeCode) || "45".equals(containerTypeCode)) {
                                containerTypeCode = containerTypeCode + "GP";
                            }
                            Boolean flag = true;
                            for (ContainerTypeData data : containerTypeDataList) {
                                if (data.getContainerTypeCode().equals(containerTypeCode)) {
                                    fdShippingAccoundetail.setContainerTypeCode(data.getContainerTypeCode());
                                    fdShippingAccoundetail.setContainerTypeName(data.getContainerTypeName());
                                    fdShippingAccoundetail.setContainerType(data.getContainerTypeSize());
                                    flag = false;
                                    break;
                                }
                            }

                            if (flag) {
                                sb.append("第" + (i + 1) + "行未查询到该箱型代码：" + containerTypeCode);
                            }
                        } else {
                            sb.append("第" + (i + 1) + "行箱型代码不能为空");
                        }
                    } else {
                        sb.append("第" + (i + 1) + "行箱型代码不能为空");
                    }

                    //件数
                    if (row.getCell(12) != null) {
                        row.getCell(12).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(12).getStringCellValue())) {
                            double doubleValue = Double.parseDouble(row.getCell(12).getStringCellValue().replaceAll(" ", ""));
                            int intValue = (int) Math.floor(doubleValue);
                            fdShippingAccoundetail.setGoodsNums(intValue);
                        } else {
                            sb.append("第" + (i + 1) + "行件数不能为空");
                        }
                    } else {
                        sb.append("第" + (i + 1) + "行件数不能为空");
                    }

                    //货重
                    if (row.getCell(13) != null) {
                        row.getCell(13).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(13).getStringCellValue())) {
                            fdShippingAccoundetail.setGoodsWeight(Double.valueOf(row.getCell(13).getStringCellValue().replaceAll(" ", "")));
                        } else {
                            sb.append("第" + (i + 1) + "行货重不能为空");
                        }
                    } else {
                        sb.append("第" + (i + 1) + "行货重不能为空");
                    }

                    //箱重
                    if (row.getCell(14) != null) {
                        String containerWeight = row.getCell(14).toString();
                        if (StrUtil.isNotBlank(containerWeight)) {
                            fdShippingAccoundetail.setContainerWeight(containerWeight.replaceAll(" ", ""));
                        } else {
                            sb.append("第" + (i + 1) + "行箱重不能为空");
                        }
                    } else {
                        sb.append("第" + (i + 1) + "行箱重不能为空");
                    }

                    //是否全程
                    if (row.getCell(15) != null) {
//                        row.getCell(15).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(15).getStringCellValue()) && row.getCell(15).getStringCellValue() != null) {
                            if (row.getCell(15).getStringCellValue().equals("否")) {
                                fdShippingAccoundetail.setIsFull("0");
                            } else if (row.getCell(15).getStringCellValue().equals("是")) {
                                fdShippingAccoundetail.setIsFull("1");
                            }
                        }
                    }
                    if (platformCode.contains(SecurityUtils.getUserInfo().getPlatformCode())) {
                        if (StrUtil.isBlank(fdShippingAccoundetail.getIsFull())) {
                            sb.append("第" + (i + 1) + "行是否全程不能为空");
                        }
                    }

                    //有色金属
                    if (row.getCell(16) != null) {
//                        row.getCell(16).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(16).getStringCellValue())) {
                            if (row.getCell(16).getStringCellValue().equals("否")) {
                                fdShippingAccoundetail.setNonFerrous("0");
                            } else if (row.getCell(16).getStringCellValue().equals("是")) {
                                fdShippingAccoundetail.setNonFerrous("1");
                            }
                        } else {
                            sb.append("第" + (i + 1) + "行有色金属不能为空");
                        }
                    } else {
                        sb.append("第" + (i + 1) + "行有色金属不能为空");
                    }

                    //货主
                    if (row.getCell(17) != null) {
//                        row.getCell(17).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(17).getStringCellValue())) {
                            fdShippingAccoundetail.setGoodsOwner(row.getCell(17).getStringCellValue().replaceAll(" ", ""));
                        }
                    }

                    //境内货源地/目的地
                    if (row.getCell(18) != null) {
//                        row.getCell(18).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(18).getStringCellValue())) {
                            fdShippingAccoundetail.setGoodsOrigin(row.getCell(18).getStringCellValue().replaceAll(" ", ""));
                        }
                    }

                    //报关单号
                    if (row.getCell(19) != null) {
                        row.getCell(19).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(19).getStringCellValue()) && row.getCell(19).getStringCellValue() != null) {
                            fdShippingAccoundetail.setClearanceNumber(row.getCell(19).getStringCellValue().replaceAll(" ", ""));
                        }
                    }
                    //货值(美金)
                    if (row.getCell(20) != null) {
                        row.getCell(20).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(20).getStringCellValue())) {
                            try {
                                String value = row.getCell(20).getStringCellValue();
                                BigDecimal bigDecimal = BigDecimal.valueOf(Double.parseDouble(value));
                                bigDecimal.setScale(2, RoundingMode.HALF_UP);
                                fdShippingAccoundetail.setValueUsd(bigDecimal);
                            } catch (Exception e) {
                                sb.append("第" + (i + 1) + "行货值(美金)数值异常，请检查数据！" + e.getMessage());
                            }
                        }
                    }

                    //海关封
                    if (row.getCell(21) != null) {
                        row.getCell(21).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(21).getStringCellValue())) {
                            fdShippingAccoundetail.setCustomsSeal(row.getCell(21).getStringCellValue().replaceAll(" ", ""));
                        }
                    }

                    //车号
                    if (row.getCell(22) != null) {
                        row.getCell(22).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(22).getStringCellValue()) && row.getCell(22).getStringCellValue() != null) {
                            fdShippingAccoundetail.setTrainNumber(row.getCell(22).getStringCellValue().replaceAll(" ", ""));
                        }
                    }

                    //订单需求号
                    if (row.getCell(23) != null) {
                        row.getCell(23).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(23).getStringCellValue())) {
                            fdShippingAccoundetail.setWaybillDemandNumber(row.getCell(23).getStringCellValue().replaceAll(" ", ""));
                        }
                    }

                    //国联订单号
                    if (row.getCell(24) != null) {
                        row.getCell(24).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(24).getStringCellValue())) {
                            fdShippingAccoundetail.setWaybillLnNumber(row.getCell(24).getStringCellValue().replaceAll(" ", ""));
                        }
                    }

                    if (row.getCell(25) != null) {
                        row.getCell(25).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(25).getStringCellValue())) {
                            try {
                                String value = row.getCell(25).getStringCellValue();
                                BigDecimal bigDecimal = BigDecimal.valueOf(Double.parseDouble(value));
                                bigDecimal.setScale(2, RoundingMode.HALF_UP);
                                fdShippingAccoundetail.setDomesticFreight(bigDecimal);
                            } catch (Exception e) {
                                sb.append("第" + (i + 1) + "行境内运费(人民币)数值异常，请检查数据！" + e.getMessage());
                            }
                        }
                    }

                    //境外运费(人民币)
                    if (row.getCell(26) != null) {
                        row.getCell(26).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(26).getStringCellValue())) {
                            try {
                                String value = row.getCell(26).getStringCellValue();
                                BigDecimal bigDecimal = BigDecimal.valueOf(Double.parseDouble(value));
                                bigDecimal.setScale(2, RoundingMode.HALF_UP);
                                fdShippingAccoundetail.setOverseasFreightCny(bigDecimal);
                                fdShippingAccoundetail.setOverseasFreightOc(bigDecimal);
                                fdShippingAccoundetail.setExchangeRate(BigDecimal.valueOf(1));
                                fdShippingAccoundetail.setMonetaryType("人民币");
                            } catch (Exception e) {
                                sb.append("第" + (i + 1) + "行境外运费数值异常，请检查数据！" + e.getMessage());
                            }
                        }
                    }

                    //箱备注
                    if (row.getCell(30) != null) {
                        row.getCell(30).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(30).getStringCellValue()) && row.getCell(30).getStringCellValue() != null) {
                            fdShippingAccoundetail.setRemarks(row.getCell(30).getStringCellValue().replaceAll(" ", ""));
                        }
                    }
                    fdShippingAccoundetail.setDeleteFlag("N");
                    fdShippingAccoundetail.setAddTime(LocalDateTime.now());
                    fdShippingAccoundetail.setAddWho(userInfo.getUserName());
                    fdShippingAccoundetail.setAddWhoName(userInfo.getRealName());
                    fdShippingAccoundetail.setContainerNewestStatus("0");
                    if (StrUtil.isNotEmpty(accountCode) && !accountCode.equals("undefined")) {
                        fdShippingAccoundetail.setAccountCode(accountCode);
                        FdShippingAccoundetail fdShipping = fdShippingAccoundetailMapper.selectAccountCodeAndContainerNumber(fdShippingAccoundetail);
                        if (fdShipping != null) {
//                            fdShippingAccoundetail.setExchangeRate(fdShipping.getExchangeRate());
                            fdShippingAccoundetail.setRowId(fdShipping.getRowId());
                            fdShippingAccoundetail.setCustomerNo(fdShipping.getCustomerNo());
                            fdShippingAccoundetail.setCustomerName(fdShipping.getCustomerName());
                            fdShippingAccoundetail.setApplicationNumber(fdShipping.getApplicationNumber());
                            fdShippingAccoundetail.setTransportOrderNumber(fdShipping.getTransportOrderNumber());
                        } else {
                            sb.append("请查看箱" + fdShippingAccoundetail.getContainerNumber() + "是否存在");
                        }
                    } else {
                        WaybillContainerInfo waybillContainerInfo = new WaybillContainerInfo();
                        waybillContainerInfo.setShiftNo(shiftNo);
                        waybillContainerInfo.setContainerNo(fdShippingAccoundetail.getContainerNumber());
                        WaybillContainerInfo waybillContainerInfo1 = waybillContainerInfoMapper.selectOrderNoAndWaybillNoByContainerNo(waybillContainerInfo);
                        if (waybillContainerInfo1 != null) {
                            fdShippingAccoundetail.setApplicationNumber(waybillContainerInfo1.getOrderNo());
                            fdShippingAccoundetail.setTransportOrderNumber(waybillContainerInfo1.getWaybillNo());
                        } else {
                            sb.append("根据箱号查询申请单号和运单号为空");
                        }
                    }
                    list.add(fdShippingAccoundetail);
                }
            }
        } else {
            sb.append("表格为空");
        }
        if (sb.length() > 0) {
            return R.error(sb.toString());
        }
        workbook.close();
        r.setData(list);
        r.setMsg("上传成功");
        r.setCode(0);
        return r;
    }

    public static boolean isXls(String fileName) {
        // (?i)忽略大小写
        if (fileName.matches("^.+\\.(?i)(xls)$")) {
            return true;
        } else if (fileName.matches("^.+\\.(?i)(xlsx)$")) {
            return false;
        } else {
            throw new RuntimeException("格式不对");
        }
    }

    //获取准确的文件行数
    public Sheet getAccuracyContextNum(Workbook workbook) {
        // 取第一个sheet
        Sheet sheet = workbook.getSheetAt(0);
        // 删除空行
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            // 删除空行
            if (this.isRowEmpty(row)) {
                int lastRowNum = sheet.getLastRowNum();
                if (i >= 0 && i < lastRowNum) {
                    sheet.shiftRows(i + 1, lastRowNum, -1);// 将行号为i+1一直到行号为lastRowNum的单元格全部上移一行，以便删除i行
                }
                if (i == lastRowNum) {
                    if (row != null) {
                        sheet.removeRow(row);
                    }
                }
                i--;
            }
        }
        return sheet;
    }

    public boolean isRowEmpty(Row row) {
        if (row != null) {
            for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
                Cell cell = row.getCell(c);
                if (cell != null) {
                    cell.setCellType(CellType.STRING);
                }
                if (cell != null && !cell.getStringCellValue().equals("")) {
                    return false;//不是空行
                }
            }
        }
        return true;
    }

    @Override
    public int updateFdShippingAccoundetailByAccountCode3(FdShippingAccoundetail fdShippingAccoundetail) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        fdShippingAccoundetail.setUpdateWho(userInfo.getUserName());
        fdShippingAccoundetail.setUpdateWhoName(userInfo.getRealName());
        fdShippingAccoundetail.setUpdateTime(LocalDateTime.now());
        return fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode3(fdShippingAccoundetail);
    }

    @Override
    public List<FdShippingAccoundetail> selectGroupList(FdShippingAccoundetail fdShippingAccoundetail) {
        return fdShippingAccoundetailMapper.selectGroupList(fdShippingAccoundetail);
    }

    @Override
    public R updateByAccountCodeByGroup(List<FdShippingAccoundetail> fdShippingAccoundetails) {
        if (CollUtil.isNotEmpty(fdShippingAccoundetails)) {
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            for (FdShippingAccoundetail fdShippingAccoundetail : fdShippingAccoundetails) {
                if (fdShippingAccoundetail.getRrOverseasFreightCny() != null || fdShippingAccoundetail.getRrOverseasFreightOc() != null || fdShippingAccoundetail.getRrDomesticFreight() != null) {
                    fdShippingAccoundetail.setUpdateWho(userInfo.getUserName());
                    fdShippingAccoundetail.setUpdateWhoName(userInfo.getRealName());
                    fdShippingAccoundetail.setUpdateTime(LocalDateTime.now());
                    fdShippingAccoundetailMapper.updateByAccountCodeByGroup(fdShippingAccoundetail);
                }
            }
        }
        return R.success();
    }

    @Override
    public void updateFdShippingAccoundetailByWaybill(FdShippingAccoundetail fdShippingAccoundetail) {
        FdShippingAccount fdShippingAccount = new FdShippingAccount();
        fdShippingAccount.setShiftNo(fdShippingAccoundetail.getShiftNo());
        fdShippingAccount.setPlatformCode(fdShippingAccoundetail.getPlatformCode());
        fdShippingAccount.setDeleteFlag("N");
        List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountList(fdShippingAccount);
        if (CollUtil.isNotEmpty(fdShippingAccounts)) {
            fdShippingAccoundetail.setDatabase(database);
            List<FdShippingAccoundetail> list = fdShippingAccoundetailMapper.containerInfoListNew2(fdShippingAccoundetail);
            if (CollUtil.isNotEmpty(list)) {

            }
            for (FdShippingAccoundetail detail : list) {
                detail.setAccountCode(fdShippingAccounts.get(0).getAccountCode());
                fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode(detail);
            }
        }
    }

    @Override
    public void updateWaybillInfo(String accountCode, List<FdShippingAccoundetail> fdShippingAccoundetail) {
        if (CollUtil.isNotEmpty(fdShippingAccoundetail)) {
            FdShippingAccount fdShippingAccount = new FdShippingAccount();
            fdShippingAccount.setAccountCode(accountCode);
            fdShippingAccount.setDeleteFlag("N");
            List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountByCode(fdShippingAccount);
            if (CollUtil.isNotEmpty(fdShippingAccounts)) {
                for (FdShippingAccoundetail detail : fdShippingAccoundetail) {
                    if (StrUtil.isNotBlank(detail.getGoodsOwner()) || StrUtil.isNotBlank(detail.getGoodsOrigin()) || detail.getValueUsd() != null) {
                        WaybillParticipants participants = new WaybillParticipants();
                        participants.setShiftNo(fdShippingAccounts.get(0).getShiftNo());
                        participants.setContainerNo(detail.getContainerNumber());
                        participants.setConsignorName(detail.getGoodsOwner());
                        participants.setCity(detail.getGoodsOrigin());
                        if ("G".equals(fdShippingAccounts.get(0).getTrip())) {
                            participants.setParticipantsType("F");
                        } else if ("R".equals(fdShippingAccounts.get(0).getTrip())) {
                            participants.setParticipantsType("S");
                        }
                        waybillParticipantsMapper.updateWaybillParticipantByShift(participants);
                    }
                    if (StrUtil.isNotBlank(detail.getClearanceNumber()) || StrUtil.isNotBlank(detail.getCustomsSeal()) || StrUtil.isNotBlank(detail.getTrainNumber()) || StrUtil.isNotBlank(detail.getWaybillDemandNumber()) || StrUtil.isNotBlank(detail.getWaybillLnNumber())) {
                        WaybillContainerInfo info = new WaybillContainerInfo();
                        info.setShiftNo(fdShippingAccounts.get(0).getShiftNo());
                        info.setContainerNo(detail.getContainerNumber());
                        info.setClearanceNumber(detail.getClearanceNumber());
                        info.setCustomsSeal(detail.getCustomsSeal());
                        info.setTrainNumber(detail.getTrainNumber());
                        info.setWaybillDemandNumber(detail.getWaybillDemandNumber());
                        info.setWaybillLnNumber(detail.getWaybillLnNumber());
                        waybillContainerInfoMapper.updateContainerInfoByShift(info);
                    }
                }
            }
        }
    }

    @Override
    public R weChatList(FdShippingAccoundetail fdShippingAccoundetail) {
        return R.success(fdShippingAccoundetailMapper.weChatList(fdShippingAccoundetail));
    }
}
