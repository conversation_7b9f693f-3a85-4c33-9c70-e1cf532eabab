package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.FdBillSub;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.FdShippingAccount;
import com.huazheng.tunny.ocean.api.vo.BillDealWithCityAndCostVO;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.List;

/**
 * 子账单表 服务接口层
 *
 * <AUTHOR>
 * @date 2023-07-19 13:11:36
 */
public interface FdBillSubService extends IService<FdBillSub> {
    /**
     * 查询子账单表信息
     *
     * @param uuid 子账单表ID
     * @return 子账单表信息
     */
    public FdBillSub selectFdBillSubById(String uuid);

    /**
     * 查询子账单表列表
     *
     * @param fdBillSub 子账单表信息
     * @return 子账单表集合
     */
    public List<FdBillSub> selectFdBillSubList(FdBillSub fdBillSub);


    /**
     * 分页模糊查询子账单表列表
     * @return 子账单表集合
     */
    public Page selectFdBillSubListByLike(Query query);



    /**
     * 新增子账单表
     *
     * @param fdBillSub 子账单表信息
     * @return 结果
     */
    public R insertFdBillSub(FdBillSub fdBillSub);

    /**
     * 修改子账单表
     *
     * @param fdBillSub 子账单表信息
     * @return 结果
     */
    public int updateFdBillSub(FdBillSub fdBillSub);

    public R reject(FdBillSub fdBillSub);

    /**
     * 删除子账单表
     *
     * @param uuid 子账单表ID
     * @return 结果
     */
    public int deleteFdBillSubById(String uuid);

    public int updateStatus(FdBillSub fdBillSub);

    /**
     * 批量删除子账单表
     *
     * @param uuids 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdBillSubByIds(Integer[] uuids);

    public void deduction(FdBillSub fdBillSub);

    public Boolean accounting(FdBillSub fdBillSub);

    public void getFdBalanceDetail (FdShippingAccount fdShippingAccount, BigDecimal val, String platformLevel, String billCode);

    List<BillDealWithCityAndCostVO>  selectFdBillSubByBillNo(String billNo);

    List<String> selectBillOfCostByBillCode(String billNo);
}

