package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FiEnterpriseAuthApply;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 经营信息授权-企业授权申请表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-03-02 15:10:32
 */
public interface FiEnterpriseAuthApplyService extends IService<FiEnterpriseAuthApply> {
    /**
     * 查询经营信息授权-企业授权申请表信息
     *
     * @param rowId 经营信息授权-企业授权申请表ID
     * @return 经营信息授权-企业授权申请表信息
     */
    public FiEnterpriseAuthApply selectFiEnterpriseAuthApplyById(String rowId);

    /**
     * 查询经营信息授权-企业授权申请表列表
     *
     * @param fiEnterpriseAuthApply 经营信息授权-企业授权申请表信息
     * @return 经营信息授权-企业授权申请表集合
     */
    public List<FiEnterpriseAuthApply> selectFiEnterpriseAuthApplyList(FiEnterpriseAuthApply fiEnterpriseAuthApply);


    /**
     * 分页模糊查询经营信息授权-企业授权申请表列表
     * @return 经营信息授权-企业授权申请表集合
     */
    public Page selectFiEnterpriseAuthApplyListByLike(Query query);



    /**
     * 新增经营信息授权-企业授权申请表
     *
     * @param fiEnterpriseAuthApply 经营信息授权-企业授权申请表信息
     * @return 结果
     */
    public int insertFiEnterpriseAuthApply(FiEnterpriseAuthApply fiEnterpriseAuthApply);

    /**
     * 修改经营信息授权-企业授权申请表
     *
     * @param fiEnterpriseAuthApply 经营信息授权-企业授权申请表信息
     * @return 结果
     */
    public int updateFiEnterpriseAuthApply(FiEnterpriseAuthApply fiEnterpriseAuthApply);

    /**
     * 删除经营信息授权-企业授权申请表
     *
     * @param rowId 经营信息授权-企业授权申请表ID
     * @return 结果
     */
    public int deleteFiEnterpriseAuthApplyById(String rowId);

    /**
     * 批量删除经营信息授权-企业授权申请表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiEnterpriseAuthApplyByIds(Integer[] rowIds);

}

