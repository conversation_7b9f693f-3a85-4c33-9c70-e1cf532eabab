package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FiPledgeApplyMapper;
import com.huazheng.tunny.ocean.api.entity.FiPledgeApply;
import com.huazheng.tunny.ocean.service.FiPledgeApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fiPledgeApplyService")
public class FiPledgeApplyServiceImpl extends ServiceImpl<FiPledgeApplyMapper, FiPledgeApply> implements FiPledgeApplyService {

    @Autowired
    private FiPledgeApplyMapper fiPledgeApplyMapper;

    public FiPledgeApplyMapper getFiPledgeApplyMapper() {
        return fiPledgeApplyMapper;
    }

    public void setFiPledgeApplyMapper(FiPledgeApplyMapper fiPledgeApplyMapper) {
        this.fiPledgeApplyMapper = fiPledgeApplyMapper;
    }

    /**
     * 查询质押申请表信息
     *
     * @param rowId 质押申请表ID
     * @return 质押申请表信息
     */
    @Override
    public FiPledgeApply selectFiPledgeApplyById(String rowId)
    {
        return fiPledgeApplyMapper.selectFiPledgeApplyById(rowId);
    }

    /**
     * 查询质押申请表列表
     *
     * @param fiPledgeApply 质押申请表信息
     * @return 质押申请表集合
     */
    @Override
    public List<FiPledgeApply> selectFiPledgeApplyList(FiPledgeApply fiPledgeApply)
    {
        return fiPledgeApplyMapper.selectFiPledgeApplyList(fiPledgeApply);
    }


    /**
     * 分页模糊查询质押申请表列表
     * @return 质押申请表集合
     */
    @Override
    public Page selectFiPledgeApplyListByLike(Query query)
    {
        FiPledgeApply fiPledgeApply =  BeanUtil.mapToBean(query.getCondition(), FiPledgeApply.class,false);
        query.setRecords(fiPledgeApplyMapper.selectFiPledgeApplyListByLike(query,fiPledgeApply));
        return query;
    }

    /**
     * 新增质押申请表
     *
     * @param fiPledgeApply 质押申请表信息
     * @return 结果
     */
    @Override
    public int insertFiPledgeApply(FiPledgeApply fiPledgeApply)
    {
        return fiPledgeApplyMapper.insertFiPledgeApply(fiPledgeApply);
    }

    /**
     * 修改质押申请表
     *
     * @param fiPledgeApply 质押申请表信息
     * @return 结果
     */
    @Override
    public int updateFiPledgeApply(FiPledgeApply fiPledgeApply)
    {
        return fiPledgeApplyMapper.updateFiPledgeApply(fiPledgeApply);
    }


    /**
     * 删除质押申请表
     *
     * @param rowId 质押申请表ID
     * @return 结果
     */
    @Override
    public int deleteFiPledgeApplyById(String rowId)
    {
        return fiPledgeApplyMapper.deleteFiPledgeApplyById( rowId);
    };


    /**
     * 批量删除质押申请表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiPledgeApplyByIds(Integer[] rowIds)
    {
        return fiPledgeApplyMapper.deleteFiPledgeApplyByIds( rowIds);
    }

}
