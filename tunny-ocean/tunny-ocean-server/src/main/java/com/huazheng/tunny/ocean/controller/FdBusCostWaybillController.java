package com.huazheng.tunny.ocean.controller;

import com.huazheng.tunny.ocean.api.entity.FdBusCostWaybill;
import com.huazheng.tunny.ocean.service.FdBusCostWaybillService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 业务流程单运单表
 *
 * <AUTHOR>
 * @date 2024-06-06 11:30:14
 */
@Slf4j
@RestController
@RequestMapping("/fdbuscostwaybill")
public class FdBusCostWaybillController {

    @Autowired
    private FdBusCostWaybillService fdBusCostWaybillService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdBusCostWaybillService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdBusCostWaybillService.selectFdBusCostWaybillListByLike(new Query<>(params));
    }

    /**
     * 信息
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Integer id) {
        FdBusCostWaybill fdBusCostWaybill =fdBusCostWaybillService.selectById(id);
        return new R<>(fdBusCostWaybill);
    }

    /**
     * 保存
     * @param fdBusCostWaybill
     * @return R
     */
    @PostMapping
    public R save(@RequestBody FdBusCostWaybill fdBusCostWaybill) {
        fdBusCostWaybillService.insert(fdBusCostWaybill);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param fdBusCostWaybill
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody FdBusCostWaybill fdBusCostWaybill) {
        fdBusCostWaybillService.updateById(fdBusCostWaybill);
        return new R<>(Boolean.TRUE);
    }

    

    /**
     * 删除
     * @param id
     * @return R
     */
    @GetMapping("/del/{id}")
    public R delete(@PathVariable  Integer id) {
        fdBusCostWaybillService.deleteById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<Integer> ids) {
        fdBusCostWaybillService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<FdBusCostWaybill> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<FdBusCostWaybill> list = fdBusCostWaybillService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), FdBusCostWaybill.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    @Transactional
    public R imported(String titleStr, MultipartFile file) throws Exception {
        return null;
    }
}
