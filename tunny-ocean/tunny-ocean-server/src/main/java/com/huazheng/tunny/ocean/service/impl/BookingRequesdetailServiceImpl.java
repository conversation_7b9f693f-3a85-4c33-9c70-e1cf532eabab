package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.ExcelObjectCheckerUtil;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.BookingRequesdetailDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.BookingRequesdetailService;
import com.huazheng.tunny.ocean.util.CheckUtil;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("bookingRequesdetailService")
public class BookingRequesdetailServiceImpl extends ServiceImpl<BookingRequesdetailMapper, BookingRequesdetail> implements BookingRequesdetailService {

    @Autowired
    private BookingRequesdetailMapper bookingRequesdetailMapper;
    @Autowired
    private BookingRequesheaderMapper bookingRequesheaderMapper;
    @Autowired
    private ShifmanagementMapper shifmanagementMapper;
    @Autowired
    private ContainerTypeDataMapper containerTypeDataMapper;
    @Autowired
    private StationManagementMapper stationManagementMapper;

    public BookingRequesdetailMapper getBookingRequesdetailMapper() {
        return bookingRequesdetailMapper;
    }

    public void setBookingRequesdetailMapper(BookingRequesdetailMapper bookingRequesdetailMapper) {
        this.bookingRequesdetailMapper = bookingRequesdetailMapper;
    }

    public BookingRequesheaderMapper getBookingRequesheaderMapper() {
        return bookingRequesheaderMapper;
    }

    public void setBookingRequesheaderMapper(BookingRequesheaderMapper bookingRequesheaderMapper) {
        this.bookingRequesheaderMapper = bookingRequesheaderMapper;
    }

    /**
     * 查询订舱申请单子表信息
     *
     * @param rowId 订舱申请单子表ID
     * @return 订舱申请单子表信息
     */
    @Override
    public BookingRequesdetail selectBookingRequesdetailById(String rowId) {
        return bookingRequesdetailMapper.selectBookingRequesdetailById(rowId);
    }

    /**
     * 查询订舱申请单子表列表
     *
     * @param bookingRequesdetail 订舱申请单子表信息
     * @return 订舱申请单子表集合
     */
    @Override
    public List<BookingRequesdetail> selectBookingRequesdetailList(BookingRequesdetail bookingRequesdetail) {
        return bookingRequesdetailMapper.selectBookingRequesdetailList(bookingRequesdetail);
    }


    /**
     * 分页模糊查询订舱申请单子表列表
     *
     * @return 订舱申请单子表集合
     */
    @Override
    public Page selectBookingRequesdetailListByLike(Query query) {
        BookingRequesdetail bookingRequesdetail = BeanUtil.mapToBean(query.getCondition(), BookingRequesdetail.class, false);
//        Integer c= bookingRequesdetailMapper.queryCount(bookingRequesdetail);
//        if(c!=null&&c!=0){
//            query.setTotal(c);
        query.setRecords(bookingRequesdetailMapper.selectBookingRequesdetailListByLike(query, bookingRequesdetail));
//        }
        return query;
    }

    @Override
    public Map<String, Object> weChatList(BookingRequesdetail bookingRequesdetail) {
        Map<String, Object> map = new HashMap<>();
        List<BookingRequesdetail> weChatList = bookingRequesdetailMapper.weChatList(bookingRequesdetail);
        map.put("weChatList", weChatList);
        List<BookingRequesdetail> weChatTotal = bookingRequesdetailMapper.weChatTotal(bookingRequesdetail);
        if (CollUtil.isNotEmpty(weChatTotal)) {
            StringBuilder sb = new StringBuilder();
            for (BookingRequesdetail detail : weChatTotal) {
                String field = detail.getResveredField01();
                String num = String.valueOf(detail.getNum());

                switch (field) {
                    case "E":
                        sb.append("出口").append(num).append(" ");
                        break;
                    case "I":
                        sb.append("进口").append(num).append(" ");
                        break;
                    case "P":
                        sb.append("过境").append(num).append(" ");
                        break;
                }
            }

            String result = sb.toString().trim();
            map.put("weChatTotal", StrUtil.isNotBlank(result) ? result.replace(" ", ",") : null);
        } else {
            map.put("weChatTotal", null);
        }
        return map;
    }

    /**
     * 新增订舱申请单子表
     *
     * @param bookingRequesdetail 订舱申请单子表信息
     * @return 结果
     */
    @Override
    public int insertBookingRequesdetail(BookingRequesdetail bookingRequesdetail) {
        return bookingRequesdetailMapper.insertBookingRequesdetail(bookingRequesdetail);
    }

    /**
     * 修改订舱申请单子表
     *
     * @param bookingRequesdetail 订舱申请单子表信息
     * @return 结果
     */
    @Override
    public R updateBookingRequesdetail(BookingRequesdetail bookingRequesdetail) {
        String del = "Y";
        if (StrUtil.isNotEmpty(bookingRequesdetail.getDeleteFlag()) && del.equals(bookingRequesdetail.getDeleteFlag())) {
            try {
                bookingRequesdetailMapper.updateBookingRequesdetail(bookingRequesdetail);
            } catch (Exception e) {
                e.printStackTrace();
                System.out.println(e.getMessage() + "删除信息失败！！！");
                return new R(Boolean.TRUE, "删除信息失败");
            }
            return new R(Boolean.TRUE, "删除信息成功");
        } else {
            try {
                bookingRequesdetailMapper.updateBookingRequesdetail(bookingRequesdetail);
            } catch (Exception e) {
                e.printStackTrace();
                System.out.println(e.getMessage() + "修改信息失败！！！");
                return new R(Boolean.TRUE, "修改信息失败");
            }
            return new R(Boolean.TRUE, "修改信息成功");
        }

    }

    @Override
    public Integer updateBookingRequesdetails(BookingRequesheader bookingRequesheader) {
        BigDecimal bigDecimal = BigDecimal.valueOf(0.00);
        for (BookingRequesdetail bookingRequesdetail : bookingRequesheader.getDetails()) {
            if (bookingRequesdetail.getContainerType().contains("4")) {
                bigDecimal = bigDecimal.add(BigDecimal.valueOf(1.0));
            } else if (bookingRequesdetail.getContainerType().contains("2")) {
                bigDecimal = bigDecimal.add(BigDecimal.valueOf(0.5));
            }
            bookingRequesdetail.setDeleteWho(SecurityUtils.getUserInfo().getUserName());
            bookingRequesdetail.setDeleteWhoName(SecurityUtils.getUserInfo().getRealName());
            bookingRequesdetailMapper.deleteBookingRequesdetails(bookingRequesdetail);
        }
        bookingRequesheader.setSpaceNums(Float.valueOf(bigDecimal.toString()));
        return bookingRequesheaderMapper.updateBookingRequesheaderBySpaceNums(bookingRequesheader);
    }


    /**
     * 删除订舱申请单子表
     *
     * @param rowId 订舱申请单子表ID
     * @return 结果
     */
    @Override
    public int deleteBookingRequesdetailById(String rowId) {
        return bookingRequesdetailMapper.deleteBookingRequesdetailById(rowId);
    }

    ;


    /**
     * 批量删除订舱申请单子表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBookingRequesdetailByIds(Integer[] rowIds) {
        return bookingRequesdetailMapper.deleteBookingRequesdetailByIds(rowIds);
    }

    @Override
    public int deleteByOrderNoAndContainerNo(BookingRequesdetail book) {
        return bookingRequesdetailMapper.deleteByOrderNoAndContainerNo(book);
    }

    @Override
    public List<String> selectBookingRequesdetailByShiftNo(String shiftNo) {
        return bookingRequesdetailMapper.selectBookingRequesdetailByShiftNo(shiftNo);
    }

    @Override
    public R importTzTemplate(MultipartFile file, String shiftNo, String platformCode) throws Exception {
        boolean isLargeNumberOfObjects = ExcelObjectCheckerUtil.containsLargeNumberOfObjects(file.getInputStream(), 0);
        if (isLargeNumberOfObjects) {
            return new R<>(new Throwable("文件中存在未知对象文件，请使用快捷键<Ctrl+G或F5>，进行定位，删除对象后再进行提交！"));
        }
        R r = new R();
        try {
            InputStream inputStream = file.getInputStream();
            // 调用工具类中方法，读取excel文件中数据
            long i = System.currentTimeMillis();
            r = readExcel(file, shiftNo, platformCode);
            inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
            return new R<>(new Throwable(e));
        }
        return r;
    }

    public R readExcel(MultipartFile file, String shiftNo, String platformCode) throws Exception {
        Workbook workbook = null;
        try {
            InputStream is = file.getInputStream();
            //利用WorkbookFactory获取Sheet，无需判断版本
            workbook = WorkbookFactory.create(is);
        } catch (Exception e) {
            e.printStackTrace();
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        //导入第一张sheet页
        Sheet sheet = workbook.getSheetAt(0);

        Shifmanagement shif = new Shifmanagement();
        shif.setShiftId(shiftNo);
        shif.setPlatformCode(platformCode);
        shif.setDeleteFlag("N");
        shif.setReleaseStatus("1");
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementListByLike(shif);
        if (CollUtil.isEmpty(shifmanagements)) {
            return new R<>(new Throwable("该班次不存在！"));
        }
        String identification = null;
        if (StrUtil.isNotBlank(shifmanagements.get(0).getIdentification())) {
            identification = shifmanagements.get(0).getIdentification();
        }

        List<BookingRequesdetailDTO> list = new ArrayList<>();
        StringBuffer sb = new StringBuffer();
        ContainerTypeData sel2 = new ContainerTypeData();
        sel2.setDeleteFlag("N");
        List<ContainerTypeData> containerTypeData = containerTypeDataMapper.selectContainerTypeDataList(sel2);
        StationManagement sel3 = new StationManagement();
        sel3.setDeleteFlag("N");
        List<StationManagement> stationManagements = stationManagementMapper.selectStationManagementList(sel3);

        List<String> stringList = new ArrayList<>();
        List<String> strings = bookingRequesdetailMapper.selectBookingRequesdetailByShiftNo(shiftNo);
        if (strings.size() > 0) {
            stringList.addAll(strings);
        }
        String portStation = null;
        //口岸站
        Row row0 = sheet.getRow(0);
        if (row0.getCell(13) != null) {
            row0.getCell(13).setCellType(CellType.STRING);
            portStation = row0.getCell(13).getStringCellValue();
        }
        //获取行数
        int lastRowNum = sheet.getLastRowNum();
        for (int i = 3; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            boolean blankFlag = CheckUtil.isRowEmpty(row);
            if (blankFlag) {
                continue;
            }
            BookingRequesdetailDTO requesdetailDTO = new BookingRequesdetailDTO();
            if (row.getCell(11) != null) {
                row.getCell(11).setCellType(CellType.STRING);
                String containerNo = row.getCell(11).getStringCellValue();
                boolean b = CheckUtil.verifyCntrCode(containerNo);
                if (b) {
                    if (!stringList.contains(containerNo)) {
                        if (containerNo.contains("\n")) {
                            containerNo.replace("\n", "");
                        }
                        if (containerNo.contains(" ")) {
                            containerNo.replace(" ", "");
                        }
                        containerNo = containerNo.trim();
                        stringList.add(containerNo);
                        requesdetailDTO.setContainerNo(containerNo);
                    } else {
                        sb.append("行" + (i + 11) + containerNo + "箱号重复,请查看本次导入箱号或者班次号下所有的订舱申请的箱号");
                    }
                } else {
                    sb.append("行" + (i + 11) + "箱号格式错误：" + containerNo + ";");
                }
            } else {
                sb.append("行" + (i + 11) + "箱号不能为空" + ";");
            }
            if (row.getCell(9) != null) {
                try {
                    row.getCell(9).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(9).getStringCellValue();
                    //箱型代码校验
                    if (CollUtil.isNotEmpty(containerTypeData)) {
                        if ("20".equals(stringCellValue) || "40".equals(stringCellValue) || "45".equals(stringCellValue)) {
                            stringCellValue = stringCellValue + "GP";
                        }
                        for (ContainerTypeData typeData : containerTypeData
                        ) {
                            if (typeData.getContainerTypeCode().equals(stringCellValue)) {
                                requesdetailDTO.setContainerTypeCode(stringCellValue);
                                requesdetailDTO.setContainerTypeName(typeData.getContainerTypeName());
                                requesdetailDTO.setContainerType(typeData.getContainerTypeSize());
                                break;
                            }
                        }
                    }
                    if (StrUtil.isEmpty(requesdetailDTO.getContainerTypeCode())) {
                        sb.append("行" + (i + 1) + "箱型无法匹配;");
                    }
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "箱型异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "箱型不能为空;");
            }

            if (row.getCell(10) != null) {
                try {
                    row.getCell(10).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(10).getStringCellValue().trim();
                    if ("自备箱".equals(stringCellValue)) {
                        requesdetailDTO.setBox("0");
                    } else if ("中铁箱".equals(stringCellValue)) {
                        requesdetailDTO.setBox("1");
                    }
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "箱属异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "箱属不能为空;");
            }

            if (row.getCell(1) != null) {
                try {
                    row.getCell(1).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(1).getStringCellValue();
                    if ("出口".equals(stringCellValue)) {
                        requesdetailDTO.setResveredField01("E");
                    } else if ("进口".equals(stringCellValue)) {
                        requesdetailDTO.setResveredField01("I");
                    } else if ("过境".equals(stringCellValue)) {
                        requesdetailDTO.setResveredField01("P");
                    }
                    if (StrUtil.isNotBlank(identification) && !identification.equals(requesdetailDTO.getResveredField01())) {
                        sb.append("行" + (i + 1) + "箱进出口过境类型与班列类型不符");
                    }
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "类型异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "类型不能为空;");
            }

            if (row.getCell(8) != null) {
                try {
                    row.getCell(8).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(8).getStringCellValue();
                    requesdetailDTO.setGoodsName(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "品名异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "品名不能为空;");
            }

            if (row.getCell(4) != null) {
                try {
                    row.getCell(4).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(4).getStringCellValue();
                    if (CollUtil.isNotEmpty(stationManagements)) {
                        for (StationManagement stationManagement : stationManagements
                        ) {
                            if (stationManagement.getStationName().equals(stringCellValue)) {
                                requesdetailDTO.setDestinationName(stringCellValue);
                                requesdetailDTO.setBureauSubordinate(stationManagement.getBureau());
                            }
                        }
                    }
                    if (StrUtil.isEmpty(requesdetailDTO.getDestinationName())) {
                        sb.append("行" + (i + 1) + "发站无法匹配;");
                    }
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "发站异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "发站不能为空;");
            }

            if (row.getCell(6) != null) {
                try {
                    row.getCell(6).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(6).getStringCellValue();
                    if (CollUtil.isNotEmpty(stationManagements)) {
                        for (StationManagement stationManagement : stationManagements
                        ) {
                            if (stationManagement.getStationName().equals(stringCellValue)) {
                                requesdetailDTO.setDestination(stringCellValue);
                            }
                        }
                    }
                    if (StrUtil.isEmpty(requesdetailDTO.getDestination())) {
                        sb.append("行" + (i + 1) + "到站无法匹配;");
                    }
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "到站异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "到站不能为空;");
            }

            if (StrUtil.isBlank(portStation) && CollUtil.isNotEmpty(shifmanagements)) {
                portStation = shifmanagements.get(0).getPortStation();
            }
            requesdetailDTO.setPortStation(portStation);
            list.add(requesdetailDTO);
        }
        workbook.close();
        if (sb.length() > 0) {
            return new R<>(new Throwable(sb.toString()));
        }
        return new R(0, true, list, "操作成功");
    }

}
