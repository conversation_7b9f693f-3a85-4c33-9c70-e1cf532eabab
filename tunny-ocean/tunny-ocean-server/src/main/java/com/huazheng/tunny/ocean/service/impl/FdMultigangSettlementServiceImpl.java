package com.huazheng.tunny.ocean.service.impl;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.ocean.api.entity.FdMultigangSettlement;
import com.huazheng.tunny.ocean.mapper.FdMultigangSettlementMapper;
import com.huazheng.tunny.ocean.service.FdMultigangSettlementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fdMultigangSettlementService")
public class FdMultigangSettlementServiceImpl extends ServiceImpl<FdMultigangSettlementMapper, FdMultigangSettlement> implements FdMultigangSettlementService {

    @Autowired
    private FdMultigangSettlementMapper fdMultigangSettlementMapper;

    public FdMultigangSettlementMapper getFdMultigangSettlementMapper() {
        return fdMultigangSettlementMapper;
    }

    public void setFdMultigangSettlementMapper(FdMultigangSettlementMapper fdMultigangSettlementMapper) {
        this.fdMultigangSettlementMapper = fdMultigangSettlementMapper;
    }

    /**
     * 查询中铁多联结算对象表信息
     *
     * @param id 中铁多联结算对象表ID
     * @return 中铁多联结算对象表信息
     */
    @Override
    public FdMultigangSettlement selectFdMultigangSettlementById(Integer id) {
        return fdMultigangSettlementMapper.selectFdMultigangSettlementById(id);
    }

    /**
     * 查询中铁多联结算对象表列表
     *
     * @param fdMultigangSettlement 中铁多联结算对象表信息
     * @return 中铁多联结算对象表集合
     */
    @Override
    public List<FdMultigangSettlement> selectFdMultigangSettlementList(FdMultigangSettlement fdMultigangSettlement) {
        return fdMultigangSettlementMapper.selectFdMultigangSettlementList(fdMultigangSettlement);
    }


    /**
     * 分页模糊查询中铁多联结算对象表列表
     *
     * @return 中铁多联结算对象表集合
     */
    @Override
    public Page selectFdMultigangSettlementListByLike(Query query) {
        FdMultigangSettlement fdMultigangSettlement = BeanUtil.mapToBean(query.getCondition(), FdMultigangSettlement.class, false);
        query.setRecords(fdMultigangSettlementMapper.selectFdMultigangSettlementListByLike(query, fdMultigangSettlement));
        return query;
    }

    /**
     * 新增中铁多联结算对象表
     *
     * @param fdMultigangSettlement 中铁多联结算对象表信息
     * @return 结果
     */
    @Override
    public int insertFdMultigangSettlement(FdMultigangSettlement fdMultigangSettlement) {
        return fdMultigangSettlementMapper.insertFdMultigangSettlement(fdMultigangSettlement);
    }

    /**
     * 修改中铁多联结算对象表
     *
     * @param fdMultigangSettlement 中铁多联结算对象表信息
     * @return 结果
     */
    @Override
    public int updateFdMultigangSettlement(FdMultigangSettlement fdMultigangSettlement) {
        return fdMultigangSettlementMapper.updateFdMultigangSettlement(fdMultigangSettlement);
    }


    /**
     * 删除中铁多联结算对象表
     *
     * @param id 中铁多联结算对象表ID
     * @return 结果
     */
    @Override
    public int deleteFdMultigangSettlementById(Integer id) {
        return fdMultigangSettlementMapper.deleteFdMultigangSettlementById(id);
    }



    /**
     * 批量删除中铁多联结算对象表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFdMultigangSettlementByIds(Integer[] ids) {
        return fdMultigangSettlementMapper.deleteFdMultigangSettlementByIds(ids);
    }

}
