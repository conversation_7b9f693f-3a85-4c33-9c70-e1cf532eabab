package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.GoodsDTO;
import com.huazheng.tunny.ocean.api.dto.RequesdetailDTO;
import com.huazheng.tunny.ocean.api.dto.RequesheaderDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.enums.MessageBusinessEnum;
import com.huazheng.tunny.ocean.api.feign.RemoteAdminService;
import com.huazheng.tunny.ocean.api.vo.FdShippingAccountVO;
import com.huazheng.tunny.ocean.controller.SignatureController;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service("bookingRequesheaderService")
public class BookingRequesheaderServiceImpl extends ServiceImpl<BookingRequesheaderMapper, BookingRequesheader> implements BookingRequesheaderService {

    @Autowired
    private BookingRequesheaderMapper bookingRequesheaderMapper;

    @Autowired
    private BookingRequesdetailMapper bookingRequesdetailMapper;

    @Autowired
    private WaybillHeaderMapper waybillHeaderMapper;

    @Autowired
    private SysNoConfigServiceImpl sysNoConfigService;

    @Autowired
    private SpaceOccupyMapper spaceOccupyMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private SignatureController signatureController;
    @Autowired
    private ShifmanagementMapper shifmanagementMapper;
    @Autowired
    private CustomerPlatformInfoMapper customerPlatformInfoMapper;
    @Autowired
    private SpaceOccupyServiceImpl spaceOccupyService;
    @Autowired
    private WaybillContainerInfoMapper waybillContainerInfoMapper;
    @Autowired
    private WaybillParticipantsMapper waybillParticipantsMapper;
    @Autowired
    private WaybillGoodsInfoMapper waybillGoodsInfoMapper;
    @Autowired
    private WaybillHeaderShMapper waybillHeaderShMapper;
    @Autowired
    private WaybillContainerInfoShMapper waybillContainerInfoShMapper;
    @Autowired
    private UploadRecordMapper uploadRecordMapper;
    @Autowired
    private SysNoConfigService noConfigService;
    @Autowired
    private FdShippingAccountMapper fdShippingAccountMapper;
    @Autowired
    private CustomerInfoMapper customerInfoMapper;
    @Autowired
    private FdShippingAccoundetailMapper fdShippingAccoundetailMapper;
    @Autowired
    private LineManagementService lineManagementService;
    @Autowired
    private MessageCenterService messageCenterService;
    @Autowired
    private RemoteAdminService remoteAdminService;
    @Autowired
    private FdBusCostMapper fdBusCostMapper;
    @Autowired
    private FdBusCostDetailMapper fdBusCostDetailMapper;
    @Autowired
    private FdBusCostWaybillMapper fdBusCostWaybillMapper;
    @Autowired
    private ProvinceShiftNoService provinceShiftNoService;
    @Autowired
    private FdPostTransportMapper fdPostTransportMapper;
    @Autowired
    private FdPostTransportDetailMapper fdPostTransportDetailMapper;
    @Autowired
    private StationManagementMapper stationManagementMapper;
    @Value("${db.database}")
    private String database;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveBookingAndWaybill(String data) {
//        String data = null;
//        String content2 = null;
//        data = signatureController.getPost(jsonObject);
//        content2 = String.valueOf(jsonObject.get("content"));
        RequesheaderDTO header = JSONUtil.toBean(data, RequesheaderDTO.class);
        BookingRequesheader bookingRequesheader = new BookingRequesheader();
        bookingRequesheader.setBookingCustcode(header.getBookingCustcode());
        bookingRequesheader.setBookingCustname(header.getBookingCustname());
        bookingRequesheader.setShiftNo(header.getShiftNo());
        bookingRequesheader.setRemarks(header.getRemarks());
        bookingRequesheader.setResveredField06(header.getResveredField06());
        if (header.getDetails() == null) {
            return JSONUtil.parseObj(new R<>(Boolean.FALSE, "请检查所填信息有遗漏！"), false).toStringPretty();
        }
        Shifmanagement shif = new Shifmanagement();
        shif.setShiftId(bookingRequesheader.getShiftNo());
        //TODO  上合待确认是否参与内联班次订舱逻辑
//        shif.setPlatformCode();
        shif.setIsNullParentId("0");
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementListByLike(shif);
        if (shifmanagements == null || shifmanagements.size() == 0) {
            return JSONUtil.parseObj(new R<>(Boolean.FALSE, "该班次不存在！"), false).toStringPretty();
        }
        //没有申请单号，须新增主子表信息
        String appNo = sysNoConfigService.genNo("SQ");
        /*if (appNo.contains("，")) {
            return JSONUtil.parseObj(new R<>(Boolean.FALSE,appNo), false).toStringPretty();
        }*/
        float num = 0f;//上报舱位数
        List<BookingRequesdetail> detailList = new ArrayList<>();
        for (RequesdetailDTO rdd : header.getDetails()) {
            BookingRequesdetail details = new BookingRequesdetail();

            String containerNo = rdd.getContainerNo();
            boolean flag3 = verifyCntrCode(containerNo);
            if (!flag3) {
                return JSONUtil.parseObj(new R<>(Boolean.FALSE, "箱号填写有误，如有疑问请联系管理员！"), false).toStringPretty();
            }
            WaybillHeader wh = new WaybillHeader();
            wh.setShiftNo(header.getShiftNo());
            wh.setResveredField05(containerNo);
            List<WaybillHeader> list = waybillHeaderMapper.selectCnExistByShiftNo(wh);
            if (list != null && list.size() > 0) {
                return JSONUtil.parseObj(new R<>(Boolean.FALSE, containerNo + ",该箱号已被申报"), false).toStringPretty();
            }
            if (rdd.getContainerType().contains("20")) {
                num = num + 0.5f;
            } else if (rdd.getContainerType().contains("40") || rdd.getContainerType().contains("45")) {
                num = num + 1.0f;
            }
            details.setOrderNo(appNo);
            details.setDestinationName(rdd.getStartStationName());
            details.setDestination(rdd.getEndStationName());
            details.setBureauSubordinate(shifmanagements.get(0).getBureau());
            details.setPortStation(shifmanagements.get(0).getPortStation());
            details.setBox(rdd.getBox());
            details.setContainerNo(containerNo);
            details.setDeadWeight(rdd.getDeadWeight());
            details.setGrossWeight(rdd.getGrossWeight());
            details.setNetWeight(rdd.getNetWeight());

            details.setContainerTypeCode(rdd.getContainerTypeCode());
            details.setContainerTypeName(rdd.getContainerTypeName());
            details.setContainerType(rdd.getContainerType());

            details.setGoodsName(rdd.getGoodsName());
            details.setResveredField01(rdd.getIdentification());
            details.setResveredField02(rdd.getResveredField02());
            details.setDeleteFlag("N");
            details.setAddWho(bookingRequesheader.getBookingCustcode());
            details.setAddWhoName(bookingRequesheader.getBookingCustname());
            details.setAddTime(new Date());
            detailList.add(details);
        }

        SpaceOccupy sp = new SpaceOccupy();
        sp.setShiftNo(header.getShiftNo());
        Float occupyNums = spaceOccupyMapper.selectOccupyNums(sp);
        if (occupyNums == null || occupyNums < 0.5) {
            occupyNums = (float) 0;
        }
        Float usedSpNumsUndShf = occupyNums;//该班次已用舱位数量
        Float trueSpaceNums = 0.00f;
        Float trueSpaceNumsSp = 0.00f;
        if (shifmanagements.get(0).getNumOfTruePositions() != null && !"".equals(shifmanagements.get(0).getNumOfTruePositions())) {
            trueSpaceNums = Float.valueOf(shifmanagements.get(0).getNumOfTruePositions());//班次发布实际舱位
        }
        if (shifmanagements.get(0).getNumOfReservePositions() != null && !"".equals(shifmanagements.get(0).getNumOfReservePositions())) {
            trueSpaceNumsSp = Float.valueOf(shifmanagements.get(0).getNumOfReservePositions()); //班次发布备用舱位
        }
        Float sumSpace = trueSpaceNums + trueSpaceNumsSp;//该班次的总舱位数（实际+备用）
        Float spaceLeft = sumSpace - usedSpNumsUndShf;//总仓位-已用=剩余舱位数 （实际+备用）
        String flag = "0";     //判断是否使用备用仓标识0不使用1使用
        if (num > spaceLeft) {//上报舱位数若大于剩余舱位数,返回提示信息
            return JSONUtil.parseObj(new R<>(Boolean.FALSE, "剩余舱位数量不足，请重新确认"), false).toStringPretty();
        } else {
            //剩余舱位数满足本次提交，则更新舱位占用表，剩余舱位（实际+备用）数据
            SpaceOccupy so = new SpaceOccupy();
            so.setSpaceNums(String.valueOf(num));//当前班次已用舱位数
            so.setOrderNo(bookingRequesheader.getOrderNo());
            so.setShiftNo(bookingRequesheader.getShiftNo());
            so.setAddTime(LocalDateTime.now());
            so.setAddWhoName(bookingRequesheader.getBookingCustname());
            so.setAddWho(bookingRequesheader.getBookingCustcode());
            spaceOccupyService.insertSpaceOccupy(so);
            //判断是否使用备用仓(已占用+本次提交)>班次发布实际舱位数，即为使用备用仓(1)，否则为未使用(0)
            flag = (usedSpNumsUndShf + num) > trueSpaceNums ? "1" : "0";
        }

        String account = sysNoConfigService.genNo("YD");
        bookingRequesheader.setRowId(UUID.randomUUID().toString());
        bookingRequesheader.setWaybillNo(account);
        bookingRequesheader.setOrderNo(appNo);
        bookingRequesheader.setShiftNo(shifmanagements.get(0).getShiftId());
        bookingRequesheader.setShippingLineCode(shifmanagements.get(0).getShippingLineCode());
        bookingRequesheader.setShippingLine(shifmanagements.get(0).getShippingLine());
        bookingRequesheader.setTrainName(shifmanagements.get(0).getShiftName());
        bookingRequesheader.setDeliveryTime(shifmanagements.get(0).getPlanShipTime());
        bookingRequesheader.setTrainType(shifmanagements.get(0).getTrainType());
        bookingRequesheader.setTotalCases(String.valueOf(header.getDetails().size()));
        bookingRequesheader.setSpaceNums(num);
        bookingRequesheader.setDocumentStatus("2");
        bookingRequesheader.setTrip(shifmanagements.get(0).getTrip());
        bookingRequesheader.setCityPlatform(shifmanagements.get(0).getResveredField02());
        bookingRequesheader.setAuditType("1");
        bookingRequesheader.setResveredField02(shifmanagements.get(0).getPlatformCode());
        bookingRequesheader.setDeleteFlag("N");
        bookingRequesheader.setAddTime(new Date());
        bookingRequesheader.setAddWho(header.getBookingCustcode());
        bookingRequesheader.setAddWhoName(header.getBookingCustname());
        bookingRequesheader.setDetails(detailList);

        bookingRequesheaderMapper.insertBookingRequesheader(bookingRequesheader);
        bookingRequesdetailMapper.insertBookingRequesdetails(bookingRequesheader.getDetails());
        if (header.getFiles() != null && header.getFiles().size() > 0) {
            for (UploadRecord file : header.getFiles()
            ) {
                file.setBillNo(account);
                file.setBusiSeg("1");
                file.setResveredField01("group1");
                file.setAddTime(LocalDateTime.now());
                file.setAddWho(header.getBookingCustcode());
                file.setAddWhoName(header.getBookingCustname());
                uploadRecordMapper.insertUploadRecord(file);
            }
        }

        //插入运单主、子表信息
        WaybillHeader waybillHeader = new WaybillHeader();
        waybillHeader.setRowId(UUID.randomUUID().toString());
        waybillHeader.setOrderNo(bookingRequesheader.getOrderNo());
        waybillHeader.setWaybillNo(account);
        waybillHeader.setCustomerNo(bookingRequesheader.getBookingCustcode());
        waybillHeader.setCustomerName(bookingRequesheader.getBookingCustname());
        waybillHeader.setPlatformCode(bookingRequesheader.getResveredField02());
        waybillHeader.setPlatformName(bookingRequesheader.getCityPlatform());
        waybillHeader.setTrainType(bookingRequesheader.getTrainType());
        waybillHeader.setIdentification(bookingRequesheader.getIdentification());
        waybillHeader.setIsCustomTransit(bookingRequesheader.getTransit());
        waybillHeader.setShiftNo(bookingRequesheader.getShiftNo());
        waybillHeader.setShippingTime(bookingRequesheader.getDeliveryTime());
        waybillHeader.setShippingLine(bookingRequesheader.getShippingLine());
        waybillHeader.setResveredField05(bookingRequesheader.getShippingLineCode());//发运线路编码
        waybillHeader.setResveredField06(bookingRequesheader.getResveredField06());//境外代理
        waybillHeader.setTrainName(bookingRequesheader.getTrainName());
        waybillHeader.setResveredField02(bookingRequesheader.getResveredField02());//市平台名称
        waybillHeader.setAuditFlag("DCSH");
        waybillHeader.setTotalCases(bookingRequesheader.getTotalCases());
        waybillHeader.setBillStatus("1");
        waybillHeader.setDeleteFlag("N");
        waybillHeader.setAddWho(bookingRequesheader.getAddWho());
        waybillHeader.setAddWhoName(bookingRequesheader.getAddWhoName());
        waybillHeader.setAddTime(new Date());
        waybillHeader.setTrip(bookingRequesheader.getTrip());
        waybillHeader.setIsTransit(bookingRequesheader.getTransit());
        waybillHeader.setResveredField01(bookingRequesheader.getResveredField03());//审核意见
        waybillHeader.setResveredField03(flag);                      //********新增逻辑 是否使用备用仓标识（0不使用1使用）
        waybillHeader.setResveredField04(String.valueOf(num)); //********新增逻辑 占用舱数
        waybillHeader.setShAct("1");//上合代申请0否1是
        waybillHeaderMapper.insertWaybillHeader(waybillHeader);

        for (RequesdetailDTO requesdetail : header.getDetails()) {
            WaybillContainerInfo waybillContainerInfo = new WaybillContainerInfo();
            waybillContainerInfo.setRowId(UUID.randomUUID().toString());
            waybillContainerInfo.setOrderNo(appNo);
            waybillContainerInfo.setWaybillNo(account);
            waybillContainerInfo.setDemandNo(requesdetail.getDemandNo());
            waybillContainerInfo.setIdentification(requesdetail.getIdentification());
            waybillContainerInfo.setBureau(shifmanagements.get(0).getBureau());

            waybillContainerInfo.setStationCompilation(requesdetail.getStationCompilation());
            waybillContainerInfo.setDestinationName(requesdetail.getStartStationName());
            waybillContainerInfo.setStartStationName(requesdetail.getStartStationName());
            waybillContainerInfo.setEndCompilation(requesdetail.getEndCompilation());
            waybillContainerInfo.setDestination(requesdetail.getEndStationName());
            waybillContainerInfo.setEndStationName(requesdetail.getEndStationName());
            waybillContainerInfo.setGoodsValue(requesdetail.getGoodsValue());
            waybillContainerInfo.setPortStation(shifmanagements.get(0).getPortStation());
            waybillContainerInfo.setContainerNo(requesdetail.getContainerNo());
            waybillContainerInfo.setContainerOwner(requesdetail.getBox());

            waybillContainerInfo.setContainerTypeCode(requesdetail.getContainerTypeCode());
            waybillContainerInfo.setContainerTypeName(requesdetail.getContainerTypeName());
            waybillContainerInfo.setContainerType(requesdetail.getContainerType());

            waybillContainerInfo.setContainerDeadWeight(requesdetail.getDeadWeight());
            waybillContainerInfo.setContainerGrossWeight(requesdetail.getGrossWeight());
            waybillContainerInfo.setContainerNetWeight(requesdetail.getNetWeight());
            waybillContainerInfo.setPortAgent(requesdetail.getPortAgent());
            waybillContainerInfo.setAbroadReachCity(requesdetail.getAbroadReachCity());
            waybillContainerInfo.setDeleteFlag("N");
            waybillContainerInfo.setAddWho(bookingRequesheader.getAddWho());
            waybillContainerInfo.setAddWhoName(bookingRequesheader.getAddWhoName());
            waybillContainerInfo.setAddTime(new Date());
            waybillContainerInfo.setDomesticFreight(BigDecimal.valueOf(0.00));
            waybillContainerInfo.setOverseasFreight(BigDecimal.valueOf(0.00));
            waybillContainerInfo.setGoodsName(requesdetail.getGoodsName());
            waybillContainerInfoMapper.insertWaybillContainerInfo(waybillContainerInfo);
        }

        List<WaybillParticipants> waybillParticipants = new ArrayList<>();
        for (RequesdetailDTO requesdetail : header.getDetails()) {
            //收货人
            WaybillParticipants waybillParticipantsShou = new WaybillParticipants();
            waybillParticipantsShou.setAppNo(appNo);
            waybillParticipantsShou.setParticipantsType("S");
            waybillParticipantsShou.setWaybillNo(waybillHeader.getWaybillNo());
            waybillParticipantsShou.setContainerNo(requesdetail.getContainerNo());
            waybillParticipantsShou.setConsignorName(requesdetail.getConsigneeName());
            waybillParticipantsShou.setCountryCode(requesdetail.getConsigneeCountryCode());
            waybillParticipantsShou.setDeleteFlag("N");
            waybillParticipantsShou.setAddWho(bookingRequesheader.getAddWho());
            waybillParticipantsShou.setAddWhoName(bookingRequesheader.getAddWhoName());
            waybillParticipantsShou.setAddTime(LocalDateTime.now());
            waybillParticipants.add(waybillParticipantsShou);
            //发货人
            WaybillParticipants waybillParticipantsFa = new WaybillParticipants();
            waybillParticipantsFa.setAppNo(appNo);
            waybillParticipantsFa.setParticipantsType("F");
            waybillParticipantsFa.setWaybillNo(waybillHeader.getWaybillNo());
            waybillParticipantsFa.setContainerNo(requesdetail.getContainerNo());
            waybillParticipantsFa.setConsignorName(requesdetail.getConsignorName());
            waybillParticipantsFa.setCountryCode(requesdetail.getConsignorCountryCode());
            waybillParticipantsFa.setDeleteFlag("N");
            waybillParticipantsFa.setAddWho(bookingRequesheader.getAddWho());
            waybillParticipantsFa.setAddWhoName(bookingRequesheader.getAddWhoName());
            waybillParticipantsFa.setAddTime(LocalDateTime.now());
            waybillParticipants.add(waybillParticipantsFa);
        }
        if (waybillParticipants.size() > 0) {
            waybillParticipantsMapper.insertWaybillParticipants(waybillParticipants);
        }

        List<WaybillGoodsInfo> waybillGoodsInfos = new ArrayList<>();
        for (RequesdetailDTO requesdetail : header.getDetails()) {
            if (requesdetail.getGoods() != null && requesdetail.getGoods().size() > 0) {
                List<GoodsDTO> gds = JSONUtil.toList(JSONUtil.parseArray(requesdetail.getGoods()), GoodsDTO.class);
                for (GoodsDTO gd : gds
                ) {
                    WaybillGoodsInfo waybillGoodsInfo = new WaybillGoodsInfo();
                    waybillGoodsInfo.setRowId(UUID.randomUUID().toString());
                    waybillGoodsInfo.setWaybillNo(account);
                    waybillGoodsInfo.setContainerNo(requesdetail.getContainerNo());
                    waybillGoodsInfo.setGoodsCode(gd.getGoodsCode());
                    waybillGoodsInfo.setGoodsChineseName(gd.getGoodsChineseName());
                    waybillGoodsInfo.setPackageType(gd.getPackageType());
                    waybillGoodsInfo.setGoodsNums(gd.getGoodsNums());
                    waybillGoodsInfo.setGoodsWeight(gd.getGoodsWeight());
                    waybillGoodsInfo.setGoodsValue(gd.getGoodsValue());

                    waybillGoodsInfo.setDeleteFlag("N");
                    waybillGoodsInfo.setAddWho(bookingRequesheader.getAddWho());
                    waybillGoodsInfo.setAddWhoName(bookingRequesheader.getAddWhoName());
                    waybillGoodsInfo.setAddTime(LocalDateTime.now());
                    waybillGoodsInfos.add(waybillGoodsInfo);
                }
            }
        }
        waybillGoodsInfoMapper.insertWaybillGoodsInfo(waybillGoodsInfos);

        List<String> oriCustcodes = new ArrayList<>();
        for (RequesdetailDTO rdd : header.getDetails()
        ) {
            if (!oriCustcodes.contains(rdd.getOriCustcode())) {
                oriCustcodes.add(rdd.getOriCustcode());
            }
        }
        for (String oriCustcode : oriCustcodes
        ) {
            if (oriCustcode != null && !"".equals(oriCustcode)) {
                String account2 = sysNoConfigService.genNo("YD");
                //插入运单主、子表信息
                WaybillHeaderSh waybillHeaderSh = new WaybillHeaderSh();
                waybillHeaderSh.setRowId(UUID.randomUUID().toString());
                waybillHeaderSh.setOrderNo(bookingRequesheader.getOrderNo());
                waybillHeaderSh.setWaybillNo(account2);
                waybillHeaderSh.setWaybillNoSh(account);
                waybillHeaderSh.setCustomerNo(oriCustcode);
                waybillHeaderSh.setPlatformCode(bookingRequesheader.getResveredField02());
                waybillHeaderSh.setPlatformName(bookingRequesheader.getCityPlatform());
                waybillHeaderSh.setTrainType(bookingRequesheader.getTrainType());
                waybillHeaderSh.setIdentification(bookingRequesheader.getIdentification());
                waybillHeaderSh.setIsCustomTransit(bookingRequesheader.getTransit());
                waybillHeaderSh.setShiftNo(bookingRequesheader.getShiftNo());
                waybillHeaderSh.setShippingTime(bookingRequesheader.getDeliveryTime());
                waybillHeaderSh.setShippingLine(bookingRequesheader.getShippingLine());
                waybillHeaderSh.setResveredField05(bookingRequesheader.getShippingLineCode());//发运线路编码
                waybillHeaderSh.setResveredField06(bookingRequesheader.getResveredField06());//境外代理
                waybillHeaderSh.setTrainName(bookingRequesheader.getTrainName());
                waybillHeaderSh.setResveredField02(bookingRequesheader.getResveredField02());//市平台名称
                waybillHeaderSh.setAuditFlag("DCSH");
                waybillHeaderSh.setBillStatus("1");
                waybillHeaderSh.setDeleteFlag("N");
                waybillHeaderSh.setAddWho(oriCustcode);
                waybillHeaderSh.setAddTime(LocalDateTime.now());
                waybillHeaderSh.setTrip(bookingRequesheader.getTrip());
                waybillHeaderSh.setIsTransit(bookingRequesheader.getTransit());
                waybillHeaderSh.setResveredField01(bookingRequesheader.getResveredField03());//审核意见
                waybillHeaderSh.setResveredField03(flag);                      //********新增逻辑 是否使用备用仓标识（0不使用1使用）

                float num2 = 0f;//上报舱位数
                int x = 0;
                for (RequesdetailDTO requesdetail : header.getDetails()) {
                    if (oriCustcode.equals(requesdetail.getOriCustcode())) {
                        x++;
                        waybillHeaderSh.setCustomerName(requesdetail.getOriCustname());
                        waybillHeaderSh.setAddWhoName(requesdetail.getOriCustname());

                        WaybillContainerInfoSh waybillContainerInfoSh = new WaybillContainerInfoSh();
                        waybillContainerInfoSh.setRowId(UUID.randomUUID().toString());
                        waybillContainerInfoSh.setOrderNo(appNo);
                        waybillContainerInfoSh.setWaybillNo(account2);
                        waybillContainerInfoSh.setWaybillNoSh(account);
                        waybillContainerInfoSh.setDemandNo(requesdetail.getDemandNo());
                        waybillContainerInfoSh.setIdentification(requesdetail.getIdentification());
                        waybillContainerInfoSh.setBureau(shifmanagements.get(0).getBureau());

                        waybillContainerInfoSh.setStationCompilation(requesdetail.getStationCompilation());
                        waybillContainerInfoSh.setDestinationName(requesdetail.getStartStationName());
                        waybillContainerInfoSh.setStartStationName(requesdetail.getStartStationName());
                        waybillContainerInfoSh.setEndCompilation(requesdetail.getEndCompilation());
                        waybillContainerInfoSh.setDestination(requesdetail.getEndStationName());
                        waybillContainerInfoSh.setEndStationName(requesdetail.getEndStationName());
                        waybillContainerInfoSh.setGoodsValue(requesdetail.getGoodsValue());
                        waybillContainerInfoSh.setPortStation(shifmanagements.get(0).getPortStation());
                        waybillContainerInfoSh.setContainerNo(requesdetail.getContainerNo());
                        waybillContainerInfoSh.setContainerOwner(requesdetail.getBox());
                        waybillContainerInfoSh.setContainerType(requesdetail.getContainerType());
                        waybillContainerInfoSh.setContainerDeadWeight(requesdetail.getDeadWeight());
                        waybillContainerInfoSh.setContainerGrossWeight(requesdetail.getGrossWeight());
                        waybillContainerInfoSh.setContainerNetWeight(requesdetail.getNetWeight());
                        waybillContainerInfoSh.setPortAgent(requesdetail.getPortAgent());
                        waybillContainerInfoSh.setDeleteFlag("N");
                        waybillContainerInfoSh.setAddWho(requesdetail.getOriCustcode());
                        waybillContainerInfoSh.setAddWhoName(requesdetail.getOriCustname());
                        waybillContainerInfoSh.setAddTime(new Date());
                        waybillContainerInfoSh.setDomesticFreight(requesdetail.getPrice());
                        waybillContainerInfoSh.setOverseasFreight(BigDecimal.valueOf(0.00));
                        waybillContainerInfoSh.setGoodsName(requesdetail.getGoodsName());
                        if (requesdetail.getContainerType().contains("20")) {
                            num2 = num2 + 0.5f;
                        } else if (requesdetail.getContainerType().contains("40") || requesdetail.getContainerType().contains("45")) {
                            num2 = num2 + 1.0f;
                        }
                        waybillContainerInfoShMapper.insertWaybillContainerInfoSh(waybillContainerInfoSh);
                    }
                }
                waybillHeaderSh.setTotalCases(String.valueOf(x));
                waybillHeaderSh.setResveredField04(String.valueOf(num2)); //********新增逻辑 占用舱数
                waybillHeaderShMapper.insertWaybillHeaderSh(waybillHeaderSh);
            }
        }

        return JSONUtil.parseObj(new R<>(Boolean.TRUE, "订舱完成！", account), false).toStringPretty();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveBookingAndWaybillForCity(RequesheaderDTO header, String platformCode) {
        CustomerInfo cus = new CustomerInfo();
        cus.setCustomerCode(platformCode);
        final CustomerInfo customerInfo = customerInfoMapper.selectCustomegenNorInfoByCustomerCode(cus);

        SecruityUser userInfo = SecurityUtils.getUserInfo();
        BookingRequesheader bookingRequesheader = new BookingRequesheader();
        bookingRequesheader.setBookingCustcode(header.getBookingCustcode());
        bookingRequesheader.setBookingCustname(header.getBookingCustname());
        bookingRequesheader.setShiftNo(header.getShiftNo());
        bookingRequesheader.setRemarks(header.getRemarks());
        bookingRequesheader.setResveredField02(platformCode);
        bookingRequesheader.setResveredField06(header.getResveredField06());
        if (header.getDetails() == null) {
            return new R<>(500, Boolean.FALSE, null, "请检查所填信息有遗漏！");
        }
        Shifmanagement shif = new Shifmanagement();
        shif.setShiftId(bookingRequesheader.getShiftNo());
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementListByLike(shif);
        if (CollUtil.isEmpty(shifmanagements)) {
            return new R<>(500, Boolean.FALSE, null, "该班次不存在！");
        }
        //没有申请单号，须新增主子表信息
        String appNo = sysNoConfigService.genNo("SQ");
        /*if (appNo.contains("，")) {
            return JSONUtil.parseObj(new R<>(Boolean.FALSE,appNo), false).toStringPretty();
        }*/
        float num = 0f;//上报舱位数
        List<BookingRequesdetail> detailList = new ArrayList<>();
        for (RequesdetailDTO rdd : header.getDetails()) {
            BookingRequesdetail details = new BookingRequesdetail();

            String containerNo = rdd.getContainerNo();
            boolean flag3 = verifyCntrCode(containerNo);
            if (!flag3) {
                return new R<>(500, Boolean.FALSE, null, "箱号填写有误，如有疑问请联系管理员！");
            }
            WaybillHeader wh = new WaybillHeader();
            wh.setShiftNo(header.getShiftNo());
            wh.setResveredField05(containerNo);
            List<WaybillHeader> list = waybillHeaderMapper.selectCnExistByShiftNo(wh);
            if (list != null && list.size() > 0) {
                return new R<>(500, Boolean.FALSE, null, containerNo + ",该箱号已被申报");
            }
            if (rdd.getContainerType().contains("20")) {
                num = num + 0.5f;
            } else if (rdd.getContainerType().contains("40") || rdd.getContainerType().contains("45")) {
                num = num + 1.0f;
            }
            details.setOrderNo(appNo);
            details.setDestinationName(rdd.getStartStationName());
            details.setDestination(rdd.getEndStationName());
            details.setBureauSubordinate(shifmanagements.get(0).getBureau());
            details.setPortStation(shifmanagements.get(0).getPortStation());
            details.setBox(rdd.getBox());
            details.setContainerNo(containerNo);
            details.setDeadWeight(rdd.getDeadWeight());
            details.setGrossWeight(rdd.getGrossWeight());
            details.setNetWeight(rdd.getNetWeight());

            details.setContainerTypeCode(rdd.getContainerTypeCode());
            details.setContainerTypeName(rdd.getContainerTypeName());
            details.setContainerType(rdd.getContainerType());

            details.setGoodsName(rdd.getGoodsName());
            details.setResveredField01(rdd.getIdentification());
            details.setResveredField02(rdd.getResveredField02());
            details.setDeleteFlag("N");
            details.setAddWho(bookingRequesheader.getBookingCustcode());
            details.setAddWhoName(bookingRequesheader.getBookingCustname());
            details.setAddTime(new Date());
            detailList.add(details);
        }

        SpaceOccupy sp = new SpaceOccupy();
        sp.setShiftNo(header.getShiftNo());
        Float occupyNums = spaceOccupyMapper.selectOccupyNums(sp);
        if (occupyNums == null || occupyNums < 0.5) {
            occupyNums = (float) 0;
        }
        Float usedSpNumsUndShf = occupyNums;//该班次已用舱位数量
        Float trueSpaceNums = 0.00f;
        Float trueSpaceNumsSp = 0.00f;
        if (shifmanagements.get(0).getNumOfTruePositions() != null && !"".equals(shifmanagements.get(0).getNumOfTruePositions())) {
            trueSpaceNums = Float.valueOf(shifmanagements.get(0).getNumOfTruePositions());//班次发布实际舱位
        }
        if (shifmanagements.get(0).getNumOfReservePositions() != null && !"".equals(shifmanagements.get(0).getNumOfReservePositions())) {
            trueSpaceNumsSp = Float.valueOf(shifmanagements.get(0).getNumOfReservePositions()); //班次发布备用舱位
        }
        Float sumSpace = trueSpaceNums + trueSpaceNumsSp;//该班次的总舱位数（实际+备用）
        Float spaceLeft = sumSpace - usedSpNumsUndShf;//总仓位-已用=剩余舱位数 （实际+备用）
        String flag = "0";     //判断是否使用备用仓标识0不使用1使用
        if (num > spaceLeft) {//上报舱位数若大于剩余舱位数,返回提示信息
            return new R<>(500, Boolean.FALSE, null, "剩余舱位数量不足，请重新确认");
        } else {
            //剩余舱位数满足本次提交，则更新舱位占用表，剩余舱位（实际+备用）数据
            SpaceOccupy so = new SpaceOccupy();
            so.setSpaceNums(String.valueOf(num));//当前班次已用舱位数
            so.setOrderNo(bookingRequesheader.getOrderNo());
            so.setShiftNo(bookingRequesheader.getShiftNo());
            so.setAddTime(LocalDateTime.now());
            so.setAddWhoName(bookingRequesheader.getBookingCustname());
            so.setAddWho(bookingRequesheader.getBookingCustcode());
            spaceOccupyService.insertSpaceOccupy(so);
            //判断是否使用备用仓(已占用+本次提交)>班次发布实际舱位数，即为使用备用仓(1)，否则为未使用(0)
            flag = (usedSpNumsUndShf + num) > trueSpaceNums ? "1" : "0";
        }

        String account = sysNoConfigService.genNo("YD");
        bookingRequesheader.setRowId(UUID.randomUUID().toString());
        bookingRequesheader.setWaybillNo(account);
        bookingRequesheader.setOrderNo(appNo);
        bookingRequesheader.setShiftNo(shifmanagements.get(0).getShiftId());
        bookingRequesheader.setShippingLineCode(shifmanagements.get(0).getShippingLineCode());
        bookingRequesheader.setShippingLine(shifmanagements.get(0).getShippingLine());
        bookingRequesheader.setTrainName(shifmanagements.get(0).getShiftName());
        bookingRequesheader.setDeliveryTime(shifmanagements.get(0).getPlanShipTime());
        bookingRequesheader.setTrainType(shifmanagements.get(0).getTrainType());
        bookingRequesheader.setTotalCases(String.valueOf(header.getDetails().size()));
        bookingRequesheader.setSpaceNums(num);
        bookingRequesheader.setDocumentStatus("2");
        bookingRequesheader.setTrip(shifmanagements.get(0).getTrip());
        bookingRequesheader.setCityPlatform(shifmanagements.get(0).getResveredField02());
        bookingRequesheader.setAuditType("1");
        bookingRequesheader.setResveredField02(shifmanagements.get(0).getPlatformCode());
        bookingRequesheader.setDeleteFlag("N");
        bookingRequesheader.setAddTime(new Date());
        bookingRequesheader.setAddWho(header.getBookingCustcode());
        bookingRequesheader.setAddWhoName(header.getBookingCustname());
        bookingRequesheader.setDetails(detailList);

        bookingRequesheaderMapper.insertBookingRequesheader(bookingRequesheader);
        bookingRequesdetailMapper.insertBookingRequesdetails(bookingRequesheader.getDetails());
        if (header.getFiles() != null && header.getFiles().size() > 0) {
            for (UploadRecord file : header.getFiles()
            ) {
                file.setBillNo(account);
                file.setBusiSeg("1");
                file.setResveredField01("group1");
                file.setAddTime(LocalDateTime.now());
                file.setAddWho(header.getBookingCustcode());
                file.setAddWhoName(header.getBookingCustname());
                uploadRecordMapper.insertUploadRecord(file);
            }
        }

        //插入运单主、子表信息
        WaybillHeader waybillHeader = new WaybillHeader();
        waybillHeader.setRowId(UUID.randomUUID().toString());
        waybillHeader.setOrderNo(bookingRequesheader.getOrderNo());
        waybillHeader.setWaybillNo(account);
        waybillHeader.setCustomerNo(bookingRequesheader.getBookingCustcode());
        waybillHeader.setCustomerName(bookingRequesheader.getBookingCustname());
        waybillHeader.setPlatformCode(bookingRequesheader.getResveredField02());
        waybillHeader.setPlatformName(bookingRequesheader.getCityPlatform());
        waybillHeader.setTrainType(bookingRequesheader.getTrainType());
        waybillHeader.setIdentification(bookingRequesheader.getIdentification());
        waybillHeader.setIsCustomTransit(bookingRequesheader.getTransit());
        waybillHeader.setShiftNo(bookingRequesheader.getShiftNo());
        waybillHeader.setShippingTime(bookingRequesheader.getDeliveryTime());
        waybillHeader.setShippingLine(bookingRequesheader.getShippingLine());
        waybillHeader.setResveredField05(bookingRequesheader.getShippingLineCode());//发运线路编码
        waybillHeader.setResveredField06(bookingRequesheader.getResveredField06());//境外代理
        waybillHeader.setTrainName(bookingRequesheader.getTrainName());
        waybillHeader.setResveredField02(bookingRequesheader.getResveredField02());//市平台名称
        waybillHeader.setAuditFlag("DCSH");
        waybillHeader.setTotalCases(bookingRequesheader.getTotalCases());
        waybillHeader.setBillStatus("2");
        waybillHeader.setDeleteFlag("N");
        waybillHeader.setAddWho(bookingRequesheader.getAddWho());
        waybillHeader.setAddWhoName(bookingRequesheader.getAddWhoName());
        waybillHeader.setAddTime(new Date());
        waybillHeader.setTrip(bookingRequesheader.getTrip());
        waybillHeader.setIsTransit(bookingRequesheader.getTransit());
        waybillHeader.setResveredField01(bookingRequesheader.getResveredField03());//审核意见
        waybillHeader.setResveredField03(flag);                      //********新增逻辑 是否使用备用仓标识（0不使用1使用）
        waybillHeader.setResveredField04(String.valueOf(num)); //********新增逻辑 占用舱数
        waybillHeader.setShAct("2");//上合代申请0否1是
        waybillHeaderMapper.insertWaybillHeader(waybillHeader);

        for (RequesdetailDTO requesdetail : header.getDetails()) {
            WaybillContainerInfo waybillContainerInfo = new WaybillContainerInfo();
            waybillContainerInfo.setRowId(UUID.randomUUID().toString());
            waybillContainerInfo.setOrderNo(appNo);
            waybillContainerInfo.setWaybillNo(account);
            waybillContainerInfo.setDemandNo(requesdetail.getDemandNo());
            waybillContainerInfo.setIdentification(requesdetail.getIdentification());
            waybillContainerInfo.setBureau(shifmanagements.get(0).getBureau());

            waybillContainerInfo.setStationCompilation(requesdetail.getStationCompilation());
            waybillContainerInfo.setDestinationName(requesdetail.getStartStationName());
            waybillContainerInfo.setStartStationName(requesdetail.getStartStationName());
            waybillContainerInfo.setEndCompilation(requesdetail.getEndCompilation());
            waybillContainerInfo.setDestination(requesdetail.getEndStationName());
            waybillContainerInfo.setEndStationName(requesdetail.getEndStationName());
            waybillContainerInfo.setGoodsValue(requesdetail.getGoodsValue());
            waybillContainerInfo.setPortStation(shifmanagements.get(0).getPortStation());
            waybillContainerInfo.setContainerNo(requesdetail.getContainerNo());
            waybillContainerInfo.setContainerOwner(requesdetail.getBox());

            waybillContainerInfo.setContainerTypeCode(requesdetail.getContainerTypeCode());
            waybillContainerInfo.setContainerTypeName(requesdetail.getContainerTypeName());
            waybillContainerInfo.setContainerType(requesdetail.getContainerType());

            waybillContainerInfo.setContainerDeadWeight(requesdetail.getDeadWeight());
            waybillContainerInfo.setContainerGrossWeight(requesdetail.getGrossWeight());
            waybillContainerInfo.setContainerNetWeight(requesdetail.getNetWeight());
            waybillContainerInfo.setPortAgent(requesdetail.getPortAgent());
            waybillContainerInfo.setAbroadReachCity(requesdetail.getAbroadReachCity());
            waybillContainerInfo.setDeleteFlag("N");
            waybillContainerInfo.setAddWho(bookingRequesheader.getAddWho());
            waybillContainerInfo.setAddWhoName(bookingRequesheader.getAddWhoName());
            waybillContainerInfo.setAddTime(new Date());
            waybillContainerInfo.setDomesticFreight(BigDecimal.valueOf(0.00));
            waybillContainerInfo.setOverseasFreight(BigDecimal.valueOf(0.00));
            waybillContainerInfo.setGoodsName(requesdetail.getGoodsName());
            waybillContainerInfoMapper.insertWaybillContainerInfo(waybillContainerInfo);
        }

        List<WaybillParticipants> waybillParticipants = new ArrayList<>();
        for (RequesdetailDTO requesdetail : header.getDetails()) {
            //收货人
            WaybillParticipants waybillParticipantsShou = new WaybillParticipants();
            waybillParticipantsShou.setRowId(UUID.randomUUID().toString());
            waybillParticipantsShou.setAppNo(appNo);
            waybillParticipantsShou.setParticipantsType("S");
            waybillParticipantsShou.setWaybillNo(waybillHeader.getWaybillNo());
            waybillParticipantsShou.setContainerNo(requesdetail.getContainerNo());
            waybillParticipantsShou.setConsignorName(requesdetail.getConsigneeName());
            waybillParticipantsShou.setCountryCode(requesdetail.getConsigneeCountryCode());
            waybillParticipantsShou.setDeleteFlag("N");
            waybillParticipantsShou.setAddWho(bookingRequesheader.getAddWho());
            waybillParticipantsShou.setAddWhoName(bookingRequesheader.getAddWhoName());
            waybillParticipantsShou.setAddTime(LocalDateTime.now());
            waybillParticipants.add(waybillParticipantsShou);
            //发货人
            WaybillParticipants waybillParticipantsFa = new WaybillParticipants();
            waybillParticipantsFa.setRowId(UUID.randomUUID().toString());
            waybillParticipantsFa.setAppNo(appNo);
            waybillParticipantsFa.setParticipantsType("F");
            waybillParticipantsFa.setWaybillNo(waybillHeader.getWaybillNo());
            waybillParticipantsFa.setContainerNo(requesdetail.getContainerNo());
            waybillParticipantsFa.setConsignorName(requesdetail.getConsignorName());
            waybillParticipantsFa.setCountryCode(requesdetail.getConsignorCountryCode());
            waybillParticipantsFa.setDeleteFlag("N");
            waybillParticipantsFa.setAddWho(bookingRequesheader.getAddWho());
            waybillParticipantsFa.setAddWhoName(bookingRequesheader.getAddWhoName());
            waybillParticipantsFa.setAddTime(LocalDateTime.now());
            waybillParticipants.add(waybillParticipantsFa);
        }
        if (waybillParticipants.size() > 0) {
            waybillParticipantsMapper.insertWaybillParticipants(waybillParticipants);
        }

        List<WaybillGoodsInfo> waybillGoodsInfos = new ArrayList<>();
        for (RequesdetailDTO requesdetail : header.getDetails()) {
            if (requesdetail.getGoods() != null && requesdetail.getGoods().size() > 0) {
                List<GoodsDTO> gds = JSONUtil.toList(JSONUtil.parseArray(requesdetail.getGoods()), GoodsDTO.class);
                for (GoodsDTO gd : gds
                ) {
                    WaybillGoodsInfo waybillGoodsInfo = new WaybillGoodsInfo();
                    waybillGoodsInfo.setRowId(UUID.randomUUID().toString());
                    waybillGoodsInfo.setWaybillNo(account);
                    waybillGoodsInfo.setContainerNo(requesdetail.getContainerNo());
                    waybillGoodsInfo.setGoodsCode(gd.getGoodsCode());
                    waybillGoodsInfo.setGoodsChineseName(gd.getGoodsChineseName());
                    waybillGoodsInfo.setPackageType(gd.getPackageType());
                    waybillGoodsInfo.setGoodsNums(gd.getGoodsNums());
                    waybillGoodsInfo.setGoodsWeight(gd.getGoodsWeight());
                    waybillGoodsInfo.setGoodsValue(gd.getGoodsValue());

                    waybillGoodsInfo.setDeleteFlag("N");
                    waybillGoodsInfo.setAddWho(bookingRequesheader.getAddWho());
                    waybillGoodsInfo.setAddWhoName(bookingRequesheader.getAddWhoName());
                    waybillGoodsInfo.setAddTime(LocalDateTime.now());
                    waybillGoodsInfos.add(waybillGoodsInfo);
                }
            }
        }
        if (CollUtil.isNotEmpty(waybillGoodsInfos)) {
            waybillGoodsInfoMapper.insertWaybillGoodsInfo(waybillGoodsInfos);
        }

        //境外运费(原币)
        BigDecimal freightOc = BigDecimal.valueOf(0);
        //境外运费(人民币)
        BigDecimal freightCny = BigDecimal.valueOf(0);
        //境内运费
        BigDecimal freight = BigDecimal.valueOf(0);

        String tsIn = noConfigService.genNo("TZ");
        if (tsIn.contains("请联系系统管理员")) {
            return new R<>(500, Boolean.FALSE, null, "生成台账编码失败！");
        }

        //下面的值应该是省平台
        String paymentCustomerCode = "MP210800001";
        String paymentCustomerName = "山东高速齐鲁号欧亚班列运营有限公司";

        CustomerPlatformInfo sel = new CustomerPlatformInfo();
        sel.setCustomerCode(platformCode);
        sel.setDeleteFlag("N");
        final List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoMapper.selectCustomerPlatformInfoList(sel);
        if (CollUtil.isNotEmpty(customerPlatformInfos)) {
            if (StrUtil.isNotEmpty(customerPlatformInfos.get(0).getPlatformCodePro())) {
                paymentCustomerCode = customerPlatformInfos.get(0).getPlatformCodePro();
            }
        }

        List<FdShippingAccoundetail> fdShippingAccoundetails = new ArrayList<>();
        for (RequesdetailDTO requesdetail : header.getDetails()) {
            FdShippingAccoundetail fdShippingAccoundetail = new FdShippingAccoundetail();
            fdShippingAccoundetail.setAccountCode(tsIn);
            fdShippingAccoundetail.setCustomerNo(bookingRequesheader.getBookingCustcode());
            fdShippingAccoundetail.setCustomerName(bookingRequesheader.getBookingCustname());
            fdShippingAccoundetail.setApplicationNumber(appNo);
            fdShippingAccoundetail.setTransportOrderNumber(account);
            fdShippingAccoundetail.setContainerNumber(requesdetail.getContainerNo());
            fdShippingAccoundetail.setIsRansit(requesdetail.getIdentification());
            fdShippingAccoundetail.setOrgUnit(bookingRequesheader.getCityPlatform());
            fdShippingAccoundetail.setDestinationName(requesdetail.getStartStationName());
            fdShippingAccoundetail.setDestinationCountry(requesdetail.getDestinationCountry());
            fdShippingAccoundetail.setDestination(requesdetail.getEndStationName());
            fdShippingAccoundetail.setPortAgent(requesdetail.getPortAgent());
            fdShippingAccoundetail.setGoodsName(requesdetail.getGoods().get(0).getGoodsChineseName());
            fdShippingAccoundetail.setContainerOwner(requesdetail.getBox());
            fdShippingAccoundetail.setContainerTypeCode(requesdetail.getContainerTypeCode());
            fdShippingAccoundetail.setContainerTypeName(requesdetail.getContainerTypeName());
            fdShippingAccoundetail.setContainerType(requesdetail.getContainerType());
            int goodsNums = 0;
            final List<GoodsDTO> goods = requesdetail.getGoods();
            if (CollUtil.isNotEmpty(goods)) {
                for (GoodsDTO good : goods
                ) {
                    goodsNums += Integer.valueOf(good.getGoodsNums());
                }
            }
            fdShippingAccoundetail.setGoodsNums(goodsNums);
            final List<GoodsDTO> goods1 = requesdetail.getGoods();
            if (CollUtil.isNotEmpty(goods1)) {
                Float val = 0f;
                for (GoodsDTO goodsDTO : goods1
                ) {
                    val = val + goodsDTO.getGoodsWeight();
                }
                fdShippingAccoundetail.setGoodsWeight(Double.valueOf(val));
            }

            fdShippingAccoundetail.setContainerWeight(String.valueOf(requesdetail.getDeadWeight()));
            fdShippingAccoundetail.setIsFull(requesdetail.getIsFull());
            fdShippingAccoundetail.setNonFerrous(requesdetail.getNonFerrous());

            if (StrUtil.isNotEmpty(shifmanagements.get(0).getTrip()) && "R".equals(shifmanagements.get(0).getTrip())) {
                fdShippingAccoundetail.setGoodsOwner(requesdetail.getConsigneeName());
//                fdShippingAccoundetail.setConsignorName(requesdetail.getConsignorName());
            } else {
                fdShippingAccoundetail.setGoodsOwner(requesdetail.getConsignorName());
//                fdShippingAccoundetail.setConsignorName(requesdetail.getConsigneeName());
            }
            fdShippingAccoundetail.setConsignorName(requesdetail.getConsigneeName());
            fdShippingAccoundetail.setClearanceNumber(requesdetail.getClearanceNumber());
            fdShippingAccoundetail.setValueUsd(requesdetail.getValueUsd());
            fdShippingAccoundetail.setCustomsSeal(requesdetail.getCustomsSeal());
            fdShippingAccoundetail.setTrainNumber(requesdetail.getTrainNumber());
            fdShippingAccoundetail.setWaybillDemandNumber(requesdetail.getWaybillDemandNumber());
            fdShippingAccoundetail.setWaybillLnNumber(requesdetail.getWaybillLnNumber());
            fdShippingAccoundetail.setDomesticFreight(requesdetail.getDomesticFreight());
            fdShippingAccoundetail.setOverseasFreightCny(requesdetail.getOverseasFreightCny());
            fdShippingAccoundetail.setOverseasFreightOc(requesdetail.getOverseasFreightOc());
            fdShippingAccoundetail.setRrDomesticFreight(requesdetail.getDomesticFreight());
            fdShippingAccoundetail.setRrOverseasFreightCny(requesdetail.getOverseasFreightCny());
            fdShippingAccoundetail.setRrOverseasFreightOc(requesdetail.getOverseasFreightOc());
            fdShippingAccoundetail.setMonetaryType(header.getMonetaryType());
            fdShippingAccoundetail.setExchangeRate(requesdetail.getExchangeRate());
            fdShippingAccoundetail.setSubsidyStandards(requesdetail.getSubsidyStandards());
            fdShippingAccoundetail.setSubsidyAmount(requesdetail.getSubsidyAmount());
            fdShippingAccoundetail.setRemarks(requesdetail.getRemarks());
            fdShippingAccoundetail.setDeleteFlag("N");
            fdShippingAccoundetail.setAddTime(LocalDateTime.now());
            fdShippingAccoundetail.setAddWho(bookingRequesheader.getAddWho());
            fdShippingAccoundetail.setAddWhoName(bookingRequesheader.getAddWhoName());
            if (fdShippingAccoundetail.getOverseasFreightOc() != null) {
                freightOc = freightOc.add(fdShippingAccoundetail.getOverseasFreightOc());
            }
            if (fdShippingAccoundetail.getOverseasFreightCny() != null) {
                freightCny = freightCny.add(fdShippingAccoundetail.getOverseasFreightCny());
            }
            if (fdShippingAccoundetail.getDomesticFreight() != null) {
                freight = freight.add(fdShippingAccoundetail.getDomesticFreight());
            }

            fdShippingAccoundetail.setGoodsOwner(requesdetail.getGoodsOwner());
            fdShippingAccoundetail.setGoodsOrigin(requesdetail.getGoodsOrigin());
            fdShippingAccoundetail.setExchangeRate(header.getExchangeRate());

            fdShippingAccoundetails.add(fdShippingAccoundetail);
        }
        FdShippingAccountVO fdShippingAccount = new FdShippingAccountVO();
        fdShippingAccount.setCustomerNo(paymentCustomerCode);
        fdShippingAccount.setCustomerName(paymentCustomerName);

        if (StrUtil.isNotBlank(shifmanagements.get(0).getDestinationName()) || StrUtil.isNotBlank(shifmanagements.get(0).getDestination())) {
            StationManagement sel2 = new StationManagement();
            if ("G".equals(shifmanagements.get(0).getTrip())) {
                sel2.setStationName(shifmanagements.get(0).getDestinationName());
                fdShippingAccount.setDestinationName(shifmanagements.get(0).getDestinationName());
            } else {
                sel2.setStationName(shifmanagements.get(0).getDestination());
                fdShippingAccount.setDestinationName(shifmanagements.get(0).getDestination());
            }
            sel2.setDeleteFlag("N");
            if (StrUtil.isNotBlank(sel2.getStationName())) {
                List<StationManagement> stationManagements = stationManagementMapper.selectStationManagementList(sel2);
                if (CollUtil.isNotEmpty(stationManagements)) {
                    fdShippingAccount.setCity(stationManagements.get(0).getCity());
                }
            }
        }

        fdShippingAccount.setDomesticFreight(freight);
        fdShippingAccount.setExchangeRate(header.getExchangeRate());
        fdShippingAccount.setOverseasFreightCny(freightCny);
        fdShippingAccount.setOverseasFreightOc(freightOc);
        fdShippingAccount.setPlatformCode(platformCode);
        fdShippingAccount.setPlatformFlag("0");
        fdShippingAccount.setPlatformName(customerInfo.getCompanyName());
        fdShippingAccount.setPortStation(shifmanagements.get(0).getPortStation());
        fdShippingAccount.setResveredField08Str(String.valueOf(shifmanagements.get(0).getPlanShipTime()));
        fdShippingAccount.setShiftName(shifmanagements.get(0).getShiftName());
        fdShippingAccount.setShiftNo(shifmanagements.get(0).getShiftId());
        fdShippingAccount.setShippingLine(shifmanagements.get(0).getShippingLine());
        fdShippingAccount.setTrip(shifmanagements.get(0).getTrip());

        fdShippingAccount.setAccountCode(tsIn);
        fdShippingAccount.setAddWho(userInfo.getUserName());
        fdShippingAccount.setAddWhoName(userInfo.getRealName());
        fdShippingAccount.setAddTime(LocalDateTime.now());
        fdShippingAccount.setCreateTime(LocalDateTime.now());

        fdShippingAccount.setShippingTime(LocalDateTime.ofInstant(shifmanagements.get(0).getPlanShipTime().toInstant(), ZoneId.systemDefault()));

        fdShippingAccount.setResveredField08(fdShippingAccount.getShippingTime());
        fdShippingAccount.setOverseasFreightOc(freightOc);
        fdShippingAccount.setOrgUnit(customerInfo.getCompanyName());
        fdShippingAccount.setMonetaryType(header.getMonetaryType());
        fdShippingAccount.setOverseasFreightCny(freightCny);
        fdShippingAccount.setDomesticFreight(freight);
        fdShippingAccount.setAmount(freightCny.add(freight));

        /*String provinceShiftNo = provinceShiftNoService.createProvinceShiftNo(fdShippingAccount);
        if (StrUtil.isNotBlank(provinceShiftNo)) {
            if (provinceShiftNo.contains("请联系系统管理员")) {
                return new R<>(500, Boolean.FALSE, null, provinceShiftNo);
            }
        }
        fdShippingAccount.setProvinceShiftNo(provinceShiftNo);*/
        fdShippingAccountMapper.insert(fdShippingAccount);
        for (FdShippingAccoundetail fdShippingAccoundetail : fdShippingAccoundetails
        ) {
            fdShippingAccoundetailMapper.insert(fdShippingAccoundetail);
        }

        return new R<>(0, Boolean.TRUE, null, "订舱完成！");
    }

    /**
     * 查询订舱申请单主表信息
     *
     * @param rowId 订舱申请单主表ID
     * @return 订舱申请单主表信息
     */
    @Override
    public BookingRequesheader selectBookingRequesheaderById(String rowId) {
        BookingRequesheader header = bookingRequesheaderMapper.selectBookingRequesheaderById(rowId);
        String shiftNo = header.getShiftNo();
        SpaceOccupy sp = new SpaceOccupy();
        sp.setShiftNo(shiftNo);
        Float sums = spaceOccupyMapper.selectOccupyNums(sp);
        if (sums == null || sums < 0.5) {
            sums = (float) 0;
        }
        header.setUsedSpNumsUndShf(sums);//已用的仓位数量
        return header;
    }

    /**
     * 查询订舱申请单主表列表
     *
     * @param bookingRequesheader 订舱申请单主表信息
     * @return 订舱申请单主表集合
     */
    @Override
    public List<BookingRequesheader> selectBookingRequesheaderList(BookingRequesheader bookingRequesheader) {
        return bookingRequesheaderMapper.selectBookingRequesheaderList(bookingRequesheader);
    }


    /**
     * 分页模糊查询订舱申请单主表列表
     *
     * @return 订舱申请单主表集合
     */
    @Override
    public Page selectBookingRequesheaderListByLike(Query query) {
        BookingRequesheader bookingRequesheader = BeanUtil.mapToBean(query.getCondition(), BookingRequesheader.class, false);
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        if ("0".equals(userInfo.getPlatformLevel())) {
            bookingRequesheader.setBookingCustcode(userInfo.getPlatformCode());
            if ("1".equals(userInfo.getDataFlag())) {
                bookingRequesheader.setMiniPlatform(userInfo.getMiniPlatform());
            }
        } else if ("1".equals(userInfo.getPlatformLevel())) {
            bookingRequesheader.setResveredField02(userInfo.getPlatformCode());
        }

        /*String usercode = userInfo.getUserName();
        bookingRequesheader.setBookingCustcode(usercode);
        Integer c;*/
        if ("0".equals(bookingRequesheader.getResveredField01())) {
            //订舱平台
            if (StrUtil.isBlank(query.getOrderByField())) {
                query.setOrderByField("x.shift_no DESC,x.booking_custname DESC,FIND_IN_SET(x.document_status, '3,1,0,2,4' ),x.add_time");
                query.setAsc(Boolean.FALSE);
            }
//            c = bookingRequesheaderMapper.queryCount(bookingRequesheader);
//            query.setTotal(c);
            query.setRecords(bookingRequesheaderMapper.selectBookingRequesheaderListByLike(query, bookingRequesheader));
        } else {
            //管理平台
            if (StrUtil.isBlank(query.getOrderByField())) {
                query.setOrderByField("x.shift_no DESC,x.booking_custname DESC,FIND_IN_SET(x.document_status, '3,1,0,2,4' ),x.add_time");
                query.setAsc(Boolean.FALSE);
            }
//            c = bookingRequesheaderMapper.queryCount2(bookingRequesheader);
//            query.setTotal(c);
            query.setRecords(bookingRequesheaderMapper.selectBookingRequesheaderListByLike1(query, bookingRequesheader));
        }
        return query;
    }

    @Override
    public Integer selectBookingRequesheaderListByLikeCount(BookingRequesheader bookingRequesheader) {
        return bookingRequesheaderMapper.queryCount2(bookingRequesheader);
    }

    //校验柜号是否正确
    /*
     * 1、第一部分由4位英文字母组成。前三位代码 (Owner Code) 主要说明箱主、经营人，第四位代码说明集装箱的类型。列如CBHU
     *  开头的标准集装箱是表明箱主和经营人为中远集运。
     * 2、 第二部分由6位数字组成。是箱体注册码（Registration Code），用于一个集装箱箱体持有的唯一标识。
     * 3、 第三部分为校验码（Check Digit）由前4位字母和6位数字经过校验规则运算得到，用于识别在校验时是否发生错误。即第11位数字。
     * 根据校验规则箱号的每个字母和数字都有一个运算的对应值。箱号的前10位字母和数字的对应值从0到Z对应数值为10到38，11、22、33不能对11取模数，所以要除去
     * 地址：https://blog.csdn.net/weixin_38611617/article/details/115069232
     */
    public static boolean verifyCntrCode(String strCode) {
        boolean result = true;
        strCode = strCode.trim();
        try {
            if (strCode.length() != 11) {
                return false;
            }
            if (strCode.startsWith("JSQ5") || strCode.startsWith("JSQ6")) {
                String str = strCode.substring(4);
                char[] codeChars = str.toCharArray();
                String charCode = "0123456789";
                for (int i = 0; i < 7; i++) {
                    int idx = charCode.indexOf(codeChars[i]);
                    if (idx == -1 || charCode.charAt(idx) == '?') {
                        return false;
                    }
                }
                return true;
            } else {
                char[] codeChars = strCode.toCharArray();
                String charCode = "0123456789A?BCDEFGHIJK?LMNOPQRSTU?VWXYZ";
                int num = 0;
                for (int i = 0; i < 10; i++) {
                    int idx = charCode.indexOf(codeChars[i]);
                    if (idx == -1 || charCode.charAt(idx) == '?') {
                        return false;
                    }
                    idx = (int) (idx * Math.pow(2, i));
                    num += idx;
                }
                num = (num % 11) % 10;
                result = Integer.parseInt(String.valueOf(codeChars[10])) == num;
            }

        } catch (Exception e) {
            result = false;
        }
        return result;
    }

    /**
     * 新增订舱申请单主表
     *
     * @param bookingRequesheader 订舱申请单主表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R insertBookingRequesheader(BookingRequesheader bookingRequesheader) {
        if (bookingRequesheader.getDetails() == null) {
            return R.error("请检查所填信息有遗漏");
        }
        Shifmanagement sel = new Shifmanagement();
        sel.setShiftId(bookingRequesheader.getShiftNo());
        sel.setPlatformCode(bookingRequesheader.getPlatformCode());
        sel.setDeleteFlag("N");
        sel.setReleaseStatus("1");
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
        if (CollUtil.isEmpty(shifmanagements)) {
            return R.error("该班次不存在或未发布，请联系平台管理员");
        }
        for (BookingRequesdetail details : bookingRequesheader.getDetails()) {
            if (StrUtil.isNotBlank(shifmanagements.get(0).getIdentification())
                    && !shifmanagements.get(0).getIdentification().equals(details.getResveredField01())) {
                return R.error("箱的进/出口类型和班列信息不一致！");
            }
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        String appNo = "";
        if (bookingRequesheader.getOrderNo() != null && !bookingRequesheader.getOrderNo().isEmpty()) {
            //有申请单号更新订舱申请子表，即集装箱信息，同时维护主表总箱数数据和更新时间
            try {
                bookingRequesdetailMapper.deleteBookingRequesdetailByOrderNo(bookingRequesheader.getOrderNo());
                for (BookingRequesdetail details : bookingRequesheader.getDetails()) {
                    log.info("shifmanagements.get(0).getIdentification(){}", shifmanagements.get(0).getIdentification());
                    log.info("bookingRequesheader.getIdentification(){}", bookingRequesheader.getIdentification());
                    log.info("bookingRequesheader.getIdentification(){}", details.getResveredField01());

                    if (StrUtil.isNotBlank(shifmanagements.get(0).getIdentification())
                            && !shifmanagements.get(0).getIdentification().equals(details.getResveredField01())) {
                        return R.error("箱的进/出口类型和班列信息不一致！");
                    }
                    String containerNo = details.getContainerNo();
                    boolean flag3 = verifyCntrCode(containerNo);
                    if (flag3) {
                    } else {
                        return R.error( "箱号填写有误，如有疑问请联系管理员");
                    }
                    WaybillHeader wh = new WaybillHeader();
                    wh.setShiftNo(bookingRequesheader.getShiftNo());
                    wh.setResveredField05(containerNo);
                    List<WaybillHeader> list = waybillHeaderMapper.selectCnExistByShiftNo(wh);
                    if (list == null || list.isEmpty()) {
                    } else {
                        return R.error( containerNo + ",该箱号已被申报");
                    }
                    details.setOrderNo(bookingRequesheader.getOrderNo());
                    details.setAddWho(usercode);
                    details.setAddWhoName(username);
                    details.setAddTime(new Date());
                }
                bookingRequesheader.setTotalCases(String.valueOf(bookingRequesheader.getDetails().size()));
                bookingRequesheader.setUpdateTime(new Date());
                bookingRequesheader.setUpdateWho(usercode);
                bookingRequesheader.setUpdateWhoName(username);
                bookingRequesheaderMapper.updateBookingRequesheader(bookingRequesheader);
                bookingRequesdetailMapper.insertBookingRequesdetails(bookingRequesheader.getDetails());
            } catch (Exception e) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("提交失败！");
            }

        } else {
            //没有申请单号，须新增主子表信息
            appNo = sysNoConfigService.genNo("SQ");
            if (appNo.contains("，")) {
                return R.error( appNo);
            }
            for (BookingRequesdetail details : bookingRequesheader.getDetails()) {
                String containerNo = details.getContainerNo();
                boolean flag3 = verifyCntrCode(containerNo);
                if (!flag3) {
                    return R.error( "箱号填写有误，如有疑问请联系管理员");
                }
                WaybillHeader wh = new WaybillHeader();
                wh.setShiftNo(bookingRequesheader.getShiftNo());
                wh.setResveredField05(containerNo);
                List<WaybillHeader> list = waybillHeaderMapper.selectCnExistByShiftNo(wh);
                if (list == null || list.size() == 0) {
                } else {
                    return R.error( containerNo + ",该箱号已被申报");
                }
                details.setOrderNo(appNo);
                details.setAddWho(usercode);
                details.setAddWhoName(username);
                details.setAddTime(new Date());
            }
            try {
                bookingRequesheader.setRowId(UUID.randomUUID().toString());
                bookingRequesheader.setAddTime(new Date());
                bookingRequesheader.setAddWho(usercode);
                bookingRequesheader.setAddWhoName(username);
                bookingRequesheader.setOrderNo(appNo);
                bookingRequesheader.setTotalCases(String.valueOf(bookingRequesheader.getDetails().size()));
                bookingRequesheaderMapper.insertBookingRequesheader(bookingRequesheader);
                bookingRequesdetailMapper.insertBookingRequesdetails(bookingRequesheader.getDetails());
            } catch (Exception e) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("订舱失败！");
            }
        }
        if ("1".equals(bookingRequesheader.getDocumentStatus())) {
            BookingRequesheader bookingRequesheader1 = bookingRequesheaderMapper.selectBookingRequesheaderByOrderNo(bookingRequesheader.getOrderNo());
            sendMiniMsgNotice(bookingRequesheader1);
        }
        return R.success("订舱成功");

    }

    public void sendMiniMsgNotice(BookingRequesheader bookingRequesheader) {
        String content = bookingRequesheader.getBookingCustname() + "提交了【" + bookingRequesheader.getTrainName() + "（" + bookingRequesheader.getShiftNo() + "）】的订舱申请，请及时登录PC端在【订舱管理-申请单查询】中审核。";
        MessageCenter messageCenter = new MessageCenter();
        messageCenter.setModuleType(MessageBusinessEnum.BOOKING_PROCESS.getKey());
        messageCenter.setModuleName(MessageBusinessEnum.BOOKING_PROCESS.getValue());
        messageCenter.setBusinessId(bookingRequesheader.getRowId());
        messageCenter.setBusinessType(MessageBusinessEnum.BOOKING_AUDIT.getKey());
        messageCenter.setBusinessName(MessageBusinessEnum.BOOKING_AUDIT.getValue());
        List<String> usernames = remoteAdminService.getUserNameByRoleName("业务岗", bookingRequesheader.getResveredField02());
        messageCenterService.sendMiniMsgNotice(usernames, "您有新的订舱申请待审核，请及时处理。", content, messageCenter);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveForSh(String data) {
        BookingRequesheader bookingRequesheader = JSONUtil.toBean(data, BookingRequesheader.class);
        if (bookingRequesheader.getDetails() == null) {
            return JSONUtil.parseObj(new R<>(Boolean.FALSE, "请检查所填信息有遗漏！"), false).toStringPretty();
        }

        BookingRequesheader requesheader = bookingRequesheaderMapper.selectBookingRequesheaderByOrderNo(bookingRequesheader.getOrderNo());
        if (requesheader != null) {
            if ("1".equals(requesheader.getDocumentStatus()) || "2".equals(requesheader.getDocumentStatus())) {
                return JSONUtil.parseObj(new R<>(Boolean.FALSE, "该申请单状态不允许修改！"), false).toStringPretty();
            }
        }
//        else{
//            return JSONUtil.parseObj(new R<>(Boolean.FALSE,"未查询到该申请单，请检查申请单号！"), false).toStringPretty();
//        }
//        SecruityUser userInfo = SecurityUtils.getUserInfo();
//        String usercode = userInfo.getUserName();
//        String username = userInfo.getRealName();
        String appNo = "";
        if (bookingRequesheader.getOrderNo() != null && !"".equals(bookingRequesheader.getOrderNo())) {
            //有申请单号更新订舱申请子表，即集装箱信息，同时维护主表总箱数数据和更新时间
            try {
                for (BookingRequesdetail details : bookingRequesheader.getDetails()) {
                    String containerNo = details.getContainerNo();
                    boolean flag3 = verifyCntrCode(containerNo);
                    if (flag3) {
                    } else {
                        return JSONUtil.parseObj(new R<>(Boolean.FALSE, "箱号填写有误，如有疑问请联系管理员！"), false).toStringPretty();
                    }
                    WaybillHeader wh = new WaybillHeader();
                    wh.setShiftNo(bookingRequesheader.getShiftNo());
                    wh.setResveredField05(containerNo);
                    List<WaybillHeader> list = waybillHeaderMapper.selectCnExistByShiftNo(wh);
                    if (list == null || list.size() == 0) {
                    } else {
                        return JSONUtil.parseObj(new R<>(Boolean.FALSE, containerNo + ",该箱号已被申报"), false).toStringPretty();
                    }
                    details.setOrderNo(bookingRequesheader.getOrderNo());
                    details.setAddWho(bookingRequesheader.getBookingCustcode());
                    details.setAddWhoName(bookingRequesheader.getBookingCustname());
                    details.setAddTime(new Date());

                    calSpaceNums(details.getContainerType());
                }
                bookingRequesheader.setTotalCases(String.valueOf(bookingRequesheader.getDetails().size()));
                bookingRequesheader.setUpdateTime(new Date());
                bookingRequesheader.setDocumentStatus("1");
                bookingRequesheader.setSpaceNums(spaceNums);
                bookingRequesheader.setResveredField01("0");
//                bookingRequesheader.setUpdateWho(usercode);
//                bookingRequesheader.setUpdateWhoName(username);
                bookingRequesdetailMapper.deleteBookingRequesdetailByOrderNo(bookingRequesheader.getOrderNo());

                bookingRequesheaderMapper.updateBookingRequesheader(bookingRequesheader);
                bookingRequesdetailMapper.insertBookingRequesdetails(bookingRequesheader.getDetails());
            } catch (Exception e) {
                System.out.println(e.getMessage() + "@@@@@@提交失败！@@@@@@");
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return JSONUtil.parseObj(new R<>(Boolean.FALSE, "提交失败！"), false).toStringPretty();
            }
            return JSONUtil.parseObj(new R<>(Boolean.TRUE, "提交成功！"), false).toStringPretty();
        } else {
            //没有申请单号，须新增主子表信息
            appNo = sysNoConfigService.genNo("SQ");
            if (appNo.contains("，")) {
                return JSONUtil.parseObj(new R<>(Boolean.FALSE, appNo), false).toStringPretty();
            }
            for (BookingRequesdetail details : bookingRequesheader.getDetails()) {
                String containerNo = details.getContainerNo();
                boolean flag3 = verifyCntrCode(containerNo);
                if (flag3) {
                } else {
                    return JSONUtil.parseObj(new R<>(Boolean.FALSE, "箱号填写有误，如有疑问请联系管理员！"), false).toStringPretty();
                }
                WaybillHeader wh = new WaybillHeader();
                wh.setShiftNo(bookingRequesheader.getShiftNo());
                wh.setResveredField05(containerNo);
                List<WaybillHeader> list = waybillHeaderMapper.selectCnExistByShiftNo(wh);
                if (list == null || list.size() == 0) {
                } else {
                    return JSONUtil.parseObj(new R<>(Boolean.FALSE, containerNo + ",该箱号已被申报"), false).toStringPretty();
                }
                details.setOrderNo(appNo);
                details.setAddWho(bookingRequesheader.getBookingCustcode());
                details.setAddWhoName(bookingRequesheader.getBookingCustname());
                details.setAddTime(new Date());

                calSpaceNums(details.getContainerType());
            }
            try {
                bookingRequesheader.setRowId(UUID.randomUUID().toString());
                bookingRequesheader.setAddTime(new Date());
                bookingRequesheader.setAddWho(bookingRequesheader.getAddWho());
                bookingRequesheader.setAddWhoName(bookingRequesheader.getAddWhoName());
                bookingRequesheader.setOrderNo(appNo);
                bookingRequesheader.setTotalCases(String.valueOf(bookingRequesheader.getDetails().size()));
                bookingRequesheader.setDocumentStatus("1");
                bookingRequesheader.setSpaceNums(spaceNums);
                bookingRequesheader.setResveredField01("0");

                bookingRequesheaderMapper.insertBookingRequesheader(bookingRequesheader);
                bookingRequesdetailMapper.insertBookingRequesdetails(bookingRequesheader.getDetails());
            } catch (Exception e) {
                System.out.println(e.getMessage() + "@@@@@@订舱失败！@@@@@@");
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return JSONUtil.parseObj(new R<>(Boolean.FALSE, "订舱失败！"), false).toStringPretty();
            }
            return JSONUtil.parseObj(new R<>(Boolean.TRUE, appNo), false).toStringPretty();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String queryAuditForSh(String data) {
        BookingRequesheader bookingRequesheader = JSONUtil.toBean(data, BookingRequesheader.class);
        if (bookingRequesheader != null) {
            if (StrUtil.isEmpty(bookingRequesheader.getOrderNo())) {
                return JSONUtil.parseObj(new R<>(Boolean.FALSE, "未接收到申请单号！"), false).toStringPretty();
            }
            if (StrUtil.isEmpty(bookingRequesheader.getAddWho())) {
                return JSONUtil.parseObj(new R<>(Boolean.FALSE, "未接收到用户账号！"), false).toStringPretty();
            }
        }
        BookingRequesheader header = bookingRequesheaderMapper.queryAuditForSh(bookingRequesheader);
        if (header != null) {
            return JSONUtil.parseObj(new R<>(Boolean.TRUE, header), true).toStringPretty();
        } else {
            return JSONUtil.parseObj(new R<>(Boolean.FALSE, "未查询到该申请单，请检查申请单号！"), false).toStringPretty();
        }
    }

    float spaceNums = 0f;//占用舱位数

    /**
     * 计算占用舱位数
     */
    private float calSpaceNums(String conType) {
        if (conType.contains("20")) {
            spaceNums = spaceNums + 0.5f;
        } else if (conType.contains("40") || conType.contains("45")) {
            spaceNums = spaceNums + 1.0f;
        } else {
            spaceNums = -1.0f;
        }
        return spaceNums;
    }


    /**
     * 删除订舱申请单信息
     *
     * @param bookingRequesheader 订舱申请单主表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateBookingRequesheader(BookingRequesheader bookingRequesheader) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        //删除
        bookingRequesheader.setDeleteWho(usercode);
        bookingRequesheader.setDeleteWhoName(username);
        bookingRequesheader.setDeleteTime(new Date());
        bookingRequesheader.setDeleteFlag("Y");
        BookingRequesdetail brd = new BookingRequesdetail();
        brd.setDeleteWho(usercode);
        brd.setDeleteWhoName(username);
        brd.setDeleteTime(new Date());
        brd.setDeleteFlag("Y");
        brd.setOrderNo(bookingRequesheader.getOrderNo());
        try {
            bookingRequesheaderMapper.updateBookingRequesheader(bookingRequesheader);
            bookingRequesdetailMapper.updateBookingRequesdetail(brd);
        } catch (Exception e) {
            System.out.println(e.getMessage() + "删除订舱申请失败！！！");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new R(500, Boolean.FALSE, "删除订舱申请失败");
        }
        return new R(200, Boolean.TRUE, "删除订舱申请成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateBookingRequesheader1(BookingRequesheader bookingRequesheader) {
        return bookingRequesheaderMapper.updateBookingRequesheader1(bookingRequesheader);
    }

    /*
     * 修改状态为待审核
     * */
    @Override
    public R commitStatus(String orderNo) {
        BookingRequesheader bookingRequesheader = bookingRequesheaderMapper.selectBookingRequesheaderByOrderNo(orderNo);
        if (bookingRequesheader != null) {
            sendMiniMsgNotice(bookingRequesheader);
            Shifmanagement sel = new Shifmanagement();
            sel.setShiftId(bookingRequesheader.getShiftNo());
            sel.setPlatformCode(bookingRequesheader.getResveredField02());
            sel.setDeleteFlag("N");
            sel.setReleaseStatus("1");
            List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
            if (CollUtil.isEmpty(shifmanagements)) {
                return new R<>(500, Boolean.FALSE, null, "该班次不存在或未发布，请联系平台管理员");
            }
        }
        bookingRequesheaderMapper.commitStatus(orderNo);
        return new R<>(200, Boolean.TRUE, null, "提交成功");
    }


    /**
     * 删除订舱申请单主表
     *
     * @param rowId 订舱申请单主表ID
     * @return 结果
     */
    @Override
    public int deleteBookingRequesheaderById(String rowId) {
        return bookingRequesheaderMapper.deleteBookingRequesheaderById(rowId);
    }


    /**
     * 批量删除订舱申请单主表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBookingRequesheaderByIds(Integer[] rowIds) {
        return bookingRequesheaderMapper.deleteBookingRequesheaderByIds(rowIds);
    }

    @Override
    public int insertUser(SysUser sysUser) {
        sysUser.setDatabase(database);
        return sysUserMapper.insertUser(sysUser);
    }

    @Override
    public int insertUserAndRole(SysUser sysUser) {
        sysUser.setDatabase(database);
        final int i = sysUserMapper.insertUser(sysUser);
        final List<SysUser> sysUsers = sysUserMapper.selectUserList(sysUser);
        if (CollUtil.isNotEmpty(sysUsers)) {
            if (sysUsers.get(0) != null) {
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setDatabase(database);
                sysUserRole.setRoleName("BOOK_CSR_MG_ADMIN");
                final List<SysUserRole> sysUserRoles = sysUserRoleMapper.selectSysRoleId(sysUserRole);
                if (CollUtil.isNotEmpty(sysUserRoles)) {
                    if (sysUserRoles.get(0) != null) {
                        sysUserRole.setUserId(sysUsers.get(0).getUserId());
                        sysUserRole.setRoleId(sysUserRoles.get(0).getRoleId());
                        sysUserRoleMapper.insertSysUserRole(sysUserRole);
                    }
                }
            }

        }
        return i;
    }


    @Override
    public List<SysUser> selectUserList(SysUser sysUser) {
        sysUser.setDatabase(database);
        return sysUserMapper.selectUserList(sysUser);
    }

    @Override
    public int updateSysUser(SysUser sysUser) {
        sysUser.setDatabase(database);
        return sysUserMapper.updateSysUserById(sysUser);
    }

    /**
     * 删除用户
     *
     * @param sysUser 用户
     * @return Boolean
     */
    @Override
    public Boolean deleteUserById(SysUser sysUser) {
        SysUserRole sysUserRole = new SysUserRole();
        sysUserRole.setUserId(sysUser.getUserId());
        sysUserRole.setDatabase(database);
        int i = sysUserRoleMapper.deleteSysUserRoleById(sysUserRole);
        int i1 = sysUserMapper.deleteSysUserById(sysUser);
        if (i == 0 || i1 == 0) {
            return false;
        }
        return true;
    }

    @Override
    public BookingRequesheader selectBookingRequesheaderByOrderNo(String orderNo) {
        return bookingRequesheaderMapper.selectBookingRequesheaderByOrderNo(orderNo);
    }

    /*
     * 删除申请单&&运单&&业务流程单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R deleteAll(@RequestBody BookingRequesheader bookingRequesheader) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        BookingRequesheader header = bookingRequesheaderMapper.selectBookingRequesheaderById(bookingRequesheader.getRowId());
        BookingRequesdetail sel = new BookingRequesdetail();
        sel.setOrderNo(header.getOrderNo());
        sel.setDeleteFlag("N");
        List<BookingRequesdetail> bookingRequesdetails = bookingRequesdetailMapper.selectBookingRequesdetailList(sel);
        if (CollUtil.isNotEmpty(bookingRequesdetails) && StrUtil.isNotBlank(header.getWaybillNo())) {
            List<WaybillHeader> waybillHeaders = waybillHeaderMapper.selectWaybillHeaderByWaybillNo(header.getWaybillNo());
            if (CollUtil.isNotEmpty(waybillHeaders)) {
                String result = deleteAllCheck(bookingRequesdetails, waybillHeaders);
                if (StrUtil.isNotBlank(result)) {
                    return R.error(result);
                }
                //删除运单&&业务流程单
                deleteWaybillAndFdBusCost(userInfo, bookingRequesdetails, waybillHeaders);
                //删除后程转运数据
                fdPostTransportMapper.deleteFdPostTransportByWaybillNo(header.getWaybillNo());
                fdPostTransportDetailMapper.deleteFdPostTransportDetailByWaybillNo(header.getWaybillNo());
            }
        }
        //删除申请单
        bookingRequesheaderMapper.deleteBookingRequesheaderById(header.getRowId());
        bookingRequesdetailMapper.deleteBookingRequesdetailByOrderNo(header.getOrderNo());
        //删除仓位占用
        spaceOccupyMapper.deleteSpaceByOrderNo(header.getOrderNo());
        return R.success();
    }

    /*
     * 删除运单&&业务流程单校验
     */
    public String deleteAllCheck(List<BookingRequesdetail> bookingRequesdetails, List<WaybillHeader> waybillHeaders) {
        FdBusCost sel2 = new FdBusCost();
        sel2.setPlatformCode(waybillHeaders.get(0).getPlatformCode());
        sel2.setCustomerCode(waybillHeaders.get(0).getCustomerNo());
        sel2.setShiftNo(waybillHeaders.get(0).getShiftNo());
        sel2.setDeleteFlag("N");
        List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel2);

        if (CollUtil.isNotEmpty(fdBusCosts) && CollUtil.isNotEmpty(bookingRequesdetails)) {
            if ("2".equals(fdBusCosts.get(0).getAuditStatus())) {
                return fdBusCosts.get(0).getPlatformName() + "业务流程单已经审批通过，不允许删除！";
            }
            if ("1".equals(fdBusCosts.get(0).getAuditStatus())) {
                return fdBusCosts.get(0).getPlatformName() + "业务流程单已经发起审批，不允许删除！";
            }
        }
        //删除内联上级运单&&业务流程单
        WaybillHeader sel4 = new WaybillHeader();
        sel4.setShiftNo(waybillHeaders.get(0).getShiftNo());
        sel4.setCustomerNo(waybillHeaders.get(0).getPlatformCode());
        sel4.setDeleteFlag("N");
        List<WaybillHeader> list = waybillHeaderMapper.selectWaybillHeaderList(sel4);
        if (CollUtil.isNotEmpty(list)) {
            String result = deleteAllCheck(bookingRequesdetails, list);
            if (StrUtil.isNotBlank(result)) {
                return result;
            }
        }
        return null;
    }

    /*
     * 删除运单&&业务流程单
     */
    public void deleteWaybillAndFdBusCost(SecruityUser userInfo, List<BookingRequesdetail> bookingRequesdetails, List<WaybillHeader> waybillHeaders) {
        FdBusCost sel2 = new FdBusCost();
        sel2.setPlatformCode(waybillHeaders.get(0).getPlatformCode());
        sel2.setCustomerCode(waybillHeaders.get(0).getCustomerNo());
        sel2.setShiftNo(waybillHeaders.get(0).getShiftNo());
        sel2.setDeleteFlag("N");
        List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel2);

        if (CollUtil.isNotEmpty(fdBusCosts) && CollUtil.isNotEmpty(bookingRequesdetails)) {
            FdBusCostDetail del = new FdBusCostDetail();
            del.setCostCode(fdBusCosts.get(0).getCostCode());
            del.setDeleteWho(userInfo.getUserName());
            del.setDeleteWhoName(userInfo.getRealName());
            del.setDeleteTime(LocalDateTime.now());
            for (BookingRequesdetail bookingRequesdetail : bookingRequesdetails
            ) {
                del.setContainerNumber(bookingRequesdetail.getContainerNo());
                fdBusCostDetailMapper.deleteFdBusCostDetailByCostCode(del);
            }

        }
        //删除运单
        for (BookingRequesdetail bookingRequesdetail : bookingRequesdetails
        ) {
            waybillContainerInfoMapper.deleteByWaybillNo(waybillHeaders.get(0).getWaybillNo(), bookingRequesdetail.getContainerNo());
            waybillParticipantsMapper.deleteByWaybillNo(waybillHeaders.get(0).getWaybillNo(), bookingRequesdetail.getContainerNo());
            waybillGoodsInfoMapper.deleteByWaybillNo(waybillHeaders.get(0).getWaybillNo(), bookingRequesdetail.getContainerNo());
        }
        WaybillContainerInfo sel5 = new WaybillContainerInfo();
        sel5.setWaybillNo(waybillHeaders.get(0).getWaybillNo());
        sel5.setDeleteFlag("N");
        List<WaybillContainerInfo> waybillContainerInfos = waybillContainerInfoMapper.selectWaybillContainerInfoList(sel5);
        if (CollUtil.isEmpty(waybillContainerInfos)) {
            waybillHeaderMapper.deleteWaybillHeaderById(waybillHeaders.get(0).getRowId());
            //删除业务流程单
            if (CollUtil.isNotEmpty(fdBusCosts)) {
                fdBusCostWaybillMapper.deleteFdBusCostWaybill(fdBusCosts.get(0).getCostCode(), waybillHeaders.get(0).getWaybillNo());
                /*FdBusCostDetail sel3 = new FdBusCostDetail();
                sel3.setCostCode(fdBusCosts.get(0).getCostCode());
                sel3.setDeleteFlag("N");
                List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList(sel3);*/
                List<WaybillContainerInfo> detailList = waybillContainerInfoMapper.selectInfoByCostCode(fdBusCosts.get(0).getCostCode());
                if (CollUtil.isEmpty(detailList)) {
                    fdBusCostMapper.deleteFdBusCostById(fdBusCosts.get(0).getId());
                }
            }
        } else {
            waybillHeaderMapper.updateTotalCases(waybillHeaders.get(0).getWaybillNo());
            waybillHeaderMapper.updateResveredField04(waybillHeaders.get(0).getWaybillNo());
        }

        //删除内联上级运单&&业务流程单
        WaybillHeader sel4 = new WaybillHeader();
        sel4.setShiftNo(waybillHeaders.get(0).getShiftNo());
        sel4.setCustomerNo(waybillHeaders.get(0).getPlatformCode());
        sel4.setDeleteFlag("N");
        List<WaybillHeader> list = waybillHeaderMapper.selectWaybillHeaderList(sel4);
        if (CollUtil.isNotEmpty(list)) {
            deleteWaybillAndFdBusCost(userInfo, bookingRequesdetails, list);
        }
    }

}
