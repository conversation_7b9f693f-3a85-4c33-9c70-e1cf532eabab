package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.FdBusCostInfoDTO;
import com.huazheng.tunny.ocean.api.dto.FdBusCostMainGroupDTO;
import com.huazheng.tunny.ocean.api.dto.RequesheaderDTO;
import com.huazheng.tunny.ocean.api.entity.FdBusCost;
import com.huazheng.tunny.ocean.api.vo.BpmVo;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 业务流程单主表 服务接口层
 *
 * <AUTHOR>
 * @date 2024-05-23 17:06:31
 */
public interface FdBusCostService extends IService<FdBusCost> {
    /**
     * 查询业务流程单主表信息
     *
     * @param id 业务流程单主表ID
     * @return 业务流程单主表信息
     */
    public FdBusCostInfoDTO selectFdBusCostById(Integer id);

    /**
     * 查询业务流程单主表列表
     *
     * @param fdBusCost 业务流程单主表信息
     * @return 业务流程单主表集合
     */
    public List<FdBusCost> selectFdBusCostList(FdBusCost fdBusCost);


    /**
     * 分页模糊查询业务流程单主表列表
     *
     * @return 业务流程单主表集合
     */
    public Page selectFdBusCostListByLike(Query query);

    public List<FdBusCost> selectFdBusCostListsByLike(FdBusCost fdBusCost);

    /**
     * 分页模糊查询业务流程单主表列表
     *
     * @return 业务流程单主表集合
     */
    public Page selectFdBusCostPage(Query query);


    /**
     * 新增业务流程单主表
     *
     * @param fdBusCost 业务流程单主表信息
     * @return 结果
     */
    public int insertFdBusCost(FdBusCost fdBusCost);

    public R saveFiles(FdBusCost fdBusCost);

    public int updateFdBusCost(FdBusCost fdBusCost);

    /**
     * 修改业务流程单主表
     *
     * @param fdBusCost 业务流程单主表信息
     * @return 结果
     */
    public R commit(FdBusCost fdBusCost);

    public R addFeesCommit(FdBusCost fdBusCost);

    public R audit(FdBusCost fdBusCost);

    public R reject(FdBusCost fdBusCost);

    public List<BpmVo> getFullNodeInfo(FdBusCost fdBusCost);

    /**
     * 删除业务流程单主表
     *
     * @param id 业务流程单主表ID
     * @return 结果
     */
    public int deleteFdBusCostById(Integer id);

    /**
     * 批量删除业务流程单主表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdBusCostByIds(Integer[] ids);

    void exportBusProcess(String costCode, HttpServletResponse response) throws Exception;

    void exportReceiveTemplate(FdBusCost fdBusCost, HttpServletResponse response) throws Exception;

    void exportReceiveCustomizeTemplate(FdBusCost fdBusCost, HttpServletResponse response) throws Exception;

    void exportPayTemplate(FdBusCost fdBusCost, HttpServletResponse response) throws Exception;

    void exportReceiveTzTemplate(FdBusCost fdBusCost, HttpServletResponse response) throws Exception;

    void exportReceiveJnTemplate(FdBusCost fdBusCost, HttpServletResponse response) throws Exception;

    void exportPayTzTemplate(FdBusCost fdBusCost, HttpServletResponse response) throws Exception;

    R importedDetail(FdBusCost fdBusCost, MultipartFile fileimportedReceive, String costType);

    R importedReceiveCustomize(FdBusCost fdBusCost, MultipartFile fileimportedReceive, String costType);

    R importedDetailTzTemplate(FdBusCost fdBusCost, MultipartFile fileimportedReceive, String costType);

    R importedReceiveJnTemplate(FdBusCost fdBusCost, MultipartFile fileimportedReceive, String costType);

    R busCostMainList(FdBusCost fdBusCost);

    R busCostMainNum(FdBusCost fdBusCost);

    void exportBusProcessToPDF(FdBusCost fdBusCost, HttpServletResponse response) throws Exception;

    void exportBusProcessToPDF2(FdBusCostMainGroupDTO fdBusCostMainGroupDTO, HttpServletResponse response) throws Exception;

    String exportBusProcessToPng(FdBusCostMainGroupDTO fdBusCostMainGroupDTO);

    R busCostMainListShift(FdBusCost fdBusCost);

    R busCostMainNumShift(FdBusCost fdBusCost);

    void exportBusProcessShiftToPDF(FdBusCost fdBusCost, HttpServletResponse response) throws Exception;

    @Transactional(rollbackFor = Exception.class)
    R saveBookingAndWaybillForCity(RequesheaderDTO header) throws Exception;

    @Transactional(rollbackFor = Exception.class)
    R saveBookingAndWaybillListForCity(List<RequesheaderDTO> headers) throws Exception;

    FdBusCost selectFdBusCostOld(FdBusCost fdBusCost, String trip);

    void exportTemplateForYw(HttpServletResponse response) throws IOException;

    void exportTemplateForYwTwo(HttpServletResponse response) throws IOException;

    void exportTemplateForYwThree(HttpServletResponse response) throws IOException;

    R importedTemplateForYw(MultipartFile file) throws Exception;

    R importedTemplateForYwTwo(MultipartFile file) throws Exception;

    R importedTemplateForYwThree(MultipartFile file) throws Exception;

    R changeRate(FdBusCost fdBusCost, String source);

    R tzCommitCheck(FdBusCost fdBusCost);

}

