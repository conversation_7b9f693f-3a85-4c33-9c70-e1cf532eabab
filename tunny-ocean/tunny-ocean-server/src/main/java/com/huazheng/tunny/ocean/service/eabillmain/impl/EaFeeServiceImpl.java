package com.huazheng.tunny.ocean.service.eabillmain.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.ocean.api.dto.eabillmain.EaFeeDTO;
import com.huazheng.tunny.ocean.api.feign.RemoteAdminService;
import com.huazheng.tunny.ocean.api.vo.SysDictVo;
import com.huazheng.tunny.ocean.mapper.eabillmain.EaFeeMapper;
import com.huazheng.tunny.ocean.api.entity.eabillmain.EaFee;
import com.huazheng.tunny.ocean.service.eabillmain.EaFeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("eaFeeService")
public class EaFeeServiceImpl extends ServiceImpl<EaFeeMapper, EaFee> implements EaFeeService {

    @Autowired
    private EaFeeMapper eaFeeMapper;
    @Autowired
    private RemoteAdminService remoteAdminService;

    /**
     * 查询费用表信息
     *
     * @param feeId 费用表ID
     * @return 费用表信息
     */
    @Override
    public EaFee selectEaFeeById(Long feeId) {
        return eaFeeMapper.selectEaFeeById(feeId);
    }

    /**
     * 查询费用表列表
     *
     * @param eaFee 费用表信息
     * @return 费用表集合
     */
    @Override
    public List<EaFeeDTO> selectEaFeeList(EaFeeDTO eaFee) {
        List<EaFeeDTO> list = eaFeeMapper.selectEaFeeList(eaFee);
        if (CollUtil.isNotEmpty(list)) {
            List<SysDictVo> xxlx = getDictList("xxlx");
            for (EaFeeDTO detail : list) {
                for (SysDictVo xxlxDict : xxlx) {
                    if (detail.getContainerOwner().equals(xxlxDict.getCode())) {
                        detail.setContainerOwner(xxlxDict.getName());
                        break;
                    }
                }
            }
        }
        return list;
    }


    /**
     * 分页模糊查询费用表列表
     *
     * @return 费用表集合
     */
    @Override
    public Page selectEaFeeListByLike(Query query) {
        EaFeeDTO eaFeeDTO = BeanUtil.mapToBean(query.getCondition(), EaFeeDTO.class, false);
        List<EaFeeDTO> list = eaFeeMapper.selectEaFeeListByLike(query, eaFeeDTO);
        if (CollUtil.isNotEmpty(list)) {
            List<SysDictVo> xxlx = getDictList("xxlx");
            for (EaFeeDTO detail : list) {
                for (SysDictVo xxlxDict : xxlx) {
                    if (detail.getContainerOwner().equals(xxlxDict.getCode())) {
                        detail.setContainerOwner(xxlxDict.getName());
                        break;
                    }
                }
            }
        }
        query.setRecords(list);
        return query;
    }
    /**
     * 查询字典
     *
     * @return List<SysDictVo> 国家列表
     * <AUTHOR>
     * @since 2025/4/25 下午5:51
     **/
    public List<SysDictVo> getDictList(String type) {
        String data = remoteAdminService.selectDictByType2(type);
        return JSONUtil.toList(JSONUtil.parseArray(data), SysDictVo.class);
    }
    /**
     * 新增费用表
     *
     * @param eaFee 费用表信息
     * @return 结果
     */
    @Override
    public int insertEaFee(EaFee eaFee) {
        return eaFeeMapper.insertEaFee(eaFee);
    }

    /**
     * 修改费用表
     *
     * @param eaFee 费用表信息
     * @return 结果
     */
    @Override
    public int updateEaFee(EaFee eaFee) {
        return eaFeeMapper.updateEaFee(eaFee);
    }


    /**
     * 删除费用表
     *
     * @param feeId 费用表ID
     * @return 结果
     */
    public int deleteEaFeeById(Long feeId) {
        return eaFeeMapper.deleteEaFeeById(feeId);
    }

    ;


    /**
     * 批量删除费用表对象
     *
     * @param feeIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEaFeeByIds(Integer[] feeIds) {
        return eaFeeMapper.deleteEaFeeByIds(feeIds);
    }

}
