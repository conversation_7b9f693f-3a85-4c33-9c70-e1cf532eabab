package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.FdPostTransportDTO;
import com.huazheng.tunny.ocean.api.entity.FdPostTransport;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.WaybillHeader;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * 后程转运表 服务接口层
 *
 * <AUTHOR>
 * @date 2024-08-23 14:17:30
 */
public interface FdPostTransportService extends IService<FdPostTransport> {
    /**
     * 查询后程转运表信息
     *
     * @param id 后程转运表ID
     * @return 后程转运表信息
     */
    public FdPostTransportDTO selectFdPostTransportById(Integer id);

    public FdPostTransportDTO infoCity(FdPostTransport fdPostTransport);

    /**
     * 查询后程转运表列表
     *
     * @param fdPostTransport 后程转运表信息
     * @return 后程转运表集合
     */
    public List<FdPostTransport> selectFdPostTransportList(FdPostTransport fdPostTransport);


    /**
     * 分页模糊查询后程转运表列表
     * @return 后程转运表集合
     */
    public Page selectFdPostTransportListByLike(Query query);

    public Page pageCustomer(Query query);

    public Page pageCity(Query query);

    /**
     * 新增后程转运表
     *
     * @param fdPostTransport 后程转运表信息
     * @return 结果
     */
    public int insertFdPostTransport(FdPostTransport fdPostTransport);

    /**
     * 修改后程转运表
     *
     * @param fdPostTransport 后程转运表信息
     * @return 结果
     */
    public R updateCustomer(FdPostTransportDTO fdPostTransport);

    public R updateCity(FdPostTransportDTO fdPostTransport);

    public int auditCity(FdPostTransport fdPostTransport);

    /**
     * 删除后程转运表
     *
     * @param id 后程转运表ID
     * @return 结果
     */
    public int deleteFdPostTransportById(Integer id);

    /**
     * 批量删除后程转运表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdPostTransportByIds(Integer[] ids);

    public void insertPostTransport(WaybillHeader waybillHeader);

    public void fdPostTransportExported(FdPostTransport fdPostTransport, HttpServletResponse response) throws Exception;
}

