package com.huazheng.tunny.ocean.controller;


import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.BasRailwaySettlement;
import com.huazheng.tunny.ocean.service.BasRailwaySettlementService;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 中铁多联结算对象表
 *
 * <AUTHOR> code ocean
 * @date 2021-08-23 15:48:45
 */
@RestController
@RequestMapping("/basrailwaysettlement")
@Slf4j
public class BasRailwaySettlementController {
    @Autowired
    private BasRailwaySettlementService basRailwaySettlementService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  basRailwaySettlementService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return basRailwaySettlementService.selectBasRailwaySettlementListByLike(new Query<>(params));
    }

    /**
     * 查询余额类型
     * @param provincialCode
     * @return
     */
    @GetMapping("/getBanlanceType")
    public R getBanlanceType(String provincialCode){
        return new R(basRailwaySettlementService.getBanlanceType(provincialCode));
    }


    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        BasRailwaySettlement basRailwaySettlement =basRailwaySettlementService.selectById(rowId);
        return new R<>(basRailwaySettlement);
    }

    /**
     * 保存
     * @param basRailwaySettlement
     * @return R
     */
    @PostMapping("/save")
    public R save(@RequestBody BasRailwaySettlement basRailwaySettlement) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        basRailwaySettlement.setAddWhoName(userInfo.getRealName());
        basRailwaySettlement.setAddWho(userInfo.getUserName());
        basRailwaySettlement.setAddTime(new Date());
        basRailwaySettlement.setDeleteFlag("N");
        basRailwaySettlement.setRowId(UUID.randomUUID().toString());
        basRailwaySettlement.setShUnitCode(getlinkNo());
        basRailwaySettlementService.insert(basRailwaySettlement);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param basRailwaySettlement
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody BasRailwaySettlement basRailwaySettlement) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        basRailwaySettlement.setUpdateWhoName(userInfo.getRealName());
        basRailwaySettlement.setUpdateWho(userInfo.getUserName());
        basRailwaySettlement.setUpdateTime(new Date());
        basRailwaySettlementService.updateById(basRailwaySettlement);
        return new R<>(Boolean.TRUE);
    }



    /**
     * 删除
     * @param basRailwaySettlement
     * @return R
     */
    @GetMapping("/delete")
    public R delete(BasRailwaySettlement basRailwaySettlement) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        basRailwaySettlement.setDeleteWhoName(userInfo.getRealName());
        basRailwaySettlement.setDeleteWho(userInfo.getUserName());
        basRailwaySettlement.setDeleteTime(new Date());
        basRailwaySettlement.setDeleteFlag("Y");
        basRailwaySettlementService.deleteBasRailwaySettlementById(basRailwaySettlement);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        basRailwaySettlementService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<BasRailwaySettlement> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = basRailwaySettlementService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<BasRailwaySettlement> list = reader.readAll(BasRailwaySettlement.class);
        basRailwaySettlementService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 生成六位数的唯一编码
     * @return
     */
    public static String getlinkNo() {
        String linkNo = "";
        // 用字符数组的方式随机
        String model = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        char[] m = model.toCharArray();
        for (int j = 0; j < 6; j++) {
            char c = m[(int) (Math.random() * 36)];
            // 保证六位随机数之间没有重复的
            if (linkNo.contains(String.valueOf(c))) {
                j--;
                continue;
            }
            linkNo = linkNo + c;
        }
        return linkNo;
    }
}
