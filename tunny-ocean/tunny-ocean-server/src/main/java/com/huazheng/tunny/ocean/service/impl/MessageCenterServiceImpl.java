package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.bpm.api.feign.RemoteBpmService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.StringUtils;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.MessageCenter;
import com.huazheng.tunny.ocean.api.enums.MessageBusinessEnum;
import com.huazheng.tunny.ocean.api.enums.MessageTypeEnum;
import com.huazheng.tunny.ocean.api.vo.BpmBacklogListVO;
import com.huazheng.tunny.ocean.common.websocket.WebSocket;
import com.huazheng.tunny.ocean.mapper.MessageCenterMapper;
import com.huazheng.tunny.ocean.service.MessageCenterService;
import com.huazheng.tunny.ocean.util.R;
import com.huazheng.tunny.wechat.api.dto.WxMiniMagParamDTO;
import com.huazheng.tunny.wechat.api.feign.WxAppletFeignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: Service
 * @Author: shaojian
 * @Date: 2022-10-09 11:31:53
 */
@Service
@Slf4j
public class MessageCenterServiceImpl extends ServiceImpl<MessageCenterMapper, MessageCenter> implements MessageCenterService {

    @Autowired
    private MessageCenterMapper messageCenterMapper;

    @Autowired
    private RemoteBpmService remoteBpmService;

    @Value("${tunny.bpm.busCostKey}")
    private String defKey;

    @Value("${wx.template.message}")
    private String messageTemplateId;

    @Value("${wx.template.auditNotice}")
    private String auditNotice;

    @Autowired
    private WxAppletFeignService wxAppletFeignService;
    @Autowired
    private WebSocket webSocket;
    /**
     * @Description: 分页
     * @Param: query
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    @Override
    public Page page(Query query) {
        MessageCenter param = BeanUtil.mapToBean(query.getCondition(), MessageCenter.class, false);
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
        param.setReceiveUser(secruityUser.getUserName());
        query.setRecords(messageCenterMapper.pageMessageCenter(query, param));
        return query;
    }

    /**
     * @Description: 详情
     * @Param: id
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    @Override
    public R info(Integer id) {
        return R.success(messageCenterMapper.infoMessageCenter(id));
    }

    /**
     * @Description: 保存
     * @Param: param
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R save(MessageCenter param) {
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
        param.setCreateName(secruityUser.getRealName());
        messageCenterMapper.saveMessageCenter(param);
        return R.success(param.getId());
    }

    /**
     * @Description: 修改
     * @Param: param
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R update(MessageCenter param) {
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
        messageCenterMapper.updateMessageCenter(param);
        return R.success();
    }

    /**
     * @Description: 删除
     * @Param: id
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R delete(Integer id) {
        messageCenterMapper.deleteMessageCenter(id);
        return R.success();
    }

    /**
     * @Description: 列表
     * @Param: param
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    @Override
    public R list(MessageCenter param) {
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
        param.setReceiveUser(secruityUser.getUserName());
        return R.success(messageCenterMapper.listMessageCenter(param));
    }

    /**
     * @Description: 切换已读
     * @Param: param
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    @Override
    public R read(Map<String, Object> param) {
        List<Integer> ids = (List<Integer>) param.get("ids");
        for (Integer id : ids) {
            messageCenterMapper.read(id);
        }
        return R.success();
    }

    /**
     * @Description: 根据模块切换已读
     * @Param: param
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    @Override
    public R readByModule(Map<String, Object> param) {
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
        param.put("receiveUser", secruityUser.getUserName());
        if (ObjectUtils.isEmpty(param.get("moduleType")) || StringUtils.isEmpty(param.get("moduleType").toString())) {
            messageCenterMapper.updateAllRead(param);
        } else {
            messageCenterMapper.updateReadByModule(param);
        }
        return R.success();
    }

    /**
     * @Description: 根据模块切换已读V2
     * @Param: param
     * @Return: R
     * @Author: shaojian
     * @Date: 2022-10-09 11:31:53
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R readByModuleV2(MessageCenter param) {
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
        // 根据传入的模块类型和业务类型, 业务 id, 接收用户将待办设置成已读;
        EntityWrapper<MessageCenter> wrapper = new EntityWrapper<>();
        wrapper.eq("module_type", param.getModuleType())
                .eq("business_id", param.getBusinessId())
                .eq("push_target", param.getPushTarget())
                .eq("receive_user", secruityUser.getId());
        if (!org.springframework.util.StringUtils.isEmpty(param.getBusinessType())) {
            wrapper.eq("business_type", param.getBusinessType());
        }
        List<MessageCenter> messageCenters = messageCenterMapper.selectList(wrapper);

        if (!CollectionUtils.isEmpty(messageCenters)) {
            messageCenters.forEach(l -> l.setIsRead(1));
            boolean b = this.updateAllColumnBatchById(messageCenters);
        }
        return R.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R getMyMsgNum(MessageCenter param) {
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
        param.setReceiveUser(secruityUser.getUserName());
        param.setIsRead(0);
        Integer num = messageCenterMapper.getMyMsgNum(param);
        return R.success(num);
    }

    /**
     * 获取当前登录人代办业务单数据
     * @return
     */
    @Override
    public R selectBpmBacklog(Query query) {
        //SecruityUser secruityUser = SecurityUtils.getUserInfo();
        R r = new R();
        Object userNameObj = query.getCondition().get("userName");
        String userName = String.valueOf(userNameObj);
        List<BpmBacklogListVO> bpmBacklogListVOS = messageCenterMapper.selectBpmBacklog(query,userName);
        if(CollectionUtil.isNotEmpty(bpmBacklogListVOS)){
            query.setRecords(bpmBacklogListVOS);
        }
        r.setData(query);
        return r;
    }

    /**
     * 获取当前登录已办理数据
     * @param query
     * @return
     */
    @Override
    public R selectBpmProcessed(Query query) {
        R r = new R();
        Object userNameObj = query.getCondition().get("userName");
        String userName = String.valueOf(userNameObj);
        List<Map<String,Object>> handle = remoteBpmService.processList(userName, "handleAndComplete", null, defKey);
        List<String> procIds = new ArrayList<>();
        if(CollUtil.isNotEmpty(handle)){
            for(Map<String, Object> map : handle){
                Object procId = map.get("procId");
                procIds.add(String.valueOf(procId));
            }
        }
        if(CollectionUtil.isNotEmpty(procIds)){
            List<BpmBacklogListVO> bpmBacklogListVOS = messageCenterMapper.selectBpmProcessedByProcIds(query,procIds);
            if(CollectionUtil.isNotEmpty(bpmBacklogListVOS)){
                for (BpmBacklogListVO vo:bpmBacklogListVOS
                     ) {
                    if(CollUtil.isNotEmpty(handle)){
                        for(Map<String, Object> map : handle){
                            Object procId = map.get("procId");
                            if(vo.getProcId().equals(String.valueOf(procId))){
                                if(map.get("flag") != null){
                                    vo.setApproveStatus(String.valueOf(map.get("flag")));
                                }
                            }
                        }
                    }
                }
                query.setRecords(bpmBacklogListVOS);
            }
        }
        r.setData(query);
        return r;
    }

    /**
     * 系统内部存储接口
     * @param addMessageDTOS
     * @return
     */
    @Override
    public R addMessage(List<MessageCenter> addMessageDTOS,String psb,String businessName) {
        SimpleDateFormat sm = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        this.insertBatch(addMessageDTOS);

        List<String> userNames = addMessageDTOS.stream().map(MessageCenter::getReceiveUser).collect(Collectors.toList());

        List<String> openIds = messageCenterMapper.selectUserOpenId(userNames);

        // userId
        List<Integer> userIds = messageCenterMapper.selectUserId(userNames);

        Map<String, Object> map = new HashMap<>();
        Map<String,Object> thing2 = new HashMap<>();
        thing2.put("value",addMessageDTOS.get(0).getBusinessName());
        Map<String,Object> time13 = new HashMap<>();
        time13.put("value",sm.format(new Date()));
        Map<String,Object> thing4 = new HashMap<>();
        thing4.put("value",psb );
        map.put("thing2", thing2);
        map.put("time13", time13);
        map.put("thing4", thing4);

        Map<String,Object> socket = new HashMap<>();
        socket.put("moduleName",MessageTypeEnum.SETTLEMENT.getValue());
        socket.put("businessName",businessName);
        socket.put("content",addMessageDTOS.get(0).getContent());
        webSocket.sendMoreMessage(userIds.toArray(new Integer[userIds.size()]), JSONObject.toJSONString(socket));

        // 推送消息
        try {
            log.info("微信推送消息内容："+map.toString());
            WxMiniMagParamDTO wxMiniMagParamDTO = new WxMiniMagParamDTO();
            wxMiniMagParamDTO.setTemplate_id(messageTemplateId);
            wxMiniMagParamDTO.setPage("");
            wxMiniMagParamDTO.setOpenIds(openIds);
            wxMiniMagParamDTO.setData(map);
            wxAppletFeignService.sendMiniMsg(wxMiniMagParamDTO);
        }catch (Exception e){
            log.error("结算单推送微信小程序消息报错："+e);
            e.printStackTrace();
        }

        return new R();
    }

    /**
     * 根据用户名查询用户消息Title
     * @param userName
     * @return
     */
    @Override
    public List<Map<String,String>> selectModelByUserName(String userName) {
        List<Map<String,String>> mapList = new ArrayList<>();
        List<String> messageTypes = messageCenterMapper.selectModelByUserName(userName);
        if(CollectionUtil.isNotEmpty(messageTypes)){
            for(String s :messageTypes){
                MessageTypeEnum messageTypeEnum = MessageTypeEnum.fromKey(s);
                if(!ObjectUtils.isEmpty(messageTypeEnum)){
                    Map<String,String> map = new HashMap<>();
                    map.put("key",messageTypeEnum.getKey());
                    map.put("value",messageTypeEnum.getValue());
                    mapList.add(map);
                }
            }
        }
        return mapList;
    }

    @Override
    public void pushWebSocket(Map<String, String> map) {
        webSocket.sendOneMessage(Integer.valueOf(map.get("userId")),map.get("content"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendMiniMsgYw(String userNames, String thing4,String content, Integer id,String name) {
        if (StrUtil.isNotBlank(userNames)) {
            if (userNames.contains("[")) {
                userNames = userNames.replace("[", "");
            }
            if (userNames.contains("]")) {
                userNames = userNames.replace("]", "");
            }
            if (StrUtil.isNotBlank(userNames)) {
                String[] split = userNames.split(",");
                List<String> list = CollUtil.toList(split);
                List<String> openIds = messageCenterMapper.selectUserOpenId(list);
                if (CollUtil.isNotEmpty(openIds)) {
                    Map<String, Object> data = new HashMap<>();
                    Map<String, Object> name1 = new HashMap<>();
                    name1.put("value",name);
                    data.put("name1", name1);
                    Map<String, Object> thing2 = new HashMap<>();
                    thing2.put("value","业务流程单审核");
                    data.put("thing2", thing2);
                    Map<String, Object> time3 = new HashMap<>();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    time3.put("value",LocalDateTime.now().format(formatter));
                    data.put("time3", time3);
                    Map<String, Object> thing = new HashMap<>();
                    thing.put("value",thing4);
                    data.put("thing4",thing );
                    try {
                        WxMiniMagParamDTO wxMiniMagParamDTO = new WxMiniMagParamDTO();
                        wxMiniMagParamDTO.setOpenIds(openIds);
                        wxMiniMagParamDTO.setTemplate_id(auditNotice);
                        wxMiniMagParamDTO.setPage("pages/home/<USER>" + id);
                        wxMiniMagParamDTO.setData(data);

                        wxAppletFeignService.sendMiniMsg(wxMiniMagParamDTO);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                    }
                }
                List<Integer> userIds = messageCenterMapper.selectUserId(list);
                for (String username : split
                ) {
                    MessageCenter messageCenter = new MessageCenter();
                    messageCenter.setReceiveUser(username);
                    messageCenter.setModuleType(MessageBusinessEnum.BUSINESS_PROCESS.getKey());
                    messageCenter.setModuleName(MessageBusinessEnum.BUSINESS_PROCESS.getValue());
                    messageCenter.setBusinessId(String.valueOf(id));
                    messageCenter.setBusinessType(MessageBusinessEnum.BUSINESS_AUDIT.getKey());
                    messageCenter.setBusinessName(MessageBusinessEnum.BUSINESS_AUDIT.getValue());
                    messageCenter.setContent(content);
                    messageCenter.setIsRead(0);
                    messageCenter.setType("0");
                    messageCenter.setDelFlag("0");
                    messageCenterMapper.saveMessageCenter(messageCenter);
                }
                webSocket.sendMoreMessageList(userIds, content);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendMiniMsgNotice(List<String> userNames, String thing4,String content, MessageCenter messageCenter) {
        if (CollUtil.isNotEmpty(userNames)) {
            List<String> openIds = messageCenterMapper.selectUserOpenId(userNames);
            if (CollUtil.isNotEmpty(openIds)) {
                Map<String, Object> data = new HashMap<>();
//                Map<String, Object> name1 = new HashMap<>();
//                name1.put("value",SecurityUtils.getUserInfo().getRealName());
//                data.put("name1", name1);
                Map<String, Object> thing2 = new HashMap<>();
                thing2.put("value",messageCenter.getModuleName());
                data.put("thing2", thing2);
                Map<String, Object> time13 = new HashMap<>();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                time13.put("value",LocalDateTime.now().format(formatter));
                data.put("time13", time13);
                Map<String, Object> thing = new HashMap<>();
                thing.put("value",thing4);
                data.put("thing4", thing);
                try {
                    WxMiniMagParamDTO wxMiniMagParamDTO = new WxMiniMagParamDTO();
                    wxMiniMagParamDTO.setOpenIds(openIds);
                    wxMiniMagParamDTO.setTemplate_id(messageTemplateId);
                    wxMiniMagParamDTO.setPage("");
                    wxMiniMagParamDTO.setData(data);
                    wxAppletFeignService.sendMiniMsg(wxMiniMagParamDTO);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
            List<Integer> userIds = messageCenterMapper.selectUserId(userNames);
            for (String username : userNames
            ) {
                messageCenter.setReceiveUser(username);
                messageCenter.setContent(content);
                messageCenter.setIsRead(0);
                messageCenter.setType("0");
                messageCenter.setDelFlag("0");
                messageCenterMapper.saveMessageCenter(messageCenter);
            }
            webSocket.sendMoreMessageList(userIds, content);
        }
    }
}
