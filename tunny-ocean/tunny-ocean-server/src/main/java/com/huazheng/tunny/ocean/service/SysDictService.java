package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.SysDict;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;
import java.util.Map;

/**
 * 字典表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-06-30 20:51:51
 */
public interface SysDictService extends IService<SysDict> {
    /**
     * 查询字典表信息
     *
     * @param rowId 字典表ID
     * @return 字典表信息
     */
    public SysDict selectSysDictById(String rowId);

    /**
     * 查询字典表信息
     *
     * @param sysDict 字典信息
     * @return 字典表信息
     */
    public SysDict selectSysDictByDictValue(SysDict sysDict);

    /**
     * 查询字典表列表
     *
     * @param sysDict 字典表信息
     * @return 字典表集合
     */
    public List<SysDict> selectSysDictList(SysDict sysDict);


    /**
     * 分页模糊查询字典表列表
     * @return 字典表集合
     */
    public Page selectSysDictListByLike(Query query);

    public List<SysDict> selectDict(Map<String, Object> params);



    /**
     * 新增字典表
     *
     * @param sysDict 字典表信息
     * @return 结果
     */
    public int insertSysDict(SysDict sysDict);

    /**
     * 修改字典表
     *
     * @param sysDict 字典表信息
     * @return 结果
     */
    public int updateSysDict(SysDict sysDict);

    /**
     * 删除字典表
     *
     * @param rowId 字典表ID
     * @return 结果
     */
    public int deleteSysDictById(String rowId);

    /**
     * 批量删除字典表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysDictByIds(Integer[] rowIds);

}

