package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.mapper.TariffReleaseHeaderMapper;
import com.huazheng.tunny.ocean.api.entity.TariffReleaseHeader;
import com.huazheng.tunny.ocean.service.TariffReleaseHeaderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("tariffReleaseHeaderService")
public class TariffReleaseHeaderServiceImpl extends ServiceImpl<TariffReleaseHeaderMapper, TariffReleaseHeader> implements TariffReleaseHeaderService {

    @Autowired
    private TariffReleaseHeaderMapper tariffReleaseHeaderMapper;

    public TariffReleaseHeaderMapper getTariffReleaseHeaderMapper() {
        return tariffReleaseHeaderMapper;
    }

    public void setTariffReleaseHeaderMapper(TariffReleaseHeaderMapper tariffReleaseHeaderMapper) {
        this.tariffReleaseHeaderMapper = tariffReleaseHeaderMapper;
    }

    /**
     * 查询运价发布主表信息
     *
     * @param rowId 运价发布主表ID
     * @return 运价发布主表信息
     */
    @Override
    public TariffReleaseHeader selectTariffReleaseHeaderById(String rowId)
    {
        return tariffReleaseHeaderMapper.selectTariffReleaseHeaderById(rowId);
    }

    /**
     * 查询运价发布主表列表
     *
     * @param tariffReleaseHeader 运价发布主表信息
     * @return 运价发布主表集合
     */
    @Override
    public List<TariffReleaseHeader> selectTariffReleaseHeaderList(TariffReleaseHeader tariffReleaseHeader)
    {
        return tariffReleaseHeaderMapper.selectTariffReleaseHeaderList(tariffReleaseHeader);
    }


    /**
     * 分页模糊查询运价发布主表列表
     * @return 运价发布主表集合
     */
    @Override
    public Page selectTariffReleaseHeaderListByLike(Query query)
    {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.status,x.add_time");
            query.setAsc(Boolean.FALSE);
        }
        TariffReleaseHeader tariffReleaseHeader =  BeanUtil.mapToBean(query.getCondition(), TariffReleaseHeader.class,false);
        if(StrUtil.isEmpty(tariffReleaseHeader.getResveredField01())){
            tariffReleaseHeader.setResveredField01(SecurityUtils.getUserInfo().getSupPlatformCode());
        }
        List<TariffReleaseHeader> tariffReleaseHeaders = tariffReleaseHeaderMapper.selectTariffReleaseHeaderListByLike(query, tariffReleaseHeader);
        query.setRecords(tariffReleaseHeaders);
        return query;
    }

    /**
     * 新增运价发布主表
     *
     * @param tariffReleaseHeader 运价发布主表信息
     * @return 结果
     */
    @Override
    public int insertTariffReleaseHeader(TariffReleaseHeader tariffReleaseHeader)
    {
        return tariffReleaseHeaderMapper.insertTariffReleaseHeader(tariffReleaseHeader);
    }

    /**
     * 修改运价发布主表
     *
     * @param tariffReleaseHeader 运价发布主表信息
     * @return 结果
     */
    @Override
    public int updateTariffReleaseHeader(TariffReleaseHeader tariffReleaseHeader)
    {
        return tariffReleaseHeaderMapper.updateTariffReleaseHeader(tariffReleaseHeader);
    }

    @Override
    public int updateTariffReleaseHeaderByNo(TariffReleaseHeader tariffReleaseHeader)
    {
        return tariffReleaseHeaderMapper.updateTariffReleaseHeaderByNo(tariffReleaseHeader);
    }


    /**
     * 删除运价发布主表
     *
     * @param tariffReleaseHeader 运价发布主表ID
     * @return 结果
     */
    public int deleteTariffReleaseHeaderById(TariffReleaseHeader tariffReleaseHeader)
    {
        return tariffReleaseHeaderMapper.deleteTariffReleaseHeaderById( tariffReleaseHeader);
    };


    /**
     * 批量删除运价发布主表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteTariffReleaseHeaderByIds(Integer[] rowIds)
    {
        return tariffReleaseHeaderMapper.deleteTariffReleaseHeaderByIds( rowIds);
    }

}
