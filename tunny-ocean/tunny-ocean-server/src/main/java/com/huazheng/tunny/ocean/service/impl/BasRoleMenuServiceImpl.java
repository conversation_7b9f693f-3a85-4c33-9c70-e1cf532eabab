package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.BasRoleMenuMapper;
import com.huazheng.tunny.ocean.api.entity.BasRoleMenu;
import com.huazheng.tunny.ocean.service.BasRoleMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("basRoleMenuService")
public class BasRoleMenuServiceImpl extends ServiceImpl<BasRoleMenuMapper, BasRoleMenu> implements BasRoleMenuService {

    @Autowired
    private BasRoleMenuMapper basRoleMenuMapper;

    public BasRoleMenuMapper getBasRoleMenuMapper() {
        return basRoleMenuMapper;
    }

    public void setBasRoleMenuMapper(BasRoleMenuMapper basRoleMenuMapper) {
        this.basRoleMenuMapper = basRoleMenuMapper;
    }

    /**
     * 查询角色菜单关联表信息
     *
     * @param rowId 角色菜单关联表ID
     * @return 角色菜单关联表信息
     */
    @Override
    public BasRoleMenu selectBasRoleMenuById(String rowId)
    {
        return basRoleMenuMapper.selectBasRoleMenuById(rowId);
    }

    /**
     * 查询角色菜单关联表列表
     *
     * @param basRoleMenu 角色菜单关联表信息
     * @return 角色菜单关联表集合
     */
    @Override
    public List<BasRoleMenu> selectBasRoleMenuList(BasRoleMenu basRoleMenu)
    {
        return basRoleMenuMapper.selectBasRoleMenuList(basRoleMenu);
    }


    /**
     * 分页模糊查询角色菜单关联表列表
     * @return 角色菜单关联表集合
     */
    @Override
    public Page selectBasRoleMenuListByLike(Query query)
    {
        BasRoleMenu basRoleMenu =  BeanUtil.mapToBean(query.getCondition(), BasRoleMenu.class,false);
        query.setRecords(basRoleMenuMapper.selectBasRoleMenuListByLike(query,basRoleMenu));
        return query;
    }

    /**
     * 新增角色菜单关联表
     *
     * @param basRoleMenu 角色菜单关联表信息
     * @return 结果
     */
    @Override
    public int insertBasRoleMenu(BasRoleMenu basRoleMenu)
    {
        return basRoleMenuMapper.insertBasRoleMenu(basRoleMenu);
    }

    /**
     * 修改角色菜单关联表
     *
     * @param basRoleMenu 角色菜单关联表信息
     * @return 结果
     */
    @Override
    public int updateBasRoleMenu(BasRoleMenu basRoleMenu)
    {
        return basRoleMenuMapper.updateBasRoleMenu(basRoleMenu);
    }


    /**
     * 删除角色菜单关联表
     *
     * @param rowId 角色菜单关联表ID
     * @return 结果
     */
    public int deleteBasRoleMenuById(String rowId)
    {
        return basRoleMenuMapper.deleteBasRoleMenuById( rowId);
    };


    /**
     * 批量删除角色菜单关联表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBasRoleMenuByIds(Integer[] rowIds)
    {
        return basRoleMenuMapper.deleteBasRoleMenuByIds( rowIds);
    }

}
