package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.FdBusCostDetailDTO;
import com.huazheng.tunny.ocean.api.entity.SupplierInfo;

import java.util.List;

/**
 * 供应商管理 服务接口层
 *
 * <AUTHOR>
 * @date 2024-07-18 13:19:24
 */
public interface SupplierInfoService extends IService<SupplierInfo> {
    /**
     * 查询供应商管理信息
     *
     * @param id 供应商管理ID
     * @return 供应商管理信息
     */
    public SupplierInfo selectSupplierInfoById(Integer id);

    /**
     * 查询供应商管理列表
     *
     * @param supplierInfo 供应商管理信息
     * @return 供应商管理集合
     */
    public List<SupplierInfo> selectSupplierInfoList(SupplierInfo supplierInfo);

    public List<SupplierInfo> getReceiveList(SupplierInfo supplierInfo);


    /**
     * 分页模糊查询供应商管理列表
     *
     * @return 供应商管理集合
     */
    public Page selectSupplierInfoListByLike(Query query);


    /**
     * 新增供应商管理
     *
     * @param supplierInfo 供应商管理信息
     * @return 结果
     */
    public R insertSupplierInfo(SupplierInfo supplierInfo);

    /**
     * 修改供应商管理
     *
     * @param supplierInfo 供应商管理信息
     * @return 结果
     */
    public R updateSupplierInfo(SupplierInfo supplierInfo);

    public R audit(SupplierInfo supplierInfo);

    /**
     * 删除供应商管理
     *
     * @param id 供应商管理ID
     * @return 结果
     */
    public R deleteSupplierInfoById(Integer id);

    /**
     * 批量删除供应商管理
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSupplierInfoByIds(Integer[] ids);

    public Page supplierInfoForm(Query query);

    public Page supplierInfoFormSecond(Query query);

    public List<FdBusCostDetailDTO> supplierInfoFormThird(FdBusCostDetailDTO fdBusCostDetailDTO);
}

