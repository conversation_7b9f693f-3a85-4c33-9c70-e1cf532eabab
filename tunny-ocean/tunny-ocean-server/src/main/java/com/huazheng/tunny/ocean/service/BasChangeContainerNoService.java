package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.BasChangeContainerNo;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 箱号变更管理 服务接口层
 *
 * <AUTHOR>
 * @date 2024-07-10 10:02:46
 */
public interface BasChangeContainerNoService extends IService<BasChangeContainerNo> {
    /**
     * 查询箱号变更管理信息
     *
     * @param id 箱号变更管理ID
     * @return 箱号变更管理信息
     */
    public BasChangeContainerNo selectBasChangeContainerNoById(Integer id);

    /**
     * 查询箱号变更管理列表
     *
     * @param basChangeContainerNo 箱号变更管理信息
     * @return 箱号变更管理集合
     */
    public List<BasChangeContainerNo> selectBasChangeContainerNoList(BasChangeContainerNo basChangeContainerNo);


    /**
     * 分页模糊查询箱号变更管理列表
     * @return 箱号变更管理集合
     */
    public Page selectBasChangeContainerNoListByLike(Query query);



    /**
     * 新增箱号变更管理
     *
     * @param basChangeContainerNo 箱号变更管理信息
     * @return 结果
     */
    public int insertBasChangeContainerNo(BasChangeContainerNo basChangeContainerNo);

    /**
     * 修改箱号变更管理
     *
     * @param basChangeContainerNo 箱号变更管理信息
     * @return 结果
     */
    public int updateBasChangeContainerNo(BasChangeContainerNo basChangeContainerNo);

    /**
     * 删除箱号变更管理
     *
     * @param id 箱号变更管理ID
     * @return 结果
     */
    public int deleteBasChangeContainerNoById(Integer id);

    /**
     * 批量删除箱号变更管理
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteBasChangeContainerNoByIds(Integer[] ids);

    void exportTemplate(HttpServletResponse response)throws IOException;

    R importedTemplate(MultipartFile file) throws Exception;
}

