package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.api.entity.EfFinancingGoods;
import com.huazheng.tunny.ocean.mapper.EfFinancingGoodsMapper;
import com.huazheng.tunny.ocean.service.EfFinancingGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("efFinancingGoodsService")
public class EfFinancingGoodsServiceImpl extends ServiceImpl<EfFinancingGoodsMapper, EfFinancingGoods> implements EfFinancingGoodsService {

    @Autowired
    private EfFinancingGoodsMapper efFinancingGoodsMapper;

    public EfFinancingGoodsMapper getEfFinancingGoodsMapper() {
        return efFinancingGoodsMapper;
    }

    public void setEfFinancingGoodsMapper(EfFinancingGoodsMapper efFinancingGoodsMapper) {
        this.efFinancingGoodsMapper = efFinancingGoodsMapper;
    }

    /**
     * 查询仓单融资货物表信息
     *
     * @param rowId 仓单融资货物表ID
     * @return 仓单融资货物表信息
     */
    @Override
    public EfFinancingGoods selectEfFinancingGoodsById(String rowId)
    {
        return efFinancingGoodsMapper.selectEfFinancingGoodsById(rowId);
    }

    /**
     * 查询仓单融资货物表列表
     *
     * @param efFinancingGoods 仓单融资货物表信息
     * @return 仓单融资货物表集合
     */
    @Override
    public List<EfFinancingGoods> selectEfFinancingGoodsList(EfFinancingGoods efFinancingGoods)
    {
        return efFinancingGoodsMapper.selectEfFinancingGoodsList(efFinancingGoods);
    }


    /**
     * 分页模糊查询仓单融资货物表列表
     * @return 仓单融资货物表集合
     */
    @Override
    public Page selectEfFinancingGoodsListByLike(Query query)
    {
        EfFinancingGoods efFinancingGoods =  BeanUtil.mapToBean(query.getCondition(), EfFinancingGoods.class,false);
        query.setRecords(efFinancingGoodsMapper.selectEfFinancingGoodsListByLike(query,efFinancingGoods));
        return query;
    }

    /**
     * 新增仓单融资货物表
     *
     * @param efFinancingGoods 仓单融资货物表信息
     * @return 结果
     */
    @Override
    public int insertEfFinancingGoods(EfFinancingGoods efFinancingGoods)
    {
        return efFinancingGoodsMapper.insertEfFinancingGoods(efFinancingGoods);
    }

    /**
     * 修改仓单融资货物表
     *
     * @param efFinancingGoods 仓单融资货物表信息
     * @return 结果
     */
    @Override
    public int updateEfFinancingGoods(EfFinancingGoods efFinancingGoods)
    {
        return efFinancingGoodsMapper.updateEfFinancingGoods(efFinancingGoods);
    }


    /**
     * 删除仓单融资货物表
     *
     * @param rowId 仓单融资货物表ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingGoodsById(String rowId)
    {
        return efFinancingGoodsMapper.deleteEfFinancingGoodsById( rowId);
    };


    /**
     * 批量删除仓单融资货物表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingGoodsByIds(Integer[] rowIds)
    {
        return efFinancingGoodsMapper.deleteEfFinancingGoodsByIds( rowIds);
    }

}
