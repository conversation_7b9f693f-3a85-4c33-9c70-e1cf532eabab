package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.AddCostByBillDTO;
import com.huazheng.tunny.ocean.api.dto.ProvinceAddBillDTO;
import com.huazheng.tunny.ocean.api.entity.BillPayProvince;
import com.huazheng.tunny.ocean.api.entity.FdShippingAccoundetail;

import java.util.List;
import java.util.Map;

/**
 * 应付账单（省平台） 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-06-13 15:34:09
 */
public interface BillPayProvinceService extends IService<BillPayProvince> {
    /**
     * 查询应付账单（省平台）信息
     *
     * @param id 应付账单（省平台）ID
     * @return 应付账单（省平台）信息
     */
    public R selectBillPayProvinceById(Map<String, Object> params);

    /**
     * 查询应付账单（省平台）列表
     *
     * @param billPayProvince 应付账单（省平台）信息
     * @return 应付账单（省平台）集合
     */
    public List<BillPayProvince> selectBillPayProvinceList(BillPayProvince billPayProvince);


    /**
     * 分页模糊查询应付账单（省平台）列表
     * @return 应付账单（省平台）集合
     */
    public Page selectBillPayProvinceListByLike(Query query);



    /**
     * 新增应付账单（省平台）
     *
     * @param billPayProvince 应付账单（省平台）信息
     * @return 结果
     */
    public int insertBillPayProvince(BillPayProvince billPayProvince);

    /**
     * 修改应付账单（省平台）
     *
     * @param billPayProvince 应付账单（省平台）信息
     * @return 结果
     */
    public int updateBillPayProvince(BillPayProvince billPayProvince);

    /**
     * 删除应付账单（省平台）
     *
     * @param id 应付账单（省平台）ID
     * @return 结果
     */
    public int deleteBillPayProvinceById(Integer id);

    Map<String, Object> selectCostInfoByBillId(Query query);

    /**
     * 批量删除应付账单（省平台）
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteBillPayProvinceByIds(Integer[] ids);

    R addCostByBill(AddCostByBillDTO addCostByBillDTO);

    List<FdShippingAccoundetail> selectAccoundetailList(Integer id, Integer pageType);

    R selectAccountByDL(Map<String, Object> params);

    R selectAccountByDLTz(Map<String, Object> params);


    R selectAllCostByDL(Map<String, Object> params);

    /**
     * 追加子帐单
     * @param addCostByBillDTO
     * @return
     */
    R addSubBill(AddCostByBillDTO addCostByBillDTO);

    /**
     * 新增账单
     * @param addBillDTOS
     * @return
     */
    R addBill(ProvinceAddBillDTO provinceAddBillDTO);


}

