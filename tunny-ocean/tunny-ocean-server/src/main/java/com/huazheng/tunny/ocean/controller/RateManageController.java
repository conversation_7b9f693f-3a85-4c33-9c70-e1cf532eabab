package com.huazheng.tunny.ocean.controller;


import cn.hutool.core.collection.CollUtil;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.RateManage;
import com.huazheng.tunny.ocean.service.RateManageService;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 汇率设置表（省平台维护）
 *
 * <AUTHOR> code ocean
 * @date 2021-08-09 13:56:48
 */
@RestController
@RequestMapping("/ratemanage")
@Slf4j
public class RateManageController {
    @Autowired
    private RateManageService rateManageService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  rateManageService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return rateManageService.selectRateManageListByLike(new Query<>(params));
    }


    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        RateManage rateManage =rateManageService.selectById(rowId);
        return new R<>(rateManage);
    }

    /**
     * 保存
     * @param rateManage
     * @return R
     */
    @PostMapping("/save")
    @Transactional(rollbackFor = Exception.class)
    public R save(@RequestBody RateManage rateManage) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        RateManage rateManage1 = new RateManage();
        rateManage1.setMonetaryValue(rateManage.getMonetaryValue());
        rateManage1.setApplicableMonth(rateManage.getApplicableMonth());
        rateManage1.setDeleteFlag("N");
//        rateManage1.setExchangeRate(rateManage.getExchangeRate());
        List<RateManage> rateManages = rateManageService.selectRateManageList(rateManage1);
        if (CollUtil.isNotEmpty(rateManages)){
            return new R<>(500,Boolean.FALSE,"该年度同一月份不可以进行同种货币的新增");
        }
        rateManage.setRowId(UUID.randomUUID().toString());
        rateManage.setProvinceCode(userInfo.getUserName());
        rateManage.setProvinceName(userInfo.getRealName());
        rateManage.setAddWho(userInfo.getUserName());
        rateManage.setAddWhoName(userInfo.getRealName());
        rateManage.setAddTime(new Date());
        rateManage.setDeleteFlag("N");
        rateManageService.insertRateManage(rateManage);
        return new R<>(200,Boolean.TRUE);
    }

    /**
     * 修改
     * @param rateManage
     * @return R
     */
    @PostMapping("/update")
    @Transactional(rollbackFor = Exception.class)
    public R update(@RequestBody RateManage rateManage) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        rateManage.setUpdateWho(userInfo.getUserName());
        rateManage.setUpdateWhoName(userInfo.getRealName());
        rateManage.setUpdateTime(new Date());
        rateManageService.updateRateManage(rateManage);
        return new R<>(200,Boolean.TRUE);
    }



    /**
     * 删除
     * @param rateManage
     * @return R
     */
    @GetMapping("/delete")
    @Transactional(rollbackFor = Exception.class)
    public R delete(RateManage rateManage) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        rateManage.setDeleteWho(userInfo.getUserName());
        rateManage.setDeleteWhoName(userInfo.getRealName());
        rateManage.setDeleteTime(new Date());
        rateManage.setDeleteFlag("Y");
        rateManageService.deleteRateManageById(rateManage);
        return new R<>(200,Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        rateManageService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<RateManage> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = rateManageService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<RateManage> list = reader.readAll(RateManage.class);
        rateManageService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }
}
