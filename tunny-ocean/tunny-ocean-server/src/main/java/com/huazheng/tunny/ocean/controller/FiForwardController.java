package com.huazheng.tunny.ocean.controller;

import cn.hutool.json.JSONObject;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 中钞请求公共入口
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:30:28
 */
@RestController
@RefreshScope
public class FiForwardController {
    @Autowired
    private FiBankAuthApplyController fiBankAuthApplyController;
    @Autowired
    private FiEnterpriseAuthApplyController fiEnterpriseAuthApplyController;
    @Autowired
    private FiBookingInformationController fiBookingInformationController;
    @Autowired
    private FiBookingFinancingController fiBookingFinancingController;
    @Autowired
    private FiHisBookingInformationController fiHisBookingInformationController;
    @Autowired
    private FiLoanRecordNewController fiLoanRecordNewController;
    @Autowired
    private FiWhiteListController fiWhiteListController;
    @Autowired
    private FiExporfinancingController fiExporfinancingController;
    @Autowired
    private EfWarehouseController efWarehouseController;
    @Autowired
    private EfWarehouseApplyController efWarehouseApplyController;
    @Autowired
    private EfWarehouseMarginController efWarehouseMarginController;
    @Autowired
    private EfSupplyApplyInfoController efSupplyApplyInfoController;
    @Autowired
    private EfReleaseInfoController efReleaseInfoController;
    @Autowired
    private EfDisposeIntentionController efDisposeIntentionController;

    //经营信息授权-银行申请表保存
    private static String str1 = "/fibankauthapply";
    //企业授权处理
    private static String str2 = "/fienterpriseauthapply/audit";
    //获取订舱单信息
    private static String str3 = "/fibookinginformation/getFiBookingInformationDetail";
    //舱单融资情况同步
    private static String str4 = "/fibookingfinancing/updateFiBookingFinancingInfo";
    //给中超的统计的接口
    private static String str5 = "/fihisbookinginformation/selectEnterpriseLicense";
    //给中超的新增修改的接口
    private static String str6 = "/filoanrecordnew/addorUpdateFiloanrecordnew";
    //同步白名单数据
    private static String str7 = "/fiwhitelist/updateFiWhiteListStatus";
    //"进出口融资情况同步
    private static String str8 = "/fiwhitelist/updateFiWhiteListStatus";
    //e融同步仓单表
    private static String str9 = "/v1/ent/warehouse/syncAsset";
    //保证金同步接口
    private static String str10 = "/v1/safe/warehouse/syncMargin";
    //预警估值同步
    private static String str11 = "/v1/ent/warehouse/syncWarnPrice";
    //补充质押通知同步
    private static String str12 = "/v1/ent/warehouse/syncSupplyApplyInfo";
    //补充质押确认同步接口
    private static String str13 = "/v1/ent/warehouse/syncSupplyConfirmInfo";
    //解质押同步接口
    private static String str14 = "/v1/ent/warehouse/syncReleaseInfo";
    //发布处置意向同步接口
    private static String str15 = "/v1/ent/warehouse/publishDisposeIntention";
    @SneakyThrows
    @RequestMapping("/")
    public String forward(@RequestBody JSONObject jsonObject){
        String method = String.valueOf(jsonObject.get("method"));
        String result = null;
        if(str1.equals(method)){
            //经营信息授权-银行申请表保存
            result = fiBankAuthApplyController.save(jsonObject);
        }else if(str2.equals(method)){
            //企业授权处理
            result = fiEnterpriseAuthApplyController.audit(jsonObject);
        }else if(str3.equals(method)){
            //获取订舱单信息
            result = fiBookingInformationController.getFiBookingInformationDetail(jsonObject);
        }else if(str4.equals(method)){
            //舱单融资情况同步
            result = fiBookingFinancingController.updateFiBookingFinancingInfo(jsonObject);
        }else if(str5.equals(method)){
            //给中超的统计的接口
            result =fiHisBookingInformationController.selectEnterpriseLicense(jsonObject);
        }else if(str6.equals(method)){
            //给中超的新增修改的接口
            result=fiLoanRecordNewController.addorUpdateFiloanrecordnew(jsonObject);
        }else if(str7.equals(method)){
            //同步白名单数据
            result=fiWhiteListController.updateFiWhiteListStatus(jsonObject);
        }else if(str8.equals(method)){
            //"进出口融资情况同步
            result=fiExporfinancingController.updateFiExporFinancing(jsonObject);
        }else if(str9.equals(method)){
            //e融同步仓单表
            result=efWarehouseApplyController.syncAsset(jsonObject);
        }else if(str10.equals(method)){
            //保证金同步接口
            result=efWarehouseMarginController.syncMargin(jsonObject);
        }else if(str11.equals(method)){
            //预警估值同步
            result=efWarehouseApplyController.syncWarnPrice(jsonObject);
        }else if(str12.equals(method)){
            //补充质押通知同步
            result=efSupplyApplyInfoController.syncSupplyApplyInfo(jsonObject);
        }else if(str13.equals(method)){
            //补充质押确认同步接口
            result=efSupplyApplyInfoController.syncSupplyConfirmInfo(jsonObject);
        }else if(str14.equals(method)){
            //解质押同步接口
            result=efReleaseInfoController.syncReleaseInfo(jsonObject);
        }else if(str15.equals(method)){
            //发布处置意向同步接口
            result=efDisposeIntentionController.publishDisposeIntention(jsonObject);
        }
        return result;
    }

    public FiBankAuthApplyController getFiBankAuthApplyController() {
        return fiBankAuthApplyController;
    }

    public void setFiBankAuthApplyController(FiBankAuthApplyController fiBankAuthApplyController) {
        this.fiBankAuthApplyController = fiBankAuthApplyController;
    }

    public FiEnterpriseAuthApplyController getFiEnterpriseAuthApplyController() {
        return fiEnterpriseAuthApplyController;
    }

    public void setFiEnterpriseAuthApplyController(FiEnterpriseAuthApplyController fiEnterpriseAuthApplyController) {
        this.fiEnterpriseAuthApplyController = fiEnterpriseAuthApplyController;
    }

    public FiBookingInformationController getFiBookingInformationController() {
        return fiBookingInformationController;
    }

    public void setFiBookingInformationController(FiBookingInformationController fiBookingInformationController) {
        this.fiBookingInformationController = fiBookingInformationController;
    }

    public FiBookingFinancingController getFiBookingFinancingController() {
        return fiBookingFinancingController;
    }

    public void setFiBookingFinancingController(FiBookingFinancingController fiBookingFinancingController) {
        this.fiBookingFinancingController = fiBookingFinancingController;
    }

    public FiHisBookingInformationController getFiHisBookingInformationController() {
        return fiHisBookingInformationController;
    }

    public void setFiHisBookingInformationController(FiHisBookingInformationController fiHisBookingInformationController) {
        this.fiHisBookingInformationController = fiHisBookingInformationController;
    }

    public FiLoanRecordNewController getFiLoanRecordNewController() {
        return fiLoanRecordNewController;
    }

    public void setFiLoanRecordNewController(FiLoanRecordNewController fiLoanRecordNewController) {
        this.fiLoanRecordNewController = fiLoanRecordNewController;
    }

    public FiWhiteListController getFiWhiteListController() {
        return fiWhiteListController;
    }

    public void setFiWhiteListController(FiWhiteListController fiWhiteListController) {
        this.fiWhiteListController = fiWhiteListController;
    }

    public FiExporfinancingController getFiExporfinancingController() {
        return fiExporfinancingController;
    }

    public void setFiExporfinancingController(FiExporfinancingController fiExporfinancingController) {
        this.fiExporfinancingController = fiExporfinancingController;
    }

    public EfWarehouseController getEfWarehouseController() {
        return efWarehouseController;
    }

    public void setEfWarehouseController(EfWarehouseController efWarehouseController) {
        this.efWarehouseController = efWarehouseController;
    }

    public EfWarehouseApplyController getEfWarehouseApplyController() {
        return efWarehouseApplyController;
    }

    public void setEfWarehouseApplyController(EfWarehouseApplyController efWarehouseApplyController) {
        this.efWarehouseApplyController = efWarehouseApplyController;
    }

    public EfWarehouseMarginController getEfWarehouseMarginController() {
        return efWarehouseMarginController;
    }

    public void setEfWarehouseMarginController(EfWarehouseMarginController efWarehouseMarginController) {
        this.efWarehouseMarginController = efWarehouseMarginController;
    }

    public EfSupplyApplyInfoController getEfSupplyApplyInfoController() {
        return efSupplyApplyInfoController;
    }

    public void setEfSupplyApplyInfoController(EfSupplyApplyInfoController efSupplyApplyInfoController) {
        this.efSupplyApplyInfoController = efSupplyApplyInfoController;
    }

    public EfReleaseInfoController getEfReleaseInfoController() {
        return efReleaseInfoController;
    }

    public void setEfReleaseInfoController(EfReleaseInfoController efReleaseInfoController) {
        this.efReleaseInfoController = efReleaseInfoController;
    }

    public EfDisposeIntentionController getEfDisposeIntentionController() {
        return efDisposeIntentionController;
    }

    public void setEfDisposeIntentionController(EfDisposeIntentionController efDisposeIntentionController) {
        this.efDisposeIntentionController = efDisposeIntentionController;
    }
}
