package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.EfFutruesPrice;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseApply;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseList;
import com.huazheng.tunny.ocean.controller.SignatureController;
import com.huazheng.tunny.ocean.mapper.EfFutruesInfoMapper;
import com.huazheng.tunny.ocean.api.entity.EfFutruesInfo;
import com.huazheng.tunny.ocean.mapper.EfFutruesPriceMapper;
import com.huazheng.tunny.ocean.service.EfFutruesInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service("efFutruesInfoService")
public class EfFutruesInfoServiceImpl extends ServiceImpl<EfFutruesInfoMapper, EfFutruesInfo> implements EfFutruesInfoService {

    @Autowired
    private EfFutruesInfoMapper efFutruesInfoMapper;
    @Autowired
    private EfFutruesPriceMapper efFutruesPriceMapper;
    @Autowired
    private SignatureController signatureController;

    /**
     * 查询仓单期货行情信息
     *
     * @param rowId 仓单期货行情ID
     * @return 仓单期货行情信息
     */
    @Override
    public EfFutruesInfo selectEfFutruesInfoById(String rowId)
    {
        final EfFutruesInfo efFutruesInfo = efFutruesInfoMapper.selectEfFutruesInfoById(rowId);
        EfFutruesPrice price = new EfFutruesPrice();
        price.setWarehouseSupervisionNo(efFutruesInfo.getWarehouseSupervisionNo());
        price.setWarehouseCode(efFutruesInfo.getWarehouseCode());
        price.setDeleteFlag("N");
        final List<EfFutruesPrice> efFutruesPrices = efFutruesPriceMapper.selectEfFutruesPriceList(price);
        efFutruesInfo.setFuturesPriceList(efFutruesPrices);
        return efFutruesInfo;
    }

    @Override
    public EfFutruesInfo getDetail(EfFutruesInfo ef)
    {
        final EfFutruesInfo efFutruesInfo = efFutruesInfoMapper.getDetail(ef);
        EfFutruesPrice price = new EfFutruesPrice();
        price.setWarehouseSupervisionNo(ef.getWarehouseSupervisionNo());
        price.setWarehouseCode(ef.getWarehouseCode());
        price.setDeleteFlag("N");
        List<EfFutruesPrice> efFutruesPrices = efFutruesPriceMapper.selectEfFutruesPriceList(price);
        if(CollUtil.isNotEmpty(efFutruesPrices)){
            efFutruesInfo.setFuturesPriceList(efFutruesPrices);
        }
        return efFutruesInfo;
    }

    /**
     * 查询仓单期货行情列表
     *
     * @param efFutruesInfo 仓单期货行情信息
     * @return 仓单期货行情集合
     */
    @Override
    public List<EfFutruesInfo> selectEfFutruesInfoList(EfFutruesInfo efFutruesInfo)
    {
        return efFutruesInfoMapper.selectEfFutruesInfoList(efFutruesInfo);
    }


    /**
     * 分页模糊查询仓单期货行情列表
     * @return 仓单期货行情集合
     */
    @Override
    public Page selectEfFutruesInfoListByLike(Query query)
    {
        EfFutruesInfo efFutruesInfo =  BeanUtil.mapToBean(query.getCondition(), EfFutruesInfo.class,false);
        query.setRecords(efFutruesInfoMapper.selectEfFutruesInfoListByLike(query,efFutruesInfo));
        return query;
    }

    /**
     * 新增仓单期货行情
     *
     * @param efFutruesInfo 仓单期货行情信息
     * @return 结果
     */
    @Override
    public int insertEfFutruesInfo(EfFutruesInfo efFutruesInfo)
    {
        return efFutruesInfoMapper.insertEfFutruesInfo(efFutruesInfo);
    }

    /**
     * 修改仓单期货行情
     *
     * @param efFutruesInfo 仓单期货行情信息
     * @return 结果
     */
    @Override
    public int updateEfFutruesInfo(EfFutruesInfo efFutruesInfo)
    {
        return efFutruesInfoMapper.updateEfFutruesInfo(efFutruesInfo);
    }


    /**
     * 删除仓单期货行情
     *
     * @param rowId 仓单期货行情ID
     * @return 结果
     */
    public int deleteEfFutruesInfoById(String rowId)
    {
        return efFutruesInfoMapper.deleteEfFutruesInfoById( rowId);
    };


    /**
     * 批量删除仓单期货行情对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfFutruesInfoByIds(Integer[] rowIds)
    {
        return efFutruesInfoMapper.deleteEfFutruesInfoByIds( rowIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String syncFutruesInfo(EfFutruesInfo efFutruesInfo)
    {
        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(efFutruesInfo!=null) {
            final List<EfFutruesPrice> futuresPriceList = efFutruesInfo.getFuturesPriceList();
            if(CollUtil.isNotEmpty(futuresPriceList)){
                //调用中钞接口，插入申请数据,解析返回数据
                String json = JSONUtil.parseObj(efFutruesInfo, true).toStringPretty();
                final String result = signatureController.doPost("/v1/safe/warehouse/syncFutruesInfo", json);

                JSONObject resultObject = JSONUtil.parseObj(result);

                flag = Boolean.valueOf(String.valueOf(resultObject.get("b")));
                msg = String.valueOf(resultObject.get("msg"));
                if(flag){
                    from = "中钞平台:";

                    for (EfFutruesPrice efFutruesPrice:futuresPriceList
                         ) {
                        efFutruesPrice.setRowId(UUID.randomUUID().toString());
                        efFutruesPrice.setWarehouseCode(efFutruesInfo.getWarehouseCode());
                        efFutruesPrice.setWarehouseSupervisionNo(efFutruesInfo.getWarehouseSupervisionNo());
                        efFutruesPrice.setAddTime(LocalDateTime.now());
                        efFutruesPrice.setAddWho("ER");
                        efFutruesPrice.setAddWhoName("E融");
                        efFutruesPriceMapper.insertEfFutruesPrice(efFutruesPrice);
                    }
                    efFutruesInfoMapper.deleteByWarehouseCode(efFutruesInfo);

                    efFutruesInfo.setRowId(UUID.randomUUID().toString());
                    efFutruesInfo.setAddTime(LocalDateTime.now());
                    efFutruesInfo.setAddWho("ER");
                    efFutruesInfo.setAddWhoName("E融");
                    efFutruesInfoMapper.insertEfFutruesInfo(efFutruesInfo);

                    int code = 500;
                    if(flag){
                        code = 0;
                    }
                    return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
                }else{
                    from = "中钞平台:";
                    int code = 500;
                    if(flag){
                        code = 0;
                    }
                    return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
                }

            }else{
                msg = "没有接受到仓单期货价格数据";
                flag = false;
            }
        }else{
            msg = "没有接受到仓单期货行情数据";
            flag = false;
        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }

}
