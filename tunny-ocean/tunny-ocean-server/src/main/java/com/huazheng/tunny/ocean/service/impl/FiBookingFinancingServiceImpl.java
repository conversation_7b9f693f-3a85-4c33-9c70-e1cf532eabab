package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.util.StringUtils;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.CustomerPlatformInfo;
import com.huazheng.tunny.ocean.api.entity.FiBookingInformation;
import com.huazheng.tunny.ocean.mapper.FiBookingFinancingMapper;
import com.huazheng.tunny.ocean.api.entity.FiBookingFinancing;
import com.huazheng.tunny.ocean.service.CustomerPlatformInfoService;
import com.huazheng.tunny.ocean.service.FiBookingFinancingService;
import com.huazheng.tunny.ocean.service.FiBookingInformationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("fiBookingFinancingService")
public class FiBookingFinancingServiceImpl extends ServiceImpl<FiBookingFinancingMapper, FiBookingFinancing> implements FiBookingFinancingService {

    @Autowired
    private FiBookingFinancingMapper fiBookingFinancingMapper;

    @Autowired
    private FiBookingInformationService fiBookingInformationService;

    @Autowired
    private CustomerPlatformInfoService customerPlatformInfoService;

    /**
     * 查询订舱单融资信息
     *
     * @param rowId 订舱单融资ID
     * @return 订舱单融资信息
     */
    @Override
    public FiBookingFinancing selectFiBookingFinancingById(String rowId)
    {
        return fiBookingFinancingMapper.selectFiBookingFinancingById(rowId);
    }

    @Override
    public FiBookingFinancing selectFiBookingFinancingByAssetCode(String assetCode)
    {
        return fiBookingFinancingMapper.selectFiBookingFinancingByAssetCode(assetCode);
    }

    /**
     * 查询订舱单融资列表
     *
     * @param fiBookingFinancing 订舱单融资信息
     * @return 订舱单融资集合
     */
    @Override
    public List<FiBookingFinancing> selectFiBookingFinancingList(FiBookingFinancing fiBookingFinancing)
    {
        return fiBookingFinancingMapper.selectFiBookingFinancingList(fiBookingFinancing);
    }

    @Override
    public List<FiBookingFinancing> selectFiBookingFinancingExportList(FiBookingFinancing fiBookingFinancing)
    {
        if(fiBookingFinancing.getPlatformLevel().equals("0")){
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            CustomerPlatformInfo customerPlatformInfo=new CustomerPlatformInfo();
            customerPlatformInfo.setPlatformCode(userInfo.getUserName());
            List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoService.selectCustomerNoByCode(customerPlatformInfo);
            if(customerPlatformInfos.size()>0){
                StringBuilder stringBuilder=new StringBuilder();
                for (CustomerPlatformInfo customerPlatform:customerPlatformInfos) {
                    stringBuilder.append(customerPlatform.getSocialUcCode()).append(",");
                }
                fiBookingFinancing.setSocialCode(stringBuilder.toString());
            }
        }
        return fiBookingFinancingMapper.selectFiBookingFinancingExportList(fiBookingFinancing);
    }


    /**
     * 分页模糊查询订舱单融资列表
     * @return 订舱单融资集合
     */
    @Override
    public Page selectFiBookingFinancingListByLike(Query query)
    {
        FiBookingFinancing fiBookingFinancing =  BeanUtil.mapToBean(query.getCondition(), FiBookingFinancing.class,false);
        fiBookingFinancing.setDeleteFlag("N");
        query.setRecords(fiBookingFinancingMapper.selectFiBookingFinancingListByLike(query,fiBookingFinancing));
        return query;
    }

    @Override
    public Page selectFiBookingFinancingPage(Query query) {
        FiBookingFinancing fiBookingFinancing =  BeanUtil.mapToBean(query.getCondition(), FiBookingFinancing.class,false);
        //市平台走下面逻辑
        if(fiBookingFinancing.getPlatformLevel().equals("0")){
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            CustomerPlatformInfo customerPlatformInfo=new CustomerPlatformInfo();
            customerPlatformInfo.setPlatformCode(userInfo.getUserName());
            List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoService.selectCustomerNoByCode(customerPlatformInfo);
            if(customerPlatformInfos.size()>0){
                StringBuilder stringBuilder=new StringBuilder();
                for (CustomerPlatformInfo customerPlatform:customerPlatformInfos) {
                    stringBuilder.append(customerPlatform.getSocialUcCode()).append(",");
                }
                fiBookingFinancing.setSocialCode(stringBuilder.toString());
            }
        }
        List<FiBookingFinancing> fiBookingFinancings = fiBookingFinancingMapper.selectFiBookingFinancingListByLike(query, fiBookingFinancing);
        if(fiBookingFinancings.size()>0){
            for (FiBookingFinancing financing:fiBookingFinancings) {
                FiBookingInformation fiBookingInformation=new FiBookingInformation();
                fiBookingInformation.setAssetCode(financing.getAssetCode());
                fiBookingInformation.setDeleteFlag("N");
                List<FiBookingInformation> fiBookingInformations = fiBookingInformationService.selectFiBookingInformationList(fiBookingInformation);
                if(fiBookingInformations.size()>0){
                    financing.setBookingNum(String.valueOf(fiBookingInformations.size()));
                    BigDecimal bigAmount=BigDecimal.valueOf(0);
                    for (FiBookingInformation information:fiBookingInformations) {
                        if(null!=information.getOccupiedAmountThis()) {
                            bigAmount = bigAmount.add(information.getOccupiedAmountThis());
                        }
                    }
                    financing.setOccupiedAmountThisTotalAmount(bigAmount);
                }
                financing.setBookingList(fiBookingInformations);
            }
        }
        query.setRecords(fiBookingFinancings);
        return query;
    }

    @Override
    public R selectFiBookingFinancingSum(Map<String, Object> params) {
        R r=new R();
        FiBookingFinancing fiBookingFinancing =  BeanUtil.mapToBean(params, FiBookingFinancing.class,false);
        //市平台走下面逻辑
        if(fiBookingFinancing.getPlatformLevel().equals("0")){
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            CustomerPlatformInfo customerPlatformInfo=new CustomerPlatformInfo();
            if(StringUtils.isBlank(fiBookingFinancing.getPlatformCode())) {
                r.setCode(500);
                r.setB(false);
                r.setMsg("请传入当前登录人的账号");
                return r;
            }
            customerPlatformInfo.setPlatformCode(fiBookingFinancing.getPlatformCode());
            List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoService.selectCustomerNoByCode(customerPlatformInfo);
            if(customerPlatformInfos.size()>0){
                StringBuilder stringBuilder=new StringBuilder();
                for (CustomerPlatformInfo customerPlatform:customerPlatformInfos) {
                    stringBuilder.append(customerPlatform.getSocialUcCode()).append(",");
                }
                fiBookingFinancing.setSocialCode(stringBuilder.toString());
            }
        }
        List<FiBookingFinancing> fiBookingFinancings = fiBookingFinancingMapper.selectFiBookingFinancingGroupByEntCode(fiBookingFinancing);
        //查询还款信息
        fiBookingFinancing.setAssetStatePayAndRepay("PAY,REPAY,UN_REPAY");
        List<FiBookingFinancing> paySum = fiBookingFinancingMapper.selectFiBookingFinancingListBySum(fiBookingFinancing);
        FiBookingFinancing pay = fiBookingFinancingMapper.selectFiBookingFinancingListPayAndRec(fiBookingFinancing);
        //查询未还款信息
        fiBookingFinancing.setAssetStatePayAndRepay("UN_REPAY,PAY");
        List<FiBookingFinancing> rePaySum = fiBookingFinancingMapper.selectFiBookingFinancingListBySum(fiBookingFinancing);
        FiBookingFinancing rec = fiBookingFinancingMapper.selectFiBookingFinancingListPayAndRec(fiBookingFinancing);

        Map<String,Object> map=new HashMap<>();
        map.put("enterprise",fiBookingFinancings.size());
        map.put("paySum",paySum);
        map.put("rePaySum",rePaySum);
        map.put("paySumZm",pay.getPayAmountDollar());
        map.put("recPaySumZm",rec.getPayAmountDollar());
        r.setCode(200);
        r.setB(true);
        r.setData(map);
        return r;
    }

    /**
     * 新增订舱单融资
     *
     * @param fiBookingFinancing 订舱单融资信息
     * @return 结果
     */
    @Override
    public int insertFiBookingFinancing(FiBookingFinancing fiBookingFinancing)
    {
        return fiBookingFinancingMapper.insertFiBookingFinancing(fiBookingFinancing);
    }

    /**
     * 修改订舱单融资
     *
     * @param fiBookingFinancing 订舱单融资信息
     * @return 结果
     */
    @Override
    public int updateFiBookingFinancing(FiBookingFinancing fiBookingFinancing)
    {
        return fiBookingFinancingMapper.updateFiBookingFinancing(fiBookingFinancing);
    }

    /**
     * 修改订舱单融资
     *
     * @param fiBookingFinancing 订舱单融资信息
     * @return 结果
     */
    @Override
    public int updateFiBookingFinancingInfo(FiBookingFinancing fiBookingFinancing)
    {
        return fiBookingFinancingMapper.updateFiBookingFinancingInfo(fiBookingFinancing);
    }


    /**
     * 删除订舱单融资
     *
     * @param rowId 订舱单融资ID
     * @return 结果
     */
    public int deleteFiBookingFinancingById(String rowId)
    {
        return fiBookingFinancingMapper.deleteFiBookingFinancingById( rowId);
    }


    /**
     * 批量删除订舱单融资对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiBookingFinancingByIds(Integer[] rowIds)
    {
        return fiBookingFinancingMapper.deleteFiBookingFinancingByIds( rowIds);
    }

}
