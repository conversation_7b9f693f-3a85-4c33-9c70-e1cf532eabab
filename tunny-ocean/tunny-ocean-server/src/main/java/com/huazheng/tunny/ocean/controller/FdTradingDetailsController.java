package com.huazheng.tunny.ocean.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.FdAggregateBalanceDTO;
import com.huazheng.tunny.ocean.api.entity.FdBalanceDetail;
import com.huazheng.tunny.ocean.api.entity.FdShippingAccount;
import com.huazheng.tunny.ocean.api.entity.FdTradingDetails;
import com.huazheng.tunny.ocean.service.FdBalanceDetailService;
import com.huazheng.tunny.ocean.service.FdShippingAccountService;
import com.huazheng.tunny.ocean.service.FdTradingDetailsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 交易明细表
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:29:45
 */
@RestController
@RequestMapping("/fdtradingdetails")
@Slf4j
public class FdTradingDetailsController {
    @Autowired
    private FdTradingDetailsService fdTradingDetailsService;

    @Autowired
    private FdBalanceDetailService fdBalanceDetailService;
    @Autowired
    private FdShippingAccountService fdShippingAccountService;

    /**
     *  查询客户余额流水列表
     * @param fdTradingDetails
     * @return
     */
    @PostMapping("/getList")
    public R getList(@RequestBody FdTradingDetails fdTradingDetails) {
        //数据库字段值完整查询
        // return  fdTradingDetailsService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        fdTradingDetails.setDelFlag("N");
        List<FdTradingDetails> list = fdTradingDetailsService.getList(fdTradingDetails);
        return new R<>(200,Boolean.TRUE,list);
    }

    /**
     *  查询汇款流水明细列表
     * @param fdTradingDetails
     * @return
     */
    @PostMapping("/getRemittanceList")
    public R getRemittanceList(@RequestBody FdTradingDetails fdTradingDetails) {
        //数据库字段值完整查询
        // return  fdTradingDetailsService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        fdTradingDetails.setDelFlag("N");
        List<FdTradingDetails> list = fdTradingDetailsService.getRemittanceList(fdTradingDetails);
        return new R<>(200,Boolean.TRUE,list);
    }

    /**
     *  查询客户余额一级List
     * @param params
     * @return
     */
    @GetMapping("/aggregateBalancePage")
    public Page selectAggregateBalanceList(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdTradingDetailsService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdTradingDetailsService.selectAggregateBalanceList(new Query<>(params));
    }

    @GetMapping("/aggregateBalancePage2")
    public Page selectAggregateBalanceList2(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdTradingDetailsService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdTradingDetailsService.selectAggregateBalanceList2(new Query<>(params));
    }

    /**
     *  市平台-市在省余额
     * @param params
     * @return
     */
    @GetMapping("/aggregateBalancePage3")
    public Page selectAggregateBalanceList3(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return fdTradingDetailsService.selectAggregateBalanceList3(new Query<>(params));
    }

    @GetMapping("/selectProvinceInRailway")
    public Page selectProvinceInRailway(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdTradingDetailsService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdTradingDetailsService.selectProvinceInRailway(new Query<>(params));
    }

    @GetMapping("/selectCityInProvince")
    public Page selectCityInProvince(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdTradingDetailsService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdTradingDetailsService.selectCityInProvince(new Query<>(params));
    }

    @GetMapping("/selectAggregateBalanceListSub")
    public List<FdAggregateBalanceDTO> selectAggregateBalanceListSub(FdAggregateBalanceDTO fdAggregateBalanceDTO) {
        //数据库字段值完整查询
        // return  fdTradingDetailsService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdTradingDetailsService.selectAggregateBalanceListSub(fdAggregateBalanceDTO);
    }

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdTradingDetailsService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdTradingDetailsService.selectFdTradingDetailsListByLike(new Query<>(params));
    }

    @PostMapping("/insertFdTradingDetailsShi")
    public R insertFdTradingDetailsShi(@RequestBody FdTradingDetails fdTradingDetails) throws Exception {
        R r = new R();
//        try {
//            r= fdTradingDetailsService.insertFdTradingDetailsShi(fdTradingDetails);
//        } catch (Exception e) {
//            r.setCode(500);
//            r.setB(false);
//            r.setMsg("新增失败");
//        }
        return  fdTradingDetailsService.insertFdTradingDetailsShi(fdTradingDetails);
    }

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/list")
    public List list(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdTradingDetailsService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        FdTradingDetails fdTradingDetails =  BeanUtil.mapToBean(params, FdTradingDetails.class,false);
        fdTradingDetails.setDelFlag("N");
        fdTradingDetails.setStauts("1,2");
        List<FdTradingDetails> fdTradingDetails1 = fdTradingDetailsService.selectFdTradingDetailsList(fdTradingDetails);
        return fdTradingDetails1;
    }

    @GetMapping("/list2")
    public List list2(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdTradingDetailsService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        FdTradingDetails fdTradingDetails =  BeanUtil.mapToBean(params, FdTradingDetails.class,false);
        fdTradingDetails.setDelFlag("N");
        fdTradingDetails.setStauts("1,2");
        List<FdTradingDetails> fdTradingDetails1 = fdTradingDetailsService.selectFdTradingDetailsList2(fdTradingDetails);
        return fdTradingDetails1;
    }



    /**
     *  根据客户编码查询余额
     * @param fdTradingDetails
     * @return
     */
    @GetMapping("/selectBalanceByCode")
    public R selectBalanceByCode(FdTradingDetails fdTradingDetails) {
        return fdTradingDetailsService.selectBalanceByCode(fdTradingDetails);
    }


    /**
     * 信息
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Integer id) {
        FdTradingDetails fdTradingDetails =fdTradingDetailsService.selectById(id);
        return new R<>(fdTradingDetails);
    }

    /**
     * 保存
     * @param fdTradingDetails
     * @return R
     */
    @PostMapping("/save")
    public R save(@RequestBody FdTradingDetails fdTradingDetails) {
        R r = new R();
        try {
            FdBalanceDetail fdBalanceDetail = new FdBalanceDetail();
            fdBalanceDetail.setPlatformCode(fdTradingDetails.getPlatformCode());
            fdBalanceDetail.setCustomerCode(fdTradingDetails.getCustomerCode());
            fdBalanceDetail.setCustomerName(fdTradingDetails.getCustomerName());
            fdBalanceDetail.setShiftId(fdTradingDetails.getProvinceTrainsNumber());
            fdBalanceDetail.setShiftNo(fdTradingDetails.getProvinceTrainsNumber());
            fdBalanceDetail.setShiftName(fdTradingDetails.getShiftName());
            fdBalanceDetail.setShUnitCode(fdTradingDetails.getShUnitCode());
            fdBalanceDetail.setShUnitName(fdTradingDetails.getShUnitName());
            fdBalanceDetail.setPaymentType(fdTradingDetails.getPaymentType());
            fdBalanceDetail.setTotalAmount(fdTradingDetails.getTransactionAmount());
            fdBalanceDetail.setRemainingAmount(fdTradingDetails.getTransactionAmount());
            fdBalanceDetail.setPlatformLevel(fdTradingDetails.getPlatformLevel());
            FdShippingAccount account = new FdShippingAccount();
            account.setProvinceShiftNo(fdTradingDetails.getProvinceTrainsNumber());
            List<FdShippingAccount> list = fdShippingAccountService.selectFdShippingAccountList(account);
            if(list!=null && list.size()>0){
                String paymentType = "";
                if(fdTradingDetails.getPaymentType()!=null){
                    if("0".equals(fdTradingDetails.getPaymentType())){
                        paymentType ="业务余额";
                    }else if("1".equals(fdTradingDetails.getPaymentType())){
                        paymentType ="量价捆绑余额";
                    }
                }
                String date = list.get(0).getShippingTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                fdBalanceDetail.setRemarks(fdTradingDetails.getCustomerName() + date + list.get(0).getPortStation() + "，省级班列号：" +
                        list.get(0).getProvinceShiftNo() + "，于" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + "新增" + paymentType
                        + String.valueOf(fdTradingDetails.getTransactionAmount()) + "元。");
            }

            fdBalanceDetailService.insertFdBalanceDetail(fdBalanceDetail);

            fdTradingDetails.setBalanceId(fdBalanceDetail.getId());
            r = fdTradingDetailsService.insertTrandingDetails(fdTradingDetails);
        }catch (Exception e){
            log.warn(e.getMessage());
            StringBuilder msg = new StringBuilder("新增失败");
            if (e.getMessage().contains("请联系系统管理员")){
                msg.append("，" + e.getMessage());
            }
            r.setMsg(msg.toString());
            r.setStatusCode(500);
            r.setB(false);
        }
        return r;
    }

    @PostMapping("/saveSheng")
    public R saveSheng(@RequestBody FdTradingDetails fdTradingDetails) {
        R r = new R();
        try {
            FdBalanceDetail fdBalanceDetail = new FdBalanceDetail();
            fdBalanceDetail.setPlatformCode(fdTradingDetails.getPlatformCode());
            fdBalanceDetail.setCustomerCode(fdTradingDetails.getCustomerCode());
            fdBalanceDetail.setCustomerName(fdTradingDetails.getCustomerName());
            fdBalanceDetail.setShUnitCode(fdTradingDetails.getShUnitCode());
            fdBalanceDetail.setShUnitName(fdTradingDetails.getShUnitName());
            fdBalanceDetail.setPaymentType(fdTradingDetails.getPaymentType());
            fdBalanceDetail.setTotalAmount(fdTradingDetails.getTransactionAmount());
            fdBalanceDetail.setRemainingAmount(fdTradingDetails.getTransactionAmount());
            fdBalanceDetail.setPlatformLevel(fdTradingDetails.getPlatformLevel());
            fdBalanceDetail.setRemarks(fdTradingDetails.getNoteInformation());
            fdBalanceDetailService.insertFdBalanceDetail(fdBalanceDetail);

            fdTradingDetails.setBalanceId(fdBalanceDetail.getId());
            r = fdTradingDetailsService.insertTrandingDetails(fdTradingDetails);
        }catch (Exception e){
            log.warn(e.getMessage());
            StringBuilder msg = new StringBuilder("新增失败");
            if (e.getMessage().contains("请联系系统管理员")){
                msg.append("，" + e.getMessage());
            }
            r.setMsg(msg.toString());
            r.setStatusCode(500);
            r.setB(false);
        }
        return r;
    }

    /**
     * 修改
     * @param fdTradingDetails
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody FdTradingDetails fdTradingDetails) {
        return fdTradingDetailsService.update(fdTradingDetails);
    }

    @PostMapping("/updateNew")
    public R updateNew(@RequestBody FdTradingDetails fdTradingDetails) {
        return fdTradingDetailsService.updateNew(fdTradingDetails);
    }

    /**
     * 修改中铁多联
     * @param fdTradingDetails
     * @return R
     */

    @PostMapping("/updateFdTradingDetailsById")
    public R updateFdTradingDetailsById(@RequestBody FdTradingDetails fdTradingDetails) {
        return fdTradingDetailsService.updateFdTradingDetailsById(fdTradingDetails);
    }



    /**
     * 删除
     * @param id
     * @return R
     */
    @DeleteMapping("/{id}")
    public R delete(@PathVariable  Integer id) {
        fdTradingDetailsService.deleteById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<Integer> ids) {
        fdTradingDetailsService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<FdTradingDetails> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = fdTradingDetailsService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<FdTradingDetails> list = reader.readAll(FdTradingDetails.class);
        fdTradingDetailsService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }
}
