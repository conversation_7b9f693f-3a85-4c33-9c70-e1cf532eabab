package com.huazheng.tunny.ocean.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.BalanceDetailDTO;
import com.huazheng.tunny.ocean.api.entity.FdBalanceDetail;
import com.huazheng.tunny.ocean.api.entity.FdBill;
import com.huazheng.tunny.ocean.api.entity.FdCosdetail;
import com.huazheng.tunny.ocean.api.entity.FdTradingDetails;
import com.huazheng.tunny.ocean.api.vo.BillBalanceJsListVO;
import com.huazheng.tunny.ocean.api.vo.BillBalanceLjkbListVO;
import com.huazheng.tunny.ocean.api.vo.BillBalanceSkListVO;
import com.huazheng.tunny.ocean.mapper.FdBalanceDetailMapper;
import com.huazheng.tunny.ocean.mapper.FdBillMapper;
import com.huazheng.tunny.ocean.mapper.FdCosdetailMapper;
import com.huazheng.tunny.ocean.mapper.FdTradingDetailsMapper;
import com.huazheng.tunny.ocean.service.FdBalanceDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service("fddBalanceDetailService")
public class FdBalanceDetailServiceImpl extends ServiceImpl<FdBalanceDetailMapper, FdBalanceDetail> implements FdBalanceDetailService {

    @Autowired
    private FdBalanceDetailMapper fdBalanceDetailMapper;

    @Autowired
    private FdCosdetailMapper fdCosdetailMapper;

    @Autowired
    private FdBillMapper fdBillMapper;
    @Autowired
    private FdTradingDetailsMapper fdTradingDetailsMapper;

    @Value("${db.database}")
    private String database;

    /**
     * 分页模糊查询子账单表列表
     *
     * @return 子账单表集合
     */
    @Override
    public Page selectFdBalanceDetailByLike(Query query) {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.add_time");
            query.setAsc(Boolean.FALSE);
        }
        FdBalanceDetail fdBalanceDetail = BeanUtil.mapToBean(query.getCondition(), FdBalanceDetail.class, false);
        query.setRecords(fdBalanceDetailMapper.selectFdBalanceDetailByLike(query, fdBalanceDetail));
        return query;
    }

    @Override
    public R pageTotal(Query query) {
        FdBalanceDetail fdBalanceDetail = BeanUtil.mapToBean(query.getCondition(), FdBalanceDetail.class, false);
        final FdBalanceDetail total = fdBalanceDetailMapper.pageTotal(fdBalanceDetail);
        return new R<>(200, Boolean.TRUE, total, null);
    }

    @Override
    public List<FdBalanceDetail> selectFdBalanceDetailList(FdBalanceDetail fdBalanceDetail) {
        return fdBalanceDetailMapper.selectFdBalanceDetailList(fdBalanceDetail);
    }

    @Override
    public R insertFdBalanceDetail(FdBalanceDetail fdBalanceDetail) {


        fdBalanceDetailMapper.insertFdBalanceDetail(fdBalanceDetail);
        return R.success();
    }

    @Override
    public List<FdBalanceDetail> selectRemarksList(FdBalanceDetail fdBalanceDetail) {
        return fdBalanceDetailMapper.selectRemarksList(fdBalanceDetail);
    }

    @Override
    public List<FdBalanceDetail> getAccountList(Query query) {
        FdBill fdBill = BeanUtil.mapToBean(query.getCondition(), FdBill.class, false);
        List<FdBill> fdBills = fdBillMapper.selectFdBillListByLike(query, fdBill);
        FdCosdetail fdCosdetail = new FdCosdetail();
        fdCosdetail.setBillCode(fdBills.get(0).getBillCode());
        fdCosdetail.setDelFlag("N");
        fdCosdetail.setDatabase(database);
        List<FdCosdetail> costdetaillist = fdCosdetailMapper.costdetaillist2(fdCosdetail);
        List<FdBill> receivableBills = fdBillMapper.getReceivableList(fdBills.get(0));
        if (receivableBills != null) {
            for (FdBill fb : receivableBills
            ) {
                FdCosdetail fc = new FdCosdetail();
                fc.setBillCode(fb.getBillCode());
                fc.setDelFlag("N");
                fc.setDatabase(database);
                List<FdCosdetail> costdetaillist2 = fdCosdetailMapper.costdetaillist2(fc);
                if (costdetaillist != null && costdetaillist2 != null && costdetaillist.size() == costdetaillist2.size()) {
                    for (FdCosdetail fcd : costdetaillist2
                    ) {
                        for (FdCosdetail fcd2 : costdetaillist
                        ) {
                            if (fcd.getContainerNumber() != null && !"".equals(fcd.getContainerNumber()) && fcd2.getContainerNumber() != null && !"".equals(fcd2.getContainerNumber())) {
                                if (fcd.getContainerNumber().equals(fcd2.getContainerNumber())) {
                                    FdBalanceDetail fdBalanceDetail = new FdBalanceDetail();
                                    fdBalanceDetail.setShiftId(fb.getProvinceTrainsNumber());
                                    fdBalanceDetail.setBillCode(fcd.getBillCode());
                                    fdBalanceDetail.setCustomerCode(fcd2.getCustomerCode());
                                    return fdBalanceDetailMapper.selectRemarksList2(fdBalanceDetail);
                                }
                            }
                        }
                    }
                }
            }
        }

        return null;
    }

    @Override
    public List<FdBalanceDetail> selectFdBalanceDetailListByShiftId(FdBalanceDetail fdBalanceDetail) {
        fdBalanceDetail.setDeleteFlag("N");
        return fdBalanceDetailMapper.selectFdBalanceDetailListByShiftId(fdBalanceDetail);
    }

    @Override
    public int updatefdBalanceDetailByShiftId(FdBalanceDetail fdBalanceDetail) {
        return fdBalanceDetailMapper.updatefdBalanceDetailByShiftId(fdBalanceDetail);
    }

    @Override
    public int updateRemainingAmountReturnById(FdBalanceDetail fdBalanceDetail) {
        return fdBalanceDetailMapper.updateRemainingAmountReturnById(fdBalanceDetail);
    }

    @Override
    @Transactional
    public R cancel(Integer id) {
        FdBalanceDetail fdBalanceDetail = fdBalanceDetailMapper.selectFdBalanceDetailListById(id);
        if (fdBalanceDetail != null) {
            if (fdBalanceDetail.getTotalAmount().compareTo(fdBalanceDetail.getAvailableAmount()) == 0) {
                //作废余额
                FdBalanceDetail updateObj = new FdBalanceDetail();
                updateObj.setId(id);
                updateObj.setDeleteFlag("Y");
                fdBalanceDetailMapper.updateFdBalanceDetailById(updateObj);

            } else {
                return R.error("该余额已抵扣，无法作废！");
            }
        } else {
            return R.error("未查询到该余额，无法作废！");
        }
        return R.success("余额已作废！");
    }

    @Override
    public void updateBalanceAmount(BalanceDetailDTO balanceDetailDTO) {
        fdBalanceDetailMapper.updateBalanceAmount(balanceDetailDTO);
    }

    @Override
    public List<BillBalanceJsListVO> selectBillJsList(String billNo) {
        List<BillBalanceJsListVO> jsList = fdBalanceDetailMapper.selectBillJsList(billNo);
        if (CollectionUtil.isNotEmpty(jsList) && !jsList.stream().anyMatch(Objects::isNull)) {
            return jsList;
        }
        return new ArrayList<>();
    }

    /**
     * 根据编码获取收款列表
     *
     * @param billNo
     * @return
     */
    @Override
    public List<BillBalanceSkListVO> selectBillSkList(String billNo) {
        List<BillBalanceSkListVO> billBalanceSkListVOS = fdBalanceDetailMapper.selectBillSkList(billNo);
        if (CollectionUtil.isNotEmpty(billBalanceSkListVOS) && !billBalanceSkListVOS.stream().anyMatch(Objects::isNull)) {
            return billBalanceSkListVOS;
        }
        return new ArrayList<>();
    }

    /**
     * 根据编码获取量价捆绑数据
     *
     * @param billNo
     * @return
     */
    @Override
    public List<BillBalanceLjkbListVO> selectBillLjkbList(String billNo) {
        List<BillBalanceLjkbListVO> billBalanceLjkbListVOS = fdBalanceDetailMapper.selectBillLjkbList(billNo);
        if (CollectionUtil.isNotEmpty(billBalanceLjkbListVOS) && !billBalanceLjkbListVOS.stream().anyMatch(Objects::isNull)) {
            return billBalanceLjkbListVOS;
        }
        return new ArrayList<>();
    }

    /**
     * 根据结算单号将余额回滚
     *
     * @param billNo
     * @return
     */
    @Override
    public int selectUpdateBalanceAmountByBalanceCode(String billNo) {
        // 查询出涉及结算单的所有余额流水
        // 结算余额数据
        List<BillBalanceJsListVO> billBalanceJsListVOS = fdTradingDetailsMapper.selectJieSuanDetailByBillNo(billNo);
        if (CollectionUtil.isNotEmpty(billBalanceJsListVOS) && !billBalanceJsListVOS.stream().anyMatch(Objects::isNull)) {
            for (BillBalanceJsListVO billBalanceJsListVO : billBalanceJsListVOS) {
                fdTradingDetailsMapper.updateJieSuanAmount(billBalanceJsListVO);
            }
        }

        // 将遗留数据删除
        return fdTradingDetailsMapper.updateDeleteByBillCode(billNo);
    }

    @Override
    public FdBalanceDetail selectFdBalanceDetailListById(Integer id) {
        return fdBalanceDetailMapper.selectFdBalanceDetailListById(id);
    }


    /**
     * 根据客户编码查询余额总数
     *
     * @Param: null
     * @Return:
     * @Author: Howe
     * @Date: 2024/6/24 上午10:29
     **/
    @Override
    public Page selectTotalPageByCustomer(Query query) {
        FdBalanceDetail fdBalanceDetail = BeanUtil.mapToBean(query.getCondition(), FdBalanceDetail.class, false);
        query.setRecords(fdBalanceDetailMapper.selectTotalByCustomer(query, fdBalanceDetail));
        return query;
    }


}
