package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfFinancingPledgedisposalinfo;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 质押物处置情况 服务接口层
 *
 * <AUTHOR>
 * @date 2023-02-08 15:13:43
 */
public interface EfFinancingPledgedisposalinfoService extends IService<EfFinancingPledgedisposalinfo> {
    /**
     * 查询质押物处置情况信息
     *
     * @param rowId 质押物处置情况ID
     * @return 质押物处置情况信息
     */
    public EfFinancingPledgedisposalinfo selectEfFinancingPledgedisposalinfoById(String rowId);

    /**
     * 查询质押物处置情况列表
     *
     * @param efFinancingPledgedisposalinfo 质押物处置情况信息
     * @return 质押物处置情况集合
     */
    public List<EfFinancingPledgedisposalinfo> selectEfFinancingPledgedisposalinfoList(EfFinancingPledgedisposalinfo efFinancingPledgedisposalinfo);


    /**
     * 分页模糊查询质押物处置情况列表
     * @return 质押物处置情况集合
     */
    public Page selectEfFinancingPledgedisposalinfoListByLike(Query query);



    /**
     * 新增质押物处置情况
     *
     * @param efFinancingPledgedisposalinfo 质押物处置情况信息
     * @return 结果
     */
    public int insertEfFinancingPledgedisposalinfo(EfFinancingPledgedisposalinfo efFinancingPledgedisposalinfo);

    /**
     * 修改质押物处置情况
     *
     * @param efFinancingPledgedisposalinfo 质押物处置情况信息
     * @return 结果
     */
    public int updateEfFinancingPledgedisposalinfo(EfFinancingPledgedisposalinfo efFinancingPledgedisposalinfo);

    /**
     * 删除质押物处置情况
     *
     * @param rowId 质押物处置情况ID
     * @return 结果
     */
    public int deleteEfFinancingPledgedisposalinfoById(String rowId);

    /**
     * 批量删除质押物处置情况
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfFinancingPledgedisposalinfoByIds(Integer[] rowIds);

}

