package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.BillBalanceBindingCity;
import com.huazheng.tunny.ocean.api.vo.BillBalanceMainCitySubDetailVO;

import java.util.List;

/**
 * 应付账单结算绑定账单表（市） 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-06-15 14:15:50
 */
public interface BillBalanceBindingCityService extends IService<BillBalanceBindingCity> {
    /**
     * 查询应付账单结算绑定账单表（市）信息
     *
     * @param id 应付账单结算绑定账单表（市）ID
     * @return 应付账单结算绑定账单表（市）信息
     */
    public BillBalanceBindingCity selectBillBalanceBindingCityById(Integer id);

    /**
     * 查询应付账单结算绑定账单表（市）列表
     *
     * @param billBalanceBindingCity 应付账单结算绑定账单表（市）信息
     * @return 应付账单结算绑定账单表（市）集合
     */
    public List<BillBalanceBindingCity> selectBillBalanceBindingCityList(BillBalanceBindingCity billBalanceBindingCity);


    /**
     * 分页模糊查询应付账单结算绑定账单表（市）列表
     *
     * @return 应付账单结算绑定账单表（市）集合
     */
    public Page selectBillBalanceBindingCityListByLike(Query query);


    /**
     * 新增应付账单结算绑定账单表（市）
     *
     * @param billBalanceBindingCity 应付账单结算绑定账单表（市）信息
     * @return 结果
     */
    public int insertBillBalanceBindingCity(BillBalanceBindingCity billBalanceBindingCity);

    /**
     * 修改应付账单结算绑定账单表（市）
     *
     * @param billBalanceBindingCity 应付账单结算绑定账单表（市）信息
     * @return 结果
     */
    public int updateBillBalanceBindingCity(BillBalanceBindingCity billBalanceBindingCity);

    /**
     * 删除应付账单结算绑定账单表（市）
     *
     * @param id 应付账单结算绑定账单表（市）ID
     * @return 结果
     */
    public int deleteBillBalanceBindingCityById(Integer id);

    /**
     * 批量删除应付账单结算绑定账单表（市）
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteBillBalanceBindingCityByIds(Integer[] ids);

    /**
     * 根据结算账单号获取子帐单列表
     *
     * @param balanceNo
     * @return
     */
    List<BillBalanceMainCitySubDetailVO> selectBillSubListByBalanceNo(String balanceNo);

    List<BillBalanceMainCitySubDetailVO> selectProvinceBillSubListByBalanceNo(String balanceNo);

    List<BillBalanceMainCitySubDetailVO> selectCustomerBillSubListByBalanceNo(String balanceNo);



    int updateDeletedBillBalanceBindingByBillCode(List<String> subBillNos,String balanceNo);

    /**
     * 根据结算单号查询子帐单编码
     * @param balanceNo
     * @return
     */
    List<String> selectBindingSubBillByBalanceCode(String balanceNo);

}

