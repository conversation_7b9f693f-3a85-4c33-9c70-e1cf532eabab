package com.huazheng.tunny.ocean.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.FdShippingNumDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.vo.*;
import com.huazheng.tunny.ocean.mapper.PayCodeMesMapper;
import com.huazheng.tunny.ocean.mapper.WaybillContainerInfoMapper;
import com.huazheng.tunny.ocean.mapper.WaybillGoodsInfoMapper;
import com.huazheng.tunny.ocean.service.*;
import com.huazheng.tunny.ocean.service.impl.FdInvoiceCostLedgerService;
import com.huazheng.tunny.ocean.service.impl.FdShippingAccountAuditService;
import com.huazheng.tunny.ocean.util.CheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 发运台账主表
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:30:06
 */
@RestController
@RequestMapping("/fdshippingaccount")
@Slf4j
public class FdShippingAccountController {
    @Autowired
    private FdShippingAccountService fdShippingAccountService;


    @Autowired
    private FdInvoiceCostLedgerService fdInvoiceCostLedgerService;

    @Autowired
    private WaybillContainerInfoMapper waybillContainerInfoMapper;

    @Autowired
    private PayCodeMesMapper payCodeMesMapper;

    @Autowired
    private WaybillGoodsInfoMapper waybillGoodsInfoMapper;

    @Autowired
    private OperationLogService operationLogService;

    @Autowired
    private FdShippingAccoundetailService fdShippingAccoundetailService;

    @Autowired
    private FdShippingAccoundetailSubService fdShippingAccoundetailSubService;
    @Autowired
    private ContainerTypeDataService containerTypeDataService;
    @Autowired
    private WaybillHeaderService waybillHeaderService;
    @Autowired
    private WaybillContainerInfoService waybillContainerInfoService;
    @Autowired
    private ShifmanagementService shifmanagementService;
    @Autowired
    private FdShippingAccountAuditService fdShippingAccountAuditService;


    @Value("${path.testPath}")
    private String testPath;
    @Value("${path.proPath}")
    private String proPath;
    @Value("${path.windowsPath}")
    private String windowsPath;
    @Value("${path.linuxPath}")
    private String linuxPath;

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdShippingAccountService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdShippingAccountService.selectFdShippingAccountListByLike(new Query<>(params));
    }

    /**
     * 导出台账数据为 Excel
     *
     * @param fdShippingAccount 台账数据
     * @param response          Http 响应对象
     * @throws IOException IO异常
     */
    @PostMapping("/exportShippingAccountExcel")
    public void exportShippingAccountExcel(@RequestBody FdShippingAccount fdShippingAccount, HttpServletResponse response) throws IOException {
        fdShippingAccountService.exportShippingAccountExcel(fdShippingAccount, response);
    }

    /**
     * 查询可生成台账列表
     *
     * @param params
     * @return
     */
    @GetMapping("/accountListPage")
    public Page accountListPage(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdShippingAccountService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdShippingAccountService.selectShippingAccountListNew(new Query<>(params));
    }


    /**
     * 市平台发运台账同步数据--查询未生成台账的账单
     */
    @GetMapping("/selectNotGenerateStandingBook")
    public List<FdShippingAccount> selectNotGenerateStandingBook(@RequestParam Map<String, Object> params) {
        return fdShippingAccountService.selectNotGenerateStandingBook(params);
    }

    /**
     * 根据班次号获取班次信息
     *
     * @param shiftNo
     * @return
     */
    @GetMapping("/selectShippingInfo")
    public R selectShippingInfo(String shiftNo) {
        return new R<>(200, true, fdShippingAccountService.selectShippingInfo(shiftNo));
    }

    /**
     * 新增发运台账
     *
     * @param vo
     * @return R
     */
    @PostMapping("/save")
    @Transactional(rollbackFor = Exception.class)
    public R save(@RequestBody FdShippingAccountVO vo) {
        R r = fdShippingAccountService.insertFdShippingAccount(vo);

        //插入操作日志
        /*SecruityUser userInfo = SecurityUtils.getUserInfo();
        OperationLog log = new OperationLog();
        log.setUuid(UUID.randomUUID().toString());
        log.setProcessCode(r.getObject().toString());
        log.setProcessType("市-台账生成-新增");
        log.setOperationCode(userInfo.getUserName());
        log.setOperationName(userInfo.getRealName());
        log.setOperationTime(new Date());
        log.setOperationResult("新增完成");
        log.setCorInterface("/fdshippingaccount/save");
        log.setDeleteFlag("N");
        operationLogService.insertOperationLog(log);*/
        return r;
    }


    /**
     * 发运台账同步数据--保存接口
     */
    @PostMapping("/saveSynchronousData")
    public R saveSynchronousData(@RequestBody FdShippingAccountVO vo) {
        R r = new R();
        try {
            r = fdShippingAccountService.saveSynchronousData(vo);
            //插入操作日志
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            OperationLog log = new OperationLog();
            log.setUuid(UUID.randomUUID().toString());
            log.setProcessCode(r.getObject().toString());
            log.setProcessType("市-台账同步-数据同步");
            log.setOperationCode(userInfo.getUserName());
            log.setOperationName(userInfo.getRealName());
            log.setOperationTime(new Date());
            log.setOperationResult("数据同步完成");
            log.setCorInterface("/fdshippingaccount/saveSynchronousData");
            log.setDeleteFlag("N");
            operationLogService.insertOperationLog(log);
        } catch (Exception e) {
            log.warn(e.getMessage());
            StringBuilder msg = new StringBuilder("新增失败");
            if (e.getMessage().contains("请联系系统管理员")) {
                msg.append("，").append(e.getMessage());
            }
            r.setMsg(msg.toString());
            r.setStatusCode(500);
            r.setB(false);
        }
        return r;
    }


    /**
     * 信息
     *
     * @param accountCode
     * @return R
     */
    @GetMapping("/{accountCode}")
    public R info(@PathVariable("accountCode") String accountCode) {
        FdShippingAccount fdShippingAccount = fdShippingAccountService.selectFdShippingAccountByAccountCode(accountCode);
        return new R<>(fdShippingAccount);
    }

    /**
     * 合并查询台账信息
     *
     * @param accountCodes
     * @return R
     */
    @PostMapping("/infoBatch")
    public R infoBatch(@RequestBody List<String> accountCodes) {
        return fdShippingAccountService.infoBatch(accountCodes);
    }


    /**
     * 修改发运台账
     *
     * @param vo
     * @return R
     */
    @PostMapping("/updateAccount")
    public R updateAccount(@RequestBody FdShippingAccountVO vo) {
        R r = fdShippingAccountService.updateFdShippingAccount(vo);

        //插入操作日志
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        OperationLog log = new OperationLog();
        log.setUuid(UUID.randomUUID().toString());
        log.setProcessCode(vo.getAccountCode());
        log.setProcessType("市-台账生成-保存提交");
        log.setOperationCode(userInfo.getUserName());
        log.setOperationName(userInfo.getRealName());
        log.setOperationTime(new Date());
        log.setOperationResult("提交完成");
        log.setCorInterface("/fdshippingaccount/updateAccount");
        log.setDeleteFlag("N");
        operationLogService.insertOperationLog(log);

        return r;
    }

    /**
     * 修改省级班列号
     */
    @PostMapping("/updateProvinceShiftNo")
    public R updateProvinceShiftNo(@RequestBody FdShippingAccount fdShippingAccount) {
        R r = fdShippingAccountService.updateProvinceShiftNoNew(fdShippingAccount);
        return r;
    }

    /**
     * 修改省级班列号校验
     */
    @PostMapping("/updateProvinceShiftNoCheck")
    public R updateProvinceShiftNoCheck(@RequestBody FdShippingAccount fdShippingAccount) {
        R r = fdShippingAccountService.updateProvinceShiftNoCheck(fdShippingAccount);
        return r;
    }

    /**
     * 省平台发运台账审核
     * 原逻辑废弃
     * 新逻辑调用auditStatus3
     */
    @PostMapping("/auditStatus")
    public R auditStatus(@RequestBody FdShippingAccountVO vo) throws Exception {
        if (vo.getRowId() == null) {
            return R.error("主键id不能为空");
        }
        if ("".equals(vo.getStatus()) && vo.getStatus() != null) {
            return R.error("状态不能为空");
        }
        return fdShippingAccountService.auditStatus3(vo);
    }


//    /**
//     * auditStatus2废弃
//     * 调用auditStatus3
//     */
//    @PostMapping("/auditStatus2")
//    public R auditStatus2(@RequestBody FdShippingAccountVO vo) throws Exception {
//        if (vo.getRowId() == null) {
//            return R.error("主键id不能为空");
//        }
//        if (StrUtil.isEmpty(vo.getStatus())) {
//            return R.error("状态不能为空");
//        }
//        return fdShippingAccountService.auditStatus3(vo);
//    }

    /**
     * 台账合并审批
     */
    @PostMapping("/auditStatusBatch")
    public R auditStatusBatch(@RequestBody List<FdShippingAccountVO> vos) throws Exception {
        if (CollUtil.isNotEmpty(vos)) {
            for (FdShippingAccountVO vo : vos) {
                if (vo.getRowId() == null) {
                    return new R<>(Boolean.FALSE, "主键id不能为空");
                }
                if (StrUtil.isEmpty(vo.getStatus())) {
                    return new R<>(Boolean.FALSE, "状态不能为空");
                }
            }
        }
        return fdShippingAccountService.auditStatusBatch(vos);
    }

    /**
     * 台账合并审批校验
     */
    @PostMapping("/auditStatusBatchCheck")
    public R auditStatusBatchCheck(@RequestBody List<FdShippingAccountVO> vos) throws Exception {
        if (CollUtil.isNotEmpty(vos)) {
            for (FdShippingAccountVO vo : vos) {
                if (vo.getRowId() == null) {
                    return new R<>(Boolean.FALSE, "主键id不能为空");
                }
            }
        }
        return fdShippingAccountService.auditStatusBatchCheck(vos);
    }

    /**
     * 小程序审核
     */
    @PostMapping("/auditStatusWx")
    public R auditStatusWx(@RequestBody FdShippingAccountVO vo) throws Exception {
        if (vo.getRowId() == null) {
            return new R<>(Boolean.FALSE, "主键id不能为空");
        }
        if (StrUtil.isEmpty(vo.getStatus())) {
            return new R<>(Boolean.FALSE, "状态不能为空");
        }
        return fdShippingAccountService.auditStatusWx(vo);
    }

    /**
     * 台账审核生成账单
     *
     * @Param: FdShippingAccountVO
     * @Return:
     * @Author: zhaohr
     * @Date: 2024/5/29 15:48
     **/
    @PostMapping("/auditStatus3")
    public R auditStatus3(@RequestBody FdShippingAccountVO vo) throws Exception {
        if (vo.getRowId() == null) {

            return new R<>(Boolean.FALSE, "主键id不能为空");
        }
        if (StrUtil.isEmpty(vo.getStatus())) {
            return new R<>(Boolean.FALSE, "状态不能为空");
        }
        fdShippingAccountService.auditStatus3(vo);

        return new R<>(Boolean.TRUE);
    }

    /**
     * 二次导入台账审核
     */
    @PostMapping("/secondAuditStatus")
    public R secondAuditStatus(@RequestBody FdShippingAccountVO vo) {
        if (vo.getRowId() == null) {
            return new R<>(Boolean.FALSE, "主键id不能为空");
        }
        if (StrUtil.isEmpty(vo.getStatus())) {
            return new R<>(Boolean.FALSE, "状态不能为空");
        }

        return fdShippingAccountService.secondAuditStatus(vo);
    }

    /**
     * 省平台修改台账发运时间
     *
     * @param fdShippingAccount
     * @return R
     */
    @PutMapping("/updateShippingTime")
    public R updateShippingTime(@RequestBody FdShippingAccount fdShippingAccount) {
        return fdShippingAccountService.updateShippingTime(fdShippingAccount);
    }

    /**
     * 市平台查询历史订舱平台
     *
     * @param param
     * @return
     */
    @GetMapping("/selectFiShippingTime")
    public R selectFiShippingTime(@RequestParam Map<String, Object> param) throws ParseException {
        return fdShippingAccountService.selectFiShippingTime(param);
    }


    /**
     * 市平台统计信息
     *
     * @param param
     * @return
     */
    @GetMapping("/selectShiStatistics")
    public R selectShiStatistics(@RequestParam Map<String, Object> param) throws ParseException {
        return fdShippingAccountService.selectShiStatistics(param);
    }


    /**
     * 删除
     *
     * @param rowId
     * @return R
     */
    @DeleteMapping("/{rowId}")
    public R delete(@PathVariable Long rowId) {
        fdShippingAccountService.deleteFdShippingAccountById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     *
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody List<String> rowIds) {
        fdShippingAccountService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 导出发运台账EXCEL
     * params:shiftNo 班次号
     *
     * @return
     */
    @GetMapping("/shippingAccountingExport")
    public R shippingAccountingExport(String shiftNo, HttpServletResponse res) throws Exception {
        //查询<运单舱单信息(必填)>sheet页数据
        String platformCode = SecurityUtils.getUserInfo().getPlatformCode();
        Shifmanagement sel = new Shifmanagement();
        sel.setShiftId(shiftNo);
        List<Shifmanagement> shifmanagements = shifmanagementService.selectShifmanagementList(sel);
        if (CollUtil.isEmpty(shifmanagements)) {
            for (Shifmanagement shifmanagement : shifmanagements) {
                if (StrUtil.isBlank(shifmanagement.getParentId())) {
                    platformCode = shifmanagement.getPlatformCode();
                }
            }
        }
        List<ShippingAccountExVO> exList = waybillContainerInfoMapper.selectShippingAccount(shiftNo, platformCode);
        if (CollUtil.isEmpty(exList)) {
            return new R(500, Boolean.FALSE, "当前班次尚未生成发运台账信息");
        }
        //移除所有的null元素
        exList.removeAll(Collections.singleton(null));
        if (CollUtil.isEmpty(exList)) {
            return new R(500, Boolean.FALSE, "当前班次尚未生成发运台账信息");
        }

        int i = 1;
        for (ShippingAccountExVO vo : exList) {
            vo.setId(i++);
        }

        //查询<付费代码)>sheet页数据
        List<PayMesVO> payMesExList = payCodeMesMapper.selectPayCodeMesListByShiftNo(shiftNo, platformCode);
        /*if (CollUtil.isEmpty(payMesExList)) {
            return new R(500, Boolean.FALSE, "当前班次尚未生成发运台账信息");
        }*/
        if (CollUtil.isNotEmpty(payMesExList)) {
            //移除所有的null元素
            payMesExList.removeAll(Collections.singleton(null));
        /*if (CollUtil.isEmpty(payMesExList)) {
            return new R(500, Boolean.FALSE, "当前班次尚未生成发运台账信息");
        }*/

            SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
            for (PayMesVO vo : payMesExList) {
                if (vo.getPaymentDate() != null) {
                    vo.setPaymentDateS(dft.format(vo.getPaymentDate()));
                }
            }
        }

        //查询<货物信息>sheet页数据
        List<GoodsVO> goodExList = waybillGoodsInfoMapper.selectWaybillGoodsEx(shiftNo, platformCode);
        /*if (CollUtil.isEmpty(goodExList)) {
            return new R(500, Boolean.FALSE, "当前班次尚未生成发运台账信息");
        }*/
        if (CollUtil.isNotEmpty(goodExList)) {
            //移除所有的null元素
            goodExList.removeAll(Collections.singleton(null));
        /*if (CollUtil.isEmpty(goodExList)) {
            return new R(500, Boolean.FALSE, "当前班次尚未生成发运台账信息");
        }*/
        }

        ExcelWriter excelWriter;
        String templateFileName = "shippingEx.xls";
        String fileName = URLEncoder.encode("发运台账（导出）", "UTF-8").replaceAll("\\+", "%20");
        //ClassUtils.getDefaultClassLoader().getResource("").getPath()
        res.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xls");
        System.out.println("测试路径：" + proPath + "&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&");
        excelWriter = EasyExcel.write(res.getOutputStream()).withTemplate(proPath + templateFileName).build();

        WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "运单舱单信息(必填)").build();
        WriteSheet writeSheet2 = EasyExcel.writerSheet(1, "付费代码").build();
        WriteSheet writeSheet3 = EasyExcel.writerSheet(2, "货物信息").build();

        //运单舱单信息存入
        excelWriter.fill(exList, writeSheet1);
        //付费代码存入
        if (CollUtil.isNotEmpty(payMesExList)) {
            excelWriter.fill(payMesExList, writeSheet2);
        }
        //付费代码存入
        if (CollUtil.isNotEmpty(goodExList)) {
            excelWriter.fill(goodExList, writeSheet3);
        }

        excelWriter.finish();
        return new R(200, Boolean.TRUE, "导出完成");
    }

    /**
     * 导入EXCEL
     * <p>
     *
     * @param titleStr
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/imported")
    public R imported(String titleStr, MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<FdShippingAccount> list = reader.readAll(FdShippingAccount.class);
        fdShippingAccountService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 导出接口
     *
     * @param map
     * @param response
     * @throws IOException
     */
    @PostMapping("/exportedFiShippingTime")
    public void exportedFile(@RequestBody Map<String, Object> map, HttpServletResponse response) throws IOException, ParseException {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("历史订舱单信息");
        XSSFRow row = sheet.createRow(0);
        //宽度
        for (int i = 0; i <= 17; i++) {
            sheet.setColumnWidth(i, 60 * 80);
        }
        row.setHeight((short) (10 * 50));
        //字体
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");

        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);//水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setBorderBottom(BorderStyle.THIN); //下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);


        XSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("序号");
        cell0.setCellStyle(style);
        XSSFCell cell1 = row.createCell(1);
        cell1.setCellValue("年");
        cell1.setCellStyle(style);
        XSSFCell cell2 = row.createCell(2);
        cell2.setCellValue("月");
        cell2.setCellStyle(style);
        XSSFCell cell3 = row.createCell(3);
        cell3.setCellValue("订舱费用");
        cell3.setCellStyle(style);
        XSSFCell cell4 = row.createCell(4);
        cell4.setCellValue("已支付费用");
        cell4.setCellStyle(style);
        XSSFCell cell5 = row.createCell(5);
        cell5.setCellValue("支付率(%)");
        cell5.setCellStyle(style);


        R r = this.selectFiShippingTime(map);

        int i = 1;
        List<Map<String, Object>> listMap = (List<Map<String, Object>>) r.getData();
        if (listMap.size() > 0) {
            for (Map<String, Object> maps : listMap) {
                XSSFRow rows = sheet.createRow(i);
                rows.createCell(0).setCellValue(i);
                if (maps.isEmpty() == false) {
                    if (maps.containsKey("year")) {
                        rows.createCell(1).setCellValue(maps.get("year").toString());
                    }
                    if (maps.containsKey("month")) {
                        rows.createCell(2).setCellValue(maps.get("month").toString());
                    }
                    if (maps.containsKey("totalCost")) {
                        rows.createCell(3).setCellValue(maps.get("totalCost").toString());
                    }
                    if (maps.containsKey("havePaid")) {
                        rows.createCell(4).setCellValue(maps.get("havePaid").toString());
                    }
                    if (maps.containsKey("payment")) {
                        rows.createCell(5).setCellValue(maps.get("payment").toString());
                    }
                }
                i++;
            }
        }

        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String("历史融资信息.xls".getBytes("GB2312"), "8859_1"));
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }


    /**
     * 下载模板
     *
     * @param response
     * @throws IOException
     */
    @PostMapping("/downloadTemplate")
    public void downloadTemplate(@RequestParam String accountCode, HttpServletResponse response) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("铁路运费");
        //字体
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");

        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);//水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setBorderBottom(BorderStyle.THIN); //下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);
        style.setWrapText(true);

        XSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setFont(font);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);//水平居中
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        titleStyle.setBorderBottom(BorderStyle.THIN); //下边框
        titleStyle.setBorderLeft(BorderStyle.THIN);//左边框
        titleStyle.setBorderTop(BorderStyle.THIN);//上边框
        titleStyle.setBorderRight(BorderStyle.THIN);//右边框
        titleStyle.setBottomBorderColor(HSSFColor.BLACK.index);
        titleStyle.setLeftBorderColor(HSSFColor.BLACK.index);
        titleStyle.setRightBorderColor(HSSFColor.BLACK.index);
        titleStyle.setTopBorderColor(HSSFColor.BLACK.index);
        titleStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        //宽度
        for (int i = 0; i <= 8; i++) {
            sheet.setColumnWidth(i, 60 * 80);
        }
//        row1.setHeight((short) (10*50));

        //第一行
        XSSFRow row0 = sheet.createRow(0);
        XSSFCell cell00 = row0.createCell(0);
        cell00.setCellValue("箱号");
        cell00.setCellStyle(titleStyle);
        XSSFCell cell01 = row0.createCell(1);
        cell01.setCellValue("铁路境内运费（人民币）");
        cell01.setCellStyle(titleStyle);
        XSSFCell cell02 = row0.createCell(2);
        cell02.setCellValue("铁路境外运费（人民币）");
        cell02.setCellStyle(titleStyle);
        XSSFCell cell03 = row0.createCell(3);
        cell03.setCellValue("箱属");
        cell03.setCellStyle(titleStyle);
        XSSFCell cell04 = row0.createCell(4);
        cell04.setCellValue("箱型代码");
        cell04.setCellStyle(titleStyle);
        XSSFCell cell05 = row0.createCell(5);
        cell05.setCellValue("类型");
        cell05.setCellStyle(titleStyle);
        XSSFCell cell06 = row0.createCell(6);
        cell06.setCellValue("发站");
        cell06.setCellStyle(titleStyle);
        XSSFCell cell07 = row0.createCell(7);
        cell07.setCellValue("到站");
        cell07.setCellStyle(titleStyle);
        XSSFCell cell08 = row0.createCell(8);
        cell08.setCellValue("目的国");
        cell08.setCellStyle(titleStyle);

        FdShippingAccount sel2 = new FdShippingAccount();
        sel2.setAccountCode(accountCode);
        sel2.setDeleteFlag("N");
        final List<FdShippingAccount> fdShippingAccounts = fdShippingAccountService.selectFdShippingAccountList(sel2);
        if (CollUtil.isNotEmpty(fdShippingAccounts)) {
            if (StrUtil.isNotEmpty(fdShippingAccounts.get(0).getStatus())) {
                if ("1".equals(fdShippingAccounts.get(0).getStatus())) {
                    //首次导入模板
                    FdShippingAccoundetail sel = new FdShippingAccoundetail();
                    sel.setAccountCode(accountCode);
                    sel.setDeleteFlag("N");
                    final List<FdShippingAccoundetail> fdShippingAccoundetails = fdShippingAccoundetailService.selectFdShippingAccoundetailList(sel);
                    if (CollUtil.isNotEmpty(fdShippingAccoundetails)) {
                        for (int i = 0; i < fdShippingAccoundetails.size(); i++) {
                            //第二行
                            XSSFRow row1 = sheet.createRow(i + 1);

                            XSSFCell cell10 = row1.createCell(0);
                            cell10.setCellValue(fdShippingAccoundetails.get(i).getContainerNumber());
                            cell10.setCellStyle(style);
                            XSSFCell cell11 = row1.createCell(1);
                            cell11.setCellStyle(style);
                            if (fdShippingAccoundetails.get(i).getRrDomesticFreight() != null || fdShippingAccoundetails.get(i).getRrDomesticFreight().compareTo(BigDecimal.ZERO) == 0) {
                                cell11.setCellValue(fdShippingAccoundetails.get(i).getRrDomesticFreight().doubleValue());
                            } else {
                                cell11.setCellValue(fdShippingAccoundetails.get(i).getDomesticFreight().doubleValue());
                            }
                            XSSFCell cell12 = row1.createCell(2);
                            if (fdShippingAccoundetails.get(i).getRrOverseasFreightCny() != null || fdShippingAccoundetails.get(i).getRrOverseasFreightCny().compareTo(BigDecimal.ZERO) == 0) {
                                cell12.setCellValue(fdShippingAccoundetails.get(i).getRrOverseasFreightCny().doubleValue());
                            } else {
                                cell12.setCellValue(fdShippingAccoundetails.get(i).getOverseasFreightCny().doubleValue());
                            }
                            cell12.setCellStyle(style);
                            XSSFCell cell13 = row1.createCell(3);
                            if (StrUtil.isNotEmpty(fdShippingAccoundetails.get(i).getContainerOwner())) {
                                if ("0".equals(fdShippingAccoundetails.get(i).getContainerOwner())) {
                                    cell13.setCellValue("自备箱");
                                } else if ("1".equals(fdShippingAccoundetails.get(i).getContainerOwner())) {
                                    cell13.setCellValue("中铁箱");
                                }
                            }
                            cell13.setCellStyle(style);

                            XSSFCell cell14 = row1.createCell(4);
                            cell14.setCellValue(String.valueOf(fdShippingAccoundetails.get(i).getContainerTypeCode()));
                            cell14.setCellStyle(style);

                            XSSFCell cell15 = row1.createCell(5);
                            if (StrUtil.isNotEmpty(fdShippingAccoundetails.get(i).getIsRansit())) {
                                if ("E".equals(fdShippingAccoundetails.get(i).getIsRansit())) {
                                    cell15.setCellValue("出口");
                                } else if ("I".equals(fdShippingAccoundetails.get(i).getIsRansit())) {
                                    cell15.setCellValue("进口");
                                } else if ("P".equals(fdShippingAccoundetails.get(i).getIsRansit())) {
                                    cell15.setCellValue("过境");
                                }
                            }
                            cell15.setCellStyle(style);
                            XSSFCell cell16 = row1.createCell(6);
                            cell16.setCellValue(String.valueOf(fdShippingAccoundetails.get(i).getDestinationName()));
                            cell16.setCellStyle(style);
                            XSSFCell cell17 = row1.createCell(7);
                            cell17.setCellValue(String.valueOf(fdShippingAccoundetails.get(i).getDestination()));
                            cell17.setCellStyle(style);
                            XSSFCell cell18 = row1.createCell(8);
                            cell18.setCellValue(String.valueOf(fdShippingAccoundetails.get(i).getDestinationCountry()));
                            cell18.setCellStyle(style);
                        }
                    }
                } else if ("4".equals(fdShippingAccounts.get(0).getStatus())) {
                    //二次导入模板
                    FdShippingAccoundetailSub sel3 = new FdShippingAccoundetailSub();
                    sel3.setAccountCode(accountCode);
                    sel3.setDeleteFlag("N");
                    final List<FdShippingAccoundetailSub> fdShippingAccoundetailSubs = fdShippingAccoundetailSubService.selectFdShippingAccoundetailSubList(sel3);
                    if (CollUtil.isNotEmpty(fdShippingAccoundetailSubs)) {
                        for (int i = 0; i < fdShippingAccoundetailSubs.size(); i++) {
                            //第二行
                            XSSFRow row1 = sheet.createRow(i + 1);

                            XSSFCell cell10 = row1.createCell(0);
                            cell10.setCellValue(fdShippingAccoundetailSubs.get(i).getContainerNumber());
                            cell10.setCellStyle(style);
                            XSSFCell cell11 = row1.createCell(1);
                            cell11.setCellValue(fdShippingAccoundetailSubs.get(i).getRrDomesticFreight().doubleValue());
                            cell11.setCellStyle(style);
                            XSSFCell cell12 = row1.createCell(2);
                            cell12.setCellValue(fdShippingAccoundetailSubs.get(i).getRrOverseasFreightCny().doubleValue());
                            cell12.setCellStyle(style);
                            XSSFCell cell13 = row1.createCell(3);
                            if (StrUtil.isNotEmpty(fdShippingAccoundetailSubs.get(i).getContainerOwner())) {
                                if ("0".equals(fdShippingAccoundetailSubs.get(i).getContainerOwner())) {
                                    cell13.setCellValue("自备箱");
                                } else if ("1".equals(fdShippingAccoundetailSubs.get(i).getContainerOwner())) {
                                    cell13.setCellValue("中铁箱");
                                }
                            }
                            cell13.setCellStyle(style);

                            XSSFCell cell14 = row1.createCell(4);
                            cell14.setCellValue(String.valueOf(fdShippingAccoundetailSubs.get(i).getContainerTypeCode()));
                            cell14.setCellStyle(style);

                            XSSFCell cell15 = row1.createCell(5);
                            if (StrUtil.isNotEmpty(fdShippingAccoundetailSubs.get(i).getIsRansit())) {
                                if ("E".equals(fdShippingAccoundetailSubs.get(i).getIsRansit())) {
                                    cell15.setCellValue("出口");
                                } else if ("I".equals(fdShippingAccoundetailSubs.get(i).getIsRansit())) {
                                    cell15.setCellValue("进口");
                                } else if ("P".equals(fdShippingAccoundetailSubs.get(i).getIsRansit())) {
                                    cell15.setCellValue("过境");
                                }
                            }
                            cell15.setCellStyle(style);
                            XSSFCell cell16 = row1.createCell(6);
                            cell16.setCellValue(String.valueOf(fdShippingAccoundetailSubs.get(i).getDestinationName()));
                            cell16.setCellStyle(style);
                            XSSFCell cell17 = row1.createCell(7);
                            cell17.setCellValue(String.valueOf(fdShippingAccoundetailSubs.get(i).getDestination()));
                            cell17.setCellStyle(style);
                            XSSFCell cell18 = row1.createCell(8);
                            cell18.setCellValue(String.valueOf(fdShippingAccoundetailSubs.get(i).getDestinationCountry()));
                            cell18.setCellStyle(style);
                            /*if ("0".equals(fdShippingAccoundetailSubs.get(i).getContainerNumber())) {
                                cell13.setCellValue("新增");
                                cell13.setCellStyle(style);
                            } else if ("1".equals(fdShippingAccoundetailSubs.get(i).getContainerNumber())) {
                                cell13.setCellValue("作废");
                                cell13.setCellStyle(style);
                            }*/
                        }
                    }
                }
            }
        }


        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        String fileName = "台账导入铁路运费模板.xlsx";
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
        response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();

    }

    /**
     * 功能描述: 判断是否为空行
     *
     * @param row 行对象
     * @return boolean
     * <AUTHOR> zheng
     * @date 2021/10/13
     */
    private boolean isRowEmpty(Row row) {
        if (row != null) {
            for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
                Cell cell = row.getCell(c);
                if (cell != null) {
                    cell.setCellType(CellType.STRING);
                }
                if (cell != null && !"".equals(cell.getStringCellValue())) {
                    return false;//不是空行
                }
            }
        }
        return true;//是空行
    }

    /**
     * 台账审核导入铁路费用
     */
    @PostMapping("/importedRr")
    @Transactional(propagation = Propagation.REQUIRED,
            isolation = Isolation.DEFAULT,
            timeout = 30,  // 设置合理超时
            rollbackFor = Exception.class)// 确保所有异常都会回滚
    public R importedRr(@RequestParam("file") MultipartFile file, @RequestParam("accountCode") String accountCode) {

        Workbook workbook = null;
        try {
            InputStream is = file.getInputStream();
            //利用WorkbookFactory获取Sheet，无需判断版本
            workbook = WorkbookFactory.create(is);
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<FdShippingAccoundetail> list = new ArrayList<>();

        //导入第一张sheet页
        Sheet sheet = workbook.getSheetAt(0);

        FdShippingAccoundetail sel = new FdShippingAccoundetail();
        sel.setAccountCode(accountCode);
        sel.setDeleteFlag("N");
        List<FdShippingAccoundetail> fdShippingAccoundetails = fdShippingAccoundetailService.selectFdShippingAccoundetailList(sel);
        if (CollUtil.isNotEmpty(fdShippingAccoundetails)) {
            //获取行数
            int lastRowNum = sheet.getLastRowNum();
            for (int i = 1; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                boolean blankFlag = isRowEmpty(row);
                if (blankFlag) {
                    continue;
                }

                FdShippingAccoundetail fdShippingAccoundetail = new FdShippingAccoundetail();

                if (row.getCell(0) != null) {
                    row.getCell(0).setCellType(CellType.STRING);
                    String containerNo = row.getCell(0).getStringCellValue();
                    boolean b = CheckUtil.verifyCntrCode(containerNo);
                    containerNo = containerNo.trim();
                    if (b) {
                        fdShippingAccoundetail.setContainerNo(containerNo);
                    } else {
                        return new R(500, Boolean.FALSE, null, "箱号格式错误：" + containerNo);
                    }
                }

                if (row.getCell(1) != null) {
                    row.getCell(1).setCellType(CellType.STRING);
                    final String stringCellValue = row.getCell(1).getStringCellValue();
                    try {
                        final Double rrDomesticFreight = Double.parseDouble(stringCellValue);
                        if (rrDomesticFreight != null) {
                            fdShippingAccoundetail.setRrDomesticFreight(BigDecimal.valueOf(rrDomesticFreight));
                        } else {
                            fdShippingAccoundetail.setRrDomesticFreight(BigDecimal.valueOf(0D));
                        }
                    } catch (Exception e) {
                        return new R(500, Boolean.FALSE, null, "铁路境内运费（人民币）错误：" + e.getMessage());
                    }

                }

                if (row.getCell(2) != null) {
                    row.getCell(2).setCellType(CellType.STRING);
                    final String stringCellValue = row.getCell(2).getStringCellValue();
                    try {
                        final Double rrOverseasFreightCny = Double.parseDouble(stringCellValue);
                        if (rrOverseasFreightCny != null) {
                            fdShippingAccoundetail.setRrOverseasFreightCny(BigDecimal.valueOf(rrOverseasFreightCny));

                            if (CollUtil.isNotEmpty(fdShippingAccoundetails)) {
                                for (FdShippingAccoundetail detail : fdShippingAccoundetails) {
                                    if (detail.getContainerNumber().equals(fdShippingAccoundetail.getContainerNo())) {
                                        if (detail.getExchangeRate() != null && detail.getExchangeRate().compareTo(BigDecimal.valueOf(0)) != 0) {
                                            fdShippingAccoundetail.setRrOverseasFreightOc(fdShippingAccoundetail.getRrOverseasFreightCny().divide(detail.getExchangeRate(), 2, BigDecimal.ROUND_HALF_UP));
                                            fdShippingAccoundetail.setExchangeRate(detail.getExchangeRate());
                                            fdShippingAccoundetail.setContainerNumber(detail.getContainerNumber());
                                        } else {
                                            fdShippingAccoundetail.setRrOverseasFreightOc(BigDecimal.valueOf(0D));
                                        }
                                    }
                                }
                            }
                        } else {
                            fdShippingAccoundetail.setRrOverseasFreightCny(BigDecimal.valueOf(0D));
                            fdShippingAccoundetail.setExchangeRate(BigDecimal.valueOf(0D));
                            fdShippingAccoundetail.setRrOverseasFreightOc(BigDecimal.valueOf(0D));
                        }
                    } catch (Exception e) {
                        return new R(500, Boolean.FALSE, null, "铁路境外运费（人民币）错误：" + e.getMessage());
                    }
                }
                fdShippingAccoundetail.setAccountCode(accountCode);
                list.add(fdShippingAccoundetail);
            }
        }
        if (CollUtil.isNotEmpty(list)) {
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            for (FdShippingAccoundetail fdShippingAccoundetail : list) {
                //rrOverseasFreightCny!=0
                if (fdShippingAccoundetail.getRrOverseasFreightCny().compareTo(BigDecimal.ZERO) != 0
                        && fdShippingAccoundetail.getExchangeRate().compareTo(BigDecimal.ZERO) != 0) {
                    fdShippingAccoundetail.setRrOverseasFreightOc(fdShippingAccoundetail.getRrOverseasFreightCny().divide(fdShippingAccoundetail.getExchangeRate(), 2, RoundingMode.HALF_UP));
                } else {
                    fdShippingAccoundetail.setRrOverseasFreightOc(BigDecimal.ZERO);
                }
                //根据境外人民币更新境外运费（原币）
                fdShippingAccoundetail.setUpdateWho(userInfo.getUserName());
                fdShippingAccoundetail.setUpdateWhoName(userInfo.getRealName());
                fdShippingAccoundetail.setContainerNumber(fdShippingAccoundetail.getContainerNo());
//                fdShippingAccoundetail.setUpdateTime(LocalDateTime.now());
                fdShippingAccoundetailService.updateFdShippingAccoundetailByAccountCode(fdShippingAccoundetail);
            }
        }

        return new R(200, Boolean.TRUE, list, "导入完成！");
    }


    /**
     * 校验台账是否可以二次导入
     *
     * @param rowId
     * @return R
     */
    @GetMapping("/checkTz/{rowId}")
    public R checkTz(@PathVariable("rowId") Long rowId) {
        return fdShippingAccountService.checkTz(rowId);
    }

    /**
     * 二次导入模板下载
     *
     * @param res
     * @return R
     */
    @PostMapping("/downloadSecondExportTemplate")
    public void downloadSecondExportTemplate(@RequestParam String accountCode, HttpServletResponse res) throws IOException {

        FdShippingAccoundetail sel = new FdShippingAccoundetail();
        sel.setAccountCode(accountCode);
        final List<FdShippingAccoundetail> fdShippingAccoundetails = fdShippingAccoundetailService.selectFdShippingAccoundetailListByCode(sel);

        ExcelWriter excelWriter = null;
        String templateFileName = "secondExport.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)) {
            templateFileName = linuxPath + templateFileName;
        } else {
            templateFileName = windowsPath + templateFileName;
        }
        String fileName = URLEncoder.encode("二次导入模板", "UTF-8").replaceAll("\\+", "%20");
        //ClassUtils.getDefaultClassLoader().getResource("").getPath()
        res.setHeader("Content-disposition", "attachment;filename*=" + fileName + ".xlsx");
        excelWriter = EasyExcel.write(res.getOutputStream()).withTemplate(templateFileName).build();

        WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "二次导入箱信息").build();
        if (fdShippingAccoundetails != null) {
            Long i = 1L;
            for (FdShippingAccoundetail detail : fdShippingAccoundetails) {
                detail.setRowId(i);
                i++;
            }
            excelWriter.fill(fdShippingAccoundetails, writeSheet1);
        }
        excelWriter.finish();
    }

    /*
     *
     * 台账二次导入
     * */
    @PostMapping("/importedSecond")
    @Transactional(rollbackFor = Exception.class)
    public R importedSecond(@RequestParam("file") MultipartFile file, @RequestParam("accountCode") String accountCode) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();

        //查询对应台账主表
        FdShippingAccount sel2 = new FdShippingAccount();
        sel2.setAccountCode(accountCode);
        sel2.setDeleteFlag("N");
        final List<FdShippingAccount> fdShippingAccounts = fdShippingAccountService.selectFdShippingAccountList(sel2);

        if (CollUtil.isNotEmpty(fdShippingAccounts)) {
            //班次剩余舱位数
            final String num1 = shifmanagementService.getNum(fdShippingAccounts.get(0).getShiftNo());

            //二次导入前该运单占用舱位数
            final String num2 = waybillContainerInfoService.getNum(fdShippingAccounts.get(0).getShiftNo());

            //二次导入后该运单占用舱位数
            Double num3 = 0D;

            Workbook workbook = null;
            try {
                InputStream is = file.getInputStream();
                //利用WorkbookFactory获取Sheet，无需判断版本
                workbook = WorkbookFactory.create(is);
            } catch (Exception e) {
                e.printStackTrace();
            }
            List<FdShippingAccoundetailSub> addList = new ArrayList<>();

            //导入第一张sheet页
            Sheet sheet = workbook.getSheetAt(0);
//        Sheet sheet = workbook.getSheetAt(0);
            int lastRowNum = sheet.getLastRowNum();

            ContainerTypeData sel = new ContainerTypeData();
            sel.setDeleteFlag("N");
            final List<ContainerTypeData> containerTypeDataList = containerTypeDataService.selectContainerTypeDataList(sel);

            if (lastRowNum > 0) {

                for (int i = 2; i <= lastRowNum; i++) {
                    FdShippingAccoundetailSub fdShippingAccoundetail = new FdShippingAccoundetailSub();
                    Row row = sheet.getRow(i);
                    //箱号
                    if (row.getCell(1) != null) {
                        row.getCell(1).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(1).getStringCellValue()) && row.getCell(1).getStringCellValue() != null) {
                        /*if(row.getCell(1).getStringCellValue().contains(" ")){
                            new R<>(500,Boolean.FALSE,null, "箱号包含空格，请检查数据！");
                        }*/
                            fdShippingAccoundetail.setContainerNumber(row.getCell(1).getStringCellValue().replaceAll(" ", ""));
                        } else {
                            new R<>(500, Boolean.FALSE, null, "箱号为空");
                        }
                        //类型
                        if (row.getCell(2) != null) {
                            row.getCell(2).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(2).getStringCellValue()) && row.getCell(2).getStringCellValue() != null) {
                        /*if(row.getCell(2).getStringCellValue().contains(" ")){
                            new R<>(500,Boolean.FALSE,null, "类型包含空格，请检查数据！");
                        }*/
                                if ("出口".equals(row.getCell(2).getStringCellValue())) {
                                    fdShippingAccoundetail.setIsRansit("E");
                                } else if ("进口".equals(row.getCell(2).getStringCellValue())) {
                                    fdShippingAccoundetail.setIsRansit("I");
                                } else if ("过境".equals(row.getCell(2).getStringCellValue())) {
                                    fdShippingAccoundetail.setIsRansit("P");
                                }
                            } else {
                                new R<>(500, Boolean.FALSE, null, "类型为空");
                            }
                        } else {
                            new R<>(500, Boolean.FALSE, null, "类型为空");
                        }
                        //货源组织单位
                        if (row.getCell(3) != null) {
                            row.getCell(3).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(3).getStringCellValue()) && row.getCell(3).getStringCellValue() != null) {
                        /*if(row.getCell(3).getStringCellValue().contains(" ")){
                            new R<>(500,Boolean.FALSE,null, "货源组织单位包含空格，请检查数据！");
                        }*/
                                fdShippingAccoundetail.setOrgUnit(row.getCell(3).getStringCellValue().replaceAll(" ", ""));
                            } else {
                                new R<>(500, Boolean.FALSE, null, "货源组织单位为空");
                            }
                        } else {
                            new R<>(500, Boolean.FALSE, null, "货源组织单位为空");
                        }

                        //收货人
                        if (row.getCell(4) != null) {
                            row.getCell(4).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(4).getStringCellValue()) && row.getCell(4).getStringCellValue() != null) {
                        /*if(row.getCell(4).getStringCellValue().contains(" ")){
                            new R<>(500,Boolean.FALSE,null, "收货人包含空格，请检查数据！");
                        }*/
                                fdShippingAccoundetail.setConsignorName(row.getCell(4).getStringCellValue().replaceAll(" ", ""));
                            } else {
                                new R<>(500, Boolean.FALSE, null, "收货人为空");
                            }
                        } else {
                            new R<>(500, Boolean.FALSE, null, "收货人为空");
                        }

                        //发站
                        if (row.getCell(5) != null) {
                            row.getCell(5).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(5).getStringCellValue()) && row.getCell(5).getStringCellValue() != null) {
                        /*if(row.getCell(5).getStringCellValue().contains(" ")){
                            new R<>(500,Boolean.FALSE,null, "发站包含空格，请检查数据！");
                        }*/
                                fdShippingAccoundetail.setDestinationName(row.getCell(5).getStringCellValue().replaceAll(" ", ""));
                            } else {
                                new R<>(500, Boolean.FALSE, null, "发站为空");
                            }
                        } else {
                            new R<>(500, Boolean.FALSE, null, "发站为空");
                        }

                        //目的国
                        if (row.getCell(6) != null) {
                            row.getCell(6).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(6).getStringCellValue()) && row.getCell(6).getStringCellValue() != null) {
                        /*if(row.getCell(6).getStringCellValue().contains(" ")){
                            new R<>(500,Boolean.FALSE,null, "目的国包含空格，请检查数据！");
                        }*/
                                fdShippingAccoundetail.setDestinationCountry(row.getCell(6).getStringCellValue().replaceAll(" ", ""));
                            } else {
                                new R<>(500, Boolean.FALSE, null, "目的国为空");
                            }
                        } else {
                            new R<>(500, Boolean.FALSE, null, "目的国为空");
                        }

                        //到站
                        if (row.getCell(7) != null) {
                            row.getCell(7).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(7).getStringCellValue()) && row.getCell(7).getStringCellValue() != null) {
                        /*if(row.getCell(7).getStringCellValue().contains(" ")){
                            new R<>(500,Boolean.FALSE,null, "到站包含空格，请检查数据！");
                        }*/
                                fdShippingAccoundetail.setDestination(row.getCell(7).getStringCellValue().replaceAll(" ", ""));
                            } else {
                                new R<>(500, Boolean.FALSE, null, "到站为空");
                            }
                        } else {
                            new R<>(500, Boolean.FALSE, null, "到站为空");
                        }

                        //口岸代理
                        if (row.getCell(8) != null) {
                            row.getCell(8).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(8).getStringCellValue()) && row.getCell(8).getStringCellValue() != null) {
                        /*if(row.getCell(8).getStringCellValue().contains(" ")){
                            new R<>(500,Boolean.FALSE,null, "口岸代理包含空格，请检查数据！");
                        }*/
                                fdShippingAccoundetail.setPortAgent(row.getCell(8).getStringCellValue().replaceAll(" ", ""));
                            } else {
                                new R<>(500, Boolean.FALSE, null, "口岸代理为空");
                            }
                        } else {
                            new R<>(500, Boolean.FALSE, null, "口岸代理为空");
                        }

                        //品名
                        if (row.getCell(9) != null) {
                            row.getCell(9).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(9).getStringCellValue()) && row.getCell(9).getStringCellValue() != null) {
                        /*if(row.getCell(9).getStringCellValue().contains(" ")){
                            new R<>(500,Boolean.FALSE,null, "品名包含空格，请检查数据！");
                        }*/
                                fdShippingAccoundetail.setGoodsName(row.getCell(9).getStringCellValue().replaceAll(" ", ""));
                            } else {
                                new R<>(500, Boolean.FALSE, null, "品名为空");
                            }
                        } else {
                            new R<>(500, Boolean.FALSE, null, "品名为空");
                        }

                        //箱属
                        if (row.getCell(10) != null) {
                            row.getCell(10).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(10).getStringCellValue()) && row.getCell(10).getStringCellValue() != null) {
                       /* if(row.getCell(10).getStringCellValue().contains(" ")){
                            new R<>(500,Boolean.FALSE,null, "箱属包含空格，请检查数据！");
                        }*/
                                if ("自备箱".equals(row.getCell(10).getStringCellValue())) {
                                    fdShippingAccoundetail.setContainerOwner("0");
                                } else if ("中铁箱".equals(row.getCell(10).getStringCellValue())) {
                                    fdShippingAccoundetail.setContainerOwner("1");
                                }
                            } else {
                                new R<>(500, Boolean.FALSE, null, "箱属为空");
                            }
                        } else {
                            new R<>(500, Boolean.FALSE, null, "箱属为空");
                        }

                        //箱型
                        if (row.getCell(11) != null) {
                            row.getCell(11).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(11).getStringCellValue()) && row.getCell(11).getStringCellValue() != null) {
                        /*if(row.getCell(11).getStringCellValue().contains(" ")){
                            new R<>(500,Boolean.FALSE,null, "箱型包含空格，请检查数据！");
                        }*/
//                            fdShippingAccoundetail.setContainerType(row.getCell(11).getStringCellValue().replaceAll(" ", ""));
                                final String containerTypeCode = row.getCell(11).getStringCellValue().replaceAll(" ", "");
                                Boolean flag = true;
                                for (ContainerTypeData data : containerTypeDataList) {
                                    if (data.getContainerTypeCode().equals(containerTypeCode)) {
                                        fdShippingAccoundetail.setContainerTypeCode(data.getContainerTypeCode());
                                        fdShippingAccoundetail.setContainerTypeName(data.getContainerTypeName());
                                        fdShippingAccoundetail.setContainerType(data.getContainerTypeSize());
                                        flag = false;
                                        break;
                                    }
                                }

                                if (flag) {
                                    new R<>(500, Boolean.FALSE, null, "未查询到该箱型代码：" + containerTypeCode);
                                }
                            } else {
                                new R<>(500, Boolean.FALSE, null, "箱型代码为空");
                            }
                        } else {
                            new R<>(500, Boolean.FALSE, null, "箱型代码为空");
                        }

                        //件数
                        if (row.getCell(12) != null) {
                            row.getCell(12).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(12).getStringCellValue()) && row.getCell(12).getStringCellValue() != null) {
                        /*if(row.getCell(12).getStringCellValue().contains(" ")){
                            new R<>(500,Boolean.FALSE,null, "件数包含空格，请检查数据！");
                        }*/
                                fdShippingAccoundetail.setGoodsNums(Integer.valueOf(row.getCell(12).getStringCellValue().replaceAll(" ", "")));
                            } else {
                                new R<>(500, Boolean.FALSE, null, "件数为空");
                            }
                        } else {
                            new R<>(500, Boolean.FALSE, null, "件数为空");
                        }

                        //货重
                        if (row.getCell(13) != null) {
                            row.getCell(13).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(13).getStringCellValue()) && row.getCell(13).getStringCellValue() != null) {
                        /*if(row.getCell(13).getStringCellValue().contains(" ")){
                            new R<>(500,Boolean.FALSE,null, "货重包含空格，请检查数据！");
                        }*/
                                fdShippingAccoundetail.setGoodsWeight(Double.valueOf(row.getCell(13).getStringCellValue().replaceAll(" ", "")));
                            } else {
                                new R<>(500, Boolean.FALSE, null, "货重为空");
                            }
                        } else {
                            new R<>(500, Boolean.FALSE, null, "货重为空");
                        }

                        //箱重
                        if (row.getCell(14) != null) {
                            row.getCell(14).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(14).getStringCellValue()) && row.getCell(14).getStringCellValue() != null) {
                        /*if(row.getCell(14).getStringCellValue().contains(" ")){
                            new R<>(500,Boolean.FALSE,null, "箱重包含空格，请检查数据！");
                        }*/
                                fdShippingAccoundetail.setContainerWeight(row.getCell(14).getStringCellValue().replaceAll(" ", ""));
                            } else {
                                new R<>(500, Boolean.FALSE, null, "箱重为空");
                            }
                        } else {
                            new R<>(500, Boolean.FALSE, null, "箱重为空");
                        }

                        //是否全程
                        if (row.getCell(15) != null) {
                            row.getCell(15).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(15).getStringCellValue()) && row.getCell(15).getStringCellValue() != null) {
                       /* if(row.getCell(15).getStringCellValue().contains(" ")){
                            new R<>(500,Boolean.FALSE,null, "是否全程包含空格，请检查数据！");
                        }*/
                                if ("否".equals(row.getCell(15).getStringCellValue())) {
                                    fdShippingAccoundetail.setIsFull("0");
                                } else if ("是".equals(row.getCell(15).getStringCellValue())) {
                                    fdShippingAccoundetail.setIsFull("1");
                                }
                            } else {
                                new R<>(500, Boolean.FALSE, null, "是否全程为空");
                            }
                        } else {
                            new R<>(500, Boolean.FALSE, null, "是否全程为空");
                        }

                        //有色金属
                        if (row.getCell(16) != null) {
                            row.getCell(16).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(16).getStringCellValue()) && row.getCell(16).getStringCellValue() != null) {
                        /*if(row.getCell(16).getStringCellValue().contains(" ")){
                            new R<>(500,Boolean.FALSE,null, "有色金属包含空格，请检查数据！");
                        }*/
                                if ("否".equals(row.getCell(16).getStringCellValue())) {
                                    fdShippingAccoundetail.setNonFerrous("0");
                                } else if ("是".equals(row.getCell(16).getStringCellValue())) {
                                    fdShippingAccoundetail.setNonFerrous("1");
                                }
                            } else {
                                new R<>(500, Boolean.FALSE, null, "有色金属为空");
                            }
                        } else {
                            new R<>(500, Boolean.FALSE, null, "有色金属为空");
                        }

                /*//状态
                row.getCell(17).setCellType(CellType.STRING);
                if (!"".equals(row.getCell(17).getStringCellValue())  && row.getCell(17).getStringCellValue() != null) {
                    if(row.getCell(17).getStringCellValue().equals("新增")) {
                        fdShippingAccoundetail.setContainerNewestStatus("0");
                    }else if(row.getCell(17).getStringCellValue().equals("撤舱")){
                        fdShippingAccoundetail.setContainerNewestStatus("1");
                    }
                }
*/
                        //货主
                        if (row.getCell(17) != null) {
                            row.getCell(17).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(17).getStringCellValue()) && row.getCell(17).getStringCellValue() != null) {
                           /* if(row.getCell(17).getStringCellValue().contains(" ")){
                                new R<>(500,Boolean.FALSE,null, "货主包含空格，请检查数据！");
                            }*/
                                fdShippingAccoundetail.setGoodsOwner(row.getCell(17).getStringCellValue().replaceAll(" ", ""));
                            }
                        }

                        //境内货源地/目的地
                        if (row.getCell(18) != null) {
                            row.getCell(18).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(18).getStringCellValue()) && row.getCell(18).getStringCellValue() != null) {
                            /*if(row.getCell(18).getStringCellValue().contains(" ")){
                                new R<>(500,Boolean.FALSE,null, "内货源地/目的地包含空格，请检查数据！");
                            }*/
                                fdShippingAccoundetail.setGoodsOrigin(row.getCell(18).getStringCellValue().replaceAll(" ", ""));
                            }
                        }

                        //报关单号
                        if (row.getCell(19) != null) {
                            row.getCell(19).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(19).getStringCellValue()) && row.getCell(19).getStringCellValue() != null) {
                            /*if(row.getCell(19).getStringCellValue().contains(" ")){
                                new R<>(500,Boolean.FALSE,null, "报关单号包含空格，请检查数据！");
                            }*/
                                fdShippingAccoundetail.setClearanceNumber(row.getCell(19).getStringCellValue().replaceAll(" ", ""));
                            }
                        }
                        //货值(美金)
                        if (row.getCell(20) != null) {
                            row.getCell(20).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(20).getStringCellValue()) && row.getCell(20).getStringCellValue() != null) {
                           /* if(row.getCell(20).getStringCellValue().contains(" ")){
                                new R<>(500,Boolean.FALSE,null, "货值(美金)包含空格，请检查数据！");
                            }*/
                                if (isNumber(row.getCell(20).getStringCellValue())) {
                                    new R<>(500, Boolean.FALSE, null, "货值(美金)必须为数值并且保留两位小数，请检查数据！");
                                }
                                fdShippingAccoundetail.setValueUsd(BigDecimal.valueOf(Double.parseDouble(row.getCell(20).getStringCellValue().replaceAll(" ", ""))));
                            }
                        }

                        //海关封
                        if (row.getCell(21) != null) {
                            row.getCell(21).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(21).getStringCellValue()) && row.getCell(21).getStringCellValue() != null) {
                            /*if(row.getCell(21).getStringCellValue().contains(" ")){
                                new R<>(500,Boolean.FALSE,null, "海关封包含空格，请检查数据！");
                            }*/
                                fdShippingAccoundetail.setCustomsSeal(row.getCell(21).getStringCellValue().replaceAll(" ", ""));
                            }
                        }

                        //车号
                        if (row.getCell(22) != null) {
                            row.getCell(22).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(22).getStringCellValue()) && row.getCell(22).getStringCellValue() != null) {
                            /*if(row.getCell(22).getStringCellValue().contains(" ")){
                                new R<>(500,Boolean.FALSE,null, "车号包含空格，请检查数据！");
                            }*/
                                fdShippingAccoundetail.setTrainNumber(row.getCell(22).getStringCellValue().replaceAll(" ", ""));
                            }
                        }

                        //订单需求号
                        if (row.getCell(23) != null) {
                            row.getCell(23).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(23).getStringCellValue()) && row.getCell(23).getStringCellValue() != null) {
                           /* if(row.getCell(23).getStringCellValue().contains(" ")){
                                new R<>(500,Boolean.FALSE,null, "订单需求号包含空格，请检查数据！");
                            }*/
                                fdShippingAccoundetail.setWaybillDemandNumber(row.getCell(23).getStringCellValue().replaceAll(" ", ""));
                            }
                        }

                        //国联订单号
                        if (row.getCell(24) != null) {
                            row.getCell(24).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(24).getStringCellValue()) && row.getCell(24).getStringCellValue() != null) {
                            /*if(row.getCell(24).getStringCellValue().contains(" ")){
                                new R<>(500,Boolean.FALSE,null, "国联订单号包含空格，请检查数据！");
                            }*/
                                fdShippingAccoundetail.setWaybillLnNumber(row.getCell(24).getStringCellValue().replaceAll(" ", ""));
                            }
                        }

                        //境内运费(人民币)
                        if (row.getCell(25) != null) {
                            row.getCell(25).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(25).getStringCellValue()) && row.getCell(25).getStringCellValue() != null) {
                            /*if(row.getCell(25).getStringCellValue().contains(" ")){
                                new R<>(500,Boolean.FALSE,null, "境内运费(人民币)包含空格，请检查数据！");
                            }*/
                                if (isNumber(row.getCell(25).getStringCellValue())) {
                                    new R<>(500, Boolean.FALSE, null, "境内运费(人民币)必须为数值并且保留两位小数，请检查数据！");
                                }
                                fdShippingAccoundetail.setDomesticFreight(BigDecimal.valueOf(Double.parseDouble(row.getCell(25).getStringCellValue().replaceAll(" ", ""))));
                            } else {
                                fdShippingAccoundetail.setDomesticFreight(BigDecimal.valueOf(0));
                            }
                        } else {
                            fdShippingAccoundetail.setDomesticFreight(BigDecimal.valueOf(0));
                        }

                        //境外运费(人民币)
                        if (row.getCell(26) != null) {
                            row.getCell(26).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(26).getStringCellValue()) && row.getCell(26).getStringCellValue() != null) {
                            /*if(row.getCell(26).getStringCellValue().contains(" ")){
                                new R<>(500,Boolean.FALSE,null, "境外运费(人民币)包含空格，请检查数据！");
                            }*/
                                if (isNumber(row.getCell(26).getStringCellValue())) {
                                    new R<>(500, Boolean.FALSE, null, "境外运费(人民币)必须为数值并且保留两位小数，请检查数据！");
                                }
                                fdShippingAccoundetail.setOverseasFreightCny(BigDecimal.valueOf(Double.parseDouble(row.getCell(26).getStringCellValue().replaceAll(" ", ""))));
                            } else {
                                fdShippingAccoundetail.setOverseasFreightCny(BigDecimal.valueOf(0));
                            }
                        } else {
                            fdShippingAccoundetail.setOverseasFreightCny(BigDecimal.valueOf(0));
                        }

                        //境外运费(原币)
//                    if(row.getCell(26)!=null) {
//                        row.getCell(26).setCellType(CellType.STRING);
//                        if (!"".equals(row.getCell(26).getStringCellValue()) && row.getCell(26).getStringCellValue() != null) {
//                            /*if(row.getCell(27).getStringCellValue().contains(" ")){
//                                new R<>(500,Boolean.FALSE,null, "境外运费(原币)包含空格，请检查数据！");
//                            }*/
//                            if(isNumber(row.getCell(26).getStringCellValue())){
//                                new R<>(500,Boolean.FALSE,null, "境外运费(原币)必须为数值并且保留两位小数，请检查数据！");
//                            }
//                            fdShippingAccoundetail.setOverseasFreightOc(BigDecimal.valueOf(row.getCell(26).getStringCellValue().replaceAll(" ","")));
//                        }
//                    }

                        //境外币种
                        if (row.getCell(27) != null) {
                            row.getCell(27).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(27).getStringCellValue()) && row.getCell(27).getStringCellValue() != null) {
                            /*if(row.getCell(28).getStringCellValue().contains(" ")){
                                new R<>(500,Boolean.FALSE,null, "境外币种包含空格，请检查数据！");
                            }*/
                                fdShippingAccoundetail.setMonetaryType(row.getCell(27).getStringCellValue().replaceAll(" ", ""));
                            }
                        }

                        //补贴标准
                        if (row.getCell(28) != null) {
                            row.getCell(28).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(28).getStringCellValue()) && row.getCell(28).getStringCellValue() != null) {
                            /*if(row.getCell(29).getStringCellValue().contains(" ")){
                                new R<>(500,Boolean.FALSE,null, "补贴标准包含空格，请检查数据！");
                            }*/
                                fdShippingAccoundetail.setSubsidyStandards(row.getCell(28).getStringCellValue().replaceAll(" ", ""));
                            }
                        }

                        //补贴金额
                        if (row.getCell(29) != null) {
                            row.getCell(29).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(29).getStringCellValue()) && row.getCell(29).getStringCellValue() != null) {
                           /* if(row.getCell(30).getStringCellValue().contains(" ")){
                                new R<>(500,Boolean.FALSE,null, "补贴金额包含空格，请检查数据！");
                            }*/
                                if (isNumber(row.getCell(29).getStringCellValue())) {
                                    new R<>(500, Boolean.FALSE, null, "补贴金额必须为数值并且保留两位小数，请检查数据！");
                                }
                                fdShippingAccoundetail.setSubsidyAmount(BigDecimal.valueOf(Double.parseDouble(row.getCell(29).getStringCellValue().replaceAll(" ", ""))));
                            }
                        }

                        //箱备注
                        if (row.getCell(30) != null) {
                            row.getCell(30).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(30).getStringCellValue()) && row.getCell(30).getStringCellValue() != null) {
                           /* if(row.getCell(31).getStringCellValue().contains(" ")){
                                new R<>(500,Boolean.FALSE,null, "箱备注包含空格，请检查数据！");
                            }*/
                                fdShippingAccoundetail.setRemarks(row.getCell(30).getStringCellValue().replaceAll(" ", ""));
                            }
                        }

                        //新增箱所属客户编码
                        if (row.getCell(31) != null) {
                            row.getCell(31).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(31).getStringCellValue()) && row.getCell(31).getStringCellValue() != null) {
                                fdShippingAccoundetail.setBookingUserCode(row.getCell(31).getStringCellValue().replaceAll(" ", ""));
                            }
                        }

                        fdShippingAccoundetail.setExchangeRate(fdShippingAccounts.get(0).getExchangeRate());
                        fdShippingAccoundetail.setCustomerNo(fdShippingAccounts.get(0).getCustomerNo());
                        fdShippingAccoundetail.setCustomerName(fdShippingAccounts.get(0).getCustomerName());
                        WaybillHeader wh = new WaybillHeader();
                        wh.setWaybillNo(fdShippingAccoundetail.getTransportOrderNumber());
                        wh.setDeleteFlag("N");
                        final List<WaybillHeader> waybillHeaders = waybillHeaderService.selectWaybillHeaderList(wh);
                        if (CollUtil.isNotEmpty(waybillHeaders)) {
                            fdShippingAccoundetail.setApplicationNumber(waybillHeaders.get(0).getOrderNo());
                        } else {
                            return new R<>(500, Boolean.FALSE, null, "该运单号不存在：" + fdShippingAccoundetail.getTransportOrderNumber());
                        }

                        if ("20".equals(fdShippingAccoundetail.getContainerType())) {
                            num3 += 0.5;
                        } else if ("40".equals(fdShippingAccoundetail.getContainerType()) || "45".equals(fdShippingAccoundetail.getContainerType())) {
                            num3 += 1;
                        }
                        fdShippingAccoundetail.setAccountCode(accountCode);
                        fdShippingAccoundetail.setDeleteFlag("N");
                        fdShippingAccoundetail.setAddTime(LocalDateTime.now());
                        fdShippingAccoundetail.setAddWho(userInfo.getUserName());
                        fdShippingAccoundetail.setAddWhoName(userInfo.getRealName());

                        //状态先设置为新增
                        fdShippingAccoundetail.setContainerNewestStatus("0");
                        fdShippingAccoundetail.setContainerStatus("0");

                        addList.add(fdShippingAccoundetail);

                    }
                }

                //校验舱位数是否足够
                if (StrUtil.isNotEmpty(num1) && StrUtil.isNotEmpty(num2)) {
                    final Double d1 = Double.valueOf(num1);
                    final Double d2 = Double.valueOf(num2);
                    if (num3 - d2 > d1) {
                        return new R<>(500, Boolean.FALSE, null, "舱位数不足，不能完成二次导入！");
                    }
                } else {
                    return new R<>(500, Boolean.FALSE, null, "舱位数异常，请联系管理员！");
                }

                //对比原有台账数据，确认现在数据状态

                FdShippingAccoundetail sel3 = new FdShippingAccoundetail();
                sel3.setAccountCode(accountCode);
                sel3.setDeleteFlag("N");
                //需要删除的原有数据
                final List<FdShippingAccoundetail> deleteList = fdShippingAccoundetailService.selectFdShippingAccoundetailList(sel3);

                //相同箱子的原有数据
                List<FdShippingAccoundetailSub> updateList = new ArrayList<>();
                List<Integer> indexesToRemoveFromAddList = new ArrayList<>();
                List<Integer> indexesToRemoveFromDeleteList = new ArrayList<>();

                if (CollUtil.isNotEmpty(addList) && CollUtil.isNotEmpty(deleteList)) {
                    for (int x = 0; x < addList.size(); x++) {
                        for (int y = 0; y < deleteList.size(); y++) {
                            if (addList.get(x).getContainerNumber().equals(deleteList.get(y).getContainerNumber())) {
                                final FdShippingAccoundetailSub fdShippingAccoundetailSub = addList.get(x);
                                fdShippingAccoundetailSub.setContainerNewestStatus(null);
                                fdShippingAccoundetailSub.setContainerStatus(null);

                                addList.get(x).setRrDomesticFreight(deleteList.get(y).getRrDomesticFreight());
                                addList.get(x).setRrOverseasFreightCny(deleteList.get(y).getRrOverseasFreightCny());
                                addList.get(x).setRrOverseasFreightOc(deleteList.get(y).getRrOverseasFreightOc());

                                updateList.add(fdShippingAccoundetailSub);

                                indexesToRemoveFromAddList.add(x);
                                indexesToRemoveFromDeleteList.add(y);
                            }
                        }
                    }
                }

                // 从大到小逆序删除 addList 中的元素
                if (CollUtil.isNotEmpty(indexesToRemoveFromAddList)) {
                    for (int i = indexesToRemoveFromAddList.size() - 1; i >= 0; i--) {
                        int index = indexesToRemoveFromAddList.get(i);
                        addList.remove(index);
                    }
                }


                if (CollUtil.isNotEmpty(indexesToRemoveFromDeleteList)) {
                    // 从大到小逆序删除 deleteList 中的元素
                    for (int i = indexesToRemoveFromDeleteList.size() - 1; i >= 0; i--) {
                        int index = indexesToRemoveFromDeleteList.get(i);
                        deleteList.remove(index);
                    }
                }

                //集合三个list数据，返回前端
                List<FdShippingAccoundetailSub> list = new ArrayList<>();
                if (CollUtil.isNotEmpty(addList)) {
                    for (FdShippingAccoundetailSub fdShippingAccoundetailSub : addList) {
                        if (StrUtil.isEmpty(fdShippingAccoundetailSub.getBookingUserCode())) {
                            return new R<>(500, Boolean.FALSE, null, "新增箱号必填填写订舱用户编码：" + fdShippingAccoundetailSub.getContainerNumber());
                        }
                        list.add(fdShippingAccoundetailSub);
                    }
                }
                if (CollUtil.isNotEmpty(updateList)) {
                    for (FdShippingAccoundetailSub fdShippingAccoundetailSub : updateList) {
                        list.add(fdShippingAccoundetailSub);
                    }
                }

                if (CollUtil.isNotEmpty(deleteList)) {
                    for (FdShippingAccoundetail fdShippingAccoundetail : deleteList) {
                        final FdShippingAccoundetailSub fdShippingAccoundetailSub = convertObj(fdShippingAccoundetail);
                        fdShippingAccoundetailSub.setContainerStatus("1");
                        fdShippingAccoundetailSub.setContainerNewestStatus("1");
                        fdShippingAccoundetailSub.setAddWho(userInfo.getUserName());
                        fdShippingAccoundetailSub.setAddWhoName(userInfo.getRealName());
                        list.add(fdShippingAccoundetailSub);
                    }
                }

                return new R<>(200, Boolean.TRUE, list, "导入完成！");
            } else {
                return new R<>(500, Boolean.FALSE, null, "表格为空！");
            }
        } else {
            return new R<>(500, Boolean.FALSE, null, "该台账不存在！");
        }
    }

    //对象转换
    public static FdShippingAccoundetailSub convertObj(FdShippingAccoundetail fdShippingAccoundetail) {
        FdShippingAccoundetailSub fdShippingAccoundetailSub = new FdShippingAccoundetailSub();
        fdShippingAccoundetailSub.setAccountCode(fdShippingAccoundetail.getAccountCode());
        fdShippingAccoundetailSub.setCustomerNo(fdShippingAccoundetail.getCustomerNo());
        fdShippingAccoundetailSub.setCustomerName(fdShippingAccoundetail.getCustomerName());
        fdShippingAccoundetailSub.setApplicationNumber(fdShippingAccoundetail.getApplicationNumber());
        fdShippingAccoundetailSub.setTransportOrderNumber(fdShippingAccoundetail.getTransportOrderNumber());
        fdShippingAccoundetailSub.setContainerNumber(fdShippingAccoundetail.getContainerNumber());
        fdShippingAccoundetailSub.setActualTrainNumber(fdShippingAccoundetail.getActualTrainNumber());
        fdShippingAccoundetailSub.setClearanceNumber(fdShippingAccoundetail.getClearanceNumber());
        fdShippingAccoundetailSub.setValueUsd(fdShippingAccoundetail.getValueUsd());
        fdShippingAccoundetailSub.setCustomsSeal(fdShippingAccoundetail.getCustomsSeal());
        fdShippingAccoundetailSub.setTrainNumber(fdShippingAccoundetail.getTrainNumber());
        fdShippingAccoundetailSub.setWaybillDemandNumber(fdShippingAccoundetail.getWaybillDemandNumber());
        fdShippingAccoundetailSub.setWaybillLnNumber(fdShippingAccoundetail.getWaybillLnNumber());
        fdShippingAccoundetailSub.setSubsidyStandards(fdShippingAccoundetail.getSubsidyStandards());
        fdShippingAccoundetailSub.setSubsidyAmount(fdShippingAccoundetail.getSubsidyAmount());
        fdShippingAccoundetailSub.setMonetaryType(fdShippingAccoundetail.getMonetaryType());
        fdShippingAccoundetailSub.setExchangeRate(fdShippingAccoundetail.getExchangeRate());
        fdShippingAccoundetailSub.setOverseasFreightOc(fdShippingAccoundetail.getOverseasFreightOc());
        fdShippingAccoundetailSub.setOverseasFreightCny(fdShippingAccoundetail.getOverseasFreightCny());
        fdShippingAccoundetailSub.setDomesticFreight(fdShippingAccoundetail.getDomesticFreight());
        fdShippingAccoundetailSub.setShippingFreight(fdShippingAccoundetail.getShippingFreight());
        fdShippingAccoundetailSub.setRemarks(fdShippingAccoundetail.getRemarks());
        fdShippingAccoundetailSub.setDeleteFlag("N");
        fdShippingAccoundetailSub.setAddTime(LocalDateTime.now());
        fdShippingAccoundetailSub.setIsRansit(fdShippingAccoundetail.getIsRansit());
        fdShippingAccoundetailSub.setOrgUnit(fdShippingAccoundetail.getOrgUnit());
        fdShippingAccoundetailSub.setDestinationName(fdShippingAccoundetail.getDestinationName());
        fdShippingAccoundetailSub.setDestination(fdShippingAccoundetail.getDestination());
        fdShippingAccoundetailSub.setContainerNo(fdShippingAccoundetail.getContainerNo());
        fdShippingAccoundetailSub.setGoodsName(fdShippingAccoundetail.getGoodsName());
        fdShippingAccoundetailSub.setGoodsNums(fdShippingAccoundetail.getGoodsNums());
        fdShippingAccoundetailSub.setGoodsWeight(fdShippingAccoundetail.getGoodsWeight());
        fdShippingAccoundetailSub.setContainerType(fdShippingAccoundetail.getContainerType());
        fdShippingAccoundetailSub.setConsignorName(fdShippingAccoundetail.getConsignorName());
        fdShippingAccoundetailSub.setDestinationCountry(fdShippingAccoundetail.getDestinationCountry());
        fdShippingAccoundetailSub.setIsFull(fdShippingAccoundetail.getIsFull());
        fdShippingAccoundetailSub.setNonFerrous(fdShippingAccoundetail.getNonFerrous());
        fdShippingAccoundetailSub.setGoodsOrigin(fdShippingAccoundetail.getGoodsOrigin());
        fdShippingAccoundetailSub.setContainerWeight(fdShippingAccoundetail.getContainerWeight());
        fdShippingAccoundetailSub.setGoodsOwner(fdShippingAccoundetail.getGoodsOwner());
        fdShippingAccoundetailSub.setPortAgent(fdShippingAccoundetail.getPortAgent());
        fdShippingAccoundetailSub.setDestinationCountryCode(fdShippingAccoundetail.getDestinationCountryCode());
        fdShippingAccoundetailSub.setContainerOwner(fdShippingAccoundetail.getContainerOwner());
        fdShippingAccoundetailSub.setResveredField07(fdShippingAccoundetail.getResveredField07());
        fdShippingAccoundetailSub.setContainerTypeCode(fdShippingAccoundetail.getContainerTypeCode());
        fdShippingAccoundetailSub.setContainerTypeName(fdShippingAccoundetail.getContainerTypeName());
        fdShippingAccoundetailSub.setResveredField10(fdShippingAccoundetail.getResveredField10());
        fdShippingAccoundetailSub.setRrOverseasFreightOc(fdShippingAccoundetail.getRrOverseasFreightOc());
        fdShippingAccoundetailSub.setRrOverseasFreightCny(fdShippingAccoundetail.getRrOverseasFreightCny());
        fdShippingAccoundetailSub.setRrDomesticFreight(fdShippingAccoundetail.getRrDomesticFreight());
        return fdShippingAccoundetailSub;
    }

    public static boolean isNumber(String number) {
        String reg_money = "\\d+(\\.\\d{1,2})?";
        Pattern pattern = Pattern.compile(reg_money);
        Matcher matcher = pattern.matcher(number);
        boolean b = matcher.matches();
        return !b;
    }

    /**
     * 台账撤回
     *
     * @param rowId
     * @return R
     */
    @GetMapping("/cancel/{rowId}")
    public R cancel(@PathVariable("rowId") Long rowId) {
        return fdShippingAccountService.cancel(rowId);
    }

    /**
     * 多联数据对比
     *
     * @param params
     * @return
     */
    @GetMapping("/selectAccountByDL")
    public R selectAccountByDL(@RequestParam Map<String, Object> params) {
        return fdShippingAccountService.selectAccountByDL(params);
    }

    /**
     * 导出模板-直接导入并生成台账（潍坊）
     *
     * @return
     */
    @PostMapping("/exportTemplateForTz")
    public void exportTemplateForTz(HttpServletResponse response) throws Exception {
        fdShippingAccountService.exportTemplateForTz(response);
    }

    /**
     * 导入并生成台账（潍坊）
     *
     * @return
     */
    @PostMapping("/importedTemplateForTz")
    @Transactional(rollbackFor = Exception.class)
    public R importedTemplateForTz(@RequestParam("file") MultipartFile file) throws Exception {
        return fdShippingAccountService.importedTemplateForTz(file);
    }

    /**
     * 新增台账时校验
     *
     * @return
     */
    @PostMapping("/tzCommitCheck")
    public R tzCommitCheck(@RequestBody FdShippingAccount fdShippingAccount) {
        return fdShippingAccountService.tzCommitCheck(fdShippingAccount);
    }

    /**
     * 发行列数合计
     *
     * @return
     */
    @PostMapping("/selectShipNum")
    public R selectShipNum() {
        return fdShippingAccountService.selectShipNum();
    }

    /**
     * 发行列数详情
     *
     * @return
     */
    @PostMapping("/selectShipNumDetail")
    public R selectShipNumDetail(@RequestBody FdShippingNumDTO fdShippingNumDTO) {
        return fdShippingAccountService.selectShipNumDetail(fdShippingNumDTO);
    }

    /**
     * 半年发运趋势
     *
     * @return
     */
    @PostMapping("/selectShipNumHalfYear")
    public R selectShipNumHalfYear() {
        return fdShippingAccountService.selectShipNumHalfYear();
    }

    /**
     * 本月各市平台经营概览
     *
     * @return
     */
    @PostMapping("/selectMonthCity")
    public R selectMonthCity() throws Exception {
        return fdShippingAccountService.selectMonthCity();
    }

    /**
     * 市平台本月经营概览
     *
     * @return
     */
    @PostMapping("/selectMonthThisCity")
    public R selectMonthThisCity() throws Exception {
        return fdShippingAccountService.selectMonthThisCity();
    }

    /**
     * 成本台账导出
     *
     * @param costLedgerDetails 成本台账导出参数
     */
    @PostMapping("/costLedgerExport")
    public void costLedgerExport(@RequestBody FdInvoiceCostLedgerDetailsVO costLedgerDetails, HttpServletResponse response) {
        fdInvoiceCostLedgerService.costLedgerExport(costLedgerDetails, response);
    }

    /**
     * auditStatus2废弃
     * 调用auditStatus3
     */
    @PostMapping("/auditOfProvincialPlatform")
    public R auditOfProvincialPlatform(@RequestBody FdShippingAccount vo) throws Exception {
        if (vo.getRowId() == null) {
            return R.error("主键id不能为空");
        }
        if (StrUtil.isEmpty(vo.getStatus())) {
            return R.error("状态不能为空");
        }
        return fdShippingAccountAuditService.auditOfProvincialPlatform(vo);
    }
}
