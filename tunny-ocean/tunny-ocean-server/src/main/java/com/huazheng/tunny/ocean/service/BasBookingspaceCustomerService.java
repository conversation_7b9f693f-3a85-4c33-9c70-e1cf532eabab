package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.BasBookingspaceCustomer;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 订舱客户信息表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-01 10:37:32
 */
public interface BasBookingspaceCustomerService extends IService<BasBookingspaceCustomer> {
    /**
     * 查询订舱客户信息表信息
     *
     * @param rowId 订舱客户信息表ID
     * @return 订舱客户信息表信息
     */
    public BasBookingspaceCustomer selectBasBookingspaceCustomerById(String rowId);

    /**
     * 查询订舱客户信息表列表
     *
     * @param basBookingspaceCustomer 订舱客户信息表信息
     * @return 订舱客户信息表集合
     */
    public List<BasBookingspaceCustomer> selectBasBookingspaceCustomerList(BasBookingspaceCustomer basBookingspaceCustomer);


    /**
     * 分页模糊查询订舱客户信息表列表
     * @return 订舱客户信息表集合
     */
    public Page selectBasBookingspaceCustomerListByLike(Query query);



    /**
     * 新增订舱客户信息表
     *
     * @param basBookingspaceCustomer 订舱客户信息表信息
     * @return 结果
     */
    public int insertBasBookingspaceCustomer(BasBookingspaceCustomer basBookingspaceCustomer);

    /**
     * 修改订舱客户信息表
     *
     * @param basBookingspaceCustomer 订舱客户信息表信息
     * @return 结果
     */
    public int updateBasBookingspaceCustomer(BasBookingspaceCustomer basBookingspaceCustomer);

    /**
     * 删除订舱客户信息表
     *
     * @param rowId 订舱客户信息表ID
     * @return 结果
     */
    public int deleteBasBookingspaceCustomerById(String rowId);

    /**
     * 批量删除订舱客户信息表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteBasBookingspaceCustomerByIds(Integer[] rowIds);

}

