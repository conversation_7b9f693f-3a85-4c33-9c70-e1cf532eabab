package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.BillPayProvinceSub;
import com.huazheng.tunny.ocean.api.vo.BillDealWithCityAndCostVO;
import com.huazheng.tunny.ocean.mapper.BillPayProvinceSubMapper;
import com.huazheng.tunny.ocean.service.BillPayProvinceSubService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("billPayProvinceSubService")
public class BillPayProvinceSubServiceImpl extends ServiceImpl<BillPayProvinceSubMapper, BillPayProvinceSub> implements BillPayProvinceSubService {

    @Autowired
    private BillPayProvinceSubMapper billPayProvinceSubMapper;

    /**
     * 查询应付账单（省平台）子账单表信息
     *
     * @param id 应付账单（省平台）子账单表ID
     * @return 应付账单（省平台）子账单表信息
     */
    @Override
    public BillPayProvinceSub selectBillPayProvinceSubById(Integer id)
    {
        return billPayProvinceSubMapper.selectBillPayProvinceSubById(id);
    }

    /**
     * 查询应付账单（省平台）子账单表列表
     *
     * @param billPayProvinceSub 应付账单（省平台）子账单表信息
     * @return 应付账单（省平台）子账单表集合
     */
    @Override
    public List<BillPayProvinceSub> selectBillPayProvinceSubList(BillPayProvinceSub billPayProvinceSub)
    {
        return billPayProvinceSubMapper.selectBillPayProvinceSubList(billPayProvinceSub);
    }


    /**
     * 分页模糊查询应付账单（省平台）子账单表列表
     * @return 应付账单（省平台）子账单表集合
     */
    @Override
    public Page selectBillPayProvinceSubListByLike(Query query)
    {
        BillPayProvinceSub billPayProvinceSub =  BeanUtil.mapToBean(query.getCondition(), BillPayProvinceSub.class,false);
        query.setRecords(billPayProvinceSubMapper.selectBillPayProvinceSubListByLike(query,billPayProvinceSub));
        return query;
    }

    /**
     * 新增应付账单（省平台）子账单表
     *
     * @param billPayProvinceSub 应付账单（省平台）子账单表信息
     * @return 结果
     */
    @Override
    public int insertBillPayProvinceSub(BillPayProvinceSub billPayProvinceSub)
    {
        return billPayProvinceSubMapper.insertBillPayProvinceSub(billPayProvinceSub);
    }

    /**
     * 修改应付账单（省平台）子账单表
     *
     * @param billPayProvinceSub 应付账单（省平台）子账单表信息
     * @return 结果
     */
    @Override
    public int updateBillPayProvinceSub(BillPayProvinceSub billPayProvinceSub)
    {
        return billPayProvinceSubMapper.updateBillPayProvinceSub(billPayProvinceSub);
    }


    /**
     * 删除应付账单（省平台）子账单表
     *
     * @param id 应付账单（省平台）子账单表ID
     * @return 结果
     */
    public int deleteBillPayProvinceSubById(Integer id)
    {
        return billPayProvinceSubMapper.deleteBillPayProvinceSubById( id);
    };


    /**
     * 批量删除应付账单（省平台）子账单表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBillPayProvinceSubByIds(Integer[] ids)
    {
        return billPayProvinceSubMapper.deleteBillPayProvinceSubByIds( ids);
    }

    @Override
    public List<BillDealWithCityAndCostVO> selectFdBillSubByBillNo(String billNo,String customerName, String platformCode) {
        return billPayProvinceSubMapper.selectFdBillSubByBillNo(billNo,customerName,platformCode);
    }

    @Override
    public List<BillDealWithCityAndCostVO> selectFdBillSubByShiftNo(String shiftNo, String customerCode) {
        return billPayProvinceSubMapper.selectFdBillSubByShiftNo(shiftNo, customerCode);
    }

    /**
     * 获取省平台应付子帐单数据
     * @param billNo
     * @return
     */
    @Override
    public List<BillPayProvinceSub> selectBillPayProvinceSubByBillCode(String billNo) {
        return billPayProvinceSubMapper.selectBillPayProvinceSubByBillCode(billNo);
    }

}
