package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseAuditinfo;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 融资审核信息 服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-04 10:08:15
 */
public interface EfWarehouseAuditinfoService extends IService<EfWarehouseAuditinfo> {
    /**
     * 查询融资审核信息信息
     *
     * @param rowId 融资审核信息ID
     * @return 融资审核信息信息
     */
    public EfWarehouseAuditinfo selectEfWarehouseAuditinfoById(String rowId);

    /**
     * 查询融资审核信息列表
     *
     * @param efWarehouseAuditinfo 融资审核信息信息
     * @return 融资审核信息集合
     */
    public List<EfWarehouseAuditinfo> selectEfWarehouseAuditinfoList(EfWarehouseAuditinfo efWarehouseAuditinfo);


    /**
     * 分页模糊查询融资审核信息列表
     * @return 融资审核信息集合
     */
    public Page selectEfWarehouseAuditinfoListByLike(Query query);



    /**
     * 新增融资审核信息
     *
     * @param efWarehouseAuditinfo 融资审核信息信息
     * @return 结果
     */
    public int insertEfWarehouseAuditinfo(EfWarehouseAuditinfo efWarehouseAuditinfo);

    /**
     * 修改融资审核信息
     *
     * @param efWarehouseAuditinfo 融资审核信息信息
     * @return 结果
     */
    public int updateEfWarehouseAuditinfo(EfWarehouseAuditinfo efWarehouseAuditinfo);

    /**
     * 删除融资审核信息
     *
     * @param rowId 融资审核信息ID
     * @return 结果
     */
    public int deleteEfWarehouseAuditinfoById(String rowId);

    /**
     * 批量删除融资审核信息
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfWarehouseAuditinfoByIds(Integer[] rowIds);

}

