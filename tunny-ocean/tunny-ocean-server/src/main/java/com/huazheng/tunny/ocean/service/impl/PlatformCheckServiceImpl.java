package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.PlatformCheckMapper;
import com.huazheng.tunny.ocean.api.entity.PlatformCheck;
import com.huazheng.tunny.ocean.service.PlatformCheckService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("platformCheckService")
public class PlatformCheckServiceImpl extends ServiceImpl<PlatformCheckMapper, PlatformCheck> implements PlatformCheckService {

    @Autowired
    private PlatformCheckMapper platformCheckMapper;

    /**
     * 查询平台校验信息
     *
     * @param id 平台校验ID
     * @return 平台校验信息
     */
    @Override
    public PlatformCheck selectPlatformCheckById(Integer id)
    {
        return platformCheckMapper.selectPlatformCheckById(id);
    }

    /**
     * 查询平台校验列表
     *
     * @param platformCheck 平台校验信息
     * @return 平台校验集合
     */
    @Override
    public List<PlatformCheck> selectPlatformCheckList(PlatformCheck platformCheck)
    {
        return platformCheckMapper.selectPlatformCheckList(platformCheck);
    }


    /**
     * 分页模糊查询平台校验列表
     * @return 平台校验集合
     */
    @Override
    public Page selectPlatformCheckListByLike(Query query)
    {
        PlatformCheck platformCheck =  BeanUtil.mapToBean(query.getCondition(), PlatformCheck.class,false);
        query.setRecords(platformCheckMapper.selectPlatformCheckListByLike(query,platformCheck));
        return query;
    }

    /**
     * 新增平台校验
     *
     * @param platformCheck 平台校验信息
     * @return 结果
     */
    @Override
    public int insertPlatformCheck(PlatformCheck platformCheck)
    {
        return platformCheckMapper.insertPlatformCheck(platformCheck);
    }

    /**
     * 修改平台校验
     *
     * @param platformCheck 平台校验信息
     * @return 结果
     */
    @Override
    public int updatePlatformCheck(PlatformCheck platformCheck)
    {
        return platformCheckMapper.updatePlatformCheck(platformCheck);
    }


    /**
     * 删除平台校验
     *
     * @param id 平台校验ID
     * @return 结果
     */
    public int deletePlatformCheckById(Integer id)
    {
        return platformCheckMapper.deletePlatformCheckById( id);
    };


    /**
     * 批量删除平台校验对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deletePlatformCheckByIds(Integer[] ids)
    {
        return platformCheckMapper.deletePlatformCheckByIds( ids);
    }

    @Override
    public String getPlatformCode(String checkType){
        return platformCheckMapper.getPlatformCode(checkType);
    }

}
