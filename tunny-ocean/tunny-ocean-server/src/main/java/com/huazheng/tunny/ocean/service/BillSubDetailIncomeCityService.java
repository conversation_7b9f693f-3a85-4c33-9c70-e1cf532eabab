package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.BillSubDetailIncomeCity;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 应收账单（市）子账单详情 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-06-11 16:58:07
 */
public interface BillSubDetailIncomeCityService extends IService<BillSubDetailIncomeCity> {
    /**
     * 查询应收账单（市）子账单详情信息
     *
     * @param id 应收账单（市）子账单详情ID
     * @return 应收账单（市）子账单详情信息
     */
    public BillSubDetailIncomeCity selectBillSubDetailIncomeCityById(Integer id);

    /**
     * 查询应收账单（市）子账单详情列表
     *
     * @param billSubDetailIncomeCity 应收账单（市）子账单详情信息
     * @return 应收账单（市）子账单详情集合
     */
    public List<BillSubDetailIncomeCity> selectBillSubDetailIncomeCityList(BillSubDetailIncomeCity billSubDetailIncomeCity);


    /**
     * 分页模糊查询应收账单（市）子账单详情列表
     * @return 应收账单（市）子账单详情集合
     */
    public Page selectBillSubDetailIncomeCityListByLike(Query query);



    /**
     * 新增应收账单（市）子账单详情
     *
     * @param billSubDetailIncomeCity 应收账单（市）子账单详情信息
     * @return 结果
     */
    public int insertBillSubDetailIncomeCity(BillSubDetailIncomeCity billSubDetailIncomeCity);

    /**
     * 修改应收账单（市）子账单详情
     *
     * @param billSubDetailIncomeCity 应收账单（市）子账单详情信息
     * @return 结果
     */
    public int updateBillSubDetailIncomeCity(BillSubDetailIncomeCity billSubDetailIncomeCity);

    /**
     * 删除应收账单（市）子账单详情
     *
     * @param id 应收账单（市）子账单详情ID
     * @return 结果
     */
    public int deleteBillSubDetailIncomeCityById(Integer id);

    /**
     * 批量删除应收账单（市）子账单详情
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteBillSubDetailIncomeCityByIds(Integer[] ids);

}

