package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.BasUser;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.vo.UserVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 系统管理员/用户信息表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-01 10:36:38
 */
public interface BasUserService extends IService<BasUser> {
    /**
     * 查询系统管理员/用户信息表信息
     *
     * @param rowId 系统管理员/用户信息表ID
     * @return 系统管理员/用户信息表信息
     */
    public BasUser selectBasUserById(String rowId);

    /**
     * 查询系统管理员/用户信息表列表
     *
     * @param basUser 系统管理员/用户信息表信息
     * @return 系统管理员/用户信息表集合
     */
    public List<BasUser> selectBasUserList(BasUser basUser);


    /**
     * 分页模糊查询系统管理员/用户信息表列表
     * @return 系统管理员/用户信息表集合
     */
    public Page selectBasUserListByLike(Query query);



    /**
     * 新增系统管理员/用户信息表
     *
     * @param basUser 系统管理员/用户信息表信息
     * @return 结果
     */
    public int insertBasUser(BasUser basUser);

    /**
     * 修改系统管理员/用户信息表
     *
     * @param basUser 系统管理员/用户信息表信息
     * @return 结果
     */
    public int updateBasUser(BasUser basUser);

    public int updateExamine(BasUser basUser);

    /**
     * 删除系统管理员/用户信息表
     *
     * @param rowId 系统管理员/用户信息表ID
     * @return 结果
     */
    public int deleteBasUserById(String rowId);

    /**
     * 批量删除系统管理员/用户信息表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteBasUserByIds(Integer[] rowIds);

    R checkUser(UserVO userVO, HttpServletRequest request);

//    public void saveSubject(MultipartFile file, BasUserService basUserService);
}

