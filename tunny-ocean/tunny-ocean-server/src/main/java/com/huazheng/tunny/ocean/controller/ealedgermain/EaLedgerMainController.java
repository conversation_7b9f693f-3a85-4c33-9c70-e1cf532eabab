package com.huazheng.tunny.ocean.controller.ealedgermain;

import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.ealedgermain.EaLedgerDetail;
import com.huazheng.tunny.ocean.api.entity.ealedgermain.EaLedgerMain;
import com.huazheng.tunny.ocean.api.vo.FdInvoiceCostLedgerDetailsVO;
import com.huazheng.tunny.ocean.service.ealedgermain.EaLedgerMainService;
import com.huazheng.tunny.ocean.service.ealedgermain.impl.EaFdInvoiceCostLedgerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 台账主表
 *
 * <AUTHOR>
 * @date 2025-07-02 15:42:27
 */
@Slf4j
@RestController
@RequestMapping("/ealedgermain")
public class EaLedgerMainController {

    @Autowired
    private EaLedgerMainService eaLedgerMainService;

    @Autowired
    private EaFdInvoiceCostLedgerService eaFdInvoiceCostLedgerService;

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  eaLedgerMainService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return eaLedgerMainService.selectEaLedgerMainListByLike(new Query<>(params));
    }

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/shiftPage")
    public Page shiftPage(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  eaLedgerMainService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return eaLedgerMainService.selectShifmanagementByLike(new Query<>(params));
    }

    /**
     * 费用列表
     *
     * @param daLedgerDetail
     * @return
     */
    @GetMapping("/feeList")
    public R feeList(EaLedgerDetail daLedgerDetail) {
        //对象模糊查询
        return eaLedgerMainService.selectFeeListByLedgerMain(daLedgerDetail);
    }

    /**
     * 详情
     *
     * @param ledgerId
     * @return R
     */
    @GetMapping("/{ledgerId}")
    public R info(@PathVariable("ledgerId") Long ledgerId) {
        EaLedgerMain eaLedgerMain = eaLedgerMainService.selectEaLedgerMainById(ledgerId);
        return new R<>(eaLedgerMain);
    }

    /**
     * 保存
     *
     * @param eaLedgerMain
     * @return R
     */
    @PostMapping
    public R save(@RequestBody EaLedgerMain eaLedgerMain) {
        eaLedgerMainService.insertEaLedgerMain(eaLedgerMain);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 生成台账
     *
     * @param eaLedgerMain 生成逻辑参数
     * @return 结果
     */
    @PostMapping("/generateLedger")
    public R generateBill(@RequestBody EaLedgerMain eaLedgerMain) {
        return eaLedgerMainService.generateLedger(eaLedgerMain);
    }

    /**
     * 删除台账
     *
     * @param eaLedgerMain 删除逻辑参数
     * @return 结果
     */
    @PostMapping("/delLedger")
    public R delLedger(@RequestBody EaLedgerMain eaLedgerMain) {
        return eaLedgerMainService.delLedger(eaLedgerMain);
    }


    /**
     * 台账提交
     *
     * @param eaLedgerMain 提交逻辑参数
     * @return 结果
     */
    @PostMapping("/submit")
    public R submitLedger(@RequestBody EaLedgerMain eaLedgerMain) {
        return eaLedgerMainService.submitLedger(eaLedgerMain);
    }

    /**
     * 台账是否可审核
     *
     * @param params 审核逻辑参数
     * @return 结果
     */
    @GetMapping("/isExamine")
    public R isExamine(@RequestParam Map<String, Object> params) {
        return eaLedgerMainService.isExamine(params);
    }

    /**
     * 台账审核
     *
     * @param params 审核逻辑参数
     * @return 结果
     */
    @GetMapping("/examineList")
    public R examineList(@RequestParam Map<String, Object> params) {
        return eaLedgerMainService.examineList(params);
    }

    /**
     * 台账审核
     *
     * @param eaLedgerMain 审核逻辑参数
     * @return 结果
     */
    @PostMapping("/examine")
    public R examineLedger(@RequestBody EaLedgerMain eaLedgerMain) {
        return eaLedgerMainService.examineLedger(eaLedgerMain);
    }

    /**
     * 台账审核撤回
     *
     * @param eaLedgerMain 审核撤回逻辑参数
     * @return 结果
     */
    @PostMapping("/examineRevoke")
    public R examineRevoke(@RequestBody EaLedgerMain eaLedgerMain) {
        return eaLedgerMainService.examineRevoke(eaLedgerMain);
    }

    /**
     * 修改
     *
     * @param eaLedgerMain
     * @return R
     */
    @PutMapping
    public R update(@RequestBody EaLedgerMain eaLedgerMain) {
        eaLedgerMainService.updateById(eaLedgerMain);
        return new R<>(Boolean.TRUE);
    }


    /**
     * 删除
     *
     * @param ledgerId
     * @return R
     */
    @DeleteMapping("/{ledgerId}")
    public R delete(@PathVariable Long ledgerId) {
        eaLedgerMainService.deleteById(ledgerId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     *
     * @param ledgerIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody List<Long> ledgerIds) {
        eaLedgerMainService.deleteBatchIds(ledgerIds);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 导出台账数据为 Excel
     *
     * @param eaLedgerMain 台账数据
     * @param response     Http 响应对象
     * @throws IOException IO异常
     */
    @PostMapping("/exportEaLedgerMainExcel")
    public void exportShippingAccountExcel(@RequestBody EaLedgerMain eaLedgerMain, HttpServletResponse response) throws IOException {
        eaLedgerMainService.exportEaLedgerMainExcel(eaLedgerMain, response);
    }

    /**
     * 导出EXCEL
     *
     * @return
     */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<EaLedgerMain> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = eaLedgerMainService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }


    /**
     * 导入EXCEL
     * <p>
     *
     * @param titleStr
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<EaLedgerMain> list = reader.readAll(EaLedgerMain.class);
        eaLedgerMainService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }


    /**
     * 修改省级班列号
     *
     * @param eaLedgerMain 修改参数
     * @return R
     * <AUTHOR>
     * @since 2025/7/28 13:50
     **/
    @PostMapping("/updateProvinceShiftNo")
    public R updateProvinceShiftNo(@RequestBody EaLedgerMain eaLedgerMain) {
        return eaLedgerMainService.updateProvinceShiftNo(eaLedgerMain);
    }


    /**
     * 修改发运时间
     *
     * @param eaLedgerMain 修改参数
     * @return R
     * <AUTHOR>
     * @since 2025/7/28 14:05
     **/
    @PostMapping("/updateShipTime")
    public R updateShipTime(@RequestBody EaLedgerMain eaLedgerMain) {
        return eaLedgerMainService.updateShipTime(eaLedgerMain);
    }


    /**
     * 导出成本台账
     *
     * @param costLedgerDetails , response
     * <AUTHOR>
     * @since 2025/4/16 下午3:18
     **/
    @PostMapping("/costLedgerExport")
    public void costLedgerExport(@RequestBody FdInvoiceCostLedgerDetailsVO costLedgerDetails, HttpServletResponse response) {
        eaFdInvoiceCostLedgerService.costLedgerExport(costLedgerDetails, response);
    }
}
