package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.FdShippingAccoundetailSub;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 发运台账分表 服务接口层
 *
 * <AUTHOR>
 * @date 2023-07-28 16:25:15
 */
public interface FdShippingAccoundetailSubService extends IService<FdShippingAccoundetailSub> {
    /**
     * 查询发运台账分表信息
     *
     * @param rowId 发运台账分表ID
     * @return 发运台账分表信息
     */
    public FdShippingAccoundetailSub selectFdShippingAccoundetailSubById(Long rowId);

    /**
     * 查询发运台账分表列表
     *
     * @param fdShippingAccoundetailSub 发运台账分表信息
     * @return 发运台账分表集合
     */
    public List<FdShippingAccoundetailSub> selectFdShippingAccoundetailSubList(FdShippingAccoundetailSub fdShippingAccoundetailSub);


    /**
     * 分页模糊查询发运台账分表列表
     *
     * @return 发运台账分表集合
     */
    public Page selectFdShippingAccoundetailSubListByLike(Query query);


    /**
     * 新增发运台账分表
     *
     * @param fdShippingAccoundetailSub 发运台账分表信息
     * @return 结果
     */
    public int insertFdShippingAccoundetailSub(FdShippingAccoundetailSub fdShippingAccoundetailSub);

    /**
     * 修改发运台账分表
     *
     * @param fdShippingAccoundetailSub 发运台账分表信息
     * @return 结果
     */
    public int updateFdShippingAccoundetailSub(FdShippingAccoundetailSub fdShippingAccoundetailSub);

    /**
     * 删除发运台账分表
     *
     * @param rowId 发运台账分表ID
     * @return 结果
     */
    public int deleteFdShippingAccoundetailSubById(Long rowId);

    /**
     * 批量删除发运台账分表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdShippingAccoundetailSubByIds(Integer[] rowIds);

    public R insertFdShippingAccoundetailSubList(List<FdShippingAccoundetailSub> list, String accountCode);
}

