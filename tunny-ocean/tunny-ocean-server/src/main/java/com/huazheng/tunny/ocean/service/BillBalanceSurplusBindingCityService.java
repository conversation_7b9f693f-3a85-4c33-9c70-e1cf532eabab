package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.BillBalanceSurplusBindingCity;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 应付账单结算绑定余额表（市） 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-06-15 14:15:52
 */
public interface BillBalanceSurplusBindingCityService extends IService<BillBalanceSurplusBindingCity> {
    /**
     * 查询应付账单结算绑定余额表（市）信息
     *
     * @param id 应付账单结算绑定余额表（市）ID
     * @return 应付账单结算绑定余额表（市）信息
     */
    public BillBalanceSurplusBindingCity selectBillBalanceSurplusBindingCityById(Integer id);

    /**
     * 查询应付账单结算绑定余额表（市）列表
     *
     * @param billBalanceSurplusBindingCity 应付账单结算绑定余额表（市）信息
     * @return 应付账单结算绑定余额表（市）集合
     */
    public List<BillBalanceSurplusBindingCity> selectBillBalanceSurplusBindingCityList(BillBalanceSurplusBindingCity billBalanceSurplusBindingCity);


    /**
     * 分页模糊查询应付账单结算绑定余额表（市）列表
     * @return 应付账单结算绑定余额表（市）集合
     */
    public Page selectBillBalanceSurplusBindingCityListByLike(Query query);



    /**
     * 新增应付账单结算绑定余额表（市）
     *
     * @param billBalanceSurplusBindingCity 应付账单结算绑定余额表（市）信息
     * @return 结果
     */
    public int insertBillBalanceSurplusBindingCity(BillBalanceSurplusBindingCity billBalanceSurplusBindingCity);

    /**
     * 修改应付账单结算绑定余额表（市）
     *
     * @param billBalanceSurplusBindingCity 应付账单结算绑定余额表（市）信息
     * @return 结果
     */
    public int updateBillBalanceSurplusBindingCity(BillBalanceSurplusBindingCity billBalanceSurplusBindingCity);

    /**
     * 删除应付账单结算绑定余额表（市）
     *
     * @param id 应付账单结算绑定余额表（市）ID
     * @return 结果
     */
    public int deleteBillBalanceSurplusBindingCityById(Integer id);

    /**
     * 批量删除应付账单结算绑定余额表（市）
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteBillBalanceSurplusBindingCityByIds(Integer[] ids);

}

