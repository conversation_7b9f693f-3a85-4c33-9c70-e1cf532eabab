package com.huazheng.tunny.ocean.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.FiHisBookingInformation;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * 历史订舱信息表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-03-08 09:42:36
 */
public interface FiHisBookingInformationService extends IService<FiHisBookingInformation> {
    /**
     * 查询历史订舱信息表信息
     *
     * @param rowId 历史订舱信息表ID
     * @return 历史订舱信息表信息
     */
    public FiHisBookingInformation selectFiHisBookingInformationById(String rowId);

    /**
     * 查询历史订舱信息表列表
     *
     * @param fiHisBookingInformation 历史订舱信息表信息
     * @return 历史订舱信息表集合
     */
    public List<FiHisBookingInformation> selectFiHisBookingInformationList(FiHisBookingInformation fiHisBookingInformation);

    /**
     * 订舱查询历史信息
     */
    public List<FiHisBookingInformation>  selectfiHisBookingInformationDingcangList (FiHisBookingInformation fiHisBookingInformation);


    /**
     * 分页模糊查询历史订舱信息表列表
     * @return 历史订舱信息表集合
     */
    public Page selectFiHisBookingInformationListByLike(Query query);

    public R selectStatistics(Map<String, Object> params) throws ParseException;



    /**
     * 新增历史订舱信息表
     *
     * @param fiHisBookingInformation 历史订舱信息表信息
     * @return 结果
     */
    public int insertFiHisBookingInformation(FiHisBookingInformation fiHisBookingInformation);

    /**
     * 修改历史订舱信息表
     *
     * @param fiHisBookingInformation 历史订舱信息表信息
     * @return 结果
     */
    public int updateFiHisBookingInformation(FiHisBookingInformation fiHisBookingInformation);

    /**
     * 删除历史订舱信息表
     *
     * @param rowId 历史订舱信息表ID
     * @return 结果
     */
    public int deleteFiHisBookingInformationById(String rowId);

    /**
     * 批量删除历史订舱信息表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiHisBookingInformationByIds(Integer[] rowIds);

    public String selectEnterpriseLicense(JSONObject jsonObject) throws ParseException;

}

