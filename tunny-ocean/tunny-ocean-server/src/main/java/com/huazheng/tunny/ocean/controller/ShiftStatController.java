package com.huazheng.tunny.ocean.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.ShiftStat;
import com.huazheng.tunny.ocean.service.ShiftStatService;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 班次统计表
 *
 * <AUTHOR>
 * @since 2025-05-06 16:19:09
 */
@RestController
@RequestMapping("shiftStat")
public class ShiftStatController {

    @Autowired
    private ShiftStatService shiftStatservice;

    /**
     * 班次统计表分页
     *
     * @param shiftStat 查询参数
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:19:09
     */
    @GetMapping("/page")
    public R<Page<ShiftStat>> page(ShiftStat shiftStat) {
        return R.success(shiftStatservice.page(shiftStat));
    }

    /**
     * 班次统计表详情
     *
     * @param statId 班次统计Id
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:19:09
     */
    @GetMapping("/{statId}")
    public R<ShiftStat> info(@PathVariable("statId") Integer statId) {
        return shiftStatservice.info(statId);
    }

    /**
     * 班次统计表保存
     *
     * @param shiftStat 班次统计
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:19:09
     */
    @PostMapping("/save")
    public R<ShiftStat> save(@RequestBody ShiftStat shiftStat) {
        return shiftStatservice.save(shiftStat);
    }

    /**
     * 班次统计表修改
     *
     * @param shiftStat 班次统计
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:19:09
     */
    @PostMapping("/renew")
    public R update(@RequestBody ShiftStat shiftStat) {
        return shiftStatservice.update(shiftStat);
    }

    /**
     * 班次统计表删除
     *
     * @param statIds 删除的班次统计表ID
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:19:09
     */
    @PostMapping("/del/{statIds}")
    public R<T> delete(@PathVariable Long[] statIds) {
        return shiftStatservice.delete(statIds);
    }

    /**
     * 班次统计表列表
     *
     * @param shiftStat 查询参数
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:19:09
     */
    @GetMapping("/list")
    public R<List<ShiftStat>> list(ShiftStat shiftStat) {
        return shiftStatservice.list(shiftStat);
    }


    /**
     * 模板导入
     *
     * @param file 导入的中亚或者中欧模板
     * @return R
     * <AUTHOR>
     * @since 2025/5/7 上午9:10
     **/
    @PostMapping("/importExcel")
    public R importExcel(@RequestParam MultipartFile file, @RequestParam String shiftType) {
        return shiftStatservice.importExcel(file, shiftType);
    }


    @PostMapping("/exportExcel")
    public void exportExcel(@RequestBody ShiftStat shiftStat, HttpServletResponse response) {
        shiftStatservice.exportExcel(shiftStat.getStatId(), response);
    }
}
