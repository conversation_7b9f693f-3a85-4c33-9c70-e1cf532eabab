package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfDisposeIntentionList;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 处置仓单编码 服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-10 15:32:39
 */
public interface EfDisposeIntentionListService extends IService<EfDisposeIntentionList> {
    /**
     * 查询处置仓单编码信息
     *
     * @param rowId 处置仓单编码ID
     * @return 处置仓单编码信息
     */
    public EfDisposeIntentionList selectEfDisposeIntentionListById(String rowId);

    /**
     * 查询处置仓单编码列表
     *
     * @param efDisposeIntentionList 处置仓单编码信息
     * @return 处置仓单编码集合
     */
    public List<EfDisposeIntentionList> selectEfDisposeIntentionListList(EfDisposeIntentionList efDisposeIntentionList);


    /**
     * 分页模糊查询处置仓单编码列表
     * @return 处置仓单编码集合
     */
    public Page selectEfDisposeIntentionListListByLike(Query query);



    /**
     * 新增处置仓单编码
     *
     * @param efDisposeIntentionList 处置仓单编码信息
     * @return 结果
     */
    public int insertEfDisposeIntentionList(EfDisposeIntentionList efDisposeIntentionList);

    /**
     * 修改处置仓单编码
     *
     * @param efDisposeIntentionList 处置仓单编码信息
     * @return 结果
     */
    public int updateEfDisposeIntentionList(EfDisposeIntentionList efDisposeIntentionList);

    /**
     * 删除处置仓单编码
     *
     * @param rowId 处置仓单编码ID
     * @return 结果
     */
    public int deleteEfDisposeIntentionListById(String rowId);

    /**
     * 批量删除处置仓单编码
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfDisposeIntentionListByIds(Integer[] rowIds);

}

