package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.FdInvoiceApplicationDetailTwoDTO;
import com.huazheng.tunny.ocean.api.dto.FdShippingDTO;
import com.huazheng.tunny.ocean.api.entity.FdInvoiceApplication;
import com.huazheng.tunny.ocean.api.entity.FdInvoiceApplicationDetail;
import com.huazheng.tunny.ocean.api.entity.FdShippingAccount;
import com.huazheng.tunny.ocean.api.vo.FdInvoiceApplicationVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 开票申请主表 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:30:13
 */
public interface FdInvoiceApplicationService extends IService<FdInvoiceApplication> {
    /**
     * 查询开票申请主表信息
     *
     * @param id 开票申请主表ID
     * @return 开票申请主表信息
     */
    public FdInvoiceApplication selectFdInvoiceApplicationById(Long id);

    public FdInvoiceApplication infoProvince(FdInvoiceApplication fdInvoiceApplication);

    public List<FdInvoiceApplicationDetailTwoDTO> infoProvinceTwo(FdInvoiceApplicationDetailTwoDTO detail);

    public List<FdInvoiceApplicationDetail> infoProvinceThree(FdInvoiceApplicationDetailTwoDTO detail);

    public void infoProvinceExport(FdInvoiceApplicationDetailTwoDTO detail, HttpServletResponse response) throws Exception;

    /**
     * 查询开票申请主表列表
     *
     * @param fdInvoiceApplication 开票申请主表信息
     * @return 开票申请主表集合
     */
    public List<FdInvoiceApplication> selectFdInvoiceApplicationList(FdInvoiceApplication fdInvoiceApplication);


    /**
     * 分页模糊查询开票申请主表列表
     *
     * @return 开票申请主表集合
     */
    public Page selectFdInvoiceApplicationListByLike(Query query);

    public void fdinvoiceapplicationExport(FdInvoiceApplication fdInvoiceApplication, HttpServletResponse response) throws Exception;

    public Page fiPage(Query query);

    public Integer selectFdInvoiceApplicationListByLikeCount(FdInvoiceApplication fdInvoiceApplication);

    /**
     * 发票统计列表接口
     */
    public Page selectInvoiceStatisticsPage(Query query);

    /**
     * 根据用户code查询发票信息
     *
     * @param fdInvoiceApplication
     * @return
     */
    public FdInvoiceApplicationVO selectInvoiceInfoByCustomerCode(FdInvoiceApplication fdInvoiceApplication);


    /**
     * 新增开票申请
     *
     * @param vo 开票申请信息
     * @return 结果
     */
    public R insertFdInvoiceApplication(FdInvoiceApplicationVO vo) throws Exception;

    /**
     * 新增开票申请
     *
     * @param vo 开票申请信息
     * @return 结果
     */
    public R saveInfo(FdInvoiceApplicationVO vo) throws Exception;

    /**
     * 修改开票申请主表
     *
     * @param vo 开票申请主表信息
     * @return 结果
     */
    public R updateFdInvoiceApplication(FdInvoiceApplicationVO vo);

    public R updateInvoiceStatusAndFile(FdInvoiceApplicationVO vo);

    /**
     * 修改开票申请主表
     *
     * @param vo 开票申请主表信息
     * @return 结果
     */
    public R update(FdInvoiceApplicationVO vo);

    /**
     * 删除开票申请主表
     *
     * @param id 开票申请主表ID
     * @return 结果
     */
    R deleteFdInvoiceApplicationById(Long id);

    /**
     * 批量删除开票申请主表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdInvoiceApplicationByIds(Integer[] ids);

    public List<FdShippingDTO> fdShippingList(FdInvoiceApplication fdInvoiceApplication);

    R cancel(Long id);

    R fiCancel(Long id);

    void revenueLedgerExport(FdShippingAccount fdShippingAccount, HttpServletResponse response) throws Exception;
}

