package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.StationLineRelation;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 站点线路管理 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-08 19:21:41
 */
public interface StationLineRelationService extends IService<StationLineRelation> {
    /**
     * 查询站点线路管理信息
     *
     * @param rowId 站点线路管理ID
     * @return 站点线路管理信息
     */
    public List<StationLineRelation> selectStationLineRelationByStationCode(String rowId);

    /**
     * 查询站点线路管理列表
     *
     * @param stationLineRelation 站点线路管理信息
     * @return 站点线路管理集合
     */
    public List<StationLineRelation> selectStationLineRelationList(StationLineRelation stationLineRelation);


    /**
     * 分页模糊查询站点线路管理列表
     * @return 站点线路管理集合
     */
    public Page selectStationLineRelationListByLike(Query query);



    /**
     * 新增站点线路管理
     *
     * @param stationLineRelation 站点线路管理信息
     * @return 结果
     */
    public int insertStationLineRelation(StationLineRelation stationLineRelation);

    /**
     * 修改站点线路管理
     *
     * @param stationLineRelation 站点线路管理信息
     * @return 结果
     */
    public int updateStationLineRelation(StationLineRelation stationLineRelation);

    /**
     * 删除站点线路管理
     *
     * @param rowId 站点线路管理ID
     * @return 结果
     */
    public int deleteStationLineRelationById(String rowId);

    /**
     * 批量删除站点线路管理
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteStationLineRelationByIds(Integer[] rowIds);

}

