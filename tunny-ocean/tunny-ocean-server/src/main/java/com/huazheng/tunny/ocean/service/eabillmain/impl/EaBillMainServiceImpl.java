package com.huazheng.tunny.ocean.service.eabillmain.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.bpm.FreightAccountCityDTO;
import com.huazheng.tunny.ocean.api.dto.eabillmain.EaBillMainDTO;
import com.huazheng.tunny.ocean.api.entity.BillBalanceMainCity;
import com.huazheng.tunny.ocean.api.entity.Shifmanagement;
import com.huazheng.tunny.ocean.api.entity.eabalance.EaBalance;
import com.huazheng.tunny.ocean.api.entity.eabalance.EaBalanceTransaction;
import com.huazheng.tunny.ocean.api.entity.eabillmain.EaBillMain;
import com.huazheng.tunny.ocean.api.entity.eabillmain.EaBillSubtable;
import com.huazheng.tunny.ocean.api.entity.eabillmain.EaFee;
import com.huazheng.tunny.ocean.api.enums.BillStageEnum;
import com.huazheng.tunny.ocean.api.enums.BillStatusEnum;
import com.huazheng.tunny.ocean.api.enums.IdentificationEnum;
import com.huazheng.tunny.ocean.api.util.SysCommon;
import com.huazheng.tunny.ocean.api.vo.BillBalanceMainCityDetailVO;
import com.huazheng.tunny.ocean.mapper.ShifmanagementMapper;
import com.huazheng.tunny.ocean.mapper.eabalance.EaBalanceMapper;
import com.huazheng.tunny.ocean.mapper.eabalance.EaBalanceTransactionMapper;
import com.huazheng.tunny.ocean.mapper.eabillmain.EaBillMainMapper;
import com.huazheng.tunny.ocean.mapper.eabillmain.EaBillSubtableMapper;
import com.huazheng.tunny.ocean.mapper.eabillmain.EaFeeMapper;
import com.huazheng.tunny.ocean.mapper.eabookingorder.EaContainerSummaryMapper;
import com.huazheng.tunny.ocean.service.SysNoConfigService;
import com.huazheng.tunny.ocean.service.eabillmain.EaBillMainService;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("eaBillMainService")
public class EaBillMainServiceImpl extends ServiceImpl<EaBillMainMapper, EaBillMain> implements EaBillMainService {

    @Autowired
    private EaBillMainMapper eaBillMainMapper;
    @Autowired
    private EaBillSubtableMapper eaBillSubtableMapper;
    @Autowired
    private EaBalanceMapper eaBalanceMapper;
    @Autowired
    private EaBalanceTransactionMapper eaBalanceTransactionMapper;
    @Autowired
    private EaFeeMapper eaFeeMapper;
    @Autowired
    private SysNoConfigService sysNoConfigService;
    @Autowired
    private ShifmanagementMapper shifmanagementMapper;

    /**
     * 查询账单主表信息
     *
     * @param billId 账单主表ID
     * @return 账单主表信息
     */
    @Override
    public EaBillMainDTO selectEaBillMainById(Long billId) {
        EaBillMainDTO eaBillMain = eaBillMainMapper.selectEaBillMainById(billId);
        if (eaBillMain == null) {
            return null;
        }
        //查询子帐单
        EaBillSubtable eaBillSubtable = new EaBillSubtable();
        eaBillSubtable.setBillCode(eaBillMain.getBillCode());
        List<EaBillSubtable> list = eaBillSubtableMapper.selectEaBillSubtableList(eaBillSubtable);
        eaBillMain.setEaBillSubtableList(list);
        return eaBillMain;
    }

    /**
     * 查询账单主表列表
     *
     * @param eaBillMain 账单主表信息
     * @return 账单主表集合
     */
    @Override
    public List<EaBillMain> selectEaBillMainList(EaBillMain eaBillMain) {
        return eaBillMainMapper.selectEaBillMainList(eaBillMain);
    }


    /**
     * 分页模糊查询账单主表列表
     *
     * @return 账单主表集合
     */
    @Override
    public Page selectEaBillMainListByLike(Query query) {
        EaBillMainDTO eaBillMain = BeanUtil.mapToBean(query.getCondition(), EaBillMainDTO.class, false);
        query.setRecords(eaBillMainMapper.selectEaBillMainListByLike(query, eaBillMain));
        return query;
    }

    /**
     * 新增账单主表
     *
     * @param eaBillMain 账单主表信息
     * @return 结果
     */
    @Override
    public int insertEaBillMain(EaBillMain eaBillMain) {
        return eaBillMainMapper.insertEaBillMain(eaBillMain);
    }

    /**
     * 修改账单主表
     *
     * @param eaBillMain 账单主表信息
     * @return 结果
     */
    @Override
    public int updateEaBillMain(EaBillMain eaBillMain) {
        return eaBillMainMapper.updateEaBillMain(eaBillMain);
    }


    /**
     * 删除账单主表
     *
     * @param billId 账单主表ID
     * @return 结果
     */
    @Override
    public int deleteEaBillMainById(Long billId) {
        return eaBillMainMapper.deleteEaBillMainById(billId);
    }


    /**
     * 批量删除账单主表对象
     *
     * @param billIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEaBillMainByIds(Integer[] billIds) {
        return eaBillMainMapper.deleteEaBillMainByIds(billIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R settlement(EaBillMain eaBillMain) {
        if (eaBillMain == null || eaBillMain.getBillId() == null) {
            return R.error("参数错误！");
        }
        Long subtableId = eaBillMain.getBillSubtableId();
        eaBillMain = eaBillMainMapper.selectEaBillMainById(eaBillMain.getBillId());
        if (eaBillMain == null) {
            return R.error("无效数据ID异常！");
        }
        if ("settled".equals(eaBillMain.getBillStatus())) {
            return R.error("该账单已结清！");
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        // 查询余额
        EaBalance eaBalance = new EaBalance();
        eaBalance.setDelFlag("N");
        if (SysCommon.RECEIVE_CODE_ZTDL.equals(eaBillMain.getPayeeCode())) {
            eaBalance.setPayerCode(eaBillMain.getPlatformCode());
            eaBalance.setPayeeCode(eaBillMain.getPayeeCode());
        } else {
            eaBalance.setPayerCode(eaBillMain.getPayerCode());
            eaBalance.setPayeeCode(eaBillMain.getPayeeCode());
        }
        eaBalance = eaBalanceMapper.selectOne(eaBalance);
        if (eaBalance == null) {
            return R.error("余额不存在");
        }
        if (subtableId == null && eaBalance.getAvailableBalance().compareTo(eaBillMain.getUnpaidAmount()) < 0) {
            return R.error("余额不足请充值！");
        }
        //查询子帐单
        EaBillSubtable eaBillSubtable = new EaBillSubtable();
        eaBillSubtable.setBillCode(eaBillMain.getBillCode());
        List<EaBillSubtable> list = eaBillSubtableMapper.selectEaBillSubtableList(eaBillSubtable);
        BigDecimal balance = eaBalance.getAvailableBalance();
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal paidAmount = BigDecimal.ZERO;
        if (CollUtil.isEmpty(list)) {
            return R.error("无子账单");
        }
        List<EaBillSubtable> updateList = new ArrayList<>();
        List<EaBalanceTransaction> transactionList = new ArrayList<>();
        for (EaBillSubtable billSubtable : list) {
            totalAmount = totalAmount.add(billSubtable.getBillAmount());
            if ("settled".equals(billSubtable.getBillStatus())) {
                if (subtableId != null && billSubtable.getBillSubtableId().equals(subtableId)) {
                    return R.error("该子账单已结清！");
                }
                paidAmount = paidAmount.add(billSubtable.getBillAmount());
                continue;
            }
            Boolean flag = false;
            if (subtableId != null) {
                if (billSubtable.getBillSubtableId().equals(subtableId)) {
                    flag = true;
                }
            } else {
                flag = true;
            }
            if (flag) {
                int result = balance.compareTo(billSubtable.getUnpaidAmount());
                if (result >= 0) {
                    paidAmount = paidAmount.add(billSubtable.getBillAmount());
                    balance = balance.subtract(billSubtable.getUnpaidAmount());
                    addEaBillSubtableList(updateList, billSubtable, billSubtable.getUnpaidAmount(), "settled", userInfo);
                    addTransactionList(transactionList, billSubtable, userInfo, "结算", eaBalance.getBalanceId(), balance);
                } else {
                    return R.error("余额不足请充值！");
                }
            }
        }
        //设置主表数据
        eaBillMain.setBillAmount(totalAmount);
        eaBillMain.setPaidAmount(paidAmount);
        eaBillMain.setBillStatus("settled");
        //设置余额数据
        eaBalance.setTotalExpenditure(eaBalance.getTotalExpenditure().add(eaBalance.getAvailableBalance().subtract(balance)));
        //修改数据库信息
        updateEaBillInfo(eaBillMain, updateList, eaBalance, transactionList);
        R r = R.success();
        r.setMsg("结算成功");
        return r;
    }

    private void addTransactionList(List<EaBalanceTransaction> transactionList, EaBillSubtable billSubtable, SecruityUser userInfo, String type, Long balanceId, BigDecimal balance) {
        EaBalanceTransaction eaBalanceTransaction = new EaBalanceTransaction();
        eaBalanceTransaction.setBillAmount(billSubtable.getBillAmount());
        eaBalanceTransaction.setBalanceId(balanceId);
        eaBalanceTransaction.setBalance(balance);
        eaBalanceTransaction.setTransactionStatus("unclaimed");
        if (type.startsWith("结算")) {
            eaBalanceTransaction.setTransactionType("支出");
        } else {
            eaBalanceTransaction.setTransactionType("收入");
        }

        eaBalanceTransaction.setPaymentMethodCode("bill_writeoff");
        eaBalanceTransaction.setPaymentMethodName("账单核销");
        eaBalanceTransaction.setOperatorName(userInfo.getRealName());
        eaBalanceTransaction.setOperationTime(LocalDateTime.now());
        eaBalanceTransaction.setCreateBy(userInfo.getRealName());
        eaBalanceTransaction.setCreateById(userInfo.getId());
        eaBalanceTransaction.setCreateTime(LocalDateTime.now());
        StringBuilder remark = new StringBuilder("账单结算：");
        remark.append(userInfo.getRealName()).append("于").append(eaBalanceTransaction.getOperationTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("进行").append(billSubtable.getShiftNo()).append("的账单").append(billSubtable.getBillSubtableCode()).append(type);
        eaBalanceTransaction.setRemark(remark.toString());
        transactionList.add(eaBalanceTransaction);
    }

    private void addEaBillSubtableList(List<EaBillSubtable> updateList, EaBillSubtable billSubtable, BigDecimal paidAmount, String billStatus, SecruityUser userInfo) {
        EaBillSubtable newBillSubtable = new EaBillSubtable();
        newBillSubtable.setBillSubtableId(billSubtable.getBillSubtableId());
        newBillSubtable.setBillStatus(billStatus);
        newBillSubtable.setPaidAmount(paidAmount);
        newBillSubtable.setUpdateBy(userInfo.getRealName());
        newBillSubtable.setUpdateById(userInfo.getId());
        updateList.add(newBillSubtable);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R revokeSettlement(EaBillMain eaBillMain) {
        if (eaBillMain == null || eaBillMain.getBillId() == null) {
            return R.error("参数错误");
        }
        Long subtableId = eaBillMain.getBillSubtableId();
        eaBillMain = eaBillMainMapper.selectEaBillMainById(eaBillMain.getBillId());
        if (eaBillMain == null) {
            return R.error("无效数据ID异常");
        }
        if ("unsettled".equals(eaBillMain.getBillStatus())) {
            return R.error("该账单未结清，无法撤销结算");
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        // 查询余额
        EaBalance eaBalance = new EaBalance();
        eaBalance.setDelFlag("N");
        eaBalance.setPayerCode(eaBillMain.getPayerCode());
        eaBalance.setPayeeCode(eaBillMain.getPayeeCode());
        eaBalance = eaBalanceMapper.selectOne(eaBalance);
        if (eaBalance == null) {
            return R.error("余额不存在");
        }
        //查询子帐单
        EaBillSubtable eaBillSubtable = new EaBillSubtable();
        eaBillSubtable.setBillCode(eaBillMain.getBillCode());
        List<EaBillSubtable> list = eaBillSubtableMapper.selectEaBillSubtableList(eaBillSubtable);
        if (CollUtil.isEmpty(list)) {
            return R.error("无账单子表数据！");
        }
        BigDecimal balance = eaBalance.getAvailableBalance();
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal paidAmount = BigDecimal.ZERO;

        List<EaBillSubtable> updateList = new ArrayList<>();
        List<EaBalanceTransaction> transactionList = new ArrayList<>();
        for (EaBillSubtable billSubtable : list) {
            totalAmount = totalAmount.add(billSubtable.getBillAmount());
            Boolean flag = false;
            if (subtableId != null) {
                if (billSubtable.getBillSubtableId().equals(subtableId)) {
                    if ("settled".equals(billSubtable.getBillStatus())) {
                        flag = true;
                    } else {
                        return R.error("该账单未结清，无法撤销结算");
                    }
                }
            } else {
                if ("settled".equals(billSubtable.getBillStatus())) {
                    flag = true;
                } else {
                    return R.error("该账单未结清，无法撤销结算");
                }
            }

            if (flag) {
                balance = balance.add(billSubtable.getPaidAmount());
                addTransactionList(transactionList, billSubtable, userInfo, "撤销结算", eaBalance.getBalanceId(), balance);
                addEaBillSubtableList(updateList, billSubtable, BigDecimal.ZERO, "unsettled", userInfo);
            } else {
                paidAmount = paidAmount.add(billSubtable.getPaidAmount());
            }
        }
        //设置主表数据
        eaBillMain.setBillAmount(totalAmount);
        eaBillMain.setPaidAmount(paidAmount);
        eaBillMain.setBillStatus("unsettled");
        //设置余额数据
        eaBalance.setTotalExpenditure(eaBalance.getTotalExpenditure().add(eaBalance.getAvailableBalance().subtract(balance)));
        eaBalance.setAvailableBalance(balance);
        //修改数据库信息
        updateEaBillInfo(eaBillMain, updateList, eaBalance, transactionList);

        R r = R.success();
        r.setMsg("取消结算成功");
        return r;
    }

    /**
     * 生成账单（一次）
     *
     * @param eaBillMain 生成逻辑参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R generateBill(EaBillMain eaBillMain) {
        if (eaBillMain == null || StrUtil.isBlank(eaBillMain.getShiftNo())) {
            return R.error("请传入班次号");
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        List<EaBillMain> mainList = new ArrayList<>();
        //统计箱汇总数据，整合成账单子表数据
        List<EaBillSubtable> subtableList = eaBillSubtableMapper.statisticsContainerSummary(eaBillMain);
        for (EaBillSubtable subtable : subtableList) {
            EaBillMain mainParams = new EaBillMain();
            EaBillSubtable subtableParams = new EaBillSubtable();
            subtableParams.setShiftNo(subtable.getShiftNo());
            subtableParams.setPayerCode(subtable.getPayerCode());
            subtableParams.setPayeeCode(subtable.getPayeeCode());
            subtableParams.setDelFlag("N");
            EaBillSubtable oldSubtable = eaBillSubtableMapper.selectOne(subtableParams);
            String billCode = null;
            if (oldSubtable == null) {
                billCode = sysNoConfigService.genNo("FDBL");
                subtable.setBillCode(billCode);
                subtable.setBillSubtableCode(sysNoConfigService.genNo("FDC"));
                subtable.setPaidAmount(BigDecimal.ZERO);
                subtable.setCreateBy(userInfo.getRealName());
                subtable.setCreateById(userInfo.getId());
                subtable.setBillStatus("unsettled");
            } else {
                billCode = oldSubtable.getBillCode();
                subtable.setBillSubtableId(oldSubtable.getBillSubtableId());
                subtable.setPaidAmount(oldSubtable.getPaidAmount());
                subtable.setUpdateBy(userInfo.getRealName());
                subtable.setUpdateById(userInfo.getId());
                if (subtable.getBillAmount().compareTo(subtable.getPaidAmount()) == 0) {
                    subtable.setBillStatus("settled");
                } else {
                    subtable.setBillStatus("unsettled");
                }
            }
            if (StrUtil.isBlank(subtable.getBillFeeType())) {
                subtable.setBillFeeType("运费");
            }
            mainParams.setBillCode(billCode);
            mainParams.setShiftNo(subtable.getShiftNo());
            mainParams.setPayerCode(subtable.getPayerCode());
            mainParams.setPayeeCode(subtable.getPayeeCode());
            mainParams.setDelFlag("N");
            EaBillMain oldMain = eaBillMainMapper.selectOne(mainParams);
            if (oldMain == null) {
                EaBillMain billMain = new EaBillMain();
                BeanUtil.copyProperties(subtable, billMain);
                billMain.setBillCode(billCode);
                billMain.setBillStage("stage_one");
                mainList.add(billMain);
            } else {
                oldMain.setBillAmount(subtable.getBillAmount());
                oldMain.setPaidAmount(oldSubtable.getPaidAmount());
                oldMain.setBillStatus(subtable.getBillStatus());
                oldMain.setPayeeName(subtable.getPayeeName());
                oldMain.setPlatformCode(subtable.getPlatformCode());
                oldMain.setPlatformName(subtable.getPlatformName());
                oldMain.setBillContainerQuantity(subtable.getBillContainerQuantity());
                oldMain.setBillSlotQuantity(subtable.getBillSlotQuantity());
                oldMain.setUpdateBy(userInfo.getRealName());
                oldMain.setUpdateById(userInfo.getId());
                oldMain.setBillStage("stage_one");
                mainList.add(oldMain);
            }
        }
        if (mainList.size() > 0) {
            int step = 50;
            for (int i = 0; i < mainList.size(); i += step) {
                eaBillMainMapper.insertEaBillMainList(mainList.subList(i, (i + step) < mainList.size() ? (i + step) : mainList.size()));
            }
        }
        if (subtableList.size() > 0) {
            int step = 50;
            for (int i = 0; i < subtableList.size(); i += step) {
                eaBillSubtableMapper.insertEaBillSubtableList(subtableList.subList(i, (i + step) < subtableList.size() ? (i + step) : subtableList.size()));
            }
        }

        return R.success();
    }

    /**
     * 生成二次账单
     *
     * @param eaBillMain 生成逻辑参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R generateBillTwo(EaBillMain eaBillMain) {
        if (eaBillMain == null || StrUtil.isBlank(eaBillMain.getShiftNo())) {
            return R.error("请传入班次号");
        }
        if (StrUtil.isBlank(eaBillMain.getPlatformCode()) || StrUtil.isBlank(eaBillMain.getPlatformName())) {
            return R.error("请传入平台编码与平台名称");
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        List<EaBillMain> mainList = new ArrayList<>();
        List<EaFee> feeList = new ArrayList<>();
        List<EaBalance> balanceList = new ArrayList<>();
        List<EaBillSubtable> delList = new ArrayList<>();
        List<EaBalanceTransaction> transactionList = new ArrayList<>();
        Map<String, EaBillMain> map = new HashMap<>();
        //统计箱汇总数据，整合成账单子表数据
        List<EaBillSubtable> subtableList = eaBillSubtableMapper.statisticsFee(eaBillMain);
        for (EaBillSubtable subtable : subtableList) {
            EaBillMain billMain = getBillMain(subtable);
            billMain.setPlatformCode(eaBillMain.getPlatformCode());
            billMain.setPlatformName(eaBillMain.getPlatformName());
            subtable.setPlatformCode(eaBillMain.getPlatformCode());
            subtable.setPlatformName(eaBillMain.getPlatformName());
            String billCode = billMain.getBillCode();
            if (map.get(billCode) == null) {
                map.put(billMain.getBillCode(), billMain);
                mainList.add(billMain);
            } else {
                billMain = map.get(billCode);
            }
            List<EaBillSubtable> billSubtableList = billMain.getEaBillSubtableList();
            if (CollUtil.isEmpty(billSubtableList)) {
                billSubtableList = new ArrayList<>();
            }
            billSubtableList.add(subtable);
            billMain.setEaBillSubtableList(billSubtableList);
        }
        if (CollUtil.isNotEmpty(mainList)) {
            for (String billCode : map.keySet()) {
                EaBillMain billMain = map.get(billCode);
                if (CollUtil.isEmpty(billMain.getEaBillSubtableList())) {
                    continue;
                } else if (billMain.getEaBillSubtableList().size() == 1) {
                    EaBillSubtable subtable = billMain.getEaBillSubtableList().get(0);
                    //获取旧账单子表
                    List<EaBillSubtable> oldSubtableList = getOldEaBillSubtable(billMain);
                    // 账单子表不存在时创建新账单子表数据
                    if (CollUtil.isEmpty(oldSubtableList)) {
                        // 创建新账单子表数据
                        this.createEaBillSubtable(subtable, billMain, userInfo);
                        // 设置费用与账单子表关联
                        feeList.add(this.getFee(subtable));
                    } else {
                        // 账单子表存在时更新 校验子表数量是否大于一个
                        if (oldSubtableList.size() > 1) {
                            // 大于一个时将多余数据添加到删除序列中
                            for (int i = 1; i < oldSubtableList.size(); i++) {
                                delList.add(oldSubtableList.get(i));
                            }
                        }
                        EaBillSubtable oldSubtable = oldSubtableList.get(0);
                        // 更新账单子表数据
                        this.updateEaBillSubtable(subtable, billMain, userInfo, oldSubtable, feeList);
                        // 检验是否需要退款
                        this.refundEaBillSubtable(subtable, userInfo, oldSubtable, balanceList, transactionList);
                    }
                } else {
                    //获取旧账单子表
                    List<EaBillSubtable> oldSubtableList = getOldEaBillSubtable(billMain);
                    // 创建子帐单编码与数据对照关系
                    Map<String, EaBillSubtable> subtableMap = new HashMap<>();
                    for (EaBillSubtable oldSubtable : oldSubtableList) {
                        subtableMap.put(oldSubtable.getBillSubtableCode(), oldSubtable);
                    }

                    for (EaBillSubtable subtable : billMain.getEaBillSubtableList()) {
                        // 校验旧数据中是否存在该账单编号对应的数据
                        EaBillSubtable oldSubtable = subtableMap.get(subtable.getBillSubtableCode());
                        // 不存在时 初始化数据
                        if (oldSubtable == null) {
                            this.createEaBillSubtable(subtable, billMain, userInfo);
                            feeList.add(this.getFee(subtable));
                        } else {
                            // 存在时 移除对照关系
                            subtableMap.remove(subtable.getBillSubtableCode());
                            // 更新账单子表数据
                            this.updateEaBillSubtable(subtable, billMain, userInfo, oldSubtable, feeList);
                            // 检验是否需要退款
                            this.refundEaBillSubtable(subtable, userInfo, oldSubtable, balanceList, transactionList);
                        }
                    }
                    // 将未匹配成功的数据添加到删除序列中
                    for (String code : subtableMap.keySet()) {
                        delList.add(subtableMap.get(code));
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(delList)) {
            // 当存在删除数据时 校验是否退回余额
            for (EaBillSubtable subtable : delList) {
                // 查询余额
                EaBalance eaBalance = getEaBalance(balanceList, subtable);
                if (eaBalance == null) {
                    continue;
                }
                if (subtable.getPaidAmount().compareTo(BigDecimal.ZERO) < 0) {
                    eaBalance.setTotalExpenditure(eaBalance.getTotalExpenditure().subtract(subtable.getPaidAmount()));
                    subtable.setBillAmount(subtable.getPaidAmount());
                    BigDecimal balance = eaBalance.getAvailableBalance().add(subtable.getPaidAmount());
                    addTransactionList(transactionList, subtable, userInfo, "撤销结算", eaBalance.getBalanceId(), balance);
                }
            }
        }
        if (CollUtil.isNotEmpty(delList)) {
            for (EaBillSubtable subtable : delList) {
                eaBillSubtableMapper.deleteEaBillSubtableById(subtable.getBillSubtableId());
            }
        }

        if (CollUtil.isNotEmpty(mainList)) {
            // 重新计算账单金额
            computeBillAmount(mainList);
            // 保存账单数据
            int step = 50;
            for (int i = 0; i < mainList.size(); i += step) {
                eaBillMainMapper.insertEaBillMainList(mainList.subList(i, (i + step) < mainList.size() ? (i + step) : mainList.size()));
            }
        }
        if (CollUtil.isNotEmpty(subtableList)) {
            // 保存账单子数据
            int step = 50;
            for (int i = 0; i < subtableList.size(); i += step) {
                eaBillSubtableMapper.insertEaBillSubtableList(subtableList.subList(i, (i + step) < subtableList.size() ? (i + step) : subtableList.size()));
            }
        }

        if (CollUtil.isNotEmpty(feeList)) {
            for (EaFee fee : feeList) {
                eaFeeMapper.updateBillSubtableCode(fee);
            }
        }

        if (CollUtil.isNotEmpty(balanceList)) {
            for (EaBalance eaBalance : balanceList) {
                eaBalanceMapper.updateEaBalance(eaBalance);
            }
        }

        if (CollUtil.isNotEmpty(transactionList)) {
            // 保存账单子数据
            int step = 50;
            for (int i = 0; i < transactionList.size(); i += step) {
                eaBalanceTransactionMapper.insertEaBalanceTransactionList(transactionList.subList(i, (i + step) < transactionList.size() ? (i + step) : transactionList.size()));
            }
        }


        return R.success();
    }

    private void refundEaBillSubtable(EaBillSubtable subtable, SecruityUser userInfo, EaBillSubtable oldSubtable, List<EaBalance> balanceList, List<EaBalanceTransaction> transactionList) {
        // 校验旧账单金额已支付金额是否大于新帐单的账单金额
        if (oldSubtable.getPaidAmount().compareTo(subtable.getBillAmount()) > 0) {
            oldSubtable.setBillAmount(oldSubtable.getPaidAmount().subtract(subtable.getBillAmount()));
            // 查询余额
            EaBalance eaBalance = getEaBalance(balanceList, oldSubtable);
            if (eaBalance == null) {
                return;
            }
            eaBalance.setTotalExpenditure(eaBalance.getTotalExpenditure().subtract(oldSubtable.getBillAmount()));
            BigDecimal balance = eaBalance.getAvailableBalance().add(oldSubtable.getBillAmount());
            addTransactionList(transactionList, oldSubtable, userInfo, "撤销结算", eaBalance.getBalanceId(), balance);
            subtable.setPaidAmount(subtable.getBillAmount());
        }
        if (subtable.getBillAmount().compareTo(subtable.getPaidAmount()) == 0) {
            subtable.setBillStatus("settled");
        } else {
            subtable.setBillStatus("unsettled");
        }
    }

    private void updateEaBillSubtable(EaBillSubtable subtable, EaBillMain billMain, SecruityUser userInfo, EaBillSubtable oldSubtable, List<EaFee> feeList) {
        if ("0".equals(subtable.getBillSubtableCode())) {
            subtable.setBillSubtableCode(oldSubtable.getBillSubtableCode());
            feeList.add(this.getFee(subtable));
        }
        subtable.setBillCode(oldSubtable.getBillCode());
        subtable.setPaidAmount(oldSubtable.getPaidAmount());
        subtable.setBillSubtableId(oldSubtable.getBillSubtableId());
        subtable.setPaidAmount(oldSubtable.getPaidAmount());
        subtable.setUpdateBy(userInfo.getRealName());
        subtable.setUpdateById(userInfo.getId());
    }

    private void createEaBillSubtable(EaBillSubtable subtable, EaBillMain billMain, SecruityUser userInfo) {
        if (StrUtil.isBlank(subtable.getBillSubtableCode()) || "0".equals(subtable.getBillSubtableCode())) {
            subtable.setBillSubtableCode(sysNoConfigService.genNo("FDC"));
        }
        subtable.setPaidAmount(BigDecimal.ZERO);
        subtable.setCreateBy(userInfo.getRealName());
        subtable.setCreateById(userInfo.getId());
        subtable.setBillStatus("unsettled");
        subtable.setBillCode(billMain.getBillCode());
    }

    private List<EaBillSubtable> getOldEaBillSubtable(EaBillMain billMain) {
        EaBillSubtable subtableParams = new EaBillSubtable();
        subtableParams.setShiftNo(billMain.getShiftNo());
        subtableParams.setPayerCode(billMain.getPayerCode());
        subtableParams.setPayeeCode(billMain.getPayeeCode());
        subtableParams.setDelFlag("N");
        List<EaBillSubtable> oldSubtableList = eaBillSubtableMapper.selectList(new EntityWrapper<>(subtableParams).orderBy("create_time", true));
        return oldSubtableList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R delBill(EaBillMain eaBillMain) {
        if (eaBillMain == null || StrUtil.isBlank(eaBillMain.getShiftNo())) {
            return R.error("请输入班次号");
        }
        EaBillSubtable params = new EaBillSubtable();
        params.setShiftNo(eaBillMain.getShiftNo());
        params.setPayerCode(eaBillMain.getPayerCode());
        params.setPayeeCode(eaBillMain.getPayeeCode());
        params.setBillStatus(BillStatusEnum.SETTLED.getKey());
        List<EaBillSubtable> list = eaBillSubtableMapper.selectEaBillSubtableList(params);
        if (CollUtil.isNotEmpty(list)) {
            return R.error("对应账单已结算，无法删除");
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        eaBillMain.setUpdateBy(userInfo.getRealName());
        eaBillMain.setUpdateById(userInfo.getId());
        eaBillMainMapper.delBillMainByShiftNo(eaBillMain);
        eaBillSubtableMapper.delBillSubtableByShiftNo(eaBillMain);
        return R.success();
    }

    @Override
    public R exportFreightAccountCity(EaBillMain eaBillMain, HttpServletResponse response) {
        EaBillMain billMain = eaBillMainMapper.selectEaBillMainByCode(eaBillMain.getBillCode());
        if (billMain == null) {
            return new R<>(new Throwable("该结算单不存在"));
        }
        if (StrUtil.isBlank(billMain.getShiftNo())) {
            return new R<>(new Throwable("该结算单没有班次信息，无法导出"));
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String[] split = billMain.getShiftNo().split(",");
        if (split.length > 0) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
            //创建工作薄对象
            HSSFWorkbook workbook = new HSSFWorkbook();
            CellStyle cellStyle = getCellStyle(workbook, (short) 12);
            DataFormat format = workbook.createDataFormat();
            cellStyle.setDataFormat(format.getFormat("0.00"));
            CellStyle style = getCellStyle(workbook, (short) 12);
            for (String shiftNo : split) {
                Shifmanagement shifmanagement = shifmanagementMapper.selectShifmanagementByPlatCodeAndShiftNo(userInfo.getPlatformCode(), shiftNo);
                Integer containerNum = 0;
                BigDecimal yfAmount = BigDecimal.ZERO;
                BigDecimal ysAmount = BigDecimal.ZERO;
                Integer rowIndex = 0;
                //创建sheet页
                HSSFSheet sheet = workbook.createSheet(shiftNo);
                //创建表头
                rowIndex = setCityTitle(sdf, shifmanagement, rowIndex, sheet, workbook);
                //创建内容
                EaBillSubtable subtable = new EaBillSubtable();
                subtable.setShiftNo(shiftNo);
                subtable.setPlatformCode(userInfo.getPlatformCode());
                List<FreightAccountCityDTO> list = eaBillSubtableMapper.selectFreightAccountCityDTOList(subtable);
                if (CollUtil.isNotEmpty(list)) {
                    Map<String, List<FreightAccountCityDTO>> map = new HashMap<>();
                    for (FreightAccountCityDTO dto : list) {
                        if (dto.getContainerNum() != null) {
                            containerNum = containerNum + dto.getContainerNum();
                        }
                        dto.setYsAmount(dto.getYsPrice().multiply(new BigDecimal(dto.getContainerNum())));
                        dto.setYfAmount(dto.getYfPrice().multiply(new BigDecimal(dto.getContainerNum())));
                        yfAmount = yfAmount.add(dto.getYfAmount());
                        ysAmount = ysAmount.add(dto.getYsAmount());

                        List<FreightAccountCityDTO> dtoList = map.get(dto.getCustomerName());
                        if (CollUtil.isEmpty(dtoList)) {
                            dtoList = new ArrayList<>();
                        }
                        dtoList.add(dto);
                        map.put(dto.getCustomerName(), dtoList);
                    }
                    for (String customerName : map.keySet()) {
                        List<FreightAccountCityDTO> dtoList = map.get(customerName);
                        if (CollUtil.isEmpty(dtoList)) {
                            continue;
                        }
                        rowIndex = setCityList(dtoList, rowIndex, sheet, style, cellStyle);
                    }
                }
                //创建合计
                Row row = sheet.createRow(rowIndex);
                Cell cell0 = row.createCell(0);
                cell0.setCellValue("合计");
                for (int i = 1; i <= 6; i++) {
                    row.createCell(i);
                }
                Cell cell7 = row.createCell(7);
                cell7.setCellValue(containerNum);
                row.createCell(8);
                Cell cell9 = row.createCell(9);
                cell9.setCellValue(Double.parseDouble(String.format("%.2f", yfAmount)));
                row.createCell(10);
                Cell cell11 = row.createCell(11);
                cell11.setCellValue(Double.parseDouble(String.format("%.2f", ysAmount)));

                for (Cell cellTemp : row) {
                    if (cellTemp.getColumnIndex() == 9 || cellTemp.getColumnIndex() == 11) {
                        cellTemp.setCellStyle(cellStyle);
                    } else {
                        cellTemp.setCellStyle(style);
                    }
                }
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 6));

                // 自动调整所有列的宽度
                for (int i = 8; i <= 11; i++) {
                    sheet.autoSizeColumn(i);
                }
            }

            try {
                //response为HttpServletResponse对象
                response.setContentType("application/vnd.ms-excel;charset=utf-8");
                //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("运费核算表", "utf-8") + ".xls");
                ServletOutputStream out = response.getOutputStream();
                workbook.write(out);
                out.flush();
                // 关闭writer，释放内存
                out.close();
                workbook.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    private EaBalance getEaBalance(List<EaBalance> balanceList, EaBillSubtable subtable) {
        if (CollUtil.isNotEmpty(balanceList)) {
            for (EaBalance balance : balanceList) {
                if (balance.getPayerCode().equals(subtable.getPayerCode()) && balance.getPayeeCode().equals(subtable.getPayeeCode())) {
                    return balance;
                }
            }
        }
        EaBalance eaBalance = new EaBalance();
        eaBalance.setDelFlag("N");
        eaBalance.setPayerCode(subtable.getPayerCode());
        eaBalance.setPayeeCode(subtable.getPayeeCode());
        eaBalance = eaBalanceMapper.selectOne(eaBalance);
        if (eaBalance != null) {
            balanceList.add(eaBalance);
        }
        return eaBalance;
    }

    private EaFee getFee(EaBillSubtable subtable) {
        EaFee fee = new EaFee();
        BeanUtil.copyProperties(subtable, fee);
        fee.setBillSubtableCode(subtable.getBillSubtableCode());
        return fee;
    }

    private void computeBillAmount(List<EaBillMain> mainList) {
        for (EaBillMain billMain : mainList) {
            Integer billContainerQuantity = 0;
            BigDecimal billSlotQuantity = BigDecimal.ZERO;
            BigDecimal billAmount = BigDecimal.ZERO;
            BigDecimal paidAmount = BigDecimal.ZERO;
            List<EaBillSubtable> billSubtableList = billMain.getEaBillSubtableList();
            for (EaBillSubtable billSubtable : billSubtableList) {
                billContainerQuantity += billSubtable.getBillContainerQuantity() == null ? 0 : billSubtable.getBillContainerQuantity();
                billSlotQuantity = billSlotQuantity.add(billSubtable.getBillSlotQuantity() == null ? BigDecimal.ZERO : billSubtable.getBillSlotQuantity());
                billAmount = billAmount.add(billSubtable.getBillAmount());
                paidAmount = paidAmount.add(billSubtable.getPaidAmount());
            }
            billMain.setBillContainerQuantity(billContainerQuantity);
            billMain.setBillSlotQuantity(billSlotQuantity);
            billMain.setBillAmount(billAmount);
            billMain.setPaidAmount(paidAmount);
            billMain.setBillStage(BillStageEnum.TWO.getKey());
            if (billAmount.compareTo(paidAmount) > 0) {
                billMain.setBillStatus("unsettled");
            } else {
                billMain.setBillStatus("settled");
            }
        }
    }

    private EaBillMain getBillMain(EaBillSubtable subtable) {
        EaBillMain mainParams = new EaBillMain();
        mainParams.setShiftNo(subtable.getShiftNo());
        mainParams.setPayerCode(subtable.getPayerCode());
        mainParams.setPayeeCode(subtable.getPayeeCode());
        mainParams.setDelFlag("N");
        EaBillMain billMain = eaBillMainMapper.selectOne(mainParams);
        if (billMain == null) {
            billMain = new EaBillMain();
            BeanUtil.copyProperties(subtable, billMain);
            billMain.setBillCode(sysNoConfigService.genNo("FDBL"));
            billMain.setBillStage(BillStageEnum.TWO.getKey());
        } else {
            subtable.setBillCode(billMain.getBillCode());
        }
        return billMain;
    }

    private void updateEaBillInfo(EaBillMain eaBillMain, List<EaBillSubtable> updateList, EaBalance eaBalance, List<EaBalanceTransaction> transactionList) {
        //修改主表状态
        eaBillMainMapper.updateEaBillMain(eaBillMain);
        //修改子表状态
        if (CollUtil.isNotEmpty(updateList)) {
            for (EaBillSubtable billSubtable : updateList) {
                eaBillSubtableMapper.updateEaBillSubtable(billSubtable);
            }
        }
        //修改余额数据
        eaBalanceMapper.updateEaBalance(eaBalance);
        //添加余额变更明细
        if (CollUtil.isNotEmpty(transactionList)) {
            for (EaBalanceTransaction eaBalanceTransaction : transactionList) {
                eaBalanceTransactionMapper.insertEaBalanceTransaction(eaBalanceTransaction);
            }
        }
    }

    public Integer setCityList(List<FreightAccountCityDTO> list, Integer rowIndex, HSSFSheet sheet, CellStyle style, CellStyle cellStyle) {
        style.setWrapText(true);
        for (FreightAccountCityDTO dto : list) {
            Row row = sheet.createRow(rowIndex);
            Cell cell0 = row.createCell(0);
            cell0.setCellValue(rowIndex - 3);
            Cell cell1 = row.createCell(1);
            cell1.setCellValue(dto.getCustomerName());
            Cell cell2 = row.createCell(2);
            cell2.setCellValue(dto.getShippingLine());
            Cell cell3 = row.createCell(3);
            cell3.setCellValue(dto.getDestinationName());
            Cell cell4 = row.createCell(4);
            cell4.setCellValue(dto.getPortStation());
            Cell cell5 = row.createCell(5);
            cell5.setCellValue(IdentificationEnum.fromKey(dto.getIdentification()));
            Cell cell6 = row.createCell(6);
            cell6.setCellValue(dto.getContainerType());
            Cell cell7 = row.createCell(7);
            cell7.setCellValue(dto.getContainerNum());
            Cell cell8 = row.createCell(8);
            cell8.setCellValue(Double.parseDouble(String.format("%.2f", dto.getYfPrice())));
            Cell cell9 = row.createCell(9);
            cell9.setCellValue(Double.parseDouble(String.format("%.2f", dto.getYfAmount())));
            Cell cell10 = row.createCell(10);
            cell10.setCellValue(Double.parseDouble(String.format("%.2f", dto.getYsPrice())));
            Cell cell11 = row.createCell(11);
            cell11.setCellValue(Double.parseDouble(String.format("%.2f", dto.getYsAmount())));
            for (Cell cellTemp : row) {
                if (cellTemp.getColumnIndex() >= 8) {
                    cellTemp.setCellStyle(cellStyle);
                } else {
                    cellTemp.setCellStyle(style);
                }
            }
            rowIndex++;
        }
        if (list.size() > 1) {
            sheet.addMergedRegion(new CellRangeAddress(rowIndex - list.size(), rowIndex - 1, 1, 1));
            sheet.addMergedRegion(new CellRangeAddress(rowIndex - list.size(), rowIndex - 1, 2, 2));
            sheet.addMergedRegion(new CellRangeAddress(rowIndex - list.size(), rowIndex - 1, 3, 3));
            sheet.addMergedRegion(new CellRangeAddress(rowIndex - list.size(), rowIndex - 1, 4, 4));
        }
        return rowIndex;
    }


    private Integer setCityTitle(SimpleDateFormat sdf, Shifmanagement shifmanagement, Integer rowIndex, HSSFSheet sheet, HSSFWorkbook workbook) {
        CellStyle style = getCellStyle(workbook, (short) 12);

        //字体
        CellStyle style1 = getCellStyle(workbook, (short) 22);
        //创建第一行——标题
        Row row = sheet.createRow(rowIndex);
        row.setHeightInPoints(35);//设置行高
        //填数据
        Cell cell = row.createCell(0);
        cell.setCellValue("运费核算表");
        for (int i = 1; i <= 11; i++) {
            row.createCell(i);
        }
        for (Cell cellTemp : row) {
            cellTemp.setCellStyle(style1);
        }
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 11));
        rowIndex++;

        Row row1 = sheet.createRow(rowIndex);
        Cell cell10 = row1.createCell(0);
        cell10.setCellValue("预发运日期");
        row1.createCell(1);
        Cell cell12 = row1.createCell(2);
        if (shifmanagement.getPlanShipTime() != null) {
            cell12.setCellValue(sdf.format(shifmanagement.getPlanShipTime()));
        }
        row1.createCell(3);
        row1.createCell(4);
        Cell cell15 = row1.createCell(5);
        cell15.setCellValue("班列代码");
        row1.createCell(6);
        row1.createCell(7);
        Cell cell18 = row1.createCell(8);
        cell18.setCellValue(shifmanagement.getShiftName());
        for (int i = 9; i <= 11; i++) {
            row1.createCell(i);
        }
        for (Cell cellTemp : row1) {
            cellTemp.setCellStyle(style);
        }
        rowIndex++;

        Row row2 = sheet.createRow(rowIndex);
        for (int i = 0; i <= 4; i++) {
            row2.createCell(i);
        }
        Cell cell25 = row2.createCell(5);
        cell25.setCellValue("类型");
        Cell cell26 = row2.createCell(6);
        cell26.setCellValue("箱型");
        Cell cell27 = row2.createCell(7);
        cell27.setCellValue("箱量");
        Cell cell28 = row2.createCell(8);
        cell28.setCellValue("应付运费");
        row2.createCell(9);
        Cell cell210 = row2.createCell(10);
        cell210.setCellValue("应收运费");
        row2.createCell(11);
        for (Cell cellTemp : row2) {
            cellTemp.setCellStyle(style);
        }
        rowIndex++;

        Row row3 = sheet.createRow(rowIndex);
        Cell cell30 = row3.createCell(0);
        cell30.setCellValue("序号");
        Cell cell31 = row3.createCell(1);
        cell31.setCellValue("客户名称");
        Cell cell32 = row3.createCell(2);
        cell32.setCellValue("线路");
        Cell cell33 = row3.createCell(3);
        cell33.setCellValue("发站");
        Cell cell34 = row3.createCell(4);
        cell34.setCellValue("口岸站");
        for (int i = 5; i <= 7; i++) {
            row3.createCell(i);
        }
        Cell cell38 = row3.createCell(8);
        cell38.setCellValue("单价");
        Cell cell39 = row3.createCell(9);
        cell39.setCellValue("金额");
        Cell cell310 = row3.createCell(10);
        cell310.setCellValue("单价");
        Cell cell311 = row3.createCell(11);
        cell311.setCellValue("金额");
        for (Cell cellTemp : row3) {
            cellTemp.setCellStyle(style);
        }
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 2, rowIndex - 1, 0, 1));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 2, rowIndex - 1, 2, 4));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 2, rowIndex - 2, 5, 7));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 2, rowIndex - 2, 8, 11));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex, 5, 5));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex, 6, 6));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex, 7, 7));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex - 1, 8, 9));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex - 1, 10, 11));
        rowIndex++;
        return rowIndex;
    }

    private static CellStyle getCellStyle(HSSFWorkbook workbook, short num) {
        //字体
        HSSFFont font = workbook.createFont();
        //样式
        font.setFontHeightInPoints(num);
        font.setFontName("方正小标宋简体");
        CellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }
}
