package com.huazheng.tunny.ocean.service.eaapproval;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.ocean.util.R;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.eaapproval.EaApprovalConfiguration;
import com.huazheng.tunny.ocean.api.entity.eaapproval.EaApprovalRecord;

import java.util.List;

/**
 * 审批记录 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2025-06-17 10:02:19
 */
public interface EaApprovalRecordService extends IService<EaApprovalRecord> {
    /**
     * 查询审批记录信息
     *
     * @param id 审批记录ID
     * @return 审批记录信息
     */
    public EaApprovalRecord selectEaApprovalRecordById(Long id);

    /**
     * 查询审批记录列表
     *
     * @param eaApprovalRecord 审批记录信息
     * @return 审批记录集合
     */
    public List<EaApprovalRecord> selectEaApprovalRecordList(EaApprovalRecord eaApprovalRecord);


    /**
     * 分页模糊查询审批记录列表
     * @return 审批记录集合
     */
    public Page selectEaApprovalRecordListByLike(Query query);



    /**
     * 新增审批记录
     *
     * @param eaApprovalRecord 审批记录信息
     * @return 结果
     */
    public int insertEaApprovalRecord(EaApprovalRecord eaApprovalRecord);

    /**
     * 修改审批记录
     *
     * @param eaApprovalRecord 审批记录信息
     * @return 结果
     */
    public int updateEaApprovalRecord(EaApprovalRecord eaApprovalRecord);

    /**
     * 删除审批记录
     *
     * @param id 审批记录ID
     * @return 结果
     */
    public int deleteEaApprovalRecordById(Long id);

    /**
     * 批量删除审批记录
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteEaApprovalRecordByIds(Integer[] ids);

    String save(List<EaApprovalConfiguration> approvalConfigs,String uuid,String desc);

    /**
     * 审批同意
     * @param eaApprovalRecord
     * @return
     */
    R agree(EaApprovalRecord eaApprovalRecord);

    /**
     * 驳回
     * @param eaApprovalRecord
     * @return
     */
    R rejected(EaApprovalRecord eaApprovalRecord);

    List<EaApprovalRecord> getFullNodeInfo(EaApprovalRecord eaApprovalRecord);
}

