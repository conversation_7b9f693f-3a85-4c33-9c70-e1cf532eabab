package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfWarehouse;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * e融同步仓单表 服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-04 10:08:04
 */
public interface EfWarehouseService extends IService<EfWarehouse> {
    /**
     * 查询e融同步仓单表信息
     *
     * @param rowId e融同步仓单表ID
     * @return e融同步仓单表信息
     */
    public EfWarehouse selectEfWarehouseById(String rowId);

    /**
     * 查询e融同步仓单表列表
     *
     * @param efWarehouse e融同步仓单表信息
     * @return e融同步仓单表集合
     */
    public List<EfWarehouse> selectEfWarehouseList(EfWarehouse efWarehouse);


    /**
     * 分页模糊查询e融同步仓单表列表
     * @return e融同步仓单表集合
     */
    public Page selectEfWarehouseListByLike(Query query);



    /**
     * 新增e融同步仓单表
     *
     * @param list e融同步仓单表信息
     * @return 结果
     */
    public String insertEfWarehouse(List<EfWarehouse> list);

    /**
     * 修改e融同步仓单表
     *
     * @param efWarehouse e融同步仓单表信息
     * @return 结果
     */
    public int updateEfWarehouse(EfWarehouse efWarehouse);

    /**
     * 删除e融同步仓单表
     *
     * @param rowId e融同步仓单表ID
     * @return 结果
     */
    public int deleteEfWarehouseById(String rowId);

    /**
     * 批量删除e融同步仓单表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfWarehouseByIds(Integer[] rowIds);

}

