package com.huazheng.tunny.ocean.service.ealedgermain.impl;

import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.ocean.api.entity.Shifmanagement;
import com.huazheng.tunny.ocean.api.vo.FdInvoiceRevenueLedgerDetailsVO;
import com.huazheng.tunny.ocean.api.vo.FdInvoiceRevenueLedgerTitleVO;
import com.huazheng.tunny.ocean.mapper.ShifmanagementMapper;
import com.huazheng.tunny.ocean.mapper.ealedgermain.EaFdInvoiceRevenueLedgerMapper;
import com.huazheng.tunny.ocean.util.ExportStyleUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * 导出收入台账
 *
 * <AUTHOR>
 * @since 2025/4/11 上午10:07
 **/
@Service
public class EaFdInvoiceRevenueLedgerService {
    @Autowired
    private ShifmanagementMapper shifmanagementMapper;

    @Autowired
    private EaFdInvoiceRevenueLedgerMapper fdInvoiceRevenueLedgerMapper;

    /**
     * 回程标识
     */
    private static final String RETURN_TRIP = "R";

    /**
     * 固定的列数
     */
    private static final int FIXED_COLUMN_COUNT = 13;

    /**
     * @param fdInvoiceRevenueLedgerDetails, response
     * <AUTHOR>
     * @since 2025/4/15 上午10:13
     **/
    public void revenueLedgerExport(FdInvoiceRevenueLedgerDetailsVO fdInvoiceRevenueLedgerDetails, HttpServletResponse response) {

        try {
            FdInvoiceRevenueLedgerTitleVO title = getFdInvoiceRevenueLedgerTitle(fdInvoiceRevenueLedgerDetails);
            List<FdInvoiceRevenueLedgerDetailsVO> detailsList = getFdInvoiceRevenueLedgerDetails(fdInvoiceRevenueLedgerDetails);

            // 1. 创建工作簿和工作表
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("收入台账");


            sheet.setDefaultColumnWidth(15);
            sheet.setColumnWidth(0, 7 * 256);
            sheet.setColumnWidth(1, 13 * 256);
            sheet.setColumnWidth(2, 52 * 256);
            sheet.setColumnWidth(4, 10 * 256);
            sheet.setColumnWidth(10, 8 * 256);
            sheet.setColumnWidth(12, 13 * 256);
            CellStyle titleStyle = ExportStyleUtil.createTitleStyle(workbook);
            CellStyle titleDetailStyle = ExportStyleUtil.createTitleDetailStyle(workbook);
            CellStyle detailsStyle = ExportStyleUtil.createDetailsStyleCenter(workbook);
            CellStyle detailsStyleRight = ExportStyleUtil.createDetailsStyleRight(workbook);
            int rowNum = 0;
            // ===== 第一行：标题信息 =====
            Row row0 = sheet.createRow(rowNum++);
            row0.createCell(0).setCellValue("线路");
            row0.createCell(1).setCellValue(title.getShippingLine());
            row0.createCell(2).setCellValue("始发城市");
            row0.createCell(3).setCellValue(title.getDestinationName());
            row0.createCell(4).setCellValue("方向");
            row0.createCell(5).setCellValue(RETURN_TRIP.equals(title.getTrip()) ? "回程" : "去程");
            row0.createCell(6).setCellValue("发运日期");
            row0.createCell(7).setCellValue(title.getPlanShipTime());
            row0.createCell(8).setCellValue("口岸站");
            row0.createCell(9).setCellValue(title.getPortStation());
            row0.createCell(10).setCellValue("省级班列单号");
            row0.createCell(11).setCellValue(title.getProvinceShiftNo());
            row0.createCell(12);
            // 合并单元格，补足标题行对齐（列数 13 或更多）
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 11, 12));

            //遍历给单元格添加样式
            for (int i = 0; i < FIXED_COLUMN_COUNT; i++) {
                if (i % 2 == 0) {
                    row0.getCell(i).setCellStyle(titleStyle);
                } else {
                    row0.getCell(i).setCellStyle(titleDetailStyle);
                }

            }
            row0.setHeightInPoints(40);
            // ===== 第二行：合并为“箱基本信息” =====
            Row row1 = sheet.createRow(rowNum++);

            for (int i = 0; i < FIXED_COLUMN_COUNT; i++) {
                row1.createCell(i).setCellStyle(titleStyle);
            }
            Cell boxInfoCell = row1.getCell(0);
            boxInfoCell.setCellValue("箱基本信息");
            // 合并单元格（从第 0 列到第 12 列）
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 12));
            boxInfoCell.setCellStyle(titleStyle);
            row1.setHeightInPoints(25);

            // ===== 第三行：表头 =====
            Row row2 = sheet.createRow(rowNum++);
            String[] headers = {
                    "序号", "类型", "货源组织单位", "目的国", "箱型", "箱号", "报关单号", "国联运单号",
                    "国内段运费金额（人民币）", "境外段运费金额（美金）", "汇率", "国外段运费金额（人民币）", "其他费用（人民币）"
            };
            for (int i = 0; i < headers.length; i++) {
                row2.createCell(i).setCellValue(headers[i]);
            }
            for (int i = 0; i < headers.length; i++) {
                row2.getCell(i).setCellStyle(titleStyle);
            }
            row2.setHeightInPoints(40);
            sheet.createFreezePane(0, 3);
            // ===== 第四行开始写入数据 =====
            int index = 1;
            for (FdInvoiceRevenueLedgerDetailsVO detail : detailsList) {
                Row row = sheet.createRow(rowNum++);
                row.setHeightInPoints(25);
                int col = 0;
                row.createCell(col++).setCellValue(index++);
                row.createCell(col++).setCellValue(detail.getIdentificationName());
                row.createCell(col++).setCellValue(detail.getPlatformName());
                row.createCell(col++).setCellValue(detail.getDestinationCountry());
                row.createCell(col++).setCellValue(detail.getContainerTypeCode());
                row.createCell(col++).setCellValue(detail.getContainerNumber());
                row.createCell(col++).setCellValue(detail.getClearanceNumber());
                row.createCell(col++).setCellValue(detail.getWaybillLnNumber());
                row.createCell(col++).setCellValue(safeDouble(detail.getDomesticFreightAmount()));
                row.createCell(col++).setCellValue(safeDouble(detail.getOverseasFreightAmountForeignCurrency()));
                row.createCell(col++).setCellValue(safeDouble(detail.getExchangeRate()));
                row.createCell(col++).setCellValue(safeDouble(detail.getOverseasFreightAmount()));
                row.createCell(col).setCellValue(safeDouble(detail.getOtherFeesAmount()));
            }


            for (int j = 0; j < detailsList.size(); j++) {
                Row row = sheet.getRow(j + 3);
                for (int i = 0; i < FIXED_COLUMN_COUNT; i++) {
                    if (i > 7) {
                        row.getCell(i).setCellStyle(detailsStyleRight);
                    } else {
                        row.getCell(i).setCellStyle(detailsStyle);
                    }
                }
            }
            Map<String, BigDecimal> totalAmount = getTotalAmount(detailsList);
            Row totalRow = sheet.createRow(rowNum);
            totalRow.setHeightInPoints(25);
            for (int i = 0; i < FIXED_COLUMN_COUNT; i++) {
                totalRow.createCell(i).setCellStyle(detailsStyleRight);
            }
            sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 7));
            totalRow.getCell(0).setCellValue("运费合计金额：" + totalAmount.get("totalAmount"));
            totalRow.getCell(0).setCellStyle(titleDetailStyle);
            totalRow.getCell(8).setCellValue(safeDouble(totalAmount.get("domesticFreightAmount")));
            totalRow.getCell(9).setCellValue(safeDouble(totalAmount.get("overseasFreightAmountForeignCurrency")));
            totalRow.getCell(10).setCellValue("--");
            totalRow.getCell(11).setCellValue(safeDouble(totalAmount.get("overseasFreightAmount")));
            totalRow.getCell(12).setCellValue(safeDouble(totalAmount.get("otherFeesAmount")));


            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            String fileName = URLEncoder.encode("收入台账_" + title.getProvinceShiftName() + "(" + title.getProvinceShiftNo() + ")" + ".xlsx", "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (Exception e) {
            throw new RuntimeException("导出失败", e);
        }
    }


    /**
     * BigDecimal 转 double
     *
     * @param val 数值
     * @return double
     * <AUTHOR>
     * @since 2025/4/15 上午10:13
     **/
    private double safeDouble(BigDecimal val) {
        return val == null ? 0.0 : val.doubleValue();
    }

    /**
     * 获取收入台账的基本信息
     *
     * @param fdInvoiceRevenueLedgerDetails 开票信息
     * @return List<FdInvoiceRevenueLedgerDetailsVO>
     * <AUTHOR>
     * @since 2025/4/14 下午4:11
     **/
    public List<FdInvoiceRevenueLedgerDetailsVO> getFdInvoiceRevenueLedgerDetails(FdInvoiceRevenueLedgerDetailsVO fdInvoiceRevenueLedgerDetails) {
        //获取所有箱号数据
        List<FdInvoiceRevenueLedgerDetailsVO> containers = fdInvoiceRevenueLedgerMapper.selectContainerNumber(fdInvoiceRevenueLedgerDetails);
        //获取国内费用数据
        List<FdInvoiceRevenueLedgerDetailsVO> domesticFreightAmountList = fdInvoiceRevenueLedgerMapper.selectDomesticFreightAmount(fdInvoiceRevenueLedgerDetails);
        //获取境外费用数据
        List<FdInvoiceRevenueLedgerDetailsVO> overseasFreightAmountList = fdInvoiceRevenueLedgerMapper.selectOverseasFreightAmount(fdInvoiceRevenueLedgerDetails);
        //获取其他费用数据
        List<FdInvoiceRevenueLedgerDetailsVO> otherFeesAmountList = fdInvoiceRevenueLedgerMapper.selectOtherFeesAmount(fdInvoiceRevenueLedgerDetails);

        //把国内费用数据、境外费用数据、其他费用数据全部转成MAP，主键是开票申请单号-班次号-箱号
        // 将所有费用数据转换为Map，主键是开票申请单号-班次号-箱号
        Map<String, FdInvoiceRevenueLedgerDetailsVO> domesticFreightAmountMap = domesticFreightAmountList.stream()
                .collect(Collectors.toMap(
                        fee -> fee.getInvoiceApplicationCode() + "-" + fee.getShiftNo() + "-" + fee.getContainerNumber(),
                        fee -> fee));
        Map<String, FdInvoiceRevenueLedgerDetailsVO> overseasFreightAmountMap = overseasFreightAmountList.stream()
                .collect(Collectors.toMap(
                        fee -> fee.getInvoiceApplicationCode() + "-" + fee.getShiftNo() + "-" + fee.getContainerNumber(),
                        fee -> fee));
        Map<String, FdInvoiceRevenueLedgerDetailsVO> otherFeesAmountMap = otherFeesAmountList.stream()
                .collect(Collectors.toMap(
                        fee -> fee.getInvoiceApplicationCode() + "-" + fee.getShiftNo() + "-" + fee.getContainerNumber(),
                        fee -> fee));
        //根据箱号遍历数据，组成新的集合fdInvoiceRevenueLedgerDetailsVOList
        for (FdInvoiceRevenueLedgerDetailsVO container : containers) {

            String key = container.getInvoiceApplicationCode() + "-" + container.getShiftNo() + "-" + container.getContainerNumber();
            //获取国内费用数据
            if (domesticFreightAmountMap.containsKey(key)) {
                container.setDomesticFreightAmount(domesticFreightAmountMap.get(key).getDomesticFreightAmount());
            }
            if (overseasFreightAmountMap.containsKey(key)) {
                FdInvoiceRevenueLedgerDetailsVO overseasFreight = overseasFreightAmountMap.get(key);
                container.setOverseasFreightAmount(overseasFreight.getOverseasFreightAmount());
                container.setOverseasFreightAmountForeignCurrency(overseasFreight.getOverseasFreightAmountForeignCurrency());
                container.setExchangeRate(overseasFreight.getExchangeRate());
                if (overseasFreight.getOverseasFreightAmountForeignCurrency() != null
                        && overseasFreight.getOverseasFreightAmount() != null) {
                    BigDecimal exchangeRate = overseasFreight.getOverseasFreightAmount().divide(overseasFreight.getOverseasFreightAmountForeignCurrency(), 4, RoundingMode.HALF_UP);
                    container.setExchangeRate(exchangeRate);
                }
            }
            if (otherFeesAmountMap.containsKey(key)) {
                container.setOtherFeesAmount(otherFeesAmountMap.get(key).getOtherFeesAmount());
            }

        }
        return containers;
    }


    /**
     * 遍历标题
     *
     * @param fdInvoiceRevenueLedgerDetailsVO 开票信息
     * @return FdInvoiceRevenueLedgerTitleVO
     * <AUTHOR>
     * @since 2025/4/14 下午3:24
     **/
    private FdInvoiceRevenueLedgerTitleVO getFdInvoiceRevenueLedgerTitle(FdInvoiceRevenueLedgerDetailsVO fdInvoiceRevenueLedgerDetailsVO) {
        Shifmanagement sel = new Shifmanagement();
        sel.setShiftId(fdInvoiceRevenueLedgerDetailsVO.getShiftNo());
        sel.setPlatformCode(fdInvoiceRevenueLedgerDetailsVO.getPlatformCode());
        sel.setDeleteFlag("N");
        List<Shifmanagement> managementList = shifmanagementMapper.selectShifmanagementList(sel);
        FdInvoiceRevenueLedgerTitleVO fdInvoiceRevenueLedgerTitle = new FdInvoiceRevenueLedgerTitleVO();
        BeanUtil.copyProperties(managementList.get(0), fdInvoiceRevenueLedgerTitle);
        fdInvoiceRevenueLedgerTitle.setProvinceShiftName(managementList.get(0).getShiftName());
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        if (RETURN_TRIP.equals(managementList.get(0).getTrip()) && managementList.get(0).getResveredField06() != null) {
            fdInvoiceRevenueLedgerTitle.setPlanShipTime(formatter.format(managementList.get(0).getResveredField06()));
        } else if (managementList.get(0).getPlanShipTime() != null) {
            fdInvoiceRevenueLedgerTitle.setPlanShipTime(formatter.format(managementList.get(0).getPlanShipTime()));
        }
        return fdInvoiceRevenueLedgerTitle;
    }


    /**
     * 计算总金额
     *
     * @param fdInvoiceRevenueLedgerDetailsVOList 明细列表
     * @return Map
     * <AUTHOR>
     * @since 2025/4/15 上午9:55
     **/
    private Map<String, BigDecimal> getTotalAmount(List<FdInvoiceRevenueLedgerDetailsVO> fdInvoiceRevenueLedgerDetailsVOList) {

        //国内运费总金额
        BigDecimal domesticFreightAmount = fdInvoiceRevenueLedgerDetailsVOList.stream()
                .map(vo -> Optional.ofNullable(vo.getDomesticFreightAmount()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //国外运费总金额
        BigDecimal overseasFreightAmount = fdInvoiceRevenueLedgerDetailsVOList.stream()
                .map(vo -> Optional.ofNullable(vo.getOverseasFreightAmount()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //其他费用总金额
        BigDecimal otherFeesAmount = fdInvoiceRevenueLedgerDetailsVOList.stream()
                .map(vo -> Optional.ofNullable(vo.getOtherFeesAmount()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //总金额
        BigDecimal totalAmount = domesticFreightAmount.add(overseasFreightAmount).add(otherFeesAmount);
        //境外段运费金额（外币）合计
        BigDecimal overseasFreightAmountForeignCurrency = fdInvoiceRevenueLedgerDetailsVOList.stream()
                .map(vo -> Optional.ofNullable(vo.getOverseasFreightAmountForeignCurrency()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        Map<String, BigDecimal> totalAmountMap = new HashMap<>(8);
        totalAmountMap.put("domesticFreightAmount", domesticFreightAmount);
        totalAmountMap.put("overseasFreightAmount", overseasFreightAmount);
        totalAmountMap.put("otherFeesAmount", otherFeesAmount);
        totalAmountMap.put("overseasFreightAmountForeignCurrency", overseasFreightAmountForeignCurrency);
        totalAmountMap.put("totalAmount", totalAmount);
        return totalAmountMap;
    }
}
