package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.SysLog;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 日志表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-08-04 11:35:56
 */
public interface SysLogService extends IService<SysLog> {
    /**
     * 查询日志表信息
     *
     * @param id 日志表ID
     * @return 日志表信息
     */
    public SysLog selectSysLogById(String id);

    /**
     * 查询日志表列表
     *
     * @param sysLog 日志表信息
     * @return 日志表集合
     */
    public List<SysLog> selectSysLogList(SysLog sysLog);


    /**
     * 分页模糊查询日志表列表
     * @return 日志表集合
     */
    public Page selectSysLogListByLike(Query query);



    /**
     * 新增日志表
     *
     * @param sysLog 日志表信息
     * @return 结果
     */
    public int insertSysLog(SysLog sysLog);

    /**
     * 修改日志表
     *
     * @param sysLog 日志表信息
     * @return 结果
     */
    public int updateSysLog(SysLog sysLog);

    /**
     * 删除日志表
     *
     * @param id 日志表ID
     * @return 结果
     */
    public int deleteSysLogById(String id);

    /**
     * 批量删除日志表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysLogByIds(Integer[] ids);

}

