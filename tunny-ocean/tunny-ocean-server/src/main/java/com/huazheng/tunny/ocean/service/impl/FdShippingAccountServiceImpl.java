package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.util.StringUtils;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.FdShippingMonthCityDTO;
import com.huazheng.tunny.ocean.api.dto.FdShippingNumDTO;
import com.huazheng.tunny.ocean.api.dto.SelectAccountByDlDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.enums.MessageBusinessEnum;
import com.huazheng.tunny.ocean.api.feign.RemoteAdminService;
import com.huazheng.tunny.ocean.api.vo.FdShippingAccountVO;
import com.huazheng.tunny.ocean.api.vo.LedgerListVO;
import com.huazheng.tunny.ocean.api.vo.SysDictVo;
import com.huazheng.tunny.ocean.controller.FdCostController;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.*;
import com.huazheng.tunny.ocean.util.CheckUtil;
import com.huazheng.tunny.ocean.util.ParallelQueryExecutor;
import com.huazheng.tunny.ocean.util.PermissionUtil;
import com.huazheng.tunny.ocean.util.spiltlistutil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("fdShippingAccountService")
@Slf4j
public class FdShippingAccountServiceImpl extends ServiceImpl<FdShippingAccountMapper, FdShippingAccount> implements FdShippingAccountService {

    @Autowired
    private FdShippingAccountMapper fdShippingAccountMapper;
    @Autowired
    private SysNoConfigService noConfigService;
    @Autowired
    private FdShippingAccoundetailService fdShippingAccoundetailService;
    @Autowired
    private PermissionUtil permissionUtil;
    @Autowired
    private FdCostController fdCostController;
    @Autowired
    private FdCostMapper fdCostMapper;
    @Autowired
    private LineManagementService lineManagementService;
    @Autowired
    private FdBillService fdBillService;
    @Autowired
    private FdCostService fdCostService;
    @Autowired
    private FdBalanceDetailService fdBalanceDetailService;
    @Autowired
    private FiHisBookingInformationMapper fiHisBookingInformationMapper;
    @Autowired
    private WaybillContainerInfoService waybillContainerInfoService;
    @Autowired
    private FdCosdetailService fdCosdetailService;
    @Autowired
    private FiHisBookingInformationService fiHisBookingInformationService;
    @Autowired
    private FdShippingAccoundetailMapper fdShippingAccoundetailMapper;
    @Autowired
    private FdBillMapper fdBillMapper;
    @Autowired
    private FdShippingAccoundetailSubMapper fdShippingAccoundetailSubMapper;
    @Autowired
    private FdBillSubMapper fdBillSubMapper;
    @Autowired
    private SysNoConfigService sysNoConfigService;
    @Autowired
    private FdBillSubDetailMapper fdBillSubDetailMapper;
    @Autowired
    private ShifmanagementMapper shifmanagementMapper;
    @Autowired
    private LineManagementMapper lineManagementMapper;
    @Autowired
    private WaybillContainerInfoMapper waybillContainerInfoMapper;
    @Autowired
    private WaybillParticipantsMapper waybillParticipantsMapper;
    @Autowired
    private WaybillGoodsInfoMapper waybillGoodsInfoMapper;
    @Autowired
    private WaybillHeaderMapper waybillHeaderMapper;
    @Autowired
    private FdCosdetailMapper fdCosdetailMapper;
    @Autowired
    private BookingRequesdetailMapper bookingRequesdetailMapper;
    @Autowired
    private CustomerInfoMapper customerInfoMapper;
    @Autowired
    private BookingRequesheaderMapper bookingRequesheaderMapper;
    @Autowired
    private StationManagementMapper stationManagementMapper;
    @Autowired
    private SpaceOccupyMapper spaceOccupyMapper;
    @Autowired
    private FdBalanceDetailMapper fdBalanceDetailMapper;
    @Autowired
    private FdTradingDetailsMapper fdTradingDetailsMapper;
    @Autowired
    private BillDealWithCityMapper billDealWithCityMapper;
    @Autowired
    private BillSubPayCityMapper billSubPayCityMapper;
    @Autowired
    private FdBusCostDetailMapper fdBusCostDetailMapper;
    @Autowired
    private BillPayProvinceMapper billPayProvinceMapper;
    @Autowired
    private BillPayProvinceSubMapper billPayProvinceSubMapper;
    @Autowired
    private BillBalanceMainCityMapper billBalanceMainCityMapper;
    @Autowired
    private RemoteAdminService remoteAdminService;
    @Autowired
    private ContainerTypeDataMapper containerTypeDataMapper;
    @Autowired
    private BasChangeboxRetreatMapper basChangeboxRetreatMapper;
    @Autowired
    private MessageCenterService messageCenterService;
    @Autowired
    private FdBusCostMapper fdBusCostMapper;
    @Autowired
    private ProvinceShiftNoService provinceShiftNoService;
    @Autowired
    private ShifmanagementService shifmanagementService;
    @Autowired
    private PlatformOverviewVariablesMapper platformOverviewVariablesMapper;

    @Value("${jyfx.jygl}")
    private String jygl;
    @Value("${path.windowsPath}")
    private String windowsPath;
    @Value("${path.linuxPath}")
    private String linuxPath;

    /**
     * 查询发运台账主表信息
     *
     * @param rowId 发运台账主表ID
     * @return 发运台账主表信息
     */
    @Override
    public FdShippingAccount selectFdShippingAccountById(Long rowId) {
        return fdShippingAccountMapper.selectFdShippingAccountById(rowId);
    }

    /**
     * 根据班次号获取班次信息
     *
     * @param shiftNo
     * @return
     */
    @Override
    public FdShippingAccount selectShippingInfo(String shiftNo) {
        return fdShippingAccountMapper.selectShippingInfoNew(shiftNo, SecurityUtils.getUserInfo().getPlatformCode());
    }

    /**
     * 查询发运台账主表列表
     *
     * @param fdShippingAccount 发运台账主表信息
     * @return 发运台账主表集合
     */
    @Override
    public List<FdShippingAccount> selectFdShippingAccountList(FdShippingAccount fdShippingAccount) {
        return fdShippingAccountMapper.selectFdShippingAccountList(fdShippingAccount);
    }


    /**
     * 分页模糊查询发运台账主表列表
     *
     * @return 发运台账主表集合
     */
    @Override
    public Page selectFdShippingAccountListByLike(Query query) {
        if (StrUtil.isBlank(query.getOrderByField())) {
            query.setOrderByField("FIELD( x.status, '3', '0', '1', '2','x','99' ) ASC,x.add_time");
            query.setAsc(Boolean.FALSE);
        }
        FdShippingAccount fdShippingAccount = BeanUtil.mapToBean(query.getCondition(), FdShippingAccount.class, false);
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        fdShippingAccount.setPlatformCode(null);
        fdShippingAccount.setPlatformName(null);
        if ("1".equals(userInfo.getPlatformLevel())) {
            fdShippingAccount.setPlatformCode(userInfo.getPlatformCode());
        } else if ("2".equals(userInfo.getPlatformLevel())) {
            fdShippingAccount.setCustomerNo(userInfo.getPlatformCode());
        }
        List<FdShippingAccount> accountList = fdShippingAccountMapper.selectFdShippingAccountListByLike(query, fdShippingAccount);
        getIncomeAndCost(accountList);
        query.setRecords(accountList);
        return query;
    }

    /**
     * 导出台账数据为 Excel
     *
     * @param fdShippingAccount 台账数据
     * @param response          Http 响应对象
     * @throws IOException IO异常
     */
    @Override
    public void exportShippingAccountExcel(FdShippingAccount fdShippingAccount, HttpServletResponse response) throws IOException {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        fdShippingAccount.setPlatformCode(null);
        fdShippingAccount.setPlatformName(null);
        if ("1".equals(userInfo.getPlatformLevel())) {
            fdShippingAccount.setPlatformCode(userInfo.getPlatformCode());
        } else if ("2".equals(userInfo.getPlatformLevel())) {
            fdShippingAccount.setCustomerNo(userInfo.getPlatformCode());
        }
        List<FdShippingAccount> accountList = fdShippingAccountMapper.selectFdShippingAccountListByLike(fdShippingAccount);
        ExcelWriter writer = null;
        OutputStream out = null;

        try {
            List<Map<String, Object>> exportList = new ArrayList<>();
            for (FdShippingAccount account : accountList) {
                Map<String, Object> row = new LinkedHashMap<>();
                row.put("班次号", account.getShiftNo());
                row.put("班列名称", account.getShiftName());
                row.put("省级班列号", account.getProvinceShiftNo());
                //状态（0待提交，1待确认，2已确认，3驳回，99历史迁移）
                if ("0".equals(account.getStatus())) {
                    row.put("状态", "待提交");
                } else if ("1".equals(account.getStatus())) {
                    row.put("状态", "待确认");
                } else if ("2".equals(account.getStatus())) {
                    row.put("状态", "已确认");
                } else if ("3".equals(account.getStatus())) {
                    row.put("状态", "驳回");
                }else if ("99".equals(account.getStatus())) {
                    row.put("状态", "历史迁移");
                }
                if ("G".equals(account.getTrip())) {
                    row.put("方向", "去程");
                } else if ("R".equalsIgnoreCase(account.getTrip())) {
                    row.put("方向", "回程");
                }
                if (account.getShippingTime() != null) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    String shippingTimeStr = account.getShippingTime().format(formatter);
                    row.put("发运时间", shippingTimeStr);
                } else {
                    row.put("发运时间", "");
                }
                row.put("发运线路", account.getShippingLine());
                row.put("货源组织单位", account.getOrgUnit());
                row.put("境内运费（人民币）", account.getDomesticFreight());
                row.put("境外运费（人民币）", account.getOverseasFreightCny());
                row.put("境外运费（原币）", account.getOverseasFreightOc());
                row.put("应付金额", account.getAmount());
                row.put("创建人", account.getAddWhoName());
                if (account.getAddTime() != null) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    String addTime = account.getAddTime().format(formatter);
                    row.put("创建时间", addTime);
                } else {
                    row.put("创建时间", "");
                }
                exportList.add(row);
            }

            writer = ExcelUtil.getWriter(true);


            // 设置文件类型和名称
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            String fileName = URLEncoder.encode("发运台账导出.xlsx", "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

            out = response.getOutputStream();
            writer.write(exportList);
            // 设置班次号列宽
            writer.setColumnWidth(0, 20);

            // 设置班列名称列宽
            writer.setColumnWidth(1, 25);

            // 设置省级班列号列宽
            writer.setColumnWidth(2, 25);

            // 设置状态列宽
            writer.setColumnWidth(3, 15);

            // 方向
            writer.setColumnWidth(4, 10);

            // 发运时间
            writer.setColumnWidth(5, 15);

            // 发运线路
            writer.setColumnWidth(6, 10);

            // 设置货源组织单位列宽
            writer.setColumnWidth(7, 60);

            // 境内运费（人民币）
            writer.setColumnWidth(8, 20);

            // 境外运费（人民币）
            writer.setColumnWidth(9, 20);

            // 境外运费（原币）
            writer.setColumnWidth(10, 20);

            // 应付金额
            writer.setColumnWidth(11, 20);

            // 创建人
            writer.setColumnWidth(12, 20);
            // 设置创建时间列宽
            writer.setColumnWidth(13, 30);
            writer.flush(out);
        } finally {
            if (writer != null) {
                writer.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }

    /**
     * 查询省平台的收入金额和成本金额（并行处理）
     *
     * @param list 台账列表
     * <AUTHOR>
     * @since 2025/04/29 下午2:00
     */
    private void getIncomeAndCost(List<FdShippingAccount> list) {
        List<Runnable> tasks = new ArrayList<>();
        for (FdShippingAccount account : list) {
            tasks.add(() -> {
                List<FdShippingAccount> settlementListProvince = fdShippingAccountMapper.checkIfOrderIsSettledProvince(account);
                List<FdShippingAccount> settlementListCity = fdShippingAccountMapper.checkIfOrderIsSettledCity(account);
                FdShippingAccount incomeAndCostUnsettled = fdShippingAccountMapper.getIncomeAndCost(account);

                if (CollUtil.isNotEmpty(settlementListCity)) {
                    FdShippingAccount incomeAmount = fdShippingAccountMapper.queryPlatformIncomeAmount(account);
                    if (ObjectUtil.isNotNull(incomeAmount)) {
                        account.setIncomeAmount(incomeAmount.getIncomeAmount());
                    }
                } else {
                    account.setIncomeAmount(incomeAndCostUnsettled.getIncomeAmount());
                }

                if (CollUtil.isNotEmpty(settlementListProvince)) {
                    FdShippingAccount costAmount = fdShippingAccountMapper.queryPlatformCostAmount(account);
                    if (ObjectUtil.isNotNull(costAmount)) {
                        account.setCostAmount(costAmount.getCostAmount());
                    }
                } else {
                    if (ObjectUtil.isNotNull(incomeAndCostUnsettled)) {
                        account.setCostAmount(incomeAndCostUnsettled.getCostAmount());
                    }
                }
            });
        }
        ParallelQueryExecutor.execute(tasks);
    }


    @Override
    public Integer selectFdShippingAccountListByLikeCount(FdShippingAccount fdShippingAccount) {
        return fdShippingAccountMapper.selectFdShippingAccountListByLikeCount(fdShippingAccount);
    }

    /**
     * 查询可生成台账List
     *
     * @param query
     * @return
     */
    @Override
    public Page selectShippingAccountList(Query query) {
        FdShippingAccount fdShippingAccount = BeanUtil.mapToBean(query.getCondition(), FdShippingAccount.class, false);
        if (StrUtil.isEmpty(fdShippingAccount.getPlatformCode())) {
            fdShippingAccount.setPlatformCode(permissionUtil.getPcPermisson(fdShippingAccount.getPlatformCode(), fdShippingAccount.getPlatformFlag()));
            fdShippingAccount.setPlatformFlag(null);
        }
        String str = "2023-09-01";
        fdShippingAccount.setShippingTimeStart(str);
        query.setRecords(fdShippingAccountMapper.selectShippingAccountList(query, fdShippingAccount));
        return query;
    }

    @Override
    public Page selectShippingAccountListNew(Query query) {
        FdShippingAccount fdShippingAccount = BeanUtil.mapToBean(query.getCondition(), FdShippingAccount.class, false);
        fdShippingAccount.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        String str = "2024-06-01";
        fdShippingAccount.setShippingTimeStart(str);
        query.setRecords(fdShippingAccountMapper.selectShippingAccountListNew(query, fdShippingAccount));
        return query;
    }

    @Override
    public List<FdShippingAccount> selectNotGenerateStandingBook(Map<String, Object> params) {
        FdShippingAccount fdShippingAccount = BeanUtil.mapToBean(params, FdShippingAccount.class, false);
        if (StrUtil.isEmpty(fdShippingAccount.getPlatformCode())) {
            fdShippingAccount.setPlatformCode(permissionUtil.getPcPermisson(fdShippingAccount.getPlatformCode(), fdShippingAccount.getPlatformFlag()));
            fdShippingAccount.setPlatformFlag(null);
        }
        return fdShippingAccountMapper.selectNotGenerateStandingBook(fdShippingAccount);
    }

    /**
     * 新增发运台账主表
     *
     * @param vo 发运台账主表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R insertFdShippingAccount(FdShippingAccountVO vo) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String tsIn = noConfigService.genNo("TZ");
        if (tsIn.contains("请联系系统管理员")) {
            return new R<>(new Throwable(tsIn));
        }
        vo.setPlatformCode(userInfo.getPlatformCode());
        vo.setPlatformName(userInfo.getPlatformName());
        vo.setAccountCode(tsIn);
        vo.setAddWho(userInfo.getUserName());
        vo.setAddWhoName(userInfo.getRealName());
        vo.setAddTime(LocalDateTime.now());
        vo.setCreateTime(LocalDateTime.now());
        List<FdShippingAccoundetail> dataList = vo.getDataList();
        //境外运费(原币)
        BigDecimal freightOc = BigDecimal.ZERO;
        //境外运费(人民币)
        BigDecimal freightCny = BigDecimal.ZERO;
        //境内运费
        BigDecimal freight = BigDecimal.ZERO;
        String orgUnit = "";
        String monetaryType = "";
        for (FdShippingAccoundetail shippingAccoundetail : dataList) {
            orgUnit = shippingAccoundetail.getOrgUnit();
            monetaryType = shippingAccoundetail.getMonetaryType();
            if (shippingAccoundetail.getOverseasFreightOc() != null) {
                freightOc = freightOc.add(shippingAccoundetail.getOverseasFreightOc());
                shippingAccoundetail.setRrOverseasFreightOc(shippingAccoundetail.getOverseasFreightOc());
            }
            if (shippingAccoundetail.getOverseasFreightCny() != null) {
                freightCny = freightCny.add(shippingAccoundetail.getOverseasFreightCny());
                shippingAccoundetail.setRrOverseasFreightCny(shippingAccoundetail.getOverseasFreightCny());
            }
            if (shippingAccoundetail.getDomesticFreight() != null) {
                freight = freight.add(shippingAccoundetail.getDomesticFreight());
                shippingAccoundetail.setRrDomesticFreight(shippingAccoundetail.getDomesticFreight());
            }
        }
        if (StrUtil.isEmpty(vo.getCustomerNo())) {
            String supPlatformCode = userInfo.getSupPlatformCode();
            if (supPlatformCode.contains("_")) {
                supPlatformCode = supPlatformCode.split("_")[0];
            }
            vo.setCustomerNo(supPlatformCode);
            CustomerInfo sel = new CustomerInfo();
            sel.setCustomerCode(supPlatformCode);
            sel.setDeleteFlag("N");
            List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoList(sel);
            if (CollUtil.isNotEmpty(customerInfos)) {
                vo.setCustomerName(customerInfos.get(0).getCompanyName());
            }
        }
        if ("1".equals(vo.getStatus())) {
            vo.setSubmitFlag("1");
            R r = tzCommitCheck(vo);
            if (r.getCode() == 1) {
                return r;
            }
        }

        if (StrUtil.isNotEmpty(vo.getResveredField08Str())) {
            vo.setShippingTime(LocalDateTime.parse(vo.getResveredField08Str() + "T00:00:00"));
        }
        vo.setResveredField08(vo.getShippingTime());
        vo.setOverseasFreightOc(freightOc);
        vo.setOrgUnit(orgUnit);
        vo.setMonetaryType(monetaryType);
        vo.setOverseasFreightCny(freightCny);
        vo.setDomesticFreight(freight);
        vo.setAmount(freightCny.add(freight));
        fdShippingAccountMapper.insert(vo);
        for (FdShippingAccoundetail dto : dataList) {
            dto.setContainerNewestStatus("0");
            dto.setAccountCode(tsIn);
            dto.setAddWho(userInfo.getUserName());
            dto.setAddWhoName(userInfo.getRealName());
            dto.setAddTime(LocalDateTime.now());
            fdShippingAccoundetailService.insert(dto);
            //更新订单信息
            updateWaybillInfo(vo, dto);
        }
        if ("1".equals(vo.getStatus())) {
            sendMiniMsgNotice(vo);
        }
        return new R<>(0, Boolean.TRUE, tsIn, "操作成功");
    }

    private void updateWaybillInfo(FdShippingAccountVO vo, FdShippingAccoundetail dto) {
        //更新订单信息
        if (StrUtil.isNotBlank(dto.getGoodsOwner()) || StrUtil.isNotBlank(dto.getGoodsOrigin()) || dto.getValueUsd() != null) {
            WaybillParticipants participants = new WaybillParticipants();
            participants.setShiftNo(vo.getShiftNo());
            participants.setContainerNo(dto.getContainerNumber());
            participants.setConsignorName(dto.getGoodsOwner());
            participants.setCity(dto.getGoodsOrigin());
            if ("G".equals(vo.getTrip())) {
                participants.setParticipantsType("F");
            } else if ("R".equals(vo.getTrip())) {
                participants.setParticipantsType("S");
            }
            waybillParticipantsMapper.updateWaybillParticipantByShift(participants);
        }
        if (StrUtil.isNotBlank(dto.getClearanceNumber()) || StrUtil.isNotBlank(dto.getCustomsSeal()) || StrUtil.isNotBlank(dto.getTrainNumber()) || StrUtil.isNotBlank(dto.getWaybillDemandNumber()) || StrUtil.isNotBlank(dto.getWaybillLnNumber()) || StrUtil.isNotBlank(dto.getIsFull()) || StrUtil.isNotBlank(dto.getNonFerrous())) {
            WaybillContainerInfo info = new WaybillContainerInfo();
            info.setShiftNo(vo.getShiftNo());
            info.setContainerNo(dto.getContainerNumber());
            info.setClearanceNumber(dto.getClearanceNumber());
            info.setCustomsSeal(dto.getCustomsSeal());
            info.setTrainNumber(dto.getTrainNumber());
            info.setWaybillDemandNumber(dto.getWaybillDemandNumber());
            info.setWaybillLnNumber(dto.getWaybillLnNumber());
            info.setIsFull(dto.getIsFull());
            info.setNonFerrous(dto.getNonFerrous());
            info.setDestinationCountryCode(dto.getDestinationCountryCode());
            info.setDestinationCountryName(dto.getDestinationCountry());
            waybillContainerInfoMapper.updateContainerInfoByShift(info);
        }
    }

    @Override
    public R saveSynchronousData(FdShippingAccountVO vo) {
        FdShippingAccount account = fdShippingAccountMapper.selectFdShippingAccountById(vo.getRowId());
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        vo.setUpdateWho(userInfo.getUserName());
        vo.setUpdateWhoName(userInfo.getRealName());
        vo.setUpdateTime(LocalDateTime.now());
        List<FdShippingAccoundetail> dataList = vo.getDataList();
        //境外运费(原币)
        BigDecimal freightOc = BigDecimal.valueOf(0);
        //境外运费(人民币)
        BigDecimal freightCny = BigDecimal.valueOf(0);
        //境内运费
        BigDecimal freight = BigDecimal.valueOf(0);
        for (FdShippingAccoundetail shippingAccoundetail : dataList) {
            if (shippingAccoundetail.getOverseasFreightOc() != null) {
                freightOc = freightOc.add(shippingAccoundetail.getOverseasFreightOc());
            }
            if (shippingAccoundetail.getOverseasFreightCny() != null) {
                freightCny = freightCny.add(shippingAccoundetail.getOverseasFreightCny());
            }
            if (shippingAccoundetail.getDomesticFreight() != null) {
                freight = freight.add(shippingAccoundetail.getDomesticFreight());
            }
        }
        vo.setOverseasFreightOc(account.getOverseasFreightOc().add(freightOc));
        vo.setOverseasFreightCny(account.getOverseasFreightCny().add(freightCny));
        vo.setDomesticFreight(account.getDomesticFreight().add(freight));
        vo.setAmount(account.getAmount().add(freightCny.add(freight)));
        //判断当前台账是否审核成功，如果成功以后补充费用
        if ("2".equals(account.getStatus())) {
            FdCost fdCost = new FdCost();
            fdCost.setProvinceTrainsNumber(vo.getProvinceShiftNo());
            List<FdCost> fdCosts = fdCostService.selectFdCostList(fdCost);
            for (FdCost fd : fdCosts) {
                HashMap<String, Object> hashMap = new HashMap<>();
                String Invoice = "应收";
                hashMap.put("Invoice", Invoice);
                hashMap.put("codeBbCategoriesName", "发运运费");
                hashMap.put("codeBbCategoriesCode", "f_fee_type");
                hashMap.put("codeSsCategoriesName", "境内运费");
                hashMap.put("codeSsCategoriesCode", "f_domestic_fee");
                //这个是境内费用的费用明细
                List<FdCosdetail> list = getcostdetailbyaccount(dataList, hashMap, fd);
                //这个是境外费用的费用明细
                hashMap.put("codeBbCategoriesName", "发运运费");
                hashMap.put("codeBbCategoriesCode", "f_fee_type");
                hashMap.put("codeSsCategoriesName", "境外费用");
                hashMap.put("codeSsCategoriesCode", "f_overseas_fee");
                List<FdCosdetail> list1 = newgetcostdetailbyaccount(dataList, hashMap, fd);
                list.addAll(list1);
                //计算境外费用
                BigDecimal bigDecimal = BigDecimal.valueOf(0);
                BigDecimal bigDecimalCny = BigDecimal.valueOf(0);
                BigDecimal jnBigDecimal = BigDecimal.valueOf(0);
                for (FdCosdetail fdCosdetail : list) {
                    if ("f_domestic_fee".equals(fdCosdetail.getCodeSsCategoriesCode())) {
                        fdCosdetail.setExchangeRate(BigDecimal.valueOf(1));
                    }
                    if ("f_overseas_fee".equals(fdCosdetail.getCodeSsCategoriesCode())) {
                        bigDecimal = bigDecimal.add(fdCosdetail.getOriginalCurrencyAmount());
                        bigDecimalCny = bigDecimalCny.add(fdCosdetail.getLocalCurrencyAmount());
                    }
                    if ("f_domestic_fee".equals(fdCosdetail.getCodeSsCategoriesCode())) {
                        jnBigDecimal = jnBigDecimal.add(fdCosdetail.getOriginalCurrencyAmount());
                    }
                }
                fd.setOverseasFreightOverseasFreight(fd.getOverseasFreightOverseasFreight().add(bigDecimal));
                fd.setOverseasFreightCny(fd.getOverseasFreightCny().add(bigDecimalCny));
                fd.setDomesticFreight(fd.getDomesticFreight().add(jnBigDecimal));
                //判断费用是应付还是应收
                if ("1".equals(fd.getPlatformLevel())) {
                    //下面生成省平台对市平台应收
                    //下面的值应该是省平台
                    String paymentCustomerName = fd.getPaymentCustomerName();
                    String paymentCustomerCode = fd.getPaymentCustomerCode();
                    //下面是市平台的数据
                    String platformCode = fd.getPlatformCode();
                    String platformName = fd.getPlatformName();
                    for (FdCosdetail fdCosdetail : list) {
                        fdCosdetail.setCostCode(fd.getCostCode());
                        fdCosdetail.setPlatformName(paymentCustomerName);
                        fdCosdetail.setPlatformCode(paymentCustomerCode);
                        fdCosdetail.setPlatformLevel("1");
                        fdCosdetail.setCustomerName(platformName);
                        fdCosdetail.setCustomerCode(platformCode);
                        fdCosdetail.setIncomeFlag("应收");
//                        fdCosdetail.setUuid(UUID.randomUUID().toString());
                    }
                    fdCostMapper.updateFdCost(fd);
//                    fdCosdetailService.insertFdCosdetailList(list);
                    List<List<FdCosdetail>> splitList1 = spiltlistutil.getSplitList(800, list);
                    for (List<FdCosdetail> fdCosdetails : splitList1) {
                        fdCosdetailService.insertBatch(fdCosdetails);
                    }
                } else if ("2".equals(fd.getPlatformLevel())) {
                    for (FdCosdetail fdCosdetails : list) {
                        fdCosdetails.setUuid(UUID.randomUUID().toString());
                        fdCosdetails.setCostCode(fd.getCostCode());
                        fdCosdetails.setPlatformLevel("2");
                        fdCosdetails.setIncomeFlag("应付");
                        fdCosdetails.setStandbyA("1");
                        fdCosdetails.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
                        fdCosdetails.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
//                        //境内运费
//                        if(fdCosdetails.getCodeSsCategoriesCode().equals("f_domestic_fee")){
//                            fDomesticFee=fDomesticFee.add(fdCosdetails.getOriginalCurrencyAmount());
//                        }
//                        //境外运费
//                        if(fdCosdetails.getCodeSsCategoriesCode().equals("f_overseas_fee")){
//                            fOverseasFee=fOverseasFee.add(fdCosdetails.getOriginalCurrencyAmount());
//                        }
                    }

//                    fd.setOriginalExchangeRate(list.get(0).getExchangeRate());//汇率
                    //新增多联费用
                    fdCostService.updateFdCost(fd);
                    //新增多联明细费用
                    fdCosdetailService.insertFdCosdetailList(list);
                }
            }
        }
        fdShippingAccountMapper.updateFdShippingAccount(vo);
        for (FdShippingAccoundetail dto : dataList) {
            dto.setContainerNewestStatus("0");
            dto.setAccountCode(vo.getAccountCode());
            dto.setAddWho(userInfo.getUserName());
            dto.setAddWhoName(userInfo.getRealName());
            dto.setAddTime(LocalDateTime.now());
            fdShippingAccoundetailService.insert(dto);
        }
        R<Boolean> booleanR = new R<>(Boolean.TRUE);
        booleanR.setObject(vo.getAccountCode());
        booleanR.setB(Boolean.TRUE);
        booleanR.setMsg("同步成功");
        return booleanR;
    }

    private List<FdCosdetail> newgetcostdetailbyaccount(List<FdShippingAccoundetail> fdShippingAccoundetails, HashMap hashMap, FdCost fdCost) {


        //获取台账和台账子表信息
        ArrayList<FdCosdetail> fdCosdetails = new ArrayList<>();
        for (FdShippingAccoundetail shippingAccoundetail : fdShippingAccoundetails) {
            //每个箱子都创建一条费用明细信息

            FdCosdetail fdCosdetail = new FdCosdetail();
            BeanUtils.copyProperties(fdCost, fdCosdetail);
            //客户信息
            fdCosdetail.setCustomerName(fdCost.getPaymentCustomerName());

            //uuid
            fdCosdetail.setCustomerCode(fdCost.getPaymentCustomerCode());
            fdCosdetail.setUuid(UUID.randomUUID().toString());

            //收支标识
            fdCosdetail.setIncomeFlag(hashMap.get("Invoice") + "");
            //箱号
            fdCosdetail.setContainerNumber(shippingAccoundetail.getContainerNumber());
            //费用大类编码
            fdCosdetail.setCodeBbCategoriesCode(hashMap.get("codeBbCategoriesCode") + "");
            fdCosdetail.setCodeBbCategoriesName(hashMap.get("codeBbCategoriesName") + "");
            //费用小类
            fdCosdetail.setCodeSsCategoriesCode(hashMap.get("codeSsCategoriesCode") + "");
            fdCosdetail.setCodeSsCategoriesName(hashMap.get("codeSsCategoriesName") + "");
            //币种

            fdCosdetail.setCurrency(shippingAccoundetail.getMonetaryType());
            // 汇率
            fdCosdetail.setExchangeRate(shippingAccoundetail.getExchangeRate());
            //本币金额

            fdCosdetail.setLocalCurrencyAmount(shippingAccoundetail.getOverseasFreightCny());
            //原币金额
            fdCosdetail.setOriginalCurrencyAmount(shippingAccoundetail.getOverseasFreightOc());
            //运单号
            fdCosdetail.setTransportOrderNumber(shippingAccoundetail.getTransportOrderNumber());
            //申请单号
            fdCosdetail.setApplicationNumber(shippingAccoundetail.getApplicationNumber());
            //货源组织单位
            fdCosdetail.setStandbyC(shippingAccoundetail.getOrgUnit());

            fdCosdetails.add(fdCosdetail);

        }
        return fdCosdetails;
    }

    private List<FdCosdetail> getcostdetailbyaccount(List<FdShippingAccoundetail> fdShippingAccoundetails, HashMap hashMap, FdCost fdCost) {

        ArrayList<FdCosdetail> fdCosdetails = new ArrayList<>();
        for (FdShippingAccoundetail shippingAccoundetail : fdShippingAccoundetails) {

            //每个箱子都创建一条费用明细信息
            FdCosdetail fdCosdetail = new FdCosdetail();
            BeanUtils.copyProperties(fdCost, fdCosdetail);
            //客户信息
            fdCosdetail.setCustomerName(fdCost.getPaymentCustomerName());

            //uuid
            fdCosdetail.setCustomerCode(fdCost.getPaymentCustomerCode());
            fdCosdetail.setUuid(UUID.randomUUID().toString());

            //收支标识
            fdCosdetail.setIncomeFlag(hashMap.get("Invoice") + "");
            //箱号
            fdCosdetail.setContainerNumber(shippingAccoundetail.getContainerNumber());
            //费用大类编码
            fdCosdetail.setCodeBbCategoriesCode(hashMap.get("codeBbCategoriesCode") + "");
            fdCosdetail.setCodeBbCategoriesName(hashMap.get("codeBbCategoriesName") + "");
            //费用小类
            fdCosdetail.setCodeSsCategoriesCode(hashMap.get("codeSsCategoriesCode") + "");
            fdCosdetail.setCodeSsCategoriesName(hashMap.get("codeSsCategoriesName") + "");
            //币种
            fdCosdetail.setCurrency("人民币");
            // 汇率
            fdCosdetail.setExchangeRate(shippingAccoundetail.getExchangeRate());
            //本币金额
            BigDecimal CurrencyAmount = shippingAccoundetail.getDomesticFreight();
            fdCosdetail.setLocalCurrencyAmount(CurrencyAmount);
            //原币金额
            fdCosdetail.setOriginalCurrencyAmount(CurrencyAmount);
            //运单号
            fdCosdetail.setTransportOrderNumber(shippingAccoundetail.getTransportOrderNumber());
            //申请单号
            fdCosdetail.setApplicationNumber(shippingAccoundetail.getApplicationNumber());
            //货源组织单位
            fdCosdetail.setStandbyC(shippingAccoundetail.getOrgUnit());

            fdCosdetails.add(fdCosdetail);

        }
        return fdCosdetails;
    }

    @Override
    public R tzCommitCheck(FdShippingAccount fdShippingAccount) {
        StringBuffer sb = new StringBuffer();
        BasChangeboxRetreat sel = new BasChangeboxRetreat();
        sel.setShiftNo(fdShippingAccount.getShiftNo());
        sel.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        sel.setStatus("1");
        List<BasChangeboxRetreat> basChangeboxRetreats = basChangeboxRetreatMapper.selectBasChangeboxRetreatList(sel);
        if (CollUtil.isNotEmpty(basChangeboxRetreats)) {
            sb.append("该班次存在撤换箱申请，请先完成撤换箱审批");
        }
        FdBusCost fdBusCost = new FdBusCost();
        fdBusCost.setShiftNo(fdShippingAccount.getShiftNo());
        fdBusCost.setDeleteFlag("N");
        List<FdBusCost> list = fdBusCostMapper.selectFdBusCostList(fdBusCost);
        if (CollUtil.isNotEmpty(list)) {
            for (FdBusCost cost : list) {
                if (!"2".equals(cost.getAuditStatus())) {
                    sb.append(cost.getPlatformName() + ":" + cost.getCostCode() + "业务流程单审批未完成；");
                }
            }
        }
        if (sb.length() > 0) {
            return R.error(sb.toString());
        }
        return R.success();
    }

    /**
     * 修改发运台账主表
     *
     * @param vo 发运台账主表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateFdShippingAccount(FdShippingAccountVO vo) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        EntityWrapper<FdShippingAccount> wrapper = new EntityWrapper<>();
        wrapper.eq("account_code", vo.getAccountCode());
        wrapper.eq("delete_flag", "N");
        vo.setUpdateTime(LocalDateTime.now());
        vo.setUpdateWho(userInfo.getUserName());
        vo.setUpdateWhoName(userInfo.getRealName());

        FdShippingAccount fdShippingAccount = selectOne(wrapper);
        if (StrUtil.isEmpty(fdShippingAccount.getCustomerNo())) {
            String supPlatformCode = userInfo.getSupPlatformCode();
            if (supPlatformCode.contains("_")) {
                supPlatformCode = supPlatformCode.split("_")[0];
            }
            vo.setCustomerNo(supPlatformCode);
            fdShippingAccount.setCustomerNo(supPlatformCode);
            CustomerInfo sel = new CustomerInfo();
            sel.setCustomerCode(supPlatformCode);
            sel.setDeleteFlag("N");
            List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoList(sel);
            if (CollUtil.isNotEmpty(customerInfos)) {
                vo.setCustomerName(customerInfos.get(0).getCompanyName());
                fdShippingAccount.setCustomerName(customerInfos.get(0).getCompanyName());
            }
        }
        if ("1".equals(vo.getStatus())) {
            vo.setSubmitFlag("1");
            R r = tzCommitCheck(vo);
            if (r != null && "1".equals(String.valueOf(r.getCode()))) {
                return r;
            }
        }

        setUpdateObj(vo, userInfo, fdShippingAccount);
        update(vo, wrapper);
        /*if ("2".equals(vo.getStatus())) {
            fdCostController.savecostandcodstdetailsbyaccount(fdShippingAccount.getRowId().toString());
        }*/
        if ("1".equals(vo.getStatus())) {
            sendMiniMsgNotice(fdShippingAccount);
        }
        return new R<>(0, Boolean.TRUE, null, "成功");
    }

    private void setUpdateObj(FdShippingAccountVO vo, SecruityUser userInfo, FdShippingAccount fdShippingAccount) {
        FdShippingAccountVO updateVo = new FdShippingAccountVO();
        BeanUtil.copyProperties(fdShippingAccount, updateVo);

        if ("0".equals(vo.getStatus()) || "1".equals(vo.getStatus())) {
            List<FdShippingAccoundetail> list = vo.getDataList();
            if (list != null && list.size() > 0) {
                for (FdShippingAccoundetail dto : list) {
                    //如果客户没有导入铁路运费，则默认填入省运费
//                    if (dto.getRrOverseasFreightOc() == null || dto.getRrOverseasFreightOc().compareTo(BigDecimal.ZERO) == 0) {
                    dto.setRrOverseasFreightOc(dto.getOverseasFreightOc());
//                    }
//                    if (dto.getRrOverseasFreightCny() == null || dto.getRrOverseasFreightCny().compareTo(BigDecimal.ZERO) == 0) {
                    dto.setRrOverseasFreightCny(dto.getOverseasFreightCny());
//                    }
//                    if (dto.getRrDomesticFreight() == null || dto.getRrDomesticFreight().compareTo(BigDecimal.ZERO) == 0) {
                    dto.setRrDomesticFreight(dto.getDomesticFreight());
//                    }
                    dto.setUpdateTime(LocalDateTime.now());
                    dto.setUpdateWho(userInfo.getUserName());
                    dto.setUpdateWhoName(userInfo.getRealName());
                    //更新订单信息
                    updateWaybillInfo(updateVo, dto);
                }
                fdShippingAccoundetailService.updateBatchById(list);
            }

            //修改主表数据
            //查询台账明细
            /*EntityWrapper<FdShippingAccoundetail> detailWrapper = new EntityWrapper<>();
            detailWrapper.eq("account_code", vo.getAccountCode());
            detailWrapper.eq("delete_flag", "N");
            detailWrapper.andNew().eq("container_status", "0").or().isNull("container_status");
            List<FdShippingAccoundetail> accoundetailList = fdShippingAccoundetailService.selectList(detailWrapper);*/
            FdShippingAccoundetail sel = new FdShippingAccoundetail();
            sel.setAccountCode(vo.getAccountCode());
            sel.setDeleteFlag("N");
            sel.setContainerStatus("0");
            List<FdShippingAccoundetail> accoundetailList = fdShippingAccoundetailMapper.selectList2(sel);
            //境外运费(原币)
            BigDecimal freightOc = BigDecimal.ZERO;
            //境外运费(人民币)
            BigDecimal freightCny = BigDecimal.ZERO;
            //境内运费
            BigDecimal freight = BigDecimal.ZERO;
            for (FdShippingAccoundetail dto : accoundetailList) {
                if (dto.getOverseasFreightOc() != null) {
                    freightOc = freightOc.add(dto.getOverseasFreightOc());
                }
                if (dto.getOverseasFreightCny() != null) {
                    freightCny = freightCny.add(dto.getOverseasFreightCny());
                }
                if (dto.getDomesticFreight() != null) {
                    freight = freight.add(dto.getDomesticFreight());
                }
            }
            if (StrUtil.isNotEmpty(vo.getResveredField08Str())) {
                vo.setResveredField08(LocalDateTime.parse(vo.getResveredField08Str() + "T00:00:00"));
            }
            vo.setOverseasFreightOc(freightOc);
            vo.setOverseasFreightCny(freightCny);
            vo.setDomesticFreight(freight);
            vo.setAmount(freightCny.add(freight));
            vo.setUpdateTime(LocalDateTime.now());
            vo.setUpdateWho(userInfo.getUserName());
            vo.setUpdateWhoName(userInfo.getRealName());
            vo.setDeleteFlag("N");
        }
    }

    public void sendMiniMsgNotice(FdShippingAccount fdShippingAccount) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String content = fdShippingAccount.getPlatformName() + "提交了新的发运台账（发运日期：" + fdShippingAccount.getShippingTime().format(formatter) + "），请及时登录PC端在【计划岗管理-台账管理】中审核。";
            MessageCenter messageCenter = new MessageCenter();
            messageCenter.setModuleType(MessageBusinessEnum.ACCOUNT_PROCESS.getKey());
            messageCenter.setModuleName(MessageBusinessEnum.ACCOUNT_PROCESS.getValue());
            messageCenter.setBusinessId(String.valueOf(fdShippingAccount.getRowId()));
            messageCenter.setBusinessType(MessageBusinessEnum.ACCOUNT_AUDIT.getKey());
            messageCenter.setBusinessName(MessageBusinessEnum.ACCOUNT_AUDIT.getValue());
            List<String> usernames = remoteAdminService.getUserNameByRoleName("计划岗", fdShippingAccount.getCustomerNo());
            messageCenterService.sendMiniMsgNotice(usernames, "您有新的台账待审核，请及时处理。", content, messageCenter);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    @Override
    public int updateFdShippingAccount2(FdShippingAccount fdShippingAccount) {
        return fdShippingAccountMapper.updateFdShippingAccount(fdShippingAccount);
    }


    /**
     * 删除发运台账主表
     *
     * @param rowId 发运台账主表ID
     * @return 结果
     */
    @Override
    public int deleteFdShippingAccountById(Long rowId) {
        FdShippingAccount fdShippingAccount = new FdShippingAccount();
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        FdShippingAccount fdShippingAccount1 = fdShippingAccountMapper.selectFdShippingAccountById(rowId);
        if (fdShippingAccount1 != null) {
            FdShippingAccoundetail sel = new FdShippingAccoundetail();
            sel.setAccountCode(fdShippingAccount1.getAccountCode());
            sel.setDeleteFlag("N");
            List<FdShippingAccoundetail> fdShippingAccoundetails = fdShippingAccoundetailMapper.selectFdShippingAccoundetailList(sel);
            if (CollUtil.isNotEmpty(fdShippingAccoundetails)) {
                for (FdShippingAccoundetail detail : fdShippingAccoundetails) {
                    FdShippingAccoundetail delobj = new FdShippingAccoundetail();
                    delobj.setRowId(detail.getRowId());
                    delobj.setDeleteFlag("Y");
                    delobj.setDeleteWho(userInfo.getUserName());
                    delobj.setDeleteWhoName(userInfo.getRealName());
                    delobj.setDeleteTime(LocalDateTime.now());
                    fdShippingAccoundetailMapper.updateFdShippingAccoundetail(delobj);
                }
            }
        }
        if ("export".equals(fdShippingAccount1.getSource())) {
            spaceOccupyMapper.deleteSpaceByShiftNo(fdShippingAccount1.getShiftNo());
        }
        fdShippingAccount.setRowId(rowId);
        fdShippingAccount.setDeleteFlag("Y");
        fdShippingAccount.setDeleteWho(userInfo.getUserName());
        fdShippingAccount.setDeleteWhoName(userInfo.getRealName());
        fdShippingAccount.setDeleteTime(LocalDateTime.now());
        return fdShippingAccountMapper.updateFdShippingAccount(fdShippingAccount);
    }


    /**
     * 批量删除发运台账主表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFdShippingAccountByIds(Integer[] rowIds) {
        return fdShippingAccountMapper.deleteFdShippingAccountByIds(rowIds);
    }

    @Override
    public int auditStatus(FdShippingAccountVO vo) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        vo.setUpdateTime(LocalDateTime.now());
        vo.setUpdateWho(userInfo.getUserName());
        vo.setUpdateWhoName(userInfo.getRealName());
        //审核同意后生成费用信息
        if ("2".equals(vo.getStatus())) {
            //省平台审核时间
            vo.setResveredField09(LocalDateTime.now() + "");
            fdCostController.savecostandcodstdetailsbyaccount(vo.getRowId().toString());
        }
        return fdShippingAccountMapper.auditStatus(vo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int auditStatus2(FdShippingAccountVO vo) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        vo.setUpdateTime(LocalDateTime.now());
        vo.setUpdateWho(userInfo.getUserName());
        vo.setUpdateWhoName(userInfo.getRealName());
        //审核同意后生成费用信息
        if ("2".equals(vo.getStatus())) {

            //更新台账中铁路运费
            List<FdShippingAccoundetail> dataList = vo.getDataList();
            if (CollUtil.isNotEmpty(dataList)) {
                for (FdShippingAccoundetail fdShippingAccoundetail : dataList) {
                    if (fdShippingAccoundetail.getRrDomesticFreight() == null || fdShippingAccoundetail.getRrDomesticFreight().compareTo(BigDecimal.valueOf(0)) == 0) {
                        fdShippingAccoundetail.setRrDomesticFreight(fdShippingAccoundetail.getDomesticFreight());
                    }
                    if (fdShippingAccoundetail.getRrOverseasFreightOc() == null || fdShippingAccoundetail.getRrOverseasFreightOc().compareTo(BigDecimal.valueOf(0)) == 0) {
                        fdShippingAccoundetail.setRrOverseasFreightOc(fdShippingAccoundetail.getOverseasFreightOc());
                    }
                    if (fdShippingAccoundetail.getRrOverseasFreightCny() == null || fdShippingAccoundetail.getRrOverseasFreightCny().compareTo(BigDecimal.valueOf(0)) == 0) {
                        fdShippingAccoundetail.setRrOverseasFreightCny(fdShippingAccoundetail.getOverseasFreightCny());
                    }
                    fdShippingAccoundetail.setUpdateTime(LocalDateTime.now());
                    fdShippingAccoundetail.setUpdateWho(userInfo.getUserName());
                    fdShippingAccoundetail.setUpdateWhoName(userInfo.getRealName());
                    fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode(fdShippingAccoundetail);
                }
            }

            //省平台审核时间
            vo.setResveredField09(LocalDateTime.now() + "");
            fdCostService.savecostandcodstdetailsbyaccount2(vo.getRowId().toString());
        }

        return fdShippingAccountMapper.auditStatus(vo);
    }

    /**
     * @Param: vo
     * @Return: int
     * @Author: zhaohr
     * @Date: 2024/05/29 15:58
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R auditStatus3(FdShippingAccountVO vo) throws Exception {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        vo.setUpdateTime(LocalDateTime.now());
        vo.setUpdateWho(userInfo.getUserName());
        vo.setUpdateWhoName(userInfo.getRealName());
        //审核同意后生成费用信息
        if ("2".equals(vo.getStatus())) {
            BigDecimal val2 = BigDecimal.valueOf(0);
            BigDecimal val3 = BigDecimal.valueOf(0);
            //更新台账中铁路运费
            List<FdShippingAccoundetail> dataList = vo.getDataList();
            if (CollUtil.isNotEmpty(dataList)) {
                for (FdShippingAccoundetail fdShippingAccoundetail : dataList) {
                    if (fdShippingAccoundetail.getRrDomesticFreight() == null) {
                        fdShippingAccoundetail.setRrDomesticFreight(fdShippingAccoundetail.getDomesticFreight());
                    }
                    if (fdShippingAccoundetail.getRrOverseasFreightOc() == null) {
                        fdShippingAccoundetail.setRrOverseasFreightOc(fdShippingAccoundetail.getOverseasFreightOc());
                    }
                    if (fdShippingAccoundetail.getRrOverseasFreightCny() == null) {
                        fdShippingAccoundetail.setRrOverseasFreightCny(fdShippingAccoundetail.getOverseasFreightCny());
                    }
                    fdShippingAccoundetail.setUpdateTime(LocalDateTime.now());
                    fdShippingAccoundetail.setUpdateWho(userInfo.getUserName());
                    fdShippingAccoundetail.setUpdateWhoName(userInfo.getRealName());
                    fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode(fdShippingAccoundetail);

                    val2 = val2.add(fdShippingAccoundetail.getOverseasFreightCny().add(fdShippingAccoundetail.getDomesticFreight()));
                    val3 = val3.add(fdShippingAccoundetail.getRrOverseasFreightCny().add(fdShippingAccoundetail.getRrDomesticFreight()));
                }
            }
            //省平台审核时间
            vo.setResveredField09(LocalDateTime.now() + "");
            FdShippingAccount fdShippingAccount = fdShippingAccountMapper.selectFdShippingAccountById(vo.getRowId());

            Shifmanagement sel = new Shifmanagement();
            sel.setShiftId(fdShippingAccount.getShiftNo());
            sel.setPlatformCode(fdShippingAccount.getPlatformCode());
            sel.setDeleteFlag("N");
            List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
            if (CollUtil.isNotEmpty(shifmanagements) && StrUtil.isBlank(shifmanagements.get(0).getProvinceShiftNo())) {
                vo.setShippingLine(fdShippingAccount.getShippingLine());
                vo.setPlatformCode(fdShippingAccount.getPlatformCode());
                vo.setTrip(fdShippingAccount.getTrip());
                vo.setShippingTime(fdShippingAccount.getShippingTime());
                vo.setShiftNo(fdShippingAccount.getShiftNo());
                String provinceShiftNo = provinceShiftNoService.createProvinceShiftNoTwo(vo);
                if (StrUtil.isNotBlank(provinceShiftNo)) {
                    if (provinceShiftNo.contains("请联系系统管理员")) {
                        throw new RuntimeException(provinceShiftNo);
                    }
                }
            }
            vo.setStatus("2");
            vo.setBillStatus("1");
            fdShippingAccountMapper.auditStatus(vo);

            // 异步执行另一个业务操作
//            insertBill(fdShippingAccount, val2, val3, dataList);
            BigDecimal finalVal2 = val2;
            BigDecimal finalVal3 = val3;
            ExecutorService executor = Executors.newFixedThreadPool(2);
            String ysCode = sysNoConfigService.genNo("FDBL");
            String yfCode = sysNoConfigService.genNo("FDBL");
            //异步生成账单，如果异常则删除所有账单
            executor.submit(() -> {
                insertProvinceBill(fdShippingAccount, finalVal2, finalVal3, dataList, userInfo, executor, ysCode, yfCode);
                System.out.println("异步任务执行中...");

            });
        } else {
            fdShippingAccountMapper.auditStatus(vo);
        }
        return R.success("操作成功！");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R auditStatusBatchCheck(List<FdShippingAccountVO> vos) throws Exception {
        if (CollUtil.isNotEmpty(vos)) {
            if (vos.size() < 2) {
                return R.error("合并审批至少选中两个台账！");
            }
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String provinceShiftNo = null;
            List<Long> rowIds = vos.stream().map(vo -> vo.getRowId()).collect(Collectors.toList());
            FdShippingAccount fsa = fdShippingAccountMapper.selectFdShippingAccountById(vos.get(0).getRowId());
            List<Shifmanagement> result = new ArrayList<>();
            if ("G".equals(fsa.getTrip())) {
                result = shifmanagementMapper.checkShiftInfoGo(rowIds);
            } else {
                result = shifmanagementMapper.checkShiftInfoReturn(rowIds);
            }
            if (CollUtil.isEmpty(result)) {
                return R.error("未查到台账对应班次信息！");
            }
            if (result.size() > 1) {
                return R.error("所选台账班次信息不匹配，不允许审批！");
            }
        } else {
            return R.error("未获取到审批数据，不允许审批！");
        }
        return R.success("校验成功！");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R auditStatusBatch(List<FdShippingAccountVO> vos) throws Exception {
        if (CollUtil.isNotEmpty(vos)) {
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String provinceShiftNo = null;

            for (FdShippingAccountVO vo : vos) {
                updateCommonFields(vo, userInfo);

                if ("2".equals(vo.getStatus())) {
                    BigDecimal val2 = BigDecimal.ZERO;
                    BigDecimal val3 = BigDecimal.ZERO;

                    List<FdShippingAccoundetail> dataList = vo.getDataList();
                    if (CollUtil.isNotEmpty(dataList)) {
                        for (FdShippingAccoundetail detail : dataList) {
                            updateDetailFields(detail, userInfo);
                            updateRrFreights(detail);

                            fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode(detail);

                            val2 = val2.add(detail.getOverseasFreightCny().add(detail.getDomesticFreight()));
                            val3 = val3.add(detail.getRrOverseasFreightCny().add(detail.getRrDomesticFreight()));
                        }
                    }

                    FdShippingAccount fdShippingAccount = fdShippingAccountMapper.selectFdShippingAccountById(vo.getRowId());

                    Shifmanagement sel = new Shifmanagement();
                    sel.setShiftId(fdShippingAccount.getShiftNo());
                    sel.setPlatformCode(fdShippingAccount.getPlatformCode());
                    sel.setDeleteFlag("N");
                    List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
                    if (StrUtil.isBlank(provinceShiftNo)) {
                        if (CollUtil.isNotEmpty(shifmanagements) && StrUtil.isBlank(shifmanagements.get(0).getProvinceShiftNo())) {
                            vo.setShippingLine(fdShippingAccount.getShippingLine());
                            vo.setPlatformCode(fdShippingAccount.getPlatformCode());
                            vo.setTrip(fdShippingAccount.getTrip());
                            vo.setShippingTime(fdShippingAccount.getShippingTime());
                            vo.setShiftNo(fdShippingAccount.getShiftNo());
                            provinceShiftNo = provinceShiftNoService.createProvinceShiftNoTwo(vo);
                            if (StrUtil.isNotBlank(provinceShiftNo)) {
                                if (provinceShiftNo.contains("请联系系统管理员")) {
                                    throw new RuntimeException(provinceShiftNo);
                                }
                            }
                        }
                    } else if (!provinceShiftNo.contains("请联系系统管理员")) {
                        //更新班次中省级班列号
                        if (StrUtil.isNotBlank(fdShippingAccount.getShiftNo())) {
                            shifmanagementMapper.updateProvinceShiftNoNew(null, provinceShiftNo, fdShippingAccount.getShiftNo());
                        }
                    } else if (provinceShiftNo.contains("请联系系统管理员")) {
                        throw new RuntimeException(provinceShiftNo);
                    }

                    vo.setStatus("2");
                    vo.setBillStatus("1");
                    fdShippingAccountMapper.auditStatus(vo);

                    vo.setFdShippingAccount(fdShippingAccount);
                    vo.setFinalVal2(val2);
                    vo.setFinalVal3(val3);
                } else {
                    fdShippingAccountMapper.auditStatus(vo);
                }
            }

            if ("2".equals(vos.get(0).getStatus())) {
                ExecutorService executor = Executors.newFixedThreadPool(2);
                for (FdShippingAccountVO vo : vos) {
                    String ysCode = sysNoConfigService.genNo("FDBL");
                    String yfCode = sysNoConfigService.genNo("FDBL");
                    //异步生成账单，如果异常则删除所有账单
                    executor.submit(() -> {
                        insertProvinceBill(vo.getFdShippingAccount(), vo.getFinalVal2(), vo.getFinalVal3(), vo.getDataList(), userInfo, executor, ysCode, yfCode);
                        System.out.println("异步任务执行中...");

                    });
                }
            }
        }

        return R.success("操作成功！");
    }

    private void updateCommonFields(FdShippingAccountVO vo, SecruityUser userInfo) {
        vo.setUpdateTime(LocalDateTime.now());
        vo.setUpdateWho(userInfo.getUserName());
        vo.setUpdateWhoName(userInfo.getRealName());
    }

    private void updateDetailFields(FdShippingAccoundetail detail, SecruityUser userInfo) {
        detail.setUpdateTime(LocalDateTime.now());
        detail.setUpdateWho(userInfo.getUserName());
        detail.setUpdateWhoName(userInfo.getRealName());
    }

    private void updateRrFreights(FdShippingAccoundetail detail) {
        if (detail.getRrDomesticFreight() == null || detail.getRrDomesticFreight().compareTo(BigDecimal.ZERO) == 0) {
            detail.setRrDomesticFreight(detail.getDomesticFreight());
        }
        if (detail.getRrOverseasFreightOc() == null || detail.getRrOverseasFreightOc().compareTo(BigDecimal.ZERO) == 0) {
            detail.setRrOverseasFreightOc(detail.getOverseasFreightOc());
        }
        if (detail.getRrOverseasFreightCny() == null || detail.getRrOverseasFreightCny().compareTo(BigDecimal.ZERO) == 0) {
            detail.setRrOverseasFreightCny(detail.getOverseasFreightCny());
        }
    }

    /**
     * @Param: vo
     * @Return: int
     * @Author: zhaohr
     * @Date: 2024/05/29 15:58
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R auditStatusWx(FdShippingAccountVO vo) throws Exception {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        vo.setUpdateTime(LocalDateTime.now());
        vo.setUpdateWho(userInfo.getUserName());
        vo.setUpdateWhoName(userInfo.getRealName());
        //审核同意后生成费用信息
        if ("2".equals(vo.getStatus())) {
            BigDecimal val2 = BigDecimal.valueOf(0);
            BigDecimal val3 = BigDecimal.valueOf(0);
            //更新台账中铁路运费
            FdShippingAccount fdShippingAccount = fdShippingAccountMapper.selectFdShippingAccountById(vo.getRowId());
            if (fdShippingAccount != null) {
                FdShippingAccoundetail sel2 = new FdShippingAccoundetail();
                sel2.setAccountCode(fdShippingAccount.getAccountCode());
                sel2.setDeleteFlag("N");
                List<FdShippingAccoundetail> dataList = fdShippingAccoundetailMapper.selectFdShippingAccoundetailList(sel2);
                if (CollUtil.isNotEmpty(dataList)) {
                    for (FdShippingAccoundetail fdShippingAccoundetail : dataList) {
                        if (fdShippingAccoundetail.getRrDomesticFreight() == null || fdShippingAccoundetail.getRrDomesticFreight().compareTo(BigDecimal.valueOf(0)) == 0) {
                            fdShippingAccoundetail.setRrDomesticFreight(fdShippingAccoundetail.getDomesticFreight());
                        }
                        if (fdShippingAccoundetail.getRrOverseasFreightOc() == null || fdShippingAccoundetail.getRrOverseasFreightOc().compareTo(BigDecimal.valueOf(0)) == 0) {
                            fdShippingAccoundetail.setRrOverseasFreightOc(fdShippingAccoundetail.getOverseasFreightOc());
                        }
                        if (fdShippingAccoundetail.getRrOverseasFreightCny() == null || fdShippingAccoundetail.getRrOverseasFreightCny().compareTo(BigDecimal.valueOf(0)) == 0) {
                            fdShippingAccoundetail.setRrOverseasFreightCny(fdShippingAccoundetail.getOverseasFreightCny());
                        }
                        fdShippingAccoundetail.setUpdateTime(LocalDateTime.now());
                        fdShippingAccoundetail.setUpdateWho(userInfo.getUserName());
                        fdShippingAccoundetail.setUpdateWhoName(userInfo.getRealName());
                        fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode(fdShippingAccoundetail);

                        val2 = val2.add(fdShippingAccoundetail.getOverseasFreightCny().add(fdShippingAccoundetail.getDomesticFreight()));
                        val3 = val3.add(fdShippingAccoundetail.getRrOverseasFreightCny().add(fdShippingAccoundetail.getRrDomesticFreight()));
                    }
                }
                //省平台审核时间
                vo.setResveredField09(LocalDateTime.now() + "");

                Shifmanagement sel = new Shifmanagement();
                sel.setShiftId(fdShippingAccount.getShiftNo());
                sel.setPlatformCode(fdShippingAccount.getPlatformCode());
                sel.setDeleteFlag("N");
                List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
                if (CollUtil.isNotEmpty(shifmanagements) && StrUtil.isBlank(shifmanagements.get(0).getProvinceShiftNo())) {
                    vo.setShippingLine(fdShippingAccount.getShippingLine());
                    vo.setPlatformCode(fdShippingAccount.getPlatformCode());
                    vo.setTrip(fdShippingAccount.getTrip());
                    vo.setShippingTime(fdShippingAccount.getShippingTime());
                    vo.setShiftNo(fdShippingAccount.getShiftNo());
                    String provinceShiftNo = provinceShiftNoService.createProvinceShiftNoTwo(vo);
                    if (StrUtil.isNotBlank(provinceShiftNo)) {
                        if (provinceShiftNo.contains("请联系系统管理员")) {
                            throw new RuntimeException(provinceShiftNo);
                        }
                    }
                }
                vo.setStatus("2");
                vo.setBillStatus("1");
                fdShippingAccountMapper.auditStatus(vo);

                // 异步执行另一个业务操作
//            insertBill(fdShippingAccount, val2, val3, dataList);
                BigDecimal finalVal2 = val2;
                BigDecimal finalVal3 = val3;
                ExecutorService executor = Executors.newFixedThreadPool(2);
                String ysCode = sysNoConfigService.genNo("FDBL");
                String yfCode = sysNoConfigService.genNo("FDBL");
                //异步生成账单，如果异常则删除所有账单
                executor.submit(() -> {
                    insertProvinceBill(fdShippingAccount, finalVal2, finalVal3, dataList, userInfo, executor, ysCode, yfCode);
                    System.out.println("异步任务执行中...");

                });
            }

        } else {
            fdShippingAccountMapper.auditStatus(vo);
        }
        return R.success("操作成功！");
    }

    /**
     * 生成省平台应收账单
     *
     * @Param: fdBusCost
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/15 11:16
     **/
    public void insertProvinceBill(FdShippingAccount vo, BigDecimal val2, BigDecimal val3, List<FdShippingAccoundetail> dataList, SecruityUser userInfo, ExecutorService executor, String ysCode, String yfCode) {
        try {
            int num = 0;
            if (CollUtil.isNotEmpty(dataList)) {
                num = dataList.size();
            }
            //应收

            BillDealWithCity billDealWithCity = new BillDealWithCity();
            billDealWithCity.setBillCode(ysCode);
            billDealWithCity.setProvinceShiftNum(vo.getShiftNo());
            billDealWithCity.setProvinceShiftName(vo.getShiftName());
            billDealWithCity.setCustomerCode(vo.getPlatformCode());
            billDealWithCity.setSourceCode(vo.getCustomerNo());
            billDealWithCity.setCustomerName(vo.getPlatformName());
            billDealWithCity.setSourceUnit(vo.getCustomerName());
            billDealWithCity.setBoxCapacity(BigDecimal.valueOf(num));
            billDealWithCity.setPostDate(Date.from(vo.getShippingTime().atZone(ZoneId.systemDefault()).toInstant()));
            billDealWithCity.setShippingLines(vo.getShippingLine());
            billDealWithCity.setDirection(vo.getTrip());
            billDealWithCity.setStage("YF");
            billDealWithCity.setCreateBy(userInfo.getRealName());
            billDealWithCity.setCreateTime(new Date());

            //子账单-运费
            BillSubPayCity billSubPayCity = new BillSubPayCity();
            billSubPayCity.setBillCode(billDealWithCity.getBillCode());
            billSubPayCity.setBillSubCode(billDealWithCity.getBillCode() + "-001");
            billSubPayCity.setAccountCode(vo.getAccountCode());
            billSubPayCity.setShiftName(vo.getShiftName());

            Shifmanagement sel = new Shifmanagement();
            sel.setShiftId(vo.getShiftNo());
            sel.setPlatformCode(vo.getPlatformCode());
            sel.setDeleteFlag("N");
            List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
            if (CollUtil.isNotEmpty(shifmanagements)) {
                if (StrUtil.isNotEmpty(shifmanagements.get(0).getPortStation())) {
                    billSubPayCity.setPortStation(shifmanagements.get(0).getPortStation());
                }
            }

            FdBusCost sel2 = new FdBusCost();
            sel2.setShiftNo(vo.getShiftNo());
            sel2.setPlatformCode(vo.getPlatformCode());
            sel2.setDeleteFlag("N");
            List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel2);
            String code = "";
            if (CollUtil.isNotEmpty(fdBusCosts)) {
                for (FdBusCost fdBusCost : fdBusCosts) {
                    code = code + fdBusCost.getCostCode() + ",";
                }

            }
            billSubPayCity.setCostCode(code);
            billSubPayCity.setShipmentTime(vo.getShippingTime());
            billSubPayCity.setPlatformCode(vo.getCustomerNo());
            billSubPayCity.setPlatformName(vo.getCustomerName());
            billSubPayCity.setPlatformLevel("0");
            billSubPayCity.setShiftNo(vo.getShiftNo());
            billSubPayCity.setBillAmount(val2);
            billSubPayCity.setBillingState("0");
            billSubPayCity.setCustomerCode(vo.getPlatformCode());
            billSubPayCity.setCustomerName(vo.getPlatformName());
            billSubPayCity.setStatus("1");
            billSubPayCity.setBoxNum(num);
            billSubPayCity.setAddWho(userInfo.getUserName());
            billSubPayCity.setAddWhoName(userInfo.getRealName());
            billSubPayCity.setAddTime(LocalDateTime.now());
            billSubPayCityMapper.insertBillSubPayCity(billSubPayCity);

            //应付
            BillPayProvince billPayProvince = new BillPayProvince();
            billPayProvince.setBillCode(yfCode);
            billPayProvince.setProvinceShiftNum(vo.getShiftNo());
            billPayProvince.setProvinceTrainName(vo.getShiftName());
            billPayProvince.setCustomerCode(vo.getCustomerNo());
            billPayProvince.setSourceCode("ztdl");
            billPayProvince.setCustomerName(vo.getCustomerName());
            billPayProvince.setSourceUnit("中铁国际多式联运有限公司");
            billPayProvince.setSourceUnitTwo(vo.getPlatformName());
            billPayProvince.setSourceUnitTwoCode(vo.getPlatformCode());
            billPayProvince.setBoxCapacity(BigDecimal.valueOf(num));
            billPayProvince.setPostDate(Date.from(vo.getShippingTime().atZone(ZoneId.systemDefault()).toInstant()));
            billPayProvince.setShippingLines(vo.getShippingLine());
            billPayProvince.setDirection(vo.getTrip());
            billPayProvince.setStage("YF");
            billPayProvince.setCreateBy(userInfo.getRealName());
            billPayProvince.setCreateTime(new Date());

            //应付子账单-运费
            BillPayProvinceSub billPayProvinceSub = new BillPayProvinceSub();
            billPayProvinceSub.setBillCode(billPayProvince.getBillCode());
            billPayProvinceSub.setBillSubCode(billPayProvince.getBillCode() + "-001");
            billPayProvinceSub.setAccountCode(vo.getAccountCode());
            billPayProvinceSub.setShiftName(vo.getShiftName());

            billPayProvinceSub.setShipmentTime(Date.from(vo.getShippingTime().atZone(ZoneId.systemDefault()).toInstant()));
            billPayProvinceSub.setCostCode(sysNoConfigService.genNo("FDC"));
            billPayProvinceSub.setPlatformCode("ztdl");
            billPayProvinceSub.setPlatformName("中铁国际多式联运有限公司");
            billPayProvinceSub.setPlatformLevel("0");
            billPayProvinceSub.setShiftNo(vo.getShiftNo());
            billPayProvinceSub.setBillAmount(val3);
            billPayProvinceSub.setBillingState("0");
            billPayProvinceSub.setCustomerCode(vo.getCustomerNo());
            billPayProvinceSub.setCustomerName(vo.getCustomerName());
            billPayProvinceSub.setStatus("1");
            billPayProvinceSub.setBoxNum(num);
            billPayProvinceSub.setAddWho(userInfo.getUserName());
            billPayProvinceSub.setAddWhoName(userInfo.getRealName());
            billPayProvinceSub.setAddTime(new Date());
            billPayProvinceSubMapper.insertBillPayProvinceSub(billPayProvinceSub);


            //补充运费费用详情
            if (CollUtil.isNotEmpty(dataList)) {

                for (FdShippingAccoundetail fdShippingAccoundetail : dataList) {
                    //更新市平台应付费用
                    FdBusCostDetail jn = new FdBusCostDetail();
                    jn.setBillSubCode(billSubPayCity.getBillSubCode());
                    jn.setCostType("1");
                    jn.setContainerNumber(fdShippingAccoundetail.getContainerNumber());
                    jn.setCodeSsCategoriesName("国内段包干");
                    jn.setReceiveCode(vo.getCustomerNo());
                    jn.setExchangeRate(BigDecimal.valueOf(1));
                    jn.setExchangeRateNew(BigDecimal.valueOf(1));
                    jn.setLocalAmount(fdShippingAccoundetail.getDomesticFreight());
                    jn.setOriginalAmount(fdShippingAccoundetail.getDomesticFreight());
                    jn.setShiftNo(vo.getShiftNo());
                    if ("export".equals(fdShippingAccoundetail.getSource())) {
                        billDealWithCity.setSource("export");
                        //潍坊
                        if (CollUtil.isNotEmpty(fdBusCosts)) {
                            for (FdBusCost cost : fdBusCosts) {
                                if (cost.getPlatformCode().equals(vo.getPlatformCode())) {

                                }
                            }
                        }
                        jn.setCodeBbCategoriesCode("f_fee_type");
                        jn.setCodeBbCategoriesName("发运运费");
                        jn.setCodeSsCategoriesCode("jndtlyf");
                        jn.setReceiveName(vo.getCustomerName());
                        jn.setPayCode(vo.getPlatformCode());
                        jn.setPayName(vo.getPlatformName());
                        jn.setAddWho(userInfo.getUserName());
                        jn.setAddWhoName(userInfo.getRealName());
                        jn.setAddTime(LocalDateTime.now());
                        fdBusCostDetailMapper.insertFdBusCostDetail(jn);
                    } else {
                        jn.setUpdateWho(userInfo.getUserName());
                        jn.setUpdateWhoName(userInfo.getRealName());
                        jn.setUpdateTime(LocalDateTime.now());
                        int i = fdBusCostDetailMapper.updateFdBusCostDetailByCostCode(jn);
                        if (i <= 0) {
                            //市平台后续应收中添加了箱信息，台账审核后同步新增到应付
                            if (CollUtil.isNotEmpty(fdBusCosts)) {
                                for (FdBusCost cost : fdBusCosts) {
                                    FdBusCostDetail fdBusCostDetail = new FdBusCostDetail();
                                    fdBusCostDetail.setCostCode(cost.getCostCode());
                                    fdBusCostDetail.setContainerNumber(jn.getContainerNumber());
                                    fdBusCostDetail.setCostType("0");
                                    List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList(fdBusCostDetail);
                                    if (CollUtil.isNotEmpty(detailList)) {
                                        jn.setCostCode(cost.getCostCode());
                                        jn.setCodeBbCategoriesCode("f_fee_type");
                                        jn.setCodeBbCategoriesName("发运运费");
                                        jn.setCodeSsCategoriesCode("jndtlyf");
                                        jn.setReceiveName(vo.getCustomerName());
                                        jn.setPayCode(vo.getPlatformCode());
                                        jn.setPayName(vo.getPlatformName());
                                        jn.setAddWho(userInfo.getUserName());
                                        jn.setAddWhoName(userInfo.getRealName());
                                        jn.setAddTime(LocalDateTime.now());
                                        fdBusCostDetailMapper.insertFdBusCostDetail(jn);
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    FdBusCostDetail jw = new FdBusCostDetail();
                    jw.setBillSubCode(billSubPayCity.getBillSubCode());
                    jw.setCostType("1");
                    jw.setContainerNumber(fdShippingAccoundetail.getContainerNumber());
                    jw.setCodeSsCategoriesName("国外段包干");
                    jw.setReceiveCode(vo.getCustomerNo());
                    jw.setCurrency(fdShippingAccoundetail.getMonetaryType());
                    jw.setExchangeRate(fdShippingAccoundetail.getExchangeRate());
                    jw.setExchangeRateNew(fdShippingAccoundetail.getExchangeRate());
                    jw.setLocalAmount(fdShippingAccoundetail.getOverseasFreightCny());
                    jw.setOriginalAmount(fdShippingAccoundetail.getOverseasFreightOc());
                    jw.setUpdateWho(userInfo.getUserName());
                    jw.setUpdateWhoName(userInfo.getRealName());
                    jw.setUpdateTime(LocalDateTime.now());
                    jw.setShiftNo(vo.getShiftNo());
                    if ("export".equals(fdShippingAccoundetail.getSource())) {
                        billDealWithCity.setSource("export");
                        //潍坊
                        jw.setCodeBbCategoriesCode("f_fee_type");
                        jw.setCodeBbCategoriesName("发运运费");
                        jw.setCodeSsCategoriesCode("jwdtlyf");
                        jw.setReceiveName(vo.getCustomerName());
                        jw.setPayCode(vo.getPlatformCode());
                        jw.setPayName(vo.getPlatformName());
                        jw.setAddWho(userInfo.getUserName());
                        jw.setAddWhoName(userInfo.getRealName());
                        jw.setAddTime(LocalDateTime.now());
                        fdBusCostDetailMapper.insertFdBusCostDetail(jw);
                    } else {
                        jw.setUpdateWho(userInfo.getUserName());
                        jw.setUpdateWhoName(userInfo.getRealName());
                        jw.setUpdateTime(LocalDateTime.now());
                        int i = fdBusCostDetailMapper.updateFdBusCostDetailByCostCode(jw);
                        if (i <= 0) {
                            //市平台后续应收中添加了箱信息，台账审核后同步新增到应付
                            if (CollUtil.isNotEmpty(fdBusCosts)) {
                                for (FdBusCost cost : fdBusCosts) {
                                    FdBusCostDetail fdBusCostDetail = new FdBusCostDetail();
                                    fdBusCostDetail.setCostCode(cost.getCostCode());
                                    fdBusCostDetail.setContainerNumber(jw.getContainerNumber());
                                    fdBusCostDetail.setCostType("0");
                                    List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList(fdBusCostDetail);
                                    if (CollUtil.isNotEmpty(detailList)) {
                                        jw.setCostCode(cost.getCostCode());
                                        jw.setCodeBbCategoriesCode("f_fee_type");
                                        jw.setCodeBbCategoriesName("发运运费");
                                        jw.setCodeSsCategoriesCode("jwdtlyf");
                                        jw.setReceiveName(vo.getCustomerName());
                                        jw.setPayCode(vo.getPlatformCode());
                                        jw.setPayName(vo.getPlatformName());
                                        jw.setAddWho(userInfo.getUserName());
                                        jw.setAddWhoName(userInfo.getRealName());
                                        jw.setAddTime(LocalDateTime.now());
                                        fdBusCostDetailMapper.insertFdBusCostDetail(jw);
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    //新增省平台应付详情
                    FdBusCostDetail jnyf = new FdBusCostDetail();
//                jnyf.setCostCode(billPayProvinceSub.getCostCode());
                    jnyf.setBillSubCode(billPayProvinceSub.getBillSubCode());
                    jnyf.setCostType("1");
                    jnyf.setContainerNumber(fdShippingAccoundetail.getContainerNumber());
                    jnyf.setCodeBbCategoriesCode("f_fee_type");
                    jnyf.setCodeBbCategoriesName("发运运费");
                    jnyf.setCodeSsCategoriesCode("jndtlyf");
                    jnyf.setCodeSsCategoriesName("国内段包干");
                    jnyf.setReceiveCode("ztdl");
                    jnyf.setReceiveName("中铁国际多式联运有限公司");
                    jnyf.setPayCode(vo.getCustomerNo());
                    jnyf.setPayName(vo.getCustomerName());
                    jnyf.setCurrency("人民币");
                    jnyf.setExchangeRate(BigDecimal.valueOf(1));
                    jnyf.setExchangeRateNew(BigDecimal.valueOf(1));
                    jnyf.setLocalAmount(fdShippingAccoundetail.getRrDomesticFreight());
                    jnyf.setOriginalAmount(fdShippingAccoundetail.getRrDomesticFreight());
                    jnyf.setAuditStatus("0");
                    jnyf.setShiftNo(vo.getShiftNo());
                    jnyf.setAddWho(userInfo.getUserName());
                    jnyf.setAddWhoName(userInfo.getRealName());
                    jnyf.setAddTime(LocalDateTime.now());
                    fdBusCostDetailMapper.insertFdBusCostDetail(jnyf);

                    FdBusCostDetail jwyf = new FdBusCostDetail();
//                jwyf.setCostCode(billPayProvinceSub.getCostCode());
                    jwyf.setBillSubCode(billPayProvinceSub.getBillSubCode());
                    jwyf.setCostType("1");
                    jwyf.setContainerNumber(fdShippingAccoundetail.getContainerNumber());
                    jwyf.setCodeBbCategoriesCode("f_fee_type");
                    jwyf.setCodeBbCategoriesName("发运运费");
                    jwyf.setCodeSsCategoriesCode("jwdtlyf");
                    jwyf.setCodeSsCategoriesName("国外段包干");
                    jwyf.setReceiveCode("ztdl");
                    jwyf.setReceiveName("中铁国际多式联运有限公司");
                    jwyf.setPayCode(vo.getCustomerNo());
                    jwyf.setPayName(vo.getCustomerName());
                    jwyf.setCurrency(fdShippingAccoundetail.getMonetaryType());
                    jwyf.setExchangeRate(fdShippingAccoundetail.getExchangeRate());
                    jwyf.setExchangeRateNew(fdShippingAccoundetail.getExchangeRate());
                    jwyf.setLocalAmount(fdShippingAccoundetail.getRrOverseasFreightCny());
                    jwyf.setOriginalAmount(fdShippingAccoundetail.getRrOverseasFreightOc());
                    jwyf.setAuditStatus("0");
                    jwyf.setShiftNo(vo.getShiftNo());
                    jwyf.setAddWho(userInfo.getUserName());
                    jwyf.setAddWhoName(userInfo.getRealName());
                    jwyf.setAddTime(LocalDateTime.now());
                    fdBusCostDetailMapper.insertFdBusCostDetail(jwyf);
                }
            }

            //应收费用主表
            //潍坊
        /*FdBusCost fdBusCost = new FdBusCost();
        fdBusCost.setCostCode(billPayProvinceSub.getCostCode());
        fdBusCost.setPlatformCode(vo.getCustomerNo());
        fdBusCost.setPlatformName(vo.getCustomerName());
        fdBusCost.setCustomerCode(vo.getCustomerNo());
        fdBusCost.setCustomerName(vo.getCustomerName());
        fdBusCost.setPlatformLevel("1");
        fdBusCost.setShiftNo(vo.getShiftNo());
        fdBusCost.setDeleteFlag("N");
        fdBusCost.setAddWho(SecurityUtils.getUserInfo().getUserName());
        fdBusCost.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
        fdBusCost.setAddTime(LocalDateTime.now());
        fdBusCostMapper.insertFdBusCost(fdBusCost);*/

            //更新提前生成的撤换箱费用账单编码
            fdBusCostDetailMapper.updateBillSubCode(billSubPayCity.getBillSubCode(), vo.getAccountCode(), null, vo.getCustomerNo());
            fdBusCostDetailMapper.updateBillSubCode(billPayProvinceSub.getBillSubCode(), vo.getAccountCode(), vo.getCustomerNo(), null);

            billDealWithCityMapper.insertBillDealWithCity(billDealWithCity);
            billPayProvinceMapper.insertBillPayProvince(billPayProvince);

            billSubPayCityMapper.updateLocalAmountByBillSubCode(billSubPayCity.getBillSubCode());
            billDealWithCityMapper.updateBillAmountByBillCode(billDealWithCity.getBillCode());

            billPayProvinceSubMapper.updateLocalAmountByBillSubCode(billPayProvinceSub.getBillSubCode());
            billPayProvinceMapper.updateBillAmountByBillCode(billPayProvince.getBillCode());

            vo.setStatus("2");
            vo.setBillStatus("3");
            fdShippingAccountMapper.auditStatus(vo);
        } catch (Exception e) {
            log.info(e.getMessage());
            back(vo.getRowId());
        } finally {
            executor.shutdown();
        }
    }


    /**
     * 二次导入台账审核
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R secondAuditStatus(FdShippingAccountVO vo) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        vo.setUpdateTime(LocalDateTime.now());
        vo.setUpdateWho(userInfo.getUserName());
        vo.setUpdateWhoName(userInfo.getRealName());
        //本次台账应收各项费用合计
        BigDecimal overseasFreightOc = BigDecimal.valueOf(0);
        BigDecimal overseasFreightCny = BigDecimal.valueOf(0);
        BigDecimal domesticFreight = BigDecimal.valueOf(0);
        //审核同意后生成台账、子账单信息
        if ("5".equals(vo.getStatus())) {
            FdShippingAccount fdShippingAccount = fdShippingAccountMapper.selectFdShippingAccountById(vo.getRowId());

            //班次剩余舱位数
            String num1 = shifmanagementMapper.getNum(fdShippingAccount.getShiftNo());

            //二次导入前该运单占用舱位数
            String num2 = waybillContainerInfoService.getNum(fdShippingAccount.getShiftNo());

            //二次导入后该运单占用舱位数
            Double num3 = 0D;


            //查询台账分表内容
            /*FdShippingAccoundetailSub sel = new FdShippingAccoundetailSub();
            sel.setAccountCode(fdShippingAccount.getAccountCode());
            sel.setDeleteFlag("N");
            List<FdShippingAccoundetailSub> fdShippingAccoundetailSubs = fdShippingAccoundetailSubMapper.selectFdShippingAccoundetailSubList(sel);*/
            List<FdShippingAccoundetailSub> addList = vo.getList();

            if (CollUtil.isNotEmpty(addList)) {

                FdShippingAccoundetail sel = new FdShippingAccoundetail();
                sel.setAccountCode(fdShippingAccount.getAccountCode());
                sel.setDeleteFlag("N");
                List<FdShippingAccoundetail> deleteList = fdShippingAccoundetailMapper.selectFdShippingAccoundetailList(sel);

                //对比新旧版本数据，确认新数据修改了哪些金额
                List<FdShippingAccoundetailSub> fdShippingAccoundetailSubs = checkStatus(addList, deleteList);

                //统计新数据舱位数
                for (FdShippingAccoundetailSub sub : fdShippingAccoundetailSubs) {
                    if (!"1".equals(sub.getContainerNewestStatus())) {
                        if ("20".equals(sub.getContainerType())) {
                            num3 += 0.5;
                        } else if ("40".equals(sub.getContainerType()) || "45".equals(sub.getContainerType())) {
                            num3 += 1;
                        }
                    }
                }

                //校验舱位数是否足够
                if (StrUtil.isNotEmpty(num1) && StrUtil.isNotEmpty(num2)) {
                    Double d1 = Double.valueOf(num1);
                    Double d2 = Double.valueOf(num2);
                    if (num3 - d2 > d1) {
                        return new R<>(500, Boolean.FALSE, null, "舱位数不足，不能完成二次导入！");
                    }
                } else {
                    return new R<>(500, Boolean.FALSE, null, "舱位数异常，请联系管理员！");
                }

                //应收主账单
                FdBill selYs = new FdBill();
                selYs.setTrainNumber(fdShippingAccount.getShiftNo());
                selYs.setPlatformLevel("1");
                selYs.setDelFlag("N");
                List<FdBill> ysBill = fdBillMapper.selectFdBillList(selYs);

                //应付主账单
                FdBill selYf = new FdBill();
                selYf.setTrainNumber(fdShippingAccount.getShiftNo());
                selYf.setPlatformLevel("2");
                selYf.setDelFlag("N");
                List<FdBill> yfBill = fdBillMapper.selectFdBillList(selYf);


                //应收账单明细
                List<FdBillSubDetail> ysList = new ArrayList<>();
                //应付账单明细
                List<FdBillSubDetail> yfList = new ArrayList<>();

                //本次台账应收/应付费用合计
                BigDecimal ysVal = BigDecimal.valueOf(0);
                BigDecimal yfVal = BigDecimal.valueOf(0);

                //计算与上次台账金额的差值
                FdShippingAccoundetail fdShippingAccoundetail = new FdShippingAccoundetail();
                fdShippingAccoundetail.setAccountCode(fdShippingAccount.getAccountCode());
                FdShippingAccoundetail oldTotal = fdShippingAccoundetailMapper.calculateTotal(fdShippingAccoundetail);

                String remarks = "二次导入产生变更（";
                String addStr = "";
                String updateStr = "";
                String deleteStr = "";

                List<FdShippingAccoundetailSub> addList2 = new ArrayList<>();
                for (FdShippingAccoundetailSub sub : fdShippingAccoundetailSubs) {
                    if ("0".equals(sub.getContainerNewestStatus())) {
                        addList2.add(sub);
                        addStr += sub.getContainerNumber() + " ";
                        //新增箱数据处理
                        ysVal = ysVal.add(sub.getDomesticFreight()).add(sub.getOverseasFreightCny());
                        yfVal = yfVal.add(sub.getRrDomesticFreight()).add(sub.getRrOverseasFreightCny());
                        overseasFreightOc = overseasFreightOc.add(sub.getOverseasFreightOc());
                        overseasFreightCny = overseasFreightCny.add(sub.getOverseasFreightCny());
                        domesticFreight = domesticFreight.add(sub.getDomesticFreight());

                        FdShippingAccoundetail newObj = new FdShippingAccoundetail();
                        newObj.setAccountCode(sub.getAccountCode());
                        newObj.setCustomerNo(sub.getCustomerNo());
                        newObj.setCustomerName(sub.getCustomerName());
                        newObj.setApplicationNumber(sub.getApplicationNumber());
                        newObj.setTransportOrderNumber(sub.getTransportOrderNumber());
                        newObj.setContainerNumber(sub.getContainerNumber());
                        newObj.setClearanceNumber(sub.getClearanceNumber());
                        newObj.setValueUsd(sub.getValueUsd());
                        newObj.setCustomsSeal(sub.getCustomsSeal());
                        newObj.setTrainNumber(sub.getTrainNumber());
                        newObj.setWaybillDemandNumber(sub.getWaybillDemandNumber());
                        newObj.setWaybillLnNumber(sub.getWaybillLnNumber());
                        newObj.setSubsidyStandards(sub.getSubsidyStandards());
                        newObj.setSubsidyAmount(sub.getSubsidyAmount());
                        newObj.setMonetaryType(sub.getMonetaryType());
                        newObj.setExchangeRate(sub.getExchangeRate());
                        newObj.setOverseasFreightOc(sub.getOverseasFreightOc());
                        newObj.setRrOverseasFreightCny(sub.getOverseasFreightCny());
                        newObj.setDomesticFreight(sub.getDomesticFreight());
                        newObj.setContainerStatus("0");
                        newObj.setShippingFreight(sub.getShippingFreight());
                        newObj.setRemarks(sub.getRemarks());
                        newObj.setIsRansit(sub.getIsRansit());
                        newObj.setOrgUnit(sub.getOrgUnit());
                        newObj.setDestinationName(sub.getDestinationName());
                        newObj.setDestination(sub.getDestination());
                        newObj.setGoodsName(sub.getGoodsName());
                        newObj.setGoodsNums(sub.getGoodsNums());
                        newObj.setGoodsWeight(sub.getGoodsWeight());
                        newObj.setContainerType(sub.getContainerType());
                        newObj.setConsignorName(sub.getConsignorName());
                        newObj.setDestinationCountry(sub.getDestinationCountry());
                        newObj.setIsFull(sub.getIsFull());
                        newObj.setNonFerrous(sub.getNonFerrous());
                        newObj.setGoodsOrigin(sub.getGoodsOrigin());
                        newObj.setContainerWeight(sub.getContainerWeight());
                        newObj.setGoodsOwner(sub.getGoodsOwner());
                        newObj.setPortAgent(sub.getPortAgent());
                        newObj.setContainerOwner(sub.getContainerOwner());
                        newObj.setContainerNewestStatus("0");
                        newObj.setContainerTypeCode(sub.getContainerTypeCode());
                        newObj.setContainerTypeName(sub.getContainerTypeName());
                        newObj.setRrOverseasFreightOc(sub.getRrOverseasFreightOc());
                        newObj.setRrOverseasFreightCny(sub.getRrOverseasFreightCny());
                        newObj.setRrDomesticFreight(sub.getRrDomesticFreight());
                        newObj.setAddWho(userInfo.getUserName());
                        newObj.setAddWhoName(userInfo.getRealName());
                        newObj.setAddTime(LocalDateTime.now());
                        fdShippingAccoundetailMapper.insertFdShippingAccoundetail(newObj);

                        //应收
                        List<FdBillSubDetail> ysBills = ysSub(sub, ysBill.get(0), "0");
                        if (CollUtil.isNotEmpty(ysBills)) {
                            ysList.add(ysBills.get(0));
                            ysList.add(ysBills.get(1));
                        }

                        //应付
                        List<FdBillSubDetail> yfBills = yfSub(sub, yfBill.get(0), "0");
                        if (CollUtil.isNotEmpty(yfBills)) {
                            yfList.add(yfBills.get(0));
                            yfList.add(yfBills.get(1));
                        }

                    } else if ("1".equals(sub.getContainerNewestStatus())) {
                        deleteStr += sub.getContainerNumber() + " ";
                        //撤箱数据处理
                        /*ysVal = ysVal.subtract(sub.getDomesticFreight()).subtract(sub.getOverseasFreightCny());
                        yfVal = yfVal.subtract(sub.getRrDomesticFreight()).subtract(sub.getRrOverseasFreightCny());*/
                        overseasFreightOc = overseasFreightOc.subtract(sub.getOverseasFreightOc());
                        overseasFreightCny = overseasFreightCny.subtract(sub.getOverseasFreightCny());
                        domesticFreight = domesticFreight.subtract(sub.getDomesticFreight());

                        FdShippingAccoundetail del = new FdShippingAccoundetail();
                        del.setAccountCode(fdShippingAccount.getAccountCode());
                        del.setContainerNumber(sub.getContainerNumber());
                        del.setDeleteFlag("C");
                        del.setContainerStatus("1");
                        del.setContainerNewestStatus("1");
                        del.setDeleteWho(userInfo.getUserName());
                        del.setDeleteWhoName(userInfo.getRealName());
                        del.setDeleteTime(LocalDateTime.now());
                        fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode(del);

                        //应收
                        List<FdBillSubDetail> ysBills = ysSub(sub, ysBill.get(0), "1");
                        if (CollUtil.isNotEmpty(ysBills)) {
                            ysList.add(ysBills.get(0));
                            ysList.add(ysBills.get(1));
                        }

                        //应付
                        List<FdBillSubDetail> yfBills = yfSub(sub, yfBill.get(0), "1");
                        if (CollUtil.isNotEmpty(yfBills)) {
                            yfList.add(yfBills.get(0));
                            yfList.add(yfBills.get(1));
                        }
                        //删除订舱用户订单相关箱数据
                        WaybillContainerInfo waybillContainerInfo = new WaybillContainerInfo();
                        waybillContainerInfo.setDeleteFlag("N");
                        waybillContainerInfo.setShiftNo(fdShippingAccount.getShiftNo());
                        waybillContainerInfo.setDeleteWho(userInfo.getUserName());
                        waybillContainerInfo.setDeleteWhoName(userInfo.getRealName());
                        waybillContainerInfo.setDeleteTime(new Date());
                        waybillContainerInfo.setOperFlag("C");
                        waybillContainerInfo.setContainerNo(sub.getContainerNumber());
                        waybillContainerInfoMapper.updateContainerInfo1(waybillContainerInfo);

                        //获取删除箱对应的申请单号
                        BookingRequesdetail book = new BookingRequesdetail();
                        book.setShiftNo(fdShippingAccount.getShiftNo());
                        book.setContainerNo(sub.getContainerNumber());
                        String order = bookingRequesdetailMapper.selectOrderByShiftNoAndContainerNo(book);
                        if (StrUtil.isNotEmpty(order)) {
                            Float delnum = 0f;
                            if ("20".equals(sub.getContainerType())) {
                                delnum = 0.5f;
                            } else if ("40".equals(sub.getContainerType()) || "45".equals(sub.getContainerType())) {
                                delnum = 1f;
                            }

                            //更新舱单占用表
                            SpaceOccupy delObj = new SpaceOccupy();
                            delObj.setShiftNo(fdShippingAccount.getShiftNo());
                            delObj.setOrderNo(order);
                            delObj.setSpaceNums(String.valueOf(delnum));
                            spaceOccupyMapper.updateSpaceNums(delObj);

                            //更新申请单舱位数
                            BookingRequesheader delObj2 = new BookingRequesheader();
                            delObj2.setOrderNo(order);
                            delObj2.setSpaceNums(delnum);
                            bookingRequesheaderMapper.updateSpaceNums(delObj2);

                            //更新运单舱位数
                            WaybillHeader delObj3 = new WaybillHeader();
                            delObj3.setOrderNo(order);
                            delObj3.setResveredField04(String.valueOf(delnum));
                            waybillHeaderMapper.updateSpaceNums(delObj3);
                        }

                        //删除订舱用户申请单相关箱数据
                        BookingRequesdetail book2 = new BookingRequesdetail();
                        book2.setShiftNo(fdShippingAccount.getShiftNo());
                        book2.setContainerNo(sub.getContainerNumber());
                        bookingRequesdetailMapper.deleteByShiftNoAndContainerNo(book2);


                        //查询箱对应的费用详情数据
                        FdCosdetail fdCosdetail = new FdCosdetail();
//                        fdCosdetail.setPlatformLevel("0");
                        fdCosdetail.setContainerNumber(sub.getContainerNumber());
                        fdCosdetail.setTrainNumber(fdShippingAccount.getShiftNo());
                        List<FdCosdetail> fdCosdetails = fdCosdetailMapper.selectByTrainsNumber(fdCosdetail);
                        if (CollUtil.isNotEmpty(fdCosdetails)) {
                            String billCode = "";
                            BigDecimal val = BigDecimal.valueOf(0);
                            FdShippingAccount fd = new FdShippingAccount();
                            fd.setShiftNo(fdShippingAccount.getShiftNo());

                            for (FdCosdetail detail : fdCosdetails) {
                                //更新省市箱的撤箱标识
                                FdCosdetail updateObj = new FdCosdetail();
                                updateObj.setUuid(detail.getUuid());
                                updateObj.setOperFlag("C");
                                fdCosdetailMapper.updateFdCosdetail(updateObj);
                                if (StrUtil.isNotEmpty(detail.getPlatformLevel()) && "0".equals(detail.getPlatformLevel())) {
                                    //过滤订舱用户的箱信息
                                    FdBill fdBill = new FdBill();
                                    fdBill.setTransportOrderNumber(detail.getTransportOrderNumber());
                                    fdBill.setDelFlag("N");
                                    List<FdBill> fdBills = fdBillMapper.selectFdBillListByLike(fdBill);
                                    if (CollUtil.isNotEmpty(fdBills)) {
                                        billCode = fdBills.get(0).getBillCode();
                                    }

                                    fd.setCustomerNo(detail.getCustomerCode());
                                    fd.setCustomerName(detail.getCustomerName());
                                    fd.setPlatformCode(detail.getPlatformCode());
                                    fd.setPlatformName(detail.getPlatformName());

                                    //合计订舱用户该箱的费用
                                    val = val.add(detail.getLocalCurrencyAmount());

                                }
                            }
                            //添加订舱用户费用余额
                            if (StrUtil.isNotEmpty(fd.getCustomerNo()) && StrUtil.isNotEmpty(fd.getCustomerName()) && StrUtil.isNotEmpty(fd.getPlatformCode()) && StrUtil.isNotEmpty(fd.getPlatformName())) {
                                getFdBalanceDetail(fd, val.negate(), "0", billCode, "撤回箱：" + sub.getContainerNumber());
                            }
                        }

                    } else if ("2".equals(sub.getContainerNewestStatus())) {
                        updateStr += sub.getContainerNumber() + " ";
                        //应收应付费用都有修改数据处理
                        ysVal = ysVal.add(sub.getDomesticFreight()).add(sub.getOverseasFreightCny());
                        yfVal = yfVal.add(sub.getRrDomesticFreight()).add(sub.getRrOverseasFreightCny());
                        overseasFreightOc = overseasFreightOc.add(sub.getOverseasFreightOc());
                        overseasFreightCny = overseasFreightCny.add(sub.getOverseasFreightCny());
                        domesticFreight = domesticFreight.add(sub.getDomesticFreight());

                        sub.setRowId(null);
                        sub.setUpdateWho(userInfo.getUserName());
                        sub.setUpdateWhoName(userInfo.getRealName());
                        sub.setUpdateTime(LocalDateTime.now());
                        fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode2(sub);

                        //应收
                        List<FdBillSubDetail> ysBills = ysSub(sub, ysBill.get(0), "2");
                        if (CollUtil.isNotEmpty(ysBills)) {
                            ysList.add(ysBills.get(0));
                            ysList.add(ysBills.get(1));
                        }

                        //应付
                        List<FdBillSubDetail> yfBills = yfSub(sub, yfBill.get(0), "2");
                        if (CollUtil.isNotEmpty(yfBills)) {
                            yfList.add(yfBills.get(0));
                            yfList.add(yfBills.get(1));
                        }
                    } else if ("3".equals(sub.getContainerNewestStatus())) {
                        updateStr += sub.getContainerNumber() + " ";
                        //应收费用修改数据处理
                        ysVal = ysVal.add(sub.getDomesticFreight()).add(sub.getOverseasFreightCny());
                        yfVal = yfVal.add(sub.getRrDomesticFreight()).add(sub.getRrOverseasFreightCny());
                        overseasFreightOc = overseasFreightOc.add(sub.getOverseasFreightOc());
                        overseasFreightCny = overseasFreightCny.add(sub.getOverseasFreightCny());
                        domesticFreight = domesticFreight.add(sub.getDomesticFreight());

                        sub.setRowId(null);
                        sub.setUpdateWho(userInfo.getUserName());
                        sub.setUpdateWhoName(userInfo.getRealName());
                        sub.setUpdateTime(LocalDateTime.now());
                        fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode2(sub);

                        //应收
                        List<FdBillSubDetail> ysBills = ysSub(sub, ysBill.get(0), "2");
                        if (CollUtil.isNotEmpty(ysBills)) {
                            ysList.add(ysBills.get(0));
                            ysList.add(ysBills.get(1));
                        }

                    } else if ("4".equals(sub.getContainerNewestStatus())) {
                        updateStr += sub.getContainerNumber() + " ";
                        //应付费用修改数据处理
                        ysVal = ysVal.add(sub.getDomesticFreight()).add(sub.getOverseasFreightCny());
                        yfVal = yfVal.add(sub.getRrDomesticFreight()).add(sub.getRrOverseasFreightCny());
                        overseasFreightOc = overseasFreightOc.add(sub.getOverseasFreightOc());
                        overseasFreightCny = overseasFreightCny.add(sub.getOverseasFreightCny());
                        domesticFreight = domesticFreight.add(sub.getDomesticFreight());

                        sub.setRowId(null);
                        sub.setUpdateWho(userInfo.getUserName());
                        sub.setUpdateWhoName(userInfo.getRealName());
                        sub.setUpdateTime(LocalDateTime.now());
                        fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode2(sub);

                        //应付
                        List<FdBillSubDetail> yfBills = yfSub(sub, yfBill.get(0), "2");
                        if (CollUtil.isNotEmpty(yfBills)) {
                            yfList.add(yfBills.get(0));
                            yfList.add(yfBills.get(1));
                        }
                    } else {
                        //金额未修改箱数据处理
                        ysVal = ysVal.add(sub.getDomesticFreight()).add(sub.getOverseasFreightCny());
                        yfVal = yfVal.add(sub.getRrDomesticFreight()).add(sub.getRrOverseasFreightCny());
                        overseasFreightOc = overseasFreightOc.add(sub.getOverseasFreightOc());
                        overseasFreightCny = overseasFreightCny.add(sub.getOverseasFreightCny());
                        domesticFreight = domesticFreight.add(sub.getDomesticFreight());

                        sub.setRowId(null);
                        sub.setUpdateWho(userInfo.getUserName());
                        sub.setUpdateWhoName(userInfo.getRealName());
                        sub.setUpdateTime(LocalDateTime.now());
                        fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode2(sub);

                    }

                }

                if (StrUtil.isNotEmpty(addStr)) {
                    remarks += "新增箱：" + addStr;
                }
                if (StrUtil.isNotEmpty(deleteStr)) {
                    remarks += "撤回箱：" + deleteStr;
                }
                if (StrUtil.isNotEmpty(updateStr)) {
                    remarks += "修改费用箱：" + updateStr;
                }
                if (StrUtil.isNotEmpty(addStr) || StrUtil.isNotEmpty(deleteStr) || StrUtil.isNotEmpty(updateStr)) {
                    remarks += "）。";
                } else {
                    remarks = null;
                }
                //给新增箱补充对应申请单和订单
                if (CollUtil.isNotEmpty(addList2)) {
                    getOrderAndWayBill(addList2, fdShippingAccount, fdShippingAccoundetailSubs);
                }

                //计算与上版本台账金额差值
                if (oldTotal != null) {
                    if (oldTotal.getSumFreight() != null) {
                        ysVal = ysVal.subtract(oldTotal.getSumFreight());
                    }
                    if (oldTotal.getSumRrFreight() != null) {
                        yfVal = yfVal.subtract(oldTotal.getSumRrFreight());
                    }
                }

                if (CollUtil.isNotEmpty(ysList)) {
                    //保存应收子账单主表
                    FdBillSub ysSub = getFdBillSub(ysBill, fdShippingAccount, ysVal, "1");
                    ysSub.setRemark(remarks);
                    ysSub.setAddWho(userInfo.getUserName());
                    ysSub.setAddWhoName(userInfo.getRealName());
                    ysSub.setAddTime(LocalDateTime.now());


                    //保存应收子账单子表
                    for (FdBillSubDetail detail : ysList) {
                        detail.setBillSubCode(ysSub.getBillSubCode());
                        detail.setAddWho(userInfo.getUserName());
                        detail.setAddWhoName(userInfo.getRealName());
                        detail.setAddTime(LocalDateTime.now());
                        fdBillSubDetailMapper.insertFdBillSubDetail(detail);
                    }

                    //若差额为负数，则需要将差额添加进市在省的余额
                    if (ysVal.compareTo(BigDecimal.valueOf(0)) < 0) {
                        getFdBalanceDetail(fdShippingAccount, ysVal, "1", ysSub.getBillSubCode(), remarks);
                        ysSub.setBillingState("4");
                    }
                    fdBillSubMapper.insertFdBillSub(ysSub);
                }

                if (CollUtil.isNotEmpty(yfList)) {
                    //保存应付子账单主表
                    FdBillSub yfSub = getFdBillSub(yfBill, fdShippingAccount, yfVal, "2");
                    yfSub.setRemark(remarks);
                    yfSub.setAddWho(userInfo.getUserName());
                    yfSub.setAddWhoName(userInfo.getRealName());
                    yfSub.setAddTime(LocalDateTime.now());


                    //保存应付子账单子表
                    for (FdBillSubDetail detail : yfList) {
                        detail.setBillSubCode(yfSub.getBillSubCode());
                        detail.setAddWho(userInfo.getUserName());
                        detail.setAddWhoName(userInfo.getRealName());
                        detail.setAddTime(LocalDateTime.now());
                        fdBillSubDetailMapper.insertFdBillSubDetail(detail);
                    }

                    //若差额为负数，则需要将差额添加进省在铁路的余额
                    if (yfVal.compareTo(BigDecimal.valueOf(0)) < 0) {
                        getFdBalanceDetail(fdShippingAccount, yfVal, "2", yfSub.getBillSubCode(), remarks);
                        yfSub.setBillingState("3");
                    }
                    fdBillSubMapper.insertFdBillSub(yfSub);
                }
                //删除台账分表数据
                FdShippingAccoundetailSub del = new FdShippingAccoundetailSub();
                del.setAccountCode(fdShippingAccount.getAccountCode());
                del.setDeleteFlag("Y");
                fdShippingAccoundetailSubMapper.updateFdShippingAccoundetailSubByAccountCode(del);

                vo.setOverseasFreightOc(overseasFreightOc);
                vo.setOverseasFreightCny(overseasFreightCny);
                vo.setDomesticFreight(domesticFreight);

            } else {
                return new R<>(500, Boolean.FALSE, null, "台账分表明细异常，请联系管理员！");
            }

        }

        fdShippingAccountMapper.auditStatus(vo);
        return new R<>(200, Boolean.TRUE, null, "审核完成！");
    }

    /*
     * 构建申请单信息和运单信息
     * */
    public void getOrderAndWayBill(List<FdShippingAccoundetailSub> addList2, FdShippingAccount fdShippingAccount, List<FdShippingAccoundetailSub> fdShippingAccoundetailSubs) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        // 根据BookingUserCode字段拆分为多个列表
        Map<String, List<FdShippingAccoundetailSub>> list = addList2.stream().collect(Collectors.groupingBy(FdShippingAccoundetailSub::getBookingUserCode));

        // 获取拆分后的每个列表
        for (Map.Entry<String, List<FdShippingAccoundetailSub>> entry : list.entrySet()) {
            String userCode = entry.getKey();
            List<FdShippingAccoundetailSub> list2 = entry.getValue();
            if (CollUtil.isNotEmpty(list2)) {
                CustomerInfo selc = new CustomerInfo();
                selc.setCustomerCode(userCode);
                selc.setDeleteFlag("N");
                List<CustomerInfo> cus = customerInfoMapper.selectCustomerInfoList(selc);

                String orderNo = sysNoConfigService.genNo("SQ");
                String wayBillNo = sysNoConfigService.genNo("YD");
                //申请单主表
                BookingRequesheader bookingRequesheader = new BookingRequesheader();
                bookingRequesheader.setRowId(UUID.randomUUID().toString());
                bookingRequesheader.setOrderNo(orderNo);
                bookingRequesheader.setWaybillNo(wayBillNo);
                bookingRequesheader.setBookingCustcode(userCode);
                bookingRequesheader.setShiftNo(fdShippingAccount.getShiftNo());

                //补充线路信息
                Shifmanagement shifmanagement = new Shifmanagement();
                shifmanagement.setShiftId(fdShippingAccount.getShiftNo());
                shifmanagement.setDeleteFlag("N");
                List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(shifmanagement);
                if (CollUtil.isNotEmpty(shifmanagements)) {
                    bookingRequesheader.setShippingLineCode(shifmanagements.get(0).getShippingLineCode());
                    bookingRequesheader.setShippingLine(shifmanagements.get(0).getShippingLine());
                    bookingRequesheader.setTrainName(shifmanagements.get(0).getShiftName());
                    bookingRequesheader.setDeliveryTime(shifmanagements.get(0).getPlanTime());
                    bookingRequesheader.setTrainType(shifmanagements.get(0).getTrainType());
//                                bookingRequesheader.setTransit();
                    bookingRequesheader.setTrip(shifmanagements.get(0).getTrip());
                }

                if (CollUtil.isNotEmpty(cus)) {
                    if (StrUtil.isNotEmpty(cus.get(0).getCompanyName())) {
                        bookingRequesheader.setBookingCustname(cus.get(0).getCompanyName());
                    }
                }

                //申请单详情
                int totalCases = 0;
                Float spaceNums = 0F;
                for (FdShippingAccoundetailSub fdShippingAccoundetailSub : list2) {
                    totalCases++;
                    if ("20".equals(fdShippingAccoundetailSub.getContainerType())) {
                        spaceNums += 0.5F;
                    } else if ("40".equals(fdShippingAccoundetailSub.getContainerType()) || "45".equals(fdShippingAccoundetailSub.getContainerType())) {
                        spaceNums += 1F;
                    }
                    BookingRequesdetail bookingRequesdetail = new BookingRequesdetail();
                    bookingRequesdetail.setRowId(UUID.randomUUID().toString());
                    bookingRequesdetail.setOrderNo(orderNo);
                    bookingRequesdetail.setDestinationName(fdShippingAccoundetailSub.getDestinationName());
                    bookingRequesdetail.setDestination(fdShippingAccoundetailSub.getDestination());
                    if (CollUtil.isNotEmpty(shifmanagements)) {
                        bookingRequesdetail.setBureauSubordinate(shifmanagements.get(0).getBureau());
                        bookingRequesdetail.setPortStation(shifmanagements.get(0).getPortStation());
                    }
                    bookingRequesdetail.setBox(fdShippingAccoundetailSub.getContainerOwner());
                    bookingRequesdetail.setContainerNo(fdShippingAccoundetailSub.getContainerNumber());
                    bookingRequesdetail.setContainerTypeCode(fdShippingAccoundetailSub.getContainerTypeCode());
                    bookingRequesdetail.setContainerTypeName(fdShippingAccoundetailSub.getContainerTypeName());
                    bookingRequesdetail.setContainerType(fdShippingAccoundetailSub.getContainerType());
                    bookingRequesdetail.setGoodsName(fdShippingAccoundetailSub.getGoodsName());
                    bookingRequesdetail.setDeleteFlag("N");
                    bookingRequesdetail.setAddWho(userInfo.getUserName());
                    bookingRequesdetail.setAddWhoName(userInfo.getRealName());
                    bookingRequesdetail.setAddTime(new Date());
                    bookingRequesdetailMapper.insertBookingRequesdetail(bookingRequesdetail);

                }
                bookingRequesheader.setIdentification(fdShippingAccoundetailSubs.get(0).getIsRansit());
                bookingRequesheader.setResveredField02(fdShippingAccoundetailSubs.get(0).getCustomerNo());
                bookingRequesheader.setCityPlatform(fdShippingAccoundetailSubs.get(0).getCustomerName());
                bookingRequesheader.setTotalCases(String.valueOf(totalCases));
                bookingRequesheader.setSpaceNums(spaceNums);
                bookingRequesheader.setDocumentStatus("2");
                bookingRequesheader.setAddWho(userInfo.getUserName());
                bookingRequesheader.setAddWhoName(userInfo.getRealName());
                bookingRequesheader.setAddTime(new Date());
                bookingRequesheader.setDeleteFlag("N");
                bookingRequesheaderMapper.insertBookingRequesheader(bookingRequesheader);

                //更新舱位占用表
                SpaceOccupy spaceOccupy = new SpaceOccupy();
                spaceOccupy.setRowId(UUID.randomUUID().toString());
                spaceOccupy.setShiftNo(fdShippingAccount.getShiftNo());
                spaceOccupy.setOrderNo(orderNo);
                spaceOccupy.setSpaceNums(String.valueOf(spaceNums));
                spaceOccupy.setDeleteFlag("N");
                spaceOccupyMapper.insertSpaceOccupy(spaceOccupy);

                //补充运单相关数据

                //插入运单主、子表信息
                WaybillHeader waybillHeader = new WaybillHeader();
                waybillHeader.setRowId(UUID.randomUUID().toString());
                waybillHeader.setOrderNo(bookingRequesheader.getOrderNo());
                waybillHeader.setWaybillNo(wayBillNo);
                waybillHeader.setCustomerNo(bookingRequesheader.getBookingCustcode());
                waybillHeader.setCustomerName(bookingRequesheader.getBookingCustname());
                waybillHeader.setPlatformCode(bookingRequesheader.getResveredField02());
                waybillHeader.setPlatformName(bookingRequesheader.getCityPlatform());
                waybillHeader.setTrainType(bookingRequesheader.getTrainType());
                waybillHeader.setIdentification(bookingRequesheader.getIdentification());
                waybillHeader.setIsCustomTransit(bookingRequesheader.getTransit());
                waybillHeader.setShiftNo(bookingRequesheader.getShiftNo());
                waybillHeader.setShippingTime(bookingRequesheader.getDeliveryTime());
                waybillHeader.setShippingLine(bookingRequesheader.getShippingLine());
                //发运线路编码
                waybillHeader.setResveredField05(bookingRequesheader.getShippingLineCode());
                //境外代理
                waybillHeader.setResveredField06(bookingRequesheader.getResveredField06());
                waybillHeader.setTrainName(bookingRequesheader.getTrainName());
                //市平台名称
                waybillHeader.setResveredField02(bookingRequesheader.getResveredField02());
                waybillHeader.setAuditFlag("DCSH");
                waybillHeader.setTotalCases(bookingRequesheader.getTotalCases());
                waybillHeader.setBillStatus("1");
                waybillHeader.setDeleteFlag("N");
                waybillHeader.setAddWho(bookingRequesheader.getAddWho());
                waybillHeader.setAddWhoName(bookingRequesheader.getAddWhoName());
                waybillHeader.setAddTime(new Date());
                waybillHeader.setTrip(bookingRequesheader.getTrip());
                waybillHeader.setIsTransit(bookingRequesheader.getTransit());
                //审核意见
                waybillHeader.setResveredField01(bookingRequesheader.getResveredField03());
                //20210811新增逻辑 是否使用备用仓标识（0不使用1使用）
                waybillHeader.setResveredField03("0");
                //20210811新增逻辑 占用舱数
                waybillHeader.setResveredField04(String.valueOf(bookingRequesheader.getSpaceNums()));
                //上合代申请0否1是
                waybillHeader.setShAct("0");
                waybillHeaderMapper.insertWaybillHeader(waybillHeader);

                //运单详情
                List<WaybillParticipants> waybillParticipants = new ArrayList<>();
                List<WaybillGoodsInfo> waybillGoodsInfos = new ArrayList<>();
                for (FdShippingAccoundetailSub fdShippingAccoundetailSub : list2) {
                    //箱信息
                    WaybillContainerInfo waybillContainerInfo = new WaybillContainerInfo();
                    waybillContainerInfo.setRowId(UUID.randomUUID().toString());
                    waybillContainerInfo.setOrderNo(orderNo);
                    waybillContainerInfo.setWaybillNo(wayBillNo);
                    waybillContainerInfo.setIdentification(fdShippingAccoundetailSub.getIsRansit());

                    StationManagement start = new StationManagement();
                    start.setStationName(fdShippingAccoundetailSub.getDestinationName());
                    start.setDeleteFlag("0");
                    List<StationManagement> stationManagements = stationManagementMapper.selectStationManagementList(start);
                    if (CollUtil.isNotEmpty(stationManagements)) {
                        waybillContainerInfo.setStationCompilation(stationManagements.get(0).getStationCode());
                    }
                    waybillContainerInfo.setDestinationName(fdShippingAccoundetailSub.getDestinationName());
                    waybillContainerInfo.setStartStationName(fdShippingAccoundetailSub.getDestinationName());

                    StationManagement end = new StationManagement();
                    end.setStationName(fdShippingAccoundetailSub.getDestination());
                    end.setDeleteFlag("0");
                    List<StationManagement> stationManagements2 = stationManagementMapper.selectStationManagementList(end);
                    if (CollUtil.isNotEmpty(stationManagements2)) {
                        waybillContainerInfo.setEndCompilation(stationManagements2.get(0).getStationCode());
                    }
                    waybillContainerInfo.setDestination(fdShippingAccoundetailSub.getDestination());
                    waybillContainerInfo.setEndStationName(fdShippingAccoundetailSub.getDestination());
                    waybillContainerInfo.setGoodsValue(Float.valueOf(String.valueOf(fdShippingAccoundetailSub.getValueUsd())));
                    if (CollUtil.isNotEmpty(shifmanagements)) {
                        waybillContainerInfo.setBureau(shifmanagements.get(0).getBureau());
                        waybillContainerInfo.setPortStation(shifmanagements.get(0).getPortStation());
                    }
                    waybillContainerInfo.setContainerNo(fdShippingAccoundetailSub.getContainerNumber());
                    waybillContainerInfo.setContainerOwner(fdShippingAccoundetailSub.getContainerOwner());
                    waybillContainerInfo.setContainerTypeCode(fdShippingAccoundetailSub.getContainerTypeCode());
                    waybillContainerInfo.setContainerTypeName(fdShippingAccoundetailSub.getContainerTypeName());
                    waybillContainerInfo.setContainerType(fdShippingAccoundetailSub.getContainerType());
                    waybillContainerInfo.setDeleteFlag("N");
                    waybillContainerInfo.setAddWho(bookingRequesheader.getAddWho());
                    waybillContainerInfo.setAddWhoName(bookingRequesheader.getAddWhoName());
                    waybillContainerInfo.setAddTime(new Date());
                    waybillContainerInfo.setDomesticFreight(fdShippingAccoundetailSub.getDomesticFreight());
                    waybillContainerInfo.setOverseasFreight(fdShippingAccoundetailSub.getOverseasFreightOc());
                    waybillContainerInfo.setOverseasFreightRmb(fdShippingAccoundetailSub.getOverseasFreightCny());
                    waybillContainerInfo.setGoodsName(fdShippingAccoundetailSub.getGoodsName());
                    waybillContainerInfoMapper.insertWaybillContainerInfo(waybillContainerInfo);

                    //收货人
                    WaybillParticipants waybillParticipantsShou = new WaybillParticipants();
                    waybillParticipantsShou.setRowId(UUID.randomUUID().toString());
                    waybillParticipantsShou.setAppNo(orderNo);
                    waybillParticipantsShou.setParticipantsType("S");
                    waybillParticipantsShou.setWaybillNo(waybillHeader.getWaybillNo());
                    waybillParticipantsShou.setContainerNo(fdShippingAccoundetailSub.getContainerNumber());
                    waybillParticipantsShou.setConsignorName(fdShippingAccoundetailSub.getConsignorName());
                    waybillParticipantsShou.setDeleteFlag("N");
                    waybillParticipantsShou.setAddWho(bookingRequesheader.getAddWho());
                    waybillParticipantsShou.setAddWhoName(bookingRequesheader.getAddWhoName());
                    waybillParticipantsShou.setAddTime(LocalDateTime.now());
                    waybillParticipants.add(waybillParticipantsShou);
                    //发货人
                    WaybillParticipants waybillParticipantsFa = new WaybillParticipants();
                    waybillParticipantsFa.setRowId(UUID.randomUUID().toString());
                    waybillParticipantsFa.setAppNo(orderNo);
                    waybillParticipantsFa.setParticipantsType("F");
                    waybillParticipantsFa.setWaybillNo(waybillHeader.getWaybillNo());
                    waybillParticipantsFa.setContainerNo(fdShippingAccoundetailSub.getContainerNumber());
                    waybillParticipantsFa.setConsignorName(fdShippingAccoundetailSub.getGoodsOwner());
                    waybillParticipantsFa.setDeleteFlag("N");
                    waybillParticipantsFa.setAddWho(bookingRequesheader.getAddWho());
                    waybillParticipantsFa.setAddWhoName(bookingRequesheader.getAddWhoName());
                    waybillParticipantsFa.setAddTime(LocalDateTime.now());
                    waybillParticipants.add(waybillParticipantsFa);

                    //货物信息
                    WaybillGoodsInfo waybillGoodsInfo = new WaybillGoodsInfo();
                    waybillGoodsInfo.setRowId(UUID.randomUUID().toString());
                    waybillGoodsInfo.setWaybillNo(wayBillNo);
                    waybillGoodsInfo.setContainerNo(fdShippingAccoundetailSub.getContainerNumber());
                    waybillGoodsInfo.setGoodsChineseName(fdShippingAccoundetailSub.getGoodsName());
                    waybillGoodsInfo.setGoodsValue(Float.valueOf(String.valueOf(fdShippingAccoundetailSub.getValueUsd())));
                    waybillGoodsInfo.setDeleteFlag("N");
                    waybillGoodsInfo.setAddWho(bookingRequesheader.getAddWho());
                    waybillGoodsInfo.setAddWhoName(bookingRequesheader.getAddWhoName());
                    waybillGoodsInfo.setAddTime(LocalDateTime.now());
                    waybillGoodsInfos.add(waybillGoodsInfo);
                }

                if (CollUtil.isNotEmpty(waybillParticipants)) {
                    waybillParticipantsMapper.insertWaybillParticipants(waybillParticipants);
                }
                if (CollUtil.isNotEmpty(waybillGoodsInfos)) {
                    waybillGoodsInfoMapper.insertWaybillGoodsInfo(waybillGoodsInfos);
                }
            }
        }
    }

    /*
     * 构建子账单主表数据
     * */
    public FdBillSub getFdBillSub(List<FdBill> bills, FdShippingAccount fdShippingAccount, BigDecimal val, String platformLevel) {
        String fdbs = sysNoConfigService.genNo("FDBS");

        FdBillSub sub = new FdBillSub();
        sub.setUuid(UUID.randomUUID().toString());
        sub.setBillCode(bills.get(0).getBillCode());
        sub.setOrderNo(bills.get(0).getApplicationNumber());
        sub.setWaybillNo(bills.get(0).getTransportOrderNumber());
        sub.setCostCode(bills.get(0).getStandbyF());
        sub.setAccountCode(fdShippingAccount.getAccountCode());
        sub.setPlatformCode(bills.get(0).getPlatformCode());
        sub.setPlatformName(bills.get(0).getPlatformName());
        sub.setPlatformLevel(platformLevel);
        sub.setProvinceShiftNo(bills.get(0).getProvinceTrainsNumber());
        sub.setShiftNo(bills.get(0).getTrainNumber());
        sub.setShiftName(bills.get(0).getTrainsName());
        sub.setBillAmount(val);
        sub.setBillingState("0");
        sub.setCustomerCode(bills.get(0).getStandbyD());
        sub.setCustomerName(bills.get(0).getStandbyC());
        sub.setBillSubCode(fdbs);
        sub.setStatus("1");
        return sub;
    }

    /*
     * 构建余额数据
     * */
    public void getFdBalanceDetail(FdShippingAccount fdShippingAccount, BigDecimal val, String platformLevel, String billCode, String remarks) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        //业务余额表
        FdBalanceDetail detail = new FdBalanceDetail();

        //余额流水明细表
        FdTradingDetails fdTradingDetails = new FdTradingDetails();

        if ("0".equals(platformLevel)) {
            detail.setPlatformCode(fdShippingAccount.getPlatformCode());
            detail.setCustomerCode(fdShippingAccount.getCustomerNo());
            detail.setCustomerName(fdShippingAccount.getCustomerName());

            fdTradingDetails.setPlatformCode(fdShippingAccount.getPlatformCode());
            fdTradingDetails.setPlatformName(fdShippingAccount.getPlatformName());
            fdTradingDetails.setCustomerCode(fdShippingAccount.getCustomerNo());
            fdTradingDetails.setCustomerName(fdShippingAccount.getCustomerName());
        } else {
            //台账中平台编码和客户编码是反过来的
            detail.setPlatformCode(fdShippingAccount.getCustomerNo());
            detail.setCustomerCode(fdShippingAccount.getPlatformCode());
            detail.setCustomerName(fdShippingAccount.getPlatformName());

            fdTradingDetails.setPlatformCode(fdShippingAccount.getCustomerNo());
            fdTradingDetails.setPlatformName(fdShippingAccount.getCustomerName());
            fdTradingDetails.setCustomerCode(fdShippingAccount.getPlatformCode());
            fdTradingDetails.setCustomerName(fdShippingAccount.getPlatformName());
        }

        detail.setShiftId(fdShippingAccount.getProvinceShiftNo());

        String tsIn = sysNoConfigService.genNo("TS");
        fdTradingDetails.setTradeSerialNumber(tsIn);
        fdTradingDetails.setUuid(UUID.randomUUID().toString());
        fdTradingDetails.setPlatformLevel(platformLevel);
        fdTradingDetails.setIncomeFlag("0");
        fdTradingDetails.setTradingHours(LocalDateTime.now());
        fdTradingDetails.setPaymentType("0");
        fdTradingDetails.setBalanceType("1");
        fdTradingDetails.setIsAdd("0");
        fdTradingDetails.setTransactionAmount(val.negate());
        fdTradingDetails.setTradingStatus("1");
        fdTradingDetails.setFromBillCode(billCode);
        fdTradingDetails.setProvinceShiftNo(fdShippingAccount.getProvinceShiftNo());

        //查询对应班列信息
        Shifmanagement shifmanagement = new Shifmanagement();
        shifmanagement.setShiftId(fdShippingAccount.getShiftNo());
        shifmanagement.setDeleteFlag("N");
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementListByLike(shifmanagement);
        if (CollUtil.isNotEmpty(shifmanagements)) {

            detail.setShiftNo(shifmanagements.get(0).getShiftId());
            detail.setShiftName(shifmanagements.get(0).getShiftName());
            detail.setCentralLineCode(shifmanagements.get(0).getShippingLineCode());
            detail.setCentralLineName(shifmanagements.get(0).getShippingLine());

            fdTradingDetails.setShiftId(shifmanagements.get(0).getShiftId());
            fdTradingDetails.setShiftName(shifmanagements.get(0).getShiftName());
            fdTradingDetails.setCentralLineCode(shifmanagements.get(0).getShippingLineCode());
            fdTradingDetails.setCentralLineName(shifmanagements.get(0).getShippingLine());

            LineManagement lineManagement = new LineManagement();
            lineManagement.setLineCode(shifmanagements.get(0).getShippingLineCode());
            lineManagement.setDeleteFlag("N");
            List<LineManagement> lineManagements = lineManagementMapper.selectLineManagementList(lineManagement);
            if (CollUtil.isNotEmpty(lineManagements)) {
                detail.setShUnitCode(lineManagements.get(0).getAreaCode());
                detail.setShUnitName(lineManagements.get(0).getAreaName());

                fdTradingDetails.setShUnitCode(lineManagements.get(0).getAreaCode());
                fdTradingDetails.setShUnitName(lineManagements.get(0).getAreaName());
            }
        }
        detail.setPaymentType("0");
        detail.setTotalAmount(val.negate());
        detail.setRemainingAmount(val.negate());
        if ("2".equals(platformLevel)) {
            String str = "";
            String trip = "";
            if (StrUtil.isNotEmpty(fdShippingAccount.getTrip())) {
                if ("G".equals(fdShippingAccount.getTrip())) {
                    trip = "去程";
                } else if ("R".equals(fdShippingAccount.getTrip())) {
                    trip = "回程";
                } else if ("P".equals(fdShippingAccount.getTrip())) {
                    trip = "过境";
                }
            }

            str = fdShippingAccount.getPlatformName() + fdShippingAccount.getShiftName() + trip + "，省级班列单号：" + fdShippingAccount.getProvinceShiftNo() + "，与多联确认最终汇率后产生余额" + val.negate() + "元；";
            detail.setRemarks(str);
        } else {
            detail.setRemarks(remarks);
        }

        detail.setBillCode(billCode);
        detail.setPlatformLevel(platformLevel);
        fdBalanceDetailMapper.insertFdBalanceDetail(detail);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        fdTradingDetails.setNoteInformation(remarks);
        fdTradingDetails.setCreateUsercode(userInfo.getUserName());
        fdTradingDetails.setCreateUserrealname(userInfo.getRealName());
        fdTradingDetails.setCreateTime(LocalDateTime.now().format(formatter));
        fdTradingDetailsMapper.insertFdTradingDetails(fdTradingDetails);
    }

    /*
     *
     * 拼装应收账单分表明细数据
     * */
    public List<FdBillSubDetail> ysSub(FdShippingAccoundetailSub sub, FdBill fdBill, String status) {
        List<FdBillSubDetail> list = new ArrayList<>();

        FdBillSubDetail jn = new FdBillSubDetail();
        jn.setUuid(UUID.randomUUID().toString());
        jn.setOrderNo(sub.getApplicationNumber());
        jn.setWaybillNo(sub.getTransportOrderNumber());
        if (fdBill != null && StrUtil.isNotEmpty(fdBill.getStandbyF())) {
            jn.setCostCode(fdBill.getStandbyF());
        }
        jn.setContainerNo(sub.getContainerNumber());
        jn.setContainerOwner(sub.getContainerOwner());
        jn.setContainerTypeCode(sub.getContainerTypeCode());
        jn.setContainerTypeName(sub.getContainerTypeName());
        jn.setContainerType(sub.getContainerType());
        /*jn.setStartCompilation();
        jn.setStartCompilationName();
        jn.setEndCompilation();
        jn.setEndCompilationName();
        jn.setDestinationCountry();
        jn.setDestinationCountryName();
        jn.setConsigneeName();
        jn.setSpaceNums();*/
        jn.setCodeBbCategoriesCode("f_fee_type");
        jn.setCodeBbCategoriesName("发运运费");
        jn.setCodeSsCategoriesCode("f_domestic_fee");
        jn.setCodeSsCategoriesName("境内运费");
        jn.setCurrency("人民币");
        jn.setExchangeRate(BigDecimal.valueOf(1));
        jn.setLocalCurrencyAmount(sub.getDomesticFreight());
        jn.setOriginalCurrencyAmount(sub.getDomesticFreight());
        jn.setStatus(status);
        jn.setRemarks(sub.getRemarks());
        jn.setGoodsName(sub.getGoodsName());
        jn.setIdentification(sub.getIsRansit());
        jn.setOrgUnit(sub.getOrgUnit());
        list.add(jn);

        FdBillSubDetail jw = new FdBillSubDetail();
        jw.setUuid(UUID.randomUUID().toString());
        jw.setOrderNo(sub.getApplicationNumber());
        jw.setWaybillNo(sub.getTransportOrderNumber());
        if (fdBill != null && StrUtil.isNotEmpty(fdBill.getStandbyF())) {
            jw.setCostCode(fdBill.getStandbyF());
        }
        jw.setContainerNo(sub.getContainerNumber());
        jw.setContainerOwner(sub.getContainerOwner());
        jw.setContainerTypeCode(sub.getContainerTypeCode());
        jw.setContainerTypeName(sub.getContainerTypeName());
        jw.setContainerType(sub.getContainerType());
        /*jw.setStartCompilation();
        jw.setStartCompilationName();
        jw.setEndCompilation();
        jw.setEndCompilationName();
        jw.setDestinationCountry();
        jw.setDestinationCountryName();
        jw.setConsigneeName();
        jw.setSpaceNums();*/
        jw.setCodeBbCategoriesCode("f_fee_type");
        jw.setCodeBbCategoriesName("发运运费");
        jw.setCodeSsCategoriesCode("f_overseas_fee");
        jw.setCodeSsCategoriesName("境外费用");
        jw.setCurrency(sub.getMonetaryType());
        jw.setExchangeRate(sub.getExchangeRate());
        jw.setLocalCurrencyAmount(sub.getOverseasFreightCny());
        jw.setOriginalCurrencyAmount(sub.getOverseasFreightOc());
        jw.setStatus(status);
        jw.setRemarks(sub.getRemarks());
        jw.setGoodsName(sub.getGoodsName());
        jw.setIdentification(sub.getIsRansit());
        jw.setOrgUnit(sub.getOrgUnit());
        list.add(jw);

        return list;
    }

    /*
     *
     * 拼装应付账单分表明细数据
     * */
    public List<FdBillSubDetail> yfSub(FdShippingAccoundetailSub sub, FdBill fdBill, String status) {
        List<FdBillSubDetail> list = new ArrayList<>();

        FdBillSubDetail jn = new FdBillSubDetail();
        jn.setUuid(UUID.randomUUID().toString());
        jn.setOrderNo(sub.getApplicationNumber());
        jn.setWaybillNo(sub.getTransportOrderNumber());
        if (fdBill != null && StrUtil.isNotEmpty(fdBill.getStandbyF())) {
            jn.setCostCode(fdBill.getStandbyF());
        }
        jn.setContainerNo(sub.getContainerNumber());
        jn.setContainerOwner(sub.getContainerOwner());
        jn.setContainerTypeCode(sub.getContainerTypeCode());
        jn.setContainerTypeName(sub.getContainerTypeName());
        jn.setContainerType(sub.getContainerType());
        /*jn.setStartCompilation();
        jn.setStartCompilationName();
        jn.setEndCompilation();
        jn.setEndCompilationName();
        jn.setDestinationCountry();
        jn.setDestinationCountryName();
        jn.setConsigneeName();
        jn.setSpaceNums();*/
        jn.setCodeBbCategoriesCode("f_fee_type");
        jn.setCodeBbCategoriesName("发运运费");
        jn.setCodeSsCategoriesCode("f_domestic_fee");
        jn.setCodeSsCategoriesName("境内运费");
        jn.setCurrency("人民币");
        jn.setExchangeRate(BigDecimal.valueOf(1));
        jn.setLocalCurrencyAmount(sub.getRrDomesticFreight());
        jn.setOriginalCurrencyAmount(sub.getRrDomesticFreight());
        jn.setStatus(status);
        jn.setRemarks(sub.getRemarks());
        jn.setGoodsName(sub.getGoodsName());
        jn.setIdentification(sub.getIsRansit());
        jn.setOrgUnit(sub.getOrgUnit());
        list.add(jn);

        FdBillSubDetail jw = new FdBillSubDetail();
        jw.setUuid(UUID.randomUUID().toString());
        jw.setOrderNo(sub.getApplicationNumber());
        jw.setWaybillNo(sub.getTransportOrderNumber());
        if (fdBill != null && StrUtil.isNotEmpty(fdBill.getStandbyF())) {
            jw.setCostCode(fdBill.getStandbyF());
        }
        jw.setContainerNo(sub.getContainerNumber());
        jw.setContainerOwner(sub.getContainerOwner());
        jw.setContainerTypeCode(sub.getContainerTypeCode());
        jw.setContainerTypeName(sub.getContainerTypeName());
        jw.setContainerType(sub.getContainerType());
        /*jw.setStartCompilation();
        jw.setStartCompilationName();
        jw.setEndCompilation();
        jw.setEndCompilationName();
        jw.setDestinationCountry();
        jw.setDestinationCountryName();
        jw.setConsigneeName();
        jw.setSpaceNums();*/
        jw.setCodeBbCategoriesCode("f_fee_type");
        jw.setCodeBbCategoriesName("发运运费");
        jw.setCodeSsCategoriesCode("f_overseas_fee");
        jw.setCodeSsCategoriesName("境外费用");
        jw.setCurrency(sub.getMonetaryType());
        jw.setExchangeRate(sub.getExchangeRate());
        jw.setLocalCurrencyAmount(sub.getRrOverseasFreightCny());
        jw.setOriginalCurrencyAmount(sub.getRrOverseasFreightOc());
        jw.setStatus(status);
        jw.setRemarks(sub.getRemarks());
        jw.setGoodsName(sub.getGoodsName());
        jw.setIdentification(sub.getIsRansit());
        jw.setOrgUnit(sub.getOrgUnit());
        list.add(jw);

        return list;
    }

    @Override
    public R updateShippingTime(FdShippingAccount fdShippingAccount) {
        int flag = 0;
        try {
            /*SecruityUser userInfo = SecurityUtils.getUserInfo();
            fdShippingAccount.setUpdateTime(LocalDateTime.now());
            fdShippingAccount.setUpdateWho(userInfo.getUserName());
            fdShippingAccount.setUpdateWhoName(userInfo.getRealName());
            //省平台更新省平台发运时间
            if (StrUtil.isNotEmpty(fdShippingAccount.getResveredField08Str())) {
                fdShippingAccount.setResveredField08(LocalDateTime.parse(fdShippingAccount.getResveredField08Str() + "T00:00:00"));
            }
            flag = fdShippingAccountMapper.updateShippingTime(fdShippingAccount);*/
            FdShippingAccount account = fdShippingAccountMapper.selectFdShippingAccountById(fdShippingAccount.getRowId());
            if (account != null && StrUtil.isNotBlank(account.getShiftNo())) {
                Shifmanagement shifmanagement = new Shifmanagement();
                shifmanagement.setShiftId(account.getShiftNo());
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                // 将字符串转换为 Date 类型
                Date date = dateFormat.parse(fdShippingAccount.getResveredField08Str());
                shifmanagement.setPlanShipTime(date);
                shifmanagementService.updateShippingTime(shifmanagement);
            }

        } catch (Exception e) {
            System.out.println(e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error("操作失败");
        }
        return R.success("操作成功");
    }

    @Override
    public List<FdShippingAccount> selectFdShippingAccountByCode(FdShippingAccount fdShippingAccount) {
        return fdShippingAccountMapper.selectFdShippingAccountByCode(fdShippingAccount);
    }

    @Override
    public int updateStatus(FdShippingAccount fdShippingAccount) {
        return fdShippingAccountMapper.updateStatus(fdShippingAccount);
    }

    @Override
    public R updateProvinceShiftNoNew(FdShippingAccount fdShippingAccount) {
        //新省级班列号班次信息
        Shifmanagement sel2 = new Shifmanagement();
        sel2.setDeleteFlag("N");
        sel2.setProvinceShiftNo(fdShippingAccount.getProvinceShiftNo());
        sel2.setParentIdNull("1");
        List<Shifmanagement> account = shifmanagementMapper.selectShifmanagementList(sel2);

        // 原省级班列号信息
        Shifmanagement sel = new Shifmanagement();
        sel.setDeleteFlag("N");
        sel.setProvinceShiftNo(fdShippingAccount.getProvinceShiftNoX());
        sel.setParentIdNull("1");
        List<Shifmanagement> province = shifmanagementMapper.selectShifmanagementList(sel);
        if (CollUtil.isEmpty(province)) {
            return R.error("当前台账省级班列号不存在，不允许修改！");
        }

        Shifmanagement shift = null;
        for (Shifmanagement p : province) {
            if (fdShippingAccount.getShiftNo().equals(p.getShiftId())) {
                shift = p;
                break;
            }
        }

        for (Shifmanagement p : province) {
            // 初始设置flag为true，表示默认是匹配的
            boolean flag = true;

            // 校验当前province的shift与shift对象的匹配性
            if (!isMatchingShift(shift, p)) {
                continue;
            }

            // 如果account非空，检查每个account对象是否与当前province匹配
            if (CollUtil.isNotEmpty(account)) {
                for (Shifmanagement a : account) {
                    if (!isMatchingShift(p, a)) {
                        flag = false;
                        break;
                    }
                }
            }

            // 如果flag仍为true，说明匹配成功，执行更新操作
            if (flag) {
                shifmanagementMapper.updateProvinceShiftNoNew(p.getProvinceShiftNo(), fdShippingAccount.getProvinceShiftNo(), p.getShiftId());
            }
        }
        return R.success("修改成功");
    }

    /*
     * 校验班次信息是否相同
     * */
    // 优化后的isMatchingShift方法
    private boolean isMatchingShift(Shifmanagement p, Shifmanagement a) {
        if (StrUtil.isBlank(p.getShiftName()) || StrUtil.isBlank(a.getShiftName()) || !p.getShiftName().equals(a.getShiftName()) || p.getPlanShipTime() == null || a.getPlanShipTime() == null || p.getPlanShipTime().compareTo(a.getPlanShipTime()) != 0 || StrUtil.isBlank(p.getShippingLineCode()) || StrUtil.isBlank(a.getShippingLineCode()) || !p.getShippingLineCode().equals(a.getShippingLineCode()) || StrUtil.isBlank(p.getPortStationCode()) || StrUtil.isBlank(a.getPortStationCode()) || !p.getPortStationCode().equals(a.getPortStationCode()) || StrUtil.isBlank(p.getDestinationCode()) || StrUtil.isBlank(a.getDestinationCode()) || !p.getDestinationCode().equals(a.getDestinationCode()) || StrUtil.isBlank(p.getTrip()) || StrUtil.isBlank(a.getTrip()) || !p.getTrip().equals(a.getTrip())) {
            // 不匹配，直接返回false
            return false;
        }

        // 处理trip为"R"的特殊情况
        if ("R".equals(a.getTrip())) {
            return !(StrUtil.isBlank(p.getDestinationCode2()) || StrUtil.isBlank(a.getDestinationCode2()) || !p.getDestinationCode2().equals(a.getDestinationCode2()));
        }
        // 其他情况都匹配
        return true;
    }

    @Override
    public R updateProvinceShiftNoCheck(FdShippingAccount fdShippingAccount) {
        if (StrUtil.isBlank(fdShippingAccount.getProvinceShiftNoX())) {
            return R.error("台账审核通过后系统会自动生成省级班列号，当前台账还未审核通过，未曾生成省级班列号，不允许修改！");
        }
        //当前台账班次信息
        Shifmanagement sel2 = new Shifmanagement();
        sel2.setDeleteFlag("N");
        sel2.setShiftId(fdShippingAccount.getShiftNo());
        sel2.setParentIdNull("1");
        List<Shifmanagement> account = shifmanagementMapper.selectShifmanagementList(sel2);
        if (CollUtil.isEmpty(account)) {
            return R.error("当前台账班次信息不存在，不允许修改！");
        }

        // 省级班列号未找到，允许修改
        Shifmanagement sel = new Shifmanagement();
        sel.setDeleteFlag("N");
        sel.setProvinceShiftNo(fdShippingAccount.getProvinceShiftNo());
        sel.setParentIdNull("1");
        List<Shifmanagement> province = shifmanagementMapper.selectShifmanagementList(sel);
        if (CollUtil.isEmpty(province)) {
            return R.success();
        }
        for (Shifmanagement p : province) {
            if (!isMatchingShift(account.get(0), p)) {
                return R.error("该省级班列号已存在，且与当前台账班次信息不匹配，不允许修改！");
            }
        }
        return R.success();
    }

    @Override
    public R updateProvinceShiftNo(FdShippingAccount fdShippingAccount) {
        R r = new R();
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        //查询发运台账主表省际班次好是否已存在
        FdShippingAccount fdShipping = new FdShippingAccount();
        fdShipping.setDeleteFlag("N");
        fdShipping.setProvinceShiftNo(fdShippingAccount.getProvinceShiftNo());
        List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountList(fdShipping);
        if (fdShippingAccounts.size() > 0) {
            r.setB(Boolean.FALSE);
            r.setCode(500);
            r.setMsg("省级班列号在发运台账表已存在");
            return r;
        }
        //查询账单汇总表省际班次号是否已存在
        FdBill fdBill = new FdBill();
        fdBill.setProvinceTrainsNumber(fdShippingAccount.getProvinceShiftNo());
        fdBill.setDelFlag("N");
        List<FdBill> fdBills = fdBillService.selectFdBillList(fdBill);
        if (fdBills.size() > 0) {
            r.setB(Boolean.FALSE);
            r.setCode(500);
            r.setMsg("省级班列号在账单汇总表已存在");
            return r;
        }
        //查询运单费用汇总表省际班次号是否已存在
        FdCost fdCost = new FdCost();
        fdCost.setDelFlag("N");
        fdCost.setProvinceTrainsNumber(fdShippingAccount.getProvinceShiftNo());
        List<FdCost> fdCosts = fdCostService.selectFdCostList(fdCost);
        if (fdCosts.size() > 0) {
            r.setB(Boolean.FALSE);
            r.setCode(500);
            r.setMsg("省级班列号在运单费用汇总表已存在");
            return r;
        }
        //查询余额明细表省际班次号是否已存在
        FdBalanceDetail fdBalanceDetail = new FdBalanceDetail();
        fdBalanceDetail.setShiftId(fdShippingAccount.getProvinceShiftNo());
        fdBalanceDetail.setDeleteFlag("N");
        List<FdBalanceDetail> fdBalanceDetails = fdBalanceDetailService.selectFdBalanceDetailListByShiftId(fdBalanceDetail);
        if (fdBalanceDetails.size() > 0) {
            r.setB(Boolean.FALSE);
            r.setCode(500);
            r.setMsg("省际班次号在余额明细表已存在");
            return r;
        }
        //修改省际班次号
        fdShippingAccount.setUpdateWho(userInfo.getUserName());
        fdShippingAccount.setUpdateWhoName(userInfo.getRealName());
        fdShippingAccountMapper.updateFdShippingAccountByProvinceShiftNo(fdShippingAccount);

        fdBill.setProvinceTrainsNumberJ(fdShippingAccount.getProvinceShiftNoX());
        fdBill.setUpdateUsercode(userInfo.getUserName());
        fdBill.setUpdateUserrealname(userInfo.getRealName());
        fdBillService.updateFdBillByProvinceTrainsNumber(fdBill);

        fdCost.setUpdateUsercode(userInfo.getUserName());
        fdCost.setUpdateUserrealname(userInfo.getRealName());
        fdCost.setProvinceTrainsNumberJ(fdShippingAccount.getProvinceShiftNoX());
        fdCostService.updateFdCostByProvinceTrainsNumber(fdCost);

        fdBalanceDetail.setShiftIdJ(fdShippingAccount.getProvinceShiftNoX());
        fdBalanceDetailService.updatefdBalanceDetailByShiftId(fdBalanceDetail);
        r.setB(Boolean.TRUE);
        r.setMsg("修改成功");
        r.setCode(200);
        return r;
    }

    @SneakyThrows
    @Override
    public R selectFiShippingTime(Map<String, Object> param) throws ParseException {

        FdShippingAccount fdShippingAccount = BeanUtil.mapToBean(param, FdShippingAccount.class, false);
        //创建返回对象
        R r = new R();
        List<Map<String, Object>> mapList = new ArrayList<>();
        if (fdShippingAccount != null) {
            if (StringUtils.isBlank(fdShippingAccount.getStartTime()) || StringUtils.isBlank(fdShippingAccount.getEndTime())) {
                r.setCode(500);
                r.setB(Boolean.FALSE);
                r.setMsg("请传入开始时间和结束时间");
                return r;
            }
        } else {
            r.setCode(500);
            r.setB(Boolean.FALSE);
            r.setMsg("请传入开始时间和结束时间");
            return r;
        }
        //约定时间
        String ydDate = "2022-03-01";
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date startTime = df.parse(fdShippingAccount.getStartTime());
        Date endTime = df.parse(fdShippingAccount.getEndTime());
        Date parse = df.parse(ydDate);
        if (isEffectiveDate(parse, startTime, endTime)) {

            FiHisBookingInformation fiHisBookingInfor = new FiHisBookingInformation();
            //获取约定时间的前一天
            String beforeDay = getBeforeDay(ydDate);
            fiHisBookingInfor.setStartTime(fdShippingAccount.getStartTime());
            fiHisBookingInfor.setEndTime(beforeDay);
            fiHisBookingInfor.setPlatformCode(fdShippingAccount.getCustomerNo());
            fiHisBookingInfor.setCustomerNo(fdShippingAccount.getCustomerNo());
            fiHisBookingInfor.setStartTimeTwo(ydDate);
            fiHisBookingInfor.setEndTimeTwo(fdShippingAccount.getEndTime());
            fiHisBookingInfor.setIsGroup("1");
            List<FiHisBookingInformation> fiHisBookingInformations = fiHisBookingInformationService.selectfiHisBookingInformationDingcangList(fiHisBookingInfor);
            for (FiHisBookingInformation information : fiHisBookingInformations) {
                Map<String, Object> map = new HashMap<>();
                LocalDateTime date = information.getDate();
//                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String format = formatter.format(date);
                String[] split = format.split("-");
                map.put("year", split[0]);
                map.put("month", split[1]);
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(split[0]).append("-").append(split[1]).append("-");
                FiHisBookingInformation hisBookingInformation = new FiHisBookingInformation();
                hisBookingInformation.setStartTime(stringBuilder.append("00").toString());
                hisBookingInformation.setEndTime(getLastDayOfMonth(Integer.valueOf(split[0]), Integer.valueOf(split[1])));
                hisBookingInformation.setPlatformCode(fdShippingAccount.getCustomerNo());
                List<FiHisBookingInformation> fiHisBookingInformationsSelect = fiHisBookingInformationService.selectfiHisBookingInformationDingcangList(hisBookingInformation);
                BigDecimal shouldCost = BigDecimal.valueOf(0);
                BigDecimal actualCost = BigDecimal.valueOf(0);
                for (FiHisBookingInformation bookingInformation : fiHisBookingInformationsSelect) {
                    shouldCost = shouldCost.add(bookingInformation.getShouldCost());
                    actualCost = actualCost.add(bookingInformation.getActualCost());
                }
                map.put("totalCost", shouldCost.setScale(2, BigDecimal.ROUND_HALF_UP));
                map.put("havePaid", actualCost.setScale(2, BigDecimal.ROUND_HALF_UP));
                if (shouldCost.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal bigDecimal = actualCost.divide(shouldCost).multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
                    map.put("payment", bigDecimal);
                } else {
                    map.put("payment", BigDecimal.valueOf(0.00));
                }

                mapList.add(map);
            }
            FdShippingAccount fiHisBookingInformation = new FdShippingAccount();
            fiHisBookingInformation.setIsGroup("1");
            fiHisBookingInformation.setPlatformCode(fdShippingAccount.getCustomerNo());
            fiHisBookingInformation.setStartTime(ydDate);
            fiHisBookingInformation.setEndTime(fdShippingAccount.getEndTime());
            List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectGroupByShippingTime(fiHisBookingInformation);
            if (CollUtil.isNotEmpty(fdShippingAccounts)) {
                for (FdShippingAccount fdShipping : fdShippingAccounts) {
                    Map<String, Object> map = new HashMap<>();
                    LocalDateTime shippingTime = fdShipping.getShippingTime();
                    DateTimeFormatter dformate = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    String shippingTimeStr = dformate.format(shippingTime);
                    String[] split = shippingTimeStr.split("-");
                    StringBuilder stringBuilder = new StringBuilder();
                    StringBuilder append = stringBuilder.append(split[0]).append("-").append(split[1]).append("-").append("01");
                    FdShippingAccount fdShip = new FdShippingAccount();
                    fdShip.setStartTime(append.toString());
                    fdShip.setPlatformCode(fdShippingAccount.getPlatformCode());
                    fdShip.setEndTime(getLastDayOfMonth(Integer.valueOf(split[0]), Integer.valueOf((split[1]))));
                    map.put("year", split[0]);
                    map.put("month", split[1]);
                    //计算订舱总费用
                    List<FdShippingAccount> selectShipping = fdShippingAccountMapper.selectGroupByShippingTime(fdShip);
                    if (selectShipping.size() > 0) {
                        BigDecimal bigDecimal = BigDecimal.valueOf(0);
                        StringBuilder stringBuil = new StringBuilder();
                        for (FdShippingAccount shippingAccount : selectShipping) {
                            bigDecimal = bigDecimal.add(shippingAccount.getOverseasFreightCny()).add(shippingAccount.getDomesticFreight());
                            stringBuil.append(shippingAccount.getProvinceShiftNo()).append(",");
                        }
                        map.put("totalCost", bigDecimal);
                        if (bigDecimal.compareTo(BigDecimal.valueOf(0)) > 0) {
                            //查询已支付费用
                            FdBill fdBill = new FdBill();
                            fdBill.setProvinceTrainsNumber(stringBuil.toString());
                            List<FdBill> fdBills = fdBillService.selectFdBillProvinceTrainNumber(fdBill);
                            if (fdBills.size() > 0) {
                                BigDecimal fdBig = BigDecimal.valueOf(0);
                                for (FdBill fd : fdBills) {
                                    fdBig = fdBig.add(fd.getBillAmount());
                                }
                                map.put("havePaid", fdBig);
                                map.put("payment", fdBig.divide(bigDecimal, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)));
                            } else {
                                map.put("payment", "0");
                                map.put("havePaid", "0");
                            }
                        } else {
                            map.put("payment", "0");
                            map.put("havePaid", "0");
                        }
                    } else {
                        map.put("totalCost", "0");
                        map.put("payment", "0");
                        map.put("havePaid", "0");
                    }
                    mapList.add(map);
                }
            }
        } else if (parse.after(endTime) || parse.equals(endTime)) {
            FiHisBookingInformation fiHisBookingInformation = new FiHisBookingInformation();
            fiHisBookingInformation.setStartTime(fdShippingAccount.getStartTime());
            fiHisBookingInformation.setEndTime(fdShippingAccount.getEndTime());
            fiHisBookingInformation.setPlatformCode(fdShippingAccount.getCustomerNo());
            fiHisBookingInformation.setIsGroup("1");
            List<FiHisBookingInformation> fiHisBookingInformations = fiHisBookingInformationService.selectfiHisBookingInformationDingcangList(fiHisBookingInformation);
            for (FiHisBookingInformation information : fiHisBookingInformations) {
                Map<String, Object> map = new HashMap<>();
                LocalDateTime date = information.getDate();
//                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String format = formatter.format(date);
                String[] split = format.split("-");
                map.put("year", split[0]);
                map.put("month", split[1]);
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(split[0]).append("-").append(split[1]).append("-");
                FiHisBookingInformation hisBookingInformation = new FiHisBookingInformation();
                hisBookingInformation.setStartTime(stringBuilder.append("00").toString());
                hisBookingInformation.setEndTime(getLastDayOfMonth(Integer.valueOf(split[0]), Integer.valueOf(split[1])));
                hisBookingInformation.setPlatformCode(fdShippingAccount.getCustomerNo());
                List<FiHisBookingInformation> fiHisBookingInformationsSelect = fiHisBookingInformationService.selectfiHisBookingInformationDingcangList(hisBookingInformation);
                BigDecimal shouldCost = BigDecimal.valueOf(0);
                BigDecimal actualCost = BigDecimal.valueOf(0);
                for (FiHisBookingInformation bookingInformation : fiHisBookingInformationsSelect) {
                    shouldCost = shouldCost.add(bookingInformation.getShouldCost());
                    actualCost = actualCost.add(bookingInformation.getActualCost());
                }
                map.put("totalCost", shouldCost.setScale(2, BigDecimal.ROUND_HALF_UP));
                map.put("havePaid", actualCost.setScale(2, BigDecimal.ROUND_HALF_UP));
                BigDecimal bigDecimal = actualCost.divide(shouldCost).multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
                map.put("payment", bigDecimal);
                mapList.add(map);
            }
        } else if (parse.before(startTime) || parse.equals(startTime)) {
            fdShippingAccount.setIsGroup("1");
            fdShippingAccount.setPlatformCode(SecurityUtils.getUserInfo().getUserName());
            fdShippingAccount.setCustomerNo(null);
            List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectGroupByShippingTime(fdShippingAccount);
            if (fdShippingAccounts.size() > 0) {
                for (FdShippingAccount fdShipping : fdShippingAccounts) {
                    Map<String, Object> map = new HashMap<>();
                    LocalDateTime shippingTime = fdShipping.getShippingTime();
                    DateTimeFormatter dformate = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    String shippingTimeStr = dformate.format(shippingTime);
                    String[] split = shippingTimeStr.split("-");
                    StringBuilder stringBuilder = new StringBuilder();
                    StringBuilder append = stringBuilder.append(split[0]).append("-").append(split[1]).append("-").append("01");
                    FdShippingAccount fdShip = new FdShippingAccount();
                    fdShip.setStartTime(append.toString());
                    fdShip.setPlatformCode(fdShippingAccount.getPlatformCode());
                    fdShip.setEndTime(getLastDayOfMonth(Integer.valueOf(split[0]), Integer.valueOf((split[1]))));
                    map.put("year", split[0]);
                    map.put("month", split[1]);
                    //计算订舱总费用
                    List<FdShippingAccount> selectShipping = fdShippingAccountMapper.selectGroupByShippingTime(fdShip);
                    if (selectShipping.size() > 0) {
                        BigDecimal bigDecimal = BigDecimal.valueOf(0);
                        StringBuilder stringBuil = new StringBuilder();
                        for (FdShippingAccount shippingAccount : selectShipping) {
                            bigDecimal = bigDecimal.add(shippingAccount.getOverseasFreightCny()).add(shippingAccount.getDomesticFreight());
                            stringBuil.append(shippingAccount.getProvinceShiftNo()).append(",");
                        }
                        map.put("totalCost", bigDecimal);
                        if (bigDecimal.compareTo(BigDecimal.valueOf(0)) > 0) {
                            //查询已支付费用
                            FdBill fdBill = new FdBill();
                            fdBill.setProvinceTrainsNumber(stringBuil.toString());
                            List<FdBill> fdBills = fdBillService.selectFdBillProvinceTrainNumber(fdBill);
                            if (fdBills.size() > 0) {
                                BigDecimal fdBig = BigDecimal.valueOf(0);
                                for (FdBill fd : fdBills) {
                                    fdBig = fdBig.add(fd.getBillAmount());
                                }
                                map.put("havePaid", fdBig);
                                map.put("payment", fdBig.divide(bigDecimal, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)));
                            } else {
                                map.put("payment", "0");
                                map.put("havePaid", "0");
                            }
                        } else {
                            map.put("payment", "0");
                            map.put("havePaid", "0");
                        }
                    } else {
                        map.put("totalCost", "0");
                        map.put("payment", "0");
                        map.put("havePaid", "0");
                    }
                    mapList.add(map);
                }
            }
        }
        r.setCode(200);
        r.setData(mapList);
        r.setMsg("查询成功");
        r.setB(Boolean.TRUE);
        return r;
    }

    @Override
    public R selectShiStatistics(Map<String, Object> param) throws ParseException {
        //创建返回对象
        R r = new R();
        FdShippingAccount fdShippingAccount = BeanUtil.mapToBean(param, FdShippingAccount.class, false);
        FiHisBookingInformation fiHisBooking = new FiHisBookingInformation();
        if (StringUtils.isBlank(fdShippingAccount.getStartTime())) {
            r.setB(false);
            r.setCode(500);
            r.setMsg("开始时间不能为空");
            return r;
        }
        if (StringUtils.isBlank(fdShippingAccount.getEndTime())) {
            r.setB(false);
            r.setCode(500);
            r.setMsg("结束时间不能为空");
            return r;
        }
        //约定时间
        String ydDate = "2022-03-01";
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date startTime = df.parse(fdShippingAccount.getStartTime());
        Date endTime = df.parse(fdShippingAccount.getEndTime());
        Date parse = df.parse(ydDate);
        if (isEffectiveDate(parse, startTime, endTime)) {
            FdShippingAccount shippingAccount = new FdShippingAccount();
            shippingAccount.setStartTime(fdShippingAccount.getStartTime());
            shippingAccount.setEndTime(getBeforeDay(ydDate));
            shippingAccount.setPlatformCode(fdShippingAccount.getCustomerNo());
            shippingAccount.setStartTimeTwo(ydDate);
            shippingAccount.setEndTimeTwo(fdShippingAccount.getEndTime());
            //查询发运路线总数
            Integer byCount = fdShippingAccountMapper.selectFiHisAndAccountByCount(shippingAccount);
            if (byCount > 0) {
                StringBuilder fiHisAndAccountBuilder = new StringBuilder();
                //查询发运线路
                List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFiHisAndAccountByShippingLing(shippingAccount);
                for (FdShippingAccount fdShipping : fdShippingAccounts) {
                    BigDecimal bigDecimal = BigDecimal.valueOf(fdShipping.getRowId()).divide(BigDecimal.valueOf(byCount), 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                    fiHisAndAccountBuilder.append(fdShipping.getShippingLine()).append(":").append(bigDecimal).append("%").append(",");
                }
                fiHisBooking.setMainExportRoutes(fiHisAndAccountBuilder.substring(0, fiHisAndAccountBuilder.toString().length() - 1));
            }

            //查询目的国
            List<FdShippingAccount> accounts = fdShippingAccountMapper.selectFiHisAndAccountByDestinationCountry(shippingAccount);
            if (accounts.size() > 0) {
                BigDecimal accountbigDecimal = BigDecimal.valueOf(0);
                for (FdShippingAccount account : accounts) {
                    accountbigDecimal = accountbigDecimal.add(BigDecimal.valueOf(account.getRowId()));
                }
                StringBuilder accountBuilder = new StringBuilder();
                BigDecimal accountMultiply = BigDecimal.valueOf(accounts.get(0).getRowId()).divide(accountbigDecimal, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                accountBuilder.append(accounts.get(0).getDestinationCountry()).append(":").append(accountMultiply).append("%");
                fiHisBooking.setMainDestinationCountries(accountBuilder.toString());
            }

            //查询下站口
            List<FdShippingAccount> destinationCounts = fdShippingAccountMapper.selectFiHisAndAccountByDestination(shippingAccount);
            if (destinationCounts.size() > 0) {
                BigDecimal destinationbigDecimal = BigDecimal.valueOf(0);
                for (FdShippingAccount destCount : destinationCounts) {
                    destinationbigDecimal = destinationbigDecimal.add(BigDecimal.valueOf(destCount.getRowId()));
                }
                StringBuilder destinationBuilder = new StringBuilder();
                BigDecimal destinationmultiply = BigDecimal.valueOf(destinationCounts.get(0).getRowId()).divide(destinationbigDecimal, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                destinationBuilder.append(destinationCounts.get(0).getDestination()).append(":").append(destinationmultiply).append("%");
                fiHisBooking.setMainExits(destinationBuilder.toString());
            }

            //总运输量
            List<FdShippingAccount> containerTypeAccounts = fdShippingAccountMapper.selectFiHisAndAccountByContainerType(shippingAccount);
            if (containerTypeAccounts.size() > 0) {
                StringBuilder containerTypeBuilder = new StringBuilder();
                BigDecimal bigDecimal = BigDecimal.valueOf(0);
                for (FdShippingAccount containerTypeAccount : containerTypeAccounts) {
                    String rowId = String.valueOf(containerTypeAccount.getRowId());
                    if (containerTypeAccount.getContainerType().contains("4")) {
                        rowId = BigDecimal.valueOf(Double.parseDouble(rowId)).multiply(BigDecimal.valueOf(2)).toString();
                    }
                    bigDecimal = bigDecimal.add(BigDecimal.valueOf(Double.parseDouble(rowId)));
                }
                containerTypeBuilder.append(bigDecimal).append("teu").append(" 其中: ");
                for (FdShippingAccount containerTypeAccountTwo : containerTypeAccounts) {
                    containerTypeBuilder.append(containerTypeAccountTwo.getContainerType()).append("尺").append(containerTypeAccountTwo.getRowId()).append("车").append(",");
                }

                fiHisBooking.setTotalTransportVolume(containerTypeBuilder.substring(0, containerTypeBuilder.length() - 1));
            }
            r.setB(Boolean.TRUE);
            r.setData(fiHisBooking);
            r.setCode(200);
            return r;
        } else if (parse.after(endTime) || parse.equals(endTime)) {
            //查历史订舱信息表
            //查询总数据
            FiHisBookingInformation fiHisBookingInformation = new FiHisBookingInformation();
            fiHisBookingInformation.setStartTime(fdShippingAccount.getStartTime());
            fiHisBookingInformation.setEndTime(fdShippingAccount.getEndTime());
            if (StringUtils.isNotBlank(fdShippingAccount.getCustomerNo())) {
                fiHisBookingInformation.setPlatformCode(fdShippingAccount.getCustomerNo());
            } else if (StringUtils.isNotBlank(fdShippingAccount.getPlatformCode())) {
                fiHisBookingInformation.setPlatformCode(fdShippingAccount.getPlatformCode());
            }
            Integer integer = fiHisBookingInformationMapper.selectByCount(fiHisBookingInformation);
            BigDecimal count = BigDecimal.valueOf(integer);
            if (count.compareTo(BigDecimal.valueOf(0)) > 0) {
                //主要出口线路
                List<FiHisBookingInformation> fiHisBookingInformations = fiHisBookingInformationMapper.selectStatistics(fiHisBookingInformation);
                if (fiHisBookingInformations.size() > 0) {
                    StringBuilder stringBuilder = new StringBuilder();
                    for (FiHisBookingInformation information : fiHisBookingInformations) {
                        BigDecimal multiply = BigDecimal.valueOf(Double.parseDouble(information.getRowId())).divide(count, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                        stringBuilder.append(information.getLine()).append(":").append(multiply).append("%").append(",");

                    }
                    fiHisBooking.setMainExportRoutes(stringBuilder.substring(0, stringBuilder.toString().length() - 1));
                }
                //主要目的国
                FiHisBookingInformation primaryEndCountry = fiHisBookingInformationMapper.selectByprimaryEndCountryHis(fiHisBookingInformation);
                StringBuilder endCountryStringBuilder = new StringBuilder();
                BigDecimal endCountrymultiply = BigDecimal.valueOf(Double.parseDouble(primaryEndCountry.getRowId())).divide(count, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                endCountryStringBuilder.append(primaryEndCountry.getEndCountry()).append(":").append(endCountrymultiply).append("%");
                fiHisBooking.setMainDestinationCountries(endCountryStringBuilder.toString());
                //主要下站口
                FiHisBookingInformation primaryEndStation = fiHisBookingInformationMapper.selectByPrimaryEndStationByHis(fiHisBookingInformation);
                StringBuilder endStationsb = new StringBuilder();
                BigDecimal endStationmultiply = BigDecimal.valueOf(Double.parseDouble(primaryEndStation.getRowId())).divide(count, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                endStationsb.append(primaryEndStation.getEndStation()).append(":").append(endStationmultiply).append("%");
                fiHisBooking.setMainExits(endStationsb.toString());
                //总运输量
                List<FiHisBookingInformation> primaryboxType = fiHisBookingInformationMapper.selectByPrimaryboxType(fiHisBookingInformation);
                if (primaryboxType.size() > 0) {
                    StringBuilder boxTypeBuilder = new StringBuilder();
                    BigDecimal bigDecimal = BigDecimal.valueOf(0);
                    for (FiHisBookingInformation information : primaryboxType) {
                        String rowId = information.getRowId();
                        if (information.getBoxType().contains("4")) {
                            rowId = BigDecimal.valueOf(Double.parseDouble(rowId)).multiply(BigDecimal.valueOf(2)).toString();
                        }
                        bigDecimal = bigDecimal.add(BigDecimal.valueOf(Double.parseDouble(rowId)));
                    }
                    boxTypeBuilder.append(bigDecimal).append("teu").append(" 其中: ");
                    for (FiHisBookingInformation containerTypeAccountTwo : primaryboxType) {
                        boxTypeBuilder.append(containerTypeAccountTwo.getBoxType()).append("尺").append(containerTypeAccountTwo.getRowId()).append("车").append(",");
                    }
                    fiHisBooking.setTotalTransportVolume(boxTypeBuilder.substring(0, boxTypeBuilder.toString().length() - 1));
                }
            }
            r.setB(Boolean.TRUE);
            r.setData(fiHisBooking);
            r.setCode(200);
            return r;
        } else if (parse.before(startTime) || parse.equals(startTime)) {
            if (StringUtils.isNotBlank(fdShippingAccount.getCustomerNo())) {
                fdShippingAccount.setPlatformCode(fdShippingAccount.getCustomerNo());
            } else if (StringUtils.isNotBlank(fdShippingAccount.getPlatformCode())) {
                fdShippingAccount.setPlatformCode(fdShippingAccount.getPlatformCode());
            }
            //查询线路总数
            Integer shippingCount = fdShippingAccountMapper.selectFdShippingAccountByCount(fdShippingAccount);
            if (shippingCount > 0) {
                StringBuilder shippingBuilder = new StringBuilder();
                List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountByShippingLine(fdShippingAccount);
                for (FdShippingAccount fdShipping : fdShippingAccounts) {
                    //计算线路的百分比
                    BigDecimal multiply = BigDecimal.valueOf(fdShipping.getRowId()).divide(BigDecimal.valueOf(shippingCount), 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                    shippingBuilder.append(fdShipping.getShippingLine()).append(":").append(multiply).append(",").append(",");
                }
                fiHisBooking.setMainExportRoutes(shippingBuilder.substring(0, shippingBuilder.length() - 1));
            }
            //查询目的国
            List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountByDestinationCountry(fdShippingAccount);
            if (fdShippingAccounts.size() > 0) {
                BigDecimal destinationCountryCount = BigDecimal.valueOf(0);
                for (FdShippingAccount fdShippingCount : fdShippingAccounts) {
                    destinationCountryCount = destinationCountryCount.add(BigDecimal.valueOf(fdShippingCount.getRowId()));
                }
                StringBuilder fdShippingBuilder = new StringBuilder();
                fdShippingBuilder.append(fdShippingAccounts.get(0).getDestinationCountry()).append(":").append(BigDecimal.valueOf(fdShippingAccounts.get(0).getRowId()).divide(destinationCountryCount, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100))).append("%");
                fiHisBooking.setMainDestinationCountries(fdShippingBuilder.toString());
            }

            //查询下站口
            List<FdShippingAccount> fdShippingAccountsDestination = fdShippingAccountMapper.selectFdShippingAccountByDestination(fdShippingAccount);
            if (fdShippingAccountsDestination.size() > 0) {
                BigDecimal destinationCount = BigDecimal.valueOf(0);
                for (FdShippingAccount fdShippingDestination : fdShippingAccountsDestination) {
                    destinationCount = destinationCount.add(BigDecimal.valueOf(fdShippingDestination.getRowId()));
                }
                StringBuilder destinationBuilder = new StringBuilder();
                destinationBuilder.append(fdShippingAccountsDestination.get(0).getDestination()).append(":").append(BigDecimal.valueOf(fdShippingAccountsDestination.get(0).getRowId()).divide(destinationCount, 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100))).append("%");
                fiHisBooking.setMainExits(destinationCount.toString());
            }

            //查询运输总量
            List<FdShippingAccount> fdShippingAccountsContainerType = fdShippingAccountMapper.selectFdShippingAccountByContainerType(fdShippingAccount);
            if (fdShippingAccountsContainerType.size() > 0) {
                StringBuilder containerTypeBuilder = new StringBuilder();
                BigDecimal bigDecimal = BigDecimal.valueOf(0);
                for (FdShippingAccount fdShippingContainerType : fdShippingAccountsContainerType) {
                    String rowId = String.valueOf(fdShippingContainerType.getRowId());
                    if (fdShippingContainerType.getContainerType().contains("4")) {
                        rowId = BigDecimal.valueOf(Double.parseDouble(rowId)).multiply(BigDecimal.valueOf(2)).toString();
                    }
                    bigDecimal = bigDecimal.add(BigDecimal.valueOf(Double.parseDouble(rowId)));
                }

                containerTypeBuilder.append(bigDecimal).append("teu").append(" 其中: ");
                for (FdShippingAccount fdShippingContainerTypeTwo : fdShippingAccountsContainerType) {
                    containerTypeBuilder.append(fdShippingContainerTypeTwo.getContainerType()).append("尺").append(fdShippingContainerTypeTwo.getRowId()).append("车").append(",");
                }
                fiHisBooking.setTotalTransportVolume(containerTypeBuilder.substring(0, containerTypeBuilder.toString().length() - 1));
            }
            r.setB(Boolean.TRUE);
            r.setCode(200);
            r.setData(fiHisBooking);
            return r;
        }
        return r;
    }

    /**
     * 获取指定日期的前一天
     *
     * @param specifiedDay
     * @return
     */
    public static String getBeforeDay(String specifiedDay) throws ParseException {
        Calendar c = Calendar.getInstance();
        Date date = null;
        date = new SimpleDateFormat("yyyy-MM-dd").parse(specifiedDay);
        c.setTime(date);
        int day = c.get(Calendar.DATE);
        c.set(Calendar.DATE, day - 1);

        String dayBefore = new SimpleDateFormat("yyyy-MM-dd").format(c.getTime());
        return dayBefore;
    }

    /**
     * 判断约定时间是否在[startTime, endTime]区间，注意时间格式要一致
     *
     * @param nowTime   约定时间
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime() || nowTime.getTime() == endTime.getTime()) {
            return false;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * 生成省级班列号
     * [线路英文简称（2位大写字母）] [地市三字码（3位大写字母）] [年份（2位）月份（2位）][省级序号（00）][整列Z/回程整列H]
     *
     * @param vo
     * @return
     */
    /*public String createProvinceShiftNo(FdShippingAccountVO vo) throws Exception {

        LineManagement lineManagement = new LineManagement();
        lineManagement.setLineName(vo.getShippingLine());
        List<LineManagement> managements = lineManagementService.selectLineManagementList(lineManagement);
        StringBuilder sjblh = new StringBuilder();
        if (managements.size() > 0) {
            if (!"".equals(managements.get(0).getLineSx()) && managements.get(0).getLineSx() != null) {
                if (StringUtils.isBlank(vo.getPlatformCode())) {
                    return "异常：平台编码不能为空，请传入平台编码";
                }
                String sjblh1 = noConfigService.createSjblh(managements.get(0).getLineSx(), vo);
                if (sjblh1.contains("异常：请联系系统管理员")) {
                    throw new Exception(sjblh1);
                } else {
                    sjblh.append(sjblh1);
                }
                if ("G".equals(vo.getTrip())) {
                    sjblh.append("Z");
                } else {
                    sjblh.append("H");
                }

            } else {
                return "异常：该线路没有缩写";
//                throw new Exception("该线路没有缩写");
            }

        } else {
            return "异常：查询不到改线路的信息";
//            throw new Exception("查询不到改线路的信息");
        }

        // 查询班列号是否存在，如果存在重新生成
        FdShippingAccount fdShippingAccount = new FdShippingAccount();
        fdShippingAccount.setProvinceShiftNo(sjblh.toString());
        fdShippingAccount.setDeleteFlag("N");
        List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountList(fdShippingAccount);
        if (fdShippingAccounts.size() > 0) {
//            String provinceShiftNo = createProvinceShiftNo(vo);
            return sjblh + "省级班列号异常，请联系管理员！";
        }
        return sjblh.toString();
    }*/


    /**
     * 获取某月的最后一天
     */
    public static String getLastDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String lastDayOfMonth = sdf.format(cal.getTime());

        return lastDayOfMonth;
    }

    @Override
    public R checkTz(Long rowId) {
        FdShippingAccount fdShippingAccount = fdShippingAccountMapper.selectFdShippingAccountById(rowId);
        if (fdShippingAccount != null) {
            if ("0".equals(fdShippingAccount.getStatus())) {
                return new R(200, Boolean.TRUE, null, "校验成功！");
            } else if ("2".equals(fdShippingAccount.getStatus()) || "5".equals(fdShippingAccount.getStatus())) {
                FdBill fdBill = new FdBill();
                fdBill.setTrainNumber(fdShippingAccount.getShiftNo());
                fdBill.setDelFlag("N");
                List<FdBill> fdBills = fdBillMapper.selectFdBillList(fdBill);
                if (CollUtil.isNotEmpty(fdBills)) {
                    for (FdBill fd : fdBills) {
                        if (!"0".equals(fd.getPlatformLevel())) {
                            if ((!"5".equals(fd.getBillingState()) && "2".equals(fd.getPlatformLevel())) || (!"4".equals(fd.getBillingState()) && "1".equals(fd.getPlatformLevel()))) {
                                return new R(500, Boolean.FALSE, null, "该台账相关账单状态下不可二次导入！");
                            }

                            FdCosdetail sel = new FdCosdetail();
                            sel.setBillCode(fd.getBillCode());
                            sel.setDelFlag("N");
                            List<FdCosdetail> fdCosdetails = fdCosdetailMapper.selectFdCosdetailList(sel);
                            if (CollUtil.isNotEmpty(fdCosdetails)) {
                                for (FdCosdetail fdCosdetail : fdCosdetails) {
                                    if ("1".equals(fdCosdetail.getInvoice())) {
                                        return new R(500, Boolean.FALSE, null, "该台账主账单已开票，不可二次导入！");
                                    }
                                }
                            }
                        }
                    }
                }

                FdBillSub fdBillSub = new FdBillSub();
                fdBillSub.setShiftNo(fdShippingAccount.getShiftNo());
                fdBillSub.setDeleteFlag("N");
                List<FdBillSub> fdBillSubs = fdBillSubMapper.selectFdBillSubList(fdBillSub);
                if (CollUtil.isNotEmpty(fdBillSubs)) {
                    for (FdBillSub fd : fdBillSubs) {
                        if (!"0".equals(fd.getPlatformLevel())) {
                            if ((!"5".equals(fd.getBillingState()) && "2".equals(fd.getPlatformLevel())) || (!"4".equals(fd.getBillingState()) && "1".equals(fd.getPlatformLevel()))) {
                                return new R(500, Boolean.FALSE, null, "该台账相关账单状态下不可二次导入！");
                            }
                            FdBillSubDetail sel = new FdBillSubDetail();
                            sel.setBillSubCode(fd.getBillSubCode());
                            sel.setDeleteFlag("N");
                            List<FdBillSubDetail> fdBillSubDetails = fdBillSubDetailMapper.selectFdBillSubDetailList(sel);
                            if (CollUtil.isNotEmpty(fdBillSubDetails)) {
                                for (FdBillSubDetail fdBillSubDetail : fdBillSubDetails) {
                                    if ("1".equals(fdBillSubDetail.getIsInvoice())) {
                                        return new R(500, Boolean.FALSE, null, "该台账子账单已开票，不可二次导入！");
                                    }
                                }
                            }
                        }
                    }
                }
                return new R(200, Boolean.TRUE, null, "校验成功！");
            } else {
                return new R(500, Boolean.FALSE, null, "该状态下不可二次导入！");
            }
        } else {
            return new R(500, Boolean.FALSE, null, "该台账不存在！");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R importedSecond(String accountCode) {
        FdShippingAccoundetail sel = new FdShippingAccoundetail();
        sel.setAccountCode(accountCode);
        sel.setDeleteFlag("N");
        List<FdShippingAccoundetail> deleteList = fdShippingAccoundetailMapper.selectFdShippingAccoundetailList(sel);

        FdShippingAccoundetailSub sel2 = new FdShippingAccoundetailSub();
        sel2.setAccountCode(accountCode);
        sel2.setDeleteFlag("N");
        List<FdShippingAccoundetailSub> addList = fdShippingAccoundetailSubMapper.selectFdShippingAccoundetailSubList(sel2);

        List<FdShippingAccoundetailSub> updateList = new ArrayList<>();
        if (CollUtil.isNotEmpty(addList)) {
            for (int x = addList.size() - 1; x >= 0; x--) {
                if (CollUtil.isNotEmpty(deleteList)) {
                    for (int y = deleteList.size() - 1; y >= 0; y--) {
                        if (addList.get(x).getContainerNumber().equals(deleteList.get(y).getContainerNumber())) {

                            updateList.add(addList.get(x));
                            addList.remove(x);
                            deleteList.remove(y);
                        }
                    }
                }
            }
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();

        if (CollUtil.isNotEmpty(updateList)) {
            for (FdShippingAccoundetailSub updateObj : updateList) {
                updateObj.setUpdateWho(userInfo.getUserName());
                updateObj.setUpdateWhoName(userInfo.getRealName());
                updateObj.setUpdateTime(LocalDateTime.now());
                fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode2(updateObj);
            }
        }

        if (CollUtil.isNotEmpty(deleteList)) {
            for (FdShippingAccoundetail deleteObj : deleteList) {
                deleteObj.setContainerNewestStatus("1");
                deleteObj.setDeleteFlag("Y");
                deleteObj.setDeleteWho(userInfo.getUserName());
                deleteObj.setDeleteWhoName(userInfo.getRealName());
                deleteObj.setDeleteTime(LocalDateTime.now());
                fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode(deleteObj);
            }
        }

        if (CollUtil.isNotEmpty(addList)) {
            for (FdShippingAccoundetailSub addObj : addList) {
                addObj.setContainerNewestStatus("0");
                addObj.setAddWho(userInfo.getUserName());
                addObj.setAddWhoName(userInfo.getRealName());
                addObj.setAddTime(LocalDateTime.now());
                fdShippingAccoundetailMapper.insertFdShippingAccoundetail2(addObj);
            }
        }

        return null;
    }

    public void back(Long rowId) {
        FdShippingAccount fdShippingAccount = fdShippingAccountMapper.selectFdShippingAccountById(rowId);
        //校验主账单，已提交则无法撤回
        if (fdShippingAccount != null) {
            //删除省应收账单
            BillDealWithCity updObj1 = new BillDealWithCity();
            updObj1.setProvinceShiftNum(fdShippingAccount.getShiftNo());
            updObj1.setCustomerCode(fdShippingAccount.getPlatformCode());
            updObj1.setSourceCode(fdShippingAccount.getCustomerNo());
            updObj1.setCustomerName(fdShippingAccount.getPlatformName());
            updObj1.setSourceUnit(fdShippingAccount.getCustomerName());
            billDealWithCityMapper.fdDeleteBillDealWithCity(updObj1);
            BillSubPayCity updObj2 = new BillSubPayCity();
            updObj2.setShiftNo(fdShippingAccount.getShiftNo());
            updObj2.setPlatformCode(fdShippingAccount.getCustomerNo());
            updObj2.setCustomerCode(fdShippingAccount.getPlatformCode());
            billSubPayCityMapper.fdDeleteBillSubPayCity(updObj2);
            //删除省应付账单
            BillPayProvince updObj3 = new BillPayProvince();
            updObj3.setProvinceShiftNum(fdShippingAccount.getShiftNo());
            updObj3.setCustomerCode(fdShippingAccount.getCustomerNo());
            updObj3.setSourceCode("ztdl");
            updObj3.setCustomerName(fdShippingAccount.getCustomerName());
            updObj3.setSourceUnit("中铁国际多式联运有限公司");
            billPayProvinceMapper.fdDeleteBillPayProvince(updObj3);
            BillPayProvinceSub updObj4 = new BillPayProvinceSub();
            updObj4.setShiftNo(fdShippingAccount.getShiftNo());
            updObj4.setPlatformCode("ztdl");
            updObj4.setCustomerCode(fdShippingAccount.getCustomerNo());
            billPayProvinceSubMapper.fdDeleteBillPayProvinceSub(updObj4);
            //删除应付账单明细
            FdBusCostDetail sel2 = new FdBusCostDetail();
            sel2.setShiftNo(fdShippingAccount.getShiftNo());
            sel2.setReceiveCode("ztdl");
            sel2.setPayCode(fdShippingAccount.getCustomerNo());
            sel2.setDeleteFlag("N");
            fdBusCostDetailMapper.updateProvinceBack(sel2);

            //修改台账状态
            FdShippingAccount updateObj = new FdShippingAccount();
            updateObj.setRowId(rowId);
            updateObj.setStatus("2");
            updateObj.setBillStatus("2");
            fdShippingAccountMapper.updateFdShippingAccount(updateObj);
        }

    }

    @Override
    public R cancel(Long rowId) {
        FdShippingAccount fdShippingAccount = fdShippingAccountMapper.selectFdShippingAccountById(rowId);
        //校验主账单，已提交则无法撤回
        if (fdShippingAccount != null) {
            //查询是否存在结算单
            BillBalanceMainCity sel = new BillBalanceMainCity();
            sel.setShiftNos(fdShippingAccount.getShiftNo());
            sel.setCustomerCode(fdShippingAccount.getCustomerNo());
            sel.setDeleteFlag("N");
            List<BillBalanceMainCity> yf = billBalanceMainCityMapper.selectBillBalanceMainCityListByLike2(sel);
            if (CollUtil.isNotEmpty(yf)) {
                return new R(new Throwable("该班次已有应付结算单，无法撤回！"));
            }

            sel.setCustomerCode(fdShippingAccount.getPlatformCode());
            List<BillBalanceMainCity> ys = billBalanceMainCityMapper.selectBillBalanceMainCityListByLike2(sel);
            if (CollUtil.isNotEmpty(ys)) {
                return new R(new Throwable("该班次已有应收结算单，无法撤回！"));
            }

            //删除省应收账单
            BillDealWithCity updObj1 = new BillDealWithCity();
            updObj1.setProvinceShiftNum(fdShippingAccount.getShiftNo());
            updObj1.setCustomerCode(fdShippingAccount.getPlatformCode());
            updObj1.setSourceCode(fdShippingAccount.getCustomerNo());
            updObj1.setCustomerName(fdShippingAccount.getPlatformName());
            updObj1.setSourceUnit(fdShippingAccount.getCustomerName());
            billDealWithCityMapper.fdDeleteBillDealWithCity(updObj1);
            BillSubPayCity updObj2 = new BillSubPayCity();
            updObj2.setShiftNo(fdShippingAccount.getShiftNo());
            updObj2.setPlatformCode(fdShippingAccount.getCustomerNo());
            updObj2.setCustomerCode(fdShippingAccount.getPlatformCode());
            billSubPayCityMapper.fdDeleteBillSubPayCity(updObj2);
            //删除省应付账单
            BillPayProvince updObj3 = new BillPayProvince();
            updObj3.setProvinceShiftNum(fdShippingAccount.getShiftNo());
            updObj3.setCustomerCode(fdShippingAccount.getCustomerNo());
            updObj3.setSourceCode("ztdl");
            updObj3.setCustomerName(fdShippingAccount.getCustomerName());
            updObj3.setSourceUnit("中铁国际多式联运有限公司");
            billPayProvinceMapper.fdDeleteBillPayProvince(updObj3);
            BillPayProvinceSub updObj4 = new BillPayProvinceSub();
            updObj4.setShiftNo(fdShippingAccount.getShiftNo());
            updObj4.setPlatformCode("ztdl");
            updObj4.setCustomerCode(fdShippingAccount.getCustomerNo());
            billPayProvinceSubMapper.fdDeleteBillPayProvinceSub(updObj4);
            //删除应付账单明细
            FdBusCostDetail sel2 = new FdBusCostDetail();
            sel2.setShiftNo(fdShippingAccount.getShiftNo());
            sel2.setReceiveCode("ztdl");
            sel2.setPayCode(fdShippingAccount.getCustomerNo());
            sel2.setDeleteFlag("N");
            List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList(sel2);
            if (CollUtil.isNotEmpty(detailList)) {
                for (FdBusCostDetail detail : detailList) {
                    FdBusCostDetail updObj = new FdBusCostDetail();
                    updObj.setId(detail.getId());
                    updObj.setDeleteFlag("Y");
                    updObj.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                    updObj.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                    updObj.setUpdateTime(LocalDateTime.now());
                    fdBusCostDetailMapper.updateFdBusCostDetail(updObj);
                }
            }

            //修改台账状态
            FdShippingAccount updateObj = new FdShippingAccount();
            updateObj.setRowId(rowId);
            updateObj.setStatus("1");
            updateObj.setBillStatus("0");
            fdShippingAccountMapper.updateFdShippingAccount(updateObj);
            return new R(0, Boolean.TRUE, null, "撤回成功！");
        } else {
            return new R(new Throwable("未查询到该台账，无法撤回！"));
        }

    }

    //校验新数据的状态（新增、删除、还是修改）
    public static List<FdShippingAccoundetailSub> checkStatus(List<FdShippingAccoundetailSub> addList, List<FdShippingAccoundetail> deleteList) {
//        List<FdShippingAccoundetailSub> fdShippingAccoundetailSubs = new ArrayList<>();

        //相同箱子的原有数据
//        List<FdShippingAccoundetailSub> updateList = new ArrayList<>();
        if (CollUtil.isNotEmpty(addList)) {
            for (FdShippingAccoundetailSub add : addList) {
                if (add.getRrDomesticFreight() == null || add.getRrDomesticFreight().compareTo(BigDecimal.valueOf(0)) == 0) {
                    add.setRrDomesticFreight(add.getDomesticFreight());
                }
                if (add.getRrOverseasFreightOc() == null || add.getRrOverseasFreightOc().compareTo(BigDecimal.valueOf(0)) == 0) {
                    add.setRrOverseasFreightOc(add.getOverseasFreightOc());
                }
                if (add.getRrOverseasFreightCny() == null || add.getRrOverseasFreightCny().compareTo(BigDecimal.valueOf(0)) == 0) {
                    add.setRrOverseasFreightCny(add.getOverseasFreightCny());
                }
            }
            if (CollUtil.isNotEmpty(deleteList)) {
                for (int x = addList.size() - 1; x >= 0; x--) {
                    if (StrUtil.isEmpty(addList.get(x).getContainerNewestStatus())) {
                        for (int y = deleteList.size() - 1; y >= 0; y--) {
                            if (addList.get(x).getContainerNumber().equals(deleteList.get(y).getContainerNumber())) {
                                //相同的箱子，状态不改变
                                String remark = "";
                                FdShippingAccoundetailSub fdShippingAccoundetailSub = addList.get(x);
                                fdShippingAccoundetailSub.setContainerNewestStatus(null);
                                fdShippingAccoundetailSub.setContainerStatus(null);
                                if ((addList.get(x).getOverseasFreightOc().compareTo(deleteList.get(y).getOverseasFreightOc()) != 0 || addList.get(x).getOverseasFreightCny().compareTo(deleteList.get(y).getOverseasFreightCny()) != 0 || addList.get(x).getDomesticFreight().compareTo(deleteList.get(y).getDomesticFreight()) != 0) && (addList.get(x).getRrOverseasFreightOc().compareTo(deleteList.get(y).getRrOverseasFreightOc()) != 0 || addList.get(x).getRrOverseasFreightCny().compareTo(deleteList.get(y).getRrOverseasFreightCny()) != 0 || addList.get(x).getRrDomesticFreight().compareTo(deleteList.get(y).getRrDomesticFreight()) != 0)

                                ) {
                                    //应收应付费用都有修改
                                    fdShippingAccoundetailSub.setContainerNewestStatus("2");
                                    fdShippingAccoundetailSub.setContainerStatus("2");
                                } else if (addList.get(x).getOverseasFreightOc().compareTo(deleteList.get(y).getOverseasFreightOc()) != 0 || addList.get(x).getOverseasFreightCny().compareTo(deleteList.get(y).getOverseasFreightCny()) != 0 || addList.get(x).getDomesticFreight().compareTo(deleteList.get(y).getDomesticFreight()) != 0) {
                                    //应收费用修改
                                    fdShippingAccoundetailSub.setContainerNewestStatus("3");
                                    fdShippingAccoundetailSub.setContainerStatus("3");
                                } else if (addList.get(x).getRrOverseasFreightOc().compareTo(deleteList.get(y).getRrOverseasFreightOc()) != 0 || addList.get(x).getRrOverseasFreightCny().compareTo(deleteList.get(y).getRrOverseasFreightCny()) != 0 || addList.get(x).getRrDomesticFreight().compareTo(deleteList.get(y).getRrDomesticFreight()) != 0) {
                                    //应付费用修改
                                    fdShippingAccoundetailSub.setContainerNewestStatus("4");
                                    fdShippingAccoundetailSub.setContainerStatus("4");
                                }
                                //箱修改备注
                                if (addList.get(x).getExchangeRate().compareTo(deleteList.get(y).getExchangeRate()) != 0) {
                                    remark = remark + "汇率由" + deleteList.get(y).getExchangeRate() + "变更为:" + addList.get(x).getExchangeRate() + ",";
                                }
                                if (addList.get(x).getOverseasFreightOc().compareTo(deleteList.get(y).getOverseasFreightOc()) != 0) {
                                    remark = remark + "境外运费(原币)由" + deleteList.get(y).getOverseasFreightOc() + "变更为:" + addList.get(x).getOverseasFreightOc() + ",";
                                }
                                if (addList.get(x).getOverseasFreightCny().compareTo(deleteList.get(y).getOverseasFreightCny()) != 0) {
                                    remark = remark + "境外运费(人民币)由" + deleteList.get(y).getOverseasFreightCny() + "变更为:" + addList.get(x).getOverseasFreightCny() + ",";
                                }
                                if (addList.get(x).getDomesticFreight().compareTo(deleteList.get(y).getDomesticFreight()) != 0) {
                                    remark = remark + "境内运费(人民币)由" + deleteList.get(y).getDomesticFreight() + "变更为:" + addList.get(x).getDomesticFreight() + ",";
                                }
                                if (addList.get(x).getRrOverseasFreightOc().compareTo(deleteList.get(y).getRrOverseasFreightOc()) != 0) {
                                    remark = remark + "铁路境外运费(原币)由" + deleteList.get(y).getRrOverseasFreightOc() + "变更为:" + addList.get(x).getRrOverseasFreightOc() + ",";
                                }
                                if (addList.get(x).getRrOverseasFreightCny().compareTo(deleteList.get(y).getRrOverseasFreightCny()) != 0) {
                                    remark = remark + "铁路境外运费(人民币)由" + deleteList.get(y).getRrOverseasFreightCny() + "变更为:" + addList.get(x).getRrOverseasFreightCny() + ",";
                                }
                                if (addList.get(x).getRrDomesticFreight().compareTo(deleteList.get(y).getRrDomesticFreight()) != 0) {
                                    remark = remark + "铁路境内运费(人民币)由" + deleteList.get(y).getRrDomesticFreight() + "变更为:" + addList.get(x).getRrDomesticFreight() + ",";
                                }
                                if (StrUtil.isNotEmpty(remark)) {
                                    remark = "箱号" + addList.get(x).getContainerNumber() + ":" + remark;
                                }
                                fdShippingAccoundetailSub.setRemarks(remark);
//                                updateList.add(fdShippingAccoundetailSub);

//                                addList.remove(x);
//                                deleteList.remove(y);
                            }
                        }
                    }
                }
            }

        }

        //集合三个list数据，返回前端
        /*if (CollUtil.isNotEmpty(addList)) {
            for (FdShippingAccoundetailSub fdShippingAccoundetailSub : addList
            ) {
                fdShippingAccoundetailSubs.add(fdShippingAccoundetailSub);
            }
        }
        if (CollUtil.isNotEmpty(updateList)) {
            for (FdShippingAccoundetailSub fdShippingAccoundetailSub : updateList
            ) {
                fdShippingAccoundetailSubs.add(fdShippingAccoundetailSub);
            }
        }

        if (CollUtil.isNotEmpty(deleteList)) {
            for (FdShippingAccoundetail fdShippingAccoundetail : deleteList
            ) {
                FdShippingAccoundetailSub fdShippingAccoundetailSub = convertObj(fdShippingAccoundetail);
                fdShippingAccoundetailSub.setContainerStatus("1");
                fdShippingAccoundetailSub.setContainerNewestStatus("1");
                fdShippingAccoundetailSubs.add(fdShippingAccoundetailSub);
            }
        }*/

        return addList;
    }

    //对象转换
    public static FdShippingAccoundetailSub convertObj(FdShippingAccoundetail fdShippingAccoundetail) {
        FdShippingAccoundetailSub fdShippingAccoundetailSub = new FdShippingAccoundetailSub();
        fdShippingAccoundetailSub.setAccountCode(fdShippingAccoundetail.getAccountCode());
        fdShippingAccoundetailSub.setCustomerNo(fdShippingAccoundetail.getCustomerNo());
        fdShippingAccoundetailSub.setCustomerName(fdShippingAccoundetail.getCustomerName());
        fdShippingAccoundetailSub.setApplicationNumber(fdShippingAccoundetail.getApplicationNumber());
        fdShippingAccoundetailSub.setTransportOrderNumber(fdShippingAccoundetail.getTransportOrderNumber());
        fdShippingAccoundetailSub.setContainerNumber(fdShippingAccoundetail.getContainerNumber());
        fdShippingAccoundetailSub.setActualTrainNumber(fdShippingAccoundetail.getActualTrainNumber());
        fdShippingAccoundetailSub.setClearanceNumber(fdShippingAccoundetail.getClearanceNumber());
        fdShippingAccoundetailSub.setValueUsd(fdShippingAccoundetail.getValueUsd());
        fdShippingAccoundetailSub.setCustomsSeal(fdShippingAccoundetail.getCustomsSeal());
        fdShippingAccoundetailSub.setTrainNumber(fdShippingAccoundetail.getTrainNumber());
        fdShippingAccoundetailSub.setWaybillDemandNumber(fdShippingAccoundetail.getWaybillDemandNumber());
        fdShippingAccoundetailSub.setWaybillLnNumber(fdShippingAccoundetail.getWaybillLnNumber());
        fdShippingAccoundetailSub.setSubsidyStandards(fdShippingAccoundetail.getSubsidyStandards());
        fdShippingAccoundetailSub.setSubsidyAmount(fdShippingAccoundetail.getSubsidyAmount());
        fdShippingAccoundetailSub.setMonetaryType(fdShippingAccoundetail.getMonetaryType());
        fdShippingAccoundetailSub.setExchangeRate(fdShippingAccoundetail.getExchangeRate());
        fdShippingAccoundetailSub.setOverseasFreightOc(fdShippingAccoundetail.getOverseasFreightOc());
        fdShippingAccoundetailSub.setOverseasFreightCny(fdShippingAccoundetail.getOverseasFreightCny());
        fdShippingAccoundetailSub.setDomesticFreight(fdShippingAccoundetail.getDomesticFreight());
        fdShippingAccoundetailSub.setShippingFreight(fdShippingAccoundetail.getShippingFreight());
        fdShippingAccoundetailSub.setRemarks(fdShippingAccoundetail.getRemarks());
        fdShippingAccoundetailSub.setDeleteFlag("N");
        fdShippingAccoundetailSub.setAddTime(LocalDateTime.now());
        fdShippingAccoundetailSub.setIsRansit(fdShippingAccoundetail.getIsRansit());
        fdShippingAccoundetailSub.setOrgUnit(fdShippingAccoundetail.getOrgUnit());
        fdShippingAccoundetailSub.setDestinationName(fdShippingAccoundetail.getDestinationName());
        fdShippingAccoundetailSub.setDestination(fdShippingAccoundetail.getDestination());
        fdShippingAccoundetailSub.setContainerNo(fdShippingAccoundetail.getContainerNo());
        fdShippingAccoundetailSub.setGoodsName(fdShippingAccoundetail.getGoodsName());
        fdShippingAccoundetailSub.setGoodsNums(fdShippingAccoundetail.getGoodsNums());
        fdShippingAccoundetailSub.setGoodsWeight(fdShippingAccoundetail.getGoodsWeight());
        fdShippingAccoundetailSub.setContainerType(fdShippingAccoundetail.getContainerType());
        fdShippingAccoundetailSub.setConsignorName(fdShippingAccoundetail.getConsignorName());
        fdShippingAccoundetailSub.setDestinationCountry(fdShippingAccoundetail.getDestinationCountry());
        fdShippingAccoundetailSub.setIsFull(fdShippingAccoundetail.getIsFull());
        fdShippingAccoundetailSub.setNonFerrous(fdShippingAccoundetail.getNonFerrous());
        fdShippingAccoundetailSub.setGoodsOrigin(fdShippingAccoundetail.getGoodsOrigin());
        fdShippingAccoundetailSub.setContainerWeight(fdShippingAccoundetail.getContainerWeight());
        fdShippingAccoundetailSub.setGoodsOwner(fdShippingAccoundetail.getGoodsOwner());
        fdShippingAccoundetailSub.setPortAgent(fdShippingAccoundetail.getPortAgent());
        fdShippingAccoundetailSub.setDestinationCountryCode(fdShippingAccoundetail.getDestinationCountryCode());
        fdShippingAccoundetailSub.setContainerOwner(fdShippingAccoundetail.getContainerOwner());
        fdShippingAccoundetailSub.setResveredField07(fdShippingAccoundetail.getResveredField07());
        fdShippingAccoundetailSub.setContainerTypeCode(fdShippingAccoundetail.getContainerTypeCode());
        fdShippingAccoundetailSub.setContainerTypeName(fdShippingAccoundetail.getContainerTypeName());
        fdShippingAccoundetailSub.setResveredField10(fdShippingAccoundetail.getResveredField10());
        fdShippingAccoundetailSub.setRrOverseasFreightOc(fdShippingAccoundetail.getRrOverseasFreightOc());
        fdShippingAccoundetailSub.setRrOverseasFreightCny(fdShippingAccoundetail.getRrOverseasFreightCny());
        fdShippingAccoundetailSub.setRrDomesticFreight(fdShippingAccoundetail.getRrDomesticFreight());
        return fdShippingAccoundetailSub;
    }

    @Override
    public FdShippingAccount selectFdShippingAccountByAccountCode(String accountCode) {
        return fdShippingAccountMapper.selectFdShippingAccountByAccountCode(accountCode);
    }

    @Override
    public R infoBatch(List<String> accountCodes) {
        // 1. 初始化数据结构
        Map<String, Object> map = new HashMap<>();
        List<FdShippingAccount> fdShippingAccounts = new ArrayList<>();
        // 用于检查 platformCode 是否重复
        Set<String> platformCodeSet = new HashSet<>();
        // 2. 根据 accountCodes 查询 FdShippingAccount 数据
        if (CollUtil.isNotEmpty(accountCodes)) {
            for (String accountCode : accountCodes) {
                FdShippingAccount fdShippingAccount = fdShippingAccountMapper.selectFdShippingAccountByAccountCode(accountCode);
                if (fdShippingAccount != null) {
                    // 3. 校验 platformCode 是否重复
                    if (!platformCodeSet.add(fdShippingAccount.getPlatformCode())) {
                        return R.error("同一平台台账，不允许混装班列审核！");
                    }
                    fdShippingAccounts.add(fdShippingAccount);
                }
            }
        }

        // 4. 如果没有找到有效的 FdShippingAccount 数据，返回空响应
        if (CollUtil.isEmpty(fdShippingAccounts)) {
            return R.error("没有查询到对应的台账信息！");
        }

        CustomerInfo customerInfo = new CustomerInfo();
        customerInfo.setCustomerFlag("1");
        customerInfo.setDeleteFlag("N");
        List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoList(customerInfo);

        for (FdShippingAccount fdShippingAccount : fdShippingAccounts) {
            String name = fdShippingAccount.getCustomerName();
            // 8. 根据 platformCode 获取对应的 CustomerInfo 名称
            Optional<CustomerInfo> customerInfoOpt = customerInfos.stream().filter(c -> c.getCustomerCode().equals(fdShippingAccount.getPlatformCode())).findFirst();
            if (customerInfoOpt.isPresent() && StrUtil.isNotBlank(customerInfoOpt.get().getCompanyShortName())) {
                name = customerInfoOpt.get().getCompanyShortName();
            }
            FdShippingAccoundetail fdShippingAccoundetail = new FdShippingAccoundetail();
            fdShippingAccoundetail.setAccountCode(fdShippingAccount.getAccountCode());
            List<FdShippingAccoundetail> list = fdShippingAccoundetailService.list(fdShippingAccoundetail);
            if (CollUtil.isNotEmpty(list)) {
                fdShippingAccount.setDetaillist(list);
            }
            map.put(name, fdShippingAccount);
        }
        return R.success(map);
    }

    /**
     * 根据用户导入数据搜索箱号
     *
     * @param params
     * @return
     */
    @Override
    public R selectAccountByDL(Map<String, Object> params) {
        R r = new R();
        try {
            SelectAccountByDlDTO selectAccountByDlDTO = BeanUtil.mapToBean(params, SelectAccountByDlDTO.class, false);
            // 读取url
            //String fileUrl = "file:///C:/Users/<USER>/Desktop/spy-oy-sql/中铁国际多式联运有限公司(收)-山东高速齐鲁号欧亚班列运营有限公司(付)-对账单数据-BLZD20240514152847097.xls";
            InputStream inputStream = new URL(selectAccountByDlDTO.getFileUrl()).openStream();
            Workbook sheets = new HSSFWorkbook(inputStream);
            Sheet sheetAt = sheets.getSheetAt(1);
            List<String> boxNums = new ArrayList<>();
            Boolean fals = true;
            int rowIn = 2;
            while (fals) {
                Row row = sheetAt.getRow(rowIn);
                Cell cell = row.getCell(1);
                if (cell != null && !cell.getStringCellValue().equals("")) {
                    boxNums.add(cell.getStringCellValue());
                    rowIn++;
                } else {
                    fals = false;
                }
            }
            // 获取当前登录人信息
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String platformCode = userInfo.getPlatformCode();

            // 查询数据库中对应的箱号
            List<LedgerListVO> ledgerListVOS = fdShippingAccountMapper.selectFdShippingAccountByDlList(selectAccountByDlDTO.getShippingLineCode(), selectAccountByDlDTO.getTrip(), selectAccountByDlDTO.getPostStartTime(), selectAccountByDlDTO.getPostEndTime(), selectAccountByDlDTO.getStartStation(), selectAccountByDlDTO.getEndStation(), boxNums, platformCode);

            Map<String, String> boxMap = boxNums.stream().collect(Collectors.toMap(Function.identity(), Function.identity()));
            List<LedgerListVO> reData = new ArrayList<>();
            for (String box : boxNums) {
                List<LedgerListVO> collect = ledgerListVOS.stream().filter(f -> f.getBoxNo().equals(box)).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(collect)) {
                    reData.addAll(collect);
                } else {
                    LedgerListVO vo = new LedgerListVO();
                    vo.setBoxNo(box);
                    reData.add(vo);
                }
            }
            sheets.close();
            r.setData(reData);
            return r;
        } catch (Exception e) {
            r.setMsg("读取数据报错");
            r.setCode(500);
            return r;
        }
    }


    @Override
    public void exportTemplateForTz(HttpServletResponse response) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("台账明细");
        XSSFRow row = sheet.createRow(0);
        //宽度
        for (int i = 0; i <= 14; i++) {
            sheet.setColumnWidth(i, 60 * 80);
        }

        row.setHeight((short) (10 * 50));
        //字体1
        XSSFCellStyle style = setStyle(workbook);
        setTitle(row, style, sheet);

        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String(("导入台账明细模板.xlsx").getBytes("GB2312"), "8859_1"));
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }

    /**
     * 设置标题
     *
     * @Param: row, style
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/04 17:57
     **/
    public void setTitle(XSSFRow row, XSSFCellStyle style, XSSFSheet sheet) {
        XSSFCell cell = row.createCell(0);
        cell.setCellValue("班次号");
        cell.setCellStyle(style);
        XSSFCell cell01 = row.createCell(1);
        cell01.setCellValue("");
        cell01.setCellStyle(style);

        XSSFRow row1 = sheet.createRow(1);
        XSSFCell cell0 = row1.createCell(0);
        cell0.setCellValue("序号");
        cell0.setCellStyle(style);
        XSSFCell cell1 = row1.createCell(1);
        cell1.setCellValue("箱号*");
        cell1.setCellStyle(style);
        XSSFCell cell2 = row1.createCell(2);
        cell2.setCellValue("类型*");
        cell2.setCellStyle(style);
        XSSFCell cell3 = row1.createCell(3);
        cell3.setCellValue("货源组织单位*");
        cell3.setCellStyle(style);
        XSSFCell cell4 = row1.createCell(4);
        cell4.setCellValue("收货人*");
        cell4.setCellStyle(style);
        XSSFCell cell5 = row1.createCell(5);
        cell5.setCellValue("发站*");
        cell5.setCellStyle(style);
        XSSFCell cell6 = row1.createCell(6);
        cell6.setCellValue("目的国*");
        cell6.setCellStyle(style);
        XSSFCell cell7 = row1.createCell(7);
        cell7.setCellValue("到站*");
        cell7.setCellStyle(style);
        XSSFCell cell8 = row1.createCell(8);
        cell8.setCellValue("口岸代理*");
        cell8.setCellStyle(style);
        XSSFCell cell9 = row1.createCell(9);
        cell9.setCellValue("品名*");
        cell9.setCellStyle(style);
        XSSFCell cell10 = row1.createCell(10);
        cell10.setCellValue("箱属*");
        cell10.setCellStyle(style);
        XSSFCell cell11 = row1.createCell(11);
        cell11.setCellValue("箱型代码*");
        cell11.setCellStyle(style);
        XSSFCell cell12 = row1.createCell(12);
        cell12.setCellValue("件数*");
        cell12.setCellStyle(style);
        XSSFCell cell13 = row1.createCell(13);
        cell13.setCellValue("货重*");
        cell13.setCellStyle(style);
        XSSFCell cell14 = row1.createCell(14);
        cell14.setCellValue("箱重*");
        cell14.setCellStyle(style);
        XSSFCell cell15 = row1.createCell(15);
        cell15.setCellValue("是否全程*");
        cell15.setCellStyle(style);
        XSSFCell cell16 = row1.createCell(16);
        cell16.setCellValue("有色金属(是/否)*");
        cell16.setCellStyle(style);
        XSSFCell cell17 = row1.createCell(17);
        cell17.setCellValue("箱补充信息");
        cell17.setCellStyle(style);
        XSSFCell cell18 = row1.createCell(18);
        cell18.setCellValue("");
        cell18.setCellStyle(style);
        XSSFCell cell19 = row1.createCell(19);
        cell19.setCellValue("");
        cell19.setCellStyle(style);
        XSSFCell cell20 = row1.createCell(20);
        cell20.setCellValue("");
        cell20.setCellStyle(style);
        XSSFCell cell21 = row1.createCell(21);
        cell21.setCellValue("");
        cell21.setCellStyle(style);
        XSSFCell cell22 = row1.createCell(22);
        cell22.setCellValue("");
        cell22.setCellStyle(style);
        XSSFCell cell23 = row1.createCell(23);
        cell23.setCellValue("");
        cell23.setCellStyle(style);
        XSSFCell cell24 = row1.createCell(24);
        cell24.setCellValue("");
        cell24.setCellStyle(style);
        XSSFCell cell25 = row1.createCell(25);
        cell25.setCellValue("运费信息");
        cell25.setCellStyle(style);
        XSSFCell cell26 = row1.createCell(26);
        cell26.setCellValue("");
        cell26.setCellStyle(style);
        XSSFCell cell27 = row1.createCell(27);
        cell27.setCellValue("");
        cell27.setCellStyle(style);
        XSSFCell cell28 = row1.createCell(28);
        cell28.setCellValue("");
        cell28.setCellStyle(style);
        XSSFCell cell29 = row1.createCell(29);
        cell29.setCellValue("");
        cell29.setCellStyle(style);
        XSSFCell cell30 = row1.createCell(30);
        cell30.setCellValue("补贴信息");
        cell30.setCellStyle(style);
        XSSFCell cell31 = row1.createCell(31);
        cell31.setCellValue("");
        cell31.setCellStyle(style);
        XSSFCell cell32 = row1.createCell(32);
        cell32.setCellValue("备注");
        cell32.setCellStyle(style);

        XSSFRow row2 = sheet.createRow(2);
        XSSFCell cell220 = row2.createCell(0);
        cell220.setCellValue("");
        cell220.setCellStyle(style);
        XSSFCell cell221 = row2.createCell(1);
        cell221.setCellValue("");
        cell221.setCellStyle(style);
        XSSFCell cell222 = row2.createCell(2);
        cell222.setCellValue("");
        cell222.setCellStyle(style);
        XSSFCell cell223 = row2.createCell(3);
        cell223.setCellValue("");
        cell223.setCellStyle(style);
        XSSFCell cell224 = row2.createCell(4);
        cell224.setCellValue("");
        cell224.setCellStyle(style);
        XSSFCell cell225 = row2.createCell(5);
        cell225.setCellValue("");
        cell225.setCellStyle(style);
        XSSFCell cell226 = row2.createCell(6);
        cell226.setCellValue("");
        cell226.setCellStyle(style);
        XSSFCell cell227 = row2.createCell(7);
        cell227.setCellValue("");
        cell227.setCellStyle(style);
        XSSFCell cell228 = row2.createCell(8);
        cell228.setCellValue("");
        cell228.setCellStyle(style);
        XSSFCell cell229 = row2.createCell(9);
        cell229.setCellValue("");
        cell229.setCellStyle(style);
        XSSFCell cell2210 = row2.createCell(10);
        cell2210.setCellValue("");
        cell2210.setCellStyle(style);
        XSSFCell cell2211 = row2.createCell(11);
        cell2211.setCellValue("");
        cell2211.setCellStyle(style);
        XSSFCell cell2212 = row2.createCell(12);
        cell2212.setCellValue("");
        cell2212.setCellStyle(style);
        XSSFCell cell2213 = row2.createCell(13);
        cell2213.setCellValue("");
        cell2213.setCellStyle(style);
        XSSFCell cell2214 = row2.createCell(14);
        cell2214.setCellValue("");
        cell2214.setCellStyle(style);
        XSSFCell cell2215 = row2.createCell(15);
        cell2215.setCellValue("");
        cell2215.setCellStyle(style);
        XSSFCell cell2216 = row2.createCell(16);
        cell2216.setCellValue("");
        cell2216.setCellStyle(style);
        XSSFCell cell2217 = row2.createCell(17);
        cell2217.setCellValue("货主");
        cell2217.setCellStyle(style);
        XSSFCell cell2218 = row2.createCell(18);
        cell2218.setCellValue("境内货源地/目的地");
        cell2218.setCellStyle(style);
        XSSFCell cell2219 = row2.createCell(19);
        cell2219.setCellValue("报关单号");
        cell2219.setCellStyle(style);
        XSSFCell cell2220 = row2.createCell(20);
        cell2220.setCellValue("货值(美金)");
        cell2220.setCellStyle(style);
        XSSFCell cell2221 = row2.createCell(21);
        cell2221.setCellValue("海关封");
        cell2221.setCellStyle(style);
        XSSFCell cell2222 = row2.createCell(22);
        cell2222.setCellValue("车号");
        cell2222.setCellStyle(style);
        XSSFCell cell2223 = row2.createCell(23);
        cell2223.setCellValue("订单需求号");
        cell2223.setCellStyle(style);
        XSSFCell cell2224 = row2.createCell(24);
        cell2224.setCellValue("国联订单号");
        cell2224.setCellStyle(style);
        XSSFCell cell2225 = row2.createCell(25);
        cell2225.setCellValue("境内运费(人民币)");
        cell2225.setCellStyle(style);
        XSSFCell cell2226 = row2.createCell(26);
        cell2226.setCellValue("境外运费(人民币)");
        cell2226.setCellStyle(style);
        XSSFCell cell2227 = row2.createCell(27);
        cell2227.setCellValue("境外运费(原币)");
        cell2227.setCellStyle(style);
        XSSFCell cell2228 = row2.createCell(28);
        cell2228.setCellValue("境外汇率");
        cell2228.setCellStyle(style);
        XSSFCell cell2229 = row2.createCell(29);
        cell2229.setCellValue("境外币种");
        cell2229.setCellStyle(style);
        XSSFCell cell2230 = row2.createCell(30);
        cell2230.setCellValue("补贴标准");
        cell2230.setCellStyle(style);
        XSSFCell cell2231 = row2.createCell(31);
        cell2231.setCellValue("补贴金额");
        cell2231.setCellStyle(style);
        XSSFCell cell2232 = row2.createCell(32);
        cell2232.setCellValue("箱备注");
        cell2232.setCellStyle(style);
        //合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 1, 1));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 2, 2));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 3, 3));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 4, 4));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 5, 5));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 6, 6));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 7, 7));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 8, 8));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 9, 9));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 10, 10));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 11, 11));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 12, 12));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 13, 13));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 14, 14));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 15, 15));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 16, 16));

        sheet.addMergedRegion(new CellRangeAddress(1, 1, 17, 24));
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 25, 29));
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 30, 31));
    }

    public XSSFCellStyle setStyle(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");
        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);
        return style;
    }

    @Transactional(rollbackFor = Exception.class)
    public R importedTemplateForTz(MultipartFile file) throws Exception {
        Workbook workbook = null;
        try {
            InputStream is = file.getInputStream();
            //利用WorkbookFactory获取Sheet，无需判断版本
            workbook = WorkbookFactory.create(is);
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<FdShippingAccoundetail> list = new ArrayList<>();

        //导入第一张sheet页
        Sheet sheet = workbook.getSheetAt(0);
        FdShippingAccount fdShippingAccount = new FdShippingAccount();
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        Row row0 = sheet.getRow(0);
        if (row0.getCell(1) != null) {
            row0.getCell(1).setCellType(CellType.STRING);
            String shiftNo = row0.getCell(1).getStringCellValue();
            Shifmanagement sel = new Shifmanagement();
            sel.setShiftId(shiftNo);
            sel.setPlatformCode(userInfo.getPlatformCode());
            sel.setReleaseStatus("1");
            List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
            if (CollUtil.isNotEmpty(shifmanagements)) {
                Shifmanagement shifmanagement = shifmanagements.get(0);
                fdShippingAccount.setShiftNo(shifmanagement.getShiftId());
                fdShippingAccount.setShiftName(shifmanagement.getShiftName());
                fdShippingAccount.setShippingTime(LocalDateTime.ofInstant(shifmanagement.getPlanShipTime().toInstant(), ZoneId.systemDefault()));
                fdShippingAccount.setShippingLine(shifmanagement.getShippingLine());
                fdShippingAccount.setTrip(shifmanagement.getTrip());
                if (StrUtil.isNotBlank(shifmanagement.getDestinationName()) || StrUtil.isNotBlank(shifmanagement.getDestination())) {
                    StationManagement sel2 = new StationManagement();
                    if ("G".equals(shifmanagement.getTrip())) {
                        sel2.setStationName(shifmanagement.getDestinationName());
                        fdShippingAccount.setDestinationName(shifmanagement.getDestinationName());
                    } else {
                        sel2.setStationName(shifmanagement.getDestination());
                        fdShippingAccount.setDestinationName(shifmanagement.getDestination());
                    }
                    sel2.setDeleteFlag("N");
                    if (StrUtil.isNotBlank(sel2.getStationName())) {
                        List<StationManagement> stationManagements = stationManagementMapper.selectStationManagementList(sel2);
                        if (CollUtil.isNotEmpty(stationManagements)) {
                            fdShippingAccount.setCity(stationManagements.get(0).getCity());
                        }
                    }
                }
                fdShippingAccount.setPortStation(shifmanagement.getPortStation());
            } else {
                return new R<>(new Throwable(shiftNo + "该班次不存在或未发布，请检查班次！"));
            }
            fdShippingAccount.setPlatformCode(userInfo.getPlatformCode());
            fdShippingAccount.setPlatformName(userInfo.getPlatformName());
            fdShippingAccount.setOrgUnit(userInfo.getPlatformName());
            fdShippingAccount.setPlatformFlag("0");
            fdShippingAccount.setAccountCode(sysNoConfigService.genNo("TZ"));
            if (StrUtil.isNotEmpty(userInfo.getSupPlatformCode())) {
                if (userInfo.getSupPlatformCode().contains("_")) {
                    fdShippingAccount.setCustomerNo(userInfo.getSupPlatformCode().split("_")[0]);
                } else {
                    fdShippingAccount.setCustomerNo(userInfo.getSupPlatformCode());
                }
                CustomerInfo sel2 = new CustomerInfo();
                sel2.setCustomerCode(userInfo.getSupPlatformCode());
                sel2.setDeleteFlag("N");
                List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoList(sel2);
                if (CollUtil.isNotEmpty(customerInfos)) {
                    fdShippingAccount.setCustomerName(customerInfos.get(0).getCompanyName());
                }
            } else {
                fdShippingAccount.setCustomerNo("MP210800001");
                fdShippingAccount.setCustomerName("山东高速齐鲁号欧亚班列运营有限公司");
            }
            fdShippingAccount.setCreateTime(LocalDateTime.now());
            fdShippingAccount.setStatus("0");
            fdShippingAccount.setSubmitFlag("0");

            /*FdShippingAccountVO vo = new FdShippingAccountVO();
            BeanUtil.copyProperties(fdShippingAccount, vo);
            String provinceShiftNo = createProvinceShiftNo(vo);
            if (provinceShiftNo != null && !"".equals(provinceShiftNo)) {
                if (provinceShiftNo.contains("异常")) {
                    return new R<>(new Throwable(provinceShiftNo));
                }
            }
//            fdShippingAccount.setProvinceShiftNo(provinceShiftNo);
            //更新班次中升级班列号
            shifmanagementMapper.updateProvinceShiftNoNew(null, provinceShiftNo, fdShippingAccount.getShiftNo());*/

            fdShippingAccount.setDeleteFlag("N");
            fdShippingAccount.setAddWho(userInfo.getUserName());
            fdShippingAccount.setAddWhoName(userInfo.getRealName());
            fdShippingAccount.setAddTime(LocalDateTime.now());

            StringBuffer sb = new StringBuffer();
            String data = remoteAdminService.selectDictByType2("country_type");
            List<SysDictVo> countryList = JSONUtil.toList(JSONUtil.parseArray(data), SysDictVo.class);

            ContainerTypeData sel2 = new ContainerTypeData();
            sel2.setDeleteFlag("N");
            List<ContainerTypeData> containerTypeData = containerTypeDataMapper.selectContainerTypeDataList(sel2);
            BigDecimal jn = BigDecimal.ZERO;
            BigDecimal jw = BigDecimal.ZERO;
            BigDecimal jwy = BigDecimal.ZERO;
            //上报舱位数
            float num = 0f;
            //获取行数
            int lastRowNum = sheet.getLastRowNum();
            for (int i = 3; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                boolean blankFlag = CheckUtil.isRowEmpty(row);
                if (blankFlag) {
                    continue;
                }
                FdShippingAccoundetail fdShippingAccoundetail = new FdShippingAccoundetail();

                if (row.getCell(1) != null) {
                    row.getCell(1).setCellType(CellType.STRING);
                    String containerNo = row.getCell(1).getStringCellValue().trim();
                    boolean b = CheckUtil.verifyCntrCode(containerNo);
                    if (b) {
                        fdShippingAccoundetail.setContainerNumber(containerNo);
                    } else {
                        sb.append("行" + (i + 1) + "箱号格式错误：" + containerNo);
                    }
                } else {
                    sb.append("行" + (i + 1) + "箱号不能为空");
                }

                if (row.getCell(2) != null) {
                    try {
                        row.getCell(2).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(2).getStringCellValue();
                        if ("出口".equals(stringCellValue)) {
                            fdShippingAccoundetail.setIsRansit("E");
                        } else if ("进口".equals(stringCellValue)) {
                            fdShippingAccoundetail.setIsRansit("I");
                        } else if ("过境".equals(stringCellValue)) {
                            fdShippingAccoundetail.setIsRansit("P");
                        }
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "类型异常");
                    }
                } else {
                    sb.append("行" + (i + 1) + "类型不能为空");
                }

                if (row.getCell(3) != null) {
                    try {
                        row.getCell(3).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(3).getStringCellValue();
                        fdShippingAccoundetail.setOrgUnit(stringCellValue);
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "货源组织单位异常");
                    }
                } else {
                    sb.append("行" + (i + 1) + "货源组织单位不能为空");
                }

                if (row.getCell(4) != null) {
                    try {
                        row.getCell(4).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(4).getStringCellValue();
                        fdShippingAccoundetail.setConsignorName(stringCellValue);
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "收货人异常");
                    }
                } else {
                    sb.append("行" + (i + 1) + "收货人不能为空");
                }

                if (row.getCell(5) != null) {
                    try {
                        row.getCell(5).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(5).getStringCellValue();
                        fdShippingAccoundetail.setDestinationName(stringCellValue);
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "发站异常");
                    }
                } else {
                    sb.append("行" + (i + 1) + "发站不能为空");
                }

                if (row.getCell(6) != null) {
                    try {
                        row.getCell(6).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(6).getStringCellValue();
                        //目的国校验
                        if (CollUtil.isNotEmpty(countryList)) {
                            for (SysDictVo sysDictVo : countryList) {
                                if (sysDictVo.getName().equals(stringCellValue)) {
                                    fdShippingAccoundetail.setDestinationCountry(stringCellValue);
                                    break;
                                }
                            }
                            if (StrUtil.isEmpty(fdShippingAccoundetail.getDestinationCountry())) {
                                sb.append("行" + (i + 1) + "目的国无法匹配");
                            }
                        } else {
                            fdShippingAccoundetail.setDestinationCountry(stringCellValue);
                        }

                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "目的国异常");
                    }
                } else {
                    sb.append("行" + (i + 1) + "目的国不能为空");
                }

                if (row.getCell(7) != null) {
                    try {
                        row.getCell(7).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(7).getStringCellValue();
                        fdShippingAccoundetail.setDestination(stringCellValue);
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "到站异常");
                    }
                } else {
                    sb.append("行" + (i + 1) + "到站不能为空");
                }

                if (row.getCell(8) != null) {
                    try {
                        row.getCell(8).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(8).getStringCellValue();
                        fdShippingAccoundetail.setPortAgent(stringCellValue);
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "口岸代理异常");
                    }
                } else {
                    sb.append("行" + (i + 1) + "口岸代理不能为空");
                }

                if (row.getCell(9) != null) {
                    try {
                        row.getCell(9).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(9).getStringCellValue();
                        fdShippingAccoundetail.setGoodsName(stringCellValue);
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "品名异常");
                    }
                } else {
                    sb.append("行" + (i + 1) + "品名不能为空");
                }

                if (row.getCell(10) != null) {
                    try {
                        row.getCell(10).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(10).getStringCellValue();
                        if ("自备箱".equals(stringCellValue)) {
                            fdShippingAccoundetail.setContainerOwner("0");
                        } else if ("中铁箱".equals(stringCellValue)) {
                            fdShippingAccoundetail.setContainerOwner("1");
                        }
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "箱属异常");
                    }
                } else {
                    sb.append("行" + (i + 1) + "箱属不能为空");
                }

                if (row.getCell(11) != null) {
                    try {
                        row.getCell(11).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(11).getStringCellValue();
                        //箱型代码校验
                        if (CollUtil.isNotEmpty(containerTypeData)) {
                            for (ContainerTypeData typeData : containerTypeData) {
                                if (typeData.getContainerTypeCode().equals(stringCellValue)) {
                                    fdShippingAccoundetail.setContainerTypeCode(stringCellValue);
                                    fdShippingAccoundetail.setContainerTypeName(typeData.getContainerTypeName());
                                    fdShippingAccoundetail.setContainerType(typeData.getContainerTypeSize());
                                    if ("20".equals(fdShippingAccoundetail.getContainerType())) {
                                        num = num + 0.5f;
                                    } else if ("40".equals(fdShippingAccoundetail.getContainerType()) || "45".equals(fdShippingAccoundetail.getContainerType())) {
                                        num = num + 1f;
                                    }
                                    break;
                                }
                            }
                        }
                        if (StrUtil.isEmpty(fdShippingAccoundetail.getContainerTypeCode())) {
                            sb.append("行" + (i + 1) + "箱型代码无法匹配");
                        }
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "箱型代码异常");
                    }
                } else {
                    sb.append("行" + (i + 1) + "箱型代码不能为空");
                }

                if (row.getCell(12) != null) {
                    try {
                        row.getCell(12).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(12).getStringCellValue();
                        fdShippingAccoundetail.setGoodsNums(Integer.parseInt(stringCellValue));
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "件数异常");
                    }
                } else {
                    sb.append("行" + (i + 1) + "件数不能为空");
                }

                if (row.getCell(13) != null) {
                    try {
                        row.getCell(13).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(13).getStringCellValue();
                        fdShippingAccoundetail.setGoodsWeight(Double.parseDouble(stringCellValue));
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "货重异常");
                    }
                } else {
                    sb.append("行" + (i + 1) + "货重不能为空");
                }

                if (row.getCell(14) != null) {
                    try {
                        row.getCell(14).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(14).getStringCellValue();
                        fdShippingAccoundetail.setContainerWeight(stringCellValue);
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "箱重异常");
                    }
                } else {
                    sb.append("行" + (i + 1) + "箱重不能为空");
                }

                if (row.getCell(15) != null) {
                    try {
                        row.getCell(15).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(15).getStringCellValue();
                        if ("否".equals(stringCellValue)) {
                            fdShippingAccoundetail.setIsFull("0");
                        } else if ("是".equals(stringCellValue)) {
                            fdShippingAccoundetail.setIsFull("1");
                        }
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "是否全程异常");
                    }
                } else {
                    sb.append("行" + (i + 1) + "是否全程不能为空");
                }

                if (row.getCell(16) != null) {
                    try {
                        row.getCell(16).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(16).getStringCellValue();
                        if ("否".equals(stringCellValue)) {
                            fdShippingAccoundetail.setNonFerrous("0");
                        } else if ("是".equals(stringCellValue)) {
                            fdShippingAccoundetail.setNonFerrous("1");
                        }
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "有色金属异常");
                    }
                } else {
                    sb.append("行" + (i + 1) + "有色金属不能为空");
                }

                if (row.getCell(17) != null) {
                    try {
                        row.getCell(17).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(17).getStringCellValue();
                        fdShippingAccoundetail.setGoodsOwner(stringCellValue);
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "货主异常");
                    }
                }

                if (row.getCell(18) != null) {
                    try {
                        row.getCell(18).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(18).getStringCellValue();
                        fdShippingAccoundetail.setGoodsOrigin(stringCellValue);
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "境内货源地/目的地异常");
                    }
                }

                if (row.getCell(19) != null) {
                    try {
                        row.getCell(19).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(19).getStringCellValue();
                        fdShippingAccoundetail.setClearanceNumber(stringCellValue);
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "报关单号异常");
                    }
                }

                if (row.getCell(20) != null) {
                    try {
                        row.getCell(20).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(20).getStringCellValue();
                        fdShippingAccoundetail.setValueUsd(BigDecimal.valueOf(Double.parseDouble(stringCellValue)));
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "货值(美金)异常");
                    }
                }

                if (row.getCell(21) != null) {
                    try {
                        row.getCell(21).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(21).getStringCellValue();
                        fdShippingAccoundetail.setCustomsSeal(stringCellValue);
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "海关封异常");
                    }
                }

                if (row.getCell(22) != null) {
                    try {
                        row.getCell(22).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(22).getStringCellValue();
                        fdShippingAccoundetail.setTrainNumber(stringCellValue);
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "车号异常");
                    }
                }

                if (row.getCell(23) != null) {
                    try {
                        row.getCell(23).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(23).getStringCellValue();
                        fdShippingAccoundetail.setWaybillDemandNumber(stringCellValue);
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "运单需求号异常");
                    }
                }

                if (row.getCell(24) != null) {
                    try {
                        row.getCell(24).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(24).getStringCellValue();
                        fdShippingAccoundetail.setWaybillLnNumber(stringCellValue);
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "国联运单号异常");
                    }
                }

                if (row.getCell(25) != null) {
                    try {
                        row.getCell(25).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(25).getStringCellValue();
                        fdShippingAccoundetail.setDomesticFreight(BigDecimal.valueOf(Double.parseDouble(stringCellValue)));
                        jn = jn.add(fdShippingAccoundetail.getDomesticFreight());
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "境内运费(人民币)异常");
                    }
                } else {
                    sb.append("行" + (i + 1) + "境内运费(人民币)不能为空");
                }

                if (row.getCell(26) != null) {
                    try {
                        row.getCell(26).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(26).getStringCellValue();
                        fdShippingAccoundetail.setOverseasFreightCny(BigDecimal.valueOf(Double.parseDouble(stringCellValue)));
                        jw = jw.add(fdShippingAccoundetail.getOverseasFreightCny());
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "境外运费(人民币)异常");
                    }
                } else {
                    sb.append("行" + (i + 1) + "境外运费(人民币)不能为空");
                }

                if (row.getCell(27) != null) {
                    try {
                        row.getCell(27).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(27).getStringCellValue();
                        fdShippingAccoundetail.setOverseasFreightOc(BigDecimal.valueOf(Double.parseDouble(stringCellValue)));
                        jwy = jwy.add(fdShippingAccoundetail.getOverseasFreightOc());
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "境外运费(原币)异常");
                    }
                } else {
                    sb.append("行" + (i + 1) + "境外运费(原币)不能为空");
                }

                if (row.getCell(28) != null) {
                    try {
                        row.getCell(28).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(28).getStringCellValue();
                        fdShippingAccoundetail.setExchangeRate(BigDecimal.valueOf(Double.parseDouble(stringCellValue)));
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "境外汇率异常");
                    }
                }

                if (row.getCell(29) != null) {
                    try {
                        row.getCell(29).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(29).getStringCellValue();
                        fdShippingAccoundetail.setMonetaryType(stringCellValue);
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "境外币种异常");
                    }
                }

                if (row.getCell(30) != null) {
                    try {
                        row.getCell(30).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(30).getStringCellValue();
                        fdShippingAccoundetail.setSubsidyStandards(stringCellValue);
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "补贴标准异常");
                    }
                }

                if (row.getCell(31) != null) {
                    try {
                        row.getCell(31).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(31).getStringCellValue();
                        fdShippingAccoundetail.setSubsidyAmount(BigDecimal.valueOf(Double.parseDouble(stringCellValue)));
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "补贴金额异常");
                    }
                }

                if (row.getCell(32) != null) {
                    try {
                        row.getCell(32).setCellType(CellType.STRING);
                        String stringCellValue = row.getCell(32).getStringCellValue();
                        fdShippingAccoundetail.setRemarks(stringCellValue);
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "箱备注异常");
                    }
                }
                fdShippingAccoundetail.setAccountCode(fdShippingAccount.getAccountCode());
                fdShippingAccoundetail.setSource("export");
                fdShippingAccoundetail.setDeleteFlag("N");
                fdShippingAccoundetail.setAddWho(userInfo.getUserName());
                fdShippingAccoundetail.setAddWhoName(userInfo.getRealName());
                fdShippingAccoundetail.setAddTime(LocalDateTime.now());
                list.add(fdShippingAccoundetail);
            }

            if (sb.length() > 0) {
                throw new RuntimeException(sb.toString());
            }
            //该班次已用舱位数量
            SpaceOccupy sp = new SpaceOccupy();
            sp.setShiftNo(fdShippingAccount.getShiftNo());
            Float occupyNums = spaceOccupyMapper.selectOccupyNums(sp);
            if (occupyNums == null || occupyNums < 0.5) {
                occupyNums = (float) 0;
            }
            Float usedSpNumsUndShf = occupyNums;
            Float trueSpaceNums = 0.00f;
            Float trueSpaceNumsSp = 0.00f;
            if (shifmanagements.get(0).getNumOfTruePositions() != null && !"".equals(shifmanagements.get(0).getNumOfTruePositions())) {
                trueSpaceNums = Float.valueOf(shifmanagements.get(0).getNumOfTruePositions());//班次发布实际舱位
            }
            if (shifmanagements.get(0).getNumOfReservePositions() != null && !"".equals(shifmanagements.get(0).getNumOfReservePositions())) {
                trueSpaceNumsSp = Float.valueOf(shifmanagements.get(0).getNumOfReservePositions()); //班次发布备用舱位
            }
            //该班次的总舱位数（实际+备用）
            Float sumSpace = trueSpaceNums + trueSpaceNumsSp;
            //总仓位-已用=剩余舱位数 （实际+备用）
            Float spaceLeft = sumSpace - usedSpNumsUndShf;
            //上报舱位数若大于剩余舱位数,返回提示信息
            if (num > spaceLeft) {
                throw new RuntimeException("剩余舱位数量不足，请重新确认");
            } else {
                //剩余舱位数满足本次提交，则更新舱位占用表，剩余舱位（实际+备用）数据
                SpaceOccupy so = new SpaceOccupy();
                //当前班次已用舱位数
                so.setSpaceNums(String.valueOf(num));
                so.setOrderNo(fdShippingAccount.getAccountCode());
                so.setShiftNo(fdShippingAccount.getShiftNo());
                so.setAddTime(LocalDateTime.now());
                so.setAddWhoName(userInfo.getUserName());
                so.setAddWho(userInfo.getRealName());
                spaceOccupyMapper.insertSpaceOccupy(so);
            }

            FdShippingAccount sel3 = new FdShippingAccount();
            sel3.setShiftNo(fdShippingAccount.getShiftNo());
            sel3.setPlatformCode(fdShippingAccount.getPlatformCode());
            sel3.setDeleteFlag("N");
            List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountList(sel3);
            if (CollUtil.isNotEmpty(fdShippingAccounts)) {
                FdShippingAccount old = fdShippingAccounts.get(0);
                if ("0".equals(old.getStatus()) || "3".equals(old.getStatus())) {
                    FdShippingAccoundetail sel4 = new FdShippingAccoundetail();
                    sel4.setAccountCode(old.getAccountCode());
                    sel4.setDeleteFlag("N");
                    List<FdShippingAccoundetail> fdShippingAccoundetails = fdShippingAccoundetailMapper.selectFdShippingAccoundetailList(sel4);
                    if (CollUtil.isNotEmpty(fdShippingAccoundetails)) {
                        for (FdShippingAccoundetail detail : fdShippingAccoundetails) {
                            FdShippingAccoundetail delobj = new FdShippingAccoundetail();
                            delobj.setRowId(detail.getRowId());
                            delobj.setDeleteFlag("Y");
                            delobj.setDeleteWho(userInfo.getUserName());
                            delobj.setDeleteWhoName(userInfo.getRealName());
                            delobj.setDeleteTime(LocalDateTime.now());
                            fdShippingAccoundetailMapper.updateFdShippingAccoundetail(delobj);
                        }
                    }
                    FdShippingAccount delobj2 = new FdShippingAccount();
                    delobj2.setRowId(old.getRowId());
                    delobj2.setDeleteFlag("Y");
                    delobj2.setDeleteWho(userInfo.getUserName());
                    delobj2.setDeleteWhoName(userInfo.getRealName());
                    delobj2.setDeleteTime(LocalDateTime.now());
                    fdShippingAccountMapper.updateFdShippingAccount(delobj2);
                } else {
                    throw new RuntimeException("当前平台已在该班次发起台账审核，无法再次导入");
                }
            }
            fdShippingAccount.setDomesticFreight(jn);
            fdShippingAccount.setOverseasFreightCny(jw);
            fdShippingAccount.setOverseasFreightOc(jwy);
            fdShippingAccount.setAmount(jn.add(jw));
            fdShippingAccountMapper.insertFdShippingAccount(fdShippingAccount);
            if (CollUtil.isNotEmpty(list)) {
                for (FdShippingAccoundetail fdShippingAccoundetail : list) {
                    fdShippingAccoundetailMapper.insert(fdShippingAccoundetail);
                }
            } else {
                throw new RuntimeException("箱详细信息未填写，不允许导入");
            }
        } else {
            return new R<>(new Throwable("班次号不能为空！"));
        }


        return new R<>(0, Boolean.TRUE, null, "导入完成！");
    }

    @Override
    public R selectShipNum() {
        Map<String, Object> map = new HashMap<>();
        // 使用DateTimeFormatter格式化日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 获取当前日期
        LocalDate today = LocalDate.now();
        String todayStr = today.format(formatter);

        FdShippingNumDTO sel = new FdShippingNumDTO();
        PlatformOverviewVariables selPov = new PlatformOverviewVariables();
        if ("1".equals(SecurityUtils.getUserInfo().getPlatformLevel())) {
            sel.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
            selPov.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        }
        sel.setDateEnd(todayStr);
        List<FdShippingNumDTO> totalList = fdShippingAccountMapper.selectShipNum(sel);
        BigDecimal totalNum = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(totalList)) {
            for (FdShippingNumDTO dto : totalList) {
                totalNum = totalNum.add(dto.getNum().divide(BigDecimal.valueOf(41), 0, RoundingMode.HALF_UP));
            }
        }
        BigDecimal totalNumPov = platformOverviewVariablesMapper.getTotalNum(todayStr, sel.getPlatformCode());
        if (totalNumPov != null) {
            totalNum = totalNum.add(totalNumPov);
        }
        map.put("totalNum", totalNum);

        sel.setDateStr(todayStr);
        List<FdShippingNumDTO> todayList = fdShippingAccountMapper.selectShipNum(sel);
        selPov.setDateStr(todayStr);
        List<PlatformOverviewVariables> todayPovList = platformOverviewVariablesMapper.getTotalWithDate(selPov);

        //昨天
        String yesterday = today.minusDays(1).format(formatter);
        sel.setDateStr(yesterday);
        List<FdShippingNumDTO> yesterdayList = fdShippingAccountMapper.selectShipNum(sel);

        putMap(map, todayList, yesterdayList, "todayNum", "todayCompared", todayPovList);
        sel.setDateStr(null);

        // 获取本周的开始日期（周一）
        LocalDate startOfWeek = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        sel.setDateStart(startOfWeek.format(formatter));
        sel.setDateEnd(todayStr);
        List<FdShippingNumDTO> weekList = fdShippingAccountMapper.selectShipNum(sel);
        selPov.setDateStart(startOfWeek.format(formatter));
        selPov.setDateEnd(todayStr);
        List<PlatformOverviewVariables> weekPovList = platformOverviewVariablesMapper.getTotalWithDate(selPov);

        // 上周同期数据查询
        LocalDate startOfLastWeek = startOfWeek.minusWeeks(1);
        LocalDate endOfLastWeek = today.minusWeeks(1);
        sel.setDateStart(startOfLastWeek.format(formatter));
        sel.setDateEnd(endOfLastWeek.format(formatter));
        List<FdShippingNumDTO> lastWeekList = fdShippingAccountMapper.selectShipNum(sel);

        // 调用putMap方法，注意参数名改为week相关的
        putMap(map, weekList, lastWeekList, "weekNum", "weekCompared", weekPovList);

        // 当前月的开始日期 (每月1号)
        LocalDate startOfCurrentMonth = today.withDayOfMonth(1);
        sel.setDateStart(startOfCurrentMonth.format(formatter));
        sel.setDateEnd(todayStr);
        log.info(sel.getDateEnd());
        List<FdShippingNumDTO> monthList = fdShippingAccountMapper.selectShipNum(sel);

        selPov.setDateStr(null);
        // 当前月的开始日期 (每月1号)
        selPov.setDateStart(startOfCurrentMonth.format(formatter));
        selPov.setDateEnd(todayStr);
        List<PlatformOverviewVariables> monthPovList = platformOverviewVariablesMapper.getTotalWithDate(selPov);

        //一月前的
        sel.setDateStart(startOfCurrentMonth.minusMonths(1).format(formatter));
        sel.setDateEnd(today.minusMonths(1).format(formatter));
        log.info(sel.getDateEnd());
        List<FdShippingNumDTO> monthList2 = fdShippingAccountMapper.selectShipNum(sel);
        putMap(map, monthList, monthList2, "monthNum", "monthCompared", monthPovList);

        // 当前年的开始日期 (1月1号)
        LocalDate startOfCurrentYear = today.withDayOfYear(1);
        sel.setDateStart(startOfCurrentYear.format(formatter));
        sel.setDateEnd(todayStr);
        log.info(sel.getDateEnd());
        List<FdShippingNumDTO> yearList = fdShippingAccountMapper.selectShipNum(sel);

        selPov.setDateStart(startOfCurrentYear.format(formatter));
        selPov.setDateEnd(todayStr);
        List<PlatformOverviewVariables> yearPovList = platformOverviewVariablesMapper.getTotalWithDate(selPov);

        //一年前
        sel.setDateStart(startOfCurrentYear.minusYears(1).format(formatter));
        sel.setDateEnd(today.minusYears(1).format(formatter));
        log.info(sel.getDateEnd());
        List<FdShippingNumDTO> yearList2 = fdShippingAccountMapper.selectShipNum(sel);
        putMap(map, yearList, yearList2, "yearNum", "yearCompared", yearPovList);
        return R.success(map);
    }


    private static void putMap(Map<String, Object> map, List<FdShippingNumDTO> newList, List<FdShippingNumDTO> oldList, String day, String compared, List<PlatformOverviewVariables> todayPovList) {
        //折算列计算公式：按进出口类型、线路、方向、口岸、发/到站（去程按发站，回程按到站）分类后以实际发运车数/41，四舍五入取整。
        BigDecimal newNum = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(newList)) {
            for (FdShippingNumDTO dto : newList) {
                newNum = newNum.add(dto.getNum().divide(BigDecimal.valueOf(41), 0, RoundingMode.HALF_UP));
            }
        }
        //数据修正
        if (CollUtil.isNotEmpty(todayPovList)) {
            for (PlatformOverviewVariables platformOverviewVariables : todayPovList) {
                if ("todayNum".equals(day)) {
                    newNum = newNum.add(platformOverviewVariables.getDayNum());
                } else if ("monthNum".equals(day)) {
                    newNum = newNum.add(platformOverviewVariables.getMonthNum());
                } else if ("yearNum".equals(day)) {
                    newNum = newNum.add(platformOverviewVariables.getYearNum());
                }
            }
        }
        map.put(day, newNum);

        BigDecimal oldNum = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(oldList)) {
            for (FdShippingNumDTO dto : oldList) {
                oldNum = oldNum.add(dto.getNum().divide(BigDecimal.valueOf(41), 0, BigDecimal.ROUND_HALF_DOWN));
            }
        }
        //今日对比
        if (oldNum.compareTo(BigDecimal.ZERO) != 0) {
            map.put(compared, (newNum.subtract(oldNum)).multiply(BigDecimal.valueOf(100)).divide(oldNum, 2, BigDecimal.ROUND_HALF_DOWN));
        } else {
            if (newNum.compareTo(BigDecimal.ZERO) != 0) {
                map.put(compared, 100);
            } else {
                map.put(compared, 0);
            }
        }
    }

    @Override
    public R selectShipNumDetail(FdShippingNumDTO fdShippingNumDTO) {
        Map<String, Object> map = new HashMap<>();
        // 使用DateTimeFormatter格式化日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 获取当前日期
        LocalDate today = LocalDate.now();
        String todayStr = today.format(formatter);
        if (StrUtil.isBlank(fdShippingNumDTO.getType())) {
            return R.error("缺少类型参数！");
        }


        FdShippingNumDTO sel = new FdShippingNumDTO();
        PlatformOverviewVariables selPov = new PlatformOverviewVariables();

        if ("1".equals(SecurityUtils.getUserInfo().getPlatformLevel())) {
            sel.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
            selPov.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        }
        if ("day".equals(fdShippingNumDTO.getType())) {
            sel.setDateStr(todayStr);
            selPov.setDateStr(todayStr);
        }else if ("week".equals(fdShippingNumDTO.getType())) {  // 新增周类型
            // 计算本周周一（周一作为周开始）
            LocalDate startOfWeek = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
            sel.setDateStart(startOfWeek.format(formatter));
            sel.setDateEnd(todayStr);
            selPov.setDateStart(startOfWeek.format(formatter));
            selPov.setDateEnd(todayStr);
        } else if ("month".equals(fdShippingNumDTO.getType())) {
            // 当前月的开始日期 (每月1号)
            LocalDate startOfCurrentMonth = today.withDayOfMonth(1);
            sel.setDateStart(startOfCurrentMonth.format(formatter));
            sel.setDateEnd(todayStr);
            selPov.setDateStart(startOfCurrentMonth.format(formatter));
            selPov.setDateEnd(todayStr);
        } else if ("year".equals(fdShippingNumDTO.getType())) {
            LocalDate startOfCurrentYear = today.withDayOfYear(1);
            sel.setDateStart(startOfCurrentYear.format(formatter));
            sel.setDateEnd(todayStr);
            selPov.setDateStart(startOfCurrentYear.format(formatter));
            selPov.setDateEnd(todayStr);
        } else {
            sel.setDateEnd(todayStr);
            selPov.setDateEnd(todayStr);
        }

        BigDecimal returnNum = BigDecimal.ZERO;
        BigDecimal goNum = BigDecimal.ZERO;
        int returnTeu = 0;
        int goTeu = 0;
        BigDecimal zo = BigDecimal.ZERO;
        BigDecimal zy = BigDecimal.ZERO;
        BigDecimal ze = BigDecimal.ZERO;
        BigDecimal zm = BigDecimal.ZERO;
        BigDecimal ny = BigDecimal.ZERO;
        BigDecimal qt = BigDecimal.ZERO;
        Map<String, Object> city = new HashMap<>();
        city.put("青岛市", BigDecimal.ZERO);
        city.put("济南市", BigDecimal.ZERO);
        city.put("临沂市", BigDecimal.ZERO);
        city.put("烟台市", BigDecimal.ZERO);
        List<BigDecimal> linePercent = new ArrayList<>();
        List<FdShippingNumDTO> list = fdShippingAccountMapper.selectShipNum(sel);

        List<PlatformOverviewVariables> povList = platformOverviewVariablesMapper.getTotalWithDate(selPov);

        if (CollUtil.isNotEmpty(list)) {
            for (FdShippingNumDTO dto : list) {
                BigDecimal num = dto.getNum().divide(BigDecimal.valueOf(41), 0, RoundingMode.HALF_UP);
                if ("G".equals(dto.getTrip())) {
                    goNum = goNum.add(num);
                    goTeu = goTeu + dto.getTeu();
                } else {
                    returnNum = returnNum.add(num);
                    returnTeu = returnTeu + dto.getTeu();
                }
                if (city.containsKey(dto.getCity())) {
                    BigDecimal decimal = num.add(BigDecimal.valueOf(Double.parseDouble(String.valueOf(city.get(dto.getCity())))));
                    city.put(dto.getCity(), decimal.intValue());
                } else {
                    city.put(dto.getCity(), num);
                }
                if ("zo".equals(dto.getAreaCode())) {
                    zo = zo.add(num);
                } else if ("zy".equals(dto.getAreaCode())) {
                    zy = zy.add(num);
                } else if ("ze".equals(dto.getAreaCode())) {
                    ze = ze.add(num);
                } else if ("zm".equals(dto.getAreaCode())) {
                    zm = zm.add(num);
                } else if ("ny".equals(dto.getAreaCode())) {
                    ny = ny.add(num);
                } else {
                    qt = qt.add(num);
                }
            }
        }
        if (CollUtil.isNotEmpty(povList)) {
            for (PlatformOverviewVariables pov : povList) {
                BigDecimal num = BigDecimal.ZERO;
                BigDecimal teu = BigDecimal.ZERO;
                if ("day".equals(fdShippingNumDTO.getType())) {
                    num = pov.getDayNum();
                    teu = pov.getDayTeu();
                }else if ("week".equals(fdShippingNumDTO.getType())) {  // 新增周处理
                    // 使用日数据累加计算周数据
                    num = pov.getDayNum();
                    teu = pov.getDayTeu();
                }else if ("month".equals(fdShippingNumDTO.getType())) {
                    num = pov.getMonthNum();
                    teu = pov.getMonthTeu();
                } else if ("year".equals(fdShippingNumDTO.getType())) {
                    num = pov.getYearNum();
                    teu = pov.getYearTeu();
                }
                if ("G".equals(pov.getTrip())) {
                    goNum = goNum.add(num);
                    goTeu = goTeu + teu.intValue();
                } else {
                    returnNum = returnNum.add(num);
                    returnTeu = returnTeu + teu.intValue();
                }
                if (city.containsKey(pov.getCity())) {
                    BigDecimal decimal = num.add(BigDecimal.valueOf(Double.parseDouble(String.valueOf(city.get(pov.getCity())))));
                    city.put(pov.getCity(), decimal.intValue());
                } else {
                    city.put(pov.getCity(), num);
                }
                if ("zo".equals(pov.getAreaCode())) {
                    zo = zo.add(num);
                } else if ("zy".equals(pov.getAreaCode())) {
                    zy = zy.add(num);
                } else if ("ze".equals(pov.getAreaCode())) {
                    ze = ze.add(num);
                } else if ("zm".equals(pov.getAreaCode())) {
                    zm = zm.add(num);
                } else if ("ny".equals(pov.getAreaCode())) {
                    ny = ny.add(num);
                } else {
                    qt = qt.add(num);
                }
            }
        }
        BigDecimal total = zo.add(zy).add(ze).add(zm).add(ny).add(qt);
        if (total.compareTo(BigDecimal.ZERO) != 0) {
            linePercent.add(zo.multiply(BigDecimal.valueOf(100)).divide(total, 2, BigDecimal.ROUND_HALF_DOWN));
            linePercent.add(zy.multiply(BigDecimal.valueOf(100)).divide(total, 2, BigDecimal.ROUND_HALF_DOWN));
            linePercent.add(ze.multiply(BigDecimal.valueOf(100)).divide(total, 2, BigDecimal.ROUND_HALF_DOWN));
            linePercent.add(zm.multiply(BigDecimal.valueOf(100)).divide(total, 2, BigDecimal.ROUND_HALF_DOWN));
            linePercent.add(ny.multiply(BigDecimal.valueOf(100)).divide(total, 2, BigDecimal.ROUND_HALF_DOWN));
            linePercent.add(qt.multiply(BigDecimal.valueOf(100)).divide(total, 2, BigDecimal.ROUND_HALF_DOWN));
        } else {
            linePercent = new ArrayList<>(Arrays.asList(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO));
        }

        List<String> lineName = new ArrayList<>(Arrays.asList("中欧", "中亚", "中俄", "中蒙", "南亚", "其他"));
        List<BigDecimal> lineNum = new ArrayList<>(Arrays.asList(zo, zy, ze, zm, ny, qt));
        map.put("returnNum", returnNum);
        map.put("goNum", goNum);
        map.put("returnTeu", returnTeu);
        map.put("goTeu", goTeu);
        map.put("city", city);
        map.put("lineName", lineName);
        map.put("lineNum", lineNum);
        map.put("linePercent", linePercent);
        return R.success(map);
    }

    /*@Override
    public R selectShipNumHalfYear(){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy-MM");
        Map<String,Object> map = new HashMap<>();
        List<String> x = new ArrayList<>();
        List<Integer> y = new ArrayList<>();
        // 获取当前日期
        LocalDate today = LocalDate.now();
        LocalDate startOfCurrentMonth = today.withDayOfMonth(1);
        LocalDate startMonth = startOfCurrentMonth.minusMonths(5);
        FdShippingNumDTO sel = new FdShippingNumDTO();
        sel.setDateStart(startMonth.format(formatter));
        sel.setDateEnd(today.format(formatter));
        List<FdShippingNumDTO> list = fdShippingAccountMapper.selectShipNumWithDate(sel);
        if(CollUtil.isNotEmpty(list)){
            for(int i = 5;i >= 0;i--){
                BigDecimal num = BigDecimal.ZERO;
                String month = startOfCurrentMonth.minusMonths(i).format(formatter2);
                for (FdShippingNumDTO dto:list
                     ) {
                    if(month.equals(dto.getPlanShipTime())){
                        num = num.add(dto.getNum());
                    }
                }
                Integer value = num.divide(BigDecimal.valueOf(41), 0, BigDecimal.ROUND_HALF_DOWN).intValue();
                x.add(month);
                y.add(value);
            }
        }
        map.put("x",x);
        map.put("y",y);
        return R.success(map);
    }*/

    @Override
    public R selectShipNumHalfYear() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy-MM");
        Map<String, Object> map = new HashMap<>();
        List<String> x = new ArrayList<>();
        List<Integer> y = new ArrayList<>();
        // 获取当前日期
        LocalDate today = LocalDate.now();
        LocalDate startOfCurrentMonth = today.withDayOfMonth(1);

        FdShippingNumDTO sel = new FdShippingNumDTO();
        if ("1".equals(SecurityUtils.getUserInfo().getPlatformLevel())) {
            sel.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        }
        for (int i = 5; i >= 0; i--) {
            LocalDate startMonth = startOfCurrentMonth.minusMonths(i);
            LocalDate lastDayOfMonth = startMonth.with(TemporalAdjusters.lastDayOfMonth());
            if (i == 0) {
                sel.setDateStart(startOfCurrentMonth.format(formatter));
                sel.setDateEnd(today.format(formatter));
            } else {
                sel.setDateStart(startMonth.format(formatter));
                sel.setDateEnd(lastDayOfMonth.format(formatter));
            }
            List<FdShippingNumDTO> list = fdShippingAccountMapper.selectShipNum(sel);
            BigDecimal num = BigDecimal.ZERO;
            if (CollUtil.isNotEmpty(list)) {
                for (FdShippingNumDTO dto : list) {
                    num = num.add(dto.getNum().divide(BigDecimal.valueOf(41), 0, BigDecimal.ROUND_HALF_DOWN));
                }
            }

            String month = startOfCurrentMonth.minusMonths(i).format(formatter2);
            Integer value = num.intValue();
            x.add(month);
            y.add(value);
        }

        map.put("x", x);
        map.put("y", y);
        return R.success(map);
    }

    @Override
    public R selectMonthCity() throws Exception {
        Map<String, Object> map = new HashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate today = LocalDate.now();
        LocalDate startOfCurrentMonth = today.withDayOfMonth(1);

        FdShippingNumDTO sel = new FdShippingNumDTO();
        sel.setDateStart(startOfCurrentMonth.format(formatter));
        sel.setDateEnd(today.format(formatter));
        List<FdShippingMonthCityDTO> list = fdShippingAccountMapper.selectMonthCity(sel);

        ObjectMapper objectMapper = new ObjectMapper();
        // 将 JSON 字符串转换为 Map,获取固定的七个平台简称
        Map<String, String> city = objectMapper.readValue(jygl, new TypeReference<Map<String, String>>() {
        });
        if (CollUtil.isNotEmpty(city)) {
            city.forEach((key, value) -> {
                map.put(String.valueOf(value), new FdShippingMonthCityDTO());
            });
        }
        if (CollUtil.isNotEmpty(list)) {
            for (FdShippingMonthCityDTO dto : list) {
                String s = String.valueOf(city.get(dto.getPlatformCode()));
                dto.setReceiveAmount(dto.getReceiveAmount().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
                dto.setPayAmount(dto.getPayAmount().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
                dto.setGrossAmount(dto.getReceiveAmount().subtract(dto.getPayAmount()));
                map.put(s, dto);
            }
        }

        return R.success(map);
    }

    @Override
    public R selectMonthThisCity() throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("中欧", new FdShippingMonthCityDTO());
        map.put("中亚", new FdShippingMonthCityDTO());
        map.put("中俄", new FdShippingMonthCityDTO());
        map.put("中蒙", new FdShippingMonthCityDTO());
        map.put("南亚", new FdShippingMonthCityDTO());
        map.put("其他", new FdShippingMonthCityDTO());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate today = LocalDate.now();
        LocalDate startOfCurrentMonth = today.withDayOfMonth(1);

        FdShippingNumDTO sel = new FdShippingNumDTO();
        sel.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        sel.setDateStart(startOfCurrentMonth.format(formatter));
        sel.setDateEnd(today.format(formatter));
        List<FdShippingMonthCityDTO> list = fdShippingAccountMapper.selectMonthThisCity(sel);
        if (CollUtil.isNotEmpty(list)) {
            for (FdShippingMonthCityDTO dto : list) {
                dto.setReceiveAmount(dto.getReceiveAmount().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
                dto.setPayAmount(dto.getPayAmount().divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_DOWN));
                dto.setGrossAmount(dto.getReceiveAmount().subtract(dto.getPayAmount()));
                if ("zo".equals(dto.getAreaCode())) {
                    map.put("中欧", dto);
                } else if ("zy".equals(dto.getAreaCode())) {
                    map.put("中亚", dto);
                } else if ("ze".equals(dto.getAreaCode())) {
                    map.put("中俄", dto);
                } else if ("zm".equals(dto.getAreaCode())) {
                    map.put("中蒙", dto);
                } else if ("ny".equals(dto.getAreaCode())) {
                    map.put("南亚", dto);
                } else {
                    map.put("其他", dto);
                }
            }
        }

        return R.success(map);
    }

    @Override
    public void costLedgerExport(FdShippingAccount fdShippingAccount, HttpServletResponse response) throws Exception {
        Shifmanagement sel = new Shifmanagement();
        sel.setShiftId(fdShippingAccount.getShiftNo());
        sel.setPlatformCode(fdShippingAccount.getPlatformCode());
        sel.setDeleteFlag("N");
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
        String shiftName = "";
        if (CollUtil.isNotEmpty(shifmanagements)) {
            shiftName = shifmanagements.get(0).getShiftName();
            if ("G".equals(shifmanagements.get(0).getTrip())) {
                shiftName = shiftName + "去程发运明细";
            } else {
                shiftName = shiftName + "回程发运明细";
            }
        }
        fdShippingAccount.setDeleteFlag("N");
        List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountList(fdShippingAccount);
        String portStation = "";
        if (CollUtil.isNotEmpty(fdShippingAccounts)) {
            portStation = fdShippingAccounts.get(0).getPortStation();
        }
        List<FdShippingAccoundetail> fdShippingAccoundetails = fdShippingAccoundetailMapper.selectFdShippingAccoundetailByList(fdShippingAccount.getShiftNo(), fdShippingAccount.getPlatformCode());

        String templateFileName = "costLedgerExport.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)) {
            templateFileName = linuxPath + templateFileName;
        } else {
            templateFileName = windowsPath + templateFileName;
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 使用 UTF-8 编码处理文件名，确保中文文件名在浏览器中正确显示
        String encodedFileName = URLEncoder.encode(shiftName + "成本台账.xlsx", "UTF-8");
        encodedFileName = encodedFileName.replaceAll("\\+", "%20");
        response.setHeader("Content-Disposition", "attachment;filename=\"" + encodedFileName + "\"");
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");

        InputStream templateFile = Files.newInputStream(Paths.get(templateFileName));
        XSSFWorkbook workbook = new XSSFWorkbook(templateFile);
        // 创建一个数字格式的 CellStyle
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        CellStyle cellStyle2 = workbook.createCellStyle();
        cellStyle2.setBorderBottom(BorderStyle.THIN);
        cellStyle2.setBorderLeft(BorderStyle.THIN);
        cellStyle2.setBorderTop(BorderStyle.THIN);
        cellStyle2.setBorderRight(BorderStyle.THIN);
        cellStyle2.setAlignment(HorizontalAlignment.CENTER);
        cellStyle2.setFont(font);

        XSSFSheet sheet = workbook.getSheetAt(0);
        XSSFRow row0 = sheet.getRow(0);
        XSSFCell cell = row0.createCell(0);
        cell.setCellValue(shiftName);
        cell.setCellStyle(cellStyle2);
        XSSFRow row1 = sheet.getRow(1);
        if (CollUtil.isNotEmpty(shifmanagements)) {
            XSSFCell cell11 = row1.createCell(2);
            cell11.setCellValue(shifmanagements.get(0).getProvinceShiftNo());
            cell11.setCellStyle(cellStyle2);
        }
        if (CollUtil.isNotEmpty(fdShippingAccounts)) {
            XSSFCell cell13 = row1.createCell(4);
            cell13.setCellValue(fdShippingAccounts.get(0).getCustomerName());
            cell13.setCellStyle(cellStyle2);
        }
        setList(shifmanagements, portStation, fdShippingAccoundetails, cellStyle, cellStyle2, sheet);
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }

    private static void setList(List<Shifmanagement> shifmanagements, String portStation, List<FdShippingAccoundetail> fdShippingAccoundetails, CellStyle cellStyle, CellStyle cellStyle2, XSSFSheet sheet) {
        int rowIndex = 4;
        BigDecimal overseasFreightCny = BigDecimal.ZERO;
        BigDecimal domesticFreight = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(fdShippingAccoundetails)) {
            for (FdShippingAccoundetail fdShippingAccoundetail : fdShippingAccoundetails) {
                XSSFRow rowCell = sheet.createRow(rowIndex);
                XSSFCell cell0 = rowCell.createCell(0);
                cell0.setCellValue(rowIndex - 3);
                cell0.setCellStyle(cellStyle);
                XSSFCell cell1 = rowCell.createCell(1);
                if (CollUtil.isNotEmpty(shifmanagements)) {
                    if ("R".equals(shifmanagements.get(0).getTrip())) {
                        cell1.setCellValue(fdShippingAccoundetail.getDestinationName());
                    } else {
                        cell1.setCellValue(fdShippingAccoundetail.getDestination());
                    }
                }
                cell1.setCellStyle(cellStyle);
                XSSFCell cell2 = rowCell.createCell(2);

                cell2.setCellValue(portStation);
                cell2.setCellStyle(cellStyle);
                XSSFCell cell3 = rowCell.createCell(3);
                cell3.setCellValue(fdShippingAccoundetail.getContainerType());
                cell3.setCellStyle(cellStyle);
                XSSFCell cell4 = rowCell.createCell(4);
                if ("0".equals(fdShippingAccoundetail.getContainerOwner())) {
                    cell4.setCellValue("自备箱");
                } else if ("1".equals(fdShippingAccoundetail.getContainerOwner())) {
                    cell4.setCellValue("中铁箱");
                }
                cell4.setCellStyle(cellStyle);
                XSSFCell cell5 = rowCell.createCell(5);
                cell5.setCellValue(fdShippingAccoundetail.getContainerNumber());
                cell5.setCellStyle(cellStyle);
                XSSFCell cell6 = rowCell.createCell(6);
                cell6.setCellValue(fdShippingAccoundetail.getDomesticFreight() != null ? Double.parseDouble(String.format("%.2f", fdShippingAccoundetail.getDomesticFreight())) : 0);
                cell6.setCellStyle(cellStyle);
                if (fdShippingAccoundetail.getDomesticFreight() != null) {
                    domesticFreight = domesticFreight.add(fdShippingAccoundetail.getDomesticFreight());
                }
                XSSFCell cell7 = rowCell.createCell(7);
                cell7.setCellValue(fdShippingAccoundetail.getOverseasFreightCny() != null ? Double.parseDouble(String.format("%.2f", fdShippingAccoundetail.getOverseasFreightCny())) : 0);
                cell7.setCellStyle(cellStyle);
                if (fdShippingAccoundetail.getOverseasFreightCny() != null) {
                    overseasFreightCny = overseasFreightCny.add(fdShippingAccoundetail.getOverseasFreightCny());
                }
                rowIndex++;
            }
        }
        XSSFRow rowCell = sheet.createRow(rowIndex);
        XSSFCell cell0 = rowCell.createCell(0);
        cell0.setCellValue("小 计");
        cell0.setCellStyle(cellStyle2);
        for (int i = 1; i < 6; i++) {
            XSSFCell celli = rowCell.createCell(i);
            celli.setCellStyle(cellStyle2);
        }
        XSSFCell cell6 = rowCell.createCell(6);
        cell6.setCellValue(Double.parseDouble(String.format("%.2f", domesticFreight)));
        cell6.setCellStyle(cellStyle2);
        XSSFCell cell7 = rowCell.createCell(7);
        cell7.setCellValue(Double.parseDouble(String.format("%.2f", overseasFreightCny)));
        cell7.setCellStyle(cellStyle2);
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 5));
        rowIndex++;

        XSSFRow rowCell2 = sheet.createRow(rowIndex);
        XSSFCell cell20 = rowCell2.createCell(0);
        cell20.setCellValue("合 计");
        cell20.setCellStyle(cellStyle2);
        for (int i = 1; i < 8; i++) {
            XSSFCell celli = rowCell2.createCell(i);
            if (i == 6) {
                celli.setCellValue(Double.parseDouble(String.format("%.2f", domesticFreight.add(overseasFreightCny))));
            }
            celli.setCellStyle(cellStyle2);
        }
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 5));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 6, 7));
    }

}
