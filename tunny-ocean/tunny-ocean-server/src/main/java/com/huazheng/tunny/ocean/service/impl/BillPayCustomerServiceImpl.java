package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.BillCostDetailDTO;
import com.huazheng.tunny.ocean.api.entity.BillPayCustomer;
import com.huazheng.tunny.ocean.api.entity.BillPayCustomerSub;
import com.huazheng.tunny.ocean.api.vo.BillCostDetailVO;
import com.huazheng.tunny.ocean.api.vo.BillDealWithCityAndCostVO;
import com.huazheng.tunny.ocean.api.vo.BillIncomeCityVO;
import com.huazheng.tunny.ocean.mapper.BillPayCustomerMapper;
import com.huazheng.tunny.ocean.mapper.BillPayCustomerSubMapper;
import com.huazheng.tunny.ocean.mapper.ShifmanagementMapper;
import com.huazheng.tunny.ocean.service.BillPayCustomerService;
import com.huazheng.tunny.ocean.service.BillPayCustomerSubService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("billPayCustomerService")
@Slf4j
public class BillPayCustomerServiceImpl extends ServiceImpl<BillPayCustomerMapper, BillPayCustomer> implements BillPayCustomerService {

    @Autowired
    private BillPayCustomerMapper billPayCustomerMapper;

    @Autowired
    private BillPayCustomerSubService billSubIncomeCityService;

    @Autowired
    private BillPayCustomerSubMapper billPayCustomerSubMapper;
    @Value("${db.database}")
    private String database;


    /**
     * 根据id查询应收账单的账单列表和子帐单信息
     *
     * @param params 应收账单（市）ID
     * @return 应收账单（市）信息
     */
    @Override
    public R selectBillIncomeCityById(Map<String, Object> params) {
        R r = new R();
        Integer id = Integer.valueOf(String.valueOf(params.get("id")));
        Object platformCodeOb = params.get("platformCode");
        Object customerNameOb = params.get("customerName");
        String platformCode = "";
        String customerName = "";
        if(!ObjectUtils.isEmpty(platformCodeOb)){
            platformCode = String.valueOf(params.get("platformCode"));
        }
        if(!ObjectUtils.isEmpty(customerNameOb)){
            customerName = String.valueOf(params.get("customerName"));
        }
        BillIncomeCityVO billIncomeCityVO = billPayCustomerMapper.selectBillIncomeCityById(id,platformCode);
        if(ObjectUtils.isEmpty(billIncomeCityVO)){
            r.setCode(500);
            r.setMsg("查询出错");
            return r;
        }
        // 查询出子账单信息
        List<BillDealWithCityAndCostVO> cityAndCostVOS = billSubIncomeCityService.selectFdBillSubByBillNo(billIncomeCityVO.getBillCode(),customerName,platformCode);
        if(!CollectionUtils.isEmpty(cityAndCostVOS)){
            billIncomeCityVO.setList(cityAndCostVOS);
        }
        r.setData(billIncomeCityVO);
        return r;

    }

    /**
     * 根据数据Id 获取费用详情
     * @param query
     * @return
     */
    @Override
    public Map<String, Object> selectCostInfoByBillId(Query query) {

        BillCostDetailDTO billCostDetailDTO = BeanUtil.mapToBean(query.getCondition(), BillCostDetailDTO.class, false);
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String platformCode = userInfo.getPlatformCode();
        BillIncomeCityVO billIncomeCityVO = billPayCustomerMapper.selectBillIncomeCityById(billCostDetailDTO.getId(),platformCode);
        Map<String, Object> map = new HashMap<>();
        if(!ObjectUtils.isEmpty(billIncomeCityVO)){
            billCostDetailDTO.setShiftNo(billIncomeCityVO.getProvinceShiftNum());
            billCostDetailDTO.setDbSource(database);
            billCostDetailDTO.setBillCode(billIncomeCityVO.getBillCode());
            List<BillCostDetailVO> billCostDetailVOS = new ArrayList<>();
            //if(billCostDetailDTO.getPageType().equals(0)){
                // 判断班次信息
                /*Shifmanagement shifmanagement = shifmanagementMapper.selectShifmanagementByPlatCodeAndShiftNo(platformCode, billIncomeCityVO.getProvinceShiftNum());
                if(ObjectUtils.isEmpty(shifmanagement)){
                    log.info("BillPayCustomerServiceImpl类：市平台查询应收账单费用明细查询班次出错！");
                    return map;
                }
                if (StringUtils.isEmpty(shifmanagement.getParentId())){
                    billCostDetailVOS = billPayCustomerMapper.selectCostInfoByCostCodeAndZeon(query, billCostDetailDTO);
                }else {
                    billCostDetailVOS = billPayCustomerMapper.selectCostInfoByOrderInfo(query, billCostDetailDTO);
                }*/
                //billCostDetailVOS = billPayCustomerMapper.selectCostInfoByOrderInfo(query, billCostDetailDTO);
            //}else {
                //billCostDetailVOS = billPayCustomerMapper.selectCostInfoByCostCode(query, billCostDetailDTO);
            billCostDetailVOS = billPayCustomerMapper.selectCostInfoByOrderInfo(query, billCostDetailDTO);
            //}
            if(!CollectionUtils.isEmpty(billCostDetailVOS)){
                Double ybJe = billCostDetailVOS.stream().mapToDouble(b -> {
                    try {
                        return Double.parseDouble(String.valueOf(b.getOriginalAmount()));
                    }catch (NumberFormatException e){
                        return 0.0;
                    }
                }).sum();

                Double bbJe = billCostDetailVOS.stream().mapToDouble(b -> {
                    try {
                        return Double.parseDouble(String.valueOf(b.getLocalAmount()));
                    }catch (NumberFormatException e){
                        return 0.0;
                    }
                }).sum();
                query.setRecords(billCostDetailVOS);
                map.put("ybJe",ybJe);
                map.put("bbJe",bbJe);
                map.put("data",query);
            }
        }
        return map;
    }

    @Override
    public Map<String, Object> selectCostInfoByBillId2(BillCostDetailDTO billCostDetailDTO){
        List<BillPayCustomerSub> list = billPayCustomerSubMapper.selectBillInfoByBillId(billCostDetailDTO.getId());
        Map<String, Object> map = new HashMap<>();
        if(!ObjectUtils.isEmpty(list)){
            billCostDetailDTO.setShiftNo(list.get(0).getShiftNo());
            billCostDetailDTO.setDbSource(database);
            billCostDetailDTO.setBillCode(list.get(0).getBillCode());
            List<BillCostDetailVO> billCostDetailVOS = new ArrayList<>();
            billCostDetailVOS = billPayCustomerMapper.selectCostInfoByOrderInfo1(billCostDetailDTO);

            billCostDetailDTO.setPlatformCode(list.get(0).getPlatformCode());
            List<BillCostDetailVO> billCostDetailVOS2 = billPayCustomerMapper.selectCostInfoByOrderInfo2(billCostDetailDTO);
            if(!CollectionUtils.isEmpty(billCostDetailVOS)){
                Double ybJe = billCostDetailVOS.stream().mapToDouble(b -> {
                    try {
                        return Double.parseDouble(String.valueOf(b.getOriginalAmount()));
                    }catch (NumberFormatException e){
                        return 0.0;
                    }
                }).sum();

                Double bbJe = billCostDetailVOS.stream().mapToDouble(b -> {
                    try {
                        return Double.parseDouble(String.valueOf(b.getLocalAmount()));
                    }catch (NumberFormatException e){
                        return 0.0;
                    }
                }).sum();
                billCostDetailVOS.stream().forEach(b -> {
                    if(CollUtil.isNotEmpty(billCostDetailVOS2)){
                        for (BillCostDetailVO b2:billCostDetailVOS2
                             ) {
                            if(b.getContainerNumber().equals(b2.getContainerNumber())){
                                b.setContainerOwner(b2.getContainerOwner());
                                b.setContainerTypeCode(b2.getContainerTypeCode());
                                b.setContainerTypeName(b2.getContainerTypeName());
                                b.setGoodsChineseName(b2.getGoodsChineseName());
                                b.setIdentification(b2.getIdentification());
                                b.setConsignorName(b2.getConsignorName());
                                b.setStartStationName(b2.getStartStationName());
                                b.setEndStationName(b2.getEndStationName());
                                b.setCountryName(b2.getCountryName());
                                b.setInvoiceAppNo(b2.getInvoiceAppNo());
                                break;
                            }
                        }
                    }
                });
                map.put("ybJe",ybJe);
                map.put("bbJe",bbJe);
                map.put("data",billCostDetailVOS);
            }
        }
        return map;
    }

    /**
     * 查询应收账单（市）列表
     *
     * @param billPayCustomer 应收账单（市）信息
     * @return 应收账单（市）集合
     */
    @Override
    public List<BillPayCustomer> selectBillIncomeCityList(BillPayCustomer billPayCustomer)
    {
        return billPayCustomerMapper.selectBillIncomeCityList(billPayCustomer);
    }


    /**
     * 分页模糊查询应收账单（市）列表
     * @return 应收账单（市）集合
     */
    @Override
    public Page selectBillIncomeCityListByLike(Query query)
    {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.sub_billing_status");
            query.setAsc(Boolean.TRUE);
        }
        BillPayCustomer billPayCustomer =  BeanUtil.mapToBean(query.getCondition(), BillPayCustomer.class,false);
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        billPayCustomer.setPlatformLevel(userInfo.getPlatformLevel());
        if("0".equals(userInfo.getPlatformLevel()) && "1".equals(userInfo.getDataFlag())){
            billPayCustomer.setMiniPlatformName(userInfo.getMiniPlatform());
        }
        query.setRecords(billPayCustomerMapper.selectBillIncomeCityListByLike(query, billPayCustomer));
        return query;
    }

    /**
     * 新增应收账单（市）
     *
     * @param billPayCustomer 应收账单（市）信息
     * @return 结果
     */
    @Override
    public int insertBillIncomeCity(BillPayCustomer billPayCustomer)
    {
        return billPayCustomerMapper.insertBillIncomeCity(billPayCustomer);
    }

    /**
     * 修改应收账单（市）
     *
     * @param billPayCustomer 应收账单（市）信息
     * @return 结果
     */
    @Override
    public int updateBillIncomeCity(BillPayCustomer billPayCustomer)
    {
        return billPayCustomerMapper.updateBillIncomeCity(billPayCustomer);
    }


    /**
     * 删除应收账单（市）
     *
     * @param id 应收账单（市）ID
     * @return 结果
     */
    public int deleteBillIncomeCityById(Integer id)
    {
        return billPayCustomerMapper.deleteBillIncomeCityById( id);
    };


    /**
     * 批量删除应收账单（市）对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBillIncomeCityByIds(Integer[] ids)
    {
        return billPayCustomerMapper.deleteBillIncomeCityByIds( ids);
    }

    @Override
    public List<BillCostDetailVO> selectCostInfoByCostCode(Query query, BillCostDetailDTO billCostDetailDTO){
        return billPayCustomerMapper.selectCostInfoByCostCode(query,billCostDetailDTO);
    }

    @Override
    public List<BillCostDetailVO> selectCostInfoByCityCostCode(Query query, BillCostDetailDTO billCostDetailDTO){
        return billPayCustomerMapper.selectCostInfoByCityCostCode(query,billCostDetailDTO);
    }

    @Override
    public List<BillCostDetailVO> selectCostInfoByOrderInfo(Query query, BillCostDetailDTO billCostDetailDTO){
        return billPayCustomerMapper.selectCostInfoByOrderInfo(query,billCostDetailDTO);
    }
}
