package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.RateManageMapper;
import com.huazheng.tunny.ocean.api.entity.RateManage;
import com.huazheng.tunny.ocean.service.RateManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("rateManageService")
public class RateManageServiceImpl extends ServiceImpl<RateManageMapper, RateManage> implements RateManageService {

    @Autowired
    private RateManageMapper rateManageMapper;

    public RateManageMapper getRateManageMapper() {
        return rateManageMapper;
    }

    public void setRateManageMapper(RateManageMapper rateManageMapper) {
        this.rateManageMapper = rateManageMapper;
    }

    /**
     * 查询汇率设置表（省平台维护）信息
     *
     * @param rowId 汇率设置表（省平台维护）ID
     * @return 汇率设置表（省平台维护）信息
     */
    @Override
    public RateManage selectRateManageById(String rowId)
    {
        return rateManageMapper.selectRateManageById(rowId);
    }

    /**
     * 查询汇率设置表（省平台维护）列表
     *
     * @param rateManage 汇率设置表（省平台维护）信息
     * @return 汇率设置表（省平台维护）集合
     */
    @Override
    public List<RateManage> selectRateManageList(RateManage rateManage)
    {
        return rateManageMapper.selectRateManageList(rateManage);
    }


    /**
     * 分页模糊查询汇率设置表（省平台维护）列表
     * @return 汇率设置表（省平台维护）集合
     */
    @Override
    public Page selectRateManageListByLike(Query query)
    {
        RateManage rateManage =  BeanUtil.mapToBean(query.getCondition(), RateManage.class,false);
        List<RateManage> rateManages = rateManageMapper.selectRateManageListByLike(query, rateManage);
        query.setRecords(rateManages);
        query.setTotal(rateManageMapper.selectAllNo(rateManage));
        return query;
    }

    /**
     * 新增汇率设置表（省平台维护）
     *
     * @param rateManage 汇率设置表（省平台维护）信息
     * @return 结果
     */
    @Override
    public int insertRateManage(RateManage rateManage)
    {
        return rateManageMapper.insertRateManage(rateManage);
    }

    /**
     * 修改汇率设置表（省平台维护）
     *
     * @param rateManage 汇率设置表（省平台维护）信息
     * @return 结果
     */
    @Override
    public int updateRateManage(RateManage rateManage)
    {
        return rateManageMapper.updateRateManage(rateManage);
    }


    /**
     * 删除汇率设置表（省平台维护）
     *
     * @param rateManage 汇率设置表（省平台维护）ID
     * @return 结果
     */
    public int deleteRateManageById(RateManage rateManage)
    {
        return rateManageMapper.deleteRateManageById(rateManage);
    };


    /**
     * 批量删除汇率设置表（省平台维护）对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteRateManageByIds(Integer[] rowIds)
    {
        return rateManageMapper.deleteRateManageByIds( rowIds);
    }

}
