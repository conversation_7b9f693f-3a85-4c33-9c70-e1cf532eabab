package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.FdMultigangSettlement;

import java.util.List;

/**
 * 中铁多联结算对象表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-09-03 09:41:07
 */
public interface FdMultigangSettlementService extends IService<FdMultigangSettlement> {
    /**
     * 查询中铁多联结算对象表信息
     *
     * @param id 中铁多联结算对象表ID
     * @return 中铁多联结算对象表信息
     */
    public FdMultigangSettlement selectFdMultigangSettlementById(Integer id);

    /**
     * 查询中铁多联结算对象表列表
     *
     * @param fdMultigangSettlement 中铁多联结算对象表信息
     * @return 中铁多联结算对象表集合
     */
    public List<FdMultigangSettlement> selectFdMultigangSettlementList(FdMultigangSettlement fdMultigangSettlement);


    /**
     * 分页模糊查询中铁多联结算对象表列表
     *
     * @return 中铁多联结算对象表集合
     */
    public Page selectFdMultigangSettlementListByLike(Query query);


    /**
     * 新增中铁多联结算对象表
     *
     * @param fdMultigangSettlement 中铁多联结算对象表信息
     * @return 结果
     */
    public int insertFdMultigangSettlement(FdMultigangSettlement fdMultigangSettlement);

    /**
     * 修改中铁多联结算对象表
     *
     * @param fdMultigangSettlement 中铁多联结算对象表信息
     * @return 结果
     */
    public int updateFdMultigangSettlement(FdMultigangSettlement fdMultigangSettlement);

    /**
     * 删除中铁多联结算对象表
     *
     * @param id 中铁多联结算对象表ID
     * @return 结果
     */
    public int deleteFdMultigangSettlementById(Integer id);

    /**
     * 批量删除中铁多联结算对象表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdMultigangSettlementByIds(Integer[] ids);

}

