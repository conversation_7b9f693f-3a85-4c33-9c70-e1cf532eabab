package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.EfWarehouseAuditinfoMapper;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseAuditinfo;
import com.huazheng.tunny.ocean.service.EfWarehouseAuditinfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("efWarehouseAuditinfoService")
public class EfWarehouseAuditinfoServiceImpl extends ServiceImpl<EfWarehouseAuditinfoMapper, EfWarehouseAuditinfo> implements EfWarehouseAuditinfoService {

    @Autowired
    private EfWarehouseAuditinfoMapper efWarehouseAuditinfoMapper;

    public EfWarehouseAuditinfoMapper getEfWarehouseAuditinfoMapper() {
        return efWarehouseAuditinfoMapper;
    }

    public void setEfWarehouseAuditinfoMapper(EfWarehouseAuditinfoMapper efWarehouseAuditinfoMapper) {
        this.efWarehouseAuditinfoMapper = efWarehouseAuditinfoMapper;
    }

    /**
     * 查询融资审核信息信息
     *
     * @param rowId 融资审核信息ID
     * @return 融资审核信息信息
     */
    @Override
    public EfWarehouseAuditinfo selectEfWarehouseAuditinfoById(String rowId)
    {
        return efWarehouseAuditinfoMapper.selectEfWarehouseAuditinfoById(rowId);
    }

    /**
     * 查询融资审核信息列表
     *
     * @param efWarehouseAuditinfo 融资审核信息信息
     * @return 融资审核信息集合
     */
    @Override
    public List<EfWarehouseAuditinfo> selectEfWarehouseAuditinfoList(EfWarehouseAuditinfo efWarehouseAuditinfo)
    {
        return efWarehouseAuditinfoMapper.selectEfWarehouseAuditinfoList(efWarehouseAuditinfo);
    }


    /**
     * 分页模糊查询融资审核信息列表
     * @return 融资审核信息集合
     */
    @Override
    public Page selectEfWarehouseAuditinfoListByLike(Query query)
    {
        EfWarehouseAuditinfo efWarehouseAuditinfo =  BeanUtil.mapToBean(query.getCondition(), EfWarehouseAuditinfo.class,false);
        query.setRecords(efWarehouseAuditinfoMapper.selectEfWarehouseAuditinfoListByLike(query,efWarehouseAuditinfo));
        return query;
    }

    /**
     * 新增融资审核信息
     *
     * @param efWarehouseAuditinfo 融资审核信息信息
     * @return 结果
     */
    @Override
    public int insertEfWarehouseAuditinfo(EfWarehouseAuditinfo efWarehouseAuditinfo)
    {
        return efWarehouseAuditinfoMapper.insertEfWarehouseAuditinfo(efWarehouseAuditinfo);
    }

    /**
     * 修改融资审核信息
     *
     * @param efWarehouseAuditinfo 融资审核信息信息
     * @return 结果
     */
    @Override
    public int updateEfWarehouseAuditinfo(EfWarehouseAuditinfo efWarehouseAuditinfo)
    {
        return efWarehouseAuditinfoMapper.updateEfWarehouseAuditinfo(efWarehouseAuditinfo);
    }


    /**
     * 删除融资审核信息
     *
     * @param rowId 融资审核信息ID
     * @return 结果
     */
    public int deleteEfWarehouseAuditinfoById(String rowId)
    {
        return efWarehouseAuditinfoMapper.deleteEfWarehouseAuditinfoById( rowId);
    };


    /**
     * 批量删除融资审核信息对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfWarehouseAuditinfoByIds(Integer[] rowIds)
    {
        return efWarehouseAuditinfoMapper.deleteEfWarehouseAuditinfoByIds( rowIds);
    }

}
