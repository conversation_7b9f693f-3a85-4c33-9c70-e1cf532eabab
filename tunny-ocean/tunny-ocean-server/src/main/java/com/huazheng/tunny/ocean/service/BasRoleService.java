package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.BasRole;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 角色信息管理表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-01 10:37:04
 */
public interface BasRoleService extends IService<BasRole> {
    /**
     * 查询角色信息管理表信息
     *
     * @param rowId 角色信息管理表ID
     * @return 角色信息管理表信息
     */
    public BasRole selectBasRoleById(String rowId);

    /**
     * 查询角色信息管理表列表
     *
     * @param basRole 角色信息管理表信息
     * @return 角色信息管理表集合
     */
    public List<BasRole> selectBasRoleList(BasRole basRole);


    /**
     * 分页模糊查询角色信息管理表列表
     * @return 角色信息管理表集合
     */
    public Page selectBasRoleListByLike(Query query);



    /**
     * 新增角色信息管理表
     *
     * @param basRole 角色信息管理表信息
     * @return 结果
     */
    public int insertBasRole(BasRole basRole);

    /**
     * 修改角色信息管理表
     *
     * @param basRole 角色信息管理表信息
     * @return 结果
     */
    public int updateBasRole(BasRole basRole);

    /**
     * 删除角色信息管理表
     *
     * @param rowId 角色信息管理表ID
     * @return 结果
     */
    public int deleteBasRoleById(String rowId);

    /**
     * 批量删除角色信息管理表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteBasRoleByIds(Integer[] rowIds);

}

