package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfFutruesPrice;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 仓单期货价格 服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-06 14:37:40
 */
public interface EfFutruesPriceService extends IService<EfFutruesPrice> {
    /**
     * 查询仓单期货价格信息
     *
     * @param rowId 仓单期货价格ID
     * @return 仓单期货价格信息
     */
    public EfFutruesPrice selectEfFutruesPriceById(String rowId);

    /**
     * 查询仓单期货价格列表
     *
     * @param efFutruesPrice 仓单期货价格信息
     * @return 仓单期货价格集合
     */
    public List<EfFutruesPrice> selectEfFutruesPriceList(EfFutruesPrice efFutruesPrice);


    /**
     * 分页模糊查询仓单期货价格列表
     * @return 仓单期货价格集合
     */
    public Page selectEfFutruesPriceListByLike(Query query);



    /**
     * 新增仓单期货价格
     *
     * @param efFutruesPrice 仓单期货价格信息
     * @return 结果
     */
    public int insertEfFutruesPrice(EfFutruesPrice efFutruesPrice);

    /**
     * 修改仓单期货价格
     *
     * @param efFutruesPrice 仓单期货价格信息
     * @return 结果
     */
    public int updateEfFutruesPrice(EfFutruesPrice efFutruesPrice);

    /**
     * 删除仓单期货价格
     *
     * @param rowId 仓单期货价格ID
     * @return 结果
     */
    public int deleteEfFutruesPriceById(String rowId);

    /**
     * 批量删除仓单期货价格
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfFutruesPriceByIds(Integer[] rowIds);

}

