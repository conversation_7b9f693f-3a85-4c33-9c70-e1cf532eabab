package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.mapper.PayCodeMesMapper;
import com.huazheng.tunny.ocean.api.entity.PayCodeMes;
import com.huazheng.tunny.ocean.service.PayCodeMesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service("payCodeMesService")
public class PayCodeMesServiceImpl extends ServiceImpl<PayCodeMesMapper, PayCodeMes> implements PayCodeMesService {

    @Autowired
    private PayCodeMesMapper payCodeMesMapper;

    public PayCodeMesMapper getPayCodeMesMapper() {
        return payCodeMesMapper;
    }

    public void setPayCodeMesMapper(PayCodeMesMapper payCodeMesMapper) {
        this.payCodeMesMapper = payCodeMesMapper;
    }

    /**
     * 查询付费代码信息表信息
     *
     * @param rowId 付费代码信息表ID
     * @return 付费代码信息表信息
     */
    @Override
    public PayCodeMes selectPayCodeMesById(String rowId)
    {
        return payCodeMesMapper.selectPayCodeMesById(rowId);
    }

    /**
     * 查询付费代码信息表列表
     *
     * @param payCodeMes 付费代码信息表信息
     * @return 付费代码信息表集合
     */
    @Override
    public List<PayCodeMes> selectPayCodeMesList(PayCodeMes payCodeMes)
    {
        return payCodeMesMapper.selectPayCodeMesList(payCodeMes);
    }


    /**
     * 分页模糊查询付费代码信息表列表
     * @return 付费代码信息表集合
     */
    @Override
    public Page selectPayCodeMesListByLike(Query query)
    {
        PayCodeMes payCodeMes =  BeanUtil.mapToBean(query.getCondition(), PayCodeMes.class,false);
        query.setRecords(payCodeMesMapper.selectPayCodeMesListByLike(query,payCodeMes));
        return query;
    }

    /**
     * 新增付费代码信息表
     *
     * @param payCodeMes 付费代码信息表信息
     * @return 结果
     */
    @Override
    public int insertPayCodeMes(PayCodeMes payCodeMes)
    {
        return payCodeMesMapper.insertPayCodeMes(payCodeMes);
    }

    @Override
    public int insertPayCodeMesBatch(List<PayCodeMes> payCodeMes) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        for (PayCodeMes info:payCodeMes) {
            info.setAddWhoName(username);
            info.setAddWho(usercode);
            info.setAddTime(LocalDateTime.now());
        }
        return payCodeMesMapper.insertPayCodeMesBatch(payCodeMes);
    }

    @Override
    public void insertOrUpdatePayCodeMesBatch(List<PayCodeMes> payCodeMes) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        for (PayCodeMes info:payCodeMes) {
            info.setUpdateTime(LocalDateTime.now());
            info.setUpdateWho(usercode);
            info.setUpdateWhoName(username);
            int i = payCodeMesMapper.updatePayCodeMesTwo(info);
            if(i <= 0){
                info.setRowId(UUID.randomUUID().toString());
                info.setDeleteFlag("N");
                info.setAddWhoName(username);
                info.setAddWho(usercode);
                info.setAddTime(LocalDateTime.now());
                payCodeMesMapper.insertPayCodeMes(info);
            }

        }
    }

    /**
     * 修改付费代码信息表
     *
     * @param payCodeMes 付费代码信息表信息
     * @return 结果
     */
    @Override
    public int updatePayCodeMes(PayCodeMes payCodeMes)
    {
        return payCodeMesMapper.updatePayCodeMes(payCodeMes);
    }

    @Override
    public int updatePayCodeMesBatch(List<PayCodeMes> payCodeMes) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        for (PayCodeMes info:payCodeMes) {
            info.setUpdateTime(LocalDateTime.now());
            info.setUpdateWho(usercode);
            info.setUpdateWhoName(username);
            payCodeMesMapper.updatePayCodeMes(info);
        }
        return 0;
    }

    @Override
    public int updatePayCodeMesBatchDelete(PayCodeMes payCodeMes) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        payCodeMes.setUpdateTime(LocalDateTime.now());
        payCodeMes.setUpdateWho(usercode);
        payCodeMes.setUpdateWhoName(username);
        return payCodeMesMapper.updatePayCodeMesBatchDelete(payCodeMes);
    }

    /**
     * 删除付费代码信息表
     *
     * @param rowId 付费代码信息表ID
     * @return 结果
     */
    @Override
    public int deletePayCodeMesById(String rowId)
    {
        return payCodeMesMapper.deletePayCodeMesById( rowId);
    };


    /**
     * 批量删除付费代码信息表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deletePayCodeMesByIds(Integer[] rowIds)
    {
        return payCodeMesMapper.deletePayCodeMesByIds( rowIds);
    }

}
