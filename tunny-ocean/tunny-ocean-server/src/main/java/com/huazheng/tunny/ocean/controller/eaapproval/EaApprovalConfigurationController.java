package com.huazheng.tunny.ocean.controller.eaapproval;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.eaapproval.EaApprovalConfiguration;
import com.huazheng.tunny.ocean.service.eaapproval.EaApprovalConfigurationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 审批配置表
 *
 * <AUTHOR> code generator
 * @date 2025-06-17 10:02:12
 */
@Slf4j
@RestController
@RequestMapping("/eaapprovalconfiguration")
public class EaApprovalConfigurationController {

    @Autowired
    private EaApprovalConfigurationService eaApprovalConfigurationService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  eaApprovalConfigurationService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return eaApprovalConfigurationService.selectEaApprovalConfigurationListByLike(new Query<>(params));
    }

    /**
     * 信息
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Long id) {
        EaApprovalConfiguration eaApprovalConfiguration =eaApprovalConfigurationService.selectById(id);
        return new R<>(eaApprovalConfiguration);
    }

    /**
     * 保存
     * @param eaApprovalConfiguration
     * @return R
     */
    @PostMapping
    public R save(@RequestBody EaApprovalConfiguration eaApprovalConfiguration) {
        eaApprovalConfigurationService.insert(eaApprovalConfiguration);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param eaApprovalConfiguration
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody EaApprovalConfiguration eaApprovalConfiguration) {
        eaApprovalConfigurationService.updateById(eaApprovalConfiguration);
        return new R<>(Boolean.TRUE);
    }

    

    /**
     * 删除
     * @param id
     * @return R
     */
    @GetMapping("/del/{id}")
    public R delete(@PathVariable  Long id) {
        eaApprovalConfigurationService.deleteById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<Long> ids) {
        eaApprovalConfigurationService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<EaApprovalConfiguration> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<EaApprovalConfiguration> list = eaApprovalConfigurationService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), EaApprovalConfiguration.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }
}
