package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.WaybillParticipants;
import com.huazheng.tunny.ocean.mapper.WaybillGoodsInfoMapper;
import com.huazheng.tunny.ocean.api.entity.WaybillGoodsInfo;
import com.huazheng.tunny.ocean.service.SysNoConfigService;
import com.huazheng.tunny.ocean.service.WaybillGoodsInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service("waybillGoodsInfoService")
public class WaybillGoodsInfoServiceImpl extends ServiceImpl<WaybillGoodsInfoMapper, WaybillGoodsInfo> implements WaybillGoodsInfoService {

    @Autowired
    private WaybillGoodsInfoMapper waybillGoodsInfoMapper;

    @Autowired
    private SysNoConfigService sysNoConfigService;

    public WaybillGoodsInfoMapper getWaybillGoodsInfoMapper() {
        return waybillGoodsInfoMapper;
    }

    public void setWaybillGoodsInfoMapper(WaybillGoodsInfoMapper waybillGoodsInfoMapper) {
        this.waybillGoodsInfoMapper = waybillGoodsInfoMapper;
    }

    public SysNoConfigService getSysNoConfigService() {
        return sysNoConfigService;
    }

    public void setSysNoConfigService(SysNoConfigService sysNoConfigService) {
        this.sysNoConfigService = sysNoConfigService;
    }

    /**
     * 查询运单货物信息表信息
     *
     * @param rowId 运单货物信息表ID
     * @return 运单货物信息表信息
     */
    @Override
    public WaybillGoodsInfo selectWaybillGoodsInfoById(String rowId)
    {
        return waybillGoodsInfoMapper.selectWaybillGoodsInfoById(rowId);
    }

    /**
     * 查询运单货物信息表列表
     *
     * @param waybillGoodsInfo 运单货物信息表信息
     * @return 运单货物信息表集合
     */
    @Override
    public List<WaybillGoodsInfo> selectWaybillGoodsInfoList(WaybillGoodsInfo waybillGoodsInfo)
    {
        return waybillGoodsInfoMapper.selectWaybillGoodsInfoList(waybillGoodsInfo);
    }


    /**
     * 分页模糊查询运单货物信息表列表
     * @return 运单货物信息表集合
     */
    @Override
    public Page selectWaybillGoodsInfoListByLike(Query query)
    {
        WaybillGoodsInfo waybillGoodsInfo =  BeanUtil.mapToBean(query.getCondition(), WaybillGoodsInfo.class,false);
        query.setRecords(waybillGoodsInfoMapper.selectWaybillGoodsInfoListByLike(query,waybillGoodsInfo));
        return query;
    }

    /**
     * 新增运单货物信息表
     *
     * @param list 运单货物信息表信息
     * @return 结果
     */
    @Override
    public int insertWaybillGoodsInfo(List<WaybillGoodsInfo> list)
    {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        for (WaybillGoodsInfo pants:list) {
            pants.setAddTime(LocalDateTime.now());
            pants.setAddWho(usercode);
            pants.setAddWhoName(username);
        }
        return waybillGoodsInfoMapper.insertWaybillGoodsInfo(list);
    }

    @Override
    public void insertOrUpdateWaybillGoodsInfo(List<WaybillGoodsInfo> list,List<String> waybillCodes)
    {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        if(CollUtil.isNotEmpty(waybillCodes)){
            for (String waybillNo:waybillCodes
                 ) {
                if(CollUtil.isNotEmpty(list)){
                    for (WaybillGoodsInfo pants:list) {
                        pants.setWaybillNo(waybillNo);
                        pants.setUpdateTime(LocalDateTime.now());
                        pants.setUpdateWho(usercode);
                        pants.setUpdateWhoName(username);
                        int i = waybillGoodsInfoMapper.updateWaybillGoodsInfo2(pants);
                        if(i <= 0){
                            pants.setRowId(UUID.randomUUID().toString());
                            pants.setDeleteFlag("N");
                            pants.setAddTime(LocalDateTime.now());
                            pants.setAddWho(usercode);
                            pants.setAddWhoName(username);
                            waybillGoodsInfoMapper.saveGoodsInfo(pants);
                        }
                    }
                }
            }
        }
    }

    /**
     * 批量更新运单货物信息表
     *
     * @param list 运单货物信息表信息
     * @return 结果
     */
    @Override
    public int updateGoodsInfoBatch(List<WaybillGoodsInfo> list)
    {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        String del = "Y";
        if(del.equals(list.get(0).getDeleteFlag())){
            for (WaybillGoodsInfo info:list) {
                info.setDeleteTime(LocalDateTime.now());
                info.setDeleteWho(usercode);
                info.setDeleteWhoName(username);
            }
        }else{
            for (WaybillGoodsInfo info:list) {
                info.setUpdateTime(LocalDateTime.now());
                info.setUpdateWho(usercode);
                info.setUpdateWhoName(username);
            }
        }
        if(CollUtil.isNotEmpty(list)){
            for (WaybillGoodsInfo info:list
                 ) {
                waybillGoodsInfoMapper.updateWaybillGoodsInfo2(info);
            }
        }
        return 1;
    }


    /**
     * 删除运单货物信息表
     *
     * @param rowId 运单货物信息表ID
     * @return 结果
     */
    @Override
    public int deleteWaybillGoodsInfoById(String rowId)
    {
        return waybillGoodsInfoMapper.deleteWaybillGoodsInfoById( rowId);
    };


    /**
     * 批量删除运单货物信息表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteWaybillGoodsInfoByIds(Integer[] rowIds)
    {
        return waybillGoodsInfoMapper.deleteWaybillGoodsInfoByIds( rowIds);
    }

    @Override
    public int updateGoodsInfo(WaybillGoodsInfo waybillGoodsInfo) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        waybillGoodsInfo.setDeleteWho(userInfo.getUserName());
        waybillGoodsInfo.setDeleteWhoName(userInfo.getRealName());
        return waybillGoodsInfoMapper.updateGoodsInfo(waybillGoodsInfo);
    }

    @Override
    public int deleteWaybillGoodsInfoByContainerNo(WaybillGoodsInfo waybillGoodsInfo) {
        return waybillGoodsInfoMapper.deleteWaybillGoodsInfoByContainerNo(waybillGoodsInfo);
    }

}
