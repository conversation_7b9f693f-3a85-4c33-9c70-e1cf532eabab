package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfFutruesInfo;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 仓单期货行情 服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-06 14:37:36
 */
public interface EfFutruesInfoService extends IService<EfFutruesInfo> {
    /**
     * 查询仓单期货行情信息
     *
     * @param rowId 仓单期货行情ID
     * @return 仓单期货行情信息
     */
    public EfFutruesInfo selectEfFutruesInfoById(String rowId);

    public EfFutruesInfo getDetail(EfFutruesInfo efFutruesInfo);
    /**
     * 查询仓单期货行情列表
     *
     * @param efFutruesInfo 仓单期货行情信息
     * @return 仓单期货行情集合
     */
    public List<EfFutruesInfo> selectEfFutruesInfoList(EfFutruesInfo efFutruesInfo);


    /**
     * 分页模糊查询仓单期货行情列表
     * @return 仓单期货行情集合
     */
    public Page selectEfFutruesInfoListByLike(Query query);



    /**
     * 新增仓单期货行情
     *
     * @param efFutruesInfo 仓单期货行情信息
     * @return 结果
     */
    public int insertEfFutruesInfo(EfFutruesInfo efFutruesInfo);

    /**
     * 修改仓单期货行情
     *
     * @param efFutruesInfo 仓单期货行情信息
     * @return 结果
     */
    public int updateEfFutruesInfo(EfFutruesInfo efFutruesInfo);

    /**
     * 删除仓单期货行情
     *
     * @param rowId 仓单期货行情ID
     * @return 结果
     */
    public int deleteEfFutruesInfoById(String rowId);

    /**
     * 批量删除仓单期货行情
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfFutruesInfoByIds(Integer[] rowIds);

    String syncFutruesInfo(EfFutruesInfo efFutruesInfo);
}

