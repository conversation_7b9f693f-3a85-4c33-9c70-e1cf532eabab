package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FiPledgeIncrease;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 仓单质押补充申请表 服务接口层
 *
 * <AUTHOR>
 * @date 2022-11-11 16:42:53
 */
public interface FiPledgeIncreaseService extends IService<FiPledgeIncrease> {
    /**
     * 查询仓单质押补充申请表信息
     *
     * @param rowId 仓单质押补充申请表ID
     * @return 仓单质押补充申请表信息
     */
    public FiPledgeIncrease selectFiPledgeIncreaseById(String rowId);

    /**
     * 查询仓单质押补充申请表列表
     *
     * @param fiPledgeIncrease 仓单质押补充申请表信息
     * @return 仓单质押补充申请表集合
     */
    public List<FiPledgeIncrease> selectFiPledgeIncreaseList(FiPledgeIncrease fiPledgeIncrease);


    /**
     * 分页模糊查询仓单质押补充申请表列表
     * @return 仓单质押补充申请表集合
     */
    public Page selectFiPledgeIncreaseListByLike(Query query);



    /**
     * 新增仓单质押补充申请表
     *
     * @param fiPledgeIncrease 仓单质押补充申请表信息
     * @return 结果
     */
    public int insertFiPledgeIncrease(FiPledgeIncrease fiPledgeIncrease);

    /**
     * 修改仓单质押补充申请表
     *
     * @param fiPledgeIncrease 仓单质押补充申请表信息
     * @return 结果
     */
    public int updateFiPledgeIncrease(FiPledgeIncrease fiPledgeIncrease);

    /**
     * 删除仓单质押补充申请表
     *
     * @param rowId 仓单质押补充申请表ID
     * @return 结果
     */
    public int deleteFiPledgeIncreaseById(String rowId);

    /**
     * 批量删除仓单质押补充申请表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiPledgeIncreaseByIds(Integer[] rowIds);

}

