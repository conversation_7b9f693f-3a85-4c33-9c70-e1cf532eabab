package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.DlInvoiceDetails;
import com.huazheng.tunny.ocean.api.vo.DlInvoiceDetailsVO;
import com.huazheng.tunny.ocean.mapper.DlInvoiceDetailsMapper;
import com.huazheng.tunny.ocean.service.DlInvoiceDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.time.LocalDateTime;
import java.util.List;

@Service("dlInvoiceDetailsService")
public class DlInvoiceDetailsServiceImpl extends ServiceImpl<DlInvoiceDetailsMapper, DlInvoiceDetails> implements DlInvoiceDetailsService {

    @Autowired
    private DlInvoiceDetailsMapper dlInvoiceDetailsMapper;

    /**
     * 查询多联开票明细信息
     *
     * @param id 多联开票明细ID
     * @return 多联开票明细信息
     */
    @Override
    public DlInvoiceDetails selectDlInvoiceDetailsById(Integer id)
    {
        return dlInvoiceDetailsMapper.selectDlInvoiceDetailsById(id);
    }

    /**
     * 查询多联开票明细列表
     *
     * @param dlInvoiceDetails 多联开票明细信息
     * @return 多联开票明细集合
     */
    @Override
    public List<DlInvoiceDetails> selectDlInvoiceDetailsList(DlInvoiceDetails dlInvoiceDetails)
    {
        return dlInvoiceDetailsMapper.selectDlInvoiceDetailsList(dlInvoiceDetails);
    }


    /**
     * 分页模糊查询多联开票明细列表
     * @return 多联开票明细集合
     */
    @Override
    public Page selectDlInvoiceDetailsListByLike(Query query)
    {
        DlInvoiceDetails dlInvoiceDetails =  BeanUtil.mapToBean(query.getCondition(), DlInvoiceDetails.class,false);
        query.setRecords(dlInvoiceDetailsMapper.selectDlInvoiceDetailsListByLike(query,dlInvoiceDetails));
        return query;
    }

    /**
     * 新增多联开票明细
     *
     * @param dlInvoiceDetails 多联开票明细信息
     * @return 结果
     */
    @Override
    public int insertDlInvoiceDetails(DlInvoiceDetails dlInvoiceDetails)
    {
        return dlInvoiceDetailsMapper.insertDlInvoiceDetails(dlInvoiceDetails);
    }

    /**
     * 修改多联开票明细
     *
     * @param dlInvoiceDetails 多联开票明细信息
     * @return 结果
     */
    @Override
    public int updateDlInvoiceDetails(DlInvoiceDetails dlInvoiceDetails)
    {
        return dlInvoiceDetailsMapper.updateDlInvoiceDetails(dlInvoiceDetails);
    }


    /**
     * 删除多联开票明细
     *
     * @param id 多联开票明细ID
     * @return 结果
     */
    public int deleteDlInvoiceDetailsById(Integer id)
    {
        return dlInvoiceDetailsMapper.deleteDlInvoiceDetailsById( id);
    };


    /**
     * 批量删除多联开票明细对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteDlInvoiceDetailsByIds(Integer[] ids)
    {
        return dlInvoiceDetailsMapper.deleteDlInvoiceDetailsByIds( ids);
    }

    /**
     * 查询该省级班列号账单中的箱数据
     *
     * @param provinceShiftNo 省级班列号
     * @return 结果
     */
    @Override
    public List<DlInvoiceDetails> getBillContainerList(String provinceShiftNo) {
        return dlInvoiceDetailsMapper.getBillContainerList(provinceShiftNo);
    }

    /**
     * 保存导入列表
     *
     * @param list
     * @return R
     */
    @Override
    public R saveImported(List<DlInvoiceDetailsVO> list) {
        if(CollUtil.isNotEmpty(list)){
            for (DlInvoiceDetailsVO vo:list
                 ) {
                if(vo!=null && CollUtil.isNotEmpty(vo.getDetailList())){
                    final SecruityUser userInfo = SecurityUtils.getUserInfo();
                    //传入数据默认全部为已核对状态
                    final List<DlInvoiceDetails> detailList = vo.getDetailList();
                    //查询数据库中该省级班列号已有箱数据
                    DlInvoiceDetails sel = new DlInvoiceDetails();
                    sel.setProvinceShiftNo(vo.getProvinceShiftNo());
                    sel.setDeleteFlag("N");
                    final List<DlInvoiceDetails> detailsList = dlInvoiceDetailsMapper.selectDlInvoiceDetailsList(sel);
                    if(CollUtil.isNotEmpty(detailsList)){
                        //存在该班次
                        for (DlInvoiceDetails dlInvoiceDetails:detailList
                        ) {
                            //存在该箱号，更新数据
                            Boolean flag = Boolean.TRUE;
                            for (DlInvoiceDetails oldObj:detailsList
                                 ) {
                                if(StrUtil.isNotEmpty(oldObj.getContainerNumber())
                                        &&dlInvoiceDetails.getContainerNumber().equals(oldObj.getContainerNumber()))
                                {
                                    dlInvoiceDetails.setId(oldObj.getId());
                                    dlInvoiceDetails.setStatus("1");
                                    dlInvoiceDetails.setUpdateWho(userInfo.getUserName());
                                    dlInvoiceDetails.setUpdateWhoName(userInfo.getRealName());
                                    dlInvoiceDetails.setUpdateTime(LocalDateTime.now());
                                    dlInvoiceDetailsMapper.updateDlInvoiceDetails(dlInvoiceDetails);
                                    flag = Boolean.FALSE;
                                    break;
                                }
                            }
                            //不存在该箱号，新增数据
                            if(flag){
                                dlInvoiceDetails.setStatus("1");
                                dlInvoiceDetails.setAddWho(userInfo.getUserName());
                                dlInvoiceDetails.setAddWhoName(userInfo.getRealName());
                                dlInvoiceDetails.setAddTime(LocalDateTime.now());
                                dlInvoiceDetailsMapper.insertDlInvoiceDetails(dlInvoiceDetails);
                            }

                        }

                    }else{
                        //不存在该班次，直接新增
                        for (DlInvoiceDetails dlInvoiceDetails:detailList
                             ) {
                            dlInvoiceDetails.setStatus("1");
                            dlInvoiceDetails.setAddWho(userInfo.getUserName());
                            dlInvoiceDetails.setAddWhoName(userInfo.getRealName());
                            dlInvoiceDetails.setAddTime(LocalDateTime.now());
                            dlInvoiceDetailsMapper.insertDlInvoiceDetails(dlInvoiceDetails);
                        }
                    }
                }
            }
        }
        return new R<>(200,Boolean.TRUE,null,"保存成功！");
    }

}
