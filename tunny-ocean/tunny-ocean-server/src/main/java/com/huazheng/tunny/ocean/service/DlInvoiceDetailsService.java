package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.DlInvoiceDetails;
import com.huazheng.tunny.ocean.api.vo.DlInvoiceDetailsVO;

import java.util.List;

/**
 * 多联开票明细 服务接口层
 *
 * <AUTHOR>
 * @date 2023-09-01 15:09:43
 */
public interface DlInvoiceDetailsService extends IService<DlInvoiceDetails> {
    /**
     * 查询多联开票明细信息
     *
     * @param id 多联开票明细ID
     * @return 多联开票明细信息
     */
    public DlInvoiceDetails selectDlInvoiceDetailsById(Integer id);

    /**
     * 查询多联开票明细列表
     *
     * @param dlInvoiceDetails 多联开票明细信息
     * @return 多联开票明细集合
     */
    public List<DlInvoiceDetails> selectDlInvoiceDetailsList(DlInvoiceDetails dlInvoiceDetails);


    /**
     * 分页模糊查询多联开票明细列表
     * @return 多联开票明细集合
     */
    public Page selectDlInvoiceDetailsListByLike(Query query);



    /**
     * 新增多联开票明细
     *
     * @param dlInvoiceDetails 多联开票明细信息
     * @return 结果
     */
    public int insertDlInvoiceDetails(DlInvoiceDetails dlInvoiceDetails);

    /**
     * 修改多联开票明细
     *
     * @param dlInvoiceDetails 多联开票明细信息
     * @return 结果
     */
    public int updateDlInvoiceDetails(DlInvoiceDetails dlInvoiceDetails);

    /**
     * 删除多联开票明细
     *
     * @param id 多联开票明细ID
     * @return 结果
     */
    public int deleteDlInvoiceDetailsById(Integer id);

    /**
     * 批量删除多联开票明细
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteDlInvoiceDetailsByIds(Integer[] ids);

    /**
     * 查询该省级班列号账单中的箱数据
     *
     * @param provinceShiftNo 省级班列号
     * @return 结果
     */
    List<DlInvoiceDetails> getBillContainerList(String provinceShiftNo);

    /**
     * 保存导入列表
     * @param list
     * @return R
     */
    R saveImported(List<DlInvoiceDetailsVO> list);
}

