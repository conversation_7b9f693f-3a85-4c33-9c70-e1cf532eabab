package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FiWhFinancingApply;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 仓单质押融资申请表 服务接口层
 *
 * <AUTHOR>
 * @date 2022-11-10 15:50:35
 */
public interface FiWhFinancingApplyService extends IService<FiWhFinancingApply> {
    /**
     * 查询仓单质押融资申请表信息
     *
     * @param rowId 仓单质押融资申请表ID
     * @return 仓单质押融资申请表信息
     */
    public FiWhFinancingApply selectFiWhFinancingApplyById(String rowId);

    /**
     * 查询仓单质押融资申请表列表
     *
     * @param fiWhFinancingApply 仓单质押融资申请表信息
     * @return 仓单质押融资申请表集合
     */
    public List<FiWhFinancingApply> selectFiWhFinancingApplyList(FiWhFinancingApply fiWhFinancingApply);


    /**
     * 分页模糊查询仓单质押融资申请表列表
     * @return 仓单质押融资申请表集合
     */
    public Page selectFiWhFinancingApplyListByLike(Query query);



    /**
     * 新增仓单质押融资申请表
     *
     * @param fiWhFinancingApply 仓单质押融资申请表信息
     * @return 结果
     */
    public int insertFiWhFinancingApply(FiWhFinancingApply fiWhFinancingApply);

    /**
     * 修改仓单质押融资申请表
     *
     * @param fiWhFinancingApply 仓单质押融资申请表信息
     * @return 结果
     */
    public int updateFiWhFinancingApply(FiWhFinancingApply fiWhFinancingApply);

    /**
     * 删除仓单质押融资申请表
     *
     * @param rowId 仓单质押融资申请表ID
     * @return 结果
     */
    public int deleteFiWhFinancingApplyById(String rowId);

    /**
     * 批量删除仓单质押融资申请表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiWhFinancingApplyByIds(Integer[] rowIds);

}

