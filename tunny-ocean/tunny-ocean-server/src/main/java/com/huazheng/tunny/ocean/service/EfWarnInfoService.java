package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfWarnInfo;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 预警信息 服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-06 14:37:43
 */
public interface EfWarnInfoService extends IService<EfWarnInfo> {
    /**
     * 查询预警信息信息
     *
     * @param rowId 预警信息ID
     * @return 预警信息信息
     */
    public EfWarnInfo selectEfWarnInfoById(String rowId);

    /**
     * 查询预警信息列表
     *
     * @param efWarnInfo 预警信息信息
     * @return 预警信息集合
     */
    public List<EfWarnInfo> selectEfWarnInfoList(EfWarnInfo efWarnInfo);


    /**
     * 分页模糊查询预警信息列表
     * @return 预警信息集合
     */
    public Page selectEfWarnInfoListByLike(Query query);



    /**
     * 新增预警信息
     *
     * @param efWarnInfo 预警信息信息
     * @return 结果
     */
    public int insertEfWarnInfo(EfWarnInfo efWarnInfo);

    /**
     * 修改预警信息
     *
     * @param efWarnInfo 预警信息信息
     * @return 结果
     */
    public int updateEfWarnInfo(EfWarnInfo efWarnInfo);

    /**
     * 删除预警信息
     *
     * @param rowId 预警信息ID
     * @return 结果
     */
    public int deleteEfWarnInfoById(String rowId);

    /**
     * 批量删除预警信息
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfWarnInfoByIds(Integer[] rowIds);

    String warnInfoSync(EfWarnInfo efWarnInfo);
}

