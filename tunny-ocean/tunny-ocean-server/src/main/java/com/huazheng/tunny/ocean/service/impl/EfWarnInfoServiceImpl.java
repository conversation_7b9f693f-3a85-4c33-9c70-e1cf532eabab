package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.EfWarnInfoDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.controller.SignatureController;
import com.huazheng.tunny.ocean.mapper.EfWarehouseApplyMapper;
import com.huazheng.tunny.ocean.mapper.EfWarehouseLogMapper;
import com.huazheng.tunny.ocean.mapper.EfWarnInfoMapper;
import com.huazheng.tunny.ocean.service.EfWarnInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service("efWarnInfoService")
public class EfWarnInfoServiceImpl extends ServiceImpl<EfWarnInfoMapper, EfWarnInfo> implements EfWarnInfoService {

    @Autowired
    private EfWarnInfoMapper efWarnInfoMapper;
    @Autowired
    private SignatureController signatureController;
    @Autowired
    private EfWarehouseLogMapper efWarehouseLogMapper;
    @Autowired
    private EfWarehouseApplyMapper efWarehouseApplyMapper;
    /**
     * 查询预警信息信息
     *
     * @param rowId 预警信息ID
     * @return 预警信息信息
     */
    @Override
    public EfWarnInfo selectEfWarnInfoById(String rowId)
    {
        return efWarnInfoMapper.selectEfWarnInfoById(rowId);
    }

    /**
     * 查询预警信息列表
     *
     * @param efWarnInfo 预警信息信息
     * @return 预警信息集合
     */
    @Override
    public List<EfWarnInfo> selectEfWarnInfoList(EfWarnInfo efWarnInfo)
    {
        return efWarnInfoMapper.selectEfWarnInfoList(efWarnInfo);
    }


    /**
     * 分页模糊查询预警信息列表
     * @return 预警信息集合
     */
    @Override
    public Page selectEfWarnInfoListByLike(Query query)
    {
        EfWarnInfo efWarnInfo =  BeanUtil.mapToBean(query.getCondition(), EfWarnInfo.class,false);
        query.setRecords(efWarnInfoMapper.selectEfWarnInfoListByLike(query,efWarnInfo));
        return query;
    }

    /**
     * 新增预警信息
     *
     * @param efWarnInfo 预警信息信息
     * @return 结果
     */
    @Override
    public int insertEfWarnInfo(EfWarnInfo efWarnInfo)
    {
        return efWarnInfoMapper.insertEfWarnInfo(efWarnInfo);
    }

    /**
     * 修改预警信息
     *
     * @param efWarnInfo 预警信息信息
     * @return 结果
     */
    @Override
    public int updateEfWarnInfo(EfWarnInfo efWarnInfo)
    {
        return efWarnInfoMapper.updateEfWarnInfo(efWarnInfo);
    }


    /**
     * 删除预警信息
     *
     * @param rowId 预警信息ID
     * @return 结果
     */
    public int deleteEfWarnInfoById(String rowId)
    {
        return efWarnInfoMapper.deleteEfWarnInfoById( rowId);
    };


    /**
     * 批量删除预警信息对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfWarnInfoByIds(Integer[] rowIds)
    {
        return efWarnInfoMapper.deleteEfWarnInfoByIds( rowIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String warnInfoSync(EfWarnInfo efWarnInfo)
    {
        //插入操作记录
        EfWarehouseLog log = new EfWarehouseLog();
        String remark = "";

        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(efWarnInfo!=null) {
            final String qlFinancingNo = efWarnInfo.getQlFinancingNo();
            efWarnInfo.setQlFinancingNo(null);
            List<EfWarnInfo> warnInfoSyncParamList = new ArrayList<>();
            warnInfoSyncParamList.add(efWarnInfo);
            EfWarnInfoDTO dto = new EfWarnInfoDTO();
            dto.setWarnInfoList(warnInfoSyncParamList);
            //调用中钞接口，插入申请数据,解析返回数据
            String json = JSONUtil.parseObj(dto, true).toStringPretty();
            final String result = signatureController.doPost("/v1/safe/warehouse/warnInfoSync", json);

            JSONObject resultObject = JSONUtil.parseObj(result);
            efWarnInfo.setQlFinancingNo(qlFinancingNo);
            flag = Boolean.valueOf(String.valueOf(resultObject.get("b")));
            msg = String.valueOf(resultObject.get("msg"));
            if(flag){
                from = "中钞平台:";
                /*FORWARD_WARN远期估值预警
                    MAIN_WARN主流合约估值预警
                    MONITOR_WARN动货视频预警
                    OTHER_WARN
                    其他类型预警*/
                if("FORWARD_WARN".equals(efWarnInfo.getType())){
                    remark = "远期估值预警";
                }else if("MAIN_WARN".equals(efWarnInfo.getType())){
                    remark = "主流合约估值预警";
                }else if("MONITOR_WARN".equals(efWarnInfo.getType())){
                    remark = "动货视频预警";
                }else if("OTHER_WARN".equals(efWarnInfo.getType())){
                    remark = "其他类型预警";
                }
                efWarnInfo.setRowId(UUID.randomUUID().toString());
                efWarnInfo.setAddTime(LocalDateTime.now());
                efWarnInfo.setAddWho("ER");
                efWarnInfo.setAddWhoName("E融");
                efWarnInfoMapper.insertEfWarnInfo(efWarnInfo);

            }else{
                from = "中钞平台:";
            }

            final EfWarehouseApply apply = efWarehouseApplyMapper.selectEfWarehouseApplyByAssetCode(efWarnInfo.getAssetCode());

            //插入操作记录
            log.setRowId(UUID.randomUUID().toString());
            if(apply!=null){
                log.setQlFinancingNo(apply.getQlFinancingNo());
            }
            log.setSerialNum(String.valueOf(System.currentTimeMillis()));
            log.setControlType("E融发起预警");
            log.setControlTime(LocalDateTime.now());
            log.setRemark(remark);
            log.setAddTime(LocalDateTime.now());
            log.setAddWho(SecurityUtils.getUserInfo().getUserName());
            log.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
            efWarehouseLogMapper.insertEfWarehouseLog(log);
        }else{
            msg = "没有接受到仓单预警信息数据";
            flag = false;
        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }
}
