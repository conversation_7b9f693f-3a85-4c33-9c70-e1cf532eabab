package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.controller.SignatureController;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.EfFinancingApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import java.util.UUID;

@Service("efFinancingApplyService")
public class EfFinancingApplyServiceImpl extends ServiceImpl<EfFinancingApplyMapper, EfFinancingApply> implements EfFinancingApplyService {

    @Autowired
    private EfFinancingApplyMapper efFinancingApplyMapper;
    @Autowired
    private EfFinancingWarehouseMapper efFinancingWarehouseMapper;
    @Autowired
    private EfFinancingGoodsMapper efFinancingGoodsMapper;
    @Autowired
    private SysNoConfigServiceImpl sysNoConfigService;
    @Autowired
    private SignatureController signatureController;
    @Autowired
    private EfFinancingApprovalinfoMapper efFinancingApprovalinfoMapper;
    @Autowired
    private EfFinancingConfirmationinfoMapper efFinancingConfirmationinfoMapper;
    @Autowired
    private EfFinancingLoaninfoMapper efFinancingLoaninfoMapper;
    @Autowired
    private EfFinancingRepaymentinfoMapper efFinancingRepaymentinfoMapper;
    @Autowired
    private EfFinancingPledgechangeinfoMapper efFinancingPledgechangeinfoMapper;
    @Autowired
    private EfFinancingPledgedisposalinfoMapper efFinancingPledgedisposalinfoMapper;

    /**
     * 查询仓单融资申请表信息
     *
     * @param rowId 仓单融资申请表ID
     * @return 仓单融资申请表信息
     */
    @Override
    public EfFinancingApply selectEfFinancingApplyById(String rowId)
    {
        EfFinancingApply efFinancingApply = efFinancingApplyMapper.selectEfFinancingApplyById(rowId);
        if(efFinancingApply!=null&& StrUtil.isNotEmpty(efFinancingApply.getQlFinancingNo())){
            List<EfFinancingWarehouse> warehouseList = efFinancingWarehouseMapper.selectEfFinancingWarehouseByQlFinancingNo(efFinancingApply.getQlFinancingNo());
            if(CollUtil.isNotEmpty(warehouseList)){
                for (EfFinancingWarehouse warehouse:warehouseList
                     ) {
                    if(warehouse!=null && StrUtil.isNotEmpty(warehouse.getWarehouseReceiptNo())){
                        List<EfFinancingGoods> goodsList = efFinancingGoodsMapper.selectEfFinancingGoodsByWarehouseReceiptNo(warehouse.getWarehouseReceiptNo());
                        warehouse.setGoods(goodsList);
                        //附件
                    }
                }
                efFinancingApply.setWarehouseReceipts(warehouseList);
            }
            //银行审批信息
            EfFinancingApprovalinfo efFinancingApprovalinfo = efFinancingApprovalinfoMapper.selectEfFinancingApprovalinfoByQlFinancingNo(efFinancingApply.getQlFinancingNo());
            efFinancingApply.setBankApprovalInfo(efFinancingApprovalinfo);

            //企业确认信息
            EfFinancingConfirmationinfo efFinancingConfirmationinfo = efFinancingConfirmationinfoMapper.selectEfFinancingConfirmationinfoByQlFinancingNo(efFinancingApply.getQlFinancingNo());
            efFinancingApply.setEntConfirmationInfo(efFinancingConfirmationinfo);

            //银行放款信息
            EfFinancingLoaninfo efFinancingLoaninfo = efFinancingLoaninfoMapper.selectEfFinancingLoaninfoByQlFinancingNo(efFinancingApply.getQlFinancingNo());
            efFinancingApply.setBankLoanInfo(efFinancingLoaninfo);

            //企业还款信息
            List<EfFinancingRepaymentinfo> efFinancingRepaymentinfos = efFinancingRepaymentinfoMapper.selectEfFinancingRepaymentinfoByQlFinancingNo(efFinancingApply.getQlFinancingNo());
//            if(CollUtil.isNotEmpty(efFinancingRepaymentinfos)){
//                for (EfFinancingRepaymentinfo repaymentinfo:efFinancingRepaymentinfos
//                     ) {
//                    EfFinancingPledgechangeinfo pledgechangeinfo = new EfFinancingPledgechangeinfo();
//                    pledgechangeinfo.setQlFinancingNo(efFinancingApply.getQlFinancingNo());
//                }
//            }
            efFinancingApply.setEntRepaymentInfo(efFinancingRepaymentinfos);

            //补质押情况
            List<EfFinancingPledgechangeinfo> efFinancingPledgechangeinfos = efFinancingPledgechangeinfoMapper.selectEfFinancingPledgechangeinfoByQlFinancingNo(efFinancingApply.getQlFinancingNo());
            efFinancingApply.setIncreasePledgeInfos(efFinancingPledgechangeinfos);

            //质押物处置情况
            EfFinancingPledgedisposalinfo efFinancingPledgedisposalinfo = efFinancingPledgedisposalinfoMapper.selectEfFinancingPledgedisposalinfoByQlFinancingNo(efFinancingApply.getQlFinancingNo());
            efFinancingApply.setPledgeDisposalInfo(efFinancingPledgedisposalinfo);

        }
        return efFinancingApply;
    }

    /**
     * 查询仓单融资申请表列表
     *
     * @param efFinancingApply 仓单融资申请表信息
     * @return 仓单融资申请表集合
     */
    @Override
    public List<EfFinancingApply> selectEfFinancingApplyList(EfFinancingApply efFinancingApply)
    {
        return efFinancingApplyMapper.selectEfFinancingApplyList(efFinancingApply);
    }


    /**
     * 分页模糊查询仓单融资申请表列表
     * @return 仓单融资申请表集合
     */
    @Override
    public Page selectEfFinancingApplyListByLike(Query query)
    {
        EfFinancingApply efFinancingApply =  BeanUtil.mapToBean(query.getCondition(), EfFinancingApply.class,false);
        efFinancingApply.setAddWho(SecurityUtils.getUserInfo().getUserName());
        efFinancingApply.setDeleteFlag("N");
        query.setRecords(efFinancingApplyMapper.selectEfFinancingApplyListByLike(query,efFinancingApply));
        return query;
    }

    /**
     * 新增仓单融资申请表
     *
     * @param efFinancingApply 仓单融资申请表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R insertEfFinancingApply(EfFinancingApply efFinancingApply)
    {
        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        List<EfFinancingWarehouse> warehouseList = new ArrayList<>();
        List<EfFinancingGoods> goodsList = new ArrayList<>();
//        String qlFinancingNo = sysNoConfigService.genNo("RZ");
        String qlFinancingNo = "RZ"+ System.currentTimeMillis();
        efFinancingApply.setRowId(UUID.randomUUID().toString());
        efFinancingApply.setQlFinancingNo(qlFinancingNo);
        efFinancingApply.setFinancingState("TO_BE_CONFIRM");
        if(CollUtil.isNotEmpty(efFinancingApply.getFinancialProtocolPath())){

        }
        efFinancingApply.setAddWho(SecurityUtils.getUserInfo().getUserName());
        efFinancingApply.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
        efFinancingApply.setAddTime(LocalDateTime.now());

        if(CollUtil.isNotEmpty(efFinancingApply.getWarehouseReceipts())){
            for (EfFinancingWarehouse warehouse:efFinancingApply.getWarehouseReceipts()
                 ) {
                warehouse.setRowId(UUID.randomUUID().toString());
                warehouse.setQlFinancingNo(qlFinancingNo);
                if(CollUtil.isNotEmpty(warehouse.getContentPath())){

                }
                if(CollUtil.isNotEmpty(warehouse.getInvoicePath())){

                }
                if(CollUtil.isNotEmpty(warehouse.getOtherPath())){

                }
                if(CollUtil.isNotEmpty(warehouse.getOrderPath())){

                }
                if(CollUtil.isNotEmpty(warehouse.getGoods())){
                    for (EfFinancingGoods goods:warehouse.getGoods()
                         ) {
                        goods.setRowId(UUID.randomUUID().toString());
                        goods.setWarehouseReceiptNo(warehouse.getWarehouseReceiptNo());
                        goods.setAddWho(SecurityUtils.getUserInfo().getUserName());
                        goods.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                        goods.setAddTime(LocalDateTime.now());
                        goodsList.add(goods);
                    }

                }
                warehouse.setAddWho(SecurityUtils.getUserInfo().getUserName());
                warehouse.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                warehouse.setAddTime(LocalDateTime.now());
                warehouseList.add(warehouse);
            }
        }
        //调用E融接口，插入申请数据
        /*String json = JSONUtil.parseObj(efFinancingApply, true).toStringPretty();
        final String result = signatureController.doPostEf("/applyPledgeFinancingToStorager", json);
        JSONObject dataObject = JSONUtil.parseObj(result);
        String code = String.valueOf(dataObject.get("code"));
        if(!"SUCCESS".equals(code)){
            from = "E融平台:";
            msg = String.valueOf(dataObject.get("msg"));
            flag = false;
            return new R<>(flag,from+msg);
        }*/

        //插入本地数据
        efFinancingApplyMapper.insertEfFinancingApply(efFinancingApply);
        if(CollUtil.isNotEmpty(warehouseList)){
            efFinancingWarehouseMapper.insertList(warehouseList);
        }
        if(CollUtil.isNotEmpty(goodsList)){
            efFinancingGoodsMapper.insertList(goodsList);
        }
        return new R<>(flag,from+msg);
    }

    /**
     * 修改仓单融资申请表
     *
     * @param efFinancingApply 仓单融资申请表信息
     * @return 结果
     */
    @Override
    public R updateEfFinancingApply(EfFinancingApply efFinancingApply)
    {
        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        List<EfFinancingWarehouse> warehouseList = new ArrayList<>();
        List<EfFinancingGoods> goodsList = new ArrayList<>();

        if(CollUtil.isNotEmpty(efFinancingApply.getFinancialProtocolPath())){

        }
        efFinancingApply.setFinancingState("TO_BE_CONFIRM");
        efFinancingApply.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
        efFinancingApply.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
        efFinancingApply.setUpdateTime(LocalDateTime.now());

        if(CollUtil.isNotEmpty(efFinancingApply.getWarehouseReceipts())){
            for (EfFinancingWarehouse warehouse:efFinancingApply.getWarehouseReceipts()
            ) {
                warehouse.setRowId(UUID.randomUUID().toString());
                warehouse.setQlFinancingNo(efFinancingApply.getQlFinancingNo());
                if(CollUtil.isNotEmpty(warehouse.getContentPath())){

                }
                if(CollUtil.isNotEmpty(warehouse.getInvoicePath())){

                }
                if(CollUtil.isNotEmpty(warehouse.getOtherPath())){

                }
                if(CollUtil.isNotEmpty(warehouse.getOrderPath())){

                }
                if(CollUtil.isNotEmpty(warehouse.getGoods())){
                    for (EfFinancingGoods goods:warehouse.getGoods()
                    ) {
                        goods.setRowId(UUID.randomUUID().toString());
                        goods.setWarehouseReceiptNo(warehouse.getWarehouseReceiptNo());
                        goods.setAddWho(SecurityUtils.getUserInfo().getUserName());
                        goods.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                        goods.setAddTime(LocalDateTime.now());
                        goodsList.add(goods);
                    }

                }
                warehouse.setAddWho(SecurityUtils.getUserInfo().getUserName());
                warehouse.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                warehouse.setAddTime(LocalDateTime.now());
                warehouseList.add(warehouse);
            }
        }
        //调用E融接口，插入申请数据
        /*String json = JSONUtil.parseObj(efFinancingApply, true).toStringPretty();
        final String result = signatureController.doPostEf("/applyPledgeFinancingToStorager", json);
        JSONObject dataObject = JSONUtil.parseObj(result);
        String code = String.valueOf(dataObject.get("code"));
        if(!"SUCCESS".equals(code)){
            from = "E融平台:";
            msg = String.valueOf(dataObject.get("msg"));
            flag = false;
            return new R<>(flag,from+msg);
        }*/

        //插入本地数据
        efFinancingApplyMapper.updateEfFinancingApply(efFinancingApply);
        if(CollUtil.isNotEmpty(warehouseList)){
            efFinancingWarehouseMapper.deleteEfFinancingWarehouseByQlFinancingNo(efFinancingApply.getQlFinancingNo());
            efFinancingWarehouseMapper.insertList(warehouseList);
        }
        if(CollUtil.isNotEmpty(goodsList)){
            efFinancingGoodsMapper.deleteEfFinancingGoodsByQlFinancingNo(efFinancingApply.getQlFinancingNo());
            efFinancingGoodsMapper.insertList(goodsList);
        }

        return new R<>(flag,from+msg);
    }

    /**
     * 修改仓单融资申请表
     *
     * @param efFinancingConfirmationinfo 仓单融资申请表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R confirmLoan(EfFinancingConfirmationinfo efFinancingConfirmationinfo)
    {
        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;

        EfFinancingApply efFinancingApply = new EfFinancingApply();
        if(efFinancingConfirmationinfo!=null && StrUtil.isNotEmpty(efFinancingConfirmationinfo.getQlFinancingNo())){
            efFinancingApply.setQlFinancingNo(efFinancingConfirmationinfo.getQlFinancingNo());
            efFinancingApply.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
            efFinancingApply.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
            efFinancingApply.setUpdateTime(LocalDateTime.now());

            if(efFinancingConfirmationinfo!=null && StrUtil.isNotEmpty(efFinancingConfirmationinfo.getConfirmationType())){
                efFinancingConfirmationinfo.setConfirmationTime(LocalDateTime.now().toString());
                if("AGREED".equals(efFinancingConfirmationinfo.getConfirmationType())){
                    efFinancingApply.setFinancingState("PENDING_LOAN");
                }else if("ABANDONED".equals(efFinancingConfirmationinfo.getConfirmationType())){
                    efFinancingApply.setFinancingState("ABANDONE");
                }
            }

            //调用中钞接口，插入申请数据
            String json = JSONUtil.parseObj(efFinancingConfirmationinfo, true).toStringPretty();
            final String result = signatureController.doPost("/confirmPledgeFinancingApplication", json);
            JSONObject dataObject = JSONUtil.parseObj(result);
            String code = String.valueOf(dataObject.get("code"));
            if(!"SUCCESS".equals(code)){
                from = "跨境平台:";
                msg = dataObject.get("msg") +" 错误代码："+code;
                flag = false;
                return new R<>(flag,from+msg);
            }

            //调用E融接口，插入申请数据
            efFinancingApply.setEntConfirmationInfo(efFinancingConfirmationinfo);
            String json2 = JSONUtil.parseObj(efFinancingConfirmationinfo, true).toStringPretty();
        /*
        final String result2 = signatureController.doPostEf("/syncPledgeFinancingToStorager", json2);
        JSONObject dataObject2 = JSONUtil.parseObj(result2);
        String code2 = String.valueOf(dataObject2.get("code"));
        if(!"SUCCESS".equals(code2)){
            from = "E融平台:";
            msg = String.valueOf(dataObject.get("msg"));
            flag = false;
            return new R<>(flag,from+msg);
        }*/
            //插入本地数据
            efFinancingConfirmationinfo.setAddWho(SecurityUtils.getUserInfo().getUserName());
            efFinancingConfirmationinfo.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
            efFinancingConfirmationinfo.setAddTime(LocalDateTime.now());
            efFinancingConfirmationinfoMapper.insertEfFinancingConfirmationinfo(efFinancingConfirmationinfo);

            efFinancingApplyMapper.updateEfFinancingApply(efFinancingApply);
        }else {
            flag = false;
            msg ="未接收到齐鲁号融资申请编号";
        }


        return new R<>(flag,from+msg);
    }


    /**
     * 删除仓单融资申请表
     *
     * @param rowId 仓单融资申请表ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingApplyById(String rowId)
    {
        return efFinancingApplyMapper.deleteEfFinancingApplyById( rowId);
    };


    /**
     * 批量删除仓单融资申请表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingApplyByIds(Integer[] rowIds)
    {
        return efFinancingApplyMapper.deleteEfFinancingApplyByIds( rowIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String auditPledgeFinancingToQl(EfFinancingApply ef){
        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(ef!=null&&StrUtil.isNotEmpty(ef.getAuditStatus())){
            if("通过".equals(ef.getAuditStatus())){
                //通过
                ef.setFinancingState("TO_BE_APPROVED");
                EfFinancingApply efFinancingApply = efFinancingApplyMapper.selectEfFinancingApplyByQlFinancingNo(ef.getQlFinancingNo());
                if(efFinancingApply!=null&& StrUtil.isNotEmpty(efFinancingApply.getQlFinancingNo())){
                    List<EfFinancingWarehouse> warehouseList = efFinancingWarehouseMapper.selectEfFinancingWarehouseByQlFinancingNo(efFinancingApply.getQlFinancingNo());
                    if(CollUtil.isNotEmpty(warehouseList)){
                        for (EfFinancingWarehouse warehouse:warehouseList
                        ) {
                            if(warehouse!=null && StrUtil.isNotEmpty(warehouse.getWarehouseReceiptNo())){
                                List<EfFinancingGoods> goodsList = efFinancingGoodsMapper.selectEfFinancingGoodsByWarehouseReceiptNo(warehouse.getWarehouseReceiptNo());
                                warehouse.setGoods(goodsList);
                                //附件

                            }
                        }
                        efFinancingApply.setWarehouseReceipts(warehouseList);
                    }
                    efFinancingApply.setFinancialProtocolNo(null);

                    //调用中钞接口
                    String json = JSONUtil.parseObj(efFinancingApply, true).toStringPretty();
                    final String result = signatureController.doPost("/applyPledgeFinancing", json);
                    JSONObject dataObject = JSONUtil.parseObj(result);
                    String b = String.valueOf(dataObject.get("b"));
                    msg = String.valueOf(dataObject.get("msg"));
                    flag = true;
                    if(!"true".equals(b)){
                        flag = false;
                        from = "跨境平台:";
                        ef.setFinancingState("TO_BE_CONFIRM");
                    }
                }
            }else{
                //拒绝
                ef.setFinancingState("NOT_CONFIRM");
            }
            efFinancingApplyMapper.updateEfFinancingApply(ef);
        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String syncPledgeFinancing(EfFinancingApply ef){
        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(ef!=null){
            if(StrUtil.isNotEmpty(ef.getQlFinancingNo())){
                EfFinancingApply efFinancingApply = efFinancingApplyMapper.selectEfFinancingApplyByQlFinancingNo(ef.getQlFinancingNo());
                if(efFinancingApply!=null){
                    if(StrUtil.isNotEmpty(ef.getFinancingState())){
                        if("TO_BE_CONFIRMED".equals(ef.getFinancingState())){
                            //TO_BE_CONFIRMED:银行审核通过，待企业确认
                            if(ef.getBankApprovalInfo()!=null){
                                EfFinancingApprovalinfo bankApprovalInfo = ef.getBankApprovalInfo();
                                bankApprovalInfo.setRowId(UUID.randomUUID().toString());
                                bankApprovalInfo.setQlFinancingNo(ef.getQlFinancingNo());
                                bankApprovalInfo.setFinancialProtocolNo(ef.getFinancialProtocolNo());
                                bankApprovalInfo.setDeleteFlag("N");
                                bankApprovalInfo.setAddWho("ZC");
                                bankApprovalInfo.setAddWhoName("中钞");
                                bankApprovalInfo.setAddTime(LocalDateTime.now());
                                efFinancingApprovalinfoMapper.insertEfFinancingApprovalinfo(bankApprovalInfo);
                            }else{
                                msg = "没有接收到银行审批信息";
                                flag = false;
                            }
                        }else if("NOT_APPROVED".equals(ef.getFinancingState())){
                            //NOT_APPROVED:银行审核拒绝

                        }else if("Refused_TO_LOAN".equals(ef.getFinancingState())){
                            //Refused_TO_LOAN:银行拒绝放款

                        }else if("IN_REPAYING".equals(ef.getFinancingState())){
                            //IN_REPAYING:银行已放款，企业还款中
                            if(ef.getBankLoanInfo()!=null){
                                EfFinancingLoaninfo bankLoanInfo = ef.getBankLoanInfo();
                                bankLoanInfo.setRowId(UUID.randomUUID().toString());
                                bankLoanInfo.setQlFinancingNo(ef.getQlFinancingNo());
                                bankLoanInfo.setFinancialProtocolNo(ef.getFinancialProtocolNo());
                                bankLoanInfo.setDeleteFlag("N");
                                bankLoanInfo.setAddWho("ZC");
                                bankLoanInfo.setAddWhoName("中钞");
                                bankLoanInfo.setAddTime(LocalDateTime.now());
                                efFinancingApply.setBankLoanInfo(bankLoanInfo);
                                //调用e融接口
                                String json = JSONUtil.parseObj(efFinancingApply, true).toStringPretty();
                                final String result = signatureController.doPostEf("/syncPledgeFinancingToStorager", json);
                                JSONObject dataObject = JSONUtil.parseObj(result);
                                String code = String.valueOf(dataObject.get("code"));
                                msg = String.valueOf(dataObject.get("msg"));
                                flag = true;
                                if("SUCCESS".equals(code)){
                                    efFinancingLoaninfoMapper.insertEfFinancingLoaninfo(bankLoanInfo);
                                }else{
                                    flag = false;
                                    from = "E融平台:";
                                }

                            }else{
                                msg = "没有接收到银行放款信息";
                                flag = false;
                            }
                        }
                        ef.setUpdateWho("ZC");
                        ef.setUpdateWhoName("中钞");
                        ef.setUpdateTime(LocalDateTime.now());
                        efFinancingApplyMapper.updateEfFinancingApplyByQlFinancingNo(ef);
                    }else{
                        msg = "没有接收到融资状态";
                        flag = false;
                    }
                }else{
                    msg = "未接收到数据";
                    flag = false;
                }
            }else{
                msg = "未接收到齐鲁号融资申请编号";
                flag = false;
            }


        }else{
            msg = "当前融资数据不存在";
            flag = false;
        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }

}
