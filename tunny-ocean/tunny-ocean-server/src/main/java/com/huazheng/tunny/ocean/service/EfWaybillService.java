package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfWaybill;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 仓单质押关联运单 服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-16 13:03:46
 */
public interface EfWaybillService extends IService<EfWaybill> {
    /**
     * 查询仓单质押关联运单信息
     *
     * @param rowId 仓单质押关联运单ID
     * @return 仓单质押关联运单信息
     */
    public EfWaybill selectEfWaybillById(String rowId);

    /**
     * 查询仓单质押关联运单列表
     *
     * @param efWaybill 仓单质押关联运单信息
     * @return 仓单质押关联运单集合
     */
    public List<EfWaybill> selectEfWaybillList(EfWaybill efWaybill);


    /**
     * 分页模糊查询仓单质押关联运单列表
     * @return 仓单质押关联运单集合
     */
    public Page selectEfWaybillListByLike(Query query);

    public Page selectWaybillPage(Query query);

    /**
     * 新增仓单质押关联运单
     *
     * @param efWaybill 仓单质押关联运单信息
     * @return 结果
     */
    public int insertEfWaybill(EfWaybill efWaybill);

    /**
     * 修改仓单质押关联运单
     *
     * @param efWaybill 仓单质押关联运单信息
     * @return 结果
     */
    public int updateEfWaybill(EfWaybill efWaybill);

    /**
     * 删除仓单质押关联运单
     *
     * @param rowId 仓单质押关联运单ID
     * @return 结果
     */
    public int deleteEfWaybillById(String rowId);

    /**
     * 批量删除仓单质押关联运单
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfWaybillByIds(Integer[] rowIds);

    EfWaybill getWaybillInfo(EfWaybill efWaybill);
}

