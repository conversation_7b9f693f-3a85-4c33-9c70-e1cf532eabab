package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.MonthBookingplanDetail;
import com.huazheng.tunny.ocean.api.enums.SysEnum;
import com.huazheng.tunny.ocean.mapper.MonthBookingplanDetailMapper;
import com.huazheng.tunny.ocean.mapper.MonthBookingplanHeaderMapper;
import com.huazheng.tunny.ocean.api.entity.MonthBookingplanHeader;
import com.huazheng.tunny.ocean.service.MonthBookingplanHeaderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service("monthBookingplanHeaderService")
public class MonthBookingplanHeaderServiceImpl extends ServiceImpl<MonthBookingplanHeaderMapper, MonthBookingplanHeader> implements MonthBookingplanHeaderService {

    @Autowired
    private MonthBookingplanHeaderMapper monthBookingplanHeaderMapper;

    @Autowired
    private MonthBookingplanDetailMapper monthBookingplanDetailMapper;

    @Autowired
    private SysNoConfigServiceImpl sysNoConfigServiceImpl;

    /**
     * 查询月计划申请表(订舱客户提交到市平台)信息
     *
     * @param rowId 月计划申请表(订舱客户提交到市平台)ID
     * @return 月计划申请表(订舱客户提交到市平台)信息
     */
    @Override
    public MonthBookingplanHeader selectMonthBookingplanHeaderById(String rowId)
    {
        return monthBookingplanHeaderMapper.selectMonthBookingplanHeaderById(rowId);
    }

    /**
     * 查询月计划申请表(订舱客户提交到市平台)列表
     *
     * @param monthBookingplanHeader 月计划申请表(订舱客户提交到市平台)信息
     * @return 月计划申请表(订舱客户提交到市平台)集合
     */
    @Override
    public List<MonthBookingplanHeader> selectMonthBookingplanHeaderList(MonthBookingplanHeader monthBookingplanHeader)
    {
        return monthBookingplanHeaderMapper.selectMonthBookingplanHeaderList(monthBookingplanHeader);
    }


    /**
     * 分页模糊查询月计划申请表(订舱客户提交到市平台)列表
     * @return 月计划申请表(订舱客户提交到市平台)集合
     */
    @Override
    public Page selectMonthBookingplanHeaderListByLike(Query query)
    {
        MonthBookingplanHeader monthBookingplanHeader =  BeanUtil.mapToBean(query.getCondition(), MonthBookingplanHeader.class,false);
        if(StrUtil.isEmpty(monthBookingplanHeader.getPlatformCode())){
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            monthBookingplanHeader.setCustomerCode(userInfo.getPlatformCode());
        }
        /*Integer c= monthBookingplanHeaderMapper.queryCount(monthBookingplanHeader);
        if(c!=null&&c!=0){
            query.setTotal(c);*/
            query.setRecords(monthBookingplanHeaderMapper.selectMonthBookingplanHeaderListByLike(query, monthBookingplanHeader));
//        }
        return query;
    }

    /**
     * 分页模糊查询月计划申请表(订舱客户提交到市平台)列表
     * @return 月计划申请表(订舱客户提交到市平台)集合
     */
    @Override
    public Page selectMonthBookingplanHeaderListByLike1(Query query)
    {
        MonthBookingplanHeader monthBookingplanHeader =  BeanUtil.mapToBean(query.getCondition(), MonthBookingplanHeader.class,false);
        if(StrUtil.isEmpty(monthBookingplanHeader.getPlatformCode())){
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            monthBookingplanHeader.setPlatformCode(userInfo.getPlatformCode());
        }
        /*Integer c= monthBookingplanHeaderMapper.queryCount1(monthBookingplanHeader);
        if(c!=null&&c!=0){
            query.setTotal(c);*/
            query.setRecords(monthBookingplanHeaderMapper.selectMonthBookingplanHeaderListByLike1(query, monthBookingplanHeader));
//        }
        return query;
    }

    /**
     * 新增月计划申请表(订舱客户提交到市平台)
     *
     * @param monthBookingplanHeader 月计划申请表(订舱客户提交到市平台)信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R insertMonthBookingplanHeader(MonthBookingplanHeader monthBookingplanHeader)
    {
        if(monthBookingplanHeader.getDetails()==null){
            return new R(500,Boolean.FALSE,"请检查所填信息有遗漏");
        }
        if(monthBookingplanHeader.getPlanMonth()==null|| "".equals(monthBookingplanHeader.getPlanMonth())){
            return new R(500,Boolean.FALSE,"请检查基本信息填写");
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        Integer count=0;
        if(monthBookingplanHeader.getPlanCode()!=null&&!"".equals(monthBookingplanHeader.getPlanCode())){
            try {
                monthBookingplanDetailMapper.deleteMonthBookingplanDetailByPlanCode(monthBookingplanHeader.getPlanCode());
                for (MonthBookingplanDetail details : monthBookingplanHeader.getDetails()) {
                    details.setPlanCode(monthBookingplanHeader.getPlanCode());
                    details.setAddWho(usercode);
                    details.setAddWhoName(username);
                    details.setAddTime(LocalDateTime.now());
                    details.setPlanMonth(monthBookingplanHeader.getPlanMonth());
                    details.setPlatformCode(monthBookingplanHeader.getPlatformCode());
                    details.setResveredField01(monthBookingplanHeader.getResveredField01());//市平台名称
                    details.setCustomerCode(monthBookingplanHeader.getCustomerCode());
                    details.setResveredField02(monthBookingplanHeader.getResveredField02());//客户名称
                    count=count+Integer.parseInt(details.getVehiclesNum());
                }
                monthBookingplanHeader.setSum(String.valueOf(count));
                monthBookingplanHeader.setUpdateTime(LocalDateTime.now());
                monthBookingplanHeader.setUpdateWho(usercode);
                monthBookingplanHeader.setUpdateWhoName(username);
                List<MonthBookingplanHeader> list=new ArrayList<>();
                list.add(monthBookingplanHeader);
                monthBookingplanHeaderMapper.updateMonthBookingplanHeader(list);
                monthBookingplanDetailMapper.insertMonthBookingplanDetails(monthBookingplanHeader.getDetails());
            } catch (Exception e) {
                System.out.println(e.getMessage() + "@@@@@@操作失败！@@@@@@");
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return new R(500,Boolean.FALSE,"操作失败！");
            }
            return new R(200,Boolean.TRUE,"操作成功");
        }else{
            String appNo=sysNoConfigServiceImpl.genNo("JH");
            if (appNo.contains("，")) {
                return new R(500, Boolean.FALSE, appNo);
            }
            for (MonthBookingplanDetail details : monthBookingplanHeader.getDetails()) {
                details.setPlanCode(appNo);
                details.setAddWho(usercode);
                details.setAddWhoName(username);
                details.setAddTime(LocalDateTime.now());
                details.setPlanMonth(monthBookingplanHeader.getPlanMonth());
                details.setPlatformCode(monthBookingplanHeader.getPlatformCode());
                details.setResveredField01(monthBookingplanHeader.getResveredField01());//市平台名称
                details.setCustomerCode(monthBookingplanHeader.getCustomerCode());
                details.setResveredField02(monthBookingplanHeader.getResveredField02());//客户名称
                count=count+Integer.parseInt(details.getVehiclesNum());
            }
            try {
                monthBookingplanHeader.setAddTime(LocalDateTime.now());
                monthBookingplanHeader.setAddWho(usercode);
                monthBookingplanHeader.setAddWhoName(username);
                monthBookingplanHeader.setPlanCode(appNo);
                monthBookingplanHeader.setSum(String.valueOf(count));
                monthBookingplanHeader.setShippingStatus(SysEnum.CUSTOMER_PLAN_STATUS_WAIT_COMMIT.getKey());
                monthBookingplanHeaderMapper.insertMonthBookingplanHeader(monthBookingplanHeader);
                monthBookingplanDetailMapper.insertMonthBookingplanDetails(monthBookingplanHeader.getDetails());
            } catch (Exception e) {
                System.out.println(e.getMessage() + "@@@@@@月计划保存失败！@@@@@@");
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return new R(500,Boolean.FALSE,"月计划保存失败！");
            }
            return new R(200,Boolean.TRUE,"保存成功",appNo);
        }
    }

    /**
     * 删除月计划申请表及子表信息(订舱客户提交到市平台)
     *
     * @param planCode 月计划申请表(订舱客户提交到市平台)信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateMonthBookingplanHeader(String[] planCode)
    {
        if(planCode.length==0){
            return new R(500,Boolean.FALSE,"至少选中一条计划");
        }
        List<MonthBookingplanHeader> list=new ArrayList<>();
        for (int i=0;i<planCode.length;i++){
            MonthBookingplanHeader temp=new MonthBookingplanHeader();
            temp.setPlanCode(planCode[i]);
            list.add(temp);
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        List<MonthBookingplanDetail> listDetail=new ArrayList<>();
        for (MonthBookingplanHeader monthBookingplanHeader: list) {
            monthBookingplanHeader.setDeleteWho(usercode);
            monthBookingplanHeader.setDeleteWhoName(username);
            monthBookingplanHeader.setDeleteTime(LocalDateTime.now());
            monthBookingplanHeader.setDeleteFlag("Y");
            MonthBookingplanDetail dbd = new MonthBookingplanDetail();
            dbd.setDeleteWho(usercode);
            dbd.setDeleteWhoName(username);
            dbd.setDeleteTime(LocalDateTime.now());
            dbd.setDeleteFlag("Y");
            dbd.setPlanCode(monthBookingplanHeader.getPlanCode());
            listDetail.add(dbd);
        }
        try {
            monthBookingplanHeaderMapper.updateMonthBookingplanHeader(list);
            monthBookingplanDetailMapper.updateMonthBookingplanDetail(listDetail);
        } catch (Exception e) {
            System.out.println(e.getMessage() + "删除失败！！！");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new R(500,Boolean.FALSE, "删除失败");
        }
        return new R(200,Boolean.TRUE,"删除成功");
    }

    @Override
    public int commitStatus(String planCode) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        MonthBookingplanHeader m=new MonthBookingplanHeader();
        m.setShippingCode(usercode);
        m.setShippingTime(new Date());
        m.setPlanCode(planCode);
        return monthBookingplanHeaderMapper.commitStatus(m);
    }

    /**
     * 删除月计划申请表(订舱客户提交到市平台)
     *
     * @param rowId 月计划申请表(订舱客户提交到市平台)ID
     * @return 结果
     */
    @Override
    public int deleteMonthBookingplanHeaderById(String rowId)
    {
        return monthBookingplanHeaderMapper.deleteMonthBookingplanHeaderById( rowId);
    };


    /**
     * 批量删除月计划申请表(订舱客户提交到市平台)对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteMonthBookingplanHeaderByIds(Integer[] rowIds)
    {
        return monthBookingplanHeaderMapper.deleteMonthBookingplanHeaderByIds( rowIds);
    }

}
