package com.huazheng.tunny.ocean.controller.eabookingorder;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.eabookingorder.EaContainerInfo;
import com.huazheng.tunny.ocean.service.eabookingorder.EaContainerInfoService;
import com.huazheng.tunny.ocean.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 集装箱信息表
 *
 * <AUTHOR> code generator
 * @date 2025-06-18 10:59:54
 */
@Slf4j
@RestController
@RequestMapping("/eacontainerinfo")
public class EaContainerInfoController {

    @Autowired
    private EaContainerInfoService eaContainerInfoService;

    /**
    *  箱号列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  eaContainerInfoService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return eaContainerInfoService.selectEaContainerInfoListByLike(new Query<>(params));
    }
    /**
    *  查询箱号,到站,箱型
    * @param params
    * @return
    */
    @GetMapping("/getList")
    public R getList(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        return eaContainerInfoService.selectEaContainerInfoList(params);
    }
    /**
    *  查询箱号,到站,箱型
    * @param params
    * @return
    */
    @GetMapping("/list")
    public R list(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        return eaContainerInfoService.selectEaContainerInfoLists(params);
    }

    /**
     * 信息
     * @param containerId
     * @return R
     */
    @GetMapping("/{containerId}")
    public R info(@PathVariable("containerId") Long containerId) {
        EaContainerInfo eaContainerInfo =eaContainerInfoService.selectById(containerId);
        return new R<>(eaContainerInfo);
    }

    /**
     * 保存
     * @param eaContainerInfo
     * @return R
     */
    @PostMapping
    public R save(@RequestBody EaContainerInfo eaContainerInfo) {
        eaContainerInfoService.insert(eaContainerInfo);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param eaContainerInfo
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody EaContainerInfo eaContainerInfo) {
        eaContainerInfoService.updateById(eaContainerInfo);
        return new R<>(Boolean.TRUE);
    }

    

    /**
     * 删除
     * @param containerId
     * @return R
     */
    @GetMapping("/del/{containerId}")
    public R delete(@PathVariable  Long containerId) {
        eaContainerInfoService.deleteById(containerId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param containerIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<Long> containerIds) {
        eaContainerInfoService.deleteBatchIds(containerIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<EaContainerInfo> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<EaContainerInfo> list = eaContainerInfoService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), EaContainerInfo.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }
}
