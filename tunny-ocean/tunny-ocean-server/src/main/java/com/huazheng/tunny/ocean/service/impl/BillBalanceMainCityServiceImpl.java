package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.*;
import com.huazheng.tunny.ocean.api.dto.bpm.FreightAccountCityDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.enums.*;
import com.huazheng.tunny.ocean.api.vo.*;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("billBalanceMainCityService")
public class BillBalanceMainCityServiceImpl extends ServiceImpl<BillBalanceMainCityMapper, BillBalanceMainCity> implements BillBalanceMainCityService {

    @Autowired
    private BillBalanceMainCityMapper billBalanceMainCityMapper;
    @Autowired
    private BillBalanceBindingCityMapper billBalanceBindingCityMapper;
    @Autowired
    private BillBalanceBindingCityService billBalanceBindingCityService;
    @Autowired
    private BillSubPayCityService billSubPayCityService;
    @Autowired
    private SysNoConfigService sysNoConfigService;
    @Autowired
    private FdTradingDetailsService fdTradingDetailsService;
    @Autowired
    private FdBalanceDetailService fdBalanceDetailService;
    @Autowired
    private FdRemittanceRecordService fdRemittanceRecordService;
    @Autowired
    private MessageCenterService messageCenterService;
    @Autowired
    private ShifmanagementMapper shifmanagementMapper;
    @Autowired
    private FdShippingAccountMapper fdShippingAccountMapper;
    @Autowired
    private FdFreightAccountingMapper fdFreightAccountingMapper;
    @Autowired
    private FdBalanceDetailMapper fdBalanceDetailMapper;
    @Autowired
    private FdTradingDetailsMapper fdTradingDetailsMapper;
    @Autowired
    private FdBusCostMapper fdBusCostMapper;
    @Value("${roles.provincePlan}")
    private String provincePlanRoleCode;

    @Value("${roles.provinceCu}")
    private String provinceCuRoleCode;

    private static final String FREFIX = "JSD";

    private static final String ORDER_KEY = "balance_city";


    /**
     * 查询应收账单结算（市）信息
     *
     * @param id 应收账单结算（市）ID
     * @return 应收账单结算（市）信息
     */
    @Override
    public BillBalanceMainCityDetailVO selectBillBalanceMainCityById(Integer id, Integer pageType) {
        // 获取当前登录人信息
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String platformLevel = userInfo.getPlatformLevel();

        BillBalanceMainCityDetailVO billBalanceMainCityDetailVO = billBalanceMainCityMapper.selectBillBalanceMainCityById(id);

        // 查询出结算数据的子帐单信息
        List<BillBalanceMainCitySubDetailVO> billDealWithCityVOS = new ArrayList<>();
        // 市应收
        if (pageType.equals(0)) {
            // 判断是省平台还是市平台
            if (platformLevel.equals(PlatformLevelEnum.PROVINCE.getKey())) {
                billDealWithCityVOS = billBalanceBindingCityService.selectBillSubListByBalanceNo(billBalanceMainCityDetailVO.getBalanceBillNo());
            } else {
                billDealWithCityVOS = billBalanceBindingCityService.selectCustomerBillSubListByBalanceNo(billBalanceMainCityDetailVO.getBalanceBillNo());
            }
        }
        // 市应付
        if (pageType.equals(1)) {
            // 客户
            if (platformLevel.equals(PlatformLevelEnum.CUSTOMER.getKey())) {
                billDealWithCityVOS = billBalanceBindingCityService.selectCustomerBillSubListByBalanceNo(billBalanceMainCityDetailVO.getBalanceBillNo());
            } else if (platformLevel.equals(PlatformLevelEnum.CITY.getKey())) {
                billDealWithCityVOS = billBalanceBindingCityService.selectBillSubListByBalanceNo(billBalanceMainCityDetailVO.getBalanceBillNo());
            } else {
                billDealWithCityVOS = billBalanceBindingCityService.selectProvinceBillSubListByBalanceNo(billBalanceMainCityDetailVO.getBalanceBillNo());
            }
        }
        billBalanceMainCityDetailVO.setBillDealWithCityVOS(billDealWithCityVOS);


        // 查询余额明细
        List<BillBalanceJsListVO> billBalanceJsListVOS = fdBalanceDetailService.selectBillJsList(billBalanceMainCityDetailVO.getBalanceBillNo());
        /*if (CollUtil.isNotEmpty(billBalanceJsListVOS)) {
            for (BillBalanceJsListVO vo : billBalanceJsListVOS
            ) {
                vo.setBcdkAmount(vo.getKdkAmount());
            }
        }*/
        billBalanceMainCityDetailVO.setBillBalanceJsListVOS(billBalanceJsListVOS);

        // 收款余额
        List<BillBalanceSkListVO> billBalanceSkListVOS = fdBalanceDetailService.selectBillSkList(billBalanceMainCityDetailVO.getBalanceBillNo());
        billBalanceMainCityDetailVO.setBillBalanceSkListVOS(billBalanceSkListVOS);


        // 量价捆绑余额
        List<BillBalanceLjkbListVO> billBalanceLjkbListVOS = fdBalanceDetailService.selectBillLjkbList(billBalanceMainCityDetailVO.getBalanceBillNo());
        billBalanceMainCityDetailVO.setBillBalanceLjkbListVOS(billBalanceLjkbListVOS);


        return billBalanceMainCityDetailVO;
    }

    /**
     * 查询应收账单结算（市）列表
     *
     * @param billBalanceMainCity 应收账单结算（市）信息
     * @return 应收账单结算（市）集合
     */
    @Override
    public List<BillBalanceMainCity> selectBillBalanceMainCityList(BillBalanceMainCity billBalanceMainCity) {
        return billBalanceMainCityMapper.selectBillBalanceMainCityList(billBalanceMainCity);
    }


    /**
     * 分页模糊查询应收账单结算（市）列表
     *
     * @return 应收账单结算（市）集合
     */
    @Override
    public Page selectBillBalanceMainCityListByLike(Query query) {
        if (StrUtil.isBlank(query.getOrderByField())) {
            query.setOrderByField("x.create_time");
            query.setAsc(Boolean.FALSE);
        }
        BillBalanceMainCity billBalanceMainCity = BeanUtil.mapToBean(query.getCondition(), BillBalanceMainCity.class, false);
        // 获取当前登录人信息
        SecruityUser userInfo = SecurityUtils.getUserInfo();

        String platformCode = userInfo.getPlatformCode();
        String platformLevel = userInfo.getPlatformLevel();
        billBalanceMainCity.setPlatformLevel(platformLevel);

        if (billBalanceMainCity.getPageType() == 0) {
            billBalanceMainCity.setPlatformCode(platformCode);
        } else {
            billBalanceMainCity.setCustomerCode(platformCode);
        }

        List<BillBalanceMainCityListVO> billBalanceMainCities = new ArrayList<>();
        if ("2".equals(platformLevel) || "3".equals(platformLevel)) {
            billBalanceMainCities = billBalanceMainCityMapper.selectBillBalanceMainCityListByLikeTwo(query, billBalanceMainCity);
        } else {
            billBalanceMainCities = billBalanceMainCityMapper.selectBillBalanceMainCityListByLike(query, billBalanceMainCity);
        }
        // 获取省班列号
        billBalanceMainCities.forEach(billBalanceMainCityListVO -> {
            if (StringUtils.isNotEmpty(billBalanceMainCityListVO.getShiftNos())) {
                billBalanceMainCityListVO.setProvinceTrainNum(billBalanceMainCityMapper.selectProvinceShiftNoByShiftNo(billBalanceMainCityListVO.getShiftNos().split(",")));
                // 班次名称
                billBalanceMainCityListVO.setShiftNames(billBalanceMainCityMapper.selectShiftNamesByShiftNo(billBalanceMainCityListVO.getShiftNos().split(",")));
            }
        });

        if (CollectionUtil.isEmpty(billBalanceMainCities)) {
            return query;
        }

        query.setRecords(billBalanceMainCities);
        return query;
    }

    /*
     * 市平台应收账单结算除已核销之外的
     * */
    @Override
    public Integer selectBillBalanceMainCityCount(BillBalanceMainCity billBalanceMainCity) {
        // 获取当前登录人信息
        SecruityUser userInfo = SecurityUtils.getUserInfo();

        String platformCode = userInfo.getPlatformCode();
        String platformLevel = userInfo.getPlatformLevel();
        billBalanceMainCity.setPlatformLevel(platformLevel);
//        billBalanceMainCity.setPlatformName(platformName);
//        billBalanceMainCity.setPlatformCode(platformCode);
        if (billBalanceMainCity.getPageType() == 0) {
            billBalanceMainCity.setPlatformCode(platformCode);
        } else {
            billBalanceMainCity.setCustomerCode(platformCode);
        }
        List<BillBalanceMainCity> billBalanceMainCities = billBalanceMainCityMapper.selectBillBalanceMainCityList(billBalanceMainCity);
        return billBalanceMainCities.size();
    }

    /**
     * 新增应收账单结算（市）
     *
     * @param billBalanceMainCityDTO 应收账单结算（市）信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R insertBillBalanceMainCity(BillBalanceMainCityDTO billBalanceMainCityDTO) {
        try {
            // 获取当前登录人信息
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String username = userInfo.getRealName();

            String platformCode = userInfo.getPlatformCode();
            String platformLevel = userInfo.getPlatformLevel();
            String platformName = userInfo.getPlatformName();

            // 生成主表信息
            String balanceCode = sysNoConfigService.genNo("JS");
            BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
            BeanUtil.copyProperties(billBalanceMainCityDTO, billBalanceMainCity);
            billBalanceMainCity.setCreateBy(username);
            billBalanceMainCity.setCreateTime(new Date());
            billBalanceMainCity.setBalanceBillNo(balanceCode);

            // 判断提交的是应收还是应付
            // 应收
            if (billBalanceMainCityDTO.getSubmitType().equals(0)) {
                billBalanceMainCity.setPlatformCode(platformCode);
                billBalanceMainCity.setPlatformName(platformName);
                billBalanceMainCity.setPlatformLevel(platformLevel);
            } else if (billBalanceMainCityDTO.getSubmitType().equals(1)) {
                billBalanceMainCity.setCustomerCode(platformCode);
                billBalanceMainCity.setCustomerName(platformName);
                billBalanceMainCity.setPlatformLevel(platformLevel);
            }


            // 抓取班次号集合
            //if (platformLevel.equals(PlatformLevelEnum.CITY.getKey())) {
            //    String selectShiftNoBySubBillNo = billSubPayCityService.selectShiftNoBySubBillNo(billBalanceMainCityDTO.getBillSubCodes());
            //    if (StringUtils.isNotEmpty(selectShiftNoBySubBillNo)) {
            //        billBalanceMainCity.setShiftNos(selectShiftNoBySubBillNo);
            //        // 查询出班次名称
            //        billBalanceMainCity.setShiftNames(billBalanceMainCityMapper.selectShiftNamesByShiftNo(selectShiftNoBySubBillNo.split(",")));
            //    }
            //}
            billBalanceMainCityMapper.insertBillBalanceMainCity(billBalanceMainCity);
//
            //// 生成账单绑定表数据
            //List<String> billSubCodes = billBalanceMainCityDTO.getBillSubCodes();
            //// 修改子账单状态
            //billSubPayCityService.updateStatusBySubBill(billSubCodes, SubBillStatusEnum.YJS.getKey());
//
            //List<BillBalanceBindingCity> billBalanceBindingCities = new ArrayList<>();
            //for (String billSubCode : billSubCodes) {
            //    BillBalanceBindingCity billBalanceBindingCity = new BillBalanceBindingCity();
            //    billBalanceBindingCity.setCreateBy(username);
            //    billBalanceBindingCity.setCreateTime(new Date());
            //    billBalanceBindingCity.setBillBalanceCode(balanceCode);
            //    billBalanceBindingCity.setBillSubCode(billSubCode);
            //    billBalanceBindingCities.add(billBalanceBindingCity);
            //}
            //billBalanceBindingCityService.insertBatch(billBalanceBindingCities);

            // 余额流水表
            //List<FdTradingDetails> tradingDetails = new ArrayList<>();
//
            //// 插入结算余额流水 以及余额明细
            //List<BalanceDetailDTO> jsDetailDTO = billBalanceMainCityDTO.getJsDetailDTO();
            //if (CollectionUtil.isNotEmpty(jsDetailDTO)) {
            //    List<FdTradingDetails> jsDataList = setBillBalanceAmountList(jsDetailDTO, platformCode, platformLevel, platformName,
            //            billBalanceMainCityDTO.getCustomerCode(),
            //            billBalanceMainCityDTO.getCustomerName(), balanceCode, "0");
            //    if (CollectionUtil.isNotEmpty(jsDataList)) {
            //        tradingDetails.addAll(jsDataList);
            //    }
            //    // 修改余额明细
            //    jsDetailDTO.forEach(balanceDetailDTO -> {
            //        fdBalanceDetailService.updateBalanceAmount(balanceDetailDTO);
            //    });
            //}
//
            //// 插入量价捆绑余额流水
            //List<BalanceDetailDTO> ljDetailDTO = billBalanceMainCityDTO.getLjDetailDTO();
            //if (CollectionUtil.isNotEmpty(ljDetailDTO)) {
            //    List<FdTradingDetails> ljDataList = setBillBalanceAmountList(ljDetailDTO, platformCode, platformLevel, platformName,
            //            billBalanceMainCityDTO.getCustomerCode(),
            //            billBalanceMainCityDTO.getCustomerName(), balanceCode, "1");
            //    if (CollectionUtil.isNotEmpty(ljDataList)) {
            //        tradingDetails.addAll(ljDataList);
            //    }
            //    // 修改余额明细
            //    ljDetailDTO.forEach(balanceDetailDTO -> {
            //        fdBalanceDetailService.updateBalanceAmount(balanceDetailDTO);
            //    });
            //}
//
            //// 插入收款余额流水
            //List<BalanceDetailDTO> skDetailDTO = billBalanceMainCityDTO.getSkDetailDTO();
            //if (CollectionUtil.isNotEmpty(skDetailDTO)) {
            //    List<FdTradingDetails> skDataList = setBillBalanceAmountList(skDetailDTO, platformCode, platformLevel, platformName,
            //            billBalanceMainCityDTO.getCustomerCode(),
            //            billBalanceMainCityDTO.getCustomerName(), balanceCode, "2");
            //    if (CollectionUtil.isNotEmpty(skDataList)) {
            //        tradingDetails.addAll(skDataList);
            //    }
            //    // 修改余额明细
            //    skDetailDTO.forEach(balanceDetailDTO -> {
            //        fdBalanceDetailService.updateBalanceAmount(balanceDetailDTO);
            //    });
            //}
//
            //if (CollectionUtil.isNotEmpty(tradingDetails)) {
            //    fdTradingDetailsService.insertBatch(tradingDetails);
            //}
            return new R<>(billBalanceMainCity.getId());
        } catch (Exception e) {
            log.error("创建市应收账单结算报错：" + e);
            e.printStackTrace();
            R r = new R();
            r.setCode(500);
            r.setMsg("系统异常");
            return r;
        }
    }

    /**
     * 修改应收账单结算（市）
     *
     * @param billBalanceMainCityDTO 应收账单结算（市）信息
     * @return 结果
     */
    @Transactional
    @Override
    public R updateBillBalanceMainCity(BillBalanceMainCityDTO billBalanceMainCityDTO) {
        try {
            // 获取当前登录人信息
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String username = userInfo.getRealName();
            String platformLevel = userInfo.getPlatformLevel();
            String platformCode = userInfo.getPlatformCode();
            String platformName = userInfo.getPlatformName();

            // 修改主表信息
            BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
            BeanUtil.copyProperties(billBalanceMainCityDTO, billBalanceMainCity);

            // 判断是否完全抵扣
            int compareTo = billBalanceMainCity.getActualAmout().compareTo(new BigDecimal(0));
            if (compareTo == 0) {
                // 如果是完全抵扣数据 将结算单变为已核销数据  将余额减少
                // 查询出余额明细数据
                List<FdTradingDetails> tradingDetails = billBalanceMainCityMapper.selectTradingListByJsCode(billBalanceMainCity.getBalanceBillNo());
                if (CollectionUtil.isNotEmpty(tradingDetails)) {
                    for (FdTradingDetails fdTradingDetails : tradingDetails) {
                        // 修改余额明细
                        billBalanceMainCityMapper.updateBalanceDetailBySyAmount(fdTradingDetails.getBalanceId(), fdTradingDetails.getTransactionAmount());
                        // 修改流水数据的状态
                        fdTradingDetails.setTradingStatus("1");
                    }
                    fdTradingDetailsService.updateBatchById(tradingDetails);
                }
                // 修改余额明细状态
                billBalanceMainCity.setBalanceStatus(BalanceStatusEnum.VERIFIED.getKey());
            }

            // 判断当前登录人平台
            List<String> userNames = new ArrayList<>();
            // 消息内容
            StringBuffer sb = new StringBuffer();
            // 通知内容
            StringBuffer psb = new StringBuffer();
            // 业务类型
            String businessType = "";
            String businessName = "";
            if (billBalanceMainCityDTO.getSubmitType().equals(1) && platformLevel.equals("1") && billBalanceMainCityDTO.getDeductionAmout() != null && billBalanceMainCityDTO.getDeductionAmout().compareTo(new BigDecimal(0)) > 0 && billBalanceMainCityDTO.getPlatformCode().equals("MP210800001") && billBalanceMainCityDTO.getBalanceStatus().equals(BalanceStatusEnum.WAITPAY.getKey())) {

                // 市平台查询省平台计划岗
                userNames = billBalanceMainCityMapper.selectMessageUserByRoleCode(provincePlanRoleCode);
                // 拼接推送内容
                sb.append(platformName).append("提交了应收账单结算").append("（").append(billBalanceMainCityDTO.getBalanceBillName()).append("），请及时登录PC端在【计划岗管理-应收账单结算（P）】中审核。");
                businessType = MessageBusinessEnum.BILL_BALANCE.getKey();
                businessName = MessageBusinessEnum.BILL_BALANCE.getValue();
                psb.append("您有新的应收账单结算待审核，请及时处理。");
            }

            /*if (billBalanceMainCityDTO.getSubmitType().equals(1) && platformLevel.equals("2")) {
                userNames = billBalanceMainCityMapper.selectMessageUserByRoleCode(provinceCuRoleCode);
                // 拼接推送内容
                sb.append(username).append("提交了应付账单结算").append("（").append(billBalanceMainCityDTO.getBalanceBillName()).append("），请及时登录PC端在【客服岗管理-应付账单结算（S）】中确认。");
                businessType = MessageBusinessEnum.PAY_BILL.getKey();
                businessName = MessageBusinessEnum.PAY_BILL.getValue();
                psb.append("您有新的应付账单结算待确认，请及时处理。");
            }*/

            // 生成消息数据
            if (CollectionUtil.isNotEmpty(userNames)) {
                List<MessageCenter> addMessageDTOS = new ArrayList<>();
                for (String userNameRo : userNames) {
                    MessageCenter addMessageDTO = new MessageCenter();
                    addMessageDTO.setReceiveUser(userNameRo);
                    addMessageDTO.setModuleType(MessageTypeEnum.SETTLEMENT.getKey());
                    addMessageDTO.setModuleName(MessageTypeEnum.SETTLEMENT.getValue());
                    addMessageDTO.setBusinessType(businessType);
                    addMessageDTO.setBusinessName(businessName);
                    addMessageDTO.setBusinessId(String.valueOf(billBalanceMainCityDTO.getId()));
                    addMessageDTO.setContent(sb.toString());
                    addMessageDTO.setIsRead(0);
                    addMessageDTO.setCreateName(username);
                    addMessageDTO.setType("0");
                    addMessageDTO.setRemark(sb.toString());
                    addMessageDTO.setDelFlag("N");
                    addMessageDTO.setCreateTime(LocalDateTime.now());
                    addMessageDTOS.add(addMessageDTO);
                }
                messageCenterService.addMessage(addMessageDTOS, psb.toString(), businessName);
            }

            // 修改账单状态
            if (CollUtil.isNotEmpty(billBalanceMainCityDTO.getBillDealWithCityVOS())) {
                for (BillBalanceMainCitySubDetailVO vo : billBalanceMainCityDTO.getBillDealWithCityVOS()) {
                    if (StrUtil.isNotBlank(vo.getBillCode())) {
                        updateBillStatusBySubBillCode(vo.getBillCode(), billBalanceMainCity.getBalanceStatus());
                    }
                }
            }

            billBalanceMainCity.setUpdateBy(username);
            billBalanceMainCity.setUpdateTime(new Date());
            billBalanceMainCityMapper.updateBillBalanceMainCity(billBalanceMainCity);
            return new R<>(Boolean.TRUE);
        } catch (Exception e) {
            log.error("修改市应收账单结算报错：" + e);
            e.printStackTrace();
            R r = new R();
            r.setCode(500);
            r.setMsg("系统异常");
            return r;
        }
    }

    @Override
    @Transactional
    public R updateRevoke(Integer id) {
        try {
            R r = new R();
            // 查询出主数据
            BillBalanceMainCityDetailVO billBalanceMainCityDetailVO = billBalanceMainCityMapper.selectBillBalanceMainCityById(id);
            if (ObjectUtil.isNull(billBalanceMainCityDetailVO)) {
                r.setMsg("未查询出主数据");
                r.setCode(500);
                return r;
            }
            updateBillStatusByCode(billBalanceMainCityDetailVO.getBalanceBillNo(), BalanceStatusEnum.NOTSUB.getKey(), null);

            // 修改数据状态为未提交
            BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
            BeanUtil.copyProperties(billBalanceMainCityDetailVO, billBalanceMainCity);
            billBalanceMainCity.setBalanceStatus(BalanceStatusEnum.NOTSUB.getKey());
            billBalanceMainCityMapper.updateBillBalanceMainCity(billBalanceMainCity);
            return r;
        } catch (Exception e) {
            log.error("修改市应收账单结算报错-撤销：" + e);
            e.printStackTrace();
            R r = new R();
            r.setCode(500);
            r.setMsg("系统异常");
            return r;
        }
    }

    /**
     * 绑定子帐单
     *
     * @param bindingSubBillDTO
     * @return
     */
    @Override
    @Transactional
    public R bindingSubBill(BindingSubBillDTO bindingSubBillDTO) {
        // 查询出结算单数据
        try {
            // 获取当前登录人信息
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String username = userInfo.getRealName();
            String platformLevel = userInfo.getPlatformLevel();

            BillBalanceMainCityDetailVO billBalanceMainCityDetailVO = billBalanceMainCityMapper.selectBillBalanceMainCityById(bindingSubBillDTO.getId());
            if (ObjectUtil.isNull(billBalanceMainCityDetailVO)) {
                return R.error("未查询出结算单数据");
            }
            // 省平台应付校验子账单
            // 可选择同一个省级班列号下的多个货源组织单位的应付子账单；
            // 或者可以选择同一个货源组织单位下的多个不同省级班列号的多个应付子账单；
            if (platformLevel.equals("2") && bindingSubBillDTO.getPageType() == 1) {
                //查询是否存在已有的账单班次
                List<Shifmanagement> oldList = shifmanagementMapper.selectShifmanagementByBillBalanceMainCityId(bindingSubBillDTO.getId());
                if (CollUtil.isNotEmpty(oldList)) {
                    List<Shifmanagement> newList = shifmanagementMapper.selectShifmanagementByBillSubCodes(bindingSubBillDTO.getBillSubCodes());
                    if (CollUtil.isNotEmpty(newList)) {
                        for (Shifmanagement oldObj : oldList) {
                            for (Shifmanagement newObj : newList) {
                                if (StrUtil.isBlank(oldObj.getProvinceShiftNo())) {
                                    return R.error("原账单班次省级班列号异常，请联系管理员：" + oldObj.getShiftId());
                                }
                                if (StrUtil.isBlank(newObj.getProvinceShiftNo())) {
                                    return R.error("原账单班次省级班列号异常，请联系管理员：" + newObj.getShiftId());
                                }
                                if (!oldObj.getPlatformCode().equals(newObj.getPlatformCode()) && !oldObj.getProvinceShiftNo().equals(newObj.getProvinceShiftNo())) {
                                    return R.error("请选择同一个省级班列号的应付子账单或同一个货源组织单位的应付子账单！");
                                }
                            }
                        }
                    }
                }
            }

            // 绑定子帐单数据
            // 生成账单绑定表数据
            List<String> billSubCodes = bindingSubBillDTO.getBillSubCodes();
            String selectShiftNoBySubBillNo = "";
            BigDecimal sumAmount = new BigDecimal("0.00");
            // 修改子账单状态
            // 应收
            if (platformLevel.equals("1") && bindingSubBillDTO.getPageType() == 0) {
                billSubPayCityService.updateCustomerStatusBySubBill(billSubCodes, BalanceStatusEnum.NOTSUB.getKey());
            }
            // 应付
            if (platformLevel.equals("1") && bindingSubBillDTO.getPageType() == 1) {
                billSubPayCityService.updateStatusBySubBill(billSubCodes, BalanceStatusEnum.NOTSUB.getKey());
                billSubPayCityService.updateCustomerStatusBySubBill(billSubCodes, BalanceStatusEnum.NOTSUB.getKey());
            }
            if (platformLevel.equals("2") && bindingSubBillDTO.getPageType() == 1) {
                billSubPayCityService.updateProvinceStatusBySubBill(billSubCodes, BalanceStatusEnum.NOTSUB.getKey());
            }

            List<BillBalanceBindingCity> billBalanceBindingCities = new ArrayList<>();
            for (String billSubCode : billSubCodes) {
                BillBalanceBindingCity billBalanceBindingCity = new BillBalanceBindingCity();
                billBalanceBindingCity.setCreateBy(username);
                billBalanceBindingCity.setCreateTime(new Date());
                billBalanceBindingCity.setBillBalanceCode(billBalanceMainCityDetailVO.getBalanceBillNo());
                billBalanceBindingCity.setBillSubCode(billSubCode);
                billBalanceBindingCities.add(billBalanceBindingCity);
            }
            billBalanceBindingCityService.insertBatch(billBalanceBindingCities);

            // 抓取班次号集合
            List<String> subBillNos = billBalanceBindingCityService.selectBindingSubBillByBalanceCode(billBalanceMainCityDetailVO.getBalanceBillNo());
            List<String> subCodes = bindingSubBillDTO.getBillSubCodes();
            if (CollectionUtil.isNotEmpty(subBillNos)) {
                subCodes.addAll(subBillNos);
            }


            if (platformLevel.equals("1") && bindingSubBillDTO.getPageType() == 0) {
                selectShiftNoBySubBillNo = billSubPayCityService.selectCustomerShiftNoBySubBillNo(subCodes);
                sumAmount = billSubPayCityService.selectCustomerSumAmountByBalanceNo(billBalanceMainCityDetailVO.getBalanceBillNo());
            }
            // 应付
            if (platformLevel.equals("1") && bindingSubBillDTO.getPageType() == 1) {
                selectShiftNoBySubBillNo = billSubPayCityService.selectShiftNoBySubBillNo(subCodes);
                sumAmount = billSubPayCityService.selectSumAmountByBalanceNo(billBalanceMainCityDetailVO.getBalanceBillNo());
            }
            if (platformLevel.equals("2") && bindingSubBillDTO.getPageType() == 1) {
                selectShiftNoBySubBillNo = billSubPayCityService.selectProvinceShiftNoBySubBillNo(subCodes);
                sumAmount = billSubPayCityService.selectProvinceSumAmountByBalanceNo(billBalanceMainCityDetailVO.getBalanceBillNo());
            }

            if (StringUtils.isNotEmpty(selectShiftNoBySubBillNo)) {
                billBalanceMainCityDetailVO.setShiftNos(selectShiftNoBySubBillNo);
                // 查询出班次名称
                billBalanceMainCityDetailVO.setShiftNames(billBalanceMainCityMapper.selectShiftNamesByShiftNo(selectShiftNoBySubBillNo.split(",")));
                // 查询出省班次号
                //billBalanceMainCityDetailVO.setProvinceTrainNum(billBalanceMainCityMapper.selectProvinceNumByShiftNo(selectShiftNoBySubBillNo.split(",")));
                // 查询出发运时间
                billBalanceMainCityDetailVO.setPostDate(billBalanceMainCityMapper.selectPostDateByShiftNo(selectShiftNoBySubBillNo.split(",")));
            }
            // 根据结算单号查询出绑定的子帐单总金额
            billBalanceMainCityDetailVO.setBillAmount(sumAmount == null ? new BigDecimal("0.00") : sumAmount);
            BigDecimal deductionAmount = billBalanceMainCityDetailVO.getDeductionAmout() == null ? new BigDecimal("0.00") : billBalanceMainCityDetailVO.getDeductionAmout();
            BigDecimal subtract = sumAmount.subtract(deductionAmount);
            billBalanceMainCityDetailVO.setActualAmout(subtract);
            BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
            BeanUtil.copyProperties(billBalanceMainCityDetailVO, billBalanceMainCity);
            billBalanceMainCityMapper.updateBillBalanceMainCity(billBalanceMainCity);
            return R.success();
        } catch (Exception e) {
            log.error("绑定子帐单数据报错：" + e);
            e.printStackTrace();
            return R.error("系统异常");
        }
    }

    /**
     * 解除子账单绑定
     *
     * @param bindingSubBillDTO
     * @return
     */
    @Override
    @Transactional
    public R removeBindingSubBill(BindingSubBillDTO bindingSubBillDTO) {
        try {

            BillBalanceMainCityDetailVO billBalanceMainCityDetailVO = billBalanceMainCityMapper.selectBillBalanceMainCityById(bindingSubBillDTO.getId());
            if (ObjectUtil.isNull(billBalanceMainCityDetailVO)) {
                return R.error("未查询出结算单数据");
            }

            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String username = userInfo.getRealName();
            String platformLevel = userInfo.getPlatformLevel();
            if (platformLevel.equals("2") && bindingSubBillDTO.getPageType() == 1 && CollUtil.isNotEmpty(bindingSubBillDTO.getBillSubCodes())) {
                List<Shifmanagement> list = shifmanagementMapper.selectShifmanagementWithoutDel(bindingSubBillDTO.getId(), bindingSubBillDTO.getBillSubCodes().get(0));
                if (CollUtil.isEmpty(list)) {
                    List<FdTradingDetails> fdTradingDetails = fdTradingDetailsMapper.selectFdTradingDetailsBybillSubCode(bindingSubBillDTO.getId(), bindingSubBillDTO.getBillSubCodes().get(0));
                    if (CollUtil.isNotEmpty(fdTradingDetails)) {
                        return R.error("该账单已选择抵扣明细，请先删除抵扣明细：" + bindingSubBillDTO.getBillSubCodes().get(0));
                    }
                }
            }
            // 修改绑定的账单信息
            List<String> billSubCodes = bindingSubBillDTO.getBillSubCodes();
            billBalanceBindingCityService.updateDeletedBillBalanceBindingByBillCode(billSubCodes, billBalanceMainCityDetailVO.getBalanceBillNo());
            // 修改绑定的子帐单状态
            // 应收
            if (platformLevel.equals("1") && bindingSubBillDTO.getPageType() == 0) {
                billSubPayCityService.updateCustomerStatusBySubBill(billSubCodes, SubBillStatusEnum.DQR.getKey());
            }
            // 应付
            if (platformLevel.equals("1") && bindingSubBillDTO.getPageType() == 1) {
                billSubPayCityService.updateStatusBySubBill(billSubCodes, SubBillStatusEnum.DQR.getKey());
            }
            if (platformLevel.equals("2") && bindingSubBillDTO.getPageType() == 1) {
                billSubPayCityService.updateProvinceStatusBySubBill(billSubCodes, SubBillStatusEnum.DQR.getKey());
            }


            // 抓取班次号集合
            String selectShiftNoBySubBillNo = "";
            BigDecimal sumAmount = new BigDecimal("0.00");
            List<String> subBillNos = billBalanceBindingCityService.selectBindingSubBillByBalanceCode(billBalanceMainCityDetailVO.getBalanceBillNo());
            if (CollectionUtil.isEmpty(subBillNos)) {
                billBalanceMainCityDetailVO.setShiftNos(null);
                billBalanceMainCityDetailVO.setShiftNames(null);
            } else {
                if (platformLevel.equals("1") && bindingSubBillDTO.getPageType() == 0) {
                    selectShiftNoBySubBillNo = billSubPayCityService.selectCustomerShiftNoBySubBillNo(subBillNos);
                    sumAmount = billSubPayCityService.selectCustomerSumAmountByBalanceNo(billBalanceMainCityDetailVO.getBalanceBillNo());
                }
                // 应付
                if (platformLevel.equals("1") && bindingSubBillDTO.getPageType() == 1) {
                    selectShiftNoBySubBillNo = billSubPayCityService.selectShiftNoBySubBillNo(subBillNos);
                    sumAmount = billSubPayCityService.selectSumAmountByBalanceNo(billBalanceMainCityDetailVO.getBalanceBillNo());
                }
                if (platformLevel.equals("2") && bindingSubBillDTO.getPageType() == 1) {
                    selectShiftNoBySubBillNo = billSubPayCityService.selectProvinceShiftNoBySubBillNo(subBillNos);
                    sumAmount = billSubPayCityService.selectProvinceSumAmountByBalanceNo(billBalanceMainCityDetailVO.getBalanceBillNo());
                }
                if (StringUtils.isNotEmpty(selectShiftNoBySubBillNo)) {
                    billBalanceMainCityDetailVO.setShiftNos(selectShiftNoBySubBillNo);
                    // 查询出班次名称
                    billBalanceMainCityDetailVO.setShiftNames(billBalanceMainCityMapper.selectShiftNamesByShiftNo(selectShiftNoBySubBillNo.split(",")));
                    // 查询出省班次号
                    //billBalanceMainCityDetailVO.setProvinceTrainNum(billBalanceMainCityMapper.selectProvinceNumByShiftNo(selectShiftNoBySubBillNo.split(",")));
                    // 查询出发运时间
                    billBalanceMainCityDetailVO.setPostDate(billBalanceMainCityMapper.selectPostDateByShiftNo(selectShiftNoBySubBillNo.split(",")));
                }
            }
            // 根据结算单号查询出绑定的子帐单总金额
            if (sumAmount == null) {
                sumAmount = new BigDecimal("0.00");
            }
            billBalanceMainCityDetailVO.setBillAmount(sumAmount);
            BigDecimal deductionAmout = billBalanceMainCityDetailVO.getDeductionAmout() == null ? new BigDecimal("0.00") : billBalanceMainCityDetailVO.getDeductionAmout();
            BigDecimal subtract = sumAmount.subtract(deductionAmout);
            billBalanceMainCityDetailVO.setActualAmout(subtract);
            BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
            BeanUtil.copyProperties(billBalanceMainCityDetailVO, billBalanceMainCity);
            billBalanceMainCityMapper.updateBillBalanceMainCity(billBalanceMainCity);
            return R.success();
        } catch (Exception e) {
            log.error("解除绑定子帐单数据报错：" + e);
            return R.error("系统异常");
        }
    }


    /**
     * 删除应收账单结算（市）
     *
     * @param id 应收账单结算（市）ID
     * @return 结果
     */
    public int deleteBillBalanceMainCityById(Integer id) {
        return billBalanceMainCityMapper.deleteBillBalanceMainCityById(id);
    }

    ;


    /**
     * 批量删除应收账单结算（市）对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBillBalanceMainCityByIds(Integer[] ids) {
        return billBalanceMainCityMapper.deleteBillBalanceMainCityByIds(ids);
    }

    /**
     * 查询余额明细列表
     * shang
     *
     * @param query
     * @return
     */
    @Override
    public Page selectBalanceDetail(Query query) {
        QueryBalanceDetailDTO queryBalanceDetailDTO = BeanUtil.mapToBean(query.getCondition(), QueryBalanceDetailDTO.class, false);

        // 获取当前登陆人信息
        /*SecruityUser userInfo = SecurityUtils.getUserInfo();
        String platformCode = userInfo.getPlatformCode();
        String platformLevel = userInfo.getPlatformLevel();
        String platformName = userInfo.getPlatformName();

        queryBalanceDetailDTO.setPlatformCode(platformCode);
        queryBalanceDetailDTO.setPlatformLevel(platformLevel);
        queryBalanceDetailDTO.setPlatformName(platformName);*/
        // 查询已绑定的余额ID
        List<Integer> traIds = billBalanceMainCityMapper.selectBalanceIdsByJsCode(queryBalanceDetailDTO.getBalanceBillNo());
        queryBalanceDetailDTO.setNotIds(traIds);

        // 判断查询类型
        query.setRecords(billBalanceMainCityMapper.selectBalanceDetailList(query, queryBalanceDetailDTO));
        return query;
    }

    /**
     * 绑定余额
     *
     * @param balanceDetailDTOS
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R bindingBalance(List<BalanceDetailDTO> balanceDetailDTOS) {
        try {
            if (CollUtil.isEmpty(balanceDetailDTOS)) {
                return R.error("余额明细为空！");
            }
            String paymentType = balanceDetailDTOS.get(0).getPaymentType();
            // 查询出结算单数据
            BillBalanceMainCityDetailVO billBalanceMainCityDetailVO = billBalanceMainCityMapper.selectBillBalanceMainCityById(balanceDetailDTOS.get(0).getId());
            if (ObjectUtils.isEmpty(billBalanceMainCityDetailVO)) {
                return R.error("为查询出结算单数据");
            }
            //省平台应付
            if ("ztdl".equals(billBalanceMainCityDetailVO.getPlatformCode())) {
                List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementByBillBalanceMainCityId(balanceDetailDTOS.get(0).getId());
                if (CollUtil.isNotEmpty(shifmanagements)) {
                    // 提前将所有 shifmanagement 的 platformCode 放入一个集合中，便于后续查找
                    Set<String> platformCodes = shifmanagements.stream().map(Shifmanagement::getPlatformCode).collect(Collectors.toSet());
                    Set<String> customerCodes = new HashSet<>();
                    for (BalanceDetailDTO balanceDetailDTO : balanceDetailDTOS) {
                        FdBalanceDetail detail = fdBalanceDetailMapper.selectFdBalanceDetailListById(balanceDetailDTO.getBalanceId());
                        if (detail != null) {
                            balanceDetailDTO.setCustomerCode(detail.getCustomerCode());
                            customerCodes.add(detail.getCustomerCode());
                            if (!platformCodes.contains(detail.getCustomerCode())) {
                                return R.error("请选择账单所属平台对应的余额！");
                            }
                        }
                    }
                    List<String> customerCodeList = new ArrayList<>(customerCodes);
                    switch (paymentType) {
                        case "0":
                            //结算余额必须全部抵扣，不能分开使用，如果应付金额小于抵扣金额总和，则不允许抵扣
                            for (String customerCode : customerCodeList) {
                                BigDecimal billAmount = billBalanceMainCityMapper.getBillAmount(balanceDetailDTOS.get(0).getId(), customerCode);
                                BigDecimal deductionAmount = billBalanceMainCityMapper.getDeductionAmount(balanceDetailDTOS.get(0).getId(), customerCode);
                                //对应平台应付金额
                                BigDecimal balanceAmount = billAmount.subtract(deductionAmount);
                                BigDecimal bcdk = balanceDetailDTOS.stream().filter(balanceDetailDTO -> customerCode.equals(balanceDetailDTO.getCustomerCode())).map(balanceDetailDTO -> balanceDetailDTO.getBcdkAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                                if (balanceAmount.compareTo(bcdk) < 0) {
                                    return R.error("应付金额小于结算余额总和，不允许抵扣！");
                                }
                            }
                            break;
                        default:
                            for (String customerCode : customerCodeList) {
                                BigDecimal billAmount = billBalanceMainCityMapper.getBillAmount(balanceDetailDTOS.get(0).getId(), customerCode);
                                BigDecimal deductionAmount = billBalanceMainCityMapper.getDeductionAmount(balanceDetailDTOS.get(0).getId(), customerCode);
                                BigDecimal balanceAmount = billAmount.subtract(deductionAmount);
                                if (balanceAmount.compareTo(BigDecimal.ZERO) <= 0) {
                                    balanceDetailDTOS.removeAll(balanceDetailDTOS.stream().filter(balanceDetailDTO -> balanceDetailDTO.getCustomerCode().equals(customerCode)).collect(Collectors.toList()));
                                } else {
                                    for (BalanceDetailDTO balanceDetailDTO : balanceDetailDTOS) {
                                        if (customerCode.equals(balanceDetailDTO.getCustomerCode()) && balanceDetailDTO.getBcdkAmount() != null) {
                                            if (balanceDetailDTO.getBcdkAmount().compareTo(balanceAmount) < 0) {
                                                balanceAmount = balanceAmount.subtract(balanceDetailDTO.getBcdkAmount());
                                            } else {
                                                balanceDetailDTO.setBcdkAmount(balanceAmount);
                                                balanceAmount = BigDecimal.ZERO;
                                            }
                                        }
                                    }
                                }
                            }
                            break;
                    }
                }
            }
            BigDecimal deductionAmoutAdd = addBindingBalance(balanceDetailDTOS, billBalanceMainCityDetailVO);

            // 计算结算单余额
            BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
            BeanUtil.copyProperties(billBalanceMainCityDetailVO, billBalanceMainCity);
            BigDecimal deductionAmout = billBalanceMainCity.getDeductionAmout() == null ? new BigDecimal("0.00") : billBalanceMainCity.getDeductionAmout();
            deductionAmout = deductionAmout.add(deductionAmoutAdd);
            billBalanceMainCity.setDeductionAmout(deductionAmout);
            BigDecimal actualAmoutSubtract = billBalanceMainCity.getBillAmount().subtract(deductionAmoutAdd);
            billBalanceMainCity.setActualAmout(actualAmoutSubtract);
            billBalanceMainCityMapper.updateBillBalanceMainCity(billBalanceMainCity);
            return R.success();
        } catch (Exception e) {
            log.error("绑定余额明细数据报错：" + e);
            return R.error("绑定余额明细异常");
        }
    }

    private BigDecimal addBindingBalance(List<BalanceDetailDTO> balanceDetailDTOS, BillBalanceMainCityDetailVO billBalanceMainCityDetailVO) {
        BigDecimal deductionAmoutAdd = BigDecimal.ZERO;
        for (BalanceDetailDTO balanceDetailDTO : balanceDetailDTOS) {
            if (balanceDetailDTO.getBcdkAmount().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            deductionAmoutAdd = deductionAmoutAdd.add(balanceDetailDTO.getBcdkAmount());
            // 存入余额流水
            FdTradingDetails fdTradingDetails = new FdTradingDetails();

            fdTradingDetails.setBalanceId(balanceDetailDTO.getBalanceId());
            fdTradingDetails.setTradeSerialNumber(sysNoConfigService.genNo("TS"));
            fdTradingDetails.setPlatformCode(billBalanceMainCityDetailVO.getPlatformCode());
            fdTradingDetails.setPlatformName(billBalanceMainCityDetailVO.getPlatformName());
            fdTradingDetails.setCustomerCode(billBalanceMainCityDetailVO.getCustomerCode());
            fdTradingDetails.setCustomerName(billBalanceMainCityDetailVO.getCustomerName());
            if (billBalanceMainCityDetailVO.getPlatformCode().substring(0, 2).equals("MC")) {
                fdTradingDetails.setPlatformLevel("0");
            }
            if (billBalanceMainCityDetailVO.getPlatformCode().substring(0, 2).equals("MP")) {
                fdTradingDetails.setPlatformLevel("1");
            }
            if (billBalanceMainCityDetailVO.getPlatformCode().equals("ztdl")) {
                fdTradingDetails.setPlatformLevel("2");
            }
            fdTradingDetails.setIncomeFlag("0");
            fdTradingDetails.setTradingHours(LocalDateTime.now());
            fdTradingDetails.setPaymentType(balanceDetailDTO.getPaymentType());
            fdTradingDetails.setIsAdd("0");
            fdTradingDetails.setTransactionAmount(balanceDetailDTO.getBcdkAmount());
            // 汇款记录编码待确定
            // fdTradingDetails.setRemittanceRecordCode();
            fdTradingDetails.setRemittanceRecordCode(balanceDetailDTO.getReceiptNo());
            fdTradingDetails.setDeductionBillCode(billBalanceMainCityDetailVO.getBalanceBillNo());
            fdTradingDetails.setTradingStatus("2");
            // 增加流水
            fdTradingDetailsService.insert(fdTradingDetails);
            // 修改余额明细数据
            billBalanceMainCityMapper.updateBalanceDetailByAdd(balanceDetailDTO.getBalanceId(), balanceDetailDTO.getBcdkAmount());

        }
        return deductionAmoutAdd;
    }

    /**
     * 解除绑定余额数据
     *
     * @param balanceDetailDTO
     * @return
     */
    @Override
    public R removeBalance(BalanceDetailDTO balanceDetailDTO) {
        try {
            // 查询出结算单数据
            BillBalanceMainCityDetailVO billBalanceMainCityDetailVO = billBalanceMainCityMapper.selectBillBalanceMainCityById(balanceDetailDTO.getId());
            if (ObjectUtils.isEmpty(billBalanceMainCityDetailVO)) {
                return R.error("为查询出结算单数据");
            }
            // 获取当前登录人信息
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String platformCode = userInfo.getPlatformCode();

            // 查询余额流水
            QueryBalanceDetailVO queryBalanceDetailVO = billBalanceMainCityMapper.selectBalanceDetailByRemove(billBalanceMainCityDetailVO.getBalanceBillNo(), balanceDetailDTO.getBalanceId(), balanceDetailDTO.getPageType(), balanceDetailDTO.getPaymentType(), platformCode, balanceDetailDTO.getReceiptNo());

            // 修改余额明细数据
            billBalanceMainCityMapper.updateBalanceDetailByRemove(balanceDetailDTO.getBalanceId(), queryBalanceDetailVO.getKdkAmount());
            // 计算结算单余额
            BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
            BeanUtil.copyProperties(billBalanceMainCityDetailVO, billBalanceMainCity);
            BigDecimal deductionAmout = billBalanceMainCity.getDeductionAmout();
            if (ObjectUtils.isEmpty(deductionAmout)) {
                return R.error("账单金额异常");
            }
            BigDecimal deductionAmoutRemove = deductionAmout.subtract(queryBalanceDetailVO.getKdkAmount());
            billBalanceMainCity.setDeductionAmout(deductionAmoutRemove);
            BigDecimal actualAmoutAdd = billBalanceMainCity.getBillAmount().subtract(deductionAmoutRemove);
            billBalanceMainCity.setActualAmout(actualAmoutAdd);
            billBalanceMainCityMapper.updateBillBalanceMainCity(billBalanceMainCity);
            // 删除余额流水
            billBalanceMainCityMapper.removeBalanceTradingById(queryBalanceDetailVO.getId());
            return R.success();
        } catch (Exception e) {
            log.error("绑定余额明细数据报错：" + e);
            e.printStackTrace();
            return R.error("系统异常");
        }
    }

    /**
     * 修改结算单绑定的抵扣数
     *
     * @param balanceDetailDTO
     * @return
     */
    @Override
    public R updateBalanceAmount(BalanceDetailDTO balanceDetailDTO) {
        R r = new R();
        try {
            if (null == balanceDetailDTO.getBcdkAmount() || balanceDetailDTO.getBcdkAmount().equals("")) {
                balanceDetailDTO.setBcdkAmount(new BigDecimal("0"));
            }

            // 查询出结算单数据
            BillBalanceMainCityDetailVO billBalanceMainCityDetailVO = billBalanceMainCityMapper.selectBillBalanceMainCityById(balanceDetailDTO.getId());
            if (ObjectUtils.isEmpty(billBalanceMainCityDetailVO)) {
                r.setMsg("未查询出结算单数据");
                r.setCode(500);
                return r;
            }

            // 获取当前登陆人信息
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String platformCode = userInfo.getPlatformCode();
            // 判断是应收应付
            // 查询余额流水
            QueryBalanceDetailVO queryBalanceDetailVO = billBalanceMainCityMapper.selectBalanceDetailByRemove(billBalanceMainCityDetailVO.getBalanceBillNo(), balanceDetailDTO.getBalanceId(), balanceDetailDTO.getPageType(), balanceDetailDTO.getPaymentType(), platformCode, balanceDetailDTO.getReceiptNo());

            if (ObjectUtils.isEmpty(queryBalanceDetailVO)) {
                r.setMsg("未查询出结算单余额流水数据");
                r.setCode(500);
                return r;
            }
            // 修改余额数据
            billBalanceMainCityMapper.updateBalanceDetailByAmount(balanceDetailDTO.getBcdkAmount(), queryBalanceDetailVO.getId());

            // 计算数据进行修改
            // 要修改的数量
            BigDecimal bcdkAmount = balanceDetailDTO.getBcdkAmount();
            // 历史数量
            BigDecimal kdkAmount = queryBalanceDetailVO.getKdkAmount();
            // 比对大小
            int compareTo = bcdkAmount.compareTo(kdkAmount);
            if (compareTo < 0) {
                // bcdkAmount 小于 kdkAmount
                BigDecimal add = kdkAmount.subtract(bcdkAmount);
                billBalanceMainCityMapper.updateBalanceDetailByRemove(balanceDetailDTO.getBalanceId(), add);
            } else if (compareTo > 0) {
                // bcdkAmount 大于 kdkAmount
                BigDecimal subtract = bcdkAmount.subtract(kdkAmount);
                billBalanceMainCityMapper.updateBalanceDetailByAdd(balanceDetailDTO.getBalanceId(), subtract);
            }
            BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
            BeanUtil.copyProperties(billBalanceMainCityDetailVO, billBalanceMainCity);
            BigDecimal sumAmount = billBalanceMainCityMapper.selectBalanceTradingSum(billBalanceMainCity.getBalanceBillNo());
            billBalanceMainCity.setDeductionAmout(sumAmount);
            billBalanceMainCity.setActualAmout(billBalanceMainCity.getBillAmount().subtract(sumAmount));
            billBalanceMainCityMapper.updateBillBalanceMainCity(billBalanceMainCity);

            return r;
        } catch (Exception e) {
            log.error("修改结算单绑定的抵扣数报错：" + e);
            e.printStackTrace();
            r.setCode(500);
            r.setMsg("系统异常");
            return r;
        }
    }

    /**
     * 审批明细增加 修改数据状态
     *
     * @param approveInfoDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveApproveInfo(BillBalanceApproveInfoDTO approveInfoDTO) {
        R r = new R();
        try {
            // 获取当前登录人信息
            SecruityUser userInfo = SecurityUtils.getUserInfo();

            String platformCode = userInfo.getPlatformCode();
            String platformName = userInfo.getPlatformName();
            // 查询出结算单数据
            BillBalanceMainCityDetailVO billBalanceMainCityDetailVO = billBalanceMainCityMapper.selectBillBalanceMainCityById(approveInfoDTO.getBillBalanceId());
            if (ObjectUtils.isEmpty(billBalanceMainCityDetailVO)) {
                r.setCode(500);
                r.setMsg("未查询出主数据");
                return r;
            }
            if (!billBalanceMainCityDetailVO.getBalanceStatus().equals(BalanceStatusEnum.WAITCHECK.getKey())) {
                r.setCode(500);
                r.setMsg("结算单非待审核状态");
                return r;
            }

            // 判断审批操作
            if (approveInfoDTO.getApproveStatus().equals(0)) {
                // 审批通过操作
                billBalanceMainCityDetailVO.setBalanceStatus(BalanceStatusEnum.WAITPAY.getKey());
            } else {
                // 审批拒绝操作
                billBalanceMainCityDetailVO.setBalanceStatus(BalanceStatusEnum.NOTSUB.getKey());
            }
            billBalanceMainCityDetailVO.setUpdateBy(platformName);
            billBalanceMainCityDetailVO.setUpdateTime(new Date());
            BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
            BeanUtil.copyProperties(billBalanceMainCityDetailVO, billBalanceMainCity);
            billBalanceMainCityMapper.updateBillBalanceMainCity(billBalanceMainCity);

            // 插入记录
            approveInfoDTO.setCreateBy(platformName);
            approveInfoDTO.setCreateByCode(platformCode);
            approveInfoDTO.setCreateTime(new Date());
            billBalanceMainCityMapper.insertApproveInfo(approveInfoDTO);

            return r;
        } catch (Exception e) {
            log.error("审批明细增加,修改数据状态报错：" + e);
            e.printStackTrace();

            r.setCode(500);
            r.setMsg("系统异常");
            return r;
        }
    }

    /**
     * 删除接口
     *
     * @param id
     * @param pageType
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateRemoveById(Integer id, Integer pageType) {
        R r = new R();
        try {
            // 查询出主数据
            BillBalanceMainCityDetailVO billBalanceMainCityDetailVO = billBalanceMainCityMapper.selectBillBalanceMainCityById(id);
            if (ObjectUtils.isEmpty(billBalanceMainCityDetailVO)) {
                r.setCode(500);
                r.setMsg("未查询出主数据");
                return r;
            }

            // 获取当前登录人信息
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String realName = userInfo.getRealName();

            String platformLevel = userInfo.getPlatformLevel();

            // 将账单解除绑定
            List<String> balanceSubCodes = billBalanceBindingCityService.selectBindingSubBillByBalanceCode(billBalanceMainCityDetailVO.getBalanceBillNo());
            if (CollectionUtil.isNotEmpty(balanceSubCodes)) {
                // 修改绑定的账单信息
                billBalanceBindingCityService.updateDeletedBillBalanceBindingByBillCode(balanceSubCodes, billBalanceMainCityDetailVO.getBalanceBillNo());
                // 修改绑定的子帐单状态
                // 应收
                if (platformLevel.equals(PlatformLevelEnum.CITY.getKey()) && pageType == 0) {
                    billSubPayCityService.updateCustomerStatusBySubBill(balanceSubCodes, SubBillStatusEnum.DQR.getKey());
                }
                // 应付
                if (platformLevel.equals(PlatformLevelEnum.CITY.getKey()) && pageType == 1) {
                    billSubPayCityService.updateStatusBySubBill(balanceSubCodes, SubBillStatusEnum.DQR.getKey());
                }
                if (platformLevel.equals(PlatformLevelEnum.PROVINCE.getKey()) && pageType == 1) {
                    billSubPayCityService.updateProvinceStatusBySubBill(balanceSubCodes, SubBillStatusEnum.DQR.getKey());
                }
            }

            // 将余额解除绑定
            List<QueryBalanceDetailVO> balanceDetailVOS = billBalanceMainCityMapper.selectBalanceTradingByList(billBalanceMainCityDetailVO.getBalanceBillNo());
            if (CollectionUtil.isNotEmpty(balanceDetailVOS)) {
                for (QueryBalanceDetailVO queryBalanceDetailVO : balanceDetailVOS) {
                    billBalanceMainCityMapper.updateBalanceDetailByRob(queryBalanceDetailVO.getId(), queryBalanceDetailVO.getTkAmount());
                }
            }
            // 删除余额流水
            billBalanceMainCityMapper.updateTradingDelByJsCode(billBalanceMainCityDetailVO.getBalanceBillNo());

            billBalanceMainCityDetailVO.setDeleteFlag("Y");
            billBalanceMainCityDetailVO.setUpdateTime(new Date());
            billBalanceMainCityDetailVO.setUpdateBy(realName);
            BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
            BeanUtil.copyProperties(billBalanceMainCityDetailVO, billBalanceMainCity);
            billBalanceMainCityMapper.updateBillBalanceMainCity(billBalanceMainCity);
            return r;
        } catch (Exception e) {
            log.error("审批明细增加,修改数据状态报错：" + e);
            e.printStackTrace();

            r.setCode(500);
            r.setMsg("系统异常");
            return r;
        }
    }

    /**
     * 查询收款方
     *
     * @return
     */
    @Override
    public List<SelectPayUserVO> selectPayeeUserList() {
        // 获取当前登录人信息
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String platformCode = userInfo.getPlatformCode();
        return billBalanceMainCityMapper.selectPayeeUserList(platformCode);
    }

    @Override
    public List<SelectPayUserVO> selectRevenueUserList() {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String platformCode = userInfo.getPlatformCode();
        return billBalanceMainCityMapper.selectRevenueUserList(platformCode);
    }

    /**
     * 生成余额明细集合
     *
     * @param jsDetailDTO
     * @param platformCode  平台编码
     * @param platformLevel 平台级别
     * @param platformName  平台名称
     * @param customerCode  客户编码
     * @param customerName  客户名称
     * @param balanceCode   结算单编号
     * @param balanceType   余额类型
     * @return
     */
    public List<FdTradingDetails> setBillBalanceAmountList(List<BalanceDetailDTO> jsDetailDTO, String platformCode, String platformLevel, String platformName, String customerCode, String customerName, String balanceCode, String balanceType) {
        List<FdTradingDetails> tradingDetails = new ArrayList();
        for (BalanceDetailDTO balanceDetailDTO : jsDetailDTO) {
            FdTradingDetails fdTradingDetails = new FdTradingDetails();
            fdTradingDetails.setBalanceId(balanceDetailDTO.getBalanceId());
            fdTradingDetails.setTradeSerialNumber(sysNoConfigService.genNo("TS"));
            fdTradingDetails.setPlatformCode(platformCode);
            fdTradingDetails.setPlatformLevel(platformLevel);
            fdTradingDetails.setPlatformName(platformName);
            fdTradingDetails.setIncomeFlag("0");
            fdTradingDetails.setCustomerCode(customerCode);
            fdTradingDetails.setCustomerName(customerName);
            fdTradingDetails.setTradingHours(LocalDateTime.now());
            fdTradingDetails.setPaymentType(balanceType);
            fdTradingDetails.setIsAdd("0");
            fdTradingDetails.setTransactionAmount(balanceDetailDTO.getBcdkAmount());
            // 汇款记录编码待确定
            // fdTradingDetails.setRemittanceRecordCode();
            fdTradingDetails.setFromBillCode(balanceCode);
            fdTradingDetails.setDeductionBillCode(balanceDetailDTO.getReceiptNo());
            fdTradingDetails.setTradingStatus("2");
            // 线路等字段是否需要插入 待确定
            tradingDetails.add(fdTradingDetails);
        }
        return tradingDetails;
    }

    @Override
    public void updateBillStatusByCode(String jsCode, String billStatus, String platformCode) {
        // 修改子帐单状态
        billBalanceMainCityMapper.updateCustomerSubBillStatusByJsCode(jsCode, billStatus);
        billBalanceMainCityMapper.updateCitySubBillStatusByJsCode(jsCode, billStatus);
        billBalanceMainCityMapper.updateProvinceSubBillStatusByJsCode(jsCode, billStatus, platformCode);
    }

    public void updateBillStatusBySubBillCode(String jsCode, String billStatus) {
        // 修改子帐单状态
        billBalanceMainCityMapper.updateCustomerSubBillStatusBySubBillCode(jsCode, billStatus);
        billBalanceMainCityMapper.updateCitySubBillStatusBySubBillCode(jsCode, billStatus);
        billBalanceMainCityMapper.updateProvinceSubBillStatusBySubBillCode(jsCode, billStatus);
    }

    /**
     * 针对省对铁路的结算单
     *
     * @param query
     * @return
     */
    @Override
    public Page pageByProvince(Query query) {
        BillBalanceMainCity billBalanceMainCity = BeanUtil.mapToBean(query.getCondition(), BillBalanceMainCity.class, false);
        List<BillBalanceMainCityListVO> billBalanceMainCities = billBalanceMainCityMapper.pageByProvince(query, billBalanceMainCity);
        query.setRecords(billBalanceMainCities);
        return query;
    }

    @Override
    public Page pageByProvinceTwo(Query query) {
        BillBalanceMainCity billBalanceMainCity = BeanUtil.mapToBean(query.getCondition(), BillBalanceMainCity.class, false);
        List<BillBalanceMainCityListVO> billBalanceMainCities = billBalanceMainCityMapper.pageByProvinceTwo(query, billBalanceMainCity);
        query.setRecords(billBalanceMainCities);
        return query;
    }

    /**
     * 省平台应付账单核销功能
     *
     * @param params
     * @return
     */
    @Override
    public R updateBillOff(Map<String, Object> params) {
        // 查询出结算单数据
        Integer id = Integer.valueOf(String.valueOf(params.get("id")));
        BillBalanceMainCityDetailVO billBalanceMainCityDetailVO = billBalanceMainCityMapper.selectBillBalanceMainCityById(id);
        List<BillBalanceMainCitySubDetailVO> billBalanceMainCitySubDetailVOS = billBalanceBindingCityMapper.selectProvinceBillSubListByBalanceNoTwo(billBalanceMainCityDetailVO.getBalanceBillNo());
        if (CollectionUtil.isEmpty(billBalanceMainCitySubDetailVOS)) {
            return R.error("业务流程出错");
        }
        for (BillBalanceMainCitySubDetailVO vo : billBalanceMainCitySubDetailVOS) {
            //根据平台认领收款记录
            R r = claimRemittanceTwo(id, billBalanceMainCityDetailVO, vo);
            if (r.getCode() == 1) {
                return r;
            }
        }
        //修改结算单及账单状态
        updateBillBalanceMainCityAndBillStatus(id, billBalanceMainCityDetailVO);
        return R.success();
    }

    public R submitWriteOff(BillBalanceMainCityDTO billBalanceMainCityDTO) {
        // 获取当前登录人信息
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String username = userInfo.getRealName();

        // 修改主表信息
        BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
        BeanUtil.copyProperties(billBalanceMainCityDTO, billBalanceMainCity);

        // 判断是否完全抵扣
        int compareTo = billBalanceMainCity.getActualAmout().compareTo(new BigDecimal(0));
        if (compareTo == 0) {
            // 如果是完全抵扣数据 将结算单变为已核销数据  将余额减少
            // 查询出余额明细数据
            List<FdTradingDetails> tradingDetails = billBalanceMainCityMapper.selectTradingListByJsCode(billBalanceMainCity.getBalanceBillNo());
            if (CollectionUtil.isNotEmpty(tradingDetails)) {
                for (FdTradingDetails fdTradingDetails : tradingDetails) {
                    // 修改余额明细
                    billBalanceMainCityMapper.updateBalanceDetailBySyAmount(fdTradingDetails.getBalanceId(), fdTradingDetails.getTransactionAmount());
                    // 修改流水数据的状态
                    fdTradingDetails.setTradingStatus("1");
                }
                fdTradingDetailsService.updateBatchById(tradingDetails);
            }
            // 修改余额明细状态
            billBalanceMainCity.setBalanceStatus(BalanceStatusEnum.VERIFIED.getKey());
        }

        // 修改账单状态
        if (CollUtil.isNotEmpty(billBalanceMainCityDTO.getBillDealWithCityVOS())) {
            for (BillBalanceMainCitySubDetailVO vo : billBalanceMainCityDTO.getBillDealWithCityVOS()) {
                if (StrUtil.isNotBlank(vo.getBillCode())) {
                    updateBillStatusBySubBillCode(vo.getBillCode(), billBalanceMainCity.getBalanceStatus());
                }
            }
        }
        billBalanceMainCity.setUpdateBy(username);
        billBalanceMainCity.setUpdateTime(new Date());
        billBalanceMainCityMapper.updateBillBalanceMainCity(billBalanceMainCity);

        BillBalanceMainCityDetailVO billBalanceMainCityDetailVO = billBalanceMainCityMapper.selectBillBalanceMainCityById(billBalanceMainCityDTO.getId());
        List<BillBalanceMainCitySubDetailVO> billBalanceMainCitySubDetailVOS = billBalanceBindingCityMapper.selectProvinceBillSubListByBalanceNoTwo(billBalanceMainCityDetailVO.getBalanceBillNo());
        if (CollectionUtil.isEmpty(billBalanceMainCitySubDetailVOS)) {
            return R.error("业务流程出错");
        }
        for (BillBalanceMainCitySubDetailVO vo : billBalanceMainCitySubDetailVOS) {
            //根据平台认领收款记录
            R r = claimRemittanceTwo(billBalanceMainCityDTO.getId(), billBalanceMainCityDetailVO, vo);
            if (r.getCode() == 1) {
                return r;
            }
        }
        //修改结算单及账单状态
        updateBillBalanceMainCityAndBillStatus(billBalanceMainCityDTO.getId(), billBalanceMainCityDetailVO);
        return R.success();
    }

    private void updateBillBalanceMainCityAndBillStatus(Integer id, BillBalanceMainCityDetailVO billBalanceMainCityDetailVO) {
        //修改结算单及账单状态
        BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
        billBalanceMainCity.setId(id);
        billBalanceMainCity.setShiftNos(billBalanceMainCityDetailVO.getShiftNos());
        billBalanceMainCity.setShiftNames(billBalanceMainCityDetailVO.getShiftNames());
        billBalanceMainCity.setBalanceStatus(BalanceStatusEnum.VERIFIED.getKey());
        billBalanceMainCityMapper.updateBillBalanceMainCity(billBalanceMainCity);
        updateBillStatusByCode(billBalanceMainCityDetailVO.getBalanceBillNo(), BalanceStatusEnum.VERIFIED.getKey(), null);
    }

    public R claimRemittanceTwo(Integer id, BillBalanceMainCityDetailVO billBalanceMainCityDetailVO, BillBalanceMainCitySubDetailVO vo) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        FdRemittanceRecord fdRemittanceRecordv = new FdRemittanceRecord();
        BillBalanceMainCityDetailVO billBalanceMainCityDetailVO1 = billBalanceMainCityMapper.selectBillBalanceMainCityById(id);
//        BigDecimal deductionAmount = billBalanceMainCityMapper.getDeductionAmount(id, vo.getSourceUnitCode());
        BigDecimal deductionAmount = BigDecimal.ZERO;
        if (billBalanceMainCityDetailVO1.getDeductionAmout() != null) {
            deductionAmount = billBalanceMainCityDetailVO1.getDeductionAmout();
        }
        if (billBalanceMainCityDetailVO1.getBillAmount() != null
                && deductionAmount.compareTo(billBalanceMainCityDetailVO1.getBillAmount()) > 0) {
            return R.error("抵扣金额大于对应平台账单金额，不允许核销！");
        }
        // 货源组织单位
        String tsIn = sysNoConfigService.genNo("RE");
        if (tsIn.contains("请联系系统管理员")) {
            return R.error("生成收款编码出现错误!");
        }
        fdRemittanceRecordv.setRemittanceRecordCode(tsIn);
        fdRemittanceRecordv.setCustomerNo(vo.getSourceUnitCode());
        fdRemittanceRecordv.setCustomerName(vo.getSourceUnitName());
        fdRemittanceRecordv.setCustomerFlag("2");
        // 省平台
        fdRemittanceRecordv.setPlatformCode(billBalanceMainCityDetailVO.getCustomerCode());
        fdRemittanceRecordv.setPlatformName(billBalanceMainCityDetailVO.getCustomerName());
        fdRemittanceRecordv.setPlatformFlag("2");
        fdRemittanceRecordv.setRemittanceAmount(vo.getBillAmount().subtract(deductionAmount));
        fdRemittanceRecordv.setClearedAmount(vo.getBillAmount().subtract(deductionAmount));
        fdRemittanceRecordv.setRemark("手动核销");
        fdRemittanceRecordv.setRemittanceName("现汇");
        fdRemittanceRecordv.setRemittanceType("XH");
        fdRemittanceRecordv.setCollectionTime(LocalDate.now());
        fdRemittanceRecordv.setBalanceBillNos(billBalanceMainCityDetailVO.getBalanceBillNo());
        //更新汇款单
        fdRemittanceRecordv.setClaimStatus("1");
        fdRemittanceRecordv.setClaimant(userInfo.getRealName());
        fdRemittanceRecordv.setClaimDate(LocalDateTime.now());
        fdRemittanceRecordService.saveTwo(fdRemittanceRecordv);

        //添加认领结算单的流水
        insertFdTradingDetailsTwo(fdRemittanceRecordv, "2", billBalanceMainCityDetailVO);

        FdTradingDetails fdTradingDetails = new FdTradingDetails();
        fdTradingDetails.setDeductionBillCode(billBalanceMainCityDetailVO.getBalanceBillNo());
        fdTradingDetails.setTradingStatus("2");
        fdTradingDetails.setDelFlag("N");
        //查询结算单余额绑定的流水
        List<FdTradingDetails> fdTradingDetailsList = fdTradingDetailsMapper.selectFdTradingDetailsList(fdTradingDetails);
        for (FdTradingDetails fdTrading : fdTradingDetailsList) {
            fdTrading.setTradingStatus("1");
            //把流水的结算状态改成结算
            fdTradingDetailsMapper.updateFdTradingDetailsTradingStatus(fdTrading);
            FdBalanceDetail fdBalanceDetail = fdBalanceDetailMapper.selectFdBalanceDetailListById(fdTrading.getBalanceId());
            //根据流水更新汇款余额和量价捆绑余额里面的锁定金额和剩余金额
            if (!fdBalanceDetail.getPaymentType().equals("0")) {
                fdBalanceDetail.setLockingAmount(fdBalanceDetail.getLockingAmount().subtract(fdTrading.getTransactionAmount()));
                fdBalanceDetail.setRemainingAmount(fdBalanceDetail.getRemainingAmount().subtract(fdTrading.getTransactionAmount()));
            } else {
                fdBalanceDetail.setLockingAmount(BigDecimal.ZERO);
                fdBalanceDetail.setRemainingAmount(BigDecimal.ZERO);
                fdBalanceDetail.setAvailableAmount(BigDecimal.ZERO);
            }
            fdBalanceDetailMapper.updateFdBalanceDetailById(fdBalanceDetail);
        }
        return R.success();
    }

    public void insertFdTradingDetailsTwo(FdRemittanceRecord fdRemittanceRecord, String paymentType, BillBalanceMainCityDetailVO billBalanceMainCity) {
        FdTradingDetails fdTradingDetails = new FdTradingDetails();
        String tsIn = sysNoConfigService.genNo("TS");
        fdTradingDetails.setUuid(UUID.randomUUID().toString());
        fdTradingDetails.setTradeSerialNumber(tsIn);
        fdTradingDetails.setPlatformCode(fdRemittanceRecord.getPlatformCode());
        fdTradingDetails.setPlatformName(fdRemittanceRecord.getPlatformName());
        fdTradingDetails.setCustomerName(fdRemittanceRecord.getCustomerName());
        fdTradingDetails.setCustomerCode(fdRemittanceRecord.getCustomerNo());
        fdTradingDetails.setTradingHours(LocalDateTime.now());
        fdTradingDetails.setPaymentType(paymentType);
        fdTradingDetails.setRemittanceRecordCode(fdRemittanceRecord.getRemittanceRecordCode());
        fdTradingDetails.setTransactionAmount(fdRemittanceRecord.getRemittanceAmount());
        fdTradingDetails.setDeductionBillCode(billBalanceMainCity.getBalanceBillNo());
        fdTradingDetails.setTradingStatus("1");
        if (fdRemittanceRecord.getPlatformFlag().equals("1")) {
            fdTradingDetails.setPlatformLevel("0");
        } else if (fdRemittanceRecord.getPlatformFlag().equals("2") && fdRemittanceRecord.getCustomerFlag().equals("1")) {
            fdTradingDetails.setPlatformLevel("1");
        } else if (fdRemittanceRecord.getPlatformFlag().equals("2") && fdRemittanceRecord.getCustomerFlag().equals("2")) {
            fdTradingDetails.setPlatformLevel("2");
        }
        fdTradingDetailsMapper.insertFdTradingDetails(fdTradingDetails);
    }

    @Override
    public R exportFreightAccountTwo(BillBalanceMainCity billBalanceMainCity, HttpServletResponse response) {
        BillBalanceMainCityDetailVO vo = billBalanceMainCityMapper.selectBillBalanceMainCityById(billBalanceMainCity.getId());
        if (vo == null) {
            return new R<>(new Throwable("该结算单不存在"));
        }
        if (StrUtil.isBlank(vo.getShiftNos())) {
            return new R<>(new Throwable("该结算单没有班次信息，无法导出"));
        }
        if (StrUtil.isNotBlank(vo.getShiftNos())) {
            String shipmentTime = "";

            BigDecimal payableT = BigDecimal.valueOf(0);
            BigDecimal hnrbT = BigDecimal.valueOf(0);
            BigDecimal deductionAmount = BigDecimal.valueOf(0);
            double total = 0;
            int x = 1;
            String remarks = "";
            String[] split = vo.getShiftNos().split(",");
            List<Shifmanagement> shifmanagementList = new ArrayList<>();
            List<FdShippingAccount> fdShippingAccountList = new ArrayList<>();
            for (String shiftId : split) {
                Shifmanagement sel = new Shifmanagement();
                sel.setShiftId(shiftId);
                sel.setDeleteFlag("N");
                List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
                if (CollUtil.isEmpty(shifmanagements)) {
                    return new R<>(new Throwable("该结算单班次信息错误，无法导出"));
                }
                shifmanagementList.add(shifmanagements.get(0));
                FdShippingAccount sel2 = new FdShippingAccount();
                sel2.setShiftNo(shifmanagements.get(0).getShiftId());
                sel2.setDeleteFlag("N");
                List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountList(sel2);
                if (CollUtil.isEmpty(fdShippingAccounts)) {
                    return new R<>(new Throwable("该结算单没有台账，无法导出"));
                }
                fdShippingAccountList.add(fdShippingAccounts.get(0));
                Date planShipTime = shifmanagements.get(0).getPlanShipTime();
                LocalDate localDate = planShipTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                shipmentTime = localDate.format(formatter);
            }
            // 查询余额明细
            List<BillBalanceJsListVO> billBalanceJsListVOS = fdBalanceDetailService.selectBillJsList(vo.getBalanceBillNo());
            if (CollUtil.isNotEmpty(billBalanceJsListVOS)) {
                for (BillBalanceJsListVO billBalanceJsListVO : billBalanceJsListVOS) {
                    deductionAmount = deductionAmount.add(billBalanceJsListVO.getBcdkAmount());
                    remarks = remarks + (x) + "、" + billBalanceJsListVO.getCustomerName() + (StrUtil.isNotBlank(billBalanceJsListVO.getProvinceShiftName()) ? billBalanceJsListVO.getProvinceShiftName() : "") + (StrUtil.isNotBlank(billBalanceJsListVO.getProvinceTrainNum()) ? "，省级班列号:" + billBalanceJsListVO.getProvinceTrainNum() : "") + "，与多联确认最终汇率后产生余额" + billBalanceJsListVO.getTkAmount() + "元，本次抵扣" + billBalanceJsListVO.getBcdkAmount() + "元;\n";
                    x += 1;
                }
            }
            // 收款余额
            List<BillBalanceSkListVO> billBalanceSkListVOS = fdBalanceDetailService.selectBillSkList(vo.getBalanceBillNo());
            if (CollUtil.isNotEmpty(billBalanceSkListVOS)) {
                for (BillBalanceSkListVO billBalanceSkListVO : billBalanceSkListVOS) {
                    deductionAmount = deductionAmount.add(billBalanceSkListVO.getBcdkAmount());
                    remarks = remarks + (x) + "、收款编号:" + billBalanceSkListVO.getReceiptNo() + "总金额" + billBalanceSkListVO.getTkAmount() + "元，本次抵扣" + billBalanceSkListVO.getBcdkAmount() + "元;\n";
                    x += 1;
                }
            }

            // 量价捆绑余额
            List<BillBalanceLjkbListVO> billBalanceLjkbListVOS = fdBalanceDetailService.selectBillLjkbList(vo.getBalanceBillNo());
            if (CollUtil.isNotEmpty(billBalanceLjkbListVOS)) {
                for (BillBalanceLjkbListVO billBalanceLjkbListVO : billBalanceLjkbListVOS) {
                    deductionAmount = deductionAmount.add(billBalanceLjkbListVO.getBcdkAmount());
                    remarks = remarks + (x) + "、发运线路:" + billBalanceLjkbListVO.getShippingLine() + "，总金额" + billBalanceLjkbListVO.getTkAmount() + "元，本次抵扣" + billBalanceLjkbListVO.getBcdkAmount() + "元;\n";
                    x += 1;
                }
            }

            //创建工作薄对象
            HSSFWorkbook workbook = new HSSFWorkbook();
            //创建工作表对象
            HSSFSheet sheet = workbook.createSheet();
            setSheet(workbook, sheet);
            //样式
            CellStyle style;
            //字体
            HSSFFont font;
            HSSFRow row;
            Cell cell;
            int rowIndex = 0;

            //样式
            font = workbook.createFont();
            font.setFontHeightInPoints((short) 22);//设置excel数据字体大小
            font.setFontName("方正小标宋简体");//设置字体
//        font.setBold(true);
            style = workbook.createCellStyle();
            style.setFont(font);
            style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
            style.setAlignment(HorizontalAlignment.CENTER);// 水平

            rowIndex = setRowTitle(shifmanagementList, shipmentTime, workbook, sheet, style, rowIndex);

            if (CollUtil.isNotEmpty(fdShippingAccountList)) {
                for (FdShippingAccount fdShippingAccount : fdShippingAccountList) {
                    BigDecimal domesticFreightT = BigDecimal.valueOf(0);
                    BigDecimal overseasFreightRmbT = BigDecimal.valueOf(0);
                    BigDecimal payable = BigDecimal.valueOf(0);
                    BigDecimal hnrb = BigDecimal.valueOf(0);
                    int num = 1;
                    List<FdFreightAccounting> list1 = fdFreightAccountingMapper.getfdFreightAccountingList4(vo.getBalanceBillNo(), fdShippingAccount.getAccountCode());
                    for (int i = 0; i < list1.size(); i++) {
                        if (list1.get(i).getContainerNum() != null && list1.get(i).getContainerNum().compareTo(BigDecimal.valueOf(0)) != 0) {

                            list1.get(i).setDomesticUnitprice(list1.get(i).getDomesticFreight().divide(list1.get(i).getContainerNum(), 2, BigDecimal.ROUND_HALF_UP));
                            list1.get(i).setOverseasUnitprice(list1.get(i).getOverseasFreightRmb().divide(list1.get(i).getContainerNum(), 2, BigDecimal.ROUND_HALF_UP));
                        } else {
                            list1.get(i).setDomesticUnitprice(BigDecimal.valueOf(0));
                            list1.get(i).setOverseasUnitprice(BigDecimal.valueOf(0));
                        }
                        list1.get(i).setRowId(String.valueOf(i + 1));
                        domesticFreightT = domesticFreightT.add(list1.get(i).getDomesticFreight());
                        overseasFreightRmbT = overseasFreightRmbT.add(list1.get(i).getOverseasFreightRmb());
                        payable = payable.add(list1.get(i).getDomesticFreight()).add(list1.get(i).getOverseasFreightRmb());
                        hnrb = payable;
                        payableT = payableT.add(list1.get(i).getDomesticFreight()).add(list1.get(i).getOverseasFreightRmb());
                        hnrbT = hnrbT.add(list1.get(i).getDomesticFreight()).add(list1.get(i).getOverseasFreightRmb());
                        if (list1.get(i).getContainerNum() != null && list1.get(i).getContainerType() != null && !"".equals(list1.get(i).getContainerType())) {
                            int containerNum = list1.get(i).getContainerNum().intValue();
                            if (list1.get(i).getContainerType().startsWith("2")) {
                                total += (0.5 * containerNum);
                            } else if (list1.get(i).getContainerType().startsWith("4")) {
                                total += (1 * containerNum);
                            }
                        }
                    }

                    rowIndex = setRowValue(workbook, sheet, rowIndex, domesticFreightT, overseasFreightRmbT, payable, hnrb, list1, num);
                    num += 1;
                }
            }

            String totalStr;
            if (String.valueOf(total).contains(".5")) {
                totalStr = String.valueOf(total);
            } else {
                totalStr = String.valueOf(total).split("\\.")[0];
            }

            remarks = remarks + "  本次合计抵扣" + deductionAmount + "元。\n";
            remarks = remarks + x + "、本列合计" + totalStr + "车。";

            if ("G".equals(shifmanagementList.get(0).getTrip())) {
                List<Map<String, String>> countryList = fdFreightAccountingMapper.selectNumByCountryTwo("S", vo.getShiftNos(), vo.getBalanceBillNo());
                if (CollUtil.isNotEmpty(countryList)) {
                    StringBuilder countryBuilder = new StringBuilder();
                    for (Map<String, String> map : countryList) {
                        if (map != null && map.get("country_name") != null
                                && !"".equals(map.get("country_name"))
                                && map.get("total_num") != null
                                && !"".equals(map.get("total_num"))
                                && !"乌兹别克斯坦".equals(map.get("country_name"))
                                && !"吉尔吉斯斯坦".equals(map.get("country_name"))
                                && !"哈萨克斯坦".equals(map.get("country_name"))
                                && !"土库曼斯坦".equals(map.get("country_name"))
                                && !"塔吉克斯坦".equals(map.get("country_name"))) {
                            String totalNum = String.valueOf(map.get("total_num"));
                            if (totalNum != null && !"".equals(totalNum)) {
                                if (totalNum.endsWith(".0")) {
                                    totalNum = totalNum.replace(".0", "");
                                }
                            }
                            countryBuilder.append("，到达").append(map.get("country_name")).append(" ").append(totalNum).append("车\n");
                        }
                    }
                    if (countryBuilder.length() > 0) {
                        countryBuilder.deleteCharAt(0);
                    }
                    String countryString = countryBuilder.toString();
                    remarks = remarks + countryString;

                } else {
                    remarks = remarks + "\n";
                }
                //判断是否以换行结尾，若不是拼接上换行符
                if (!remarks.endsWith("\n")) {
                    remarks = remarks + "\n";
                }

            } else if ("R".equals(shifmanagementList.get(0).getTrip())) {
                List<Map<String, String>> countryList = fdFreightAccountingMapper.selectNumByCountryTwo("F", vo.getShiftNos(), vo.getBalanceBillNo());
                if (CollUtil.isNotEmpty(countryList) && countryList.get(0) != null) {
                    if (countryList.get(0).get("total_num") != null && !"".equals(countryList.get(0).get("total_num"))) {
                        String totalNum = String.valueOf(countryList.get(0).get("total_num"));
                        if (totalNum != null && !"".equals(totalNum)) {
                            if (totalNum.endsWith(".0")) {
                                totalNum = totalNum.replace(".0", "");
                            }
                        }
                        if (!"0".equals(totalNum)) {
                            remarks = remarks + "发站国" + " " + totalNum + "车\n";
                        }
                    }
                } else {
                    remarks = remarks + "\n";
                }
                //判断是否以换行结尾，若不是拼接上换行符
                if (!remarks.endsWith("\n")) {
                    remarks = remarks + "\n";
                }
            }
            if (StrUtil.isNotBlank(vo.getPlatformName())) {
                x += 1;
                remarks = remarks + x + "、收款人：" + vo.getPlatformName();
            }
            remarks += "。";
            setLastRow(response, payableT, hnrbT, remarks, workbook, sheet, rowIndex);
        }
        return new R<>(0, Boolean.TRUE, null, "导出完成！");
    }

    private static void setLastRow(HttpServletResponse response, BigDecimal payableT, BigDecimal hnrbT, String remarks, HSSFWorkbook workbook, HSSFSheet sheet, int rowIndex) {
        //另起一行
        rowIndex++;
        CellStyle style;
        HSSFRow row;
        HSSFFont font;
    /*
    写入合计行，TODO 数据自行从数据库取
     */
        row = sheet.createRow(rowIndex);
        row.setHeightInPoints(35);//设置行高
        //构建数据
        row.createCell(0).setCellValue("应付合计");
        row.createCell(1);
        row.createCell(2);
        row.createCell(3);
        row.createCell(4).setCellValue(String.valueOf(payableT));
        row.createCell(5);
        row.createCell(6);
        row.createCell(7);
        row.createCell(8).setCellValue("实付合计");
        row.createCell(9);
        row.createCell(10);
        row.createCell(11);
        row.createCell(12).setCellValue(String.valueOf(hnrbT));
        row.createCell(13);
        row.createCell(14);
        row.createCell(15);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 3));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 4, 7));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 8, 11));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 12, 15));
        int cellIndex_count = 0;
        for (Cell cellTemp : row) {
            //样式
            font = workbook.createFont();
            font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
            font.setFontName("宋体");//设置字体
            if (cellIndex_count >= 8) {
                font.setBold(true);
            }
            style = workbook.createCellStyle();
            style.setFont(font);
            style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
            style.setAlignment(HorizontalAlignment.CENTER);// 水平
            style.setBorderTop(BorderStyle.THIN);//上边框
            style.setBorderBottom(BorderStyle.THIN);//下边框
            style.setBorderLeft(BorderStyle.THIN);//左边框
            style.setBorderRight(BorderStyle.THIN);//右边框

            cellTemp.setCellStyle(style);
            cellIndex_count++;
        }
        //另起一行
        rowIndex++;

                                    /*
                                    写入备注行，TODO 数据自行从数据库取
                                     */
        row = sheet.createRow(rowIndex);
        row.setHeightInPoints(28 * (remarks.length() - remarks.replace("\n", "").length() + 1));//计算bzStr中换行符\n出现次数，动态设置行高
        //构建数据
        row.createCell(0).setCellValue("备注");
        row.createCell(1).setCellValue(remarks);
        row.createCell(2);
        row.createCell(3);
        row.createCell(4);
        row.createCell(5);
        row.createCell(6);
        row.createCell(7);
        row.createCell(8);
        row.createCell(9);
        row.createCell(10);
        row.createCell(11);
        row.createCell(12);
        row.createCell(13);
        row.createCell(14);
        row.createCell(15);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 1, 15));
        //样式
        font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
        font.setFontName("宋体");//设置字体
        font.setBold(true);
        style = workbook.createCellStyle();
        style.setFont(font);
        style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
        style.setAlignment(HorizontalAlignment.LEFT);// 水平
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderBottom(BorderStyle.THIN);//下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setWrapText(true);//设置单元格中的值 使用有\n换行符
        for (Cell cellTemp : row) {
            cellTemp.setCellStyle(style);
        }
        rowIndex++;
        rowIndex++;

                                    /*
                                    复核人、制单人
                                     */
        row = sheet.createRow(rowIndex);
//        row.createCell(4).setCellValue("复核人：");
//        row.createCell(10).setCellValue("制单人：");
        //样式
        font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
        font.setFontName("宋体");//设置字体
        font.setBold(false);
        style = workbook.createCellStyle();
        style.setFont(font);
        for (Cell cellTemp : row) {
            cellTemp.setCellStyle(style);
        }

        //设置 边距、页眉、页脚
        HSSFPrintSetup printSetup = sheet.getPrintSetup();
        printSetup.setPaperSize(HSSFPrintSetup.A4_PAPERSIZE); //纸张类型
        //打印方向，true：横向，false：纵向(默认)
        printSetup.setLandscape(true);
        printSetup.setHeaderMargin(0.2);
        printSetup.setFooterMargin(0.2);
        //设置打印缩放为88%
        ///printSetup.setScale((short) 55);
        //这个是sheet缩放设置，设置行调整为一列和行调整为一列必须要true
        sheet.setAutobreaks(true);
        //将所有列调整为一页
        printSetup.setFitHeight((short) 0);
        //将所有行调整为一页
        printSetup.setFitWidth((short) 1);
        //写入数据流下载
        try {
            //response为HttpServletResponse对象
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("运费核算表", "utf-8") + ".xls");
            ServletOutputStream out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            // 关闭writer，释放内存
            out.close();
            workbook.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static int setRowValue(HSSFWorkbook workbook, HSSFSheet sheet, int rowIndex, BigDecimal domesticFreightT, BigDecimal overseasFreightRmbT, BigDecimal payable, BigDecimal hnrb, List<FdFreightAccounting> list1, int num) {
        HSSFFont font;
        CellStyle style;
        HSSFRow row;
        //另起一行
        rowIndex++;
//                    rowIndex = 3;
        //样式
        font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
        font.setFontName("宋体");//设置字体
        style = workbook.createCellStyle();
        style.setFont(font);
        style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
        style.setAlignment(HorizontalAlignment.CENTER);// 水平
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderBottom(BorderStyle.THIN);//下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setWrapText(true);
        int y = 0;
        for (int i = 0; i < list1.size(); i++) {
            int cellIndex_data = 0;
            row = sheet.createRow(rowIndex);
            row.setHeightInPoints(35);//设置行高
            //构建数据
            row.createCell(0).setCellValue(String.valueOf(num));//序号
            row.createCell(1).setCellValue(String.valueOf(list1.get(i).getCustomerName()));//货源组织单位
            row.createCell(2).setCellValue(String.valueOf(list1.get(i).getShippingLine()));//线路
            row.createCell(3).setCellValue(String.valueOf(list1.get(i).getDestinationName()));//发站
            row.createCell(4).setCellValue(String.valueOf(list1.get(i).getPortStation()));//口岸站
            row.createCell(5).setCellValue(String.valueOf(list1.get(i).getTrip()));//方向

            row.createCell(6).setCellValue(String.valueOf(list1.get(i).getBusinessIdentification()));//类型
            row.createCell(7).setCellValue(String.valueOf(list1.get(i).getIsFull()));//是否全程
            row.createCell(8).setCellValue(String.valueOf(list1.get(i).getContainerOwner()));//箱属
            row.createCell(9).setCellValue(String.valueOf(list1.get(i).getContainerType()));//箱型
            row.createCell(10).setCellValue(String.valueOf(list1.get(i).getContainerNum()));//箱量
            row.createCell(11).setCellValue(String.valueOf(list1.get(i).getDomesticUnitprice()));//境内单价
            row.createCell(12).setCellValue(String.valueOf(list1.get(i).getDomesticFreight()));//境内运费
            row.createCell(13).setCellValue(String.valueOf(list1.get(i).getOverseasUnitprice()));//境外单价
            row.createCell(14).setCellValue(String.valueOf(list1.get(i).getOverseasFreightRmb()));//境外运费
            //币种
            row.createCell(15).setCellValue(StrUtil.isNotBlank(list1.get(i).getMonetaryType()) ? list1.get(i).getMonetaryType() : "人民币");
            for (Cell cellTemp : row) {
                cellTemp.setCellStyle(style);
            }
            if (i != 0) {
                if (list1.get(i).getCustomerName().equals(list1.get(i - 1).getCustomerName()) && list1.get(i).getShippingLine().equals(list1.get(i - 1).getShippingLine()) && list1.get(i).getDestinationName().equals(list1.get(i - 1).getDestinationName()) && list1.get(i).getPortStation().equals(list1.get(i - 1).getPortStation()) && list1.get(i).getTrip().equals(list1.get(i - 1).getTrip())) {
                    y += 1;
                } else if (y != 0) {
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex - y, rowIndex, 0, 0));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex - y, rowIndex, 1, 1));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex - y, rowIndex, 2, 2));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex - y, rowIndex, 3, 3));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex - y, rowIndex, 4, 4));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex - y, rowIndex, 5, 5));
                    y = 0;
                }
            }
            if (i == list1.size() - 1 && y != 0) {
                sheet.addMergedRegion(new CellRangeAddress(rowIndex - y, rowIndex, 0, 0));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex - y, rowIndex, 1, 1));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex - y, rowIndex, 2, 2));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex - y, rowIndex, 3, 3));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex - y, rowIndex, 4, 4));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex - y, rowIndex, 5, 5));
            }
            //另起一行
            rowIndex++;
        }

                                    /*
                                    写入总价行，TODO 数据自行从数据库取
                                     */
        row = sheet.createRow(rowIndex);
        row.setHeightInPoints(35);//设置行高
        //构建数据
        row.createCell(0).setCellValue("境内总价");
        row.createCell(1);
        row.createCell(2);
        row.createCell(3);
        row.createCell(4).setCellValue(String.valueOf(domesticFreightT));
        row.createCell(5);
        row.createCell(6);
        row.createCell(7);
        row.createCell(8).setCellValue("境外总价");
        row.createCell(9);
        row.createCell(10);
        row.createCell(11);
        row.createCell(12).setCellValue(String.valueOf(overseasFreightRmbT));
        row.createCell(13);
        row.createCell(14);
        row.createCell(15);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 3));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 4, 7));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 8, 11));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 12, 15));
        //样式
        font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
        font.setFontName("宋体");//设置字体
        style = workbook.createCellStyle();
        style.setFont(font);
        style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
        style.setAlignment(HorizontalAlignment.CENTER);// 水平
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderBottom(BorderStyle.THIN);//下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        for (Cell cellTemp : row) {
            cellTemp.setCellStyle(style);
        }
        //另起一行
        rowIndex++;

                                    /*
                                    写入应付、实付行，TODO 数据自行从数据库取
                                     */
        row = sheet.createRow(rowIndex);
        row.setHeightInPoints(35);//设置行高
        //构建数据
        row.createCell(0).setCellValue("应付");
        row.createCell(1);
        row.createCell(2);
        row.createCell(3);
        row.createCell(4).setCellValue(String.valueOf(payable));
        row.createCell(5);
        row.createCell(6);
        row.createCell(7);
        row.createCell(8).setCellValue("实付");
        row.createCell(9);
        row.createCell(10);
        row.createCell(11);
        row.createCell(12).setCellValue(String.valueOf(hnrb));
        row.createCell(13);
        row.createCell(14);
        row.createCell(15);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 3));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 4, 7));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 8, 11));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 12, 15));
        //样式
        font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
        font.setFontName("宋体");//设置字体
        style = workbook.createCellStyle();
        style.setFont(font);
        style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
        style.setAlignment(HorizontalAlignment.CENTER);// 水平
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderBottom(BorderStyle.THIN);//下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        for (Cell cellTemp : row) {
            cellTemp.setCellStyle(style);
        }
        return rowIndex;
    }

    private static void setSheet(HSSFWorkbook workbook, HSSFSheet sheet) {
        sheet.setColumnWidth(0, 65 * 27);
        sheet.setColumnWidth(1, 215 * 27);
        sheet.setColumnWidth(2, 80 * 27);
        sheet.setColumnWidth(3, 80 * 27);
        sheet.setColumnWidth(4, 100 * 27);
        sheet.setColumnWidth(5, 100 * 27);
        sheet.setColumnWidth(6, 100 * 27);
        sheet.setColumnWidth(7, 100 * 27);
        sheet.setColumnWidth(8, 100 * 27);
        sheet.setColumnWidth(9, 100 * 27);
        sheet.setColumnWidth(10, 100 * 27);
        sheet.setColumnWidth(11, 115 * 27);
        sheet.setColumnWidth(12, 115 * 27);
        sheet.setColumnWidth(13, 115 * 27);
        sheet.setColumnWidth(14, 115 * 27);
        sheet.setColumnWidth(15, 100 * 27);

        //设置sheet的Name
        workbook.setSheetName(0, "运费核算表");
    }

    @Override
    public R exportFreightAccount(BillBalanceMainCity billBalanceMainCity, HttpServletResponse response) {
        BillBalanceMainCityDetailVO vo = billBalanceMainCityMapper.selectBillBalanceMainCityById(billBalanceMainCity.getId());
        if (vo == null) {
            return new R<>(new Throwable("该结算单不存在"));
        }
        if (StrUtil.isBlank(vo.getShiftNos())) {
            return new R<>(new Throwable("该结算单没有班次信息，无法导出"));
        }
        Shifmanagement sel = new Shifmanagement();
        sel.setShiftId(vo.getShiftNos());
        sel.setDeleteFlag("N");
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
        if (CollUtil.isEmpty(shifmanagements)) {
            return new R<>(new Throwable("该结算单班次信息错误，无法导出"));
        }
        FdShippingAccount sel2 = new FdShippingAccount();
        sel2.setShiftNo(shifmanagements.get(0).getShiftId());
        sel2.setDeleteFlag("N");
        List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountList(sel2);
        if (CollUtil.isEmpty(fdShippingAccounts)) {
            return new R<>(new Throwable("该结算单没有台账，无法导出"));
        }
        Date planShipTime = shifmanagements.get(0).getPlanShipTime();
        LocalDate localDate = planShipTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String shipmentTime = localDate.format(formatter);

        List<FdFreightAccounting> list1 = fdFreightAccountingMapper.getfdFreightAccountingList4(vo.getBalanceBillNo(), fdShippingAccounts.get(0).getAccountCode());

        BigDecimal domesticFreightT = BigDecimal.valueOf(0);
        BigDecimal overseasFreightRmbT = BigDecimal.valueOf(0);
        BigDecimal payable = BigDecimal.valueOf(0);
        BigDecimal payableT = BigDecimal.valueOf(0);
        BigDecimal hnrb = BigDecimal.valueOf(0);
        BigDecimal hnrbT = BigDecimal.valueOf(0);
        BigDecimal deductionAmount = BigDecimal.valueOf(0);
        double total = 0;
        int x = 1;
        String remarks = "";
        for (int i = 0; i < list1.size(); i++) {
            if (list1.get(i).getContainerNum() != null && list1.get(i).getContainerNum().compareTo(BigDecimal.valueOf(0)) != 0) {

                list1.get(i).setDomesticUnitprice(list1.get(i).getDomesticFreight().divide(list1.get(i).getContainerNum(), 2, BigDecimal.ROUND_HALF_UP));
                list1.get(i).setOverseasUnitprice(list1.get(i).getOverseasFreightRmb().divide(list1.get(i).getContainerNum(), 2, BigDecimal.ROUND_HALF_UP));
            } else {
                list1.get(i).setDomesticUnitprice(BigDecimal.valueOf(0));
                list1.get(i).setOverseasUnitprice(BigDecimal.valueOf(0));
            }
            list1.get(i).setRowId(String.valueOf(i + 1));
            domesticFreightT = domesticFreightT.add(list1.get(i).getDomesticFreight());
            overseasFreightRmbT = overseasFreightRmbT.add(list1.get(i).getOverseasFreightRmb());
            payable = payable.add(list1.get(i).getDomesticFreight()).add(list1.get(i).getOverseasFreightRmb());
            payableT = payableT.add(list1.get(i).getDomesticFreight()).add(list1.get(i).getOverseasFreightRmb());
            if (list1.get(i).getContainerNum() != null && list1.get(i).getContainerType() != null && !"".equals(list1.get(i).getContainerType())) {
                int num = list1.get(i).getContainerNum().intValue();
                if (list1.get(i).getContainerType().startsWith("2")) {
                    total += (0.5 * num);
                } else if (list1.get(i).getContainerType().startsWith("4")) {
                    total += (1 * num);
                }
            }
        }
        if (vo.getDeductionAmout() != null) {
            deductionAmount = vo.getDeductionAmout();
        }
        payable = payable.subtract(deductionAmount);
        payableT = payableT.subtract(deductionAmount);
        hnrb = payable;
        hnrbT = payableT;
        // 查询余额明细
        List<BillBalanceJsListVO> billBalanceJsListVOS = fdBalanceDetailService.selectBillJsList(vo.getBalanceBillNo());
        if (CollUtil.isNotEmpty(billBalanceJsListVOS)) {
            for (BillBalanceJsListVO billBalanceJsListVO : billBalanceJsListVOS) {
                remarks = remarks + (x) + "、" + vo.getCustomerName() + shifmanagements.get(0).getShiftName() + "，省级班列号:" + shifmanagements.get(0).getProvinceShiftNo() + "，与多联确认最终汇率后产生余额" + billBalanceJsListVO.getTkAmount() + "元，本次抵扣" + billBalanceJsListVO.getBcdkAmount() + "元;\n";
                x += 1;
            }
        }

        // 收款余额
        List<BillBalanceSkListVO> billBalanceSkListVOS = fdBalanceDetailService.selectBillSkList(vo.getBalanceBillNo());
        if (CollUtil.isNotEmpty(billBalanceSkListVOS)) {
            for (BillBalanceSkListVO billBalanceSkListVO : billBalanceSkListVOS) {
                remarks = remarks + (x) + "、收款编号:" + billBalanceSkListVO.getReceiptNo() + "总金额" + billBalanceSkListVO.getTkAmount() + "元，本次抵扣" + billBalanceSkListVO.getBcdkAmount() + "元;\n";
                x += 1;
            }
        }

        // 量价捆绑余额
        List<BillBalanceLjkbListVO> billBalanceLjkbListVOS = fdBalanceDetailService.selectBillLjkbList(vo.getBalanceBillNo());
        if (CollUtil.isNotEmpty(billBalanceLjkbListVOS)) {
            for (BillBalanceLjkbListVO billBalanceLjkbListVO : billBalanceLjkbListVOS) {
                remarks = remarks + (x) + "、发运线路:" + billBalanceLjkbListVO.getShippingLine() + "总金额" + billBalanceLjkbListVO.getTkAmount() + "元，本次抵扣" + billBalanceLjkbListVO.getBcdkAmount() + "元;\n";
                x += 1;
            }
        }
        String totalStr;
        if (String.valueOf(total).contains(".5")) {
            totalStr = String.valueOf(total);
        } else {
            totalStr = String.valueOf(total).split("\\.")[0];
        }

        remarks = remarks + "  本次合计抵扣" + deductionAmount + "元。\n";
        remarks = remarks + x + "、本列合计" + totalStr + "车";

        if ("G".equals(shifmanagements.get(0).getTrip())) {
            List<Map<String, String>> countryList = fdFreightAccountingMapper.selectNumByCountry("S", shifmanagements.get(0).getShiftId(), fdShippingAccounts.get(0).getPlatformCode(), vo.getBalanceBillNo());
            if (CollUtil.isNotEmpty(countryList)) {
                for (Map<String, String> map : countryList) {
                    if (map != null && map.get("country_name") != null && !"".equals(map.get("country_name")) && map.get("total_num") != null && !"".equals(map.get("total_num")) && !"乌兹别克斯坦".equals(map.get("country_name")) && !"吉尔吉斯斯坦".equals(map.get("country_name")) && !"哈萨克斯坦".equals(map.get("country_name")) && !"土库曼斯坦".equals(map.get("country_name")) && !"塔吉克斯坦".equals(map.get("country_name"))) {
                        String totalNum = String.valueOf(map.get("total_num"));
                        if (totalNum != null && !"".equals(totalNum)) {
                            if (totalNum.endsWith(".0")) {
                                totalNum = totalNum.replace(".0", "");
                            }
                        }
                        remarks = remarks + "，到达" + map.get("country_name") + " " + totalNum + "车";
                    }
                }
            } else {
                remarks = remarks + "\n";
            }

        } else if ("R".equals(shifmanagements.get(0).getTrip())) {
            List<Map<String, String>> countryList = fdFreightAccountingMapper.selectNumByCountry("F", shifmanagements.get(0).getShiftId(), fdShippingAccounts.get(0).getPlatformCode(), vo.getBalanceBillNo());
            if (CollUtil.isNotEmpty(countryList)) {
                if (countryList.get(0).get("total_num") != null && !"".equals(countryList.get(0).get("total_num"))) {
                    String totalNum = String.valueOf(countryList.get(0).get("total_num"));
                    if (totalNum != null && !"".equals(totalNum)) {
                        if (totalNum.endsWith(".0")) {
                            totalNum = totalNum.replace(".0", "");
                        }
                    }
                    if (!"0".equals(totalNum)) {
                        remarks = remarks + "，发站国" + " " + totalNum + "车\n";
                    }
                }
            } else {
                remarks = remarks + "\n";
            }
        }

        if (StrUtil.isNotBlank(vo.getPlatformName())) {
            x += 1;
            remarks = remarks + x + "、收款人：" + vo.getPlatformName();
        }
        remarks += "。";
        //创建工作薄对象
        HSSFWorkbook workbook = new HSSFWorkbook();
        //创建工作表对象
        HSSFSheet sheet = workbook.createSheet();
        setSheet(workbook, sheet);
        //样式
        CellStyle style;
        //字体
        HSSFFont font;
        HSSFRow row;
        Cell cell;
        int rowIndex = 0;

        //样式
        font = workbook.createFont();
        font.setFontHeightInPoints((short) 22);//设置excel数据字体大小
        font.setFontName("方正小标宋简体");//设置字体
//        font.setBold(true);
        style = workbook.createCellStyle();
        style.setFont(font);
        style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
        style.setAlignment(HorizontalAlignment.CENTER);// 水平
        rowIndex = setRowTitle(shifmanagements, shipmentTime, workbook, sheet, style, rowIndex);
        ////

        rowIndex = setRowValue(workbook, sheet, rowIndex, domesticFreightT, overseasFreightRmbT, payable, hnrb, list1, 1);

////
        setLastRow(response, payableT, hnrbT, remarks, workbook, sheet, rowIndex);
        return new R<>(0, Boolean.TRUE, null, "导出完成！");
    }

    private static int setRowTitle(List<Shifmanagement> shifmanagements, String shipmentTime, HSSFWorkbook workbook, HSSFSheet sheet, CellStyle style, int rowIndex) {
        HSSFFont font;
        Cell cell;
        HSSFRow row;
    /*
    创建第一行——标题
    */
        row = sheet.createRow(rowIndex);//设置第一行，从零开始
        row.setHeightInPoints(35);//设置行高
        //填数据
        cell = row.createCell(0);
        cell.setCellValue("运费核算表");//第一行第一列为
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 15));
        for (Cell cellTemp : row) {
            cellTemp.setCellStyle(style);
        }
        rowIndex++;//另起一行

                                    /*
                                    创建第二行
                                    */
        row = sheet.createRow(rowIndex);
        row.setHeightInPoints(35);//设置行高
        //构建数据
        row.createCell(0).setCellValue("预发运日期");
        row.createCell(1);
        row.createCell(2).setCellValue(shipmentTime);
        row.createCell(3);
        row.createCell(4);
        row.createCell(5).setCellValue("省级班列单号");
        row.createCell(6);
        row.createCell(7);
        row.createCell(8);
        row.createCell(9);
        row.createCell(10).setCellValue(shifmanagements.get(0).getProvinceShiftNo());
        row.createCell(11);
        row.createCell(12);
        row.createCell(13);
        row.createCell(14);
        row.createCell(15);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 1));//合并0 - 1列
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 2, 4));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 5, 9));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 10, 15));

        //样式
        font = workbook.createFont();
        font.setFontHeightInPoints((short) 12);//设置excel数据字体大小
        font.setFontName("宋体");//设置字体
        style = workbook.createCellStyle();
        style.setFont(font);
        style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
        style.setAlignment(HorizontalAlignment.CENTER);// 水平
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderBottom(BorderStyle.THIN);//下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        for (Cell cellTemp : row) {
            cellTemp.setCellStyle(style);
        }
        //另起一行
        rowIndex++;

/*
        创建数据表格标题行
        */
        row = sheet.createRow(rowIndex);
        row.setHeightInPoints(35);//设置行高
        //构建数据
        row.createCell(0).setCellValue("序号");
        row.createCell(1).setCellValue("货源组织单位");
        row.createCell(2).setCellValue("线路");
        row.createCell(3).setCellValue("发站");
        row.createCell(4).setCellValue("口岸站");
        row.createCell(5).setCellValue("方向");
        row.createCell(6).setCellValue("类型");
        row.createCell(7).setCellValue("是否全程");
        row.createCell(8).setCellValue("箱属");
        row.createCell(9).setCellValue("箱型");
        row.createCell(10).setCellValue("箱量");
        row.createCell(11).setCellValue("境内单价");
        row.createCell(12).setCellValue("境内运费");
        row.createCell(13).setCellValue("境外单价");
        row.createCell(14).setCellValue("境外运费");
        row.createCell(15).setCellValue("币种");
        //样式
        font = workbook.createFont();
        font.setFontHeightInPoints((short) 12);//设置excel数据字体大小
        font.setFontName("宋体");//设置字体
        style = workbook.createCellStyle();
        style.setFont(font);
        style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
        style.setAlignment(HorizontalAlignment.CENTER);// 水平
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderBottom(BorderStyle.THIN);//下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        for (Cell cellTemp : row) {
            cellTemp.setCellStyle(style);
        }
        return rowIndex;
    }

    /**
     * 确认并核销
     *
     * @param billBalanceMainCityDTO 应收账单结算（市）信息
     * @return 结果
     */
    @Transactional
    @Override
    public R confirmAndVerify(BillBalanceMainCityDTO billBalanceMainCityDTO) {
        try {
            // 获取当前登录人信息
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String username = userInfo.getRealName();
            String platformLevel = userInfo.getPlatformLevel();
            String platformCode = userInfo.getPlatformCode();
            String platformName = userInfo.getPlatformName();

            // 修改主表信息
            BillBalanceMainCity billBalanceMainCity = new BillBalanceMainCity();
            BeanUtil.copyProperties(billBalanceMainCityDTO, billBalanceMainCity);

            // 判断是否完全抵扣
            int compareTo = billBalanceMainCity.getActualAmout().compareTo(new BigDecimal(0));
            if (compareTo == 0) {
                // 如果是完全抵扣数据 将结算单变为已核销数据  将余额减少
                // 查询出余额明细数据
                List<FdTradingDetails> tradingDetails = billBalanceMainCityMapper.selectTradingListByJsCode(billBalanceMainCity.getBalanceBillNo());
                if (CollectionUtil.isNotEmpty(tradingDetails)) {
                    for (FdTradingDetails fdTradingDetails : tradingDetails) {
                        // 修改余额明细
                        billBalanceMainCityMapper.updateBalanceDetailBySyAmount(fdTradingDetails.getBalanceId(), fdTradingDetails.getTransactionAmount());
                        // 修改流水数据的状态
                        fdTradingDetails.setTradingStatus("1");
                    }
                    fdTradingDetailsService.updateBatchById(tradingDetails);
                }
                // 修改余额明细状态
                billBalanceMainCity.setBalanceStatus(BalanceStatusEnum.VERIFIED.getKey());
            }

            // 判断当前登录人平台
            List<String> userNames = new ArrayList<>();
            // 消息内容
            StringBuffer sb = new StringBuffer();
            // 通知内容
            StringBuffer psb = new StringBuffer();
            // 业务类型
            String businessType = "";
            String businessName = "";
            if (billBalanceMainCityDTO.getSubmitType().equals(1) && platformLevel.equals("1") && billBalanceMainCityDTO.getDeductionAmout() != null && billBalanceMainCityDTO.getDeductionAmout().compareTo(new BigDecimal(0)) > 0 && billBalanceMainCityDTO.getPlatformCode().equals("MP210800001") && billBalanceMainCityDTO.getBalanceStatus().equals(BalanceStatusEnum.WAITPAY.getKey())) {

                // 市平台查询省平台计划岗
                userNames = billBalanceMainCityMapper.selectMessageUserByRoleCode(provincePlanRoleCode);
                // 拼接推送内容
                sb.append(platformName).append("提交了应收账单结算").append("（").append(billBalanceMainCityDTO.getBalanceBillName()).append("），请及时登录PC端在【计划岗管理-应收账单结算（P）】中审核。");
                businessType = MessageBusinessEnum.BILL_BALANCE.getKey();
                businessName = MessageBusinessEnum.BILL_BALANCE.getValue();
                psb.append("您有新的应收账单结算待审核，请及时处理。");
            }

            if (billBalanceMainCityDTO.getSubmitType().equals(1) && platformLevel.equals("2")) {
                userNames = billBalanceMainCityMapper.selectMessageUserByRoleCode(provinceCuRoleCode);
                // 拼接推送内容
                sb.append(username).append("提交了应付账单结算").append("（").append(billBalanceMainCityDTO.getBalanceBillName()).append("），请及时登录PC端在【客服岗管理-应付账单结算（S）】中确认。");
                businessType = MessageBusinessEnum.PAY_BILL.getKey();
                businessName = MessageBusinessEnum.PAY_BILL.getValue();
                psb.append("您有新的应付账单结算待确认，请及时处理。");
            }

            // 生成消息数据
            if (CollectionUtil.isNotEmpty(userNames)) {
                List<MessageCenter> addMessageDTOS = new ArrayList<>();
                for (String userNameRo : userNames) {
                    MessageCenter addMessageDTO = new MessageCenter();
                    addMessageDTO.setReceiveUser(userNameRo);
                    addMessageDTO.setModuleType(MessageTypeEnum.SETTLEMENT.getKey());
                    addMessageDTO.setModuleName(MessageTypeEnum.SETTLEMENT.getValue());
                    addMessageDTO.setBusinessType(businessType);
                    addMessageDTO.setBusinessName(businessName);
                    addMessageDTO.setBusinessId(String.valueOf(billBalanceMainCityDTO.getId()));
                    addMessageDTO.setContent(sb.toString());
                    addMessageDTO.setIsRead(0);
                    addMessageDTO.setCreateName(username);
                    addMessageDTO.setType("0");
                    addMessageDTO.setRemark(sb.toString());
                    addMessageDTO.setDelFlag("N");
                    addMessageDTO.setCreateTime(LocalDateTime.now());
                    addMessageDTOS.add(addMessageDTO);
                }
                messageCenterService.addMessage(addMessageDTOS, psb.toString(), businessName);
            }

            BillBalanceMainCityDetailVO billBalanceMainCityDetailVO = billBalanceMainCityMapper.selectBillBalanceMainCityById(billBalanceMainCityDTO.getId());
            List<BillBalanceMainCitySubDetailVO> billBalanceMainCitySubDetailVOS = billBalanceBindingCityMapper.selectProvinceBillSubListByBalanceNoTwo(billBalanceMainCityDetailVO.getBalanceBillNo());
            if (CollectionUtil.isEmpty(billBalanceMainCitySubDetailVOS)) {
                return R.error("业务流程出错");
            }
            for (BillBalanceMainCitySubDetailVO vo : billBalanceMainCitySubDetailVOS) {
                R r = claimRemittanceTwo(billBalanceMainCityDTO.getId(), billBalanceMainCityDetailVO, vo);
                if (r.getCode() == 1) {
                    return r;
                }
            }
            //修改结算单及账单状态
            updateBillBalanceMainCityAndBillStatus(billBalanceMainCityDTO.getId(), billBalanceMainCityDetailVO);
            return R.success();
        } catch (Exception e) {
            log.error("修改市应收账单结算报错：" + e);
            e.printStackTrace();
            return R.error("系统异常");
        }
    }

    @Override
    public R exportFreightAccountCity(BillBalanceMainCity billBalanceMainCity, HttpServletResponse response) {
        BillBalanceMainCityDetailVO vo = billBalanceMainCityMapper.selectBillBalanceMainCityById(billBalanceMainCity.getId());
        if (vo == null) {
            return new R<>(new Throwable("该结算单不存在"));
        }
        if (StrUtil.isBlank(vo.getShiftNos())) {
            return new R<>(new Throwable("该结算单没有班次信息，无法导出"));
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String[] split = vo.getShiftNos().split(",");
        if (split.length > 0) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
            //创建工作薄对象
            HSSFWorkbook workbook = new HSSFWorkbook();
            CellStyle cellStyle = getCellStyle(workbook, (short) 12);
            DataFormat format = workbook.createDataFormat();
            cellStyle.setDataFormat(format.getFormat("0.00"));
            CellStyle style = getCellStyle(workbook, (short) 12);
            for (String shiftNo : split) {
                Shifmanagement shifmanagement = shifmanagementMapper.selectShifmanagementByPlatCodeAndShiftNo(userInfo.getPlatformCode(), shiftNo);
                Integer containerNum = 0;
                BigDecimal yfAmount = BigDecimal.ZERO;
                BigDecimal ysAmount = BigDecimal.ZERO;
                Integer rowIndex = 0;
                //创建sheet页
                HSSFSheet sheet = workbook.createSheet(shiftNo);
                //创建表头
                rowIndex = setCityTitle(sdf, shifmanagement, rowIndex, sheet, workbook);
                //创建内容
                List<String> customerNos = fdBusCostMapper.selectCustomerNo(shiftNo, userInfo.getPlatformCode());
                if (CollUtil.isNotEmpty(customerNos)) {
                    for (String customerNo : customerNos) {
                        List<FreightAccountCityDTO> list = fdBusCostMapper.selectContainerNum(shiftNo, userInfo.getPlatformCode(), customerNo);
                        if (CollUtil.isNotEmpty(list)) {
                            rowIndex = setCityList(list, rowIndex, sheet, style, cellStyle);
                            for (FreightAccountCityDTO dto : list
                            ) {
                                if (dto.getContainerNum() != null) {
                                    containerNum = containerNum + dto.getContainerNum();
                                }
                                yfAmount = yfAmount.add(dto.getYfAmount());
                                ysAmount = ysAmount.add(dto.getYsAmount());
                            }
                        }
                    }
                }
                //创建合计
                Row row = sheet.createRow(rowIndex);
                Cell cell0 = row.createCell(0);
                cell0.setCellValue("合计");
                for (int i = 1; i <= 6; i++) {
                    row.createCell(i);
                }
                Cell cell7 = row.createCell(7);
                cell7.setCellValue(containerNum);
                row.createCell(8);
                Cell cell9 = row.createCell(9);
                cell9.setCellValue(Double.parseDouble(String.format("%.2f", yfAmount)));
                row.createCell(10);
                Cell cell11 = row.createCell(11);
                cell11.setCellValue(Double.parseDouble(String.format("%.2f", ysAmount)));

                for (Cell cellTemp : row) {
                    if (cellTemp.getColumnIndex() == 9 || cellTemp.getColumnIndex() == 11) {
                        cellTemp.setCellStyle(cellStyle);
                    } else {
                        cellTemp.setCellStyle(style);
                    }
                }
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 6));

                // 自动调整所有列的宽度
                for (int i = 8; i <= 11; i++) {
                    sheet.autoSizeColumn(i);
                }
            }

            try {
                //response为HttpServletResponse对象
                response.setContentType("application/vnd.ms-excel;charset=utf-8");
                //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("运费核算表", "utf-8") + ".xls");
                ServletOutputStream out = response.getOutputStream();
                workbook.write(out);
                out.flush();
                // 关闭writer，释放内存
                out.close();
                workbook.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public Integer setCityList(List<FreightAccountCityDTO> list, Integer rowIndex, HSSFSheet sheet, CellStyle style, CellStyle cellStyle) {
        style.setWrapText(true);
        for (FreightAccountCityDTO dto : list) {
            Row row = sheet.createRow(rowIndex);
            Cell cell0 = row.createCell(0);
            cell0.setCellValue(rowIndex - 3);
            Cell cell1 = row.createCell(1);
            cell1.setCellValue(dto.getCustomerName());
            Cell cell2 = row.createCell(2);
            cell2.setCellValue(dto.getShippingLine());
            Cell cell3 = row.createCell(3);
            cell3.setCellValue(dto.getDestinationName());
            Cell cell4 = row.createCell(4);
            cell4.setCellValue(dto.getPortStation());
            Cell cell5 = row.createCell(5);
            cell5.setCellValue(IdentificationEnum.fromKey(dto.getIdentification()));
            Cell cell6 = row.createCell(6);
            cell6.setCellValue(dto.getContainerType());
            Cell cell7 = row.createCell(7);
            cell7.setCellValue(dto.getContainerNum());
            Cell cell8 = row.createCell(8);
            cell8.setCellValue(Double.parseDouble(String.format("%.2f", dto.getYfPrice())));
            Cell cell9 = row.createCell(9);
            cell9.setCellValue(Double.parseDouble(String.format("%.2f", dto.getYfAmount())));
            Cell cell10 = row.createCell(10);
            cell10.setCellValue(Double.parseDouble(String.format("%.2f", dto.getYsPrice())));
            Cell cell11 = row.createCell(11);
            cell11.setCellValue(Double.parseDouble(String.format("%.2f", dto.getYsAmount())));
            for (Cell cellTemp : row) {
                if (cellTemp.getColumnIndex() >= 8) {
                    cellTemp.setCellStyle(cellStyle);
                } else {
                    cellTemp.setCellStyle(style);
                }
            }
            rowIndex++;
        }
        if (list.size() > 1) {
            sheet.addMergedRegion(new CellRangeAddress(rowIndex - list.size(), rowIndex - 1, 1, 1));
            sheet.addMergedRegion(new CellRangeAddress(rowIndex - list.size(), rowIndex - 1, 2, 2));
            sheet.addMergedRegion(new CellRangeAddress(rowIndex - list.size(), rowIndex - 1, 3, 3));
            sheet.addMergedRegion(new CellRangeAddress(rowIndex - list.size(), rowIndex - 1, 4, 4));
        }
        return rowIndex;
    }

    private Integer setCityTitle(SimpleDateFormat sdf, Shifmanagement shifmanagement, Integer rowIndex, HSSFSheet sheet, HSSFWorkbook workbook) {
        CellStyle style = getCellStyle(workbook, (short) 12);

        //字体
        CellStyle style1 = getCellStyle(workbook, (short) 22);
        //创建第一行——标题
        Row row = sheet.createRow(rowIndex);
        row.setHeightInPoints(35);//设置行高
        //填数据
        Cell cell = row.createCell(0);
        cell.setCellValue("运费核算表");
        for (int i = 1; i <= 11; i++) {
            row.createCell(i);
        }
        for (Cell cellTemp : row) {
            cellTemp.setCellStyle(style1);
        }
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 11));
        rowIndex++;

        Row row1 = sheet.createRow(rowIndex);
        Cell cell10 = row1.createCell(0);
        cell10.setCellValue("预发运日期");
        row1.createCell(1);
        Cell cell12 = row1.createCell(2);
        if (shifmanagement.getPlanShipTime() != null) {
            cell12.setCellValue(sdf.format(shifmanagement.getPlanShipTime()));
        }
        row1.createCell(3);
        row1.createCell(4);
        Cell cell15 = row1.createCell(5);
        cell15.setCellValue("班列代码");
        row1.createCell(6);
        row1.createCell(7);
        Cell cell18 = row1.createCell(8);
        cell18.setCellValue(shifmanagement.getShiftName());
        for (int i = 9; i <= 11; i++) {
            row1.createCell(i);
        }
        for (Cell cellTemp : row1) {
            cellTemp.setCellStyle(style);
        }
        rowIndex++;

        Row row2 = sheet.createRow(rowIndex);
        for (int i = 0; i <= 4; i++) {
            row2.createCell(i);
        }
        Cell cell25 = row2.createCell(5);
        cell25.setCellValue("类型");
        Cell cell26 = row2.createCell(6);
        cell26.setCellValue("箱型");
        Cell cell27 = row2.createCell(7);
        cell27.setCellValue("箱量");
        Cell cell28 = row2.createCell(8);
        cell28.setCellValue("应付运费");
        row2.createCell(9);
        Cell cell210 = row2.createCell(10);
        cell210.setCellValue("应收运费");
        row2.createCell(11);
        for (Cell cellTemp : row2) {
            cellTemp.setCellStyle(style);
        }
        rowIndex++;

        Row row3 = sheet.createRow(rowIndex);
        Cell cell30 = row3.createCell(0);
        cell30.setCellValue("序号");
        Cell cell31 = row3.createCell(1);
        cell31.setCellValue("客户名称");
        Cell cell32 = row3.createCell(2);
        cell32.setCellValue("线路");
        Cell cell33 = row3.createCell(3);
        cell33.setCellValue("发站");
        Cell cell34 = row3.createCell(4);
        cell34.setCellValue("口岸站");
        for (int i = 5; i <= 7; i++) {
            row3.createCell(i);
        }
        Cell cell38 = row3.createCell(8);
        cell38.setCellValue("单价");
        Cell cell39 = row3.createCell(9);
        cell39.setCellValue("金额");
        Cell cell310 = row3.createCell(10);
        cell310.setCellValue("单价");
        Cell cell311 = row3.createCell(11);
        cell311.setCellValue("金额");
        for (Cell cellTemp : row3) {
            cellTemp.setCellStyle(style);
        }
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 2, rowIndex - 1, 0, 1));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 2, rowIndex - 1, 2, 4));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 2, rowIndex - 2, 5, 7));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 2, rowIndex - 2, 8, 11));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex, 5, 5));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex, 6, 6));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex, 7, 7));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex - 1, 8, 9));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex - 1, 10, 11));
        rowIndex++;
        return rowIndex;
    }

    private static CellStyle getCellStyle(HSSFWorkbook workbook, short num) {
        //字体
        HSSFFont font = workbook.createFont();
        //样式
        font.setFontHeightInPoints(num);
        font.setFontName("方正小标宋简体");
        CellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }
}
