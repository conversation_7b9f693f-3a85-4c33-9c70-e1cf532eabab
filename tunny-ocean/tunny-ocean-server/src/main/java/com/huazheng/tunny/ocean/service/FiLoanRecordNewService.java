package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.FiLoanRecordNew;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;
import java.util.Map;

/**
 * 企业信用贷-贷款记录（新建） 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-03-16 15:10:37
 */
public interface FiLoanRecordNewService extends IService<FiLoanRecordNew> {
    /**
     * 查询企业信用贷-贷款记录（新建）信息
     *
     * @param rowId 企业信用贷-贷款记录（新建）ID
     * @return 企业信用贷-贷款记录（新建）信息
     */
    public FiLoanRecordNew selectFiLoanRecordNewById(String rowId);

    /**
     * 查询企业信用贷-贷款记录（新建）列表
     *
     * @param fiLoanRecordNew 企业信用贷-贷款记录（新建）信息
     * @return 企业信用贷-贷款记录（新建）集合
     */
    public List<FiLoanRecordNew> selectFiLoanRecordNewList(FiLoanRecordNew fiLoanRecordNew);

    public List<FiLoanRecordNew> selectFiLoanRecordNewExportList(FiLoanRecordNew fiLoanRecordNew);


    /**
     * 分页模糊查询企业信用贷-贷款记录（新建）列表
     * @return 企业信用贷-贷款记录（新建）集合
     */
    public Page selectFiLoanRecordNewListByLike(Query query);

    public R statisticsSum(Map<String, Object> params);



    /**
     * 新增企业信用贷-贷款记录（新建）
     *
     * @param fiLoanRecordNew 企业信用贷-贷款记录（新建）信息
     * @return 结果
     */
    public int insertFiLoanRecordNew(FiLoanRecordNew fiLoanRecordNew);

    /**
     * 修改企业信用贷-贷款记录（新建）
     *
     * @param fiLoanRecordNew 企业信用贷-贷款记录（新建）信息
     * @return 结果
     */
    public int updateFiLoanRecordNew(FiLoanRecordNew fiLoanRecordNew);

    /**
     * 删除企业信用贷-贷款记录（新建）
     *
     * @param rowId 企业信用贷-贷款记录（新建）ID
     * @return 结果
     */
    public int deleteFiLoanRecordNewById(String rowId);

    /**
     * 批量删除企业信用贷-贷款记录（新建）
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiLoanRecordNewByIds(Integer[] rowIds);

    /**
     * 新增或修改的接口
     * @param fiLoanRecordNew
     * @return
     */
    public R addorUpdateFiloanrecordnew(FiLoanRecordNew fiLoanRecordNew);

}

