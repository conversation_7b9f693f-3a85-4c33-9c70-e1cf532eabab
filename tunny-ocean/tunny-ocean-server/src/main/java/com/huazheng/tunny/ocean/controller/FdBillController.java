package com.huazheng.tunny.ocean.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.vo.FdBillVO;
import com.huazheng.tunny.ocean.api.vo.Remittance;
import com.huazheng.tunny.ocean.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.security.Security;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import java.util.UUID;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * 账单汇总表
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:30:35
 */
@RestController
@RequestMapping("/fdbill")
@Slf4j
public class FdBillController {
    @Autowired
    private FdBillService fdBillService;
    @Autowired
    private FdCosdetailService fdCosdetailService;
    @Autowired
    private FdTradingDetailsService fdTradingDetailsService;
    @Autowired
    private WaybillHeaderService waybillHeaderService;
    @Autowired
    private SysNoConfigService sysNoConfigService;
    @Autowired
    private FdFreightAccountingService fdFreightAccountingService;
    @Autowired
    private FdBalanceDetailService fdBalanceDetailService;
    @Autowired
    private WaybillContainerInfoService waybillContainerInfoService;
    @Autowired
    private OperationLogService operationLogService;
    @Autowired
    private FdBillSubService fdBillSubService;
    @Autowired
    private FdShippingAccountService fdShippingAccountService;
    @Value("${path.windowsPath}")
    private String windowsPath;
    @Value("${path.linuxPath}")
    private String linuxPath;
    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdBillService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdBillService.selectFdBillListByLike(new Query<>(params));
    }

    /**
     * 查询收支明细
     * @param
     * @return R
     */
    @PostMapping("/getDetails")
    public R getDetails(@RequestBody FdBill fdBill) {
        List<FdBill> list = fdBillService.getDetails(fdBill);
        return new R<>(list);
    }


    /**
     * 信息
     * @param
     * @return R
     */
    @GetMapping("/selectbyuuid")
    public R info(@RequestParam("uuid") String uuid) {
        FdBill fdBill = fdBillService.selectFdBillById(uuid);
        return new R<>(fdBill);
    }

    /**
     * 信息
     *
     * @param Map
     * @return R
     */
    @PostMapping("/selectwaybillheader")
    public R selectwaybillheader(@RequestParam Map<String,Object> Map) {
        WaybillHeader waybillHeader  = BeanUtil.mapToBean(Map, WaybillHeader.class, false);
        waybillHeader.setAuditStatus("1");
        List<WaybillHeader> waybillHeaders = waybillHeaderService.selectWaybillHeaderList(waybillHeader);
        if ("1".equals(Map.get("type"))) {
//type等于1去重
            waybillHeaders = waybillHeaders.stream().collect(
                    collectingAndThen(
                            toCollection(() -> new TreeSet<>(Comparator.comparing(WaybillHeader::getCustomerName))), ArrayList::new)
            );
        }



        return new R<>(waybillHeaders);
    }

    /**
     * 保存
     *
     * @param fdBillVO
     * @return R
     */
    @PostMapping("/save")
    public R save(@RequestBody FdBillVO fdBillVO) {
        FdBill fdBill = new FdBill();
        BeanUtils.copyProperties(fdBillVO, fdBill);
        if("1".equals(fdBill.getIsOffsetBalance())) {
            if (fdBill.getOffsetBalance() == null || "".equals(fdBill.getOffsetBalance()) || fdBill.getOffsetBalance().compareTo(BigDecimal.valueOf(0)) == 0) {
                fdBill.setIsOffsetBalance("0");
            }
        }
        fdBill.setBillingState("0");
        fdBill.setUuid(UUID.randomUUID().toString());
        fdBill.setCreateUserrealname(SecurityUtils.getUserInfo().getRealName());
        fdBill.setCreateUsercode(SecurityUtils.getUserInfo().getUserName());
        fdBill.setBillCode(sysNoConfigService.genNo("FDBL"));
        fdBillService.insertFdBill(fdBill);
        String[] costdetailids = fdBillVO.getCostdetailids();
        HashMap<String, Object> map = new HashMap<>();
        map.put("ids", Arrays.asList(costdetailids));
        map.put("billCode", fdBill.getBillCode());
        map.put("billGenerate", "1");
        map.put("platformLevel", fdBillVO.getPlatformLevel());
        Integer integer = fdCosdetailService.updatebycostdetailids(map);

        //插入操作日志
        OperationLog log = new OperationLog();
        log.setUuid(UUID.randomUUID().toString());
        log.setProcessCode(fdBill.getBillCode());
        if(fdBill.getPlatformLevel()!=null&&!"".equals(fdBill.getPlatformLevel())){
            if("0".equals(fdBill.getPlatformLevel())){
                log.setProcessType("市-账单-新增");
            }else if("1".equals(fdBill.getPlatformLevel())){
                log.setProcessType("省-应收账单-新增");
            }
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        log.setOperationCode(userInfo.getUserName());
        log.setOperationName(userInfo.getRealName());
        log.setOperationTime(new Date());
        log.setOperationResult("新增完成");
        log.setCorInterface("/fdbill/save");
        log.setDeleteFlag("N");
        operationLogService.insertOperationLog(log);

        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param fdBillVO
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody FdBillVO fdBillVO) {
        FdBill fdBill = new FdBill();
        BeanUtils.copyProperties(fdBillVO, fdBill);
        fdBillService.updateFdBill(fdBill);
        String[] costdetailids = fdBillVO.getCostdetailids();
        if(StrUtil.isNotEmpty(fdBill.getTransportOrderNumber())){
            HashMap<String, Object> map = new HashMap<>();
            map.put("billCode", null);
            map.put("billGenerate", "0");
            map.put("transportOrderNumber", fdBill.getTransportOrderNumber());
            Integer integer = fdCosdetailService.updatebytransportOrderNumber(map);
            //把之前关联的费用明细的关联全部取消
            if(costdetailids!=null && costdetailids.length>0){
                map.put("ids", Arrays.asList(costdetailids));
                map.put("billCode", fdBill.getBillCode());
                map.put("billGenerate", "1");
                //关联新的费用明细
                Integer integer1 = fdCosdetailService.updatebycostdetailids(map);
            }
        }
        return new R<>(Boolean.TRUE);
    }

    /**
     * 应付账单--财务确认支付
     * @param fdBill
     * @return R
     */
    @PostMapping("/confirmPayment")
    public R confirmPayment(@RequestBody FdBill fdBill) {
        fdBillService.updateFdBill(fdBill);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param fdBill
     * @return R
     */
    @PostMapping("/reject")
    public R reject(@RequestBody FdBill fdBill) {
        return fdBillService.reject(fdBill);
    }

    /**
     * 支付
     */
    @PostMapping("/updatePayment")
    public R updatePayment(@RequestBody FdBill fdBill) {
        FdBill sel = new FdBill();
        sel.setBillCode(fdBill.getBillCode());
        sel.setDelFlag("N");
        fdBill.setBillingState("3");
        final List<FdBill> fdBills = fdBillService.selectFdBillList(sel);
        if(CollUtil.isNotEmpty(fdBills)){
            //应收账单的应收金额为0，则无需人工核销
            if(StrUtil.isNotEmpty(fdBills.get(0).getPlatformLevel()) && "1".equals(fdBills.get(0).getPlatformLevel())){
                if(fdBills.get(0).getAmountPayable()!=null && fdBills.get(0).getAmountPayable().compareTo(BigDecimal.valueOf(0))==0){
                    fdBill.setBillingState("4");
                }
            }
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        fdBill.setUpdateUsercode(userInfo.getUserName());
        fdBill.setUpdateUserrealname(userInfo.getRealName());
        fdBillService.updateFdBill(fdBill);

        //插入操作日志
        /*OperationLog log = new OperationLog();
        log.setUuid(UUID.randomUUID().toString());
        log.setProcessCode(fdBill.getBillCode());
        log.setProcessType("市-结算管理-账单支付");
        log.setOperationCode(userInfo.getUserName());
        log.setOperationName(userInfo.getRealName());
        log.setOperationTime(new Date());
        log.setOperationResult("支付成功");
        log.setOperationOpinion(fdBill.getNoteInformation());
        log.setCorInterface("/fdbill/updatePayment");
        log.setDeleteFlag("N");
        operationLogService.insertOperationLog(log);*/
        return new R(true);
    }
    /**
     * 余额抵扣
     *
     */

    @PostMapping("/deduction")
    @Transactional(rollbackFor = Exception.class)
    public R deduction(@RequestBody FdBillVO fdBillVO) {

        FdBill fdBill = new FdBill();
        BeanUtils.copyProperties(fdBillVO, fdBill);
        if(fdBill.getIsOffsetBalance()!=null){
            if("1".equals(fdBill.getIsOffsetBalance())) {
                if (fdBill.getOffsetBalance() == null || fdBill.getOffsetBalance().compareTo(BigDecimal.valueOf(0)) == 0) {
                    fdBill.setIsOffsetBalance("0");
                }
            }
        }else{
            fdBill.setIsOffsetBalance("0");
        }

        fdBill.setBillingState("1");
        fdBill.setBillConfirmerCode(SecurityUtils.getUserInfo().getUserName());
        fdBill.setBillConfirmerName(SecurityUtils.getUserInfo().getRealName());
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime time = LocalDateTime.now();
        String localTime = df.format(time);
        fdBill.setStandbyH(localTime);
        fdBillService.updateFdBill(fdBill);
        String[] costdetailids = fdBillVO.getCostdetailids();
        if(StrUtil.isNotEmpty(fdBill.getTransportOrderNumber())){
            HashMap<String, Object> map = new HashMap<>();
            map.put("billCode", null);
            map.put("billGenerate", "0");
            map.put("transportOrderNumber", fdBill.getTransportOrderNumber());
            Integer integer = fdCosdetailService.updatebytransportOrderNumber(map);
            //把之前关联的费用明细的关联全部取消
                if(costdetailids!=null && costdetailids.length>0){
                    map.put("ids", Arrays.asList(costdetailids));
                    map.put("billCode", fdBill.getBillCode());
                    map.put("billGenerate", "1");
                    //关联新的费用明细
                    Integer integer1 = fdCosdetailService.updatebycostdetailids(map);
                }
        }

        if("1".equals(fdBillVO.getPlatformLevel())||"2".equals(fdBillVO.getPlatformLevel())){
            //生成交易明细---省平台
            fdCosdetailService.insertFdTradingDetailsbydeduction2(fdBillVO);
//        }else if(fdBillVO.getProvinceTrainsNumber()!=null && !"".equals(fdBillVO.getProvinceTrainsNumber())){//市平台业务余额、量价捆绑不生成余额明细
        }else if("0".equals(fdBillVO.getPlatformLevel())){
            //生成交易明细---市平台
            fdCosdetailService.insertFdTradingDetailsbydeduction(fdBillVO);
        }

        //插入操作日志
        /*OperationLog log = new OperationLog();
        log.setUuid(UUID.randomUUID().toString());
        log.setProcessCode(fdBill.getBillCode());
        if(fdBill.getPlatformLevel()!=null&&!"".equals(fdBill.getPlatformLevel())){
            if("0".equals(fdBill.getPlatformLevel())){
                log.setProcessType("市-账单-预结算");
            }else if("1".equals(fdBill.getPlatformLevel())){
                log.setProcessType("省-应收账单-预结算");
            }
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        log.setOperationCode(userInfo.getUserName());
        log.setOperationName(userInfo.getRealName());
        log.setOperationTime(new Date());
        log.setOperationResult("预结算完成");
        log.setCorInterface("/fdbill/deduction");
        log.setDeleteFlag("N");
        operationLogService.insertOperationLog(log);*/

        return new R<>(Boolean.TRUE);
    }


    /**
     * 市平台余额抵扣
     */

    @PostMapping("/citybillconfirmed")
    @Transactional(rollbackFor = Exception.class)
    public R citybillconfirmed(@RequestBody FdBillVO fdBillVO) {
        FdBill fdBill = new FdBill();
        BeanUtils.copyProperties(fdBillVO, fdBill);
        fdBill.setBillingState("1");
        fdBillService.updateFdBill(fdBill);
        String[] costdetailids = fdBillVO.getCostdetailids();
        if(StrUtil.isNotEmpty(fdBill.getTransportOrderNumber())){
            HashMap<String, Object> map = new HashMap<>();
            map.put("billCode", null);
            map.put("billGenerate", "0");
            map.put("transportOrderNumber", fdBill.getTransportOrderNumber());
            Integer integer = fdCosdetailService.updatebytransportOrderNumber(map);
            //把之前关联的费用明细的关联全部取消
            if(costdetailids!=null && costdetailids.length>0){
                map.put("ids", Arrays.asList(costdetailids));
                map.put("billCode", fdBill.getBillCode());
                map.put("billGenerate", "1");
                //关联新的费用明细
                Integer integer1 = fdCosdetailService.updatebycostdetailids(map);
            }
        }
        //生成交易明细
        //调用关联余额模块
        fdCosdetailService.insertFdTradingDetailsbycitydeduction(fdBillVO);

        return new R<>(Boolean.TRUE);

    }


    /**
     *市平台核算
     */

    @PostMapping("/cityaccounting")
    @Transactional(rollbackFor = Exception.class)
    public R cityaccounting(@RequestBody FdBillVO fdBillVO) {
        //修改费用明细状态
// TODO: 2021/8/23 目前看核算结束后不需要修改明细
        //修改修改账单状态
        fdBillVO.setBillingState("2");
        fdBillVO.setSettlementUserCode(SecurityUtils.getUserInfo().getUserName());
        fdBillVO.setSettlementUserName(SecurityUtils.getUserInfo().getRealName());
        fdBillService.updateFdBill(fdBillVO);
        //记录交易明细

        fdTradingDetailsService.insertFdTradingDetailsbyRemittance(fdBillVO);
        // TODO: 2021/8/25  生成上省平台对多联的结算费用

        return new R();
    }





    /**
     * 作废
     *
     */

    @PostMapping("/invalid")
    @Transactional(rollbackFor = Exception.class)
    public R invalid(@RequestBody FdBillVO fdBillVO) {
        // TODO: 2021/8/23 这里是不是要判断一下什么情况下不能作废账单
      //作废账单
        fdBillService.invalidbycode(fdBillVO.getBillCode());
        //删除费用明细和账单的关系
        fdCosdetailService.deleteFdCosdetailBybillcode(fdBillVO.getBillCode());
        //作废交易明细

//        fdTradingDetailsService.invalidbybillcode(fdBillVO.getBillCode());
        FdTradingDetails fdTradingDetails = new FdTradingDetails();
        fdTradingDetails.setTradingStatus("0");
        fdTradingDetails.setDeductionBillCode(fdBillVO.getBillCode());
        fdTradingDetailsService.update(fdTradingDetails);

        return new R<>(Boolean.TRUE);
    }


 /**
     * 核算
     *
     */

    @PostMapping("/accounting")
    @Transactional(rollbackFor = Exception.class)
    public R accounting(@RequestBody FdBillVO fdBillVO) {
//修改费用明细状态
// TODO: 2021/8/23 目前看核算结束后不需要修改明细
        //修改修改账单状态
        fdBillVO.setBillingState("已结算");
        fdBillVO.setBillingState("2");
        fdBillService.updateFdBill(fdBillVO);



        //2021-11-29 修改 结算时 判断是否为负值，若为负值 在判断是否为量价捆绑或是业务余额，交易明细表新增量价捆绑或是业务余额 在判断是否为省平台，省平台要添加到余额明细表中（注意里面内容都要补全），市平台不需要添加到余额明细表
        //记录交易明细
        fdTradingDetailsService.insertFdTradingDetailsbyRemittance(fdBillVO);
        /*//量价捆绑结算不用记录交易明细
        if (!"1".equals(fdBillVO.getStandbyE())) {

            fdTradingDetailsService.insertFdTradingDetailsbyRemittance(fdBillVO);
        }
*/
        //插入操作日志
        /*SecruityUser userInfo = SecurityUtils.getUserInfo();
        OperationLog log = new OperationLog();
        log.setUuid(UUID.randomUUID().toString());
        log.setProcessCode(fdBillVO.getBillCode());
        if(fdBillVO.getPlatformLevel()!=null&&!"".equals(fdBillVO.getPlatformLevel())){
            if("0".equals(fdBillVO.getPlatformLevel())){
                log.setProcessType("市-结算管理-结算");
            }else if("1".equals(fdBillVO.getPlatformLevel())){
                log.setProcessType("省-结算管理-结算");
            }
        }
        log.setOperationCode(userInfo.getUserName());
        log.setOperationName(userInfo.getRealName());
        log.setOperationTime(new Date());
        if(fdBillVO.getBillingState()!=null &&!"".equals(fdBillVO.getBillingState())){
            if("0".equals(fdBillVO.getBillingState())){
                log.setOperationResult("待确认");
            }else if("1".equals(fdBillVO.getBillingState())){
                log.setOperationResult("预结算");
            }else if("2".equals(fdBillVO.getBillingState())){
                log.setOperationResult("已结算");
            }else if("作废".equals(fdBillVO.getBillingState())){
                log.setOperationResult("作废");
            }
        }
        log.setOperationOpinion(fdBillVO.getNoteInformation());
        log.setCorInterface("/fdbill/accounting");
        log.setDeleteFlag("N");
        operationLogService.insertOperationLog(log);*/

        return new R<>(Boolean.TRUE);
    }

    @PostMapping("/accounting2")
    @Transactional(rollbackFor = Exception.class)
    public R accounting2(@RequestBody FdBillVO fdBillVO) {
//修改费用明细状态
// TODO: 2021/8/23 目前看核算结束后不需要修改明细
        //修改修改账单状态
        fdBillVO.setBillingState("4");
        fdBillService.updateFdBill(fdBillVO);

        //2021-11-29 修改 结算时 判断是否为负值，若为负值 在判断是否为量价捆绑或是业务余额，交易明细表新增量价捆绑或是业务余额 在判断是否为省平台，省平台要添加到余额明细表中（注意里面内容都要补全），市平台不需要添加到余额明细表
        //记录交易明细
        fdTradingDetailsService.insertFdTradingDetailsbyRemittance2(fdBillVO);

        return new R<>(Boolean.TRUE);
    }




    /**
     * 删除
     * @param id
     * @return R
     */
    @DeleteMapping("/{id}")
    public R delete(@PathVariable  Integer id) {
        fdBillService.deleteById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<Integer> ids) {
        fdBillService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<FdBill> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = fdBillService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<FdBill> list = reader.readAll(FdBill.class);
        fdBillService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }

    @GetMapping("/fdBillExport")
    public R fdBillExport(FdBill fdBill, HttpServletResponse response) throws Exception{
        List<FdBill> fdBills = fdBillService.selectFdBillList(fdBill);
        if(CollUtil.isNotEmpty(fdBills)){
            for (FdBill bill:fdBills
                 ) {
                if(StrUtil.isEmpty(bill.getBillingState())||!"3".equals(bill.getBillingState())){
                    return new R<>(500,Boolean.FALSE,null,"该账单未完成账单确认，无法导出！");
                }else{
                    FdBillSub sel = new FdBillSub();
                    sel.setBillSubCode(bill.getBillCode());
                    sel.setDeleteFlag("N");
                    final List<FdBillSub> fdBillSubs = fdBillSubService.selectFdBillSubList(sel);
                    if(CollUtil.isNotEmpty(fdBillSubs)) {
                        for (FdBillSub fdBillSub : fdBillSubs
                        ) {
                            if (StrUtil.isEmpty(fdBillSub.getBillingState()) || !"3".equals(fdBillSub.getBillingState())) {
                                return new R<>(500, Boolean.FALSE, null, "该账单子账单未完成账单确认，无法导出！");
                            }
                        }
                    }
                }
            }
        }
        String shipmentTime = fdBills.get(0).getShipmentTime();
        if(shipmentTime !=null && !"".equals(shipmentTime)){
            if(shipmentTime.contains(" 00:00:00")){
                shipmentTime = shipmentTime.replace(" 00:00:00","");
            }
        }else{
            shipmentTime = "";
        }

        List<FdFreightAccounting> list1 = fdFreightAccountingService.getfdFreightAccountingList2(fdBills.get(0).getProvinceTrainsNumber());
        BigDecimal domesticFreightT = BigDecimal.valueOf(0);
        BigDecimal overseasFreightRmbT = BigDecimal.valueOf(0);
        BigDecimal payable = BigDecimal.valueOf(0);
        BigDecimal payableT = BigDecimal.valueOf(0);
        BigDecimal hnrb = BigDecimal.valueOf(0);
        BigDecimal hnrbT = BigDecimal.valueOf(0);
        BigDecimal deductionAmount = BigDecimal.valueOf(0);
        int total = 0;
        int x = 1;
        String remarks = "";
        for(int i = 0;i < list1.size();i ++){
            if(list1.get(i).getContainerNum()!=null&&list1.get(i).getContainerNum().compareTo(BigDecimal.valueOf(0))!=0){

                list1.get(i).setDomesticUnitprice(list1.get(i).getDomesticFreight().divide(list1.get(i).getContainerNum(),2,BigDecimal.ROUND_HALF_UP));
                list1.get(i).setOverseasUnitprice(list1.get(i).getOverseasFreightRmb().divide(list1.get(i).getContainerNum(),2,BigDecimal.ROUND_HALF_UP));
            }else{
                list1.get(i).setDomesticUnitprice(BigDecimal.valueOf(0));
                list1.get(i).setOverseasUnitprice(BigDecimal.valueOf(0));
            }
            list1.get(i).setRowId(String.valueOf(i+1));
            domesticFreightT = domesticFreightT.add(list1.get(i).getDomesticFreight());
            overseasFreightRmbT = overseasFreightRmbT.add(list1.get(i).getOverseasFreightRmb());
            payable = payable.add(list1.get(i).getDomesticFreight()).add(list1.get(i).getOverseasFreightRmb());
            payableT = payableT.add(list1.get(i).getDomesticFreight()).add(list1.get(i).getOverseasFreightRmb());
            if(list1.get(i).getContainerNum() !=null &&list1.get(i).getContainerType()!=null && !"".equals(list1.get(i).getContainerType())){
                int num = list1.get(i).getContainerNum().intValue();
                if(list1.get(i).getContainerType().startsWith("2")){
                    total += (0.5 * num);
                }else if(list1.get(i).getContainerType().startsWith("4")){
                    total += (1 * num);
                }
            }
        }


        List<FdBill> receivableBills = fdBillService.getReceivableList(fdBills.get(0));
        if(receivableBills!=null && receivableBills.size()>0){
            FdBalanceDetail fdBalanceDetail = new FdBalanceDetail();
            fdBalanceDetail.setShiftId(receivableBills.get(0).getProvinceTrainsNumber());
            fdBalanceDetail.setShiftNo(receivableBills.get(0).getProvinceTrainsNumber());
            fdBalanceDetail.setBillCode(receivableBills.get(0).getBillCode());
            List<FdBalanceDetail> list2 = fdBalanceDetailService.selectRemarksList(fdBalanceDetail);
            for(int i = 0;i < list2.size();i ++){
                if(list2.get(i).getRemarks()!=null && !"".equals(list2.get(i).getRemarks())){
                    remarks = remarks + (i+1) + "、" +list2.get(i).getRemarks() + ";\n";
                    x += 1;
                }
            }
        }
        FdTradingDetails fdTradingDetails = new FdTradingDetails();
        fdTradingDetails.setDeductionBillCode(fdBill.getBillCode());
        fdTradingDetails.setPlatformLevel(fdBill.getPlatformLevel());
        List<FdTradingDetails> list3 = fdTradingDetailsService.selectHnrbList(fdTradingDetails);
        if(CollUtil.isNotEmpty(list3)){
            for(int i = 0;i < list3.size();i ++){
                if(list3.get(i).getBalanceId()!=null){
                    final FdBalanceDetail fdBalanceDetail = fdBalanceDetailService.selectFdBalanceDetailListById(list3.get(i).getBalanceId());
                    if(fdBalanceDetail!=null && StrUtil.isNotEmpty(fdBalanceDetail.getRemarks())){
                        remarks = remarks + (i+1) + "、" +fdBalanceDetail.getRemarks() +",本次抵扣"+list3.get(i).getTransactionAmount()+ "元;\n";
                        x += 1;
                    }
                }
                if(list3.get(i).getTransactionAmount()!=null){
                    deductionAmount = deductionAmount.add(list3.get(i).getTransactionAmount());
                }
            }
        }

        /*payable = payable.subtract(deductionAmount);
        payableT = payableT.subtract(deductionAmount);*/
        hnrb = payable.subtract(deductionAmount);
        hnrbT = payableT.subtract(deductionAmount);
        remarks = remarks +  "本次合计抵扣" + deductionAmount + "元。\n";

        remarks = remarks + x + "、本列合计" + total + "车";
        WaybillContainerInfo info = new WaybillContainerInfo();
        info.setShiftNo(fdBills.get(0).getTrainNumber());
        if("G".equals(fdBills.get(0).getDirection())){
            List<Map<String,String>> countryList = waybillContainerInfoService.selectNumByCountry(info);
            if(countryList!=null && countryList.size()>0){
                if(fdBills.get(0).getDirection()!=null &&!"".equals(fdBills.get(0).getDirection())){
                    for (Map<String,String> map:countryList
                    ) {
                        if(map.get("country_name")!=null && !"".equals(map.get("country_name")) && map.get("total_num")!=null && !"".equals(map.get("total_num"))
                        &&!"乌兹别克斯坦".equals(map.get("country_name"))
                        &&!"吉尔吉斯斯坦".equals(map.get("country_name"))
                        &&!"哈萨克斯坦".equals(map.get("country_name"))
                        &&!"土库曼斯坦".equals(map.get("country_name"))
                        &&!"塔吉克斯坦".equals(map.get("country_name"))){
                            String totalNum = String.valueOf(map.get("total_num"));
                            if(totalNum!=null && !"".equals(totalNum)){
                                if(totalNum.endsWith(".0")){
                                    totalNum = totalNum.replace(".0","");
                                }
                            }
                            remarks = remarks + "，到达" + map.get("country_name") +" "+ totalNum + "车";
                        }
                    }

                }
            }
        }else if("R".equals(fdBills.get(0).getDirection())){
            List<Map<String,String>> countryList = waybillContainerInfoService.selectNumFromCountry(info);
            if(countryList!=null && countryList.size()>0){
                if(countryList.get(0).get("total_num")!=null && !"".equals(countryList.get(0).get("total_num"))){
                    remarks = remarks + "，发站国" +" "+ countryList.get(0).get("total_num") + "车";
                }
            }
        }

        remarks +="。";


        //创建工作薄对象
        HSSFWorkbook workbook = new HSSFWorkbook();
        //创建工作表对象
        HSSFSheet sheet = workbook.createSheet();
        sheet.setColumnWidth(0,65*28);
        sheet.setColumnWidth(1,215*28);
        sheet.setColumnWidth(2,80*28);
        sheet.setColumnWidth(3,80*28);
        sheet.setColumnWidth(4,100*28);
        sheet.setColumnWidth(5,100*28);
        sheet.setColumnWidth(6,100*28);
        sheet.setColumnWidth(7,100*28);
        sheet.setColumnWidth(8,100*28);
        sheet.setColumnWidth(9,100*28);
        sheet.setColumnWidth(10,100*28);
        sheet.setColumnWidth(11,115*28);
        sheet.setColumnWidth(12,115*28);
        sheet.setColumnWidth(13,115*28);
        sheet.setColumnWidth(14,115*28);
        sheet.setColumnWidth(15,100*28);


        //设置sheet的Name
        workbook.setSheetName(0, "运费核算表");
        //样式
        CellStyle style;
        //字体
        HSSFFont font;
        HSSFRow row;
        Cell cell;
        int rowIndex = 0;

        /*
        创建第一行——标题
        */
        row = sheet.createRow(rowIndex);//设置第一行，从零开始
        row.setHeightInPoints(30);//设置行高
        //样式
        font = workbook.createFont();
        font.setFontHeightInPoints((short) 22);//设置excel数据字体大小
        font.setFontName("方正小标宋简体");//设置字体
//        font.setBold(true);
        style = workbook.createCellStyle();
        style.setFont(font);
        style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
        style.setAlignment(HorizontalAlignment.CENTER);// 水平
        //填数据
        cell = row.createCell(0);
        cell.setCellValue("运费核算表");//第一行第一列为
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 15));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
        for (Cell cellTemp : row) {
            cellTemp.setCellStyle(style);
        }
        rowIndex++;//另起一行

                                    /*
                                    创建第二行
                                    */
        row = sheet.createRow(rowIndex);
        row.setHeightInPoints(30);//设置行高
        //构建数据
        row.createCell(0).setCellValue("预发运日期");
        row.createCell(1);
        row.createCell(2).setCellValue(shipmentTime);
        row.createCell(3);
        row.createCell(4);
        row.createCell(5).setCellValue("省级班列单号");
        row.createCell(6);
        row.createCell(7);
        row.createCell(8);
        row.createCell(9);
        row.createCell(10).setCellValue(fdBills.get(0).getProvinceTrainsNumber());
        row.createCell(11);
        row.createCell(12);
        row.createCell(13);
        row.createCell(14);
        row.createCell(15);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 1));//合并0 - 1列
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 2, 4));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 5, 9));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 10, 15));

        //样式
        font = workbook.createFont();
        font.setFontHeightInPoints((short) 12);//设置excel数据字体大小
        font.setFontName("宋体");//设置字体
        style = workbook.createCellStyle();
        style.setFont(font);
        style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
        style.setAlignment(HorizontalAlignment.CENTER);// 水平
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderBottom(BorderStyle.THIN);//下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        for (Cell cellTemp : row) {
            cellTemp.setCellStyle(style);
        }
        //另起一行
        rowIndex++;
/*
        创建数据表格标题行
        */
        row = sheet.createRow(rowIndex);
        row.setHeightInPoints(30);//设置行高
        //构建数据
        row.createCell(0).setCellValue("序号");
        row.createCell(1).setCellValue("货源组织单位");
        row.createCell(2).setCellValue("线路");
        row.createCell(3).setCellValue("发站");
        row.createCell(4).setCellValue("口岸站");
        row.createCell(5).setCellValue("方向");
        row.createCell(6).setCellValue("类型");
        row.createCell(7).setCellValue("是否全程");
        row.createCell(8).setCellValue("箱属");
        row.createCell(9).setCellValue("箱型");
        row.createCell(10).setCellValue("箱量");
        row.createCell(11).setCellValue("境内单价");
        row.createCell(12).setCellValue("境内运费");
        row.createCell(13).setCellValue("境外单价");
        row.createCell(14).setCellValue("境外运费");
        row.createCell(15).setCellValue("币种");
        //样式
        font = workbook.createFont();
        font.setFontHeightInPoints((short) 12);//设置excel数据字体大小
        font.setFontName("宋体");//设置字体
        style = workbook.createCellStyle();
        style.setFont(font);
        style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
        style.setAlignment(HorizontalAlignment.CENTER);// 水平
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderBottom(BorderStyle.THIN);//下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        for (Cell cellTemp : row) {
            cellTemp.setCellStyle(style);
        }
        //另起一行
        rowIndex++;
        rowIndex = 3;
        //样式
        font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
        font.setFontName("宋体");//设置字体
        style = workbook.createCellStyle();
        style.setFont(font);
        style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
        style.setAlignment(HorizontalAlignment.CENTER);// 水平
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderBottom(BorderStyle.THIN);//下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setWrapText(true);
        int num = 1;
        int y = 0;
        for (int i = 0; i < list1.size(); i++) {
            int cellIndex_data = 0;
            row = sheet.createRow(3 + i);
            row.setHeightInPoints(30);//设置行高
            //构建数据
            if(i!=0){
                if(list1.get(i).getCustomerName().equals(list1.get(i-1).getCustomerName())
                        &&list1.get(i).getShippingLine().equals(list1.get(i-1).getShippingLine())
                        &&list1.get(i).getDestinationName().equals(list1.get(i-1).getDestinationName())
                        &&list1.get(i).getPortStation().equals(list1.get(i-1).getPortStation())
                        &&list1.get(i).getTrip().equals(list1.get(i-1).getTrip())
                ){
                    row.createCell(0).setCellValue(String.valueOf(num));//序号
                }else{
                    num += 1;
                    row.createCell(0).setCellValue(String.valueOf(num));//序号
                }
            }else{
                row.createCell(0).setCellValue(String.valueOf(num));//序号
            }
            row.createCell(1).setCellValue(String.valueOf(list1.get(i).getCustomerName()));//货源组织单位
            row.createCell(2).setCellValue(String.valueOf(list1.get(i).getShippingLine()));//线路
            row.createCell(3).setCellValue(String.valueOf(list1.get(i).getDestinationName()));//发站
            row.createCell(4).setCellValue(String.valueOf(list1.get(i).getPortStation()));//口岸站
            row.createCell(5).setCellValue(String.valueOf(list1.get(i).getTrip()));//方向

            row.createCell(6).setCellValue(String.valueOf(list1.get(i).getBusinessIdentification()));//类型
            row.createCell(7).setCellValue(String.valueOf(list1.get(i).getIsFull()));//是否全程
            row.createCell(8).setCellValue(String.valueOf(list1.get(i).getContainerOwner()));//箱属
            row.createCell(9).setCellValue(String.valueOf(list1.get(i).getContainerType()));//箱型
            row.createCell(10).setCellValue(String.valueOf(list1.get(i).getContainerNum()));//箱量
            row.createCell(11).setCellValue(String.valueOf(list1.get(i).getDomesticUnitprice()));//境内单价
            row.createCell(12).setCellValue(String.valueOf(list1.get(i).getDomesticFreight()));//境内运费
            row.createCell(13).setCellValue(String.valueOf(list1.get(i).getOverseasUnitprice()));//境外单价
            row.createCell(14).setCellValue(String.valueOf(list1.get(i).getOverseasFreightRmb()));//境外运费
            row.createCell(15).setCellValue(String.valueOf(list1.get(i).getMonetaryType()));//币种
            for (Cell cellTemp : row) {
                cellTemp.setCellStyle(style);
            }
            if(i!=0){
                if(list1.get(i).getCustomerName().equals(list1.get(i-1).getCustomerName())
                        &&list1.get(i).getShippingLine().equals(list1.get(i-1).getShippingLine())
                        &&list1.get(i).getDestinationName().equals(list1.get(i-1).getDestinationName())
                        &&list1.get(i).getPortStation().equals(list1.get(i-1).getPortStation())
                        &&list1.get(i).getTrip().equals(list1.get(i-1).getTrip())
                ){
                    y += 1;
                }else{
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 0, 0));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 1, 1));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 2, 2));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 3, 3));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 4, 4));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 5, 5));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                    y = 0;
                }
            }
            if(i == list1.size()-1 && y != 0){
                sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 0, 0));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 1, 1));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 2, 2));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 3, 3));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 4, 4));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 5, 5));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
            }
            //另起一行
            rowIndex++;
        }

                                    /*
                                    写入总价行，TODO 数据自行从数据库取
                                     */
        row = sheet.createRow(rowIndex);
        row.setHeightInPoints(30);//设置行高
        //构建数据
        row.createCell(0).setCellValue("境内总价");
        row.createCell(1);
        row.createCell(2);
        row.createCell(3);
        row.createCell(4).setCellValue(String.valueOf(domesticFreightT));
        row.createCell(5);
        row.createCell(6);
        row.createCell(7);
        row.createCell(8).setCellValue("境外总价");
        row.createCell(9);
        row.createCell(10);
        row.createCell(11);
        row.createCell(12).setCellValue(String.valueOf(overseasFreightRmbT));
        row.createCell(13);
        row.createCell(14);
        row.createCell(15);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 3));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 4, 7));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 8, 11));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 12, 15));
        //样式
        font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
        font.setFontName("宋体");//设置字体
        style = workbook.createCellStyle();
        style.setFont(font);
        style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
        style.setAlignment(HorizontalAlignment.CENTER);// 水平
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderBottom(BorderStyle.THIN);//下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        for (Cell cellTemp : row) {
            cellTemp.setCellStyle(style);
        }
        //另起一行
        rowIndex++;

                                    /*
                                    写入应付、实付行，TODO 数据自行从数据库取
                                     */
        row = sheet.createRow(rowIndex);
        row.setHeightInPoints(30);//设置行高
        //构建数据
        row.createCell(0).setCellValue("应付");
        row.createCell(1);
        row.createCell(2);
        row.createCell(3);
        row.createCell(4).setCellValue(String.valueOf(payable));
        row.createCell(5);
        row.createCell(6);
        row.createCell(7);
        row.createCell(8).setCellValue("实付");
        row.createCell(9);
        row.createCell(10);
        row.createCell(11);
        row.createCell(12).setCellValue(String.valueOf(hnrb));
        row.createCell(13);
        row.createCell(14);
        row.createCell(15);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 3));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 4, 7));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 8, 11));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 12, 15));
        //样式
        font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
        font.setFontName("宋体");//设置字体
        style = workbook.createCellStyle();
        style.setFont(font);
        style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
        style.setAlignment(HorizontalAlignment.CENTER);// 水平
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderBottom(BorderStyle.THIN);//下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        for (Cell cellTemp : row) {
            cellTemp.setCellStyle(style);
        }
        //另起一行
        rowIndex++;

                                    /*
                                    写入合计行，TODO 数据自行从数据库取
                                     */
        row = sheet.createRow(rowIndex);
        row.setHeightInPoints(30);//设置行高
        //构建数据
        row.createCell(0).setCellValue("应付合计");
        row.createCell(1);
        row.createCell(2);
        row.createCell(3);
        row.createCell(4).setCellValue(String.valueOf(payableT));
        row.createCell(5);
        row.createCell(6);
        row.createCell(7);
        row.createCell(8).setCellValue("实付合计");
        row.createCell(9);
        row.createCell(10);
        row.createCell(11);
        row.createCell(12).setCellValue(String.valueOf(hnrbT));
        row.createCell(13);
        row.createCell(14);
        row.createCell(15);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 3));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 4, 7));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 8, 11));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 12, 15));
        int cellIndex_count = 0;
        for (Cell cellTemp : row) {
            //样式
            font = workbook.createFont();
            font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
            font.setFontName("宋体");//设置字体
            if (cellIndex_count >= 8) {
                font.setBold(true);
            }
            style = workbook.createCellStyle();
            style.setFont(font);
            style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
            style.setAlignment(HorizontalAlignment.CENTER);// 水平
            style.setBorderTop(BorderStyle.THIN);//上边框
            style.setBorderBottom(BorderStyle.THIN);//下边框
            style.setBorderLeft(BorderStyle.THIN);//左边框
            style.setBorderRight(BorderStyle.THIN);//右边框

            cellTemp.setCellStyle(style);
            cellIndex_count++;
        }
        //另起一行
        rowIndex++;

                                    /*
                                    写入备注行，TODO 数据自行从数据库取
                                     */
        row = sheet.createRow(rowIndex);
        row.setHeightInPoints(28 * (remarks.length() - remarks.replace("\n", "").length() + 1));//计算bzStr中换行符\n出现次数，动态设置行高
        //构建数据
        row.createCell(0).setCellValue("备注");
        row.createCell(1).setCellValue(remarks);
        row.createCell(2);
        row.createCell(3);
        row.createCell(4);
        row.createCell(5);
        row.createCell(6);
        row.createCell(7);
        row.createCell(8);
        row.createCell(9);
        row.createCell(10);
        row.createCell(11);
        row.createCell(12);
        row.createCell(13);
        row.createCell(14);
        row.createCell(15);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 1, 15));
        //样式
        font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
        font.setFontName("宋体");//设置字体
        font.setBold(true);
        style = workbook.createCellStyle();
        style.setFont(font);
        style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
        style.setAlignment(HorizontalAlignment.LEFT);// 水平
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderBottom(BorderStyle.THIN);//下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setWrapText(true);//设置单元格中的值 使用有\n换行符
        for (Cell cellTemp : row) {
            cellTemp.setCellStyle(style);
        }
        rowIndex++;
        rowIndex++;

                                    /*
                                    复核人、制单人
                                     */
        row = sheet.createRow(rowIndex);
        row.createCell(4).setCellValue("复核人：");
        row.createCell(10).setCellValue("制单人：");
        //样式
        font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
        font.setFontName("宋体");//设置字体
        font.setBold(false);
        style = workbook.createCellStyle();
        style.setFont(font);
        for (Cell cellTemp : row) {
            cellTemp.setCellStyle(style);
        }

        //写入数据流下载
        try {
            //response为HttpServletResponse对象
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("运费核算表", "utf-8") + ".xls");
            ServletOutputStream out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            // 关闭writer，释放内存
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new R<>(200, Boolean.TRUE, null, "导出完成！");
    }

    @GetMapping("/fdBillExport2")
    public R fdBillExport2(FdBill fdBill, HttpServletResponse response) throws Exception{
        List<FdBill> fdBills = fdBillService.selectFdBillList(fdBill);
        if(CollUtil.isNotEmpty(fdBills)){
            for (FdBill bill:fdBills
            ) {
                if(StrUtil.isNotEmpty(bill.getBillingState())&&"0".equals(bill.getBillingState())){
                    return new R<>(500,Boolean.FALSE,null,"该账单未提交，无法导出！");
                }
            }
            String shipmentTime = fdBills.get(0).getShipmentTime();
            if(shipmentTime !=null && !"".equals(shipmentTime)){
                if(shipmentTime.contains(" 00:00:00")){
                    shipmentTime = shipmentTime.replace(" 00:00:00","");
                }
            }else{
                shipmentTime = "";
            }

            FdShippingAccount sel = new FdShippingAccount();
            sel.setProvinceShiftNo(fdBills.get(0).getProvinceTrainsNumber());
            sel.setDeleteFlag("N");
            final List<FdShippingAccount> fdShippingAccounts = fdShippingAccountService.selectFdShippingAccountList(sel);
            if(CollUtil.isNotEmpty(fdShippingAccounts)){
                FdFreightAccounting sel2 = new FdFreightAccounting();
                sel2.setBillCode(fdBills.get(0).getBillCode());
                sel2.setAccountCode(fdShippingAccounts.get(0).getAccountCode());
                List<FdFreightAccounting> list1 = fdFreightAccountingService.getfdFreightAccountingList3(sel2);
                BigDecimal domesticFreightT = BigDecimal.valueOf(0);
                BigDecimal overseasFreightRmbT = BigDecimal.valueOf(0);
                BigDecimal payable = BigDecimal.valueOf(0);
                BigDecimal payableT = BigDecimal.valueOf(0);
                BigDecimal hnrb = BigDecimal.valueOf(0);
                BigDecimal hnrbT = BigDecimal.valueOf(0);
                BigDecimal deductionAmount = BigDecimal.valueOf(0);
                int total = 0;
                int x = 1;
                String remarks = "";
                for(int i = 0;i < list1.size();i ++){
                    if(list1.get(i).getContainerNum()!=null&&list1.get(i).getContainerNum().compareTo(BigDecimal.valueOf(0))!=0){

                        list1.get(i).setDomesticUnitprice(list1.get(i).getDomesticFreight().divide(list1.get(i).getContainerNum(),2,BigDecimal.ROUND_HALF_UP));
                        list1.get(i).setOverseasUnitprice(list1.get(i).getOverseasFreightRmb().divide(list1.get(i).getContainerNum(),2,BigDecimal.ROUND_HALF_UP));
                    }else{
                        list1.get(i).setDomesticUnitprice(BigDecimal.valueOf(0));
                        list1.get(i).setOverseasUnitprice(BigDecimal.valueOf(0));
                    }
                    list1.get(i).setRowId(String.valueOf(i+1));
                    domesticFreightT = domesticFreightT.add(list1.get(i).getDomesticFreight());
                    overseasFreightRmbT = overseasFreightRmbT.add(list1.get(i).getOverseasFreightRmb());
                    payable = payable.add(list1.get(i).getDomesticFreight()).add(list1.get(i).getOverseasFreightRmb());
                    payableT = payableT.add(list1.get(i).getDomesticFreight()).add(list1.get(i).getOverseasFreightRmb());
                    if(list1.get(i).getContainerNum() !=null &&list1.get(i).getContainerType()!=null && !"".equals(list1.get(i).getContainerType())){
                        int num = list1.get(i).getContainerNum().intValue();
                        if(list1.get(i).getContainerType().startsWith("2")){
                            total += (0.5 * num);
                        }else if(list1.get(i).getContainerType().startsWith("4")){
                            total += (1 * num);
                        }
                    }
                }


                List<FdBill> receivableBills = fdBillService.getReceivableList(fdBills.get(0));
                if(receivableBills!=null && receivableBills.size()>0){
                    FdBalanceDetail fdBalanceDetail = new FdBalanceDetail();
                    fdBalanceDetail.setShiftId(receivableBills.get(0).getProvinceTrainsNumber());
                    fdBalanceDetail.setShiftNo(receivableBills.get(0).getProvinceTrainsNumber());
                    fdBalanceDetail.setBillCode(receivableBills.get(0).getBillCode());
                    List<FdBalanceDetail> list2 = fdBalanceDetailService.selectRemarksList(fdBalanceDetail);
                    for(int i = 0;i < list2.size();i ++){
                        if(list2.get(i).getRemarks()!=null && !"".equals(list2.get(i).getRemarks())){
                            remarks = remarks + (i+1) + "、" +list2.get(i).getRemarks() + ";\n";
                            x += 1;
                        }
                    }
                }
                FdTradingDetails fdTradingDetails = new FdTradingDetails();
                fdTradingDetails.setDeductionBillCode(fdBills.get(0).getBillCode());
                fdTradingDetails.setPlatformLevel(fdBills.get(0).getPlatformLevel());
                List<FdTradingDetails> list3 = fdTradingDetailsService.selectHnrbList(fdTradingDetails);
                if(CollUtil.isNotEmpty(list3)){
                    for(int i = 0;i < list3.size();i ++){
                        if(list3.get(i).getBalanceId()!=null){
                            final FdBalanceDetail fdBalanceDetail = fdBalanceDetailService.selectFdBalanceDetailListById(list3.get(i).getBalanceId());
                            if(fdBalanceDetail!=null && StrUtil.isNotEmpty(fdBalanceDetail.getRemarks())){
                                remarks = remarks + (i+1) + "、" +fdBalanceDetail.getRemarks() +",本次抵扣"+list3.get(i).getTransactionAmount()+ "元;\n";
                                x += 1;
                            }
                        }
                        if(list3.get(i).getTransactionAmount()!=null){
                            deductionAmount = deductionAmount.add(list3.get(i).getTransactionAmount().negate());
                        }
                    }
                }

                payable = payable.subtract(deductionAmount);
                payableT = payableT.subtract(deductionAmount);
                hnrb = payable.subtract(deductionAmount);
                hnrbT = payableT.subtract(deductionAmount);
                remarks = remarks +  "本次合计抵扣" + deductionAmount + "元。\n";

                remarks = remarks + x + "、本列合计" + total + "车";
                List<Map<String,String>> countryList = fdBillService.selectNumByCountry(fdBills.get(0).getBillCode());
                if(countryList!=null && countryList.size()>0){
                    if("G".equals(fdBills.get(0).getDirection())){

                        if(fdBills.get(0).getDirection()!=null &&!"".equals(fdBills.get(0).getDirection())){
                            for (Map<String,String> map:countryList
                            ) {
                                if(map.get("country_name")!=null && !"".equals(map.get("country_name")) && map.get("total_num")!=null && !"".equals(map.get("total_num"))
                                        &&!"乌兹别克斯坦".equals(map.get("country_name"))
                                        &&!"吉尔吉斯斯坦".equals(map.get("country_name"))
                                        &&!"哈萨克斯坦".equals(map.get("country_name"))
                                        &&!"土库曼斯坦".equals(map.get("country_name"))
                                        &&!"塔吉克斯坦".equals(map.get("country_name"))){
                                    String totalNum = String.valueOf(map.get("total_num"));
                                    if(totalNum!=null && !"".equals(totalNum)){
                                        if(totalNum.endsWith(".0")){
                                            totalNum = totalNum.replace(".0","");
                                        }
                                    }
                                    remarks = remarks + "，到达" + map.get("country_name") +" "+ totalNum + "车";
                                }
                            }
                        }
                    }else if("R".equals(fdBills.get(0).getDirection())){
                        if(countryList!=null && countryList.size()>0){
                            if(countryList.get(0).get("total_num")!=null && !"".equals(countryList.get(0).get("total_num"))){
                                String totalNum = String.valueOf(countryList.get(0).get("total_num"));
                                if(totalNum!=null && !"".equals(totalNum)){
                                    if(totalNum.endsWith(".0")){
                                        totalNum = totalNum.replace(".0","");
                                    }
                                }
                                if(!"0".equals(totalNum)){
                                    remarks = remarks + "，发站国" +" "+ totalNum + "车";
                                }
                            }
                        }
                    }
                }

                remarks +="。";


                //创建工作薄对象
                HSSFWorkbook workbook = new HSSFWorkbook();
                //创建工作表对象
                HSSFSheet sheet = workbook.createSheet();
                sheet.setColumnWidth(0,65*27);
                sheet.setColumnWidth(1,215*27);
                sheet.setColumnWidth(2,80*27);
                sheet.setColumnWidth(3,80*27);
                sheet.setColumnWidth(4,100*27);
                sheet.setColumnWidth(5,100*27);
                sheet.setColumnWidth(6,100*27);
                sheet.setColumnWidth(7,100*27);
                sheet.setColumnWidth(8,100*27);
                sheet.setColumnWidth(9,100*27);
                sheet.setColumnWidth(10,100*27);
                sheet.setColumnWidth(11,115*27);
                sheet.setColumnWidth(12,115*27);
                sheet.setColumnWidth(13,115*27);
                sheet.setColumnWidth(14,115*27);
                sheet.setColumnWidth(15,100*27);

                //设置sheet的Name
                workbook.setSheetName(0, "运费核算表");
                //样式
                CellStyle style;
                //字体
                HSSFFont font;
                HSSFRow row;
                Cell cell;
                int rowIndex = 0;

        /*
        创建第一行——标题
        */
                row = sheet.createRow(rowIndex);//设置第一行，从零开始
                row.setHeightInPoints(35);//设置行高
                //样式
                font = workbook.createFont();
                font.setFontHeightInPoints((short) 22);//设置excel数据字体大小
                font.setFontName("方正小标宋简体");//设置字体
//        font.setBold(true);
                style = workbook.createCellStyle();
                style.setFont(font);
                style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
                style.setAlignment(HorizontalAlignment.CENTER);// 水平
                //填数据
                cell = row.createCell(0);
                cell.setCellValue("运费核算表");//第一行第一列为
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 15));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                for (Cell cellTemp : row) {
                    cellTemp.setCellStyle(style);
                }
                rowIndex++;//另起一行

                                    /*
                                    创建第二行
                                    */
                row = sheet.createRow(rowIndex);
                row.setHeightInPoints(35);//设置行高
                //构建数据
                row.createCell(0).setCellValue("预发运日期");
                row.createCell(1);
                row.createCell(2).setCellValue(shipmentTime);
                row.createCell(3);
                row.createCell(4);
                row.createCell(5).setCellValue("省级班列单号");
                row.createCell(6);
                row.createCell(7);
                row.createCell(8);
                row.createCell(9);
                row.createCell(10).setCellValue(fdBills.get(0).getProvinceTrainsNumber());
                row.createCell(11);
                row.createCell(12);
                row.createCell(13);
                row.createCell(14);
                row.createCell(15);
                //合并单元格
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 1));//合并0 - 1列
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 2, 4));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 5, 9));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 10, 15));

                //样式
                font = workbook.createFont();
                font.setFontHeightInPoints((short) 12);//设置excel数据字体大小
                font.setFontName("宋体");//设置字体
                style = workbook.createCellStyle();
                style.setFont(font);
                style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
                style.setAlignment(HorizontalAlignment.CENTER);// 水平
                style.setBorderTop(BorderStyle.THIN);//上边框
                style.setBorderBottom(BorderStyle.THIN);//下边框
                style.setBorderLeft(BorderStyle.THIN);//左边框
                style.setBorderRight(BorderStyle.THIN);//右边框
                for (Cell cellTemp : row) {
                    cellTemp.setCellStyle(style);
                }
                //另起一行
                rowIndex++;
/*
        创建数据表格标题行
        */
                row = sheet.createRow(rowIndex);
                row.setHeightInPoints(35);//设置行高
                //构建数据
                row.createCell(0).setCellValue("序号");
                row.createCell(1).setCellValue("货源组织单位");
                row.createCell(2).setCellValue("线路");
                row.createCell(3).setCellValue("发站");
                row.createCell(4).setCellValue("口岸站");
                row.createCell(5).setCellValue("方向");
                row.createCell(6).setCellValue("类型");
                row.createCell(7).setCellValue("是否全程");
                row.createCell(8).setCellValue("箱属");
                row.createCell(9).setCellValue("箱型");
                row.createCell(10).setCellValue("箱量");
                row.createCell(11).setCellValue("境内单价");
                row.createCell(12).setCellValue("境内运费");
                row.createCell(13).setCellValue("境外单价");
                row.createCell(14).setCellValue("境外运费");
                row.createCell(15).setCellValue("币种");
                //样式
                font = workbook.createFont();
                font.setFontHeightInPoints((short) 12);//设置excel数据字体大小
                font.setFontName("宋体");//设置字体
                style = workbook.createCellStyle();
                style.setFont(font);
                style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
                style.setAlignment(HorizontalAlignment.CENTER);// 水平
                style.setBorderTop(BorderStyle.THIN);//上边框
                style.setBorderBottom(BorderStyle.THIN);//下边框
                style.setBorderLeft(BorderStyle.THIN);//左边框
                style.setBorderRight(BorderStyle.THIN);//右边框
                for (Cell cellTemp : row) {
                    cellTemp.setCellStyle(style);
                }
                //另起一行
                rowIndex++;
                rowIndex = 3;
                //样式
                font = workbook.createFont();
                font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
                font.setFontName("宋体");//设置字体
                style = workbook.createCellStyle();
                style.setFont(font);
                style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
                style.setAlignment(HorizontalAlignment.CENTER);// 水平
                style.setBorderTop(BorderStyle.THIN);//上边框
                style.setBorderBottom(BorderStyle.THIN);//下边框
                style.setBorderLeft(BorderStyle.THIN);//左边框
                style.setBorderRight(BorderStyle.THIN);//右边框
                style.setWrapText(true);
                int num = 1;
                int y = 0;
                for (int i = 0; i < list1.size(); i++) {
                    int cellIndex_data = 0;
                    row = sheet.createRow(3 + i);
                    row.setHeightInPoints(35);//设置行高
                    //构建数据
                    if(i!=0){
                        if(list1.get(i).getCustomerName().equals(list1.get(i-1).getCustomerName())
                                &&list1.get(i).getShippingLine().equals(list1.get(i-1).getShippingLine())
                                &&list1.get(i).getDestinationName().equals(list1.get(i-1).getDestinationName())
                                &&list1.get(i).getPortStation().equals(list1.get(i-1).getPortStation())
                                &&list1.get(i).getTrip().equals(list1.get(i-1).getTrip())
                        ){
                            row.createCell(0).setCellValue(String.valueOf(num));//序号
                        }else{
                            num += 1;
                            row.createCell(0).setCellValue(String.valueOf(num));//序号
                        }
                    }else{
                        row.createCell(0).setCellValue(String.valueOf(num));//序号
                    }
                    row.createCell(1).setCellValue(String.valueOf(list1.get(i).getCustomerName()));//货源组织单位
                    row.createCell(2).setCellValue(String.valueOf(list1.get(i).getShippingLine()));//线路
                    row.createCell(3).setCellValue(String.valueOf(list1.get(i).getDestinationName()));//发站
                    row.createCell(4).setCellValue(String.valueOf(list1.get(i).getPortStation()));//口岸站
                    row.createCell(5).setCellValue(String.valueOf(list1.get(i).getTrip()));//方向

                    row.createCell(6).setCellValue(String.valueOf(list1.get(i).getBusinessIdentification()));//类型
                    row.createCell(7).setCellValue(String.valueOf(list1.get(i).getIsFull()));//是否全程
                    row.createCell(8).setCellValue(String.valueOf(list1.get(i).getContainerOwner()));//箱属
                    row.createCell(9).setCellValue(String.valueOf(list1.get(i).getContainerType()));//箱型
                    row.createCell(10).setCellValue(String.valueOf(list1.get(i).getContainerNum()));//箱量
                    row.createCell(11).setCellValue(String.valueOf(list1.get(i).getDomesticUnitprice()));//境内单价
                    row.createCell(12).setCellValue(String.valueOf(list1.get(i).getDomesticFreight()));//境内运费
                    row.createCell(13).setCellValue(String.valueOf(list1.get(i).getOverseasUnitprice()));//境外单价
                    row.createCell(14).setCellValue(String.valueOf(list1.get(i).getOverseasFreightRmb()));//境外运费
                    row.createCell(15).setCellValue(String.valueOf(list1.get(i).getMonetaryType()));//币种
                    for (Cell cellTemp : row) {
                        cellTemp.setCellStyle(style);
                    }
                    if(i!=0){
                        if(list1.get(i).getCustomerName().equals(list1.get(i-1).getCustomerName())
                                &&list1.get(i).getShippingLine().equals(list1.get(i-1).getShippingLine())
                                &&list1.get(i).getDestinationName().equals(list1.get(i-1).getDestinationName())
                                &&list1.get(i).getPortStation().equals(list1.get(i-1).getPortStation())
                                &&list1.get(i).getTrip().equals(list1.get(i-1).getTrip())
                        ){
                            y += 1;
                        }else{
                            sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 0, 0));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                            sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 1, 1));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                            sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 2, 2));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                            sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 3, 3));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                            sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 4, 4));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                            sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 5, 5));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                            y = 0;
                        }
                    }
                    if(i == list1.size()-1 && y != 0){
                        sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 0, 0));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                        sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 1, 1));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                        sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 2, 2));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                        sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 3, 3));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                        sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 4, 4));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                        sheet.addMergedRegion(new CellRangeAddress(rowIndex-y, rowIndex, 5, 5));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
                    }
                    //另起一行
                    rowIndex++;
                }

                                    /*
                                    写入总价行，TODO 数据自行从数据库取
                                     */
                row = sheet.createRow(rowIndex);
                row.setHeightInPoints(35);//设置行高
                //构建数据
                row.createCell(0).setCellValue("境内总价");
                row.createCell(1);
                row.createCell(2);
                row.createCell(3);
                row.createCell(4).setCellValue(String.valueOf(domesticFreightT));
                row.createCell(5);
                row.createCell(6);
                row.createCell(7);
                row.createCell(8).setCellValue("境外总价");
                row.createCell(9);
                row.createCell(10);
                row.createCell(11);
                row.createCell(12).setCellValue(String.valueOf(overseasFreightRmbT));
                row.createCell(13);
                row.createCell(14);
                row.createCell(15);
                //合并单元格
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 3));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 4, 7));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 8, 11));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 12, 15));
                //样式
                font = workbook.createFont();
                font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
                font.setFontName("宋体");//设置字体
                style = workbook.createCellStyle();
                style.setFont(font);
                style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
                style.setAlignment(HorizontalAlignment.CENTER);// 水平
                style.setBorderTop(BorderStyle.THIN);//上边框
                style.setBorderBottom(BorderStyle.THIN);//下边框
                style.setBorderLeft(BorderStyle.THIN);//左边框
                style.setBorderRight(BorderStyle.THIN);//右边框
                for (Cell cellTemp : row) {
                    cellTemp.setCellStyle(style);
                }
                //另起一行
                rowIndex++;

                                    /*
                                    写入应付、实付行，TODO 数据自行从数据库取
                                     */
                row = sheet.createRow(rowIndex);
                row.setHeightInPoints(35);//设置行高
                //构建数据
                row.createCell(0).setCellValue("应付");
                row.createCell(1);
                row.createCell(2);
                row.createCell(3);
                row.createCell(4).setCellValue(String.valueOf(payable));
                row.createCell(5);
                row.createCell(6);
                row.createCell(7);
                row.createCell(8).setCellValue("实付");
                row.createCell(9);
                row.createCell(10);
                row.createCell(11);
                row.createCell(12).setCellValue(String.valueOf(hnrb));
                row.createCell(13);
                row.createCell(14);
                row.createCell(15);
                //合并单元格
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 3));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 4, 7));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 8, 11));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 12, 15));
                //样式
                font = workbook.createFont();
                font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
                font.setFontName("宋体");//设置字体
                style = workbook.createCellStyle();
                style.setFont(font);
                style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
                style.setAlignment(HorizontalAlignment.CENTER);// 水平
                style.setBorderTop(BorderStyle.THIN);//上边框
                style.setBorderBottom(BorderStyle.THIN);//下边框
                style.setBorderLeft(BorderStyle.THIN);//左边框
                style.setBorderRight(BorderStyle.THIN);//右边框
                for (Cell cellTemp : row) {
                    cellTemp.setCellStyle(style);
                }
                //另起一行
                rowIndex++;

                                    /*
                                    写入合计行，TODO 数据自行从数据库取
                                     */
                row = sheet.createRow(rowIndex);
                row.setHeightInPoints(35);//设置行高
                //构建数据
                row.createCell(0).setCellValue("应付合计");
                row.createCell(1);
                row.createCell(2);
                row.createCell(3);
                row.createCell(4).setCellValue(String.valueOf(payableT));
                row.createCell(5);
                row.createCell(6);
                row.createCell(7);
                row.createCell(8).setCellValue("实付合计");
                row.createCell(9);
                row.createCell(10);
                row.createCell(11);
                row.createCell(12).setCellValue(String.valueOf(hnrbT));
                row.createCell(13);
                row.createCell(14);
                row.createCell(15);
                //合并单元格
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 3));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 4, 7));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 8, 11));
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 12, 15));
                int cellIndex_count = 0;
                for (Cell cellTemp : row) {
                    //样式
                    font = workbook.createFont();
                    font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
                    font.setFontName("宋体");//设置字体
                    if (cellIndex_count >= 8) {
                        font.setBold(true);
                    }
                    style = workbook.createCellStyle();
                    style.setFont(font);
                    style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
                    style.setAlignment(HorizontalAlignment.CENTER);// 水平
                    style.setBorderTop(BorderStyle.THIN);//上边框
                    style.setBorderBottom(BorderStyle.THIN);//下边框
                    style.setBorderLeft(BorderStyle.THIN);//左边框
                    style.setBorderRight(BorderStyle.THIN);//右边框

                    cellTemp.setCellStyle(style);
                    cellIndex_count++;
                }
                //另起一行
                rowIndex++;

                                    /*
                                    写入备注行，TODO 数据自行从数据库取
                                     */
                row = sheet.createRow(rowIndex);
                row.setHeightInPoints(28 * (remarks.length() - remarks.replace("\n", "").length() + 1));//计算bzStr中换行符\n出现次数，动态设置行高
                //构建数据
                row.createCell(0).setCellValue("备注");
                row.createCell(1).setCellValue(remarks);
                row.createCell(2);
                row.createCell(3);
                row.createCell(4);
                row.createCell(5);
                row.createCell(6);
                row.createCell(7);
                row.createCell(8);
                row.createCell(9);
                row.createCell(10);
                row.createCell(11);
                row.createCell(12);
                row.createCell(13);
                row.createCell(14);
                row.createCell(15);
                //合并单元格
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 1, 15));
                //样式
                font = workbook.createFont();
                font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
                font.setFontName("宋体");//设置字体
                font.setBold(true);
                style = workbook.createCellStyle();
                style.setFont(font);
                style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
                style.setAlignment(HorizontalAlignment.LEFT);// 水平
                style.setBorderTop(BorderStyle.THIN);//上边框
                style.setBorderBottom(BorderStyle.THIN);//下边框
                style.setBorderLeft(BorderStyle.THIN);//左边框
                style.setBorderRight(BorderStyle.THIN);//右边框
                style.setWrapText(true);//设置单元格中的值 使用有\n换行符
                for (Cell cellTemp : row) {
                    cellTemp.setCellStyle(style);
                }
                rowIndex++;
                rowIndex++;

                                    /*
                                    复核人、制单人
                                     */
                row = sheet.createRow(rowIndex);
                row.createCell(4).setCellValue("复核人：");
                row.createCell(10).setCellValue("制单人：");
                //样式
                font = workbook.createFont();
                font.setFontHeightInPoints((short) 11);//设置excel数据字体大小
                font.setFontName("宋体");//设置字体
                font.setBold(false);
                style = workbook.createCellStyle();
                style.setFont(font);
                for (Cell cellTemp : row) {
                    cellTemp.setCellStyle(style);
                }

                //设置 边距、页眉、页脚
                HSSFPrintSetup printSetup = sheet.getPrintSetup();
                printSetup.setPaperSize(HSSFPrintSetup.A4_PAPERSIZE); //纸张类型
                //打印方向，true：横向，false：纵向(默认)
                printSetup.setLandscape(true);
                printSetup.setHeaderMargin(0.2);
                printSetup.setFooterMargin(0.2);
                //设置打印缩放为88%
                ///printSetup.setScale((short) 55);
                //这个是sheet缩放设置，设置行调整为一列和行调整为一列必须要true
                sheet.setAutobreaks(true);
                //将所有列调整为一页
                printSetup.setFitHeight((short) 0);
                //将所有行调整为一页
                printSetup.setFitWidth((short) 1);


                //写入数据流下载
                try {
                    //response为HttpServletResponse对象
                    response.setContentType("application/vnd.ms-excel;charset=utf-8");
                    //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
                    response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("运费核算表", "utf-8") + ".xls");
                    ServletOutputStream out = response.getOutputStream();
                    workbook.write(out);
                    out.flush();
                    // 关闭writer，释放内存
                    out.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }else{
                return new R<>(500, Boolean.FALSE, null, "未查询到该账单的台账，无法导出！");
            }
        }else{
            return new R<>(500, Boolean.FALSE, null, "未查询到该账单，无法导出！");
        }

        return new R<>(200, Boolean.TRUE, null, "导出完成！");
    }

}
