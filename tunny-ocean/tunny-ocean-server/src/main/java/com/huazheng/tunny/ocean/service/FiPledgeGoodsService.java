package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FiPledgeGoods;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 质押货物表 服务接口层
 *
 * <AUTHOR>
 * @date 2022-11-10 10:38:38
 */
public interface FiPledgeGoodsService extends IService<FiPledgeGoods> {
    /**
     * 查询质押货物表信息
     *
     * @param rowId 质押货物表ID
     * @return 质押货物表信息
     */
    public FiPledgeGoods selectFiPledgeGoodsById(String rowId);

    /**
     * 查询质押货物表列表
     *
     * @param fiPledgeGoods 质押货物表信息
     * @return 质押货物表集合
     */
    public List<FiPledgeGoods> selectFiPledgeGoodsList(FiPledgeGoods fiPledgeGoods);


    /**
     * 分页模糊查询质押货物表列表
     * @return 质押货物表集合
     */
    public Page selectFiPledgeGoodsListByLike(Query query);



    /**
     * 新增质押货物表
     *
     * @param fiPledgeGoods 质押货物表信息
     * @return 结果
     */
    public int insertFiPledgeGoods(FiPledgeGoods fiPledgeGoods);

    /**
     * 修改质押货物表
     *
     * @param fiPledgeGoods 质押货物表信息
     * @return 结果
     */
    public int updateFiPledgeGoods(FiPledgeGoods fiPledgeGoods);

    /**
     * 删除质押货物表
     *
     * @param rowId 质押货物表ID
     * @return 结果
     */
    public int deleteFiPledgeGoodsById(String rowId);

    public int deleteFiPledgeGoodsByPledgeCode(String pledgeCode);

    /**
     * 批量删除质押货物表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiPledgeGoodsByIds(Integer[] rowIds);

}

