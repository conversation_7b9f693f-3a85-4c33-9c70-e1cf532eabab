package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.FdBill;

import java.util.List;
import java.util.Map;

/**
 * 账单汇总表 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:30:35
 */
public interface FdBillService extends IService<FdBill> {
    /**
     * 查询账单汇总表信息
     *
     * @param id 账单汇总表ID
     * @return 账单汇总表信息
     */
    public FdBill selectFdBillById(String id);

    /**
     * 查询账单汇总表列表
     *
     * @param fdBill 账单汇总表信息
     * @return 账单汇总表集合
     */
    public List<FdBill> selectFdBillList(FdBill fdBill);

    /**
     * 查询收支明细
     *
     * @param fdBill 账单汇总表信息
     * @return 账单汇总表集合
     */
    public List<FdBill> getDetails(FdBill fdBill);
    /**
     * 分页模糊查询账单汇总表列表
     * @return 账单汇总表集合
     */
    public Page selectFdBillListByLike(Query query);

    public Integer selectFdBillListByLikeCount(FdBill fdBill);



    /**
     * 新增账单汇总表
     *
     * @param fdBill 账单汇总表信息
     * @return 结果
     */
    public int insertFdBill(FdBill fdBill);

    /**
     * 修改账单汇总表
     *
     * @param fdBill 账单汇总表信息
     * @return 结果
     */
    public int updateFdBill(FdBill fdBill);

    public R reject(FdBill fdBill);

    /**
     * 删除账单汇总表
     *
     * @param id 账单汇总表ID
     * @return 结果
     */
    public int deleteFdBillById(Integer id);

    /**
     * 批量删除账单汇总表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdBillByIds(Integer[] ids);

    void invalidbycode(String billCode);

    public List<Map<String, Object>> getInfo(String billCode);

    public List<FdBill> getReceivableList(FdBill fdBill);

    public int updateFdBillByProvinceTrainsNumber(FdBill fdBill);

    public List<FdBill> selectFdBillProvinceTrainNumber(FdBill fdBill);

    public int updatePayment(FdBill fdBill);

    List<Map<String, String>> selectNumByCountry(String billCode);
}

