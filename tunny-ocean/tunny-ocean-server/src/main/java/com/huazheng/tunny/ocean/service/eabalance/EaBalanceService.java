package com.huazheng.tunny.ocean.service.eabalance;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.eabalance.EaBalance;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 余额表 服务接口层
 *
 * <AUTHOR>
 * @date 2025-07-01 13:37:31
 */
public interface EaBalanceService extends IService<EaBalance> {
    /**
     * 查询余额表信息
     *
     * @param balanceId 余额表ID
     * @return 余额表信息
     */
    public EaBalance selectEaBalanceById(Long balanceId);

    /**
     * 查询余额表列表
     *
     * @param eaBalance 余额表信息
     * @return 余额表集合
     */
    public List<EaBalance> selectEaBalanceList(EaBalance eaBalance);


    /**
     * 分页模糊查询余额表列表
     * @return 余额表集合
     */
    public Page selectEaBalanceListByLike(Query query);



    /**
     * 新增余额表
     *
     * @param eaBalance 余额表信息
     * @return 结果
     */
    public int insertEaBalance(EaBalance eaBalance);

    /**
     * 修改余额表
     *
     * @param eaBalance 余额表信息
     * @return 结果
     */
    public int updateEaBalance(EaBalance eaBalance);

    /**
     * 删除余额表
     *
     * @param balanceId 余额表ID
     * @return 结果
     */
    public int deleteEaBalanceById(Long balanceId);

    /**
     * 批量删除余额表
     *
     * @param balanceIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEaBalanceByIds(Integer[] balanceIds);

    public R generateBalance(EaBalance eaBalance);
}

