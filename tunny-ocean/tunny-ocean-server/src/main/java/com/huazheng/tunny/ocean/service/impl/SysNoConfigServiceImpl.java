package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.CustomerPlatformInfo;
import com.huazheng.tunny.ocean.api.vo.FdShippingAccountVO;
import com.huazheng.tunny.ocean.mapper.SysNoConfigMapper;
import com.huazheng.tunny.ocean.api.entity.SysNoConfig;
import com.huazheng.tunny.ocean.service.CustomerPlatformInfoService;
import com.huazheng.tunny.ocean.service.SysNoConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

@Service("sysNoConfigService")
public class SysNoConfigServiceImpl extends ServiceImpl<SysNoConfigMapper, SysNoConfig> implements SysNoConfigService {

    @Autowired
    private SysNoConfigMapper sysNoConfigMapper;
    @Autowired
    private CustomerPlatformInfoService customerPlatformInfoService;

    public SysNoConfigMapper getSysNoConfigMapper() {
        return sysNoConfigMapper;
    }

    public void setSysNoConfigMapper(SysNoConfigMapper sysNoConfigMapper) {
        this.sysNoConfigMapper = sysNoConfigMapper;
    }

    public CustomerPlatformInfoService getCustomerPlatformInfoService() {
        return customerPlatformInfoService;
    }

    public void setCustomerPlatformInfoService(CustomerPlatformInfoService customerPlatformInfoService) {
        this.customerPlatformInfoService = customerPlatformInfoService;
    }

    /**
     * 查询系统单号配置表信息
     *
     * @param rowId 系统单号配置表ID
     * @return 系统单号配置表信息
     */
    @Override
    public SysNoConfig selectSysNoConfigById(String rowId)
    {
        return sysNoConfigMapper.selectSysNoConfigById(rowId);
    }

    /**
     * 查询系统单号配置表列表
     *
     * @param sysNoConfig 系统单号配置表信息
     * @return 系统单号配置表集合
     */
    @Override
    public List<SysNoConfig> selectSysNoConfigList(SysNoConfig sysNoConfig)
    {
        return sysNoConfigMapper.selectSysNoConfigList(sysNoConfig);
    }


    /**
     * 分页模糊查询系统单号配置表列表
     * @return 系统单号配置表集合
     */
    @Override
    public Page selectSysNoConfigListByLike(Query query)
    {
        SysNoConfig sysNoConfig =  BeanUtil.mapToBean(query.getCondition(), SysNoConfig.class,false);
        query.setRecords(sysNoConfigMapper.selectSysNoConfigListByLike(query,sysNoConfig));
        return query;
    }

    @Override
    public String genNo(String flag) {
        SysNoConfig sysNoConfig=new SysNoConfig();
        String year=String.valueOf(Calendar.getInstance().get(Calendar.YEAR)).substring(2,4);
        Calendar c = Calendar.getInstance();
        c.add(Calendar.MONTH, 0);
        String month = new SimpleDateFormat("MM",Locale.CHINA).format(c.getTime());
        sysNoConfig.setNoType(flag);
        sysNoConfig.setYear(year);
        sysNoConfig.setMonth(month);
        List<SysNoConfig>list=sysNoConfigMapper.selectSysNoConfigList(sysNoConfig);
        if(CollUtil.isNotEmpty(list)){
            //若能匹配的上，则获取（当前序号+1）不能超过最大值，根据配置表中的序列号位数，自动补0
            Integer serialNo=Integer.parseInt(list.get(0).getSerialNo());
            Integer noNum=Integer.parseInt(list.get(0).getNoNum());
            Integer maxNum=Integer.parseInt(list.get(0).getMaxValue());
            Integer curSerialNo=serialNo+1;
            if(curSerialNo>maxNum){
                return "请联系系统管理员，系统需重新设置最大值，请补充";
            }
            String noWithZero=getNumber(curSerialNo,noNum);
            //更新序列号数据库表
            SysNoConfig temp=new SysNoConfig();
            temp.setNoType(flag);
            temp.setSerialNo(String.valueOf(curSerialNo));
            temp.setYear(year);
            temp.setMonth(month);
            sysNoConfigMapper.updateSysNoConfig(temp);
            return sysNoConfig.getNoType()+year+month+noWithZero;
        }else{
            //若匹配不上，则根据单号类型，上一月对应的年份（后两位，注意跨年）、上一月份,查询出：序列号位数、最大值、当前序号（0），新增到系统单号管理表；
            SysNoConfig temp=new SysNoConfig();
            Calendar a = Calendar.getInstance();
            a.add(Calendar.MONTH, -1);
            String dateStr = new SimpleDateFormat("yyyyMM",Locale.CHINA).format(a.getTime());
            String lastMonth = dateStr.substring(4,6);
            String yearOflastMonth = dateStr.substring(2,4);
            temp.setYear(yearOflastMonth);
            temp.setNoType(sysNoConfig.getNoType());
            temp.setMonth(lastMonth);
            List<SysNoConfig>list2=sysNoConfigMapper.selectSysNoConfigListLastMonth(temp);

            if(CollUtil.isNotEmpty(list2)){
                SysNoConfig temp2=list2.get(0);
                SysNoConfig curSysConfig=new SysNoConfig();
                curSysConfig.setYear(year);
                curSysConfig.setMonth(month);
                curSysConfig.setNoType(temp2.getNoType());
                curSysConfig.setMaxValue(temp2.getMaxValue());
                curSysConfig.setNoNum(temp2.getNoNum());
                curSysConfig.setSerialNo("0");
                curSysConfig.setAddTime(LocalDateTime.now());
                sysNoConfigMapper.insertSysNoConfig(curSysConfig);
                return sysNoConfig.getNoType()+year+month+getNumber(0,Integer.parseInt(temp2.getNoNum()));
            }else{
                return "请联系系统管理员，系统没有上月相关设置，请补充";
            }
        }
    }

    /**
     * [线路英文简称（2位大写字母）] [地市三字码（3位大写字母）] [年份（2位）月份（2位）][省级序号（00）][整列Z/回程整列H]
     * @param flag
     * @return
     */
    @Override
    public String createSjblh(String flag, FdShippingAccountVO vo) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        CustomerPlatformInfo customerPlatformInfo=new CustomerPlatformInfo();
        customerPlatformInfo.setCustomerCode(vo.getPlatformCode());
        List<CustomerPlatformInfo> list2 = customerPlatformInfoService.selectCustomerPlatformInfoList(customerPlatformInfo);
        if(CollUtil.isNotEmpty(list2)){
            if("".equals(list2.get(0).getRemarksCode()) && list2.get(0).getRemarksCode()==null){
                return "请联系系统管理员,该账号没有助记码，请补充账号助记码";
            }
        }

        SysNoConfig sysNoConfig=new SysNoConfig();
        if(vo.getShippingTime() == null){
            return "请联系系统管理员,该台账没有发运日期";
        }
        // 定义日期时间格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yy-MM");

        // 格式化 LocalDateTime 为两位长度的年和月
        String formattedDate = vo.getShippingTime().format(formatter);
        String year=formattedDate.split("-")[0];
        String month = formattedDate.split("-")[1];
        sysNoConfig.setNoType(flag);
        sysNoConfig.setMonth(month);
        sysNoConfig.setYear(year);
        sysNoConfig.setDeleteFlag("N");
        sysNoConfig.setResveredField01("SJBLH");
        sysNoConfig.setResveredField02(list2.get(0).getRemarksCode());
        List<SysNoConfig>list=sysNoConfigMapper.selectSysNoConfigList(sysNoConfig);
        if(list.size()>0){
            Integer serialNo=Integer.parseInt(list.get(0).getSerialNo());
            SysNoConfig temp=new SysNoConfig();
            temp.setNoType(flag);
            Integer curSerialNo=serialNo+1;
            Integer a = 10;
            if(curSerialNo<a){
                String x = "0";
                StringBuilder stringBuilder=new StringBuilder();
                stringBuilder.append(x).append(curSerialNo);
                temp.setSerialNo(stringBuilder.toString());
            }else{
                temp.setSerialNo(String.valueOf(curSerialNo));
            }
            temp.setYear(year);
            temp.setMonth(month);
            temp.setUpdateTime(LocalDateTime.now());
            temp.setUpdateWho(usercode);
            temp.setUpdateWhoName(username);
            temp.setResveredField01("SJBLH");
            temp.setResveredField02(list2.get(0).getRemarksCode());
            sysNoConfigMapper.updateSysNoConfigSjbl(temp);
            return flag+list2.get(0).getRemarksCode()+year+month+temp.getSerialNo();
        }else{
            SysNoConfig temp=new SysNoConfig();
            temp.setNoType(flag);
            temp.setSerialNo("02");
            temp.setYear(year);
            temp.setMonth(month);
            temp.setAddTime(LocalDateTime.now());
            temp.setAddWho(usercode);
            temp.setAddWhoName(username);
            temp.setResveredField01("SJBLH");
            temp.setResveredField02(list2.get(0).getRemarksCode());
            sysNoConfigMapper.insertSysNoConfig(temp);
            return flag+list2.get(0).getRemarksCode()+year+month+"01";
        }
    }

    /**
     * 新增系统单号配置表
     *
     * @param sysNoConfig 系统单号配置表信息
     * @return 结果
     */
    @Override
    public int insertSysNoConfig(SysNoConfig sysNoConfig)
    {
        return sysNoConfigMapper.insertSysNoConfig(sysNoConfig);
    }

    /**
     * 修改系统单号配置表
     *
     * @param sysNoConfig 系统单号配置表信息
     * @return 结果
     */
    @Override
    public int updateSysNoConfig(SysNoConfig sysNoConfig)
    {
        return sysNoConfigMapper.updateSysNoConfig(sysNoConfig);
    }


    /**
     * 删除系统单号配置表
     *
     * @param rowId 系统单号配置表ID
     * @return 结果
     */
    @Override
    public int deleteSysNoConfigById(String rowId)
    {
        return sysNoConfigMapper.deleteSysNoConfigById( rowId);
    };


    /**
     * 批量删除系统单号配置表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSysNoConfigByIds(Integer[] rowIds)
    {
        return sysNoConfigMapper.deleteSysNoConfigByIds( rowIds);
    }

    public static String getNumber(int number,int nums){
        NumberFormat formatter = NumberFormat.getNumberInstance();
        formatter.setMinimumIntegerDigits(nums);
        formatter.setGroupingUsed(false);
        return formatter.format(number);
    }

    public static void main(String[] args) {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.MONTH, 0);
        String month = new SimpleDateFormat("MM",Locale.CHINA).format(c.getTime());
        System.out.println(month);
    }

}
