package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.FdBusCostDetail;
import com.huazheng.tunny.ocean.api.entity.FdInvoiceApplicationDetail;
import com.huazheng.tunny.ocean.api.entity.FdInvoiceCheckHistory;
import com.huazheng.tunny.ocean.mapper.FdBusCostDetailMapper;
import com.huazheng.tunny.ocean.mapper.FdInvoiceApplicationDetailMapper;
import com.huazheng.tunny.ocean.mapper.FdInvoiceCheckHistoryMapper;
import com.huazheng.tunny.ocean.service.FdInvoiceCheckHistoryService;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.List;

@Service("fdInvoiceCheckHistoryService")
public class FdInvoiceCheckHistoryServiceImpl extends ServiceImpl<FdInvoiceCheckHistoryMapper, FdInvoiceCheckHistory> implements FdInvoiceCheckHistoryService {

    @Autowired
    private FdInvoiceCheckHistoryMapper fdInvoiceCheckHistoryMapper;
    @Autowired
    private FdBusCostDetailMapper fdBusCostDetailMapper;
    @Autowired
    private FdInvoiceApplicationDetailMapper fdInvoiceApplicationDetailMapper;
    @Value("${path.windowsPath}")
    private String windowsPath;
    @Value("${path.linuxPath}")
    private String linuxPath;

    /**
     * 查询信息
     *
     * @param id ID
     * @return 信息
     */
    @Override
    public FdInvoiceCheckHistory selectFdInvoiceCheckHistoryById(Integer id) {
        return fdInvoiceCheckHistoryMapper.selectFdInvoiceCheckHistoryById(id);
    }

    /**
     * 查询列表
     *
     * @param fdInvoiceCheckHistory 信息
     * @return 集合
     */
    @Override
    public List<FdInvoiceCheckHistory> selectFdInvoiceCheckHistoryList(FdInvoiceCheckHistory fdInvoiceCheckHistory) {
        return fdInvoiceCheckHistoryMapper.selectFdInvoiceCheckHistoryList(fdInvoiceCheckHistory);
    }

    @Override
    public List<FdInvoiceCheckHistory> selectFdInvoiceCheckHistoryListByLikeTwo(FdInvoiceCheckHistory fdInvoiceCheckHistory) {
        return fdInvoiceCheckHistoryMapper.selectFdInvoiceCheckHistoryListByLikeTwo(fdInvoiceCheckHistory);
    }


    /**
     * 分页模糊查询列表
     *
     * @return 集合
     */
    @Override
    public Page selectFdInvoiceCheckHistoryListByLike(Query query) {
        if (StrUtil.isBlank(query.getOrderByField())) {
            query.setOrderByField("x.province_shift_no DESC, x.update_time DESC, x.container_number");
            query.setAsc(Boolean.TRUE);
        }
        FdInvoiceCheckHistory fdInvoiceCheckHistory = BeanUtil.mapToBean(query.getCondition(), FdInvoiceCheckHistory.class, false);
        List<FdInvoiceCheckHistory> fdInvoiceCheckHistories = fdInvoiceCheckHistoryMapper.selectFdInvoiceCheckHistoryListByLike(query, fdInvoiceCheckHistory);
        if(CollUtil.isNotEmpty(fdInvoiceCheckHistories)){
            setAmount(fdInvoiceCheckHistories);
        }
        query.setRecords(fdInvoiceCheckHistories);
        return query;
    }

    private void setAmount(List<FdInvoiceCheckHistory> fdInvoiceCheckHistories) {
        String provinceShiftNo = fdInvoiceCheckHistories.get(0).getProvinceShiftNo();
        List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectLocalAmountByProvinceShiftNo(provinceShiftNo);
        List<FdInvoiceApplicationDetail> fdInvoiceApplicationDetails = fdInvoiceApplicationDetailMapper.selectLocalAmountByProvinceShiftNo(provinceShiftNo);
        for (FdInvoiceCheckHistory check: fdInvoiceCheckHistories
        ) {
            check.setCityAmount(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
            check.setApplyAmount(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
            if(!provinceShiftNo.equals(check.getProvinceShiftNo())){
                provinceShiftNo = check.getProvinceShiftNo();
                detailList = fdBusCostDetailMapper.selectLocalAmountByProvinceShiftNo(provinceShiftNo);
                fdInvoiceApplicationDetails = fdInvoiceApplicationDetailMapper.selectLocalAmountByProvinceShiftNo(provinceShiftNo);
            }
            // 使用 Stream 查找匹配的 containerNumber
            if (CollUtil.isNotEmpty(detailList)) {
                detailList.stream()
                        .filter(detail -> check.getContainerNumber().equals(detail.getContainerNumber()))
                        .findFirst()
                        .ifPresent(detail -> check.setCityAmount(detail.getLocalAmount()));
            }
            if (CollUtil.isNotEmpty(fdInvoiceApplicationDetails)) {
                fdInvoiceApplicationDetails.stream()
                        .filter(detail -> check.getContainerNumber().equals(detail.getContainerNumber()))
                        .findFirst()
                        .ifPresent(detail -> check.setApplyAmount(detail.getLocalCurrencyAmount()));
            }
        }
    }

    /**
     * 新增
     *
     * @param fdInvoiceCheckHistory 信息
     * @return 结果
     */
    @Override
    public int insertFdInvoiceCheckHistory(FdInvoiceCheckHistory fdInvoiceCheckHistory) {
        return fdInvoiceCheckHistoryMapper.insertFdInvoiceCheckHistory(fdInvoiceCheckHistory);
    }

    /**
     * 修改
     *
     * @param fdInvoiceCheckHistory 信息
     * @return 结果
     */
    @Override
    public int updateFdInvoiceCheckHistory(FdInvoiceCheckHistory fdInvoiceCheckHistory) {
        return fdInvoiceCheckHistoryMapper.updateFdInvoiceCheckHistory(fdInvoiceCheckHistory);
    }


    /**
     * 删除
     *
     * @param id ID
     * @return 结果
     */
    public int deleteFdInvoiceCheckHistoryById(Integer id) {
        return fdInvoiceCheckHistoryMapper.deleteFdInvoiceCheckHistoryById(id);
    }

    ;


    /**
     * 批量删除对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFdInvoiceCheckHistoryByIds(Integer[] ids) {
        return fdInvoiceCheckHistoryMapper.deleteFdInvoiceCheckHistoryByIds(ids);
    }

    @Override
    public void historyExported(FdInvoiceCheckHistory fdInvoiceCheckHistory, HttpServletResponse response) throws Exception {
        List<FdInvoiceCheckHistory> list = fdInvoiceCheckHistoryMapper.selectFdInvoiceCheckHistoryListByLikeTwo(fdInvoiceCheckHistory);
        if(CollUtil.isNotEmpty(list)){
            setAmount(list);
        }
        String templateFileName = "historyExported.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)) {
            templateFileName = linuxPath + templateFileName;
        } else {
            templateFileName = windowsPath + templateFileName;
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 使用 UTF-8 编码处理文件名，确保中文文件名在浏览器中正确显示
        String encodedFileName = URLEncoder.encode("历史核对表.xlsx", "UTF-8");
        encodedFileName = encodedFileName.replaceAll("\\+", "%20"); // 处理空格问题
        response.setHeader("Content-Disposition", "attachment;filename=\"" + encodedFileName + "\"");
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");

        InputStream templateFile = new FileInputStream(templateFileName);
        XSSFWorkbook workbook = new XSSFWorkbook(templateFile);
        XSSFSheet sheet = workbook.getSheetAt(0);
        if (CollUtil.isNotEmpty(list)) {
            int rowIndex = 1;
            // 创建一个数字格式的 CellStyle
            CellStyle cellStyle = workbook.createCellStyle();
            DataFormat format = workbook.createDataFormat();
            // 保留两位小数
            cellStyle.setDataFormat(format.getFormat("0.00"));
            for (FdInvoiceCheckHistory history : list) {
                XSSFRow row = sheet.createRow(rowIndex);

                XSSFCell cell0 = row.createCell(0);
                cell0.setCellValue(rowIndex);

                XSSFCell cell1 = row.createCell(1);
                cell1.setCellValue(history.getProvinceShiftNo());

                XSSFCell cell2 = row.createCell(2);
                cell2.setCellValue(history.getContainerNumber());

                XSSFCell cell3 = row.createCell(3);
                cell3.setCellValue(history.getContainerType());

                XSSFCell cell4 = row.createCell(4);
                cell4.setCellValue(history.getPortStation());

                XSSFCell cell5 = row.createCell(5);
                cell5.setCellValue(history.getDestination());

                XSSFCell cell6 = row.createCell(6);
                cell6.setCellValue(history.getCityAmount() != null ? Double.parseDouble(String.format("%.2f", history.getCityAmount())) : 0.00);
                cell6.setCellStyle(cellStyle);

                XSSFCell cell7 = row.createCell(7);
                cell7.setCellValue(history.getDlAmount() != null ? Double.parseDouble(String.format("%.2f", history.getDlAmount())) : 0.00);
                cell7.setCellStyle(cellStyle);

                XSSFCell cell8 = row.createCell(8);
                cell8.setCellValue(history.getApplyAmount() != null ? Double.parseDouble(String.format("%.2f", history.getApplyAmount())) : 0.00);
                cell8.setCellStyle(cellStyle);

                rowIndex++;
            }
        }
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }

}
