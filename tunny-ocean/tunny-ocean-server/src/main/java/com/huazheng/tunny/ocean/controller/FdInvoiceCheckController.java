package com.huazheng.tunny.ocean.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.FdInvoiceCheck;
import com.huazheng.tunny.ocean.api.entity.FdInvoiceCheckHistory;
import com.huazheng.tunny.ocean.api.vo.InvoiceStatisticsDetailListVO;
import com.huazheng.tunny.ocean.api.vo.SelectInvoiceStatisticsCostListVO;
import com.huazheng.tunny.ocean.mapper.FdBusCostDetailMapper;
import com.huazheng.tunny.ocean.service.FdInvoiceCheckHistoryService;
import com.huazheng.tunny.ocean.service.FdInvoiceCheckService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> code generator
 * @date 2024-07-23 14:23:39
 */
@Slf4j
@RestController
@RequestMapping("/fdinvoicecheck")
public class FdInvoiceCheckController {

    @Autowired
    private FdInvoiceCheckService fdInvoiceCheckService;
    @Autowired
    private FdBusCostDetailMapper fdBusCostDetailMapper;
    @Autowired
    private FdInvoiceCheckHistoryService fdInvoiceCheckHistoryService;

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public List<FdInvoiceCheck> page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdInvoiceCheckService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdInvoiceCheckService.selectFdInvoiceCheckListNotPage(params);
    }

    /**
     * 历史核对列表
     *
     * @param params
     * @return
     */
    @GetMapping("/pageHistory")
    public Page pageHistory(@RequestParam Map<String, Object> params) {
        return fdInvoiceCheckService.selectFdInvoiceCheckHistoryListByLike(new Query<>(params));
    }

    /**
     * 信息
     *
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Integer id) {
        FdInvoiceCheck fdInvoiceCheck = fdInvoiceCheckService.selectById(id);
        return new R<>(fdInvoiceCheck);
    }

    /**
     * 保存
     *
     * @param fdInvoiceChecks
     * @return R
     */
    @PostMapping("/save")
    public R save(@RequestBody List<FdInvoiceCheck> fdInvoiceChecks) {
        fdInvoiceCheckService.insertInvoiceCheckBatch(fdInvoiceChecks);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     *
     * @param fdInvoiceCheck
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody FdInvoiceCheck fdInvoiceCheck) {
        fdInvoiceCheckService.updateById(fdInvoiceCheck);
        return new R<>(Boolean.TRUE);
    }


    /**
     * 删除
     *
     * @param id
     * @return R
     */
    @GetMapping("/del/{id}")
    public R delete(@PathVariable Integer id) {
        fdInvoiceCheckService.deleteById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody List<Integer> ids) {
        fdInvoiceCheckService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

    @PostMapping("/imported")
    public R imported(String titleStr, MultipartFile file) throws Exception {
        R r = new R();
        String fileName = file.getOriginalFilename();
        if (!fileName.matches("^.+\\.(?i)(xls)$") && !fileName.matches("^.+\\.(?i)(xlsx)$")) {
            r.setCode(500);
            r.setMsg("模板后缀名格式错误！");
            return r;
        }
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        List<FdInvoiceCheck> fdInvoiceChecks = new ArrayList<>();
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheetAt = workbook.getSheetAt(i);
            if (null == sheetAt) {
                break;
            }
            // 手动读取表格中的数据
            Row provinceShiftNoRow = sheetAt.getRow(2);
            if (null == provinceShiftNoRow) {
                r.setCode(500);
                r.setMsg("sheet页:" + sheetAt.getSheetName() + "不存在省级班列号！");
                break;
            }
            Cell provinceShiftNoCell = provinceShiftNoRow.getCell(0);
            String provinceShiftNo = "";
            if (provinceShiftNoCell != null && provinceShiftNoCell.getCellTypeEnum() == CellType.STRING) {
                provinceShiftNo = provinceShiftNoCell.getStringCellValue();
            } else {
                continue;
            }
            // 遍历Sheet页里的数据
            int rowInt = 3;
            while (true) {
                Row row = sheetAt.getRow(rowInt);

                Cell cell = row.getCell(0);
                if (cell == null) {
                    break;
                }

                Cell destinationCell = row.getCell(2);
                FdInvoiceCheck fdInvoiceCheck = new FdInvoiceCheck();
                fdInvoiceCheck.setProvinceShiftNo(provinceShiftNo);
                // 到站
                if (destinationCell != null && destinationCell.getCellTypeEnum() == CellType.STRING) {
                    fdInvoiceCheck.setDestination(destinationCell.getStringCellValue());
                } else {
                    break;
                }

                // 口岸
                Cell portStationCell = row.getCell(3);
                if (portStationCell != null && portStationCell.getCellTypeEnum() == CellType.STRING) {
                    fdInvoiceCheck.setPortStation(portStationCell.getStringCellValue());
                }

                // 箱号
                Cell containerNumberCell = row.getCell(5);
                if (containerNumberCell != null && containerNumberCell.getCellTypeEnum() == CellType.STRING) {
                    fdInvoiceCheck.setContainerNumber(containerNumberCell.getStringCellValue());
                }

                // 箱型
                Cell containerTypeCell = row.getCell(6);
                /*if(containerTypeCell != null && containerTypeCell.getCellTypeEnum() == CellType.NUMERIC){
                    fdInvoiceCheck.setContainerType(String.valueOf(containerTypeCell.getNumericCellValue()));
                }else if(containerTypeCell != null && containerTypeCell.getCellTypeEnum() == CellType.STRING){
                    fdInvoiceCheck.setContainerType(containerTypeCell.getStringCellValue());
                }*/
                containerTypeCell.setCellType(CellType.STRING);
                fdInvoiceCheck.setContainerType(containerTypeCell.getStringCellValue());

                // 境内价格
                Cell churchyardAmountCell = row.getCell(8);
                if (churchyardAmountCell != null && churchyardAmountCell.getCellTypeEnum() == CellType.NUMERIC) {
                    fdInvoiceCheck.setChurchyardAmount(new BigDecimal(String.valueOf(churchyardAmountCell.getNumericCellValue())));
                } else if (churchyardAmountCell != null && churchyardAmountCell.getCellTypeEnum() == CellType.STRING) {
                    fdInvoiceCheck.setChurchyardAmount(new BigDecimal(churchyardAmountCell.getStringCellValue()));
                }

                // 境外价格
                Cell abroadAmountCell = row.getCell(9);
                if (abroadAmountCell != null && abroadAmountCell.getCellTypeEnum() == CellType.NUMERIC) {
                    fdInvoiceCheck.setAbroadAmount(new BigDecimal(String.valueOf(abroadAmountCell.getNumericCellValue())));
                } else if (abroadAmountCell != null && abroadAmountCell.getCellTypeEnum() == CellType.STRING) {
                    fdInvoiceCheck.setAbroadAmount(new BigDecimal(abroadAmountCell.getStringCellValue()));
                }

                fdInvoiceCheck.setCreateBy(userInfo.getUserName());
                fdInvoiceCheck.setCreateTime(new Date());
                fdInvoiceCheck.setPlatformLevel(userInfo.getPlatformLevel());
                fdInvoiceCheck.setPlatformCode(userInfo.getPlatformCode());

                fdInvoiceChecks.add(fdInvoiceCheck);
                rowInt++;
            }
        }
        fdInvoiceCheckService.selectCheckByList(fdInvoiceChecks, userInfo);
        return r;
    }

    /**
     * 导出EXCEL
     *
     * @return
     */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {

        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<FdInvoiceCheck> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<FdInvoiceCheck> list = fdInvoiceCheckService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), FdInvoiceCheck.class).includeColumnFiledNames(keyList).sheet(1).doWrite(list);
    }

    /**
     * 历史核对表数据导出EXCEL
     *
     * @return
     */
    @PostMapping("/historyExported")
    public void historyExported(@RequestBody FdInvoiceCheckHistory fdInvoiceCheckHistory, HttpServletResponse response) throws Exception {
        fdInvoiceCheckHistoryService.historyExported(fdInvoiceCheckHistory, response);
    }

    /**
     * 发票统计接口
     *
     * @param params
     * @return
     */
    @GetMapping("/selectInvoiceStatistics")
    public Page selectInvoiceStatistics(@RequestParam Map<String, Object> params) {
        return fdInvoiceCheckService.selectInvoiceStatistics(new Query<>(params));
    }

    /**
     * 统计详情接口
     *
     * @param platformCode
     * @param planShipTime
     * @param shippingLine
     * @param trip
     * @param provinceShiftNo
     * @return
     */
    @GetMapping("/selectInvoiceStatisticsDetail")
    public List<InvoiceStatisticsDetailListVO> selectInvoiceStatisticsDetail(@RequestParam("platformCode") String platformCode, @RequestParam("planShipTime") String planShipTime, @RequestParam("shippingLine") String shippingLine, @RequestParam("trip") String trip, @RequestParam("provinceShiftNo") String provinceShiftNo) {
        return fdInvoiceCheckService.selectInvoiceStatisticsDetail(platformCode, planShipTime, shippingLine, trip, provinceShiftNo);
    }

    /**
     * 根据班次号获取已开票或者未开票数据
     *
     * @param shiftNo
     * @return
     */
    @GetMapping("/selectInvoiceStatisticsCostListVO")
    public List<SelectInvoiceStatisticsCostListVO> selectInvoiceStatisticsCostListVO(@RequestParam("shiftNo") String shiftNo, @RequestParam("isInvoice") Integer isInvoice) {
        return fdInvoiceCheckService.selectInvoiceStatisticsCostListVO(shiftNo, isInvoice);
    }


    /**
     * 导入成本台账
     *
     * @param file  导入的文件
     * @return R
     * <AUTHOR>
     * @since 2025/5/20 09:18
     **/
    @PostMapping("/importCostLedgerExcel")
    public R importCostLedgerExcel(MultipartFile file) throws IOException {
        return fdInvoiceCheckService.importCostLedgerExcel(file);
    }




    /**
     * 导入中亚台账
     *
     * @param file  导入的文件
     * @return R
     * <AUTHOR>
     * @since 2025/5/20 09:18
     **/
    @PostMapping("/importCentralAsiaLedgerExcel")
    public R importCentralAsiaLedgerExcel(MultipartFile file) throws Exception {
        return fdInvoiceCheckService.importCentralAsiaLedgerExcel(file);
    }
}
