package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.BasChangeboxContainerInfo;
import com.huazheng.tunny.ocean.api.entity.BasChangeboxCostDetail;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 换箱费用表 服务接口层
 *
 * <AUTHOR>
 * @date 2024-06-18 13:15:26
 */
public interface BasChangeboxCostDetailService extends IService<BasChangeboxCostDetail> {
    /**
     * 查询换箱费用表信息
     *
     * @param id 换箱费用表ID
     * @return 换箱费用表信息
     */
    public BasChangeboxCostDetail selectBasChangeboxCostDetailById(Integer id);

    /**
     * 查询换箱费用表列表
     *
     * @param basChangeboxCostDetail 换箱费用表信息
     * @return 换箱费用表集合
     */
    public List<BasChangeboxCostDetail> selectBasChangeboxCostDetailList(BasChangeboxCostDetail basChangeboxCostDetail);


    /**
     * 分页模糊查询换箱费用表列表
     * @return 换箱费用表集合
     */
    public Page selectBasChangeboxCostDetailListByLike(Query query);



    /**
     * 新增换箱费用表
     *
     * @param basChangeboxCostDetail 换箱费用表信息
     * @return 结果
     */
    public int insertBasChangeboxCostDetail(BasChangeboxCostDetail basChangeboxCostDetail);

    public void saveCost(BasChangeboxContainerInfo basChangeboxContainerInfo);

    /**
     * 修改换箱费用表
     *
     * @param basChangeboxCostDetail 换箱费用表信息
     * @return 结果
     */
    public int updateBasChangeboxCostDetail(BasChangeboxCostDetail basChangeboxCostDetail);

    /**
     * 删除换箱费用表
     *
     * @param id 换箱费用表ID
     * @return 结果
     */
    public int deleteBasChangeboxCostDetailById(Integer id);

    /**
     * 批量删除换箱费用表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteBasChangeboxCostDetailByIds(Integer[] ids);

}

