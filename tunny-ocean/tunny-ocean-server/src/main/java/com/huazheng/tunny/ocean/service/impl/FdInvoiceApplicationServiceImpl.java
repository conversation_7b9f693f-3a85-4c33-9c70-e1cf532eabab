package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.FdInvoiceApplicationDetailDTO;
import com.huazheng.tunny.ocean.api.dto.FdInvoiceApplicationDetailTwoDTO;
import com.huazheng.tunny.ocean.api.dto.FdShippingDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.enums.InvoiceCheckEnum;
import com.huazheng.tunny.ocean.api.enums.TripEnum;
import com.huazheng.tunny.ocean.api.vo.FdInvoiceApplicationVO;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.*;
import com.huazheng.tunny.ocean.util.PermissionUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service("fdInvoiceApplicationService")
public class FdInvoiceApplicationServiceImpl extends ServiceImpl<FdInvoiceApplicationMapper, FdInvoiceApplication> implements FdInvoiceApplicationService {

    @Autowired
    private FdInvoiceApplicationMapper fdInvoiceApplicationMapper;
    @Autowired
    private FdInvoiceApplicationDetailService fdInvoiceApplicationDetailService;
    @Autowired
    private SysNoConfigService noConfigService;
    @Autowired
    private PermissionUtil permissionUtil;
    @Autowired
    private FdCosdetailService fdCosdetailService;
    @Autowired
    private CustomerInfoService customerInfoService;
    @Autowired
    private FdInvoiceApplicationDetailMapper fdInvoiceApplicationDetailMapper;
    @Autowired
    private FdShippingAccountMapper fdShippingAccountMapper;
    @Autowired
    private FdShippingAccoundetailMapper fdShippingAccoundetailMapper;
    @Autowired
    private FdBillSubDetailMapper fdBillSubDetailMapper;
    @Autowired
    private FdBusCostDetailMapper fdBusCostDetailMapper;
    @Autowired
    private FdInvoiceCheckHistoryMapper fdInvoiceCheckHistoryMapper;
    @Autowired
    private ShifmanagementMapper shifmanagementMapper;
    @Value("${path.windowsPath}")
    private String windowsPath;
    @Value("${path.linuxPath}")
    private String linuxPath;

    /**
     * 查询开票申请主表信息
     *
     * @param id 开票申请主表ID
     * @return 开票申请主表信息
     */
    @Override
    public FdInvoiceApplication selectFdInvoiceApplicationById(Long id) {
        FdInvoiceApplication fdInvoiceApplication = fdInvoiceApplicationMapper.selectFdInvoiceApplicationById(id);
        if (fdInvoiceApplication != null && StrUtil.isNotEmpty(fdInvoiceApplication.getInvoiceApplicationCode())) {
            FdInvoiceApplicationDetail sel = new FdInvoiceApplicationDetail();
            sel.setInvoiceApplicationCode(fdInvoiceApplication.getInvoiceApplicationCode());
            List<FdInvoiceApplicationDetail> fdInvoiceApplicationDetails = fdInvoiceApplicationDetailMapper.selectFdInvoiceApplicationDetailList(sel);
            fdInvoiceApplication.setList(fdInvoiceApplicationDetails);
        }

        return fdInvoiceApplication;
    }

    @Override
    public FdInvoiceApplication infoProvince(FdInvoiceApplication invoiceApplication) {
        FdInvoiceApplication fdInvoiceApplication = fdInvoiceApplicationMapper.selectFdInvoiceApplicationById(invoiceApplication.getId());
        if (fdInvoiceApplication != null && StrUtil.isNotEmpty(fdInvoiceApplication.getInvoiceApplicationCode())) {
            List<FdInvoiceApplicationDetailDTO> fdInvoiceApplicationDetails = infoProvinceOne(fdInvoiceApplication.getInvoiceApplicationCode());
            fdInvoiceApplication.setDetailDtoList(fdInvoiceApplicationDetails);
        }
        return fdInvoiceApplication;
    }

    private List<FdInvoiceApplicationDetailDTO> infoProvinceOne(String invoiceApplicationCode) {
        List<FdInvoiceApplicationDetailDTO> fdInvoiceApplicationDetails = fdInvoiceApplicationDetailMapper.selectListOne(invoiceApplicationCode);
        if (CollUtil.isNotEmpty(fdInvoiceApplicationDetails)) {
            for (FdInvoiceApplicationDetailDTO dto : fdInvoiceApplicationDetails) {
                if (dto != null && StrUtil.isNotBlank(dto.getShiftNo())) {
                    BigDecimal cityAmount = fdBusCostDetailMapper.selectSumLocalAmountByShiftNo(dto.getShiftNo());
                    dto.setCityAmount(cityAmount);
                    BigDecimal kpAmount = fdInvoiceApplicationDetailMapper.selectSumLocalAmountOne(invoiceApplicationCode, dto.getShiftNo());
                    dto.setKpAmount(kpAmount);
                }
            }
        }
        return fdInvoiceApplicationDetails;
    }

    @Override
    public List<FdInvoiceApplicationDetailTwoDTO> infoProvinceTwo(FdInvoiceApplicationDetailTwoDTO detail) {
        List<FdInvoiceApplicationDetailTwoDTO> fdInvoiceApplicationDetails = fdInvoiceApplicationDetailMapper.selectListTwo(detail);
        if (CollUtil.isNotEmpty(fdInvoiceApplicationDetails)) {
            List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectSumLocalAmountByShiftNoTwo(detail.getInvoiceApplicationCode(), detail.getShiftNo());
            List<FdInvoiceApplicationDetail> applicationDetails = fdInvoiceApplicationDetailMapper.selectSumLocalAmountTwo(detail.getInvoiceApplicationCode(), detail.getShiftNo());
            for (FdInvoiceApplicationDetailTwoDTO dto : fdInvoiceApplicationDetails) {
                dto.setCityAmount(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                dto.setKpAmount(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                if (CollUtil.isNotEmpty(detailList)) {
                    for (FdBusCostDetail fdBusCostDetail : detailList) {
                        if (fdBusCostDetail != null && StrUtil.isNotBlank(fdBusCostDetail.getContainerNumber()) && dto.getContainerNumber().equals(fdBusCostDetail.getContainerNumber())) {
                            dto.setCityAmount(fdBusCostDetail.getLocalAmount());
                            break;
                        }
                    }
                }
                if (CollUtil.isNotEmpty(applicationDetails)) {
                    for (FdInvoiceApplicationDetail invoiceApplicationDetail : applicationDetails) {
                        if (dto.getContainerNumber().equals(invoiceApplicationDetail.getContainerNumber()) && dto.getShiftNo().equals(invoiceApplicationDetail.getShiftNo())) {
                            dto.setKpAmount(invoiceApplicationDetail.getLocalCurrencyAmount());
                            break;
                        }
                    }
                }
            }
        }
        return fdInvoiceApplicationDetails;
    }

    @Override
    public List<FdInvoiceApplicationDetail> infoProvinceThree(FdInvoiceApplicationDetailTwoDTO detail) {
        return fdInvoiceApplicationDetailMapper.infoProvinceThree(detail.getInvoiceApplicationCode(), detail.getContainerNumber());
    }

    @Override
    public void infoProvinceExport(FdInvoiceApplicationDetailTwoDTO detail, HttpServletResponse response) throws Exception {
        String templateFileName = "infoProvinceExport.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)) {
            templateFileName = linuxPath + templateFileName;
        } else {
            templateFileName = windowsPath + templateFileName;
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 使用 UTF-8 编码处理文件名，确保中文文件名在浏览器中正确显示
        String encodedFileName = URLEncoder.encode("开票确认明细.xlsx", "UTF-8");
        encodedFileName = encodedFileName.replaceAll("\\+", "%20"); // 处理空格问题
        response.setHeader("Content-Disposition", "attachment;filename=\"" + encodedFileName + "\"");
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");

        InputStream templateFile = new FileInputStream(templateFileName);
        XSSFWorkbook workbook = new XSSFWorkbook(templateFile);
        // 创建一个数字格式的 CellStyle
        CellStyle cellStyle = workbook.createCellStyle();
        DataFormat format = workbook.createDataFormat();
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        // 保留两位小数
        cellStyle.setDataFormat(format.getFormat("0.00"));
        //班列维度
        setOneList(detail, workbook, cellStyle, dft);
        //箱维度
        setTwoList(detail, workbook, cellStyle);
        //费用科目维度
        setThreeList(detail, workbook, cellStyle);

        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }

    private void setOneList(FdInvoiceApplicationDetailTwoDTO detail, XSSFWorkbook workbook, CellStyle cellStyle, SimpleDateFormat dft) {
        List<FdInvoiceApplicationDetailDTO> oneList = infoProvinceOne(detail.getInvoiceApplicationCode());
        if (CollUtil.isNotEmpty(oneList)) {
            XSSFSheet sheet = workbook.getSheetAt(0);
            int rowIndex = 1;
            for (FdInvoiceApplicationDetailDTO dto : oneList) {
                XSSFRow row = sheet.createRow(rowIndex);

                XSSFCell cell0 = row.createCell(0);
                cell0.setCellValue(rowIndex);

                XSSFCell cell1 = row.createCell(1);
                cell1.setCellValue(dto.getProvinceShiftNo());

                XSSFCell cell2 = row.createCell(2);
                cell2.setCellValue(dto.getShiftName());

                XSSFCell cell3 = row.createCell(3);
                cell3.setCellValue(dft.format(dto.getPlanShipTime()));

                XSSFCell cell4 = row.createCell(4);
                cell4.setCellValue(dto.getShippingLine());

                XSSFCell cell5 = row.createCell(5);
                cell5.setCellValue(TripEnum.fromKey(dto.getTrip()));

                XSSFCell cell6 = row.createCell(6);
                cell6.setCellValue(dto.getCityAmount() != null ? Double.parseDouble(String.format("%.2f", dto.getCityAmount())) : 0.00);
                cell6.setCellStyle(cellStyle);

                XSSFCell cell7 = row.createCell(7);
                cell7.setCellValue(dto.getDlAmount() != null ? Double.parseDouble(String.format("%.2f", dto.getDlAmount())) : 0.00);
                cell7.setCellStyle(cellStyle);

                XSSFCell cell8 = row.createCell(8);
                cell8.setCellValue(dto.getKpAmount() != null ? Double.parseDouble(String.format("%.2f", dto.getKpAmount())) : 0.00);
                cell8.setCellStyle(cellStyle);

                XSSFCell cell9 = row.createCell(9);
                cell9.setCellValue(dto.getApplyAmount() != null ? Double.parseDouble(String.format("%.2f", dto.getApplyAmount())) : 0.00);
                cell9.setCellStyle(cellStyle);

                rowIndex++;
            }
        }
    }

    private void setTwoList(FdInvoiceApplicationDetailTwoDTO detail, XSSFWorkbook workbook, CellStyle cellStyle) {
        List<FdInvoiceApplicationDetailTwoDTO> twoList = infoProvinceTwo(detail);
        if (CollUtil.isNotEmpty(twoList)) {
            XSSFSheet sheet = workbook.getSheetAt(1);
            int rowIndex = 1;
            for (FdInvoiceApplicationDetailTwoDTO dto : twoList) {
                XSSFRow row = sheet.createRow(rowIndex);

                XSSFCell cell0 = row.createCell(0);
                cell0.setCellValue(rowIndex);

                XSSFCell cell1 = row.createCell(1);
                cell1.setCellValue(dto.getCustomerName());

                XSSFCell cell2 = row.createCell(2);
                cell2.setCellValue(dto.getContainerNumber());

                XSSFCell cell3 = row.createCell(3);
                cell3.setCellValue(dto.getContainerTypeCode());

                XSSFCell cell4 = row.createCell(4);
                cell4.setCellValue(dto.getCityAmount() != null ? Double.parseDouble(String.format("%.2f", dto.getCityAmount())) : 0.00);
                cell4.setCellStyle(cellStyle);

                XSSFCell cell5 = row.createCell(5);
                cell5.setCellValue(dto.getDlAmount() != null ? Double.parseDouble(String.format("%.2f", dto.getDlAmount())) : 0.00);
                cell5.setCellStyle(cellStyle);

                XSSFCell cell6 = row.createCell(6);
                cell6.setCellValue(dto.getKpAmount() != null ? Double.parseDouble(String.format("%.2f", dto.getKpAmount())) : 0.00);
                cell6.setCellStyle(cellStyle);

                XSSFCell cell7 = row.createCell(7);
                cell7.setCellValue(dto.getApplyAmount() != null ? Double.parseDouble(String.format("%.2f", dto.getApplyAmount())) : 0.00);
                cell7.setCellStyle(cellStyle);

                XSSFCell cell8 = row.createCell(8);
                cell8.setCellValue(dto.getGoodsName());

                XSSFCell cell9 = row.createCell(9);
                cell9.setCellValue(dto.getIdentificationName());

                XSSFCell cell10 = row.createCell(10);
                cell10.setCellValue(dto.getConsignorName());

                XSSFCell cell11 = row.createCell(11);
                cell11.setCellValue(dto.getDestinationName());

                XSSFCell cell12 = row.createCell(12);
                cell12.setCellValue(dto.getDestination());

                XSSFCell cell13 = row.createCell(13);
                cell13.setCellValue(dto.getProvinceShiftNo());

                rowIndex++;
            }
        }
    }

    private void setThreeList(FdInvoiceApplicationDetailTwoDTO detail, XSSFWorkbook workbook, CellStyle cellStyle) {
        List<FdInvoiceApplicationDetail> threeList = infoProvinceThree(detail);
        if (CollUtil.isNotEmpty(threeList)) {
            XSSFSheet sheet = workbook.getSheetAt(2);
            int rowIndex = 1;
            for (FdInvoiceApplicationDetail dto : threeList) {
                XSSFRow row = sheet.createRow(rowIndex);

                XSSFCell cell0 = row.createCell(0);
                cell0.setCellValue(rowIndex);

                XSSFCell cell1 = row.createCell(1);
                cell1.setCellValue(dto.getContainerNumber());

                XSSFCell cell2 = row.createCell(2);
                cell2.setCellValue(dto.getLocalCurrencyAmount() != null ? Double.parseDouble(String.format("%.2f", dto.getLocalCurrencyAmount())) : 0.00);
                cell2.setCellStyle(cellStyle);

                XSSFCell cell3 = row.createCell(3);
                cell3.setCellValue(dto.getCodeBbCategoriesName());

                XSSFCell cell4 = row.createCell(4);
                cell4.setCellValue(dto.getCodeSsCategoriesName());

                rowIndex++;
            }
        }
    }

    /**
     * 查询开票申请主表列表
     *
     * @param fdInvoiceApplication 开票申请主表信息
     * @return 开票申请主表集合
     */
    @Override
    public List<FdInvoiceApplication> selectFdInvoiceApplicationList(FdInvoiceApplication fdInvoiceApplication) {
        return fdInvoiceApplicationMapper.selectFdInvoiceApplicationList(fdInvoiceApplication);
    }


    /**
     * 分页模糊查询开票申请主表列表
     *
     * @return 开票申请主表集合
     */
    @Override
    public Page selectFdInvoiceApplicationListByLike(Query query) {
        if (StrUtil.isBlank(query.getOrderByField())) {
            query.setOrderByField("x.status,x.add_time");
            query.setAsc(Boolean.FALSE);
        }
        FdInvoiceApplication fdInvoiceApplication = BeanUtil.mapToBean(query.getCondition(), FdInvoiceApplication.class, false);
        /*if (StrUtil.isNotEmpty(fdInvoiceApplication.getPlatformCode())) {
            fdInvoiceApplication.setCompanyCode(permissionUtil.getPcPermissonCustomer(fdInvoiceApplication.getPlatformCode(), fdInvoiceApplication.getPlatformFlag()));
            *//*fdInvoiceApplication.setPlatformName(null);
            fdInvoiceApplication.setPlatformFlag(null);*//*
        }else if(StrUtil.isEmpty(fdInvoiceApplication.getPlatformCode())){
            fdInvoiceApplication.setPlatformCode(SecurityUtils.getUserInfo().getSupPlatformCode());
        }*/
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        if ("2".equals(userInfo.getPlatformLevel()) && StringUtils.isEmpty(fdInvoiceApplication.getStatus())) {
            fdInvoiceApplication.setStatus("1,2,3");
        }
        fdInvoiceApplication.setDeleteFlag("N");
        List<FdInvoiceApplication> fdInvoiceApplications = fdInvoiceApplicationMapper.selectFdInvoiceApplicationListByLike(query, fdInvoiceApplication);
        /*if(CollUtil.isNotEmpty(fdInvoiceApplications)){
            List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoList(null);
            if(CollUtil.isNotEmpty(customerInfos)){
                for (FdInvoiceApplication fd:fdInvoiceApplications
                     ) {
                    for (CustomerInfo cus:customerInfos
                    ) {
                        if(StrUtil.isNotBlank(fd.getConfirmUser())&&fd.getConfirmUser().equals(cus.getCustomerCode())){
                            fd.setConfirmUser(cus.getCompanyName());
                            break;
                        }
                    }
                }
            }
        }*/
        query.setRecords(fdInvoiceApplications);
        return query;
    }

    @Override
    public void fdinvoiceapplicationExport(FdInvoiceApplication fdInvoiceApplication, HttpServletResponse response) throws Exception {
        FdInvoiceApplicationDetail sel = new FdInvoiceApplicationDetail();
        sel.setInvoiceApplicationCode(fdInvoiceApplication.getInvoiceApplicationCode());
        List<FdInvoiceApplicationDetail> fdInvoiceApplicationDetails = fdInvoiceApplicationDetailMapper.selectFdInvoiceApplicationDetailList(sel);
        String templateFileName = "fdinvoiceapplicationExport.xlsx";

        String osName = System.getProperties().getProperty("os.name");

        if ("Linux".equals(osName)) {
            templateFileName = linuxPath + templateFileName;
        } else {
            templateFileName = windowsPath + templateFileName;
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 使用 UTF-8 编码处理文件名，确保中文文件名在浏览器中正确显示
        String encodedFileName = URLEncoder.encode("开票明细.xlsx", "UTF-8");
        encodedFileName = encodedFileName.replaceAll("\\+", "%20"); // 处理空格问题
        response.setHeader("Content-Disposition", "attachment;filename=\"" + encodedFileName + "\"");
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");

        InputStream templateFile = new FileInputStream(templateFileName);
        XSSFWorkbook workbook = new XSSFWorkbook(templateFile);
        XSSFSheet sheet = workbook.getSheetAt(0);
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (CollUtil.isNotEmpty(fdInvoiceApplicationDetails)) {
            // 创建一个数字格式的 CellStyle
            CellStyle cellStyle = workbook.createCellStyle();
            DataFormat format = workbook.createDataFormat();
            // 保留两位小数
            cellStyle.setDataFormat(format.getFormat("0.00"));
            int rowIndex = 1;
            for (FdInvoiceApplicationDetail detail : fdInvoiceApplicationDetails) {
                XSSFRow row = sheet.createRow(rowIndex);

                XSSFCell cell0 = row.createCell(0);
                cell0.setCellValue(rowIndex);

                XSSFCell cell1 = row.createCell(1);
                cell1.setCellValue(detail.getProvinceShiftNo());

                XSSFCell cell2 = row.createCell(2);
                cell2.setCellValue(detail.getContainerNumber());

                XSSFCell cell3 = row.createCell(3);
                cell3.setCellValue(detail.getContainerTypeCode());

                XSSFCell cell4 = row.createCell(4);
                cell4.setCellValue(detail.getGoodsName());

                XSSFCell cell5 = row.createCell(5);
                cell5.setCellValue(detail.getOriginalCurrencyAmount() != null ? Double.parseDouble(String.format("%.2f", detail.getOriginalCurrencyAmount())) : 0.00);
                cell5.setCellStyle(cellStyle);

                XSSFCell cell6 = row.createCell(6);
                cell6.setCellValue(detail.getLocalCurrencyAmount() != null ? Double.parseDouble(String.format("%.2f", detail.getLocalCurrencyAmount())) : 0.00);
                cell6.setCellStyle(cellStyle);

                XSSFCell cell7 = row.createCell(7);
                cell7.setCellValue(detail.getCodeBbCategoriesName());

                XSSFCell cell8 = row.createCell(8);
                cell8.setCellValue(detail.getCodeSsCategoriesName());

                XSSFCell cell9 = row.createCell(9);
                cell9.setCellValue(detail.getDestinationName());

                XSSFCell cell10 = row.createCell(10);
                cell10.setCellValue(detail.getDestination());

                XSSFCell cell11 = row.createCell(11);
                cell11.setCellValue(detail.getIdentificationName());

                XSSFCell cell12 = row.createCell(12);
                cell12.setCellValue(detail.getConsignorName());

                rowIndex++;
            }
        }
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();

    }

    /**
     * 分页模糊查询开票申请主表列表
     *
     * @return 开票申请主表集合
     */
    @Override
    public Page fiPage(Query query) {
        FdInvoiceApplication fdInvoiceApplication = BeanUtil.mapToBean(query.getCondition(), FdInvoiceApplication.class, false);
        if (StrUtil.isNotEmpty(fdInvoiceApplication.getPlatformCode())) {
            fdInvoiceApplication.setPlatformCode(permissionUtil.getPcPermissonCustomer(fdInvoiceApplication.getPlatformCode(), fdInvoiceApplication.getPlatformFlag()));
        }
        query.setRecords(fdInvoiceApplicationMapper.fiPage(query, fdInvoiceApplication));
        return query;
    }

    @Override
    public Integer selectFdInvoiceApplicationListByLikeCount(FdInvoiceApplication fdInvoiceApplication) {
        if (StrUtil.isNotEmpty(fdInvoiceApplication.getPlatformCode())) {
            fdInvoiceApplication.setPlatformCode(permissionUtil.getPcPermissonCustomer(fdInvoiceApplication.getPlatformCode(), fdInvoiceApplication.getPlatformFlag()));
            /*fdInvoiceApplication.setPlatformName(null);
            fdInvoiceApplication.setPlatformFlag(null);*/
        }
        return fdInvoiceApplicationMapper.selectFdInvoiceApplicationListByLikeCount(fdInvoiceApplication);
    }

    @Override
    public Page selectInvoiceStatisticsPage(Query query) {
//        FdInvoiceApplication fdInvoiceApplication = BeanUtil.mapToBean(query.getCondition(), FdInvoiceApplication.class, false);
//        if (StrUtil.isNotEmpty(fdInvoiceApplication.getPlatformCode())) {
//            fdInvoiceApplication.setPlatformCode(permissionUtil.getPCPermissonCustomer(fdInvoiceApplication.getPlatformCode(), fdInvoiceApplication.getPlatformFlag()));
//        }
//        List<FdInvoiceApplication> fdInvoiceApplications = fdInvoiceApplicationMapper.selectGroupCustomerNo(query, fdInvoiceApplication);
        CustomerInfo customerInfo = BeanUtil.mapToBean(query.getCondition(), CustomerInfo.class, false);
        Page page = customerInfoService.selectCustomerInfoPage(query);
        List<CustomerInfo> records = page.getRecords();
        for (CustomerInfo customer : records) {
            FdInvoiceApplication fdInvoice = new FdInvoiceApplication();
            //添加日期
            if (StrUtil.isNotEmpty(customer.getAddTimeStr())) {
                fdInvoice.setAddTimeStr(customer.getAddTimeStr());
            }
            if (StrUtil.isNotEmpty(customer.getStandbyC())) {
                fdInvoice.setStandbyC(customer.getStandbyC());
            }
            fdInvoice.setCustomerNo(customer.getCustomerCode());
            //查询已开票的境内境外的总和
            fdInvoice.setStatus("2");
            FdInvoiceApplication fdInvoiceApplicationykp = fdInvoiceApplicationMapper.selectSumJnAndJw(fdInvoice);
            if (fdInvoiceApplicationykp != null) {
                customer.setJnykp(fdInvoiceApplicationykp.getJnykp());
                customer.setJwykp(fdInvoiceApplicationykp.getJwykp());
            } else {
                customer.setJnykp(BigDecimal.valueOf(0));
                customer.setJwykp(BigDecimal.valueOf(0));
            }
            //查询未开票的境内境外的总和
            fdInvoice.setStatus("1");
            FdInvoiceApplication fdInvoiceApplicationwkp = fdInvoiceApplicationMapper.selectSumJnAndJw(fdInvoice);
            if (fdInvoiceApplicationwkp != null) {
                customer.setJnwkp(fdInvoiceApplicationwkp.getJnwkp());
                customer.setJwwkp(fdInvoiceApplicationwkp.getJwwkp());
            } else {
                customer.setJnwkp(BigDecimal.valueOf(0));
                customer.setJwwkp(BigDecimal.valueOf(0));
            }
        }
//        query.setRecords(fdInvoiceApplications);
//        query.setTotal(fdInvoiceApplicationMapper.selectGroupCustomerNoCount(fdInvoiceApplication));
        return page;
    }

    /**
     * 根据用户code查询发票信息
     *
     * @param fdInvoiceApplication
     * @return
     */
    @Override
    public FdInvoiceApplicationVO selectInvoiceInfoByCustomerCode(FdInvoiceApplication fdInvoiceApplication) {
        if (StrUtil.isNotEmpty(fdInvoiceApplication.getPlatformCode())) {
            fdInvoiceApplication.setPlatformCode(permissionUtil.getPcPermisson(fdInvoiceApplication.getPlatformCode(), fdInvoiceApplication.getPlatformFlag()));
        }
//        fdInvoiceApplication.setPlatformCode(null);
        FdInvoiceApplicationVO vo = fdInvoiceApplicationMapper.selectInvoiceInfoByCustomerCode(fdInvoiceApplication);
        /*if (StrUtil.isNotEmpty(vo.getInvoiceApplicationCode())){
            EntityWrapper<FdInvoiceApplicationDetail> wrapper = new EntityWrapper<>();
            wrapper.eq("invoice_application_code", vo.getInvoiceApplicationCode());
            wrapper.eq("del_flag", "0");
            vo.setDetailList(fdInvoiceApplicationDetailService.selectList(wrapper));
        }*/
        //若为根据用户编码查询，设置申请号等信息为null
        if (vo != null) {
            if (StrUtil.isNotEmpty(fdInvoiceApplication.getCustomerNo())) {
                vo.setInvoiceApplicationCode(null);
                vo.setInvoiceType(null);
                vo.setInvoiceFlag(null);
            }
            //拆分文件名和文件地址
            if (StrUtil.isNotEmpty(vo.getAttachment())) {
                String[] attachment = vo.getAttachment().split(",");
                String[] attachmentName = vo.getAttachmentName().split(",");
                ArrayList<Map<String, String>> fileList = new ArrayList<>();
                for (int i = 0; i < attachment.length; i++) {
                    Map<String, String> map = new HashMap<>();
                    map.put("name", attachmentName[i]);
                    map.put("url", attachment[i]);
                    fileList.add(map);
                }
                vo.setFileList(fileList);
            }
        }
        return vo;
    }

    /**
     * 新增开票申请
     *
     * @param vo 开票申请信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R insertFdInvoiceApplication(FdInvoiceApplicationVO vo) throws Exception {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String tsIn = noConfigService.genNo("INV");
        if (tsIn.contains("请联系系统管理员")) {
            throw new Exception(tsIn);
        }
        vo.setInvoiceApplicationCode(tsIn);
        vo.setAddWho(userInfo.getUserName());
        vo.setCustomerNo(vo.getCustomerCode());
        vo.setCustomerName(vo.getCompanyName());
        vo.setAddWhoName(userInfo.getRealName());
        vo.setAddTime(LocalDateTime.now());
        List<FdInvoiceApplicationDetail> detailList = vo.getDetailList();
        BigDecimal sum = BigDecimal.valueOf(0);
        for (FdInvoiceApplicationDetail detail : detailList) {
            sum = sum.add(detail.getLocalCurrencyAmount());
            detail.setInvoiceApplicationCode(tsIn);
            detail.setDelFlag("N");
            detail.setUpdateUsercode(userInfo.getUserName());
            detail.setUpdateUserrealname(userInfo.getRealName());
            detail.setUpdateTime(LocalDateTime.now());
            fdInvoiceApplicationDetailService.insert(detail);
            // 如果有回程 修改发票核对表信息
            if ("R".equals(detail.getTrip())) {
                fdInvoiceApplicationMapper.updateInvoiceCheckByProvinceShiftNoAndContainerNumber(detail.getProvinceShiftNo(), detail.getContainerNumber(), "Y", tsIn, InvoiceCheckEnum.ALREADY_APPLY_CHECK.getKey());
            }
        }
        if (CollUtil.isNotEmpty(detailList)) {
            List<Integer> costIds = detailList.stream().map(FdInvoiceApplicationDetail::getCostId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(costIds)) {
                // 修改费用状态
                fdInvoiceApplicationMapper.updateCostIsInvoiceByBillInfo(costIds, 1, tsIn);
            }
        }
        vo.setInvoiceAmount(sum);
        fdInvoiceApplicationMapper.insert(vo);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 新增开票申请
     *
     * @param vo 开票申请信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveInfo(FdInvoiceApplicationVO vo) throws Exception {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String tsIn = noConfigService.genNo("INV");
        if (tsIn.contains("请联系系统管理员")) {
            throw new Exception(tsIn);
        }
        vo.setStatus("0");
        vo.setInvoiceApplicationCode(tsIn);
        vo.setAddWho(userInfo.getUserName());
        vo.setCustomerNo(vo.getCustomerCode());
        vo.setCustomerName(vo.getCompanyName());
        vo.setAddWhoName(userInfo.getRealName());
        vo.setAddTime(LocalDateTime.now());
        List<FdInvoiceApplicationDetail> detailList = vo.getDetailList();
        BigDecimal sum = BigDecimal.valueOf(0);
        for (FdInvoiceApplicationDetail detail : detailList) {
            sum = sum.add(detail.getLocalCurrencyAmount());
            detail.setInvoiceApplicationCode(tsIn);
            detail.setDelFlag("N");
            detail.setCreateUsercode(userInfo.getUserName());
            detail.setCreateUserrealname(userInfo.getRealName());
            detail.setCreateTime(LocalDateTime.now());
            //2023-09-07 发票新增时更新台账、子账单
            if ("f_fee_type".equals(detail.getCodeBbCategoriesCode()) || "发运运费".equals(detail.getCodeBbCategoriesName())) {
                FdShippingAccoundetail updateObj = new FdShippingAccoundetail();
                updateObj.setRowId(Long.parseLong(detail.getUuid()));
                updateObj.setUpdateWho(userInfo.getUserName());
                updateObj.setUpdateWhoName(userInfo.getRealName());
                updateObj.setUpdateTime(LocalDateTime.now());
                if ("f_overseas_fee".equals(detail.getCodeSsCategoriesCode()) || "境外费用".equals(detail.getCodeSsCategoriesName())) {
                    updateObj.setIsInvoiceJw("1");
                    updateObj.setInvoiceNumberJw(tsIn);
                } else if ("f_domestic_fee".equals(detail.getCodeSsCategoriesCode()) || "境内运费".equals(detail.getCodeSsCategoriesName())) {
                    updateObj.setIsInvoiceJn("1");
                    updateObj.setInvoiceNumberJn(tsIn);
                }
                fdShippingAccoundetailMapper.updateFdShippingAccoundetail(updateObj);
            } else {
                FdBillSubDetail updateObj = new FdBillSubDetail();
                updateObj.setUuid(detail.getUuid());
                updateObj.setIsInvoice("1");
                updateObj.setInvoiceApplicationCode(tsIn);
                updateObj.setUpdateWho(userInfo.getUserName());
                updateObj.setUpdateWhoName(userInfo.getRealName());
                updateObj.setUpdateTime(LocalDateTime.now());
                fdBillSubDetailMapper.updateFdBillSubDetail(updateObj);
            }
            fdInvoiceApplicationDetailMapper.insertFdInvoiceApplicationDetail(detail);
        }
        vo.setInvoiceAmount(sum);
        fdInvoiceApplicationMapper.insert(vo);
        //2021-12-10 发票新增时更新费用明细表的数据信息
        return new R<>(200, Boolean.TRUE, null, "保存成功！");
    }

    /**
     * 修改开票申请主表
     *
     * @param vo 开票申请主表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateFdInvoiceApplication(FdInvoiceApplicationVO vo) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        if ("0".equals(vo.getStatus()) || "1".equals(vo.getStatus())) {
            if (vo.getDetailList() != null) {
                // 修改子表数据
                EntityWrapper<FdInvoiceApplicationDetail> wrapper = new EntityWrapper<>();
                wrapper.eq("invoice_application_code", vo.getInvoiceApplicationCode());
                wrapper.eq("del_flag", 'N');
                List<FdInvoiceApplicationDetail> fdInvoiceApplicationDetails = fdInvoiceApplicationDetailService.selectList(wrapper);
                for (FdInvoiceApplicationDetail fdInvoiceApplicationDetail : fdInvoiceApplicationDetails) {
                    fdInvoiceApplicationDetail.setDelFlag("Y");
                    fdInvoiceApplicationDetailService.updateFdInvoiceApplicationDetail(fdInvoiceApplicationDetail);
                    // 如果有回程 修改发票核对表信息
                    if ("R".equals(fdInvoiceApplicationDetail.getTrip())) {
                        fdInvoiceApplicationMapper.updateInvoiceCheckByProvinceShiftNoAndContainerNumber(fdInvoiceApplicationDetail.getProvinceShiftNo(), fdInvoiceApplicationDetail.getContainerNumber(), "N", null, InvoiceCheckEnum.NOT_APPLY_CHECK.getKey());
                    }
                }
                List<Integer> costIds = fdInvoiceApplicationDetails.stream().map(FdInvoiceApplicationDetail::getCostId).collect(Collectors.toList());
                if (!costIds.isEmpty()) {
                    // 修改费用信息 解除绑定
                    fdInvoiceApplicationMapper.updateCostIsInvoiceByBillInfo(costIds, 0, vo.getInvoiceApplicationCode());
                }


                List<FdInvoiceApplicationDetail> detailList = vo.getDetailList();
                BigDecimal sum = BigDecimal.valueOf(0);
                for (FdInvoiceApplicationDetail dto : detailList) {
                    dto.setId(null);
                    dto.setCreateTime(LocalDateTime.now());
                    dto.setCreateUsercode(userInfo.getUserName());
                    dto.setCreateUserrealname(userInfo.getRealName());
                    dto.setInvoiceApplicationCode(vo.getInvoiceApplicationCode());
                    sum = sum.add(dto.getLocalCurrencyAmount());
                    // 如果有回程 修改发票核对表信息
                    if ("R".equals(dto.getTrip())) {
                        fdInvoiceApplicationMapper.updateInvoiceCheckByProvinceShiftNoAndContainerNumber(dto.getProvinceShiftNo(), dto.getContainerNumber(), "Y", dto.getInvoiceApplicationCode(), InvoiceCheckEnum.ALREADY_APPLY_CHECK.getKey());
                    }
                }
                vo.setInvoiceAmount(sum);
                if (CollUtil.isNotEmpty(detailList)) {
                    // 修改费用信息，绑定
                    List<Integer> bindingCostIds = detailList.stream().map(FdInvoiceApplicationDetail::getCostId).collect(Collectors.toList());
                    if (!bindingCostIds.isEmpty()) {
                        fdInvoiceApplicationMapper.updateCostIsInvoiceByBillInfo(bindingCostIds, 1, vo.getInvoiceApplicationCode());
                    }
                    if (!detailList.isEmpty()) {
                        //新增子表中该单号的数据
                        fdInvoiceApplicationDetailService.insertBatch(detailList);
                    }
                }
                //修改开票校验历史状态
                fdInvoiceCheckHistoryMapper.updateCheckStatusByInvoiceCode(vo.getInvoiceApplicationCode());

            }
        }


        //修改费用明细中的数据
        if ("2".equals(vo.getStatus()) || "3".equals(vo.getStatus())) {
            ArrayList<String> arrayList = new ArrayList<>();
            EntityWrapper<FdInvoiceApplicationDetail> detailWrapper = new EntityWrapper<>();
            detailWrapper.eq("invoice_application_code", vo.getInvoiceApplicationCode());
            detailWrapper.eq("del_flag", 'N');

            int costInvoiceStatus = "2".equals(vo.getStatus()) ? 2 : 0;

            List<FdInvoiceApplicationDetail> list = fdInvoiceApplicationDetailService.selectList(detailWrapper);
            for (FdInvoiceApplicationDetail detail : list) {
                arrayList.add(detail.getUuid());

            }
            List<Integer> costIds = list.stream().map(FdInvoiceApplicationDetail::getCostId).collect(Collectors.toList());
            fdInvoiceApplicationMapper.updateCostIsInvoiceByBillInfo(costIds, costInvoiceStatus, vo.getInvoiceApplicationCode());
            if ("2".equals(vo.getStatus())) {
                /*map.put("invoice", 1);
                map.put("invoiceApplicationNumber", vo.getInvoiceApplicationCode());
                fdCosdetailService.updateinvoicebycostdetailids(map);*/

                //修改子表中开票状态
                FdInvoiceApplicationDetail detail = new FdInvoiceApplicationDetail();
                detail.setInvoice("1");
                fdInvoiceApplicationDetailService.update(detail, detailWrapper);

                vo.setConfirmTime(new Date());
                vo.setConfirmUser(userInfo.getRealName());

                fdInvoiceApplicationMapper.updateInvoiceCheckByInvoiceCode(detail.getInvoiceApplicationCode(), InvoiceCheckEnum.ALREADY_CHECK.getKey(), "Y");

            }

        }

        //修改主表的数据
        EntityWrapper<FdInvoiceApplication> wrapper = new EntityWrapper<>();
        wrapper.eq("invoice_application_code", vo.getInvoiceApplicationCode());
        //保存文件名称和url
        if (vo.getFileList() != null && !vo.getFileList().isEmpty()) {
            List<Map<String, String>> urlList = vo.getFileList();
            StringBuilder fileName = new StringBuilder();
            StringBuilder fileUrl = new StringBuilder();
            for (Map<String, String> map : urlList) {
                fileName.append(map.get("name")).append(",");
                fileUrl.append(map.get("url")).append(",");
            }
            vo.setAttachment(fileUrl.substring(0, fileUrl.length() - 1));
            vo.setAttachmentName(fileName.substring(0, fileName.length() - 1));
        }
        fdInvoiceApplicationMapper.update(vo, wrapper);

        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改市发票状态和附件
     *
     * @param vo
     * @return
     */
    @Override
    public R updateInvoiceStatusAndFile(FdInvoiceApplicationVO vo) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();

        // 查询出主数据
        FdInvoiceApplication fdInvoiceApplication = fdInvoiceApplicationMapper.selectById(vo.getId());

        if (!StringUtils.isEmpty(vo.getStatus())) {
            fdInvoiceApplication.setStatus(vo.getStatus());

            if ("2".equals(vo.getStatus())) {
                EntityWrapper<FdInvoiceApplicationDetail> detailWrapper = new EntityWrapper<>();
                detailWrapper.eq("invoice_application_code", fdInvoiceApplication.getInvoiceApplicationCode());
                detailWrapper.eq("del_flag", 'N');

                //修改子表中开票状态
                FdInvoiceApplicationDetail detail = new FdInvoiceApplicationDetail();
                detail.setInvoice("1");
                fdInvoiceApplicationDetailService.update(detail, detailWrapper);

                fdInvoiceApplication.setConfirmTime(new Date());
                fdInvoiceApplication.setConfirmUser(userInfo.getRealName());

                fdInvoiceApplicationMapper.updateInvoiceCheckByInvoiceCode(fdInvoiceApplication.getInvoiceApplicationCode(), InvoiceCheckEnum.ALREADY_CHECK.getKey(), "Y");
            }
            if ("3".equals(vo.getStatus())) {
                EntityWrapper<FdInvoiceApplicationDetail> detailWrapper = new EntityWrapper<>();
                detailWrapper.eq("invoice_application_code", fdInvoiceApplication.getInvoiceApplicationCode());
                detailWrapper.eq("del_flag", 'N');

                //修改子表中开票状态
                FdInvoiceApplicationDetail detail = new FdInvoiceApplicationDetail();
                detail.setInvoice("0");
                fdInvoiceApplicationDetailService.update(detail, detailWrapper);

                fdInvoiceApplication.setConfirmTime(new Date());
                fdInvoiceApplication.setConfirmUser(userInfo.getRealName());

                fdInvoiceApplicationMapper.updateInvoiceCheckByInvoiceCode(fdInvoiceApplication.getInvoiceApplicationCode(), InvoiceCheckEnum.TO_VOID.getKey(), "N");

                //作废费用明细相关数据
                fdBusCostDetailMapper.updateFdBusCostDetailByInvoiceCode(fdInvoiceApplication.getInvoiceApplicationCode());
            }
        }

        //修改主表的数据
        //保存文件名称和url
        if (vo.getFileList() != null && vo.getFileList().size() > 0) {
            List<Map<String, String>> urlList = vo.getFileList();
            StringBuilder fileName = new StringBuilder();
            StringBuilder fileUrl = new StringBuilder();
            for (Map<String, String> map : urlList) {
                fileName.append(map.get("name") + ",");
                fileUrl.append(map.get("url") + ",");
            }
            fdInvoiceApplication.setAttachment(fileUrl.substring(0, fileUrl.length() - 1));
            fdInvoiceApplication.setAttachmentName(fileName.substring(0, fileName.length() - 1));
        } else {
            fdInvoiceApplication.setAttachment(null);
            fdInvoiceApplication.setAttachmentName(null);
            fdInvoiceApplicationMapper.clearAttachment(fdInvoiceApplication.getInvoiceApplicationCode());
        }
        //修改主表的数据
        EntityWrapper<FdInvoiceApplication> wrapper = new EntityWrapper<>();
        wrapper.eq("invoice_application_code", fdInvoiceApplication.getInvoiceApplicationCode());
        fdInvoiceApplicationMapper.update(fdInvoiceApplication, wrapper);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改开票申请主表
     *
     * @param vo 开票申请主表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R update(FdInvoiceApplicationVO vo) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        //修改主表的数据
        EntityWrapper<FdInvoiceApplication> wrapper = new EntityWrapper<>();
        wrapper.eq("invoice_application_code", vo.getInvoiceApplicationCode());

        if (StrUtil.isNotEmpty(vo.getStatus())) {
            if ("1".equals(vo.getStatus())) {
                //保存文件名称和url
                if (CollUtil.isNotEmpty(vo.getFileList())) {
                    List<Map<String, String>> urlList = vo.getFileList();
                    StringBuilder fileName = new StringBuilder();
                    StringBuilder fileUrl = new StringBuilder();
                    for (Map<String, String> map : urlList) {
                        fileName.append(map.get("name") + ",");
                        fileUrl.append(map.get("url") + ",");
                    }
                    vo.setAttachment(fileUrl.substring(0, fileUrl.length() - 1));
                    vo.setAttachmentName(fileName.substring(0, fileName.length() - 1));
                }

                List<FdInvoiceApplicationDetail> detailList = vo.getDetailList();
                BigDecimal sum = BigDecimal.valueOf(0);
                List<String> containerNumbers = new ArrayList<>();
                for (FdInvoiceApplicationDetail detail : detailList) {
                    containerNumbers.add(detail.getContainerNumber());
                    sum = sum.add(detail.getLocalCurrencyAmount());
                    if (detail.getId() == null) {
                        detail.setInvoiceApplicationCode(vo.getInvoiceApplicationCode());
                        detail.setDelFlag("N");
                        detail.setCreateUsercode(userInfo.getUserName());
                        detail.setCreateUserrealname(userInfo.getRealName());
                        detail.setCreateTime(LocalDateTime.now());
                        //2023-09-07 发票新增时更新台账、子账单
                        changeDetail(detail, userInfo, vo.getInvoiceApplicationCode(), "1");
                        fdInvoiceApplicationDetailMapper.insertFdInvoiceApplicationDetail(detail);
                    }
                    /*else{
                        detail.setUpdateUsercode(userInfo.getUserName());
                        detail.setUpdateUserrealname(userInfo.getRealName());
                        detail.setUpdateTime(LocalDateTime.now());
                        fdInvoiceApplicationDetailMapper.updateFdInvoiceApplicationDetail(detail);
                    }*/
                }

                //作废已经删除的旧数据
                if (CollUtil.isNotEmpty(containerNumbers)) {
                    List<FdInvoiceApplicationDetail> deleteList = fdInvoiceApplicationDetailMapper.selectDeletedList(vo.getInvoiceApplicationCode(), containerNumbers);
                    if (CollUtil.isNotEmpty(deleteList)) {
                        for (FdInvoiceApplicationDetail fdInvoiceApplicationDetail : deleteList) {
                            if (StrUtil.isNotEmpty(fdInvoiceApplicationDetail.getCodeBbCategoriesCode()) && StrUtil.isNotEmpty(fdInvoiceApplicationDetail.getUuid())) {
                                //运费关联作废台账详情
                                changeDetail(fdInvoiceApplicationDetail, userInfo, "开票已作废", "0");
                            }
                            //作废开票明细
                            FdInvoiceApplicationDetail delObj = new FdInvoiceApplicationDetail();
                            delObj.setId(fdInvoiceApplicationDetail.getId());
                            delObj.setDelFlag("Y");
                            delObj.setDelUsercode(userInfo.getUserName());
                            delObj.setDelUserrealname(userInfo.getRealName());
                            delObj.setDelTime(LocalDateTime.now());
                            fdInvoiceApplicationDetailMapper.updateFdInvoiceApplicationDetail(delObj);
                        }
                    }
                }

                vo.setInvoiceAmount(sum);
            } else if ("2".equals(vo.getStatus())) {
                //2023-09-15 发票确认时更新台账、子账单
                FdInvoiceApplicationDetail sel = new FdInvoiceApplicationDetail();
                sel.setInvoiceApplicationCode(vo.getInvoiceApplicationCode());
                sel.setDelFlag("N");
                final List<FdInvoiceApplicationDetail> fdInvoiceApplicationDetails = fdInvoiceApplicationDetailMapper.selectFdInvoiceApplicationDetailList(sel);
                if (CollUtil.isNotEmpty(fdInvoiceApplicationDetails)) {
                    for (FdInvoiceApplicationDetail fdInvoiceApplicationDetail : fdInvoiceApplicationDetails) {
                        changeDetail(fdInvoiceApplicationDetail, userInfo, vo.getInvoiceApplicationCode(), "2");
                    }
                }
            }
        }


        vo.setUpdateWho(userInfo.getUserName());
        vo.setUpdateWhoName(userInfo.getRealName());
        vo.setUpdateTime(LocalDateTime.now());
        fdInvoiceApplicationMapper.update(vo, wrapper);
        return new R<>(200, Boolean.TRUE, null, "操作成功！");
    }

    public void changeDetail(FdInvoiceApplicationDetail detail, SecruityUser userInfo, String invoiceApplicationCode, String isInvoice) {
        if ("f_fee_type".equals(detail.getCodeBbCategoriesCode()) || "发运运费".equals(detail.getCodeBbCategoriesName())) {
            FdShippingAccoundetail updateObj = new FdShippingAccoundetail();
            updateObj.setRowId(Long.parseLong(detail.getUuid()));
            updateObj.setUpdateWho(userInfo.getUserName());
            updateObj.setUpdateWhoName(userInfo.getRealName());
            updateObj.setUpdateTime(LocalDateTime.now());
            if ("f_overseas_fee".equals(detail.getCodeSsCategoriesCode()) || "境外费用".equals(detail.getCodeSsCategoriesName())) {
                updateObj.setIsInvoiceJw(isInvoice);
                updateObj.setInvoiceNumberJw(invoiceApplicationCode);
            } else if ("f_domestic_fee".equals(detail.getCodeSsCategoriesCode()) || "境内运费".equals(detail.getCodeSsCategoriesName())) {
                updateObj.setIsInvoiceJn(isInvoice);
                updateObj.setInvoiceNumberJn(invoiceApplicationCode);
            }
            fdShippingAccoundetailMapper.updateFdShippingAccoundetail(updateObj);
        } else {
            FdBillSubDetail updateObj = new FdBillSubDetail();
            updateObj.setUuid(detail.getUuid());
            updateObj.setIsInvoice(isInvoice);
            updateObj.setInvoiceApplicationCode(invoiceApplicationCode);
            updateObj.setUpdateWho(userInfo.getUserName());
            updateObj.setUpdateWhoName(userInfo.getRealName());
            updateObj.setUpdateTime(LocalDateTime.now());
            fdBillSubDetailMapper.updateFdBillSubDetail(updateObj);
        }
    }


    /**
     * 删除开票申请主表
     *
     * @param id 开票申请主表ID
     * @return 结果
     */
    @Override
    public R deleteFdInvoiceApplicationById(Long id) {
        FdInvoiceApplication fdInvoiceApplication = fdInvoiceApplicationMapper.selectFdInvoiceApplicationById(id);
        if (fdInvoiceApplication == null) {
            return R.error("查询不到此数据！");
        }
        if (!"0".equals(fdInvoiceApplication.getStatus())){
            return R.error("只有待提交状态才能删除！");
        }
        // 修改子表数据
        EntityWrapper<FdInvoiceApplicationDetail> wrapper = new EntityWrapper<>();
        wrapper.eq("invoice_application_code", fdInvoiceApplication.getInvoiceApplicationCode());
        wrapper.eq("del_flag", 'N');
        List<FdInvoiceApplicationDetail> fdInvoiceApplicationDetails = fdInvoiceApplicationDetailService.selectList(wrapper);
        for (FdInvoiceApplicationDetail fdInvoiceApplicationDetail : fdInvoiceApplicationDetails) {
            fdInvoiceApplicationDetail.setDelFlag("Y");
            fdInvoiceApplicationDetailService.updateFdInvoiceApplicationDetail(fdInvoiceApplicationDetail);
            // 如果有回程 修改发票核对表信息
            if ("R".equals(fdInvoiceApplicationDetail.getTrip())) {
                fdInvoiceApplicationMapper.updateInvoiceCheckByProvinceShiftNoAndContainerNumber(fdInvoiceApplicationDetail.getProvinceShiftNo(), fdInvoiceApplicationDetail.getContainerNumber(), "N", null, InvoiceCheckEnum.NOT_APPLY_CHECK.getKey());
            }
        }
        if (CollUtil.isNotEmpty(fdInvoiceApplicationDetails)) {
            List<Integer> costIds = fdInvoiceApplicationDetails.stream().map(FdInvoiceApplicationDetail::getCostId).collect(Collectors.toList());
            if (!costIds.isEmpty()) {
                // 修改费用信息 解除绑定
                fdInvoiceApplicationMapper.updateCostIsInvoiceByBillInfo(costIds, 0, fdInvoiceApplication.getInvoiceApplicationCode());
            }
        }

        fdInvoiceApplicationMapper.deleteFdInvoiceApplicationById(id);
        return R.success();
    }


    /**
     * 批量删除开票申请主表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFdInvoiceApplicationByIds(Integer[] ids) {
        return fdInvoiceApplicationMapper.deleteFdInvoiceApplicationByIds(ids);
    }

    @Override
    public List<FdShippingDTO> fdShippingList(FdInvoiceApplication fdInvoiceApplication) {
        return fdInvoiceApplicationMapper.fdShippingList(fdInvoiceApplication);
    }

    /**
     * @param id
     * @return
     */
    @Override
    public R cancel(Long id) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        final FdInvoiceApplication fdInvoiceApplication = fdInvoiceApplicationMapper.selectFdInvoiceApplicationById(id);
        if (fdInvoiceApplication != null && StrUtil.isNotEmpty(fdInvoiceApplication.getInvoiceApplicationCode())) {
            FdInvoiceApplicationDetail sel = new FdInvoiceApplicationDetail();
            sel.setInvoiceApplicationCode(fdInvoiceApplication.getInvoiceApplicationCode());
            sel.setDelFlag("N");
            final List<FdInvoiceApplicationDetail> fdInvoiceApplicationDetails = fdInvoiceApplicationDetailMapper.selectFdInvoiceApplicationDetailList(sel);
            if (CollUtil.isNotEmpty(fdInvoiceApplicationDetails)) {
                for (FdInvoiceApplicationDetail fdInvoiceApplicationDetail : fdInvoiceApplicationDetails) {
                    if (StrUtil.isNotEmpty(fdInvoiceApplicationDetail.getCodeBbCategoriesCode()) && StrUtil.isNotEmpty(fdInvoiceApplicationDetail.getUuid())) {
                        //运费关联作废台账详情
                        if ("f_fee_type".equals(fdInvoiceApplicationDetail.getCodeBbCategoriesCode())) {
                            FdShippingAccoundetail updateObj = new FdShippingAccoundetail();
                            updateObj.setRowId(Long.parseLong(fdInvoiceApplicationDetail.getUuid()));
                            if (StrUtil.isNotEmpty(fdInvoiceApplicationDetail.getCodeSsCategoriesCode())) {
                                if ("f_overseas_fee".equals(fdInvoiceApplicationDetail.getCodeSsCategoriesCode())) {
                                    updateObj.setIsInvoiceJw("0");
                                    updateObj.setInvoiceNumberJw("开票已作废");
                                } else if ("f_domestic_fee".equals(fdInvoiceApplicationDetail.getCodeSsCategoriesCode())) {
                                    updateObj.setIsInvoiceJn("0");
                                    updateObj.setInvoiceNumberJn("开票已作废");
                                }
                            }
                            updateObj.setUpdateWho(userInfo.getUserName());
                            updateObj.setUpdateWhoName(userInfo.getRealName());
                            updateObj.setUpdateTime(LocalDateTime.now());
                            fdShippingAccoundetailMapper.updateFdShippingAccoundetail(updateObj);
                        } else {
                            //杂费关联作废子账单
                            FdBillSubDetail updateObj = new FdBillSubDetail();
                            updateObj.setUuid(fdInvoiceApplicationDetail.getUuid());
                            updateObj.setIsInvoice("0");
                            updateObj.setInvoiceApplicationCode("开票已作废");
                            updateObj.setUpdateWho(userInfo.getUserName());
                            updateObj.setUpdateWhoName(userInfo.getRealName());
                            updateObj.setUpdateTime(LocalDateTime.now());
                            fdBillSubDetailMapper.updateFdBillSubDetail(updateObj);
                        }
                    }
                    //作废开票明细
                    FdInvoiceApplicationDetail delObj = new FdInvoiceApplicationDetail();
                    delObj.setId(fdInvoiceApplicationDetail.getId());
                    delObj.setDelFlag("Y");
                    delObj.setDelUsercode(userInfo.getUserName());
                    delObj.setDelUserrealname(userInfo.getRealName());
                    delObj.setDelTime(LocalDateTime.now());
                    fdInvoiceApplicationDetailMapper.updateFdInvoiceApplicationDetail(delObj);
                }
            }
        }
        FdInvoiceApplication newObj = new FdInvoiceApplication();
        newObj.setId(id);
        newObj.setDeleteFlag("Y");
        newObj.setStatus("3");
        newObj.setDeleteWho(userInfo.getUserName());
        newObj.setDeleteWhoName(userInfo.getRealName());
        newObj.setDeleteTime(LocalDateTime.now());
        fdInvoiceApplicationMapper.updateFdInvoiceApplication(newObj);
        return new R(200, Boolean.TRUE, null, "作废成功！");
    }


    /**
     * @param id
     * @return
     */
    @Override
    public R fiCancel(Long id) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        final FdInvoiceApplication fdInvoiceApplication = fdInvoiceApplicationMapper.selectFdInvoiceApplicationById(id);
        if (fdInvoiceApplication != null && StrUtil.isNotEmpty(fdInvoiceApplication.getInvoiceApplicationCode())) {
            FdInvoiceApplicationDetail sel = new FdInvoiceApplicationDetail();
            sel.setInvoiceApplicationCode(fdInvoiceApplication.getInvoiceApplicationCode());
            sel.setDelFlag("N");
            final List<FdInvoiceApplicationDetail> fdInvoiceApplicationDetails = fdInvoiceApplicationDetailMapper.selectFdInvoiceApplicationDetailList(sel);
            if (CollUtil.isNotEmpty(fdInvoiceApplicationDetails)) {
                for (FdInvoiceApplicationDetail fdInvoiceApplicationDetail : fdInvoiceApplicationDetails) {
                    if (StrUtil.isNotEmpty(fdInvoiceApplicationDetail.getCodeBbCategoriesCode()) && StrUtil.isNotEmpty(fdInvoiceApplicationDetail.getUuid())) {
                        //运费关联作废台账详情
                        if ("f_fee_type".equals(fdInvoiceApplicationDetail.getCodeBbCategoriesCode())) {
                            FdShippingAccoundetail updateObj = new FdShippingAccoundetail();
                            updateObj.setRowId(Long.parseLong(fdInvoiceApplicationDetail.getUuid()));
                            if (StrUtil.isNotEmpty(fdInvoiceApplicationDetail.getCodeSsCategoriesCode())) {
                                if ("f_overseas_fee".equals(fdInvoiceApplicationDetail.getCodeSsCategoriesCode())) {
                                    updateObj.setIsInvoiceJw("0");
                                    updateObj.setInvoiceNumberJw("开票已作废");
                                } else if ("f_domestic_fee".equals(fdInvoiceApplicationDetail.getCodeSsCategoriesCode())) {
                                    updateObj.setIsInvoiceJn("0");
                                    updateObj.setInvoiceNumberJn("开票已作废");
                                }
                            }
                            updateObj.setUpdateWho(userInfo.getUserName());
                            updateObj.setUpdateWhoName(userInfo.getRealName());
                            updateObj.setUpdateTime(LocalDateTime.now());
                            fdShippingAccoundetailMapper.updateFdShippingAccoundetail(updateObj);
                        } else {
                            //杂费关联作废子账单
                            FdBillSubDetail updateObj = new FdBillSubDetail();
                            updateObj.setUuid(fdInvoiceApplicationDetail.getUuid());
                            updateObj.setIsInvoice("0");
                            updateObj.setInvoiceApplicationCode("开票已作废");
                            updateObj.setUpdateWho(userInfo.getUserName());
                            updateObj.setUpdateWhoName(userInfo.getRealName());
                            updateObj.setUpdateTime(LocalDateTime.now());
                            fdBillSubDetailMapper.updateFdBillSubDetail(updateObj);
                        }
                    }
                    //作废开票明细
                    FdInvoiceApplicationDetail delObj = new FdInvoiceApplicationDetail();
                    delObj.setId(fdInvoiceApplicationDetail.getId());
                    delObj.setDelFlag("C");
                    delObj.setDelUsercode(userInfo.getUserName());
                    delObj.setDelUserrealname(userInfo.getRealName());
                    delObj.setDelTime(LocalDateTime.now());
                    fdInvoiceApplicationDetailMapper.updateFdInvoiceApplicationDetail(delObj);
                }
            }
        }
        FdInvoiceApplication newObj = new FdInvoiceApplication();
        newObj.setId(id);
        newObj.setDeleteFlag("C");
        newObj.setStatus("3");
        newObj.setDeleteWho(userInfo.getUserName());
        newObj.setDeleteWhoName(userInfo.getRealName());
        newObj.setDeleteTime(LocalDateTime.now());
        fdInvoiceApplicationMapper.updateFdInvoiceApplication(newObj);
        return new R(200, Boolean.TRUE, null, "作废成功！");
    }

    @Override
    public void revenueLedgerExport(FdShippingAccount fdShippingAccount, HttpServletResponse response) throws Exception {
        Shifmanagement sel = new Shifmanagement();
        sel.setShiftId(fdShippingAccount.getShiftNo());
        sel.setPlatformCode(fdShippingAccount.getPlatformCode());
        sel.setDeleteFlag("N");
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
        String shiftName = "";
        String lineName = "";
        if (CollUtil.isNotEmpty(shifmanagements)) {
            shiftName = shifmanagements.get(0).getShiftName();
            lineName = shifmanagements.get(0).getShippingLine() + "(" + shifmanagements.get(0).getDestinationName() + "-" + shifmanagements.get(0).getDestination() + ")";
        }
        fdShippingAccount.setDeleteFlag("N");
        List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountList(fdShippingAccount);

        List<FdShippingAccoundetail> fdShippingAccoundetails = fdShippingAccoundetailMapper.selectFdShippingAccoundetailByList(fdShippingAccount.getShiftNo(), fdShippingAccount.getPlatformCode());

        String templateFileName = "revenueLedgerExport.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)) {
            templateFileName = linuxPath + templateFileName;
        } else {
            templateFileName = windowsPath + templateFileName;
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 使用 UTF-8 编码处理文件名，确保中文文件名在浏览器中正确显示
        String encodedFileName = URLEncoder.encode(shiftName + "收入台账.xlsx", "UTF-8");
        encodedFileName = encodedFileName.replaceAll("\\+", "%20"); // 处理空格问题
        response.setHeader("Content-Disposition", "attachment;filename=\"" + encodedFileName + "\"");
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");

        InputStream templateFile = new FileInputStream(templateFileName);
        XSSFWorkbook workbook = new XSSFWorkbook(templateFile);
        // 创建一个数字格式的 CellStyle
        CellStyle cellStyle = workbook.createCellStyle();

        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);

        Font font = workbook.createFont();
        font.setBold(true);
        CellStyle cellStyle2 = workbook.createCellStyle();
        cellStyle2.setBorderBottom(BorderStyle.THIN);
        cellStyle2.setBorderLeft(BorderStyle.THIN);
        cellStyle2.setBorderTop(BorderStyle.THIN);
        cellStyle2.setBorderRight(BorderStyle.THIN);
        cellStyle2.setAlignment(HorizontalAlignment.CENTER);
        cellStyle2.setFont(font);

        SimpleDateFormat dft = new SimpleDateFormat("yyyyMMdd");
        XSSFSheet sheet = workbook.getSheetAt(0);
        setTitle(shifmanagements, lineName, fdShippingAccounts, dft, sheet);

        setList(fdShippingAccoundetails, cellStyle, cellStyle2, sheet);

        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }

    /**
     * 设置内容
     */
    private static void setList(List<FdShippingAccoundetail> fdShippingAccoundetails, CellStyle cellStyle, CellStyle cellStyle2, XSSFSheet sheet) {
        int rowIndex = 3;
        BigDecimal overseasFreightOc = BigDecimal.ZERO;
        BigDecimal overseasFreightCny = BigDecimal.ZERO;
        BigDecimal domesticFreight = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(fdShippingAccoundetails)) {
            for (FdShippingAccoundetail fdShippingAccoundetail : fdShippingAccoundetails) {
                XSSFRow rowCell = sheet.createRow(rowIndex);
                XSSFCell cell0 = rowCell.createCell(0);
                cell0.setCellValue(rowIndex - 2);
                cell0.setCellStyle(cellStyle);
                XSSFCell cell1 = rowCell.createCell(1);
                if ("G".equals(fdShippingAccoundetail.getTrip())) {
                    cell1.setCellValue("去");
                } else {
                    cell1.setCellValue("回");
                }
                cell1.setCellStyle(cellStyle);
                XSSFCell cell2 = rowCell.createCell(2);
                cell2.setCellValue(fdShippingAccoundetail.getOrgUnit());
                cell2.setCellStyle(cellStyle);
                XSSFCell cell3 = rowCell.createCell(3);
                cell3.setCellValue(fdShippingAccoundetail.getDestinationCountry());
                cell3.setCellStyle(cellStyle);
                XSSFCell cell4 = rowCell.createCell(4);
                cell4.setCellValue(fdShippingAccoundetail.getContainerType());
                cell4.setCellStyle(cellStyle);
                XSSFCell cell5 = rowCell.createCell(5);
                cell5.setCellValue(fdShippingAccoundetail.getContainerNumber());
                cell5.setCellStyle(cellStyle);
                XSSFCell cell6 = rowCell.createCell(6);
                cell6.setCellValue(fdShippingAccoundetail.getClearanceNumber());
                cell6.setCellStyle(cellStyle);
                XSSFCell cell7 = rowCell.createCell(7);
                cell7.setCellValue(fdShippingAccoundetail.getWaybillLnNumber());
                cell7.setCellStyle(cellStyle);
                XSSFCell cell8 = rowCell.createCell(8);
                cell8.setCellValue(fdShippingAccoundetail.getDomesticFreight() != null ? Double.parseDouble(String.format("%.2f", fdShippingAccoundetail.getDomesticFreight())) : 0.00);
                cell8.setCellStyle(cellStyle);
                if (fdShippingAccoundetail.getDomesticFreight() != null) {
                    domesticFreight = domesticFreight.add(fdShippingAccoundetail.getDomesticFreight());
                }
                XSSFCell cell9 = rowCell.createCell(9);
                cell9.setCellValue(fdShippingAccoundetail.getOverseasFreightOc() != null ? Double.parseDouble(String.format("%.2f", fdShippingAccoundetail.getOverseasFreightOc())) : 0.00);
                cell9.setCellStyle(cellStyle);
                if (fdShippingAccoundetail.getOverseasFreightOc() != null) {
                    overseasFreightOc = overseasFreightOc.add(fdShippingAccoundetail.getOverseasFreightOc());
                }
                XSSFCell cell10 = rowCell.createCell(10);
                cell10.setCellValue(fdShippingAccoundetail.getExchangeRate() != null ? Double.parseDouble(String.format("%.4f", fdShippingAccoundetail.getExchangeRate())) : 0.0000);
                cell10.setCellStyle(cellStyle);
                XSSFCell cell11 = rowCell.createCell(11);
                cell11.setCellValue(fdShippingAccoundetail.getOverseasFreightCny() != null ? Double.parseDouble(String.format("%.2f", fdShippingAccoundetail.getOverseasFreightCny())) : 0.00);
                cell11.setCellStyle(cellStyle);
                if (fdShippingAccoundetail.getOverseasFreightCny() != null) {
                    overseasFreightCny = overseasFreightCny.add(fdShippingAccoundetail.getOverseasFreightCny());
                }
                rowIndex++;
            }
        }
        XSSFRow rowCell = sheet.createRow(rowIndex);
        XSSFCell cell0 = rowCell.createCell(0);
        cell0.setCellValue("运费合计金额：" + Double.parseDouble(String.format("%.2f", domesticFreight.add(overseasFreightCny))));
        cell0.setCellStyle(cellStyle2);
        for (int i = 1; i < 8; i++) {
            XSSFCell celli = rowCell.createCell(i);
            celli.setCellStyle(cellStyle2);
        }
        XSSFCell cell8 = rowCell.createCell(8);
        cell8.setCellValue(Double.parseDouble(String.format("%.2f", domesticFreight)));
        cell8.setCellStyle(cellStyle);
        XSSFCell cell9 = rowCell.createCell(9);
        cell9.setCellValue(Double.parseDouble(String.format("%.2f", overseasFreightOc)));
        cell9.setCellStyle(cellStyle);
        XSSFCell cell10 = rowCell.createCell(10);
        cell10.setCellStyle(cellStyle);
        XSSFCell cell11 = rowCell.createCell(11);
        cell11.setCellValue(Double.parseDouble(String.format("%.2f", overseasFreightCny)));
        cell11.setCellStyle(cellStyle);
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 7));
    }

    /**
     * 设置表头
     */
    private static void setTitle(List<Shifmanagement> shifmanagements, String lineName, List<FdShippingAccount> fdShippingAccounts, SimpleDateFormat dft, XSSFSheet sheet) {
        XSSFRow row = sheet.getRow(0);
        XSSFCell cell = row.createCell(1);
        cell.setCellValue(lineName);

        if (CollUtil.isNotEmpty(fdShippingAccounts)) {
            XSSFCell cell3 = row.createCell(3);
            cell3.setCellValue(fdShippingAccounts.get(0).getCity());
        }

        if (CollUtil.isNotEmpty(shifmanagements)) {
            XSSFCell cell5 = row.createCell(5);
            if ("G".equals(shifmanagements.get(0).getTrip())) {
                cell5.setCellValue("去");
            } else {
                cell5.setCellValue("回");
            }

            XSSFCell cell7 = row.createCell(7);
            cell7.setCellValue(dft.format(shifmanagements.get(0).getPlanShipTime()));

            XSSFCell cell9 = row.createCell(9);
            cell9.setCellValue(shifmanagements.get(0).getPortStation());

            XSSFCell cell11 = row.createCell(11);
            cell11.setCellValue(shifmanagements.get(0).getProvinceShiftNo());
        }
    }
}
