package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfFinancingConfirmationinfo;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 仓单质押企业确认信息 服务接口层
 *
 * <AUTHOR>
 * @date 2023-02-08 15:14:04
 */
public interface EfFinancingConfirmationinfoService extends IService<EfFinancingConfirmationinfo> {
    /**
     * 查询仓单质押企业确认信息信息
     *
     * @param rowId 仓单质押企业确认信息ID
     * @return 仓单质押企业确认信息信息
     */
    public EfFinancingConfirmationinfo selectEfFinancingConfirmationinfoById(String rowId);

    /**
     * 查询仓单质押企业确认信息列表
     *
     * @param efFinancingConfirmationinfo 仓单质押企业确认信息信息
     * @return 仓单质押企业确认信息集合
     */
    public List<EfFinancingConfirmationinfo> selectEfFinancingConfirmationinfoList(EfFinancingConfirmationinfo efFinancingConfirmationinfo);


    /**
     * 分页模糊查询仓单质押企业确认信息列表
     * @return 仓单质押企业确认信息集合
     */
    public Page selectEfFinancingConfirmationinfoListByLike(Query query);



    /**
     * 新增仓单质押企业确认信息
     *
     * @param efFinancingConfirmationinfo 仓单质押企业确认信息信息
     * @return 结果
     */
    public int insertEfFinancingConfirmationinfo(EfFinancingConfirmationinfo efFinancingConfirmationinfo);

    /**
     * 修改仓单质押企业确认信息
     *
     * @param efFinancingConfirmationinfo 仓单质押企业确认信息信息
     * @return 结果
     */
    public int updateEfFinancingConfirmationinfo(EfFinancingConfirmationinfo efFinancingConfirmationinfo);

    /**
     * 删除仓单质押企业确认信息
     *
     * @param rowId 仓单质押企业确认信息ID
     * @return 结果
     */
    public int deleteEfFinancingConfirmationinfoById(String rowId);

    /**
     * 批量删除仓单质押企业确认信息
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfFinancingConfirmationinfoByIds(Integer[] rowIds);

}

