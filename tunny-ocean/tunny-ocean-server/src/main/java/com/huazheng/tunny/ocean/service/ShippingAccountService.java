package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.ShippingAccount;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 发运台账表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-13 11:48:47
 */
public interface ShippingAccountService extends IService<ShippingAccount> {
    /**
     * 查询发运台账表信息
     *
     * @param rowId 发运台账表ID
     * @return 发运台账表信息
     */
    public ShippingAccount selectShippingAccountById(String rowId);

    /**
     * 查询发运台账表列表
     *
     * @param shippingAccount 发运台账表信息
     * @return 发运台账表集合
     */
    public List<ShippingAccount> selectShippingAccountList(ShippingAccount shippingAccount);


    /**
     * 分页模糊查询发运台账表列表
     * @return 发运台账表集合
     */
    public Page selectShippingAccountListByLike(Query query);


    /**
     * 根据班次号查询下属集装箱信息分页列表
     * @return 发运台账表集合
     */
    public Page selectShippingAccountListByShiftId(Query query);



    /**
     * 新增发运台账表
     *
     * @param shippingAccount 发运台账表信息
     * @return 结果
     */
    public int insertShippingAccount(ShippingAccount shippingAccount);

    /**
     * 修改发运台账表
     *
     * @param shippingAccount 发运台账表信息
     * @return 结果
     */
    public int updateShippingAccount(ShippingAccount shippingAccount);

    /**
     * 删除发运台账表
     *
     * @param rowId 发运台账表ID
     * @return 结果
     */
    public int deleteShippingAccountById(String rowId);

    /**
     * 批量删除发运台账表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteShippingAccountByIds(Integer[] rowIds);

}

