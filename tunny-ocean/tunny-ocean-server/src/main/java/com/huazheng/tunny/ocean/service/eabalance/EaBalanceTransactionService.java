package com.huazheng.tunny.ocean.service.eabalance;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.eabalance.EaBalanceTransaction;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 余额流水表 服务接口层
 *
 * <AUTHOR>
 * @date 2025-07-01 13:37:37
 */
public interface EaBalanceTransactionService extends IService<EaBalanceTransaction> {
    /**
     * 查询余额流水表信息
     *
     * @param transactionId 余额流水表ID
     * @return 余额流水表信息
     */
    public EaBalanceTransaction selectEaBalanceTransactionById(Long transactionId);

    /**
     * 查询余额流水表列表
     *
     * @param eaBalanceTransaction 余额流水表信息
     * @return 余额流水表集合
     */
    public List<EaBalanceTransaction> selectEaBalanceTransactionList(EaBalanceTransaction eaBalanceTransaction);


    /**
     * 分页模糊查询余额流水表列表
     * @return 余额流水表集合
     */
    public Page selectEaBalanceTransactionListByLike(Query query);



    /**
     * 新增余额流水表
     *
     * @param eaBalanceTransaction 余额流水表信息
     * @return 结果
     */
    public R insertEaBalanceTransaction(EaBalanceTransaction eaBalanceTransaction);

    /**
     * 修改余额流水表
     *
     * @param eaBalanceTransaction 余额流水表信息
     * @return 结果
     */
    public int updateEaBalanceTransaction(EaBalanceTransaction eaBalanceTransaction);

    /**
     * 删除余额流水表
     *
     * @param transactionId 余额流水表ID
     * @return 结果
     */
    public int deleteEaBalanceTransactionById(Long transactionId);

    /**
     * 批量删除余额流水表
     *
     * @param transactionIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEaBalanceTransactionByIds(Integer[] transactionIds);

}

