package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.util.StringUtils;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.feign.RemoteAdminService;
import com.huazheng.tunny.ocean.api.vo.FdShippingAccountVO;
import com.huazheng.tunny.ocean.api.vo.SysDictVo;
import com.huazheng.tunny.ocean.mapper.CustomerPlatformInfoMapper;
import com.huazheng.tunny.ocean.mapper.ShifmanagementMapper;
import com.huazheng.tunny.ocean.mapper.StationManagementMapper;
import com.huazheng.tunny.ocean.mapper.SysNoConfigMapper;
import com.huazheng.tunny.ocean.service.FdShippingAccountService;
import com.huazheng.tunny.ocean.service.LineManagementService;
import com.huazheng.tunny.ocean.service.ProvinceShiftNoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
@Service("provinceShiftNoService")
public class ProvinceShiftNoServiceImpl implements ProvinceShiftNoService {

    @Autowired
    private LineManagementService lineManagementService;

    @Autowired
    private SysNoConfigMapper sysNoConfigMapper;

    @Autowired
    private FdShippingAccountService fdShippingAccountService;

    @Autowired
    private CustomerPlatformInfoMapper customerPlatformInfoMapper;
    @Autowired
    private ShifmanagementMapper shifmanagementMapper;

    @Autowired
    private StationManagementMapper stationManagementMapper;

    @Autowired
    private RemoteAdminService remoteAdminService;

    @Override
    public R getProvinceShiftNo(FdShippingAccountVO vo) {
        return null;
    }

    /**
     * 生成省级班列号
     * [线路英文简称（2位大写字母）] [地市三字码（3位大写字母）] [年份（2位）月份（2位）][省级序号（00）][整列Z/回程整列H]
     *
     * @param vo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createProvinceShiftNo(FdShippingAccountVO vo) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        LineManagement lineManagement = new LineManagement();
        lineManagement.setLineName(vo.getShippingLine());
        //查询线路信息
        List<LineManagement> managements = lineManagementService.selectLineManagementList(lineManagement);
        StringBuilder sjblh = new StringBuilder();
        if (CollUtil.isEmpty(managements)) {
            return "查询不到改线路的信息,请联系系统管理员";
        }
        if (StrUtil.isBlank(managements.get(0).getLineSx())) {
            return "该线路没有缩写,请联系系统管理员";
        }
        if (StringUtils.isBlank(vo.getPlatformCode())) {
            return "平台编码不能为空，请传入平台编码,请联系系统管理员";
        }
        String flag = managements.get(0).getLineSx();
        String sjblh1;
        CustomerPlatformInfo customerPlatformInfo = new CustomerPlatformInfo();
        customerPlatformInfo.setCustomerCode(vo.getPlatformCode());
        //查询平台信息
        List<CustomerPlatformInfo> list2 = customerPlatformInfoMapper.selectCustomerPlatformInfoList(customerPlatformInfo);
        if (CollUtil.isNotEmpty(list2)) {
            if ("".equals(list2.get(0).getRemarksCode()) && list2.get(0).getRemarksCode() == null) {
                return "该账号没有助记码，请补充账号助记码,请联系系统管理员";
            }
        }
        SysNoConfig sysNoConfig = new SysNoConfig();
        if (vo.getShippingTime() == null) {
            return "该台账没有发运日期,请联系系统管理员";
        }
        // 定义日期时间格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yy-MM");
        // 格式化 LocalDateTime 为两位长度的年和月
        String formattedDate = vo.getShippingTime().format(formatter);
        String year = formattedDate.split("-")[0];
        String month = formattedDate.split("-")[1];
        sysNoConfig.setNoType(flag);
        sysNoConfig.setMonth(month);
        sysNoConfig.setYear(year);
        sysNoConfig.setDeleteFlag("N");
        sysNoConfig.setResveredField01("SJBLH");
        sysNoConfig.setResveredField02(list2.get(0).getRemarksCode());
        //获取编码配置
        List<SysNoConfig> list = sysNoConfigMapper.selectSysNoConfigList(sysNoConfig);
        sjblh1 = flag + list2.get(0).getRemarksCode() + year + month;
        //获取已有省级班列号最大值
        Integer max = shifmanagementMapper.selectMaxValueByProvinceShiftNo(sjblh1);
        SysNoConfig temp = new SysNoConfig();
        temp.setNoType(flag);
        temp.setYear(year);
        temp.setMonth(month);
        temp.setResveredField01("SJBLH");
        temp.setResveredField02(list2.get(0).getRemarksCode());
        // 返回序号
        Integer returnNo = 1;
        sjblh.append(sjblh1);
        // 判断列表是否为空
        if (CollUtil.isNotEmpty(list)) {
            // 从列表中获取序号
            returnNo = Integer.parseInt(list.get(0).getSerialNo());
            // 如果max不为null，更新序号
            if (max != null) {
                max += 1;
                returnNo = Math.max(returnNo, max);
            }
            // 更新序号
            updateSerialNo(temp, returnNo, sjblh);
            temp.setUpdateTime(LocalDateTime.now());
            temp.setUpdateWho(userInfo.getUserName());
            temp.setUpdateWhoName(userInfo.getRealName());
            sysNoConfigMapper.updateSysNoConfigSjbl(temp);
        } else {
            // 如果max不为null，更新序号
            if (max != null) {
                max += 1;
                returnNo = Math.max(returnNo, max);
            }
            // 更新序号
            updateSerialNo(temp, returnNo, sjblh);
            temp.setAddTime(LocalDateTime.now());
            temp.setAddWho(userInfo.getUserName());
            temp.setAddWhoName(userInfo.getRealName());
            sysNoConfigMapper.insertSysNoConfig(temp);
        }
        // 根据trip的值更新sjblh
        appendTrip(sjblh, vo.getTrip());
        return sjblh.toString();
    }

    // Helper 方法：更新序号
    private void updateSerialNo(SysNoConfig temp, Integer returnNo, StringBuilder sjblh) {
        Integer updateNo = returnNo + 1;
        if (updateNo < 10) {
            // 处理序号小于10的情况，补零
            temp.setSerialNo("0" + updateNo);
            sjblh.append("0").append(returnNo);
        } else {
            // 序号大于或等于10的情况
            temp.setSerialNo(String.valueOf(updateNo));
            sjblh.append(returnNo);
        }
    }

    // Helper 方法：根据trip的值更新sjblh
    private void appendTrip(StringBuilder sjblh, String trip) {
        if ("G".equals(trip)) {
            sjblh.append("Z");
        } else {
            sjblh.append("H");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createProvinceShiftNoTwo(FdShippingAccountVO vo) {
        LineManagement lineManagement = new LineManagement();
        lineManagement.setLineName(vo.getShippingLine());
        //查询线路信息
        List<LineManagement> managements = lineManagementService.selectLineManagementList(lineManagement);
        StringBuilder sjblh = new StringBuilder();
        if (CollUtil.isEmpty(managements)) {
            return "查询不到改线路的信息,请联系系统管理员";
        }
        if (StrUtil.isBlank(managements.get(0).getLineSx())) {
            return "该线路没有缩写,请联系系统管理员";
        }
        if (StringUtils.isBlank(vo.getPlatformCode())) {
            return "平台编码不能为空，请传入平台编码,请联系系统管理员";
        }
        String xl = managements.get(0).getLineSx();
        String sjblh1;

        Shifmanagement shifmanagement = new Shifmanagement();
        shifmanagement.setPlatformCode(vo.getPlatformCode());
        shifmanagement.setShiftId(vo.getShiftNo());
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(shifmanagement);
        if (CollUtil.isEmpty(shifmanagements)) {
            return "未查询到该台账班次,请联系系统管理员";
        }
        if (StrUtil.isBlank(shifmanagements.get(0).getTrip())) {
            return "该班次没有维护去程/回程,请联系系统管理员";
        }
        String stationCode = null;
        if ("G".equals(shifmanagements.get(0).getTrip())) {
            stationCode = shifmanagements.get(0).getDestinationCode();
        } else if ("R".equals(shifmanagements.get(0).getTrip())) {
            stationCode = shifmanagements.get(0).getDestinationCode2();
        }
        if (StrUtil.isBlank(stationCode)) {
            return "该班次发到站信息维护不完善,请联系系统管理员";
        }
        StationManagement stationManagement = new StationManagement();
        stationManagement.setStationCode(stationCode);
        stationManagement.setDeleteFlag("N");
        List<StationManagement> stationManagements = stationManagementMapper.selectStationManagementList(stationManagement);
        if (CollUtil.isEmpty(stationManagements)) {
            if (StrUtil.isBlank(stationCode)) {
                return "未查询到该站点信息,请联系系统管理员";
            }
        }
        if (StrUtil.isBlank(stationManagements.get(0).getCity())) {
            return "该站点所属城市信息维护不完善,请联系系统管理员";
        }

        String result = remoteAdminService.selectSysDicts("省级班列号助记码专用");
        List<SysDictVo> citys = JSONUtil.toList(JSONUtil.parseArray(result), SysDictVo.class);
        if (CollUtil.isEmpty(citys)) {
            return "未查询到该城市助记码,请联系系统管理员";
        }

        String remarkCode = null;
        for (SysDictVo sysDictVo : citys
        ) {
            if (stationManagements.get(0).getCity().equals(sysDictVo.getName())) {
                remarkCode = sysDictVo.getCode();
                break;
            }
        }

        if (StrUtil.isBlank(remarkCode)) {
            return "未查询到该城市助记码,请联系系统管理员";
        }

        if (vo.getShippingTime() == null) {
            return "该台账没有发运日期,请联系系统管理员";
        }
        // 定义日期时间格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yy-MM");

        // 格式化 LocalDateTime 为两位长度的年和月
        String formattedDate = vo.getShippingTime().format(formatter);
        String year = formattedDate.split("-")[0];
        String month = formattedDate.split("-")[1];
        //获取编码配置
        sjblh1 = xl + remarkCode + year + month;
        sjblh.append(sjblh1);
        //获取已有省级班列号最大值
        String trip;
        if ("G".equals(vo.getTrip())) {
            trip = "Z";
        } else {
            trip = "H";
        }
        synchronized (this) {
            List<Integer> list = shifmanagementMapper.selectListByProvinceShiftNo(sjblh1, trip);
            if (CollUtil.isNotEmpty(list)) {
                Integer index = 1;
                while (index <= 500 && list.contains(index)) {
                    index++;
                }
                if (index > 500) {
                    return "省级班列号生成异常,请联系系统管理员:" + sjblh + "XX" + vo.getTrip();
                }
                sjblh.append(String.format("%02d", index));
            } else {
                sjblh.append("01");
            }
            if ("G".equals(vo.getTrip())) {
                sjblh.append("Z");
            } else {
                sjblh.append("H");
            }
            //更新班次中省级班列号
            shifmanagementMapper.updateProvinceShiftNoNew(null, sjblh.toString(), vo.getShiftNo());
        }

        log.info(sjblh.toString());
        return sjblh.toString();
    }

}
