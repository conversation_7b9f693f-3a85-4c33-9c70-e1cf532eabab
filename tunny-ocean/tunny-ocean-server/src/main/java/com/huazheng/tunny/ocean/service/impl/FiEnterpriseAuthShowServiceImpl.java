package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FiEnterpriseAuthShowMapper;
import com.huazheng.tunny.ocean.api.entity.FiEnterpriseAuthShow;
import com.huazheng.tunny.ocean.service.FiEnterpriseAuthShowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fiEnterpriseAuthShowService")
public class FiEnterpriseAuthShowServiceImpl extends ServiceImpl<FiEnterpriseAuthShowMapper, FiEnterpriseAuthShow> implements FiEnterpriseAuthShowService {

    @Autowired
    private FiEnterpriseAuthShowMapper fiEnterpriseAuthShowMapper;

    public FiEnterpriseAuthShowMapper getFiEnterpriseAuthShowMapper() {
        return fiEnterpriseAuthShowMapper;
    }

    public void setFiEnterpriseAuthShowMapper(FiEnterpriseAuthShowMapper fiEnterpriseAuthShowMapper) {
        this.fiEnterpriseAuthShowMapper = fiEnterpriseAuthShowMapper;
    }

    /**
     * 查询经营信息授权-企业授权展示表信息
     *
     * @param rowId 经营信息授权-企业授权展示表ID
     * @return 经营信息授权-企业授权展示表信息
     */
    @Override
    public FiEnterpriseAuthShow selectFiEnterpriseAuthShowById(String rowId)
    {
        return fiEnterpriseAuthShowMapper.selectFiEnterpriseAuthShowById(rowId);
    }

    /**
     * 查询经营信息授权-企业授权展示表列表
     *
     * @param fiEnterpriseAuthShow 经营信息授权-企业授权展示表信息
     * @return 经营信息授权-企业授权展示表集合
     */
    @Override
    public List<FiEnterpriseAuthShow> selectFiEnterpriseAuthShowList(FiEnterpriseAuthShow fiEnterpriseAuthShow)
    {
        return fiEnterpriseAuthShowMapper.selectFiEnterpriseAuthShowList(fiEnterpriseAuthShow);
    }

    @Override
    public List<FiEnterpriseAuthShow> selectFiEnterpriseAuthShowByEntSocialCode(FiEnterpriseAuthShow fiEnterpriseAuthShow) {
        return fiEnterpriseAuthShowMapper.selectFiEnterpriseAuthShowByEntSocialCode(fiEnterpriseAuthShow);
    }


    /**
     * 分页模糊查询经营信息授权-企业授权展示表列表
     * @return 经营信息授权-企业授权展示表集合
     */
    @Override
    public Page selectFiEnterpriseAuthShowListByLike(Query query)
    {
        FiEnterpriseAuthShow fiEnterpriseAuthShow =  BeanUtil.mapToBean(query.getCondition(), FiEnterpriseAuthShow.class,false);
        fiEnterpriseAuthShow.setDeleteFlag("N");
        query.setRecords(fiEnterpriseAuthShowMapper.selectFiEnterpriseAuthShowListByLike(query,fiEnterpriseAuthShow));
        return query;
    }

    /**
     * 分页模糊查询经营信息授权-企业授权展示表列表-市平台
     * @return 经营信息授权-企业授权展示表集合-市平台
     */
    @Override
    public Page selectFiEnterpriseAuthShowListForCity(Query query)
    {
        FiEnterpriseAuthShow fiEnterpriseAuthShow =  BeanUtil.mapToBean(query.getCondition(), FiEnterpriseAuthShow.class,false);
        fiEnterpriseAuthShow.setDeleteFlag("N");
        query.setRecords(fiEnterpriseAuthShowMapper.selectFiEnterpriseAuthShowListForCity(query,fiEnterpriseAuthShow));
        return query;
    }

    /**
     * 分页模糊查询经营信息授权-企业授权展示表列表-省平台
     * @return 经营信息授权-企业授权展示表集合-省平台
     */
    @Override
    public Page selectFiEnterpriseAuthShowListForProvince(Query query)
    {
        FiEnterpriseAuthShow fiEnterpriseAuthShow =  BeanUtil.mapToBean(query.getCondition(), FiEnterpriseAuthShow.class,false);
        fiEnterpriseAuthShow.setDeleteFlag("N");
        query.setRecords(fiEnterpriseAuthShowMapper.selectFiEnterpriseAuthShowListForProvince(query,fiEnterpriseAuthShow));
        return query;
    }

    /**
     * 新增经营信息授权-企业授权展示表
     *
     * @param fiEnterpriseAuthShow 经营信息授权-企业授权展示表信息
     * @return 结果
     */
    @Override
    public int insertFiEnterpriseAuthShow(FiEnterpriseAuthShow fiEnterpriseAuthShow)
    {
        return fiEnterpriseAuthShowMapper.insertFiEnterpriseAuthShow(fiEnterpriseAuthShow);
    }

    /**
     * 修改经营信息授权-企业授权展示表
     *
     * @param fiEnterpriseAuthShow 经营信息授权-企业授权展示表信息
     * @return 结果
     */
    @Override
    public int updateFiEnterpriseAuthShow(FiEnterpriseAuthShow fiEnterpriseAuthShow)
    {
        return fiEnterpriseAuthShowMapper.updateFiEnterpriseAuthShow(fiEnterpriseAuthShow);
    }


    /**
     * 删除经营信息授权-企业授权展示表
     *
     * @param rowId 经营信息授权-企业授权展示表ID
     * @return 结果
     */
    public int deleteFiEnterpriseAuthShowById(String rowId)
    {
        return fiEnterpriseAuthShowMapper.deleteFiEnterpriseAuthShowById( rowId);
    }


    /**
     * 批量删除经营信息授权-企业授权展示表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiEnterpriseAuthShowByIds(Integer[] rowIds)
    {
        return fiEnterpriseAuthShowMapper.deleteFiEnterpriseAuthShowByIds( rowIds);
    }

    @Override
    public void cancelAuth(FiEnterpriseAuthShow fiEnterpriseAuthShow){
        fiEnterpriseAuthShowMapper.cancelAuth(fiEnterpriseAuthShow);
    }

}
