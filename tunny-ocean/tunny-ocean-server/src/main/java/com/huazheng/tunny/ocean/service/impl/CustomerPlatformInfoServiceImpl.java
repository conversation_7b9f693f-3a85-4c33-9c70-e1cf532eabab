package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.api.dto.KvDTO;
import com.huazheng.tunny.ocean.mapper.CustomerPlatformInfoMapper;
import com.huazheng.tunny.ocean.api.entity.CustomerPlatformInfo;
import com.huazheng.tunny.ocean.service.CustomerPlatformInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("customerPlatformInfoService")
public class CustomerPlatformInfoServiceImpl extends ServiceImpl<CustomerPlatformInfoMapper, CustomerPlatformInfo> implements CustomerPlatformInfoService {

    @Autowired
    private CustomerPlatformInfoMapper customerPlatformInfoMapper;

    public CustomerPlatformInfoMapper getCustomerPlatformInfoMapper() {
        return customerPlatformInfoMapper;
    }

    public void setCustomerPlatformInfoMapper(CustomerPlatformInfoMapper customerPlatformInfoMapper) {
        this.customerPlatformInfoMapper = customerPlatformInfoMapper;
    }

    @Value("${db.database}")
    private String database;

    /**
     * 查询订舱客户签约平台子表信息
     *
     * @param rowId 订舱客户签约平台子表ID
     * @return 订舱客户签约平台子表信息
     */
    @Override
    public CustomerPlatformInfo selectCustomerPlatformInfoById(String rowId)
    {
        return customerPlatformInfoMapper.selectCustomerPlatformInfoById(rowId);
    }

    /**
     * 查询订舱客户签约平台子表列表
     *
     * @param customerPlatformInfo 订舱客户签约平台子表信息
     * @return 订舱客户签约平台子表集合
     */
    @Override
    public List<CustomerPlatformInfo> selectCustomerPlatformInfoList(CustomerPlatformInfo customerPlatformInfo)
    {
        return customerPlatformInfoMapper.selectCustomerPlatformInfoList(customerPlatformInfo);
    }

    /**
     * 根据平台查询用户编码
     * @param customerPlatformInfo
     * @return
     */
    @Override
    public List<String> selectCustomerNoByPlatform(CustomerPlatformInfo customerPlatformInfo){
        return customerPlatformInfoMapper.selectCustomerNoByPlatform(customerPlatformInfo);
    }

    @Override
    public List<CustomerPlatformInfo> selectCustomerNoByCode(CustomerPlatformInfo customerPlatformInfo) {
        return customerPlatformInfoMapper.selectCustomerNoByCode(customerPlatformInfo);
    }

    /**
     * 根据平台查询订舱客户编码
     * @param customerPlatformInfo
     * @return
     */
    @Override
    public List<String> selectBookCustomerNoByPlatform(CustomerPlatformInfo customerPlatformInfo){
        return customerPlatformInfoMapper.selectBookCustomerNoByPlatform(customerPlatformInfo);
    }

    @Override
    public List<String> selectBookCustomerNoListByPlatform(CustomerPlatformInfo customerPlatformInfo){
        return customerPlatformInfoMapper.selectBookCustomerNoListByPlatform(customerPlatformInfo);
    }

    @Override
    public CustomerPlatformInfo selectPlatformCodeByCustomerCode(CustomerPlatformInfo customerPlatformInfo) {
        return customerPlatformInfoMapper.selectPlatformCodeByCustomerCode(customerPlatformInfo);
    }

    /**
     * 根据平台查询子账户
     * @param createBy
     * @return
     */
    @Override
    public String selectSubByPlatform(String createBy){
        Map map=new HashMap<String,String>();
        map.put("createBy",createBy);
        map.put("database",database);
        return customerPlatformInfoMapper.selectSubByPlatform(map);
    }

    /**
     * 根据订舱平台查询所属市平台
     */


    /**
     * 分页模糊查询订舱客户签约平台子表列表
     * @return 订舱客户签约平台子表集合
     */
    @Override
    public Page selectCustomerPlatformInfoListByLike(Query query)
    {
        CustomerPlatformInfo customerPlatformInfo =  BeanUtil.mapToBean(query.getCondition(), CustomerPlatformInfo.class,false);
        query.setRecords(customerPlatformInfoMapper.selectCustomerPlatformInfoListByLike(query,customerPlatformInfo));
        return query;
    }

    /**
     * 根据平台编码查询客户键值对
     * @param customerPlatformInfo
     * @return
     */
    @Override
    public List<KvDTO> selectKvByPlatform(CustomerPlatformInfo customerPlatformInfo){
        return customerPlatformInfoMapper.selectKvByPlatform(customerPlatformInfo);
    }

    /**
     * 新增订舱客户签约平台子表
     *
     * @param customerPlatformInfo 订舱客户签约平台子表信息
     * @return 结果
     */
    @Override
    public int insertCustomerPlatformInfo(CustomerPlatformInfo customerPlatformInfo)
    {
        return customerPlatformInfoMapper.insertCustomerPlatformInfo(customerPlatformInfo);
    }

    @Override
    public int insertCustomerPlatformInfo1(CustomerPlatformInfo customerPlatformInfo)
    {
        return customerPlatformInfoMapper.insertCustomerPlatformInfo1(customerPlatformInfo);
    }

    /**
     * 修改订舱客户签约平台子表
     *
     * @param customerPlatformInfo 订舱客户签约平台子表信息
     * @return 结果
     */
    @Override
    public int updateCustomerPlatformInfo(CustomerPlatformInfo customerPlatformInfo)
    {
        return customerPlatformInfoMapper.updateCustomerPlatformInfo(customerPlatformInfo);
    }

    @Override
    public int updateCustomerPlatformInfoByNo(CustomerPlatformInfo customerPlatformInfo)
    {
        return customerPlatformInfoMapper.updateCustomerPlatformInfoByNo(customerPlatformInfo);
    }



    /**
     * 删除订舱客户签约平台子表
     *
     * @param rowId 订舱客户签约平台子表ID
     * @return 结果
     */
    public int deleteCustomerPlatformInfoById(String rowId)
    {
        return customerPlatformInfoMapper.deleteCustomerPlatformInfoById( rowId);
    };


    /**
     * 批量删除订舱客户签约平台子表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteCustomerPlatformInfoByIds(Integer[] rowIds)
    {
        return customerPlatformInfoMapper.deleteCustomerPlatformInfoByIds( rowIds);
    }

}
