package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.Audiopinion;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 审核意见表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-06-30 15:14:18
 */
public interface AudiopinionService extends IService<Audiopinion> {
    /**
     * 查询审核意见表信息
     *
     * @param rowId 审核意见表ID
     * @return 审核意见表信息
     */
    public Audiopinion selectAudiopinionById(String rowId);

    /**
     * 查询审核意见表列表
     *
     * @param audiopinion 审核意见表信息
     * @return 审核意见表集合
     */
    public List<Audiopinion> selectAudiopinionList(Audiopinion audiopinion);


    /**
     * 分页模糊查询审核意见表列表
     * @return 审核意见表集合
     */
    public Page selectAudiopinionListByLike(Query query);



    /**
     * 新增审核意见表
     *
     * @param audiopinion 审核意见表信息
     * @return 结果
     */
    public int insertAudiopinion(Audiopinion audiopinion);

    /**
     * 修改审核意见表
     *
     * @param audiopinion 审核意见表信息
     * @return 结果
     */
    public int updateAudiopinion(Audiopinion audiopinion);

    /**
     * 删除审核意见表
     *
     * @param rowId 审核意见表ID
     * @return 结果
     */
    public int deleteAudiopinionById(String rowId);

    /**
     * 批量删除审核意见表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteAudiopinionByIds(Integer[] rowIds);

}

