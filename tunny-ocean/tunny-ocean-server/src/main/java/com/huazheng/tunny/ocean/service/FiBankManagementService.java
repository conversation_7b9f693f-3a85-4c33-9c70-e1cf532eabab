package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.FiBankManagement;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;
import java.util.Map;

/**
 *  服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-04-14 15:07:55
 */
public interface FiBankManagementService extends IService<FiBankManagement> {
    /**
     * 查询信息
     *
     * @param id ID
     * @return 信息
     */
    public FiBankManagement selectFiBankManagementById(Integer id);

    /**
     * 查询列表
     *
     * @param fiBankManagement 信息
     * @return 集合
     */
    public List<FiBankManagement> selectFiBankManagementList(FiBankManagement fiBankManagement);

    public List<FiBankManagement> selectFiBankManagementList2(FiBankManagement fiBankManagement);

    public R selectHeadBankName(Map<String, Object> params);

    public R selectBankName(Map<String, Object> params);


    /**
     * 分页模糊查询列表
     * @return 集合
     */
    public Page selectFiBankManagementListByLike(Query query);



    /**
     * 新增
     *
     * @param fiBankManagement 信息
     * @return 结果
     */
    public int insertFiBankManagement(FiBankManagement fiBankManagement);

    /**
     * 修改
     *
     * @param fiBankManagement 信息
     * @return 结果
     */
    public int updateFiBankManagement(FiBankManagement fiBankManagement);

    /**
     * 删除
     *
     * @param id ID
     * @return 结果
     */
    public int deleteFiBankManagementById(Integer id);

    /**
     * 批量删除
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiBankManagementByIds(Integer[] ids);

}

