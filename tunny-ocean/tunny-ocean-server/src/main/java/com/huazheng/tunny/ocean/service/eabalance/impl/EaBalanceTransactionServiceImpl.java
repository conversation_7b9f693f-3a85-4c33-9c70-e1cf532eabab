package com.huazheng.tunny.ocean.service.eabalance.impl;

import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.mapper.eabalance.EaBalanceTransactionMapper;
import com.huazheng.tunny.ocean.api.entity.eabalance.EaBalanceTransaction;
import com.huazheng.tunny.ocean.service.eabalance.EaBalanceTransactionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service("eaBalanceTransactionService")
public class EaBalanceTransactionServiceImpl extends ServiceImpl<EaBalanceTransactionMapper, EaBalanceTransaction> implements EaBalanceTransactionService {

    @Autowired
    private EaBalanceTransactionMapper eaBalanceTransactionMapper;

    /**
     * 查询余额流水表信息
     *
     * @param transactionId 余额流水表ID
     * @return 余额流水表信息
     */
    @Override
    public EaBalanceTransaction selectEaBalanceTransactionById(Long transactionId) {
        return eaBalanceTransactionMapper.selectEaBalanceTransactionById(transactionId);
    }

    /**
     * 查询余额流水表列表
     *
     * @param eaBalanceTransaction 余额流水表信息
     * @return 余额流水表集合
     */
    @Override
    public List<EaBalanceTransaction> selectEaBalanceTransactionList(EaBalanceTransaction eaBalanceTransaction) {
        return eaBalanceTransactionMapper.selectEaBalanceTransactionList(eaBalanceTransaction);
    }


    /**
     * 分页模糊查询余额流水表列表
     *
     * @return 余额流水表集合
     */
    @Override
    public Page selectEaBalanceTransactionListByLike(Query query) {
        EaBalanceTransaction eaBalanceTransaction = BeanUtil.mapToBean(query.getCondition(), EaBalanceTransaction.class, false);
        query.setRecords(eaBalanceTransactionMapper.selectEaBalanceTransactionListByLike(query, eaBalanceTransaction));
        return query;
    }

    /**
     * 新增余额流水表
     *
     * @param eaBalanceTransaction 余额流水表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R insertEaBalanceTransaction(EaBalanceTransaction eaBalanceTransaction) {
        eaBalanceTransactionMapper.insertEaBalanceTransaction(eaBalanceTransaction);
        return R.success();
    }

    /**
     * 修改余额流水表
     *
     * @param eaBalanceTransaction 余额流水表信息
     * @return 结果
     */
    @Override
    public int updateEaBalanceTransaction(EaBalanceTransaction eaBalanceTransaction) {
        return eaBalanceTransactionMapper.updateEaBalanceTransaction(eaBalanceTransaction);
    }


    /**
     * 删除余额流水表
     *
     * @param transactionId 余额流水表ID
     * @return 结果
     */
    @Override
    public int deleteEaBalanceTransactionById(Long transactionId) {
        return eaBalanceTransactionMapper.deleteEaBalanceTransactionById(transactionId);
    }

    /**
     * 批量删除余额流水表对象
     *
     * @param transactionIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEaBalanceTransactionByIds(Integer[] transactionIds) {
        return eaBalanceTransactionMapper.deleteEaBalanceTransactionByIds(transactionIds);
    }

}
