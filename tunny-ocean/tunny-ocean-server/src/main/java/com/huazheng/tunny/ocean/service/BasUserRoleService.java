package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.BasUserRole;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 用户角色关联表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-01 09:20:08
 */
public interface BasUserRoleService extends IService<BasUserRole> {
    /**
     * 查询用户角色关联表信息
     *
     * @param rowId 用户角色关联表ID
     * @return 用户角色关联表信息
     */
    public BasUserRole selectBasUserRoleById(String rowId);

    /**
     * 查询用户角色关联表列表
     *
     * @param basUserRole 用户角色关联表信息
     * @return 用户角色关联表集合
     */
    public List<BasUserRole> selectBasUserRoleList(BasUserRole basUserRole);


    /**
     * 分页模糊查询用户角色关联表列表
     * @return 用户角色关联表集合
     */
    public Page selectBasUserRoleListByLike(Query query);



    /**
     * 新增用户角色关联表
     *
     * @param basUserRole 用户角色关联表信息
     * @return 结果
     */
    public int insertBasUserRole(BasUserRole basUserRole);

    /**
     * 修改用户角色关联表
     *
     * @param basUserRole 用户角色关联表信息
     * @return 结果
     */
    public int updateBasUserRole(BasUserRole basUserRole);

    /**
     * 删除用户角色关联表
     *
     * @param rowId 用户角色关联表ID
     * @return 结果
     */
    public int deleteBasUserRoleById(String rowId);

    /**
     * 批量删除用户角色关联表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteBasUserRoleByIds(Integer[] rowIds);

}

