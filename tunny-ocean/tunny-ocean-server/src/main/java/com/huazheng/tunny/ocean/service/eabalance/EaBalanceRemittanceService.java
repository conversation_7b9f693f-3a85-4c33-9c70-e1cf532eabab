package com.huazheng.tunny.ocean.service.eabalance;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.eabalance.EaBalanceRemittance;

import java.util.List;

/**
 * 余额汇款表 服务接口层
 *
 * <AUTHOR>
 * @date 2025-07-01 13:37:37
 */
public interface EaBalanceRemittanceService extends IService<EaBalanceRemittance> {
    /**
     * 查询余额汇款表信息
     *
     * @param transactionId 余额汇款表ID
     * @return 余额汇款表信息
     */
    public EaBalanceRemittance selectEaBalanceRemittanceById(Long transactionId);

    /**
     * 查询余额汇款表列表
     *
     * @param eaBalanceRemittance 余额汇款表信息
     * @return 余额汇款表集合
     */
    public List<EaBalanceRemittance> selectEaBalanceRemittanceList(EaBalanceRemittance eaBalanceRemittance);


    /**
     * 分页模糊查询余额汇款表列表
     * @return 余额汇款表集合
     */
    public Page selectEaBalanceRemittanceListByLike(Query query);



    /**
     * 新增余额汇款表
     *
     * @param eaBalanceRemittance 余额汇款表信息
     * @return 结果
     */
    public R insertEaBalanceRemittance(EaBalanceRemittance eaBalanceRemittance);

    /**
     * 修改余额汇款表
     *
     * @param eaBalanceRemittance 余额汇款表信息
     * @return 结果
     */
    public R updateEaBalanceRemittance(EaBalanceRemittance eaBalanceRemittance);

    /**
     * 删除余额汇款表
     *
     * @param transactionId 余额汇款表ID
     * @return 结果
     */
    public int deleteEaBalanceRemittanceById(Long transactionId);

    /**
     * 批量删除余额汇款表
     *
     * @param transactionIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEaBalanceRemittanceByIds(Integer[] transactionIds);
    /**
     * 认领/取消认领
     * @param eaBalanceRemittance
     * @return R
     */
    R claimTransactionList(EaBalanceRemittance eaBalanceRemittance);
}

