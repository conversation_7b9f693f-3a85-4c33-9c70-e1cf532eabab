package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.ShiftManagementWechatDTO;
import com.huazheng.tunny.ocean.api.dto.ShippingPlanReportDTO;
import com.huazheng.tunny.ocean.api.entity.Shifmanagement;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.vo.ShifmanagementVO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 班次管理 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-02 14:46:01
 */
public interface ShifmanagementService extends IService<Shifmanagement> {

    /**
     * 查询班次及下属关联站点信息
     *
     * @param rowId 班次管理ID
     * @return 班次管理及下属关联站点信息
     */
    public Shifmanagement selectShiftDetail(String rowId);

    /**
     * 查询班次管理信息
     *
     * @param rowId 班次管理ID
     * @return 班次管理信息
     */
    public Shifmanagement selectShifmanagementById(String rowId);

    /**
     * 查询班次管理列表
     *
     * @param shifmanagement 班次管理信息
     * @return 班次管理集合
     */
    public List<Shifmanagement> selectShifmanagementList(Shifmanagement shifmanagement);

    /**
     * 查询班次日历修改后
     *
     * @param shifmanagement 班次管理信息
     * @return 班次管理集合
     */
    public List<ShifmanagementVO> findShiftCan(Shifmanagement shifmanagement);

    public List<ShifmanagementVO> findShiftCanList(Shifmanagement shifmanagement);

    public List<Shifmanagement> findShiftCanForSh(Shifmanagement shifmanagement);


    /**
     * 分页模糊查询班次客户信息
     * @return 班次客户信息
     */
    public Page findSpaceNums(Query query);

    /**
     * 分页模糊查询班次管理列表
     * @return 班次管理集合
     */
    public Page selectShifmanagementListByLike(Query query);

    public Page findShiftCanPage(Query query);

    public Integer findShiftCanCount(Shifmanagement shifmanagement);

    public Page selectShifmanagementListByLike2(Query query);

    public R countForPage(Query query);
    public Integer selectShifmanagementListByLikeCount (Shifmanagement shifmanagement);


    /**
     * 新增班次管理
     *
     * @param shifmanagement 班次管理信息
     * @return 结果
     */
    public R insertShifmanagement(Shifmanagement shifmanagement);

    /**
     * 修改班次发运时间
     * @param shifmanagement 班次管理信息
     * @return 结果
     */
    public R updateShippingTime(Shifmanagement shifmanagement);

    /**
     * 修改班次管理
     *
     * @param shifmanagement 班次管理信息
     * @return 结果
     */
    public R updateShifmanagement(Shifmanagement shifmanagement);


    public R updatereleaseStatus(Shifmanagement shifmanagement);

    public R shiftRelease(Shifmanagement shifmanagement);


    /**
     * 删除班次管理
     *
     * @param rowId 班次管理ID
     * @return 结果
     */
    public int deleteShifmanagementById(String rowId);

    /**
     * 批量删除班次管理
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteShifmanagementByIds(Integer[] rowIds);

    public Page shippingPlanReportList(Query query);

    public List<ShippingPlanReportDTO> shippingPlanReportList2(Shifmanagement shifmanagement);

    public Integer selectShifmanagementByCount(Shifmanagement shifmanagement);

    public Shifmanagement selectShifmanagementByPortStation(Shifmanagement shifmanagement);

    public R updateShippingLine(Shifmanagement shifmanagement);

    public R updateShiftName(Shifmanagement shifmanagement);

    public String getNum(String shiftId);

    Page selectToChangeList(Query query);

    Page selectToChangeListCity(Query query);

    public R addCustomer(Shifmanagement shifmanagement);

    public R updateShiftNumOfTruePositions(Shifmanagement shifmanagement);

    public Page pageWechat(Query query);

    public R selectCityList(ShiftManagementWechatDTO shiftManagementWechatDTO);

    R customerBookingNums(Map<String,Object> params);


    R updateShiftAndSyncLedger(Shifmanagement shifmanagement);
}

