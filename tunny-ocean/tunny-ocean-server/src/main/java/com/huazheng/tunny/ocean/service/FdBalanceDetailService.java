package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.BalanceDetailDTO;
import com.huazheng.tunny.ocean.api.entity.FdBalanceDetail;
import com.huazheng.tunny.ocean.api.vo.BillBalanceJsListVO;
import com.huazheng.tunny.ocean.api.vo.BillBalanceLjkbListVO;
import com.huazheng.tunny.ocean.api.vo.BillBalanceSkListVO;

import java.util.List;

/**
 * 余额明细表 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:30:20
 */
public interface FdBalanceDetailService extends IService<FdBalanceDetail> {

    public Page selectFdBalanceDetailByLike(Query query);

    public FdBalanceDetail selectFdBalanceDetailListById(Integer id);

    public R pageTotal(Query query);

    public List<FdBalanceDetail> selectFdBalanceDetailList(FdBalanceDetail fdBalanceDetail);

    /**
     * 保存
     *
     * @param fdBalanceDetail
     * @return
     */
    public R insertFdBalanceDetail(FdBalanceDetail fdBalanceDetail);

    public List<FdBalanceDetail> selectRemarksList(FdBalanceDetail fdBalanceDetail);

    public List<FdBalanceDetail> getAccountList(Query query);

    public List<FdBalanceDetail> selectFdBalanceDetailListByShiftId(FdBalanceDetail fdBalanceDetail);

    public int updatefdBalanceDetailByShiftId(FdBalanceDetail fdBalanceDetail);

    public int updateRemainingAmountReturnById(FdBalanceDetail fdBalanceDetail);

    R cancel(Integer id);

    void updateBalanceAmount(BalanceDetailDTO balanceDetailDTO);

    List<BillBalanceJsListVO> selectBillJsList(String billNo);

    List<BillBalanceSkListVO> selectBillSkList(String billNo);

    List<BillBalanceLjkbListVO> selectBillLjkbList(String billNo);

    int selectUpdateBalanceAmountByBalanceCode(String billNo);


    /**
     * 根据客户编码查询余额总数
     *
     * @Param: null
     * @Return:
     * @Author: Howe
     * @Date: 2024/6/24 上午10:29
     **/
    Page selectTotalPageByCustomer(Query query);
}

