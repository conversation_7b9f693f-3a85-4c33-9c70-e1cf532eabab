//package com.huazheng.tunny.ocean.exception.handler;
//
//
//
//import com.huazheng.tunny.common.core.util.R;
//import com.huazheng.tunny.ocean.exception.BizException;
//import com.huazheng.tunny.ocean.exception.RemoteCallException;
//import com.huazheng.tunny.ocean.exception.domain.ErrorMsg;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.converter.HttpMessageNotReadableException;
//import org.springframework.validation.BindException;
//import org.springframework.validation.FieldError;
//import org.springframework.validation.ObjectError;
//import org.springframework.web.bind.MethodArgumentNotValidException;
//import org.springframework.web.bind.annotation.ExceptionHandler;
//import org.springframework.web.bind.annotation.ResponseBody;
//import org.springframework.web.bind.annotation.ResponseStatus;
//import org.springframework.web.bind.annotation.RestControllerAdvice;
//
//import javax.servlet.http.HttpServletRequest;
//import javax.validation.ConstraintViolation;
//import javax.validation.ConstraintViolationException;
//import java.util.List;
//import java.util.stream.Collectors;
//
//
///**
// * @Description: 异常统一处理
// * @Author: Howe
// * @Date: 2019/10/30 15:22
// */
//@RestControllerAdvice
//@Slf4j
//public class GlobalExceptionHandler {
//    private static final Integer ERROR_CODE = -1;
//    private static final String ERROR_MSG = "系统发生异常，请与管理员联系";
//    private static final String PARAM_ERROR_MSG = "参数传值异常，请检查";
//
//    @ExceptionHandler(value = {BizException.class})
//    @ResponseStatus(HttpStatus.OK)
//    public ErrorMsg<String> jsonErrorHandler(HttpServletRequest request, BizException e) {
//        log.info("异常被捕获，执行【{}】时发生异常", request.getRequestURL().toString(), e);
//        ErrorMsg<String> errorInfo = new ErrorMsg<>();
//        errorInfo.setCode(ERROR_CODE);
//        errorInfo.setMsg(e.getMessage());
//        errorInfo.setUrl(request.getRequestURL().toString());
//        return errorInfo;
//    }
//
//    @ExceptionHandler(value = {Exception.class})
//    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
//    public ErrorMsg<String> jsonErrorHandler(HttpServletRequest request, Throwable e) {
//        log.error("异常被捕获，执行【{}】时发生异常", request.getRequestURL().toString(), e);
//        ErrorMsg<String> errorInfo = new ErrorMsg<>();
//        errorInfo.setCode(ERROR_CODE);
//        errorInfo.setMsg(ERROR_MSG);
//        errorInfo.setUrl(request.getRequestURL().toString());
//        errorInfo.setData(e.getMessage());
//        return errorInfo;
//    }
//
//    @ExceptionHandler(value = BindException.class)
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    public ErrorMsg<List<String>> jsonErrorHandler(HttpServletRequest request, BindException exception) {
//        log.error("异常被捕获，执行【{}】时发生异常", request.getRequestURL().toString(), exception);
//        List<String> errorList = exception.getBindingResult().getFieldErrors().stream().map(FieldError::getDefaultMessage).collect(Collectors.toList());
//        return returnErrorMsg(request, errorList);
//    }
//
//    @ExceptionHandler(value = MethodArgumentNotValidException.class)
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    public ErrorMsg<List<String>> jsonErrorHandler(HttpServletRequest request, MethodArgumentNotValidException exception) {
//        log.error("异常被捕获，执行【{}】时发生异常", request.getRequestURL().toString(), exception);
//        List<String> errorList = exception.getBindingResult().getAllErrors().stream().map(ObjectError::getDefaultMessage).collect(Collectors.toList());
//        return returnErrorMsg(request, errorList);
//    }
//
//    @ExceptionHandler(value = ConstraintViolationException.class)
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    public ErrorMsg<List<String>> jsonErrorHandler(HttpServletRequest request, ConstraintViolationException exception) {
//        log.error("异常被捕获，执行【{}】时发生异常", request.getRequestURL().toString(), exception);
//        List<String> errorList = exception.getConstraintViolations().stream().map(ConstraintViolation::getMessage).collect(Collectors.toList());
//        return returnErrorMsg(request, errorList);
//    }
//
//    @ExceptionHandler(value = HttpMessageNotReadableException.class)
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    public Object jsonErrorHandler(HttpServletRequest request, HttpMessageNotReadableException exception) {
//        ErrorMsg<String> errorInfo = new ErrorMsg<>();
//        errorInfo.setCode(ERROR_CODE);
//        errorInfo.setMsg(PARAM_ERROR_MSG);
//        errorInfo.setUrl(request.getRequestURL().toString());
//        errorInfo.setData("Json数据格式错误");
//        return errorInfo;
//    }
//
//
//    @ExceptionHandler(value = RemoteCallException.class)
//    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
//    public ErrorMsg<String> remoteErrorHandler(HttpServletRequest request, RemoteCallException e) {
//        log.error("异常被捕获，执行【{}】时发生异常", request.getRequestURL().toString(), e);
//        ErrorMsg<String> errorInfo = new ErrorMsg<>();
//        errorInfo.setCode(ERROR_CODE);
//        errorInfo.setMsg("远程调用异常" + "e.getMessage()");
//        errorInfo.setData(e.getMessage());
//        errorInfo.setUrl(request.getRequestURL().toString());
//        return errorInfo;
//    }
//
//    /**
//     * @Description: 返回异常信息
//     * @Param: [request, errorList]
//     * @Return: com.thingshive.edge.exception.domain.ErrorMsg<java.util.List < java.lang.String>>
//     * @Author: Howe
//     * @Date: 2019/10/30 15:27
//     */
//    private ErrorMsg<List<String>> returnErrorMsg(HttpServletRequest request, List<String> errorList) {
//        ErrorMsg<List<String>> errorInfo = new ErrorMsg<>();
//        errorInfo.setCode(ERROR_CODE);
//        errorInfo.setMsg(PARAM_ERROR_MSG);
//        errorInfo.setUrl(request.getRequestURL().toString());
//        errorInfo.setData(errorList);
//        return errorInfo;
//    }
//
//    /**
//     * @Description: 捕获 RuntimeException
//     * @param: e
//     * @Return: com.huazheng.tunny.common.core.util.R
//     * @Author: wx
//     * @Date: 2020-07-17 11:45
//     */
//    @ResponseBody
//    @ExceptionHandler(RuntimeException.class)
//    public R exception(RuntimeException e) {
//        return R.error(e.getMessage());
//    }
//}