package com.huazheng.tunny.ocean.controller;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.huazheng.tunny.ocean.api.entity.StoredCompareConvert;
import com.huazheng.tunny.ocean.api.entity.StoredCompareNature;
import com.huazheng.tunny.ocean.service.StoredCompareNatureService;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 省平台-数据同比（进出口、过境、回程）---自然列
 *
 * <AUTHOR> code ocean
 * @date 2021-10-13 13:51:51
 */
@RestController
@RequestMapping("/storedcomparenature")
@Slf4j
public class StoredCompareNatureController {
    @Autowired
    private StoredCompareNatureService storedCompareNatureService;
    @Value("${path.windowsPath}")
    private String windowsPath;
    @Value("${path.linuxPath}")
    private String linuxPath;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  storedCompareNatureService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return storedCompareNatureService.selectStoredCompareNatureListByLike(new Query<>(params));
    }

    @GetMapping("/getList")
    public R getList(StoredCompareNature storedCompareNature) {
        //数据库字段值完整查询
        // return  storedCompareNatureService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        List<StoredCompareNature> list = storedCompareNatureService.selectStoredCompareNatureList(storedCompareNature);
        //对象模糊查询
        return new R<>(200,Boolean.TRUE,list);
    }


    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") Integer rowId) {
        StoredCompareNature storedCompareNature =storedCompareNatureService.selectById(rowId);
        return new R<>(storedCompareNature);
    }

    /**
     * 保存
     * @param storedCompareNature
     * @return R
     */
    @PostMapping
    public R save(@RequestBody StoredCompareNature storedCompareNature) {
        storedCompareNatureService.insert(storedCompareNature);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param storedCompareNature
     * @return R
     */
    @PutMapping
    public R update(@RequestBody StoredCompareNature storedCompareNature) {
        storedCompareNatureService.updateById(storedCompareNature);
        return new R<>(Boolean.TRUE);
    }



    /**
     * 删除
     * @param rowId
     * @return R
     */
    @DeleteMapping("/{rowId}")
    public R delete(@PathVariable  Integer rowId) {
        storedCompareNatureService.deleteById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<Integer> rowIds) {
        storedCompareNatureService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<StoredCompareNature> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = storedCompareNatureService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<StoredCompareNature> list = reader.readAll(StoredCompareNature.class);
        storedCompareNatureService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }

    /**
     * @description 省平台-数据同比（进出口、过境、回程）---自然列导出Excel
     *
     * <AUTHOR> code ocean
     * <AUTHOR> xiu jian
     * @date 2021/10/14 9:34
     */
    @GetMapping("/storedCompareNatureExport")
    public R storedcomparenatureExport(HttpServletResponse res, StoredCompareNature storedCompareNature) throws IOException {

        String year = "";
        if(storedCompareNature!=null && (storedCompareNature.getDate() ==null || "".equals(storedCompareNature.getDate()))){
            year= LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy"));
        }else {
            year = storedCompareNature.getDate().split("-")[0];
        }
        HashMap<String,Object> map = new HashMap<>();
        List<StoredCompareNature> storedCompareNatureList = storedCompareNatureService.selectStoredCompareNatureList(storedCompareNature);


        map.put("year",year);
        map.put("lastYear",Integer.toString(Integer.parseInt(year)-1));
        com.alibaba.excel.ExcelWriter excelWriter=null;
        String templateFileName = "storedCompareNature.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)){
            templateFileName = linuxPath + templateFileName;
        }else{
            templateFileName = windowsPath + templateFileName;
        }
        String fileName = URLEncoder.encode("省平台-数据同比（进出口、过境、回程）导出", "UTF-8").replaceAll("\\+", "%20");
//        res.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        res.setHeader("Content-disposition", "attachment;filename*=" + fileName + ".xlsx");

        excelWriter = EasyExcel.write(res.getOutputStream()).withTemplate(templateFileName).build();

        WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "自然列").build();

        excelWriter.fill(storedCompareNatureList, writeSheet1);
        excelWriter.fill(map, writeSheet1);

        excelWriter.finish();
        return new R<>(Boolean.TRUE);
    }
}
