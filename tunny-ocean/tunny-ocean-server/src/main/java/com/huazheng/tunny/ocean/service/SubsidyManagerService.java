package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.SubsidyManager;

import java.util.List;

/**
 * 补贴管理表 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-07-25 10:46:40
 */
public interface SubsidyManagerService extends IService<SubsidyManager> {
    /**
     * 查询补贴管理表信息
     *
     * @param id 补贴管理表ID
     * @return 补贴管理表信息
     */
    public SubsidyManager selectSubsidyManagerById(Integer id);

    /**
     * 查询补贴管理表列表
     *
     * @param subsidyManager 补贴管理表信息
     * @return 补贴管理表集合
     */
    public List<SubsidyManager> selectSubsidyManagerList(SubsidyManager subsidyManager);


    /**
     * 分页模糊查询补贴管理表列表
     * @return 补贴管理表集合
     */
    public Page selectSubsidyManagerListByLike(Query query);



    /**
     * 新增补贴管理表
     *
     * @param subsidyManager 补贴管理表信息
     * @return 结果
     */
    public R insertSubsidyManager(SubsidyManager subsidyManager);

    /**
     * 修改补贴管理表
     *
     * @param subsidyManager 补贴管理表信息
     * @return 结果
     */
    public R updateSubsidyManager(SubsidyManager subsidyManager);

    /**
     * 删除补贴管理表
     *
     * @param id 补贴管理表ID
     * @return 结果
     */
    public int deleteSubsidyManagerById(Integer id);

    /**
     * 批量删除补贴管理表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSubsidyManagerByIds(Integer[] ids);

}

