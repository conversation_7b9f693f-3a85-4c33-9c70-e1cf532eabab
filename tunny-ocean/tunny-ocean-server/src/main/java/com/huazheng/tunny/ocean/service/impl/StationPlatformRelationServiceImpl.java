package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.StationPlatformRelationMapper;
import com.huazheng.tunny.ocean.api.entity.StationPlatformRelation;
import com.huazheng.tunny.ocean.service.StationPlatformRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("stationPlatformRelationService")
public class StationPlatformRelationServiceImpl extends ServiceImpl<StationPlatformRelationMapper, StationPlatformRelation> implements StationPlatformRelationService {

    @Autowired
    private StationPlatformRelationMapper stationPlatformRelationMapper;

    public StationPlatformRelationMapper getStationPlatformRelationMapper() {
        return stationPlatformRelationMapper;
    }

    public void setStationPlatformRelationMapper(StationPlatformRelationMapper stationPlatformRelationMapper) {
        this.stationPlatformRelationMapper = stationPlatformRelationMapper;
    }

    @Override
    public List<StationPlatformRelation> selectPlatByStationCode(String rowId) {
        return stationPlatformRelationMapper.selectPlatByStationCode(rowId);
    }

    /**
     * 查询站点平台关系表信息
     *
     * @param rowId 站点平台关系表ID
     * @return 站点平台关系表信息
     */
    @Override
    public StationPlatformRelation selectStationPlatformRelationById(String rowId)
    {
        return stationPlatformRelationMapper.selectStationPlatformRelationById(rowId);
    }

    /**
     * 查询站点平台关系表列表
     *
     * @param stationPlatformRelation 站点平台关系表信息
     * @return 站点平台关系表集合
     */
    @Override
    public List<StationPlatformRelation> selectStationPlatformRelationList(StationPlatformRelation stationPlatformRelation)
    {
        return stationPlatformRelationMapper.selectStationPlatformRelationList(stationPlatformRelation);
    }


    /**
     * 分页模糊查询站点平台关系表列表
     * @return 站点平台关系表集合
     */
    @Override
    public Page selectStationPlatformRelationListByLike(Query query)
    {
        StationPlatformRelation stationPlatformRelation =  BeanUtil.mapToBean(query.getCondition(), StationPlatformRelation.class,false);
        query.setRecords(stationPlatformRelationMapper.selectStationPlatformRelationListByLike(query,stationPlatformRelation));
        return query;
    }

    /**
     * 新增站点平台关系表
     *
     * @param stationPlatformRelation 站点平台关系表信息
     * @return 结果
     */
    @Override
    public int insertStationPlatformRelation(List<StationPlatformRelation> stationPlatformRelation)
    {
        return stationPlatformRelationMapper.insertStationPlatformRelation(stationPlatformRelation);
    }

    /**
     * 修改站点平台关系表
     *
     * @param stationPlatformRelation 站点平台关系表信息
     * @return 结果
     */
    @Override
    public int updateStationPlatformRelation(List<StationPlatformRelation> stationPlatformRelation)
    {
        return stationPlatformRelationMapper.updateStationPlatformRelation(stationPlatformRelation);
    }


    /**
     * 删除站点平台关系表
     *
     * @param rowId 站点平台关系表ID
     * @return 结果
     */
    @Override
    public int deleteStationPlatformRelationById(String rowId)
    {
        return stationPlatformRelationMapper.deleteStationPlatformRelationById( rowId);
    };


    /**
     * 批量删除站点平台关系表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteStationPlatformRelationByIds(Integer[] rowIds)
    {
        return stationPlatformRelationMapper.deleteStationPlatformRelationByIds( rowIds);
    }

}
