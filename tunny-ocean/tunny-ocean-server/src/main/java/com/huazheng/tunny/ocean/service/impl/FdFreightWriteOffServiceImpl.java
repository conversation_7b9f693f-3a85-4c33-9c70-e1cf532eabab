package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.dto.FdShippingAccoundetailDTO;
import com.huazheng.tunny.ocean.api.entity.FdFreightAccounting;
import com.huazheng.tunny.ocean.api.entity.FdFreightWriteOff;
import com.huazheng.tunny.ocean.mapper.FdFreightAccountingMapper;
import com.huazheng.tunny.ocean.mapper.FdFreightWriteOffMapper;
import com.huazheng.tunny.ocean.service.FdFreightAccountingService;
import com.huazheng.tunny.ocean.service.FdFreightWriteOffService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Service("fdFreightWriteOffService")
public class FdFreightWriteOffServiceImpl extends ServiceImpl<FdFreightWriteOffMapper, FdFreightWriteOff> implements FdFreightWriteOffService {

    @Autowired
    private FdFreightWriteOffMapper fdFreightWriteOffMapper;

    public FdFreightWriteOffMapper getFdFreightWriteOffMapper() {
        return fdFreightWriteOffMapper;
    }

    public void setFdFreightWriteOffMapper(FdFreightWriteOffMapper fdFreightWriteOffMapper) {
        this.fdFreightWriteOffMapper = fdFreightWriteOffMapper;
    }

    public Page getFdFreightWriteOffList(Query query) {
        FdFreightWriteOff fdFreightWriteOff = BeanUtil.mapToBean(query.getCondition(), FdFreightWriteOff.class, false);
        query.setRecords(fdFreightWriteOffMapper.getFdFreightWriteOffList(query, fdFreightWriteOff));
        query.setTotal(fdFreightWriteOffMapper.getFdFreightWriteOffListCount(fdFreightWriteOff));
        return query;
    }


    public FdFreightWriteOff getFdFreightWriteOffListTotal(Query query) {
        FdFreightWriteOff fdFreightWriteOff = BeanUtil.mapToBean(query.getCondition(), FdFreightWriteOff.class, false);
        List<FdFreightWriteOff> fdFreightWriteOffListTotal = fdFreightWriteOffMapper.getFdFreightWriteOffListTotal(fdFreightWriteOff);
        FdFreightWriteOff off = new FdFreightWriteOff();
        BigDecimal overseasFreightOc = BigDecimal.valueOf(0);
        BigDecimal overseasFreightCny = BigDecimal.valueOf(0);
        BigDecimal domesticFreight = BigDecimal.valueOf(0);
        BigDecimal writtenOff = BigDecimal.valueOf(0);
        BigDecimal notWrittenOff = BigDecimal.valueOf(0);
        for (FdFreightWriteOff o:fdFreightWriteOffListTotal
             ) {
            if(o.getOverseasFreightOc()!=null){
                overseasFreightOc = overseasFreightOc.add(o.getOverseasFreightOc());
            }
            if(o.getOverseasFreightCny()!=null){
                overseasFreightCny = overseasFreightCny.add(o.getOverseasFreightCny());
            }
            if(o.getDomesticFreight()!=null){
                domesticFreight = domesticFreight.add(o.getDomesticFreight());
            }
            if (o.getDomesticInvoice() != null && !"".equals(o.getDomesticInvoice())) {
                if ("已核销".equals(o.getDomesticInvoice())) {
                    if (o.getOverseasFreightCny() != null) {
                        writtenOff = writtenOff.add(o.getOverseasFreightCny());
                    }
                    if (o.getDomesticFreight() != null) {
                        writtenOff = writtenOff.add(o.getDomesticFreight());
                    }
                } else if ("未核销".equals(o.getDomesticInvoice())) {
                    if (o.getOverseasFreightCny() != null) {
                        notWrittenOff = notWrittenOff.add(o.getOverseasFreightCny());
                    }
                    if (o.getDomesticFreight() != null) {
                        notWrittenOff = notWrittenOff.add(o.getDomesticFreight());
                    }
                }
            }
        }
        off.setOverseasFreightOc(overseasFreightOc);
        off.setOverseasFreightCny(overseasFreightCny);
        off.setDomesticFreight(domesticFreight);
        off.setWrittenOff(writtenOff);
        off.setNotWrittenOff(notWrittenOff);
        return off;
    }
}
