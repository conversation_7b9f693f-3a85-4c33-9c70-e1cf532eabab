package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.EfFinancingGoods;

import java.util.List;

/**
 * 仓单融资货物表 服务接口层
 *
 * <AUTHOR>
 * @date 2023-02-06 14:10:43
 */
public interface EfFinancingGoodsService extends IService<EfFinancingGoods> {
    /**
     * 查询仓单融资货物表信息
     *
     * @param rowId 仓单融资货物表ID
     * @return 仓单融资货物表信息
     */
    public EfFinancingGoods selectEfFinancingGoodsById(String rowId);

    /**
     * 查询仓单融资货物表列表
     *
     * @param efFinancingGoods 仓单融资货物表信息
     * @return 仓单融资货物表集合
     */
    public List<EfFinancingGoods> selectEfFinancingGoodsList(EfFinancingGoods efFinancingGoods);


    /**
     * 分页模糊查询仓单融资货物表列表
     * @return 仓单融资货物表集合
     */
    public Page selectEfFinancingGoodsListByLike(Query query);



    /**
     * 新增仓单融资货物表
     *
     * @param efFinancingGoods 仓单融资货物表信息
     * @return 结果
     */
    public int insertEfFinancingGoods(EfFinancingGoods efFinancingGoods);

    /**
     * 修改仓单融资货物表
     *
     * @param efFinancingGoods 仓单融资货物表信息
     * @return 结果
     */
    public int updateEfFinancingGoods(EfFinancingGoods efFinancingGoods);

    /**
     * 删除仓单融资货物表
     *
     * @param rowId 仓单融资货物表ID
     * @return 结果
     */
    public int deleteEfFinancingGoodsById(String rowId);

    /**
     * 批量删除仓单融资货物表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfFinancingGoodsByIds(Integer[] rowIds);

}

