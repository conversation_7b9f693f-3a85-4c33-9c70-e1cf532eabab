package com.huazheng.tunny.ocean.controller;

import com.huazheng.tunny.ocean.api.entity.FiWhFinancingGoods;
import com.huazheng.tunny.ocean.service.FiWhFinancingGoodsService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 仓单质押融资货物表
 *
 * <AUTHOR>
 * @date 2022-11-10 15:50:39
 */
@Slf4j
@RestController
@RequestMapping("/fiwhfinancinggoods")
public class FiWhFinancingGoodsController {

    @Autowired
    private FiWhFinancingGoodsService fiWhFinancingGoodsService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fiWhFinancingGoodsService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fiWhFinancingGoodsService.selectFiWhFinancingGoodsListByLike(new Query<>(params));
    }

    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        FiWhFinancingGoods fiWhFinancingGoods =fiWhFinancingGoodsService.selectById(rowId);
        return new R<>(fiWhFinancingGoods);
    }

    /**
     * 保存
     * @param fiWhFinancingGoods
     * @return R
     */
    @PostMapping
    public R save(@RequestBody FiWhFinancingGoods fiWhFinancingGoods) {
        fiWhFinancingGoodsService.insert(fiWhFinancingGoods);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param fiWhFinancingGoods
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody FiWhFinancingGoods fiWhFinancingGoods) {
        fiWhFinancingGoodsService.updateById(fiWhFinancingGoods);
        return new R<>(Boolean.TRUE);
    }

    

    /**
     * 删除
     * @param rowId
     * @return R
     */
    @GetMapping("/del/{rowId}")
    public R delete(@PathVariable  String rowId) {
        fiWhFinancingGoodsService.deleteById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        fiWhFinancingGoodsService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<FiWhFinancingGoods> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<FiWhFinancingGoods> list = fiWhFinancingGoodsService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), FiWhFinancingGoods.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    /*@PostMapping("/imported")
    @Transactional
    public R imported(String titleStr, MultipartFile file) throws Exception {
        
        List list = new ArrayList();
        try {
            EasyExcel.read(file.getInputStream(), FiWhFinancingGoods.class, new EasyExcelListener<FiWhFinancingGoods>() {
                //数据处理逻辑，需要实现数据校验必须重写父级的invoke方法
                @Override
                public void invoke(FiWhFinancingGoods entity, AnalysisContext analysisContext) {
                    //log.info("解析到一条数据:{}", JSON.toJSONString(excelItem));
                    //数据校验逻辑
                    //String name = entity.getId();
                    //if (name.length()>10) {
                    //    throw new RuntimeException(String.format("第%s行错误，名称过长", analysisContext.readRowHolder().getRowIndex() + 1));
                    //}
                    //每读取1000条数据保存一次
                    list.add(entity);
                    if (list.size() >= 1000) {
                        saveData(list);
                        list.clear();
                    }
                }

                *//**
                 * 所有数据解析完成了就会来调用，确保最后遗留的数据也存储到数据库
                 *//*
                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    saveData(list);
                    list.clear();
                }

                //数据批量保存逻辑
                @Override
                public void saveData(List<FiWhFinancingGoods> infoList) {
                        fiWhFinancingGoodsService.insertBatch(infoList);
                }
                //headRowNumber()声明标题行占用行数
            }).headRowNumber(1).sheet().doRead();
        }catch (Exception e){
            e.printStackTrace();
            return new R<>(Boolean.FALSE, e.getMessage());
        }
        return new R<>(Boolean.TRUE);
    }*/
}
