package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FiPledgeGoodsMapper;
import com.huazheng.tunny.ocean.api.entity.FiPledgeGoods;
import com.huazheng.tunny.ocean.service.FiPledgeGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fiPledgeGoodsService")
public class FiPledgeGoodsServiceImpl extends ServiceImpl<FiPledgeGoodsMapper, FiPledgeGoods> implements FiPledgeGoodsService {

    @Autowired
    private FiPledgeGoodsMapper fiPledgeGoodsMapper;

    public FiPledgeGoodsMapper getFiPledgeGoodsMapper() {
        return fiPledgeGoodsMapper;
    }

    public void setFiPledgeGoodsMapper(FiPledgeGoodsMapper fiPledgeGoodsMapper) {
        this.fiPledgeGoodsMapper = fiPledgeGoodsMapper;
    }

    /**
     * 查询质押货物表信息
     *
     * @param rowId 质押货物表ID
     * @return 质押货物表信息
     */
    @Override
    public FiPledgeGoods selectFiPledgeGoodsById(String rowId)
    {
        return fiPledgeGoodsMapper.selectFiPledgeGoodsById(rowId);
    }

    /**
     * 查询质押货物表列表
     *
     * @param fiPledgeGoods 质押货物表信息
     * @return 质押货物表集合
     */
    @Override
    public List<FiPledgeGoods> selectFiPledgeGoodsList(FiPledgeGoods fiPledgeGoods)
    {
        return fiPledgeGoodsMapper.selectFiPledgeGoodsList(fiPledgeGoods);
    }


    /**
     * 分页模糊查询质押货物表列表
     * @return 质押货物表集合
     */
    @Override
    public Page selectFiPledgeGoodsListByLike(Query query)
    {
        FiPledgeGoods fiPledgeGoods =  BeanUtil.mapToBean(query.getCondition(), FiPledgeGoods.class,false);
        query.setRecords(fiPledgeGoodsMapper.selectFiPledgeGoodsListByLike(query,fiPledgeGoods));
        return query;
    }

    /**
     * 新增质押货物表
     *
     * @param fiPledgeGoods 质押货物表信息
     * @return 结果
     */
    @Override
    public int insertFiPledgeGoods(FiPledgeGoods fiPledgeGoods)
    {
        return fiPledgeGoodsMapper.insertFiPledgeGoods(fiPledgeGoods);
    }

    /**
     * 修改质押货物表
     *
     * @param fiPledgeGoods 质押货物表信息
     * @return 结果
     */
    @Override
    public int updateFiPledgeGoods(FiPledgeGoods fiPledgeGoods)
    {
        return fiPledgeGoodsMapper.updateFiPledgeGoods(fiPledgeGoods);
    }


    /**
     * 删除质押货物表
     *
     * @param rowId 质押货物表ID
     * @return 结果
     */
    @Override
    public int deleteFiPledgeGoodsById(String rowId)
    {
        return fiPledgeGoodsMapper.deleteFiPledgeGoodsById( rowId);
    };

    @Override
    public int deleteFiPledgeGoodsByPledgeCode(String pledgeCode)
    {
        return fiPledgeGoodsMapper.deleteFiPledgeGoodsByPledgeCode( pledgeCode);
    };


    /**
     * 批量删除质押货物表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiPledgeGoodsByIds(Integer[] rowIds)
    {
        return fiPledgeGoodsMapper.deleteFiPledgeGoodsByIds( rowIds);
    }

}
