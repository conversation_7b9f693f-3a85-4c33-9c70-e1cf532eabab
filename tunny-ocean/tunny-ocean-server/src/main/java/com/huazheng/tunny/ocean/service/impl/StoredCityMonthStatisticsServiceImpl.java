package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.ocean.mapper.StoredCityMonthStatisticsMapper;
import com.huazheng.tunny.ocean.api.entity.StoredCityMonthStatistics;
import com.huazheng.tunny.ocean.service.StoredCityMonthStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service("storedCityMonthStatisticsService")
public class StoredCityMonthStatisticsServiceImpl extends ServiceImpl<StoredCityMonthStatisticsMapper, StoredCityMonthStatistics> implements StoredCityMonthStatisticsService {

    @Autowired
    private StoredCityMonthStatisticsMapper storedCityMonthStatisticsMapper;

    public StoredCityMonthStatisticsMapper getStoredCityMonthStatisticsMapper() {
        return storedCityMonthStatisticsMapper;
    }

    public void setStoredCityMonthStatisticsMapper(StoredCityMonthStatisticsMapper storedCityMonthStatisticsMapper) {
        this.storedCityMonthStatisticsMapper = storedCityMonthStatisticsMapper;
    }

    /**
     * 查询各地市汇总表-单月统计信息
     *
     * @param rowId 各地市汇总表-单月统计ID
     * @return 各地市汇总表-单月统计信息
     */
    @Override
    public StoredCityMonthStatistics selectStoredCityMonthStatisticsById(Integer rowId)
    {
        return storedCityMonthStatisticsMapper.selectStoredCityMonthStatisticsById(rowId);
    }

    /**
     * 查询各地市汇总表-单月统计列表
     *
     * @param storedCityMonthStatistics 各地市汇总表-单月统计信息
     * @return 各地市汇总表-单月统计集合
     */
    @Override
    public List<StoredCityMonthStatistics> selectStoredCityMonthStatisticsList(StoredCityMonthStatistics storedCityMonthStatistics)
    {
        if(storedCityMonthStatistics!=null && StrUtil.isEmpty(storedCityMonthStatistics.getDate())){
            String date= LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
            storedCityMonthStatistics.setDate(date);
        }
        List<StoredCityMonthStatistics> list = storedCityMonthStatisticsMapper.selectStoredCityMonthStatisticsList(storedCityMonthStatistics);
        StoredCityMonthStatistics total = storedCityMonthStatisticsMapper.selectStoredCityMonthStatisticsListTotal(storedCityMonthStatistics);
        list.add(total);
        return list;
    }


    /**
     * 分页模糊查询各地市汇总表-单月统计列表
     * @return 各地市汇总表-单月统计集合
     */
    @Override
    public Page selectStoredCityMonthStatisticsListByLike(Query query)
    {
        StoredCityMonthStatistics storedCityMonthStatistics =  BeanUtil.mapToBean(query.getCondition(), StoredCityMonthStatistics.class,false);
        query.setRecords(storedCityMonthStatisticsMapper.selectStoredCityMonthStatisticsListByLike(query,storedCityMonthStatistics));
        return query;
    }

    /**
     * 新增各地市汇总表-单月统计
     *
     * @param storedCityMonthStatistics 各地市汇总表-单月统计信息
     * @return 结果
     */
    @Override
    public int insertStoredCityMonthStatistics(StoredCityMonthStatistics storedCityMonthStatistics)
    {
        return storedCityMonthStatisticsMapper.insertStoredCityMonthStatistics(storedCityMonthStatistics);
    }

    /**
     * 修改各地市汇总表-单月统计
     *
     * @param storedCityMonthStatistics 各地市汇总表-单月统计信息
     * @return 结果
     */
    @Override
    public int updateStoredCityMonthStatistics(StoredCityMonthStatistics storedCityMonthStatistics)
    {
        return storedCityMonthStatisticsMapper.updateStoredCityMonthStatistics(storedCityMonthStatistics);
    }


    /**
     * 删除各地市汇总表-单月统计
     *
     * @param rowId 各地市汇总表-单月统计ID
     * @return 结果
     */
    public int deleteStoredCityMonthStatisticsById(Integer rowId)
    {
        return storedCityMonthStatisticsMapper.deleteStoredCityMonthStatisticsById( rowId);
    };


    /**
     * 批量删除各地市汇总表-单月统计对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteStoredCityMonthStatisticsByIds(Integer[] rowIds)
    {
        return storedCityMonthStatisticsMapper.deleteStoredCityMonthStatisticsByIds( rowIds);
    }

}
