package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfFinancingRepaymentinfo;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 仓单质押企业还款信息 服务接口层
 *
 * <AUTHOR>
 * @date 2023-02-08 15:13:33
 */
public interface EfFinancingRepaymentinfoService extends IService<EfFinancingRepaymentinfo> {
    /**
     * 查询仓单质押企业还款信息信息
     *
     * @param rowId 仓单质押企业还款信息ID
     * @return 仓单质押企业还款信息信息
     */
    public EfFinancingRepaymentinfo selectEfFinancingRepaymentinfoById(String rowId);

    /**
     * 查询仓单质押企业还款信息列表
     *
     * @param efFinancingRepaymentinfo 仓单质押企业还款信息信息
     * @return 仓单质押企业还款信息集合
     */
    public List<EfFinancingRepaymentinfo> selectEfFinancingRepaymentinfoList(EfFinancingRepaymentinfo efFinancingRepaymentinfo);


    /**
     * 分页模糊查询仓单质押企业还款信息列表
     * @return 仓单质押企业还款信息集合
     */
    public Page selectEfFinancingRepaymentinfoListByLike(Query query);



    /**
     * 新增仓单质押企业还款信息
     *
     * @param efFinancingRepaymentinfo 仓单质押企业还款信息信息
     * @return 结果
     */
    public int insertEfFinancingRepaymentinfo(EfFinancingRepaymentinfo efFinancingRepaymentinfo);

    /**
     * 修改仓单质押企业还款信息
     *
     * @param efFinancingRepaymentinfo 仓单质押企业还款信息信息
     * @return 结果
     */
    public int updateEfFinancingRepaymentinfo(EfFinancingRepaymentinfo efFinancingRepaymentinfo);

    /**
     * 删除仓单质押企业还款信息
     *
     * @param rowId 仓单质押企业还款信息ID
     * @return 结果
     */
    public int deleteEfFinancingRepaymentinfoById(String rowId);

    /**
     * 批量删除仓单质押企业还款信息
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfFinancingRepaymentinfoByIds(Integer[] rowIds);

}

