package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.MonthPlanDetailCityToPro;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.Date;
import java.util.List;

/**
 * 月计划申请子表(市平台提交到省平台) 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-07 11:49:29
 */
public interface MonthPlanDetailCityToProService extends IService<MonthPlanDetailCityToPro> {
    /**
     * 查询月计划申请子表(市平台提交到省平台)信息
     *
     * @param rowId 月计划申请子表(市平台提交到省平台)ID
     * @return 月计划申请子表(市平台提交到省平台)信息
     */
    public MonthPlanDetailCityToPro selectMonthPlanDetailCityToProById(String rowId);

    /**
     * 查询月计划申请子表(市平台提交到省平台)列表
     *
     * @param monthPlanDetailCityToPro 月计划申请子表(市平台提交到省平台)信息
     * @return 月计划申请子表(市平台提交到省平台)集合
     */
    public List<MonthPlanDetailCityToPro> selectMonthPlanDetailCityToProList(MonthPlanDetailCityToPro monthPlanDetailCityToPro);


    /**
     * 分页模糊查询月计划申请子表(市平台提交到省平台)列表
     * @return 月计划申请子表(市平台提交到省平台)集合
     */
    public Page selectMonthPlanDetailCityToProListByLike(Query query);



    /**
     * 新增月计划申请子表(市平台提交到省平台)
     *
     * @param monthPlanDetailCityToPro 月计划申请子表(市平台提交到省平台)信息
     * @return 结果
     */
    public int insertMonthPlanDetailCityToPro(MonthPlanDetailCityToPro monthPlanDetailCityToPro);

    /**
     * 修改月计划申请子表(市平台提交到省平台)
     *
     * @param monthPlanDetailCityToPro 月计划申请子表(市平台提交到省平台)信息
     * @return 结果
     */
    public int updateMonthPlanDetailCityToPro(MonthPlanDetailCityToPro monthPlanDetailCityToPro);

    public int updateMonthPlanDetailCityToProByNo(MonthPlanDetailCityToPro monthPlanDetailCityToPro);

    /**
     * 删除月计划申请子表(市平台提交到省平台)
     *
     * @param monthPlanDetailCityToPro 月计划申请子表(市平台提交到省平台)ID
     * @return 结果
     */
    public int deleteMonthPlanDetailCityToProById(MonthPlanDetailCityToPro monthPlanDetailCityToPro);

    /**
     * 批量删除月计划申请子表(市平台提交到省平台)
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteMonthPlanDetailCityToProByIds(Integer[] rowIds);

    List<MonthPlanDetailCityToPro> selectListByMonth(MonthPlanDetailCityToPro monthPlanDetailCityToPro);
}

