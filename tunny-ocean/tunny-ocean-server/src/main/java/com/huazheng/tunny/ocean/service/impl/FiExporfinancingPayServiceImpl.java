package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FiExporfinancingPayMapper;
import com.huazheng.tunny.ocean.api.entity.FiExporfinancingPay;
import com.huazheng.tunny.ocean.service.FiExporfinancingPayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fiExporfinancingPayService")
public class FiExporfinancingPayServiceImpl extends ServiceImpl<FiExporfinancingPayMapper, FiExporfinancingPay> implements FiExporfinancingPayService {

    @Autowired
    private FiExporfinancingPayMapper fiExporfinancingPayMapper;

    public FiExporfinancingPayMapper getFiExporfinancingPayMapper() {
        return fiExporfinancingPayMapper;
    }

    public void setFiExporfinancingPayMapper(FiExporfinancingPayMapper fiExporfinancingPayMapper) {
        this.fiExporfinancingPayMapper = fiExporfinancingPayMapper;
    }

    /**
     * 查询出口融资放款信息表信息
     *
     * @param rowId 出口融资放款信息表ID
     * @return 出口融资放款信息表信息
     */
    @Override
    public FiExporfinancingPay selectFiExporfinancingPayById(String rowId)
    {
        return fiExporfinancingPayMapper.selectFiExporfinancingPayById(rowId);
    }

    @Override
    public FiExporfinancingPay selectFiExporfinancingPayByAssetCode(FiExporfinancingPay fiExporfinancingPay) {

        return fiExporfinancingPayMapper.selectFiExporfinancingPayByAssetCode(fiExporfinancingPay);
    }

    /**
     * 查询出口融资放款信息表列表
     *
     * @param fiExporfinancingPay 出口融资放款信息表信息
     * @return 出口融资放款信息表集合
     */
    @Override
    public List<FiExporfinancingPay> selectFiExporfinancingPayList(FiExporfinancingPay fiExporfinancingPay)
    {
        return fiExporfinancingPayMapper.selectFiExporfinancingPayList(fiExporfinancingPay);
    }


    /**
     * 分页模糊查询出口融资放款信息表列表
     * @return 出口融资放款信息表集合
     */
    @Override
    public Page selectFiExporfinancingPayListByLike(Query query)
    {
        FiExporfinancingPay fiExporfinancingPay =  BeanUtil.mapToBean(query.getCondition(), FiExporfinancingPay.class,false);
        query.setRecords(fiExporfinancingPayMapper.selectFiExporfinancingPayListByLike(query,fiExporfinancingPay));
        return query;
    }

    /**
     * 新增出口融资放款信息表
     *
     * @param fiExporfinancingPay 出口融资放款信息表信息
     * @return 结果
     */
    @Override
    public int insertFiExporfinancingPay(FiExporfinancingPay fiExporfinancingPay)
    {
        return fiExporfinancingPayMapper.insertFiExporfinancingPay(fiExporfinancingPay);
    }

    /**
     * 修改出口融资放款信息表
     *
     * @param fiExporfinancingPay 出口融资放款信息表信息
     * @return 结果
     */
    @Override
    public int updateFiExporfinancingPay(FiExporfinancingPay fiExporfinancingPay)
    {
        return fiExporfinancingPayMapper.updateFiExporfinancingPay(fiExporfinancingPay);
    }


    /**
     * 删除出口融资放款信息表
     *
     * @param rowId 出口融资放款信息表ID
     * @return 结果
     */
    public int deleteFiExporfinancingPayById(String rowId)
    {
        return fiExporfinancingPayMapper.deleteFiExporfinancingPayById( rowId);
    };


    /**
     * 批量删除出口融资放款信息表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiExporfinancingPayByIds(Integer[] rowIds)
    {
        return fiExporfinancingPayMapper.deleteFiExporfinancingPayByIds( rowIds);
    }

}
