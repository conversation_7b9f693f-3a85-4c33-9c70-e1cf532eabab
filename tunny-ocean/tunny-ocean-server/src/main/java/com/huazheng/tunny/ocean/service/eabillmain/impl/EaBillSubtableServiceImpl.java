package com.huazheng.tunny.ocean.service.eabillmain.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.eabusinessprocess.EaBusinessProcessAppendDto;
import com.huazheng.tunny.ocean.api.entity.eabillmain.EaBillMain;
import com.huazheng.tunny.ocean.api.entity.eabillmain.EaBillSubtable;
import com.huazheng.tunny.ocean.api.entity.eabillmain.EaFee;
import com.huazheng.tunny.ocean.api.enums.SysEnum;
import com.huazheng.tunny.ocean.mapper.eabillmain.EaBillMainMapper;
import com.huazheng.tunny.ocean.mapper.eabillmain.EaBillSubtableMapper;
import com.huazheng.tunny.ocean.service.SysNoConfigService;
import com.huazheng.tunny.ocean.service.eabillmain.EaBillMainService;
import com.huazheng.tunny.ocean.service.eabillmain.EaBillSubtableService;
import com.huazheng.tunny.ocean.service.eabillmain.EaFeeService;
import com.huazheng.tunny.ocean.service.eabookingorder.EaBookingOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("eaBillSubtableService")
public class EaBillSubtableServiceImpl extends ServiceImpl<EaBillSubtableMapper, EaBillSubtable> implements EaBillSubtableService {

    @Autowired
    private EaBillSubtableMapper eaBillSubtableMapper;
    @Autowired
    private EaBillMainMapper eaBillMainMapper;
    @Autowired
    private EaBookingOrderService eaBookingOrderService;
    @Autowired
    private EaFeeService eaFeeService;
    @Autowired
    private SysNoConfigService sysNoConfigService;

    /**
     * 查询账单子表信息
     *
     * @param billSubtableId 账单子表ID
     * @return 账单子表信息
     */
    @Override
    public EaBillSubtable selectEaBillSubtableById(Long billSubtableId) {
        return eaBillSubtableMapper.selectEaBillSubtableById(billSubtableId);
    }

    /**
     * 查询账单子表列表
     *
     * @param eaBillSubtable 账单子表信息
     * @return 账单子表集合
     */
    @Override
    public List<EaBillSubtable> selectEaBillSubtableList(EaBillSubtable eaBillSubtable) {
        return eaBillSubtableMapper.selectEaBillSubtableList(eaBillSubtable);
    }


    /**
     * 分页模糊查询账单子表列表
     *
     * @return 账单子表集合
     */
    @Override
    public Page selectEaBillSubtableListByLike(Query query) {
        EaBillSubtable eaBillSubtable = BeanUtil.mapToBean(query.getCondition(), EaBillSubtable.class, false);
        query.setRecords(eaBillSubtableMapper.selectEaBillSubtableListByLike(query, eaBillSubtable));
        return query;
    }

    /**
     * 新增账单子表
     *
     * @param eaBillSubtable 账单子表信息
     * @return 结果
     */
    @Override
    public int insertEaBillSubtable(EaBillSubtable eaBillSubtable) {
        return eaBillSubtableMapper.insertEaBillSubtable(eaBillSubtable);
    }

    /**
     * 修改账单子表
     *
     * @param eaBillSubtable 账单子表信息
     * @return 结果
     */
    @Override
    public int updateEaBillSubtable(EaBillSubtable eaBillSubtable) {
        return eaBillSubtableMapper.updateEaBillSubtable(eaBillSubtable);
    }


    /**
     * 删除账单子表
     *
     * @param billSubtableId 账单子表ID
     * @return 结果
     */
    @Override
    public int deleteEaBillSubtableById(Long billSubtableId) {
        return eaBillSubtableMapper.deleteEaBillSubtableById(billSubtableId);
    }


    /**
     * 批量删除账单子表对象
     *
     * @param billSubtableIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEaBillSubtableByIds(Integer[] billSubtableIds) {
        return eaBillSubtableMapper.deleteEaBillSubtableByIds(billSubtableIds);
    }

    @Override
    public R backupBillSubtable(EaBillSubtable eaBillSubtable) {
        if (eaBillSubtable == null || StrUtil.isBlank(eaBillSubtable.getShiftNo())) {
            return R.error("请传入批次号");
        }
        EaBillMain eaBillMain = new EaBillMain();
        BeanUtil.copyProperties(eaBillSubtable, eaBillMain);
        List<String> list = eaBillMainMapper.getBillStage(eaBillMain);
        for (String stage : list) {
            if ("stage_two".equals(stage)) {
                return R.error("该批次账单已进入第二阶段");
            }
        }
        eaBillSubtableMapper.backupBillSubtable(eaBillSubtable);
        return R.success();
    }

    /**
     * 保存账单子表信息
     *
     * @param list
     * @return
     */
    @Override
    public int saveBillSubtableInfo(List<EaBusinessProcessAppendDto> list) {
        List<Map<String, Object>> arrayList = new ArrayList<>();
        List<EaBusinessProcessAppendDto> eaBusinessProcessAppendDtos = sumDomesticFlatRateByPayerCode(list);
        for (EaBusinessProcessAppendDto appendDto : eaBusinessProcessAppendDtos) {
            String code = null;
            Map<String, Object> map = new HashMap<>();
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            EntityWrapper<EaBillMain> wrapper = new EntityWrapper<>();
            wrapper.eq("shift_no", appendDto.getShiftNo());
            wrapper.eq("platform_code", userInfo.getPlatformCode());
            if ("1".equals(appendDto.getType())) {
                wrapper.eq("payer_code", appendDto.getPayerCode());
                code = appendDto.getPayerCode();
            } else {
                wrapper.eq("payee_code", appendDto.getPayeeCode());
                code = appendDto.getPayeeCode();
            }
            wrapper.eq("del_flag", SysEnum.N.getKey());
            List<EaBillMain> eaBillMains = eaBillMainMapper.selectList(wrapper);
            if (eaBillMains != null && eaBillMains.size() > 0 && appendDto != null) {
                EaBillSubtable eaBillSubtable = new EaBillSubtable();
                eaBillSubtable.setShiftNo(appendDto.getShiftNo());
                eaBillSubtable.setBillCode(eaBillMains.get(0).getBillCode());
                eaBillSubtable.setBillFeeType(appendDto.getFeeSubcategoryCode());
                eaBillSubtable.setBillSubtableCode(sysNoConfigService.genNo("FDC"));
                eaBillSubtable.setPlatformCode(appendDto.getPlatformCode());
                eaBillSubtable.setPlatformName(appendDto.getPlatformName());
                eaBillSubtable.setPayerCode(appendDto.getPayerCode());
                eaBillSubtable.setPayerName(appendDto.getPayerName());
                eaBillSubtable.setPayeeCode(appendDto.getPayeeCode());
                eaBillSubtable.setPayeeName(appendDto.getPayeeName());
                if ("jndtlyf".equals(eaBillSubtable.getBillFeeType()) || "jwdtlyf".equals(eaBillSubtable.getBillFeeType())) {
                    BigDecimal decimal = eaBookingOrderService.getBoxVolume(appendDto.getContainerSize(), appendDto.getContainerQuantity());
                    eaBillSubtable.setBillSlotQuantity(decimal);
                }
                eaBillSubtable.setBillFeeType(appendDto.getFeeSubcategoryName());
                eaBillSubtable.setBillAmount(appendDto.getDomesticFlatRate());
                eaBillSubtable.setPaidAmount(BigDecimal.ZERO);
                eaBillSubtable.setBillStatus("unsettled");
                eaBillSubtable.setBillContainerQuantity(1);
                eaBillSubtable.setCreateBy(userInfo.getRealName());
                eaBillSubtable.setCreateById(userInfo.getId());
                eaBillSubtable.setCreateTime(LocalDateTime.now());
                int i = eaBillSubtableMapper.insertEaBillSubtable(eaBillSubtable);
                if (i > 0) {
                    eaBillMains.get(0).setBillAmount(eaBillMains.get(0).getBillAmount().add(appendDto.getDomesticFlatRate()));
                    eaBillMains.get(0).setUpdateBy(userInfo.getRealName());
                    eaBillMains.get(0).setUpdateById(userInfo.getId());
                    eaBillMains.get(0).setUpdateTime(LocalDateTime.now());
                    Integer ii = eaBillMainMapper.updateEaBillMain(eaBillMains.get(0));
                    map.put(code, eaBillSubtable.getBillSubtableCode());
                    arrayList.add(map);
                }
            }
        }
        for (EaBusinessProcessAppendDto appendDto : list) {
            //生成费用
            EaFee eaFee = new EaFee();
            BeanUtil.copyProperties(appendDto, eaFee);
            for (Map<String, Object> map : arrayList) {
                if ("1".equals(appendDto.getType()) && map.get(appendDto.getPayerCode()) != null) {
                    eaFee.setBillSubtableCode(map.get(appendDto.getPayerCode()).toString());
                }
                if ("0".equals(appendDto.getType()) && map.get(appendDto.getPayeeCode()) != null) {
                    eaFee.setBillSubtableCode(map.get(appendDto.getPayeeCode()).toString());
                }
            }
            eaFee.setOriginalAmount(appendDto.getForeignFlatRateOriginal());
            int fee = eaFeeService.insertEaFee(eaFee);
        }
        return 0;
    }

    List<EaBusinessProcessAppendDto> sumDomesticFlatRateByPayerCode(List<EaBusinessProcessAppendDto> list) {
        // 按 type 决定使用 payer_code 还是 payee_code 分组并求和 domesticFlatRate
        Map<String, BigDecimal> groupedSum = list.stream()
                .filter(dto -> {
                    if ("1".equals(dto.getType())) {
                        return dto.getPayerCode() != null && !dto.getPayerCode().isEmpty();
                    } else {
                        return dto.getPayeeCode() != null && !dto.getPayeeCode().isEmpty();
                    }
                })
                .collect(Collectors.groupingBy(
                        dto -> {
                            if ("1".equals(dto.getType())) {
                                return dto.getPayerCode();
                            } else {
                                return dto.getPayeeCode();
                            }
                        },
                        Collectors.reducing(
                                BigDecimal.ZERO,
                                EaBusinessProcessAppendDto::getDomesticFlatRate,
                                BigDecimal::add
                        )
                ));

        // 构造新的 EaBusinessProcessAppendDto 列表
        List<EaBusinessProcessAppendDto> result = new ArrayList<>();

        // 获取每个分组码对应的原始对象作为模板
        Map<String, EaBusinessProcessAppendDto> templateMap = list.stream()
                .filter(dto -> {
                    if ("1".equals(dto.getType())) {
                        return dto.getPayerCode() != null && !dto.getPayerCode().isEmpty();
                    } else {
                        return dto.getPayeeCode() != null && !dto.getPayeeCode().isEmpty();
                    }
                })
                .collect(Collectors.toMap(
                        dto -> {
                            if ("1".equals(dto.getType())) {
                                return dto.getPayerCode();
                            } else {
                                return dto.getPayeeCode();
                            }
                        },
                        dto -> dto,
                        (existing, replacement) -> existing // 保留第一个遇到的对象作为模板
                ));

        // 创建新的 EaBusinessProcessAppendDto 对象，设置总金额
        for (Map.Entry<String, BigDecimal> entry : groupedSum.entrySet()) {
            String groupCode = entry.getKey();
            BigDecimal totalDomesticFlatRate = entry.getValue();

            // 使用该 groupCode 的第一个对象作为模板
            if (templateMap.containsKey(groupCode)) {
                EaBusinessProcessAppendDto template = templateMap.get(groupCode);
                EaBusinessProcessAppendDto summedDto = new EaBusinessProcessAppendDto();

                // 复制模板对象的所有属性
                BeanUtil.copyProperties(template, summedDto);

                // 设置总 domesticFlatRate
                summedDto.setDomesticFlatRate(totalDomesticFlatRate);

                result.add(summedDto);
            }
        }

        return result;
    }
}
