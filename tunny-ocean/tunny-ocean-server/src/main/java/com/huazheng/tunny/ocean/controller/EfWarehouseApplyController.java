package com.huazheng.tunny.ocean.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.ocean.api.dto.EfFileDTO;
import com.huazheng.tunny.ocean.api.entity.EfFinancingApply;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseApply;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseInfo;
import com.huazheng.tunny.ocean.api.entity.OperationLog;
import com.huazheng.tunny.ocean.service.EfWarehouseApplyService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.service.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * 仓单融资申请表
 *
 * <AUTHOR>
 * @date 2023-05-04 10:08:10
 */
@Slf4j
@RestController
@RequestMapping("/efwarehouseapply")
public class EfWarehouseApplyController {

    @Autowired
    private EfWarehouseApplyService efWarehouseApplyService;
    @Autowired
    private SignatureController signatureController;
    @Autowired
    private OperationLogService operationLogService;
    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  efWarehouseApplyService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return efWarehouseApplyService.selectEfWarehouseApplyListByLike(new Query<>(params));
    }

    @GetMapping("/pageForPlatformCode")
    public Page pageForPlatformCode(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  efWarehouseApplyService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return efWarehouseApplyService.pageForPlatformCode(new Query<>(params));
    }

    @GetMapping("/sumForPlatformCode")
    public R sumForPlatformCode(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  efWarehouseApplyService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return efWarehouseApplyService.sumForPlatformCode(new Query<>(params));
    }

    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        EfWarehouseInfo info =efWarehouseApplyService.selectEfWarehouseApplyById(rowId);
        return new R<>(info);
    }

    /**
     * 保存
     * @param efWarehouseApply
     * @return R
     */
    @PostMapping
    public String save(@RequestBody EfWarehouseApply efWarehouseApply) {
        String content = null;
        content = efWarehouseApplyService.insertEfWarehouseApply(efWarehouseApply);
        return content;
    }

    /**
     * 修改
     * @param efWarehouseApply
     * @return R
     */
    @PostMapping("/update")
    public String update(@RequestBody EfWarehouseApply efWarehouseApply) {
        String content = null;
        content = efWarehouseApplyService.updateEfWarehouseApply(efWarehouseApply);
        return content;
    }

    

    /**
     * 删除
     * @param rowId
     * @return R
     */
    @GetMapping("/del/{rowId}")
    public R delete(@PathVariable  String rowId) {
        efWarehouseApplyService.deleteById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        efWarehouseApplyService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<EfWarehouseApply> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<EfWarehouseApply> list = efWarehouseApplyService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), EfWarehouseApply.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }

    @PostMapping("/auditFromEf")
    public String auditFromEf(@RequestBody JSONObject jsonObject) {
        String content = null;
        try {
            //调用接口
            String data = signatureController.getPost(jsonObject);

            String content2 = String.valueOf(jsonObject.get("content"));
            OperationLog log4 = new OperationLog();
            log4.setUuid(UUID.randomUUID().toString());
            log4.setProcessType("E融审核质押申请");
            log4.setOperationCode("ER");
            log4.setOperationName("E融");
            log4.setOperationTime(new Date());
            log4.setOperationOpinion(content2 + data );
            operationLogService.insertOperationLog(log4);

            EfWarehouseApply efWarehouseApply = JSONUtil.toBean(data, EfWarehouseApply.class);

            //插入数据
            content = efWarehouseApplyService.auditFromEf(efWarehouseApply);
        }catch (Exception e){
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE,e.getMessage()), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPostEf("/efwarehouseapply/auditFromEf", content);
        return result;
    }

    @PostMapping("/syncAsset")
    public String syncAsset(@RequestBody JSONObject jsonObject) {
        String content = null;
        try {
            //调用接口
            String data = signatureController.getPost(jsonObject);
            EfWarehouseInfo efWarehouseInfo = JSONUtil.toBean(data, EfWarehouseInfo.class);

            String content2 = String.valueOf(jsonObject.get("content"));
            OperationLog log4 = new OperationLog();
            log4.setUuid(UUID.randomUUID().toString());
            log4.setProcessType("银行同步融资信息");
            log4.setOperationCode("ZC");
            log4.setOperationName("中钞");
            log4.setOperationTime(new Date());
            log4.setOperationOpinion(content2 + data );
            operationLogService.insertOperationLog(log4);

            //插入数据
            content = efWarehouseApplyService.syncAsset(efWarehouseInfo);
        }catch (Exception e){
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE,e.getMessage()), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPost("/v1/ent/warehouse/syncAsset", content);
        return result;
    }

    @PostMapping("/syncWarnPrice")
    public String syncWarnPrice(@RequestBody JSONObject jsonObject) {
        String content = null;
        try {
            //调用接口
            String data = signatureController.getPost(jsonObject);
            EfWarehouseApply efWarehouseApply = JSONUtil.toBean(data, EfWarehouseApply.class);

            String content2 = String.valueOf(jsonObject.get("content"));
            OperationLog log4 = new OperationLog();
            log4.setUuid(UUID.randomUUID().toString());
            log4.setProcessType("银行同步预警估值");
            log4.setOperationCode("ZC");
            log4.setOperationName("中钞");
            log4.setOperationTime(new Date());
            log4.setOperationOpinion(content2 + data );
            operationLogService.insertOperationLog(log4);

            //插入数据
            content = efWarehouseApplyService.syncWarnPrice(efWarehouseApply);
        }catch (Exception e){
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE,e.getMessage()), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPost("/v1/ent/warehouse/syncWarnPrice", content);
        return result;
    }

    @PostMapping("/downloadFile")
    public EfFileDTO downloadFile(@RequestBody EfFileDTO efFileDTO,HttpServletResponse response) throws IOException {
        return efWarehouseApplyService.downloadFile(efFileDTO);
    }

    @PostMapping("/downloadFileForEf")
    public String downloadFileForEf(@RequestBody JSONObject jsonObject) {
        String content = null;
        try {
            //调用接口
            String data = signatureController.getPost(jsonObject);
            EfFileDTO efFileDTO = JSONUtil.toBean(data, EfFileDTO.class);

            //插入数据
            content = efWarehouseApplyService.downloadFileForEf(efFileDTO);
        }catch (Exception e){
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE,e.getMessage()), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPostEf("/efwarehouseapply/downloadFileForEf", content);
        return result;
    }
}
