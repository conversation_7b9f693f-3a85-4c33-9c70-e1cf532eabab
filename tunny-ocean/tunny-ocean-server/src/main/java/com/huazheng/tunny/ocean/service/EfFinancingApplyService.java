package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.EfFinancingApply;
import com.huazheng.tunny.ocean.api.entity.EfFinancingConfirmationinfo;

import java.util.List;

/**
 * 仓单融资申请表 服务接口层
 *
 * <AUTHOR>
 * @date 2023-02-06 14:10:36
 */
public interface EfFinancingApplyService extends IService<EfFinancingApply> {
    /**
     * 查询仓单融资申请表信息
     *
     * @param rowId 仓单融资申请表ID
     * @return 仓单融资申请表信息
     */
    public EfFinancingApply selectEfFinancingApplyById(String rowId);

    /**
     * 查询仓单融资申请表列表
     *
     * @param efFinancingApply 仓单融资申请表信息
     * @return 仓单融资申请表集合
     */
    public List<EfFinancingApply> selectEfFinancingApplyList(EfFinancingApply efFinancingApply);


    /**
     * 分页模糊查询仓单融资申请表列表
     * @return 仓单融资申请表集合
     */
    public Page selectEfFinancingApplyListByLike(Query query);



    /**
     * 新增仓单融资申请表
     *
     * @param efFinancingApply 仓单融资申请表信息
     * @return 结果
     */
    public R insertEfFinancingApply(EfFinancingApply efFinancingApply);

    /**
     * 修改仓单融资申请表
     *
     * @param efFinancingApply 仓单融资申请表信息
     * @return 结果
     */
    public R updateEfFinancingApply(EfFinancingApply efFinancingApply);

    /**
     * 删除仓单融资申请表
     *
     * @param rowId 仓单融资申请表ID
     * @return 结果
     */
    public int deleteEfFinancingApplyById(String rowId);

    /**
     * 批量删除仓单融资申请表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfFinancingApplyByIds(Integer[] rowIds);

    public String auditPledgeFinancingToQl(EfFinancingApply efFinancingApply);

    public String syncPledgeFinancing(EfFinancingApply efFinancingApply);

    public R confirmLoan(EfFinancingConfirmationinfo efFinancingConfirmationinfo);
}

