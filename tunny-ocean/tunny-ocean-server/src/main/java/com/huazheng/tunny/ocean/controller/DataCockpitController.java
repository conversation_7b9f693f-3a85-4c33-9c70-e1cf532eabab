package com.huazheng.tunny.ocean.controller;

import com.huazheng.tunny.ocean.api.vo.DataCockpitLineChartVO;
import com.huazheng.tunny.ocean.api.vo.DataCockpitTitleNumBerVO;
import com.huazheng.tunny.ocean.service.DataCockpitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/** 数据驾驶舱
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2024/7/29 15:26
 */
@RestController
@RequestMapping("/cockpit")
@Slf4j
public class DataCockpitController {

    @Autowired
    private DataCockpitService dataCockpitService;

    /**
     * 获取数据驾驶舱-省平台-开行列数，收入总数，毛利
     * @return
     */
    @GetMapping("/selectDataCockpitTitleNumber")
    public DataCockpitTitleNumBerVO selectDataCockpitTitleNumber(@RequestParam("startTime") String startTime,
                                                                 @RequestParam("endTime") String endTime){

        return dataCockpitService.selectDataCockpitTitleNumber(startTime, endTime);

    }

    /**
     * 收入总计折线图
     * @return
     */
    @GetMapping("/selectIncomeSumLineChart")
    public DataCockpitLineChartVO selectIncomeSumLineChart(@RequestParam("startTime") String startTime,
                                                           @RequestParam("endTime") String endTime){
        return dataCockpitService.selectIncomeSumLineChart(startTime, endTime);
    }

    /**
     * 各市单车平均收入(市总数)
     * @return
     */
    @GetMapping("/selectCityUnitTrainSum")
    public DataCockpitLineChartVO selectCityUnitTrainSum(@RequestParam("startTime") String startTime,
                                                           @RequestParam("endTime") String endTime){
        return dataCockpitService.selectCityUnitTrainSum(startTime, endTime);
    }


    /**
     * 各市单车平均收入(市明细)
     * @return
     */
    @GetMapping("/selectCityUnitTrainDetail")
    public DataCockpitLineChartVO selectCityUnitTrainDetail(@RequestParam("startTime") String startTime,
                                                         @RequestParam("endTime") String endTime){
        return dataCockpitService.selectCityUnitTrainDetail(startTime, endTime);
    }


}
