package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.*;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.feign.RemoteAdminService;
import com.huazheng.tunny.ocean.api.vo.*;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.*;
import com.huazheng.tunny.ocean.util.CellValueUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Service("billPayProvinceService")
public class BillPayProvinceServiceImpl extends ServiceImpl<BillPayProvinceMapper, BillPayProvince> implements BillPayProvinceService {

    @Autowired
    private BillPayProvinceMapper billPayProvinceMapper;

    @Autowired
    private BillPayProvinceSubService billPayProvinceSubService;

    @Autowired
    private FdBusCostMapper fdBusCostMapper;

    @Autowired
    private FdBusCostDetailService busCostDetailService;

    @Autowired
    private BillDealWithCityMapper billDealWithCityMapper;

    @Autowired
    private FdShippingAccoundetailMapper fdShippingAccoundetailMapper;

    @Autowired
    private BillSubPayCityMapper billSubPayCityMapper;
    @Autowired
    private RemoteAdminService remoteAdminService;
    @Value("${db.database}")
    private String database;

    @Autowired
    private FdRemittanceRecordService fdRemittanceRecordService;
    @Autowired
    private SysNoConfigService sysNoConfigService;

    /**
     * 查询应付账单（省平台）信息
     *
     * @param params 应付账单（省平台）ID
     * @return 应付账单（省平台）信息
     */
    @Override
    public R selectBillPayProvinceById(Map<String, Object> params) {
        R r = new R();
        Integer id = Integer.valueOf(String.valueOf(params.get("id")));
        Object platformCodeOb = params.get("platformCode");
        Object customerNameOb = params.get("customerName");
        String platformCode = "";
        String customerName = "";
        if (!ObjectUtils.isEmpty(platformCodeOb)) {
            platformCode = String.valueOf(params.get("platformCode"));
        }
        if (!ObjectUtils.isEmpty(customerNameOb)) {
            customerName = String.valueOf(params.get("customerName"));
        }
        BillPayProvinceVO billPayProvinceVO = billPayProvinceMapper.selectBillPayProvinceById(id);
        if (ObjectUtils.isEmpty(billPayProvinceVO)) {
            r.setCode(500);
            r.setMsg("查询出错");
            return r;
        }
        // 查询出子账单信息
        List<BillDealWithCityAndCostVO> cityAndCostVOS = billPayProvinceSubService.selectFdBillSubByBillNo(billPayProvinceVO.getBillCode(), customerName, platformCode);
        if (!CollectionUtils.isEmpty(cityAndCostVOS)) {
            billPayProvinceVO.setList(cityAndCostVOS);
            r.setData(billPayProvinceVO);
        }
        return r;
    }

    /**
     * 查询应付账单（省平台）列表
     *
     * @param billPayProvince 应付账单（省平台）信息
     * @return 应付账单（省平台）集合
     */
    @Override
    public List<BillPayProvince> selectBillPayProvinceList(BillPayProvince billPayProvince) {
        return billPayProvinceMapper.selectBillPayProvinceList(billPayProvince);
    }


    /**
     * 分页模糊查询应付账单（省平台）列表
     *
     * @return 应付账单（省平台）集合
     */
    @Override
    public Page selectBillPayProvinceListByLike(Query query) {
        if (StrUtil.isBlank(query.getOrderByField())) {
            query.setOrderByField("x.create_time");
            query.setAsc(Boolean.FALSE);
        }
        BillPayProvince billPayProvince = BeanUtil.mapToBean(query.getCondition(), BillPayProvince.class, false);
        query.setRecords(billPayProvinceMapper.selectBillPayProvinceListByLike(query, billPayProvince));
        return query;
    }

    /**
     * 新增应付账单（省平台）
     *
     * @param billPayProvince 应付账单（省平台）信息
     * @return 结果
     */
    @Override
    public int insertBillPayProvince(BillPayProvince billPayProvince) {
        return billPayProvinceMapper.insertBillPayProvince(billPayProvince);
    }

    /**
     * 修改应付账单（省平台）
     *
     * @param billPayProvince 应付账单（省平台）信息
     * @return 结果
     */
    @Override
    public int updateBillPayProvince(BillPayProvince billPayProvince) {
        return billPayProvinceMapper.updateBillPayProvince(billPayProvince);
    }


    /**
     * 删除应付账单（省平台）
     *
     * @param id 应付账单（省平台）ID
     * @return 结果
     */
    public int deleteBillPayProvinceById(Integer id) {
        return billPayProvinceMapper.deleteBillPayProvinceById(id);
    }

    @Override
    public Map<String, Object> selectCostInfoByBillId(Query query) {
        BillCostDetailDTO billCostDetailDTO = BeanUtil.mapToBean(query.getCondition(), BillCostDetailDTO.class, false);
        BillPayProvinceVO billPayProvinceVO = billPayProvinceMapper.selectBillPayProvinceById(billCostDetailDTO.getId());
        Map<String, Object> map = new HashMap<>();
        if (!ObjectUtils.isEmpty(billPayProvinceVO)) {
            billCostDetailDTO.setShiftNo(billPayProvinceVO.getProvinceShiftNum());
            billCostDetailDTO.setDbSource(database);
            billCostDetailDTO.setBillCode(billPayProvinceVO.getBillCode());
            List<BillCostDetailVO> billCostDetailVOS = billPayProvinceMapper.selectCostInfoByCostCode(query, billCostDetailDTO);
            if (!CollectionUtils.isEmpty(billCostDetailVOS)) {
                Double ybJe = billCostDetailVOS.stream().mapToDouble(b -> {
                    try {
                        return Double.parseDouble(String.valueOf(b.getOriginalAmount()));
                    } catch (NumberFormatException e) {
                        return 0.0;
                    }
                }).sum();

                Double bbJe = billCostDetailVOS.stream().mapToDouble(b -> {
                    try {
                        return Double.parseDouble(String.valueOf(b.getLocalAmount()));
                    } catch (NumberFormatException e) {
                        return 0.0;
                    }
                }).sum();
                query.setRecords(billCostDetailVOS);
                map.put("ybJe", ybJe);
                map.put("bbJe", bbJe);
                map.put("data", query);
            }
        }
        return map;
    }

    ;


    /**
     * 批量删除应付账单（省平台）对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBillPayProvinceByIds(Integer[] ids) {
        return billPayProvinceMapper.deleteBillPayProvinceByIds(ids);
    }

    /**
     * 省平台应付
     *
     * @param addCostByBillDTO
     * @return
     */
    @Override
    @Transactional
    public R addCostByBill(AddCostByBillDTO addCostByBillDTO) {
        R r = new R();
        try {
            // 获取当前登录人信息
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String platformCode = userInfo.getPlatformCode();
            String platformLevel = userInfo.getPlatformLevel();
            String platformName = userInfo.getPlatformName();


            // 查询账单信息
            Integer pageType = addCostByBillDTO.getPageType();
            if (pageType.equals(0)) {
                BillPayProvinceVO billPayProvinceVO = billPayProvinceMapper.selectBillPayProvinceById(addCostByBillDTO.getId());
                if (ObjectUtil.isNull(billPayProvinceVO)) {
                    r.setMsg("未查询出账单数据");
                    r.setCode(500);
                    return r;
                }

                // 已绑定的子帐单信息
                List<BillPayProvinceSub> billPayProvinceSubs = billPayProvinceMapper.selectBillSubByBillCode(billPayProvinceVO.getBillCode());
                // 班列信息
                Shifmanagement shifmanagement = billPayProvinceMapper.selectShiftInfo(billPayProvinceVO.getProvinceShiftNum(), platformCode);
                // 费用流程单
                //billDealWithCityMapper.selectFdBusCodeBy
                // 查询出发运台账信息
                FdShippingAccount fdShippingAccount = billPayProvinceMapper.selectAccountByShiftNoAndPlatformCode(billPayProvinceVO.getProvinceShiftNum(), platformCode);
                // 生成子帐单数据
                BillPayProvinceSub billSubPayCity = new BillPayProvinceSub();
                billSubPayCity.setBillCode(billPayProvinceVO.getBillCode());
                String billSubCode = billPayProvinceVO.getBillCode() + String.format("%03d", billPayProvinceSubs.size());
                billSubPayCity.setBillSubCode(billSubCode);
                //billSubPayCity.setWaybillNo();
                FdBusCost sel2 = new FdBusCost();
                sel2.setShiftNo(billPayProvinceVO.getProvinceShiftNum());
                sel2.setPlatformCode(platformCode);
                sel2.setDeleteFlag("N");
                List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel2);
                if (CollUtil.isNotEmpty(fdBusCosts)) {
                    billSubPayCity.setCostCode(fdBusCosts.get(0).getCostCode());
                }
                billSubPayCity.setAccountCode(fdShippingAccount.getAccountCode());
                billSubPayCity.setPlatformCode(billPayProvinceSubs.get(0).getPlatformCode());
                billSubPayCity.setPlatformName(billPayProvinceSubs.get(0).getPlatformName());
                billSubPayCity.setPlatformLevel(billPayProvinceSubs.get(0).getPlatformLevel());
                billSubPayCity.setProvinceShiftNo(shifmanagement.getProvinceShiftNo());
                billSubPayCity.setShiftNo(shifmanagement.getShiftId());
                billSubPayCity.setShiftName(shifmanagement.getShiftName());

                BigDecimal reduce = addCostByBillDTO.getAddList().stream().map(AddCostByBillListDTO::getLocalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

                billSubPayCity.setBillAmount(reduce);
                billSubPayCity.setBillingState("0");
                billSubPayCity.setAddWho(platformCode);
                billSubPayCity.setAddWhoName(platformName);
                billSubPayCity.setAddTime(new Date());
                billSubPayCity.setCustomerCode(billPayProvinceSubs.get(0).getCustomerCode());
                billSubPayCity.setCustomerName(billPayProvinceSubs.get(0).getCustomerName());
                billSubPayCity.setPortStation(shifmanagement.getPortStation());
                billSubPayCity.setStatus("1");
                billSubPayCity.setBoxNum(addCostByBillDTO.getAddList().size());

                // 费用明细增加
                List<FdBusCostDetail> fdBusCostDetails = new ArrayList<>();
                for (AddCostByBillListDTO addCostByBillListDTO : addCostByBillDTO.getAddList()) {
                    FdBusCostDetail fdBusCostDetail = new FdBusCostDetail();
                    fdBusCostDetail.setCostCode(billPayProvinceSubs.get(0).getCostCode());
                    fdBusCostDetail.setCostType("0");
                    fdBusCostDetail.setContainerNumber(addCostByBillListDTO.getBoxNum());
                    fdBusCostDetail.setCodeBbCategoriesCode(addCostByBillListDTO.getCodeBbCategoriesCode());
                    fdBusCostDetail.setCodeBbCategoriesName(addCostByBillListDTO.getCodeBbCategoriesName());
                    fdBusCostDetail.setCodeSsCategoriesCode(addCostByBillListDTO.getCodeSsCategoriesCode());
                    fdBusCostDetail.setCodeSsCategoriesName(addCostByBillListDTO.getCodeSsCategoriesName());
                    fdBusCostDetail.setReceiveCode(platformCode);
                    fdBusCostDetail.setReceiveName(platformName);
                    fdBusCostDetail.setPayCode(fdShippingAccount.getCustomerNo());
                    fdBusCostDetail.setPayName(fdShippingAccount.getCustomerName());
                    fdBusCostDetail.setCurrency(addCostByBillListDTO.getCurrency());
                    fdBusCostDetail.setExchangeRate(addCostByBillListDTO.getExchangeRate());
                    fdBusCostDetail.setOriginalAmount(addCostByBillListDTO.getOriginalAmount());
                    fdBusCostDetail.setLocalAmount(addCostByBillListDTO.getLocalAmount());
                    fdBusCostDetail.setAuditStatus("0");
                    fdBusCostDetail.setAddWho(platformCode);
                    fdBusCostDetail.setAddWhoName(platformName);
                    fdBusCostDetail.setAddTime(LocalDateTime.now());
                    fdBusCostDetail.setShiftNo(shifmanagement.getShiftId());
                    fdBusCostDetail.setBillSubCode(billSubCode);
                    fdBusCostDetails.add(fdBusCostDetail);
                }

                billPayProvinceSubService.insert(billSubPayCity);
                busCostDetailService.insertBatch(fdBusCostDetails);
            }

            return r;
        } catch (Exception e) {
            log.error("修改市应收账单结算报错：" + e);
            e.printStackTrace();
            r.setCode(500);
            r.setMsg("系统异常");
            return r;
        }
    }

    /**
     * 获取箱号集合
     *
     * @param id
     * @param pageType
     * @return
     */
    @Override
    public List<FdShippingAccoundetail> selectAccoundetailList(Integer id, Integer pageType) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String platformCode = userInfo.getPlatformCode();
        String platformLevel = userInfo.getPlatformLevel();
        String platformName = userInfo.getPlatformName();
        List<FdShippingAccoundetail> fdShippingAccoundetails = new ArrayList<>();
        if (pageType.equals(0)) {
            BillDealWithCityVO billDealWithCityVO = billDealWithCityMapper.selectBillDealWithCityById(id, platformCode, platformLevel);
            if (ObjectUtils.isEmpty(billDealWithCityVO)) {
                return fdShippingAccoundetails;
            }
            List<BillSubPayCity> billSubPayCities = billDealWithCityMapper.selectBillSubByBillCode(billDealWithCityVO.getBillCode());
            if (CollectionUtil.isEmpty(billSubPayCities)) {
                return fdShippingAccoundetails;
            }

            fdShippingAccoundetails = fdShippingAccoundetailMapper.selectFdShippingAccoundetailByList(billSubPayCities.get(0).getShiftNo(), billSubPayCities.get(0).getCustomerCode());
            return fdShippingAccoundetails;
        }
        if (pageType.equals(1)) {
            BillPayProvinceVO billPayProvinceVO = billPayProvinceMapper.selectBillPayProvinceById(id);
            if (ObjectUtils.isEmpty(billPayProvinceVO)) {
                return fdShippingAccoundetails;
            }
            List<BillPayProvinceSub> billPayProvinceSubs = billPayProvinceMapper.selectBillSubByBillCode(billPayProvinceVO.getBillCode());
            if (CollectionUtil.isEmpty(billPayProvinceSubs)) {
                return fdShippingAccoundetails;
            }
            if ("YF".equals(billPayProvinceSubs.get(0).getBillType()) && "ztdl".equals(billPayProvinceSubs.get(0).getPlatformCode())) {
                fdShippingAccoundetails = fdShippingAccoundetailMapper.selectFdShippingAccoundetailByProvince(billPayProvinceSubs.get(0).getShiftNo(), billPayProvinceSubs.get(0).getCustomerCode());
            } else {
                fdShippingAccoundetails = fdShippingAccoundetailMapper.selectFdShippingAccoundetailByList(billPayProvinceSubs.get(0).getShiftNo(), billPayProvinceSubs.get(0).getCustomerCode());
            }

            return fdShippingAccoundetails;
        }
        return fdShippingAccoundetails;
    }

    /**
     * 省平台对比账单功能
     *
     * @param params
     * @return
     */
    @Override
    public R selectAccountByDL(Map<String, Object> params) {
        R r = new R();
        try {
            SelectAccountByDlDTO selectAccountByDlDTO = BeanUtil.mapToBean(params, SelectAccountByDlDTO.class, false);
            // 读取url
            //String fileUrl = "file:///C:/Users/<USER>/Desktop/spy-oy-sql/中铁国际多式联运有限公司(收.xls";
            //InputStream inputStream = new URL(fileUrl).openStream();
            InputStream inputStream = new URL(selectAccountByDlDTO.getFileUrl()).openStream();
            // 获取当前登录人信息
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String platformCode = userInfo.getPlatformCode();
            String platformLevel = userInfo.getPlatformLevel();

            // 查询数据库中对应的箱号
            // 应收数据
            BillCostDetailDTO billCostDetailDTO = new BillCostDetailDTO();
            if (selectAccountByDlDTO.getPageType().equals(0)) {
                BillDealWithCityVO billDealWithCityVO = billDealWithCityMapper.selectBillDealWithCityById(selectAccountByDlDTO.getId(), platformCode, platformLevel);
                if (!ObjectUtils.isEmpty(billDealWithCityVO)) {
                    billCostDetailDTO.setShiftNo(billDealWithCityVO.getProvinceShiftNum());
                    billCostDetailDTO.setBillCode(billDealWithCityVO.getBillCode());
                }
            }
            // 应付数据
            if (selectAccountByDlDTO.getPageType().equals(1)) {
                BillPayProvinceVO billPayProvinceVO = billPayProvinceMapper.selectBillPayProvinceById(selectAccountByDlDTO.getId());
                if (!ObjectUtils.isEmpty(billPayProvinceVO)) {
                    billCostDetailDTO.setShiftNo(billPayProvinceVO.getProvinceShiftNum());
                    billCostDetailDTO.setBillCode(billPayProvinceVO.getBillCode());
                }
            }

            List<BillCheckListVO> billCheckListVOS = new ArrayList<>();

            Workbook sheets = new HSSFWorkbook(inputStream);
            Sheet sheetAt = sheets.getSheetAt(1);
            int rowIn = 2;
            while (true) {
                Row row = sheetAt.getRow(rowIn);
                if (row == null) {
                    break;
                }
                // 箱号
                String boxNum = "";
                Cell cell = row.getCell(1);
                if (cell != null && !cell.getStringCellValue().equals("")) {
                    boxNum = cell.getStringCellValue();
                } else {
                    break;
                }
                BillCheckListVO billCheckListVO = new BillCheckListVO();
                billCheckListVO.setExcelContainerNumber(boxNum);

                billCostDetailDTO.setContainerNumber(boxNum);

                // 费目
                String costMd = "";
                Cell cell5 = row.getCell(5);
                if (cell5 != null && !cell5.getStringCellValue().equals("")) {
                    costMd = cell5.getStringCellValue();
                } else {
                    break;
                }
                billCostDetailDTO.setCodeSsCategoriesName(costMd);

                // 汇率
                BigDecimal hl = BigDecimal.ONE;
                Cell cell6 = row.getCell(6);
                if (cell6 != null) {
                    if (cell6.getCellTypeEnum() == CellType.STRING) {
                        hl = new BigDecimal(cell6.getStringCellValue());
                    }
                    if (cell6.getCellTypeEnum() == CellType.NUMERIC) {
                        hl = BigDecimal.valueOf(cell6.getNumericCellValue());
                    }
                } else {
                    break;
                }
                billCheckListVO.setExcelExchangeRate(hl);

                // 币种
                String bz = "";
                Cell cell7 = row.getCell(7);
                if (cell7 != null && !cell7.getStringCellValue().equals("")) {
                    bz = cell7.getStringCellValue();
                } else {
                    break;
                }
                billCheckListVO.setExcelCurrency(bz);

                // 金额(本币) 默认为0.00
                BigDecimal amount = new BigDecimal("0.00");
                Cell cell8 = row.getCell(8);
                if (cell8 != null) {
                    amount =  CellValueUtil.getCellValueBigDecimal(cell8);
                } else {
                    break;
                }
                billCheckListVO.setExcelLocalAmount(amount);
                billCheckListVO.setExcelAmountCha(amount);

                // 委托编号
                String wtbh = "";
                Cell cell15 = row.getCell(15);
                if (cell15 != null && !cell15.getStringCellValue().equals("")) {
                    wtbh = cell15.getStringCellValue();
                } else {
                    break;
                }
                billCheckListVO.setExcelWtCode(wtbh);

                // 将查询出的数据进行封装
                List<BillCostDetailVO> billCostDetailVOS = new ArrayList<>();
                if (selectAccountByDlDTO.getPageType().equals(0)) {
                    billCostDetailVOS = billDealWithCityMapper.selectCostInfoByCostCode(billCostDetailDTO);
                }
                if (selectAccountByDlDTO.getPageType().equals(1)) {
                    billCostDetailVOS = billPayProvinceMapper.selectCostInfoByCostCode(billCostDetailDTO);
                }

                if (CollectionUtil.isNotEmpty(billCostDetailVOS)) {
                    billCheckListVO.setSysContainerNumber(billCostDetailVOS.get(0).getContainerNumber());
                    billCheckListVO.setSysStartStationName(billCostDetailVOS.get(0).getStartStationName());
                    billCheckListVO.setSysEndStationName(billCostDetailVOS.get(0).getEndStationName());
                    billCheckListVO.setSysCodeBbCategoriesCode(billCostDetailVOS.get(0).getCodeBbCategoriesCode());
                    billCheckListVO.setSysCodeBbCategoriesName(billCostDetailVOS.get(0).getCodeBbCategoriesName());
                    billCheckListVO.setSysCodeSsCategoriesCode(billCostDetailVOS.get(0).getCodeSsCategoriesCode());
                    billCheckListVO.setSysCodeSsCategoriesName(billCostDetailVOS.get(0).getCodeSsCategoriesName());
                    billCheckListVO.setSysCurrency(billCostDetailVOS.get(0).getCurrency());
                    billCheckListVO.setSysExchangeRate(billCostDetailVOS.get(0).getExchangeRate());
                    BigDecimal sum = billCostDetailVOS.stream().map(BillCostDetailVO::getLocalAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    billCheckListVO.setSysLocalAmount(sum);
                    billCheckListVO.setExcelAmountCha(billCheckListVO.getExcelLocalAmount().subtract(sum));
                }
                billCheckListVOS.add(billCheckListVO);
                rowIn++;
            }

            sheets.close();
            r.setData(billCheckListVOS);
            return r;
        } catch (Exception e) {
            r.setMsg("读取数据报错:" + e.getMessage());
            r.setCode(500);
            return r;
        }
    }

    @Override
    public R selectAccountByDLTz(Map<String, Object> params) {
        R r = new R();
        try {
            SelectAccountByDlDTO selectAccountByDlDTO = BeanUtil.mapToBean(params, SelectAccountByDlDTO.class, false);
            // 读取url
            String fileUrl = selectAccountByDlDTO.getFileUrl();
//            String fileUrl = "file:///D:/3根据市平台台账明细追加子账单.xlsx";
//            InputStream inputStream = new URL(fileUrl).openStream();
            InputStream inputStream = new URL(selectAccountByDlDTO.getFileUrl()).openStream();
            // 获取当前登录人信息
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String platformCode = userInfo.getPlatformCode();
            String platformLevel = userInfo.getPlatformLevel();

            // 查询数据库中对应的箱号
            // 应收数据
            BillCostDetailDTO billCostDetailDTO = new BillCostDetailDTO();
            if (selectAccountByDlDTO.getPageType().equals(0)) {
                BillDealWithCityVO billDealWithCityVO = billDealWithCityMapper.selectBillDealWithCityById(selectAccountByDlDTO.getId(), platformCode, platformLevel);
                if (!ObjectUtils.isEmpty(billDealWithCityVO)) {
                    billCostDetailDTO.setShiftNo(billDealWithCityVO.getProvinceShiftNum());
                    billCostDetailDTO.setBillCode(billDealWithCityVO.getBillCode());
                }
            }
            // 应付数据
            if (selectAccountByDlDTO.getPageType().equals(1)) {
                BillPayProvinceVO billPayProvinceVO = billPayProvinceMapper.selectBillPayProvinceById(selectAccountByDlDTO.getId());
                if (!ObjectUtils.isEmpty(billPayProvinceVO)) {
                    billCostDetailDTO.setShiftNo(billPayProvinceVO.getProvinceShiftNum());
                    billCostDetailDTO.setBillCode(billPayProvinceVO.getBillCode());
                }
            }

            List<BillCheckListVO> billCheckListVOS = new ArrayList<>();

            Workbook workbook = null;

            if (fileUrl.endsWith(".xlsx")) {
                // 使用 XSSFWorkbook 处理 .xlsx 文件
                workbook = new XSSFWorkbook(inputStream);
            } else if (fileUrl.endsWith(".xls")) {
                // 使用 HSSFWorkbook 处理 .xls 文件
                workbook = new HSSFWorkbook(inputStream);
            } else {
                throw new IllegalArgumentException("不支持的文件类型");
            }
            Sheet sheetAt = workbook.getSheetAt(0);
            int rowIn = 3;
            while (true) {
                Row row = sheetAt.getRow(rowIn);
                if (row == null) {
                    break;
                }
                // 箱号
                String boxNum = "";
                Cell cell = row.getCell(11);
                if (cell != null && !cell.getStringCellValue().equals("")) {
                    boxNum = cell.getStringCellValue();
                } else {
                    break;
                }

                billCostDetailDTO.setContainerNumber(boxNum);
                //境内
                Cell cell25 = row.getCell(25);
                if (cell25 != null && CellValueUtil.getCellValueBigDecimal(cell25) != null) {
                    BillCheckListVO billCheckListVO = new BillCheckListVO();
                    // 费目
                    billCostDetailDTO.setCodeSsCategoriesName("国内段包干");
                    billCheckListVO.setExcelCurrency("人民币");
                    BigDecimal amount = CellValueUtil.getCellValueBigDecimal(cell25);
                    billCheckListVO.setExcelLocalAmount(amount);
                    billCheckListVO.setExcelAmountCha(amount);
                    billCheckListVO.setExcelExchangeRate(BigDecimal.valueOf(1));
                    addList(billCheckListVO, selectAccountByDlDTO, billCostDetailDTO, billCheckListVOS);
                }
                //境外
                Cell cell26 = row.getCell(26);
                if (cell26 != null && CellValueUtil.getCellValueBigDecimal(cell26) != null) {
                    BillCheckListVO billCheckListVO = new BillCheckListVO();
                    // 费目
                    billCostDetailDTO.setCodeSsCategoriesName("国外段包干");
                    // 币种
                    String bz = "";
                    Cell cell28 = row.getCell(28);
                    if (cell28 != null && !cell28.getStringCellValue().isEmpty()) {
                        bz = cell28.getStringCellValue();
                    } else {
                        break;
                    }
                    billCheckListVO.setExcelCurrency(bz);
                    BigDecimal amount = CellValueUtil.getCellValueBigDecimal(cell26);

                    Cell cell27 = row.getCell(27);
                    if (cell27 != null && CellValueUtil.getCellValueBigDecimal(cell27) != null) {
                        BigDecimal exchangeRate = CellValueUtil.getCellValueBigDecimal(cell27);
                        billCheckListVO.setExcelExchangeRate(exchangeRate);
                        if (exchangeRate != null) {
                            BigDecimal localAmount = exchangeRate.multiply(amount).setScale(2, RoundingMode.HALF_UP);
                            billCheckListVO.setExcelLocalAmount(localAmount);
                            billCheckListVO.setExcelAmountCha(localAmount);
                        }
                    }
                    addList(billCheckListVO, selectAccountByDlDTO, billCostDetailDTO, billCheckListVOS);
                }
                rowIn++;
            }

            workbook.close();
            r.setData(billCheckListVOS);
            return r;
        } catch (Exception e) {
            r.setMsg("读取数据报错:" + e.getMessage());
            r.setCode(500);
            return r;
        }
    }

    private void addList(BillCheckListVO billCheckListVO, SelectAccountByDlDTO selectAccountByDlDTO, BillCostDetailDTO billCostDetailDTO, List<BillCheckListVO> billCheckListVOS) {
        // 将查询出的数据进行封装
        List<BillCostDetailVO> billCostDetailVOS = new ArrayList<>();
        if (selectAccountByDlDTO.getPageType().equals(0)) {
            billCostDetailVOS = billDealWithCityMapper.selectCostInfoByCostCode(billCostDetailDTO);
        }
        if (selectAccountByDlDTO.getPageType().equals(1)) {
            billCostDetailVOS = billPayProvinceMapper.selectCostInfoByCostCode(billCostDetailDTO);
        }

        if (CollectionUtil.isNotEmpty(billCostDetailVOS)) {
            billCheckListVO.setExcelContainerNumber(billCostDetailVOS.get(0).getContainerNumber());
            billCheckListVO.setSysContainerNumber(billCostDetailVOS.get(0).getContainerNumber());
            billCheckListVO.setSysStartStationName(billCostDetailVOS.get(0).getStartStationName());
            billCheckListVO.setSysEndStationName(billCostDetailVOS.get(0).getEndStationName());
            billCheckListVO.setSysCodeBbCategoriesCode(billCostDetailVOS.get(0).getCodeBbCategoriesCode());
            billCheckListVO.setSysCodeBbCategoriesName(billCostDetailVOS.get(0).getCodeBbCategoriesName());
            billCheckListVO.setSysCodeSsCategoriesCode(billCostDetailVOS.get(0).getCodeSsCategoriesCode());
            billCheckListVO.setSysCodeSsCategoriesName(billCostDetailVOS.get(0).getCodeSsCategoriesName());
            billCheckListVO.setSysCurrency(billCostDetailVOS.get(0).getCurrency());
            billCheckListVO.setSysExchangeRate(billCostDetailVOS.get(0).getExchangeRate());
            BigDecimal sum = billCostDetailVOS.stream().map(BillCostDetailVO::getLocalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            billCheckListVO.setSysLocalAmount(sum);
            billCheckListVO.setExcelAmountCha(billCheckListVO.getExcelLocalAmount().subtract(sum));
        }
        billCheckListVOS.add(billCheckListVO);
    }

    /**
     * 多联费用对比-全账单
     *
     * @param params
     * @return
     */
    @Override
    public R selectAllCostByDL(Map<String, Object> params) {
        R r = new R();
        try {
            SelectAccountByDlDTO selectAccountByDlDTO = BeanUtil.mapToBean(params, SelectAccountByDlDTO.class, false);
            // 读取url
            //String fileUrl = "file:///C:/Users/<USER>/Desktop/spy-oy-sql/多联导入.xls";
            //InputStream inputStream = new URL(fileUrl).openStream();
            InputStream inputStream = new URL(selectAccountByDlDTO.getFileUrl()).openStream();

            // 获取当前登录人信息
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String platformCode = userInfo.getPlatformCode();
            String platformLevel = userInfo.getPlatformLevel();

            String dl = remoteAdminService.selectCategoriesDict("费用类型");
            List<CategoriesDictVO> list = JSONUtil.toList(JSONUtil.parseArray(dl), CategoriesDictVO.class);

            // 查询数据库中对应的箱号
            // 应收数据
            BillCostDetailDTO billCostDetailDTO = new BillCostDetailDTO();
            billCostDetailDTO.setStartSite(selectAccountByDlDTO.getStartStation());
            billCostDetailDTO.setEndSite(selectAccountByDlDTO.getEndStation());
            billCostDetailDTO.setPlatformCode(platformCode);
            billCostDetailDTO.setShippingLines(selectAccountByDlDTO.getShippingLineCode());
            billCostDetailDTO.setDirection(selectAccountByDlDTO.getTrip());

            List<ExcelAddCostVO> excelAddCostVOS = new ArrayList<>();
            Workbook sheets = new HSSFWorkbook(inputStream);
            Sheet sheetAt = sheets.getSheetAt(1);
            int rowIn = 2;
            while (true) {
                Row row = sheetAt.getRow(rowIn);
                if (row == null) {
                    break;
                }
                // 箱号
                String boxNum = "";
                Cell cell = row.getCell(1);
                if (cell != null && !cell.getStringCellValue().equals("")) {
                    boxNum = cell.getStringCellValue().trim();
                } else {
                    break;
                }
                ExcelAddCostVO excelAddCostVO = new ExcelAddCostVO();

                excelAddCostVO.setContainerNumber(boxNum);

                billCostDetailDTO.setContainerNumber(boxNum);

                // 费目
                String costMd = "";
                Cell cell5 = row.getCell(5);
                if (cell5 != null && !cell5.getStringCellValue().equals("")) {
                    costMd = cell5.getStringCellValue();
                } else {
                    break;
                }
                if (CollUtil.isNotEmpty(list) && StrUtil.isNotBlank(costMd)) {
                    for (CategoriesDictVO dictVO : list
                    ) {
                        if (costMd.equals(dictVO.getCodeSsCategoriesName())) {
                            billCostDetailDTO.setCodeSsCategoriesName(costMd);
                            excelAddCostVO.setCodeSsCategoriesName(costMd);
                            excelAddCostVO.setCodeSsCategoriesCode(dictVO.getCodeSsCategoriesCode());
                            excelAddCostVO.setCodeBbCategoriesName(dictVO.getCodeBbCategoriesName());
                            excelAddCostVO.setCodeBbCategoriesCode(dictVO.getCodeBbCategoriesCode());
                        }
                    }
                }


                // 汇率
                BigDecimal hl = new BigDecimal("0.00");
                Cell cell6 = row.getCell(6);
                if (cell6 != null) {
                    if (cell6.getCellTypeEnum() == CellType.STRING) {
                        hl = new BigDecimal(cell6.getStringCellValue());
                    }
                    if (cell6.getCellTypeEnum() == CellType.NUMERIC) {
                        hl = new BigDecimal(cell6.getNumericCellValue());
                    }
                } else {
                    break;
                }
                excelAddCostVO.setExchangeRate(hl);


                // 币种
                String bz = "";
                Cell cell7 = row.getCell(7);
                if (cell7 != null && !cell7.getStringCellValue().equals("")) {
                    bz = cell7.getStringCellValue();
                } else {
                    break;
                }
                excelAddCostVO.setCurrency(bz);
                billCostDetailDTO.setCurrency(bz);


                // 金额(本币)
                BigDecimal amount = new BigDecimal("0.00");
                Cell cell8 = row.getCell(8);
                if (cell8 != null) {
                    if (cell8.getCellTypeEnum() == CellType.STRING) {
                        amount = new BigDecimal(cell8.getStringCellValue());
                    }
                    if (cell8.getCellTypeEnum() == CellType.NUMERIC) {
                        amount = new BigDecimal(cell8.getNumericCellValue());
                    }
                } else {
                    break;
                }
                excelAddCostVO.setOriginalAmount(amount.divide(excelAddCostVO.getExchangeRate(), BigDecimal.ROUND_CEILING));
                excelAddCostVO.setLocalAmount(amount);
                excelAddCostVO.setAddAmount(amount);

                Cell cell19 = row.getCell(19);
                if (cell19 != null) {

                    try {
                        if (cell19.getCellTypeEnum() == CellType.NUMERIC && DateUtil.isCellDateFormatted(cell19)) {
                            // 读取 Excel 单元格的日期时间数值
                            Date excelDate = cell19.getDateCellValue();

                            // 将 Date 转换为 LocalDateTime
                            LocalDateTime localDateTime = LocalDateTime.ofInstant(excelDate.toInstant(), java.time.ZoneId.systemDefault());

                            // 定义输出的日期格式
                            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

                            // 格式化为 yyyy-MM-dd 格式
                            String formattedDate = localDateTime.format(outputFormatter);

//                            if (formattedDate.compareTo(selectAccountByDlDTO.getPostStartTime()) >= 0 && formattedDate.compareTo(selectAccountByDlDTO.getPostEndTime()) <= 0) {
                            billCostDetailDTO.setStartTime(formattedDate);
                            billCostDetailDTO.setEndTime(formattedDate);
//                            } else {
//                                continue;
//                            }
                        }
                    } catch (Exception e) {
                        System.out.println("Invalid date format: " + e.getMessage());
//                        billCostDetailDTO.setStartTime(selectAccountByDlDTO.getPostStartTime());
//                        billCostDetailDTO.setEndTime(selectAccountByDlDTO.getPostEndTime());
                    }
                }
//                else {
//                    billCostDetailDTO.setStartTime(selectAccountByDlDTO.getPostStartTime());
//                    billCostDetailDTO.setEndTime(selectAccountByDlDTO.getPostEndTime());
//                }


                // 将查询出的数据进行封装
                List<BillCostDetailVO> billCostDetailVOS = new ArrayList<>();
                if (selectAccountByDlDTO.getPageType().equals(0)) {
                    billCostDetailVOS = billPayProvinceMapper.selectCostListPaymentsByExcel(billCostDetailDTO);
                }
                if (selectAccountByDlDTO.getPageType().equals(1)) {
                    billCostDetailVOS = billPayProvinceMapper.selectCostListPayByExcel(billCostDetailDTO);
                }

                if (CollectionUtil.isNotEmpty(billCostDetailVOS)) {
                    excelAddCostVO.setContainerOwner(billCostDetailVOS.get(0).getContainerOwner());
                    excelAddCostVO.setContainerTypeCode(billCostDetailVOS.get(0).getContainerTypeCode());
                    excelAddCostVO.setProvinceShiftNo(billCostDetailVOS.get(0).getProvinceShiftNo());
                    excelAddCostVO.setPostDate(billCostDetailVOS.get(0).getPostDate());
                    excelAddCostVO.setShippingLines(billCostDetailVOS.get(0).getShippingLines());
                    excelAddCostVO.setDirection(billCostDetailVOS.get(0).getDirection());
                    excelAddCostVO.setShiftNo(billCostDetailVOS.get(0).getShiftNo());
                }
                excelAddCostVOS.add(excelAddCostVO);
                rowIn++;
            }

            sheets.close();
            r.setData(excelAddCostVOS);
            return r;
        } catch (Exception e) {
            e.printStackTrace();
            r.setMsg("读取数据报错");
            r.setCode(500);
            return r;
        }
    }

    /**
     * 追加子帐单
     *
     * @param addCostByBillDTO
     * @return
     */
    @Override
    public R addSubBill(AddCostByBillDTO addCostByBillDTO) {
        R r = new R();
        try {
            // 查询出账单数据
            // 主账单Id
            Integer id = addCostByBillDTO.getId();
            Integer pageType = addCostByBillDTO.getPageType();
            // 获取当前登录人信息
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String platformCode = userInfo.getPlatformCode();
            String platformLevel = userInfo.getPlatformLevel();
            String platformName = userInfo.getPlatformName();

            String shiftNo = "";
            String billCode = "";
            int listSize = 0;
            String costCode = "";
            // 应收
            if (pageType.equals(0)) {
                // 查询市应付账单数据
                BillDealWithCityVO billDealWithCityVO = billDealWithCityMapper.selectBillDealWithCityById(id, platformCode, platformLevel);
                if (ObjectUtils.isEmpty(billDealWithCityVO)) {
                    r.setCode(500);
                    r.setMsg("未查询出账单主数据");
                    return r;
                }
                List<BillSubPayCity> billSubPayCities = billDealWithCityMapper.selectBillSubByBillCode(billDealWithCityVO.getBillCode());
                if (CollectionUtil.isEmpty(billSubPayCities)) {
                    r.setCode(500);
                    r.setMsg("未查询出账单数据");
                    return r;
                }
                shiftNo = billDealWithCityVO.getProvinceShiftNum();
                billCode = billDealWithCityVO.getBillCode();
                listSize = billSubPayCities.size();
                costCode = billSubPayCities.get(0).getCostCode();
            }

            if (pageType.equals(1)) {
                BillPayProvinceVO billPayProvinceVO = billPayProvinceMapper.selectBillPayProvinceById(id);
                if (ObjectUtils.isEmpty(billPayProvinceVO)) {
                    r.setCode(500);
                    r.setMsg("未查询出账单主数据");
                    return r;
                }
                List<BillPayProvinceSub> billPayProvinceSubs = billPayProvinceSubService.selectBillPayProvinceSubByBillCode(billPayProvinceVO.getBillCode());
                if (CollectionUtil.isEmpty(billPayProvinceSubs)) {
                    r.setCode(500);
                    r.setMsg("未查询出账单数据");
                    return r;
                }
                shiftNo = billPayProvinceVO.getProvinceShiftNum();
                billCode = billPayProvinceVO.getBillCode();
                listSize = billPayProvinceSubs.size();
                costCode = billPayProvinceSubs.get(0).getCostCode();
            }

            if (listSize == 0) {
                r.setCode(500);
                r.setMsg("未查询出账单数据");
                return r;
            }

            // 将数据封装
            // 班列信息
            Shifmanagement shifmanagement = billDealWithCityMapper.selectShiftInfo(shiftNo, platformCode);
            // 费用流程单
            //billDealWithCityMapper.selectFdBusCodeBy
            // 查询出发运台账信息
            FdShippingAccount fdShippingAccount = billDealWithCityMapper.selectAccountByShiftNoAndPlatformCode(shiftNo, platformCode);
            // 生成子帐单数据
            BillSubPayCity billSubPayCity = new BillSubPayCity();
            billSubPayCity.setBillCode(billCode);
            String billSubCode = billCode + "-" + String.format("%03d", listSize + 1);
            billSubPayCity.setBillSubCode(billSubCode);
            //billSubPayCity.setWaybillNo();
            FdBusCost sel2 = new FdBusCost();
            sel2.setShiftNo(shiftNo);
            sel2.setPlatformCode(platformCode);
            sel2.setDeleteFlag("N");
            List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel2);
            if (CollUtil.isNotEmpty(fdBusCosts)) {
                billSubPayCity.setCostCode(fdBusCosts.get(0).getCostCode());
            }
            billSubPayCity.setAccountCode(fdShippingAccount.getAccountCode());
            billSubPayCity.setPlatformCode(platformCode);
            billSubPayCity.setPlatformName(platformName);
            billSubPayCity.setPlatformLevel(platformLevel);
            billSubPayCity.setProvinceShiftNo(shifmanagement.getProvinceShiftNo());
            billSubPayCity.setShiftNo(shifmanagement.getShiftId());
            billSubPayCity.setShiftName(shifmanagement.getShiftName());

            BigDecimal reduce = addCostByBillDTO.getAddList().stream().map(AddCostByBillListDTO::getLocalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            billSubPayCity.setBillAmount(reduce);
            billSubPayCity.setBillingState("0");
            billSubPayCity.setAddWho(platformCode);
            billSubPayCity.setAddWhoName(platformName);
            billSubPayCity.setAddTime(LocalDateTime.now());
            billSubPayCity.setCustomerCode(fdShippingAccount.getPlatformCode());
            billSubPayCity.setCustomerName(fdShippingAccount.getPlatformName());
            billSubPayCity.setPortStation(shifmanagement.getPortStation());
            billSubPayCity.setStatus("1");
            billSubPayCity.setBoxNum(addCostByBillDTO.getAddList().size());
            billSubPayCity.setCostCode(costCode);
            // 费用明细增加
            List<FdBusCostDetail> fdBusCostDetails = new ArrayList<>();
            for (AddCostByBillListDTO addCostByBillListDTO : addCostByBillDTO.getAddList()) {
                FdBusCostDetail fdBusCostDetail = new FdBusCostDetail();
                fdBusCostDetail.setCostCode(costCode);
                fdBusCostDetail.setCostType("0");
                fdBusCostDetail.setContainerNumber(addCostByBillListDTO.getBoxNum());
                fdBusCostDetail.setCodeBbCategoriesCode(addCostByBillListDTO.getCodeBbCategoriesCode());
                fdBusCostDetail.setCodeBbCategoriesName(addCostByBillListDTO.getCodeBbCategoriesName());
                fdBusCostDetail.setCodeSsCategoriesCode(addCostByBillListDTO.getCodeSsCategoriesCode());
                fdBusCostDetail.setCodeSsCategoriesName(addCostByBillListDTO.getCodeSsCategoriesName());
                fdBusCostDetail.setReceiveCode(platformCode);
                fdBusCostDetail.setReceiveName(platformName);
                fdBusCostDetail.setPayCode(fdShippingAccount.getCustomerNo());
                fdBusCostDetail.setPayName(fdShippingAccount.getCustomerName());
                fdBusCostDetail.setCurrency(addCostByBillListDTO.getCurrency());
                fdBusCostDetail.setExchangeRate(addCostByBillListDTO.getExchangeRate());
                fdBusCostDetail.setOriginalAmount(addCostByBillListDTO.getOriginalAmount());
                fdBusCostDetail.setLocalAmount(addCostByBillListDTO.getLocalAmount());
                fdBusCostDetail.setAuditStatus("0");
                fdBusCostDetail.setAddWho(platformCode);
                fdBusCostDetail.setAddWhoName(platformName);
                fdBusCostDetail.setAddTime(LocalDateTime.now());
                fdBusCostDetail.setShiftNo(shifmanagement.getShiftId());
                fdBusCostDetail.setBillSubCode(billSubCode);
                fdBusCostDetails.add(fdBusCostDetail);
            }

            billSubPayCityMapper.insert(billSubPayCity);
            busCostDetailService.insertBatch(fdBusCostDetails);
            return r;
        } catch (Exception e) {
            e.printStackTrace();
            r.setMsg("读取数据报错");
            r.setCode(500);
            return r;
        }
    }

    /**
     * 新增账单
     *
     * @param provinceAddBillDTO
     * @return
     */
    @Override
    public R addBill(ProvinceAddBillDTO provinceAddBillDTO) {
        R r = new R();
        // 获取当前登录人信息
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String platformCode = userInfo.getPlatformCode();
        String platformLevel = userInfo.getPlatformLevel();
        String platformName = userInfo.getPlatformName();

        // 转为map
        Map<String, List<AddBillDTO>> map = new HashMap<>();
        for (AddBillDTO addBillDTO : provinceAddBillDTO.getAddBillDTOList()) {
            if (CollectionUtils.isEmpty(map.get(addBillDTO.getShiftNo()))) {
                List<AddBillDTO> adds = new ArrayList<>();
                adds.add(addBillDTO);
                map.put(addBillDTO.getShiftNo(), adds);
            } else {
                List<AddBillDTO> addBillDTOS = map.get(addBillDTO.getShiftNo());
                addBillDTOS.add(addBillDTO);
                map.put(addBillDTO.getShiftNo(), addBillDTOS);
            }
        }

        for (String shiftNo : map.keySet()) {
            // 班次信息
            Shifmanagement shifmanagement = billPayProvinceMapper.selectShiftInfoByProvince(shiftNo);

            // 台账信息
            FdShippingAccount fdShippingAccount = billPayProvinceMapper.selectShippingAccountByShiftNo(shiftNo);

            // 存储数据
            List<AddBillDTO> addBillDTOS = map.get(shiftNo);

            BigDecimal sumMoney = addBillDTOS.stream().map(AddBillDTO::getAddAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

            String billSubCode = "";

            // 判断是应收数据还是应付数据
            if (provinceAddBillDTO.getPageType().equals(0)) {
                // 存放账单信息
                // 查询出主账单数据
                BillDealWithCity billDealWithCity = billDealWithCityMapper.selectBillDealWithCityByShiftNo(shiftNo, platformName);
                if (ObjectUtils.isEmpty(billDealWithCity)) {
                    break;
                }
                List<BillSubPayCity> billSubPayCities = billDealWithCityMapper.selectBillSubByBillCode(billDealWithCity.getBillCode());
                if (CollectionUtil.isNotEmpty(billSubPayCities)) {
                    billSubCode = billDealWithCity.getBillCode() + "-" + String.format("%03d", billSubPayCities.size() + 1);
                }
                // 生成子账单
                BillSubPayCity billSubPayCity = new BillSubPayCity();
                billSubPayCity.setBillCode(billDealWithCity.getBillCode());
                billSubPayCity.setBillSubCode(billSubCode);
                billSubPayCity.setAccountCode(fdShippingAccount.getAccountCode());
                billSubPayCity.setPlatformCode(platformCode);
                billSubPayCity.setPlatformName(userInfo.getPlatformName());
                billSubPayCity.setPlatformLevel(platformLevel);
                billSubPayCity.setProvinceShiftNo(shifmanagement.getProvinceShiftNo());
                billSubPayCity.setShiftNo(shiftNo);
                billSubPayCity.setShiftName(shifmanagement.getShiftName());
                billSubPayCity.setBillAmount(sumMoney);
                billSubPayCity.setBillingState("0");
                billSubPayCity.setDeleteFlag("N");
                billSubPayCity.setAddWho(platformCode);
                billSubPayCity.setAddWhoName(userInfo.getPlatformName());
                billSubPayCity.setAddTime(LocalDateTime.now());
                billSubPayCity.setCustomerCode(shifmanagement.getPlatformCode());
                billSubPayCity.setCustomerName(shifmanagement.getResveredField02());
                billSubPayCity.setPortStation(shifmanagement.getPortStation());
                billSubPayCity.setStatus("1");
                billSubPayCity.setOffsetBalance(new BigDecimal("0"));
                billSubPayCity.setIsAdd("1");
                billSubPayCity.setShipmentTime(LocalDateTime.ofInstant(shifmanagement.getPlanShipTime().toInstant(), ZoneId.systemDefault()));
                billSubPayCity.setBillType("YF");
                billSubPayCity.setBoxNum(addBillDTOS.size());

                //billDealWithCityMapper.insertBillDealWithCity(billDealWithCity);
                billSubPayCityMapper.insertBillSubPayCity(billSubPayCity);

            }

            if (provinceAddBillDTO.getPageType().equals(1)) {
                // 存放账单信息
                BillPayProvince billPayProvince = billPayProvinceMapper.selectBillPayProvinceByShift(shiftNo, platformName);
                if (ObjectUtils.isEmpty(billPayProvince)) {
                    break;
                }
                List<BillPayProvinceSub> billPayProvinceSubs = billPayProvinceSubService.selectBillPayProvinceSubByBillCode(billPayProvince.getBillCode());
                if (CollectionUtil.isNotEmpty(billPayProvinceSubs)) {
                    billSubCode = billPayProvince.getBillCode() + "-" + String.format("%03d", billPayProvinceSubs.size() + 1);
                }

                // 生成子账单
                BillPayProvinceSub billPayProvinceSub = new BillPayProvinceSub();
                billPayProvinceSub.setBillCode(billPayProvince.getBillCode());
                billPayProvinceSub.setBillSubCode(billSubCode);
                billPayProvinceSub.setAccountCode(fdShippingAccount.getAccountCode());
                billPayProvinceSub.setPlatformCode(platformCode);
                billPayProvinceSub.setPlatformName(userInfo.getPlatformName());
                billPayProvinceSub.setPlatformLevel(platformLevel);
                billPayProvinceSub.setProvinceShiftNo(shifmanagement.getProvinceShiftNo());
                billPayProvinceSub.setShiftNo(shiftNo);
                billPayProvinceSub.setShiftName(shifmanagement.getShiftName());
                billPayProvinceSub.setBillAmount(sumMoney);
                billPayProvinceSub.setBillingState("0");
                billPayProvinceSub.setDeleteFlag("N");
                billPayProvinceSub.setAddWho(platformCode);
                billPayProvinceSub.setAddWhoName(userInfo.getPlatformName());
                billPayProvinceSub.setAddTime(new Date());
                billPayProvinceSub.setCustomerCode(shifmanagement.getPlatformCode());
                billPayProvinceSub.setCustomerName(shifmanagement.getResveredField02());
                billPayProvinceSub.setPortStation(shifmanagement.getPortStation());
                billPayProvinceSub.setStatus("1");
                billPayProvinceSub.setOffsetBalance(new BigDecimal("0"));
                billPayProvinceSub.setIsAdd("1");
                billPayProvinceSub.setShipmentTime(shifmanagement.getPlanShipTime());
                billPayProvinceSub.setBillType("YF");
                billPayProvinceSub.setBoxNum(addBillDTOS.size());

                //billPayProvinceMapper.insertBillPayProvince(billPayProvince);
                billPayProvinceSubService.insertBillPayProvinceSub(billPayProvinceSub);

            }

            // 费用明细增加
            List<FdBusCostDetail> fdBusCostDetails = new ArrayList<>();
            for (AddBillDTO addBillDTO : addBillDTOS) {
                FdBusCostDetail fdBusCostDetail = new FdBusCostDetail();
                fdBusCostDetail.setCostCode(sysNoConfigService.genNo("FDC"));
                fdBusCostDetail.setCostType("0");
                fdBusCostDetail.setContainerNumber(addBillDTO.getContainerNumber());
                fdBusCostDetail.setCodeBbCategoriesCode(addBillDTO.getCodeBbCategoriesCode());
                fdBusCostDetail.setCodeBbCategoriesName(addBillDTO.getCodeBbCategoriesName());
                fdBusCostDetail.setCodeSsCategoriesCode(addBillDTO.getCodeSsCategoriesCode());
                fdBusCostDetail.setCodeSsCategoriesName(addBillDTO.getCodeSsCategoriesName());
                fdBusCostDetail.setReceiveCode(platformCode);
                fdBusCostDetail.setReceiveName(userInfo.getPlatformName());
                fdBusCostDetail.setPayCode(fdShippingAccount.getCustomerNo());
                fdBusCostDetail.setPayName(fdShippingAccount.getCustomerName());
                fdBusCostDetail.setCurrency(addBillDTO.getCurrency());
                fdBusCostDetail.setExchangeRate(addBillDTO.getExchangeRate());
                fdBusCostDetail.setOriginalAmount(addBillDTO.getOriginalAmount());
                fdBusCostDetail.setLocalAmount(addBillDTO.getAddAmount());
                fdBusCostDetail.setAuditStatus("0");
                fdBusCostDetail.setAddWho(platformCode);
                fdBusCostDetail.setAddWhoName(userInfo.getPlatformName());
                fdBusCostDetail.setAddTime(LocalDateTime.now());
                fdBusCostDetail.setShiftNo(shiftNo);
                fdBusCostDetail.setBillSubCode(billSubCode);
                fdBusCostDetails.add(fdBusCostDetail);
            }

            busCostDetailService.insertBatch(fdBusCostDetails);

        }

        return r;
    }


}
