package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.FdInvoiceApplicationDetail;
import com.huazheng.tunny.ocean.api.vo.FdInvoiceStatisticsVO;

import java.util.List;

/**
 * 开票申请明细表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-08-23 18:21:02
 */
public interface FdInvoiceApplicationDetailService extends IService<FdInvoiceApplicationDetail> {
    /**
     * 查询开票申请明细表信息
     *
     * @param id 开票申请明细表ID
     * @return 开票申请明细表信息
     */
    public FdInvoiceApplicationDetail selectFdInvoiceApplicationDetailById(Integer id);

    /**
     * 查询开票申请明细表列表
     *
     * @param fdInvoiceApplicationDetail 开票申请明细表信息
     * @return 开票申请明细表集合
     */
    public List<FdInvoiceApplicationDetail> selectFdInvoiceApplicationDetailList(FdInvoiceApplicationDetail fdInvoiceApplicationDetail);


    /**
     * 分页模糊查询开票申请明细表列表
     * @return 开票申请明细表集合
     */
    public Page selectFdInvoiceApplicationDetailListByLike(Query query);

    /**
     * 查询用户下的费用明细
     *
     * @return 开票申请明细表集合
     */
    public Page selectCanInvoiceInfo(Query query);

    public Page selectCanInvoiceInfos(Query query);

    /**
     * 新增开票申请明细表
     *
     * @param detailList 开票申请明细表信息
     * @return 结果
     */
    public R insertFdInvoiceApplicationDetail(List<FdInvoiceApplicationDetail> detailList);

    /**
     * 修改开票申请明细表
     *
     * @param fdInvoiceApplicationDetail 开票申请明细表信息
     * @return 结果
     */
    public int updateFdInvoiceApplicationDetail(FdInvoiceApplicationDetail fdInvoiceApplicationDetail);

    /**
     * 删除开票申请明细表
     *
     * @param id 开票申请明细表ID
     * @return 结果
     */
    public int deleteFdInvoiceApplicationDetailById(Integer id);

    /**
     * 批量删除开票申请明细表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdInvoiceApplicationDetailByIds(Integer[] ids);

    R cancel(Integer id);


    /**
     * 发票统计列表接口
     */
    public Page selectInvoiceStatisticsPage2(Query query);

    R selectInvoiceStatisticsList(FdInvoiceStatisticsVO fdInvoiceStatisticsVO);

    R selectInvoiceStatisticsWk(FdInvoiceStatisticsVO fdInvoiceStatisticsVO);

    R selectInvoiceStatisticsYk(FdInvoiceStatisticsVO fdInvoiceStatisticsVO);

    Page selectCanInvoiceInfoNew(Query<Object> objectQuery);
}

