package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.mapper.WaybillParticipantsMapper;
import com.huazheng.tunny.ocean.api.entity.WaybillParticipants;
import com.huazheng.tunny.ocean.service.WaybillParticipantsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service("waybillParticipantsService")
public class WaybillParticipantsServiceImpl extends ServiceImpl<WaybillParticipantsMapper, WaybillParticipants> implements WaybillParticipantsService {

    @Autowired
    private WaybillParticipantsMapper waybillParticipantsMapper;

    public WaybillParticipantsMapper getWaybillParticipantsMapper() {
        return waybillParticipantsMapper;
    }

    public void setWaybillParticipantsMapper(WaybillParticipantsMapper waybillParticipantsMapper) {
        this.waybillParticipantsMapper = waybillParticipantsMapper;
    }

    /**
     * 查询运单参与方信息表信息
     *
     * @param rowId 运单参与方信息表ID
     * @return 运单参与方信息表信息
     */
    @Override
    public WaybillParticipants selectWaybillParticipantsById(String rowId)
    {
        return waybillParticipantsMapper.selectWaybillParticipantsById(rowId);
    }

    /**
     * 查询运单参与方信息表列表
     *
     * @param waybillParticipants 运单参与方信息表信息
     * @return 运单参与方信息表集合
     */
    @Override
    public List<WaybillParticipants> selectWaybillParticipantsList(WaybillParticipants waybillParticipants)
    {
        return waybillParticipantsMapper.selectWaybillParticipantsList(waybillParticipants);
    }

    @Override
    public List<WaybillParticipants> selectWaybillParticipantsByWayBillNo(WaybillParticipants waybillParticipants) {
        return waybillParticipantsMapper.selectWaybillParticipantsByWayBillNo(waybillParticipants);
    }


    /**
     * 分页模糊查询运单参与方信息表列表
     * @return 运单参与方信息表集合
     */
    @Override
    public Page selectWaybillParticipantsListByLike(Query query)
    {
        WaybillParticipants waybillParticipants =  BeanUtil.mapToBean(query.getCondition(), WaybillParticipants.class,false);
        query.setRecords(waybillParticipantsMapper.selectWaybillParticipantsListByLike(query,waybillParticipants));
        return query;
    }

    /**
     * 新增运单参与方信息表
     *
     * @param list 运单参与方信息表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertWaybillParticipants(List<WaybillParticipants> list)
    {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        for (WaybillParticipants pants:list) {
            pants.setRowId(UUID.randomUUID().toString());
            pants.setAddTime(LocalDateTime.now());
            pants.setAddWho(usercode);
            pants.setAddWhoName(username);
        }
        return waybillParticipantsMapper.insertWaybillParticipants(list);
    }

    /**
     * 批量更新运单参与方信息
     *
     * @param list 运单参与方信息表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateWaybillParticipants(List<WaybillParticipants> list)
    {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        for (WaybillParticipants pants:list) {
            pants.setUpdateTime(LocalDateTime.now());
            pants.setUpdateWho(usercode);
            pants.setUpdateWhoName(username);
            waybillParticipantsMapper.updateWaybillParticipant(pants);
        }
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertOrUpdateWaybillParticipants(List<WaybillParticipants> list, List<String> waybillCodes)
    {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        if(CollUtil.isNotEmpty(waybillCodes)){
            for (String waybillNo: waybillCodes
                 ) {
                if(CollUtil.isNotEmpty(list)){
                    for (WaybillParticipants pants:list) {
                        pants.setWaybillNo(waybillNo);
                        pants.setUpdateTime(LocalDateTime.now());
                        pants.setUpdateWho(usercode);
                        pants.setUpdateWhoName(username);
                        int i = waybillParticipantsMapper.updateWaybillParticipant(pants);
                        if(i <= 0){
                            pants.setRowId(UUID.randomUUID().toString());
                            pants.setDeleteFlag("N");
                            pants.setAddTime(LocalDateTime.now());
                            pants.setAddWho(usercode);
                            pants.setAddWhoName(username);
                            waybillParticipantsMapper.insertWaybillParticipant(pants);
                        }
                    }
                }
            }
        }
    }


    /**
     * 删除运单参与方信息表
     *
     * @param rowId 运单参与方信息表ID
     * @return 结果
     */
    @Override
    public int deleteWaybillParticipantsById(String rowId)
    {
        return waybillParticipantsMapper.deleteWaybillParticipantsById( rowId);
    };


    /**
     * 批量删除运单参与方信息表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteWaybillParticipantsByIds(Integer[] rowIds)
    {
        return waybillParticipantsMapper.deleteWaybillParticipantsByIds( rowIds);
    }

    @Override
    public Integer selectWaybillParticipantsByCount(WaybillParticipants waybillParticipants) {
        return waybillParticipantsMapper.selectWaybillParticipantsByCount(waybillParticipants);
    }

    @Override
    public WaybillParticipants selectWaybillParticipantsByList(WaybillParticipants waybillParticipants) {
        return waybillParticipantsMapper.selectWaybillParticipantsByList(waybillParticipants);
    }

    @Override
    public int updateWaybillParticipantsByWayBillNo(WaybillParticipants waybillParticipants) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        waybillParticipants.setDeleteWho(userInfo.getUserName());
        waybillParticipants.setDeleteWhoName(userInfo.getRealName());
        return waybillParticipantsMapper.updateWaybillParticipantsByWayBillNo(waybillParticipants);
    }

    @Override
    public int deleteWaybillParticipantsByContainerNo(WaybillParticipants waybillParticipants) {
        return waybillParticipantsMapper.deleteWaybillParticipantsByContainerNo(waybillParticipants);
    }

}
