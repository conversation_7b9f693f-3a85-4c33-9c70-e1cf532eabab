package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.ShiftStat;
import com.huazheng.tunny.ocean.api.entity.ShiftStatDetail;
import com.huazheng.tunny.ocean.mapper.ShiftStatDetailMapper;
import com.huazheng.tunny.ocean.mapper.ShiftStatMapper;
import com.huazheng.tunny.ocean.service.ShiftStatService;
import com.huazheng.tunny.ocean.util.ExportStyleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 班次统计表Service
 *
 * <AUTHOR>
 * @since 2025-05-06 16:19:09
 */
@Slf4j
@Service
public class ShiftStatServiceImpl extends ServiceImpl<ShiftStatMapper, ShiftStat> implements ShiftStatService {

    @Autowired
    private ShiftStatMapper shiftStatMapper;
    @Autowired
    private ShiftStatDetailMapper shiftStatDetailMapper;

    private static final String CELL_TYPE_PROVINCE = "province";
    private static final String CELL_TYPE_CITY = "city";
    private static final String CHINA_EUROPE = "中欧";
    private static final String CHINA_ASIA = "中亚";
    /**
     * 单元格列数
     */
    public static final int TOTAL_COLUMN_COUNT = 22;
    /**
     * 单元格标题行数
     */
    public static final int TOTAL_ROW_COUNT = 6;
    /**
     * 明细中的三级标题{去程、回程、列数	排名	}
     */
    public static final String[] TOTAL_COLUMN_TITLE_ARRAY = new String[]{"去程", "回程", "列数", "排名", "去程", "回程", "小计",
            "去程", "回程", "列数", "排名", "去程", "回程", "小计",
            "去程", "回程", "列数", "排名", "去程", "回程", "小计"};
    /**
     * 特殊省市
     */
    public static final String[] SPECIAL_PROVINCE_CITY = new String[]{"山东", "青岛", "济南", "临沂", "淄博", "潍坊", "烟台", "日照"};
    /**
     * 主要城市列数
     */
    public static final int MAIN_CITY_COLUMN_COUNT = 23;
    /**
     * 主要城市标题行数
     */
    public static final int MAIN_CITY_ROW_COUNT = 5;
    /**
     * 主要省份同比列数
     */
    private static final int MAIN_PROVINCE_COLUMN_COUNT = 13;
    /**
     * 主要省份同比行数
     */
    private static final int MAIN_PROVINCE_ROW_COUNT = 3;
    /**
     * 云南、广西数据列数
     */
    private static final int YUNAN_GUANGXI_COLUMN_COUNT = 22;
    /**
     * 云南、广西数据行数
     */
    private static final int YUNAN_GUANGXI_ROW_COUNT = 3;

    /**
     * 总数sheet和主要省份同比可以共用的变量
     */
    private Map<String, ShiftStatDetail> shiftStatDetailMap;
    private Map<String, Integer> chinaEuropeMap;
    private Map<String, Integer> chinaAsiaMap;
    private Map<String, Integer> sortTotalDetail;
    private List<String> provinceList;

    /**
     * 班次统计表分页
     *
     * @param shiftStat 班次统计表
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:19:09
     */
    @Override
    public Page<ShiftStat> page(ShiftStat shiftStat) {
        Page<ShiftStat> page = new Page<>(shiftStat.getPage() == null ? 1 : shiftStat.getPage(),
                shiftStat.getLimit() == null ? 10 : shiftStat.getLimit());
        List<ShiftStat> records = shiftStatMapper.pageShiftStat(page, shiftStat);
        page.setRecords(records);
        return page;
    }

    /**
     * 班次统计表详情
     *
     * @param statId 班次统计id
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:19:09
     */
    @Override
    public R<ShiftStat> info(Integer statId) {
        ShiftStat shiftStat = shiftStatMapper.infoShiftStat(statId);
        ShiftStatDetail shiftStatDetail = new ShiftStatDetail();
        shiftStatDetail.setStatId(shiftStat.getStatId());
        List<ShiftStatDetail> shiftStatDetails = shiftStatDetailMapper.listShiftStatDetail(shiftStatDetail);
        List<ShiftStatDetail> chinaEuropeDetails = new ArrayList<>();
        List<ShiftStatDetail> chinaAsiaDetails = new ArrayList<>();
        for (ShiftStatDetail detail : shiftStatDetails) {
            if (CHINA_EUROPE.equals(detail.getShiftType())) {
                chinaEuropeDetails.add(detail);
            } else if (CHINA_ASIA.equals(detail.getShiftType())) {
                chinaAsiaDetails.add(detail);
            }
        }
        shiftStat.setChinaAsiaDetails(chinaAsiaDetails);
        shiftStat.setChinaEuropeDetails(chinaEuropeDetails);
        return R.success(shiftStat);
    }

    /**
     * 班次统计表保存
     *
     * @param shiftStat 班次统计
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:19:09
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<ShiftStat> save(ShiftStat shiftStat) {
        List<ShiftStatDetail> chinaAsiaDetails = shiftStat.getChinaAsiaDetails();
        List<ShiftStatDetail> chinaEuropeDetails = shiftStat.getChinaEuropeDetails();
        if (CollUtil.isEmpty(chinaAsiaDetails)) {
            return R.error("中亚班次统计详情不能为空");
        }
        if (CollUtil.isEmpty(chinaEuropeDetails)) {
            return R.error("中欧班次统计详情不能为空");
        }
        //判断明细中的省的个数是否小于10
        List<String> provinceList = chinaAsiaDetails.stream().map(ShiftStatDetail::getProvince).distinct().collect(Collectors.toList());
        //表格只取前十名
        int ranking = 10;
        if (provinceList.size() < ranking) {
            return R.error("中亚班次统计详情中的省不能小于8个");
        }
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
        if (shiftStat.getStatId() != null) {
            shiftStatDetailMapper.deleteShiftStatDetailByStatId(shiftStat.getStatId(), secruityUser.getId());
            shiftStat.setUpdateBy(secruityUser.getId());
            shiftStat.setUpdateName(secruityUser.getRealName());
            shiftStat.setUpdateTime(LocalDateTime.now());
            shiftStatMapper.updateShiftStat(shiftStat);
        } else {
            shiftStat.setCreateBy(secruityUser.getId());
            shiftStat.setCreateName(secruityUser.getRealName());
            shiftStat.setCreateTime(LocalDateTime.now());
            shiftStat.setUpdateBy(secruityUser.getId());
            shiftStat.setUpdateName(secruityUser.getRealName());
            shiftStat.setCreateBy(secruityUser.getId());
            shiftStatMapper.saveShiftStat(shiftStat);
        }
        List<ShiftStatDetail> shiftStatDetails = new ArrayList<>(chinaAsiaDetails.size() + chinaEuropeDetails.size());
        shiftStatDetails.addAll(chinaAsiaDetails);
        shiftStatDetails.addAll(chinaEuropeDetails);
        for (ShiftStatDetail shiftStatDetail : shiftStatDetails) {
            shiftStatDetail.setStatId(shiftStat.getStatId());
            shiftStatDetail.setCreateBy(secruityUser.getId());
            shiftStatDetail.setCreateTime(LocalDateTime.now());
            shiftStatDetail.setDelFlag("N");
            shiftStatDetail.setCreateTime(LocalDateTime.now());
        }
        shiftStatDetailMapper.batchInsertShiftStatDetail(shiftStatDetails);
        return R.success(shiftStat);
    }

    /**
     * 班次统计表修改
     *
     * @param shiftStat 班次统计
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:19:09
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<T> update(ShiftStat shiftStat) {
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
        shiftStat.setUpdateBy(secruityUser.getId());
        shiftStatMapper.updateShiftStat(shiftStat);
        return R.success();
    }

    /**
     * 班次统计表删除
     *
     * @param statIds 班次统计ids
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:19:09
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<T> delete(Long[] statIds) {
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
        shiftStatMapper.deleteShiftStat(statIds, secruityUser.getId());
        return R.success();
    }

    /**
     * 班次统计表列表
     *
     * @param shiftStat 班次统计
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:19:09
     */
    @Override
    public R<List<ShiftStat>> list(ShiftStat shiftStat) {
        return R.success(shiftStatMapper.listShiftStat(shiftStat));
    }


    /**
     * 模板导入,读取模板中的信息返回给前端
     *
     * @param file 导入的中亚或者中欧模板
     * @param type 模板类型
     * @return R
     * <AUTHOR>
     * @since 2025/5/7 上午9:10
     **/
    @Override
    public R<List<ShiftStatDetail>> importExcel(MultipartFile file, String type) {
        if (file.isEmpty()) {
            return R.error("文件不能为空!");
        }
        ExcelReader reader;
        try {
            reader = ExcelUtil.getReader(file.getInputStream());
        } catch (IOException e) {
            return R.error("读取Excel文件失败，请检查文件格式或内容");
        }

        //从第8行开始读取数据
        List<List<Object>> rows = reader.read(4);
        List<ShiftStatDetail> shiftStatDetails = new ArrayList<>();
        for (int i = 0; i < rows.size(); i++) {
            List<Object> row = rows.get(i);
            ShiftStatDetail shiftStatDetail = new ShiftStatDetail();
            shiftStatDetail.setShiftType(type);
            if (i == 0) {
                shiftStatDetail.setProvince("国铁总数");
                shiftStatDetail.setDomesticCity(row.get(0).toString());
            } else {
                String province = getProvinceAndCity(shiftStatDetails, row.get(1).toString(), CELL_TYPE_PROVINCE, i);
                shiftStatDetail.setProvince(province);
                String city = getProvinceAndCity(shiftStatDetails, row.get(2).toString(), CELL_TYPE_CITY, i);
                shiftStatDetail.setDomesticCity(city);
            }
            shiftStatDetail.setShiftDirection(row.get(3).toString());
            shiftStatDetail.setTotalTrainCount(parseValueSafe(row.get(8)));
            shiftStatDetail.setTrainCountYoy(ExportStyleUtil.toBigDecimalSafe(row.get(9).toString()));
            shiftStatDetail.setTotalBoxCount(parseValueSafe(row.get(10)));
            shiftStatDetail.setBoxCountYoy(ExportStyleUtil.toBigDecimalSafe(row.get(11).toString()));
            shiftStatDetails.add(shiftStatDetail);
        }
        return R.success(shiftStatDetails);
    }


    /**
     * 省和市的值若是当前单元格为空，就获取上个对象的值
     *
     * @param shiftStatDetails cellValue
     * @return String 省和市
     * <AUTHOR>
     * @since 2025/5/7 上午9:47
     **/
    public String getProvinceAndCity(List<ShiftStatDetail> shiftStatDetails, String cellValue, String type, int i) {
        if (StrUtil.isBlank(cellValue) && type.equals(CELL_TYPE_PROVINCE)) {
            return shiftStatDetails.get(i - 1).getProvince();
        } else if (StrUtil.isBlank(cellValue) && type.equals(CELL_TYPE_CITY)) {
            return shiftStatDetails.get(i - 1).getDomesticCity();
        } else {
            return cellValue;
        }
    }

    /**
     * 转换为int
     *
     * @param obj 对象
     * @return int
     * <AUTHOR>
     * @since 2025/5/7 下午2:34
     **/
    private int parseValueSafe(Object obj) {

        if (obj == null) {
            return 0;
        }
        try {
            return Integer.parseInt(obj.toString());
        } catch (Exception e) {
            return 0;
        }
    }


    /**
     * 导出文件
     *
     * @param statId 班次统计id
     * <AUTHOR>
     * @since 2025/5/7 下午4:28
     **/
    @Override
    public void exportExcel(Long statId, HttpServletResponse response) {
        ShiftStat shiftStat = shiftStatMapper.selectById(statId);
        if (shiftStat == null) {
            throw new RuntimeException("导出Excel失败");
        }

        // 创建总数 sheet
        Workbook workbook = new XSSFWorkbook();
        Sheet totalSheet = workbook.createSheet("总数");
        Sheet mainCitySheet = workbook.createSheet("主要城市");
        Sheet mainProvinceSheet = workbook.createSheet("主要省份同比");
        Sheet yunNanGuangXiSheet = workbook.createSheet("云南、广西数据");
        exportTotalSheet(shiftStat, totalSheet, workbook);
        exportMainCitySheet(shiftStat, mainCitySheet, workbook);
        exportMainProvinceSheet(shiftStat, mainProvinceSheet, workbook);
        exportYunNanGuangXiSheet(shiftStat, yunNanGuangXiSheet, workbook);


        // 写入文件
        try {
            String fileName = URLEncoder.encode(shiftStat.getTitle(), "UTF-8").replaceAll("\\+", "%20") + "_" + LocalDateTime.now() + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            workbook.write(response.getOutputStream());
            response.getOutputStream().flush();
        } catch (IOException e) {
            throw new RuntimeException("导出Excel失败", e);
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                log.error("关闭workbook失败: {}", e.getMessage());
            }
        }

    }

    /**
     * 省份数据处理
     *
     * @param shiftStat
     * <AUTHOR>
     * @since 2025/5/9 下午1:08
     **/
    private void getProvinceData(ShiftStat shiftStat) {
        ShiftStatDetail shiftStatDetail = new ShiftStatDetail();
        shiftStatDetail.setStatId(shiftStat.getStatId());
        shiftStatDetail.setDomesticCity("合计");
        List<ShiftStatDetail> shiftStatDetails = shiftStatDetailMapper.listShiftStatDetail(shiftStatDetail);
        //数据转MAP
        shiftStatDetailMap = shiftStatDetails.stream()
                .collect(Collectors.toMap(
                        detail -> detail.getProvince() + detail.getShiftType() + detail.getDomesticCity() + detail.getShiftDirection(),
                        detail -> detail,
                        // 如 key 冲突，保留后者
                        (existing, replacement) -> replacement
                ));
        //处理成渝的数据
        String[] shiftTypes = {"中欧", "中亚"};
        String[] directions = {"去程", "回程"};

        for (String shiftType : shiftTypes) {
            for (String direction : directions) {
                ShiftStatDetail cqDetail = shiftStatDetailMap.get("重庆" + shiftType + "合计" + direction);
                ShiftStatDetail scDetail = shiftStatDetailMap.get("四川" + shiftType + "合计" + direction);

                // 合并逻辑：列数、TEU 相加（为空则当作 0）
                ShiftStatDetail merged = new ShiftStatDetail();
                merged.setProvince("成渝");
                merged.setDomesticCity("合计");
                merged.setShiftType(shiftType);
                merged.setShiftDirection(direction);
                merged.setStatId(shiftStat.getStatId());

                int totalTrainCount = ExportStyleUtil.toInt(cqDetail != null ? cqDetail.getTotalTrainCount() : null)
                        + ExportStyleUtil.toInt(scDetail != null ? scDetail.getTotalTrainCount() : null);
                int totalBoxCount = ExportStyleUtil.toInt(cqDetail != null ? cqDetail.getTotalBoxCount() : null)
                        + ExportStyleUtil.toInt(scDetail != null ? scDetail.getTotalBoxCount() : null);

                merged.setTotalTrainCount(totalTrainCount);
                merged.setTotalBoxCount(totalBoxCount);

                // 放入 map
                String key = "成渝" + shiftType + "合计" + direction;
                shiftStatDetailMap.put(key, merged);
            }
        }


        //中欧排序
        chinaEuropeMap = sortForShiftStatDetail(shiftStatDetail, CHINA_EUROPE);
        //中亚排序
        chinaAsiaMap = sortForShiftStatDetail(shiftStatDetail, CHINA_ASIA);
        //按照省份中合计的列数进行排序（中欧+中亚）
        sortTotalDetail = sortForShiftStatDetail(shiftStatDetail, "");

        //遍历省列表的排序
        provinceList = sortTotalDetail.entrySet()
                .stream()
                .sorted(Comparator.comparingInt(Map.Entry::getValue))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        //补全中欧\中亚排序
        completeSort(chinaAsiaMap, provinceList);
        completeSort(chinaEuropeMap, provinceList);
    }

    /**
     * 导出总数 sheet
     *
     * @param shiftStat  shiftStat
     * @param sheetTotal sheetTotal
     * @param workbook   workbook
     * <AUTHOR>
     * @since 2025/5/8 下午7:56
     **/
    private void exportTotalSheet(ShiftStat shiftStat, Sheet sheetTotal, Workbook workbook) {
        //处理省的数据
        getProvinceData(shiftStat);
        //遍历明细数据
        List<List<Object>> rowDetailList = new ArrayList<>();
        //遍历表格中需要填写的明细数据,根据城市和一级二级标题、三级标题生成行数据
        for (int i = 0; i < provinceList.size(); i++) {
            String province = provinceList.get(i);
            //第一行
            List<Object> row = new ArrayList<>();
            row.add(province);
            row.add(shiftStatDetailMap.get(province + "中欧" + "合计" + "去程") == null ?
                    0 : shiftStatDetailMap.get(province + "中欧" + "合计" + "去程").getTotalTrainCount());
            row.add(shiftStatDetailMap.get(province + "中欧" + "合计" + "回程") == null ?
                    0 : shiftStatDetailMap.get(province + "中欧" + "合计" + "回程").getTotalTrainCount());
            row.add("SUM(B" + (TOTAL_ROW_COUNT + i + 1) + ":C" + (TOTAL_ROW_COUNT + i + 1) + ")");
            //排名
            row.add(chinaEuropeMap.get(province));

            row.add(shiftStatDetailMap.get(province + "中欧" + "合计" + "去程") == null ?
                    0 : shiftStatDetailMap.get(province + "中欧" + "合计" + "去程").getTotalBoxCount());
            row.add(shiftStatDetailMap.get(province + "中欧" + "合计" + "回程") == null ?
                    0 : shiftStatDetailMap.get(province + "中欧" + "合计" + "回程").getTotalBoxCount());
            row.add("SUM(F" + (TOTAL_ROW_COUNT + i + 1) + ":G" + (TOTAL_ROW_COUNT + i + 1) + ")");

            row.add(shiftStatDetailMap.get(province + "中亚" + "合计" + "去程") == null ?
                    0 : shiftStatDetailMap.get(province + "中亚" + "合计" + "去程").getTotalTrainCount());
            row.add(shiftStatDetailMap.get(province + "中亚" + "合计" + "回程") == null ?
                    0 : shiftStatDetailMap.get(province + "中亚" + "合计" + "回程").getTotalTrainCount());
            row.add("SUM(I" + (TOTAL_ROW_COUNT + i + 1) + ":J" + (TOTAL_ROW_COUNT + i + 1) + ")");
            //排名
            row.add(chinaAsiaMap.get(province));

            row.add(shiftStatDetailMap.get(province + "中亚" + "合计" + "去程") == null ?
                    0 : shiftStatDetailMap.get(province + "中亚" + "合计" + "去程").getTotalBoxCount());
            row.add(shiftStatDetailMap.get(province + "中亚" + "合计" + "回程") == null ?
                    0 : shiftStatDetailMap.get(province + "中亚" + "合计" + "回程").getTotalBoxCount());
            row.add("SUM(M" + (TOTAL_ROW_COUNT + i + 1) + ":N" + (TOTAL_ROW_COUNT + i + 1) + ")");

            //合计
            row.add("B" + (TOTAL_ROW_COUNT + i + 1) + "+I" + (TOTAL_ROW_COUNT + i + 1));
            row.add("C" + (TOTAL_ROW_COUNT + i + 1) + "+J" + (TOTAL_ROW_COUNT + i + 1));
            row.add("D" + (TOTAL_ROW_COUNT + i + 1) + "+K" + (TOTAL_ROW_COUNT + i + 1));
            //排名
            row.add(sortTotalDetail.get(province));

            row.add("F" + (TOTAL_ROW_COUNT + i + 1) + "+M" + (TOTAL_ROW_COUNT + i + 1));
            row.add("G" + (TOTAL_ROW_COUNT + i + 1) + "+N" + (TOTAL_ROW_COUNT + i + 1));
            row.add("H" + (TOTAL_ROW_COUNT + i + 1) + "+O" + (TOTAL_ROW_COUNT + i + 1));
            rowDetailList.add(row);
        }
        generateExcel(shiftStat, provinceList, rowDetailList, sheetTotal, workbook);
    }

    /**
     * 导出主要城市 sheet
     *
     * @param shiftStat  shiftStat
     * @param sheetTotal sheetTotal
     * @param workbook   workbook
     * <AUTHOR>
     * @since 2025/5/8 下午7:56
     **/
    private void exportMainCitySheet(ShiftStat shiftStat, Sheet sheetTotal, Workbook workbook) {
        ShiftStatDetail shiftStatDetail = new ShiftStatDetail();
        shiftStatDetail.setStatId(shiftStat.getStatId());
        List<ShiftStatDetail> shiftStatDetails = shiftStatDetailMapper.listShiftStatDetail(shiftStatDetail);
        //去掉合计的数据
        String domesticCity = "合计";
        shiftStatDetails = shiftStatDetails.stream()
                .filter(detail -> !detail.getDomesticCity().equals(domesticCity))
                .collect(Collectors.toList());
        //数据转MAP
        Map<String, ShiftStatDetail> shiftStatDetailMap = shiftStatDetails.stream()
                .collect(Collectors.toMap(
                        detail -> detail.getProvince() + "_" + detail.getDomesticCity() + detail.getShiftType() + detail.getShiftDirection(),
                        detail -> detail,
                        // 如 key 冲突，保留后者
                        (existing, replacement) -> replacement
                ));
        //中欧排序
        Map<String, Integer> chinaEuropeMap = sortForShiftStatDetail(shiftStatDetail, CHINA_EUROPE);
        //中亚排序
        Map<String, Integer> chinaAsiaMap = sortForShiftStatDetail(shiftStatDetail, CHINA_ASIA);
        //按照省份中合计的列数进行排序（中欧+中亚）
        Map<String, Integer> sortTotalDetail = sortForShiftStatDetail(shiftStatDetail, "");
        //遍历省列表的排序
        List<String> cityList = sortTotalDetail.entrySet()
                .stream()
                .sorted(Comparator.comparingInt(Map.Entry::getValue))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        //补全中欧\中亚排序
        completeSort(chinaAsiaMap, cityList);
        completeSort(chinaEuropeMap, cityList);
        //遍历明细数据
        List<List<Object>> rowDetailList = new ArrayList<>();
        //遍历表格中需要填写的明细数据,根据城市和一级二级标题、三级标题生成行数据
        for (int i = 0; i < cityList.size(); i++) {
            String key = cityList.get(i);
            String[] provinceAndCity = cityList.get(i).split("_");
            log.info(Arrays.toString(provinceAndCity));
            String province = provinceAndCity[0];
            String city = provinceAndCity[1];
            //第一行
            List<Object> row = new ArrayList<>();
            row.add(city);
            row.add(province);
            row.add(shiftStatDetailMap.get(key + "中欧" + "去程") == null ?
                    0 : shiftStatDetailMap.get(key + "中欧" + "去程").getTotalTrainCount());
            row.add(shiftStatDetailMap.get(key + "中欧" + "回程") == null ?
                    0 : shiftStatDetailMap.get(key + "中欧" + "回程").getTotalTrainCount());
            row.add("C" + (TOTAL_ROW_COUNT + i) + "+D" + (TOTAL_ROW_COUNT + i));
            //排名
            row.add(chinaEuropeMap.get(key));

            row.add(shiftStatDetailMap.get(key + "中欧" + "去程") == null ?
                    0 : shiftStatDetailMap.get(key + "中欧" + "去程").getTotalBoxCount());
            row.add(shiftStatDetailMap.get(key + "中欧" + "回程") == null ?
                    0 : shiftStatDetailMap.get(key + "中欧" + "回程").getTotalBoxCount());
            row.add("G" + (TOTAL_ROW_COUNT + i) + "+H" + (TOTAL_ROW_COUNT + i));

            row.add(shiftStatDetailMap.get(key + "中亚" + "去程") == null ?
                    0 : shiftStatDetailMap.get(key + "中亚" + "去程").getTotalTrainCount());
            row.add(shiftStatDetailMap.get(key + "中亚" + "回程") == null ?
                    0 : shiftStatDetailMap.get(key + "中亚" + "回程").getTotalTrainCount());
            row.add("J" + (TOTAL_ROW_COUNT + i) + "+K" + (TOTAL_ROW_COUNT + i));
            //排名
            row.add(chinaAsiaMap.get(key));

            row.add(shiftStatDetailMap.get(key + "中亚" + "去程") == null ?
                    0 : shiftStatDetailMap.get(key + "中亚" + "去程").getTotalBoxCount());
            row.add(shiftStatDetailMap.get(key + "中亚" + "回程") == null ?
                    0 : shiftStatDetailMap.get(key + "中亚" + "回程").getTotalBoxCount());
            row.add("N" + (TOTAL_ROW_COUNT + i) + "+O" + (TOTAL_ROW_COUNT + i));

            //合计
            row.add("C" + (TOTAL_ROW_COUNT + i) + "+J" + (TOTAL_ROW_COUNT + i));
            row.add("D" + (TOTAL_ROW_COUNT + i) + "+K" + (TOTAL_ROW_COUNT + i));
            row.add("Q" + (TOTAL_ROW_COUNT + i) + "+R" + (TOTAL_ROW_COUNT + i));
            //排名
            row.add(sortTotalDetail.get(key));

            row.add("G" + (TOTAL_ROW_COUNT + i) + "+N" + (TOTAL_ROW_COUNT + i));
            row.add("H" + (TOTAL_ROW_COUNT + i) + "+O" + (TOTAL_ROW_COUNT + i));
            row.add("U" + (TOTAL_ROW_COUNT + i) + "+V" + (TOTAL_ROW_COUNT + i));
            rowDetailList.add(row);
        }
        generateMainCitySheet(shiftStat, cityList, rowDetailList, sheetTotal, workbook);
    }

    /**
     * 生成主要城市表格
     *
     * @param shiftStat     shiftStat
     * @param provinceList  省列表
     * @param rowDetailList 行数据
     * <AUTHOR>
     * @since 2025/5/8 下午3:48
     **/
    public void generateMainCitySheet(ShiftStat shiftStat, List<String> provinceList, List<List<Object>> rowDetailList, Sheet sheetTotal, Workbook workbook) {

        //设置单元格样式
        setMainCitySheetCellStyle(workbook, sheetTotal, provinceList);
        //第一行合并单元格
        sheetTotal.addMergedRegion(new CellRangeAddress(0, 0, 0, MAIN_CITY_COLUMN_COUNT - 1));
        //写入标题
        Row row0 = sheetTotal.getRow(0);
        Cell cell0 = row0.getCell(0);
        //国内主要省市中欧班列发运量（自然列）
        // 提取年份（以“年”结尾前的数字）
        String title = shiftStat.getTitle();
        String year = title.substring(0, title.indexOf("年") + 1);
        // 提取月份（位于“年”和“月”之间的部分）
        int yearIndex = title.indexOf("年");
        String month = title.substring(yearIndex + 1, title.indexOf("月") + 1);
        if (StrUtil.isNotBlank(year) && StrUtil.isNotBlank(month)) {
            cell0.setCellValue(year + "1-" + month + "主要发运城市发运量（自然列）");
        } else {
            cell0.setCellValue("主要发运城市发运量（自然列）");
        }

        //第二行
        sheetTotal.addMergedRegion(new CellRangeAddress(1, 1, 0, MAIN_CITY_COLUMN_COUNT - 1));
        Row row1 = sheetTotal.getRow(1);
        Cell cell1 = row1.getCell(0);
        cell1.setCellValue("数据来源：中欧班列网；不含云南、广西数据");
        //第三行
        Row row2 = sheetTotal.getRow(2);
        row2.getCell(0).setCellValue("城市");
        row2.getCell(1).setCellValue("省份");
        row2.getCell(2).setCellValue("中欧班列");
        row2.getCell(9).setCellValue("中亚班列");
        row2.getCell(16).setCellValue("合计");
        sheetTotal.addMergedRegion(new CellRangeAddress(2, 4, 0, 0));
        sheetTotal.addMergedRegion(new CellRangeAddress(2, 4, 1, 1));
        sheetTotal.addMergedRegion(new CellRangeAddress(2, 2, 2, 8));
        sheetTotal.addMergedRegion(new CellRangeAddress(2, 2, 9, 15));
        sheetTotal.addMergedRegion(new CellRangeAddress(2, 2, 16, 22));


        //第四行
        Row row3 = sheetTotal.getRow(3);
        row3.getCell(2).setCellValue("列数");
        row3.getCell(6).setCellValue("TEU");
        row3.getCell(9).setCellValue("列数");
        row3.getCell(13).setCellValue("TEU");
        row3.getCell(16).setCellValue("列数");
        row3.getCell(20).setCellValue("TEU");
        sheetTotal.addMergedRegion(new CellRangeAddress(3, 3, 2, 5));
        sheetTotal.addMergedRegion(new CellRangeAddress(3, 3, 6, 8));
        sheetTotal.addMergedRegion(new CellRangeAddress(3, 3, 9, 12));
        sheetTotal.addMergedRegion(new CellRangeAddress(3, 3, 13, 15));
        sheetTotal.addMergedRegion(new CellRangeAddress(3, 3, 16, 19));
        sheetTotal.addMergedRegion(new CellRangeAddress(3, 3, 20, 22));

        //第五行
        Row row4 = sheetTotal.getRow(4);
        for (int i = 0; i < TOTAL_COLUMN_TITLE_ARRAY.length; i++) {
            row4.getCell(i + 2).setCellValue(TOTAL_COLUMN_TITLE_ARRAY[i]);
        }
        //第六行
        for (int i = 0; i < rowDetailList.size(); i++) {
            Row row = sheetTotal.getRow(i + MAIN_CITY_ROW_COUNT);
            List<Object> rowDetail = rowDetailList.get(i);
            //城市
            row.getCell(0).setCellValue(rowDetail.get(0).toString());
            //省份
            row.getCell(1).setCellValue(rowDetail.get(1).toString());

            //中欧
            //去程
            row.getCell(2).setCellValue(ExportStyleUtil.toInt(rowDetail.get(2)));
            //回程
            row.getCell(3).setCellValue(ExportStyleUtil.toInt(rowDetail.get(3)));
            //列数
            row.getCell(4).setCellFormula(rowDetail.get(4).toString());
            //排名
            row.getCell(5).setCellValue(ExportStyleUtil.toInt(rowDetail.get(5)));
            //去程
            row.getCell(6).setCellValue(ExportStyleUtil.toInt(rowDetail.get(6)));
            //回程
            row.getCell(7).setCellValue(ExportStyleUtil.toInt(rowDetail.get(7)));
            //小计
            row.getCell(8).setCellFormula(rowDetail.get(8).toString());

            //中亚
            //去程
            row.getCell(9).setCellValue(ExportStyleUtil.toInt(rowDetail.get(9)));
            //回程
            row.getCell(10).setCellValue(ExportStyleUtil.toInt(rowDetail.get(10)));
            //列数
            row.getCell(11).setCellFormula(rowDetail.get(11).toString());
            //排名
            row.getCell(12).setCellValue(ExportStyleUtil.toInt(rowDetail.get(12)));
            //去程
            row.getCell(13).setCellValue(ExportStyleUtil.toInt(rowDetail.get(13)));
            //回程
            row.getCell(14).setCellValue(ExportStyleUtil.toInt(rowDetail.get(14)));
            //小计
            row.getCell(15).setCellFormula(rowDetail.get(15).toString());

            //合计
            //去程
            row.getCell(16).setCellFormula(rowDetail.get(16).toString());
            //回程
            row.getCell(17).setCellFormula(rowDetail.get(17).toString());
            //列数
            row.getCell(18).setCellFormula(rowDetail.get(18).toString());
            //排名
            row.getCell(19).setCellValue(ExportStyleUtil.toInt(rowDetail.get(19)));
            //去程
            row.getCell(20).setCellFormula(rowDetail.get(20).toString());
            //回程
            row.getCell(21).setCellFormula(rowDetail.get(21).toString());
            //小计
            row.getCell(22).setCellFormula(rowDetail.get(22).toString());
        }
        //写入特殊样式
        CellStyle greenStyle = ExportStyleUtil.createGreenStyle(workbook);
        for (int i = 0; i < rowDetailList.size(); i++) {
            String province = rowDetailList.get(i).get(1).toString();
            if (Arrays.asList(SPECIAL_PROVINCE_CITY).contains(province)) {
                //整行变绿
                Row row = sheetTotal.getRow(i + MAIN_CITY_ROW_COUNT);
                for (int j = 0; j < MAIN_CITY_COLUMN_COUNT; j++) {
                    row.getCell(j).setCellStyle(greenStyle);
                }
            }
        }

    }

    /**
     * 设置总数sheet单元格样式
     *
     * @param workbook workbook
     * @param sheet    sheet
     * @param cityList 省份列表
     * <AUTHOR>
     * @since 2025/5/8 下午1:32
     **/
    private void setMainCitySheetCellStyle(Workbook workbook, Sheet sheet, List<String> cityList) {
        //设置单元格的格式
        CellStyle titleStyle = ExportStyleUtil.createTitleStyle(workbook);
        CellStyle titleDetailStyle = ExportStyleUtil.createTitleDetailStyle(workbook);
        CellStyle detailsStyle = ExportStyleUtil.createDetailsStyleCenter(workbook);
        //居右样式
        CellStyle detailsStyleRight = ExportStyleUtil.createDetailsStyleRight(workbook);

        //第一行设置成标题明细样式
        Row row0 = sheet.createRow(0);
        setRowStyle(row0, titleDetailStyle, MAIN_CITY_COLUMN_COUNT);
        //第二行设置成明细居右样式
        Row row1 = sheet.createRow(1);
        setRowStyle(row1, detailsStyleRight, MAIN_CITY_COLUMN_COUNT);
        //第三行到第五行设置成标题样式、
        for (int i = 2; i < MAIN_CITY_ROW_COUNT; i++) {
            Row row = sheet.createRow(i);
            setRowStyle(row, titleStyle, MAIN_CITY_COLUMN_COUNT);
        }
        //第六行开始设置成明细样式
        for (int i = MAIN_CITY_ROW_COUNT; i < cityList.size() + MAIN_CITY_ROW_COUNT; i++) {
            Row row = sheet.createRow(i);
            setRowStyle(row, detailsStyle, MAIN_CITY_COLUMN_COUNT);
        }
        //锁定第五行和第一列
        sheet.createFreezePane(2, 5);
    }


    /**
     * 导出主要省份同比sheet
     *
     * @param shiftStat         shiftStat
     * @param mainProvinceSheet 主要省份同比sheet
     * @param workbook          workbook
     * <AUTHOR>
     * @since 2025/5/8 下午7:57
     **/
    private void exportMainProvinceSheet(ShiftStat shiftStat, Sheet mainProvinceSheet, Workbook workbook) {

        //遍历明细数据
        List<List<Object>> rowDetailList = new ArrayList<>();
        //只取前8个省
        List<String> advancedProvince = provinceList.stream().limit(10).collect(Collectors.toList());
        //遍历表格中需要填写的明细数据,根据城市和一级二级标题、三级标题生成行数据
        for (int i = 0; i < advancedProvince.size(); i++) {
            String province = advancedProvince.get(i);
            //第一行
            List<Object> row = new ArrayList<>();
            row.add(province);
            row.add(shiftStatDetailMap.get(province + "中欧" + "合计" + "去程") == null ?
                    0 : shiftStatDetailMap.get(province + "中欧" + "合计" + "去程").getTotalTrainCount());
            row.add(shiftStatDetailMap.get(province + "中欧" + "合计" + "回程") == null ?
                    0 : shiftStatDetailMap.get(province + "中欧" + "合计" + "回程").getTotalTrainCount());
            row.add("B" + (MAIN_PROVINCE_ROW_COUNT + i + 1) + "+C" + (MAIN_PROVINCE_ROW_COUNT + i + 1));
            //排名
            row.add(chinaEuropeMap.get(province));

            row.add(shiftStatDetailMap.get(province + "中亚" + "合计" + "去程") == null ?
                    0 : shiftStatDetailMap.get(province + "中亚" + "合计" + "去程").getTotalTrainCount());
            row.add(shiftStatDetailMap.get(province + "中亚" + "合计" + "回程") == null ?
                    0 : shiftStatDetailMap.get(province + "中亚" + "合计" + "回程").getTotalTrainCount());
            row.add("F" + (MAIN_PROVINCE_ROW_COUNT + i + 1) + "+G" + (MAIN_PROVINCE_ROW_COUNT + i + 1));
            //排名
            row.add(chinaAsiaMap.get(province));

            //合计
            row.add("B" + (MAIN_PROVINCE_ROW_COUNT + i + 1) + "+F" + (MAIN_PROVINCE_ROW_COUNT + i + 1));
            row.add("C" + (MAIN_PROVINCE_ROW_COUNT + i + 1) + "+G" + (MAIN_PROVINCE_ROW_COUNT + i + 1));
            row.add("J" + (MAIN_PROVINCE_ROW_COUNT + i + 1) + "+K" + (MAIN_PROVINCE_ROW_COUNT + i + 1));
            //排名
            row.add(sortTotalDetail.get(province));
            rowDetailList.add(row);
        }
        generateMainProvinceSheet(shiftStat, advancedProvince, rowDetailList, mainProvinceSheet, workbook);

    }


    /**
     * 生成主要省份同比表格
     *
     * @param shiftStat        shiftStat
     * @param advancedProvince 省列表
     * @param rowDetailList    行数据
     * <AUTHOR>
     * @since 2025/5/8 下午3:48
     **/
    public void generateMainProvinceSheet(ShiftStat shiftStat, List<String> advancedProvince, List<List<Object>> rowDetailList, Sheet mainProvinceSheet, Workbook workbook) {


        setMainProvinceSheetCellStyle(workbook, mainProvinceSheet, advancedProvince);
        //第一行合并单元格
        mainProvinceSheet.addMergedRegion(new CellRangeAddress(0, 0, 0, MAIN_PROVINCE_COLUMN_COUNT - 1));
        //写入标题
        Row row0 = mainProvinceSheet.getRow(0);
        Cell cell0 = row0.getCell(0);
        //国内主要省市中欧班列发运量（自然列）
        // 提取年份（以“年”结尾前的数字）
        String title = shiftStat.getTitle();
        String year = title.substring(0, title.indexOf("年") + 1);
        // 提取月份（位于“年”和“月”之间的部分）
        int yearIndex = title.indexOf("年");
        String month = title.substring(yearIndex + 1, title.indexOf("月") + 1);
        if (StrUtil.isNotBlank(year) && StrUtil.isNotBlank(month)) {
            cell0.setCellValue(year + "1-" + month + "主要省份开行数据");
        } else {
            cell0.setCellValue("主要省份开行数据");
        }

        //第二行
        Row row1 = mainProvinceSheet.getRow(1);
        row1.getCell(0).setCellValue("省份");
        row1.getCell(1).setCellValue("中欧班列");
        row1.getCell(5).setCellValue("中亚班列");
        row1.getCell(9).setCellValue("合计");
        mainProvinceSheet.addMergedRegion(new CellRangeAddress(1, 2, 0, 0));
        mainProvinceSheet.addMergedRegion(new CellRangeAddress(1, 1, 1, 4));
        mainProvinceSheet.addMergedRegion(new CellRangeAddress(1, 1, 5, 8));
        mainProvinceSheet.addMergedRegion(new CellRangeAddress(1, 1, 9, 12));


        //第三行
        Row row2 = mainProvinceSheet.getRow(2);
        row2.getCell(1).setCellValue("去程");
        row2.getCell(2).setCellValue("回程");
        row2.getCell(3).setCellValue("列数");
        row2.getCell(4).setCellValue("排名");

        row2.getCell(5).setCellValue("去程");
        row2.getCell(6).setCellValue("回程");
        row2.getCell(7).setCellValue("列数");
        row2.getCell(8).setCellValue("排名");

        row2.getCell(9).setCellValue("去程");
        row2.getCell(10).setCellValue("回程");
        row2.getCell(11).setCellValue("列数");
        row2.getCell(12).setCellValue("排名");

        //明细行
        for (int i = 0; i < rowDetailList.size(); i++) {
            Row row = mainProvinceSheet.getRow(i + MAIN_PROVINCE_ROW_COUNT);
            List<Object> rowDetail = rowDetailList.get(i);
            //省份
            row.getCell(0).setCellValue(rowDetail.get(0).toString());

            //中欧
            //去程
            row.getCell(1).setCellValue(ExportStyleUtil.toInt(rowDetail.get(1)));
            //回程
            row.getCell(2).setCellValue(ExportStyleUtil.toInt(rowDetail.get(2)));
            //列数
            row.getCell(3).setCellFormula(rowDetail.get(3).toString());
            //排名
            row.getCell(4).setCellValue(ExportStyleUtil.toInt(rowDetail.get(4)));


            //中亚
            //去程
            row.getCell(5).setCellValue(ExportStyleUtil.toInt(rowDetail.get(5)));
            //回程
            row.getCell(6).setCellValue(ExportStyleUtil.toInt(rowDetail.get(6)));
            //列数
            row.getCell(7).setCellFormula(rowDetail.get(7).toString());
            //排名
            row.getCell(8).setCellValue(ExportStyleUtil.toInt(rowDetail.get(8)));

            //合计
            //去程
            row.getCell(9).setCellFormula(rowDetail.get(9).toString());
            //回程
            row.getCell(10).setCellFormula(rowDetail.get(10).toString());
            //列数
            row.getCell(11).setCellFormula(rowDetail.get(11).toString());
            //排名
            row.getCell(12).setCellValue(ExportStyleUtil.toInt(rowDetail.get(12)));
        }
        //写入最后一行
        Row rowLast = mainProvinceSheet.getRow(MAIN_PROVINCE_ROW_COUNT + rowDetailList.size());
        rowLast.getCell(0).setCellValue("不含广西、云南数据");
        mainProvinceSheet.addMergedRegion(new CellRangeAddress(MAIN_PROVINCE_ROW_COUNT + rowDetailList.size(),
                MAIN_PROVINCE_ROW_COUNT + rowDetailList.size(), 0, MAIN_PROVINCE_COLUMN_COUNT - 1));


        //开始写第二个表格
        //空两行，写入一级标题列
        int yearOnYearDataIndex = MAIN_PROVINCE_ROW_COUNT + rowDetailList.size() + 3;
        Row rowFirstTitle = mainProvinceSheet.getRow(yearOnYearDataIndex);
        if (StrUtil.isNotBlank(year) && StrUtil.isNotBlank(month)) {
            rowFirstTitle.getCell(0).setCellValue(year + "1-" + month + "主要省份同比数据");
        } else {
            rowFirstTitle.getCell(0).setCellValue("主要省份同比数据");
        }
        mainProvinceSheet.addMergedRegion(new CellRangeAddress(yearOnYearDataIndex,
                yearOnYearDataIndex, 0, MAIN_PROVINCE_COLUMN_COUNT - 4));
        //写入二级标题列
        Row rowSecondTitle = mainProvinceSheet.getRow(yearOnYearDataIndex + 1);
        rowSecondTitle.getCell(0).setCellValue("省份");
        rowSecondTitle.getCell(1).setCellValue("中欧班列");
        rowSecondTitle.getCell(4).setCellValue("中亚班列");
        rowSecondTitle.getCell(7).setCellValue("合计");
        mainProvinceSheet.addMergedRegion(new CellRangeAddress(yearOnYearDataIndex + 1, yearOnYearDataIndex + 2, 0, 0));
        mainProvinceSheet.addMergedRegion(new CellRangeAddress(yearOnYearDataIndex + 1, yearOnYearDataIndex + 1, 1, 3));
        mainProvinceSheet.addMergedRegion(new CellRangeAddress(yearOnYearDataIndex + 1, yearOnYearDataIndex + 1, 4, 6));
        mainProvinceSheet.addMergedRegion(new CellRangeAddress(yearOnYearDataIndex + 1, yearOnYearDataIndex + 1, 7, 9));

        //写入三级标题列
        Row rowThirdTitle = mainProvinceSheet.getRow(yearOnYearDataIndex + 2);
        rowThirdTitle.getCell(1).setCellValue("去程");
        rowThirdTitle.getCell(2).setCellValue("回程");
        rowThirdTitle.getCell(3).setCellValue("小计");

        rowThirdTitle.getCell(4).setCellValue("去程");
        rowThirdTitle.getCell(5).setCellValue("回程");
        rowThirdTitle.getCell(6).setCellValue("小计");

        rowThirdTitle.getCell(7).setCellValue("去程");
        rowThirdTitle.getCell(8).setCellValue("回程");
        rowThirdTitle.getCell(9).setCellValue("合计");

        //比率开始行
        int ratioIndex = MAIN_PROVINCE_ROW_COUNT + rowDetailList.size() + 3 + MAIN_PROVINCE_ROW_COUNT + rowDetailList.size() + 4;
        //明细开始行
        int detailIndex = MAIN_PROVINCE_ROW_COUNT + rowDetailList.size() + 3 + MAIN_PROVINCE_ROW_COUNT + 1;
        //成渝明细开始行
        int ctyDetailIndex = MAIN_PROVINCE_ROW_COUNT + rowDetailList.size() + 3
                + MAIN_PROVINCE_ROW_COUNT + rowDetailList.size()
                + 3 + rowDetailList.size() + 2;
        //写入明细值
        for (int i = 0; i < advancedProvince.size(); i++) {
            Row row = mainProvinceSheet.getRow(i + yearOnYearDataIndex + 3);
            row.getCell(0).setCellValue(advancedProvince.get(i));
            String specialProvince = "成渝";

            if (advancedProvince.get(i).equals(specialProvince)) {
                row.getCell(1).setCellFormula("B" + (ctyDetailIndex + 2) + "+B" + (ctyDetailIndex + 6));
                row.getCell(2).setCellFormula("C" + (ctyDetailIndex + 2) + "+C" + (ctyDetailIndex + 6));
                row.getCell(3).setCellFormula("D" + (ctyDetailIndex + 2) + "+D" + (ctyDetailIndex + 6));
                row.getCell(4).setCellFormula("E" + (ctyDetailIndex + 2) + "+E" + (ctyDetailIndex + 6));
                row.getCell(5).setCellFormula("F" + (ctyDetailIndex + 2) + "+F" + (ctyDetailIndex + 6));
                row.getCell(6).setCellFormula("G" + (ctyDetailIndex + 2) + "+G" + (ctyDetailIndex + 6));
            } else {
                row.getCell(1).setCellFormula("B" + (4 + i) + "-ROUND(B" + (4 + i) + "/(1+B" + (ratioIndex + i) + "),0)");
                row.getCell(2).setCellFormula("C" + (4 + i) + "-ROUND(C" + (4 + i) + "/(1+C" + (ratioIndex + i) + "),0)");
                row.getCell(3).setCellFormula("D" + (4 + i) + "-ROUND(D" + (4 + i) + "/(1+D" + (ratioIndex + i) + "),0)");
                row.getCell(4).setCellFormula("F" + (4 + i) + "-ROUND(F" + (4 + i) + "/(1+E" + (ratioIndex + i) + "),0)");
                row.getCell(5).setCellFormula("G" + (4 + i) + "-ROUND(G" + (4 + i) + "/(1+F" + (ratioIndex + i) + "),0)");
                row.getCell(6).setCellFormula("H" + (4 + i) + "-ROUND(H" + (4 + i) + "/(1+G" + (ratioIndex + i) + "),0)");
            }
            row.getCell(7).setCellFormula("B" + (detailIndex + i) + "+E" + (detailIndex + i));
            row.getCell(8).setCellFormula("C" + (detailIndex + i) + "+F" + (detailIndex + i));
            row.getCell(9).setCellFormula("H" + (detailIndex + i) + "+I" + (detailIndex + i));

        }

        //写入特殊样式
        CellStyle greenStyle = ExportStyleUtil.createGreenStyle(workbook);
        for (int i = 0; i < rowDetailList.size(); i++) {
            String province = rowDetailList.get(i).get(0).toString();
            if (Arrays.asList(SPECIAL_PROVINCE_CITY).contains(province)) {
                //整行变绿
                Row row = mainProvinceSheet.getRow(i + MAIN_PROVINCE_ROW_COUNT);
                for (int j = 0; j < MAIN_PROVINCE_COLUMN_COUNT; j++) {
                    row.getCell(j).setCellStyle(greenStyle);
                }
                //写入第二表格的特殊样式
                int columnDifference = 3;
                Row secondTableRow = mainProvinceSheet.getRow(i + MAIN_PROVINCE_ROW_COUNT + MAIN_PROVINCE_ROW_COUNT + 3 + advancedProvince.size());
                for (int j = 0; j < MAIN_PROVINCE_COLUMN_COUNT - columnDifference; j++) {
                    secondTableRow.getCell(j).setCellStyle(greenStyle);
                }
            }
        }

        //空一行写入第三表格
        int rumNum = MAIN_PROVINCE_ROW_COUNT + MAIN_PROVINCE_ROW_COUNT + advancedProvince.size() + advancedProvince.size() + 4;
        Row ratioRowFirstTitle = mainProvinceSheet.getRow(rumNum);
        ratioRowFirstTitle.getCell(0).setCellValue("省市");
        ratioRowFirstTitle.getCell(1).setCellValue("中欧");
        ratioRowFirstTitle.getCell(4).setCellValue("中亚");
        ratioRowFirstTitle.getCell(7).setCellValue("合计");

        mainProvinceSheet.addMergedRegion(new CellRangeAddress(rumNum, rumNum + 1, 0, 0));
        mainProvinceSheet.addMergedRegion(new CellRangeAddress(rumNum, rumNum, 1, 3));
        mainProvinceSheet.addMergedRegion(new CellRangeAddress(rumNum, rumNum, 4, 6));
        mainProvinceSheet.addMergedRegion(new CellRangeAddress(rumNum, rumNum, 7, 9));
        //写入第二行的标题列
        Row ratioRowSecondTitle = mainProvinceSheet.getRow(rumNum + 1);
        ratioRowSecondTitle.getCell(1).setCellValue("去程");
        ratioRowSecondTitle.getCell(2).setCellValue("回程");
        ratioRowSecondTitle.getCell(3).setCellValue("小计");

        ratioRowSecondTitle.getCell(4).setCellValue("去程");
        ratioRowSecondTitle.getCell(5).setCellValue("回程");
        ratioRowSecondTitle.getCell(6).setCellValue("小计");

        ratioRowSecondTitle.getCell(7).setCellValue("去程");
        ratioRowSecondTitle.getCell(8).setCellValue("回程");
        ratioRowSecondTitle.getCell(8).setCellValue("合计");
        //遍历明细
        for (int i = 0; i < advancedProvince.size(); i++) {
            String province = advancedProvince.get(i);
            Row row = mainProvinceSheet.getRow(rumNum + i + 2);
            row.getCell(0).setCellValue(province);
            //中欧去程
            ShiftStatDetail europeOutbound = shiftStatDetailMap.get(province + "中欧" + "合计" + "去程");
            if (europeOutbound != null) {
                row.getCell(1).setCellValue(ExportStyleUtil.toDouble(divide100(europeOutbound.getTrainCountYoy())));
            } else {
                row.getCell(1).setCellValue(0.0);
            }
            //中欧回程
            ShiftStatDetail europeReturn = shiftStatDetailMap.get(province + "中欧" + "合计" + "回程");
            if (europeReturn != null) {
                row.getCell(2).setCellValue(ExportStyleUtil.toDouble(divide100(europeReturn.getTrainCountYoy())));
            } else {
                row.getCell(2).setCellValue(0.0);
            }
            //中欧汇总
            ShiftStatDetail europeOutboundAmount = shiftStatDetailMap.get(province + "中欧" + "合计" + "合计");
            if (europeOutboundAmount != null) {
                row.getCell(3).setCellValue(ExportStyleUtil.toDouble(divide100(europeOutboundAmount.getTrainCountYoy())));
            } else {
                row.getCell(3).setCellValue(0.0);
            }


            //中亚去程
            ShiftStatDetail asiaOutbound = shiftStatDetailMap.get(province + "中亚" + "合计" + "去程");
            if (asiaOutbound != null) {
                row.getCell(4).setCellValue(ExportStyleUtil.toDouble(divide100(asiaOutbound.getTrainCountYoy())));
            } else {
                row.getCell(4).setCellValue(0.0);
            }
            //中亚回程
            ShiftStatDetail asiaReturn = shiftStatDetailMap.get(province + "中亚" + "合计" + "回程");
            if (asiaReturn != null) {
                row.getCell(5).setCellValue(ExportStyleUtil.toDouble(divide100(asiaReturn.getTrainCountYoy())));
            } else {
                row.getCell(5).setCellValue(0.0);
            }
            //中亚汇总
            ShiftStatDetail asiaTotal = shiftStatDetailMap.get(province + "中亚" + "合计" + "合计");
            if (asiaTotal != null) {
                row.getCell(6).setCellValue(ExportStyleUtil.toDouble(divide100(asiaTotal.getTrainCountYoy())));
            } else {
                row.getCell(6).setCellValue(0.0);
            }

        }

        //空一行写入重庆的数据
        int chongQingNum = MAIN_PROVINCE_ROW_COUNT * 3 + advancedProvince.size() * 3 + 4;
        Row rowChongQingTrain = mainProvinceSheet.getRow(chongQingNum);
        Row rowChongQingRatio = mainProvinceSheet.getRow(chongQingNum + 1);
        Row rowChongQingCount = mainProvinceSheet.getRow(chongQingNum + 2);
        rowChongQingTrain.getCell(0).setCellValue("重庆");
        mainProvinceSheet.addMergedRegion(new CellRangeAddress(chongQingNum, chongQingNum + 2, 0, 0));

        //重庆中欧去程
        ShiftStatDetail chongQingEuropeGo = shiftStatDetailMap.get("重庆" + "中欧" + "合计" + "去程");
        if (chongQingEuropeGo != null) {
            rowChongQingTrain.getCell(1).setCellValue(chongQingEuropeGo.getTotalTrainCount());
            rowChongQingRatio.getCell(1).setCellValue(ExportStyleUtil.toDouble(divide100(chongQingEuropeGo.getTrainCountYoy())));
        } else {
            rowChongQingTrain.getCell(1).setCellValue(0);
            rowChongQingRatio.getCell(1).setCellValue(0.0);
        }
        rowChongQingCount.getCell(1).setCellFormula("B" + (chongQingNum + 1) + "-ROUND(B" + (chongQingNum + 1) + "/(1+B" + (chongQingNum + 2) + "),0)");

        //重庆中欧回程
        ShiftStatDetail chongQingEuropeReturn = shiftStatDetailMap.get("重庆" + "中欧" + "合计" + "回程");
        if (chongQingEuropeReturn != null) {
            rowChongQingTrain.getCell(2).setCellValue(chongQingEuropeReturn.getTotalTrainCount());
            rowChongQingRatio.getCell(2).setCellValue(ExportStyleUtil.toDouble(divide100(chongQingEuropeReturn.getTrainCountYoy())));
        } else {
            rowChongQingTrain.getCell(2).setCellValue(0);
            rowChongQingRatio.getCell(2).setCellValue(0.0);
        }
        rowChongQingCount.getCell(2).setCellFormula("C" + (chongQingNum + 1) + "-ROUND(C" + (chongQingNum + 1) + "/(1+C" + (chongQingNum + 2) + "),0)");
        //重庆中欧小计
        ShiftStatDetail chongQingEuropeTotal = shiftStatDetailMap.get("重庆" + "中欧" + "合计" + "合计");
        if (chongQingEuropeTotal != null) {
            rowChongQingTrain.getCell(3).setCellValue(chongQingEuropeTotal.getTotalTrainCount());
            rowChongQingRatio.getCell(3).setCellValue(ExportStyleUtil.toDouble(divide100(chongQingEuropeTotal.getTrainCountYoy())));
        } else {
            rowChongQingTrain.getCell(3).setCellValue(0);
            rowChongQingRatio.getCell(3).setCellValue(0.0);
        }
        rowChongQingCount.getCell(3).setCellFormula("D" + (chongQingNum + 1) + "-ROUND(D" + (chongQingNum + 1) + "/(1+D" + (chongQingNum + 2) + "),0)");


        //重庆中亚去程
        ShiftStatDetail chongQingAsiaGo = shiftStatDetailMap.get("重庆" + "中亚" + "合计" + "去程");
        if (chongQingAsiaGo != null) {
            rowChongQingTrain.getCell(4).setCellValue(chongQingAsiaGo.getTotalTrainCount());
            rowChongQingRatio.getCell(4).setCellValue(ExportStyleUtil.toDouble(divide100(chongQingAsiaGo.getTrainCountYoy())));
        } else {
            rowChongQingTrain.getCell(4).setCellValue(0);
            rowChongQingRatio.getCell(4).setCellValue(0.0);
        }
        rowChongQingCount.getCell(4).setCellFormula("E" + (chongQingNum + 1) + "-ROUND(E" + (chongQingNum + 1) + "/(1+E" + (chongQingNum + 2) + "),0)");
        //重庆中欧回程
        ShiftStatDetail chongQingAsiaReturn = shiftStatDetailMap.get("重庆" + "中亚" + "合计" + "回程");
        if (chongQingAsiaReturn != null) {
            rowChongQingTrain.getCell(5).setCellValue(chongQingAsiaReturn.getTotalTrainCount());
            rowChongQingRatio.getCell(5).setCellValue(ExportStyleUtil.toDouble(divide100(chongQingAsiaReturn.getTrainCountYoy())));
        } else {
            rowChongQingTrain.getCell(5).setCellValue(0);
            rowChongQingRatio.getCell(5).setCellValue(0.0);
        }
        rowChongQingCount.getCell(5).setCellFormula("F" + (chongQingNum + 1) + "-ROUND(F" + (chongQingNum + 1) + "/(1+F" + (chongQingNum + 2) + "),0)");
        //重庆中欧小计
        ShiftStatDetail chongQingAsiaTotal = shiftStatDetailMap.get("重庆" + "中亚" + "合计" + "合计");
        if (chongQingAsiaTotal != null) {
            rowChongQingTrain.getCell(6).setCellValue(chongQingAsiaTotal.getTotalTrainCount());
            rowChongQingRatio.getCell(6).setCellValue(ExportStyleUtil.toDouble(divide100(chongQingAsiaTotal.getTrainCountYoy())));
        } else {
            rowChongQingTrain.getCell(6).setCellValue(0);
            rowChongQingRatio.getCell(6).setCellValue(0.0);
        }
        rowChongQingCount.getCell(6).setCellFormula("G" + (chongQingNum + 1) + "-ROUND(G" + (chongQingNum + 1) + "/(1+G" + (chongQingNum + 2) + "),0)");

        //重庆 写入最后一列
        rowChongQingTrain.getCell(10).setCellValue("开行");
        rowChongQingRatio.getCell(10).setCellValue("同比比例");
        rowChongQingCount.getCell(10).setCellValue("同比列数");


        //空一行写入四川的数据
        int siChuanNum = MAIN_PROVINCE_ROW_COUNT * 3 + advancedProvince.size() * 3 + 8;
        Row rowSiChuanTrain = mainProvinceSheet.getRow(siChuanNum);
        Row rowSiChuanRatio = mainProvinceSheet.getRow(siChuanNum + 1);
        Row rowSiChuanCount = mainProvinceSheet.getRow(siChuanNum + 2);
        rowSiChuanTrain.getCell(0).setCellValue("四川");
        mainProvinceSheet.addMergedRegion(new CellRangeAddress(siChuanNum, siChuanNum + 2, 0, 0));

        //四川中欧去程
        ShiftStatDetail siChuanEuropeGo = shiftStatDetailMap.get("四川" + "中欧" + "合计" + "去程");
        if (siChuanEuropeGo != null) {
            rowSiChuanTrain.getCell(1).setCellValue(siChuanEuropeGo.getTotalTrainCount());
            rowSiChuanRatio.getCell(1).setCellValue(ExportStyleUtil.toDouble(divide100(siChuanEuropeGo.getTrainCountYoy())));
        } else {
            rowSiChuanTrain.getCell(1).setCellValue(0);
            rowSiChuanRatio.getCell(1).setCellValue(0.0);
        }
        rowSiChuanCount.getCell(1).setCellFormula("B" + (siChuanNum + 1) + "-ROUND(B" + (siChuanNum + 1) + "/(1+B" + (siChuanNum + 2) + "),0)");
        //四川中欧回程
        ShiftStatDetail siChuanEuropeReturn = shiftStatDetailMap.get("四川" + "中欧" + "合计" + "回程");
        if (siChuanEuropeReturn != null) {
            rowSiChuanTrain.getCell(2).setCellValue(siChuanEuropeReturn.getTotalTrainCount());
            rowSiChuanRatio.getCell(2).setCellValue(ExportStyleUtil.toDouble(divide100(siChuanEuropeReturn.getTrainCountYoy())));
        } else {
            rowSiChuanTrain.getCell(2).setCellValue(0);
            rowSiChuanRatio.getCell(2).setCellValue(0.0);
        }
        rowSiChuanCount.getCell(2).setCellFormula("C" + (siChuanNum + 1) + "-ROUND(C" + (siChuanNum + 1) + "/(1+C" + (siChuanNum + 2) + "),0)");
        //四川中欧小计
        ShiftStatDetail siChuanEuropeTotal = shiftStatDetailMap.get("四川" + "中欧" + "合计" + "合计");
        if (siChuanEuropeTotal != null) {
            rowSiChuanTrain.getCell(3).setCellValue(siChuanEuropeTotal.getTotalTrainCount());
            rowSiChuanRatio.getCell(3).setCellValue(ExportStyleUtil.toDouble(divide100(siChuanEuropeTotal.getTrainCountYoy())));
        } else {
            rowSiChuanTrain.getCell(3).setCellValue(0);
            rowSiChuanRatio.getCell(3).setCellValue(0.0);
        }
        rowSiChuanCount.getCell(3).setCellFormula("D" + (siChuanNum + 1) + "-ROUND(D" + (siChuanNum + 1) + "/(1+D" + (siChuanNum + 2) + "),0)");


        //四川中亚去程
        ShiftStatDetail siChuanAsiaGo = shiftStatDetailMap.get("四川" + "中亚" + "合计" + "去程");
        if (siChuanAsiaGo != null) {
            rowSiChuanTrain.getCell(4).setCellValue(siChuanAsiaGo.getTotalTrainCount());
            rowSiChuanRatio.getCell(4).setCellValue(ExportStyleUtil.toDouble(divide100(siChuanAsiaGo.getTrainCountYoy())));
        } else {
            rowSiChuanTrain.getCell(4).setCellValue(0);
            rowSiChuanRatio.getCell(4).setCellValue(0.0);
        }
        rowSiChuanCount.getCell(4).setCellFormula("E" + (siChuanNum + 1) + "-ROUND(E" + (siChuanNum + 1) + "/(1+E" + (siChuanNum + 2) + "),0)");
        //四川中亚回程
        ShiftStatDetail siChuanAsiaReturn = shiftStatDetailMap.get("四川" + "中亚" + "合计" + "回程");
        if (siChuanAsiaReturn != null) {
            rowSiChuanTrain.getCell(5).setCellValue(siChuanAsiaReturn.getTotalTrainCount());
            rowSiChuanRatio.getCell(5).setCellValue(ExportStyleUtil.toDouble(divide100(siChuanAsiaReturn.getTrainCountYoy())));
        } else {
            rowSiChuanTrain.getCell(5).setCellValue(0);
            rowSiChuanRatio.getCell(5).setCellValue(0.0);
        }
        rowSiChuanCount.getCell(5).setCellFormula("F" + (siChuanNum + 1) + "-ROUND(F" + (siChuanNum + 1) + "/(1+F" + (siChuanNum + 2) + "),0)");
        //四川中欧小计
        ShiftStatDetail siChuanAsiaTotal = shiftStatDetailMap.get("四川" + "中亚" + "合计" + "合计");
        if (siChuanAsiaTotal != null) {
            rowSiChuanTrain.getCell(6).setCellValue(siChuanAsiaTotal.getTotalTrainCount());
            rowSiChuanRatio.getCell(6).setCellValue(ExportStyleUtil.toDouble(divide100(siChuanAsiaTotal.getTrainCountYoy())));
        } else {
            rowSiChuanTrain.getCell(6).setCellValue(0);
            rowSiChuanRatio.getCell(6).setCellValue(0.0);
        }
        rowSiChuanCount.getCell(6).setCellFormula("G" + (siChuanNum + 1) + "-ROUND(G" + (siChuanNum + 1) + "/(1+G" + (siChuanNum + 2) + "),0)");

        //四川 写入最后一列
        rowSiChuanTrain.getCell(10).setCellValue("开行");
        rowSiChuanRatio.getCell(10).setCellValue("同比比例");
        rowSiChuanCount.getCell(10).setCellValue("同比列数");

    }

    /**
     * 百分比的数据除以100
     *
     * @param bigDecimal bigDecimal
     * @return BigDecimal
     * <AUTHOR>
     * @since 2025/5/10 上午10:41
     **/
    private static BigDecimal divide100(BigDecimal bigDecimal) {
        if (bigDecimal != null && bigDecimal.compareTo(BigDecimal.ZERO) != 0) {
            bigDecimal = bigDecimal.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
        } else {
            bigDecimal = BigDecimal.ZERO;
        }
        return bigDecimal;
    }

    /**
     * 设置总数sheet单元格样式
     *
     * @param workbook     workbook
     * @param sheet        sheet
     * @param provinceList 省份列表
     * <AUTHOR>
     * @since 2025/5/8 下午1:32
     **/
    private void setMainProvinceSheetCellStyle(Workbook workbook, Sheet sheet, List<String> provinceList) {
        sheet.setDefaultColumnWidth(13);
        //设置单元格的格式
        CellStyle titleStyle = ExportStyleUtil.createTitleStyle(workbook);
        CellStyle detailsStyle = ExportStyleUtil.createDetailsStyleCenter(workbook);
        CellStyle percentageDetailStyle = ExportStyleUtil.createPercentageDetailStyle(workbook);

        //第一行设置成标题明细样式
        Row row0 = sheet.createRow(0);
        setRowStyle(row0, titleStyle, MAIN_PROVINCE_COLUMN_COUNT);
        //第二行设置成明细居右样式
        Row row1 = sheet.createRow(1);
        setRowStyle(row1, titleStyle, MAIN_PROVINCE_COLUMN_COUNT);
        //第三行设置成标题样式、
        Row row2 = sheet.createRow(2);
        setRowStyle(row2, titleStyle, MAIN_PROVINCE_COLUMN_COUNT);
        //第六行开始设置成明细样式
        for (int i = MAIN_PROVINCE_ROW_COUNT; i < provinceList.size() + MAIN_PROVINCE_ROW_COUNT + 1; i++) {
            Row row = sheet.createRow(i);
            setRowStyle(row, detailsStyle, MAIN_PROVINCE_COLUMN_COUNT);
        }
        //锁定第3行和第1列
        sheet.createFreezePane(1, 3);

        //空两行生成省同比表格样式
        //设置表格一级标题样式
        Row rowFirstTitle = sheet.createRow(provinceList.size() + MAIN_PROVINCE_ROW_COUNT + 3);
        setRowStyle(rowFirstTitle, titleStyle, MAIN_PROVINCE_COLUMN_COUNT - 3);
        //设置表格二级标题样式
        Row rowSecondTitle = sheet.createRow(provinceList.size() + MAIN_PROVINCE_ROW_COUNT + 4);
        setRowStyle(rowSecondTitle, titleStyle, MAIN_PROVINCE_COLUMN_COUNT - 3);
        //设置表格三级标题样式
        Row rowThirdTitle = sheet.createRow(provinceList.size() + MAIN_PROVINCE_ROW_COUNT + 5);
        setRowStyle(rowThirdTitle, titleStyle, MAIN_PROVINCE_COLUMN_COUNT - 3);
        //设置明细列样式
        for (int i = 0; i < provinceList.size(); i++) {
            Row row = sheet.createRow(provinceList.size() + MAIN_PROVINCE_ROW_COUNT + 6 + i);
            setRowStyle(row, detailsStyle, MAIN_PROVINCE_COLUMN_COUNT - 3);
        }

        //空一行，写入第三个表格
        //写入第三表格一级标题
        Row ratioRowFirstTitle = sheet.createRow(provinceList.size() + MAIN_PROVINCE_ROW_COUNT + MAIN_PROVINCE_ROW_COUNT + 4 + provinceList.size());
        setRowStyle(ratioRowFirstTitle, titleStyle, MAIN_PROVINCE_COLUMN_COUNT - 3);
        //写入第三表格二级标题
        Row ratioRowSecondTitle = sheet.createRow(provinceList.size() + MAIN_PROVINCE_ROW_COUNT + MAIN_PROVINCE_ROW_COUNT + 5 + provinceList.size());
        setRowStyle(ratioRowSecondTitle, titleStyle, MAIN_PROVINCE_COLUMN_COUNT - 3);

        //设置明细列样式
        for (int i = 0; i < provinceList.size(); i++) {
            Row row = sheet.createRow(provinceList.size() + MAIN_PROVINCE_ROW_COUNT + MAIN_PROVINCE_ROW_COUNT + 6 + provinceList.size() + i);
            setRowStyle(row, percentageDetailStyle, MAIN_PROVINCE_COLUMN_COUNT - 3);
        }


        //空一行，写入第四\第五个表格
        int ratioRowFirstNum = provinceList.size() * 3 + MAIN_PROVINCE_ROW_COUNT * 3 + 4;
        //需要写入单元格样式的行数
        int ratioRowNum = 7;
        for (int i = 0; i < ratioRowNum; i++) {
            //第四行空
            if (i == 3) {
                continue;
            }
            Row row = sheet.createRow(ratioRowFirstNum + i);
            if (i == 1 || i == 5) {
                setRowStyle(row, percentageDetailStyle, MAIN_PROVINCE_COLUMN_COUNT - 2);
            } else {
                setRowStyle(row, detailsStyle, MAIN_PROVINCE_COLUMN_COUNT - 2);
            }

        }

        //隐藏开始行
        int startRow = 30;
        //隐藏结束行
        int endRow = 50;
        for (int i = startRow; i <= endRow; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                row = sheet.createRow(i);
            }
            // 隐藏该行
            row.setZeroHeight(true);
        }
    }

    /**
     * 导出云南、广西数据 sheet
     *
     * @param shiftStat  shiftStat
     * @param sheetTotal sheetTotal
     * @param workbook   workbook
     * <AUTHOR>
     * @since 2025/5/8 下午7:57
     **/
    private void exportYunNanGuangXiSheet(ShiftStat shiftStat, Sheet sheetTotal, Workbook workbook) {
        {
            //遍历明细数据
            List<List<Object>> rowDetailList = new ArrayList<>();
            //遍历表格中需要填写的明细数据,根据城市和一级二级标题、三级标题生成行数据
            //特殊省份
            List<String> specialProvinceList = new ArrayList<>();
            specialProvinceList.add("云南");
            specialProvinceList.add("广西");
            for (int i = 0; i < specialProvinceList.size(); i++) {
                String province = specialProvinceList.get(i);
                //第一行
                List<Object> row = new ArrayList<>();
                row.add(province);
                row.add(shiftStatDetailMap.get(province + "中欧" + "合计" + "去程") == null ?
                        0 : shiftStatDetailMap.get(province + "中欧" + "合计" + "去程").getTotalTrainCount());
                row.add(shiftStatDetailMap.get(province + "中欧" + "合计" + "回程") == null ?
                        0 : shiftStatDetailMap.get(province + "中欧" + "合计" + "回程").getTotalTrainCount());
                row.add("SUM(B" + (YUNAN_GUANGXI_ROW_COUNT + i + 1) + ":C" + (YUNAN_GUANGXI_ROW_COUNT + i + 1) + ")");
                row.add(null);

                row.add(shiftStatDetailMap.get(province + "中欧" + "合计" + "去程") == null ?
                        0 : shiftStatDetailMap.get(province + "中欧" + "合计" + "去程").getTotalBoxCount());
                row.add(shiftStatDetailMap.get(province + "中欧" + "合计" + "回程") == null ?
                        0 : shiftStatDetailMap.get(province + "中欧" + "合计" + "回程").getTotalBoxCount());
                row.add("SUM(F" + (YUNAN_GUANGXI_ROW_COUNT + i + 1) + ":G" + (YUNAN_GUANGXI_ROW_COUNT + i + 1) + ")");

                row.add(shiftStatDetailMap.get(province + "中亚" + "合计" + "去程") == null ?
                        0 : shiftStatDetailMap.get(province + "中亚" + "合计" + "去程").getTotalTrainCount());
                row.add(shiftStatDetailMap.get(province + "中亚" + "合计" + "回程") == null ?
                        0 : shiftStatDetailMap.get(province + "中亚" + "合计" + "回程").getTotalTrainCount());
                row.add("SUM(I" + (YUNAN_GUANGXI_ROW_COUNT + i + 1) + ":J" + (YUNAN_GUANGXI_ROW_COUNT + i + 1) + ")");
                row.add(null);

                row.add(shiftStatDetailMap.get(province + "中亚" + "合计" + "去程") == null ?
                        0 : shiftStatDetailMap.get(province + "中亚" + "合计" + "去程").getTotalBoxCount());
                row.add(shiftStatDetailMap.get(province + "中亚" + "合计" + "回程") == null ?
                        0 : shiftStatDetailMap.get(province + "中亚" + "合计" + "回程").getTotalBoxCount());
                row.add("SUM(M" + (YUNAN_GUANGXI_ROW_COUNT + i + 1) + ":N" + (YUNAN_GUANGXI_ROW_COUNT + i + 1) + ")");

                //合计
                row.add("B" + (YUNAN_GUANGXI_ROW_COUNT + i + 1) + "+I" + (YUNAN_GUANGXI_ROW_COUNT + i + 1));
                row.add("C" + (YUNAN_GUANGXI_ROW_COUNT + i + 1) + "+J" + (YUNAN_GUANGXI_ROW_COUNT + i + 1));
                row.add("D" + (YUNAN_GUANGXI_ROW_COUNT + i + 1) + "+K" + (YUNAN_GUANGXI_ROW_COUNT + i + 1));
                row.add(null);

                row.add("F" + (YUNAN_GUANGXI_ROW_COUNT + i + 1) + "+M" + (YUNAN_GUANGXI_ROW_COUNT + i + 1));
                row.add("G" + (YUNAN_GUANGXI_ROW_COUNT + i + 1) + "+N" + (YUNAN_GUANGXI_ROW_COUNT + i + 1));
                row.add("H" + (YUNAN_GUANGXI_ROW_COUNT + i + 1) + "+O" + (YUNAN_GUANGXI_ROW_COUNT + i + 1));
                rowDetailList.add(row);
            }
            generateYunNanGuangXiSheet(specialProvinceList, rowDetailList, sheetTotal, workbook);
        }
    }

    /**
     * 设置总数sheet单元格样式
     *
     * @param workbook     workbook
     * @param sheet        sheet
     * @param provinceList 省份列表
     * <AUTHOR>
     * @since 2025/5/8 下午1:32
     **/
    private void setYunNanGuangXiSheetCellStyle(Workbook workbook, Sheet sheet, List<String> provinceList) {
        //设置单元格的格式
        CellStyle titleStyle = ExportStyleUtil.createTitleStyle(workbook);
        CellStyle detailsStyle = ExportStyleUtil.createDetailsStyleCenter(workbook);


        //第一行到第三行设置成标题样式、
        for (int i = 0; i < YUNAN_GUANGXI_ROW_COUNT; i++) {
            Row row = sheet.createRow(i);
            setRowStyle(row, titleStyle, YUNAN_GUANGXI_COLUMN_COUNT);
        }
        //第四行开始设置成明细样式
        for (int i = YUNAN_GUANGXI_ROW_COUNT; i < provinceList.size() + YUNAN_GUANGXI_ROW_COUNT; i++) {
            Row row = sheet.createRow(i);
            setRowStyle(row, detailsStyle, YUNAN_GUANGXI_COLUMN_COUNT);
        }
        //锁定第五行和第一列
        sheet.createFreezePane(1, 5);
    }

    /**
     * 生成云南、广西数据sheet
     *
     * @param provinceList  省列表
     * @param rowDetailList 行数据
     * <AUTHOR>
     * @since 2025/5/10 上午11:26
     **/
    private void generateYunNanGuangXiSheet(List<String> provinceList, List<List<Object>> rowDetailList, Sheet sheetTotal, Workbook workbook) {
        {

            //设置单元格样式
            setYunNanGuangXiSheetCellStyle(workbook, sheetTotal, provinceList);
            //第一行
            Row row0 = sheetTotal.getRow(0);
            row0.getCell(0).setCellValue("省份");
            row0.getCell(1).setCellValue("中欧");
            row0.getCell(8).setCellValue("中亚");
            row0.getCell(15).setCellValue("合计");
            sheetTotal.addMergedRegion(new CellRangeAddress(0, 2, 0, 0));
            sheetTotal.addMergedRegion(new CellRangeAddress(0, 0, 1, 7));
            sheetTotal.addMergedRegion(new CellRangeAddress(0, 0, 8, 14));
            sheetTotal.addMergedRegion(new CellRangeAddress(0, 0, 15, 21));
            //第二行
            Row row1 = sheetTotal.getRow(1);
            row1.getCell(1).setCellValue("列数");
            row1.getCell(5).setCellValue("TEU");
            row1.getCell(8).setCellValue("列数");
            row1.getCell(12).setCellValue("TEU");
            row1.getCell(15).setCellValue("列数");
            row1.getCell(19).setCellValue("TEU");
            sheetTotal.addMergedRegion(new CellRangeAddress(1, 1, 1, 4));
            sheetTotal.addMergedRegion(new CellRangeAddress(1, 1, 5, 7));
            sheetTotal.addMergedRegion(new CellRangeAddress(1, 1, 8, 11));
            sheetTotal.addMergedRegion(new CellRangeAddress(1, 1, 12, 14));
            sheetTotal.addMergedRegion(new CellRangeAddress(1, 1, 15, 18));
            sheetTotal.addMergedRegion(new CellRangeAddress(1, 1, 19, 21));

            //第三行
            Row row2 = sheetTotal.getRow(2);
            for (int i = 0; i < TOTAL_COLUMN_TITLE_ARRAY.length; i++) {
                row2.getCell(i + 1).setCellValue(TOTAL_COLUMN_TITLE_ARRAY[i]);
            }

            //第四行
            for (int i = 0; i < rowDetailList.size(); i++) {
                Row row = sheetTotal.getRow(i + 3);
                List<Object> rowDetail = rowDetailList.get(i);
                //省份
                row.getCell(0).setCellValue(rowDetail.get(0).toString());

                //中欧
                //去程
                row.getCell(1).setCellValue(ExportStyleUtil.toInt(rowDetail.get(1)));
                //回程
                row.getCell(2).setCellValue(ExportStyleUtil.toInt(rowDetail.get(2)));
                //列数
                row.getCell(3).setCellFormula(rowDetail.get(3).toString());

                //去程
                row.getCell(5).setCellValue(ExportStyleUtil.toInt(rowDetail.get(5)));
                //回程
                row.getCell(6).setCellValue(ExportStyleUtil.toInt(rowDetail.get(6)));
                //小计
                row.getCell(7).setCellFormula(rowDetail.get(7).toString());

                //中亚
                //去程
                row.getCell(8).setCellValue(ExportStyleUtil.toInt(rowDetail.get(8)));
                //回程
                row.getCell(9).setCellValue(ExportStyleUtil.toInt(rowDetail.get(9)));
                //列数
                row.getCell(10).setCellFormula(rowDetail.get(10).toString());

                //去程
                row.getCell(12).setCellValue(ExportStyleUtil.toInt(rowDetail.get(12)));
                //回程
                row.getCell(13).setCellValue(ExportStyleUtil.toInt(rowDetail.get(13)));
                //小计
                row.getCell(14).setCellFormula(rowDetail.get(14).toString());

                //合计
                //去程
                row.getCell(15).setCellFormula(rowDetail.get(15).toString());
                //回程
                row.getCell(16).setCellFormula(rowDetail.get(16).toString());
                //列数
                row.getCell(17).setCellFormula(rowDetail.get(17).toString());

                //去程
                row.getCell(19).setCellFormula(rowDetail.get(19).toString());
                //回程
                row.getCell(20).setCellFormula(rowDetail.get(20).toString());
                //小计
                row.getCell(21).setCellFormula(rowDetail.get(21).toString());
            }

        }

    }


    /**
     * 排序
     *
     * @param shiftStatDetail 统计明细
     * @param shiftType       中欧、中亚
     * @return Map<String, Integer>
     * <AUTHOR>
     * @since 2025/5/8 下午3:05
     **/
    private Map<String, Integer> sortForShiftStatDetail(ShiftStatDetail shiftStatDetail, String shiftType) {
        ShiftStatDetail detail = new ShiftStatDetail();
        detail.setStatId(shiftStatDetail.getStatId());
        if (shiftType.equals(CHINA_EUROPE) || shiftType.equals(CHINA_ASIA)) {
            detail.setShiftType(shiftType);

        }
        List<ShiftStatDetail> sortDetailList;
        String domesticCity = "合计";
        if (StrUtil.isNotBlank(shiftStatDetail.getDomesticCity()) && domesticCity.equals(shiftStatDetail.getDomesticCity())) {
            sortDetailList = shiftStatDetailMapper.sortForShiftStatDetail(detail);
        } else {
            sortDetailList = shiftStatDetailMapper.sortCityForShiftStatDetail(detail);
        }
        Map<String, Integer> sortMap = new HashMap<>(sortDetailList.size());
        for (int i = 0; i < sortDetailList.size(); i++) {
            ShiftStatDetail sortDetail = sortDetailList.get(i);
            if (StrUtil.isNotBlank(shiftStatDetail.getDomesticCity()) && domesticCity.equals(shiftStatDetail.getDomesticCity())) {
                sortMap.put(sortDetail.getProvince(), i + 1);
            } else {
                sortMap.put(sortDetail.getProvince() + "_" + sortDetail.getDomesticCity(), i + 1);
            }

        }
        return sortMap;
    }

    /**
     * 补全排序
     *
     * @param sortMap             排序
     * @param provinceAndCityList 省份/城市
     * <AUTHOR>
     * @since 2025/5/8 下午5:17
     **/
    private void completeSort(Map<String, Integer> sortMap, List<String> provinceAndCityList) {
        // 获取中欧最大值
        int maxRank = sortMap.values().stream().max(Integer::compareTo).orElse(0);
        int index = 1;
        for (String province : provinceAndCityList) {
            if (!sortMap.containsKey(province)) {
                sortMap.put(province, index + maxRank);
                index++;
            }
        }
    }


    /**
     * 生成表格
     *
     * @param shiftStat     shiftStat
     * @param provinceList  省列表
     * @param rowDetailList 行数据
     * <AUTHOR>
     * @since 2025/5/8 下午3:48
     **/
    public void generateExcel(ShiftStat shiftStat, List<String> provinceList, List<List<Object>> rowDetailList, Sheet sheetTotal, Workbook workbook) {

        //设置单元格样式
        setTotalSheetCellStyle(workbook, sheetTotal, provinceList);
        //第一行合并单元格
        sheetTotal.addMergedRegion(new CellRangeAddress(0, 0, 0, TOTAL_COLUMN_COUNT - 1));
        //写入标题
        Row row0 = sheetTotal.getRow(0);
        Cell cell0 = row0.getCell(0);
        //国内主要省市中欧班列发运量（自然列）
        // 提取年份（以“年”结尾前的数字）
        String title = shiftStat.getTitle();
        String year = title.substring(0, title.indexOf("年") + 1);
        // 提取月份（位于“年”和“月”之间的部分）
        int yearIndex = title.indexOf("年");
        String month = title.substring(yearIndex + 1, title.indexOf("月") + 1);
        if (StrUtil.isNotBlank(year) && StrUtil.isNotBlank(month)) {
            cell0.setCellValue(year + "1-" + month + "国内主要省市中欧班列发运量（自然列）");
        } else {
            cell0.setCellValue("国内主要省市中欧班列发运量（自然列）");
        }

        //第二行
        sheetTotal.addMergedRegion(new CellRangeAddress(1, 1, 0, TOTAL_COLUMN_COUNT - 1));
        Row row1 = sheetTotal.getRow(1);
        Cell cell1 = row1.getCell(0);
        cell1.setCellValue("数据来源：中欧班列网；不含云南、广西数据");
        //第三行
        Row row2 = sheetTotal.getRow(2);
        row2.getCell(0).setCellValue("省份");
        row2.getCell(1).setCellValue("中欧");
        row2.getCell(8).setCellValue("中亚");
        row2.getCell(15).setCellValue("合计");
        sheetTotal.addMergedRegion(new CellRangeAddress(2, 4, 0, 0));
        sheetTotal.addMergedRegion(new CellRangeAddress(2, 2, 1, 7));
        sheetTotal.addMergedRegion(new CellRangeAddress(2, 2, 8, 14));
        sheetTotal.addMergedRegion(new CellRangeAddress(2, 2, 15, 21));
        //第四行
        Row row3 = sheetTotal.getRow(3);
        row3.getCell(1).setCellValue("列数");
        row3.getCell(5).setCellValue("TEU");
        row3.getCell(8).setCellValue("列数");
        row3.getCell(12).setCellValue("TEU");
        row3.getCell(15).setCellValue("列数");
        row3.getCell(19).setCellValue("TEU");
        sheetTotal.addMergedRegion(new CellRangeAddress(3, 3, 1, 4));
        sheetTotal.addMergedRegion(new CellRangeAddress(3, 3, 5, 7));
        sheetTotal.addMergedRegion(new CellRangeAddress(3, 3, 8, 11));
        sheetTotal.addMergedRegion(new CellRangeAddress(3, 3, 12, 14));
        sheetTotal.addMergedRegion(new CellRangeAddress(3, 3, 15, 18));
        sheetTotal.addMergedRegion(new CellRangeAddress(3, 3, 19, 21));

        //第五行
        Row row4 = sheetTotal.getRow(4);
        for (int i = 0; i < TOTAL_COLUMN_TITLE_ARRAY.length; i++) {
            row4.getCell(i + 1).setCellValue(TOTAL_COLUMN_TITLE_ARRAY[i]);
        }

        //第七行
        for (int i = 0; i < rowDetailList.size(); i++) {
            Row row = sheetTotal.getRow(i + TOTAL_ROW_COUNT);
            List<Object> rowDetail = rowDetailList.get(i);
            //省份
            row.getCell(0).setCellValue(rowDetail.get(0).toString());

            //中欧
            //去程
            row.getCell(1).setCellValue(ExportStyleUtil.toInt(rowDetail.get(1)));
            //回程
            row.getCell(2).setCellValue(ExportStyleUtil.toInt(rowDetail.get(2)));
            //列数
            row.getCell(3).setCellFormula(rowDetail.get(3).toString());
            //排名
            row.getCell(4).setCellValue(ExportStyleUtil.toInt(rowDetail.get(4)));
            //去程
            row.getCell(5).setCellValue(ExportStyleUtil.toInt(rowDetail.get(5)));
            //回程
            row.getCell(6).setCellValue(ExportStyleUtil.toInt(rowDetail.get(6)));
            //小计
            row.getCell(7).setCellFormula(rowDetail.get(7).toString());

            //中亚
            //去程
            row.getCell(8).setCellValue(ExportStyleUtil.toInt(rowDetail.get(8)));
            //回程
            row.getCell(9).setCellValue(ExportStyleUtil.toInt(rowDetail.get(9)));
            //列数
            row.getCell(10).setCellFormula(rowDetail.get(10).toString());
            //排名
            row.getCell(11).setCellValue(ExportStyleUtil.toInt(rowDetail.get(11)));
            //去程
            row.getCell(12).setCellValue(ExportStyleUtil.toInt(rowDetail.get(12)));
            //回程
            row.getCell(13).setCellValue(ExportStyleUtil.toInt(rowDetail.get(13)));
            //小计
            row.getCell(14).setCellFormula(rowDetail.get(14).toString());

            //合计
            //去程
            row.getCell(15).setCellFormula(rowDetail.get(15).toString());
            //回程
            row.getCell(16).setCellFormula(rowDetail.get(16).toString());
            //列数
            row.getCell(17).setCellFormula(rowDetail.get(17).toString());
            //排名
            row.getCell(18).setCellValue(ExportStyleUtil.toInt(rowDetail.get(18)));
            //去程
            row.getCell(19).setCellFormula(rowDetail.get(19).toString());
            //回程
            row.getCell(20).setCellFormula(rowDetail.get(20).toString());
            //小计
            row.getCell(21).setCellFormula(rowDetail.get(21).toString());
        }
        //写入第6行
        Row row5 = sheetTotal.getRow(5);
        String provinceTotal = "国铁总数";
        row5.getCell(0).setCellValue(provinceTotal);
        row5.getCell(1).setCellValue(shiftStatDetailMap.get(provinceTotal + "中欧" + "合计" + "去程") == null ?
                0 : shiftStatDetailMap.get(provinceTotal + "中欧" + "合计" + "去程").getTotalTrainCount());
        row5.getCell(2).setCellValue(shiftStatDetailMap.get(provinceTotal + "中欧" + "合计" + "回程") == null ?
                0 : shiftStatDetailMap.get(provinceTotal + "中欧" + "合计" + "回程").getTotalTrainCount());
        row5.getCell(3).setCellValue(shiftStatDetailMap.get(provinceTotal + "中欧" + "合计" + "合计") == null ?
                0 : shiftStatDetailMap.get(provinceTotal + "中欧" + "合计" + "合计").getTotalTrainCount());
        row5.getCell(4).setCellValue("--");

        row5.getCell(5).setCellValue(shiftStatDetailMap.get(provinceTotal + "中欧" + "合计" + "去程") == null ?
                0 : shiftStatDetailMap.get(provinceTotal + "中欧" + "合计" + "去程").getTotalBoxCount());
        row5.getCell(6).setCellValue(shiftStatDetailMap.get(provinceTotal + "中欧" + "合计" + "回程") == null ?
                0 : shiftStatDetailMap.get(provinceTotal + "中欧" + "合计" + "回程").getTotalBoxCount());
        row5.getCell(7).setCellValue(shiftStatDetailMap.get(provinceTotal + "中欧" + "合计" + "合计") == null ?
                0 : shiftStatDetailMap.get(provinceTotal + "中欧" + "合计" + "合计").getTotalBoxCount());

        row5.getCell(8).setCellValue(shiftStatDetailMap.get(provinceTotal + "中亚" + "合计" + "去程") == null ?
                0 : shiftStatDetailMap.get(provinceTotal + "中亚" + "合计" + "去程").getTotalTrainCount());
        row5.getCell(9).setCellValue(shiftStatDetailMap.get(provinceTotal + "中亚" + "合计" + "回程") == null ?
                0 : shiftStatDetailMap.get(provinceTotal + "中亚" + "合计" + "回程").getTotalTrainCount());
        row5.getCell(10).setCellValue(shiftStatDetailMap.get(provinceTotal + "中亚" + "合计" + "合计") == null ?
                0 : shiftStatDetailMap.get(provinceTotal + "中亚" + "合计" + "合计").getTotalTrainCount());
        row5.getCell(11).setCellValue("--");

        row5.getCell(12).setCellValue(shiftStatDetailMap.get(provinceTotal + "中亚" + "合计" + "去程") == null ?
                0 : shiftStatDetailMap.get(provinceTotal + "中亚" + "合计" + "去程").getTotalBoxCount());
        row5.getCell(13).setCellValue(shiftStatDetailMap.get(provinceTotal + "中亚" + "合计" + "回程") == null ?
                0 : shiftStatDetailMap.get(provinceTotal + "中亚" + "合计" + "回程").getTotalBoxCount());
        row5.getCell(14).setCellValue(shiftStatDetailMap.get(provinceTotal + "中亚" + "合计" + "合计") == null ?
                0 : shiftStatDetailMap.get(provinceTotal + "中亚" + "合计" + "合计").getTotalBoxCount());


        row5.getCell(15).setCellFormula("B6+I6");
        row5.getCell(16).setCellFormula("C6+J6");
        row5.getCell(17).setCellFormula("D6+K6");
        row5.getCell(18).setCellValue("--");
        row5.getCell(19).setCellFormula("F6+M6");
        row5.getCell(20).setCellFormula("G6+N6");
        row5.getCell(21).setCellFormula("H6+O6");

        //写入特殊样式
        CellStyle greenStyle = ExportStyleUtil.createGreenStyle(workbook);
        for (int i = 0; i < rowDetailList.size(); i++) {
            String province = rowDetailList.get(i).get(0).toString();
            if (Arrays.asList(SPECIAL_PROVINCE_CITY).contains(province)) {
                //整行变绿
                Row row = sheetTotal.getRow(i + TOTAL_ROW_COUNT);
                for (int j = 0; j < TOTAL_COLUMN_COUNT; j++) {
                    row.getCell(j).setCellStyle(greenStyle);
                }
            }
        }


    }

    /**
     * 设置总数sheet单元格样式
     *
     * @param workbook     workbook
     * @param sheet        sheet
     * @param provinceList 省份列表
     * <AUTHOR>
     * @since 2025/5/8 下午1:32
     **/
    private void setTotalSheetCellStyle(Workbook workbook, Sheet sheet, List<String> provinceList) {
        //设置单元格的格式
        CellStyle titleStyle = ExportStyleUtil.createTitleStyle(workbook);
        CellStyle titleDetailStyle = ExportStyleUtil.createTitleDetailStyle(workbook);
        CellStyle detailsStyle = ExportStyleUtil.createDetailsStyleCenter(workbook);
        //居右样式
        CellStyle detailsStyleRight = ExportStyleUtil.createDetailsStyleRight(workbook);

        //第一行设置成标题明细样式
        Row row0 = sheet.createRow(0);
        setRowStyle(row0, titleDetailStyle, TOTAL_COLUMN_COUNT);
        //第二行设置成明细居右样式
        Row row1 = sheet.createRow(1);
        setRowStyle(row1, detailsStyleRight, TOTAL_COLUMN_COUNT);
        //第三行到第五行设置成标题样式、
        for (int i = 2; i < TOTAL_ROW_COUNT - 1; i++) {
            Row row = sheet.createRow(i);
            setRowStyle(row, titleStyle, TOTAL_COLUMN_COUNT);
        }
        //第六行开始设置成明细样式
        for (int i = TOTAL_ROW_COUNT; i < provinceList.size() + TOTAL_ROW_COUNT + 1; i++) {
            Row row = sheet.createRow(i - 1);
            setRowStyle(row, detailsStyle, TOTAL_COLUMN_COUNT);
        }
        //锁定第五行和第一列
        sheet.createFreezePane(1, 5);
    }


    /**
     * 设置行样式
     *
     * @param row   行
     * @param style 样式
     * <AUTHOR>
     * @since 2025/5/8 下午1:27
     **/
    public void setRowStyle(Row row, CellStyle style, int columnCount) {
        for (int i = 0; i < columnCount; i++) {
            Cell cell = row.createCell(i);
            cell.setCellStyle(style);
        }
    }
}


