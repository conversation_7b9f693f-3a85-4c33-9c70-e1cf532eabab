package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FdBusCostWaybillMapper;
import com.huazheng.tunny.ocean.api.entity.FdBusCostWaybill;
import com.huazheng.tunny.ocean.service.FdBusCostWaybillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fdBusCostWaybillService")
public class FdBusCostWaybillServiceImpl extends ServiceImpl<FdBusCostWaybillMapper, FdBusCostWaybill> implements FdBusCostWaybillService {

    @Autowired
    private FdBusCostWaybillMapper fdBusCostWaybillMapper;

    /**
     * 查询业务流程单运单表信息
     *
     * @param id 业务流程单运单表ID
     * @return 业务流程单运单表信息
     */
    @Override
    public FdBusCostWaybill selectFdBusCostWaybillById(Integer id)
    {
        return fdBusCostWaybillMapper.selectFdBusCostWaybillById(id);
    }

    /**
     * 查询业务流程单运单表列表
     *
     * @param fdBusCostWaybill 业务流程单运单表信息
     * @return 业务流程单运单表集合
     */
    @Override
    public List<FdBusCostWaybill> selectFdBusCostWaybillList(FdBusCostWaybill fdBusCostWaybill)
    {
        return fdBusCostWaybillMapper.selectFdBusCostWaybillList(fdBusCostWaybill);
    }


    /**
     * 分页模糊查询业务流程单运单表列表
     * @return 业务流程单运单表集合
     */
    @Override
    public Page selectFdBusCostWaybillListByLike(Query query)
    {
        FdBusCostWaybill fdBusCostWaybill =  BeanUtil.mapToBean(query.getCondition(), FdBusCostWaybill.class,false);
        query.setRecords(fdBusCostWaybillMapper.selectFdBusCostWaybillListByLike(query,fdBusCostWaybill));
        return query;
    }

    /**
     * 新增业务流程单运单表
     *
     * @param fdBusCostWaybill 业务流程单运单表信息
     * @return 结果
     */
    @Override
    public int insertFdBusCostWaybill(FdBusCostWaybill fdBusCostWaybill)
    {
        return fdBusCostWaybillMapper.insertFdBusCostWaybill(fdBusCostWaybill);
    }

    /**
     * 修改业务流程单运单表
     *
     * @param fdBusCostWaybill 业务流程单运单表信息
     * @return 结果
     */
    @Override
    public int updateFdBusCostWaybill(FdBusCostWaybill fdBusCostWaybill)
    {
        return fdBusCostWaybillMapper.updateFdBusCostWaybill(fdBusCostWaybill);
    }


    /**
     * 删除业务流程单运单表
     *
     * @param id 业务流程单运单表ID
     * @return 结果
     */
    public int deleteFdBusCostWaybillById(Integer id)
    {
        return fdBusCostWaybillMapper.deleteFdBusCostWaybillById( id);
    };


    /**
     * 批量删除业务流程单运单表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFdBusCostWaybillByIds(Integer[] ids)
    {
        return fdBusCostWaybillMapper.deleteFdBusCostWaybillByIds( ids);
    }

}
