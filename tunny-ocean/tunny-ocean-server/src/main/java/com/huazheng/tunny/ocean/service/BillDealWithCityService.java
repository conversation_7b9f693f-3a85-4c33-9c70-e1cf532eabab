package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.AddCostByBillDTO;
import com.huazheng.tunny.ocean.api.dto.ExchangeRateCalculationDTO;
import com.huazheng.tunny.ocean.api.entity.BillDealWithCity;
import com.huazheng.tunny.ocean.api.vo.BalanceSubBillVO;

import java.util.List;
import java.util.Map;

/**
 * 应付账单（市） 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-06-07 14:04:14
 */
public interface BillDealWithCityService extends IService<BillDealWithCity> {
    /**
     * 查询应付账单（市）信息
     *
     * @param id 应付账单（市）ID
     * @return 应付账单（市）信息
     */
    public R selectBillDealWithCityById(Map<String, Object> params);

    Map<String, Object> selectCostInfoByBillId(Query query);

    R updateYfOfSf(Integer id, Integer pageType);

    R addCostByBill(AddCostByBillDTO addCostByBillDTO);

    /**
     * 查询应付账单（市）列表
     *
     * @param billDealWithCity 应付账单（市）信息
     * @return 应付账单（市）集合
     */
    public List<BillDealWithCity> selectBillDealWithCityList(BillDealWithCity billDealWithCity);


    /**
     * 分页模糊查询应付账单（市）列表
     *
     * @return 应付账单（市）集合
     */
    public Page selectBillDealWithCityListByLike(Query query);

    public Page pageSubBill(Query query);

    public List<BalanceSubBillVO> selectCustomerList(Map<String, Object> params);


    /**
     * 新增应付账单（市）
     *
     * @param billDealWithCity 应付账单（市）信息
     * @return 结果
     */
    public int insertBillDealWithCity(BillDealWithCity billDealWithCity);

    /**
     * 修改应付账单（市）
     *
     * @param billDealWithCity 应付账单（市）信息
     * @return 结果
     */
    public int updateBillDealWithCity(BillDealWithCity billDealWithCity);

    /**
     * 删除应付账单（市）
     *
     * @param id 应付账单（市）ID
     * @return 结果
     */
    public int deleteBillDealWithCityById(Integer id);

    /**
     * 批量删除应付账单（市）
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteBillDealWithCityByIds(Integer[] ids);

    public R getCostByBillId(Integer id,Integer pageType);

    public R addCalculation(ExchangeRateCalculationDTO exchangeRateCalculationDTO);

}

