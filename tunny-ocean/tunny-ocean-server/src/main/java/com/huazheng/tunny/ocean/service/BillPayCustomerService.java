package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.BillCostDetailDTO;
import com.huazheng.tunny.ocean.api.entity.BillPayCustomer;
import com.huazheng.tunny.ocean.api.vo.BillCostDetailVO;

import java.util.List;
import java.util.Map;

/**
 * 应收账单（市） 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-06-11 15:39:26
 */
public interface BillPayCustomerService extends IService<BillPayCustomer> {
    /**
     * 查询应收账单（市）信息
     *
     * @param params 应收账单（市）ID
     * @return 应收账单（市）信息
     */
    public R selectBillIncomeCityById(Map<String, Object> params);

    Map<String, Object> selectCostInfoByBillId(Query query);

    Map<String, Object> selectCostInfoByBillId2(BillCostDetailDTO billCostDetailDTO);

    /**
     * 查询应收账单（市）列表
     *
     * @param billPayCustomer 应收账单（市）信息
     * @return 应收账单（市）集合
     */
    public List<BillPayCustomer> selectBillIncomeCityList(BillPayCustomer billPayCustomer);


    /**
     * 分页模糊查询应收账单（市）列表
     * @return 应收账单（市）集合
     */
    public Page selectBillIncomeCityListByLike(Query query);



    /**
     * 新增应收账单（市）
     *
     * @param billPayCustomer 应收账单（市）信息
     * @return 结果
     */
    public int insertBillIncomeCity(BillPayCustomer billPayCustomer);

    /**
     * 修改应收账单（市）
     *
     * @param billPayCustomer 应收账单（市）信息
     * @return 结果
     */
    public int updateBillIncomeCity(BillPayCustomer billPayCustomer);

    /**
     * 删除应收账单（市）
     *
     * @param id 应收账单（市）ID
     * @return 结果
     */
    public int deleteBillIncomeCityById(Integer id);

    /**
     * 批量删除应收账单（市）
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteBillIncomeCityByIds(Integer[] ids);

    public List<BillCostDetailVO> selectCostInfoByCostCode(Query query, BillCostDetailDTO billCostDetailDTO);

    public List<BillCostDetailVO> selectCostInfoByCityCostCode(Query query, BillCostDetailDTO billCostDetailDTO);

    public List<BillCostDetailVO> selectCostInfoByOrderInfo(Query query, BillCostDetailDTO billCostDetailDTO);


}

