package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.BasChangeboxDetail;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 换箱申请箱号信息表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-08-25 18:14:11
 */
public interface BasChangeboxDetailService extends IService<BasChangeboxDetail> {
    /**
     * 查询换箱申请箱号信息表信息
     *
     * @param rowId 换箱申请箱号信息表ID
     * @return 换箱申请箱号信息表信息
     */
    public BasChangeboxDetail selectBasChangeboxDetailById(String rowId);

    /**
     * 查询换箱申请箱号信息表列表
     *
     * @param basChangeboxDetail 换箱申请箱号信息表信息
     * @return 换箱申请箱号信息表集合
     */
    public List<BasChangeboxDetail> selectBasChangeboxDetailList(BasChangeboxDetail basChangeboxDetail);


    /**
     * 分页模糊查询换箱申请箱号信息表列表
     * @return 换箱申请箱号信息表集合
     */
    public Page selectBasChangeboxDetailListByLike(Query query);


    /**
     * 新增换箱申请箱号信息表
     *
     * @param basChangeboxDetail 换箱申请箱号信息表信息
     * @return 结果
     */
    public int insertBasChangeboxDetail(BasChangeboxDetail basChangeboxDetail);

    /**
     * 修改换箱申请箱号信息表
     *
     * @param basChangeboxDetail 换箱申请箱号信息表信息
     * @return 结果
     */
    public int updateBasChangeboxDetail(BasChangeboxDetail basChangeboxDetail);

    /**
     * 删除换箱申请箱号信息表
     *
     * @param basChangeboxDetail 换箱申请箱号信息表ID
     * @return 结果
     */
    public int deleteBasChangeboxDetailById(BasChangeboxDetail basChangeboxDetail);

    /**
     * 批量删除换箱申请箱号信息表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteBasChangeboxDetailByIds(Integer[] rowIds);

}

