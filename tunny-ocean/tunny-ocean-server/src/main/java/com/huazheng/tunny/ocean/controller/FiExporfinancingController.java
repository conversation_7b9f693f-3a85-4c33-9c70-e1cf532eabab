package com.huazheng.tunny.ocean.controller;


import cn.hutool.json.JSONObject;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.service.*;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;


/**
 * 出口融资主表
 *
 * <AUTHOR> code ocean
 * @date 2022-04-25 13:55:16
 */
@RestController
@RequestMapping("/fiexporfinancing")
@Slf4j
public class FiExporfinancingController {
    @Autowired
    private FiExporfinancingService fiExporfinancingService;
    @Autowired
    private FiExporfinancingApplyService fiExporfinancingApplyService;
    @Autowired
    private FiExporfinancingInvoicesService fiExporfinancingInvoicesService;
    @Autowired
    private FiExporfinancingInvoicesManifestsService fiExporfinancingInvoicesManifestsService;
    @Autowired
    private SysAttachmentsService sysAttachmentsService;
    @Autowired
    private SignatureController signatureController;
    @Autowired
    private FiExporfinancingAcceptService fiExporfinancingAcceptService;
    @Autowired
    private FiExporfinancingAuditService fiExporfinancingAuditService;
    @Autowired
    private FiExporfinancingPayService fiExporfinancingPayService;
    @Autowired
    private FiExporfinancingRecsService fiExporfinancingRecsService;
    @Autowired
    private OperationLogService operationLogService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fiExporfinancingService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fiExporfinancingService.selectFiExporfinancingListByLike(new Query<>(params));
    }


    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        FiExporfinancing fiExporfinancing =fiExporfinancingService.selectById(rowId);
        return new R<>(fiExporfinancing);
    }

    /**
     * 详情
     * @param params
     * @return
     */
    @GetMapping("/selectByAssetCode")
    public R selectByAssetCode(@RequestParam Map<String, Object> params) {
        return fiExporfinancingService.selectByAssetCode(params);
    }

    /**
     * 保存
     * @param fiExporfinancing
     * @return R
     */
    @PostMapping
    public R save(@RequestBody FiExporfinancing fiExporfinancing) {
        fiExporfinancingService.insert(fiExporfinancing);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param fiExporfinancing
     * @return R
     */
    @PutMapping
    public R update(@RequestBody FiExporfinancing fiExporfinancing) {
        fiExporfinancingService.updateById(fiExporfinancing);
        return new R<>(Boolean.TRUE);
    }

    //"进出口融资情况同步
    @PostMapping("/updateFiExporFinancing")
    public String updateFiExporFinancing(@RequestBody JSONObject jsonObject) {
        String content = null;

        try {
            String data = signatureController.getPost(jsonObject);
            String content2 = String.valueOf(jsonObject.get("content"));
            OperationLog log4 = new OperationLog();
            log4.setUuid(UUID.randomUUID().toString());
            log4.setProcessType("进出口融资情况同步");
            log4.setOperationCode("zc");
            log4.setOperationName("中钞");
            log4.setOperationTime(new Date());
            log4.setOperationOpinion(content2 + data );
            operationLogService.insertOperationLog(log4);

            FiExporfinancing fiExporfinancing = JSONUtil.toBean(data, FiExporfinancing.class);
            FiExporfinancingApply fiExporfinancingApply = fiExporfinancingApplyService.selectFiExporfinancingApplyByAssetCode(fiExporfinancing.getAssetCode());
            if(fiExporfinancingApply!=null){
                if(fiExporfinancing.getAssetState()!=null &&!"".equals(fiExporfinancing.getAssetState())&&!fiExporfinancing.getAssetState().equals(fiExporfinancingApply.getAssetState())){

                    if("ACCEPT_AGREE".equals(fiExporfinancing.getAssetState())||"ACCEPT_REFUSE".equals(fiExporfinancing.getAssetState())){
                        //融资受理通过，待审核
                        FiExporfinancingAccept accept = fiExporfinancing.getAccept();
                        if(accept!=null){
                            accept.setRowId(UUID.randomUUID().toString());
                            accept.setMainRowId(fiExporfinancingApply.getRowId());
                            accept.setAssetCode(fiExporfinancingApply.getAssetCode());
                            accept.setAssetState(fiExporfinancingApply.getAssetState());
                            accept.setAddWho("ZC");
                            accept.setAddWhoName("中钞");
                            accept.setAddTime(LocalDateTime.now());
                            fiExporfinancingAcceptService.insertFiExporfinancingAccept(accept);

                        }
                    }
                    if("AUDIT_AGREE".equals(fiExporfinancing.getAssetState())||"AUDIT_REFUSE".equals(fiExporfinancing.getAssetState())){
                        //融资审核通过，待放款
                        FiExporfinancingAudit audit = fiExporfinancing.getAudit();
                        if(audit!=null){
                            audit.setRowId(UUID.randomUUID().toString());
                            audit.setMainRowId(fiExporfinancingApply.getRowId());
                            audit.setAssetCode(fiExporfinancingApply.getAssetCode());
                            if(audit.getAcceptAmount()!=null){
                                audit.setAcceptAmountDecimal((BigDecimal.valueOf(audit.getAcceptAmount())).divide(BigDecimal.valueOf(100)));

                            }
                            audit.setAssetState(fiExporfinancingApply.getAssetState());
                            audit.setAddWho("ZC");
                            audit.setAddWhoName("中钞");
                            audit.setAddTime(LocalDateTime.now());
                            fiExporfinancingAuditService.insertFiExporfinancingAudit(audit);

                        }
                    }
                    if("PAY".equals(fiExporfinancing.getAssetState())||"CANCEL_PAY".equals(fiExporfinancing.getAssetState())){
                        //放款登记，待还款
                        FiExporfinancingPay pay = fiExporfinancing.getPay();
                        if(pay!=null){
                            pay.setRowId(UUID.randomUUID().toString());
                            pay.setMainRowId(fiExporfinancingApply.getRowId());
                            pay.setAssetCode(fiExporfinancingApply.getAssetCode());
                            if(pay.getPayAmount()!=null){
                                pay.setPayAmountDecimal((BigDecimal.valueOf(pay.getPayAmount())).divide(BigDecimal.valueOf(100)));

                            }
                            pay.setAddWho("ZC");
                            pay.setAddWhoName("中钞");
                            pay.setAddTime(LocalDateTime.now());
                            fiExporfinancingPayService.insertFiExporfinancingPay(pay);
                        }

                    }
                    if("CLEAR".equals(fiExporfinancing.getAssetState())){
                        //还款结清，已结清
                        List<FiExporfinancingRecs> recs = fiExporfinancing.getRec().getRecs();
                        if(recs!=null && recs.size()>0){
                            for (FiExporfinancingRecs r:recs
                            ) {
                                r.setRowId(UUID.randomUUID().toString());
                                r.setMainRowId(fiExporfinancingApply.getRowId());
                                r.setAssetCode(fiExporfinancingApply.getAssetCode());
                                if(r.getRecAmount()!=null){
                                    r.setRecAmountDecimal((BigDecimal.valueOf(r.getRecAmount())).divide(BigDecimal.valueOf(100)));

                                }
                                r.setAddWho("ZC");
                                r.setAddWhoName("中钞");
                                r.setAddTime(LocalDateTime.now());
                                fiExporfinancingRecsService.insertFiExporfinancingRecs(r);
                            }
                        }
                    }

                    FiExporfinancingApply apply = new FiExporfinancingApply();
                    apply.setRowId(fiExporfinancingApply.getRowId());
                    apply.setAssetState(fiExporfinancing.getAssetState());
                    apply.setBizState(fiExporfinancing.getBizState());
                    apply.setUpdateWho("ZC");
                    apply.setUpdateWhoName("中钞");
                    apply.setUpdateTime(LocalDateTime.now());
                    fiExporfinancingApplyService.updateFiExporfinancingApply(apply);

                }
                content = JSONUtil.parseObj(new R<>(Boolean.TRUE,"同步数据完成！"), false).toStringPretty();
            }else{
                content = JSONUtil.parseObj(new R<>(Boolean.FALSE,"未查询到该融资申请！"), false).toStringPretty();
            }

        }catch (Exception e){

            content = JSONUtil.parseObj(new R<>(Boolean.FALSE,e.getMessage()), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPost("/fiexporfinancing/updateFiExporfinancing", content);
        return result;
    }



    /**
     * 删除
     * @param rowId
     * @return R
     */
    @DeleteMapping("/{rowId}")
    public R delete(@PathVariable  String rowId) {
        fiExporfinancingService.deleteById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        fiExporfinancingService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<FiExporfinancing> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = fiExporfinancingService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<FiExporfinancing> list = reader.readAll(FiExporfinancing.class);
        fiExporfinancingService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }
}
