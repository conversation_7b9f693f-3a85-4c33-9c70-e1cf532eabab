package com.huazheng.tunny.ocean.controller.eabusinessprocess;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.dto.eabusinessprocess.EaBusinessMainGroupDTO;
import com.huazheng.tunny.ocean.api.dto.eabusinessprocess.EaBusinessProcessDto;
import com.huazheng.tunny.ocean.api.entity.eabusinessprocess.EaBusinessProcess;
import com.huazheng.tunny.ocean.api.vo.eabusinessprocess.EaBusinessProcessVo;
import com.huazheng.tunny.ocean.service.eabusinessprocess.EaBusinessProcessService;
import com.huazheng.tunny.ocean.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 业务流程单表
 *
 * <AUTHOR> code generator
 * @date 2025-06-18 11:00:01
 */
@Slf4j
@RestController
@RequestMapping("/eabusinessprocess")
public class EaBusinessProcessController {

    @Autowired
    private EaBusinessProcessService eaBusinessProcessService;

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  eaBusinessProcessService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return eaBusinessProcessService.selectEaBusinessProcessListByLike(new Query<>(params));
    }

    /**
     * 信息
     *
     * @param businessProcessId
     * @return R
     */
    @GetMapping("/{businessProcessId}")
    public R info(@PathVariable("businessProcessId") Long businessProcessId) {
        EaBusinessProcessVo eaBusinessProcess = eaBusinessProcessService.selectBusinessProcessList(businessProcessId);
        return new R<>(eaBusinessProcess);
    }

    /**
     * 保存
     *
     * @param eaBusinessProcess
     * @return R
     */
    @PostMapping
    public R save(@RequestBody EaBusinessProcess eaBusinessProcess) {
        eaBusinessProcessService.insert(eaBusinessProcess);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     *
     * @param eaBusinessProcess
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody EaBusinessProcess eaBusinessProcess) {

        return eaBusinessProcessService.updateEaBusinessProcessById(eaBusinessProcess);
    }

    /**
     * 提交
     *
     * @param eaBusinessProcess
     * @return R
     */
    @PostMapping("/submit")
    public R submit(@RequestBody EaBusinessProcess eaBusinessProcess) {

        return eaBusinessProcessService.submit(eaBusinessProcess);
    }

    /**
     * 审核
     *
     * @param eaBusinessProcess
     * @return R
     */
    @PostMapping("/review")
    public R review(@RequestBody EaBusinessProcessDto eaBusinessProcess) {

        return eaBusinessProcessService.review(eaBusinessProcess);
    }

    /**
     * 撤回
     *
     * @param eaBusinessProcess
     * @return R
     */
    @PostMapping("/withdrawal")
    public R orderWithdrawal(@RequestBody EaBusinessProcess eaBusinessProcess) {

        return eaBusinessProcessService.updateWithdrawal(eaBusinessProcess);
    }


    /**
     * 删除
     *
     * @param businessProcessId
     * @return R
     */
    @GetMapping("/del/{businessProcessId}")
    public R delete(@PathVariable Long businessProcessId) {
        eaBusinessProcessService.deleteById(businessProcessId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     *
     * @param businessProcessIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody List<Long> businessProcessIds) {
        eaBusinessProcessService.deleteBatchIds(businessProcessIds);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 导出EXCEL
     *
     * @return
     */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {

        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<EaBusinessProcess> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<EaBusinessProcess> list = eaBusinessProcessService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), EaBusinessProcess.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }

    /**
     * 一次导入(箱汇)
     */
    @PostMapping("/importOnceBusinessProcess")
    public R importOnceBusinessProcess(@RequestParam("file") MultipartFile file) throws Exception {
        return eaBusinessProcessService.importOnceBusinessProcess(file);
    }

    /**
     * 二次导入(箱号)
     */
    @PostMapping("/importSecondaryBoxNo")
    public R importSecondaryBoxNo(@RequestParam("file") MultipartFile file) throws Exception {
        return eaBusinessProcessService.importSecondaryBoxNo(file);
    }

    /**
     * 导出业务流程单PDF
     *
     * @return
     */
    @PostMapping("/exportBusProcessToPDF")
    public R exportBusProcessToPDF(@RequestBody EaBusinessMainGroupDTO eaBusinessProcessDto, HttpServletResponse response) throws Exception {
        return R.success(eaBusinessProcessService.exportBusProcessToPDF(eaBusinessProcessDto, response));
    }

    /**
     * 导出业务流程单PDF
     *
     * @return
     */
    @PostMapping("/exportBusProcessToPNG")
    public R exportBusProcessToPNG(@RequestBody EaBusinessMainGroupDTO eaBusinessProcessDto, HttpServletResponse response) throws Exception {
        return R.success(eaBusinessProcessService.exportBusProcessToPNG(eaBusinessProcessDto, response));
    }

    /**
     * 获取业务流程单数量
     *
     * @param
     * @return
     * @throws Exception
     */
    @PostMapping("/businessProcessMainNum")
    public R businessProcessMainNum(@RequestBody EaBusinessMainGroupDTO eaBusinessProcess) {
        return eaBusinessProcessService.businessProcessMainNum(eaBusinessProcess);
    }

    /**
     * 保存附件
     *
     * @param eaBusinessProcess
     * @return R
     */
    @PostMapping("/saveFiles")
    public R saveFiles(@RequestBody EaBusinessProcess eaBusinessProcess) {
        return eaBusinessProcessService.saveFiles(eaBusinessProcess);
    }

    /**
     * 下载业务流程单PDF
     *
     * @return
     */
    @PostMapping("/exportBusProcessPDF")
    public void exportBusProcessPDF(@RequestBody EaBusinessMainGroupDTO eaBusinessProcessDto, HttpServletResponse response) throws Exception {
        eaBusinessProcessService.exportBusProcessPDF(eaBusinessProcessDto, response);
    }

}
