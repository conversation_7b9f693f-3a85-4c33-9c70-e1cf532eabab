package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.google.gson.Gson;
import com.huazheng.tunny.common.core.util.ExcelObjectCheckerUtil;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.*;
import com.huazheng.tunny.ocean.api.dto.bpm.BpmAuditorDTO;
import com.huazheng.tunny.ocean.api.dto.bpm.BpmNodeDTO;
import com.huazheng.tunny.ocean.api.dto.bpm.BpmViewDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.feign.RemoteAdminService;
import com.huazheng.tunny.ocean.api.feign.RemoteBpmService;
import com.huazheng.tunny.ocean.api.vo.BpmVo;
import com.huazheng.tunny.ocean.api.vo.CategoriesDictVO;
import com.huazheng.tunny.ocean.api.vo.FdBusCostVO;
import com.huazheng.tunny.ocean.api.vo.SysDictVo;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.*;
import com.huazheng.tunny.ocean.util.CellValueUtil;
import com.huazheng.tunny.ocean.util.CheckUtil;
import com.itextpdf.text.pdf.BaseFont;
import gui.ava.html.parser.HtmlParser;
import gui.ava.html.parser.HtmlParserImpl;
import gui.ava.html.renderer.ImageRenderer;
import gui.ava.html.renderer.ImageRendererImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.commons.io.IOUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("fdBusCostService")
public class FdBusCostServiceImpl extends ServiceImpl<FdBusCostMapper, FdBusCost> implements FdBusCostService {

    @Autowired
    private FdBusCostMapper fdBusCostMapper;
    @Autowired
    private FdBusCostDetailMapper fdBusCostDetailMapper;
    @Autowired
    private FdBusCostDetailHisMapper fdBusCostDetailHisMapper;
    @Autowired
    private RemoteAdminService remoteAdminService;
    @Autowired
    private CustomerInfoMapper customerInfoMapper;
    @Autowired
    private WaybillContainerInfoMapper waybillContainerInfoMapper;
    @Autowired
    private WaybillHeaderMapper waybillHeaderMapper;
    @Autowired
    private WaybillHeaderService waybillHeaderService;
    @Autowired
    private FdBusCostWaybillMapper fdBusCostWaybillMapper;
    @Autowired
    private ShifmanagementMapper shifmanagementMapper;
    @Autowired
    private LineManagementMapper lineManagementMapper;
    @Autowired
    private RemoteBpmService remoteBpmService;
    @Autowired
    private SysNoConfigService sysNoConfigService;
    @Autowired
    private BillPayCustomerMapper billPayCustomerMapper;
    @Autowired
    private BillPayCustomerSubMapper billPayCustomerSubMapper;
    @Autowired
    private BillDealWithCityMapper billDealWithCityMapper;
    @Autowired
    private BillSubPayCityMapper billSubPayCityMapper;
    @Autowired
    private FdShippingAccountMapper fdShippingAccountMapper;
    @Autowired
    private SpaceOccupyMapper spaceOccupyMapper;
    @Autowired
    private BookingRequesdetailMapper bookingRequesdetailMapper;
    @Autowired
    private BookingRequesheaderMapper bookingRequesheaderMapper;
    @Autowired
    private WaybillParticipantsMapper waybillParticipantsMapper;
    @Autowired
    private WaybillGoodsInfoMapper waybillGoodsInfoMapper;
    @Autowired
    private ContainerTypeDataMapper containerTypeDataMapper;
    @Autowired
    private StationManagementMapper stationManagementMapper;
    @Autowired
    private BasChangeboxRetreatMapper basChangeboxRetreatMapper;
    @Autowired
    private SupplierInfoMapper supplierInfoMapper;
    @Autowired
    private MessageCenterService messageCenterService;
    @Autowired
    private UploadRecordMapper uploadRecordMapper;
    @Autowired
    private SubsidyManagerMapper subsidyManagerMapper;
    @Autowired
    private BasChangeboxRetreatService basChangeboxRetreatService;
    @Autowired
    private BillPayProvinceSubMapper billPayProvinceSubMapper;
    @Autowired
    private FdShippingAccoundetailMapper fdShippingAccoundetailMapper;
    @Autowired
    private FdTradingDetailsMapper fdTradingDetailsMapper;
    @Autowired
    private FdBalanceDetailMapper fdBalanceDetailMapper;
    @Autowired
    private CustomerPlatformInfoMapper customerPlatformInfoMapper;
    @Autowired
    private FdPostTransportService fdPostTransportService;

    @Autowired
    private SysDictMapper sysDictMapper;
    @Value("${db.database}")
    private String database;

    @Value("${tunny.bpm.busCostKey}")
    private String busCostKey;

    @Value("${fonts.simSun}")
    private String fontsSimSun;

    @Value("${path.windowsPath}")
    private String windowsPath;
    @Value("${path.linuxPath}")
    private String linuxPath;

    /**
     * 查询业务流程单主表信息
     *
     * @param id 业务流程单主表ID
     * @return 业务流程单主表信息
     */
    @Override
    public FdBusCostInfoDTO selectFdBusCostById(Integer id) {
        FdBusCostInfoDTO fdBusCost = fdBusCostMapper.selectFdBusCostInfoById(id);
        if (fdBusCost != null && StrUtil.isNotEmpty(fdBusCost.getCostCode())) {
            List<WaybillHeaderInfoDTO> waybills = waybillHeaderMapper.selectInfoWithBusCost(fdBusCost.getCostCode());
            if (CollUtil.isNotEmpty(waybills)) {
                for (WaybillHeaderInfoDTO waybillHeaderInfoDTO : waybills) {
                    if (StrUtil.isNotBlank(waybillHeaderInfoDTO.getWaybillNo())) {
                        String identification = waybillContainerInfoMapper.selectIdentificationByWaybillNo(waybillHeaderInfoDTO.getWaybillNo());
                        waybillHeaderInfoDTO.setIdentification(identification);
                    }

                }
                fdBusCost.setWaybills(waybills);
            }
            UploadRecord sel = new UploadRecord();
            sel.setBillNo(fdBusCost.getCostCode());
            sel.setDeleteFlag("N");
            List<UploadRecord> uploadRecords = uploadRecordMapper.selectUploadRecordList(sel);
            if (CollUtil.isNotEmpty(uploadRecords)) {
                fdBusCost.setFiles(uploadRecords);
            }
        }
        return fdBusCost;
    }

    /**
     * 查询业务流程单主表列表
     *
     * @param fdBusCost 业务流程单主表信息
     * @return 业务流程单主表集合
     */
    @Override
    public List<FdBusCost> selectFdBusCostList(FdBusCost fdBusCost) {
        return fdBusCostMapper.selectFdBusCostList(fdBusCost);
    }


    /**
     * 分页模糊查询业务流程单主表列表
     *
     * @return 业务流程单主表集合
     */
    @Override
    public Page selectFdBusCostListByLike(Query query) {
        FdBusCost fdBusCost = BeanUtil.mapToBean(query.getCondition(), FdBusCost.class, false);
        query.setRecords(fdBusCostMapper.selectFdBusCostListByLike(query, fdBusCost));
        return query;
    }

    @Override
    public List<FdBusCost> selectFdBusCostListsByLike(FdBusCost fdBusCost) {
        return fdBusCostMapper.selectFdBusCostListByLike(fdBusCost);
    }

    /**
     * 分页模糊查询业务流程单主表列表
     *
     * @return 业务流程单主表集合
     */
    @Override
    public Page selectFdBusCostPage(Query query) {
        if (StrUtil.isBlank(query.getOrderByField())) {
            query.setOrderByField("FIND_IN_SET(x.audit_status, '0,3,1,2'), x.add_time");
            query.setAsc(Boolean.FALSE);
        }
        FdBusCostVO fdBusCostVO = BeanUtil.mapToBean(query.getCondition(), FdBusCostVO.class, false);
        fdBusCostVO.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        query.setRecords(fdBusCostMapper.selectFdBusCostPage(query, fdBusCostVO));
        return query;
    }


    /**
     * 新增业务流程单主表
     *
     * @param fdBusCost 业务流程单主表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertFdBusCost(FdBusCost fdBusCost) {
        return fdBusCostMapper.insertFdBusCost(fdBusCost);
    }

    /**
     * 修改业务流程单主表
     *
     * @param fdBusCost 业务流程单主表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateFdBusCost(FdBusCost fdBusCost) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        fdBusCost.setUpdateWho(userInfo.getUserName());
        fdBusCost.setUpdateWhoName(userInfo.getRealName());
        fdBusCost.setUpdateTime(LocalDateTime.now());

        if (CollUtil.isNotEmpty(fdBusCost.getFiles())) {
            List<UploadRecord> files = fdBusCost.getFiles();
            for (UploadRecord uploadRecord : files) {
                if (StrUtil.isBlank(uploadRecord.getRowId())) {
                    uploadRecord.setResveredField01(uploadRecord.getGroupName());
                    uploadRecord.setResveredField02(uploadRecord.getDelUrl());
                    uploadRecord.setBillNo(fdBusCost.getCostCode());
                    uploadRecord.setAddTime(LocalDateTime.now());
                    uploadRecord.setAddWho(userInfo.getUserName());
                    uploadRecord.setAddWhoName(userInfo.getRealName());
                    uploadRecordMapper.insertUploadRecord(uploadRecord);
                }
            }
        }
        /*List<FdBusCostDetail> detailList = fdBusCost.getDetailList();
        if (CollUtil.isNotEmpty(detailList)) {
            fdBusCostDetailMapper.deleteFdBusCostDetailByCode(fdBusCost.getCostCode());
            //新增费用明细
            for (FdBusCostDetail detail : detailList
            ) {
                detail.setCostCode(fdBusCost.getCostCode());
                detail.setAddWho(userInfo.getUserName());
                detail.setAddWhoName(userInfo.getRealName());
                detail.setAddTime(LocalDateTime.now());
                fdBusCostDetailMapper.insertFdBusCostDetail(detail);
            }
        }*/
        return fdBusCostMapper.updateFdBusCost(fdBusCost);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveFiles(FdBusCost fdBusCost) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        FdBusCost cost = fdBusCostMapper.selectFdBusCostById(fdBusCost.getId());
        if (cost != null) {
            if (CollUtil.isNotEmpty(fdBusCost.getFiles())) {
                List<UploadRecord> files = fdBusCost.getFiles();
                for (UploadRecord uploadRecord : files) {
                    if (StrUtil.isBlank(uploadRecord.getRowId())) {
                        uploadRecord.setResveredField01(uploadRecord.getGroupName());
                        uploadRecord.setResveredField02(uploadRecord.getDelUrl());
                        uploadRecord.setBillNo(cost.getCostCode());
                        uploadRecord.setAddTime(LocalDateTime.now());
                        uploadRecord.setAddWho(userInfo.getUserName());
                        uploadRecord.setAddWhoName(userInfo.getRealName());
                        uploadRecordMapper.insertUploadRecord(uploadRecord);
                    }
                }
            }
        } else {
            return new R<>(new Throwable("业务流程单不存在"));
        }
        return new R<>(0, Boolean.TRUE, null, "保存成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R commit(FdBusCost fdBusCost) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        FdBusCost fdBusCost1 = fdBusCostMapper.selectFdBusCostById(fdBusCost.getId());

        BasChangeboxRetreat sel = new BasChangeboxRetreat();
        sel.setShiftNo(fdBusCost1.getShiftNo());
        sel.setPlatformCode(userInfo.getPlatformCode());
        sel.setStatus("1");
        List<BasChangeboxRetreat> basChangeboxRetreats = basChangeboxRetreatMapper.selectBasChangeboxRetreatList(sel);
        if (CollUtil.isNotEmpty(basChangeboxRetreats)) {
            return new R<>(new Throwable("该班次存在撤换箱申请，请先完成撤换箱审批"));
        }
        Shifmanagement sel2 = new Shifmanagement();
        sel2.setShiftId(fdBusCost1.getShiftNo());
        sel2.setPlatformCode(fdBusCost1.getPlatformCode());
        sel2.setDeleteFlag("N");
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel2);
        String content = "";
        if (CollUtil.isNotEmpty(shifmanagements)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            content = shifmanagements.get(0).getShiftName() + "（" + sdf.format(shifmanagements.get(0).getPlanShipTime()) + "），" + fdBusCost1.getCustomerName() + "业务流程单审批。";
        }

        if (CollUtil.isNotEmpty(fdBusCost.getFiles())) {
            List<UploadRecord> files = fdBusCost.getFiles();
            for (UploadRecord uploadRecord : files) {
                if (StrUtil.isBlank(uploadRecord.getRowId())) {
                    uploadRecord.setResveredField01(uploadRecord.getGroupName());
                    uploadRecord.setResveredField02(uploadRecord.getDelUrl());
                    uploadRecord.setBillNo(fdBusCost.getCostCode());
                    uploadRecord.setAddTime(LocalDateTime.now());
                    uploadRecord.setAddWho(userInfo.getUserName());
                    uploadRecord.setAddWhoName(userInfo.getRealName());
                    uploadRecordMapper.insertUploadRecord(uploadRecord);
                }
            }
        }

        fdBusCost.setAuditStatus("1");
        fdBusCost.setUpdateWho(userInfo.getUserName());
        fdBusCost.setUpdateWhoName(userInfo.getRealName());
        fdBusCost.setUpdateTime(LocalDateTime.now());
        if (StrUtil.isNotEmpty(fdBusCost.getProcId())) {
            //调用审批
            Map<String, String> map = new HashMap<>();
            map.put("procId", fdBusCost.getProcId());
            map.put("opinion", fdBusCost.getOpinion());
            map.put("taskId", fdBusCost.getTaskId());
            map.put("empNo", userInfo.getUserName());
            map.put("empName", userInfo.getRealName());
            Map<String, Object> agree = remoteBpmService.agree(map);
            String status = agree.get("STATUS").toString();
            if ("END".equals(status)) {
                //审批完成
                auditComplete(fdBusCost1);
                //生成市平台应收账单
                insertReceiveBill(fdBusCost1);
                //生成市平台应付账单
                insertPayBill(fdBusCost1);
                //更新上级市平台应收
                addParentFdCostBus(fdBusCost1);
            } else if ("SUCCESS".equals(status)) {
                String procId = agree.get("PROCID").toString();
                String taskId = agree.get("TASKID").toString();
                String nodeusers = agree.get("NODEUSERS").toString();
                fdBusCost.setProcId(procId);
                fdBusCost.setTaskId(taskId);
                fdBusCost.setNextAuditCode(nodeusers);
                messageCenterService.sendMiniMsgYw(nodeusers, "您有新的业务流程单待审批，请及时处理。", content, fdBusCost1.getId(), fdBusCost1.getOperationsSupervisor());
            } else if ("ERROR".equals(status)) {
                throw new RuntimeException("流程审批失败");
            }
        } else {
            fdBusCost.setOperationsSupervisor(userInfo.getRealName());
            if (StrUtil.isBlank(fdBusCost.getNoAudit())) {
                //调用审批
                List<String> departmentManager = remoteAdminService.getUserNameByRoleName("市平台部门经理", SecurityUtils.getUserInfo().getPlatformCode());
                List<String> businessAccounting = remoteAdminService.getUserNameByRoleName("市平台商务会计", SecurityUtils.getUserInfo().getPlatformCode());
                Map<String, Object> map = new HashMap<>();
                map.put("departmentManager", departmentManager);
                map.put("businessAccounting", businessAccounting);
                Map<String, Object> resultMap = remoteBpmService.startProcess(busCostKey, userInfo.getUserName(), userInfo.getRealName(), JSONUtil.toJsonStr(map), null);
                String status = resultMap.get("STATUS").toString();
                if ("SUCCESS".equals(status)) {
                    String procId = resultMap.get("PROCID").toString();
                    String taskId = resultMap.get("TASKID").toString();
                    String nodeusers = resultMap.get("NODEUSERS").toString();
                    fdBusCost.setProcId(procId);
                    fdBusCost.setTaskId(taskId);
                    fdBusCost.setNextAuditCode(nodeusers);
                    messageCenterService.sendMiniMsgYw(nodeusers, "您有新的业务流程单待审批，请及时处理。", content, fdBusCost1.getId(), fdBusCost.getOperationsSupervisor());
                } else if ("ERROR".equals(status)) {
                    throw new RuntimeException("提交审批失败");
                }

            } else {
                auditComplete(fdBusCost1);
                //生成市平台应收账单
                insertReceiveBill(fdBusCost1);
                //生成市平台应付账单
                insertPayBill(fdBusCost1);
                //更新上级市平台应收
                addParentFdCostBus(fdBusCost1);
            }
        }
        fdBusCostMapper.updateFdBusCost(fdBusCost);
        return new R<>(0, Boolean.TRUE, null, "提交审批成功");
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public R addFeesCommit(FdBusCost fdBusCost) {
        //应收
        FdBusCost cost = fdBusCostMapper.selectFdBusCostByCostCode(fdBusCost.getCostCode());
        if (cost != null) {
            FdBusCostDetail sel = new FdBusCostDetail();
            sel.setCostCode(fdBusCost.getCostCode());
            sel.setAuditStatus("0");
            sel.setDeleteFlag("N");
            List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList(sel);
            if (CollUtil.isEmpty(detailList)) {
                return new R<>(new Throwable("不存在新追加费用，无须提交审核！"));
            }
            //插入追加费用应收账单
            insertReceiveBillByAddFees(cost);
            //插入追加费用应付账单
            insertPayBillByAddFees(cost);
        }


        return new R<>(0, Boolean.TRUE, null, "追加费用成功");
    }

    /**
     * 插入追加费用应付账单
     *
     * @Param: fdBusCost
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/27 14:12
     **/
    public void insertPayBillByAddFees(FdBusCost fdBusCost) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        //查询第三方追加费用
        String platformCode = null;
        Shifmanagement sel = new Shifmanagement();
        sel.setShiftId(fdBusCost.getShiftNo());
        sel.setPlatformCode(fdBusCost.getPlatformCode());
        sel.setDeleteFlag("N");
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
        if (CollUtil.isNotEmpty(shifmanagements)) {
            if (StrUtil.isEmpty(shifmanagements.get(0).getParentId())) {
                platformCode = userInfo.getSupPlatformCode();
                if (platformCode.contains("_")) {
                    platformCode = platformCode.split("_")[0];
                }
            } else {
                platformCode = shifmanagements.get(0).getSharePlatformCode();
            }
        }
        FdBusCostDetail sel2 = new FdBusCostDetail();
        sel2.setCostCode(fdBusCost.getCostCode());
        sel2.setReceiveCode(platformCode);
        sel2.setAuditStatus("0");
        //追加费用第三方编码
        List<FdBusCostDetail> receiveCodes = fdBusCostDetailMapper.getReceiveCodeByCostCode(sel2);
        if (CollUtil.isNotEmpty(receiveCodes)) {
            for (FdBusCostDetail fdBusCostDetail : receiveCodes) {
                //查询是否存在第三方账单
                BillSubPayCity sel3 = new BillSubPayCity();
                sel3.setShiftNo(fdBusCost.getShiftNo());
                sel3.setPlatformCode(fdBusCostDetail.getReceiveCode());
                sel3.setCustomerCode(fdBusCost.getPlatformCode());
                sel3.setStatus("1");
                List<BillSubPayCity> billSubPayCities = billSubPayCityMapper.selectBillSubPayCityList(sel3);

                FdBusCostDetail sel4 = new FdBusCostDetail();
                sel4.setCostCode(fdBusCost.getCostCode());
                sel4.setReceiveCode(fdBusCostDetail.getReceiveCode());
                sel4.setCostType("1");
                sel4.setAuditStatus("0");
                List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList(sel4);

                BigDecimal yfAmount = BigDecimal.ZERO;
                Map<String, String> addMap = new HashMap<>();

                if (CollUtil.isNotEmpty(billSubPayCities)) {
                    //存在，新增子账单
                    BillSubPayCity last = billSubPayCities.get(billSubPayCities.size() - 1);
                    List<BillDealWithCity> billDealWithCity = billDealWithCityMapper.selectBillDealWithCityByBillCode(last.getBillCode());
                    String yfBillCode = CheckUtil.getNumber(last.getBillSubCode(), 3);
                    BillSubPayCity addObj = new BillSubPayCity();
                    addObj.setBillCode(last.getBillCode());
                    addObj.setBillSubCode(yfBillCode);
                    addObj.setOrderNo(last.getOrderNo());
                    addObj.setWaybillNo(last.getWaybillNo());
                    addObj.setCostCode(last.getCostCode());
                    addObj.setAccountCode(last.getAccountCode());
                    addObj.setPlatformCode(last.getPlatformCode());
                    addObj.setPlatformName(last.getPlatformName());
                    addObj.setPlatformLevel("0");
                    addObj.setShiftNo(last.getShiftNo());
                    addObj.setShiftName(last.getShiftName());
                    addObj.setCustomerCode(last.getCustomerCode());
                    addObj.setCustomerName(last.getCustomerName());
                    addObj.setPortStation(last.getPortStation());
                    addObj.setStatus("1");
                    if (CollUtil.isNotEmpty(billDealWithCity)) {
                        addObj.setBillType(billDealWithCity.get(0).getStage());
                    }
                    addObj.setShipmentTime(last.getShipmentTime());
                    addObj.setAddWho(SecurityUtils.getUserInfo().getUserName());
                    addObj.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                    addObj.setAddTime(LocalDateTime.now());
                    //更新业务流程单明细账单编号
                    yfAmount = getAmount(detailList, yfBillCode, userInfo, yfAmount, addMap);
                    addObj.setBillAmount(yfAmount);
                    addObj.setBoxNum(addMap.size());
                    addObj.setBillingState("0");
                    billSubPayCityMapper.insertBillSubPayCity(addObj);

                    //更新主表
                    billDealWithCityMapper.updateBillAmountByBillCode(addObj.getBillCode());
                } else {
                    //存在，新增主账单+子账单
                    String yfBillCode = sysNoConfigService.genNo("FDBL");
                    BillDealWithCity billDealWithCity = new BillDealWithCity();

                    billDealWithCity.setBillCode(yfBillCode);
                    billDealWithCity.setProvinceShiftNum(fdBusCost.getShiftNo());
                    billDealWithCity.setCustomerCode(fdBusCost.getPlatformCode());
                    billDealWithCity.setSourceCode(fdBusCostDetail.getReceiveCode());
                    billDealWithCity.setCustomerName(fdBusCost.getPlatformName());
                    billDealWithCity.setSourceUnit(fdBusCostDetail.getReceiveName());
                    billDealWithCity.setStage("YF");
                    billDealWithCity.setCreateBy(userInfo.getRealName());
                    billDealWithCity.setCreateTime(new Date());

                    BillSubPayCity billSubPayCity = new BillSubPayCity();
                    billSubPayCity.setBillCode(yfBillCode);
                    billSubPayCity.setBillSubCode(yfBillCode + "-001");
                    FdBusCostWaybill fdBusCostWaybill = fdBusCostWaybillMapper.getNosByCostCode(fdBusCost.getCostCode(), fdBusCost.getCustomerCode());
                    if (fdBusCostWaybill != null) {
                        billSubPayCity.setOrderNo(fdBusCostWaybill.getApplicationNumber());
                        billSubPayCity.setWaybillNo(fdBusCostWaybill.getWaybillNo());
                    }
                    billSubPayCity.setCostCode(fdBusCost.getCostCode());
                    billSubPayCity.setPlatformCode(fdBusCostDetail.getReceiveCode());
                    billSubPayCity.setPlatformName(fdBusCostDetail.getReceiveName());
                    billSubPayCity.setPlatformLevel("0");
                    billSubPayCity.setShiftNo(fdBusCost.getShiftNo());
                    billSubPayCity.setBillingState("0");
                    billSubPayCity.setCustomerCode(fdBusCost.getPlatformCode());
                    billSubPayCity.setCustomerName(fdBusCost.getPlatformName());
                    billSubPayCity.setStatus("1");
                    billSubPayCity.setAddWho(SecurityUtils.getUserInfo().getUserName());
                    billSubPayCity.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                    billSubPayCity.setAddTime(LocalDateTime.now());

                    if (CollUtil.isNotEmpty(shifmanagements)) {
                        billDealWithCity.setProvinceShiftName(shifmanagements.get(0).getShiftName());
                        billDealWithCity.setPostDate(shifmanagements.get(0).getPlanShipTime());
                        billDealWithCity.setShippingLines(shifmanagements.get(0).getShippingLine());
                        billDealWithCity.setDirection(shifmanagements.get(0).getTrip());

                        billSubPayCity.setShiftName(shifmanagements.get(0).getShiftName());
                        billSubPayCity.setPortStation(shifmanagements.get(0).getPortStation());
                        billSubPayCity.setShipmentTime(LocalDateTime.ofInstant(shifmanagements.get(0).getPlanShipTime().toInstant(), ZoneId.systemDefault()));
                    }

                    yfAmount = getAmount(detailList, yfBillCode + "-001", userInfo, yfAmount, addMap);
                    billDealWithCity.setBoxCapacity(BigDecimal.valueOf(addMap.size()));
                    billDealWithCity.setBillAmount(yfAmount);
                    billDealWithCityMapper.insertBillDealWithCity(billDealWithCity);

                    billSubPayCity.setBillAmount(yfAmount);
                    billSubPayCity.setBoxNum(addMap.size());
                    billSubPayCityMapper.insertBillSubPayCity(billSubPayCity);
                }
            }
        }

    }

    /**
     * 计算应付金额和箱量
     *
     * @Param: detailList, yfBillCode, userInfo, yfAmount, addMap
     * @Return: java.math.BigDecimal
     * @Author: zhaohr
     * @Date: 2024/06/27 15:45
     **/
    private BigDecimal getAmount(List<FdBusCostDetail> detailList, String yfBillCode, SecruityUser userInfo, BigDecimal yfAmount, Map<String, String> addMap) {

        //更新业务流程单明细账单编号
        if (CollUtil.isNotEmpty(detailList)) {
            for (FdBusCostDetail detail : detailList) {
                detail.setAuditStatus("1");
                detail.setBillSubCode(yfBillCode);
                detail.setUpdateWho(userInfo.getUserName());
                detail.setUpdateWhoName(userInfo.getRealName());
                detail.setUpdateTime(LocalDateTime.now());
                detail.setAuditTime(LocalDateTime.now());
                fdBusCostDetailMapper.updateFdBusCostDetail(detail);

                yfAmount = yfAmount.add(detail.getLocalAmount());
                addMap.put(detail.getContainerNumber(), detail.getContainerNumber());
            }
        }
        return yfAmount;
    }

    /**
     * 插入追加费用应收账单
     *
     * @Param: fdBusCost
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/27 14:12
     **/
    public void insertReceiveBillByAddFees(FdBusCost fdBusCost) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        List<FdBusCostWaybill> miniCustomerCode = fdBusCostWaybillMapper.getMiniCustomerCode(fdBusCost.getCostCode());
        if (CollUtil.isNotEmpty(miniCustomerCode)) {
            for (FdBusCostWaybill customer : miniCustomerCode
            ) {
                List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailListThree(fdBusCost.getCostCode(), customer.getAddWhoName());
                if (CollUtil.isNotEmpty(detailList)) {
                    String ysBillCode = "";
                    BillPayCustomerSub sel2 = new BillPayCustomerSub();
                    sel2.setShiftNo(fdBusCost.getShiftNo());
                    sel2.setPlatformCode(fdBusCost.getPlatformCode());
                    sel2.setCustomerCode(fdBusCost.getCustomerCode());
                    sel2.setStatus("1");
                    sel2.setDeleteFlag("N");
                    sel2.setMiniPlatformName(customer.getAddWhoName());
                    List<BillPayCustomerSub> billPayCustomerSubs = billPayCustomerSubMapper.selectBillSubIncomeCityList2(sel2);
                    if (CollUtil.isNotEmpty(billPayCustomerSubs)) {
                        BillPayCustomerSub last = billPayCustomerSubs.get(billPayCustomerSubs.size() - 1);
                        ysBillCode = CheckUtil.getNumber(last.getBillSubCode(), 3);
                        BigDecimal ysAmount = BigDecimal.ZERO;
                        BillPayCustomerSub addObj = new BillPayCustomerSub();
                        addObj.setBillCode(last.getBillCode());
                        addObj.setBillSubCode(ysBillCode);
                        addObj.setOrderNo(last.getOrderNo());
                        addObj.setWaybillNo(last.getWaybillNo());
                        addObj.setCostCode(fdBusCost.getCostCode());
                        addObj.setPlatformCode(last.getPlatformCode());
                        addObj.setPlatformName(last.getPlatformName());
                        addObj.setPlatformLevel("0");
                        addObj.setShiftNo(last.getShiftNo());
                        addObj.setShiftName(last.getShiftName());
                        addObj.setBillingState("0");
                        addObj.setCustomerCode(last.getCustomerCode());
                        addObj.setCustomerName(last.getCustomerName());
                        addObj.setPortStation(last.getPortStation());
                        addObj.setStatus("1");
                        addObj.setShipmentTime(last.getShipmentTime());
                        addObj.setMiniPlatformCode(customer.getAddWho());
                        addObj.setMiniPlatformName(customer.getAddWhoName());
                        List<BillPayCustomer> billPayCustomers = billPayCustomerMapper.selectBillPayCustomerByBillCode(last.getBillCode());
                        if (CollUtil.isNotEmpty(billPayCustomers)) {
                            addObj.setBillType(billPayCustomers.get(0).getStage());
                        }
                        addObj.setAddWho(userInfo.getUserName());
                        addObj.setAddWhoName(userInfo.getRealName());
                        addObj.setAddTime(new Date());
                        addObj.setAddWho(userInfo.getUserName());
                        addObj.setAddWhoName(userInfo.getRealName());
                        addObj.setAddTime(new Date());

                        FdBusCostDetail sel4 = new FdBusCostDetail();
                        sel4.setShiftNo(fdBusCost.getShiftNo());
                        sel4.setPayCode(fdBusCost.getCustomerCode());

                        for (FdBusCostDetail detail : detailList) {
                            detail.setBillSubCode(ysBillCode);
                            detail.setAuditStatus("1");
                            detail.setUpdateWho(userInfo.getUserName());
                            detail.setUpdateWhoName(userInfo.getRealName());
                            detail.setUpdateTime(LocalDateTime.now());
                            detail.setAuditTime(LocalDateTime.now());
                            fdBusCostDetailMapper.updateFdBusCostDetail(detail);
                            ysAmount = ysAmount.add(detail.getLocalAmount());

                            //插入下级平台业务流程单(未审批状态，只做展示用）
                            sel4.setContainerNumber(detail.getContainerNumber());
                            List<String> codes = fdBusCostDetailMapper.getLowerCostCode(sel4);
                            if (CollUtil.isNotEmpty(codes)) {
                                detail.setId(null);
                                detail.setBillSubCode(null);
                                detail.setCostCode(codes.get(0));
                                detail.setCostType("1");
                                detail.setAuditStatus("1");
                                detail.setDeleteFlag("N");
                                detail.setAuditTime(LocalDateTime.now());
                                detail.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                detail.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                detail.setAddTime(LocalDateTime.now());
                                fdBusCostDetailMapper.insertFdBusCostDetail(detail);

                                //汇率变更
                                if (StrUtil.isNotBlank(detail.getRemark()) && detail.getRemark().contains("原汇率")) {
                                    FdBusCostDetail updObj2 = new FdBusCostDetail();
                                    updObj2.setCostCode(codes.get(0));
                                    updObj2.setCostType("1");
                                    updObj2.setContainerNumber(detail.getContainerNumber());
                                    updObj2.setCodeSsCategoriesCode(detail.getCodeBbCategoriesCode());
                                    updObj2.setCodeBbCategoriesCode(detail.getCodeBbCategoriesCode());
                                    updObj2.setExchangeRateNew(detail.getExchangeRate());
                                    updObj2.setUpdateWho(userInfo.getUserName());
                                    updObj2.setUpdateWhoName(userInfo.getRealName());
                                    updObj2.setUpdateTime(LocalDateTime.now());
                                    fdBusCostDetailMapper.updateFdBusCostDetailByCostCode(updObj2);
                                }
                            }
                        }
                        addObj.setBillingState("0");

                        addObj.setBillAmount(ysAmount);


                        if (addObj.getBillAmount().compareTo(BigDecimal.ZERO) < 0 && ("VERIFIED".equals(billPayCustomerSubs.get(0).getBillingState()) || "VERIFICATION".equals(billPayCustomerSubs.get(0).getBillingState()))) {
                            addObj.setBillingState("VERIFIED");
                            FdBalanceDetail detail = new FdBalanceDetail();
                            detail.setPlatformCode(addObj.getPlatformCode());
                            detail.setCustomerCode(addObj.getCustomerCode());
                            detail.setCustomerName(addObj.getCustomerName());
                            detail.setShiftNo(addObj.getShiftNo());
                            detail.setCodeBbCategoriesCode("f_fee_type");
                            detail.setCodeBbCategoriesName("发运运费");
                            detail.setCodeSsCategoriesCode("f_clearing_balance");
                            detail.setCodeSsCategoriesName("结算余额");
                            detail.setPaymentType("0");
                            detail.setTotalAmount(addObj.getBillAmount().negate());
                            detail.setRemainingAmount(addObj.getBillAmount().negate());
                            detail.setPlatformLevel("0");
                            detail.setBillCode(addObj.getBillSubCode());
                            detail.setRemarks("");
                            detail.setAddWho(userInfo.getUserName());
                            detail.setAddWhoName(userInfo.getRealName());
                            detail.setAddTime(LocalDateTime.now());
                            fdBalanceDetailMapper.insertFdBalanceDetail(detail);

                            FdTradingDetails fdTradingDetails = new FdTradingDetails();
                            String tsIn = sysNoConfigService.genNo("TS");
                            fdTradingDetails.setUuid(UUID.randomUUID().toString());
                            fdTradingDetails.setTradeSerialNumber(tsIn);
                            fdTradingDetails.setPlatformCode(detail.getPlatformCode());
                            fdTradingDetails.setPlatformName(detail.getPlatformName());
                            fdTradingDetails.setCustomerName(detail.getCustomerName());
                            fdTradingDetails.setCustomerCode(detail.getCustomerCode());
                            fdTradingDetails.setTradingHours(LocalDateTime.now());
                            fdTradingDetails.setPaymentType("0");
                            fdTradingDetails.setTransactionAmount(addObj.getBillAmount());
                            fdTradingDetails.setFromBillCode(addObj.getBillSubCode());
                            fdTradingDetails.setTradingStatus("1");
                            fdTradingDetails.setPlatformLevel("0");
                            fdTradingDetailsMapper.insertFdTradingDetails(fdTradingDetails);
                        }

                        billPayCustomerSubMapper.insertBillSubIncomeCity(addObj);
                        //更新主子账单金额和箱量
                        billPayCustomerSubMapper.updateBoxNumByBillSubCode(addObj.getBillSubCode());
                        billPayCustomerMapper.updateBillAmountByBillCode(addObj.getBillCode());
                    } else {
                        //追加费用对应用户没有账单，则追加对应账单
                        BillPayCustomer addObj = new BillPayCustomer();
                        BillPayCustomerSub billPayCustomerSub = new BillPayCustomerSub();
                        addObj.setBillCode(sysNoConfigService.genNo("FDBL"));
                        addObj.setCustomerCode(fdBusCost.getCustomerCode());
                        addObj.setCustomerName(fdBusCost.getCustomerName());
                        addObj.setProvinceShiftNum(fdBusCost.getShiftNo());

                        Shifmanagement sel = new Shifmanagement();
                        sel.setShiftId(fdBusCost.getShiftNo());
                        sel.setPlatformCode(fdBusCost.getPlatformCode());
                        sel.setDeleteFlag("N");
                        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
                        if (CollUtil.isNotEmpty(shifmanagements)) {
                            addObj.setProvinceTrainName(shifmanagements.get(0).getShiftName());
                            addObj.setPostDate(shifmanagements.get(0).getPlanShipTime());
                            addObj.setShippingLines(shifmanagements.get(0).getShippingLine());
                            addObj.setDirection(shifmanagements.get(0).getTrip());
                            billPayCustomerSub.setPortStation(shifmanagements.get(0).getPortStation());
                            billPayCustomerSub.setShipmentTime(shifmanagements.get(0).getPlanShipTime());
                            billPayCustomerSub.setShiftName(shifmanagements.get(0).getShiftName());
                        }

                        FdBusCostDetail sel3 = new FdBusCostDetail();
                        sel3.setCostCode(fdBusCost.getCostCode());
                        sel3.setCostType("0");
                        sel3.setPayCode(fdBusCost.getCustomerCode());
                        sel3.setMiniPlatformName(customer.getAddWhoName());

                        FdBusCostWaybill fdBusCostWaybill = fdBusCostWaybillMapper.getNosByCostCode2(fdBusCost.getCostCode(), fdBusCost.getCustomerCode(), customer.getAddWhoName());
                        if (fdBusCostWaybill != null) {
                            addObj.setOrderCode(fdBusCostWaybill.getWaybillNo());
                            billPayCustomerSub.setOrderNo(fdBusCostWaybill.getApplicationNumber());
                            billPayCustomerSub.setWaybillNo(fdBusCostWaybill.getWaybillNo());
                        }

                        addObj.setStage("YF");
                        addObj.setSourceCode(fdBusCost.getPlatformCode());
                        addObj.setSourceUnit(fdBusCost.getPlatformName());
                        addObj.setCreateBy(SecurityUtils.getUserInfo().getRealName());
                        addObj.setCreateTime(new Date());
                        addObj.setMiniPlatformCode(customer.getAddWho());
                        addObj.setMiniPlatformName(customer.getAddWhoName());

                        billPayCustomerSub.setBillCode(addObj.getBillCode());
                        billPayCustomerSub.setBillSubCode(addObj.getBillCode() + "-001");
                        billPayCustomerSub.setCostCode(fdBusCost.getCostCode());
                        billPayCustomerSub.setPlatformCode(fdBusCost.getPlatformCode());
                        billPayCustomerSub.setPlatformName(fdBusCost.getPlatformName());
                        billPayCustomerSub.setPlatformLevel("0");
                        billPayCustomerSub.setShiftNo(fdBusCost.getShiftNo());
                        billPayCustomerSub.setBillingState("0");
                        billPayCustomerSub.setCustomerCode(fdBusCost.getCustomerCode());
                        billPayCustomerSub.setCustomerName(fdBusCost.getCustomerName());
                        billPayCustomerSub.setStatus("1");
                        billPayCustomerSub.setAddWho(SecurityUtils.getUserInfo().getUserName());
                        billPayCustomerSub.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                        billPayCustomerSub.setAddTime(new Date());
                        billPayCustomerSub.setMiniPlatformCode(customer.getAddWho());
                        billPayCustomerSub.setMiniPlatformName(customer.getAddWhoName());

                        FdBusCostDetail sel4 = new FdBusCostDetail();
                        sel4.setShiftNo(fdBusCost.getShiftNo());
                        sel4.setPayCode(fdBusCost.getCustomerCode());
                        for (FdBusCostDetail detail : detailList) {
                            detail.setBillSubCode(billPayCustomerSub.getBillSubCode());
                            detail.setAuditStatus("1");
                            detail.setUpdateWho(userInfo.getUserName());
                            detail.setUpdateWhoName(userInfo.getRealName());
                            detail.setUpdateTime(LocalDateTime.now());
                            detail.setAuditTime(LocalDateTime.now());
                            fdBusCostDetailMapper.updateFdBusCostDetail(detail);

                            //插入下级平台业务流程单(未审批状态，只做展示用）
                            sel4.setContainerNumber(detail.getContainerNumber());
                            List<String> codes = fdBusCostDetailMapper.getLowerCostCode(sel4);
                            if (CollUtil.isNotEmpty(codes)) {
                                detail.setId(null);
                                detail.setBillSubCode(null);
                                detail.setCostCode(codes.get(0));
                                detail.setCostType("1");
                                detail.setAuditStatus("1");
                                detail.setDeleteFlag("N");
                                detail.setAuditTime(LocalDateTime.now());
                                detail.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                detail.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                detail.setAddTime(LocalDateTime.now());
                                fdBusCostDetailMapper.insertFdBusCostDetail(detail);
                            }
                        }
                        billPayCustomerSubMapper.insertBillSubIncomeCity(billPayCustomerSub);
                        billPayCustomerMapper.insertBillIncomeCity(addObj);
                        //更新主子账单金额和箱量
                        billPayCustomerSubMapper.updateBoxNumByBillSubCode(billPayCustomerSub.getBillSubCode());
                        billPayCustomerMapper.updateBillAmountByBillCode(addObj.getBillCode());
                    }
                }
            }
        }
    }

    /**
     * 生成市平台应付账单
     *
     * @Param: fdBusCost
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/15 11:16
     **/
    public void insertPayBill(FdBusCost fdBusCost) {
        /*String platformCode = null;
        Shifmanagement sel = new Shifmanagement();
        sel.setShiftId(fdBusCost.getShiftNo());
        sel.setPlatformCode(fdBusCost.getPlatformCode());
        sel.setDeleteFlag("N");
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
        if (CollUtil.isNotEmpty(shifmanagements)) {
            if (StrUtil.isEmpty(shifmanagements.get(0).getParentId())) {
                platformCode = SecurityUtils.getUserInfo().getSupPlatformCode();
                if (platformCode.contains("_")) {
                    platformCode = platformCode.split("_")[0];
                }
            } else {
                platformCode = shifmanagements.get(0).getSharePlatformCode();
            }
        }
        FdBusCostDetail sel2 = new FdBusCostDetail();
        sel2.setCostCode(fdBusCost.getCostCode());
        sel2.setReceiveCode(platformCode);
        sel2.setAuditStatus("1");
        List<FdBusCostDetail> receiveCodes = fdBusCostDetailMapper.getReceiveCodeByCostCode(sel2);
        if (CollUtil.isNotEmpty(receiveCodes)) {
            for (FdBusCostDetail fdBusCostDetail : receiveCodes
            ) {
                BillDealWithCity billDealWithCity = new BillDealWithCity();
                BillSubPayCity billSubPayCity = new BillSubPayCity();

                billDealWithCity.setBillCode(sysNoConfigService.genNo("FDBL"));
                billDealWithCity.setProvinceShiftNum(fdBusCost.getShiftNo());
                billDealWithCity.setCustomerName(fdBusCost.getPlatformName());
                billDealWithCity.setSourceUnit(fdBusCostDetail.getReceiveName());

                FdBusCostDetail sel3 = new FdBusCostDetail();
                sel3.setCostCode(fdBusCost.getCostCode());
                sel3.setCostType("1");
                sel3.setReceiveCode(fdBusCostDetail.getReceiveCode());
                int num = fdBusCostDetailMapper.getContainerNumByCostCode(sel3);
                BigDecimal amount = fdBusCostDetailMapper.getLocalAmountByCostCode(sel3);

                billDealWithCity.setBoxCapacity(BigDecimal.valueOf(num));
                billDealWithCity.setBillAmount(amount);

                if (CollUtil.isNotEmpty(shifmanagements)) {
                    billDealWithCity.setProvinceShiftName(shifmanagements.get(0).getShiftName());
                    billDealWithCity.setPostDate(shifmanagements.get(0).getPlanShipTime());
                    billDealWithCity.setShippingLines(shifmanagements.get(0).getShippingLine());
                    billDealWithCity.setDirection(shifmanagements.get(0).getTrip());

                    billSubPayCity.setShiftName(shifmanagements.get(0).getShiftName());
                    billSubPayCity.setPortStation(shifmanagements.get(0).getPortStation());
                    billSubPayCity.setShipmentTime(LocalDateTime.ofInstant(shifmanagements.get(0).getPlanShipTime().toInstant(), ZoneId.systemDefault()));
                }
                billDealWithCity.setStage("YF");
                billDealWithCity.setCreateBy(SecurityUtils.getUserInfo().getRealName());
                billDealWithCity.setCreateTime(new Date());
                billDealWithCityMapper.insertBillDealWithCity(billDealWithCity);

                billSubPayCity.setBillCode(billDealWithCity.getBillCode());
                billSubPayCity.setBillSubCode(billDealWithCity.getBillCode() + "-001");
                FdBusCostWaybill fdBusCostWaybill = fdBusCostWaybillMapper.getNosByCostCode(fdBusCost.getCostCode(), fdBusCost.getCustomerCode());
                if (fdBusCostWaybill != null) {
                    billSubPayCity.setOrderNo(fdBusCostWaybill.getApplicationNumber());
                    billSubPayCity.setWaybillNo(fdBusCostWaybill.getWaybillNo());
                }
                billSubPayCity.setCostCode(fdBusCost.getCostCode());
                billSubPayCity.setPlatformCode(fdBusCostDetail.getReceiveCode());
                billSubPayCity.setPlatformName(fdBusCostDetail.getReceiveName());
                billSubPayCity.setPlatformLevel("0");
                billSubPayCity.setShiftNo(fdBusCost.getShiftNo());
                billSubPayCity.setBillAmount(amount);
                billSubPayCity.setBillingState("0");
                billSubPayCity.setCustomerCode(fdBusCost.getPlatformCode());
                billSubPayCity.setCustomerName(fdBusCost.getPlatformName());
                billSubPayCity.setStatus("1");
                billSubPayCity.setBoxNum(num);
                billSubPayCity.setAddWho(SecurityUtils.getUserInfo().getUserName());
                billSubPayCity.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                billSubPayCity.setAddTime(LocalDateTime.now());
                billSubPayCityMapper.insertBillSubPayCity(billSubPayCity);

                sel3.setBillSubCode(billSubPayCity.getBillSubCode());
                fdBusCostDetailMapper.updateFdBusCostDetailByCostCode(sel3);
            }
        }*/
    }

    /**
     * 生成市平台应收账单
     *
     * @Param: fdBusCost
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/15 11:16
     **/
    public void insertReceiveBill(FdBusCost fdBusCost) {
        BillPayCustomer billPayCustomer = new BillPayCustomer();
        BillPayCustomerSub billPayCustomerSub = new BillPayCustomerSub();

        billPayCustomer.setCustomerCode(fdBusCost.getCustomerCode());
        billPayCustomer.setCustomerName(fdBusCost.getCustomerName());
        billPayCustomer.setProvinceShiftNum(fdBusCost.getShiftNo());
        Shifmanagement sel = new Shifmanagement();
        sel.setShiftId(fdBusCost.getShiftNo());
        sel.setPlatformCode(fdBusCost.getPlatformCode());
        sel.setDeleteFlag("N");
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
        if (CollUtil.isNotEmpty(shifmanagements)) {
            billPayCustomer.setProvinceTrainName(shifmanagements.get(0).getShiftName());
            billPayCustomer.setPostDate(shifmanagements.get(0).getPlanShipTime());
            billPayCustomer.setShippingLines(shifmanagements.get(0).getShippingLine());
            billPayCustomer.setDirection(shifmanagements.get(0).getTrip());
            billPayCustomerSub.setPortStation(shifmanagements.get(0).getPortStation());
            billPayCustomerSub.setShipmentTime(shifmanagements.get(0).getPlanShipTime());
            billPayCustomerSub.setShiftName(shifmanagements.get(0).getShiftName());
        }
        //查询账单所属小客户平台,按小客户生成各自的账单，没有小客户按大客户算
        List<FdBusCostWaybill> miniCustomerCode = fdBusCostWaybillMapper.getMiniCustomerCode(fdBusCost.getCostCode());
        if (CollUtil.isNotEmpty(miniCustomerCode)) {
            for (FdBusCostWaybill customer : miniCustomerCode
            ) {
                BillPayCustomer addObj = new BillPayCustomer();
                BeanUtil.copyProperties(billPayCustomer, addObj);
                addObj.setBillCode(sysNoConfigService.genNo("FDBL"));
                FdBusCostDetail sel2 = new FdBusCostDetail();
                sel2.setCostCode(fdBusCost.getCostCode());
                sel2.setCostType("0");
                sel2.setPayCode(fdBusCost.getCustomerCode());
                sel2.setMiniPlatformName(customer.getAddWhoName());
                int num = fdBusCostDetailMapper.getContainerNumByCostCode2(sel2);
                addObj.setBoxCapacity(BigDecimal.valueOf(num));
                FdBusCostWaybill fdBusCostWaybill = fdBusCostWaybillMapper.getNosByCostCode2(fdBusCost.getCostCode(), fdBusCost.getCustomerCode(), customer.getAddWhoName());
                if (fdBusCostWaybill != null) {
                    addObj.setOrderCode(fdBusCostWaybill.getWaybillNo());
                    billPayCustomerSub.setOrderNo(fdBusCostWaybill.getApplicationNumber());
                    billPayCustomerSub.setWaybillNo(fdBusCostWaybill.getWaybillNo());
                }

                BigDecimal amount = fdBusCostDetailMapper.getLocalAmountByCostCodeThree(sel2);
                addObj.setBillAmount(amount);
                addObj.setStage("YF");
                addObj.setSourceCode(fdBusCost.getPlatformCode());
                addObj.setSourceUnit(fdBusCost.getPlatformName());
                addObj.setCreateBy(SecurityUtils.getUserInfo().getRealName());
                addObj.setCreateTime(new Date());
                addObj.setMiniPlatformCode(customer.getAddWho());
                addObj.setMiniPlatformName(customer.getAddWhoName());
                billPayCustomerMapper.insertBillIncomeCity(addObj);

                billPayCustomerSub.setBillCode(addObj.getBillCode());
                billPayCustomerSub.setBillSubCode(addObj.getBillCode() + "-001");
                billPayCustomerSub.setCostCode(fdBusCost.getCostCode());
                billPayCustomerSub.setPlatformCode(fdBusCost.getPlatformCode());
                billPayCustomerSub.setPlatformName(fdBusCost.getPlatformName());
                billPayCustomerSub.setPlatformLevel("0");
                billPayCustomerSub.setShiftNo(fdBusCost.getShiftNo());
                billPayCustomerSub.setBillAmount(amount);
                billPayCustomerSub.setBillingState("0");
                billPayCustomerSub.setCustomerCode(fdBusCost.getCustomerCode());
                billPayCustomerSub.setCustomerName(fdBusCost.getCustomerName());
                billPayCustomerSub.setStatus("1");
                billPayCustomerSub.setBoxNum(num);
                billPayCustomerSub.setAddWho(SecurityUtils.getUserInfo().getUserName());
                billPayCustomerSub.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                billPayCustomerSub.setAddTime(new Date());
                billPayCustomerSub.setMiniPlatformCode(customer.getAddWho());
                billPayCustomerSub.setMiniPlatformName(customer.getAddWhoName());


                sel2.setBillSubCode(billPayCustomerSub.getBillSubCode());
                int i = fdBusCostDetailMapper.updateFdBusCostDetailByCostCode3(sel2);
                if (i > 0) {
                    billPayCustomerSubMapper.insertBillSubIncomeCity(billPayCustomerSub);
                    billPayCustomerSubMapper.updateBoxNumByBillSubCode(billPayCustomerSub.getBillSubCode());
                }

            }
        }

        //更新下级平台业务流程单
        FdBusCostDetail sel4 = new FdBusCostDetail();
        sel4.setCostCode(fdBusCost.getCostCode());
        sel4.setCostType("0");
        sel4.setDeleteFlag("N");
        List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList(sel4);

        FdBusCost sel3 = new FdBusCost();
        sel3.setPlatformCode(fdBusCost.getCustomerCode());
        sel3.setShiftNo(fdBusCost.getShiftNo());
        sel3.setDeleteFlag("N");
        List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel3);

        if (CollUtil.isNotEmpty(fdBusCosts)) {
            for (FdBusCost cost : fdBusCosts) {
                if (CollUtil.isNotEmpty(detailList)) {
                    for (FdBusCostDetail detail : detailList) {
                        List<WaybillContainerInfo> list = waybillContainerInfoMapper.selectWaybillContainerInfoByShiftNo(cost.getShiftNo(), cost.getPlatformCode(), cost.getCustomerCode(), detail.getContainerNumber());
                        if (CollUtil.isNotEmpty(list)) {
                            FdBusCostDetail updObj = new FdBusCostDetail();
                            updObj.setCostCode(cost.getCostCode());
                            updObj.setCostType("1");
                            updObj.setContainerNumber(detail.getContainerNumber());
                            updObj.setCodeBbCategoriesCode(detail.getCodeBbCategoriesCode());
                            updObj.setCodeSsCategoriesCode(detail.getCodeSsCategoriesCode());
                            updObj.setCurrency(detail.getCurrency());
                            updObj.setLocalAmount(detail.getLocalAmount());
                            updObj.setOriginalAmount(detail.getOriginalAmount());
                            updObj.setExchangeRate(detail.getExchangeRate());
                            updObj.setExchangeRateNew(detail.getExchangeRateNew());
                            updObj.setAuditStatus("1");
                            updObj.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
                            updObj.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
                            updObj.setUpdateTime(LocalDateTime.now());
                            int i = fdBusCostDetailMapper.updateFdBusCostDetailByCostCode(updObj);
                            if (i == 0) {
                                updObj.setReceiveCode(detail.getReceiveCode());
                                updObj.setReceiveName(detail.getReceiveName());
                                updObj.setPayCode(detail.getPayCode());
                                updObj.setPayName(detail.getPayName());
                                updObj.setCodeBbCategoriesName(detail.getCodeBbCategoriesName());
                                updObj.setCodeSsCategoriesName(detail.getCodeSsCategoriesName());
                                updObj.setShiftNo(detail.getShiftNo());
                                updObj.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                updObj.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                updObj.setAddTime(LocalDateTime.now());
                                updObj.setAuditTime(LocalDateTime.now());
                                fdBusCostDetailMapper.insertFdBusCostDetail(updObj);
                            }
                        }
                    }
                }
            }
        }
    }

    public void auditComplete(FdBusCost fdBusCost) {
        fdBusCost.setAuditStatus("2");
        fdBusCost.setNextAuditCode("[]");
        fdBusCostMapper.updateFdBusCost(fdBusCost);
        fdBusCostDetailMapper.updateByAudit(fdBusCost.getCostCode());
        //fd_bus_cost_detail字段修改需要与fd_bus_cost_detail_his保持同步
        fdBusCostDetailHisMapper.insertByFdBusCostDetail(fdBusCost.getCostCode());
    }

    /*
     * 添加上级平台业务流程单信息
     */
    public void addParentFdCostBus(FdBusCost fdBusCost) {
        FdBusCost sel = new FdBusCost();
        sel.setShiftNo(fdBusCost.getShiftNo());
        sel.setCustomerCode(fdBusCost.getPlatformCode());
        sel.setDeleteFlag("N");
        List<FdBusCost> list = fdBusCostMapper.selectFdBusCostList(sel);
        if (CollUtil.isNotEmpty(list)) {
            //上级市平台明细
            FdBusCostDetail sel2 = new FdBusCostDetail();
            sel2.setCostCode(list.get(0).getCostCode());
            sel2.setCostType("0");
            sel2.setDeleteFlag("N");
            List<FdBusCostDetail> parentList = fdBusCostDetailMapper.selectFdBusCostDetailList(sel2);

            //当前业务流程单应付明细
            FdBusCostDetail sel3 = new FdBusCostDetail();
            sel3.setCostCode(fdBusCost.getCostCode());
            sel3.setReceiveCode(list.get(0).getPlatformCode());
            sel3.setPayCode(fdBusCost.getPlatformCode());
            sel3.setCostType("1");
            sel3.setDeleteFlag("N");
            List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList(sel3);

            Boolean isInsert = Boolean.TRUE;
            if (CollUtil.isNotEmpty(parentList) && CollUtil.isNotEmpty(detailList)) {
                for (FdBusCostDetail p : parentList) {
                    for (FdBusCostDetail d : detailList) {
                        if (p.getContainerNumber().equals(d.getContainerNumber())) {
                            isInsert = Boolean.FALSE;
                            break;
                        }
                    }
                    if (!isInsert) {
                        break;
                    }
                }
            }
            //上级市平台对应箱号应收为空才插入
            if (isInsert) {
                for (FdBusCostDetail detail : detailList) {
                    detail.setId(null);
                    detail.setCostCode(list.get(0).getCostCode());
                    detail.setCostType("0");
                    detail.setAuditStatus("0");
                    detail.setAddWho(SecurityUtils.getUserInfo().getUserName());
                    detail.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                    detail.setAddTime(LocalDateTime.now());
                    detail.setUpdateWho(null);
                    detail.setUpdateWhoName(null);
                    detail.setUpdateTime(null);
                    fdBusCostDetailMapper.insertFdBusCostDetail(detail);
                }
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R audit(FdBusCost cost) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        cost.setUpdateWho(userInfo.getUserName());
        cost.setUpdateWhoName(userInfo.getRealName());
        cost.setUpdateTime(LocalDateTime.now());

        FdBusCost fdBusCost = fdBusCostMapper.selectFdBusCostById(cost.getId());

        Shifmanagement sel2 = new Shifmanagement();
        sel2.setShiftId(fdBusCost.getShiftNo());
        sel2.setPlatformCode(fdBusCost.getPlatformCode());
        sel2.setDeleteFlag("N");
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel2);
        String content = "";
        if (CollUtil.isNotEmpty(shifmanagements)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            content = shifmanagements.get(0).getShiftName() + "（" + sdf.format(shifmanagements.get(0).getPlanShipTime()) + "），" + fdBusCost.getCustomerName() + "业务流程单审批。";
        }
        //调用审批
        Map<String, String> map = new HashMap<>();
        map.put("procId", fdBusCost.getProcId());
        if (StrUtil.isNotEmpty(cost.getOpinion())) {
            map.put("opinion", cost.getOpinion());
        } else {
            map.put("opinion", "审批同意");
        }

        map.put("taskId", fdBusCost.getTaskId());
        map.put("empNo", userInfo.getUserName());
        map.put("empName", userInfo.getRealName());
        Map<String, Object> agree = remoteBpmService.agree(map);
        String status = agree.get("STATUS").toString();
        if ("END".equals(status)) {
            cost.setBusinessAccounting(userInfo.getRealName());
            //审批完成
            auditComplete(fdBusCost);
            //生成市平台应收账单
            insertReceiveBill(fdBusCost);
            //生成市平台应付账单
            insertPayBill(fdBusCost);
            //添加上级平台应收
            addParentFdCostBus(fdBusCost);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            content = shifmanagements.get(0).getShiftName() + "（" + sdf.format(shifmanagements.get(0).getPlanShipTime()) + "），" + fdBusCost.getCustomerName() + "业务流程单已审批通过，请知悉。";
            messageCenterService.sendMiniMsgYw(fdBusCost.getCustomerCode(), "您发起的业务流程单已审批通过，请知悉。", content, fdBusCost.getId(), fdBusCost.getOperationsSupervisor());
        } else if ("SUCCESS".equals(status)) {
            cost.setDepartmentManager(userInfo.getRealName());
            String procId = agree.get("PROCID").toString();
            String taskId = agree.get("TASKID").toString();
            String nodeusers = agree.get("NODEUSERS").toString();
            cost.setProcId(procId);
            cost.setTaskId(taskId);
            cost.setNextAuditCode(nodeusers);
            messageCenterService.sendMiniMsgYw(nodeusers, "您有新的业务流程单待审批，请及时处理。", content, fdBusCost.getId(), fdBusCost.getOperationsSupervisor());
        } else if ("ERROR".equals(status)) {
            throw new RuntimeException("流程审批失败");
        }
        fdBusCostMapper.updateFdBusCost(cost);
        return new R<>(0, Boolean.TRUE, null, "流程审批成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R reject(FdBusCost cost) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        cost.setUpdateWho(userInfo.getUserName());
        cost.setUpdateWhoName(userInfo.getRealName());
        cost.setUpdateTime(LocalDateTime.now());
        //调用审批
        FdBusCost fdBusCost = fdBusCostMapper.selectFdBusCostById(cost.getId());

        Shifmanagement sel2 = new Shifmanagement();
        sel2.setShiftId(fdBusCost.getShiftNo());
        sel2.setPlatformCode(fdBusCost.getPlatformCode());
        sel2.setDeleteFlag("N");
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel2);
        String content = "";
        if (CollUtil.isNotEmpty(shifmanagements)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            content = shifmanagements.get(0).getShiftName() + "（" + sdf.format(shifmanagements.get(0).getPlanShipTime()) + "），" + fdBusCost.getCustomerName() + "业务流程单（单号：" + fdBusCost.getCostCode() + "）已被驳回，请及时处理。";
        }

        Map<String, String> map = new HashMap<>();
        map.put("procId", fdBusCost.getProcId());
        map.put("opinion", cost.getOpinion());
        map.put("taskId", fdBusCost.getTaskId());
        map.put("empNo", userInfo.getUserName());
        map.put("empName", userInfo.getRealName());
        map.put("destination", "start");
        map.put("backHandMode", "normal");
        Map<String, Object> reject = remoteBpmService.reject(map);
        String status = reject.get("STATUS").toString();
        if ("SUCCESS".equals(status)) {
            cost.setAuditStatus("3");
            String procId = reject.get("PROCID").toString();
            String taskId = reject.get("TASKID").toString();
            String nodeusers = reject.get("NODEUSERS").toString();
            cost.setProcId(procId);
            cost.setTaskId(taskId);
            cost.setNextAuditCode(nodeusers);
            messageCenterService.sendMiniMsgYw(nodeusers, "您发起的业务流程单已被驳回，请及时处理。", content, fdBusCost.getId(), userInfo.getRealName());
        } else if ("ERROR".equals(status)) {
            throw new RuntimeException("流程审批失败");
        }
        fdBusCostMapper.updateFdBusCost(cost);
        return new R<>(0, Boolean.TRUE, null, "流程审批成功");
    }

    @Override
    public List<BpmVo> getFullNodeInfo(FdBusCost fdBusCost) {
        List<BpmVo> bpmVoList = new ArrayList<>();
        if (StrUtil.isNotEmpty(fdBusCost.getProcId())) {
            String str = remoteBpmService.fullNodeInfo(fdBusCost.getProcId());
            Gson gson = new Gson();
            BpmViewDTO bpmViewDTO = gson.fromJson(str, BpmViewDTO.class);
            List<BpmNodeDTO> nodes = bpmViewDTO.getNodes();
            for (int i = 0; i < nodes.size(); i++) {
                BpmVo vo = new BpmVo();
                vo.setNodeName(nodes.get(i).getNodeName());
                vo.setStatusName(nodes.get(i).getStatusName());
                List<BpmAuditorDTO> auditor = nodes.get(i).getAuditor();
                if (auditor == null || auditor.size() == 0) {
                    continue;
                }
                //如果是第一步
                if ("待审批".equals(nodes.get(i).getStatusName()) && "发起人".equals(nodes.get(i).getNodeName())) {
                    //代办工号
                    vo.setEmpNo(auditor.get(0).getEmpno());
                    //代办人名字
                    vo.setAuditorName(auditor.get(0).getAuditorName());
                    bpmVoList.add(vo);
                    continue;
                } else if ("待审批".equals(nodes.get(i).getStatusName()) && !"发起人".equals(nodes.get(i).getNodeName())) {
                    //代办人名字
                    vo.setAuditorName(auditor.stream().map(BpmAuditorDTO::getAuditorName).collect(Collectors.toList()).stream().collect(Collectors.joining(",")));
                    bpmVoList.add(vo);
                    continue;
                } else {
                    //印模
                    vo.setSignName(auditor.get(0).getSignName());
                    //代办工号
                    vo.setEmpNo(auditor.get(0).getEmpno());
                    //代办人名字
                    vo.setAuditorName(auditor.get(0).getAuditorName());
                    //审批意见
                    vo.setOpinion(nodes.get(i).getOpinion());
                    //审批时间
                    vo.setCompleteTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(nodes.get(i).getCompleteTime())), ZoneId.systemDefault()));
                    if (StrUtil.isEmpty(vo.getAuditorName())) {
                        List<String> names = remoteAdminService.getUserRealNameByRoleName(nodes.get(i).getNodeName(), SecurityUtils.getUserInfo().getPlatformCode());
                        if (CollUtil.isNotEmpty(names)) {
                            String auditorName = "";
                            for (String name : names) {
                                auditorName = auditorName + name + ",";
                            }
                            vo.setAuditorName(auditorName.substring(0, auditorName.length() - 1));
                        }
                    }
                    bpmVoList.add(vo);
                }
            }
        }

        return bpmVoList;
    }


    /**
     * 删除业务流程单主表
     *
     * @param id 业务流程单主表ID
     * @return 结果
     */
    @Override
    public int deleteFdBusCostById(Integer id) {
        return fdBusCostMapper.deleteFdBusCostById(id);
    }


    /**
     * 批量删除业务流程单主表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFdBusCostByIds(Integer[] ids) {
        return fdBusCostMapper.deleteFdBusCostByIds(ids);
    }

    @Override
    public void exportBusProcess(String costCode, HttpServletResponse response) throws Exception {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("业务流程单");
        XSSFRow row = sheet.createRow(0);
        //宽度
        for (int i = 0; i <= 5; i++) {
            sheet.setColumnWidth(i, 60 * 80);
        }

        int rowIndex = 0;
        row.setHeight((short) (10 * 50));
        //字体1
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");

        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);

        // 创建一个单元格样式，并设置字体为Wingdings
        Font wingdingsFont = workbook.createFont();
        wingdingsFont.setFontName("Wingdings");
        CellStyle style2 = workbook.createCellStyle();
        style2.setFont(wingdingsFont);
        style2.setAlignment(HorizontalAlignment.CENTER);
        style2.setVerticalAlignment(VerticalAlignment.CENTER);
        style2.setBorderBottom(BorderStyle.THIN);
        style2.setBorderLeft(BorderStyle.THIN);
        style2.setBorderTop(BorderStyle.THIN);
        style2.setBorderRight(BorderStyle.THIN);
        style2.setBottomBorderColor(HSSFColor.BLACK.index);
        style2.setLeftBorderColor(HSSFColor.BLACK.index);
        style2.setRightBorderColor(HSSFColor.BLACK.index);
        style2.setTopBorderColor(HSSFColor.BLACK.index);

        // 创建一个包含方框和√的字符串
        String checkboxUnchecked = "¨";
        String checkboxChecked = "þ";

        //标题
        XSSFCell cell0 = row.createCell(rowIndex);
        cell0.setCellValue("业 务 流 程 单");
        cell0.setCellStyle(style);
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 5));
        //第一行
        rowIndex++;
        XSSFRow row1 = sheet.createRow(rowIndex);
        XSSFCell cell10 = row1.createCell(0);
        cell10.setCellValue("委托人\n" + "（实际付款人）");
        cell10.setCellStyle(style);
        XSSFCell cell11 = row1.createCell(1);
        cell11.setCellValue("");
        cell11.setCellStyle(style);
        XSSFCell cell12 = row1.createCell(2);
        cell12.setCellValue("");
        cell12.setCellStyle(style);
        XSSFCell cell13 = row1.createCell(3);
        cell13.setCellValue("");
        cell13.setCellStyle(style);
        XSSFCell cell14 = row1.createCell(4);
        cell14.setCellValue("联系人");
        cell14.setCellStyle(style);
        XSSFCell cell15 = row1.createCell(5);
        cell15.setCellValue("");
        cell15.setCellStyle(style);
        //第二行
        rowIndex++;
        XSSFRow row2 = sheet.createRow(rowIndex);
        XSSFCell cell20 = row2.createCell(0);
        cell20.setCellValue("");
        cell20.setCellStyle(style);
        XSSFCell cell21 = row2.createCell(1);
        cell21.setCellValue("");
        cell21.setCellStyle(style);
        XSSFCell cell22 = row2.createCell(2);
        cell22.setCellValue("");
        cell22.setCellStyle(style);
        XSSFCell cell23 = row2.createCell(3);
        cell23.setCellValue("");
        cell23.setCellStyle(style);
        XSSFCell cell24 = row2.createCell(4);
        cell24.setCellValue("联系电话");
        cell24.setCellStyle(style);
        XSSFCell cell25 = row2.createCell(5);
        cell25.setCellValue("");
        cell25.setCellStyle(style);
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex, 1, 3));
        //第三行
        rowIndex++;
        XSSFRow row3 = sheet.createRow(rowIndex);
        XSSFCell cell30 = row3.createCell(0);
        cell30.setCellValue("班列信息");
        cell30.setCellStyle(style);
        XSSFCell cell31 = row3.createCell(1);
        cell31.setCellValue("中欧/亚班列（发站 至 到站）");
        cell31.setCellStyle(style);
        XSSFCell cell32 = row3.createCell(2);
        cell32.setCellValue("");
        cell32.setCellStyle(style);
        XSSFCell cell33 = row3.createCell(3);
        cell33.setCellValue("");
        cell33.setCellStyle(style);
        XSSFCell cell34 = row3.createCell(4);
        cell34.setCellValue("发运日期");
        cell34.setCellStyle(style);
        XSSFCell cell35 = row3.createCell(5);
        cell35.setCellValue("20240101");
        cell35.setCellStyle(style);
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 1, 3));
        //第四行
        rowIndex++;
        XSSFRow row4 = sheet.createRow(rowIndex);
        XSSFCell cell40 = row4.createCell(0);
        cell40.setCellValue("口岸站");
        cell40.setCellStyle(style);
        XSSFCell cell41 = row4.createCell(1);
        cell41.setCellValue("");
        cell41.setCellStyle(style);
        XSSFCell cell42 = row4.createCell(2);
        cell42.setCellValue("口岸代理");
        cell42.setCellStyle(style);
        XSSFCell cell43 = row4.createCell(3);
        cell43.setCellValue("");
        cell43.setCellStyle(style);
        XSSFCell cell44 = row4.createCell(4);
        cell44.setCellValue("境外代理");
        cell44.setCellStyle(style);
        XSSFCell cell45 = row4.createCell(5);
        cell45.setCellValue("");
        cell45.setCellStyle(style);
        //第五行
        rowIndex++;
        XSSFRow row5 = sheet.createRow(rowIndex);
        XSSFCell cell50 = row5.createCell(0);
        cell50.setCellValue("目的国");
        cell50.setCellStyle(style);
        XSSFCell cell51 = row5.createCell(1);
        cell51.setCellValue("");
        cell51.setCellStyle(style);
        XSSFCell cell52 = row5.createCell(2);
        cell52.setCellValue("委托类型");
        cell52.setCellStyle(style);
        XSSFCell cell53 = row5.createCell(3);
        cell53.setCellValue("");
        cell53.setCellStyle(style);
        XSSFCell cell54 = row5.createCell(4);
        cell54.setCellValue("委托区段");
        cell54.setCellStyle(style);
        XSSFCell cell55 = row5.createCell(5);
        cell55.setCellValue("");
        cell55.setCellStyle(style);
        //第六行
        rowIndex++;
        XSSFRow row6 = sheet.createRow(rowIndex);
        XSSFCell cell60 = row6.createCell(0);
        cell60.setCellValue("箱量");
        cell60.setCellStyle(style);
        XSSFCell cell61 = row6.createCell(1);
        cell61.setCellValue("20GP/SOC");
        cell61.setCellStyle(style);
        XSSFCell cell62 = row6.createCell(2);
        cell62.setCellValue("40GP/SOC");
        cell62.setCellStyle(style);
        XSSFCell cell63 = row6.createCell(3);
        cell63.setCellValue("40HQ/SOC");
        cell63.setCellStyle(style);
        XSSFCell cell64 = row6.createCell(4);
        cell64.setCellValue("45HQ/SOC");
        cell64.setCellStyle(style);
        XSSFCell cell65 = row6.createCell(5);
        cell65.setCellValue("40RF/SOC");
        cell65.setCellStyle(style);
        //第七行
        rowIndex++;
        XSSFRow row7 = sheet.createRow(rowIndex);
        XSSFCell cell70 = row7.createCell(0);
        cell70.setCellValue("");
        cell70.setCellStyle(style);
        XSSFCell cell71 = row7.createCell(1);
        cell71.setCellValue("");
        cell71.setCellStyle(style);
        XSSFCell cell72 = row7.createCell(2);
        cell72.setCellValue("");
        cell72.setCellStyle(style);
        XSSFCell cell73 = row7.createCell(3);
        cell73.setCellValue("");
        cell73.setCellStyle(style);
        XSSFCell cell74 = row7.createCell(4);
        cell74.setCellValue("");
        cell74.setCellStyle(style);
        XSSFCell cell75 = row7.createCell(5);
        cell75.setCellValue("");
        cell75.setCellStyle(style);
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 1, 2));
        //第八行
        rowIndex++;
        XSSFRow row8 = sheet.createRow(rowIndex);
        XSSFCell cell80 = row8.createCell(0);
        cell80.setCellValue("");
        cell80.setCellStyle(style);
        XSSFCell cell81 = row8.createCell(1);
        cell81.setCellValue("20GP/COC");
        cell81.setCellStyle(style);
        XSSFCell cell82 = row8.createCell(2);
        cell82.setCellValue("40GP/COC");
        cell82.setCellStyle(style);
        XSSFCell cell83 = row8.createCell(3);
        cell83.setCellValue("40HQ/COC");
        cell83.setCellStyle(style);
        XSSFCell cell84 = row8.createCell(4);
        cell84.setCellValue("45HQ/COC");
        cell84.setCellStyle(style);
        XSSFCell cell85 = row8.createCell(5);
        cell85.setCellValue("40R/COC");
        cell85.setCellStyle(style);
        //第九行
        rowIndex++;
        XSSFRow row9 = sheet.createRow(rowIndex);
        XSSFCell cell90 = row9.createCell(0);
        cell90.setCellValue("");
        cell90.setCellStyle(style);
        XSSFCell cell91 = row9.createCell(1);
        cell91.setCellValue("");
        cell91.setCellStyle(style);
        XSSFCell cell92 = row9.createCell(2);
        cell92.setCellValue("");
        cell92.setCellStyle(style);
        XSSFCell cell93 = row9.createCell(3);
        cell93.setCellValue("");
        cell93.setCellStyle(style);
        XSSFCell cell94 = row9.createCell(4);
        cell94.setCellValue("");
        cell94.setCellStyle(style);
        XSSFCell cell95 = row9.createCell(5);
        cell95.setCellValue("");
        cell95.setCellStyle(style);
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 3, rowIndex, 0, 0));
        //第十行
        rowIndex++;
        XSSFRow row10 = sheet.createRow(rowIndex);
        XSSFCell cell100 = row10.createCell(0);
        cell100.setCellValue("客户对接人");
        cell100.setCellStyle(style);
        XSSFCell cell101 = row10.createCell(1);
        cell101.setCellValue("");
        cell101.setCellStyle(style);
        XSSFCell cell103 = row10.createCell(3);
        cell103.setCellValue("联系方式");
        cell103.setCellStyle(style);
        XSSFCell cell105 = row10.createCell(5);
        cell105.setCellValue("");
        cell105.setCellStyle(style);
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 1, 2));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 4, 5));
        //第11行
        rowIndex++;
        XSSFRow row11 = sheet.createRow(rowIndex);
        XSSFCell cell110 = row11.createCell(0);
        cell110.setCellValue("前端服务");
        cell110.setCellStyle(style);
        XSSFCell cell111 = row11.createCell(1);
        cell111.setCellValue("拖车");
        cell111.setCellStyle(style);
        XSSFCell cell112 = row11.createCell(2);
        cell112.setCellValue(checkboxUnchecked);
        cell112.setCellStyle(style2);
        XSSFCell cell113 = row11.createCell(3);
        cell113.setCellValue("内贸铁路");
        cell113.setCellStyle(style);
        XSSFCell cell114 = row11.createCell(4);
        cell114.setCellValue(checkboxChecked);
        cell114.setCellStyle(style2);
        XSSFCell cell115 = row11.createCell(5);
        cell115.setCellValue("操作员");
        cell115.setCellStyle(style);
        //第12行
        rowIndex++;
        XSSFRow row12 = sheet.createRow(rowIndex);
        XSSFCell cell120 = row12.createCell(0);
        cell120.setCellValue("前端服务");
        cell120.setCellStyle(style);
        XSSFCell cell121 = row12.createCell(1);
        cell121.setCellValue("报关");
        cell121.setCellStyle(style);
        XSSFCell cell122 = row12.createCell(2);
        cell122.setCellValue("");
        cell122.setCellStyle(style);
        // 设置复选框的选中状态
//        cell122.getCTCell().setV("True");
        XSSFCell cell123 = row12.createCell(3);
        cell123.setCellValue("租箱");
        cell123.setCellStyle(style);
        XSSFCell cell124 = row12.createCell(4);
        cell124.setCellValue("");
        cell124.setCellStyle(style);
        // 设置复选框的选中状态
//        cell124.getCTCell().setV("True");
        XSSFCell cell125 = row12.createCell(5);
        cell125.setCellValue("");
        cell125.setCellStyle(style);
        //第13行
        rowIndex++;
        XSSFRow row13 = sheet.createRow(rowIndex);
        XSSFCell cell130 = row13.createCell(0);
        cell130.setCellValue("前端服务");
        cell130.setCellStyle(style);
        XSSFCell cell131 = row13.createCell(1);
        cell131.setCellValue("其它");
        cell131.setCellStyle(style);
        XSSFCell cell132 = row13.createCell(2);
        cell132.setCellValue("");
        cell132.setCellStyle(style);
        XSSFCell cell133 = row13.createCell(3);
        cell133.setCellValue("");
        cell133.setCellStyle(style);
        XSSFCell cell134 = row13.createCell(4);
        cell134.setCellValue("");
        cell134.setCellStyle(style);
        XSSFCell cell135 = row13.createCell(5);
        cell135.setCellValue("");
        cell135.setCellStyle(style);
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 2, rowIndex, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex, 5, 5));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 2, 4));

        //第14行
        rowIndex++;
        XSSFRow row14 = sheet.createRow(rowIndex);
        XSSFCell cell140 = row14.createCell(0);
        cell140.setCellValue("现场操作");
        cell140.setCellStyle(style);
        XSSFCell cell141 = row14.createCell(1);
        cell141.setCellValue("装箱/掏箱");
        cell141.setCellStyle(style);
        XSSFCell cell142 = row14.createCell(2);
        cell142.setCellValue("");
        cell142.setCellStyle(style);
        // 设置复选框的选中状态
//        cell142.getCTCell().setV("True");
        XSSFCell cell143 = row14.createCell(3);
        cell143.setCellValue("调偏/加固");
        cell143.setCellStyle(style);
        XSSFCell cell144 = row14.createCell(4);
        cell144.setCellValue("");
        cell144.setCellStyle(style);
        // 设置复选框的选中状态
//        cell144.getCTCell().setV("True");
        XSSFCell cell145 = row14.createCell(5);
        cell145.setCellValue("操作员");
        cell145.setCellStyle(style);
        //第15行
        rowIndex++;
        XSSFRow row15 = sheet.createRow(rowIndex);
        XSSFCell cell150 = row15.createCell(0);
        cell150.setCellValue("现场操作");
        cell150.setCellStyle(style);
        XSSFCell cell151 = row15.createCell(1);
        cell151.setCellValue("仓储堆存");
        cell151.setCellStyle(style);
        XSSFCell cell152 = row15.createCell(2);
        cell152.setCellValue("");
        cell152.setCellStyle(style);
        // 设置复选框的选中状态
//        cell152.getCTCell().setV("True");
        XSSFCell cell153 = row15.createCell(3);
        cell153.setCellValue("其它");
        cell153.setCellStyle(style);
        XSSFCell cell154 = row15.createCell(4);
        cell154.setCellValue("");
        cell154.setCellStyle(style);
        XSSFCell cell155 = row15.createCell(5);
        cell155.setCellValue("");
        cell155.setCellStyle(style);
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex, 0, 0));

        //第16行
        rowIndex++;
        XSSFRow row16 = sheet.createRow(rowIndex);
        XSSFCell cell160 = row16.createCell(0);
        cell160.setCellValue("后端服务");
        cell160.setCellStyle(style);
        XSSFCell cell161 = row16.createCell(1);
        cell161.setCellValue("口岸");
        cell161.setCellStyle(style);
        XSSFCell cell162 = row16.createCell(2);
        cell162.setCellValue("");
        cell162.setCellStyle(style);
        // 设置复选框的选中状态
//        cell162.getCTCell().setV("True");
        XSSFCell cell163 = row16.createCell(3);
        cell163.setCellValue("分拨");
        cell163.setCellStyle(style);
        XSSFCell cell164 = row16.createCell(4);
        cell164.setCellValue("");
        cell164.setCellStyle(style);
        // 设置复选框的选中状态
//        cell164.getCTCell().setV("True");
        XSSFCell cell165 = row16.createCell(5);
        cell165.setCellValue("操作员");
        cell165.setCellStyle(style);
        //第17行
        rowIndex++;
        XSSFRow row17 = sheet.createRow(rowIndex);
        XSSFCell cell170 = row17.createCell(0);
        cell170.setCellValue("后端服务");
        cell170.setCellStyle(style);
        XSSFCell cell171 = row17.createCell(1);
        cell171.setCellValue("清关");
        cell171.setCellStyle(style);
        XSSFCell cell172 = row17.createCell(2);
        cell172.setCellValue("");
        cell172.setCellStyle(style);
        // 设置复选框的选中状态
//        cell172.getCTCell().setV("True");
        XSSFCell cell173 = row17.createCell(3);
        cell173.setCellValue("仓储堆存");
        cell173.setCellStyle(style);
        XSSFCell cell174 = row17.createCell(4);
        cell174.setCellValue("");
        cell174.setCellStyle(style);
        // 设置复选框的选中状态
//        cell174.getCTCell().setV("True");
        XSSFCell cell175 = row17.createCell(5);
        cell175.setCellValue("");
        cell175.setCellStyle(style);
        //第18行
        rowIndex++;
        XSSFRow row18 = sheet.createRow(rowIndex);
        XSSFCell cell180 = row18.createCell(0);
        cell180.setCellValue("");
        cell180.setCellStyle(style);
        XSSFCell cell181 = row18.createCell(1);
        cell181.setCellValue("其它");
        cell181.setCellStyle(style);
        XSSFCell cell182 = row18.createCell(2);
        cell182.setCellValue("");
        cell182.setCellStyle(style);
        XSSFCell cell183 = row18.createCell(3);
        cell183.setCellValue("");
        cell183.setCellStyle(style);
        XSSFCell cell184 = row18.createCell(4);
        cell184.setCellValue("");
        cell184.setCellStyle(style);
        XSSFCell cell185 = row18.createCell(5);
        cell185.setCellValue("");
        cell185.setCellStyle(style);
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 2, rowIndex, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex, 5, 5));
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 2, 4));
        //第19行
        rowIndex++;
        XSSFRow row19 = sheet.createRow(rowIndex);
        XSSFCell cell190 = row19.createCell(0);
        cell190.setCellValue("款项");
        cell190.setCellStyle(style);
        XSSFCell cell191 = row19.createCell(1);
        cell191.setCellValue("应收金额");
        cell191.setCellStyle(style);
        XSSFCell cell192 = row19.createCell(2);
        cell192.setCellValue("应收单位");
        cell192.setCellStyle(style);
        XSSFCell cell193 = row19.createCell(3);
        cell193.setCellValue("应付金额");
        cell193.setCellStyle(style);
        XSSFCell cell194 = row19.createCell(4);
        cell194.setCellValue("应付单位");
        cell194.setCellStyle(style);
        XSSFCell cell195 = row19.createCell(5);
        cell195.setCellValue("");
        cell195.setCellStyle(style);
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 4, 5));

        String data = remoteAdminService.selectDictByType2("expense_account");
        List<SysDictVo> costList = JSONUtil.toList(JSONUtil.parseArray(data), SysDictVo.class);
        if (CollUtil.isNotEmpty(costList)) {
            for (SysDictVo sysDictVo : costList) {
                rowIndex++;
                XSSFRow rowx = sheet.createRow(rowIndex);
                XSSFCell cellx0 = rowx.createCell(0);
                cellx0.setCellValue(sysDictVo.getName());
                cellx0.setCellStyle(style);
                XSSFCell cellx1 = rowx.createCell(1);
                cellx1.setCellValue("");
                cellx1.setCellStyle(style);
                XSSFCell cellx2 = rowx.createCell(2);
                cellx2.setCellValue("");
                cellx2.setCellStyle(style);
                XSSFCell cellx3 = rowx.createCell(3);
                cellx3.setCellValue("");
                cellx3.setCellStyle(style);
                XSSFCell cellx4 = rowx.createCell(4);
                cellx4.setCellValue("");
                cellx4.setCellStyle(style);
                XSSFCell cellx5 = rowx.createCell(5);
                cellx5.setCellValue("");
                cellx5.setCellStyle(style);
                sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 4, 5));
            }
        }
        //第20行
        rowIndex++;
        XSSFRow row20 = sheet.createRow(rowIndex);
        XSSFCell cell200 = row20.createCell(0);
        cell200.setCellValue("合计");
        cell200.setCellStyle(style);
        XSSFCell cell201 = row20.createCell(1);
        cell201.setCellValue("");
        cell201.setCellStyle(style);
        XSSFCell cell202 = row20.createCell(2);
        cell202.setCellValue("");
        cell202.setCellStyle(style);
        XSSFCell cell203 = row20.createCell(3);
        cell203.setCellValue("");
        cell203.setCellStyle(style);
        XSSFCell cell204 = row20.createCell(4);
        cell204.setCellValue("");
        cell204.setCellStyle(style);
        XSSFCell cell205 = row20.createCell(5);
        cell205.setCellValue("");
        cell205.setCellStyle(style);
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 4, 5));

        //第21行
        rowIndex++;
        XSSFRow row21 = sheet.createRow(rowIndex);
        XSSFCell cell210 = row21.createCell(0);
        cell210.setCellValue("操作主管");
        XSSFCell cell211 = row21.createCell(1);
        cell211.setCellValue("");
        XSSFCell cell212 = row21.createCell(2);
        cell212.setCellValue("部门经理");
        XSSFCell cell213 = row21.createCell(3);
        cell213.setCellValue("");
        XSSFCell cell214 = row21.createCell(4);
        cell214.setCellValue("商务会计");
        XSSFCell cell215 = row21.createCell(5);
        cell215.setCellValue("");

        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String("业务流程单.xls".getBytes("GB2312"), "8859_1"));
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }

    @Override
    public void exportReceiveTemplate(FdBusCost fdBusCost, HttpServletResponse response) throws Exception {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("应收费用明细");
        XSSFRow row = sheet.createRow(0);
        //宽度
        for (int i = 0; i <= 14; i++) {
            sheet.setColumnWidth(i, 60 * 80);
        }
        int rowIndex = 0;
        row.setHeight((short) (10 * 50));
        //字体1
        XSSFCellStyle style = setStyle(workbook);
        setTitle(row, style);
        rowIndex++;

        setReceiveList(fdBusCost.getCostCode(), sheet, rowIndex);
        setSheet1(fdBusCost.getCostCode(), workbook);
        setSheet2(workbook);
        setSheet3(workbook);
//        response.setContentType("application/octet-stream;charset=UTF-8");
//        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
//        response.setHeader("Content-Disposition", "attachment;filename=" + new String("应收费用明细.xlsx".getBytes("GB2312"), "8859_1"));
//        response.addHeader("Pargam", "no-cache");
//        response.addHeader("Cache-Control", "no-cache");
        // 设置响应头
        //写入数据流下载
        try {
            //response为HttpServletResponse对象
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("应收费用明细", "utf-8") + ".xls");
            ServletOutputStream out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            // 关闭writer，释放内存
            out.close();
            workbook.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void exportReceiveCustomizeTemplate(FdBusCost fdBusCost, HttpServletResponse response) throws Exception {
        String templateFileName = "exportReceiveCustomizeTemplate.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)) {
            templateFileName = linuxPath + templateFileName;
        } else {
            templateFileName = windowsPath + templateFileName;
        }
        InputStream templateFile = new FileInputStream(templateFileName);
        XSSFWorkbook workbook = new XSSFWorkbook(templateFile);

        setSheet2(workbook);
        setSheet3(workbook);

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 使用 UTF-8 编码处理文件名，确保中文文件名在浏览器中正确显示
        String encodedFileName = URLEncoder.encode("应收费用明细.xlsx", "UTF-8");
        encodedFileName = encodedFileName.replaceAll("\\+", "%20"); // 处理空格问题
        response.setHeader("Content-Disposition", "attachment;filename=\"" + encodedFileName + "\"");

        // 设置缓存控制
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }

    @Override
    public void exportReceiveTzTemplate(FdBusCost fdBusCost, HttpServletResponse response) throws Exception {
        com.alibaba.excel.ExcelWriter excelWriter = null;
        String templateFileName = "exportTzTemplate.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)) {
            templateFileName = linuxPath + templateFileName;
        } else {
            templateFileName = windowsPath + templateFileName;
        }
        String fileName = URLEncoder.encode("应收费用明细", "UTF-8").replaceAll("\\+", "%20");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + ".xlsx\"");

        excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(templateFileName).build();
        WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "应收费用明细").build();
        /*excelWriter.fill(map, writeSheet1);
        if (CollUtil.isNotEmpty(fdShippingAccoundetails)) {
            excelWriter.fill(fdShippingAccoundetails, writeSheet1);
        }*/
        excelWriter.finish();
    }

    /**
     * 导出应收费用明细模板（济南模板）
     *
     * @return
     */
    @Override
    public void exportReceiveJnTemplate(FdBusCost fdBusCost, HttpServletResponse response) throws Exception {
        com.alibaba.excel.ExcelWriter excelWriter = null;
        String templateFileName = "exportJnTemplate.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)) {
            templateFileName = linuxPath + templateFileName;
        } else {
            templateFileName = windowsPath + templateFileName;
        }
        String fileName = URLEncoder.encode("应收费用明细", "UTF-8").replaceAll("\\+", "%20");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + ".xlsx\"");

        excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(templateFileName).build();
        WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "应收费用明细").build();
        /*excelWriter.fill(map, writeSheet1);
        if (CollUtil.isNotEmpty(fdShippingAccoundetails)) {
            excelWriter.fill(fdShippingAccoundetails, writeSheet1);
        }*/
        excelWriter.finish();
    }

    /**
     * 币种参考
     *
     * @Param: workbook
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/05 9:57
     **/
    public void setSheet2(XSSFWorkbook workbook) {
        XSSFSheet sheet = workbook.createSheet("币种参考");
        int rowIndex = 0;
        XSSFRow row = sheet.createRow(rowIndex);
        //字体1
        XSSFCellStyle style = setStyle(workbook);
        XSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("币种名称");
        cell0.setCellStyle(style);
        rowIndex++;
        String data = remoteAdminService.selectDictByType2("currcode");
        List<SysDictVo> list = JSONUtil.toList(JSONUtil.parseArray(data), SysDictVo.class);
        if (CollUtil.isNotEmpty(list)) {
            for (SysDictVo sysDictVo : list) {
                XSSFRow row2 = sheet.createRow(rowIndex);
                XSSFCell cell20 = row2.createCell(0);
                cell20.setCellValue(sysDictVo.getName());
                rowIndex++;
            }

        }
    }

    /**
     * 箱信息参考
     *
     * @Param: workbook
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/05 9:57
     **/
    public void setSheet1(String costCode, XSSFWorkbook workbook) {
        XSSFSheet sheet = workbook.createSheet("箱信息参考");
        int rowIndex = 0;
        XSSFRow row = sheet.createRow(rowIndex);
        //字体1
        XSSFCellStyle style = setStyle(workbook);
        XSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("箱号");
        cell0.setCellStyle(style);
        XSSFCell cell1 = row.createCell(1);
        cell1.setCellValue("进/出口类型");
        cell1.setCellStyle(style);
        XSSFCell cell2 = row.createCell(2);
        cell2.setCellValue("箱型代码");
        cell2.setCellStyle(style);
        XSSFCell cell3 = row.createCell(3);
        cell3.setCellValue("出入境口岸站");
        cell3.setCellStyle(style);
        XSSFCell cell4 = row.createCell(4);
        cell4.setCellValue("撤换箱");
        cell4.setCellStyle(style);
        rowIndex++;
        List<WaybillContainerInfo> infos = waybillContainerInfoMapper.getBusCostExport(costCode);
        if (CollUtil.isNotEmpty(infos)) {
            for (WaybillContainerInfo info : infos) {
                XSSFRow row2 = sheet.createRow(rowIndex);
                XSSFCell cell20 = row2.createCell(0);
                cell20.setCellValue(info.getContainerNo());

                XSSFCell cell21 = row2.createCell(1);
                // E出口、I进口、P过境
                if ("E".equals(info.getIdentification())) {
                    cell21.setCellValue("出口");
                } else if ("I".equals(info.getIdentification())) {
                    cell21.setCellValue("进口");
                } else if ("P".equals(info.getIdentification())) {
                    cell21.setCellValue("过境");
                }

                XSSFCell cell22 = row2.createCell(2);
                cell22.setCellValue(info.getContainerTypeCode());

                XSSFCell cell23 = row2.createCell(3);
                cell23.setCellValue(info.getPortStation());

                XSSFCell cell24 = row2.createCell(4);
                if ("C".equals(info.getOperFlag())) {
                    cell24.setCellValue("撤");
                } else if ("H".equals(info.getOperFlag())) {
                    cell24.setCellValue("换");
                }
                rowIndex++;
            }

        }
    }

    /**
     * 费用类型参考
     *
     * @Param: workbook
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/05 9:57
     **/
    public void setSheet3(XSSFWorkbook workbook) {
        XSSFSheet sheet = workbook.createSheet("费用类型参考");
        int rowIndex = 0;
        XSSFRow row = sheet.createRow(rowIndex);
        //字体1
        XSSFCellStyle style = setStyle(workbook);
        XSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("费用大类");
        cell0.setCellStyle(style);

        XSSFCell cell1 = row.createCell(1);
        cell1.setCellValue("费用小类");
        cell1.setCellStyle(style);
        rowIndex++;
        String dl = remoteAdminService.selectSysDictIndexs("费用类型");
        List<SysDictVo> dlList = JSONUtil.toList(JSONUtil.parseArray(dl), SysDictVo.class);
        if (CollUtil.isNotEmpty(dlList)) {
            for (SysDictVo sysDictVo : dlList) {
                String xl = remoteAdminService.selectDictByType2(sysDictVo.getCode());
                List<SysDictVo> xlList = JSONUtil.toList(JSONUtil.parseArray(xl), SysDictVo.class);
                if (CollUtil.isNotEmpty(xlList)) {
                    for (SysDictVo xlVo : xlList) {
                        XSSFRow row2 = sheet.createRow(rowIndex);
                        XSSFCell cell20 = row2.createCell(0);
                        cell20.setCellValue(sysDictVo.getName());
                        XSSFCell cell21 = row2.createCell(1);
                        cell21.setCellValue(xlVo.getName());
                        rowIndex++;
                    }
                }
            }
        }
    }

    /**
     * 设置应收明细
     *
     * @Param: costCode, sheet, rowIndex
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/04 18:31
     **/
    public void setReceiveList(String costCode, XSSFSheet sheet, int rowIndex) {
        FdBusCost sel = new FdBusCost();
        sel.setCostCode(costCode);
        sel.setDeleteFlag("N");
        List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel);
        if (CollUtil.isNotEmpty(fdBusCosts)) {
            FdBusCostDetail sel3 = new FdBusCostDetail();
            sel3.setCostCode(costCode);
            sel3.setCostType("0");
            sel3.setDeleteFlag("N");
            List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList(sel3);
            if (CollUtil.isEmpty(detailList)) {
                //导出所有运单数据
                FdBusCostWaybill sel5 = new FdBusCostWaybill();
                sel5.setCostCode(costCode);
//                sel5.setAuditStatus("0");
                sel5.setDeleteFlag("N");
                List<FdBusCostWaybill> fdBusCostWaybills = fdBusCostWaybillMapper.selectFdBusCostWaybillList(sel5);
                if (CollUtil.isNotEmpty(fdBusCostWaybills)) {
                    WaybillContainerInfo sel2 = new WaybillContainerInfo();
                    sel2.setDeleteFlag("N");
                    for (FdBusCostWaybill fdBusCostWaybill : fdBusCostWaybills) {
                        sel2.setWaybillNo(fdBusCostWaybill.getWaybillNo());
                        List<WaybillContainerInfo> waybillContainerInfos = waybillContainerInfoMapper.selectWaybillContainerInfoListByLike(sel2);
                        if (CollUtil.isNotEmpty(waybillContainerInfos)) {
                            for (WaybillContainerInfo info : waybillContainerInfos) {
                                //国内段包干
                                setRow(sheet, rowIndex, fdBusCosts, info, "国内段包干", fdBusCosts.get(0).getCustomerName(), fdBusCosts.get(0).getPlatformName());
                                rowIndex++;
                                //国外段包干
                                setRow(sheet, rowIndex, fdBusCosts, info, "国外段包干", fdBusCosts.get(0).getCustomerName(), fdBusCosts.get(0).getPlatformName());
                                rowIndex++;
                            }
                        }
                    }
                }

            } else {
                //导出未审核业务明细和未进入业务明细的运单信息
                sel3.setAuditStatus("0");
                List<FdBusCostDetailDTO> detailList2 = fdBusCostDetailMapper.selectFdBusCostDetailExportList(sel3);
                if (CollUtil.isNotEmpty(detailList2)) {
                    for (FdBusCostDetailDTO detail : detailList2) {
                        setRow2(sheet, rowIndex, detail);
                        rowIndex++;
                    }
                }

                List<WaybillContainerInfo> waybillContainerInfos = waybillContainerInfoMapper.selectListWithOutBusCost(costCode);
                if (CollUtil.isNotEmpty(waybillContainerInfos)) {
                    for (WaybillContainerInfo info : waybillContainerInfos) {
                        //国内段包干
                        setRow(sheet, rowIndex, fdBusCosts, info, "国内段包干", fdBusCosts.get(0).getCustomerName(), fdBusCosts.get(0).getPlatformName());
                        //国外段包干
                        rowIndex++;
                        setRow(sheet, rowIndex, fdBusCosts, info, "国外段包干", fdBusCosts.get(0).getCustomerName(), fdBusCosts.get(0).getPlatformName());
                        rowIndex++;
                    }
                }
            }
        }
    }

    @Override
    public void exportPayTemplate(FdBusCost fdBusCost, HttpServletResponse response) throws Exception {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("应付费用明细");
        XSSFRow row = sheet.createRow(0);
        //宽度
        for (int i = 0; i <= 14; i++) {
            sheet.setColumnWidth(i, 60 * 80);
        }
        int rowIndex = 0;
        row.setHeight((short) (10 * 50));
        //字体1
        XSSFCellStyle style = setStyle(workbook);
        setTitle(row, style);
        rowIndex++;
        setPayList(fdBusCost.getCostCode(), sheet, rowIndex);
        setSheet1(fdBusCost.getCostCode(), workbook);
        setSheet2(workbook);
        setSheet3(workbook);
        //写入数据流下载
        try {
            //response为HttpServletResponse对象
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("应付费用明细", "utf-8") + ".xls");
            ServletOutputStream out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            // 关闭writer，释放内存
            out.close();
            workbook.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void exportPayTzTemplate(FdBusCost fdBusCost, HttpServletResponse response) throws Exception {
        com.alibaba.excel.ExcelWriter excelWriter = null;
        String templateFileName = "exportTzTemplate.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)) {
            templateFileName = linuxPath + templateFileName;
        } else {
            templateFileName = windowsPath + templateFileName;
        }
        String fileName = URLEncoder.encode("应付费用明细", "UTF-8").replaceAll("\\+", "%20");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + ".xlsx\"");

        excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(templateFileName).build();
        WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "应付费用明细").build();
        /*excelWriter.fill(map, writeSheet1);
        if (CollUtil.isNotEmpty(fdShippingAccoundetails)) {
            excelWriter.fill(fdShippingAccoundetails, writeSheet1);
        }*/
        excelWriter.finish();
    }

    /**
     * 设置应付明细
     *
     * @Param: costCode, sheet, rowIndex
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/04 18:31
     **/
    public void setPayList(String costCode, XSSFSheet sheet, int rowIndex) {

        String receiveName = "";
        FdBusCost fdBusCost = fdBusCostMapper.selectFdBusCostByCostCode(costCode);
        if (fdBusCost != null) {
            Shifmanagement sel6 = new Shifmanagement();
            sel6.setShiftId(fdBusCost.getShiftNo());
            sel6.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
            sel6.setDeleteFlag("N");
            List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel6);
            if (CollUtil.isNotEmpty(shifmanagements)) {
                if (StrUtil.isNotEmpty(shifmanagements.get(0).getParentId())) {
                    receiveName = shifmanagements.get(0).getSharePlatformName();
                } else {
                    SupplierInfo sel4 = new SupplierInfo();
                    String supPlatformCode = SecurityUtils.getUserInfo().getSupPlatformCode();
                    if (supPlatformCode.contains("_")) {
                        sel4.setCustomerCode(supPlatformCode.split("_")[0]);
                    } else {
                        sel4.setCustomerCode(supPlatformCode);
                    }
                    sel4.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
                    sel4.setPartnerShip("normal");
                    List<SupplierInfo> supplierInfos = supplierInfoMapper.getReceiveList(sel4);
                    if (CollUtil.isNotEmpty(supplierInfos)) {
                        receiveName = supplierInfos.get(0).getCustomerName();
                    }
                }
            }
        }

        FdBusCost sel = new FdBusCost();
        sel.setCostCode(costCode);
        sel.setDeleteFlag("N");
        List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel);
        if (CollUtil.isNotEmpty(fdBusCosts)) {
            FdBusCostDetail sel3 = new FdBusCostDetail();
            sel3.setCostCode(costCode);
            sel3.setCostType("1");
            sel3.setDeleteFlag("N");
            List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList(sel3);
            if (CollUtil.isEmpty(detailList)) {
                //导出所有运单数据
                FdBusCostWaybill sel5 = new FdBusCostWaybill();
                sel5.setCostCode(costCode);
//                sel5.setAuditStatus("0");
                sel5.setDeleteFlag("N");
                List<FdBusCostWaybill> fdBusCostWaybills = fdBusCostWaybillMapper.selectFdBusCostWaybillList(sel5);
                if (CollUtil.isNotEmpty(fdBusCostWaybills)) {
                    WaybillContainerInfo sel2 = new WaybillContainerInfo();
                    sel2.setDeleteFlag("N");
                    for (FdBusCostWaybill fdBusCostWaybill : fdBusCostWaybills) {
                        sel2.setWaybillNo(fdBusCostWaybill.getWaybillNo());
                        List<WaybillContainerInfo> waybillContainerInfos = waybillContainerInfoMapper.selectWaybillContainerInfoListByLike(sel2);
                        if (CollUtil.isNotEmpty(waybillContainerInfos)) {
                            for (WaybillContainerInfo info : waybillContainerInfos) {
                                //国内段包干
                                setRow(sheet, rowIndex, fdBusCosts, info, "国内段包干", fdBusCosts.get(0).getPlatformName(), receiveName);
                                rowIndex++;
                                //国外段包干
                                setRow(sheet, rowIndex, fdBusCosts, info, "国外段包干", fdBusCosts.get(0).getPlatformName(), receiveName);
                                rowIndex++;
                            }
                        }
                    }
                }
            } else {
                //导出未审核业务明细和未进入业务明细的运单信息
                sel3.setAuditStatus("0");
                List<FdBusCostDetailDTO> detailList2 = fdBusCostDetailMapper.selectFdBusCostDetailExportList(sel3);
                if (CollUtil.isNotEmpty(detailList2)) {
                    for (FdBusCostDetailDTO detail : detailList2) {
                        setRow2(sheet, rowIndex, detail);
                        rowIndex++;
                    }
                }
                List<WaybillContainerInfo> waybillContainerInfos = waybillContainerInfoMapper.selectListWithOutBusCost(costCode);
                if (CollUtil.isNotEmpty(waybillContainerInfos)) {
                    for (WaybillContainerInfo info : waybillContainerInfos) {
                        //国内段包干
                        setRow(sheet, rowIndex, fdBusCosts, info, "国内段包干", fdBusCosts.get(0).getPlatformName(), receiveName);
                        rowIndex++;
                        //国外段包干
                        setRow(sheet, rowIndex, fdBusCosts, info, "国外段包干", fdBusCosts.get(0).getPlatformName(), receiveName);
                        rowIndex++;
                    }
                }
            }
        }
    }

    public XSSFCellStyle setStyle(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");
        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);
        return style;
    }

    /**
     * 设置标题
     *
     * @Param: row, style
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/04 17:57
     **/
    public void setTitle(XSSFRow row, XSSFCellStyle style) {
        XSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("付款方");
        cell0.setCellStyle(style);
        XSSFCell cell1 = row.createCell(1);
        cell1.setCellValue("收款方");
        cell1.setCellStyle(style);
        XSSFCell cell2 = row.createCell(2);
        cell2.setCellValue("箱号");
        cell2.setCellStyle(style);
        XSSFCell cell3 = row.createCell(3);
        cell3.setCellValue("箱属");
        cell3.setCellStyle(style);
        XSSFCell cell4 = row.createCell(4);
        cell4.setCellValue("箱型代码");
        cell4.setCellStyle(style);
        XSSFCell cell5 = row.createCell(5);
        cell5.setCellValue("发站");
        cell5.setCellStyle(style);
        XSSFCell cell6 = row.createCell(6);
        cell6.setCellValue("到站");
        cell6.setCellStyle(style);
        XSSFCell cell7 = row.createCell(7);
        cell7.setCellValue("类型");
        cell7.setCellStyle(style);
        XSSFCell cell8 = row.createCell(8);
        cell8.setCellValue("费用大类名称");
        cell8.setCellStyle(style);
        XSSFCell cell9 = row.createCell(9);
        cell9.setCellValue("费用小类名称");
        cell9.setCellStyle(style);
        XSSFCell cell10 = row.createCell(10);
        cell10.setCellValue("币种");
        cell10.setCellStyle(style);
        XSSFCell cell11 = row.createCell(11);
        cell11.setCellValue("汇率");
        cell11.setCellStyle(style);
        XSSFCell cell12 = row.createCell(12);
        cell12.setCellValue("原币金额");
        cell12.setCellStyle(style);
        XSSFCell cell13 = row.createCell(13);
        cell13.setCellValue("是否全程");
        cell13.setCellStyle(style);
        XSSFCell cell14 = row.createCell(14);
        cell14.setCellValue("有色金属");
        cell14.setCellStyle(style);
    }

    /**
     * 从业务流程明细获取数据
     *
     * @Param: sheet, rowIndex, detail
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/04 16:57
     **/
    public void setRow2(XSSFSheet sheet, int rowIndex, FdBusCostDetailDTO detail) {
        XSSFRow row2 = sheet.createRow(rowIndex);
        XSSFCell cell20 = row2.createCell(0);
        cell20.setCellValue(detail.getPayName());
        XSSFCell cell21 = row2.createCell(1);
        cell21.setCellValue(detail.getReceiveName());
        XSSFCell cell22 = row2.createCell(2);
        cell22.setCellValue(detail.getContainerNumber());
        XSSFCell cell23 = row2.createCell(3);
        if (StrUtil.isNotEmpty(detail.getContainerOwner())) {
            if ("0".equals(detail.getContainerOwner())) {
                cell23.setCellValue("自备箱");
            } else if ("1".equals(detail.getContainerOwner())) {
                cell23.setCellValue("中铁箱");
            }
        }

        XSSFCell cell24 = row2.createCell(4);
        cell24.setCellValue(detail.getContainerTypeCode());
        XSSFCell cell25 = row2.createCell(5);
        cell25.setCellValue(detail.getDestinationName());
        XSSFCell cell26 = row2.createCell(6);
        cell26.setCellValue(detail.getDestination());
        XSSFCell cell27 = row2.createCell(7);
        if (StrUtil.isNotEmpty(detail.getIdentification())) {
            if ("E".equals(detail.getIdentification())) {
                cell27.setCellValue("出口");
            } else if ("P".equals(detail.getIdentification())) {
                cell27.setCellValue("过境");
            } else if ("I".equals(detail.getIdentification())) {
                cell27.setCellValue("进口");
            }
        }
        XSSFCell cell28 = row2.createCell(8);
        cell28.setCellValue(detail.getCodeBbCategoriesName());
        XSSFCell cell29 = row2.createCell(9);
        cell29.setCellValue(detail.getCodeSsCategoriesName());

        XSSFCell cell210 = row2.createCell(10);
        if (StrUtil.isNotBlank(detail.getCurrency())) {
            cell210.setCellValue(detail.getCurrency());
        } else {
            cell210.setCellValue("");
        }

        XSSFCell cell211 = row2.createCell(11);
        if (detail.getExchangeRate() != null) {
            cell211.setCellValue(detail.getExchangeRate().doubleValue());
        } else {
            cell211.setCellValue(1.00);
        }

        XSSFCell cell212 = row2.createCell(12);
        if (detail.getOriginalAmount() != null) {
            cell212.setCellValue(detail.getOriginalAmount().doubleValue());
        } else {
            cell212.setCellValue(0.00);
        }

        XSSFCell cell213 = row2.createCell(13);
        if ("0".equals(detail.getIsFull())) {
            cell213.setCellValue("否");
        } else if ("1".equals(detail.getIsFull())) {
            cell213.setCellValue("是");
        }

        XSSFCell cell214 = row2.createCell(14);
        if ("0".equals(detail.getNonFerrous())) {
            cell214.setCellValue("否");
        } else if ("1".equals(detail.getNonFerrous())) {
            cell214.setCellValue("是");
        }
    }

    /**
     * 从运单获取数据
     *
     * @Param: sheet, rowIndex, fdBusCosts, info, codeSsCategoriesName
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/04 16:57
     **/
    public void setRow(XSSFSheet sheet, int rowIndex, List<FdBusCost> fdBusCosts, WaybillContainerInfo info, String codeSsCategoriesName, String payName, String receiveName) {
        XSSFRow row2 = sheet.createRow(rowIndex);
        XSSFCell cell20 = row2.createCell(0);
        cell20.setCellValue(payName);
        XSSFCell cell21 = row2.createCell(1);
        cell21.setCellValue(receiveName);
        XSSFCell cell22 = row2.createCell(2);
        cell22.setCellValue(info.getContainerNo());
        XSSFCell cell23 = row2.createCell(3);
        if (StrUtil.isNotEmpty(info.getContainerOwner())) {
            if ("0".equals(info.getContainerOwner())) {
                cell23.setCellValue("自备箱");
            } else if ("1".equals(info.getContainerOwner())) {
                cell23.setCellValue("中铁箱");
            }
        }

        XSSFCell cell24 = row2.createCell(4);
        cell24.setCellValue(info.getContainerTypeCode());
        XSSFCell cell25 = row2.createCell(5);
        cell25.setCellValue(info.getStartStationName());
        XSSFCell cell26 = row2.createCell(6);
        cell26.setCellValue(info.getEndStationName());
        XSSFCell cell27 = row2.createCell(7);
        if (StrUtil.isNotEmpty(info.getIdentification())) {
            if ("E".equals(info.getIdentification())) {
                cell27.setCellValue("出口");
            } else if ("P".equals(info.getIdentification())) {
                cell27.setCellValue("过境");
            } else if ("I".equals(info.getIdentification())) {
                cell27.setCellValue("进口");
            }
        }
        XSSFCell cell28 = row2.createCell(8);
        cell28.setCellValue("发运运费");
        XSSFCell cell29 = row2.createCell(9);
        cell29.setCellValue(codeSsCategoriesName);

        XSSFCell cell213 = row2.createCell(13);
        if (StrUtil.isNotBlank(info.getIsFull())) {
            if (info.getIsFull().equals("1")) {
                cell213.setCellValue("是");
            } else if (info.getIsFull().equals("0")) {
                cell213.setCellValue("否");
            }
        }

        XSSFCell cell214 = row2.createCell(14);
        if (StrUtil.isNotBlank(info.getNonFerrous())) {
            if (info.getNonFerrous().equals("1")) {
                cell214.setCellValue("是");
            } else if (info.getNonFerrous().equals("0")) {
                cell214.setCellValue("否");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R importedDetail(FdBusCost fdBusCost, MultipartFile file, String costType) {
        Workbook workbook = null;
        try {
            InputStream is = file.getInputStream();
            //利用WorkbookFactory获取Sheet，无需判断版本
            workbook = WorkbookFactory.create(is);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //获取行数
        Sheet sheet = workbook.getSheetAt(0);
        int lastRowNum = sheet.getLastRowNum();
        StringBuffer sb = new StringBuffer();


        List<FdBusCostDetail> list = new ArrayList<>();
        FdBusCost cost = fdBusCostMapper.selectFdBusCostByCostCode(fdBusCost.getCostCode());
        getList(sheet, lastRowNum, cost, costType, sb, list);
        R r = null;
        r = updateCostBusDetail(fdBusCost, costType, sb, list, cost);
        if (r != null) {
            return r;
        }
        return new R<>(0, Boolean.TRUE, null, "导入成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R importedReceiveCustomize(FdBusCost fdBusCost, MultipartFile file, String costType) {
        Workbook workbook = null;
        try {
            InputStream is = file.getInputStream();
            //利用WorkbookFactory获取Sheet，无需判断版本
            workbook = WorkbookFactory.create(is);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //获取行数
        Sheet sheet = workbook.getSheetAt(0);
        int lastRowNum = sheet.getLastRowNum();
        StringBuffer sb = new StringBuffer();

        List<FdBusCostDetail> list = new ArrayList<>();
        FdBusCost cost = fdBusCostMapper.selectFdBusCostByCostCode(fdBusCost.getCostCode());
        R result = null;
        result = getCustomizeList(sheet, lastRowNum, cost, costType, sb, list);
        if (result != null) {
            return result;
        }
        if (sb.length() > 0) {
            return R.error(sb.toString());
        }
        R r = null;
        r = updateCostBusDetail(fdBusCost, costType, sb, list, cost);
        if (r != null) {
            return r;
        }
        return new R<>(0, Boolean.TRUE, null, "导入成功");
    }

    public R getCustomizeList(Sheet sheet, int lastRowNum, FdBusCost fdBusCost, String costType, StringBuffer sb, List<FdBusCostDetail> list) {
        Row titleRow = sheet.getRow(1);
        Map<String, Object> titleMap = new HashMap<>();
        String dl = remoteAdminService.selectCategoriesDict("费用类型");
        List<CategoriesDictVO> dlList = JSONUtil.toList(JSONUtil.parseArray(dl), CategoriesDictVO.class);

        short titleNum = titleRow.getLastCellNum();
        for (int i = 1; i <= titleNum; i += 3) {
            Cell cell = titleRow.getCell(i);
            if (cell != null && StrUtil.isNotBlank(cell.toString())) {
                String codeSsCategoriesName = cell.toString();
                Boolean flag = false;
                if (CollUtil.isNotEmpty(dlList)) {
                    for (CategoriesDictVO dict : dlList) {
                        if (StrUtil.isNotBlank(dict.getCodeSsCategoriesName()) && dict.getCodeSsCategoriesName().equals(codeSsCategoriesName)) {
                            flag = true;
                            if (!titleMap.isEmpty() && titleMap.containsValue(dict)) {
                                return R.error("费用小类重复，无法导入：" + codeSsCategoriesName);
                            } else {
                                titleMap.put(String.valueOf(i), dict);
                            }
                        }
                    }
                }
                if (!flag) {
                    return R.error("费用小类不匹配，无法导入：" + codeSsCategoriesName);
                }
            } else {
                break;
            }
        }

        Map<String, String> map = new HashMap<>();
        for (int i = 3; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);

            Cell cell = row.getCell(0);
            String containerNumber = null;
            if (cell != null && StrUtil.isNotBlank(cell.toString())) {
                containerNumber = cell.toString().trim();
                boolean b = CheckUtil.verifyCntrCode(containerNumber);
                if (!b) {
                    sb.append("行" + (i + 1) + containerNumber + "箱号校验未通过！");
                }
                if (map.containsKey(containerNumber)) {
                    sb.append("行" + (i + 1) + containerNumber + "箱号重复，请检查数据！");
                } else {
                    map.put(containerNumber, containerNumber);
                }
                List<WaybillContainerInfo> waybillContainerInfo = waybillContainerInfoMapper.selectInfoByContainerNo(fdBusCost.getShiftNo(), containerNumber);
                if (CollUtil.isEmpty(waybillContainerInfo)) {
                    sb.append("行" + (i + 1) + containerNumber + "订单中不存在该箱号，请检查数据！");
                }
            }
            if (StrUtil.isEmpty(containerNumber)) {
                sb.append("行" + (i + 1) + "箱号异常！");
            }

            short lastCellNum = row.getLastCellNum();
            for (int x = 1; x <= lastCellNum; x += 3) {

                Cell cell1 = row.getCell(x);
                if (cell1 != null && StrUtil.isNotBlank(cell1.toString())) {
                    FdBusCostDetail detail = new FdBusCostDetail();
                    //箱号
                    detail.setContainerNumber(containerNumber);
                    //原币金额
                    try {
                        detail.setOriginalAmount(BigDecimal.valueOf(Double.parseDouble(cell1.toString())));
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "列" + (x + 1) + "数值异常：" + cell1.toString());
                        log.error("行" + (i + 1) + "列" + (x + 1) + "数值异常：" + cell1.toString() + "-->" + e.getMessage());
                    }
                    //币种
                    Cell cell2 = row.getCell(x + 1);
                    if (cell2 != null && StrUtil.isNotBlank(cell2.toString())) {
                        detail.setCurrency(cell2.toString());
                    } else {
                        detail.setCurrency("人民币");
                    }
                    //汇率
                    Cell cell3 = row.getCell(x + 2);
                    try {
                        if (cell3 != null && StrUtil.isNotBlank(cell3.toString())) {
                            detail.setExchangeRate(BigDecimal.valueOf(Double.parseDouble(cell3.toString())));
                        } else {
                            detail.setExchangeRate(BigDecimal.valueOf(1));
                        }
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "列" + (x + 2) + "数值异常：" + cell3.toString());
                        log.error("行" + (i + 1) + "列" + (x + 2) + "数值异常：" + cell3.toString() + "-->" + e.getMessage());
                    }
                    if (detail.getOriginalAmount() != null && detail.getExchangeRate() != null) {
                        detail.setLocalAmount(detail.getOriginalAmount().multiply(detail.getExchangeRate()).setScale(2, RoundingMode.HALF_UP));
                    }
                    detail.setExchangeRateNew(detail.getExchangeRate());
                    detail.setPayCode(fdBusCost.getCustomerCode());
                    detail.setPayName(fdBusCost.getCustomerName());
                    detail.setReceiveCode(fdBusCost.getPlatformCode());
                    detail.setReceiveName(fdBusCost.getPlatformName());
                    detail.setCostCode(fdBusCost.getCostCode());
                    detail.setCostType(costType);
                    detail.setAuditStatus("0");
                    detail.setDeleteFlag("N");
                    CategoriesDictVO dict = (CategoriesDictVO) titleMap.get(String.valueOf(x));
                    if (dict != null) {
                        detail.setCodeBbCategoriesCode(dict.getCodeBbCategoriesCode());
                        detail.setCodeBbCategoriesName(dict.getCodeBbCategoriesName());
                        detail.setCodeSsCategoriesCode(dict.getCodeSsCategoriesCode());
                        detail.setCodeSsCategoriesName(dict.getCodeSsCategoriesName());
                    } else {
                        sb.append("列" + (x + 1) + "费用小类异常!");
                    }

                    SecruityUser userInfo = SecurityUtils.getUserInfo();
                    detail.setAddWho(userInfo.getUserName());
                    detail.setAddWhoName(userInfo.getRealName());
                    detail.setAddTime(LocalDateTime.now());
                    list.add(detail);
                }
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R importedDetailTzTemplate(FdBusCost fdBusCost, MultipartFile file, String costType) {
        Workbook workbook = null;
        try {
            InputStream is = file.getInputStream();
            //利用WorkbookFactory获取Sheet，无需判断版本
            workbook = WorkbookFactory.create(is);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //获取行数
        Sheet sheet = workbook.getSheetAt(0);
        int lastRowNum = sheet.getLastRowNum();
        StringBuffer sb = new StringBuffer();


        List<FdBusCostDetail> list = new ArrayList<>();
        FdBusCost cost = fdBusCostMapper.selectFdBusCostByCostCode(fdBusCost.getCostCode());
        getList2(sheet, lastRowNum, cost, costType, sb, list);
        R r = null;
        r = updateCostBusDetail(fdBusCost, costType, sb, list, cost);
        if (r != null) {
            return r;
        }
        return new R<>(0, Boolean.TRUE, null, "导入成功");
    }

    /**
     * 导入应收费用明细模板（济南模板）
     *
     * @param fdBusCost
     * @param file
     * @return
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R importedReceiveJnTemplate(FdBusCost fdBusCost, MultipartFile file, String costType) {
        Workbook workbook = null;
        try {
            InputStream is = file.getInputStream();
            //利用WorkbookFactory获取Sheet，无需判断版本
            workbook = WorkbookFactory.create(is);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //获取行数
        Sheet sheet = workbook.getSheetAt(0);
        int lastRowNum = sheet.getLastRowNum();
        StringBuffer sb = new StringBuffer();


        List<FdBusCostDetail> list = new ArrayList<>();
        FdBusCost cost = fdBusCostMapper.selectFdBusCostByCostCode(fdBusCost.getCostCode());
        getList3(sheet, lastRowNum, cost, costType, sb, list);
        R r = null;
        r = updateCostBusDetail(fdBusCost, costType, sb, list, cost);
        if (r != null) {
            return r;
        }
        return new R<>(0, Boolean.TRUE, null, "导入成功");
    }

    private R<Object> updateCostBusDetail(FdBusCost fdBusCost, String costType, StringBuffer sb, List<FdBusCostDetail> list, FdBusCost cost) {
        if (CollUtil.isNotEmpty(list)) {
            //排查箱号是否通过审核
            FdBusCostDetail sel = new FdBusCostDetail();
            sel.setCostCode(fdBusCost.getCostCode());
            List<WaybillContainerInfo> waybillContainerInfos = waybillContainerInfoMapper.selectListWithBusCost(sel);
            if (CollUtil.isNotEmpty(waybillContainerInfos)) {

                for (FdBusCostDetail fdBusCostDetail : list) {
                    if (cost != null) {
                        fdBusCostDetail.setShiftNo(cost.getShiftNo());
                    }
                    Boolean flag = Boolean.FALSE;
                    for (WaybillContainerInfo audit : waybillContainerInfos) {
                        if (audit.getContainerNo().equals(fdBusCostDetail.getContainerNumber())) {
                            flag = Boolean.TRUE;
                        }
                    }
                    if (!flag) {
                        sb.append("箱号:" + fdBusCostDetail.getContainerNumber() + "未通过订单审核!");
                    }
                    if (StrUtil.isNotEmpty(fdBusCostDetail.getReceiveCode())) {
                        if (SecurityUtils.getUserInfo().getSupPlatformCode().contains(fdBusCostDetail.getReceiveCode())) {
                            FdShippingAccount sel2 = new FdShippingAccount();
                            sel2.setShiftNo(fdBusCostDetail.getShiftNo());
                            sel2.setPlatformCode(fdBusCostDetail.getPayCode());
                            sel2.setDeleteFlag("N");
                            List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountList(sel2);
                            if (CollUtil.isNotEmpty(fdShippingAccounts) && !"0".equals(fdShippingAccounts.get(0).getStatus()) && !"3".equals(fdShippingAccounts.get(0).getStatus())) {
                                if (!sb.toString().contains("台账已提交审核，无法追加省平台费用！")) {
                                    sb.append("台账已提交审核，无法追加省平台费用！");
                                }
                            }
                        }
                    }
                }
            }

            if (StrUtil.isNotEmpty(sb.toString())) {
                return new R<>(new Throwable(sb.toString()));
            }

            //删除对应未审核数据
            FdBusCostDetail delObj = new FdBusCostDetail();
            delObj.setCostCode(fdBusCost.getCostCode());
            delObj.setCostType(costType);
            delObj.setAuditStatus("0");
            delObj.setDeleteWho(SecurityUtils.getUserInfo().getUserName());
            delObj.setDeleteWhoName(SecurityUtils.getUserInfo().getRealName());
            delObj.setDeleteTime(LocalDateTime.now());
            fdBusCostDetailMapper.deleteFdBusCostDetailByCostCode(delObj);

            for (FdBusCostDetail fdBusCostDetail : list) {
                if (fdBusCostDetail.getLocalAmount() != null && fdBusCostDetail.getOriginalAmount() != null && fdBusCostDetail.getLocalAmount().compareTo(BigDecimal.ZERO) != 0 && fdBusCostDetail.getOriginalAmount().compareTo(BigDecimal.ZERO) != 0) {
                    fdBusCostDetail.setAuditStatus("0");
                    fdBusCostDetail.setAddWho(SecurityUtils.getUserInfo().getUserName());
                    fdBusCostDetail.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                    fdBusCostDetail.setAddTime(LocalDateTime.now());
                    fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
                }

            }
        }
        return null;
    }

    /**
     * 获取导入数据
     *
     * @Param: sheet, lastRowNum, fdBusCost, costType, sb, list
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/06 15:03
     **/
    public void getList(Sheet sheet, int lastRowNum, FdBusCost fdBusCost, String costType, StringBuffer sb, List<FdBusCostDetail> list) {
        List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoList(null);

        SupplierInfo sel4 = new SupplierInfo();
        sel4.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        sel4.setPartnerShip("normal");
        List<SupplierInfo> supplierInfos = supplierInfoMapper.getReceiveList(sel4);

        String dl = remoteAdminService.selectCategoriesDict("费用类型");
        List<CategoriesDictVO> dlList = JSONUtil.toList(JSONUtil.parseArray(dl), CategoriesDictVO.class);
        for (int i = 1; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            boolean blankFlag = isRowEmpty(row);
            if (blankFlag == true) {
                continue;
            }
            FdBusCostDetail fdBusCostDetail = new FdBusCostDetail();
            fdBusCostDetail.setCostCode(fdBusCost.getCostCode());
            fdBusCostDetail.setCostType(costType);
            if ("0".equals(costType)) {
                if (row.getCell(0) != null) {
                    row.getCell(0).setCellType(CellType.STRING);
                    String payName = row.getCell(0).getStringCellValue();
                    if (StrUtil.isNotBlank(payName)) {
                        if (payName.equals(fdBusCost.getCustomerName())) {
                            fdBusCostDetail.setPayName(payName);
                            fdBusCostDetail.setPayCode(fdBusCost.getCustomerCode());
                        } else {
                            sb.append("行" + (i + 1) + "付款方必须为" + fdBusCost.getCustomerName());
                        }
                    } else {
                        sb.append("行" + (i + 1) + "付款方不能为空！");
                    }
                }
                /*if (StrUtil.isBlank(fdBusCostDetail.getPayCode())) {
                    sb.append("行" + (i + 1) + "付款方不存在！");
                }*/
                fdBusCostDetail.setReceiveCode(SecurityUtils.getUserInfo().getPlatformCode());
                fdBusCostDetail.setReceiveName(SecurityUtils.getUserInfo().getPlatformName());
            } else if ("1".equals(costType)) {
                fdBusCostDetail.setPayCode(SecurityUtils.getUserInfo().getPlatformCode());
                fdBusCostDetail.setPayName(SecurityUtils.getUserInfo().getPlatformName());
                if (row.getCell(1) != null) {
                    row.getCell(1).setCellType(CellType.STRING);
                    String receiveName = row.getCell(1).getStringCellValue();
                    if (StrUtil.isNotBlank(receiveName)) {
                        fdBusCostDetail.setReceiveName(receiveName);
                        if (CollUtil.isNotEmpty(supplierInfos)) {
                            for (SupplierInfo info : supplierInfos) {
                                if (receiveName.equals(info.getCustomerName())) {
                                    fdBusCostDetail.setReceiveCode(info.getCustomerCode());
                                }
                            }
                        }
                    } else {
                        sb.append("行" + (i + 1) + "收款方不能为空！");
                    }
                }
                if (StrUtil.isBlank(fdBusCostDetail.getReceiveCode())) {
                    sb.append("行" + (i + 1) + "收款方不存在！");
                }
            } else {
                if (row.getCell(0) != null) {
                    row.getCell(0).setCellType(CellType.STRING);
                    String payName = row.getCell(0).getStringCellValue();
                    if (StrUtil.isNotBlank(payName)) {
                        fdBusCostDetail.setPayName(payName);
                        if (CollUtil.isNotEmpty(customerInfos)) {
                            for (CustomerInfo info : customerInfos) {
                                if (payName.equals(info.getCompanyName())) {
                                    fdBusCostDetail.setPayCode(info.getCustomerCode());
                                }
                            }
                        }
                    } else {
                        sb.append("行" + (i + 1) + "付款方不能为空！");
                    }
                }
                if (row.getCell(1) != null) {
                    row.getCell(1).setCellType(CellType.STRING);
                    String receiveName = row.getCell(1).getStringCellValue();
                    if (StrUtil.isNotBlank(receiveName)) {
                        fdBusCostDetail.setReceiveName(receiveName);
                        if (CollUtil.isNotEmpty(customerInfos)) {
                            for (CustomerInfo info : customerInfos) {
                                if (receiveName.equals(info.getCompanyName())) {
                                    fdBusCostDetail.setReceiveCode(info.getCustomerCode());
                                }
                            }
                        }
                    } else {
                        sb.append("行" + (i + 1) + "收款方不能为空！");
                    }

                }
                if (StrUtil.isBlank(fdBusCostDetail.getPayCode())) {
                    sb.append("行" + (i + 1) + "付款方不存在！");
                }
                if (StrUtil.isBlank(fdBusCostDetail.getReceiveCode())) {
                    sb.append("行" + (i + 1) + "收款方不存在！");
                }
            }

            if (row.getCell(2) != null) {
                row.getCell(2).setCellType(CellType.STRING);
                String containerNumber = row.getCell(2).getStringCellValue().trim();
                if (StrUtil.isNotBlank(containerNumber)) {
                    boolean b = CheckUtil.verifyCntrCode(containerNumber);
                    fdBusCostDetail.setContainerNumber(containerNumber);
                    if (!b) {
                        sb.append("行" + (i + 1) + containerNumber + "箱号校验未通过！");
                    } else {
                        fdBusCostDetail.setContainerNumber(containerNumber);
                    }
                } else {
                    sb.append("行" + (i + 1) + "箱号不能为空！");
                }
            }

            if (row.getCell(8) != null) {
                row.getCell(8).setCellType(CellType.STRING);
                String codeBbCategoriesName = row.getCell(8).getStringCellValue();
                if (StrUtil.isNotBlank(codeBbCategoriesName)) {
                    fdBusCostDetail.setCodeBbCategoriesName(codeBbCategoriesName);
                    if (CollUtil.isNotEmpty(dlList)) {
                        for (CategoriesDictVO vo : dlList) {
                            if (fdBusCostDetail.getCodeBbCategoriesName().equals(vo.getCodeBbCategoriesName())) {
                                fdBusCostDetail.setCodeBbCategoriesCode(vo.getCodeBbCategoriesCode());
                                break;
                            }
                        }
                        if (StrUtil.isEmpty(fdBusCostDetail.getCodeBbCategoriesCode())) {
                            sb.append("行" + (i + 1) + "费用大类匹配失败！");
                        }
                    }
                } else {
                    sb.append("行" + (i + 1) + "费用大类名称不能为空！");
                }

            }
            if (row.getCell(9) != null) {
                row.getCell(9).setCellType(CellType.STRING);
                String codeSsCategoriesName = row.getCell(9).getStringCellValue();
                if (StrUtil.isNotBlank(codeSsCategoriesName)) {
                    fdBusCostDetail.setCodeSsCategoriesName(codeSsCategoriesName);
                    if (CollUtil.isNotEmpty(dlList)) {
                        for (CategoriesDictVO vo : dlList) {
                            if (fdBusCostDetail.getCodeSsCategoriesName().equals(vo.getCodeSsCategoriesName()) && fdBusCostDetail.getCodeBbCategoriesCode().equals(vo.getCodeBbCategoriesCode()) && fdBusCostDetail.getCodeBbCategoriesName().equals(vo.getCodeBbCategoriesName())) {
                                fdBusCostDetail.setCodeSsCategoriesCode(vo.getCodeSsCategoriesCode());
                                break;
                            }
                        }
                        if (StrUtil.isEmpty(fdBusCostDetail.getCodeSsCategoriesCode())) {
                            sb.append("行" + (i + 1) + "费用小类匹配失败！");
                        }
                    }
                } else {
                    sb.append("行" + (i + 1) + "费用小类名称不能为空！");
                }

            }
            if (row.getCell(10) != null) {
                Cell cell = row.getCell(10);
                String currency = CellValueUtil.getCellValueString(cell);
                if (StrUtil.isNotBlank(currency)) {
                    fdBusCostDetail.setCurrency(currency);
                } else {
                    fdBusCostDetail.setCurrency("人民币");
                }
            } else {
                fdBusCostDetail.setCurrency("人民币");
            }
            if (row.getCell(11) != null) {
                Cell cell = row.getCell(11);
                BigDecimal exchangeRate = CellValueUtil.getCellValueBigDecimal(cell);
                if (exchangeRate != null) {
                    fdBusCostDetail.setExchangeRate(exchangeRate);
                } else {
                    fdBusCostDetail.setExchangeRate(BigDecimal.valueOf(1));
                }
            } else {
                fdBusCostDetail.setExchangeRate(BigDecimal.valueOf(1));
            }
            if (row.getCell(12) != null) {
                Cell cell = row.getCell(12);
                BigDecimal originalAmount = CellValueUtil.getCellValueBigDecimal(cell);
                if (originalAmount != null) {
                    fdBusCostDetail.setOriginalAmount(originalAmount);
                    fdBusCostDetail.setLocalAmount(fdBusCostDetail.getExchangeRate().multiply(fdBusCostDetail.getOriginalAmount()).setScale(2, RoundingMode.HALF_UP));
                } else {
                    sb.append("行" + (i + 1) + "原币金额不能为空！");
                }

            } else {
                sb.append("行" + (i + 1) + "原币金额不能为空！");
            }
            list.add(fdBusCostDetail);
        }
    }


    /**
     * 判断模板第28列是否是数字
     *
     * @param sheet 模板。lastRowNum  总行
     * @return
     * <AUTHOR>
     * @since 2025/4/30 下午1:38
     **/
    private boolean isRowNumber(Sheet sheet, int lastRowNum) {
        boolean blankFlag = true;
        for (int i = 3; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            if (row.getCell(27) != null) {
                Cell cell = row.getCell(27);
                String exchangeRate = CellValueUtil.getCellValueString(cell);
                if (StrUtil.isNotBlank(exchangeRate) && !NumberUtil.isNumber(exchangeRate)) {
                    blankFlag = false;
                    break;
                }
            }
        }
        return blankFlag;
    }

    public void getList2(Sheet sheet, int lastRowNum, FdBusCost fdBusCost, String costType, StringBuffer sb, List<FdBusCostDetail> list) {
        List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoList(null);

        //判断模板第28列是否是数字
        boolean isRowNumber = isRowNumber(sheet, lastRowNum);

        for (int i = 3; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            boolean blankFlag = isRowEmpty(row);
            if (blankFlag == true) {
                continue;
            }
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            FdBusCostDetail fdBusCostDetail = new FdBusCostDetail();
            fdBusCostDetail.setCostCode(fdBusCost.getCostCode());
            fdBusCostDetail.setCostType(costType);
            if ("0".equals(costType)) {
                fdBusCostDetail.setReceiveCode(userInfo.getPlatformCode());
                fdBusCostDetail.setReceiveName(userInfo.getPlatformName());
                fdBusCostDetail.setPayCode(fdBusCost.getCustomerCode());
                fdBusCostDetail.setPayName(fdBusCost.getCustomerName());
            } else if ("1".equals(costType)) {
                fdBusCostDetail.setPayCode(userInfo.getPlatformCode());
                fdBusCostDetail.setPayName(userInfo.getPlatformName());
                Shifmanagement sel = new Shifmanagement();
                sel.setPlatformCode(fdBusCost.getPlatformCode());
                sel.setShiftId(fdBusCost.getShiftNo());
                sel.setReleaseStatus("1");
                List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
                if (CollUtil.isNotEmpty(shifmanagements) && StrUtil.isNotBlank(shifmanagements.get(0).getSharePlatformCode())) {
                    fdBusCostDetail.setReceiveCode(shifmanagements.get(0).getSharePlatformCode());
                } else {
                    fdBusCostDetail.setReceiveCode(userInfo.getSupPlatformCode());
                }
                if (StrUtil.isNotBlank(fdBusCostDetail.getReceiveCode()) && CollUtil.isNotEmpty(customerInfos)) {
                    for (CustomerInfo customerInfo : customerInfos) {
                        if (fdBusCostDetail.getReceiveCode().equals(customerInfo.getCustomerCode())) {
                            fdBusCostDetail.setReceiveName(customerInfo.getCompanyName());
                            break;
                        }
                    }
                }
            }

            if (row.getCell(11) != null) {
                row.getCell(11).setCellType(CellType.STRING);
                String containerNumber = row.getCell(11).getStringCellValue().trim();
                if (StrUtil.isNotBlank(containerNumber)) {
                    boolean b = CheckUtil.verifyCntrCode(containerNumber);
                    fdBusCostDetail.setContainerNumber(containerNumber);
                    if (!b) {
                        sb.append("行" + (i + 1) + containerNumber + "箱号校验未通过！");
                    } else {
                        fdBusCostDetail.setContainerNumber(containerNumber);
                    }
                } else {
                    sb.append("行" + (i + 1) + "箱号不能为空！");
                }
            }

            fdBusCostDetail.setCodeBbCategoriesCode("f_fee_type");
            fdBusCostDetail.setCodeBbCategoriesName("发运运费");

            FdBusCostDetail jw = new FdBusCostDetail();
            BeanUtil.copyProperties(fdBusCostDetail, jw);

            if (row.getCell(25) != null) {
                Cell cell = row.getCell(25);
                BigDecimal jnAmount = CellValueUtil.getCellValueBigDecimal(cell);
                if (jnAmount != null) {
                    fdBusCostDetail.setOriginalAmount(jnAmount);
                    fdBusCostDetail.setLocalAmount(jnAmount);
                } else {
                    sb.append("行" + (i + 1) + "境内运费(人民币)不能为空！");
                }

            } else {
                sb.append("行" + (i + 1) + "境内运费(人民币)不能为空！");
            }

            fdBusCostDetail.setExchangeRate(BigDecimal.ONE);
            fdBusCostDetail.setExchangeRateNew(BigDecimal.ONE);
            fdBusCostDetail.setCodeSsCategoriesCode("jndtlyf");
            fdBusCostDetail.setCodeSsCategoriesName("国内段包干");
            fdBusCostDetail.setCurrency("人民币");
            list.add(fdBusCostDetail);

            if (row.getCell(26) != null) {
                Cell cell = row.getCell(26);
                BigDecimal jnAmount = CellValueUtil.getCellValueBigDecimal(cell);
                if (jnAmount != null) {
                    jw.setOriginalAmount(jnAmount);
                } else {
                    jw.setOriginalAmount(BigDecimal.ZERO);
                }
            }

            if (isRowNumber) {

                if (row.getCell(27) != null) {
                    Cell cell = row.getCell(27);
                    BigDecimal exchangeRate = CellValueUtil.getCellValueBigDecimal(cell);
                    if (exchangeRate != null) {
                        jw.setExchangeRate(exchangeRate);
                        jw.setExchangeRateNew(exchangeRate);
                    } else {
                        jw.setExchangeRate(BigDecimal.ONE);
                        jw.setExchangeRateNew(BigDecimal.ONE);
                    }
                } else {
                    jw.setExchangeRate(BigDecimal.ONE);
                    jw.setExchangeRateNew(BigDecimal.ONE);
                }

                if (row.getCell(28) != null) {
                    Cell cell = row.getCell(28);
                    String currency = CellValueUtil.getCellValueString(cell);
                    if (StrUtil.isNotBlank(currency)) {
                        jw.setCurrency(currency);
                    } else {
                        jw.setCurrency("人民币");
                    }
                } else {
                    jw.setCurrency("人民币");
                }
            }else {
                jw.setExchangeRate(BigDecimal.ONE);
                jw.setExchangeRateNew(BigDecimal.ONE);
                if (row.getCell(27) != null) {
                    Cell cell = row.getCell(27);
                    String currency = CellValueUtil.getCellValueString(cell);
                    if (StrUtil.isNotBlank(currency)) {
                        jw.setCurrency(currency);
                    } else {
                        jw.setCurrency("人民币");
                    }
                } else {
                    jw.setCurrency("人民币");
                }

            }

            jw.setLocalAmount(jw.getOriginalAmount().multiply(jw.getExchangeRate()));
            jw.setCodeSsCategoriesCode("jwdtlyf");
            jw.setCodeSsCategoriesName("国外段包干");
            list.add(jw);
        }
    }

    public void getList3(Sheet sheet, int lastRowNum, FdBusCost fdBusCost, String costType, StringBuffer sb, List<FdBusCostDetail> list) {
        List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoList(null);

        for (int i = 2; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            boolean blankFlag = isRowEmpty(row);
            if (blankFlag == true) {
                continue;
            }
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            FdBusCostDetail fdBusCostDetail = new FdBusCostDetail();
            fdBusCostDetail.setCostCode(fdBusCost.getCostCode());
            fdBusCostDetail.setCostType(costType);
            if ("0".equals(costType)) {
                fdBusCostDetail.setReceiveCode(userInfo.getPlatformCode());
                fdBusCostDetail.setReceiveName(userInfo.getPlatformName());
                fdBusCostDetail.setPayCode(fdBusCost.getCustomerCode());
                fdBusCostDetail.setPayName(fdBusCost.getCustomerName());
            } else if ("1".equals(costType)) {
                fdBusCostDetail.setPayCode(userInfo.getPlatformCode());
                fdBusCostDetail.setPayName(userInfo.getPlatformName());
                Shifmanagement sel = new Shifmanagement();
                sel.setPlatformCode(fdBusCost.getPlatformCode());
                sel.setShiftId(fdBusCost.getShiftNo());
                sel.setReleaseStatus("1");
                List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
                if (CollUtil.isNotEmpty(shifmanagements) && StrUtil.isNotBlank(shifmanagements.get(0).getSharePlatformCode())) {
                    fdBusCostDetail.setReceiveCode(shifmanagements.get(0).getSharePlatformCode());
                } else {
                    fdBusCostDetail.setReceiveCode(userInfo.getSupPlatformCode());
                }
                if (StrUtil.isNotBlank(fdBusCostDetail.getReceiveCode()) && CollUtil.isNotEmpty(customerInfos)) {
                    for (CustomerInfo customerInfo : customerInfos) {
                        if (fdBusCostDetail.getReceiveCode().equals(customerInfo.getCustomerCode())) {
                            fdBusCostDetail.setReceiveName(customerInfo.getCompanyName());
                            break;
                        }
                    }
                }
            }

            if (row.getCell(8) != null) {
                row.getCell(8).setCellType(CellType.STRING);
                String containerNumber = row.getCell(8).getStringCellValue();
                if (StrUtil.isNotBlank(containerNumber)) {
                    containerNumber = containerNumber.trim();
                    boolean b = CheckUtil.verifyCntrCode(containerNumber);
                    fdBusCostDetail.setContainerNumber(containerNumber);
                    if (!b) {
                        sb.append("行" + (i + 1) + containerNumber + "箱号校验未通过！");
                    } else {
                        fdBusCostDetail.setContainerNumber(containerNumber);
                    }
                } else {
                    sb.append("行" + (i + 1) + "箱号不能为空！");
                }
            }

            fdBusCostDetail.setCurrency("人民币");
            fdBusCostDetail.setExchangeRate(BigDecimal.valueOf(1));
            fdBusCostDetail.setExchangeRateNew(BigDecimal.valueOf(1));

            if (row.getCell(10) != null) {
                Cell cell = row.getCell(10);
                BigDecimal jnAmount = CellValueUtil.getCellValueBigDecimal(cell);
                if (jnAmount != null) {
                    FdBusCostDetail jn = new FdBusCostDetail();
                    BeanUtil.copyProperties(fdBusCostDetail, jn);
                    jn.setOriginalAmount(jnAmount);
                    jn.setLocalAmount(jnAmount);
                    jn.setCodeBbCategoriesCode("f_fee_type");
                    jn.setCodeBbCategoriesName("发运运费");
                    jn.setCodeSsCategoriesCode("jndtlyf");
                    jn.setCodeSsCategoriesName("国内段包干");
                    list.add(jn);
                }

            }

            if (row.getCell(11) != null) {
                Cell cell = row.getCell(11);
                BigDecimal jnAmount = CellValueUtil.getCellValueBigDecimal(cell);
                if (jnAmount != null) {
                    FdBusCostDetail jw = new FdBusCostDetail();
                    BeanUtil.copyProperties(fdBusCostDetail, jw);
                    jw.setOriginalAmount(jnAmount);
                    jw.setLocalAmount(jnAmount);
                    jw.setCodeBbCategoriesCode("f_fee_type");
                    jw.setCodeBbCategoriesName("发运运费");
                    jw.setCodeSsCategoriesCode("jwdtlyf");
                    jw.setCodeSsCategoriesName("国外段包干");
                    list.add(jw);
                }

            }

            if (row.getCell(12) != null) {
                Cell cell = row.getCell(12);
                BigDecimal amount = CellValueUtil.getCellValueBigDecimal(cell);
                if (amount != null) {
                    FdBusCostDetail cost = new FdBusCostDetail();
                    BeanUtil.copyProperties(fdBusCostDetail, cost);
                    cost.setOriginalAmount(amount);
                    cost.setLocalAmount(amount);
                    cost.setCodeBbCategoriesCode("f_front_endservices_fee");
                    cost.setCodeBbCategoriesName("前端服务");
                    cost.setCodeSsCategoriesCode("f_containerrental_fee");
                    cost.setCodeSsCategoriesName("租箱费");
                    list.add(cost);
                }

            }

            if (row.getCell(13) != null) {
                Cell cell = row.getCell(13);
                BigDecimal amount = CellValueUtil.getCellValueBigDecimal(cell);
                if (amount != null) {
                    FdBusCostDetail cost = new FdBusCostDetail();
                    BeanUtil.copyProperties(fdBusCostDetail, cost);
                    cost.setOriginalAmount(amount);
                    cost.setLocalAmount(amount);
                    cost.setCodeBbCategoriesCode("f_back_endservices_fee");
                    cost.setCodeBbCategoriesName("后端服务");
                    cost.setCodeSsCategoriesCode("f_reardistribution_fee");
                    cost.setCodeSsCategoriesName("后程分拨费");
                    list.add(cost);
                }

            }
        }
    }

    /**
     * 功能描述: 判断是否为空行
     *
     * @param row 行对象
     * @return boolean
     * <AUTHOR> zheng
     * @date 2021/10/13
     */
    private boolean isRowEmpty(Row row) {
        if (row != null) {
            for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
                Cell cell = row.getCell(c);
                if (cell != null) {
                    cell.setCellType(CellType.STRING);
                }
                if (cell != null && !cell.getStringCellValue().equals("")) {
                    return false;//不是空行
                }
            }
        }
        return true;//是空行
    }


    @Override
    public R busCostMainList(FdBusCost fdBusCost) {
        List<FdBusCostMainDTO> mainList = new ArrayList<>();
        fdBusCost.setDeleteFlag("N");
        FdBusCost cost = fdBusCostMapper.selectFdBusCostById(fdBusCost.getId());
        extracted(mainList, cost);
        return new R<>(mainList);
    }

    public void getGroupDTO(FdBusCostMainGroupDTO groupDTO, List<FdBusCostMainGroupDTO> groupsContainerNo) {
        if (CollUtil.isEmpty(groupsContainerNo)) {
            return; // 如果列表为空，直接返回
        }

        List<String> containerNo = groupsContainerNo.stream().filter(no -> isMatch(groupDTO, no)) // 使用过滤条件
                .map(FdBusCostMainGroupDTO::getContainerNo) // 提取 containerNo
                .collect(Collectors.toList()); // 收集结果

        groupDTO.setList(containerNo);
    }

    // 判断是否匹配的单独方法
    private boolean isMatch(FdBusCostMainGroupDTO groupDTO, FdBusCostMainGroupDTO no) {
        return isEqualOrBothBlank(groupDTO.getStationCompilation(), no.getStationCompilation()) && isEqualOrBothBlank(groupDTO.getEndCompilation(), no.getEndCompilation()) && isEqualOrBothBlank(groupDTO.getEndStationName(), no.getEndStationName()) && isEqualOrBothBlank(groupDTO.getIdentification(), no.getIdentification()) && isEqualOrBothBlank(groupDTO.getContainerTypeCode(), no.getContainerTypeCode()) && isEqualOrBothBlank(groupDTO.getPortAgent(), no.getPortAgent()) && isEqualOrBothBlank(groupDTO.getAboardAgent(), no.getAboardAgent()) && isEqualOrBothBlank(groupDTO.getDestinationCountry(), no.getDestinationCountry());
    }

    // 辅助方法，判断两个字符串是否相等或同时为空
    private boolean isEqualOrBothBlank(String str1, String str2) {
        return (StrUtil.isNotBlank(str1) && StrUtil.isNotBlank(str2) && str1.equals(str2)) || (StrUtil.isBlank(str1) && StrUtil.isBlank(str2));
    }

    @Override
    public R busCostMainNum(FdBusCost fdBusCost) {
        List<FdBusCostMainGroupDTO> mainList = new ArrayList<>();
        fdBusCost.setDeleteFlag("N");
        FdBusCost cost = fdBusCostMapper.selectFdBusCostById(fdBusCost.getId());
        if (cost != null) {
            Shifmanagement sel = new Shifmanagement();
            sel.setShiftId(cost.getShiftNo());
            sel.setDeleteFlag("0");
            List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
            if (CollUtil.isNotEmpty(shifmanagements)) {
                //查询全程箱号
                List<String> wholeList = fdBusCostDetailMapper.selectWholeist(cost.getCostCode());
                if (CollUtil.isNotEmpty(wholeList)) {
                    List<FdBusCostMainGroupDTO> groups = fdBusCostDetailMapper.selectFdBusCostDetalGroup(database, cost.getCostCode(), wholeList);
                    List<FdBusCostMainGroupDTO> groupsContainerNo = fdBusCostDetailMapper.selectFdBusCostDetalGroupContainerNo(database, cost.getCostCode(), wholeList);
                    if (CollUtil.isNotEmpty(groups)) {
                        for (FdBusCostMainGroupDTO groupDTO : groups) {
                            groupDTO.setSection("全程");
                            getGroupDTO(groupDTO, groupsContainerNo);
//                            groupDTO.setList(wholeList);
                            groupDTO.setId(cost.getId());
                        }
                    }
                    mainList.addAll(groups);
                }
                //查询半程箱号
                List<String> halfList = fdBusCostDetailMapper.selectHalfList(cost.getCostCode());
                if (CollUtil.isNotEmpty(halfList)) {
                    List<FdBusCostMainGroupDTO> groups = fdBusCostDetailMapper.selectFdBusCostDetalGroup(database, cost.getCostCode(), halfList);
                    List<FdBusCostMainGroupDTO> groupsContainerNo = fdBusCostDetailMapper.selectFdBusCostDetalGroupContainerNo(database, cost.getCostCode(), halfList);
                    if (CollUtil.isNotEmpty(groups)) {
                        for (FdBusCostMainGroupDTO groupDTO : groups) {
                            groupDTO.setSection("半程");
                            getGroupDTO(groupDTO, groupsContainerNo);
//                            groupDTO.setList(halfList);
                            groupDTO.setId(cost.getId());
                        }
                    }
                    mainList.addAll(groups);
                }

                //查询其他箱号
                List<String> cancelList = fdBusCostDetailMapper.selectCancelList(cost.getCostCode());
                if (CollUtil.isNotEmpty(cancelList)) {
                    List<FdBusCostMainGroupDTO> groups = fdBusCostDetailMapper.selectFdBusCostDetalGroup(database, cost.getCostCode(), cancelList);
                    List<FdBusCostMainGroupDTO> groupsContainerNo = fdBusCostDetailMapper.selectFdBusCostDetalGroupContainerNo(database, cost.getCostCode(), cancelList);
                    if (CollUtil.isNotEmpty(groups)) {
                        for (FdBusCostMainGroupDTO groupDTO : groups) {
                            groupDTO.setSection("");
                            getGroupDTO(groupDTO, groupsContainerNo);
//                            groupDTO.setList(cancelList);
                            groupDTO.setId(cost.getId());
                        }
                    }
                    mainList.addAll(groups);
                }
            }
        }
        return new R<>(mainList);
    }

    public FdBusCostMainDTO getFdBusCostMainDTO2(FdBusCostMainGroupDTO groupDTO) {
//        log.info("1====================================" + LocalDateTime.now());
        FdBusCostMainDTO main = new FdBusCostMainDTO();
        if (groupDTO.getId() != null) {
            FdBusCost cost = fdBusCostMapper.selectFdBusCostById(groupDTO.getId());
            Shifmanagement sel = new Shifmanagement();
            sel.setShiftId(cost.getShiftNo());
            sel.setDeleteFlag("0");
            List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);

            main.setShiftNo(cost.getShiftNo());
            main.setPrincipalName(cost.getPrincipalName());
            main.setContactsName(cost.getContactsName());
            main.setContactsPhone(cost.getContactsPhone());
            if (CollUtil.isNotEmpty(shifmanagements)) {
                Shifmanagement shifmanagement = shifmanagements.get(0);
                main.setDestinationName(shifmanagement.getDestinationName());
                main.setDestination(groupDTO.getEndStationName());
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                main.setPlanShipTime(sdf.format(shifmanagement.getPlanShipTime()));
                main.setPortStation(shifmanagement.getPortStation());
                LineManagement sel2 = new LineManagement();
                sel2.setLineCode(shifmanagement.getShippingLineCode());
                sel2.setDeleteFlag("N");
//                log.info("2====================================" + LocalDateTime.now());
                List<LineManagement> lineManagements = lineManagementMapper.selectLineManagementList(sel2);
                if (CollUtil.isNotEmpty(lineManagements)) {
                    main.setAreaName(lineManagements.get(0).getAreaName());
                }
            }

            main.setPortAgent(groupDTO.getPortAgent());
            main.setAboardAgent(groupDTO.getAboardAgent());
            main.setDestinationCountry(groupDTO.getDestinationCountry());
            if ("E".equals(groupDTO.getIdentification())) {
                main.setIdentification("出口");
            } else if ("P".equals(groupDTO.getIdentification())) {
                main.setIdentification("过境");
            } else if ("I".equals(groupDTO.getIdentification())) {
                main.setIdentification("进口");
            }
            main.setSection(groupDTO.getSection());

            groupDTO.setCostCode(cost.getCostCode());
            groupDTO.setDatabase(database);

//            log.info("3====================================" + LocalDateTime.now());
            List<Integer> ids = fdBusCostDetailMapper.selectFdBusCostContainerIds(groupDTO);
            if (CollUtil.isNotEmpty(ids)) {
                //箱量信息
//                log.info("4====================================" + LocalDateTime.now());
                List<FdBusCostContainerTypeDTO> fdBusCostContainerTypeDTOS = fdBusCostDetailMapper.selectFdBusCostContainerType(ids);
                String containerDesc = "";
                if (CollUtil.isNotEmpty(fdBusCostContainerTypeDTOS)) {
                    for (FdBusCostContainerTypeDTO dto : fdBusCostContainerTypeDTOS) {
                        if (StrUtil.isNotEmpty(dto.getCoc())) {
                            if ("1".equals(dto.getCoc())) {
                                containerDesc = containerDesc + dto.getContainerTypeCode() + "/COC " + dto.getNum() + "个        ";
                            } else {
                                containerDesc = containerDesc + dto.getContainerTypeCode() + "/SOC " + dto.getNum() + "个        ";
                            }
                        }
                    }
                }
                if (StrUtil.isNotBlank(groupDTO.getSection())) {
                    BigDecimal subsidyAmount = getSubsidyAmount(ids, shifmanagements.get(0), cost.getCostCode());
                    main.setSubsidyAmount(subsidyAmount);
                } else {
                    main.setSubsidyAmount(BigDecimal.ZERO);
                }
                main.setContainerDesc(containerDesc);
                main.setCustomerLiaison(cost.getCustomerLiaison());
                main.setCustomerPhone(cost.getCustomerPhone());
                main.setFrontOperator(cost.getFrontOperator());
                main.setOnsiteOperator(cost.getOnsiteOperator());
                main.setBackendOperator(cost.getBackendOperator());
                main.setOperationsSupervisor(cost.getOperationsSupervisor());
                main.setDepartmentManager(cost.getDepartmentManager());
                main.setBusinessAccounting(cost.getBusinessAccounting());
//                log.info("5====================================" + LocalDateTime.now());
                List<FdBusCostPaymentDTO> paymentDTOS = fdBusCostDetailMapper.selectFdBusCostDetalGroupInfo(ids, database);
                if (CollUtil.isNotEmpty(paymentDTOS)) {
                    for (FdBusCostPaymentDTO paymentDTO : paymentDTOS) {
                        if (StrUtil.isEmpty(paymentDTO.getPayment())) {
                            continue;
                        }
                        if (paymentDTO.getPayment().contains("拖车")) {
                            main.setTrailer(1);
                        }
                        if (paymentDTO.getPayment().contains("报关")) {
                            main.setDeclaration(1);
                        }
                        if (paymentDTO.getPayment().contains("内贸铁路")) {
                            main.setTradeRailway(1);
                        }
                        if (paymentDTO.getPayment().contains("租箱")) {
                            main.setRentContainers(1);
                        }
                        if (paymentDTO.getPayment().contains("装箱/掏箱")) {
                            main.setPackUnload(1);
                        }
                        if (paymentDTO.getPayment().contains("加固费/调偏费")) {
                            main.setDeviationReinforce(1);
                        }
                        if (paymentDTO.getPayment().contains("仓储堆存")) {
                            main.setStorage(1);
                        }
                        if (paymentDTO.getPayment().contains("口岸")) {
                            main.setPort(1);
                        }
                        if (paymentDTO.getPayment().contains("分拨")) {
                            main.setAllocation(1);
                        }
                        if (paymentDTO.getPayment().contains("清关")) {
                            main.setClearance(1);
                        }
                        if (paymentDTO.getPayment().contains("仓储堆存")) {
                            main.setBackendStorage(1);
                        }
                    }
                    //款项合计
                    FdBusCostPaymentDTO total = new FdBusCostPaymentDTO();
                    total.setPayment("合计");
                    total.setReceiveAmount(BigDecimal.ZERO);
                    total.setPayAmount(BigDecimal.ZERO);
//                    log.info("6====================================" + LocalDateTime.now());
                    List<FdBusCostDetail> totalDTOS = fdBusCostDetailMapper.selectFdBusCostDetalGroupTotal(ids);
                    if (CollUtil.isNotEmpty(totalDTOS)) {
                        for (FdBusCostDetail totalDTO : totalDTOS) {
                            if (StrUtil.isNotEmpty(totalDTO.getCostType())) {
                                if ("0".equals(totalDTO.getCostType())) {
                                    total.setReceiveAmount(totalDTO.getLocalAmount());
                                } else if ("1".equals(totalDTO.getCostType())) {
                                    total.setPayAmount(totalDTO.getLocalAmount());
                                }
                            }
                        }
                    }
                    BigDecimal beforeGrossProfit = BigDecimal.ZERO;
                    BigDecimal afterGrossProfit = BigDecimal.ZERO;
                    beforeGrossProfit = total.getReceiveAmount().subtract(total.getPayAmount());
                    afterGrossProfit = main.getSubsidyAmount().add(beforeGrossProfit);
                    main.setBeforeGrossProfit(beforeGrossProfit);
                    main.setAfterGrossProfit(afterGrossProfit);
                    paymentDTOS.add(total);
                    main.setPayments(paymentDTOS);
                }
            }
        }
//        log.info("7====================================" + LocalDateTime.now());
        return main;
    }

    /**
     * 计算补贴金额
     *
     * @Param: fdBusCostContainerTypeDTOS, shifmanagement, platformCode
     * @Return: java.math.BigDecimal
     * @Author: zhaohr
     * @Date: 2024/07/26 14:03
     **/
    public BigDecimal getSubsidyAmount(List<Integer> ids, Shifmanagement shifmanagement, String costCode) {
        BigDecimal subsidyAmount = BigDecimal.ZERO;
        if (StrUtil.isNotBlank(shifmanagement.getShippingLineCode()) && StrUtil.isNotBlank(shifmanagement.getTrip())) {
            Shifmanagement sel = new Shifmanagement();
            sel.setShippingLineCode(shifmanagement.getShippingLineCode());
            sel.setTrip(shifmanagement.getTrip());
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            if ("G".equals(shifmanagement.getTrip())) {
                sel.setStartTime(formatter.format(shifmanagement.getPlanShipTime()));
            } else if ("R".equals(shifmanagement.getTrip())) {
                sel.setStartTime(formatter.format(shifmanagement.getPlanShipTime()));
            }
            if (StrUtil.isNotBlank(sel.getStartTime())) {
                //市补贴
                /*if (StrUtil.isNotBlank(platformCode)) {
                    sel.setPlatformCode(platformCode);
                    List<SubsidyManager> list = subsidyManagerMapper.selectSubsidyManagerListByShift(sel);
                    if (CollUtil.isNotEmpty(list)) {
                        for (FdBusCostContainerTypeDTO dto : fdBusCostContainerTypeDTOS) {
                            if ("20".equals(dto.getContainerType())) {
                                Double num = 0.5;
                                subsidyAmount = subsidyAmount.add(list.get(0).getSubsidyAmount().multiply(BigDecimal.valueOf(num)).multiply(BigDecimal.valueOf(dto.getNum())));
                            } else if ("40".equals(dto.getContainerType()) || "45".equals(dto.getContainerType())) {
                                subsidyAmount = subsidyAmount.add(list.get(0).getSubsidyAmount().multiply(BigDecimal.valueOf(dto.getNum())));
                            }
                        }
                    }
                }*/
                //省补贴
                SecruityUser userInfo = SecurityUtils.getUserInfo();
                if ("1".equals(userInfo.getPlatformLevel())) {
                    String supPlatformCode = userInfo.getSupPlatformCode();
                    if (supPlatformCode.contains("-")) {
                        supPlatformCode = supPlatformCode.split("-")[0];
                    }
                    sel.setPlatformCode(supPlatformCode);
                } else {
                    sel.setPlatformCode(userInfo.getPlatformCode());
                }
                List<FdBusCostContainerTypeDTO> fdBusCostContainerTypeDTOS = fdBusCostDetailMapper.subsidyTotal(costCode, sel.getPlatformCode(), ids);
                List<SubsidyManager> list = subsidyManagerMapper.selectSubsidyManagerListByShift(sel);
                if (CollUtil.isNotEmpty(list) && CollUtil.isNotEmpty(fdBusCostContainerTypeDTOS)) {
                    for (FdBusCostContainerTypeDTO dto : fdBusCostContainerTypeDTOS) {
                        if ("20".equals(dto.getContainerType())) {
                            Double num = 0.5;
                            subsidyAmount = subsidyAmount.add(list.get(0).getSubsidyAmount().multiply(BigDecimal.valueOf(num)).multiply(BigDecimal.valueOf(dto.getNum())));
                        } else if ("40".equals(dto.getContainerType()) || "45".equals(dto.getContainerType())) {
                            subsidyAmount = subsidyAmount.add(list.get(0).getSubsidyAmount().multiply(BigDecimal.valueOf(dto.getNum())));
                        }
                    }
                }
            }
        }
        return subsidyAmount;
    }

    public void getFdBusCostMainDTO(List<FdBusCostMainDTO> mainList, List<LineManagement> lineManagements, Shifmanagement shifmanagement, FdBusCost cost, List<String> list, String section) {
        //获取箱的业务流程分类
        List<FdBusCostMainGroupDTO> groups = fdBusCostDetailMapper.selectFdBusCostDetalGroup(database, cost.getCostCode(), list);
        List<FdBusCostMainGroupDTO> groupsContainerNo = fdBusCostDetailMapper.selectFdBusCostDetalGroupContainerNo(database, cost.getCostCode(), list);
        if (CollUtil.isNotEmpty(groups)) {
            for (FdBusCostMainGroupDTO groupDTO : groups) {
                groupDTO.setId(cost.getId());
                groupDTO.setSection(section);

                getGroupDTO(groupDTO, groupsContainerNo);
//              groupDTO.setList(list);
                FdBusCostMainDTO main = getFdBusCostMainDTO2(groupDTO);
                mainList.add(main);
                /*FdBusCostMainDTO main = new FdBusCostMainDTO();
                main.setPrincipalName(cost.getPrincipalName());
                main.setContactsName(cost.getContactsName());
                main.setContactsPhone(cost.getContactsPhone());
                if (CollUtil.isNotEmpty(lineManagements)) {
                    main.setAreaName(lineManagements.get(0).getAreaName());
                }
                main.setDestinationName(shifmanagement.getDestinationName());
                main.setDestination(shifmanagement.getDestination());
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                main.setPlanShipTime(sdf.format(shifmanagement.getPlanShipTime()));
                main.setPortStation(shifmanagement.getPortStation());
                main.setPortAgent(groupDTO.getPortAgent());
                main.setAboardAgent(groupDTO.getAboardAgent());
                main.setDestinationCountry(groupDTO.getDestinationCountry());
                if ("E".equals(groupDTO.getIdentification())) {
                    main.setIdentification("出口");
                } else if ("P".equals(groupDTO.getIdentification())) {
                    main.setIdentification("过境");
                } else if ("I".equals(groupDTO.getIdentification())) {
                    main.setIdentification("进口");
                }
                main.setSection(section);

                groupDTO.setCostCode(cost.getCostCode());
                groupDTO.setDatabase(database);
                groupDTO.setList(list);

                List<Integer> ids = fdBusCostDetailMapper.selectFdBusCostContainerIds(groupDTO);
                if (CollUtil.isNotEmpty(ids)) {
                    //箱量信息
                    List<FdBusCostContainerTypeDTO> fdBusCostContainerTypeDTOS = fdBusCostDetailMapper.selectFdBusCostContainerType(ids);
                    String containerDesc = "";
                    if (CollUtil.isNotEmpty(fdBusCostContainerTypeDTOS)) {
                        for (FdBusCostContainerTypeDTO dto : fdBusCostContainerTypeDTOS
                        ) {
                            if (StrUtil.isNotEmpty(dto.getCoc())) {
                                if ("1".equals(dto.getCoc())) {
                                    containerDesc = containerDesc + dto.getContainerTypeCode() + "/COC " + dto.getNum() + "个        ";
                                } else {
                                    containerDesc = containerDesc + dto.getContainerTypeCode() + "/SOC " + dto.getNum() + "个        ";
                                }
                            }
                        }
                    }
                    if(StrUtil.isNotBlank(groupDTO.getSection())){
                        BigDecimal subsidyAmount = getSubsidyAmount(fdBusCostContainerTypeDTOS, shifmanagement, cost.getPlatformCode());
                        main.setSubsidyAmount(subsidyAmount);
                    }else{
                        main.setSubsidyAmount(BigDecimal.ZERO);
                    }
                    main.setContainerDesc(containerDesc);
                    main.setCustomerLiaison(cost.getCustomerLiaison());
                    main.setCustomerPhone(cost.getCustomerPhone());
                    main.setFrontOperator(cost.getFrontOperator());
                    main.setOnsiteOperator(cost.getOnsiteOperator());
                    main.setBackendOperator(cost.getBackendOperator());
                    main.setOperationsSupervisor(cost.getOperationsSupervisor());
                    main.setDepartmentManager(cost.getDepartmentManager());
                    main.setBusinessAccounting(cost.getBusinessAccounting());
                    List<FdBusCostPaymentDTO> paymentDTOS = fdBusCostDetailMapper.selectFdBusCostDetalGroupInfo(ids, database);
                    if (CollUtil.isNotEmpty(paymentDTOS)) {
                        for (FdBusCostPaymentDTO paymentDTO : paymentDTOS
                        ) {
                            if (StrUtil.isEmpty(paymentDTO.getPayment())) {
                                continue;
                            }
                            if (paymentDTO.getPayment().contains("拖车")) {
                                main.setTrailer(1);
                            }
                            if (paymentDTO.getPayment().contains("报关")) {
                                main.setDeclaration(1);
                            }
                            if (paymentDTO.getPayment().contains("内贸铁路")) {
                                main.setTradeRailway(1);
                            }
                            if (paymentDTO.getPayment().contains("租箱")) {
                                main.setRentContainers(1);
                            }
                            if (paymentDTO.getPayment().contains("装箱/掏箱")) {
                                main.setPackUnload(1);
                            }
                            if (paymentDTO.getPayment().contains("加固费/调偏费")) {
                                main.setDeviationReinforce(1);
                            }
                            if (paymentDTO.getPayment().contains("仓储堆存")) {
                                main.setStorage(1);
                            }
                            if (paymentDTO.getPayment().contains("口岸")) {
                                main.setPort(1);
                            }
                            if (paymentDTO.getPayment().contains("分拨")) {
                                main.setAllocation(1);
                            }
                            if (paymentDTO.getPayment().contains("清关")) {
                                main.setClearance(1);
                            }
                            if (paymentDTO.getPayment().contains("仓储堆存")) {
                                main.setBackendStorage(1);
                            }
                        }
                        //款项合计
                        FdBusCostPaymentDTO total = new FdBusCostPaymentDTO();
                        total.setPayment("合计");
                        total.setReceiveAmount(BigDecimal.ZERO);
                        total.setPayAmount(BigDecimal.ZERO);
                        List<FdBusCostDetail> totalDTOS = fdBusCostDetailMapper.selectFdBusCostDetalGroupTotal(ids);
                        if (CollUtil.isNotEmpty(totalDTOS)) {
                            for (FdBusCostDetail totalDTO : totalDTOS
                            ) {
                                if (StrUtil.isNotEmpty(totalDTO.getCostType())) {
                                    if ("0".equals(totalDTO.getCostType())) {
                                        total.setReceiveAmount(totalDTO.getLocalAmount());
                                    } else if ("1".equals(totalDTO.getCostType())) {
                                        total.setPayAmount(totalDTO.getLocalAmount());
                                    }
                                }
                            }
                        }
                        BigDecimal beforeGrossProfit = BigDecimal.ZERO;
                        BigDecimal afterGrossProfit = BigDecimal.ZERO;
                        beforeGrossProfit = total.getReceiveAmount().subtract(total.getPayAmount());
                        afterGrossProfit = main.getSubsidyAmount().add(beforeGrossProfit);
                        main.setBeforeGrossProfit(beforeGrossProfit);
                        main.setAfterGrossProfit(afterGrossProfit);
                        paymentDTOS.add(total);
                        main.setPayments(paymentDTOS);
                    }
                    mainList.add(main);
                }*/
            }
        }
    }


    @Override
    public void exportBusProcessToPDF(FdBusCost fdBusCost, HttpServletResponse response) throws Exception {
        //样式 - 通用
        String style = "" + "width:16.66%;" + "height:32px;" + "font-weight:bold;" + "font-size:14px;" + "font-family: 'SimSun';" + "text-align:center;" + "vertical-align:middle;" + "border-top:1px solid black;" + "border-bottom:1px solid black;" + "border-left:1px solid black;" + "border-right:1px solid black;";

        //样式 - 数据
        String style_data = "" + "width:16.66%;" + "height:32px;" + "font-size:14px;" + "font-family: 'SimSun';" + "text-align:center;" + "vertical-align:middle;" + "border-top:1px solid black;" + "border-bottom:1px solid black;" + "border-left:1px solid black;" + "border-right:1px solid black;";

        //样式 - 表单标题
        String style_form_title = "" + "height:64px;" + "font-weight:bold;" + "font-size:20px;" + "font-family: 'SimSun';" + "text-align:center;" + "vertical-align:middle;";

        //样式 - 签名
        String style_sign = "" + "height:32px;" + "font-size:16px;" + "font-family: 'SimSun';" + "text-align:right;" + "vertical-align:middle;";

        //样式 - 委托人标题
        String style_data_wtr = "" + "height:32px;" + "font-weight:bold;" + "font-size:16px;" + "font-family: 'SimSun';" + "text-align:center;" + "vertical-align:middle;" + "border-top:1px solid black;" + "border-bottom:1px solid black;";

        //样式 - 口岸代理
        String style_data_portAgent = "" + "width:16.66%;" + "height:32px;" + "font-size:12px;" + "font-family: 'SimSun';" + "text-align:center;" + "vertical-align:middle;" + "border-top:1px solid black;" + "border-bottom:1px solid black;" + "border-left:1px solid black;" + "border-right:1px solid black;";

        //样式 - 间隔空行
        String style_empty = "" + "height:16px;" + "border-top:1px solid black;" + "border-bottom:1px solid black;" + "border-left:1px solid black;" + "border-right:1px solid black;";

        // 创建一个包含方框和√的字符串
        String checkboxUnchecked = "<input type=\"checkbox\" />";
        String checkboxChecked = "<input type=\"checkbox\" checked = \"checked\" />";

        //取数据
        FdBusCost fdBusCostInfo = fdBusCostMapper.selectFdBusCostById(fdBusCost.getId());
        R r = this.busCostMainList(fdBusCost);
        List<FdBusCostMainDTO> mainList = (List<FdBusCostMainDTO>) r.getData();

        Integer startIndex;
        Integer endIndex;
        //判断是否导出所有，-1为导出全部PDF并打包为ZIP下载，其他值为指定下标数据生成PDF预览
        if (fdBusCost.getPdfNum() == -1) {
            startIndex = 0;
            // 若没查出数据，默认生成1个空的PDF
            endIndex = mainList.size() > 0 ? (mainList.size() - 1) : 0;
        } else {
            //动态获取前端传入的mainList下标，生成对应下标数据的pdf。mainList无数据或下标溢出将返回空表单pdf。
            startIndex = fdBusCost.getPdfNum();
            endIndex = fdBusCost.getPdfNum();
        }
        List<File> pdfFiles = new ArrayList<>();
        for (int i = startIndex; i <= endIndex; i++) {
            FdBusCostMainDTO fdBusCostMainDTO;
            if (mainList.size() == 0 || i > (mainList.size() - 1)) {
                // 未查出数据 或 指定的下标溢出，生成空表单pdf
                fdBusCostMainDTO = new FdBusCostMainDTO();
            } else {
                fdBusCostMainDTO = mainList.get(i);
            }

            String trContent = "";
            //标题
            String row = "<tr>";
            row += "<td style=\"" + style_form_title + "\" rowspan='" + 1 + "' colspan='" + 6 + "'>" + "业务流程单" + "</td>";
            trContent += row + "</tr>";

            //第1行
            String row1 = "<tr>";
            row1 += "<td style=\"" + style + "\" rowspan='" + 2 + "' colspan='" + 1 + "'>" + "委托人" + "</td>";
            row1 += "<td style=\"" + style_data_wtr + "\" rowspan='" + 2 + "' colspan='" + 3 + "'>" + ifNull(fdBusCostMainDTO.getPrincipalName()) + "</td>";
            row1 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "联系人" + "</td>";
            row1 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getContactsName()) + "</td>";
            trContent += row1 + "</tr>";

            //第2行
            String row2 = "<tr>";
            row2 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "联系电话" + "</td>";
            row2 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getContactsPhone()) + "</td>";
            trContent += row2 + "</tr>";

            //第3行
            String row3 = "<tr>";
            row3 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "班列信息" + "</td>";
            row3 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 3 + "'>" + ifNull(fdBusCostMainDTO.getAreaName()) + "（ " + ifNull(fdBusCostMainDTO.getDestinationName()) + " 至 " + ifNull(fdBusCostMainDTO.getDestination()) + " ）" + "</td>";
            row3 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "发运日期" + "</td>";
            row3 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getPlanShipTime()) + "</td>";
            trContent += row3 + "</tr>";

            //第4行
            String row4 = "<tr>";
            row4 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "口岸站" + "</td>";
            row4 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getPortStation()) + "</td>";
            row4 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "口岸代理" + "</td>";
            row4 += "<td style=\"" + style_data_portAgent + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getPortAgent()) + "</td>";
            row4 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "境外代理" + "</td>";
            row4 += "<td style=\"" + style_data_portAgent + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getAboardAgent()) + "</td>";
            trContent += row4 + "</tr>";

            //第5行
            String row5 = "<tr>";
            row5 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "目的国" + "</td>";
            row5 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getDestinationCountry()) + "</td>";
            row5 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "委托类型" + "</td>";
            row5 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getIdentification()) + "</td>";
            row5 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "委托区段" + "</td>";
            row5 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getSection()) + "</td>";
            trContent += row5 + "</tr>";

            //第6行
            String row6 = "<tr>";
            row6 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "箱型/箱量" + "</td>";
            row6 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 5 + "'>" + ifNull(fdBusCostMainDTO.getContainerDesc()) + "</td>";
            trContent += row6 + "</tr>";

            //第7行
            String row7 = "<tr>";
            row7 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "客户对接人" + "</td>";
            row7 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 2 + "'>" + ifNull(fdBusCostMainDTO.getCustomerLiaison()) + "</td>";
            row7 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "联系方式" + "</td>";
            row7 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 2 + "'>" + ifNull(fdBusCostMainDTO.getCustomerPhone()) + "</td>";
            trContent += row7 + "</tr>";

            //第8行
            String row8 = "<tr>";
            row8 += "<td style=\"" + style + "\" rowspan='" + 2 + "' colspan='" + 1 + "'>" + "前端服务" + "</td>";
            row8 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "拖车  " + (fdBusCostMainDTO.getTrailer() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row8 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "内贸铁路  " + (fdBusCostMainDTO.getTradeRailway() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row8 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "报关  " + (fdBusCostMainDTO.getDeclaration() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row8 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "租箱  " + (fdBusCostMainDTO.getRentContainers() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row8 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "操作员" + "</td>";
            trContent += row8 + "</tr>";

            //第9行
            String row9 = "<tr>";
            row9 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "其它" + "</td>";
            row9 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 3 + "'>" + ifNull(fdBusCostMainDTO.getFrontOthers()) + "</td>";
            row9 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getFrontOperator()) + "</td>";
            trContent += row9 + "</tr>";

            //第10行
            String row10 = "<tr>";
            row10 += "<td style=\"" + style + "\" rowspan='" + 2 + "' colspan='" + 1 + "'>" + "现场操作" + "</td>";
            row10 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "装箱/掏箱  " + (fdBusCostMainDTO.getPackUnload() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row10 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "加固费/调偏费  " + (fdBusCostMainDTO.getDeviationReinforce() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row10 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "仓储堆存  " + (fdBusCostMainDTO.getStorage() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row10 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "" + "</td>";
            row10 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "操作员" + "</td>";
            trContent += row10 + "</tr>";

            //第11行
            String row11 = "<tr>";
            row11 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "其它" + "</td>";
            row11 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 3 + "'>" + ifNull(fdBusCostMainDTO.getOnsiteOthers()) + "</td>";
            row11 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getOnsiteOperator()) + "</td>";
            trContent += row11 + "</tr>";

            //第12行
            String row12 = "<tr>";
            row12 += "<td style=\"" + style + "\" rowspan='" + 2 + "' colspan='" + 1 + "'>" + "后端服务" + "</td>";
            row12 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "口岸  " + (fdBusCostMainDTO.getPort() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row12 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "分拨  " + (fdBusCostMainDTO.getAllocation() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row12 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "清关  " + (fdBusCostMainDTO.getClearance() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row12 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "仓储堆存  " + (fdBusCostMainDTO.getBackendStorage() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row12 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "操作员" + "</td>";
            trContent += row12 + "</tr>";

            //第13行
            String row13 = "<tr>";
            row13 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "其它" + "</td>";
            row13 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 3 + "'>" + ifNull(fdBusCostMainDTO.getBackendOthers()) + "</td>";
            row13 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getBackendOperator()) + "</td>";
            trContent += row13 + "</tr>";

            //第14行
            String row14 = "<tr>";
            row14 += "<td style=\"" + style_empty + "\" rowspan='" + 1 + "' colspan='" + 6 + "'>" + "" + "</td>";
            trContent += row14 + "</tr>";

            //第15行
            String row15 = "<tr>";
            row15 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "款项" + "</td>";
            row15 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "应收金额" + "</td>";
            row15 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "应收单位" + "</td>";
            row15 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "应付金额" + "</td>";
            row15 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 2 + "'>" + "应付单位" + "</td>";
            trContent += row15 + "</tr>";

            List<FdBusCostPaymentDTO> payments = fdBusCostMainDTO.getPayments();
            /*if (CollUtil.isNotEmpty(payments)) {
                for(FdBusCostPaymentDTO payment : payments) {
                    String rowx = "<tr>";
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(payment.getPayment()) + "</td>";
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(payment.getReceiveAmount().toString()) + "</td>";
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(payment.getReceiveUnit()) + "</td>";
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(payment.getPayAmount().toString()) + "</td>";
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 2 + "'>" + ifNull(payment.getPayUnit()) + "</td>";
                    trContent += rowx + "</tr>";
                }
            }*/
            if (CollUtil.isNotEmpty(payments)) {
                // Iterate through payments list
                for (int x = 0; x < payments.size(); x++) {
                    FdBusCostPaymentDTO payment = payments.get(x);
                    // Initialize row HTML
                    String rowx = "<tr>";

                    // Check if it's the first payment or if current payment is different from previous one
                    if (x == 0 || !isSamePayment(payment, payments.get(x - 1))) {
                        int rowspan = calculateRowspan(payment, payments, x);
                        // If different payment, start new row and set rowspan for payment column
                        rowx += "<td style=\"" + style_data + "\" rowspan='" + rowspan + "' colspan='" + 1 + "'>" + ifNull(payment.getPayment()) + "</td>";
                        rowx += "<td style=\"" + style_data + "\" rowspan='" + rowspan + "' colspan='" + 1 + "'>" + ifNull(payment.getReceiveAmount().toString()) + "</td>";
                        rowx += "<td style=\"" + style_data + "\" rowspan='" + rowspan + "' colspan='" + 1 + "'>" + ifNull(payment.getReceiveUnit()) + "</td>";
                    }

                    // Add other columns
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(payment.getPayAmount().toString()) + "</td>";
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 2 + "'>" + ifNull(payment.getPayUnit()) + "</td>";

                    // Close row HTML
                    rowx += "</tr>";

                    // Append row to table content
                    trContent += rowx;
                }
            }

            //第18行
            String row18 = "<tr>";
            row18 += "<td style=\"" + style_empty + "\" rowspan='" + 1 + "' colspan='" + 6 + "'>" + "" + "</td>";
            trContent += row18 + "</tr>";

            //第19行
            String row19 = "<tr>";
            row19 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "补贴前毛利" + "</td>";
            row19 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifBigDecimalNull(fdBusCostMainDTO.getBeforeGrossProfit()) + "</td>";
            row19 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "补贴金额" + "</td>";
            row19 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifBigDecimalNull(fdBusCostMainDTO.getSubsidyAmount()) + "</td>";
            row19 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "补贴后毛利" + "</td>";
            row19 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifBigDecimalNull(fdBusCostMainDTO.getAfterGrossProfit()) + "</td>";
            trContent += row19 + "</tr>";

            //第16行
            String row16 = "<tr>";
            row16 += "<td style=\"" + style_sign + "\" rowspan='" + 1 + "' colspan='" + 6 + "'>" + "" + "</td>";
            trContent += row16 + "</tr>";

            //第17行
            String row17 = "<tr>";
            row17 += "<td style=\"" + style_sign + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "操作主管：" + "</td>";
            row17 += "<td style=\"" + style_sign + "text-align:center;\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getOperationsSupervisor()) + "</td>";
            row17 += "<td style=\"" + style_sign + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "部门经理：" + "</td>";
            row17 += "<td style=\"" + style_sign + "text-align:center;\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getDepartmentManager()) + "</td>";
            row17 += "<td style=\"" + style_sign + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "商务会计：" + "</td>";
            row17 += "<td style=\"" + style_sign + "text-align:center;\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getBusinessAccounting()) + "</td>";
            trContent += row17 + "</tr>";

            String tablecontent = "<table style='width:100%;border-collapse: collapse;'>" + trContent + "</table>";
            String htmlContent = "<html><head></head><body>" + tablecontent + "</body></html>";

            // 创建Document对象
            DocumentBuilder builder = DocumentBuilderFactory.newInstance().newDocumentBuilder();
            Document doc = builder.parse(new ByteArrayInputStream(htmlContent.getBytes()));

            // 使用Flying Saucer将HTML转换为PDF输出流
            ITextRenderer renderer = new ITextRenderer();
            ITextFontResolver fontResolver = renderer.getFontResolver();
            String fontPath = fontsSimSun;
            File file = new File(fontPath);
            if (!file.exists()) {
                // 若指定路径文件不存在时直接取resouces下的文件（若报错，可能因为路径中有中文）
                fontPath = this.getClass().getResource("/fonts/SimSun.ttf").toExternalForm();
            }
//            fontPath = "file:/D:/workspace/SimSun.ttf";
            fontResolver.addFont(fontPath, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            renderer.setDocument(doc, null);
            renderer.layout();

            if (fdBusCost.getPdfNum() == -1) {
                //全部导出时，生成PDF临时文件
                String filename = (i + 1) + "、业务流程单_" + fdBusCostInfo.getShiftNo() + "_";
                File tempFile = File.createTempFile(filename, ".pdf");
                OutputStream os = new FileOutputStream(tempFile);
                try {
                    renderer.createPDF(os);
                    pdfFiles.add(tempFile);
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    os.close();
                }
            } else {
                //指定生成下标时，利用Flying Saucer实现HTML代码生成PDF文档并像前段输出文件流
                response.setContentType("application/octet-stream;charset=UTF-8");
                response.setHeader("Content-Type", "application/pdf");
                response.setHeader("Content-Disposition", "attachment;filename=" + new String(("业务流程单_" + fdBusCostInfo.getShiftNo() + "_" + (i + 1) + ".pdf").getBytes("GB2312"), "8859_1"));
                response.addHeader("Pargam", "no-cache");
                response.addHeader("Cache-Control", "no-cache");
                OutputStream out = response.getOutputStream();
                try {
                    // 将PDF写入输出流
                    renderer.createPDF(out);
                    out.flush();
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    out.close();
                }
            }
        }

        //全部导出时，将生成PDF打包为ZIP返回前端
        if (fdBusCost.getPdfNum() == -1) {
            // 生成ZIP
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ZipArchiveOutputStream zipOut = new ZipArchiveOutputStream(baos);
            try {
                for (File tempFile : pdfFiles) {
                    FileInputStream fis = new FileInputStream(tempFile);
                    try {

                        ZipArchiveEntry zipEntry = new ZipArchiveEntry(tempFile.getName());
                        zipOut.putArchiveEntry(zipEntry);
                        IOUtils.copy(fis, zipOut);
                        zipOut.closeArchiveEntry();
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        fis.close();
                    }
                }
                zipOut.finish();
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                zipOut.close();
            }

            // 推送ZIP文件的文件流
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Type", "application/zip");
            response.setHeader("Content-Disposition", "attachment; filename=" + new String(("业务流程单_" + fdBusCostInfo.getShiftNo() + "_" + System.currentTimeMillis() + ".zip").getBytes("GB2312"), "8859_1"));
            response.addHeader("Pargam", "no-cache");
            response.addHeader("Cache-Control", "no-cache");

            // 将zip文件写入HttpServletResponse的输出流
            OutputStream out = response.getOutputStream();
            try {
                out.write(baos.toByteArray());
                out.flush();
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                out.close();
            }
        }

    }

    /**
     * 获取html字符串
     *
     * @Param: null
     * @Return:
     * @Author: Howe
     * @Date: 2024/7/19 下午1:45
     **/
    public String exportHtml(FdBusCostMainDTO fdBusCostMainDTO) {

        //样式 - 通用
        String style = "" + "width:16.66%;" + "height:32px;" + "font-weight:bold;" + "font-size:14px;" + "font-family: 'SimSun';" + "text-align:center;" + "vertical-align:middle;" + "border-top:1px solid black;" + "border-bottom:1px solid black;" + "border-left:1px solid black;" + "border-right:1px solid black;";

        //样式 - 数据
        String style_data = "" + "width:16.66%;" + "height:32px;" + "font-size:14px;" + "font-family: 'SimSun';" + "text-align:center;" + "vertical-align:middle;" + "border-top:1px solid black;" + "border-bottom:1px solid black;" + "border-left:1px solid black;" + "border-right:1px solid black;";

        //样式 - 表单标题
        String style_form_title = "" + "height:64px;" + "font-weight:bold;" + "font-size:20px;" + "font-family: 'SimSun';" + "text-align:center;" + "vertical-align:middle;";

        //样式 - 签名
        String style_sign = "" + "height:32px;" + "font-size:16px;" + "font-family: 'SimSun';" + "text-align:right;" + "vertical-align:middle;";

        //样式 - 委托人标题
        String style_data_wtr = "" + "height:32px;" + "font-weight:bold;" + "font-size:16px;" + "font-family: 'SimSun';" + "text-align:center;" + "vertical-align:middle;" + "border-top:1px solid black;" + "border-bottom:1px solid black;";

        //样式 - 口岸代理
        String style_data_portAgent = "" + "width:16.66%;" + "height:32px;" + "font-size:12px;" + "font-family: 'SimSun';" + "text-align:center;" + "vertical-align:middle;" + "border-top:1px solid black;" + "border-bottom:1px solid black;" + "border-left:1px solid black;" + "border-right:1px solid black;";

        //样式 - 间隔空行
        String style_empty = "" + "height:16px;" + "border-top:1px solid black;" + "border-bottom:1px solid black;" + "border-left:1px solid black;" + "border-right:1px solid black;";

        // 创建一个包含方框和√的字符串
        String checkboxUnchecked = "<input type=\"checkbox\" />";
        String checkboxChecked = "<input type=\"checkbox\" checked = \"checked\" />";


        String trContent = "";
        //标题
        String row = "<tr>";
        row += "<td style=\"" + style_form_title + "\" rowspan='" + 1 + "' colspan='" + 6 + "'>" + "业务流程单" + "</td>";
        trContent += row + "</tr>";

        //第1行
        String row1 = "<tr>";
        row1 += "<td style=\"" + style + "\" rowspan='" + 2 + "' colspan='" + 1 + "'>" + "委托人" + "</td>";
        row1 += "<td style=\"" + style_data_wtr + "\" rowspan='" + 2 + "' colspan='" + 3 + "'>" + ifNull(fdBusCostMainDTO.getPrincipalName()) + "</td>";
        row1 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "联系人" + "</td>";
        row1 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getContactsName()) + "</td>";
        trContent += row1 + "</tr>";

        //第2行
        String row2 = "<tr>";
        row2 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "联系电话" + "</td>";
        row2 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getContactsPhone()) + "</td>";
        trContent += row2 + "</tr>";

        //第3行
        String row3 = "<tr>";
        row3 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "班列信息" + "</td>";
        row3 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 3 + "'>" + ifNull(fdBusCostMainDTO.getAreaName()) + "（ " + ifNull(fdBusCostMainDTO.getDestinationName()) + " 至 " + ifNull(fdBusCostMainDTO.getDestination()) + " ）" + "</td>";
        row3 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "发运日期" + "</td>";
        row3 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getPlanShipTime()) + "</td>";
        trContent += row3 + "</tr>";

        //第4行
        String row4 = "<tr>";
        row4 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "口岸站" + "</td>";
        row4 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getPortStation()) + "</td>";
        row4 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "口岸代理" + "</td>";
        row4 += "<td style=\"" + style_data_portAgent + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getPortAgent()) + "</td>";
        row4 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "境外代理" + "</td>";
        row4 += "<td style=\"" + style_data_portAgent + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getAboardAgent()) + "</td>";
        trContent += row4 + "</tr>";

        //第5行
        String row5 = "<tr>";
        row5 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "目的国" + "</td>";
        row5 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getDestinationCountry()) + "</td>";
        row5 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "委托类型" + "</td>";
        row5 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getIdentification()) + "</td>";
        row5 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "委托区段" + "</td>";
        row5 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getSection()) + "</td>";
        trContent += row5 + "</tr>";

        //第6行
        String row6 = "<tr>";
        row6 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "箱型/箱量" + "</td>";
        row6 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 5 + "'>" + ifNull(fdBusCostMainDTO.getContainerDesc()) + "</td>";
        trContent += row6 + "</tr>";

        //第7行
        String row7 = "<tr>";
        row7 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "客户对接人" + "</td>";
        row7 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 2 + "'>" + ifNull(fdBusCostMainDTO.getCustomerLiaison()) + "</td>";
        row7 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "联系方式" + "</td>";
        row7 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 2 + "'>" + ifNull(fdBusCostMainDTO.getCustomerPhone()) + "</td>";
        trContent += row7 + "</tr>";

        //第8行
        String row8 = "<tr>";
        row8 += "<td style=\"" + style + "\" rowspan='" + 2 + "' colspan='" + 1 + "'>" + "前端服务" + "</td>";
        row8 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "拖车  " + (fdBusCostMainDTO.getTrailer() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
        row8 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "内贸铁路  " + (fdBusCostMainDTO.getTradeRailway() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
        row8 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "报关  " + (fdBusCostMainDTO.getDeclaration() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
        row8 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "租箱  " + (fdBusCostMainDTO.getRentContainers() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
        row8 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "操作员" + "</td>";
        trContent += row8 + "</tr>";

        //第9行
        String row9 = "<tr>";
        row9 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "其它" + "</td>";
        row9 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 3 + "'>" + ifNull(fdBusCostMainDTO.getFrontOthers()) + "</td>";
        row9 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getFrontOperator()) + "</td>";
        trContent += row9 + "</tr>";

        //第10行
        String row10 = "<tr>";
        row10 += "<td style=\"" + style + "\" rowspan='" + 2 + "' colspan='" + 1 + "'>" + "现场操作" + "</td>";
        row10 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "装箱/掏箱  " + (fdBusCostMainDTO.getPackUnload() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
        row10 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "加固费/调偏费  " + (fdBusCostMainDTO.getDeviationReinforce() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
        row10 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "仓储堆存  " + (fdBusCostMainDTO.getStorage() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
        row10 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "" + "</td>";
        row10 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "操作员" + "</td>";
        trContent += row10 + "</tr>";

        //第11行
        String row11 = "<tr>";
        row11 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "其它" + "</td>";
        row11 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 3 + "'>" + ifNull(fdBusCostMainDTO.getOnsiteOthers()) + "</td>";
        row11 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getOnsiteOperator()) + "</td>";
        trContent += row11 + "</tr>";

        //第12行
        String row12 = "<tr>";
        row12 += "<td style=\"" + style + "\" rowspan='" + 2 + "' colspan='" + 1 + "'>" + "后端服务" + "</td>";
        row12 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "口岸  " + (fdBusCostMainDTO.getPort() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
        row12 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "分拨  " + (fdBusCostMainDTO.getAllocation() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
        row12 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "清关  " + (fdBusCostMainDTO.getClearance() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
        row12 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "仓储堆存  " + (fdBusCostMainDTO.getBackendStorage() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
        row12 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "操作员" + "</td>";
        trContent += row12 + "</tr>";

        //第13行
        String row13 = "<tr>";
        row13 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "其它" + "</td>";
        row13 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 3 + "'>" + ifNull(fdBusCostMainDTO.getBackendOthers()) + "</td>";
        row13 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getBackendOperator()) + "</td>";
        trContent += row13 + "</tr>";

        //第14行
        String row14 = "<tr>";
        row14 += "<td style=\"" + style_empty + "\" rowspan='" + 1 + "' colspan='" + 6 + "'>" + "" + "</td>";
        trContent += row14 + "</tr>";

        //第15行
        String row15 = "<tr>";
        row15 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "款项" + "</td>";
        row15 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "应收金额" + "</td>";
        row15 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "应收单位" + "</td>";
        row15 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "应付金额" + "</td>";
        row15 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 2 + "'>" + "应付单位" + "</td>";
        trContent += row15 + "</tr>";

        List<FdBusCostPaymentDTO> payments = fdBusCostMainDTO.getPayments();
            /*if (CollUtil.isNotEmpty(payments)) {
                for(FdBusCostPaymentDTO payment : payments) {
                    String rowx = "<tr>";
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(payment.getPayment()) + "</td>";
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(payment.getReceiveAmount().toString()) + "</td>";
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(payment.getReceiveUnit()) + "</td>";
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(payment.getPayAmount().toString()) + "</td>";
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 2 + "'>" + ifNull(payment.getPayUnit()) + "</td>";
                    trContent += rowx + "</tr>";
                }
            }*/
        if (CollUtil.isNotEmpty(payments)) {
            // Iterate through payments list
            for (int x = 0; x < payments.size(); x++) {
                FdBusCostPaymentDTO payment = payments.get(x);
                // Initialize row HTML
                String rowx = "<tr>";

                // Check if it's the first payment or if current payment is different from previous one
                if (x == 0 || !isSamePayment(payment, payments.get(x - 1))) {
                    int rowspan = calculateRowspan(payment, payments, x);
                    // If different payment, start new row and set rowspan for payment column
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + rowspan + "' colspan='" + 1 + "'>" + ifNull(payment.getPayment()) + "</td>";
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + rowspan + "' colspan='" + 1 + "'>" + ifNull(payment.getReceiveAmount().toString()) + "</td>";
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + rowspan + "' colspan='" + 1 + "'>" + ifNull(payment.getReceiveUnit()) + "</td>";
                }

                // Add other columns
                rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(payment.getPayAmount().toString()) + "</td>";
                rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 2 + "'>" + ifNull(payment.getPayUnit()) + "</td>";

                // Close row HTML
                rowx += "</tr>";

                // Append row to table content
                trContent += rowx;
            }
        }

        //第18行
        String row18 = "<tr>";
        row18 += "<td style=\"" + style_empty + "\" rowspan='" + 1 + "' colspan='" + 6 + "'>" + "" + "</td>";
        trContent += row18 + "</tr>";

        //第19行
        String row19 = "<tr>";
        row19 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "补贴前毛利" + "</td>";
        row19 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifBigDecimalNull(fdBusCostMainDTO.getBeforeGrossProfit()) + "</td>";
        row19 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "补贴金额" + "</td>";
        row19 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifBigDecimalNull(fdBusCostMainDTO.getSubsidyAmount()) + "</td>";
        row19 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "补贴后毛利" + "</td>";
        row19 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifBigDecimalNull(fdBusCostMainDTO.getAfterGrossProfit()) + "</td>";
        trContent += row19 + "</tr>";

        //第16行
        String row16 = "<tr>";
        row16 += "<td style=\"" + style_sign + "\" rowspan='" + 1 + "' colspan='" + 6 + "'>" + "" + "</td>";
        trContent += row16 + "</tr>";

        //第17行
        String row17 = "<tr>";
        row17 += "<td style=\"" + style_sign + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "操作主管：" + "</td>";
        row17 += "<td style=\"" + style_sign + "text-align:center;\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getOperationsSupervisor()) + "</td>";
        row17 += "<td style=\"" + style_sign + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "部门经理：" + "</td>";
        row17 += "<td style=\"" + style_sign + "text-align:center;\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getDepartmentManager()) + "</td>";
        row17 += "<td style=\"" + style_sign + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "商务会计：" + "</td>";
        row17 += "<td style=\"" + style_sign + "text-align:center;\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getBusinessAccounting()) + "</td>";
        trContent += row17 + "</tr>";

        String tablecontent = "<table style='width:100%;border-collapse: collapse;'>" + trContent + "</table>";
        String htmlContent = "<html><head></head><body>" + tablecontent + "</body></html>";

        return htmlContent;


    }

    /**
     * 导出一个图片字符串
     *
     * @Param: fdBusCostMainGroupDTO
     * @Return: java.lang.String
     * @Author: Howe
     * @Date: 2024/7/19 下午3:04
     **/
    @Override
    public String exportBusProcessToPng(FdBusCostMainGroupDTO fdBusCostMainGroupDTO) {


        //取数据
        FdBusCostMainDTO fdBusCostMainDTO = getFdBusCostMainDTO2(fdBusCostMainGroupDTO);
        if (fdBusCostMainDTO == null) {
            fdBusCostMainDTO = new FdBusCostMainDTO();
        }
        String htmlContent = exportHtml(fdBusCostMainDTO);
        try {

            HtmlParser htmlParser = new HtmlParserImpl();
            htmlParser.loadHtml(htmlContent);
            // 渲染HTML为图片，这里假设有一个renderHtmlAsImage方法
            ImageRenderer imageRenderer = new ImageRendererImpl(htmlParser);
            BufferedImage image = imageRenderer.getBufferedImage();
            // 设置图像上的字体为支持中文的字体
            Graphics2D g2 = image.createGraphics();
            g2.setFont(new java.awt.Font("SimSun", java.awt.Font.PLAIN, 12));
            // 将图片转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "png", baos);
            byte[] imageBytes = baos.toByteArray();
            return Base64.getEncoder().encodeToString(imageBytes);
        } catch (Exception e) {
            // 处理异常
            e.printStackTrace();
        }

        return null;
    }


    @Override
    public void exportBusProcessToPDF2(FdBusCostMainGroupDTO fdBusCostMainGroupDTO, HttpServletResponse response) throws Exception {
        //取数据
        FdBusCostMainDTO fdBusCostMainDTO = getFdBusCostMainDTO2(fdBusCostMainGroupDTO);
        if (fdBusCostMainDTO == null) {
            fdBusCostMainDTO = new FdBusCostMainDTO();
        }
        String htmlContent = exportHtml(fdBusCostMainDTO);


        // 创建Document对象
        DocumentBuilder builder = DocumentBuilderFactory.newInstance().newDocumentBuilder();
        Document doc = builder.parse(new ByteArrayInputStream(htmlContent.getBytes()));

        // 使用Flying Saucer将HTML转换为PDF输出流
        ITextRenderer renderer = new ITextRenderer();
        ITextFontResolver fontResolver = renderer.getFontResolver();
        String fontPath = fontsSimSun;
        File file = new File(fontPath);
        if (!file.exists()) {
            // 若指定路径文件不存在时直接取resouces下的文件（若报错，可能因为路径中有中文）
            fontPath = this.getClass().getResource("/fonts/SimSun.ttf").toExternalForm();
        }
//        fontPath = "file:/D:/workspace/SimSun.ttf";
        fontResolver.addFont(fontPath, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        renderer.setDocument(doc, null);
        renderer.layout();

        //指定生成下标时，利用Flying Saucer实现HTML代码生成PDF文档并像前段输出文件流
        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type", "application/pdf");
        if (fdBusCostMainGroupDTO.getPdfNum() != null && StrUtil.isNotEmpty(fdBusCostMainDTO.getShiftNo())) {
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(("业务流程单_" + fdBusCostMainDTO.getShiftNo() + "_" + (fdBusCostMainGroupDTO.getPdfNum() + 1) + ".pdf").getBytes("GB2312"), "8859_1"));
        }

        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        try {
            // 将PDF写入输出流
            renderer.createPDF(out);
            out.flush();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            out.close();
        }


    }

    @Override
    public R busCostMainListShift(FdBusCost fdBusCost) {
        List<FdBusCostMainDTO> mainList = new ArrayList<>();
        fdBusCost.setDeleteFlag("N");
        List<FdBusCost> list = fdBusCostMapper.selectFdBusCostList(fdBusCost);
        if (CollUtil.isNotEmpty(list)) {
            for (FdBusCost cost : list) {
                extracted(mainList, cost);
            }
        } else {
            return new R<>(new Throwable("该班次不存在"));
        }

        return new R<>(mainList);
    }

    @Override
    public R busCostMainNumShift(FdBusCost fdBusCost) {
        List<FdBusCostMainGroupDTO> mainList = new ArrayList<>();
        fdBusCost.setDeleteFlag("N");
        List<FdBusCost> list = fdBusCostMapper.selectFdBusCostList(fdBusCost);
        if (CollUtil.isNotEmpty(list)) {
            for (FdBusCost cost : list) {
                if (cost != null) {
                    Shifmanagement sel = new Shifmanagement();
                    sel.setShiftId(cost.getShiftNo());
                    sel.setDeleteFlag("0");
                    List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
                    if (CollUtil.isNotEmpty(shifmanagements)) {
                        //查询全程箱号
                        List<String> wholeList = fdBusCostDetailMapper.selectWholeist(cost.getCostCode());
                        if (CollUtil.isNotEmpty(wholeList)) {
                            List<FdBusCostMainGroupDTO> groups = fdBusCostDetailMapper.selectFdBusCostDetalGroup(database, cost.getCostCode(), wholeList);
                            List<FdBusCostMainGroupDTO> groupsContainerNo = fdBusCostDetailMapper.selectFdBusCostDetalGroupContainerNo(database, cost.getCostCode(), wholeList);
                            if (CollUtil.isNotEmpty(groups)) {
                                for (FdBusCostMainGroupDTO groupDTO : groups) {
                                    groupDTO.setSection("全程");

                                    getGroupDTO(groupDTO, groupsContainerNo);
//                            groupDTO.setList(wholeList);
                                    groupDTO.setId(cost.getId());
                                }
                            }
                            mainList.addAll(groups);
                        }
                        //查询半程箱号
                        List<String> halfList = fdBusCostDetailMapper.selectHalfList(cost.getCostCode());
                        if (CollUtil.isNotEmpty(halfList)) {
                            List<FdBusCostMainGroupDTO> groups = fdBusCostDetailMapper.selectFdBusCostDetalGroup(database, cost.getCostCode(), halfList);
                            List<FdBusCostMainGroupDTO> groupsContainerNo = fdBusCostDetailMapper.selectFdBusCostDetalGroupContainerNo(database, cost.getCostCode(), halfList);
                            if (CollUtil.isNotEmpty(groups)) {
                                for (FdBusCostMainGroupDTO groupDTO : groups) {
                                    groupDTO.setSection("半程");
                                    getGroupDTO(groupDTO, groupsContainerNo);
//                            groupDTO.setList(halfList);
                                    groupDTO.setId(cost.getId());
                                }
                            }
                            mainList.addAll(groups);
                        }

                        //查询其他箱号
                        List<String> cancelList = fdBusCostDetailMapper.selectCancelList(cost.getCostCode());
                        if (CollUtil.isNotEmpty(cancelList)) {
                            List<FdBusCostMainGroupDTO> groups = fdBusCostDetailMapper.selectFdBusCostDetalGroup(database, cost.getCostCode(), cancelList);
                            List<FdBusCostMainGroupDTO> groupsContainerNo = fdBusCostDetailMapper.selectFdBusCostDetalGroupContainerNo(database, cost.getCostCode(), cancelList);
                            if (CollUtil.isNotEmpty(groups)) {
                                for (FdBusCostMainGroupDTO groupDTO : groups) {
                                    groupDTO.setSection("");
                                    getGroupDTO(groupDTO, groupsContainerNo);
//                            groupDTO.setList(cancelList);
                                    groupDTO.setId(cost.getId());
                                }
                            }
                            mainList.addAll(groups);
                        }
                    }
                }
            }
        } else {
            return new R<>(new Throwable("该班次不存在"));
        }

        return new R<>(mainList);
    }

    private void extracted(List<FdBusCostMainDTO> mainList, FdBusCost cost) {
        if (cost != null) {
            Shifmanagement sel = new Shifmanagement();
            sel.setShiftId(cost.getShiftNo());
            sel.setDeleteFlag("0");
            List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
            if (CollUtil.isNotEmpty(shifmanagements)) {
                LineManagement sel2 = new LineManagement();
                sel2.setLineCode(shifmanagements.get(0).getShippingLineCode());
                sel2.setDeleteFlag("N");
                List<LineManagement> lineManagements = lineManagementMapper.selectLineManagementList(sel2);
                //查询全程箱号
                List<String> wholeList = fdBusCostDetailMapper.selectWholeist(cost.getCostCode());
                if (CollUtil.isNotEmpty(wholeList)) {
                    getFdBusCostMainDTO(mainList, lineManagements, shifmanagements.get(0), cost, wholeList, "全程");
                }
                //查询半程箱号
                List<String> halfList = fdBusCostDetailMapper.selectHalfList(cost.getCostCode());
                if (CollUtil.isNotEmpty(halfList)) {
                    getFdBusCostMainDTO(mainList, lineManagements, shifmanagements.get(0), cost, halfList, "半程");
                }

                //查询其他箱号
                List<String> cancelList = fdBusCostDetailMapper.selectCancelList(cost.getCostCode());
                if (CollUtil.isNotEmpty(cancelList)) {
                    getFdBusCostMainDTO(mainList, lineManagements, shifmanagements.get(0), cost, cancelList, "");
                }
            }
        }
    }

    @Override
    public void exportBusProcessShiftToPDF(FdBusCost fdBusCost, HttpServletResponse response) throws Exception {
        //样式 - 通用
        String style = "" + "width:16.66%;" + "height:32px;" + "font-weight:bold;" + "font-size:14px;" + "font-family: 'SimSun';" + "text-align:center;" + "vertical-align:middle;" + "border-top:1px solid black;" + "border-bottom:1px solid black;" + "border-left:1px solid black;" + "border-right:1px solid black;";

        //样式 - 数据
        String style_data = "" + "width:16.66%;" + "height:32px;" + "font-size:14px;" + "font-family: 'SimSun';" + "text-align:center;" + "vertical-align:middle;" + "border-top:1px solid black;" + "border-bottom:1px solid black;" + "border-left:1px solid black;" + "border-right:1px solid black;";

        //样式 - 表单标题
        String style_form_title = "" + "height:64px;" + "font-weight:bold;" + "font-size:20px;" + "font-family: 'SimSun';" + "text-align:center;" + "vertical-align:middle;";

        //样式 - 签名
        String style_sign = "" + "height:32px;" + "font-size:16px;" + "font-family: 'SimSun';" + "text-align:right;" + "vertical-align:middle;";

        //样式 - 委托人标题
        String style_data_wtr = "" + "height:32px;" + "font-weight:bold;" + "font-size:16px;" + "font-family: 'SimSun';" + "text-align:center;" + "vertical-align:middle;" + "border-top:1px solid black;" + "border-bottom:1px solid black;";

        //样式 - 口岸代理
        String style_data_portAgent = "" + "width:16.66%;" + "height:32px;" + "font-size:12px;" + "font-family: 'SimSun';" + "text-align:center;" + "vertical-align:middle;" + "border-top:1px solid black;" + "border-bottom:1px solid black;" + "border-left:1px solid black;" + "border-right:1px solid black;";

        //样式 - 间隔空行
        String style_empty = "" + "height:16px;" + "border-top:1px solid black;" + "border-bottom:1px solid black;" + "border-left:1px solid black;" + "border-right:1px solid black;";

        // 创建一个包含方框和√的字符串
        String checkboxUnchecked = "<input type=\"checkbox\" />";
        String checkboxChecked = "<input type=\"checkbox\" checked = \"checked\" />";

        //取数据
        fdBusCost.setDeleteFlag("N");
        List<FdBusCost> list = fdBusCostMapper.selectFdBusCostList(fdBusCost);
        if (CollUtil.isEmpty(list)) {
            return;
        }
        R r = this.busCostMainListShift(fdBusCost);
        List<FdBusCostMainDTO> mainList = (List<FdBusCostMainDTO>) r.getData();

        Integer startIndex;
        Integer endIndex;
        //判断是否导出所有，-1为导出全部PDF并打包为ZIP下载，其他值为指定下标数据生成PDF预览
        if (fdBusCost.getPdfNum() == -1) {
            startIndex = 0;
            // 若没查出数据，默认生成1个空的PDF
            endIndex = mainList.size() > 0 ? (mainList.size() - 1) : 0;
        } else {
            //动态获取前端传入的mainList下标，生成对应下标数据的pdf。mainList无数据或下标溢出将返回空表单pdf。
            startIndex = fdBusCost.getPdfNum();
            endIndex = fdBusCost.getPdfNum();
        }
        List<File> pdfFiles = new ArrayList<>();
        for (int i = startIndex; i <= endIndex; i++) {
            FdBusCostMainDTO fdBusCostMainDTO;
            if (mainList.size() == 0 || i > (mainList.size() - 1)) {
                // 未查出数据 或 指定的下标溢出，生成空表单pdf
                fdBusCostMainDTO = new FdBusCostMainDTO();
            } else {
                fdBusCostMainDTO = mainList.get(i);
            }

            String trContent = "";
            //标题
            String row = "<tr>";
            row += "<td style=\"" + style_form_title + "\" rowspan='" + 1 + "' colspan='" + 6 + "'>" + "业务流程单" + "</td>";
            trContent += row + "</tr>";

            //第1行
            String row1 = "<tr>";
            row1 += "<td style=\"" + style + "\" rowspan='" + 2 + "' colspan='" + 1 + "'>" + "委托人" + "</td>";
            row1 += "<td style=\"" + style_data_wtr + "\" rowspan='" + 2 + "' colspan='" + 3 + "'>" + ifNull(fdBusCostMainDTO.getPrincipalName()) + "</td>";
            row1 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "联系人" + "</td>";
            row1 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getContactsName()) + "</td>";
            trContent += row1 + "</tr>";

            //第2行
            String row2 = "<tr>";
            row2 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "联系电话" + "</td>";
            row2 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getContactsPhone()) + "</td>";
            trContent += row2 + "</tr>";

            //第3行
            String row3 = "<tr>";
            row3 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "班列信息" + "</td>";
            row3 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 3 + "'>" + ifNull(fdBusCostMainDTO.getAreaName()) + "（ " + ifNull(fdBusCostMainDTO.getDestinationName()) + " 至 " + ifNull(fdBusCostMainDTO.getDestination()) + " ）" + "</td>";
            row3 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "发运日期" + "</td>";
            row3 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getPlanShipTime()) + "</td>";
            trContent += row3 + "</tr>";

            //第4行
            String row4 = "<tr>";
            row4 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "口岸站" + "</td>";
            row4 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getPortStation()) + "</td>";
            row4 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "口岸代理" + "</td>";
            row4 += "<td style=\"" + style_data_portAgent + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getPortAgent()) + "</td>";
            row4 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "境外代理" + "</td>";
            row4 += "<td style=\"" + style_data_portAgent + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getAboardAgent()) + "</td>";
            trContent += row4 + "</tr>";

            //第5行
            String row5 = "<tr>";
            row5 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "目的国" + "</td>";
            row5 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getDestinationCountry()) + "</td>";
            row5 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "委托类型" + "</td>";
            row5 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getIdentification()) + "</td>";
            row5 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "委托区段" + "</td>";
            row5 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getSection()) + "</td>";
            trContent += row5 + "</tr>";

            //第6行
            String row6 = "<tr>";
            row6 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "箱型/箱量" + "</td>";
            row6 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 5 + "'>" + ifNull(fdBusCostMainDTO.getContainerDesc()) + "</td>";
            trContent += row6 + "</tr>";

            //第7行
            String row7 = "<tr>";
            row7 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "客户对接人" + "</td>";
            row7 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 2 + "'>" + ifNull(fdBusCostMainDTO.getCustomerLiaison()) + "</td>";
            row7 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "联系方式" + "</td>";
            row7 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 2 + "'>" + ifNull(fdBusCostMainDTO.getCustomerPhone()) + "</td>";
            trContent += row7 + "</tr>";

            //第8行
            String row8 = "<tr>";
            row8 += "<td style=\"" + style + "\" rowspan='" + 2 + "' colspan='" + 1 + "'>" + "前端服务" + "</td>";
            row8 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "拖车  " + (fdBusCostMainDTO.getTrailer() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row8 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "内贸铁路  " + (fdBusCostMainDTO.getTradeRailway() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row8 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "报关  " + (fdBusCostMainDTO.getDeclaration() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row8 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "租箱  " + (fdBusCostMainDTO.getRentContainers() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row8 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "操作员" + "</td>";
            trContent += row8 + "</tr>";

            //第9行
            String row9 = "<tr>";
            row9 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "其它" + "</td>";
            row9 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 3 + "'>" + ifNull(fdBusCostMainDTO.getFrontOthers()) + "</td>";
            row9 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getFrontOperator()) + "</td>";
            trContent += row9 + "</tr>";

            //第10行
            String row10 = "<tr>";
            row10 += "<td style=\"" + style + "\" rowspan='" + 2 + "' colspan='" + 1 + "'>" + "现场操作" + "</td>";
            row10 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "装箱/掏箱  " + (fdBusCostMainDTO.getPackUnload() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row10 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "调偏/加固  " + (fdBusCostMainDTO.getDeviationReinforce() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row10 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "仓储堆存  " + (fdBusCostMainDTO.getStorage() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row10 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "" + "</td>";
            row10 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "操作员" + "</td>";
            trContent += row10 + "</tr>";

            //第11行
            String row11 = "<tr>";
            row11 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "其它" + "</td>";
            row11 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 3 + "'>" + ifNull(fdBusCostMainDTO.getOnsiteOthers()) + "</td>";
            row11 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getOnsiteOperator()) + "</td>";
            trContent += row11 + "</tr>";

            //第12行
            String row12 = "<tr>";
            row12 += "<td style=\"" + style + "\" rowspan='" + 2 + "' colspan='" + 1 + "'>" + "后端服务" + "</td>";
            row12 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "口岸  " + (fdBusCostMainDTO.getPort() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row12 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "分拨  " + (fdBusCostMainDTO.getAllocation() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row12 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "清关  " + (fdBusCostMainDTO.getClearance() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row12 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "仓储堆存  " + (fdBusCostMainDTO.getBackendStorage() == 1 ? checkboxChecked : checkboxUnchecked) + "</td>";
            row12 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "操作员" + "</td>";
            trContent += row12 + "</tr>";

            //第13行
            String row13 = "<tr>";
            row13 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "其它" + "</td>";
            row13 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 3 + "'>" + ifNull(fdBusCostMainDTO.getBackendOthers()) + "</td>";
            row13 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getBackendOperator()) + "</td>";
            trContent += row13 + "</tr>";

            //第14行
            String row14 = "<tr>";
            row14 += "<td style=\"" + style_empty + "\" rowspan='" + 1 + "' colspan='" + 6 + "'>" + "" + "</td>";
            trContent += row14 + "</tr>";

            //第15行
            String row15 = "<tr>";
            row15 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "款项" + "</td>";
            row15 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "应收金额" + "</td>";
            row15 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "应收单位" + "</td>";
            row15 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "应付金额" + "</td>";
            row15 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 2 + "'>" + "应付单位" + "</td>";
            trContent += row15 + "</tr>";

            List<FdBusCostPaymentDTO> payments = fdBusCostMainDTO.getPayments();
            /*if (CollUtil.isNotEmpty(payments)) {
                for(FdBusCostPaymentDTO payment : payments) {
                    String rowx = "<tr>";
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(payment.getPayment()) + "</td>";
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(payment.getReceiveAmount().toString()) + "</td>";
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(payment.getReceiveUnit()) + "</td>";
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(payment.getPayAmount().toString()) + "</td>";
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 2 + "'>" + ifNull(payment.getPayUnit()) + "</td>";
                    trContent += rowx + "</tr>";
                }
            }*/
            if (CollUtil.isNotEmpty(payments)) {
                // Iterate through payments list
                for (int x = 0; x < payments.size(); x++) {
                    FdBusCostPaymentDTO payment = payments.get(x);
                    // Initialize row HTML
                    String rowx = "<tr>";

                    // Check if it's the first payment or if current payment is different from previous one
                    if (x == 0 || !isSamePayment(payment, payments.get(x - 1))) {
                        int rowspan = calculateRowspan(payment, payments, x);
                        // If different payment, start new row and set rowspan for payment column
                        rowx += "<td style=\"" + style_data + "\" rowspan='" + rowspan + "' colspan='" + 1 + "'>" + ifNull(payment.getPayment()) + "</td>";
                        rowx += "<td style=\"" + style_data + "\" rowspan='" + rowspan + "' colspan='" + 1 + "'>" + ifNull(payment.getReceiveAmount().toString()) + "</td>";
                        rowx += "<td style=\"" + style_data + "\" rowspan='" + rowspan + "' colspan='" + 1 + "'>" + ifNull(payment.getReceiveUnit()) + "</td>";
                    }

                    // Add other columns
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(payment.getPayAmount().toString()) + "</td>";
                    rowx += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 2 + "'>" + ifNull(payment.getPayUnit()) + "</td>";

                    // Close row HTML
                    rowx += "</tr>";

                    // Append row to table content
                    trContent += rowx;
                }
            }

            //第18行
            String row18 = "<tr>";
            row18 += "<td style=\"" + style_empty + "\" rowspan='" + 1 + "' colspan='" + 6 + "'>" + "" + "</td>";
            trContent += row18 + "</tr>";

            //第19行
            String row19 = "<tr>";
            row19 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "补贴前毛利" + "</td>";
            row19 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifBigDecimalNull(fdBusCostMainDTO.getBeforeGrossProfit()) + "</td>";
            row19 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "补贴金额" + "</td>";
            row19 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifBigDecimalNull(fdBusCostMainDTO.getSubsidyAmount()) + "</td>";
            row19 += "<td style=\"" + style + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "补贴后毛利" + "</td>";
            row19 += "<td style=\"" + style_data + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifBigDecimalNull(fdBusCostMainDTO.getAfterGrossProfit()) + "</td>";
            trContent += row19 + "</tr>";

            //第16行
            String row16 = "<tr>";
            row16 += "<td style=\"" + style_sign + "\" rowspan='" + 1 + "' colspan='" + 6 + "'>" + "" + "</td>";
            trContent += row16 + "</tr>";

            //第17行
            String row17 = "<tr>";
            row17 += "<td style=\"" + style_sign + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "操作主管：" + "</td>";
            row17 += "<td style=\"" + style_sign + "text-align:center;\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getOperationsSupervisor()) + "</td>";
            row17 += "<td style=\"" + style_sign + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "部门经理：" + "</td>";
            row17 += "<td style=\"" + style_sign + "text-align:center;\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getDepartmentManager()) + "</td>";
            row17 += "<td style=\"" + style_sign + "\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + "商务会计：" + "</td>";
            row17 += "<td style=\"" + style_sign + "text-align:center;\" rowspan='" + 1 + "' colspan='" + 1 + "'>" + ifNull(fdBusCostMainDTO.getBusinessAccounting()) + "</td>";
            trContent += row17 + "</tr>";

            String tablecontent = "<table style='width:100%;border-collapse: collapse;'>" + trContent + "</table>";
            String htmlContent = "<html><head></head><body>" + tablecontent + "</body></html>";

            // 创建Document对象
            DocumentBuilder builder = DocumentBuilderFactory.newInstance().newDocumentBuilder();
            Document doc = builder.parse(new ByteArrayInputStream(htmlContent.getBytes()));

            // 使用Flying Saucer将HTML转换为PDF输出流
            ITextRenderer renderer = new ITextRenderer();
            ITextFontResolver fontResolver = renderer.getFontResolver();
            String fontPath = fontsSimSun;
            File file = new File(fontPath);
            if (!file.exists()) {
                // 若指定路径文件不存在时直接取resouces下的文件（若报错，可能因为路径中有中文）
                fontPath = this.getClass().getResource("/fonts/SimSun.ttf").toExternalForm();
            }
//            fontPath = "file:/D:/workspace/SimSun.ttf";
            fontResolver.addFont(fontPath, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            renderer.setDocument(doc, null);
            renderer.layout();

            if (fdBusCost.getPdfNum() == -1) {
                //全部导出时，生成PDF临时文件
                String filename = (i + 1) + "、业务流程单_" + list.get(0).getShiftNo() + "_";
                File tempFile = File.createTempFile(filename, ".pdf");
                OutputStream os = new FileOutputStream(tempFile);
                try {
                    renderer.createPDF(os);
                    pdfFiles.add(tempFile);
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    os.close();
                }
            } else {
                //指定生成下标时，利用Flying Saucer实现HTML代码生成PDF文档并像前段输出文件流
                response.setContentType("application/octet-stream;charset=UTF-8");
                response.setHeader("Content-Type", "application/pdf");
                response.setHeader("Content-Disposition", "attachment;filename=" + new String(("业务流程单_" + list.get(0).getShiftNo() + "_" + (i + 1) + ".pdf").getBytes("GB2312"), "8859_1"));
                response.addHeader("Pargam", "no-cache");
                response.addHeader("Cache-Control", "no-cache");
                OutputStream out = response.getOutputStream();
                try {
                    // 将PDF写入输出流
                    renderer.createPDF(out);
                    out.flush();
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    out.close();
                }
            }
        }

        //全部导出时，将生成PDF打包为ZIP返回前端
        if (fdBusCost.getPdfNum() == -1) {
            // 生成ZIP
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ZipArchiveOutputStream zipOut = new ZipArchiveOutputStream(baos);
            try {
                for (File tempFile : pdfFiles) {
                    FileInputStream fis = new FileInputStream(tempFile);
                    try {

                        ZipArchiveEntry zipEntry = new ZipArchiveEntry(tempFile.getName());
                        zipOut.putArchiveEntry(zipEntry);
                        IOUtils.copy(fis, zipOut);
                        zipOut.closeArchiveEntry();
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        fis.close();
                    }
                }
                zipOut.finish();
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                zipOut.close();
            }

            // 推送ZIP文件的文件流
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Type", "application/zip");
            response.setHeader("Content-Disposition", "attachment; filename=" + new String(("业务流程单_" + list.get(0).getShiftNo() + "_" + System.currentTimeMillis() + ".zip").getBytes("GB2312"), "8859_1"));
            response.addHeader("Pargam", "no-cache");
            response.addHeader("Cache-Control", "no-cache");

            // 将zip文件写入HttpServletResponse的输出流
            OutputStream out = response.getOutputStream();
            try {
                out.write(baos.toByteArray());
                out.flush();
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                out.close();
            }
        }

    }


    // Function to check if two payments are the same based on your criteria
    private boolean isSamePayment(FdBusCostPaymentDTO payment1, FdBusCostPaymentDTO payment2) {
        return payment1.getPayment().equals(payment2.getPayment()) && payment1.getReceiveAmount().equals(payment2.getReceiveAmount()) && payment1.getReceiveUnit().equals(payment2.getReceiveUnit());
    }

    // Function to calculate rowspan for the payment column
    private int calculateRowspan(FdBusCostPaymentDTO currentPayment, List<FdBusCostPaymentDTO> payments, int currentIndex) {
        int rowspan = 1;
        // Count number of consecutive same payments starting from currentIndex
        for (int i = currentIndex + 1; i < payments.size(); i++) {
            if (isSamePayment(currentPayment, payments.get(i))) {
                rowspan++;
            } else {
                // Stop counting if payment changes
                break;
            }
        }
        return rowspan;
    }

    private String ifNull(String str) {
        if (str == null) {
            return "";
        }
        return str;
    }

    private String ifBigDecimalNull(BigDecimal num) {
        if (num == null) {
            return "0.00";
        }
        return String.valueOf(num.setScale(2, RoundingMode.HALF_UP));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveBookingAndWaybillForCity(RequesheaderDTO header) throws Exception {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        BookingRequesheader bookingRequesheader = new BookingRequesheader();
        bookingRequesheader.setBookingCustcode(header.getBookingCustcode());
        bookingRequesheader.setBookingCustname(header.getBookingCustname());
        bookingRequesheader.setShiftNo(header.getShiftNo());
        bookingRequesheader.setRemarks(header.getRemarks());
        bookingRequesheader.setResveredField02(userInfo.getPlatformCode());
        bookingRequesheader.setResveredField06(header.getResveredField06());
        if (header.getDetails() == null) {
            throw new RuntimeException("请检查所填信息有遗漏！");
        }
        Shifmanagement shif = new Shifmanagement();
        shif.setShiftId(header.getShiftNo());
        shif.setPlatformCode(userInfo.getPlatformCode());
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementListByLike(shif);
        //没有申请单号，须新增主子表信息
        String appNo = sysNoConfigService.genNo("SQ");
        float num = 0f;//上报舱位数
        List<BookingRequesdetail> detailList = new ArrayList<>();
        for (RequesdetailDTO rdd : header.getDetails()) {
            BookingRequesdetail details = new BookingRequesdetail();

            String containerNo = rdd.getContainerNo();
            WaybillHeader wh = new WaybillHeader();
            wh.setShiftNo(header.getShiftNo());
            wh.setResveredField05(containerNo);
            List<WaybillHeader> list = waybillHeaderMapper.selectCnExistByShiftNo(wh);
            if (list != null && list.size() > 0) {
                throw new RuntimeException(containerNo + ",该箱号已被申报");
            }
            if (rdd.getContainerType().contains("20")) {
                num = num + 0.5f;
            } else if (rdd.getContainerType().contains("40") || rdd.getContainerType().contains("45")) {
                num = num + 1.0f;
            }
            details.setOrderNo(appNo);
            details.setDestinationName(rdd.getStartStationName());
            details.setDestination(rdd.getEndStationName());
            details.setBureauSubordinate(shifmanagements.get(0).getBureau());
            details.setPortStation(shifmanagements.get(0).getPortStation());
            details.setBox(rdd.getBox());
            details.setContainerNo(containerNo);
            details.setDeadWeight(rdd.getDeadWeight());
            details.setGrossWeight(rdd.getGrossWeight());
            details.setNetWeight(rdd.getNetWeight());

            details.setContainerTypeCode(rdd.getContainerTypeCode());
            details.setContainerTypeName(rdd.getContainerTypeName());
            details.setContainerType(rdd.getContainerType());

            details.setGoodsName(rdd.getGoodsName());
            details.setResveredField01(rdd.getIdentification());
            details.setResveredField02(rdd.getResveredField02());
            details.setDeleteFlag("N");
            details.setAddWho(bookingRequesheader.getBookingCustcode());
            details.setAddWhoName(bookingRequesheader.getBookingCustname());
            details.setAddTime(new Date());
            detailList.add(details);
        }

        SpaceOccupy sp = new SpaceOccupy();
        sp.setShiftNo(header.getShiftNo());
        Float occupyNums = spaceOccupyMapper.selectOccupyNums(sp);
        if (occupyNums == null || occupyNums < 0.5) {
            occupyNums = (float) 0;
        }
        Float usedSpNumsUndShf = occupyNums;//该班次已用舱位数量
        Float trueSpaceNums = 0.00f;
        Float trueSpaceNumsSp = 0.00f;
        if (shifmanagements.get(0).getNumOfTruePositions() != null && !"".equals(shifmanagements.get(0).getNumOfTruePositions())) {
            trueSpaceNums = Float.valueOf(shifmanagements.get(0).getNumOfTruePositions());//班次发布实际舱位
            bookingRequesheader.setResveredField04(trueSpaceNums);
        }
        if (shifmanagements.get(0).getNumOfReservePositions() != null && !"".equals(shifmanagements.get(0).getNumOfReservePositions())) {
            trueSpaceNumsSp = Float.valueOf(shifmanagements.get(0).getNumOfReservePositions()); //班次发布备用舱位
            bookingRequesheader.setResveredField05(trueSpaceNumsSp);
        }
        Float sumSpace = trueSpaceNums + trueSpaceNumsSp;//该班次的总舱位数（实际+备用）
        Float spaceLeft = sumSpace - usedSpNumsUndShf;//总仓位-已用=剩余舱位数 （实际+备用）
        String flag = "0";     //判断是否使用备用仓标识0不使用1使用
        if (num > spaceLeft) {//上报舱位数若大于剩余舱位数,返回提示信息
            throw new RuntimeException("剩余舱位数量不足，请重新确认");
        } else {
            //剩余舱位数满足本次提交，则更新舱位占用表，剩余舱位（实际+备用）数据
            SpaceOccupy so = new SpaceOccupy();
            //当前班次已用舱位数
            so.setSpaceNums(String.valueOf(num));
            so.setOrderNo(appNo);
            so.setShiftNo(bookingRequesheader.getShiftNo());
            so.setAddTime(LocalDateTime.now());
            so.setAddWhoName(bookingRequesheader.getBookingCustname());
            so.setAddWho(bookingRequesheader.getBookingCustcode());
            spaceOccupyMapper.insertSpaceOccupy(so);
            //判断是否使用备用仓(已占用+本次提交)>班次发布实际舱位数，即为使用备用仓(1)，否则为未使用(0)
            flag = (usedSpNumsUndShf + num) > trueSpaceNums ? "1" : "0";
        }

        String account = sysNoConfigService.genNo("YD");
        bookingRequesheader.setRowId(UUID.randomUUID().toString());
        bookingRequesheader.setWaybillNo(account);
        bookingRequesheader.setOrderNo(appNo);
        bookingRequesheader.setShiftNo(shifmanagements.get(0).getShiftId());
        bookingRequesheader.setShippingLineCode(shifmanagements.get(0).getShippingLineCode());
        bookingRequesheader.setShippingLine(shifmanagements.get(0).getShippingLine());
        bookingRequesheader.setTrainName(shifmanagements.get(0).getShiftName());
        bookingRequesheader.setDeliveryTime(shifmanagements.get(0).getPlanShipTime());
        bookingRequesheader.setTrainType(shifmanagements.get(0).getTrainType());
        bookingRequesheader.setTotalCases(String.valueOf(header.getDetails().size()));
        bookingRequesheader.setSpaceNums(num);
        bookingRequesheader.setDocumentStatus("2");
        bookingRequesheader.setTrip(shifmanagements.get(0).getTrip());
        bookingRequesheader.setCityPlatform(userInfo.getPlatformName());
        bookingRequesheader.setAuditType("1");
        bookingRequesheader.setResveredField02(shifmanagements.get(0).getPlatformCode());
        bookingRequesheader.setDeleteFlag("N");
        bookingRequesheader.setAddTime(new Date());
        bookingRequesheader.setAddWho(header.getBookingCustcode());
        bookingRequesheader.setAddWhoName(header.getBookingCustname());
        bookingRequesheader.setDetails(detailList);

        bookingRequesheaderMapper.insertBookingRequesheader(bookingRequesheader);
        bookingRequesdetailMapper.insertBookingRequesdetails(bookingRequesheader.getDetails());

        WaybillHeader waybillHeader = insertWaybill(bookingRequesheader, flag, num, header, shifmanagements);
        //补充业务流程单
        insertBusCost(waybillHeader, header.getDetails());
        //插入上级平台订单数据
        waybillHeaderService.insertShareWaybill(waybillHeader);
        return new R<>(0, Boolean.TRUE, null, "操作成功！");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveBookingAndWaybillListForCity(List<RequesheaderDTO> headers) throws Exception {
        if (CollUtil.isNotEmpty(headers)) {
            for (RequesheaderDTO header : headers) {
                SecruityUser userInfo = SecurityUtils.getUserInfo();
                BookingRequesheader bookingRequesheader = new BookingRequesheader();
                bookingRequesheader.setBookingCustcode(header.getBookingCustcode());
                bookingRequesheader.setBookingCustname(header.getBookingCustname());
                bookingRequesheader.setShiftNo(header.getShiftNo());
                bookingRequesheader.setRemarks(header.getRemarks());
                bookingRequesheader.setResveredField02(userInfo.getPlatformCode());
                bookingRequesheader.setResveredField06(header.getResveredField06());
                if (header.getDetails() == null) {
                    throw new RuntimeException("请检查所填信息有遗漏！");
                }
                Shifmanagement shif = new Shifmanagement();
                shif.setShiftId(header.getShiftNo());
                shif.setPlatformCode(userInfo.getPlatformCode());
                List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementListByLike(shif);
                //没有申请单号，须新增主子表信息
                String appNo = sysNoConfigService.genNo("SQ");
                float num = 0f;//上报舱位数
                List<BookingRequesdetail> detailList = new ArrayList<>();
                for (RequesdetailDTO rdd : header.getDetails()) {
                    BookingRequesdetail details = new BookingRequesdetail();

                    String containerNo = rdd.getContainerNo();
                    WaybillHeader wh = new WaybillHeader();
                    wh.setShiftNo(header.getShiftNo());
                    wh.setResveredField05(containerNo);
                    List<WaybillHeader> list = waybillHeaderMapper.selectCnExistByShiftNo(wh);
                    if (list != null && list.size() > 0) {
                        throw new RuntimeException(containerNo + ",该箱号已被申报");
                    }
                    if (rdd.getContainerType().contains("20")) {
                        num = num + 0.5f;
                    } else if (rdd.getContainerType().contains("40") || rdd.getContainerType().contains("45")) {
                        num = num + 1.0f;
                    }
                    details.setOrderNo(appNo);
                    details.setDestinationName(rdd.getStartStationName());
                    details.setDestination(rdd.getEndStationName());
                    details.setBureauSubordinate(shifmanagements.get(0).getBureau());
                    details.setPortStation(shifmanagements.get(0).getPortStation());
                    details.setBox(rdd.getBox());
                    details.setContainerNo(containerNo);
                    details.setDeadWeight(rdd.getDeadWeight());
                    details.setGrossWeight(rdd.getGrossWeight());
                    details.setNetWeight(rdd.getNetWeight());

                    details.setContainerTypeCode(rdd.getContainerTypeCode());
                    details.setContainerTypeName(rdd.getContainerTypeName());
                    details.setContainerType(rdd.getContainerType());

                    details.setGoodsName(rdd.getGoodsName());
                    details.setResveredField01(rdd.getIdentification());
                    details.setResveredField02(rdd.getResveredField02());
                    details.setDeleteFlag("N");
                    details.setAddWho(bookingRequesheader.getBookingCustcode());
                    details.setAddWhoName(bookingRequesheader.getBookingCustname());
                    details.setAddTime(new Date());
                    detailList.add(details);
                }

                SpaceOccupy sp = new SpaceOccupy();
                sp.setShiftNo(header.getShiftNo());
                Float occupyNums = spaceOccupyMapper.selectOccupyNums(sp);
                if (occupyNums == null || occupyNums < 0.5) {
                    occupyNums = (float) 0;
                }
                Float usedSpNumsUndShf = occupyNums;//该班次已用舱位数量
                Float trueSpaceNums = 0.00f;
                Float trueSpaceNumsSp = 0.00f;
                if (shifmanagements.get(0).getNumOfTruePositions() != null && !"".equals(shifmanagements.get(0).getNumOfTruePositions())) {
                    trueSpaceNums = Float.valueOf(shifmanagements.get(0).getNumOfTruePositions());//班次发布实际舱位
                    bookingRequesheader.setResveredField04(trueSpaceNums);
                }
                if (shifmanagements.get(0).getNumOfReservePositions() != null && !"".equals(shifmanagements.get(0).getNumOfReservePositions())) {
                    trueSpaceNumsSp = Float.valueOf(shifmanagements.get(0).getNumOfReservePositions()); //班次发布备用舱位
                    bookingRequesheader.setResveredField05(trueSpaceNumsSp);
                }
                Float sumSpace = trueSpaceNums + trueSpaceNumsSp;//该班次的总舱位数（实际+备用）
                Float spaceLeft = sumSpace - usedSpNumsUndShf;//总仓位-已用=剩余舱位数 （实际+备用）
                String flag = "0";     //判断是否使用备用仓标识0不使用1使用
                if (num > spaceLeft) {//上报舱位数若大于剩余舱位数,返回提示信息
                    throw new RuntimeException("剩余舱位数量不足，请重新确认");
                } else {
                    //剩余舱位数满足本次提交，则更新舱位占用表，剩余舱位（实际+备用）数据
                    SpaceOccupy so = new SpaceOccupy();
                    //当前班次已用舱位数
                    so.setSpaceNums(String.valueOf(num));
                    so.setOrderNo(appNo);
                    so.setShiftNo(bookingRequesheader.getShiftNo());
                    so.setAddTime(LocalDateTime.now());
                    so.setAddWhoName(bookingRequesheader.getBookingCustname());
                    so.setAddWho(bookingRequesheader.getBookingCustcode());
                    spaceOccupyMapper.insertSpaceOccupy(so);
                    //判断是否使用备用仓(已占用+本次提交)>班次发布实际舱位数，即为使用备用仓(1)，否则为未使用(0)
                    flag = (usedSpNumsUndShf + num) > trueSpaceNums ? "1" : "0";
                }

                String account = sysNoConfigService.genNo("YD");
                bookingRequesheader.setRowId(UUID.randomUUID().toString());
                bookingRequesheader.setWaybillNo(account);
                bookingRequesheader.setOrderNo(appNo);
                bookingRequesheader.setShiftNo(shifmanagements.get(0).getShiftId());
                bookingRequesheader.setShippingLineCode(shifmanagements.get(0).getShippingLineCode());
                bookingRequesheader.setShippingLine(shifmanagements.get(0).getShippingLine());
                bookingRequesheader.setTrainName(shifmanagements.get(0).getShiftName());
                bookingRequesheader.setDeliveryTime(shifmanagements.get(0).getPlanShipTime());
                bookingRequesheader.setTrainType(shifmanagements.get(0).getTrainType());
                bookingRequesheader.setTotalCases(String.valueOf(header.getDetails().size()));
                bookingRequesheader.setSpaceNums(num);
                bookingRequesheader.setDocumentStatus("2");
                bookingRequesheader.setTrip(shifmanagements.get(0).getTrip());
                bookingRequesheader.setCityPlatform(userInfo.getPlatformName());
                bookingRequesheader.setAuditType("1");
                bookingRequesheader.setResveredField02(shifmanagements.get(0).getPlatformCode());
                bookingRequesheader.setDeleteFlag("N");
                bookingRequesheader.setAddTime(new Date());
                bookingRequesheader.setAddWho(header.getBookingCustcode());
                bookingRequesheader.setAddWhoName(header.getBookingCustname());
                bookingRequesheader.setDetails(detailList);

                bookingRequesheaderMapper.insertBookingRequesheader(bookingRequesheader);
                bookingRequesdetailMapper.insertBookingRequesdetails(bookingRequesheader.getDetails());

                WaybillHeader waybillHeader = insertWaybill(bookingRequesheader, flag, num, header, shifmanagements);
                //补充业务流程单
                insertBusCost(waybillHeader, header.getDetails());
                //插入上级平台订单数据
                waybillHeaderService.insertShareWaybill(waybillHeader);
            }
        }
        return new R<>(0, Boolean.TRUE, null, "操作成功！");
    }


    public WaybillHeader insertWaybill(BookingRequesheader bookingRequesheader, String flag, float num, RequesheaderDTO header, List<Shifmanagement> shifmanagements) {
        //插入运单主、子表信息
        WaybillHeader waybillHeader = new WaybillHeader();
        waybillHeader.setRowId(UUID.randomUUID().toString());
        waybillHeader.setOrderNo(bookingRequesheader.getOrderNo());
        waybillHeader.setWaybillNo(bookingRequesheader.getWaybillNo());
        waybillHeader.setCustomerNo(bookingRequesheader.getBookingCustcode());
        waybillHeader.setCustomerName(bookingRequesheader.getBookingCustname());
        waybillHeader.setPlatformCode(bookingRequesheader.getResveredField02());
        waybillHeader.setPlatformName(bookingRequesheader.getCityPlatform());
        waybillHeader.setTrainType(bookingRequesheader.getTrainType());
        waybillHeader.setIdentification(bookingRequesheader.getIdentification());
        waybillHeader.setIsCustomTransit(bookingRequesheader.getTransit());
        waybillHeader.setShiftNo(bookingRequesheader.getShiftNo());
        waybillHeader.setShippingTime(bookingRequesheader.getDeliveryTime());
        waybillHeader.setShippingLine(bookingRequesheader.getShippingLine());
        waybillHeader.setResveredField05(bookingRequesheader.getShippingLineCode());//发运线路编码
        waybillHeader.setResveredField06(bookingRequesheader.getResveredField06());//境外代理
        waybillHeader.setTrainName(bookingRequesheader.getTrainName());
        waybillHeader.setResveredField02(bookingRequesheader.getResveredField02());//市平台名称
        waybillHeader.setAuditFlag("DCSH");
        waybillHeader.setTotalCases(bookingRequesheader.getTotalCases());
        waybillHeader.setBillStatus("2");
        waybillHeader.setDeleteFlag("N");
        waybillHeader.setAddWho(bookingRequesheader.getAddWho());
        waybillHeader.setAddWhoName(bookingRequesheader.getAddWhoName());
        waybillHeader.setAddTime(new Date());
        waybillHeader.setTrip(bookingRequesheader.getTrip());
        waybillHeader.setIsTransit(bookingRequesheader.getTransit());
        waybillHeader.setResveredField01(bookingRequesheader.getResveredField03());//审核意见
        waybillHeader.setResveredField03(flag);                      //20210811新增逻辑 是否使用备用仓标识（0不使用1使用）
        waybillHeader.setResveredField04(String.valueOf(num)); //20210811新增逻辑 占用舱数
        waybillHeader.setShAct("2");//上合代申请0否1是
        waybillHeaderMapper.insertWaybillHeader(waybillHeader);

        for (RequesdetailDTO requesdetail : header.getDetails()) {
            WaybillContainerInfo waybillContainerInfo = new WaybillContainerInfo();
            waybillContainerInfo.setRowId(UUID.randomUUID().toString());
            waybillContainerInfo.setOrderNo(bookingRequesheader.getOrderNo());
            waybillContainerInfo.setWaybillNo(bookingRequesheader.getWaybillNo());
            waybillContainerInfo.setDemandNo(requesdetail.getDemandNo());
            waybillContainerInfo.setIdentification(requesdetail.getIdentification());
            waybillContainerInfo.setBureau(shifmanagements.get(0).getBureau());

            waybillContainerInfo.setStationCompilation(requesdetail.getStationCompilation());
            waybillContainerInfo.setDestinationName(requesdetail.getStartStationName());
            waybillContainerInfo.setStartStationName(requesdetail.getStartStationName());
            waybillContainerInfo.setEndCompilation(requesdetail.getEndCompilation());
            waybillContainerInfo.setDestination(requesdetail.getEndStationName());
            waybillContainerInfo.setEndStationName(requesdetail.getEndStationName());
            waybillContainerInfo.setGoodsValue(requesdetail.getGoodsValue());
            waybillContainerInfo.setPortStation(shifmanagements.get(0).getPortStation());
            waybillContainerInfo.setContainerNo(requesdetail.getContainerNo());
            waybillContainerInfo.setContainerOwner(requesdetail.getBox());

            waybillContainerInfo.setContainerTypeCode(requesdetail.getContainerTypeCode());
            waybillContainerInfo.setContainerTypeName(requesdetail.getContainerTypeName());
            waybillContainerInfo.setContainerType(requesdetail.getContainerType());

            waybillContainerInfo.setContainerDeadWeight(requesdetail.getDeadWeight());
            waybillContainerInfo.setContainerGrossWeight(requesdetail.getGrossWeight());
            waybillContainerInfo.setContainerNetWeight(requesdetail.getNetWeight());
            waybillContainerInfo.setPortAgent(requesdetail.getPortAgent());
            waybillContainerInfo.setAbroadReachCity(requesdetail.getAbroadReachCity());
            waybillContainerInfo.setDeleteFlag("N");
            waybillContainerInfo.setAddWho(bookingRequesheader.getAddWho());
            waybillContainerInfo.setAddWhoName(bookingRequesheader.getAddWhoName());
            waybillContainerInfo.setAddTime(new Date());
            waybillContainerInfo.setGoodsName(requesdetail.getGoodsName());
            waybillContainerInfo.setIsFull(requesdetail.getIsFull());
            waybillContainerInfo.setNonFerrous(requesdetail.getNonFerrous());

            waybillContainerInfo.setClearanceNumber(requesdetail.getClearanceNumber());
            waybillContainerInfo.setCustomsSeal(requesdetail.getCustomsSeal());
            waybillContainerInfo.setTrainNumber(requesdetail.getTrainNumber());
            waybillContainerInfo.setWaybillDemandNumber(requesdetail.getWaybillDemandNumber());
            waybillContainerInfo.setWaybillLnNumber(requesdetail.getWaybillLnNumber());
            waybillContainerInfo.setGoodsOwner(requesdetail.getGoodsOwner());
            waybillContainerInfo.setDestinationCountryCode(requesdetail.getDestinationCountryCode());
            waybillContainerInfo.setDestinationCountryName(requesdetail.getDestinationCountryName());
            waybillContainerInfoMapper.insertWaybillContainerInfo(waybillContainerInfo);
        }

        List<WaybillParticipants> waybillParticipants = new ArrayList<>();
        for (RequesdetailDTO requesdetail : header.getDetails()) {
            //收货人
            WaybillParticipants waybillParticipantsShou = new WaybillParticipants();
            waybillParticipantsShou.setRowId(UUID.randomUUID().toString());
            waybillParticipantsShou.setAppNo(bookingRequesheader.getOrderNo());
            waybillParticipantsShou.setParticipantsType("S");
            waybillParticipantsShou.setWaybillNo(waybillHeader.getWaybillNo());
            waybillParticipantsShou.setContainerNo(requesdetail.getContainerNo());
            waybillParticipantsShou.setConsignorName(requesdetail.getConsigneeName());
            waybillParticipantsShou.setCountryCode(requesdetail.getConsigneeCountryCode());
            waybillParticipantsShou.setDeleteFlag("N");
            waybillParticipantsShou.setAddWho(bookingRequesheader.getAddWho());
            waybillParticipantsShou.setAddWhoName(bookingRequesheader.getAddWhoName());
            waybillParticipantsShou.setAddTime(LocalDateTime.now());
            waybillParticipants.add(waybillParticipantsShou);
            //发货人
            WaybillParticipants waybillParticipantsFa = new WaybillParticipants();
            waybillParticipantsFa.setRowId(UUID.randomUUID().toString());
            waybillParticipantsFa.setAppNo(bookingRequesheader.getOrderNo());
            waybillParticipantsFa.setParticipantsType("F");
            waybillParticipantsFa.setWaybillNo(waybillHeader.getWaybillNo());
            waybillParticipantsFa.setContainerNo(requesdetail.getContainerNo());
            waybillParticipantsFa.setConsignorName(requesdetail.getConsignorName());
            waybillParticipantsFa.setCountryCode(requesdetail.getConsignorCountryCode());
            waybillParticipantsFa.setDeleteFlag("N");
            waybillParticipantsFa.setAddWho(bookingRequesheader.getAddWho());
            waybillParticipantsFa.setAddWhoName(bookingRequesheader.getAddWhoName());
            waybillParticipantsFa.setAddTime(LocalDateTime.now());
            waybillParticipants.add(waybillParticipantsFa);

            if (CollUtil.isNotEmpty(shifmanagements)) {
                if ("G".equals(shifmanagements.get(0).getTrip())) {
                    waybillParticipantsFa.setCity(requesdetail.getGoodsOrigin());
                } else if ("R".equals(shifmanagements.get(0).getTrip())) {
                    waybillParticipantsShou.setCity(requesdetail.getGoodsOrigin());
                }
            }
        }
        if (waybillParticipants.size() > 0) {
            waybillParticipantsMapper.insertWaybillParticipants(waybillParticipants);
        }

        List<WaybillGoodsInfo> waybillGoodsInfos = new ArrayList<>();
        for (RequesdetailDTO requesdetail : header.getDetails()) {
            if (requesdetail.getGoods() != null && requesdetail.getGoods().size() > 0) {
                List<GoodsDTO> gds = JSONUtil.toList(JSONUtil.parseArray(requesdetail.getGoods()), GoodsDTO.class);
                for (GoodsDTO gd : gds) {
                    WaybillGoodsInfo waybillGoodsInfo = new WaybillGoodsInfo();
                    waybillGoodsInfo.setRowId(UUID.randomUUID().toString());
                    waybillGoodsInfo.setWaybillNo(bookingRequesheader.getWaybillNo());
                    waybillGoodsInfo.setContainerNo(requesdetail.getContainerNo());
                    waybillGoodsInfo.setGoodsCode(gd.getGoodsCode());
                    waybillGoodsInfo.setGoodsChineseName(gd.getGoodsChineseName());
                    waybillGoodsInfo.setPackageType(gd.getPackageType());
                    waybillGoodsInfo.setGoodsNums(gd.getGoodsNums());
                    waybillGoodsInfo.setGoodsWeight(gd.getGoodsWeight());
                    waybillGoodsInfo.setGoodsValue(gd.getGoodsValue());

                    waybillGoodsInfo.setDeleteFlag("N");
                    waybillGoodsInfo.setAddWho(bookingRequesheader.getAddWho());
                    waybillGoodsInfo.setAddWhoName(bookingRequesheader.getAddWhoName());
                    waybillGoodsInfo.setAddTime(LocalDateTime.now());
                    waybillGoodsInfos.add(waybillGoodsInfo);
                }
            }
        }
        if (CollUtil.isNotEmpty(waybillGoodsInfos)) {
            waybillGoodsInfoMapper.insertWaybillGoodsInfo(waybillGoodsInfos);
        }
        if (StrUtil.isNotBlank(waybillHeader.getTrip()) && "R".equals(waybillHeader.getTrip())) {
            if (StrUtil.isNotBlank(waybillHeader.getCustomerNo()) && waybillHeader.getCustomerNo().contains("CUS")) {
                fdPostTransportService.insertPostTransport(waybillHeader);
            }
        }
        return waybillHeader;
    }

    public void insertBusCost(WaybillHeader waybillHeader, List<RequesdetailDTO> details) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        WaybillHeader wh = waybillHeaderMapper.selectWaybillHeaderById(waybillHeader.getRowId());

        Shifmanagement shifmanagement = shifmanagementMapper.selectShifmanagementByPlatCodeAndShiftNo(wh.getPlatformCode(), wh.getShiftNo());

        FdBusCost sel = new FdBusCost();
        sel.setPlatformCode(wh.getPlatformCode());
        sel.setCustomerCode(wh.getCustomerNo());
        sel.setPlatformLevel("0");
        sel.setShiftNo(wh.getShiftNo());
        sel.setDeleteFlag("N");
        List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel);
        FdBusCostWaybill fdBusCostWaybill = new FdBusCostWaybill();
        if (CollUtil.isNotEmpty(fdBusCosts)) {
            fdBusCostWaybill.setCostCode(fdBusCosts.get(0).getCostCode());
        } else {
            //主数据
            FdBusCost fdBusCost = new FdBusCost();
            fdBusCost.setCostCode(sysNoConfigService.genNo("FDC"));
            fdBusCost.setPlatformCode(wh.getPlatformCode());
            fdBusCost.setPlatformName(wh.getPlatformName());
            fdBusCost.setCustomerCode(wh.getCustomerNo());
            fdBusCost.setCustomerName(wh.getCustomerName());
            fdBusCost.setPlatformLevel("0");
            fdBusCost.setShiftNo(wh.getShiftNo());
            fdBusCost.setAuditStatus("0");
            fdBusCost.setAddWho(userInfo.getUserName());
            fdBusCost.setAddWhoName(userInfo.getRealName());
            fdBusCost.setAddTime(LocalDateTime.now());
            fdBusCost.setPrincipalName(wh.getCustomerName());

            CustomerPlatformInfo sel2 = new CustomerPlatformInfo();
            sel2.setPlatformCode(wh.getPlatformCode());
            sel2.setCustomerCode(wh.getCustomerNo());
            sel2.setDeleteFlag("N");
            List<CustomerPlatformInfo> list = customerPlatformInfoMapper.selectCustomerPlatformInfoList(sel2);
            if (CollUtil.isNotEmpty(list)) {
                fdBusCost.setContactsName(list.get(0).getContactPerson());
                fdBusCost.setContactsPhone(list.get(0).getContactNo());
            } else {
                sel2.setPlatformCode(null);
                List<CustomerPlatformInfo> list2 = customerPlatformInfoMapper.selectCustomerPlatformInfoList(sel2);
                if (CollUtil.isNotEmpty(list2)) {
                    fdBusCost.setContactsName(list2.get(0).getContactPerson());
                    fdBusCost.setContactsPhone(list2.get(0).getContactNo());
                }
            }
            //处理“客户对接人”、“联系方式”、“前端服务操作员”、“后端服务操作员”、“现场服务操作员”
            fdBusCost = selectFdBusCostOld(fdBusCost, shifmanagement.getTrip());
            fdBusCostMapper.insertFdBusCost(fdBusCost);

            fdBusCostWaybill.setCostCode(fdBusCost.getCostCode());
        }

        FdBusCostWaybill sel2 = new FdBusCostWaybill();
        sel2.setCostCode(fdBusCostWaybill.getCostCode());
        sel2.setWaybillNo(wh.getWaybillNo());
        sel2.setDeleteFlag("N");
        List<FdBusCostWaybill> fdBusCostWaybills = fdBusCostWaybillMapper.selectFdBusCostWaybillList(sel2);
        if (CollUtil.isEmpty(fdBusCostWaybills)) {
            fdBusCostWaybill.setWaybillNo(wh.getWaybillNo());
            fdBusCostWaybill.setApplicationNumber(wh.getOrderNo());
            fdBusCostWaybill.setAuditStatus("1");
            fdBusCostWaybill.setAddWho(userInfo.getUserName());
            fdBusCostWaybill.setAddWhoName(userInfo.getRealName());
            fdBusCostWaybill.setAddTime(LocalDateTime.now());
            fdBusCostWaybillMapper.insertFdBusCostWaybill(fdBusCostWaybill);
        }

        if (CollUtil.isNotEmpty(details)) {
            String supPlatformCode = userInfo.getSupPlatformCode();
            String supPlatformName = "";
            if (supPlatformCode.contains("_")) {
                supPlatformCode = supPlatformCode.split("_")[0];
            }
            CustomerInfo customerInfo = new CustomerInfo();
            customerInfo.setCustomerCode(supPlatformCode);
            customerInfo.setDeleteFlag("N");
            List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoList(customerInfo);
            if (CollUtil.isNotEmpty(customerInfos)) {
                supPlatformName = customerInfos.get(0).getCompanyName();
            }
            for (RequesdetailDTO dto : details) {
                if (dto.getDomesticFreight() != null && dto.getDomesticFreight().compareTo(BigDecimal.ZERO) != 0) {
                    FdBusCostDetail jn = new FdBusCostDetail();
                    jn.setCostCode(fdBusCostWaybill.getCostCode());
                    jn.setCostType("0");
                    jn.setContainerNumber(dto.getContainerNo());
                    jn.setCodeBbCategoriesCode("f_fee_type");
                    jn.setCodeBbCategoriesName("发运运费");
                    jn.setCodeSsCategoriesCode("jndtlyf");
                    jn.setCodeSsCategoriesName("国内段包干");
                    jn.setReceiveCode(wh.getPlatformCode());
                    jn.setReceiveName(wh.getPlatformName());
                    jn.setPayCode(wh.getCustomerNo());
                    jn.setPayName(wh.getCustomerName());
                    jn.setCurrency("人民币");
                    jn.setExchangeRate(BigDecimal.valueOf(1));
                    jn.setExchangeRateNew(dto.getExchangeRate());
                    jn.setOriginalAmount(dto.getDomesticFreight());
                    jn.setLocalAmount(dto.getDomesticFreight());
                    jn.setAuditStatus("0");
                    jn.setAddWho(userInfo.getUserName());
                    jn.setAddWhoName(userInfo.getRealName());
                    jn.setAddTime(LocalDateTime.now());
                    jn.setShiftNo(wh.getShiftNo());
                    fdBusCostDetailMapper.insertFdBusCostDetail(jn);
                }
                if (dto.getOverseasFreightOc() != null && dto.getOverseasFreightCny() != null && dto.getOverseasFreightOc().compareTo(BigDecimal.ZERO) != 0 && dto.getOverseasFreightCny().compareTo(BigDecimal.ZERO) != 0) {
                    FdBusCostDetail jw = new FdBusCostDetail();
                    jw.setCostCode(fdBusCostWaybill.getCostCode());
                    jw.setCostType("0");
                    jw.setContainerNumber(dto.getContainerNo());
                    jw.setCodeBbCategoriesCode("f_fee_type");
                    jw.setCodeBbCategoriesName("发运运费");
                    jw.setCodeSsCategoriesCode("jwdtlyf");
                    jw.setCodeSsCategoriesName("国外段包干");
                    jw.setReceiveCode(wh.getPlatformCode());
                    jw.setReceiveName(wh.getPlatformName());
                    jw.setPayCode(wh.getCustomerNo());
                    jw.setPayName(wh.getCustomerName());
                    jw.setCurrency(dto.getMonetaryType());
                    jw.setExchangeRate(dto.getExchangeRate());
                    jw.setExchangeRateNew(dto.getExchangeRate());
                    jw.setOriginalAmount(dto.getOverseasFreightOc());
                    jw.setLocalAmount(dto.getOverseasFreightCny());
                    jw.setAuditStatus("0");
                    jw.setAddWho(userInfo.getUserName());
                    jw.setAddWhoName(userInfo.getRealName());
                    jw.setAddTime(LocalDateTime.now());
                    jw.setShiftNo(wh.getShiftNo());
                    fdBusCostDetailMapper.insertFdBusCostDetail(jw);
                }
                if (dto.getYfDomesticFreight() != null && dto.getYfDomesticFreight().compareTo(BigDecimal.ZERO) != 0) {
                    FdBusCostDetail jn = new FdBusCostDetail();
                    jn.setCostCode(fdBusCostWaybill.getCostCode());
                    jn.setCostType("1");
                    jn.setContainerNumber(dto.getContainerNo());
                    jn.setCodeBbCategoriesCode("f_fee_type");
                    jn.setCodeBbCategoriesName("发运运费");
                    jn.setCodeSsCategoriesCode("jndtlyf");
                    jn.setCodeSsCategoriesName("国内段包干");
                    if (shifmanagement != null && StrUtil.isNotBlank(shifmanagement.getSharePlatformCode())) {
                        jn.setReceiveCode(shifmanagement.getSharePlatformCode());
                    } else {
                        jn.setReceiveCode(supPlatformCode);
                    }
                    if (shifmanagement != null && StrUtil.isNotBlank(shifmanagement.getSharePlatformName())) {
                        jn.setReceiveName(shifmanagement.getSharePlatformName());
                    } else {
                        jn.setReceiveName(supPlatformName);
                    }
                    jn.setPayCode(wh.getPlatformCode());
                    jn.setPayName(wh.getPlatformName());
                    jn.setCurrency("人民币");
                    jn.setExchangeRate(BigDecimal.valueOf(1));
                    jn.setExchangeRateNew(dto.getExchangeRate());
                    jn.setOriginalAmount(dto.getYfDomesticFreight());
                    jn.setLocalAmount(dto.getYfDomesticFreight());
                    jn.setAuditStatus("0");
                    jn.setAddWho(userInfo.getUserName());
                    jn.setAddWhoName(userInfo.getRealName());
                    jn.setAddTime(LocalDateTime.now());
                    jn.setShiftNo(wh.getShiftNo());
                    fdBusCostDetailMapper.insertFdBusCostDetail(jn);
                }
                if (dto.getYfOverseasFreightOc() != null && dto.getYfExchangeRate() != null && dto.getYfOverseasFreightOc().compareTo(BigDecimal.ZERO) != 0 && dto.getYfExchangeRate().compareTo(BigDecimal.ZERO) != 0) {
                    FdBusCostDetail jw = new FdBusCostDetail();
                    jw.setCostCode(fdBusCostWaybill.getCostCode());
                    jw.setCostType("1");
                    jw.setContainerNumber(dto.getContainerNo());
                    jw.setCodeBbCategoriesCode("f_fee_type");
                    jw.setCodeBbCategoriesName("发运运费");
                    jw.setCodeSsCategoriesCode("jwdtlyf");
                    jw.setCodeSsCategoriesName("国外段包干");
                    if (shifmanagement != null && StrUtil.isNotBlank(shifmanagement.getSharePlatformCode())) {
                        jw.setReceiveCode(shifmanagement.getSharePlatformCode());
                    } else {
                        jw.setReceiveCode(supPlatformCode);
                    }
                    if (shifmanagement != null && StrUtil.isNotBlank(shifmanagement.getSharePlatformName())) {
                        jw.setReceiveName(shifmanagement.getSharePlatformName());
                    } else {
                        jw.setReceiveName(supPlatformName);
                    }
                    jw.setPayCode(wh.getPlatformCode());
                    jw.setPayName(wh.getPlatformName());
                    jw.setCurrency(dto.getYfMonetaryType());
                    jw.setExchangeRate(dto.getYfExchangeRate());
                    jw.setExchangeRateNew(dto.getYfExchangeRate());
                    jw.setOriginalAmount(dto.getYfOverseasFreightOc());
                    jw.setLocalAmount(dto.getYfOverseasFreightOc().multiply(dto.getYfExchangeRate()).setScale(2, BigDecimal.ROUND_HALF_UP));
                    jw.setAuditStatus("0");
                    jw.setAddWho(userInfo.getUserName());
                    jw.setAddWhoName(userInfo.getRealName());
                    jw.setAddTime(LocalDateTime.now());
                    jw.setShiftNo(wh.getShiftNo());
                    fdBusCostDetailMapper.insertFdBusCostDetail(jw);
                }
            }
        }
    }

    /**
     * 查询上次业务流程单
     * 1.在生成业务流程单数据时，依据订舱客户查询该市平台下该订舱客户以往的业务流程单中，
     * “客户对接人”、“联系方式”、“前端服务操作员”、“后端服务操作员”、“现场服务操作员”字段填写的最新的值是什么，
     * 补充到新生成的业务流程单基本信息中。
     *
     * @param fdBusCost 订单信息, waybillHeader  运单
     * @return FdBusCost
     * <AUTHOR>
     * @since 2025/4/21 下午1:44
     **/
    @Override
    public FdBusCost selectFdBusCostOld(FdBusCost fdBusCost, String trip) {
        FdBusCost fdBusCostOldParameter = new FdBusCost();
        fdBusCostOldParameter.setPlatformCode(fdBusCost.getPlatformCode());
        fdBusCostOldParameter.setCustomerCode(fdBusCost.getCustomerCode());
        fdBusCostOldParameter.setPlatformLevel("0");
        fdBusCostOldParameter.setTrip(trip);
        FdBusCost fdBusCostOld = fdBusCostMapper.selectFdBusCostOld(fdBusCostOldParameter);
        if (fdBusCostOld == null) {
            return fdBusCost;
        }
        // 客户对接人
        if (StrUtil.isBlank(fdBusCost.getCustomerLiaison())) {
            fdBusCost.setCustomerLiaison(fdBusCostOld.getCustomerLiaison());
        }
        //客户联系方式
        if (StrUtil.isBlank(fdBusCost.getCustomerPhone())) {
            fdBusCost.setCustomerPhone(fdBusCostOld.getCustomerPhone());
        }
        //前端操作员
        if (StrUtil.isBlank(fdBusCost.getFrontOperator())) {
            fdBusCost.setFrontOperator(fdBusCostOld.getFrontOperator());
        }
        //后端操作员
        if (StrUtil.isBlank(fdBusCost.getBackendOperator())) {
            fdBusCost.setBackendOperator(fdBusCostOld.getBackendOperator());
        }
        //现场操作员
        if (StrUtil.isBlank(fdBusCost.getOnsiteOperator())) {
            fdBusCost.setOnsiteOperator(fdBusCostOld.getOnsiteOperator());
        }
        return fdBusCost;
    }

    @Override
    public void exportTemplateForYw(HttpServletResponse response) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("导入明细");
        XSSFRow row = sheet.createRow(0);
        //宽度
        for (int i = 0; i <= 14; i++) {
            sheet.setColumnWidth(i, 60 * 80);
        }

        row.setHeight((short) (10 * 50));
        //字体1
        XSSFCellStyle style = setStyle(workbook);
        setTitleYw(row, style, sheet);

        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String(("导入业务流程单模板.xlsx").getBytes("GB2312"), "8859_1"));
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }

    @Override
    public void exportTemplateForYwTwo(HttpServletResponse response) throws IOException {
        com.alibaba.excel.ExcelWriter excelWriter = null;
        String templateFileName = "exportTemplateForYwTwo.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)) {
            templateFileName = linuxPath + templateFileName;
        } else {
            templateFileName = windowsPath + templateFileName;
        }
        String fileName = URLEncoder.encode("【快捷订舱】业务流程单导入模板", "UTF-8").replaceAll("\\+", "%20");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + ".xlsx\"");

        excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(templateFileName).build();
        WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "业务流程单").build();

        excelWriter.finish();
    }

    @Override
    public void exportTemplateForYwThree(HttpServletResponse response) throws IOException {
        com.alibaba.excel.ExcelWriter excelWriter = null;
        String templateFileName = "exportTemplateForYwThree.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)) {
            templateFileName = linuxPath + templateFileName;
        } else {
            templateFileName = windowsPath + templateFileName;
        }
        String fileName = URLEncoder.encode("【快捷订舱】业务流程单导入模板", "UTF-8").replaceAll("\\+", "%20");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + ".xlsx\"");

        excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(templateFileName).build();
        WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "业务流程单").build();

        excelWriter.finish();
    }

    /**
     * 设置标题
     *
     * @Param: row, style
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/04 17:57
     **/
    public void setTitleYw(XSSFRow row, XSSFCellStyle style, XSSFSheet sheet) {
        XSSFCell cell = row.createCell(0);
        cell.setCellValue("主要信息");
        cell.setCellStyle(style);
        for (int i = 1; i <= 9; i++) {
            XSSFCell cell1 = row.createCell(i);
            cell1.setCellValue("");
            cell1.setCellStyle(style);
        }

        XSSFRow row1 = sheet.createRow(1);
        XSSFCell cell10 = row1.createCell(0);
        cell10.setCellValue("订舱客户编码*");
        cell10.setCellStyle(style);
        XSSFCell cell11 = row1.createCell(1);
        cell11.setCellValue("");
        cell11.setCellStyle(style);
        XSSFCell cell12 = row1.createCell(2);
        cell12.setCellValue("订舱客户名称");
        cell12.setCellStyle(style);
        XSSFCell cell13 = row1.createCell(3);
        cell13.setCellValue("");
        cell13.setCellStyle(style);
        XSSFCell cell14 = row1.createCell(4);
        cell14.setCellValue("班次号*");
        cell14.setCellStyle(style);
        XSSFCell cell15 = row1.createCell(5);
        cell15.setCellValue("");
        cell15.setCellStyle(style);
        XSSFCell cell16 = row1.createCell(6);
        cell16.setCellValue("境外代理");
        cell16.setCellStyle(style);
        XSSFCell cell17 = row1.createCell(7);
        cell17.setCellValue("");
        cell17.setCellStyle(style);
        XSSFCell cell18 = row1.createCell(8);
        cell18.setCellValue("备注信息");
        cell18.setCellStyle(style);
        XSSFCell cell19 = row1.createCell(9);
        cell19.setCellValue("");
        cell19.setCellStyle(style);

        XSSFRow row2 = sheet.createRow(2);
        XSSFCell cell20 = row2.createCell(0);
        cell20.setCellValue("订舱信息");
        cell20.setCellStyle(style);
        for (int i = 1; i <= 22; i++) {
            XSSFCell cell21 = row2.createCell(i);
            cell21.setCellValue("");
            cell21.setCellStyle(style);
        }
        XSSFRow row3 = sheet.createRow(3);
        XSSFCell cell30 = row3.createCell(0);
        cell30.setCellValue("序号");
        cell30.setCellStyle(style);
        XSSFCell cell31 = row3.createCell(1);
        cell31.setCellValue("箱号*");
        cell31.setCellStyle(style);
        XSSFCell cell32 = row3.createCell(2);
        cell32.setCellValue("箱型*");
        cell32.setCellStyle(style);
        XSSFCell cell33 = row3.createCell(3);
        cell33.setCellValue("箱属*");
        cell33.setCellStyle(style);
        XSSFCell cell34 = row3.createCell(4);
        cell34.setCellValue("类型*");
        cell34.setCellStyle(style);
        XSSFCell cell35 = row3.createCell(5);
        cell35.setCellValue("品名*");
        cell35.setCellStyle(style);
        XSSFCell cell36 = row3.createCell(6);
        cell36.setCellValue("件数*");
        cell36.setCellStyle(style);
        XSSFCell cell37 = row3.createCell(7);
        cell37.setCellValue("货重*");
        cell37.setCellStyle(style);
        XSSFCell cell38 = row3.createCell(8);
        cell38.setCellValue("箱重*");
        cell38.setCellStyle(style);
        XSSFCell cell39 = row3.createCell(9);
        cell39.setCellValue("收货人*");
        cell39.setCellStyle(style);

        XSSFCell cell310 = row3.createCell(10);
        cell310.setCellValue("到站站编*");
        cell310.setCellStyle(style);
        XSSFCell cell311 = row3.createCell(11);
        cell311.setCellValue("发货人*");
        cell311.setCellStyle(style);
        XSSFCell cell312 = row3.createCell(12);
        cell312.setCellValue("发站站编*");
        cell312.setCellStyle(style);
        XSSFCell cell313 = row3.createCell(13);
        cell313.setCellValue("发货人所属国家代码*");
        cell313.setCellStyle(style);
        XSSFCell cell314 = row3.createCell(14);
        cell314.setCellValue("收货人所属国家代码*");
        cell314.setCellStyle(style);
        XSSFCell cell315 = row3.createCell(15);
        cell315.setCellValue("封号");
        cell315.setCellStyle(style);
        XSSFCell cell316 = row3.createCell(16);
        cell316.setCellValue("发货人声明");
        cell316.setCellStyle(style);
        XSSFCell cell317 = row3.createCell(17);
        cell317.setCellValue("口岸代理");
        cell317.setCellStyle(style);
        XSSFCell cell318 = row3.createCell(18);
        cell318.setCellValue("境内运费(人民币)");
        cell318.setCellStyle(style);
        XSSFCell cell319 = row3.createCell(19);
        cell319.setCellValue("境外运费(人民币)");
        cell319.setCellStyle(style);
        XSSFCell cell320 = row3.createCell(20);
        cell320.setCellValue("境外运费(原币)");
        cell320.setCellStyle(style);
        XSSFCell cell321 = row3.createCell(21);
        cell321.setCellValue("境外汇率");
        cell321.setCellStyle(style);
        XSSFCell cell322 = row3.createCell(22);
        cell322.setCellValue("境外币种");
        cell322.setCellStyle(style);
        XSSFCell cell323 = row3.createCell(23);
        cell323.setCellValue("是否全程*");
        cell323.setCellStyle(style);
        XSSFCell cell324 = row3.createCell(24);
        cell324.setCellValue("有色金属*");
        cell324.setCellStyle(style);

        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 9));
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 22));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R importedTemplateForYw(MultipartFile file) throws Exception {
        Workbook workbook = null;
        try {
            InputStream is = file.getInputStream();
            //利用WorkbookFactory获取Sheet，无需判断版本
            workbook = WorkbookFactory.create(is);
        } catch (Exception e) {
            e.printStackTrace();
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        RequesheaderDTO requesheaderDTO = new RequesheaderDTO();
        //导入第一张sheet页
        Sheet sheet = workbook.getSheetAt(0);
        Row row0 = sheet.getRow(1);
        if (row0.getCell(1) != null) {
            row0.getCell(1).setCellType(CellType.STRING);
            String bookingCustcode = row0.getCell(1).getStringCellValue();
            if (StrUtil.isNotBlank(bookingCustcode)) {
                CustomerPlatformInfo sel = new CustomerPlatformInfo();
                sel.setCustomerCode(bookingCustcode);
                sel.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
                sel.setDeleteFlag("N");
                List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoMapper.selectCustomerPlatformInfoList(sel);
                if (CollUtil.isNotEmpty(customerPlatformInfos)) {
                    CustomerInfo sel2 = new CustomerInfo();
                    sel2.setCustomerCode(bookingCustcode);
                    sel2.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
                    sel2.setDeleteFlag("N");
                    CustomerInfo customerInfo = customerInfoMapper.selectCustomegenNorInfoByCustomerCode(sel2);
                    if (customerInfo != null) {
                        requesheaderDTO.setBookingCustname(customerInfo.getCompanyName());
                    }
                    requesheaderDTO.setBookingCustcode(bookingCustcode);
                } else {
                    return new R<>(new Throwable("订舱客户编码在当前平台无法匹配！"));
                }
            } else {
                return new R<>(new Throwable("订舱客户编码不能为空！"));
            }

        }

        if (row0.getCell(5) != null) {
            row0.getCell(5).setCellType(CellType.STRING);
            String shiftNo = row0.getCell(5).getStringCellValue();
            if (StrUtil.isNotBlank(shiftNo)) {
                Shifmanagement shif = new Shifmanagement();
                shif.setShiftId(shiftNo);
                shif.setPlatformCode(userInfo.getPlatformCode());
                shif.setDeleteFlag("N");
                shif.setReleaseStatus("1");
                List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementListByLike(shif);
                if (CollUtil.isEmpty(shifmanagements)) {
                    return new R<>(new Throwable("该班次不存在！"));
                }
                requesheaderDTO.setShiftNo(shiftNo);
                requesheaderDTO.setResveredField06(shifmanagements.get(0).getOverseasAgency());
            } else {
                return new R<>(new Throwable("班次号不能为空！"));
            }

        }

        if (row0.getCell(7) != null) {
            row0.getCell(7).setCellType(CellType.STRING);
            String stringCellValue = row0.getCell(7).getStringCellValue();
            if (StrUtil.isNotBlank(stringCellValue)) {
                requesheaderDTO.setResveredField06(stringCellValue);
            }
        }

        if (row0.getCell(9) != null) {
            row0.getCell(9).setCellType(CellType.STRING);
            String stringCellValue = row0.getCell(9).getStringCellValue();
            requesheaderDTO.setRemarks(stringCellValue);
        }

        List<RequesdetailDTO> details = new ArrayList<>();
        StringBuffer sb = new StringBuffer();
        ContainerTypeData sel2 = new ContainerTypeData();
        sel2.setDeleteFlag("N");
        List<ContainerTypeData> containerTypeData = containerTypeDataMapper.selectContainerTypeDataList(sel2);
        StationManagement sel3 = new StationManagement();
        sel3.setDeleteFlag("N");
        List<StationManagement> stationManagements = stationManagementMapper.selectStationManagementList(sel3);
        String data = remoteAdminService.selectDictByType2("country_type");
        List<SysDictVo> countryList = JSONUtil.toList(JSONUtil.parseArray(data), SysDictVo.class);
        //获取行数
        int lastRowNum = sheet.getLastRowNum();
        for (int i = 4; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            boolean blankFlag = CheckUtil.isRowEmpty(row);
            if (blankFlag) {
                continue;
            }
            RequesdetailDTO requesdetailDTO = new RequesdetailDTO();
            GoodsDTO goodsDTO = new GoodsDTO();
            if (row.getCell(1) != null) {
                row.getCell(1).setCellType(CellType.STRING);
                String containerNo = row.getCell(1).getStringCellValue().trim();
                if (StrUtil.isNotBlank(containerNo)) {
                    boolean b = CheckUtil.verifyCntrCode(containerNo);
                    if (b) {
                        requesdetailDTO.setContainerNo(containerNo);
                        goodsDTO.setContainerNo(containerNo);
                    } else {
                        sb.append("行" + (i + 1) + "箱号格式错误：" + containerNo + ";");
                    }
                } else {
                    sb.append("行" + (i + 1) + "箱号不能为空" + ";");
                }

            }
            if (row.getCell(2) != null) {

                row.getCell(2).setCellType(CellType.STRING);
                String stringCellValue = row.getCell(2).getStringCellValue();
                if (StrUtil.isNotBlank(stringCellValue)) {
                    try {
                        //箱型代码校验
                        if (CollUtil.isNotEmpty(containerTypeData)) {
                            for (ContainerTypeData typeData : containerTypeData) {
                                if (typeData.getContainerTypeCode().equals(stringCellValue)) {
                                    requesdetailDTO.setContainerTypeCode(stringCellValue);
                                    requesdetailDTO.setContainerTypeName(typeData.getContainerTypeName());
                                    requesdetailDTO.setContainerType(typeData.getContainerTypeSize());
                                    break;
                                }
                            }
                        }
                        if (StrUtil.isEmpty(requesdetailDTO.getContainerTypeCode())) {
                            sb.append("行" + (i + 1) + "箱型代码无法匹配;");
                        }
                    } catch (Exception e) {
                        sb.append("行" + (i + 1) + "箱型代码异常;");
                    }

                } else {
                    sb.append("行" + (i + 1) + "箱型代码不能为空;");
                }
            }

            if (row.getCell(3) != null) {
                try {
                    row.getCell(3).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(3).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if ("自备箱".equals(stringCellValue)) {
                            requesdetailDTO.setBox("0");
                        } else if ("中铁箱".equals(stringCellValue)) {
                            requesdetailDTO.setBox("1");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "箱属不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "箱属异常;");
                }
            }

            if (row.getCell(4) != null) {
                try {
                    row.getCell(4).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(4).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if ("出口".equals(stringCellValue)) {
                            requesdetailDTO.setIdentification("E");
                        } else if ("进口".equals(stringCellValue)) {
                            requesdetailDTO.setIdentification("I");
                        } else if ("过境".equals(stringCellValue)) {
                            requesdetailDTO.setIdentification("P");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "类型不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "类型异常;");
                }
            }

            if (row.getCell(5) != null) {
                try {
                    row.getCell(5).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(5).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        requesdetailDTO.setGoodsName(stringCellValue);
                        goodsDTO.setGoodsChineseName(stringCellValue);
                    } else {
                        sb.append("行" + (i + 1) + "品名不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "品名异常;");
                }
            }

            if (row.getCell(6) != null) {
                try {
                    row.getCell(6).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(6).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        goodsDTO.setGoodsNums(stringCellValue);
                    } else {
                        sb.append("行" + (i + 1) + "件数不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "件数异常;");
                }
            }

            if (row.getCell(7) != null) {
                try {
                    row.getCell(7).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(7).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        goodsDTO.setGoodsWeight(Float.parseFloat(stringCellValue));
                    } else {
                        sb.append("行" + (i + 1) + "货重不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "货重异常;");
                }
            }

            if (row.getCell(8) != null) {
                try {
                    row.getCell(8).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(8).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        requesdetailDTO.setDeadWeight(Float.parseFloat(stringCellValue));
                    } else {
                        sb.append("行" + (i + 1) + "箱重不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "箱重异常;");
                }
            }

            if (row.getCell(9) != null) {
                try {
                    row.getCell(9).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(9).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        requesdetailDTO.setConsigneeName(stringCellValue);
                    } else {
                        sb.append("行" + (i + 1) + "收货人不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "收货人异常;");
                }
            }

            if (row.getCell(10) != null) {
                try {
                    row.getCell(10).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(10).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if (CollUtil.isNotEmpty(stationManagements)) {
                            for (StationManagement stationManagement : stationManagements) {
                                if (stationManagement.getStationCode().equals(stringCellValue)) {
                                    requesdetailDTO.setEndCompilation(stringCellValue);
                                    requesdetailDTO.setEndStationName(stationManagement.getStationName());
                                }
                            }
                        }
                        if (StrUtil.isEmpty(requesdetailDTO.getEndCompilation())) {
                            sb.append("行" + (i + 1) + "到站站编无法匹配;");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "到站站编不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "到站站编异常;");
                }
            }

            if (row.getCell(11) != null) {
                try {
                    row.getCell(11).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(11).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        requesdetailDTO.setConsignorName(stringCellValue);
                    } else {
                        sb.append("行" + (i + 1) + "发货人不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "发货人异常;");
                }
            }

            if (row.getCell(12) != null) {
                try {
                    row.getCell(12).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(12).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if (CollUtil.isNotEmpty(stationManagements)) {
                            for (StationManagement stationManagement : stationManagements) {
                                if (stationManagement.getStationCode().equals(stringCellValue)) {
                                    requesdetailDTO.setStationCompilation(stringCellValue);
                                    requesdetailDTO.setStartStationName(stationManagement.getStationName());
                                }
                            }
                        }
                        if (StrUtil.isEmpty(requesdetailDTO.getStationCompilation())) {
                            sb.append("行" + (i + 1) + "发站站编无法匹配;");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "发站站编不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "发站站编异常;");
                }
            }

            if (row.getCell(13) != null) {
                try {
                    row.getCell(13).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(13).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if (CollUtil.isNotEmpty(countryList)) {
                            for (SysDictVo sysDictVo : countryList) {
                                if (sysDictVo.getCode().equals(stringCellValue)) {
                                    requesdetailDTO.setConsignorCountryCode(stringCellValue);
                                    break;
                                }
                            }
                        }
                        if (StrUtil.isEmpty(requesdetailDTO.getConsignorCountryCode())) {
                            sb.append("行" + (i + 1) + "发货人所属国家代码无法匹配;");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "发货人所属国家代码不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "发货人所属国家代码异常;");
                }
            }

            if (row.getCell(14) != null) {
                try {
                    row.getCell(14).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(14).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if (CollUtil.isNotEmpty(countryList)) {
                            for (SysDictVo sysDictVo : countryList) {
                                if (sysDictVo.getCode().equals(stringCellValue)) {
                                    requesdetailDTO.setConsigneeCountryCode(stringCellValue);
                                    break;
                                }
                            }
                        }
                        if (StrUtil.isEmpty(requesdetailDTO.getConsigneeCountryCode())) {
                            sb.append("行" + (i + 1) + "收货人所属国家代码无法匹配;");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "收货人所属国家代码不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "收货人所属国家代码异常;");
                }
            }

            if (row.getCell(15) != null) {
                try {
                    row.getCell(15).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(15).getStringCellValue();
                    requesdetailDTO.setTitle(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "封号异常;");
                }
            }

            if (row.getCell(16) != null) {
                try {
                    row.getCell(16).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(16).getStringCellValue();
                    requesdetailDTO.setResveredField02(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "发货人声明异常;");
                }
            }

            if (row.getCell(17) != null) {
                try {
                    row.getCell(17).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(17).getStringCellValue();
                    requesdetailDTO.setPortAgent(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "口岸代理异常;");
                }
            }

            if (row.getCell(18) != null) {
                try {
                    row.getCell(18).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(18).getStringCellValue();
                    requesdetailDTO.setDomesticFreight(BigDecimal.valueOf(Double.parseDouble(stringCellValue)));
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "境内运费(人民币)异常;");
                }
            }

            if (row.getCell(19) != null) {
                try {
                    row.getCell(19).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(19).getStringCellValue();
                    requesdetailDTO.setOverseasFreightCny(BigDecimal.valueOf(Double.parseDouble(stringCellValue)));
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "境外运费(人民币)异常;");
                }
            }

            if (row.getCell(20) != null) {
                try {
                    row.getCell(20).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(20).getStringCellValue();
                    requesdetailDTO.setOverseasFreightOc(BigDecimal.valueOf(Double.parseDouble(stringCellValue)));
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "境外运费(原币)异常;");
                }
            }

            if (row.getCell(21) != null) {
                try {
                    row.getCell(21).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(21).getStringCellValue();
                    requesdetailDTO.setExchangeRate(BigDecimal.valueOf(Double.parseDouble(stringCellValue)));
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "境外汇率异常;");
                }
            }

            if (row.getCell(22) != null) {
                try {
                    row.getCell(22).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(22).getStringCellValue();
                    requesdetailDTO.setMonetaryType(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "境外币种异常;");
                }
            }

            if (row.getCell(23) != null) {
                try {
                    row.getCell(23).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(23).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if (StrUtil.isNotBlank(stringCellValue)) {
                            if ("是".equals(stringCellValue)) {
                                requesdetailDTO.setIsFull("1");
                            } else if ("否".equals(stringCellValue)) {
                                requesdetailDTO.setIsFull("0");
                            }
                        }
                    } else {
                        sb.append("行" + (i + 1) + "是否全程不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "是否全程异常;");
                }
            }

            if (row.getCell(24) != null) {
                try {
                    row.getCell(24).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(24).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if (StrUtil.isNotBlank(stringCellValue)) {
                            if ("是".equals(stringCellValue)) {
                                requesdetailDTO.setNonFerrous("1");
                            } else if ("否".equals(stringCellValue)) {
                                requesdetailDTO.setNonFerrous("0");
                            }
                        }
                    } else {
                        sb.append("行" + (i + 1) + "有色金属不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "有色金属异常;");
                }
            }

            List<GoodsDTO> goodsDTOList = new ArrayList<>();
            goodsDTOList.add(goodsDTO);
            requesdetailDTO.setGoods(goodsDTOList);
            requesdetailDTO.setOriCustcode(requesheaderDTO.getBookingCustcode());
            requesdetailDTO.setOriCustname(requesheaderDTO.getBookingCustname());
            details.add(requesdetailDTO);
        }
        if (sb.length() > 0) {
            throw new RuntimeException(sb.toString());
        }
        requesheaderDTO.setDetails(details);
        return saveBookingAndWaybillForCity(requesheaderDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R importedTemplateForYwTwo(MultipartFile file) throws Exception {
        boolean isLargeNumberOfObjects = ExcelObjectCheckerUtil.containsLargeNumberOfObjects(file.getInputStream(), 0);
        if (isLargeNumberOfObjects) {
            return new R<>(new Throwable("文件中存在未知对象文件，请使用快捷键<Ctrl+G或F5>，进行定位，删除对象后再进行提交！"));
        }
        /*// 获取文件大小（字节）
        long fileSize = file.getSize();
        // 判断文件大小
        if (fileSize > 1048576) { // 例如：判断是否大于1MB
            System.out.println("文件大小超过1MB");
        }*/
        Workbook workbook = null;
        try {
            InputStream is = file.getInputStream();
            //利用WorkbookFactory获取Sheet，无需判断版本
            workbook = WorkbookFactory.create(is);
        } catch (Exception e) {
            e.printStackTrace();
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        RequesheaderDTO requesheaderDTO = new RequesheaderDTO();
        //导入第一张sheet页
        Sheet sheet = workbook.getSheetAt(0);
        Row row0 = sheet.getRow(0);
        if (row0.getCell(1) != null) {
            row0.getCell(1).setCellType(CellType.STRING);
            String bookingCustcode = row0.getCell(1).getStringCellValue().trim();
            if (StrUtil.isNotBlank(bookingCustcode)) {
                if (bookingCustcode.contains("MC") || bookingCustcode.contains("MP")) {
                    return new R<>(new Throwable("订舱客户编码填写有误，请不要填写市平台编码（MCxxxxxx）作为订舱客户编码，请使用通过订舱端注册成为订舱客户的编码（CUSxxxxxx）。"));
                }
                CustomerPlatformInfo sel = new CustomerPlatformInfo();
                sel.setCustomerCode(bookingCustcode);
                sel.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
                sel.setDeleteFlag("N");
                List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoMapper.selectCustomerPlatformInfoList(sel);
                if (CollUtil.isNotEmpty(customerPlatformInfos)) {
                    CustomerInfo sel2 = new CustomerInfo();
                    sel2.setCustomerCode(bookingCustcode);
                    sel2.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
                    sel2.setDeleteFlag("N");
                    CustomerInfo customerInfo = customerInfoMapper.selectCustomegenNorInfoByCustomerCode(sel2);
                    if (customerInfo != null) {
                        requesheaderDTO.setBookingCustname(customerInfo.getCompanyName());
                    }
                    requesheaderDTO.setBookingCustcode(bookingCustcode);
                } else {
                    return new R<>(new Throwable("订舱客户编码在当前平台无法匹配！"));
                }
            } else {
                return new R<>(new Throwable("订舱客户编码不能为空！"));
            }
        } else {
            return new R<>(new Throwable("订舱客户编码不能为空！"));
        }

        String trip = null;
        String identification = null;
        if (row0.getCell(5) != null) {
            row0.getCell(5).setCellType(CellType.STRING);
            String shiftNo = row0.getCell(5).getStringCellValue().trim();
            if (StrUtil.isNotBlank(shiftNo)) {
                Shifmanagement shif = new Shifmanagement();
                shif.setShiftId(shiftNo);
                shif.setPlatformCode(userInfo.getPlatformCode());
                shif.setDeleteFlag("N");
                shif.setReleaseStatus("1");
                List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementListByLike(shif);
                if (CollUtil.isEmpty(shifmanagements)) {
                    return new R<>(new Throwable("该班次不存在！"));
                } else {
                    if ("0".equals(shifmanagements.get(0).getIsUsable())) {
                        return new R<>(new Throwable("该班次未对当前平台客户开放订舱！"));
                    }
                    if ("1".equals(shifmanagements.get(0).getIsUsable()) && StrUtil.isNotBlank(shifmanagements.get(0).getResveredField04()) && !shifmanagements.get(0).getResveredField04().contains(requesheaderDTO.getBookingCustcode())) {
                        return new R<>(new Throwable("该班次只对指定客户开放订舱！"));
                    }
                }
                requesheaderDTO.setShiftNo(shiftNo);
                requesheaderDTO.setResveredField06(shifmanagements.get(0).getOverseasAgency());
                requesheaderDTO.setTrip(shifmanagements.get(0).getTrip());

                if (CollUtil.isNotEmpty(shifmanagements) && StrUtil.isNotBlank(shifmanagements.get(0).getIdentification())) {
                    identification = shifmanagements.get(0).getIdentification();
                }
            } else {
                return new R<>(new Throwable("班次号不能为空！"));
            }
        } else {
            return new R<>(new Throwable("班次号不能为空！"));
        }
        //校验业务流程单
        FdBusCost sel = new FdBusCost();
        sel.setShiftNo(requesheaderDTO.getShiftNo());
        sel.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        sel.setCustomerCode(requesheaderDTO.getBookingCustcode());
        sel.setDeleteFlag("N");
        List<FdBusCost> list = fdBusCostMapper.selectFdBusCostList(sel);
        if (CollUtil.isNotEmpty(list)) {
            for (FdBusCost cost : list) {
                if ("1".equals(cost.getAuditStatus())) {
                    return new R<>(new Throwable("该客户存在审批中的业务流程单，请先驳回再导入：" + cost.getCostCode()));
                } else if ("2".equals(cost.getAuditStatus())) {
                    return new R<>(new Throwable("该客户存在已审批的业务流程单，不允许导入：" + cost.getCostCode()));
                }
            }
        }

        if (row0.getCell(7) != null) {
            row0.getCell(7).setCellType(CellType.STRING);
            String stringCellValue = row0.getCell(7).getStringCellValue();
            if (StrUtil.isNotBlank(stringCellValue)) {
                requesheaderDTO.setResveredField06(stringCellValue);
            }
        }

        if (row0.getCell(9) != null) {
            row0.getCell(9).setCellType(CellType.STRING);
            String stringCellValue = row0.getCell(9).getStringCellValue();
            requesheaderDTO.setRemarks(stringCellValue);
        }

        List<RequesdetailDTO> details = new ArrayList<>();
        StringBuffer sb = new StringBuffer();
        ContainerTypeData sel2 = new ContainerTypeData();
        sel2.setDeleteFlag("N");
        List<ContainerTypeData> containerTypeData = containerTypeDataMapper.selectContainerTypeDataList(sel2);
        StationManagement sel3 = new StationManagement();
        sel3.setDeleteFlag("N");
        List<StationManagement> stationManagements = stationManagementMapper.selectStationManagementList(sel3);
        String data = remoteAdminService.selectDictByType2("country_type");
        List<SysDictVo> countryList = JSONUtil.toList(JSONUtil.parseArray(data), SysDictVo.class);
        //获取行数
        int lastRowNum = sheet.getLastRowNum();
        for (int i = 3; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            boolean blankFlag = CheckUtil.isRowEmpty(row);
            if (blankFlag) {
                continue;
            }
            RequesdetailDTO requesdetailDTO = new RequesdetailDTO();
            GoodsDTO goodsDTO = new GoodsDTO();
            if (row.getCell(11) != null) {
                row.getCell(11).setCellType(CellType.STRING);
                String containerNo = row.getCell(11).getStringCellValue().trim();
                if (StrUtil.isNotBlank(containerNo)) {
                    boolean b = CheckUtil.verifyCntrCode(containerNo);
                    if (b) {
                        requesdetailDTO.setContainerNo(containerNo);
                        goodsDTO.setContainerNo(containerNo);
                    } else {
                        sb.append("行" + (i + 11) + "箱号格式错误：" + containerNo + ";");
                    }
                } else {
                    sb.append("行" + (i + 11) + "箱号不能为空" + ";");
                }

            } else {
                sb.append("行" + (i + 11) + "箱号不能为空" + ";");
            }
            if (row.getCell(9) != null) {
                try {
                    row.getCell(9).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(9).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        //箱型代码校验
                        if (CollUtil.isNotEmpty(containerTypeData)) {
                            if ("20".equals(stringCellValue) || "40".equals(stringCellValue) || "45".equals(stringCellValue)) {
                                stringCellValue = stringCellValue + "GP";
                            }
                            for (ContainerTypeData typeData : containerTypeData) {
                                if (typeData.getContainerTypeCode().equals(stringCellValue)) {
                                    requesdetailDTO.setContainerTypeCode(stringCellValue);
                                    requesdetailDTO.setContainerTypeName(typeData.getContainerTypeName());
                                    requesdetailDTO.setContainerType(typeData.getContainerTypeSize());
                                    break;
                                }
                            }
                        }
                        if (StrUtil.isEmpty(requesdetailDTO.getContainerTypeCode())) {
                            sb.append("行" + (i + 1) + "箱型无法匹配;");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "箱型不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "箱型异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "箱型不能为空;");
            }

            if (row.getCell(10) != null) {
                try {
                    row.getCell(10).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(10).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if ("自备箱".equals(stringCellValue)) {
                            requesdetailDTO.setBox("0");
                        } else if ("中铁箱".equals(stringCellValue)) {
                            requesdetailDTO.setBox("1");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "箱属不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "箱属异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "箱属不能为空;");
            }

            if (row.getCell(1) != null) {
                try {
                    row.getCell(1).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(1).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if ("出口".equals(stringCellValue)) {
                            requesdetailDTO.setIdentification("E");
                        } else if ("进口".equals(stringCellValue)) {
                            requesdetailDTO.setIdentification("I");
                        } else if ("过境".equals(stringCellValue)) {
                            requesdetailDTO.setIdentification("P");
                        }
                        if (StrUtil.isNotBlank(identification) && !identification.equals(requesdetailDTO.getIdentification())) {
                            sb.append("行" + (i + 1) + "箱进出口过境类型与班列类型不符");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "类型不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "类型异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "类型不能为空;");
            }

            if (row.getCell(8) != null) {
                try {
                    row.getCell(8).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(8).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        requesdetailDTO.setGoodsName(stringCellValue);
                        goodsDTO.setGoodsChineseName(stringCellValue);
                    } else {
                        sb.append("行" + (i + 1) + "品名不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "品名异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "品名不能为空;");
            }

            if (row.getCell(12) != null) {
                try {
                    row.getCell(12).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(12).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        goodsDTO.setGoodsNums(stringCellValue);
                    } else {
                        sb.append("行" + (i + 1) + "件数不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "件数异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "件数不能为空;");
            }

            if (row.getCell(13) != null) {
                try {
                    row.getCell(13).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(13).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        goodsDTO.setGoodsWeight(Float.parseFloat(stringCellValue));
                    } else {
                        sb.append("行" + (i + 1) + "货重不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "货重异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "货重不能为空;");
            }

            if (row.getCell(14) != null) {
                try {
                    row.getCell(14).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(14).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        requesdetailDTO.setDeadWeight(Float.parseFloat(stringCellValue));
                    } else {
                        sb.append("行" + (i + 1) + "箱重不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "箱重异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "箱重不能为空;");
            }

            if (row.getCell(3) != null) {
                try {
                    row.getCell(3).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(3).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        requesdetailDTO.setConsigneeName(stringCellValue);
                    } else {
                        sb.append("行" + (i + 1) + "收货人不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "收货人异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "收货人不能为空;");
            }

            if (row.getCell(17) != null) {
                try {
                    String stringCellValue = CellValueUtil.getCellValueString(row.getCell(17));
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        requesdetailDTO.setGoodsOwner(stringCellValue);
                    }
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "货主异常;");
                }
            }

            if (row.getCell(6) != null) {
                try {
                    row.getCell(6).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(6).getStringCellValue().trim();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if (CollUtil.isNotEmpty(stationManagements)) {
                            for (StationManagement stationManagement : stationManagements) {
                                if (stationManagement.getStationName().equals(stringCellValue)) {
                                    requesdetailDTO.setEndCompilation(stationManagement.getStationCode());
                                    requesdetailDTO.setEndStationName(stringCellValue);
                                }
                            }
                        }
                        if (StrUtil.isEmpty(requesdetailDTO.getEndCompilation())) {
                            sb.append("行" + (i + 1) + "到站无法匹配;");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "到站不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "到站异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "到站不能为空;");
            }

            if (row.getCell(4) != null) {
                try {
                    row.getCell(4).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(4).getStringCellValue().trim();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if (CollUtil.isNotEmpty(stationManagements)) {
                            for (StationManagement stationManagement : stationManagements) {
                                if (stationManagement.getStationName().equals(stringCellValue)) {
                                    requesdetailDTO.setStationCompilation(stationManagement.getStationCode());
                                    requesdetailDTO.setStartStationName(stringCellValue);
                                    if (CollUtil.isNotEmpty(countryList)) {
                                        for (SysDictVo sysDictVo : countryList) {
                                            if (sysDictVo.getName().equals(stationManagement.getNation())) {
                                                requesdetailDTO.setConsigneeCountryCode(sysDictVo.getCode());
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (StrUtil.isEmpty(requesdetailDTO.getStationCompilation())) {
                            sb.append("行" + (i + 1) + "发站无法匹配;");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "发站不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "发站异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "发站不能为空;");
            }

            if (row.getCell(5) != null) {
                try {
                    row.getCell(5).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(5).getStringCellValue().trim();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if (CollUtil.isNotEmpty(countryList)) {
                            for (SysDictVo sysDictVo : countryList) {
                                if (sysDictVo.getName().equals(stringCellValue)) {
                                    requesdetailDTO.setConsignorCountryCode(sysDictVo.getCode());
                                    break;
                                }
                            }
                        }
                        if (StrUtil.isEmpty(requesdetailDTO.getConsignorCountryCode())) {
                            sb.append("行" + (i + 1) + "目的国无法匹配;");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "目的国不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "目的国异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "目的国不能为空;");
            }

            if (row.getCell(21) != null) {
                try {
                    row.getCell(21).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(21).getStringCellValue();
                    requesdetailDTO.setTitle(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "海关封异常;");
                }
            }

            if (row.getCell(7) != null) {
                try {
                    row.getCell(7).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(7).getStringCellValue();
                    requesdetailDTO.setPortAgent(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "口岸代理异常;");
                }
            }

            if (row.getCell(25) != null) {
                try {
                    BigDecimal value = CellValueUtil.getCellValueBigDecimal(row.getCell(25));
                    if (value != null) {
                        requesdetailDTO.setDomesticFreight(value);
                    } else {
                        requesdetailDTO.setDomesticFreight(BigDecimal.ZERO);
                    }
                } catch (Exception e) {
                    requesdetailDTO.setDomesticFreight(BigDecimal.ZERO);
                }
            }

            if (row.getCell(26) != null) {
                try {
                    BigDecimal value = CellValueUtil.getCellValueBigDecimal(row.getCell(26));
                    if (value != null) {
                        requesdetailDTO.setOverseasFreightOc(value);
                    } else {
                        requesdetailDTO.setOverseasFreightOc(BigDecimal.ZERO);
                    }
                } catch (Exception e) {
                    requesdetailDTO.setOverseasFreightOc(BigDecimal.ZERO);
                }
            } else {
                requesdetailDTO.setOverseasFreightOc(BigDecimal.ZERO);
            }

            if (row.getCell(27) != null) {
                try {
                    BigDecimal value = CellValueUtil.getCellValueBigDecimal(row.getCell(27));
                    if (value != null) {
                        requesdetailDTO.setExchangeRate(value);
                    } else {
                        requesdetailDTO.setExchangeRate(BigDecimal.valueOf(1D));
                    }
                } catch (Exception e) {
                    requesdetailDTO.setExchangeRate(BigDecimal.valueOf(1D));
                }
            } else {
                requesdetailDTO.setExchangeRate(BigDecimal.valueOf(1D));
            }

            requesdetailDTO.setOverseasFreightCny(requesdetailDTO.getOverseasFreightOc().multiply(requesdetailDTO.getExchangeRate()));

            if (row.getCell(28) != null) {
                try {
                    row.getCell(28).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(28).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        requesdetailDTO.setMonetaryType(stringCellValue);
                    } else {
                        requesdetailDTO.setMonetaryType("人民币");
                    }
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "境外币种异常;");
                }
            } else {
                requesdetailDTO.setMonetaryType("人民币");
            }

            if (row.getCell(15) != null) {
                try {
                    row.getCell(15).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(15).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if (StrUtil.isNotBlank(stringCellValue)) {
                            if ("是".equals(stringCellValue)) {
                                requesdetailDTO.setIsFull("1");
                            } else if ("否".equals(stringCellValue)) {
                                requesdetailDTO.setIsFull("0");
                            }
                        }
                    } else {
                        sb.append("行" + (i + 1) + "是否全程不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "是否全程异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "是否全程不能为空;");
            }

            if (row.getCell(16) != null) {
                try {
                    row.getCell(16).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(16).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if (StrUtil.isNotBlank(stringCellValue)) {
                            if ("是".equals(stringCellValue)) {
                                requesdetailDTO.setNonFerrous("1");
                            } else if ("否".equals(stringCellValue)) {
                                requesdetailDTO.setNonFerrous("0");
                            }
                        }
                    } else {
                        sb.append("行" + (i + 1) + "有色金属不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "有色金属异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "有色金属不能为空;");
            }

            if (row.getCell(31) != null) {
                try {
                    row.getCell(31).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(31).getStringCellValue();
                    requesdetailDTO.setRemarks(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "箱备注异常;");
                }
            }

            if (row.getCell(18) != null) {
                try {
                    row.getCell(18).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(18).getStringCellValue();
                    requesdetailDTO.setGoodsOrigin(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "境内货源地/目的地异常;");
                }
            }

            if (row.getCell(19) != null) {
                try {
                    row.getCell(19).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(19).getStringCellValue();
                    requesdetailDTO.setClearanceNumber(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "报关单号异常;");
                }
            }

            if (row.getCell(21) != null) {
                try {
                    row.getCell(21).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(21).getStringCellValue();
                    requesdetailDTO.setCustomsSeal(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "海关封异常;");
                }
            }

            if (row.getCell(22) != null) {
                try {
                    row.getCell(22).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(22).getStringCellValue();
                    requesdetailDTO.setTrainNumber(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "车号异常;");
                }
            }

            if (row.getCell(23) != null) {
                try {
                    row.getCell(23).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(23).getStringCellValue();
                    requesdetailDTO.setWaybillDemandNumber(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "运单需求号异常;");
                }
            }

            if (row.getCell(24) != null) {
                try {
                    row.getCell(24).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(24).getStringCellValue();
                    requesdetailDTO.setWaybillLnNumber(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "国联运单号异常;");
                }
            }

            if (row.getCell(32) != null) {
                try {
                    row.getCell(32).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(32).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        requesdetailDTO.setYfDomesticFreight(BigDecimal.valueOf(Double.parseDouble(stringCellValue)));
                    } else {
                        requesdetailDTO.setYfDomesticFreight(BigDecimal.ZERO);
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "应付境内运费（人民币）异常;");
                }
            } else {
                requesdetailDTO.setYfDomesticFreight(BigDecimal.ZERO);
            }

            if (row.getCell(33) != null) {
                try {
                    row.getCell(33).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(33).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        requesdetailDTO.setYfOverseasFreightOc(BigDecimal.valueOf(Double.parseDouble(stringCellValue)));
                    } else {
                        requesdetailDTO.setYfOverseasFreightOc(BigDecimal.ZERO);
                    }
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "应付境内运费（人民币）异常;");
                }
            } else {
                requesdetailDTO.setYfOverseasFreightOc(BigDecimal.ZERO);
            }

            if (row.getCell(34) != null) {
                try {
                    row.getCell(34).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(34).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        requesdetailDTO.setYfExchangeRate(BigDecimal.valueOf(Double.parseDouble(stringCellValue)));
                    } else {
                        requesdetailDTO.setYfExchangeRate(BigDecimal.valueOf(1D));
                    }
                } catch (Exception e) {
                    requesdetailDTO.setYfExchangeRate(BigDecimal.valueOf(1D));
                }
            } else {
                requesdetailDTO.setYfExchangeRate(BigDecimal.valueOf(1D));
            }

            requesdetailDTO.setYfOverseasFreightCny(requesdetailDTO.getYfOverseasFreightOc().multiply(requesdetailDTO.getYfExchangeRate()));

            if (row.getCell(35) != null) {
                try {
                    row.getCell(35).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(35).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        requesdetailDTO.setYfMonetaryType(stringCellValue);
                    } else {
                        requesdetailDTO.setYfMonetaryType("人民币");
                    }
                } catch (Exception e) {
                    requesdetailDTO.setYfMonetaryType("人民币");
                }
            } else {
                requesdetailDTO.setYfMonetaryType("人民币");
            }

            List<GoodsDTO> goodsDTOList = new ArrayList<>();
            goodsDTOList.add(goodsDTO);
            requesdetailDTO.setGoods(goodsDTOList);
            requesdetailDTO.setOriCustcode(requesheaderDTO.getBookingCustcode());
            requesdetailDTO.setOriCustname(requesheaderDTO.getBookingCustname());
            details.add(requesdetailDTO);
        }
        if (sb.length() > 0) {
            throw new RuntimeException(sb.toString());
        }
        if (CollUtil.isEmpty(details)) {
            return new R<>(new Throwable("未获取到箱信息，请检查导入模板"));
        }
        requesheaderDTO.setDetails(details);
        return saveBookingAndWaybillForCity(requesheaderDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R importedTemplateForYwThree(MultipartFile file) throws Exception {
        boolean isLargeNumberOfObjects = ExcelObjectCheckerUtil.containsLargeNumberOfObjects(file.getInputStream(), 0);
        if (isLargeNumberOfObjects) {
            return new R<>(new Throwable("文件中存在未知对象文件，请使用快捷键<Ctrl+G或F5>，进行定位，删除对象后再进行提交！"));
        }
        Workbook workbook = null;
        try {
            InputStream is = file.getInputStream();
            //利用WorkbookFactory获取Sheet，无需判断版本
            workbook = WorkbookFactory.create(is);
        } catch (Exception e) {
            e.printStackTrace();
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        List<RequesheaderDTO> requesheaders = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        //导入第一张sheet页
        Sheet sheet = workbook.getSheetAt(0);
        //获取表头数据
        RequesheaderDTO requesheaderDTO = new RequesheaderDTO();
        List<Shifmanagement> shifmanagements = new ArrayList<>();
        String identification = null;

        Row row0 = sheet.getRow(0);
        if (row0.getCell(1) != null) {
            row0.getCell(1).setCellType(CellType.STRING);
            String shiftNo = row0.getCell(1).getStringCellValue().trim();
            if (StrUtil.isNotBlank(shiftNo)) {
                Shifmanagement shif = new Shifmanagement();
                shif.setShiftId(shiftNo);
                shif.setPlatformCode(userInfo.getPlatformCode());
                shif.setDeleteFlag("N");
                shif.setReleaseStatus("1");
                shifmanagements = shifmanagementMapper.selectShifmanagementListByLike(shif);
                if (CollUtil.isEmpty(shifmanagements)) {
                    return new R<>(new Throwable("该班次不存在！"));
                } else {
                    if ("0".equals(shifmanagements.get(0).getIsUsable())) {
                        return new R<>(new Throwable("该班次未对当前平台客户开放订舱！"));
                    }
                }
                requesheaderDTO.setShiftNo(shiftNo);
                requesheaderDTO.setResveredField06(shifmanagements.get(0).getOverseasAgency());
                if (CollUtil.isNotEmpty(shifmanagements) && StrUtil.isNotBlank(shifmanagements.get(0).getIdentification())) {
                    identification = shifmanagements.get(0).getIdentification();
                }
            } else {
                return new R<>(new Throwable("班次号不能为空！"));
            }

        }

        if (row0.getCell(3) != null) {
            row0.getCell(3).setCellType(CellType.STRING);
            String stringCellValue = row0.getCell(3).getStringCellValue();
            if (StrUtil.isNotBlank(stringCellValue)) {
                requesheaderDTO.setResveredField06(stringCellValue);
            }
        }

        if (row0.getCell(5) != null) {
            row0.getCell(5).setCellType(CellType.STRING);
            String stringCellValue = row0.getCell(5).getStringCellValue();
            requesheaderDTO.setRemarks(stringCellValue);
        }

        //获取每行中的客户信息
        int lastRowNum = sheet.getLastRowNum();
        for (int i = 3; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            if (row.getCell(32) != null) {
                row.getCell(32).setCellType(CellType.STRING);
                String bookingCustcode = row.getCell(32).getStringCellValue().trim();
                if (StrUtil.isBlank(bookingCustcode)) {
                    return new R<>(new Throwable("行" + (i + 1) + "订舱客户编码不能为空！"));
                }
                if (bookingCustcode.contains("MC") || bookingCustcode.contains("MP")) {
                    return new R<>(new Throwable("订舱客户编码填写有误，请不要填写市平台编码（MCxxxxxx）作为订舱客户编码，请使用通过订舱端注册成为订舱客户的编码（CUSxxxxxx）。"));
                }
                if (!map.containsKey(bookingCustcode)) {
                    //通过map校验是否重复
                    map.put(bookingCustcode, bookingCustcode);

                    if (CollUtil.isNotEmpty(shifmanagements)) {
                        if ("1".equals(shifmanagements.get(0).getIsUsable()) && StrUtil.isNotBlank(shifmanagements.get(0).getResveredField04()) && !shifmanagements.get(0).getResveredField04().contains(bookingCustcode)) {
                            return new R<>(new Throwable("该班次只对指定客户开放订舱，" + bookingCustcode + "不允许订舱！"));
                        }
                    }

                    CustomerPlatformInfo sel = new CustomerPlatformInfo();
                    sel.setCustomerCode(bookingCustcode);
                    sel.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
                    sel.setDeleteFlag("N");
                    List<CustomerPlatformInfo> customerPlatformInfos = customerPlatformInfoMapper.selectCustomerPlatformInfoList(sel);
                    if (CollUtil.isNotEmpty(customerPlatformInfos)) {
                        RequesheaderDTO dto = new RequesheaderDTO();
                        dto.setBookingCustcode(bookingCustcode);
                        dto.setShiftNo(requesheaderDTO.getShiftNo());
                        dto.setResveredField06(requesheaderDTO.getResveredField06());
                        dto.setRemarks(requesheaderDTO.getRemarks());

                        if (row.getCell(33) != null) {
                            row.getCell(33).setCellType(CellType.STRING);
                            String bookingCustname = row.getCell(33).getStringCellValue().trim();
                            dto.setBookingCustname(bookingCustname);
                        }

                        CustomerInfo sel2 = new CustomerInfo();
                        sel2.setCustomerCode(bookingCustcode);
                        sel2.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
                        sel2.setDeleteFlag("N");
                        CustomerInfo customerInfo = customerInfoMapper.selectCustomegenNorInfoByCustomerCode(sel2);
                        if (customerInfo != null) {
                            dto.setBookingCustname(customerInfo.getCompanyName());
                        }

                        //校验业务流程单
                        FdBusCost fdBusCost = new FdBusCost();
                        fdBusCost.setShiftNo(dto.getShiftNo());
                        fdBusCost.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
                        fdBusCost.setCustomerCode(dto.getBookingCustcode());
                        fdBusCost.setDeleteFlag("N");
                        List<FdBusCost> list = fdBusCostMapper.selectFdBusCostList(fdBusCost);
                        if (CollUtil.isNotEmpty(list)) {
                            for (FdBusCost cost : list) {
                                if ("1".equals(cost.getAuditStatus())) {
                                    return new R<>(new Throwable("该客户: " + dto.getBookingCustname() + " 存在审批中的业务流程单，请先驳回再导入：" + cost.getCostCode()));
                                } else if ("2".equals(cost.getAuditStatus())) {
                                    return new R<>(new Throwable("该客户: " + dto.getBookingCustname() + " 存在已审批的业务流程单，不允许导入：" + cost.getCostCode()));
                                }
                            }
                        }
                        requesheaders.add(dto);
                    } else {
                        return new R<>(new Throwable("行" + (i + 1) + "订舱客户编码在当前平台无法匹配！"));
                    }
                }
            }
        }


        List<RequesdetailDTO> details = new ArrayList<>();
        StringBuffer sb = new StringBuffer();
        ContainerTypeData sel2 = new ContainerTypeData();
        sel2.setDeleteFlag("N");
        List<ContainerTypeData> containerTypeData = containerTypeDataMapper.selectContainerTypeDataList(sel2);
        StationManagement sel3 = new StationManagement();
        sel3.setDeleteFlag("N");
        List<StationManagement> stationManagements = stationManagementMapper.selectStationManagementList(sel3);
        String data = remoteAdminService.selectDictByType2("country_type");
        List<SysDictVo> countryList = JSONUtil.toList(JSONUtil.parseArray(data), SysDictVo.class);
        //获取行数
        for (int i = 3; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            boolean blankFlag = CheckUtil.isRowEmpty(row);
            if (blankFlag) {
                continue;
            }
            RequesdetailDTO requesdetailDTO = new RequesdetailDTO();
            GoodsDTO goodsDTO = new GoodsDTO();
            if (row.getCell(11) != null) {
                row.getCell(11).setCellType(CellType.STRING);
                String containerNo = row.getCell(11).getStringCellValue().trim();
                if (StrUtil.isNotBlank(containerNo)) {
                    boolean b = CheckUtil.verifyCntrCode(containerNo);
                    if (b) {
                        requesdetailDTO.setContainerNo(containerNo);
                        goodsDTO.setContainerNo(containerNo);
                    } else {
                        sb.append("行" + (i + 1) + "箱号格式错误：" + containerNo + ";");
                    }
                } else {
                    sb.append("行" + (i + 1) + "箱号不能为空" + ";");
                }

            } else {
                sb.append("行" + (i + 1) + "箱号不能为空" + ";");
            }
            if (row.getCell(9) != null) {
                try {
                    row.getCell(9).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(9).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        //箱型代码校验
                        if (CollUtil.isNotEmpty(containerTypeData)) {
                            if ("20".equals(stringCellValue) || "40".equals(stringCellValue) || "45".equals(stringCellValue)) {
                                stringCellValue = stringCellValue + "GP";
                            }
                            for (ContainerTypeData typeData : containerTypeData) {
                                if (typeData.getContainerTypeCode().equals(stringCellValue)) {
                                    requesdetailDTO.setContainerTypeCode(stringCellValue);
                                    requesdetailDTO.setContainerTypeName(typeData.getContainerTypeName());
                                    requesdetailDTO.setContainerType(typeData.getContainerTypeSize());
                                    break;
                                }
                            }
                        }
                        if (StrUtil.isEmpty(requesdetailDTO.getContainerTypeCode())) {
                            sb.append("行" + (i + 1) + "箱型无法匹配;");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "箱型不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "箱型异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "箱型不能为空;");
            }

            if (row.getCell(10) != null) {
                try {
                    row.getCell(10).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(10).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if ("自备箱".equals(stringCellValue)) {
                            requesdetailDTO.setBox("0");
                        } else if ("中铁箱".equals(stringCellValue)) {
                            requesdetailDTO.setBox("1");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "箱属不能为空;");
                    }
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "箱属异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "箱属不能为空;");
            }
            if (StrUtil.isBlank(requesdetailDTO.getBox())) {
                sb.append("行" + (i + 1) + "箱属不能为空;");
            }

            if (row.getCell(1) != null) {
                try {
                    row.getCell(1).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(1).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if ("出口".equals(stringCellValue)) {
                            requesdetailDTO.setIdentification("E");
                        } else if ("进口".equals(stringCellValue)) {
                            requesdetailDTO.setIdentification("I");
                        } else if ("过境".equals(stringCellValue)) {
                            requesdetailDTO.setIdentification("P");
                        }
                        if (StrUtil.isNotBlank(identification) && !identification.equals(requesdetailDTO.getIdentification())) {
                            sb.append("行" + (i + 1) + "箱进出口过境类型与班列类型不符");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "类型不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "类型异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "类型不能为空;");
            }
            if (StrUtil.isBlank(requesdetailDTO.getIdentification())) {
                sb.append("行" + (i + 1) + "类型异常;");
            }

            if (row.getCell(8) != null) {
                try {
                    row.getCell(8).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(8).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        requesdetailDTO.setGoodsName(stringCellValue);
                        goodsDTO.setGoodsChineseName(stringCellValue);
                    } else {
                        sb.append("行" + (i + 1) + "品名不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "品名异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "品名不能为空;");
            }

            if (row.getCell(12) != null) {
                try {
                    row.getCell(12).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(12).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        goodsDTO.setGoodsNums(stringCellValue);
                    } else {
                        sb.append("行" + (i + 1) + "件数不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "件数异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "件数不能为空;");
            }

            if (row.getCell(13) != null) {
                try {
                    row.getCell(13).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(13).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        goodsDTO.setGoodsWeight(Float.parseFloat(stringCellValue));
                    } else {
                        sb.append("行" + (i + 1) + "货重不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "货重异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "货重不能为空;");
            }

            if (row.getCell(14) != null) {
                try {
                    row.getCell(14).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(14).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {

                    }
                    requesdetailDTO.setDeadWeight(Float.parseFloat(stringCellValue));
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "箱重异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "箱重不能为空;");
            }

            if (row.getCell(3) != null) {
                try {
                    row.getCell(3).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(3).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        requesdetailDTO.setConsigneeName(stringCellValue);
                    } else {
                        sb.append("行" + (i + 1) + "收货人不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "收货人异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "收货人不能为空;");
            }

            if (row.getCell(6) != null) {
                try {
                    row.getCell(6).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(6).getStringCellValue().trim();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if (CollUtil.isNotEmpty(stationManagements)) {
                            for (StationManagement stationManagement : stationManagements) {
                                if (stationManagement.getStationName().equals(stringCellValue)) {
                                    requesdetailDTO.setEndCompilation(stationManagement.getStationCode());
                                    requesdetailDTO.setEndStationName(stringCellValue);
                                }
                            }
                        }
                        if (StrUtil.isEmpty(requesdetailDTO.getEndCompilation())) {
                            sb.append("行" + (i + 1) + "到站无法匹配;");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "到站不能为空;");
                    }
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "到站异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "到站不能为空;");
            }

            if (row.getCell(4) != null) {
                try {
                    row.getCell(4).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(4).getStringCellValue().trim();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if (CollUtil.isNotEmpty(stationManagements)) {
                            for (StationManagement stationManagement : stationManagements) {
                                if (stationManagement.getStationName().equals(stringCellValue)) {
                                    requesdetailDTO.setStationCompilation(stationManagement.getStationCode());
                                    requesdetailDTO.setStartStationName(stringCellValue);
                                    if (CollUtil.isNotEmpty(countryList)) {
                                        for (SysDictVo sysDictVo : countryList) {
                                            if (sysDictVo.getName().equals(stationManagement.getNation())) {
                                                requesdetailDTO.setConsigneeCountryCode(sysDictVo.getCode());
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (StrUtil.isEmpty(requesdetailDTO.getStationCompilation())) {
                            sb.append("行" + (i + 1) + "发站无法匹配;");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "发站不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "发站异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "发站不能为空;");
            }

            if (row.getCell(5) != null) {
                try {
                    row.getCell(5).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(5).getStringCellValue().trim();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if (CollUtil.isNotEmpty(countryList)) {
                            for (SysDictVo sysDictVo : countryList) {
                                if (sysDictVo.getName().equals(stringCellValue)) {
                                    requesdetailDTO.setConsignorCountryCode(sysDictVo.getCode());
                                    break;
                                }
                            }
                        }
                        if (StrUtil.isEmpty(requesdetailDTO.getConsignorCountryCode())) {
                            sb.append("行" + (i + 1) + "目的国无法匹配;");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "目的国不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "目的国异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "目的国不能为空;");
            }

            if (row.getCell(21) != null) {
                try {
                    row.getCell(21).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(21).getStringCellValue();
                    requesdetailDTO.setTitle(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "海关封异常;");
                }
            }

            if (row.getCell(7) != null) {
                try {
                    row.getCell(7).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(7).getStringCellValue();
                    requesdetailDTO.setPortAgent(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "口岸代理异常;");
                }
            }

            if (row.getCell(25) != null) {
                try {
                    BigDecimal value = CellValueUtil.getCellValueBigDecimal(row.getCell(25));
                    if (value != null) {
                        requesdetailDTO.setDomesticFreight(value);
                    } else {
                        requesdetailDTO.setDomesticFreight(BigDecimal.ZERO);
                    }

                } catch (Exception e) {
                    requesdetailDTO.setDomesticFreight(BigDecimal.ZERO);
                }
            } else {
                requesdetailDTO.setDomesticFreight(BigDecimal.ZERO);
            }

            if (row.getCell(26) != null) {
                try {
                    BigDecimal value = CellValueUtil.getCellValueBigDecimal(row.getCell(26));
                    if (value != null) {
                        requesdetailDTO.setOverseasFreightOc(value);
                    } else {
                        requesdetailDTO.setOverseasFreightOc(BigDecimal.ZERO);
                    }

                } catch (Exception e) {
                    requesdetailDTO.setOverseasFreightOc(BigDecimal.ZERO);
                }
            } else {
                requesdetailDTO.setOverseasFreightOc(BigDecimal.ZERO);
            }

            if (row.getCell(27) != null) {
                try {
                    BigDecimal value = CellValueUtil.getCellValueBigDecimal(row.getCell(27));
                    if (value != null) {
                        requesdetailDTO.setExchangeRate(value);
                    } else {
                        requesdetailDTO.setExchangeRate(BigDecimal.valueOf(1D));
                    }
                } catch (Exception e) {
                    requesdetailDTO.setExchangeRate(BigDecimal.valueOf(1D));
                }
            } else {
                requesdetailDTO.setExchangeRate(BigDecimal.valueOf(1D));
            }

            requesdetailDTO.setOverseasFreightCny(requesdetailDTO.getOverseasFreightOc().multiply(requesdetailDTO.getExchangeRate()));

            if (row.getCell(28) != null) {
                try {
                    row.getCell(28).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(28).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        requesdetailDTO.setMonetaryType(stringCellValue);
                    } else {
                        requesdetailDTO.setMonetaryType("人民币");
                    }
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "境外币种异常;");
                }
            } else {
                requesdetailDTO.setMonetaryType("人民币");
            }

            if (row.getCell(15) != null) {
                try {
                    row.getCell(15).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(15).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if (StrUtil.isNotBlank(stringCellValue)) {
                            if ("是".equals(stringCellValue)) {
                                requesdetailDTO.setIsFull("1");
                            } else if ("否".equals(stringCellValue)) {
                                requesdetailDTO.setIsFull("0");
                            }
                        }
                    } else {
                        sb.append("行" + (i + 1) + "是否全程不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "是否全程异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "是否全程不能为空;");
            }

            if (row.getCell(16) != null) {
                try {
                    row.getCell(16).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(16).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        if (StrUtil.isNotBlank(stringCellValue)) {
                            if ("是".equals(stringCellValue)) {
                                requesdetailDTO.setNonFerrous("1");
                            } else if ("否".equals(stringCellValue)) {
                                requesdetailDTO.setNonFerrous("0");
                            }
                        }
                    } else {
                        sb.append("行" + (i + 1) + "有色金属不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "有色金属异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "有色金属不能为空;");
            }

            if (row.getCell(17) != null) {
                try {
                    String stringCellValue = CellValueUtil.getCellValueString(row.getCell(17));
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        requesdetailDTO.setGoodsOwner(stringCellValue);
                    }
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "货主异常;");
                }
            }

            if (row.getCell(31) != null) {
                try {
                    row.getCell(31).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(31).getStringCellValue();
                    requesdetailDTO.setRemarks(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "箱备注异常;");
                }
            }

            if (row.getCell(18) != null) {
                try {
                    row.getCell(18).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(18).getStringCellValue();
                    requesdetailDTO.setGoodsOrigin(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "境内货源地/目的地异常;");
                }
            }

            if (row.getCell(19) != null) {
                try {
                    row.getCell(19).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(19).getStringCellValue();
                    requesdetailDTO.setClearanceNumber(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "报关单号异常;");
                }
            }

            if (row.getCell(21) != null) {
                try {
                    row.getCell(21).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(21).getStringCellValue();
                    requesdetailDTO.setCustomsSeal(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "海关封异常;");
                }
            }

            if (row.getCell(22) != null) {
                try {
                    row.getCell(22).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(22).getStringCellValue();
                    requesdetailDTO.setTrainNumber(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "车号异常;");
                }
            }

            if (row.getCell(23) != null) {
                try {
                    row.getCell(23).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(23).getStringCellValue();
                    requesdetailDTO.setWaybillDemandNumber(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "运单需求号异常;");
                }
            }

            if (row.getCell(24) != null) {
                try {
                    row.getCell(24).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(24).getStringCellValue();
                    requesdetailDTO.setWaybillLnNumber(stringCellValue);
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "国联运单号异常;");
                }
            }

            if (row.getCell(32) != null) {
                try {
                    row.getCell(32).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(32).getStringCellValue().trim();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        requesdetailDTO.setOriCustcode(stringCellValue);
                        if (StrUtil.isBlank(requesdetailDTO.getOriCustcode())) {
                            sb.append("行" + (i + 1) + "客户编码不能为空;");
                        }
                    } else {
                        sb.append("行" + (i + 1) + "客户编码不能为空;");
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "客户编码异常;");
                }
            } else {
                sb.append("行" + (i + 1) + "客户编码不能为空;");
            }

            if (row.getCell(34) != null) {
                try {
                    BigDecimal value = CellValueUtil.getCellValueBigDecimal(row.getCell(34));
                    if (value != null) {
                        requesdetailDTO.setYfDomesticFreight(value);
                    } else {
                        requesdetailDTO.setYfDomesticFreight(BigDecimal.ZERO);
                    }

                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "应付境内运费（人民币）异常;");
                }
            } else {
                requesdetailDTO.setYfDomesticFreight(BigDecimal.ZERO);
            }

            if (row.getCell(35) != null) {
                try {
                    BigDecimal value = CellValueUtil.getCellValueBigDecimal(row.getCell(35));
                    if (value != null) {
                        requesdetailDTO.setYfOverseasFreightOc(value);
                    } else {
                        requesdetailDTO.setYfOverseasFreightOc(BigDecimal.ZERO);
                    }
                } catch (Exception e) {
                    sb.append("行" + (i + 1) + "应付境内运费（人民币）异常;");
                }
            } else {
                requesdetailDTO.setYfOverseasFreightOc(BigDecimal.ZERO);
            }

            if (row.getCell(36) != null) {
                try {
                    BigDecimal value = CellValueUtil.getCellValueBigDecimal(row.getCell(36));
                    if (value != null) {
                        requesdetailDTO.setYfExchangeRate(value);
                    } else {
                        requesdetailDTO.setYfExchangeRate(BigDecimal.valueOf(1D));
                    }
                } catch (Exception e) {
                    requesdetailDTO.setYfExchangeRate(BigDecimal.valueOf(1D));
                }
            } else {
                requesdetailDTO.setYfExchangeRate(BigDecimal.valueOf(1D));
            }

            requesdetailDTO.setYfOverseasFreightCny(requesdetailDTO.getYfOverseasFreightOc().multiply(requesdetailDTO.getYfExchangeRate()));

            if (row.getCell(37) != null) {
                try {
                    row.getCell(37).setCellType(CellType.STRING);
                    String stringCellValue = row.getCell(37).getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        requesdetailDTO.setYfMonetaryType(stringCellValue);
                    } else {
                        requesdetailDTO.setYfMonetaryType("人民币");
                    }
                } catch (Exception e) {
                    requesdetailDTO.setYfMonetaryType("人民币");
                }
            } else {
                requesdetailDTO.setYfMonetaryType("人民币");
            }

            List<GoodsDTO> goodsDTOList = new ArrayList<>();
            goodsDTOList.add(goodsDTO);
            requesdetailDTO.setGoods(goodsDTOList);
            details.add(requesdetailDTO);
        }

        if (CollUtil.isEmpty(details)) {
            return new R<>(new Throwable("未获取到箱信息，请检查导入模板"));
        }
        if (CollUtil.isNotEmpty(requesheaders) && CollUtil.isNotEmpty(details)) {
            for (RequesheaderDTO dto : requesheaders) {
                List<RequesdetailDTO> list = new ArrayList<>();
                for (RequesdetailDTO detail : details) {
                    if (dto.getBookingCustcode().equals(detail.getOriCustcode())) {
                        detail.setOriCustname(dto.getBookingCustname());
                        list.add(detail);
                    }
                }
                dto.setDetails(list);
            }

        }
        if (sb.length() > 0) {
            throw new RuntimeException(sb.toString());
        }

        return saveBookingAndWaybillListForCity(requesheaders);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R changeRate(FdBusCost fdBusCost, String source) {
        if (CollUtil.isNotEmpty(fdBusCost.getDetailList()) && fdBusCost.getExchangeRate() != null) {
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            Map<String, FdBusCostDetail> map = new HashMap<>();
            for (FdBusCostDetail fdBusCostDetail : fdBusCost.getDetailList()) {
                if (!"1".equals(fdBusCostDetail.getStatus())) {
                    String key = fdBusCostDetail.getContainerNumber() + "-" + fdBusCostDetail.getCodeBbCategoriesCode() + "-" + fdBusCostDetail.getCodeSsCategoriesCode();
                    Object o = map.get(key);
                    if (o == null) {
                        map.put(key, fdBusCostDetail);
                    }
                }
            }
            FdBusCost cost = fdBusCostMapper.selectFdBusCostById(fdBusCost.getId());
            FdShippingAccount sel = new FdShippingAccount();
            sel.setShiftNo(cost.getShiftNo());
            sel.setPlatformCode(cost.getPlatformCode());
            sel.setDeleteFlag("N");
            List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountList(sel);
            /*if (CollUtil.isEmpty(fdShippingAccounts)) {
                return new R<>(new Throwable("未查询到该账单对应的台账"));
            }*/
            if ("cityReceive".equals(source)) {
                cost.setExchangeRate(fdBusCost.getExchangeRate());
                R r = insertCityReceive(cost, userInfo, map, fdShippingAccounts);
                if (r != null) {
                    return r;
                }
            }
        }
        return new R<>(0, Boolean.TRUE, null, "操作成功");
    }

    private R insertCityReceive(FdBusCost fdBusCost, SecruityUser userInfo, Map<String, FdBusCostDetail> map, List<FdShippingAccount> fdShippingAccounts) {
//        String ysBillSubCode = null;
        BillPayCustomerSub sel2 = new BillPayCustomerSub();
        sel2.setShiftNo(fdBusCost.getShiftNo());
        sel2.setPlatformCode(fdBusCost.getPlatformCode());
        sel2.setDeleteFlag("N");
        List<BillPayCustomerSub> billPayCustomerSubs = billPayCustomerSubMapper.selectBillSubIncomeCityList(sel2);
        if (CollUtil.isEmpty(billPayCustomerSubs)) {
            return new R<>(new Throwable("未查询到该账单对应子账单"));
        }
        //下级平台业务流程单
        /*FdBusCost sel3 = new FdBusCost();
        sel3.setShiftNo(fdBusCost.getShiftNo());
        sel3.setPlatformCode(fdBusCost.getCustomerCode());
        sel3.setDeleteFlag("N");
        List<FdBusCost> lowList = fdBusCostMapper.selectFdBusCostList(sel3);
        BillPayCustomerSub last = billPayCustomerSubs.get(billPayCustomerSubs.size() - 1);
        ysBillSubCode = CheckUtil.getNumber(last.getBillSubCode(), 3);
        BigDecimal totalAmount = BigDecimal.ZERO;*/
        for (FdBusCostDetail fdBusCostDetail : map.values()) {
            BigDecimal oldExchangeRate = fdBusCostDetail.getExchangeRateNew() != null ? fdBusCostDetail.getExchangeRateNew() : fdBusCostDetail.getExchangeRate();
            //如果调整汇率与最新的汇率值相同，则跳过逻辑
            if (oldExchangeRate.compareTo(fdBusCost.getExchangeRate()) != 0) {
                FdBusCostDetail total = fdBusCostDetailMapper.selectLocalAmount(fdBusCostDetail);
                if (total != null && total.getOriginalAmount() != null) {
                    BigDecimal localAmount = total.getOriginalAmount().multiply(fdBusCost.getExchangeRate());
                    BigDecimal localAmountNew = localAmount.subtract(total.getLocalAmount() != null ? total.getLocalAmount() : BigDecimal.ZERO);
//                    totalAmount = totalAmount.add(localAmountNew);
                    fdBusCostDetail.setId(null);
//                fdBusCostDetail.setBillSubCode(ysBillSubCode);
                    fdBusCostDetail.setOriginalAmount(BigDecimal.ZERO);
                    fdBusCostDetail.setLocalAmount(localAmountNew);
                    fdBusCostDetail.setAddWho(userInfo.getUserName());
                    fdBusCostDetail.setAddWhoName(userInfo.getRealName());
                    fdBusCostDetail.setAddTime(LocalDateTime.now());
                    fdBusCostDetail.setUpdateWho(null);
                    fdBusCostDetail.setUpdateWhoName(null);
                    fdBusCostDetail.setUpdateTime(null);
                    fdBusCostDetail.setAuditStatus("0");
                    fdBusCostDetail.setExchangeRate(fdBusCost.getExchangeRate());
                    fdBusCostDetail.setExchangeRateNew(fdBusCost.getExchangeRate());
                    fdBusCostDetail.setRemark("原币金额:" + total.getOriginalAmount() + ",原汇率:" + total.getExchangeRateNew());
                    fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);

                    FdBusCostDetail updObj = new FdBusCostDetail();
                    updObj.setCostCode(fdBusCostDetail.getCostCode());
                    updObj.setCostType("0");
                    updObj.setContainerNumber(fdBusCostDetail.getContainerNumber());
                    updObj.setCodeSsCategoriesCode(fdBusCostDetail.getCodeBbCategoriesCode());
                    updObj.setCodeBbCategoriesCode(fdBusCostDetail.getCodeBbCategoriesCode());
                    updObj.setExchangeRateNew(fdBusCost.getExchangeRate());
                    updObj.setUpdateWho(userInfo.getUserName());
                    updObj.setUpdateWhoName(userInfo.getRealName());
                    updObj.setUpdateTime(LocalDateTime.now());
                    fdBusCostDetailMapper.updateFdBusCostDetailByCostCode(updObj);
                    //下级平台明细
                /*if(CollUtil.isNotEmpty(lowList)){
                    fdBusCostDetail.setId(null);
                    fdBusCostDetail.setCostCode(lowList.get(0).getCostCode());
                    fdBusCostDetail.setCostType("1");
                    *//*fdBusCostDetail.setReceiveCode();
                    fdBusCostDetail.setReceiveName();
                    fdBusCostDetail.setPayCode();
                    fdBusCostDetail.setPayName();*//*
                    fdBusCostDetail.setLocalAmount(localAmountNew);
                    fdBusCostDetail.setAddWho(userInfo.getUserName());
                    fdBusCostDetail.setAddWhoName(userInfo.getRealName());
                    fdBusCostDetail.setAddTime(LocalDateTime.now());
                    fdBusCostDetail.setUpdateWho(null);
                    fdBusCostDetail.setUpdateWhoName(null);
                    fdBusCostDetail.setUpdateTime(null);
                    fdBusCostDetail.setAuditStatus("1");
                    fdBusCostDetail.setExchangeRate(fdBusCost.getExchangeRate());
                    fdBusCostDetail.setExchangeRateNew(fdBusCost.getExchangeRate());
                    fdBusCostDetail.setRemark("原币金额:" + total.getOriginalAmount() + ",原汇率:" + total.getExchangeRateNew());
                    fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);

                    FdBusCostDetail updObj2 = new FdBusCostDetail();
                    updObj2.setCostCode(lowList.get(0).getCostCode());
                    updObj2.setCostType("1");
                    updObj2.setContainerNumber(fdBusCostDetail.getContainerNumber());
                    updObj2.setCodeSsCategoriesCode(fdBusCostDetail.getCodeBbCategoriesCode());
                    updObj2.setCodeBbCategoriesCode(fdBusCostDetail.getCodeBbCategoriesCode());
                    updObj2.setExchangeRateNew(fdBusCost.getExchangeRate());
                    updObj2.setUpdateWho(userInfo.getUserName());
                    updObj2.setUpdateWhoName(userInfo.getRealName());
                    updObj2.setUpdateTime(LocalDateTime.now());
                    fdBusCostDetailMapper.updateFdBusCostDetailByCostCode(updObj2);
                }*/
                }
            }
        }
        //市平台应收账单
        /*List<BillPayCustomer> billPayCustomers = billPayCustomerMapper.selectBillPayCustomerByBillCode(last.getBillCode());
        BillPayCustomerSub addObj = new BillPayCustomerSub();
        addObj.setBillCode(last.getBillCode());
        addObj.setBillSubCode(ysBillSubCode);
        addObj.setOrderNo(last.getOrderNo());
        addObj.setWaybillNo(last.getWaybillNo());
        addObj.setCostCode(fdBusCost.getCostCode());
        addObj.setPlatformCode(fdBusCost.getPlatformCode());
        addObj.setPlatformName(fdBusCost.getPlatformName());
        addObj.setPlatformLevel("0");
        addObj.setShiftNo(fdBusCost.getShiftNo());
        addObj.setShiftName(last.getShiftName());
        addObj.setBillAmount(totalAmount.negate());
        addObj.setBillingState("0");
        addObj.setCustomerCode(last.getCustomerCode());
        addObj.setCustomerName(last.getCustomerName());
        addObj.setPortStation(last.getPortStation());
        addObj.setStatus("1");
        addObj.setShipmentTime(last.getShipmentTime());
        if(CollUtil.isNotEmpty(billPayCustomers)){
            addObj.setBillType(billPayCustomers.get(0).getStage());
        }
        addObj.setAddWho(userInfo.getUserName());
        addObj.setAddWhoName(userInfo.getRealName());
        addObj.setAddTime(new Date());
        billPayCustomerSubMapper.insertBillSubIncomeCity(addObj);
        billPayCustomerMapper.updateBillAmountByBillCode(last.getBillCode());
        */
        return null;
    }

    private R insertReceive(FdBusCost fdBusCost, SecruityUser userInfo, Map<String, FdBusCostDetail> map, List<FdShippingAccount> fdShippingAccounts) {
        String yfBillSubCode = null;
        BillSubPayCity sel2 = new BillSubPayCity();
        sel2.setBillCode(fdBusCost.getBillCode());
        sel2.setDeleteFlag("N");
        List<BillSubPayCity> billSubPayCities = billSubPayCityMapper.selectBillSubPayCityList(sel2);
        if (CollUtil.isEmpty(billSubPayCities)) {
            return new R<>(new Throwable("未查询到该账单对应子账单"));
        }
        BillSubPayCity last = billSubPayCities.get(billSubPayCities.size() - 1);
        yfBillSubCode = CheckUtil.getNumber(last.getBillSubCode(), 3);
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (FdBusCostDetail fdBusCostDetail : map.values()) {
            FdBusCostDetail total = fdBusCostDetailMapper.selectLocalAmount(fdBusCostDetail);
            if (total.getOriginalAmount() != null) {
                BigDecimal localAmount = total.getOriginalAmount().multiply(fdBusCost.getExchangeRate());
                BigDecimal localAmountNew = localAmount.subtract(total.getLocalAmount() != null ? total.getLocalAmount() : BigDecimal.ZERO);
                totalAmount = totalAmount.add(localAmountNew);
                fdBusCostDetail.setId(null);
                fdBusCostDetail.setCostCode(null);
                fdBusCostDetail.setBillSubCode(yfBillSubCode);
                fdBusCostDetail.setLocalAmount(localAmountNew);
                fdBusCostDetail.setAddWho(userInfo.getUserName());
                fdBusCostDetail.setAddWhoName(userInfo.getRealName());
                fdBusCostDetail.setAddTime(LocalDateTime.now());
                fdBusCostDetail.setAuditTime(LocalDateTime.now());
                fdBusCostDetail.setUpdateWho(null);
                fdBusCostDetail.setUpdateWhoName(null);
                fdBusCostDetail.setUpdateTime(null);
                fdBusCostDetail.setAuditStatus("1");
                fdBusCostDetail.setExchangeRate(fdBusCost.getExchangeRate());
                fdBusCostDetail.setExchangeRateNew(fdBusCost.getExchangeRate());
                fdBusCostDetail.setRemark("原币金额:" + total.getOriginalAmount() + ",原汇率:" + total.getExchangeRateNew());
                fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);

                FdBusCostDetail updObj = new FdBusCostDetail();
                updObj.setCostCode(fdBusCostDetail.getCostCode());
                updObj.setCostType(fdBusCostDetail.getCostType());
                updObj.setContainerNumber(fdBusCostDetail.getContainerNumber());
                updObj.setCodeSsCategoriesCode(fdBusCostDetail.getCodeBbCategoriesCode());
                updObj.setCodeBbCategoriesCode(fdBusCostDetail.getCodeBbCategoriesCode());
                updObj.setExchangeRateNew(fdBusCost.getExchangeRate());
                updObj.setUpdateWho(userInfo.getUserName());
                updObj.setUpdateWhoName(userInfo.getRealName());
                updObj.setUpdateTime(LocalDateTime.now());
                fdBusCostDetailMapper.updateFdBusCostDetailByCostCode(updObj);
                //存在台账，当前为省平台
                if (CollUtil.isNotEmpty(fdShippingAccounts)) {
                    FdShippingAccoundetail updObj2 = new FdShippingAccoundetail();
                    updObj2.setAccountCode(fdShippingAccounts.get(0).getAccountCode());
                    updObj2.setContainerNumber(fdBusCostDetail.getContainerNumber());
                    updObj2.setUpdateWho(userInfo.getUserName());
                    updObj2.setUpdateWhoName(userInfo.getRealName());
                    updObj2.setUpdateTime(LocalDateTime.now());
                    if ("jndtlyf".equals(fdBusCostDetail.getCodeSsCategoriesCode())) {
                        updObj2.setDomesticFreight(localAmount);
                        updObj2.setExchangeRate(fdBusCost.getExchangeRate());
                        fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode(updObj2);
                    } else if ("jwdtlyf".equals(fdBusCostDetail.getCodeSsCategoriesCode())) {
                        updObj2.setOverseasFreightCny(localAmount);
                        updObj2.setExchangeRate(fdBusCost.getExchangeRate());
                        fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode(updObj2);
                    }
                }
            }
        }
        //插入省平台应付账单 业务余额相关
//        basChangeboxRetreatService.insertBillProvinceSub(userInfo, yfBillSubCode, fdShippingAccounts, billPayProvinceSubs);
        return null;
    }

    private R insertProvincePay(FdBusCost fdBusCost, SecruityUser userInfo, Map<String, FdBusCostDetail> map, List<FdShippingAccount> fdShippingAccounts) {
        String yfBillSubCode = null;
        BillPayProvinceSub sel2 = new BillPayProvinceSub();
        sel2.setBillCode(fdBusCost.getBillCode());
        sel2.setDeleteFlag("N");
        List<BillPayProvinceSub> billPayProvinceSubs = billPayProvinceSubMapper.selectBillPayProvinceSubList(sel2);
        if (CollUtil.isEmpty(billPayProvinceSubs)) {
            return new R<>(new Throwable("未查询到该账单对应子账单"));
        }
        BillPayProvinceSub last = billPayProvinceSubs.get(billPayProvinceSubs.size() - 1);
        yfBillSubCode = CheckUtil.getNumber(last.getBillSubCode(), 3);
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (FdBusCostDetail fdBusCostDetail : map.values()) {
            FdBusCostDetail total = fdBusCostDetailMapper.selectLocalAmount(fdBusCostDetail);
            if (total.getOriginalAmount() != null) {
                BigDecimal localAmount = total.getOriginalAmount().multiply(fdBusCost.getExchangeRate());
                BigDecimal localAmountNew = localAmount.subtract(total.getLocalAmount() != null ? total.getLocalAmount() : BigDecimal.ZERO);
                totalAmount = totalAmount.add(localAmountNew);
                fdBusCostDetail.setId(null);
                fdBusCostDetail.setCostCode(null);
                fdBusCostDetail.setBillSubCode(yfBillSubCode);
                fdBusCostDetail.setLocalAmount(localAmountNew);
                fdBusCostDetail.setAddWho(userInfo.getUserName());
                fdBusCostDetail.setAddWhoName(userInfo.getRealName());
                fdBusCostDetail.setAddTime(LocalDateTime.now());
                fdBusCostDetail.setAuditTime(LocalDateTime.now());
                fdBusCostDetail.setUpdateWho(null);
                fdBusCostDetail.setUpdateWhoName(null);
                fdBusCostDetail.setUpdateTime(null);
                fdBusCostDetail.setAuditStatus("1");
                fdBusCostDetail.setExchangeRate(fdBusCost.getExchangeRate());
                fdBusCostDetail.setExchangeRateNew(fdBusCost.getExchangeRate());
                fdBusCostDetail.setRemark("原币金额:" + total.getOriginalAmount() + ",原汇率:" + total.getExchangeRateNew());
                fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);

                FdBusCostDetail updObj = new FdBusCostDetail();
                updObj.setCostType(fdBusCostDetail.getCostType());
                updObj.setContainerNumber(fdBusCostDetail.getContainerNumber());
                updObj.setCodeSsCategoriesCode(fdBusCostDetail.getCodeBbCategoriesCode());
                updObj.setCodeBbCategoriesCode(fdBusCostDetail.getCodeBbCategoriesCode());
                updObj.setExchangeRateNew(fdBusCost.getExchangeRate());
                updObj.setUpdateWho(userInfo.getUserName());
                updObj.setUpdateWhoName(userInfo.getRealName());
                updObj.setUpdateTime(LocalDateTime.now());
                fdBusCostDetailMapper.updateFdBusCostDetailByCostCode(updObj);
                //存在台账，当前为省平台
                if (CollUtil.isNotEmpty(fdShippingAccounts)) {
                    FdShippingAccoundetail updObj2 = new FdShippingAccoundetail();
                    updObj2.setAccountCode(fdShippingAccounts.get(0).getAccountCode());
                    updObj2.setContainerNumber(fdBusCostDetail.getContainerNumber());
                    updObj2.setUpdateWho(userInfo.getUserName());
                    updObj2.setUpdateWhoName(userInfo.getRealName());
                    updObj2.setUpdateTime(LocalDateTime.now());
                    if ("jndtlyf".equals(fdBusCostDetail.getCodeSsCategoriesCode())) {
                        updObj2.setRrDomesticFreight(localAmount);
                        fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode(updObj2);
                    } else if ("jwdtlyf".equals(fdBusCostDetail.getCodeSsCategoriesCode())) {
                        updObj2.setRrOverseasFreightCny(localAmount);
                        fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode(updObj2);
                    }
                }
            }
        }
        //插入省平台应付账单 业务余额相关
        basChangeboxRetreatService.insertBillProvinceSub(userInfo, yfBillSubCode, fdShippingAccounts, billPayProvinceSubs);
        return null;
    }

    @Override
    public R tzCommitCheck(FdBusCost fdBusCost) {
        fdBusCost.setDeleteFlag("N");
        List<FdBusCost> list = fdBusCostMapper.selectFdBusCostList(fdBusCost);
        StringBuffer sb = new StringBuffer();
        if (CollUtil.isNotEmpty(list)) {
            for (FdBusCost cost : list) {
                if (!"2".equals(cost.getAuditStatus())) {
                    sb.append(cost.getPlatformName() + ":" + cost.getCostCode() + "业务流程单审批未完成；");
                }
            }
        }
        if (sb.length() > 0) {
            return R.error(sb.toString());
        }
        return R.success();
    }
}
