package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.StationManagement;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 站点管理 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-08 19:21:24
 */
public interface StationManagementService extends IService<StationManagement> {

    /**
     * 查询站点及关联机构信息
     *
     * @param rowId 线路管信息
     */
    public StationManagement selectPlatByStationCode(String rowId);
    /**
     * 查询站点管理信息
     *
     * @param rowId 站点管理ID
     * @return 站点管理信息
     */
    public StationManagement selectStationManagementById(String rowId);

    /**
     * 查询站点管理列表
     *
     * @param stationManagement 站点管理信息
     * @return 站点管理集合
     */
    public List<StationManagement> selectStationManagementList(StationManagement stationManagement);

    public List<StationManagement> selectStationManagementListByStationCode(StationManagement stationManagement);


    /**
     * 分页模糊查询站点管理列表
     * @return 站点管理集合
     */
    public Page selectStationManagementListByLike(Query query);



    /**
     * 新增站点管理
     *
     * @param stationManagement 站点管理信息
     * @return 结果
     */
    public R insertStationManagement(StationManagement stationManagement);

    /**
     * 新增站点管理
     *
     * @param stationManagement 站点管理信息
     * @return 结果
     */
    public int insertInfoBatch(List<StationManagement> stationManagement);

    /**
     * 修改站点管理
     *
     * @param stationCode 站点管理信息
     * @return 结果
     */
    public R updateStationManagement(String[] stationCode);

    /**
     * 删除站点管理
     *
     * @param rowId 站点管理ID
     * @return 结果
     */
    public int deleteStationManagementById(String rowId);

    /**
     * 批量删除站点管理
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteStationManagementByIds(Integer[] rowIds);

}

