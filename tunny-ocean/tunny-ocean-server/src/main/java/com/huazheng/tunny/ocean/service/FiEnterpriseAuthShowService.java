package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FiEnterpriseAuthShow;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 经营信息授权-企业授权展示表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-03-02 15:10:49
 */
public interface FiEnterpriseAuthShowService extends IService<FiEnterpriseAuthShow> {
    /**
     * 查询经营信息授权-企业授权展示表信息
     *
     * @param rowId 经营信息授权-企业授权展示表ID
     * @return 经营信息授权-企业授权展示表信息
     */
    public FiEnterpriseAuthShow selectFiEnterpriseAuthShowById(String rowId);

    /**
     * 查询经营信息授权-企业授权展示表列表
     *
     * @param fiEnterpriseAuthShow 经营信息授权-企业授权展示表信息
     * @return 经营信息授权-企业授权展示表集合
     */
    public List<FiEnterpriseAuthShow> selectFiEnterpriseAuthShowList(FiEnterpriseAuthShow fiEnterpriseAuthShow);


    public List<FiEnterpriseAuthShow> selectFiEnterpriseAuthShowByEntSocialCode(FiEnterpriseAuthShow fiEnterpriseAuthShow);

    /**
     * 分页模糊查询经营信息授权-企业授权展示表列表
     * @return 经营信息授权-企业授权展示表集合
     */
    public Page selectFiEnterpriseAuthShowListByLike(Query query);

    /**
     * 分页模糊查询经营信息授权-企业授权展示表列表-市平台
     * @return 经营信息授权-企业授权展示表集合-市平台
     */
    public Page selectFiEnterpriseAuthShowListForCity(Query query);

    /**
     * 分页模糊查询经营信息授权-企业授权展示表列表-省平台
     * @return 经营信息授权-企业授权展示表集合-省平台
     */
    public Page selectFiEnterpriseAuthShowListForProvince(Query query);



    /**
     * 新增经营信息授权-企业授权展示表
     *
     * @param fiEnterpriseAuthShow 经营信息授权-企业授权展示表信息
     * @return 结果
     */
    public int insertFiEnterpriseAuthShow(FiEnterpriseAuthShow fiEnterpriseAuthShow);

    /**
     * 修改经营信息授权-企业授权展示表
     *
     * @param fiEnterpriseAuthShow 经营信息授权-企业授权展示表信息
     * @return 结果
     */
    public int updateFiEnterpriseAuthShow(FiEnterpriseAuthShow fiEnterpriseAuthShow);

    /**
     * 删除经营信息授权-企业授权展示表
     *
     * @param rowId 经营信息授权-企业授权展示表ID
     * @return 结果
     */
    public int deleteFiEnterpriseAuthShowById(String rowId);

    /**
     * 批量删除经营信息授权-企业授权展示表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiEnterpriseAuthShowByIds(Integer[] rowIds);

    public void cancelAuth(FiEnterpriseAuthShow fiEnterpriseAuthShow);
}

