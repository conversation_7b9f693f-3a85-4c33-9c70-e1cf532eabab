package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfReleaseInfo;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 解质押同步数据 服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-10 11:11:43
 */
public interface EfReleaseInfoService extends IService<EfReleaseInfo> {
    /**
     * 查询解质押同步数据信息
     *
     * @param rowId 解质押同步数据ID
     * @return 解质押同步数据信息
     */
    public EfReleaseInfo selectEfReleaseInfoById(String rowId);

    /**
     * 查询解质押同步数据列表
     *
     * @param efReleaseInfo 解质押同步数据信息
     * @return 解质押同步数据集合
     */
    public List<EfReleaseInfo> selectEfReleaseInfoList(EfReleaseInfo efReleaseInfo);


    /**
     * 分页模糊查询解质押同步数据列表
     * @return 解质押同步数据集合
     */
    public Page selectEfReleaseInfoListByLike(Query query);



    /**
     * 新增解质押同步数据
     *
     * @param efReleaseInfo 解质押同步数据信息
     * @return 结果
     */
    public int insertEfReleaseInfo(EfReleaseInfo efReleaseInfo);

    /**
     * 修改解质押同步数据
     *
     * @param efReleaseInfo 解质押同步数据信息
     * @return 结果
     */
    public int updateEfReleaseInfo(EfReleaseInfo efReleaseInfo);

    /**
     * 删除解质押同步数据
     *
     * @param rowId 解质押同步数据ID
     * @return 结果
     */
    public int deleteEfReleaseInfoById(String rowId);

    /**
     * 批量删除解质押同步数据
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfReleaseInfoByIds(Integer[] rowIds);

    String syncSupplyConfirmInfo(EfReleaseInfo efReleaseInfo);
}

