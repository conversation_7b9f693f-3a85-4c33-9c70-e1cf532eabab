package com.huazheng.tunny.ocean.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.ocean.api.dto.EfWarehouseDTO;
import com.huazheng.tunny.ocean.api.entity.EfFinancingApply;
import com.huazheng.tunny.ocean.api.entity.EfWarehouse;
import com.huazheng.tunny.ocean.service.EfWarehouseService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * e融同步仓单表
 *
 * <AUTHOR>
 * @date 2023-05-04 10:08:04
 */
@Slf4j
@RestController
@RequestMapping("/efwarehouse")
public class EfWarehouseController {

    @Autowired
    private EfWarehouseService efWarehouseService;
    @Autowired
    private SignatureController signatureController;
    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  efWarehouseService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return efWarehouseService.selectEfWarehouseListByLike(new Query<>(params));
    }

    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        EfWarehouse efWarehouse =efWarehouseService.selectById(rowId);
        return new R<>(efWarehouse);
    }

    /**
     * 保存
     * @param jsonObject
     * @return R
     */
    @PostMapping("/insertEfWarehouse")
    public String save(@RequestBody JSONObject jsonObject) {
        String content = null;
        try {
            //调用接口
            String data = signatureController.getPost(jsonObject);
            List<EfWarehouse> list = JSONUtil.toList(JSONUtil.parseArray(data), EfWarehouse.class);

            List<EfWarehouseDTO> datelist = JSONUtil.toList(JSONUtil.parseArray(data), EfWarehouseDTO.class);

            if(CollUtil.isNotEmpty(list)&&CollUtil.isNotEmpty(datelist)){
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                for (EfWarehouse l1:list
                     ) {
                    for (EfWarehouseDTO l2:datelist
                    ) {
                        if(l1.getWarehouseReceiptNo().equals(l2.getWarehouseReceiptNo())){
                            if(StrUtil.isNotEmpty(l2.getApplyDate())){
                                final LocalDateTime parse = LocalDateTime.parse(l2.getApplyDate(), formatter);
                                l1.setApplyDate(parse);
                            }
                        }

                        if(l1.getWarehouseReceiptNo().equals(l2.getWarehouseReceiptNo())){
                            if(StrUtil.isNotEmpty(l2.getFinishWhTime())){
                                final LocalDateTime parse = LocalDateTime.parse(l2.getFinishWhTime(), formatter);
                                l1.setFinishWhTime(parse);
                            }
                        }
                    }
                }
            }

            content = efWarehouseService.insertEfWarehouse(list);
        }catch (Exception e){
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE,e.getMessage()), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPostEf("/efwarehouse/insertEfWarehouse", content);
        return result;
    }

    /**
     * 修改
     * @param efWarehouse
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody EfWarehouse efWarehouse) {
        efWarehouseService.updateById(efWarehouse);
        return new R<>(Boolean.TRUE);
    }

    

    /**
     * 删除
     * @param rowId
     * @return R
     */
    @GetMapping("/del/{rowId}")
    public R delete(@PathVariable  String rowId) {
        efWarehouseService.deleteById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        efWarehouseService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<EfWarehouse> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<EfWarehouse> list = efWarehouseService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), EfWarehouse.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }

}
