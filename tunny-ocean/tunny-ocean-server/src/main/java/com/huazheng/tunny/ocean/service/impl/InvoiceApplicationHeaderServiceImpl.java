package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.InvoiceApplicationHeaderMapper;
import com.huazheng.tunny.ocean.api.entity.InvoiceApplicationHeader;
import com.huazheng.tunny.ocean.service.InvoiceApplicationHeaderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("invoiceApplicationHeaderService")
public class InvoiceApplicationHeaderServiceImpl extends ServiceImpl<InvoiceApplicationHeaderMapper, InvoiceApplicationHeader> implements InvoiceApplicationHeaderService {

    @Autowired
    private InvoiceApplicationHeaderMapper invoiceApplicationHeaderMapper;

    public InvoiceApplicationHeaderMapper getInvoiceApplicationHeaderMapper() {
        return invoiceApplicationHeaderMapper;
    }

    public void setInvoiceApplicationHeaderMapper(InvoiceApplicationHeaderMapper invoiceApplicationHeaderMapper) {
        this.invoiceApplicationHeaderMapper = invoiceApplicationHeaderMapper;
    }

    /**
     * 查询开票申请主表信息
     *
     * @param rowId 开票申请主表ID
     * @return 开票申请主表信息
     */
    @Override
    public InvoiceApplicationHeader selectInvoiceApplicationHeaderById(String rowId)
    {
        return invoiceApplicationHeaderMapper.selectInvoiceApplicationHeaderById(rowId);
    }

    /**
     * 查询开票申请主表列表
     *
     * @param invoiceApplicationHeader 开票申请主表信息
     * @return 开票申请主表集合
     */
    @Override
    public List<InvoiceApplicationHeader> selectInvoiceApplicationHeaderList(InvoiceApplicationHeader invoiceApplicationHeader)
    {
        return invoiceApplicationHeaderMapper.selectInvoiceApplicationHeaderList(invoiceApplicationHeader);
    }


    /**
     * 分页模糊查询开票申请主表列表
     * @return 开票申请主表集合
     */
    @Override
    public Page selectInvoiceApplicationHeaderListByLike(Query query)
    {
        InvoiceApplicationHeader invoiceApplicationHeader =  BeanUtil.mapToBean(query.getCondition(), InvoiceApplicationHeader.class,false);
        query.setRecords(invoiceApplicationHeaderMapper.selectInvoiceApplicationHeaderListByLike(query,invoiceApplicationHeader));
        return query;
    }

    /**
     * 新增开票申请主表
     *
     * @param invoiceApplicationHeader 开票申请主表信息
     * @return 结果
     */
    @Override
    public int insertInvoiceApplicationHeader(InvoiceApplicationHeader invoiceApplicationHeader)
    {
        return invoiceApplicationHeaderMapper.insertInvoiceApplicationHeader(invoiceApplicationHeader);
    }

    /**
     * 修改开票申请主表
     *
     * @param invoiceApplicationHeader 开票申请主表信息
     * @return 结果
     */
    @Override
    public int updateInvoiceApplicationHeader(InvoiceApplicationHeader invoiceApplicationHeader)
    {
        return invoiceApplicationHeaderMapper.updateInvoiceApplicationHeader(invoiceApplicationHeader);
    }


    /**
     * 删除开票申请主表
     *
     * @param rowId 开票申请主表ID
     * @return 结果
     */
    public int deleteInvoiceApplicationHeaderById(String rowId)
    {
        return invoiceApplicationHeaderMapper.deleteInvoiceApplicationHeaderById( rowId);
    };


    /**
     * 批量删除开票申请主表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteInvoiceApplicationHeaderByIds(Integer[] rowIds)
    {
        return invoiceApplicationHeaderMapper.deleteInvoiceApplicationHeaderByIds( rowIds);
    }

}
