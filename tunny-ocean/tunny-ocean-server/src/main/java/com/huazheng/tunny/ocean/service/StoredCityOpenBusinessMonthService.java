package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.StoredCityOpenBusinessMonth;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 *  服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-09-29 10:19:27
 */
public interface StoredCityOpenBusinessMonthService extends IService<StoredCityOpenBusinessMonth> {
    /**
     * 查询信息
     *
     * @param rowId ID
     * @return 信息
     */
    public StoredCityOpenBusinessMonth selectStoredCityOpenBusinessMonthById(Integer rowId);

    /**
     * 查询列表
     *
     * @param storedCityOpenBusinessMonth 信息
     * @return 集合
     */
    public List<StoredCityOpenBusinessMonth> selectStoredCityOpenBusinessMonthList(StoredCityOpenBusinessMonth storedCityOpenBusinessMonth);


    /**
     * 分页模糊查询列表
     * @return 集合
     */
    public Page selectStoredCityOpenBusinessMonthListByLike(Query query);



    /**
     * 新增
     *
     * @param storedCityOpenBusinessMonth 信息
     * @return 结果
     */
    public int insertStoredCityOpenBusinessMonth(StoredCityOpenBusinessMonth storedCityOpenBusinessMonth);

    /**
     * 修改
     *
     * @param storedCityOpenBusinessMonth 信息
     * @return 结果
     */
    public int updateStoredCityOpenBusinessMonth(StoredCityOpenBusinessMonth storedCityOpenBusinessMonth);

    /**
     * 删除
     *
     * @param rowId ID
     * @return 结果
     */
    public int deleteStoredCityOpenBusinessMonthById(Integer rowId);

    /**
     * 批量删除
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteStoredCityOpenBusinessMonthByIds(Integer[] rowIds);

}

