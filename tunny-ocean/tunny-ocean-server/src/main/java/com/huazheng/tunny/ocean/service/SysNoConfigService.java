package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.SysNoConfig;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.vo.FdShippingAccountVO;

import java.util.List;

/**
 * 系统单号配置表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-01 13:02:05
 */
public interface SysNoConfigService extends IService<SysNoConfig> {
    /**
     * 查询系统单号配置表信息
     *
     * @param rowId 系统单号配置表ID
     * @return 系统单号配置表信息
     */
    public SysNoConfig selectSysNoConfigById(String rowId);

    /**
     * 查询系统单号配置表列表
     *
     * @param sysNoConfig 系统单号配置表信息
     * @return 系统单号配置表集合
     */
    public List<SysNoConfig> selectSysNoConfigList(SysNoConfig sysNoConfig);


    /**
     * 分页模糊查询系统单号配置表列表
     * @return 系统单号配置表集合
     */
    public Page selectSysNoConfigListByLike(Query query);

    /**
     * 系统单号生成
     * @return 单号
     */
    public String genNo(String flag);

    /**
     * 生成省级班列号
     * @return 班列号
     */
    public String createSjblh(String flag, FdShippingAccountVO vo);

    /**
     * 新增系统单号配置表
     *
     * @param sysNoConfig 系统单号配置表信息
     * @return 结果
     */
    public int insertSysNoConfig(SysNoConfig sysNoConfig);

    /**
     * 修改系统单号配置表
     *
     * @param sysNoConfig 系统单号配置表信息
     * @return 结果
     */
    public int updateSysNoConfig(SysNoConfig sysNoConfig);

    /**
     * 删除系统单号配置表
     *
     * @param rowId 系统单号配置表ID
     * @return 结果
     */
    public int deleteSysNoConfigById(String rowId);

    /**
     * 批量删除系统单号配置表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysNoConfigByIds(Integer[] rowIds);

}

