package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.google.gson.Gson;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.WaybillHeader;
import com.huazheng.tunny.ocean.api.entity.eabookingorder.EaBookingOrder;
import com.huazheng.tunny.ocean.api.enums.SysEnum;
import com.huazheng.tunny.ocean.mapper.UploadRecordMapper;
import com.huazheng.tunny.ocean.api.entity.UploadRecord;
import com.huazheng.tunny.ocean.mapper.WaybillHeaderMapper;
import com.huazheng.tunny.ocean.service.UploadRecordService;
import com.huazheng.tunny.ocean.service.eabookingorder.EaBookingOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("uploadRecordService")
public class UploadRecordServiceImpl extends ServiceImpl<UploadRecordMapper, UploadRecord> implements UploadRecordService {

    @Autowired
    private UploadRecordMapper uploadRecordMapper;

    @Autowired
    private WaybillHeaderMapper waybillHeaderMapper;
    @Autowired
    private EaBookingOrderService eaBookingOrderService;
    /**
     * 查询信息
     *
     * @param rowId ID
     * @return 信息
     */
    @Override
    public UploadRecord selectUploadRecordById(String rowId)
    {
        return uploadRecordMapper.selectUploadRecordById(rowId);
    }

    /**
     * 查询列表
     *
     * @param uploadRecord 信息
     * @return 集合
     */
    @Override
    public List<UploadRecord> selectUploadRecordList(UploadRecord uploadRecord)
    {
        return uploadRecordMapper.selectUploadRecordList(uploadRecord);
    }


    /**
     * 分页模糊查询列表
     * @return 集合
     */
    @Override
    public Page selectUploadRecordListByLike(Query query)
    {
        UploadRecord uploadRecord =  BeanUtil.mapToBean(query.getCondition(), UploadRecord.class,false);
        query.setRecords(uploadRecordMapper.selectUploadRecordListByLike(query,uploadRecord));
        return query;
    }

    /**
     * 新增
     *
     * @param uploadRecord 信息
     * @return 结果
     */
    @Override
    public int insertUploadRecord(UploadRecord uploadRecord)
    {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        uploadRecord.setAddTime(LocalDateTime.now());
        uploadRecord.setAddWho(usercode);
        uploadRecord.setAddWhoName(username);
        return uploadRecordMapper.insertUploadRecord(uploadRecord);
    }

    @Override
    public int saveWaybillFile(UploadRecord uploadRecord)
    {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        uploadRecord.setAddTime(LocalDateTime.now());
        uploadRecord.setAddWho(usercode);
        uploadRecord.setAddWhoName(username);
        int i = uploadRecordMapper.insertUploadRecord(uploadRecord);

        WaybillHeader sel = new WaybillHeader();
        sel.setWaybillNo(uploadRecord.getBillNo());
        sel.setDeleteFlag("N");
        List<WaybillHeader> waybillHeaders = waybillHeaderMapper.selectWaybillHeaderList(sel);
        if(CollUtil.isNotEmpty(waybillHeaders)){
            WaybillHeader waybillHeader = waybillHeaders.get(0);
            if(StrUtil.isNotBlank(waybillHeader.getPlatformCode()) && !waybillHeader.getPlatformCode().equals(waybillHeader.getCustomerNo())){
                saveShareWaybillFile(waybillHeader,uploadRecord);
            }
        }
        return i;
    }

    public void saveShareWaybillFile(WaybillHeader waybillHeader,UploadRecord uploadRecord){
        WaybillHeader sel2 = new WaybillHeader();
        sel2.setShiftNo(waybillHeader.getShiftNo());
        sel2.setCustomerNo(waybillHeader.getPlatformCode());
        sel2.setDeleteFlag("N");
        List<WaybillHeader> waybillHeaders2 = waybillHeaderMapper.selectWaybillHeaderList(sel2);
        if(CollUtil.isNotEmpty(waybillHeaders2)){
            WaybillHeader waybillHeader2 = waybillHeaders2.get(0);
            uploadRecord.setBillNo(waybillHeader2.getWaybillNo());
            uploadRecordMapper.insertUploadRecord(uploadRecord);
            if(StrUtil.isNotBlank(waybillHeader2.getPlatformCode()) && !waybillHeader2.getPlatformCode().equals(waybillHeader2.getCustomerNo())){
                saveShareWaybillFile(waybillHeader2,uploadRecord);
            }
        }
    }
    public void saveEaBookinFile(EaBookingOrder eaBookingOrder,UploadRecord uploadRecord){
        EntityWrapper<EaBookingOrder> wrapper = new EntityWrapper<>();
        wrapper.eq("shift_no",eaBookingOrder.getShiftNo());
        wrapper.eq("customer_code",eaBookingOrder.getPlatformCode());
        wrapper.eq("del_flag",SysEnum.N.getKey());
        List<EaBookingOrder> eaBookingOrders = eaBookingOrderService.selectList(wrapper);
        if(CollUtil.isNotEmpty(eaBookingOrders)){
            EaBookingOrder bookingOrder = eaBookingOrders.get(0);
            uploadRecord.setBillNo(bookingOrder.getOrderCode());
            uploadRecordMapper.insertUploadRecord(uploadRecord);
            if(StrUtil.isNotBlank(bookingOrder.getPlatformCode()) && !bookingOrder.getPlatformCode().equals(bookingOrder.getCustomerCode())){
                saveEaBookinFile(bookingOrder,uploadRecord);
            }
        }
    }

    /**
     * 修改
     *
     * @param uploadRecord 信息
     * @return 结果
     */
    @Override
    public int updateUploadRecord(UploadRecord uploadRecord)
    {
        return uploadRecordMapper.updateUploadRecord(uploadRecord);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteFilesForSh(UploadRecord uploadRecord)
    {
        Map<String, Object> map = new HashMap<>();
        map.put("delUrl",uploadRecord.getDelUrl());
        map.put("groupName",uploadRecord.getGroupName());
        String resInfo = HttpUtil.post("http://127.0.0.1:8001/upload/deleteFilesForSh", map, 20000);
        return uploadRecordMapper.updateUploadRecord(uploadRecord);
    }


    /**
     * 删除
     *
     * @param rowId ID
     * @return 结果
     */
    @Override
    public int deleteUploadRecordById(String rowId)
    {
        return uploadRecordMapper.deleteUploadRecordById( rowId);
    };


    /**
     * 批量删除对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteUploadRecordByIds(Integer[] rowIds)
    {
        return uploadRecordMapper.deleteUploadRecordByIds( rowIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String uploadPic(MultipartFile file)
    {
        File tempFile = null;
        try {
            tempFile = File.createTempFile("temp", null);
            if (tempFile.exists()) {
                tempFile.delete();
            }
            file.transferTo(tempFile);
            String url = "http://127.0.0.1:8001/upload/uploadPicAndNameForSh";
            HttpRequest request = HttpUtil.createPost(url);
            request.form("file", tempFile, file.getOriginalFilename());
            HttpResponse response = request.execute();
            String body = response.body();
            JSONObject jsonObject = JSONUtil.parseObj(body);
            return JSONUtil.parseObj(new R<>(Boolean.TRUE,jsonObject), false).toStringPretty();
        } catch (IOException e) {
            return JSONUtil.parseObj(new R<>(Boolean.FALSE,e.toString()), false).toStringPretty();
        }
    }

    /**
     * 资料补传
     * @param uploadRecord
     * @return
     */
    @Override
    public int saveBookingFile(UploadRecord uploadRecord) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String usercode = userInfo.getUserName();
        String username = userInfo.getRealName();
        uploadRecord.setAddTime(LocalDateTime.now());
        uploadRecord.setAddWho(usercode);
        uploadRecord.setAddWhoName(username);
        int i = uploadRecordMapper.insertUploadRecord(uploadRecord);
        EntityWrapper<EaBookingOrder> wrapper = new EntityWrapper<>();
        wrapper.eq("order_code",uploadRecord.getBillNo());
        wrapper.eq("del_flag", SysEnum.N.getKey());
        List<EaBookingOrder> eaBookingOrders = eaBookingOrderService.selectList(wrapper);
        if(CollUtil.isNotEmpty(eaBookingOrders)){
            EaBookingOrder bookingOrder = eaBookingOrders.get(0);
            if(StrUtil.isNotBlank(bookingOrder.getPlatformCode()) && !bookingOrder.getPlatformCode().equals(bookingOrder.getCustomerCode())){
                saveEaBookinFile(bookingOrder,uploadRecord);
            }
        }
        return i;
    }

}
