package com.huazheng.tunny.ocean.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.AddCostByBillDTO;
import com.huazheng.tunny.ocean.api.dto.ExchangeRateCalculationDTO;
import com.huazheng.tunny.ocean.api.entity.BillDealWithCity;
import com.huazheng.tunny.ocean.api.vo.BalanceSubBillVO;
import com.huazheng.tunny.ocean.service.BillDealWithCityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 应付账单（市）
 *
 * <AUTHOR> code generator
 * @date 2024-06-07 14:04:14
 */
@Slf4j
@RestController
@RequestMapping("/billdealwithcity")
public class BillDealWithCityController {

    @Autowired
    private BillDealWithCityService billDealWithCityService;

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page<BillDealWithCity> page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  billDealWithCityService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return billDealWithCityService.selectBillDealWithCityListByLike(new Query<>(params));
    }

    /**
     * 查询所有子帐单信息
     *
     * @param params
     * @return
     */
    @GetMapping("/pageSubBill")
    public Page pageSubBill(@RequestParam Map<String, Object> params) {
        return billDealWithCityService.pageSubBill(new Query<>(params));
    }

    /**
     * 查询所有子帐单信息
     *
     * @param params
     * @return
     */
    @GetMapping("/selectCustomer")
    public List<BalanceSubBillVO> selectCustomerList(@RequestParam Map<String, Object> params) {
        return billDealWithCityService.selectCustomerList(params);
    }

    /**
     * 信息
     *
     * @param params
     * @return R
     */
    @GetMapping("/info")
    public R info(@RequestParam Map<String, Object> params) {
        return billDealWithCityService.selectBillDealWithCityById(params);
    }

    /**
     * 获取费用明细数据
     *
     * @param
     * @return R
     */
    @GetMapping("/selectCostInfoByBillId")
    public Map<String, Object> selectCostInfoByBillId(@RequestParam Map<String, Object> params) {
        return billDealWithCityService.selectCostInfoByBillId(new Query(params));
    }

    /**
     * 保存
     *
     * @param billDealWithCity
     * @return R
     */
    @PostMapping
    public R save(@RequestBody BillDealWithCity billDealWithCity) {
        billDealWithCityService.insert(billDealWithCity);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     *
     * @param billDealWithCity
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody BillDealWithCity billDealWithCity) {
        billDealWithCityService.updateById(billDealWithCity);
        return new R<>(Boolean.TRUE);
    }


    /**
     * 删除
     *
     * @param id
     * @return R
     */
    @GetMapping("/del/{id}")
    public R delete(@PathVariable Integer id) {
        billDealWithCityService.deleteById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody List<Integer> ids) {
        billDealWithCityService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 转实付接口
     * @param id
     * @param pageType
     * @return
     */
    @GetMapping("/updateYfOfSf")
    public R updateYfOfSf(@RequestParam("id") Integer id, @RequestParam("pageType") Integer pageType){
        return billDealWithCityService.updateYfOfSf(id,pageType);
    }

    /**
     * 追加费用
     * @param addCostByBillDTO
     * @return
     */
    @PostMapping("/addCostByBill")
    public R addCostByBill(@RequestBody AddCostByBillDTO addCostByBillDTO){
        return billDealWithCityService.addCostByBill(addCostByBillDTO);
    }

    /**
     * 获取账单下所以费用
     * @param id
     * @return
     */
    @GetMapping("/getCostByBillId")
    public R getCostByBillId(@RequestParam("id") Integer id,@RequestParam("pageType") Integer pageType){
        return billDealWithCityService.getCostByBillId(id,pageType);
    }

    /**
     * 计算汇率列表
     * @return
     */
    @PostMapping("/addCalculation")
    public R addCalculation(@RequestBody ExchangeRateCalculationDTO exchangeRateCalculationDTO){
        return billDealWithCityService.addCalculation(exchangeRateCalculationDTO);
    }

    /**
     * 导出EXCEL
     *
     * @return
     */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {

        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<BillDealWithCity> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<BillDealWithCity> list = billDealWithCityService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), BillDealWithCity.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }


    /**
     * 导入EXCEL
     * <p>
     *
     * @param titleStr
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/imported")
    @Transactional
    public R imported(String titleStr, MultipartFile file) throws Exception {
        /*
        List list = new ArrayList();
        try {
            EasyExcel.read(file.getInputStream(), BillDealWithCity.class, new EasyExcelListener<BillDealWithCity>() {
                //数据处理逻辑，需要实现数据校验必须重写父级的invoke方法
                @Override
                public void invoke(BillDealWithCity entity, AnalysisContext analysisContext) {
                    //log.info("解析到一条数据:{}", JSON.toJSONString(excelItem));
                    //数据校验逻辑
                    //String name = entity.getId();
                    //if (name.length()>10) {
                    //    throw new RuntimeException(String.format("第%s行错误，名称过长", analysisContext.readRowHolder().getRowIndex() + 1));
                    //}
                    //每读取1000条数据保存一次
                    list.add(entity);
                    if (list.size() >= 1000) {
                        saveData(list);
                        list.clear();
                    }
                }

                *//**
         * 所有数据解析完成了就会来调用，确保最后遗留的数据也存储到数据库
         *//*
                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    saveData(list);
                    list.clear();
                }

                //数据批量保存逻辑
                @Override
                public void saveData(List<BillDealWithCity> infoList) {
                        billDealWithCityService.insertBatch(infoList);
                }
                //headRowNumber()声明标题行占用行数
            }).headRowNumber(1).sheet().doRead();
        }catch (Exception e){
            e.printStackTrace();
            return new R<>(Boolean.FALSE, e.getMessage());
        }*/
        return new R<>(Boolean.TRUE);
    }
}
