package com.huazheng.tunny.ocean.controller;


import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.BasColumnStatus;
import com.huazheng.tunny.ocean.service.BasColumnStatusService;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 用户列展示状态
 *
 * <AUTHOR>
 * @date 2024-04-23 14:48:54
 */
@RestController
@RequestMapping("/bascolumnstatus")
@Slf4j
public class BasColumnStatusController {
    @Autowired
    private BasColumnStatusService basColumnStatusService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  basColumnStatusService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return basColumnStatusService.selectBasColumnStatusListByLike(new Query<>(params));
    }

    /**
     *  查询当前用户所有列状态
     * @param basColumnStatus
     * @return
     */
    @PostMapping("/getListByUser")
    public R<List<BasColumnStatus>> getListByUser(@RequestBody BasColumnStatus basColumnStatus) {
        //数据库字段值完整查询
        // return  basColumnStatusService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        basColumnStatus.setAddWho(userInfo.getUserName());
        basColumnStatus.setDeleteFlag("N");
        List<BasColumnStatus> basColumnStatuses = basColumnStatusService.selectBasColumnStatusList(basColumnStatus);
        return new R<>(basColumnStatuses);
    }


    /**
     * 信息
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Integer id) {
        BasColumnStatus basColumnStatus =basColumnStatusService.selectById(id);
        return new R<>(basColumnStatus);
    }

    /**
     * 保存
     * @param basColumnStatus
     * @return R
     */
    @PostMapping
    public R save(@RequestBody BasColumnStatus basColumnStatus) {
        basColumnStatusService.insertBasColumnStatus(basColumnStatus);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 更新用户列状态
     * @param basColumnStatus
     * @return R
     */
    @PostMapping("/insertOrUpdate")
    public R insertOrUpdate(@RequestBody BasColumnStatus basColumnStatus) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        basColumnStatus.setAddWho(userInfo.getUserName());
        basColumnStatus.setAddWhoName(userInfo.getRealName());
        basColumnStatus.setAddTime(LocalDateTime.now());
        basColumnStatus.setUpdateWho(userInfo.getUserName());
        basColumnStatus.setUpdateWhoName(userInfo.getRealName());
        basColumnStatus.setUpdateTime(LocalDateTime.now());
        basColumnStatusService.insertOrUpdate(basColumnStatus);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param basColumnStatus
     * @return R
     */
    @PutMapping
    public R update(@RequestBody BasColumnStatus basColumnStatus) {
        basColumnStatusService.updateById(basColumnStatus);
        return new R<>(Boolean.TRUE);
    }



    /**
     * 删除
     * @param id
     * @return R
     */
    @DeleteMapping("/{id}")
    public R delete(@PathVariable  Integer id) {
        basColumnStatusService.deleteById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<Integer> ids) {
        basColumnStatusService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<BasColumnStatus> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = basColumnStatusService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<BasColumnStatus> list = reader.readAll(BasColumnStatus.class);
        basColumnStatusService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }
}
