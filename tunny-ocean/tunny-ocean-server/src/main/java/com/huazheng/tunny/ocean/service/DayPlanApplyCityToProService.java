package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.DayPlanApplyCityToPro;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 旬/周计划申请表(市平台提交到省平台) 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-07 11:48:59
 */
public interface DayPlanApplyCityToProService extends IService<DayPlanApplyCityToPro> {
    /**
     * 查询旬/周计划申请表(市平台提交到省平台)信息
     *
     * @param rowId 旬/周计划申请表(市平台提交到省平台)ID
     * @return 旬/周计划申请表(市平台提交到省平台)信息
     */
    public DayPlanApplyCityToPro selectDayPlanApplyCityToProById(String rowId);

    /**
     * 查询旬/周计划申请表(市平台提交到省平台)列表
     *
     * @param dayPlanApplyCityToPro 旬/周计划申请表(市平台提交到省平台)信息
     * @return 旬/周计划申请表(市平台提交到省平台)集合
     */
    public List<DayPlanApplyCityToPro> selectDayPlanApplyCityToProList(DayPlanApplyCityToPro dayPlanApplyCityToPro);


    /**
     * 分页模糊查询旬/周计划申请表(市平台提交到省平台)列表
     * @return 旬/周计划申请表(市平台提交到省平台)集合
     */
    public Page selectDayPlanApplyCityToProListByLike(Query query);

    public Page selectDayPlanApplyCityToProListByLike1(Query query);

    /**
     * 新增旬/周计划申请表(市平台提交到省平台)
     *
     * @param dayPlanApplyCityToPro 旬/周计划申请表(市平台提交到省平台)信息
     * @return 结果
     */
    public int insertDayPlanApplyCityToPro(DayPlanApplyCityToPro dayPlanApplyCityToPro);

    /**
     * 修改旬/周计划申请表(市平台提交到省平台)
     *
     * @param dayPlanApplyCityToPro 旬/周计划申请表(市平台提交到省平台)信息
     * @return 结果
     */
    public int updateDayPlanApplyCityToPro(DayPlanApplyCityToPro dayPlanApplyCityToPro);

    public int updateDayPlanApplyCityToProByNo(DayPlanApplyCityToPro dayPlanApplyCityToPro);

    /**
     * 删除旬/周计划申请表(市平台提交到省平台)
     *
     * @param dayPlanApplyCityToPro 旬/周计划申请表(市平台提交到省平台)ID
     * @return 结果
     */
    public int deleteDayPlanApplyCityToProById(DayPlanApplyCityToPro dayPlanApplyCityToPro);

    /**
     * 批量删除旬/周计划申请表(市平台提交到省平台)
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDayPlanApplyCityToProByIds(Integer[] rowIds);

}

