package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.*;
import com.huazheng.tunny.ocean.api.entity.WaybillHeader;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.vo.IndexVO;
import org.springframework.web.bind.annotation.RequestBody;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * 运单主表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-05 10:57:51
 */
public interface WaybillHeaderService extends IService<WaybillHeader> {
    /**
     * 查询运单主表信息
     *
     * @param rowId 运单主表ID
     * @return 运单主表信息
     */
    public WaybillHeader selectWaybillHeaderById(String rowId);

    /**
     * 查询运单主表列表
     *
     * @param waybillHeader 运单主表信息
     * @return 运单主表集合
     */
    public List<WaybillHeader> selectWaybillHeaderList(WaybillHeader waybillHeader);

    /**
     * 查询客户订舱数量
     * @param indexVO
     * @return
     */
    public String selectBookedNum(IndexVO indexVO);

    public String selectBookedNum2(IndexVO indexVO);

    /**
     * 根据年月查询客户订舱数量
     * @param indexVO
     * @return
     */
    public List<KvDTO> selectBookedNumByMonth(IndexVO indexVO);


    /**
     * 分页模糊查询运单主表列表
     * @return 运单主表集合
     */
    public Page selectWaybillHeaderListByLike(Query query);

    public Integer selectWaybillHeaderListByLikeCount(WaybillHeader waybillHeader);



    /**
     * 新增运单主表
     *
     * @param waybillHeader 运单主表信息
     * @return 结果
     */
    public int insertWaybillHeader(WaybillHeader waybillHeader);

    /**
     * 修改运单主表
     *
     * @param waybillHeader 运单主表信息
     * @return 结果
     */
    public R updateWaybillInfos(WaybillHeader waybillHeader);

    /**
     * 修改运单单据状态
     *
     * @param waybillHeader 运单主表信息
     * @return 结果
     */
    public int commitStatus(WaybillHeader waybillHeader);

    public String commitStatusForSh(String data);

    /**
     * 修改运单主表
     *
     * @param waybillHeader 运单主表信息
     * @return 结果
     */
    public int updateWaybillHeader(WaybillHeader waybillHeader);

    /**
     * 批量修改运单主表
     *
     * @param waybillHeader 运单主表信息
     * @return 结果
     */
    public int updateWaybillHeaderBatch(List<WaybillHeader> waybillHeader);

    /**
     * 删除运单主表
     *
     * @param rowId 运单主表ID
     * @return 结果
     */
    public int deleteWaybillHeaderById(String rowId);

    /**
     * 批量删除运单主表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteWaybillHeaderByIds(Integer[] rowIds);

    public List<PlatFormDetailDTO> platformReportList(WaybillHeader waybillHeader);

    public List<PortDTO> monthReportList(WaybillHeader waybillHeader);

    public Page jiNanList(Query query);

    public List<JiNanDTO> jiNanList2(WaybillHeader waybillHeader);

    public List<JiNanMonthDTO> selectCensusList(WaybillHeader waybillHeader);

//    public List<TrainsMonthlyDTO> trainsMonthlyList(WaybillHeader waybillHeader);

    public Page selectCustomerShippingLineList(Query query);

    public R selectFiBookingFee(Map<String, Object> params) throws ParseException;

    public void updateTotalCases(String waybillNo);

    public int updateWaybillNoByBillStatus(WaybillHeader waybillHeader);

    public int updateWaybillCheXiang(WaybillHeader waybillHeader);

    public Integer selectWaybillCount(WaybillHeader waybillHeader);

    public List<WaybillHeader> selectShippingLine(WaybillHeader waybillHeader);

    public String saveInfoForSh(String data);

    public String queryAuditForSh(String data);

    public R waybillExamine( WaybillHeader waybillHeader);

    public void insertShareWaybill(WaybillHeader waybillHeader);

    public void insertPostTransport(WaybillHeader waybillHeader);
}

