package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.controller.SignatureController;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.EfReleaseInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import java.util.UUID;

@Service("efReleaseInfoService")
public class EfReleaseInfoServiceImpl extends ServiceImpl<EfReleaseInfoMapper, EfReleaseInfo> implements EfReleaseInfoService {

    @Autowired
    private EfReleaseInfoMapper efReleaseInfoMapper;
    @Autowired
    private SignatureController signatureController;
    @Autowired
    private EfWarehouseApplyMapper efWarehouseApplyMapper;
    @Autowired
    private EfWarehouseListMapper efWarehouseListMapper;
    @Autowired
    private EfWarehouseMapper efWarehouseMapper;
    @Autowired
    private EfWarehouseLogMapper efWarehouseLogMapper;
    @Autowired
    private EfDisposeIntentionListMapper efDisposeIntentionListMapper;

    /**
     * 查询解质押同步数据信息
     *
     * @param rowId 解质押同步数据ID
     * @return 解质押同步数据信息
     */
    @Override
    public EfReleaseInfo selectEfReleaseInfoById(String rowId)
    {
        return efReleaseInfoMapper.selectEfReleaseInfoById(rowId);
    }

    /**
     * 查询解质押同步数据列表
     *
     * @param efReleaseInfo 解质押同步数据信息
     * @return 解质押同步数据集合
     */
    @Override
    public List<EfReleaseInfo> selectEfReleaseInfoList(EfReleaseInfo efReleaseInfo)
    {
        return efReleaseInfoMapper.selectEfReleaseInfoList(efReleaseInfo);
    }


    /**
     * 分页模糊查询解质押同步数据列表
     * @return 解质押同步数据集合
     */
    @Override
    public Page selectEfReleaseInfoListByLike(Query query)
    {
        EfReleaseInfo efReleaseInfo =  BeanUtil.mapToBean(query.getCondition(), EfReleaseInfo.class,false);
        query.setRecords(efReleaseInfoMapper.selectEfReleaseInfoListByLike(query,efReleaseInfo));
        return query;
    }

    /**
     * 新增解质押同步数据
     *
     * @param efReleaseInfo 解质押同步数据信息
     * @return 结果
     */
    @Override
    public int insertEfReleaseInfo(EfReleaseInfo efReleaseInfo)
    {
        return efReleaseInfoMapper.insertEfReleaseInfo(efReleaseInfo);
    }

    /**
     * 修改解质押同步数据
     *
     * @param efReleaseInfo 解质押同步数据信息
     * @return 结果
     */
    @Override
    public int updateEfReleaseInfo(EfReleaseInfo efReleaseInfo)
    {
        return efReleaseInfoMapper.updateEfReleaseInfo(efReleaseInfo);
    }


    /**
     * 删除解质押同步数据
     *
     * @param rowId 解质押同步数据ID
     * @return 结果
     */
    public int deleteEfReleaseInfoById(String rowId)
    {
        return efReleaseInfoMapper.deleteEfReleaseInfoById( rowId);
    };


    /**
     * 批量删除解质押同步数据对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfReleaseInfoByIds(Integer[] rowIds)
    {
        return efReleaseInfoMapper.deleteEfReleaseInfoByIds( rowIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String syncSupplyConfirmInfo(EfReleaseInfo efReleaseInfo)
    {
        //插入操作记录
        EfWarehouseLog log = new EfWarehouseLog();
        String remark = "";

        String msg = "success";
        String from = "齐鲁号:";
        Boolean flag = true;
        if(efReleaseInfo!=null) {
            final EfWarehouseApply efWarehouseApply = efWarehouseApplyMapper.selectEfWarehouseApplyByAssetCode(efReleaseInfo.getAssetCode());
            if(efWarehouseApply!=null){
                efReleaseInfo.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
            }
            from = "E融平台:";
            //调用E融接口，插入申请数据
            String json = JSONUtil.parseObj(efReleaseInfo, true).toStringPretty();
            final String result = signatureController.doPostEf("/efreleaseinfo/syncSupplyConfirmInfo", json);
            JSONObject dataObject = JSONUtil.parseObj(result);
            flag = Boolean.valueOf(String.valueOf(dataObject.get("b")));
            msg = String.valueOf(dataObject.get("msg"));

            if(flag){
                remark = "解质押仓单编码：" +efReleaseInfo.getWarehouseCode();

                EfWarehouseList efWarehouseList = new EfWarehouseList();
                efWarehouseList.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                efWarehouseList.setWarehouseSupervisionNo(efReleaseInfo.getWarehouseSupervisionNo());
                efWarehouseList.setWarehouseCode(efReleaseInfo.getWarehouseCode());
                efWarehouseList.setPledgeStatus("2");
                efWarehouseList.setUpdateTime(LocalDateTime.now());
                efWarehouseList.setUpdateWho("ZC");
                efWarehouseList.setUpdateWhoName("中钞");
                efWarehouseListMapper.updateByRelease(efWarehouseList);

                /*EfWarehouse efWarehouse = new EfWarehouse();
                efWarehouse.setWarehouseReceiptNo(efReleaseInfo.getWarehouseCode());
                efWarehouse.setPledgeStatus(0);
                efWarehouse.setUpdateTime(LocalDateTime.now());
                efWarehouse.setUpdateWho("ZC");
                efWarehouse.setUpdateWhoName("中钞");
                efWarehouseMapper.updateEfWarehouse(efWarehouse);*/

                EfDisposeIntentionList efDisposeIntentionList = new EfDisposeIntentionList();
                efDisposeIntentionList.setAssetCode(efWarehouseApply.getAssetCode());
                efDisposeIntentionList.setWarehouseCode(efReleaseInfo.getWarehouseCode());
                efDisposeIntentionList.setStatus("1");
                efDisposeIntentionListMapper.updateEfDisposeIntentionList(efDisposeIntentionList);

                efReleaseInfo.setRowId(UUID.randomUUID().toString());
                efReleaseInfo.setEntSocialCode(efWarehouseApply.getEntSocialCode());
                efReleaseInfo.setPlatformCode(efWarehouseApply.getPlatformCode());
                efReleaseInfo.setAddTime(LocalDateTime.now());
                efReleaseInfo.setAddWho("ZC");
                efReleaseInfo.setAddWhoName("中钞");
                efReleaseInfoMapper.insertEfReleaseInfo(efReleaseInfo);

                //插入操作记录
                log.setRowId(UUID.randomUUID().toString());
                log.setQlFinancingNo(efWarehouseApply.getQlFinancingNo());
                log.setSerialNum(String.valueOf(System.currentTimeMillis()));
                log.setControlType("同步解质押");
                log.setControlTime(LocalDateTime.now());
                log.setRemark(remark);
                log.setAddTime(LocalDateTime.now());
                log.setAddWho("ZC");
                log.setAddWhoName("中钞");
                efWarehouseLogMapper.insertEfWarehouseLog(log);
            }

        }else{
            msg = "没有接收到仓单融资审核数据";
            flag = false;
        }
        int code = 500;
        if(flag){
            code = 0;
        }
        return JSONUtil.parseObj(new R<>(code,flag,null,from+msg), false).toStringPretty();
    }

}
