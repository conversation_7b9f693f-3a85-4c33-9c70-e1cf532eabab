package com.huazheng.tunny.ocean.controller;


import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.ocean.api.entity.BasUserOrg;
import com.huazheng.tunny.ocean.api.entity.BasUserRole;
import com.huazheng.tunny.ocean.api.entity.UserInfo;
import com.huazheng.tunny.ocean.api.util.TokenUtil;
import com.huazheng.tunny.ocean.service.BasUserRoleService;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 用户角色关联表
 *
 * <AUTHOR> code ocean
 * @date 2021-07-01 09:20:08
 */
@RestController
@RequestMapping("/basuserrole")
@Slf4j
public class BasUserRoleController {
    @Autowired
    private BasUserRoleService basUserRoleService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  basUserRoleService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return basUserRoleService.selectBasUserRoleListByLike(new Query<>(params));
    }

    /**
     * 列表
     *
     * @param basUserRole r
     * @return page
     */
    @PostMapping("/getAll")
    public Page<BasUserRole> page(@RequestBody BasUserRole basUserRole) {

        EntityWrapper<BasUserRole> wrapper = new EntityWrapper<>();
        if (StrUtil.isNotBlank(basUserRole.getRoleCode())) {
            wrapper.eq("role_code", basUserRole.getRoleCode());
        }
        if (StrUtil.isNotBlank(basUserRole.getDeleteFlag())) {
            wrapper.eq("delete_flag", basUserRole.getDeleteFlag());
        }
//        if (StrUtil.isNotBlank(basUserRole.getOrgCode())) {
//            wrapper.andNew().like("org_code", basUserRole.getOrgCode())
//                    .or().like("user_name", basUserRole.getUserName());
//        }
        Page<BasUserRole> page = new Page<>();
        if (null != basUserRole.getPage() && null != basUserRole.getLimit()) {
            page.setCurrent(basUserRole.getPage());
            page.setSize(basUserRole.getLimit());
        }
        return basUserRoleService.selectPage(page, wrapper);
    }

    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        BasUserRole basUserRole =basUserRoleService.selectById(rowId);
        return new R<>(basUserRole);
    }

    /**
     * 保存
     * @param basUserRole
     * @return R
     */
    @PostMapping
    public R save(@RequestBody BasUserRole basUserRole) {
        basUserRoleService.insert(basUserRole);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param basUserRole
     * @return R
     */
    @PutMapping
    public R update(@RequestBody BasUserRole basUserRole) {
        basUserRoleService.updateById(basUserRole);
        return new R<>(Boolean.TRUE);
    }



    /**
     * 删除
     * @param rowId
     * @return R
     */
    @DeleteMapping("/{rowId}")
    public R delete(@PathVariable  String rowId) {
        BasUserRole basUserRole = basUserRoleService.selectById(rowId);
        if (null != basUserRole) {
            basUserRole.setDeleteFlag("Y");
            basUserRoleService.updateById(basUserRole);
        }else {
            return new R<>(500,false,"未存在该条数据");
        }
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        basUserRoleService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<BasUserRole> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = basUserRoleService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<BasUserRole> list = reader.readAll(BasUserRole.class);
        basUserRoleService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }
}
