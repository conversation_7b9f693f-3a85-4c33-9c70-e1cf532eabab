package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.FdBill;
import com.huazheng.tunny.ocean.api.entity.FdCosdetail;
import com.huazheng.tunny.ocean.api.entity.FdCost;
import com.huazheng.tunny.ocean.api.vo.FdBillVO;
import com.huazheng.tunny.ocean.api.vo.FdCosdetailvo;
import com.huazheng.tunny.ocean.api.vo.IndexVO;

import java.util.HashMap;
import java.util.List;

/**
 * 运单费用明细表 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:30:20
 */
public interface FdCosdetailService extends IService<FdCosdetail> {
    /**
     * 查询运单费用明细表信息
     *
     * @param fdCosdetails 运单费用明细表ID
     * @return 运单费用明细表信息
     */
    public FdCosdetail selectFdCosdetailByuuId(FdCosdetail fdCosdetails);

    /**
     * 查询运单费用明细表列表
     *
     * @param fdCosdetail 运单费用明细表信息
     * @return 运单费用明细表集合
     */
    public List<FdCosdetail> selectFdCosdetailList(FdCosdetail fdCosdetail);

    public List<FdCosdetail> selectFdCosdetailListByGroup(FdCosdetail fdCosdetail);


    /**
     * 分页模糊查询运单费用明细表列表
     * @return 运单费用明细表集合
     */
    public Page selectFdCosdetailListByLike(Query query);



    /**
     * 新增运单费用明细表
     *
     * @param fdCosdetail 运单费用明细表信息
     * @return 结果
     */
    public int insertFdCosdetail(FdCosdetail fdCosdetail);

    /**
     * 查询登录用户累计支付运费
     * @param indexVO
     * @return
     */
    public String selectPayedAmount(IndexVO indexVO);

    public String selectPayedAmount2(IndexVO indexVO);

    /**
     * 修改运单费用明细表
     *
     * @param fdCosdetail 运单费用明细表信息
     * @return 结果
     */
    public int updateFdCosdetail(FdCosdetail fdCosdetail);

    /**
     * 删除运单费用明细表
     *
     * @param id 运单费用明细表ID
     * @return 结果
     */
    public int deleteFdCosdetailById(Integer id);

    /**
     * 批量删除运单费用明细表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdCosdetailByIds(Integer[] ids);

    Boolean deleteFdCosdetailByuuId(FdCosdetail fdCosdetails);
    Boolean deleteFdCosdetailBybillcode(String billcode);

    Integer updatebycostdetailids(HashMap<String, Object> map);
    Integer updateinvoicebycostdetailids(HashMap<String, Object> map);

    Integer updatebytransportOrderNumber(HashMap<String, Object> map);

    Boolean insertFdTradingDetailsbydeduction(FdBillVO fdBillVO);

    Boolean insertFdTradingDetailsbydeduction2(FdBillVO fdBillVO);

    List selectcontainerNumberbycostcode(String code);

    Page costdetaillist(Query query);

    List costdetaillistnew(FdCosdetail fdCosdetail);

    Page selectShengListpage(Query query);

    List selectShengList(FdCosdetail fdCosdetail);
    List<FdCosdetail> costdetaillist2(FdCosdetail fdCosdetail);

    Boolean insertFdTradingDetailsbycitydeduction(FdBillVO fdBillVO);

    Boolean updateCosdetailbycontainernumber(List<String> list,String provincetrainsnumber,String trainnumber);

    Page amountpage(Query<Object> objectQuery);

    List<FdCosdetail> selectFdCosdetailListByIdList(FdCosdetail fdCosdetail);

    int updateCostDetailByCostCode(FdCosdetail fdCosdetails);

    //批量新增
    int insertFdCosdetailList(List<FdCosdetail> fdCosdetails);

    /**
     * 省平台编辑
     */
    int updateFdCosdetalSheng(List<FdCosdetail> fdCosdetails);

    /**
     * 导入时查询
     */
    public FdCosdetail selectFdCosdetailByCostCodeAndXh(FdCosdetail fdCosdetail);

    public List<FdCosdetail> selectConstDeatilByCostCode(String code);

    public List<FdCosdetailvo> costdetaillistYf(FdCosdetail fdCosdetail);

    public int updateFdCosdetalInvoice(List<FdCosdetail> fdCosdetails);

    public int updateFdCosdetailExchangeRate(List<FdCosdetail> fdCosdetails);

    public String selectProvinceTrainsNumberByUuid(FdCosdetail fdCosdetail);

    public int updateFdCosdetailByCostCode(FdCosdetail fdCosdetail);

    public int deleteFdCosdetailByContainerNo(FdCosdetail fdCosdetail);

    Integer selectNotPayedCount(IndexVO indexVO);
}

