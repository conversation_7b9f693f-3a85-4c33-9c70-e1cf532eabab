package com.huazheng.tunny.ocean.controller;


import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.FdBusCostDetailDTO;
import com.huazheng.tunny.ocean.api.dto.GrossProfitDeatilsDTO;
import com.huazheng.tunny.ocean.api.dto.IncomesContainerDeatilsDTO;
import com.huazheng.tunny.ocean.api.dto.IncomesDeatilsDTO;
import com.huazheng.tunny.ocean.api.entity.RptBusinessAnalysis;
import com.huazheng.tunny.ocean.service.RptBusinessAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 经营数据
 *
 * <AUTHOR> code ocean
 * @date 2021-08-04 11:35:56
 */
@RestController
@RequestMapping("/rptbusinessanalysis")
@Slf4j
public class RptBusinessAnalysisController {

    @Autowired
    private RptBusinessAnalysisService rptBusinessAnalysisService;
    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page<List<RptBusinessAnalysis>> page(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return rptBusinessAnalysisService.selectRptBusinessAnalysisLike(new Query<>(params));
    }

    /**
     * 列表导出
     *
     * @param rptBusinessAnalysis
     * @return
     */
    @PostMapping("/pageExport")
    public void page(@RequestBody RptBusinessAnalysis rptBusinessAnalysis, HttpServletResponse response) {
        //对象模糊查询
        rptBusinessAnalysisService.pageExport(rptBusinessAnalysis,response);
    }

    /**
     * 收入明细
     *
     * @param incomesDeatilsDTO
     * @return
     */
    @PostMapping("/incomesDeatilsList")
    public R incomesDeatilsList(@RequestBody IncomesDeatilsDTO incomesDeatilsDTO) {
        return new R(rptBusinessAnalysisService.incomesDeatilsList(incomesDeatilsDTO));
    }

    /**
     * 收入箱号列表
     *
     * @param incomesDeatilsDTO
     * @return
     */
    @PostMapping("/incomesContainerNumber")
    public List<String> incomesContainerNumber(@RequestBody IncomesDeatilsDTO incomesDeatilsDTO) {
        return rptBusinessAnalysisService.incomesContainerNumber(incomesDeatilsDTO);
    }

    /**
     * 收入明细-箱维度
     *
     * @param incomesContainerDeatilsDTO
     * @return
     */
    @PostMapping("/incomesContainerDeatilsList")
    public R<Map<String,Object>> incomesContainerDeatilsList(@RequestBody IncomesContainerDeatilsDTO incomesContainerDeatilsDTO) {
        return new R(rptBusinessAnalysisService.incomesContainerDeatilsList(incomesContainerDeatilsDTO));
    }

    /**
     * 还原收入明细
     *
     * @param incomesDeatilsDTO
     * @return
     */
    @PostMapping("/restoreIncomesDeatilsList")
    public R<List<IncomesDeatilsDTO>> restoreIncomesDeatilsList(@RequestBody IncomesDeatilsDTO incomesDeatilsDTO) {
        return new R(rptBusinessAnalysisService.restoreIncomesDeatilsList(incomesDeatilsDTO));
    }

    /**
     * 还原收入明细-箱维度
     *
     * @param incomesContainerDeatilsDTO
     * @return
     */
    @PostMapping("/restoreIncomesContainerDeatilsList")
    public R<Map<String,Object>> restoreIncomesContainerDeatilsList(@RequestBody IncomesContainerDeatilsDTO incomesContainerDeatilsDTO) {
        return new R(rptBusinessAnalysisService.restoreIncomesContainerDeatilsList(incomesContainerDeatilsDTO));
    }

    /**
     * 支出明细
     *
     * @param incomesDeatilsDTO
     * @return
     */
    @PostMapping("/expDeatilsList")
    public R<List<IncomesDeatilsDTO>> expDeatilsList(@RequestBody IncomesDeatilsDTO incomesDeatilsDTO) {
        return new R(rptBusinessAnalysisService.expDeatilsList(incomesDeatilsDTO));
    }

    /**
     * 支出明细-箱维度
     *
     * @param incomesContainerDeatilsDTO
     * @return
     */
    @PostMapping("/expContainerDeatilsList")
    public R<List<FdBusCostDetailDTO>> expContainerDeatilsList(@RequestBody IncomesContainerDeatilsDTO incomesContainerDeatilsDTO) {
        return new R(rptBusinessAnalysisService.expContainerDeatilsList(incomesContainerDeatilsDTO));
    }

    /**
     * 毛利-箱维度
     *
     * @param incomesDeatilsDTO
     * @return
     */
    @PostMapping("/grossProfitContainerList")
    public R<List<RptBusinessAnalysis>> grossProfitContainerList(@RequestBody IncomesDeatilsDTO incomesDeatilsDTO) {
        return new R(rptBusinessAnalysisService.grossProfitContainerList(incomesDeatilsDTO));
    }

    /**
     * 毛利明细-箱维度
     *
     * @param incomesDeatilsDTO
     * @return
     */
    @PostMapping("/grossProfitContainerDetails")
    public R<GrossProfitDeatilsDTO> grossProfitContainerDetails(@RequestBody IncomesDeatilsDTO incomesDeatilsDTO) {
        return new R(rptBusinessAnalysisService.grossProfitContainerDetails(incomesDeatilsDTO));
    }
}
