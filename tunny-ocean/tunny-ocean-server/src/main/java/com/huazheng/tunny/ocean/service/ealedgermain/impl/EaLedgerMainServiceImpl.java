package com.huazheng.tunny.ocean.service.ealedgermain.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.Shifmanagement;
import com.huazheng.tunny.ocean.api.entity.StationManagement;
import com.huazheng.tunny.ocean.api.entity.eabillmain.EaBillMain;
import com.huazheng.tunny.ocean.api.entity.eabillmain.EaBillSubtable;
import com.huazheng.tunny.ocean.api.entity.eabookingorder.EaContainerInfo;
import com.huazheng.tunny.ocean.api.entity.eabookingorder.EaContainerSummary;
import com.huazheng.tunny.ocean.api.entity.eabusinessprocess.EaBusinessProcess;
import com.huazheng.tunny.ocean.api.entity.ealedgermain.EaLedgerDetail;
import com.huazheng.tunny.ocean.api.entity.ealedgermain.EaLedgerMain;
import com.huazheng.tunny.ocean.api.enums.*;
import com.huazheng.tunny.ocean.api.feign.RemoteAdminService;
import com.huazheng.tunny.ocean.api.util.SysCommon;
import com.huazheng.tunny.ocean.api.vo.FdShippingAccountVO;
import com.huazheng.tunny.ocean.api.vo.SysDictVo;
import com.huazheng.tunny.ocean.mapper.ShifmanagementMapper;
import com.huazheng.tunny.ocean.mapper.StationManagementMapper;
import com.huazheng.tunny.ocean.mapper.eabillmain.EaBillMainMapper;
import com.huazheng.tunny.ocean.mapper.eabillmain.EaBillSubtableMapper;
import com.huazheng.tunny.ocean.mapper.eabookingorder.EaContainerInfoMapper;
import com.huazheng.tunny.ocean.mapper.eabookingorder.EaContainerSummaryMapper;
import com.huazheng.tunny.ocean.mapper.eabusinessprocess.EaBusinessProcessMapper;
import com.huazheng.tunny.ocean.mapper.ealedgermain.EaLedgerDetailMapper;
import com.huazheng.tunny.ocean.mapper.ealedgermain.EaLedgerMainMapper;
import com.huazheng.tunny.ocean.service.ProvinceShiftNoService;
import com.huazheng.tunny.ocean.service.eabillmain.EaBillMainService;
import com.huazheng.tunny.ocean.service.ealedgermain.EaLedgerMainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service("eaLedgerMainService")
public class EaLedgerMainServiceImpl extends ServiceImpl<EaLedgerMainMapper, EaLedgerMain> implements EaLedgerMainService {

    @Autowired
    private EaLedgerMainMapper eaLedgerMainMapper;
    @Autowired
    private EaLedgerDetailMapper eaLedgerDetailMapper;
    @Autowired
    private EaBusinessProcessMapper eaBusinessProcessMapper;
    @Autowired
    private EaContainerInfoMapper eaContainerInfoMapper;
    @Autowired
    private EaContainerSummaryMapper eaContainerSummaryMapper;
    @Autowired
    private ShifmanagementMapper shifmanagementMapper;
    @Autowired
    private EaBillMainService eaBillMainService;
    @Autowired
    private EaBillMainMapper eaBillMainMapper;
    @Autowired
    private EaBillSubtableMapper eaBillSubtableMapper;
    @Autowired
    private RemoteAdminService remoteAdminService;
    @Autowired
    private StationManagementMapper stationManagementMapper;
    @Autowired
    private ProvinceShiftNoService provinceShiftNoService;

    /**
     * 查询台账主表信息
     *
     * @param ledgerId 台账主表ID
     * @return 台账主表信息
     */
    @Override
    public EaLedgerMain selectEaLedgerMainById(Long ledgerId) {
        return eaLedgerMainMapper.selectEaLedgerMainById(ledgerId);
    }

    /**
     * 查询台账主表列表
     *
     * @param eaLedgerMain 台账主表信息
     * @return 台账主表集合
     */
    @Override
    public List<EaLedgerMain> selectEaLedgerMainList(EaLedgerMain eaLedgerMain) {
        return eaLedgerMainMapper.selectEaLedgerMainList(eaLedgerMain);
    }


    /**
     * 分页模糊查询台账主表列表
     *
     * @return 台账主表集合
     */
    @Override
    public Page selectEaLedgerMainListByLike(Query query) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        EaLedgerMain eaLedgerMain = BeanUtil.mapToBean(query.getCondition(), EaLedgerMain.class, false);
        //eaLedgerMain.setPlatformCode(userInfo.getPlatformCode());
        query.setRecords(eaLedgerMainMapper.selectEaLedgerMainListByLike(query, eaLedgerMain));
        return query;
    }

    @Override
    public Page selectShifmanagementByLike(Query query) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        Shifmanagement shifmanagement = BeanUtil.mapToBean(query.getCondition(), Shifmanagement.class, false);
        shifmanagement.setPlatformCode(userInfo.getPlatformCode());
        query.setRecords(eaLedgerMainMapper.selectShifmanagementByLike(query, shifmanagement));
        return query;
    }

    @Override
    public R selectFeeListByLedgerMain(EaLedgerDetail eaLedgerDetail) {
        List<EaLedgerDetail> list = eaLedgerDetailMapper.selectEaLedgerDetailList(eaLedgerDetail);
        if (CollUtil.isNotEmpty(list)) {
            List<SysDictVo> xxlx = getDictList("xxlx");
            for (EaLedgerDetail detail : list) {
                for (SysDictVo xxlxDict : xxlx) {
                    if (detail.getContainerOwner().equals(xxlxDict.getCode())) {
                        detail.setContainerOwner(xxlxDict.getName());
                        break;
                    }
                }
            }
        }
        return R.success(list);
    }

    /**
     * 查询字典
     *
     * @return List<SysDictVo> 国家列表
     * <AUTHOR>
     * @since 2025/4/25 下午5:51
     **/
    public List<SysDictVo> getDictList(String type) {
        String data = remoteAdminService.selectDictByType2(type);
        return JSONUtil.toList(JSONUtil.parseArray(data), SysDictVo.class);
    }

    @Override
    public R isExamine(Map<String, Object> params) {
        EaLedgerMain eaLedgerMain = BeanUtil.mapToBean(params, EaLedgerMain.class, false);
        if (eaLedgerMain == null || eaLedgerMain.getLedgerId() == null) {
            return R.error("请输入台账ID");
        }
        EaLedgerMain oldLedgerMain = eaLedgerMainMapper.selectEaLedgerMainById(eaLedgerMain.getLedgerId());
        Shifmanagement shifmanagement = shifmanagementMapper.selectShifmanagementByPlatCodeAndShiftNo(oldLedgerMain.getPlatformCode(), oldLedgerMain.getShiftNo());
        if(shifmanagement == null){
            return R.error("未查询到对应班次");
        }
        List<String> shiftNoList = new ArrayList<>();
        if (SysEnum.Y.getKey().equals(shifmanagement.getIsMixed())) {
            Shifmanagement param = new Shifmanagement();
            param.setShiftName(shifmanagement.getShiftName());
            param.setIsMixed(shifmanagement.getIsMixed());
            param.setDeleteFlag(SysEnum.N.getKey());
            List<Shifmanagement> list = shifmanagementMapper.selectShifmanagementList(param);
            if (CollUtil.isEmpty(list) || list.size() == 1) {
                return R.error("现有未上传班次，等班次上传成功后再审核");
            }
            for (Shifmanagement item : list) {
                shiftNoList.add(item.getShiftId());
            }
        } else {
            shiftNoList.add(oldLedgerMain.getShiftNo());
        }
        eaLedgerMain.setShiftNoList(shiftNoList);
        List<EaLedgerMain> mainList = eaLedgerMainMapper.selectEaLedgerMainByShiftNoList(eaLedgerMain);
        if (CollUtil.isEmpty(mainList)) {
            return R.error("没有查询到对应的台账信息！");
        }
        if (CollUtil.isEmpty(mainList) || mainList.size() < shiftNoList.size()) {
            return R.error("现有未提交台账，等台账提交成功后再审核");
        }
        for (EaLedgerMain item : mainList) {
            if (!LedgerAuditStatus.PENDING.getKey().equals(item.getAuditStatus())) {
                return R.error("请勿重复审核");
            }
            if (LedgerAuditStatus.UNCOMMITTED.getKey().equals(eaLedgerMain.getAuditStatus())) {
                return R.error("请先提交" + item.getShiftNo() + "班次的台账");
            }
        }
        return R.success();
    }

    @Override
    public R examineList(Map<String, Object> params) {
        EaLedgerMain eaLedgerMain = BeanUtil.mapToBean(params, EaLedgerMain.class, false);
        if (eaLedgerMain == null || eaLedgerMain.getLedgerId() == null) {
            return R.error("请输入台账ID");
        }
        EaLedgerMain oldLedgerMain = eaLedgerMainMapper.selectEaLedgerMainById(eaLedgerMain.getLedgerId());
        Shifmanagement shifmanagement = shifmanagementMapper.selectShifmanagementByPlatCodeAndShiftNo(oldLedgerMain.getPlatformCode(), oldLedgerMain.getShiftNo());
        List<String> shiftNoList = new ArrayList<>();
        if (SysEnum.Y.getKey().equals(shifmanagement.getIsMixed())) {
            Shifmanagement param = new Shifmanagement();
            param.setShiftName(shifmanagement.getShiftName());
            param.setDeleteFlag(SysEnum.N.getKey());
            List<Shifmanagement> list = shifmanagementMapper.selectShifmanagementList(param);
            if (CollUtil.isEmpty(list) || list.size() == 1) {
                return R.error("现有未上传班次，等班次上传成功后再审核");
            }
            for (Shifmanagement item : list) {
                shiftNoList.add(item.getShiftId());
            }
        } else {
            shiftNoList.add(oldLedgerMain.getShiftNo());
        }
        eaLedgerMain.setShiftNoList(shiftNoList);
        List<EaLedgerMain> mainList = eaLedgerMainMapper.selectEaLedgerMainByShiftNoList(eaLedgerMain);
        if (CollUtil.isEmpty(mainList)) {
            return R.error("没有查询到对应的台账信息！");
        }

        Map<String, EaLedgerMain> map = new HashMap<>();
        for (EaLedgerMain item : mainList) {
            EaLedgerDetail detail = new EaLedgerDetail();
            detail.setShiftNo(item.getShiftNo());
            detail.setDelFlag(SysEnum.N.getKey());
            List<EaLedgerDetail> detailList = eaLedgerDetailMapper.selectEaLedgerDetailList(detail);
            item.setDetailList(detailList);
            map.put(item.getPlatformName(), item);
        }
        return R.success(map);
    }

    public static LocalDateTime convertToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * 新增台账主表
     *
     * @param eaLedgerMain 台账主表信息
     * @return 结果
     */
    @Override
    public int insertEaLedgerMain(EaLedgerMain eaLedgerMain) {
        return eaLedgerMainMapper.insertEaLedgerMain(eaLedgerMain);
    }

    /**
     * 修改台账主表
     *
     * @param eaLedgerMain 台账主表信息
     * @return 结果
     */
    @Override
    public int updateEaLedgerMain(EaLedgerMain eaLedgerMain) {
        return eaLedgerMainMapper.updateEaLedgerMain(eaLedgerMain);
    }


    /**
     * 删除台账主表
     *
     * @param ledgerId 台账主表ID
     * @return 结果
     */
    @Override
    public int deleteEaLedgerMainById(Long ledgerId) {
        return eaLedgerMainMapper.deleteEaLedgerMainById(ledgerId);
    }


    /**
     * 批量删除台账主表对象
     *
     * @param ledgerIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEaLedgerMainByIds(Integer[] ledgerIds) {
        return eaLedgerMainMapper.deleteEaLedgerMainByIds(ledgerIds);
    }

    /**
     * 生成台账
     *
     * @param eaLedgerMain 生成逻辑参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R generateLedger(EaLedgerMain eaLedgerMain) {
        if (eaLedgerMain == null || CollUtil.isEmpty(eaLedgerMain.getShiftNoList())) {
            return R.error("请传入班次号集合");
        }
        Map<String, String> map = new HashMap<>();
        Map<String, Shifmanagement> shiftMap = new HashMap<>();
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        for (String shiftNo : eaLedgerMain.getShiftNoList()) {
            EaBusinessProcess eaBusinessProcess = new EaBusinessProcess();
            eaBusinessProcess.setShiftNo(shiftNo);
            eaBusinessProcess.setDelFlag("N");
            eaBusinessProcess.setAuditStatus(LedgerAuditStatus.UNCOMMITTED.getKey());
            List<EaBusinessProcess> processList = eaBusinessProcessMapper.selectEaBusinessProcessList(eaBusinessProcess);
            if (CollUtil.isNotEmpty(processList)) {
                return R.error("存在未审核的业务流程单，无法生成台账,班次号：" + shiftNo);
            }
            eaBusinessProcess.setAuditStatus(LedgerAuditStatus.PENDING.getKey());
            processList = eaBusinessProcessMapper.selectEaBusinessProcessList(eaBusinessProcess);
            if (CollUtil.isNotEmpty(processList)) {
                return R.error("存在未审核的业务流程单，无法生成台账,班次号：" + shiftNo);
            }
            Shifmanagement shift = checkShift(shiftNo, userInfo);
            if (shift == null) {
                return R.error("查询不到班次信息");
            } else {
                shiftMap.put(shiftNo, shift);
            }
            EaContainerInfo eaContainerInfo = new EaContainerInfo();
            eaContainerInfo.setShiftNo(shiftNo);
            eaContainerInfo.setDelFlag("N");
            List<EaContainerInfo> containerInfoList = eaContainerInfoMapper.selectEaContainerInfoList(eaContainerInfo);
            if (CollUtil.isNotEmpty(containerInfoList)) {
                map.put(shiftNo, BillStageEnum.TWO.getKey());
                continue;
            }
            map.put(shiftNo, BillStageEnum.ONE.getKey());
        }
        List<EaContainerSummary> summaryList = new ArrayList<>();
        List<EaLedgerMain> ledgerList = new ArrayList<>();
        List<EaLedgerDetail> detailList = new ArrayList<>();
        for (String shiftNo : eaLedgerMain.getShiftNoList()) {
            if (BillStageEnum.ONE.getKey().equals(map.get(shiftNo))) {
                this.generateLedgerOne(shiftMap.get(shiftNo), ledgerList, summaryList, userInfo);
            } else {
                this.generateLedgerTwo(shiftMap.get(shiftNo), ledgerList, detailList, userInfo);
            }
        }
        //批量插入台账子表数据
        if (CollUtil.isNotEmpty(detailList)) {
            eaLedgerDetailMapper.deleteEaLedgerDetailByShiftNoList(eaLedgerMain.getShiftNoList());
            int step = 50;
            for (int i = 0; i < detailList.size(); i += step) {
                eaLedgerDetailMapper.insertEaLedgerDetailList(detailList.subList(i, (i + step) < detailList.size() ? (i + step) : detailList.size()));
            }
        }
        //批量插入台账数据
        if (CollUtil.isNotEmpty(ledgerList)) {
            int step = 50;
            for (int i = 0; i < ledgerList.size(); i += step) {
                eaLedgerMainMapper.insertEaLedgerMainList(ledgerList.subList(i, (i + step) < ledgerList.size() ? (i + step) : ledgerList.size()));
            }
        }
        //删除旧箱汇-省与铁路总数据
        for (String shiftNo : eaLedgerMain.getShiftNoList()) {
            if (BillStageEnum.ONE.getKey().equals(map.get(shiftNo))) {
                updateEaContainerSummaryByShiftNo(userInfo, shiftNo);
            }
        }
        if (CollUtil.isNotEmpty(summaryList)) {
            for (EaContainerSummary summary : summaryList) {
                eaContainerSummaryMapper.insertEaContainerSummary(summary);
            }
        }
        return R.success();
    }

    public void updateEaContainerSummaryByShiftNo(SecruityUser userInfo, String shiftNo) {
        EntityWrapper<EaContainerSummary> wrapper = new EntityWrapper<>();
        wrapper.eq("shift_no", shiftNo);
        wrapper.eq("payee_code", SysCommon.RECEIVE_CODE_ZTDL);
        wrapper.eq("del_flag", SysEnum.N.getKey());
        EaContainerSummary process = new EaContainerSummary();
        process.setDelFlag(SysEnum.Y.getKey());
        process.setUpdateTime(LocalDateTime.now());
        process.setUpdateBy(userInfo.getRealName());
        process.setUpdateById(userInfo.getId());
        eaContainerSummaryMapper.update(process, wrapper);
    }

    /**
     * 生成台账(一次)
     *
     * @param shift      班次信息
     * @param ledgerList
     * @param userInfo
     * @return 结果
     */
    public void generateLedgerOne(Shifmanagement shift, List<EaLedgerMain> ledgerList, List<EaContainerSummary> summaryList, SecruityUser userInfo) {
        EaLedgerMain eaLedgerMain = new EaLedgerMain();
        eaLedgerMain.setShiftNo(shift.getShiftId());
        //统计箱汇总数据，整合成台账数据
        List<EaLedgerMain> list = eaLedgerMainMapper.statisticsContainerSummary(eaLedgerMain);
        if (CollUtil.isNotEmpty(list)) {
            for (EaLedgerMain ledger : list) {
                //检验台账是否已存在
                EaLedgerMain params = new EaLedgerMain();
                params.setShiftNo(ledger.getShiftNo());
                params.setPlatformCode(ledger.getPlatformCode());
                params.setDelFlag("N");
                List<EaLedgerMain> oldList = eaLedgerMainMapper.selectEaLedgerMainList(params);
                if (CollUtil.isNotEmpty(oldList)) {
                    //检验已存在时,赋值台账记录ID、更新人名称、更新人ID
                    ledger.setLedgerId(oldList.get(0).getLedgerId());
                    ledger.setUpdateBy(userInfo.getRealName());
                    ledger.setUpdateById(userInfo.getId());
                    if (StrUtil.isNotBlank(oldList.get(0).getAuditStatus())) {
                        ledger.setAuditStatus(oldList.get(0).getAuditStatus());
                    } else {
                        ledger.setAuditStatus(LedgerAuditStatus.UNCOMMITTED.getKey());
                    }
                    if (StrUtil.isBlank(oldList.get(0).getShippingCity())) {
                        updateShippingCity(shift, ledger);
                    } else {
                        ledger.setShippingCity(oldList.get(0).getShippingCity());
                    }
                } else {
                    //检验不存在时，赋值创建人名称、创建人ID
                    ledger.setCreateBy(userInfo.getRealName());
                    ledger.setCreateById(userInfo.getId());
                    ledger.setCreateTime(LocalDateTime.now());
                    ledger.setAuditStatus(LedgerAuditStatus.UNCOMMITTED.getKey());
                    updateShippingCity(shift, ledger);
                }
                ledger.setPayableRailwayDomesticFee(ledger.getPayableDomesticFee());
                ledger.setPayableRailwayForeignFee(ledger.getPayableForeignFee());
                ledger.setPayableRailwayForeignOriginal(ledger.getPayableForeignOriginal());
                ledger.setLedgerStage(BillStageEnum.ONE.getKey());

                EaContainerSummary summary = new EaContainerSummary();
                summary.setShiftNo(ledger.getShiftNo());
                summary.setPayerCode(ledger.getPlatformCode());
                summary.setPayeeCode(ledger.getProvincialPlatformCode());
                summary.setDelFlag(SysEnum.N.getKey());
                List<EaContainerSummary> sumList = eaContainerSummaryMapper.selectEaContainerSummaryList(summary);
                if (CollUtil.isNotEmpty(sumList)) {
                    for (EaContainerSummary eaContainerSummary : sumList) {
                        eaContainerSummary.setContainerSummaryId(null);
                        eaContainerSummary.setPayerCode(ledger.getProvincialPlatformCode());
                        eaContainerSummary.setPayerName(ledger.getProvincialPlatformName());
                        eaContainerSummary.setPayeeCode(SysCommon.RECEIVE_CODE_ZTDL);
                        eaContainerSummary.setPayeeName(SysCommon.RECEIVE_NAME_ZTDL);
                    }
                    summaryList.addAll(sumList);
                }
            }
            ledgerList.addAll(list);
        }
    }

    private void updateShippingCity(Shifmanagement shift, EaLedgerMain ledger) {
        if (StrUtil.isNotBlank(shift.getDestinationName()) || StrUtil.isNotBlank(shift.getDestination())) {
            StationManagement sel2 = new StationManagement();
            if ("G".equals(shift.getTrip())) {
                sel2.setStationName(shift.getDestinationName());
            } else {
                sel2.setStationName(shift.getDestination());
            }
            sel2.setDeleteFlag("N");
            if (StrUtil.isNotBlank(sel2.getStationName())) {
                List<StationManagement> stationManagements = stationManagementMapper.selectStationManagementList(sel2);
                if (CollUtil.isNotEmpty(stationManagements)) {
                    ledger.setShippingCity(stationManagements.get(0).getCity());
                }
            }
        }
    }


    /**
     * 生成台账(二次)
     *
     * @param shift      班次信息
     * @param ledgerList
     * @param userInfo
     * @return 结果
     */
    public void generateLedgerTwo(Shifmanagement shift, List<EaLedgerMain> ledgerList, List<EaLedgerDetail> detailList, SecruityUser userInfo) {
        Map<String, EaLedgerMain> mainMap = new HashMap<>();
        EaLedgerDetail detail = new EaLedgerDetail();
        detail.setShiftNo(shift.getShiftId());
        detail.setPlatformCode("MC");
        //统计箱汇总数据，整合成台账数据
        List<EaLedgerDetail> list = eaLedgerDetailMapper.statisticsEaLedgerDetailByFee(detail);
        detail.setPlatformCode("MP");
        List<EaLedgerDetail> shengList = eaLedgerDetailMapper.statisticsEaLedgerDetailByFee(detail);
        detail.setPlatformCode("ztdl");
        List<EaLedgerDetail> tieList = eaLedgerDetailMapper.statisticsEaLedgerDetailByFee(detail);
        if (CollUtil.isNotEmpty(list)) {
            for (EaLedgerDetail ledger : list) {
                for (EaLedgerDetail shengLedger : shengList) {
                    if (ledger.getShiftNo().equals(shengLedger.getShiftNo()) && ledger.getContainerNumber().equals(shengLedger.getContainerNumber())) {
                        ledger.setPayableDomesticFee(shengLedger.getReceivableDomesticFee());
                        ledger.setPayableForeignFee(shengLedger.getReceivableForeignFee());
                        ledger.setPayableForeignOriginal(shengLedger.getReceivableForeignOriginal());
                    }
                }
                for (EaLedgerDetail tieLedger : tieList) {
                    if (ledger.getShiftNo().equals(tieLedger.getShiftNo()) && ledger.getContainerNumber().equals(tieLedger.getContainerNumber())) {
                        ledger.setPayableRailwayDomesticFee(tieLedger.getReceivableDomesticFee());
                        ledger.setPayableRailwayForeignFee(tieLedger.getReceivableForeignFee());
                        ledger.setPayableRailwayForeignOriginal(tieLedger.getReceivableForeignOriginal());
                    }
                }
                ledger.setCreateBy(userInfo.getRealName());
                ledger.setCreateById(userInfo.getId());
                ledger.setCreateTime(LocalDateTime.now());
                this.convertEaLedgerDetail(ledger, ledgerList, mainMap, shift);
            }
            detailList.addAll(list);
        }
    }

    private void convertEaLedgerDetail(EaLedgerDetail ledger, List<EaLedgerMain> mainList, Map<String, EaLedgerMain> mainMap, Shifmanagement shifmanagement) {
        EaLedgerMain main = mainMap.get(ledger.getShiftNo() + "@" + ledger.getPlatformCode());
        if (main == null) {
            EaLedgerMain params = new EaLedgerMain();
            params.setShiftNo(ledger.getShiftNo());
            params.setPlatformCode(ledger.getPlatformCode());
            params.setDelFlag("N");
            List<EaLedgerMain> oldList = eaLedgerMainMapper.selectEaLedgerMainList(params);
            if (CollUtil.isEmpty(oldList)) {
                main = new EaLedgerMain();
                BeanUtil.copyProperties(ledger, main);
                BeanUtil.copyProperties(shifmanagement, main);
                main.setShiftNo(shifmanagement.getSelfShiftCode());
                main.setProvincialPlatformCode(PlatformLevelEnum.PROVINCE_CODE.getKey());
                main.setProvincialPlatformName(PlatformLevelEnum.PROVINCE_NAME.getKey());
                main.setAuditStatus(LedgerAuditStatus.UNCOMMITTED.getKey());
                updateShippingCity(shifmanagement, main);
            } else {
                main = oldList.get(0);
                if (StrUtil.isBlank(main.getShippingCity())) {
                    updateShippingCity(shifmanagement, main);
                }
            }
            main.setReceivableDomesticFee(BigDecimal.ZERO);
            main.setReceivableForeignFee(BigDecimal.ZERO);
            main.setReceivableForeignOriginal(BigDecimal.ZERO);
            main.setPayableDomesticFee(BigDecimal.ZERO);
            main.setPayableForeignFee(BigDecimal.ZERO);
            main.setPayableForeignOriginal(BigDecimal.ZERO);
            main.setPayableRailwayDomesticFee(BigDecimal.ZERO);
            main.setPayableRailwayForeignFee(BigDecimal.ZERO);
            main.setPayableRailwayForeignOriginal(BigDecimal.ZERO);
            main.setLedgerStage(BillStageEnum.TWO.getKey());
            mainMap.put(ledger.getShiftNo() + "@" + ledger.getPlatformCode(), main);
            mainList.add(main);
        }
        main.setReceivableDomesticFee(main.getReceivableDomesticFee().add(ledger.getReceivableDomesticFee()));
        main.setReceivableForeignFee(main.getReceivableForeignFee().add(ledger.getReceivableForeignFee()));
        main.setReceivableForeignOriginal(main.getReceivableForeignOriginal().add(ledger.getReceivableForeignOriginal()));
        main.setPayableDomesticFee(main.getPayableDomesticFee().add(ledger.getPayableDomesticFee()));
        main.setPayableForeignFee(main.getPayableForeignFee().add(ledger.getPayableForeignFee()));
        main.setPayableForeignOriginal(main.getPayableForeignOriginal().add(ledger.getPayableForeignOriginal()));
        main.setPayableRailwayDomesticFee(main.getPayableRailwayDomesticFee().add(ledger.getPayableRailwayDomesticFee()));
        main.setPayableRailwayForeignFee(main.getPayableRailwayForeignFee().add(ledger.getPayableRailwayForeignFee()));
        main.setPayableRailwayForeignOriginal(main.getPayableRailwayForeignOriginal().add(ledger.getPayableRailwayForeignOriginal()));
    }

    /**
     * 删除台账
     *
     * @param eaLedgerMain 删除逻辑参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R delLedger(EaLedgerMain eaLedgerMain) {
        if (eaLedgerMain == null || StrUtil.isBlank(eaLedgerMain.getShiftNo())) {
            return R.error("请传入班次号");
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        eaLedgerMain.setUpdateById(userInfo.getId());
        eaLedgerMain.setUpdateBy(userInfo.getRealName());
        //删除台账主表数据
        eaLedgerMainMapper.deleteEaLedgerMainByShiftNo(eaLedgerMain);
        //删除台账子表数据
        EaLedgerDetail eaLedgerDetail = new EaLedgerDetail();
        BeanUtil.copyProperties(eaLedgerMain, eaLedgerDetail);
        eaLedgerDetailMapper.deleteEaLedgerDetailByShiftNo(eaLedgerDetail);
        return R.success();
    }

    @Override
    public R submitLedger(EaLedgerMain eaLedgerMain) {
        if (eaLedgerMain == null || eaLedgerMain.getLedgerId() == null) {
            return R.error("请传台账ID");
        }
        EaLedgerMain oldLedger = eaLedgerMainMapper.selectEaLedgerMainById(eaLedgerMain.getLedgerId());
        if (!LedgerAuditStatus.UNCOMMITTED.getKey().equals(oldLedger.getAuditStatus()) && !LedgerAuditStatus.REJECTED.getKey().equals(oldLedger.getAuditStatus())) {
            return R.error("该台账已提交");
        }
        EaLedgerMain ledger = new EaLedgerMain();
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        ledger.setLedgerId(eaLedgerMain.getLedgerId());
        ledger.setUpdateById(userInfo.getId());
        ledger.setUpdateBy(userInfo.getRealName());
        oldLedger.setAuditStatus(LedgerAuditStatus.PENDING.getKey());
        eaLedgerMainMapper.updateEaLedgerMain(oldLedger);
        return R.success();
    }

    /**
     * 台账审核
     *
     * @param eaLedgerMain 审核逻辑参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R examineLedger(EaLedgerMain eaLedgerMain) {
        if (eaLedgerMain == null || eaLedgerMain.getLedgerId() == null || StrUtil.isBlank(eaLedgerMain.getAuditStatus())) {
            return R.error("请传台账ID及审核状态");
        }
        EaLedgerMain oldLedger = eaLedgerMainMapper.selectEaLedgerMainById(eaLedgerMain.getLedgerId());
        if (LedgerAuditStatus.UNCOMMITTED.getKey().equals(oldLedger.getAuditStatus())) {
            return R.error("该台账未提交");
        }
        if (!LedgerAuditStatus.PENDING.getKey().equals(oldLedger.getAuditStatus())) {
            return R.error("该台账已审核");
        }
        Shifmanagement shifmanagement = shifmanagementMapper.selectShifmanagementByPlatCodeAndShiftNo(oldLedger.getPlatformCode(), oldLedger.getShiftNo());
        List<String> shiftNoList = new ArrayList<>();
        if ("Y".equals(shifmanagement.getIsMixed())) {
            Shifmanagement param = new Shifmanagement();
            param.setProvinceShiftNo(shifmanagement.getProvinceShiftNo());
            param.setDeleteFlag("N");
            List<Shifmanagement> list = shifmanagementMapper.selectShifmanagementList(shifmanagement);
            if (CollUtil.isNotEmpty(list)) {
                for (Shifmanagement item : list) {
                    shiftNoList.add(item.getSelfShiftCode());
                }
            }
        } else {
            shiftNoList.add(oldLedger.getShiftNo());
        }
        if (CollUtil.isEmpty(shiftNoList)) {
            return R.error("班次信息获取异常");
        }

        if (shifmanagement != null && StrUtil.isBlank(shifmanagement.getProvinceShiftNo())
                && LedgerAuditStatus.APPROVED.getKey().equals(eaLedgerMain.getAuditStatus())) {
            FdShippingAccountVO vo = new FdShippingAccountVO();
            vo.setShippingLine(oldLedger.getShippingLine());
            vo.setPlatformCode(oldLedger.getPlatformCode());
            vo.setTrip(oldLedger.getTrip());
            vo.setShippingTime(convertToLocalDateTime(oldLedger.getPlanShipTime()));
            vo.setShiftNo(oldLedger.getShiftNo());
            String provinceShiftNo = provinceShiftNoService.createProvinceShiftNoTwo(vo);
            if (StrUtil.isNotBlank(provinceShiftNo)) {
                if (provinceShiftNo.contains("请联系系统管理员")) {
                    throw new RuntimeException(provinceShiftNo);
                }
            }
            oldLedger.setProvinceShiftNo(provinceShiftNo);
        } else {
            oldLedger.setProvinceShiftNo(oldLedger.getProvinceShiftNo());
        }

        EaLedgerMain ledgerMain = new EaLedgerMain();
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        ledgerMain.setShiftNoList(shiftNoList);
        ledgerMain.setUpdateById(userInfo.getId());
        ledgerMain.setUpdateBy(userInfo.getRealName());
        ledgerMain.setAuditStatus(eaLedgerMain.getAuditStatus());
        ledgerMain.setAuditRemark(eaLedgerMain.getAuditRemark());
        ledgerMain.setProvinceShiftNo(oldLedger.getProvinceShiftNo());
        ledgerMain.setAuditTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        ledgerMain.setAuditName(userInfo.getRealName());
        eaLedgerMainMapper.examineLedger(ledgerMain);

        for (String shiftNo : shiftNoList) {
            EaBillMain eaBillMain = new EaBillMain();
            eaBillMain.setShiftNo(shiftNo);
            if (BillStageEnum.ONE.getKey().equals(oldLedger.getLedgerStage())) {
                eaBillMainService.generateBill(eaBillMain);
            } else {
                eaBillMain.setPayeeCode(PlatformLevelEnum.TIELU_CODE.getKey());
                eaBillMainService.generateBillTwo(eaBillMain);
            }
        }
        return R.success();
    }

    @Override
    public R examineRevoke(EaLedgerMain eaLedgerMain) {
        if (eaLedgerMain == null || eaLedgerMain.getLedgerId() == null) {
            return R.error("请传台账ID");
        }
        EaLedgerMain oldLedger = eaLedgerMainMapper.selectEaLedgerMainById(eaLedgerMain.getLedgerId());
        if (!LedgerAuditStatus.APPROVED.getKey().equals(oldLedger.getAuditStatus())) {
            return R.error("该台账未审核通过");
        }
        Shifmanagement shifmanagement = shifmanagementMapper.selectShifmanagementByPlatCodeAndShiftNo(oldLedger.getPlatformCode(), oldLedger.getShiftNo());
        List<String> shiftNoList = new ArrayList<>();
        if ("Y".equals(shifmanagement.getIsMixed())) {
            Shifmanagement param = new Shifmanagement();
            param.setProvinceShiftNo(shifmanagement.getProvinceShiftNo());
            param.setDeleteFlag("N");
            List<Shifmanagement> list = shifmanagementMapper.selectShifmanagementList(shifmanagement);
            if (CollUtil.isNotEmpty(list)) {
                for (Shifmanagement item : list) {
                    shiftNoList.add(item.getSelfShiftCode());
                }
            }
        } else {
            shiftNoList.add(oldLedger.getShiftNo());
        }
        if (CollUtil.isEmpty(shiftNoList)) {
            return R.error("班次信息获取异常");
        }

        EaBillSubtable eaBillSubtable = new EaBillSubtable();
        eaBillSubtable.setShiftNoList(shiftNoList);
        eaBillSubtable.setBillStatus(BillStatusEnum.SETTLED.getKey());
        List<EaBillSubtable> subtableList = eaBillSubtableMapper.selectEaBillSubtableList(eaBillSubtable);
        if (CollUtil.isNotEmpty(subtableList)) {
            return R.error("存在已结清的账单，该台账无法撤回");
        }

        EaLedgerMain ledgerMain = new EaLedgerMain();
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        ledgerMain.setShiftNoList(shiftNoList);
        ledgerMain.setUpdateById(userInfo.getId());
        ledgerMain.setUpdateBy(userInfo.getRealName());
        ledgerMain.setAuditStatus(LedgerAuditStatus.PENDING.getKey());
        eaLedgerMainMapper.examineLedger(ledgerMain);

        EaBillMain eaBillMain = new EaBillMain();
        eaBillMain.setShiftNoList(shiftNoList);
        eaBillMain.setPayeeCode(PlatformLevelEnum.TIELU_CODE.getKey());
        eaBillMainMapper.delBillMainByShiftNo(eaBillMain);
        eaBillSubtableMapper.delBillSubtableByShiftNo(eaBillMain);
        return R.success();
    }


    /**
     * 修改省级班列号
     *
     * @param eaLedgerMain 修改参数
     * @return R
     * <AUTHOR>
     * @since 2025/7/28 13:50
     **/
    @Override
    public R updateProvinceShiftNo(EaLedgerMain eaLedgerMain) {
        EaLedgerMain eaLedger = eaLedgerMainMapper.selectEaLedgerMainById(eaLedgerMain.getLedgerId());
        if (eaLedger == null) {
            return R.error("台账不存在");
        }
        if (StrUtil.isBlank(eaLedger.getProvinceShiftNo())) {
            return R.error("请在台账审核之后，再进行修改");
        }
        //查询班次信息
        Shifmanagement shifmanagement = shifmanagementMapper.selectShifmanagementByPlatCodeAndShiftNo(eaLedger.getPlatformCode(), eaLedger.getShiftNo());
        if (shifmanagement == null) {
            return R.error("该班次不存在");
        }
        //检查省级班次号是否存在
        Shifmanagement parameter = new Shifmanagement();
        parameter.setDeleteFlag("N");
        parameter.setProvinceShiftNo(eaLedgerMain.getProvinceShiftNo());
        parameter.setParentIdNull("1");
        List<Shifmanagement> province = shifmanagementMapper.selectShifmanagementList(parameter);
        if (CollUtil.isNotEmpty(province)) {
            for (Shifmanagement p : province) {
                //若不是混编班列，省级班次号不会重复
                if (!"Y".equals(p.getIsMixed()) && !p.getShiftId().equals(shifmanagement.getShiftId())) {
                    return R.error("该省级班次号已存在");
                }
            }
        }
        //根据省级班次号更新台账(包含混编班次)
        EaLedgerMain param = new EaLedgerMain();
        param.setProvinceShiftNo(eaLedger.getProvinceShiftNo());
        List<EaLedgerMain> eaLedgerMainList = eaLedgerMainMapper.selectEaLedgerMainList(param);
        if (CollUtil.isNotEmpty(eaLedgerMainList)) {
            for (EaLedgerMain item : eaLedgerMainList) {
                EaLedgerMain updateObject = new EaLedgerMain();
                updateObject.setLedgerId(item.getLedgerId());
                updateObject.setProvinceShiftNo(eaLedgerMain.getProvinceShiftNo());
                updateObject.setPlanShipTime(eaLedgerMain.getPlanShipTime());
                eaLedgerMainMapper.updateEaLedgerMain(updateObject);
            }
        }
        //更新班次信息
        shifmanagementMapper.updateProvinceShiftNo(eaLedgerMain.getProvinceShiftNo(), eaLedgerMain.getPlanShipTime(), eaLedger.getProvinceShiftNo());

        return R.success("修改成功");
    }


    /**
     * 修改发运时间
     *
     * @param eaLedgerMain 修改参数
     * @return R
     * <AUTHOR>
     * @since 2025/7/28 14:05
     **/
    @Override
    public R updateShipTime(EaLedgerMain eaLedgerMain) {
        EaLedgerMain eaLedger = eaLedgerMainMapper.selectEaLedgerMainById(eaLedgerMain.getLedgerId());
        if (eaLedger == null) {
            return R.error("台账不存在");
        }
        //查询班次信息
        Shifmanagement shifmanagement = shifmanagementMapper.selectShifmanagementByPlatCodeAndShiftNo(eaLedger.getPlatformCode(), eaLedger.getShiftNo());
        if (shifmanagement == null) {
            return R.error("该班次不存在");
        }
        eaLedgerMainMapper.updateEaLedgerMain(eaLedgerMain);
        Shifmanagement parameter = new Shifmanagement();
        parameter.setRowId(shifmanagement.getRowId());
        parameter.setPlanShipTime(eaLedgerMain.getPlanShipTime());
        //更新班次信息
        shifmanagementMapper.updateShifmanagement(parameter);
        return R.success("修改成功");

    }

    @Override
    public void exportEaLedgerMainExcel(EaLedgerMain eaLedgerMain, HttpServletResponse response) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        List<EaLedgerMain> list = eaLedgerMainMapper.selectEaLedgerMainListByLike(eaLedgerMain);
        ExcelWriter writer = null;
        OutputStream out = null;
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            List<Map<String, Object>> exportList = new ArrayList<>();
            for (EaLedgerMain ledgerMain : list) {
                Map<String, Object> row = new LinkedHashMap<>();
                row.put("省级班列号", ledgerMain.getProvinceShiftNo());
                row.put("班次号", ledgerMain.getShiftNo());
                row.put("班列名称", ledgerMain.getShiftName());
                row.put("平台名称", ledgerMain.getPlatformName());
                if (LedgerAuditStatus.UNCOMMITTED.getKey().equals(ledgerMain.getAuditStatus())) {
                    row.put("状态", "待提交");
                } else if (LedgerAuditStatus.PENDING.getKey().equals(ledgerMain.getAuditStatus())) {
                    row.put("状态", "待确认");
                } else if (LedgerAuditStatus.APPROVED.getKey().equals(ledgerMain.getAuditStatus())) {
                    row.put("状态", "已确认");
                } else if (LedgerAuditStatus.REJECTED.getKey().equals(ledgerMain.getAuditStatus())) {
                    row.put("状态", "驳回");
                } else {
                    row.put("状态", "历史迁移");
                }
                if (ledgerMain.getPlanShipTime() != null) {
                    row.put("发运时间", dateFormat.format(ledgerMain.getPlanShipTime()));
                } else {
                    row.put("发运时间", "");
                }
                row.put("发运线路", ledgerMain.getShippingLine());
                if ("G".equals(ledgerMain.getTrip())) {
                    row.put("方向", "去程");
                } else if ("R".equalsIgnoreCase(ledgerMain.getTrip())) {
                    row.put("方向", "回程");
                }
                row.put("货源组织单位", ledgerMain.getPlatformName());
                if (userInfo.getPlatformCode().startsWith("MP")) {
                    row.put("应收总金额", ledgerMain.getPayableTotalFee());
                    row.put("应收境内运费", ledgerMain.getPayableDomesticFee());
                    row.put("应收境外运费", ledgerMain.getPayableForeignFee());
                    row.put("应收境外运费（原币）", ledgerMain.getPayableForeignOriginal());
                    row.put("应付总金额", ledgerMain.getPayableRailwayTotalFee());
                    row.put("应付境内运费", ledgerMain.getPayableRailwayDomesticFee());
                    row.put("应付境外运费", ledgerMain.getPayableRailwayForeignFee());
                    row.put("应付境外运费（原币）", ledgerMain.getPayableRailwayForeignOriginal());
                } else {
                    row.put("应收总金额", ledgerMain.getReceivableTotalFee());
                    row.put("应收境内运费", ledgerMain.getReceivableDomesticFee());
                    row.put("应收境外运费", ledgerMain.getReceivableForeignFee());
                    row.put("应收境外运费（原币）", ledgerMain.getReceivableForeignOriginal());
                    row.put("应付总金额", ledgerMain.getPayableTotalFee());
                    row.put("应付境内运费", ledgerMain.getPayableDomesticFee());
                    row.put("应付境外运费", ledgerMain.getPayableForeignFee());
                    row.put("应付境外运费（原币）", ledgerMain.getPayableForeignOriginal());
                }

                if (BillStageEnum.ONE.getKey().equals(ledgerMain.getLedgerStage())) {
                    row.put("所处阶段", BillStageEnum.ONE.getValue());
                } else {
                    row.put("所处阶段", BillStageEnum.TWO.getValue());
                }
                row.put("审批意见", ledgerMain.getAuditRemark());
                row.put("创建人", ledgerMain.getCreateBy());
                if (ledgerMain.getCreateTime() != null) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    String addTime = ledgerMain.getCreateTime().format(formatter);
                    row.put("创建时间", addTime);
                } else {
                    row.put("创建时间", "");
                }
                exportList.add(row);
            }

            writer = ExcelUtil.getWriter(true);
            // 设置文件类型和名称
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            String fileName = URLEncoder.encode("发运台账导出.xlsx", "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

            out = response.getOutputStream();
            writer.write(exportList);
            // 设置省级班次号列宽
            writer.setColumnWidth(0, 18);
            // 设置班次号列宽
            writer.setColumnWidth(1, 18);
            // 设置班列名称列宽
            writer.setColumnWidth(2, 25);
            // 设置平台名称列宽
            writer.setColumnWidth(3, 50);
            // 设置状态列宽
            writer.setColumnWidth(4, 15);
            // 设置发运时间列宽
            writer.setColumnWidth(5, 15);
            // 设置发运路线列宽
            writer.setColumnWidth(6, 10);
            // 方向
            writer.setColumnWidth(7, 10);
            // 设置货源组织单位列宽
            writer.setColumnWidth(8, 50);
            // 应收境内运费（人民币）
            writer.setColumnWidth(9, 20);
            // 应收境外运费（人民币）
            writer.setColumnWidth(10, 20);
            // 应收境外运费（原币）
            writer.setColumnWidth(11, 20);
            // 应收应付金额
            writer.setColumnWidth(12, 20);
            // 应付境内运费（人民币）
            writer.setColumnWidth(13, 20);
            // 应付境外运费（人民币）
            writer.setColumnWidth(14, 20);
            // 应付境外运费（原币）
            writer.setColumnWidth(15, 20);
            // 应付应付金额
            writer.setColumnWidth(16, 20);
            // 所处阶段
            writer.setColumnWidth(17, 15);
            // 审批意见
            writer.setColumnWidth(18, 20);
            // 创建人
            writer.setColumnWidth(19, 50);
            // 设置创建时间列宽
            writer.setColumnWidth(20, 30);
            writer.flush(out);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (writer != null) {
                writer.close();
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }


    /**
     * 校验班次
     *
     * @param shiftNo  班次号
     * @param userInfo
     * @return R 错误信息
     * <AUTHOR>
     * @since 2025/4/25 下午3:47
     **/
    private Shifmanagement checkShift(String shiftNo, SecruityUser userInfo) {
        if (StrUtil.isBlank(shiftNo)) {
            return null;
        }
        Shifmanagement shift = new Shifmanagement();
        shift.setShiftId(shiftNo);
        shift.setPlatformCode(userInfo.getPlatformCode());
        shift.setDeleteFlag("N");
        shift.setReleaseStatus("1");
        List<Shifmanagement> shifList = shifmanagementMapper.selectShifmanagementListByLike(shift);
        if (CollUtil.isEmpty(shifList)) {
            return null;
        }
        shift = shifList.get(0);
        shift.setPlatformName(userInfo.getPlatformName());
        return shift;
    }
}
