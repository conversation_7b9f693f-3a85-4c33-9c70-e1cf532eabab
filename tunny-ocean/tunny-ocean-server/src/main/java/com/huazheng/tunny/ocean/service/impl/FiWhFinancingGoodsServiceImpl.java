package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FiWhFinancingGoodsMapper;
import com.huazheng.tunny.ocean.api.entity.FiWhFinancingGoods;
import com.huazheng.tunny.ocean.service.FiWhFinancingGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fiWhFinancingGoodsService")
public class FiWhFinancingGoodsServiceImpl extends ServiceImpl<FiWhFinancingGoodsMapper, FiWhFinancingGoods> implements FiWhFinancingGoodsService {

    @Autowired
    private FiWhFinancingGoodsMapper fiWhFinancingGoodsMapper;

    public FiWhFinancingGoodsMapper getFiWhFinancingGoodsMapper() {
        return fiWhFinancingGoodsMapper;
    }

    public void setFiWhFinancingGoodsMapper(FiWhFinancingGoodsMapper fiWhFinancingGoodsMapper) {
        this.fiWhFinancingGoodsMapper = fiWhFinancingGoodsMapper;
    }

    /**
     * 查询仓单质押融资货物表信息
     *
     * @param rowId 仓单质押融资货物表ID
     * @return 仓单质押融资货物表信息
     */
    @Override
    public FiWhFinancingGoods selectFiWhFinancingGoodsById(String rowId)
    {
        return fiWhFinancingGoodsMapper.selectFiWhFinancingGoodsById(rowId);
    }

    /**
     * 查询仓单质押融资货物表列表
     *
     * @param fiWhFinancingGoods 仓单质押融资货物表信息
     * @return 仓单质押融资货物表集合
     */
    @Override
    public List<FiWhFinancingGoods> selectFiWhFinancingGoodsList(FiWhFinancingGoods fiWhFinancingGoods)
    {
        return fiWhFinancingGoodsMapper.selectFiWhFinancingGoodsList(fiWhFinancingGoods);
    }


    /**
     * 分页模糊查询仓单质押融资货物表列表
     * @return 仓单质押融资货物表集合
     */
    @Override
    public Page selectFiWhFinancingGoodsListByLike(Query query)
    {
        FiWhFinancingGoods fiWhFinancingGoods =  BeanUtil.mapToBean(query.getCondition(), FiWhFinancingGoods.class,false);
        query.setRecords(fiWhFinancingGoodsMapper.selectFiWhFinancingGoodsListByLike(query,fiWhFinancingGoods));
        return query;
    }

    /**
     * 新增仓单质押融资货物表
     *
     * @param fiWhFinancingGoods 仓单质押融资货物表信息
     * @return 结果
     */
    @Override
    public int insertFiWhFinancingGoods(FiWhFinancingGoods fiWhFinancingGoods)
    {
        return fiWhFinancingGoodsMapper.insertFiWhFinancingGoods(fiWhFinancingGoods);
    }

    /**
     * 修改仓单质押融资货物表
     *
     * @param fiWhFinancingGoods 仓单质押融资货物表信息
     * @return 结果
     */
    @Override
    public int updateFiWhFinancingGoods(FiWhFinancingGoods fiWhFinancingGoods)
    {
        return fiWhFinancingGoodsMapper.updateFiWhFinancingGoods(fiWhFinancingGoods);
    }


    /**
     * 删除仓单质押融资货物表
     *
     * @param rowId 仓单质押融资货物表ID
     * @return 结果
     */
    @Override
    public int deleteFiWhFinancingGoodsById(String rowId)
    {
        return fiWhFinancingGoodsMapper.deleteFiWhFinancingGoodsById( rowId);
    }

    @Override
    public int deleteFiWhFinancingGoodsByFinancingCode(String financingCode)
    {
        return fiWhFinancingGoodsMapper.deleteFiWhFinancingGoodsByFinancingCode( financingCode);
    }


    /**
     * 批量删除仓单质押融资货物表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiWhFinancingGoodsByIds(Integer[] rowIds)
    {
        return fiWhFinancingGoodsMapper.deleteFiWhFinancingGoodsByIds( rowIds);
    }

}
