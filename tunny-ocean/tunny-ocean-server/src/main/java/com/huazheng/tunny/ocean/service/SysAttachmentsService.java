package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.SysAttachments;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 系统上传附件管理 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-04-25 12:24:49
 */
public interface SysAttachmentsService extends IService<SysAttachments> {
    /**
     * 查询系统上传附件管理信息
     *
     * @param rowId 系统上传附件管理ID
     * @return 系统上传附件管理信息
     */
    public SysAttachments selectSysAttachmentsById(String rowId);

    /**
     * 查询系统上传附件管理列表
     *
     * @param sysAttachments 系统上传附件管理信息
     * @return 系统上传附件管理集合
     */
    public List<SysAttachments> selectSysAttachmentsList(SysAttachments sysAttachments);


    /**
     * 分页模糊查询系统上传附件管理列表
     * @return 系统上传附件管理集合
     */
    public Page selectSysAttachmentsListByLike(Query query);



    /**
     * 新增系统上传附件管理
     *
     * @param sysAttachments 系统上传附件管理信息
     * @return 结果
     */
    public int insertSysAttachments(SysAttachments sysAttachments);

    /**
     * 修改系统上传附件管理
     *
     * @param sysAttachments 系统上传附件管理信息
     * @return 结果
     */
    public int updateSysAttachments(SysAttachments sysAttachments);

    /**
     * 删除系统上传附件管理
     *
     * @param rowId 系统上传附件管理ID
     * @return 结果
     */
    public int deleteSysAttachmentsById(String rowId);

    public int deleteSysAttachmentsByBusCode(String busCode);

    /**
     * 批量删除系统上传附件管理
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysAttachmentsByIds(Integer[] rowIds);

    public R saveFileName(SysAttachments sysAttachments);

}

