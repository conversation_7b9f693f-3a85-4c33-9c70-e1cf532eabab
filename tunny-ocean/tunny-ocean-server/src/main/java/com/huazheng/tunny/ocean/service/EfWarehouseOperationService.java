package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseOperation;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * e融同步仓单操作表 服务接口层
 *
 * <AUTHOR>
 * @date 2023-05-04 10:08:30
 */
public interface EfWarehouseOperationService extends IService<EfWarehouseOperation> {
    /**
     * 查询e融同步仓单操作表信息
     *
     * @param rowId e融同步仓单操作表ID
     * @return e融同步仓单操作表信息
     */
    public EfWarehouseOperation selectEfWarehouseOperationById(String rowId);

    /**
     * 查询e融同步仓单操作表列表
     *
     * @param efWarehouseOperation e融同步仓单操作表信息
     * @return e融同步仓单操作表集合
     */
    public List<EfWarehouseOperation> selectEfWarehouseOperationList(EfWarehouseOperation efWarehouseOperation);


    /**
     * 分页模糊查询e融同步仓单操作表列表
     * @return e融同步仓单操作表集合
     */
    public Page selectEfWarehouseOperationListByLike(Query query);



    /**
     * 新增e融同步仓单操作表
     *
     * @param efWarehouseOperation e融同步仓单操作表信息
     * @return 结果
     */
    public int insertEfWarehouseOperation(EfWarehouseOperation efWarehouseOperation);

    /**
     * 修改e融同步仓单操作表
     *
     * @param efWarehouseOperation e融同步仓单操作表信息
     * @return 结果
     */
    public int updateEfWarehouseOperation(EfWarehouseOperation efWarehouseOperation);

    /**
     * 删除e融同步仓单操作表
     *
     * @param rowId e融同步仓单操作表ID
     * @return 结果
     */
    public int deleteEfWarehouseOperationById(String rowId);

    /**
     * 批量删除e融同步仓单操作表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfWarehouseOperationByIds(Integer[] rowIds);

}

