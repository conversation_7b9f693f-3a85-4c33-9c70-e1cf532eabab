package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.EfFinancingPledgechangeinfo;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 解/补质押物 服务接口层
 *
 * <AUTHOR>
 * @date 2023-02-08 15:13:52
 */
public interface EfFinancingPledgechangeinfoService extends IService<EfFinancingPledgechangeinfo> {
    /**
     * 查询解/补质押物信息
     *
     * @param rowId 解/补质押物ID
     * @return 解/补质押物信息
     */
    public EfFinancingPledgechangeinfo selectEfFinancingPledgechangeinfoById(String rowId);

    /**
     * 查询解/补质押物列表
     *
     * @param efFinancingPledgechangeinfo 解/补质押物信息
     * @return 解/补质押物集合
     */
    public List<EfFinancingPledgechangeinfo> selectEfFinancingPledgechangeinfoList(EfFinancingPledgechangeinfo efFinancingPledgechangeinfo);


    /**
     * 分页模糊查询解/补质押物列表
     * @return 解/补质押物集合
     */
    public Page selectEfFinancingPledgechangeinfoListByLike(Query query);



    /**
     * 新增解/补质押物
     *
     * @param efFinancingPledgechangeinfo 解/补质押物信息
     * @return 结果
     */
    public String insertEfFinancingPledgechangeinfo(EfFinancingPledgechangeinfo efFinancingPledgechangeinfo);

    /**
     * 修改解/补质押物
     *
     * @param efFinancingPledgechangeinfo 解/补质押物信息
     * @return 结果
     */
    public R updateEfFinancingPledgechangeinfo(EfFinancingPledgechangeinfo efFinancingPledgechangeinfo);
    /**
     * 修改解/补质押物
     *
     * @param efFinancingPledgechangeinfo 解/补质押物信息
     * @return 结果
     */
    public R refuse(EfFinancingPledgechangeinfo efFinancingPledgechangeinfo);

    /**
     * 删除解/补质押物
     *
     * @param rowId 解/补质押物ID
     * @return 结果
     */
    public int deleteEfFinancingPledgechangeinfoById(String rowId);

    /**
     * 批量删除解/补质押物
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfFinancingPledgechangeinfoByIds(Integer[] rowIds);

}

