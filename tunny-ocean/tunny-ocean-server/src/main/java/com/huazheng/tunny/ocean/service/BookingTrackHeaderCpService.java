package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.BookingTrackHeaderCp;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 运踪信息主表(市平台-省) 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-08-12 18:13:55
 */
public interface BookingTrackHeaderCpService extends IService<BookingTrackHeaderCp> {
    /**
     * 查询运踪信息主表(市平台-省)信息
     *
     * @param rowId 运踪信息主表(市平台-省)ID
     * @return 运踪信息主表(市平台-省)信息
     */
    public BookingTrackHeaderCp selectBookingTrackHeaderCpById(String rowId);

    /**
     * 查询运踪信息主表(市平台-省)列表
     *
     * @param bookingTrackHeaderCp 运踪信息主表(市平台-省)信息
     * @return 运踪信息主表(市平台-省)集合
     */
    public List<BookingTrackHeaderCp> selectBookingTrackHeaderCpList(BookingTrackHeaderCp bookingTrackHeaderCp);


    /**
     * 分页模糊查询运踪信息主表(市平台-省)列表
     * @return 运踪信息主表(市平台-省)集合
     */
    public Page selectBookingTrackHeaderCpListByLike(Query query);



    /**
     * 新增运踪信息主表(市平台-省)
     *
     * @param bookingTrackHeaderCp 运踪信息主表(市平台-省)信息
     * @return 结果
     */
    public R insertBookingTrackHeaderCp(BookingTrackHeaderCp bookingTrackHeaderCp);

    /**
     * 修改运踪信息主表(市平台-省)
     *
     * @param bookingTrackHeaderCp 运踪信息主表(市平台-省)信息
     * @return 结果
     */
    public int updateBookingTrackHeaderCp(BookingTrackHeaderCp bookingTrackHeaderCp);

    /**
     * 删除运踪信息主表(市平台-省)
     *
     * @param bookingTrackHeaderCp 运踪信息主表(市平台-省)ID
     * @return 结果
     */
    public R deleteBookingTrackHeaderCp(BookingTrackHeaderCp bookingTrackHeaderCp);

    /**
     * 删除运踪信息主表(市平台-省)
     *
     * @param rowId 运踪信息主表(市平台-省)ID
     * @return 结果
     */
    public int deleteBookingTrackHeaderCpById(String rowId);

    /**
     * 批量删除运踪信息主表(市平台-省)
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteBookingTrackHeaderCpByIds(Integer[] rowIds);

}

