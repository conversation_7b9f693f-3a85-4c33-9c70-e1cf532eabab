package com.huazheng.tunny.ocean.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.vo.FdBillVO;
import com.huazheng.tunny.ocean.api.vo.FdCosdetailvo;
import com.huazheng.tunny.ocean.api.vo.IndexVO;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.FdCosdetailService;
import com.huazheng.tunny.ocean.service.FdTradingDetailsService;
import com.huazheng.tunny.ocean.service.SysNoConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service("fdCosdetailService")
public class FdCosdetailServiceImpl extends ServiceImpl<FdCosdetailMapper, FdCosdetail> implements FdCosdetailService {

    @Autowired
    private FdCosdetailMapper fdCosdetailMapper;
    @Autowired
    private SysNoConfigService sysNoConfigService;
    @Autowired
    private FdTradingDetailsService fdTradingDetailsService;
    @Autowired
    private FdBalanceDetailMapper fdBalanceDetailMapper;
    @Autowired
    private FdShippingAccoundetailMapper fdShippingAccoundetailMapper;
    @Autowired
    private WaybillContainerInfoMapper waybillContainerInfoMapper;
    @Autowired
    private WaybillGoodsInfoMapper waybillGoodsInfoMapper;
    @Autowired
    private WaybillParticipantsMapper waybillParticipantsMapper;
    @Value("${db.database}")
    private String database;

    /**
     * 查询运单费用明细表信息
     *
     */
    @Override
    public FdCosdetail selectFdCosdetailByuuId(FdCosdetail fdCosdetails)
    {
        return fdCosdetailMapper.selectFdCosdetailByuuId(fdCosdetails);
    }

    /**
     * 查询运单费用明细表列表
     *
     * @param fdCosdetail 运单费用明细表信息
     * @return 运单费用明细表集合
     */
    @Override
    public List<FdCosdetail> selectFdCosdetailList(FdCosdetail fdCosdetail)
    {
        fdCosdetail.setDelFlag("N");
        return fdCosdetailMapper.selectFdCosdetailList(fdCosdetail);
    }

    @Override
    public List<FdCosdetail> selectFdCosdetailListByGroup(FdCosdetail fdCosdetail)
    {
        return fdCosdetailMapper.selectFdCosdetailListByGroup(fdCosdetail);
    }


    /**
     * 分页模糊查询运单费用明细表列表
     * @return 运单费用明细表集合
     */
    @Override
    public Page selectFdCosdetailListByLike(Query query)
    {
        FdCosdetail fdCosdetail =  BeanUtil.mapToBean(query.getCondition(), FdCosdetail.class,false);
        query.setRecords(fdCosdetailMapper.selectFdCosdetailListByLike(query,fdCosdetail));
        return query;
    }

    @Override
    public Page costdetaillist(Query query) {
        if ("1".equals(query.getCondition().get("type"))) {
            FdCosdetail fdCosdetail = BeanUtil.mapToBean(query.getCondition(), FdCosdetail.class, false);
            /*int costdetaillistcount = fdCosdetailMapper.newcostdetaillistcount(fdCosdetail);
            query.setTotal(costdetaillistcount);*/
            fdCosdetail.setDelFlag("N");
            fdCosdetail.setDatabase(database);
            List costdetaillist = fdCosdetailMapper.newcostdetaillist(fdCosdetail);
            query.setRecords(costdetaillist);
            return query;
        }
        FdCosdetail fdCosdetail = BeanUtil.mapToBean(query.getCondition(), FdCosdetail.class, false);
        /*int costdetaillistcount = fdCosdetailMapper.costdetaillistcount(fdCosdetail);
        query.setTotal(costdetaillistcount);*/
        fdCosdetail.setDelFlag("N");
        fdCosdetail.setDatabase(database);
        List costdetaillist = fdCosdetailMapper.costdetaillist(fdCosdetail);
        query.setRecords(costdetaillist);
        return query;
    }

    @Override
    public List costdetaillistnew(FdCosdetail fdCosdetail) {
        fdCosdetail.setDelFlag("N");
//        fdCosdetail.setDatabase(database);
        List<FdCosdetailvo> costdetaillist = fdCosdetailMapper.costdetaillist3(fdCosdetail);
        if(CollUtil.isNotEmpty(costdetaillist)){
            List<String> list = new ArrayList<>();
            for (FdCosdetail cost:costdetaillist
                 ) {
                list.add(cost.getTransportOrderNumber());
            }
            if(CollUtil.isNotEmpty(list)){
                List<WaybillContainerInfo> list2 = waybillContainerInfoMapper.getList(list);
                if(CollUtil.isNotEmpty(list2)){
                    for (FdCosdetailvo fdCosdetail1:costdetaillist
                    ) {
                        for (WaybillContainerInfo wci:list2
                        ) {
                            if(wci.getWaybillNo().equals(fdCosdetail1.getTransportOrderNumber())&&wci.getContainerNo().equals(fdCosdetail1.getContainerNumber())){
                                fdCosdetail1.setContainerOwner(wci.getContainerOwner());
                                fdCosdetail1.setContainerTypeCode(wci.getContainerTypeCode());
                                fdCosdetail1.setContainerTypeName(wci.getContainerTypeName());
                                fdCosdetail1.setContainerType(wci.getContainerType());
                                fdCosdetail1.setDestinationName(wci.getStartStationName());
                                fdCosdetail1.setDestination(wci.getEndStationName());
                                fdCosdetail1.setIdentification(wci.getIdentification());
                                fdCosdetail1.setMonetaryType(wci.getMonetaryType());
                                fdCosdetail1.setPortStation(wci.getPortStation());
                            }
                        }
                    }
                }

                final List<FdShippingAccoundetail> list1 = fdShippingAccoundetailMapper.getList(list);
                if(CollUtil.isNotEmpty(list1)){
                    for (FdShippingAccoundetail fdShippingAccoundetail:list1
                    ) {
                        for (FdCosdetailvo fdCosdetail1:costdetaillist
                        ) {
                            if(fdShippingAccoundetail.getApplicationNumber().equals(fdCosdetail1.getApplicationNumber())
                                    &&fdShippingAccoundetail.getTransportOrderNumber().equals(fdCosdetail1.getTransportOrderNumber())
                                    &&fdShippingAccoundetail.getContainerNumber().equals(fdCosdetail1.getContainerNumber())){
                                if(StrUtil.isEmpty(fdShippingAccoundetail.getDestinationName())){
                                    fdShippingAccoundetail.setDestinationName(fdCosdetail1.getDestinationName());
                                }
                                if(StrUtil.isEmpty(fdShippingAccoundetail.getDestination())){
                                    fdShippingAccoundetail.setDestination(fdCosdetail1.getDestination());
                                }
                            }
                        }
                    }
                }

                List<WaybillGoodsInfo> list3 = waybillGoodsInfoMapper.getList(list);
                if(CollUtil.isNotEmpty(list3)){
                    for (FdCosdetailvo fdCosdetail1:costdetaillist
                    ) {
                        for (WaybillGoodsInfo wgi:list3
                        ) {
                            if(wgi.getWaybillNo().equals(fdCosdetail1.getTransportOrderNumber())&&wgi.getContainerNo().equals(fdCosdetail1.getContainerNumber())){
                                fdCosdetail1.setGoodsName(wgi.getGoodsChineseName());
                            }
                        }
                    }
                }

                List<WaybillParticipants> list4 = waybillParticipantsMapper.getList(database,list);
                if(CollUtil.isNotEmpty(list4)){
                    for (FdCosdetailvo fdCosdetail1:costdetaillist
                    ) {
                        for (WaybillParticipants wp:list4
                        ) {
                            if(wp.getWaybillNo().equals(fdCosdetail1.getTransportOrderNumber())&&wp.getContainerNo().equals(fdCosdetail1.getContainerNumber())){
                                fdCosdetail1.setConsignorName(wp.getConsignorName());
                                fdCosdetail1.setCountryname(wp.getResveredField01());
                            }
                        }
                    }
                }
            }

        }
        return costdetaillist;
    }

    @Override
    public Page selectShengListpage(Query query) {
        FdCosdetail fdCosdetail = BeanUtil.mapToBean(query.getCondition(), FdCosdetail.class, false);
        List list = fdCosdetailMapper.selectShengListpage(fdCosdetail);
        query.setRecords(list);
        return query;
    }

    @Override
    public List selectShengList(FdCosdetail fdCosdetail) {
        return fdCosdetailMapper.selectShengListpage(fdCosdetail);
    }

    @Override
    public List<FdCosdetail> costdetaillist2(FdCosdetail fdCosdetail){
        fdCosdetail.setDatabase(database);
        return fdCosdetailMapper.costdetaillist2(fdCosdetail);
    }
    /**
     * 新增运单费用明细表
     *
     * @param fdCosdetail 运单费用明细表信息
     * @return 结果
     */
    @Override
    public int insertFdCosdetail(FdCosdetail fdCosdetail)
    {
        return fdCosdetailMapper.insertFdCosdetail(fdCosdetail);
    }

    /**
     * 查询登录用户累计支付运费
     * @param indexVO
     * @return
     */
    @Override
    public String selectPayedAmount(IndexVO indexVO){
        return fdCosdetailMapper.selectPayedAmount(indexVO);
    }

    @Override
    public String selectPayedAmount2(IndexVO indexVO){
        return fdCosdetailMapper.selectPayedAmount(indexVO);
    }

    @Override
    public Integer selectNotPayedCount(IndexVO indexVO){
        return fdCosdetailMapper.selectNotPayedCount(indexVO);
    }
    /**
     * 修改运单费用明细表
     *
     * @param fdCosdetail 运单费用明细表信息
     * @return 结果
     */
    @Override
    public int updateFdCosdetail(FdCosdetail fdCosdetail)
    {
        return fdCosdetailMapper.updateFdCosdetail(fdCosdetail);
    }

    @Override
    public int deleteFdCosdetailById(Integer id) {
        return 0;
    }


    /**
     * 删除运单费用明细表
     * @return 结果
     */
    @Override
    public Boolean deleteFdCosdetailByuuId(FdCosdetail fdCosdetails)
    {
        int i = fdCosdetailMapper.deleteFdCosdetailByuuid(fdCosdetails);
        if (i == 0) {
            return false;
        }
        return true;
    }

    @Override
    public Boolean deleteFdCosdetailBybillcode(String billcode) {
        int i = fdCosdetailMapper.deleteFdCosdetailBybillcode(billcode);
        if (i == 0) {
            return false;
        }
        return true;

    }

    @Override
    public Integer updatebycostdetailids(HashMap<String, Object> map) {

        int i = fdCosdetailMapper.updatebycostdetailids(map);
        return i;
    }

    @Override
    public Integer updateinvoicebycostdetailids(HashMap<String, Object> map) {
        int i = fdCosdetailMapper.updateinvoicebycostdetailids(map);
        return i;
    }

    @Override
    public Integer updatebytransportOrderNumber(HashMap<String, Object> map) {
        int i = fdCosdetailMapper.updatebytransportOrderNumber(map);
        return i;
    }

    @Override
    public Boolean insertFdTradingDetailsbydeduction2(FdBillVO fdBillVO) {
        List<FdBalanceDetail> fdBalanceDetailList = fdBillVO.getFdBalanceDetailList();
        if(fdBalanceDetailList!=null && fdBalanceDetailList.size()>0){
            for (FdBalanceDetail fdBalanceDetail:fdBalanceDetailList
            ) {
                FdTradingDetails fdTradingDetails = new FdTradingDetails();

                String shipmentTime = fdBillVO.getShipmentTime();
                fdBillVO.setShipmentTime(null);
                BeanUtil.copyProperties(fdBillVO, fdTradingDetails);
                if(shipmentTime!=null &&!"".equals(shipmentTime)){
                    DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    LocalDateTime localDateTime = LocalDateTime.parse(shipmentTime, dtf);
                    fdTradingDetails.setShipmentTime(localDateTime);
                    if(shipmentTime.contains(" 00:00:00")){
                        String str = shipmentTime.replace(" 00:00:00", "");
                        fdTradingDetails.setShipmentTimeStr(str);
                    }
                }
                fdTradingDetails.setPlatformLevel(fdBillVO.getPlatformLevel());
                fdTradingDetails.setFromBillCode(fdBillVO.getBillCode());
                fdTradingDetails.setIsAdd("0");
                fdTradingDetails.setTradingStatus("2");
                fdTradingDetails.setCustomerCode(fdBillVO.getStandbyD());
                fdTradingDetails.setCustomerName(fdBillVO.getStandbyC());
                fdTradingDetails.setShiftId(fdBalanceDetail.getShiftNo());
                fdTradingDetails.setShiftName(fdBalanceDetail.getShiftName());
                fdTradingDetails.setBalanceId(fdBalanceDetail.getId());
                fdTradingDetails.setTradeSerialNumber(sysNoConfigService.genNo("FTD"));

                //todo: 添加锁定金额、剩余金额、可用金额的逻辑
                fdTradingDetails.setPaymentType(fdBalanceDetail.getPaymentType());
                fdTradingDetails.setProvinceShiftNo(fdBalanceDetail.getShiftId());
                fdTradingDetails.setFromBillCode(null);
                fdTradingDetails.setDeductionBillCode(fdBillVO.getBillCode());
                try {
                    fdTradingDetailsService.insertTrandingDetails(fdTradingDetails);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                //todo: 以下注释 优化为锁定金额、剩余金额、可用金额的逻辑
//                if(fdBalanceDetail.getRemainingAmount()!=null && fdBalanceDetail.getDeductionAmount()!=null){
//                    fdBalanceDetail.setRemainingAmount(fdBalanceDetail.getRemainingAmount().subtract(fdBalanceDetail.getDeductionAmount()));
//                    fdBalanceDetailMapper.updateRemainingAmountById(fdBalanceDetail);
//                }
            }
        }

        return true;
    }

    @Override
    public Boolean insertFdTradingDetailsbydeduction(FdBillVO fdBillVO) {
        FdTradingDetails fdTradingDetails = new FdTradingDetails();


        BeanUtils.copyProperties(fdBillVO, fdTradingDetails);

        if ("1".equals(fdBillVO.getIsAmateurbalance())) {//业务余额
            fdTradingDetails = getfdTradingDetailsbyfdBillVO(fdBillVO, fdTradingDetails);
            fdTradingDetails.setPaymentType("0");

            fdTradingDetails.setIsAdd("0");
            fdTradingDetails.setTradingStatus("1");
            fdTradingDetails.setCustomerCode(fdBillVO.getStandbyD());
            fdTradingDetails.setCustomerName(fdBillVO.getStandbyC());
            fdTradingDetails.setTransactionAmount(fdBillVO.getAmateurbalance().negate());
            fdTradingDetails.setFromBillCode(null);
            fdTradingDetails.setDeductionBillCode(fdBillVO.getBillCode());
            try {
                fdTradingDetailsService.insertTrandingDetails(fdTradingDetails);
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        if ("1".equals(fdBillVO.getIsBoundbalance())) {//量价捆绑余额
            fdTradingDetails = getfdTradingDetailsbyfdBillVO(fdBillVO, fdTradingDetails);
            fdTradingDetails.setPaymentType("1");

            fdTradingDetails.setIsAdd("0");
            fdTradingDetails.setTradingStatus("1");
            fdTradingDetails.setTransactionAmount(fdBillVO.getBoundbalance().negate());
            fdTradingDetails.setCustomerCode(fdBillVO.getStandbyD());
            fdTradingDetails.setCustomerName(fdBillVO.getStandbyC());
            fdTradingDetails.setFromBillCode(null);
            fdTradingDetails.setDeductionBillCode(fdBillVO.getBillCode());
            try {
                fdTradingDetailsService.insertTrandingDetails(fdTradingDetails);
            } catch (Exception e) {
                e.printStackTrace();
            }
//            fdTradingDetailsService.insert(fdTradingDetails);
        } else {
            return false;
        }
        return true;
    }

    @Override
    public List selectcontainerNumberbycostcode(String code) {
        return   fdCosdetailMapper.selectcontainerNumberbycostcode(code);

    }



    @Override
    public Boolean insertFdTradingDetailsbycitydeduction(FdBillVO fdBillVO) {
        // TODO: 2021/9/10 市平台目前设计是在这里循环fdBillVO里的那个maps

        FdTradingDetails fdTradingDetails = new FdTradingDetails();


        String shipmentTime = fdBillVO.getShipmentTime();
        fdBillVO.setShipmentTime(null);
        BeanUtil.copyProperties(fdBillVO, fdTradingDetails);
        if(shipmentTime!=null &&!"".equals(shipmentTime)){
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime localDateTime = LocalDateTime.parse(shipmentTime, dtf);
            fdTradingDetails.setShipmentTime(localDateTime);
            if(shipmentTime.contains(" 00:00:00")){
                String str = shipmentTime.replace(" 00:00:00", "");
                fdTradingDetails.setShipmentTimeStr(str);
            }
        }
        List<Map> maps = fdBillVO.getMaps();
        for (Map map : maps) {
// TODO: 2021/9/10 现在超鹏那个返回余额的接口只返回了有余额的类型    实际上没有余额也需要返回 在这个基础上(因为退款的时候用得上)

            // TODO: 2021/9/10 前台哪个使用了就给 use 这个字段一个1 没使用 不用给我   没使用的不需要生成记录
            if (!"1".equals(map.get("use"))) {
                continue;
            }
            fdTradingDetails = getfdTradingDetailsbyfdBillVO(fdBillVO, fdTradingDetails);
            // TODO: 2021/9/10   这里是余额或者量价捆绑  支付类型(0业务余额、1量价捆绑余额、2汇款)
            fdTradingDetails.setPaymentType(map.get("paymentType")+"");
            // TODO: 2021/9/10  这个字段是区分线路  前台传  这里我也不知道怎么定义的问王云杰
            fdTradingDetails.setBalanceType(map.get("balanceType")+"");


            fdTradingDetails.setIsAdd("0");
            fdTradingDetails.setTradingStatus("1");
            fdTradingDetails.setCustomerCode(fdBillVO.getStandbyD());
            fdTradingDetails.setCustomerName(fdBillVO.getStandbyC());
            //省平台结算这个字段传1
            fdTradingDetails.setSearchFlag("1");
            // TODO: 2021/9/10 这里是金额 需要前台定义字段暂定是这个
            fdTradingDetails.setTransactionAmount(BigDecimal.valueOf(Double.parseDouble(String.valueOf(map.get("transactionAmount")))));
            try {
                // TODO: 2021/9/10 这里是调用超鹏的接口修改余额和费用 和新增交易明细
                fdTradingDetailsService.insertTrandingDetails(fdTradingDetails);
            } catch (Exception e) {
                e.printStackTrace();
            }

        }

//        if ("1".equals(fdBillVO.getIsAmateurbalance())) {
//            fdTradingDetails = getfdTradingDetailsbyfdBillVO(fdBillVO, fdTradingDetails);
//            fdTradingDetails.setPaymentType("0");
//            fdTradingDetails.setBalanceType("1");
//            fdTradingDetails.setIsAdd("0");
//            fdTradingDetails.setTradingStatus("1");
//            fdTradingDetails.setCustomerCode(fdBillVO.getStandbyD());
//            fdTradingDetails.setCustomerName(fdBillVO.getStandbyC());
//            fdTradingDetails.setTransactionAmount(fdBillVO.getAmateurbalance());
//            try {
//                fdTradingDetailsService.insertTrandingDetails(fdTradingDetails);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//
//        } else if ("1".equals(fdBillVO.getIsBoundbalance())) {
//            fdTradingDetails = getfdTradingDetailsbyfdBillVO(fdBillVO, fdTradingDetails);
//            fdTradingDetails.setPaymentType("2");
//            fdTradingDetails.setBalanceType("2");
//            fdTradingDetails.setIsAdd("0");
//            fdTradingDetails.setTradingStatus("1");
//            fdTradingDetails.setTransactionAmount(fdBillVO.getBoundbalance());
//            fdTradingDetails.setCustomerCode(fdBillVO.getStandbyD());
//            fdTradingDetails.setCustomerName(fdBillVO.getStandbyC());
//            try {
//                fdTradingDetailsService.insertTrandingDetails(fdTradingDetails);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        } else {
//            return false;
//        }
        return true;
    }

    @Override
    public Boolean updateCosdetailbycontainernumber(List<String> list,String provincetrainsnumber,String trainnumber) {

            HashMap<Object, Object> map = new HashMap<>();
            //箱号
            map.put("list", list);
            //省级班列号
            map.put("provinceTrainsNumber", provincetrainsnumber);
            //下面查询的查询出来的是市平台和省平台的费用明细
            List<FdCosdetail> fdCosdetailList = fdCosdetailMapper.selectFdCosdetailListbycontainernumber(map);
            for (FdCosdetail fdCosdetail : fdCosdetailList) {
                fdCosdetail.setLocalCurrencyAmount(fdCosdetail.getLocalCurrencyAmount().negate());
                fdCosdetail.setOriginalCurrencyAmount(fdCosdetail.getOriginalCurrencyAmount().negate());
                fdCosdetail.setUuid(UUID.randomUUID().toString());
            }

            map.remove("provinceTrainsNumber");
            map.put("trainNumber", trainnumber);
            //下面查询的查询出来的是市平台和订舱客户和费用明细
            List<FdCosdetail> fdCosdetails = fdCosdetailMapper.selectFdCosdetailListbycontainernumber(map);

            for (FdCosdetail fdCosdetail : fdCosdetails) {
                fdCosdetail.setLocalCurrencyAmount(fdCosdetail.getLocalCurrencyAmount().negate());
                fdCosdetail.setOriginalCurrencyAmount(fdCosdetail.getOriginalCurrencyAmount().negate());
                fdCosdetail.setUuid(UUID.randomUUID().toString());
            }
        if (fdCosdetailList.size() != 0) {
            insertFdCosdetailList(fdCosdetailList);
        }
        if (fdCosdetails.size() != 0) {
            insertFdCosdetailList(fdCosdetails);
        }

        return true;
    }

    @Override
    public Page amountpage(Query<Object> query) {

        FdCosdetail fdCosdetail = BeanUtil.mapToBean(query.getCondition(), FdCosdetail.class, false);
        /*int costdetaillistcount = fdCosdetailMapper.amountpagecount(fdCosdetail);
        query.setTotal(costdetaillistcount);*/
        fdCosdetail.setDatabase(database);
        List costdetaillist = fdCosdetailMapper.amountpage(query, fdCosdetail);
        query.setRecords(costdetaillist);
        return query;
    }

    private FdTradingDetails getfdTradingDetailsbyfdBillVO(FdBillVO fdBillVO, FdTradingDetails fdTradingDetails) {
        fdTradingDetails.setTradeSerialNumber(sysNoConfigService.genNo("FTD"));
        fdTradingDetails.setCustomerCode(fdBillVO.getSettlementUserCode());
        fdTradingDetails.setCustomerName(fdBillVO.getSettlementUserName());

        return fdTradingDetails;
    }

    ;


    /**
     * 批量删除运单费用明细表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFdCosdetailByIds(Integer[] ids)
    {
        return fdCosdetailMapper.deleteFdCosdetailByIds( ids);
    }

    @Override
    public List<FdCosdetail> selectFdCosdetailListByIdList(FdCosdetail fdCosdetail){
        return fdCosdetailMapper.selectFdCosdetailListByIdList(fdCosdetail);
    }

    @Override
    public int updateCostDetailByCostCode(FdCosdetail fdCosdetails) {
        return fdCosdetailMapper.updateCostDetailByCostCode(fdCosdetails);
    }

    @Override
    public int insertFdCosdetailList(List<FdCosdetail> fdCosdetails) {
        return fdCosdetailMapper.insertFdCosdetailList(fdCosdetails);
    }

    /**
     * 省平台编辑 （重新删除重新增加）
     * @param fdCosdetails
     * @return
     */
    @Override
    public int updateFdCosdetalSheng(List<FdCosdetail> fdCosdetails) {
        FdCosdetail fdCosdetail=new FdCosdetail();
        fdCosdetail.setCostCode(fdCosdetails.get(0).getCostCode());
        fdCosdetail.setDelUsercode(SecurityUtils.getUserInfo().getUserName());
        fdCosdetail.setDelUserrealname(SecurityUtils.getUserInfo().getRealName());
        fdCosdetailMapper.updateFdCosdetailByCostCode(fdCosdetail);
        return fdCosdetailMapper.insertFdCosdetailList(fdCosdetails);
    }

    @Override
    public FdCosdetail selectFdCosdetailByCostCodeAndXh(FdCosdetail fdCosdetail) {
        return fdCosdetailMapper.selectFdCosdetailByCostCodeAndXh(fdCosdetail);
    }

    @Override
    public List selectConstDeatilByCostCode(String code) {
        return fdCosdetailMapper.selectConstDeatilByCostCode(code);
    }

    @Override
    public List<FdCosdetailvo> costdetaillistYf(FdCosdetail fdCosdetail) {
        fdCosdetail.setDatabase(database);
        return fdCosdetailMapper.costdetaillist(fdCosdetail);
    }

    @Override
    public int updateFdCosdetalInvoice(List<FdCosdetail> fdCosdetails) {
        return fdCosdetailMapper.updateFdCosdetalInvoice(fdCosdetails);
    }

    @Override
    public int updateFdCosdetailExchangeRate(List<FdCosdetail> fdCosdetails) {
        return fdCosdetailMapper.updateFdCosdetailExchangeRate(fdCosdetails);
    }

    @Override
    public String selectProvinceTrainsNumberByUuid(FdCosdetail fdCosdetail) {
        return fdCosdetailMapper.selectProvinceTrainsNumberByUuid(fdCosdetail);
    }

    @Override
    public int updateFdCosdetailByCostCode(FdCosdetail fdCosdetail) {
        return fdCosdetailMapper.updateFdCosdetailByCostCode(fdCosdetail);
    }

    @Override
    public int deleteFdCosdetailByContainerNo(FdCosdetail fdCosdetail) {
        return fdCosdetailMapper.deleteFdCosdetailByContainerNo(fdCosdetail);
    }
}
