package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.OperationLog;
import com.huazheng.tunny.ocean.api.entity.SysLog;
import com.huazheng.tunny.ocean.mapper.OperationLogMapper;
import com.huazheng.tunny.ocean.mapper.SysLogMapper;
import com.huazheng.tunny.ocean.service.OperationLogService;
import com.huazheng.tunny.ocean.service.SysLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("operationLogService")
public class OperationLogServiceImpl extends ServiceImpl<OperationLogMapper, OperationLog> implements OperationLogService {

    @Autowired
    private OperationLogMapper operationLogMapper;

    public OperationLogMapper getOperationLogMapper() {
        return operationLogMapper;
    }

    public void setOperationLogMapper(OperationLogMapper operationLogMapper) {
        this.operationLogMapper = operationLogMapper;
    }

    /**
     * 新增日志表
     *
     * @param operationLog 日志表信息
     * @return 结果
     */
    @Override
    public int insertOperationLog(OperationLog operationLog)
    {

        return operationLogMapper.insertOperationLog(operationLog);
    }


}
