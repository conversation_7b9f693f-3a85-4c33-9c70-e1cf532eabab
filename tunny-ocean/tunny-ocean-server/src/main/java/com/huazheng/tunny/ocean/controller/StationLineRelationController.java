package com.huazheng.tunny.ocean.controller;


import com.huazheng.tunny.ocean.api.entity.StationLineRelation;
import com.huazheng.tunny.ocean.service.StationLineRelationService;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 站点线路管理
 *
 * <AUTHOR> code ocean
 * @date 2021-07-08 19:21:41
 */
@RestController
@RequestMapping("/stationlinerelation")
@Slf4j
public class StationLineRelationController {
    @Autowired
    private StationLineRelationService stationLineRelationService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //分页模糊查询
        return stationLineRelationService.selectStationLineRelationListByLike(new Query<>(params));
    }


    /**
     * 根据站点编码查询当前站点所在的线路List
     * @param stationCode
     * @return R
     */
    @GetMapping("/{stationCode}")
    public R info(@PathVariable("stationCode") String stationCode) {
        List<StationLineRelation> list =stationLineRelationService.selectStationLineRelationByStationCode(stationCode);
        return new R<>(list);
    }


    /**
     * 删除详情页中站点关联（传入站点编码、线路编码）
     * @param stationLineRelation
     * @return R
     */
    @PutMapping("cancelStationShip")
    public R cancelStationShip(@RequestBody StationLineRelation stationLineRelation) {
        stationLineRelationService.updateStationLineRelation(stationLineRelation);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 保存
     * @param stationLineRelation
     * @return R
     */
    @PostMapping
    public R save(@RequestBody StationLineRelation stationLineRelation) {
        stationLineRelationService.insert(stationLineRelation);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param stationLineRelation
     * @return R
     */
    @PutMapping
    public R update(@RequestBody StationLineRelation stationLineRelation) {
        stationLineRelationService.updateById(stationLineRelation);
        return new R<>(Boolean.TRUE);
    }



    /**
     * 删除
     * @param rowId
     * @return R
     */
    @DeleteMapping("/{rowId}")
    public R delete(@PathVariable  String rowId) {
        stationLineRelationService.deleteById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        stationLineRelationService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<StationLineRelation> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = stationLineRelationService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<StationLineRelation> list = reader.readAll(StationLineRelation.class);
        stationLineRelationService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }
}
