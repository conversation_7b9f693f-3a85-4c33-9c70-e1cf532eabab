package com.huazheng.tunny.ocean.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.GoodsDTO;
import com.huazheng.tunny.ocean.api.dto.RequesdetailDTO;
import com.huazheng.tunny.ocean.api.dto.RequesheaderDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.feign.RemoteAdminService;
import com.huazheng.tunny.ocean.api.vo.SysDictVo;
import com.huazheng.tunny.ocean.mapper.SpaceOccupyMapper;
import com.huazheng.tunny.ocean.mapper.WaybillHeaderMapper;
import com.huazheng.tunny.ocean.service.*;
import com.huazheng.tunny.ocean.service.impl.SpaceOccupyServiceImpl;
import com.huazheng.tunny.ocean.util.CheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 订舱申请单主表
 *
 * <AUTHOR> code ocean
 * @date 2021-07-02 14:38:40
 */
@RestController
@RequestMapping("/bookingrequesheader")
@Slf4j
public class BookingRequesheaderController {
    @Autowired
    private BookingRequesheaderService bookingRequesheaderService;
    @Autowired
    private SysNoConfigService sysNoConfigService;
    @Autowired
    private AudiopinionService audiopinionService;
    @Autowired
    private WaybillHeaderService waybillHeaderService;
    @Autowired
    private WaybillContainerInfoService waybillContainerInfoService;
    @Autowired
    private BookingRequesdetailService bookingRequesdetailService;
    @Autowired
    private SpaceOccupyServiceImpl spaceOccupyService;
    @Autowired
    private SpaceOccupyMapper spaceOccupyMapper;
    @Autowired
    private WaybillHeaderMapper waybillHeaderMapper;
    @Autowired
    private SignatureController signatureController;
    @Autowired
    private OperationLogService operationLogService;
    @Autowired
    private CustomerInfoService customerInfoService;
    @Autowired
    private ContainerTypeDataService containerTypeDataService;
    @Autowired
    private StationManagementService stationManagementService;
    @Autowired
    private RemoteAdminService remoteAdminService;


    /**
     * 下载模板
     *
     * @param response
     * @throws IOException
     */
    @PostMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("箱信息");
        //字体
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");

        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);//水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setBorderBottom(BorderStyle.THIN); //下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);
        style.setWrapText(true);

        XSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setFont(font);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);//水平居中
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        titleStyle.setBorderBottom(BorderStyle.THIN); //下边框
        titleStyle.setBorderLeft(BorderStyle.THIN);//左边框
        titleStyle.setBorderTop(BorderStyle.THIN);//上边框
        titleStyle.setBorderRight(BorderStyle.THIN);//右边框
        titleStyle.setBottomBorderColor(HSSFColor.BLACK.index);
        titleStyle.setLeftBorderColor(HSSFColor.BLACK.index);
        titleStyle.setRightBorderColor(HSSFColor.BLACK.index);
        titleStyle.setTopBorderColor(HSSFColor.BLACK.index);
        titleStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        //宽度
        for (int i = 0; i <= 35; i++) {
            sheet.setColumnWidth(i, 60 * 80);
        }
//        row1.setHeight((short) (10*50));

        //第一行
        XSSFRow row0 = sheet.createRow(0);
        XSSFCell cell00 = row0.createCell(0);
        cell00.setCellValue("主要信息");
        cell00.setCellStyle(titleStyle);
        for (int i = 1; i <= 13; i++) {
            XSSFCell cell = row0.createCell(i);
            cell.setCellValue("");
            cell.setCellStyle(titleStyle);
        }

        //第二行
        XSSFRow row1 = sheet.createRow(1);

        XSSFCell cell10 = row1.createCell(0);
        cell10.setCellValue("订舱客户编码*");
        cell10.setCellStyle(style);
        XSSFCell cell11 = row1.createCell(1);
        cell11.setCellValue("");
        cell11.setCellStyle(style);
        XSSFCell cell12 = row1.createCell(2);
        cell12.setCellValue("订舱客户名称");
        cell12.setCellStyle(style);
        XSSFCell cell13 = row1.createCell(3);
        cell13.setCellValue("");
        cell13.setCellStyle(style);
        XSSFCell cell14 = row1.createCell(4);
        cell14.setCellValue("班次号*");
        cell14.setCellStyle(style);
        XSSFCell cell15 = row1.createCell(5);
        cell15.setCellValue("");
        cell15.setCellStyle(style);
        XSSFCell cell16 = row1.createCell(6);
        cell16.setCellValue("境外代理");
        cell16.setCellStyle(style);
        XSSFCell cell17 = row1.createCell(7);
        cell17.setCellValue("");
        cell17.setCellStyle(style);
        XSSFCell cell18 = row1.createCell(8);
        cell18.setCellValue("备注信息");
        cell18.setCellStyle(style);
        XSSFCell cell19 = row1.createCell(9);
        cell19.setCellValue("");
        cell19.setCellStyle(style);
        XSSFCell cell110 = row1.createCell(10);
        cell110.setCellValue("境外汇率");
        cell110.setCellStyle(style);
        XSSFCell cell111 = row1.createCell(11);
        cell111.setCellValue("");
        cell111.setCellStyle(style);
        XSSFCell cell112 = row1.createCell(12);
        cell112.setCellValue("境外币种");
        cell112.setCellStyle(style);
        XSSFCell cell113 = row1.createCell(13);
        cell113.setCellValue("");
        cell113.setCellStyle(style);

        //第三行
        XSSFRow row2 = sheet.createRow(2);

        XSSFCell cell20 = row2.createCell(0);
        cell20.setCellValue("订舱信息");
        cell20.setCellStyle(titleStyle);
        XSSFCell cell223 = row2.createCell(13);
        cell223.setCellValue("台账信息");
        cell223.setCellStyle(titleStyle);
        for (int i = 1; i <= 29; i++) {
            if (i != 14) {
                XSSFCell cell = row2.createCell(i);
                cell.setCellValue("");
                cell.setCellStyle(titleStyle);
            }
        }

        //第四行
        XSSFRow row3 = sheet.createRow(3);

        XSSFCell cell30 = row3.createCell(0);
        cell30.setCellValue("箱号*");
        cell30.setCellStyle(style);

        XSSFCell cell31 = row3.createCell(1);
        cell31.setCellValue("类型(进口/出\r\n口/过境)*");
        cell31.setCellStyle(style);

        XSSFCell cell32 = row3.createCell(2);
        cell32.setCellValue("箱属(自备箱\r\n/中铁箱)*");
        cell32.setCellStyle(style);

        XSSFCell cell33 = row3.createCell(3);
        cell33.setCellValue("箱型代码*");
        cell33.setCellStyle(style);

        XSSFCell cell38 = row3.createCell(4);
        cell38.setCellValue("发站站编*");
        cell38.setCellStyle(style);

        XSSFCell cell39 = row3.createCell(5);
        cell39.setCellValue("到站站编*");
        cell39.setCellStyle(style);

        XSSFCell cell319 = row3.createCell(6);
        cell319.setCellValue("发货人*");
        cell319.setCellStyle(style);

        XSSFCell cell320 = row3.createCell(7);
        cell320.setCellValue("发货人国\r\n家编码*");
        cell320.setCellStyle(style);

        XSSFCell cell321 = row3.createCell(8);
        cell321.setCellValue("收货人*");
        cell321.setCellStyle(style);

        XSSFCell cell322 = row3.createCell(9);
        cell322.setCellValue("收货人国\r\n家编码*");
        cell322.setCellStyle(style);

        XSSFCell cell316 = row3.createCell(10);
        cell316.setCellValue("境外到达城市*");
        cell316.setCellStyle(style);

        XSSFCell cell310 = row3.createCell(11);
        cell310.setCellValue("国境口岸站编*");
        cell310.setCellStyle(style);

        XSSFCell cell311 = row3.createCell(12);
        cell311.setCellValue("口岸代理名称*");
        cell311.setCellStyle(style);

        XSSFCell cell333 = row3.createCell(13);
        cell333.setCellValue("箱重*");
        cell333.setCellStyle(style);

        XSSFCell cell325 = row3.createCell(14);
        cell325.setCellValue("是否全程\r\n(是/否)*");
        cell325.setCellStyle(style);

        XSSFCell cell326 = row3.createCell(15);
        cell326.setCellValue("有色金属\r\n(是/否)*");
        cell326.setCellStyle(style);

        XSSFCell cell312 = row3.createCell(16);
        cell312.setCellValue("货主*");
        cell312.setCellStyle(style);

        XSSFCell cell314 = row3.createCell(17);
        cell314.setCellValue("境内货源地/目的地");
        cell314.setCellStyle(style);

        XSSFCell cell327 = row3.createCell(18);
        cell327.setCellValue("报关单号");
        cell327.setCellStyle(style);

        XSSFCell cell328 = row3.createCell(19);
        cell328.setCellValue("货值(美金)");
        cell328.setCellStyle(style);

        XSSFCell cell329 = row3.createCell(20);
        cell329.setCellValue("海关封");
        cell329.setCellStyle(style);

        XSSFCell cell330 = row3.createCell(21);
        cell330.setCellValue("车号");
        cell330.setCellStyle(style);

        XSSFCell cell331 = row3.createCell(22);
        cell331.setCellValue("订单需求号");
        cell331.setCellStyle(style);

        XSSFCell cell332 = row3.createCell(23);
        cell332.setCellValue("国联订单号");
        cell332.setCellStyle(style);

        XSSFCell cell323 = row3.createCell(24);
        cell323.setCellValue("境内费用(CNY)*");
        cell323.setCellStyle(style);

        XSSFCell cell324 = row3.createCell(25);
        cell324.setCellValue("境外费用(CNY)*");
        cell324.setCellStyle(style);

        XSSFCell cell3338 = row3.createCell(26);
        cell3338.setCellValue("补贴标准");
        cell3338.setCellStyle(style);

        XSSFCell cell334 = row3.createCell(27);
        cell334.setCellValue("补贴金额");
        cell334.setCellStyle(style);

        XSSFCell cell335 = row3.createCell(28);
        cell335.setCellValue("箱备注");
        cell335.setCellStyle(style);



        /*XSSFCell cell313 = row3.createCell(13);
        cell313.setCellValue("封号");
        cell313.setCellStyle(style);

        XSSFCell cell315 = row3.createCell(15);
        cell315.setCellValue("口岸代理*");
        cell315.setCellStyle(style);


        XSSFCell cell317 = row3.createCell(17);
        cell317.setCellValue("托运货物价值*");
        cell317.setCellStyle(style);

        XSSFCell cell318 = row3.createCell(18);
        cell318.setCellValue("货物金额\r\n类型代码*");
        cell318.setCellStyle(style);

        XSSFCell cell34 = row3.createCell(4);
        cell34.setCellValue("集装箱自重*");
        cell34.setCellStyle(style);

        XSSFCell cell35 = row3.createCell(5);
        cell35.setCellValue("集装箱毛重*");
        cell35.setCellStyle(style);

        XSSFCell cell36 = row3.createCell(6);
        cell36.setCellValue("集装箱净重*");
        cell36.setCellStyle(style);

        XSSFCell cell37 = row3.createCell(7);
        cell37.setCellValue("局属*");
        cell37.setCellStyle(style);*/

        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 13));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 13));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 14, 29));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。

        //固定表头   前3列前2行：(3, 2, 3, 2)
        sheet.createFreezePane(1, 4, 1, 4);

        XSSFSheet sheet2 = workbook.createSheet("货物信息");
        //宽度
        for (int i = 0; i <= 22; i++) {
            sheet2.setColumnWidth(i, 60 * 80);
        }

        //第一行
        XSSFRow srow0 = sheet2.createRow(0);
        XSSFCell scell00 = srow0.createCell(0);
        scell00.setCellValue("货物信息");
        scell00.setCellStyle(titleStyle);
        for (int i = 1; i <= 6; i++) {
            XSSFCell cell = srow0.createCell(i);
            cell.setCellValue("");
            cell.setCellStyle(titleStyle);
        }

        //第二行
        XSSFRow srow1 = sheet2.createRow(1);
        XSSFCell scell10 = srow1.createCell(0);
        scell10.setCellValue("箱号*");
        scell10.setCellStyle(style);
        XSSFCell scell11 = srow1.createCell(1);
        scell11.setCellValue("货物编码*");
        scell11.setCellStyle(style);
        XSSFCell scell12 = srow1.createCell(2);
        scell12.setCellValue("货物中文名称*");
        scell12.setCellStyle(style);
        XSSFCell scell13 = srow1.createCell(3);
        scell13.setCellValue("包装种类*");
        scell13.setCellStyle(style);
        XSSFCell scell14 = srow1.createCell(4);
        scell14.setCellValue("件数*");
        scell14.setCellStyle(style);
        XSSFCell scell15 = srow1.createCell(5);
        scell15.setCellValue("货物重量*");
        scell15.setCellStyle(style);
        XSSFCell scell16 = srow1.createCell(6);
        scell16.setCellValue("托运货物价值*");
        scell16.setCellStyle(style);

        sheet2.addMergedRegion(new CellRangeAddress(0, 0, 0, 6));//合并单元格，指定 4 个参数，起始行，结束行，起始列，结束列。然后这个区域将被合并。

        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        String fileName = "市平台导入订舱数据模板.xlsx";
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
        response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();

    }

    /**
     * 导入EXCEL
     */
    @PostMapping("/importedForCity")
    @Transactional(rollbackFor = Exception.class)
    public R importedForCity(@RequestParam("file") MultipartFile file, @RequestParam("platformCode") String platformCode) throws Exception {

        Workbook workbook = null;
        try {
            InputStream is = file.getInputStream();
            //利用WorkbookFactory获取Sheet，无需判断版本
            workbook = WorkbookFactory.create(is);
        } catch (Exception e) {
            e.printStackTrace();
        }
        RequesheaderDTO header = new RequesheaderDTO();

        //导入第一张sheet页
        Sheet sheet = workbook.getSheetAt(0);
        Row row1 = sheet.getRow(1);
        if (row1.getCell(1) != null) {
            row1.getCell(1).setCellType(CellType.STRING);
            String bookingCustcode = row1.getCell(1).getStringCellValue();

            CustomerInfo sel = new CustomerInfo();
            sel.setCustomerCode(bookingCustcode);
            sel.setDeleteFlag("N");
            CustomerInfo customerInfo = customerInfoService.selectCustomegenNorInfoByCustomerCode(sel);
            if (customerInfo != null) {
                header.setBookingCustcode(bookingCustcode);
                header.setBookingCustname(customerInfo.getCompanyName());
            } else {
                return new R(500, Boolean.FALSE, null, "该订舱客户不存在：" + bookingCustcode);
            }
        } else {
            return new R(500, Boolean.FALSE, null, "订舱客户编码不能为空！");
        }

        if (row1.getCell(5) != null) {
            row1.getCell(5).setCellType(CellType.STRING);
            String shiftNo = row1.getCell(5).getStringCellValue();
            header.setShiftNo(shiftNo);
        } else {
            return new R(500, Boolean.FALSE, null, "班次号不能为空！");
        }

        if (row1.getCell(7) != null) {
            row1.getCell(7).setCellType(CellType.STRING);
            String resveredField06 = row1.getCell(7).getStringCellValue();
            header.setResveredField06(resveredField06);
        }

        if (row1.getCell(9) != null) {
            row1.getCell(9).setCellType(CellType.STRING);
            String remarks = row1.getCell(9).getStringCellValue();
            header.setRemarks(remarks);
        }

        if (row1.getCell(11) != null) {
            row1.getCell(11).setCellType(CellType.NUMERIC);
            BigDecimal exchangeRate = BigDecimal.valueOf(row1.getCell(11).getNumericCellValue());
            header.setExchangeRate(exchangeRate);
        } else {
            return new R(500, Boolean.FALSE, null, "境外汇率不能为空！");
        }

        if (row1.getCell(13) != null) {
            row1.getCell(13).setCellType(CellType.STRING);
            String monetaryType = row1.getCell(13).getStringCellValue();
            header.setMonetaryType(monetaryType);
        } else {
            return new R(500, Boolean.FALSE, null, "境外币种不能为空！");
        }

        ContainerTypeData sel2 = new ContainerTypeData();
        sel2.setDeleteFlag("N");
        List<ContainerTypeData> containerTypeData = containerTypeDataService.selectContainerTypeDataList(sel2);

        StationManagement stationManagement = new StationManagement();
        stationManagement.setIsPort("1");
        stationManagement.setDeleteFlag("N");
        List<StationManagement> portStations = stationManagementService.selectStationManagementList(stationManagement);

        StationManagement stationManagement2 = new StationManagement();
        stationManagement2.setIsPort("0");
        stationManagement2.setDeleteFlag("N");
        List<StationManagement> startStation = stationManagementService.selectStationManagementList(stationManagement2);

        String data2 = remoteAdminService.selectDictByType2("country_type");
        List<SysDictVo> countryList = JSONUtil.toList(JSONUtil.parseArray(data2), SysDictVo.class);

        List<RequesdetailDTO> details = new ArrayList<>();
        //获取行数
        int lastRowNum = sheet.getLastRowNum();
        for (int i = 4; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            boolean blankFlag = isRowEmpty(row);
            if (blankFlag == true) {
                continue;
            }

            RequesdetailDTO dto = new RequesdetailDTO();

            if (row.getCell(0) != null) {
                row.getCell(0).setCellType(CellType.STRING);
                String containerNo = row.getCell(0).getStringCellValue();
                containerNo = containerNo.trim();
                boolean b = CheckUtil.verifyCntrCode(containerNo);
                if (b) {
                    dto.setContainerNo(containerNo);
                } else {
                    return new R(500, Boolean.FALSE, null, "箱号格式错误：" + containerNo);
                }
            } else {
                return new R(500, Boolean.FALSE, null, "箱号不能为空！");
            }

            if (row.getCell(1) != null) {
                row.getCell(1).setCellType(CellType.STRING);
                String identification = row.getCell(1).getStringCellValue();
                if ("出口".equals(identification)) {
                    dto.setIdentification("E");
                } else if ("进口".equals(identification)) {
                    dto.setIdentification("I");
                } else if ("过境".equals(identification)) {
                    dto.setIdentification("P");
                } else {
                    return new R(500, Boolean.FALSE, null, "进出口类型格式错误！");
                }
            } else {
                return new R(500, Boolean.FALSE, null, "进出口类型不能为空！");
            }

            if (row.getCell(2) != null) {
                row.getCell(2).setCellType(CellType.STRING);
                String box = row.getCell(2).getStringCellValue();
                if ("自备箱".equals(box)) {
                    dto.setBox("0");
                } else if ("中铁箱".equals(box)) {
                    dto.setBox("1");
                } else {
                    return new R(500, Boolean.FALSE, null, "箱属格式错误！");
                }
            } else {
                return new R(500, Boolean.FALSE, null, "箱属不能为空！");
            }

            if (row.getCell(3) != null) {
                row.getCell(3).setCellType(CellType.STRING);
                String containerType = row.getCell(3).getStringCellValue();

                Boolean flag = true;
                for (ContainerTypeData data : containerTypeData
                ) {
                    if (data.getContainerTypeCode().equals(containerType)) {
                        dto.setContainerTypeCode(data.getContainerTypeCode());
                        dto.setContainerTypeName(data.getContainerTypeName());
                        dto.setContainerType(data.getContainerTypeSize());
                        flag = false;
                        break;
                    }
                }

                if (flag) {
                    return new R(500, Boolean.FALSE, null, "未查询到该箱型代码：" + containerType);
                }
            } else {
                return new R(500, Boolean.FALSE, null, "箱型代码不能为空！");
            }

            if (row.getCell(4) != null) {
                row.getCell(4).setCellType(CellType.STRING);
                String stationCompilation = row.getCell(4).getStringCellValue();
                Boolean flag = true;
                for (StationManagement station : startStation
                ) {
                    if (station.getStationCode().equals(stationCompilation)) {
                        dto.setStationCompilation(stationCompilation);
                        dto.setStartStationName(station.getStationName());
                        flag = false;
                        break;
                    }
                }
                if (flag) {
                    return new R(500, Boolean.FALSE, null, "未查询到该发站站编：" + stationCompilation);
                }
            } else {
                return new R(500, Boolean.FALSE, null, "发站站编不能为空！");
            }

            if (row.getCell(5) != null) {
                row.getCell(5).setCellType(CellType.STRING);
                String endCompilation = row.getCell(5).getStringCellValue();
                Boolean flag = true;
                for (StationManagement station : startStation
                ) {
                    if (station.getStationCode().equals(endCompilation)) {
                        dto.setEndCompilation(endCompilation);
                        dto.setEndStationName(station.getStationName());
                        dto.setDestinationCountry(station.getNation());
                        flag = false;
                        break;
                    }
                }
                if (flag) {
                    return new R(500, Boolean.FALSE, null, "未查询到该到站站编：" + endCompilation);
                }
            } else {
                return new R(500, Boolean.FALSE, null, "到站站编不能为空！");
            }

            if (row.getCell(6) != null) {
                row.getCell(6).setCellType(CellType.STRING);
                String consignorName = row.getCell(6).getStringCellValue();
                dto.setConsignorName(consignorName);
            } else {
                return new R(500, Boolean.FALSE, null, "发货人不能为空！");
            }

            if (row.getCell(7) != null) {
                row.getCell(7).setCellType(CellType.STRING);
                String consignorCountryCode = row.getCell(7).getStringCellValue();
                Boolean flag = true;
                for (SysDictVo sysDictVo : countryList
                ) {
                    if (sysDictVo.getCode().equals(consignorCountryCode)) {
                        dto.setConsignorCountryCode(consignorCountryCode);
                        flag = false;
                        break;
                    }
                }
                if (flag) {
                    return new R(500, Boolean.FALSE, null, "未查询到该发货人国家编码：" + consignorCountryCode);
                }
            } else {
                return new R(500, Boolean.FALSE, null, "发货人国家编码不能为空！");
            }

            if (row.getCell(8) != null) {
                row.getCell(8).setCellType(CellType.STRING);
                String consigneeName = row.getCell(8).getStringCellValue();
                dto.setConsigneeName(consigneeName);
            } else {
                return new R(500, Boolean.FALSE, null, "收货人不能为空！");
            }

            if (row.getCell(9) != null) {
                row.getCell(9).setCellType(CellType.STRING);
                String consigneeCountryCode = row.getCell(9).getStringCellValue();
                Boolean flag = true;
                for (SysDictVo sysDictVo : countryList
                ) {
                    if (sysDictVo.getCode().equals(consigneeCountryCode)) {
                        dto.setConsigneeCountryCode(consigneeCountryCode);
                        flag = false;
                        break;
                    }
                }
                if (flag) {
                    return new R(500, Boolean.FALSE, null, "未查询到该收货人国家编码：" + consigneeCountryCode);
                }
            } else {
                return new R(500, Boolean.FALSE, null, "收货人国家编码不能为空！");
            }

            if (row.getCell(10) != null) {
                row.getCell(10).setCellType(CellType.STRING);
                String abroadReachCity = row.getCell(10).getStringCellValue();
                dto.setAbroadReachCity(abroadReachCity);
            } else {
                return new R(500, Boolean.FALSE, null, "境外到达城市不能为空！");
            }

            if (row.getCell(11) != null) {
                row.getCell(11).setCellType(CellType.STRING);
                String frontierPortstation = row.getCell(11).getStringCellValue();
                dto.setFrontierPortstation(frontierPortstation);
            } else {
                return new R(500, Boolean.FALSE, null, "国境口岸站编不能为空！");
            }

            if (row.getCell(12) != null) {
                row.getCell(12).setCellType(CellType.STRING);
                String portAgent = row.getCell(12).getStringCellValue();
                dto.setPortAgent(portAgent);
            }
            /*else{
                return new R(500,Boolean.FALSE,null,"口岸代理不能为空！");
            }*/

            if (row.getCell(13) != null) {
                Cell cell = row.getCell(13);
                if (cell.getCellTypeEnum() == CellType.NUMERIC) {
                    double value = row.getCell(13).getNumericCellValue();
                    dto.setDeadWeight(Float.valueOf(value + ""));
                } else {
                    cell.setCellType(CellType.STRING);
                    String value = cell.getStringCellValue();
                    dto.setDeadWeight(Float.valueOf(value));
                }
            } else {
                return new R(500, Boolean.FALSE, null, "箱重不能为空！");
            }

            if (row.getCell(14) != null) {
                row.getCell(14).setCellType(CellType.STRING);
                String isFull = row.getCell(14).getStringCellValue();
                if ("是".equals(isFull)) {
                    dto.setIsFull("1");
                } else if ("否".equals(isFull)) {
                    dto.setIsFull("0");
                } else {
                    return new R(500, Boolean.FALSE, null, "是否全程格式错误！");
                }
            } else {
                return new R(500, Boolean.FALSE, null, "是否全程不能为空！");
            }

            if (row.getCell(15) != null) {
                row.getCell(15).setCellType(CellType.STRING);
                String nonFerrous = row.getCell(15).getStringCellValue();
                if ("是".equals(nonFerrous)) {
                    dto.setNonFerrous("1");
                } else if ("否".equals(nonFerrous)) {
                    dto.setNonFerrous("0");
                } else {
                    return new R(500, Boolean.FALSE, null, "有色金属格式错误！");
                }
            } else {
                return new R(500, Boolean.FALSE, null, "有色金属不能为空！");
            }

            if (row.getCell(16) != null) {
                row.getCell(16).setCellType(CellType.STRING);
                String goodsOwner = row.getCell(16).getStringCellValue();
                dto.setGoodsOwner(goodsOwner);
            } else {
                return new R(500, Boolean.FALSE, null, "货主不能为空！");
            }

            if (row.getCell(17) != null) {
                row.getCell(17).setCellType(CellType.STRING);
                String goodsOrigin = row.getCell(17).getStringCellValue();
                dto.setGoodsOrigin(goodsOrigin);
            } else {
                return new R(500, Boolean.FALSE, null, "境内货源地/目的地不能为空！");
            }

            if (row.getCell(18) != null) {
                row.getCell(18).setCellType(CellType.STRING);
                String clearanceNumber = row.getCell(18).getStringCellValue();
                dto.setClearanceNumber(clearanceNumber);
            }
            /*else{
                return new R(500,Boolean.FALSE,null,"报关单号不能为空！");
            }*/

            if (row.getCell(19) != null) {
                row.getCell(19).setCellType(CellType.NUMERIC);
                double value = row.getCell(19).getNumericCellValue();
                dto.setValueUsd(BigDecimal.valueOf(value));
            }
            /*else{
                return new R(500,Boolean.FALSE,null,"货值(美金)不能为空！");
            }*/

            if (row.getCell(20) != null) {
                row.getCell(20).setCellType(CellType.STRING);
                String customsSeal = row.getCell(20).getStringCellValue();
                dto.setCustomsSeal(customsSeal);
            }
            /*else{
                return new R(500,Boolean.FALSE,null,"海关封不能为空！");
            }*/

            if (row.getCell(21) != null) {
                row.getCell(21).setCellType(CellType.STRING);
                String trainNumber = row.getCell(21).getStringCellValue();
                dto.setTrainNumber(trainNumber);
            }
            /*else{
                return new R(500,Boolean.FALSE,null,"车号不能为空！");
            }*/

            if (row.getCell(22) != null) {
                row.getCell(22).setCellType(CellType.STRING);
                String waybillDemandNumber = row.getCell(22).getStringCellValue();
                dto.setWaybillDemandNumber(waybillDemandNumber);
            }
            /*else{
                return new R(500,Boolean.FALSE,null,"运单需求号不能为空！");
            }*/

            if (row.getCell(23) != null) {
                row.getCell(23).setCellType(CellType.STRING);
                String waybillLnNumber = row.getCell(23).getStringCellValue();
                dto.setWaybillLnNumber(waybillLnNumber);
            }
            /*else{
                return new R(500,Boolean.FALSE,null,"国联运单号不能为空！");
            }*/

            if (row.getCell(24) != null) {
                row.getCell(24).setCellType(CellType.NUMERIC);
                double value = row.getCell(24).getNumericCellValue();
                dto.setDomesticFreight(BigDecimal.valueOf(value));

            } else {
                return new R(500, Boolean.FALSE, null, "境内运费不能为空！");
            }

            if (row.getCell(25) != null) {
                row.getCell(25).setCellType(CellType.NUMERIC);
                double value = row.getCell(25).getNumericCellValue();
                if (header.getExchangeRate().compareTo(BigDecimal.ZERO) == 0) {
                    dto.setOverseasFreightOc(BigDecimal.valueOf(0));
                } else {
                    dto.setOverseasFreightOc(BigDecimal.valueOf(value).divide(header.getExchangeRate(), 2, BigDecimal.ROUND_HALF_UP));
                }

                dto.setOverseasFreightCny(BigDecimal.valueOf(value));

            } else {
                return new R(500, Boolean.FALSE, null, "境外运费不能为空！");
            }

            if (row.getCell(26) != null) {
                row.getCell(26).setCellType(CellType.STRING);
                String subsidyStandards = row.getCell(26).getStringCellValue();
                dto.setSubsidyStandards(subsidyStandards);
            }
            /*else{
                return new R(500,Boolean.FALSE,null,"补贴标准不能为空！");
            }*/

            if (row.getCell(27) != null) {
                row.getCell(27).setCellType(CellType.NUMERIC);
                double value = row.getCell(27).getNumericCellValue();
                dto.setSubsidyAmount(BigDecimal.valueOf(value));

            }
            /*else{
                return new R(500,Boolean.FALSE,null,"补贴金额不能为空！");
            }*/

            if (row.getCell(28) != null) {
                row.getCell(28).setCellType(CellType.STRING);
                String remarks = row.getCell(28).getStringCellValue();
                dto.setRemarks(remarks);
            }
            /*else{
                return new R(500,Boolean.FALSE,null,"箱备注不能为空！");
            }*/

            details.add(dto);

        }

        List<GoodsDTO> goods = new ArrayList<>();
        //导入第2张sheet页
        Sheet sheet2 = workbook.getSheetAt(1);
        //获取行数
        int lastRowNum2 = sheet2.getLastRowNum();
        for (int i = 2; i <= lastRowNum2; i++) {
            Row row = sheet2.getRow(i);
            boolean blankFlag = isRowEmpty(row);
            if (blankFlag == true) {
                continue;
            }
            GoodsDTO dto = new GoodsDTO();

            if (row.getCell(0) != null) {
                row.getCell(0).setCellType(CellType.STRING);
                String containerNo = row.getCell(0).getStringCellValue();
                containerNo = containerNo.trim();
                boolean b = CheckUtil.verifyCntrCode(containerNo);
                if (b) {
                    dto.setContainerNo(containerNo);
                } else {
                    return new R(500, Boolean.FALSE, null, "货物数据中箱号格式错误：" + containerNo);
                }
            } else {
                return new R(500, Boolean.FALSE, null, "货物数据中箱号不能为空！");
            }

            if (row.getCell(1) != null) {
                row.getCell(1).setCellType(CellType.STRING);
                String goodsCode = row.getCell(1).getStringCellValue();
                dto.setGoodsCode(goodsCode);
            } else {
                return new R(500, Boolean.FALSE, null, "货物编码不能为空！");
            }

            if (row.getCell(2) != null) {
                row.getCell(2).setCellType(CellType.STRING);
                String goodsChineseName = row.getCell(2).getStringCellValue();
                dto.setGoodsChineseName(goodsChineseName);
            } else {
                return new R(500, Boolean.FALSE, null, "货物中文名称不能为空！");
            }

            if (row.getCell(3) != null) {
                row.getCell(3).setCellType(CellType.STRING);
                String packageType = row.getCell(3).getStringCellValue();
                dto.setPackageType(packageType);
            } else {
                return new R(500, Boolean.FALSE, null, "包装种类不能为空！");
            }

            if (row.getCell(4) != null) {
                row.getCell(4).setCellType(CellType.STRING);
                String goodsNums = row.getCell(4).getStringCellValue();
                dto.setGoodsNums(goodsNums);
            } else {
                return new R(500, Boolean.FALSE, null, "件数不能为空！");
            }

            if (row.getCell(5) != null) {
                row.getCell(5).setCellType(CellType.NUMERIC);
                double value = row.getCell(5).getNumericCellValue();
                dto.setGoodsWeight(Float.valueOf(value + ""));

            } else {
                return new R(500, Boolean.FALSE, null, "货物重量不能为空！");
            }

            if (row.getCell(6) != null) {
                row.getCell(6).setCellType(CellType.NUMERIC);
                double value = row.getCell(6).getNumericCellValue();
                dto.setGoodsValue(Float.valueOf(value + ""));

            } else {
                return new R(500, Boolean.FALSE, null, "托运货物价值不能为空！");
            }

            goods.add(dto);
        }

        if (CollUtil.isNotEmpty(details) && CollUtil.isNotEmpty(goods)) {
            for (RequesdetailDTO requesdetailDTO : details
            ) {
                List<GoodsDTO> goodsDTOList = new ArrayList<>();
                for (GoodsDTO goodsDTO : goods
                ) {
                    if (requesdetailDTO.getContainerNo().equals(goodsDTO.getContainerNo())) {
                        goodsDTOList.add(goodsDTO);
                    }
                }
                requesdetailDTO.setGoods(goodsDTOList);
            }
        }
        header.setDetails(details);

        return bookingRequesheaderService.saveBookingAndWaybillForCity(header, platformCode);
    }

    @PostMapping("/saveBookingAndWaybill")
    public String saveBookingAndWaybill(@RequestBody JSONObject jsonObject) {
        String data = null;
        String content2 = null;
        String content = null;
        try {
            data = signatureController.getPost(jsonObject);
            content2 = String.valueOf(jsonObject.get("content"));

            OperationLog log4 = new OperationLog();
            log4.setUuid(UUID.randomUUID().toString());
            log4.setProcessType("保存上合代舱");
            log4.setOperationCode("sh");
            log4.setOperationName("上合");
            log4.setOperationTime(new Date());
            log4.setOperationOpinion(content2 + data);
            operationLogService.insertOperationLog(log4);

            content = bookingRequesheaderService.saveBookingAndWaybill(data);
        } catch (Exception e) {
            System.out.println(e.toString());
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE, "系统错误：" + e), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPostSh2("/bookingrequesheader/saveBookingAndWaybill", content);
        return result;
    }

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //分页模糊查询
        return bookingRequesheaderService.selectBookingRequesheaderListByLike(new Query<>(params));
    }


    /**
     * 查询订单详情信息
     *
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        BookingRequesheader bookingRequesheader = bookingRequesheaderService.selectBookingRequesheaderById(rowId);
        return new R<>(bookingRequesheader);
    }

    /**
     * 保存
     *
     * @param bookingRequesheader
     * @return R
     */
    @PostMapping
    public R save(@RequestBody BookingRequesheader bookingRequesheader) {
        return bookingRequesheaderService.insertBookingRequesheader(bookingRequesheader);
    }

    @PostMapping("/saveForSh")
    public String saveForSh(@RequestBody JSONObject jsonObject) {
        String data = null;
        String content2 = null;
        String content = null;
        try {
            data = signatureController.getPost(jsonObject);
            content2 = String.valueOf(jsonObject.get("content"));

            OperationLog log4 = new OperationLog();
            log4.setUuid(UUID.randomUUID().toString());
            log4.setProcessType("保存/修改上合订舱");
            log4.setOperationCode("sh");
            log4.setOperationName("上合");
            log4.setOperationTime(new Date());
            log4.setOperationOpinion(content2 + data);
            operationLogService.insertOperationLog(log4);

            content = bookingRequesheaderService.saveForSh(data);
        } catch (Exception e) {
            System.out.println(e.toString());
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE, "系统错误：" + e), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPostSh2("/bookingrequesheader/saveForSh", content);
        return result;
    }

    /**
     * 查询审核状态
     *
     * @param jsonObject
     * @return R
     */
    @PostMapping("/queryAuditForSh")
    public String queryAuditForSh(@RequestBody JSONObject jsonObject) {
        String data = null;
        String content = null;
        try {
            data = signatureController.getPost(jsonObject);

            content = bookingRequesheaderService.queryAuditForSh(data);
        } catch (Exception e) {
            System.out.println(e.toString());
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE, "系统错误：" + e), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPostSh2("/bookingrequesheader/queryAuditForSh", content);
        return result;
    }

    /**
     * 删除订舱申请表及子表信息
     *
     * @param bookingRequesheader
     * @return R
     */
    @PutMapping
    public R update(@RequestBody BookingRequesheader bookingRequesheader) {
        return bookingRequesheaderService.updateBookingRequesheader(bookingRequesheader);
    }

    /**
     * 提交信息
     *
     * @param orderNo
     * @return R
     */
    @PutMapping("/commitStatus")
    public R commitStatus(String orderNo) {
        return bookingRequesheaderService.commitStatus(orderNo);
    }


    /**
     * 删除
     *
     * @param rowId
     * @return R
     */
    @DeleteMapping("/{rowId}")
    public R delete(@PathVariable String rowId) {
        bookingRequesheaderService.deleteById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     *
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody List<String> rowIds) {
        bookingRequesheaderService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 导出EXCEL
     *
     * @return
     */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<BookingRequesheader> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = bookingRequesheaderService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }


    /**
     * 导入EXCEL
     * <p>
     *
     * @param titleStr
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<BookingRequesheader> list = reader.readAll(BookingRequesheader.class);
        bookingRequesheaderService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }

    //订舱申请审核  选中某条数据后，根据申请单找到某条数据，生成运单号，更改状态等，审核信息表等、通过后更新运单表信息
    @PostMapping("/bookingAudit")
    @Transactional(rollbackFor = Exception.class)
    public R bookingAudit(@RequestBody BookingRequesheader bookingRequesheader) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        BookingRequesheader bookingRequesheader1 = new BookingRequesheader();
        bookingRequesheader1.setDeleteFlag("N");
        bookingRequesheader1.setOrderNo(bookingRequesheader.getOrderNo());
        List<BookingRequesheader> bookingRequesheaders = bookingRequesheaderService.selectBookingRequesheaderList(bookingRequesheader1);
        if (bookingRequesheaders.isEmpty()) {
            return new R<>(500, false, "未找到该条数据信息，请重新确认");
        }
        if ("0".equals(bookingRequesheader.getAuditType())) {
            BookingRequesheader booking = new BookingRequesheader();
            booking.setAuditType("0");
            booking.setDocumentStatus("3");//未通过
            booking.setOrderNo(bookingRequesheader.getOrderNo());
            booking.setAuditOpinion(bookingRequesheader.getAuditOpinion());
            booking.setUpdateWho(userInfo.getUserName());
            booking.setUpdateWhoName(userInfo.getRealName());
            booking.setUpdateTime(new Date());
            bookingRequesheaderService.updateBookingRequesheader1(booking);

            Audiopinion audiopinion = new Audiopinion();
            audiopinion.setRowId(UUID.randomUUID().toString());
            audiopinion.setStatus("0");
            audiopinion.setOrderNo(bookingRequesheader.getOrderNo());
            audiopinion.setAuditNo(userInfo.getUserName());
            audiopinion.setAuditOpinion("DCSH");
            audiopinion.setAuditTime(LocalDateTime.now());
            audiopinion.setDeleteFlag("N");
            audiopinion.setAddWho(userInfo.getUserName());
            audiopinion.setAddWhoName(userInfo.getRealName());
            audiopinion.setAddTime(new Date());
            audiopinion.setResveredField01(bookingRequesheader.getCityPlatform());
            audiopinion.setResveredField02(bookingRequesheader.getBookingCustcode());
            audiopinion.setResveredField03(bookingRequesheader.getResveredField03());
            audiopinionService.insertAudiopinion(audiopinion);
        } else {
            /*添加占用舱位逻辑：addTime:2021/08/11 **************************start********************************************************************
             * 以上业务员审核驳回校验略过，若选择审核通过，后台须校验，首先用户提报需要使用的舱位数量能否满足现在当前班次剩余舱位数量，若不满足，则提示业务员重新选择驳回；
             * 校验通过后，对舱位占用表以班次纬度维护数据，保存数据为剩余舱位数量（实际+备用）；
             * 若存在备用仓，向申请单主表传递“是否备用舱位”；
             * */
            Float spaceNums = bookingRequesheader.getSpaceNums();//上报舱位数
            SpaceOccupy sp = new SpaceOccupy();
            sp.setShiftNo(bookingRequesheader.getShiftNo());
            Float occupyNums = spaceOccupyMapper.selectOccupyNums(sp);
            if (occupyNums == null || occupyNums < 0.5) {
                occupyNums = (float) 0;
            }
            Float usedSpNumsUndShf = occupyNums;//该班次已用舱位数量

            Float trueSpaceNums = bookingRequesheader.getResveredField04();//班次发布实际舱位
            Float trueSpaceNumsSp = bookingRequesheader.getResveredField05(); //班次发布备用舱位
            Float sumSpace = trueSpaceNums + trueSpaceNumsSp;//该班次的总舱位数（实际+备用）
            Float spaceLeft = sumSpace - usedSpNumsUndShf;//总仓位-已用=剩余舱位数 （实际+备用）
            String flag = "0";     //判断是否使用备用仓标识0不使用1使用
            if (spaceNums > spaceLeft) {//上报舱位数若大于剩余舱位数,返回提示信息
                return new R<>(500, false, "剩余舱位数量不足，请重新确认");
            } else {
                //剩余舱位数满足本次提交，则更新舱位占用表，剩余舱位（实际+备用）数据
                SpaceOccupy so = new SpaceOccupy();
                so.setSpaceNums(String.valueOf(spaceNums));//当前班次已用舱位数
                so.setOrderNo(bookingRequesheader.getOrderNo());
                so.setShiftNo(bookingRequesheader.getShiftNo());
                so.setAddTime(LocalDateTime.now());
                so.setAddWhoName(userInfo.getRealName());
                so.setAddWho(userInfo.getUserName());
                spaceOccupyService.insertSpaceOccupy(so);
                //判断是否使用备用仓(已占用+本次提交)>班次发布实际舱位数，即为使用备用仓(1)，否则为未使用(0)
                flag = (usedSpNumsUndShf + spaceNums) > trueSpaceNums ? "1" : "0";
            }
            /*添加逻辑end   addTime:2021/08/11
             * end*******************************************end****************************************************************************
             * */
            String account = sysNoConfigService.genNo("YD");
            BookingRequesheader booking = new BookingRequesheader();
            booking.setAuditType("1");
            booking.setDocumentStatus("2");
            booking.setOrderNo(bookingRequesheader.getOrderNo());
            booking.setWaybillNo(account);
            booking.setAuditOpinion(bookingRequesheader.getAuditOpinion());
            booking.setUpdateWho(userInfo.getUserName());
            booking.setUpdateWhoName(userInfo.getRealName());
            booking.setUpdateTime(new Date());
            bookingRequesheaderService.updateBookingRequesheader1(booking);

            CustomerInfo customerInfo = new CustomerInfo();
            customerInfo.setDeleteFlag("N");
            customerInfo.setCustomerCode(bookingRequesheader.getBookingCustcode());

            //将生成的账号、密码同步到SysUser表中
//            SysUser sysUser = new SysUser();
//            String userNo = sysNoConfigService.genNo("UC");
//            Random rand = new Random();
//            int randNum = rand.nextInt(1000);
//            Integer integer = Integer.valueOf(randNum);
//            sysUser.setUserId(integer);
//            sysUser.setUsername(userNo);
//            sysUser.setPassword(SecureUtil.md5("123456"));
//            sysUser.setDelFlag("0");
//            sysUser.setUserFlag("0");
//            sysUser.setCreateTime(new Date());
//            sysUser.setEmpno(bookingRequesheader.getBookingCustcode());
//            List<CustomerInfo> customerInfos = customerInfoService.selectCustomerInfoList(customerInfo);
//            if (customerInfos != null || customerInfos.size() > 0) {
//                sysUser.setUserRealname(customerInfos.get(0).getCompanyName());
//            }
//            bookingRequesheaderService.insertUser(sysUser);

            Audiopinion audiopinion = new Audiopinion();
            audiopinion.setRowId(UUID.randomUUID().toString());
            audiopinion.setStatus("1");
            audiopinion.setOrderNo(bookingRequesheader.getOrderNo());
            audiopinion.setWaybillNo(account);
            audiopinion.setAuditNo(userInfo.getUserName());
            audiopinion.setAuditOpinion("DCSH");
            audiopinion.setAuditTime(LocalDateTime.now());
            audiopinion.setDeleteFlag("N");
            audiopinion.setAddWho(userInfo.getUserName());
            audiopinion.setAddWhoName(userInfo.getRealName());
            audiopinion.setAddTime(new Date());
            audiopinion.setResveredField01(bookingRequesheader.getResveredField02());
            audiopinion.setResveredField02(bookingRequesheader.getBookingCustcode());
            audiopinion.setResveredField03(bookingRequesheader.getResveredField03());
            audiopinionService.insertAudiopinion(audiopinion);

            //插入运单主、子表信息
            WaybillHeader waybillHeader = new WaybillHeader();
            waybillHeader.setRowId(UUID.randomUUID().toString());
            waybillHeader.setOrderNo(bookingRequesheader.getOrderNo());
            waybillHeader.setWaybillNo(account);
            waybillHeader.setCustomerNo(bookingRequesheader.getBookingCustcode());
            waybillHeader.setCustomerName(bookingRequesheader.getBookingCustname());
            waybillHeader.setPlatformCode(bookingRequesheader.getResveredField02());
            waybillHeader.setPlatformName(bookingRequesheader.getCityPlatform());
            waybillHeader.setTrainType(bookingRequesheader.getTrainType());
            waybillHeader.setIdentification(bookingRequesheader.getIdentification());
            waybillHeader.setIsCustomTransit(bookingRequesheader.getTransit());
            waybillHeader.setShiftNo(bookingRequesheader.getShiftNo());
            waybillHeader.setShippingTime(bookingRequesheader.getDeliveryTime());
            waybillHeader.setShippingLine(bookingRequesheader.getShippingLine());
            //发运线路编码
            waybillHeader.setResveredField05(bookingRequesheader.getShippingLineCode());
            //发运线路编码
            waybillHeader.setResveredField06(bookingRequesheader.getResveredField06());
            waybillHeader.setTrainName(bookingRequesheader.getTrainName());
            //市平台名称
            waybillHeader.setResveredField02(bookingRequesheader.getResveredField02());
            waybillHeader.setAuditFlag("DCSH");
            waybillHeader.setTotalCases(bookingRequesheader.getTotalCases());
            waybillHeader.setBillStatus("0");
            waybillHeader.setAddWho(bookingRequesheader.getAddWho());
            waybillHeader.setAddWhoName(bookingRequesheader.getAddWhoName());
            waybillHeader.setAddTime(new Date());
            waybillHeader.setTrip(bookingRequesheader.getTrip());
            waybillHeader.setIsTransit(bookingRequesheader.getTransit());
            //审核意见
            waybillHeader.setResveredField01(bookingRequesheader.getResveredField03());
            //20210811新增逻辑 是否使用备用仓标识（0不使用1使用）
            waybillHeader.setResveredField03(flag);
            //20210811新增逻辑 占用舱数
            waybillHeader.setResveredField04(String.valueOf(spaceNums));
            waybillHeaderService.insertWaybillHeader(waybillHeader);

            BookingRequesdetail bookingRequesdetail = new BookingRequesdetail();
            bookingRequesdetail.setOrderNo(bookingRequesheader.getOrderNo());
            bookingRequesdetail.setDeleteFlag("N");
            List<BookingRequesdetail> bookingRequesdetails = bookingRequesdetailService.selectBookingRequesdetailList(bookingRequesdetail);
            if (bookingRequesdetails.isEmpty()) {
                return new R<>(500, false, "该申请单未有对应的详情信息");
            } else {
                for (BookingRequesdetail details : bookingRequesdetails) {
                    String cn = details.getContainerNo();
                    WaybillHeader wh = new WaybillHeader();
                    wh.setShiftNo(bookingRequesheader.getShiftNo());
                    wh.setResveredField05(cn);
                    List<WaybillHeader> list = waybillHeaderMapper.selectCnExistByShiftNo(wh);
                    if (list == null || list.size() == 0) {
                    } else {
                        return new R(500, Boolean.FALSE, null, cn + ",该箱号已被申报");
                    }
                }
                for (BookingRequesdetail requesdetail : bookingRequesdetails) {
                    WaybillContainerInfo waybillContainerInfo = new WaybillContainerInfo();
                    waybillContainerInfo.setRowId(UUID.randomUUID().toString());
                    waybillContainerInfo.setOrderNo(requesdetail.getOrderNo());
                    waybillContainerInfo.setWaybillNo(account);
                    waybillContainerInfo.setIdentification(requesdetail.getResveredField01());
                    waybillContainerInfo.setDestinationName(requesdetail.getDestinationName());
                    waybillContainerInfo.setBureau(requesdetail.getBureauSubordinate());
                    waybillContainerInfo.setDestination(requesdetail.getDestination());
                    waybillContainerInfo.setPortStation(requesdetail.getPortStation());
                    waybillContainerInfo.setContainerNo(requesdetail.getContainerNo());
                    waybillContainerInfo.setContainerOwner(requesdetail.getBox());

                    waybillContainerInfo.setContainerTypeCode(requesdetail.getContainerTypeCode());
                    waybillContainerInfo.setContainerTypeName(requesdetail.getContainerTypeName());
                    waybillContainerInfo.setContainerType(requesdetail.getContainerType());

                    waybillContainerInfo.setContainerDeadWeight(requesdetail.getDeadWeight());
                    waybillContainerInfo.setContainerGrossWeight(requesdetail.getGrossWeight());
                    waybillContainerInfo.setContainerNetWeight(requesdetail.getNetWeight());
                    waybillContainerInfo.setAddWho(userInfo.getUserName());
                    waybillContainerInfo.setAddWhoName(userInfo.getRealName());
                    waybillContainerInfo.setAddTime(new Date());
                    waybillContainerInfo.setDomesticFreight(BigDecimal.valueOf(0.00));
                    waybillContainerInfo.setOverseasFreight(BigDecimal.valueOf(0.00));
                    waybillContainerInfo.setGoodsName(requesdetail.getGoodsName());
                    waybillContainerInfoService.insertWaybillContainerInfo(waybillContainerInfo);
                }
            }
        }

        //插入操作日志
        OperationLog log = new OperationLog();
        log.setUuid(UUID.randomUUID().toString());
        log.setProcessCode(bookingRequesheader.getOrderNo());
        log.setProcessType("市-申请单-审核");
        log.setOperationCode(userInfo.getUserName());
        log.setOperationName(userInfo.getRealName());
        log.setOperationTime(new Date());
        String s = "";
        if (bookingRequesheader.getAuditType() != null && !"".equals(bookingRequesheader.getAuditType())) {
            if ("1".equals(bookingRequesheader.getAuditType())) {
                log.setOperationResult("同意");
                s = "审核成功";
            } else if ("0".equals(bookingRequesheader.getAuditType())) {
                log.setOperationResult("驳回");
                s = "驳回成功";
            }
        }
        log.setOperationOpinion(bookingRequesheader.getResveredField03());
        log.setCorInterface("/bookingrequesheader/bookingAudit");
        log.setDeleteFlag("N");
        operationLogService.insertOperationLog(log);

        return new R<>(200, Boolean.TRUE, s);
    }


    /**
     * 换箱申请
     *
     * @param bookingRequesheader
     * @return R
     */
    @PostMapping("/changeBox")
    public R changeBox(@RequestBody BookingRequesheader bookingRequesheader) {

        return bookingRequesheaderService.insertBookingRequesheader(bookingRequesheader);
    }

    /**
     * 功能描述: 判断是否为空行
     *
     * @param row 行对象
     * @return boolean
     * <AUTHOR> zheng
     * @date 2021/10/13
     */
    private boolean isRowEmpty(Row row) {
        if (row != null) {
            for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
                Cell cell = row.getCell(c);
                if (cell != null) {
                    cell.setCellType(CellType.STRING);
                }
                if (cell != null && !"".equals(cell.getStringCellValue())) {
                    return false;//不是空行
                }
            }
        }
        return true;//是空行
    }

    /**
     * 删除申请单&&运单&&业务流程单
     *
     * @param bookingRequesheader
     * @return R
     */
    @PostMapping("/deleteAll")
    public R deleteAll(@RequestBody BookingRequesheader bookingRequesheader) {
        return bookingRequesheaderService.deleteAll(bookingRequesheader);
    }
}
