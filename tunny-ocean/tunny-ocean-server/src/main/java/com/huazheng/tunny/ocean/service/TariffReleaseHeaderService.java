package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.TariffReleaseHeader;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 运价发布主表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-08 11:24:00
 */
public interface TariffReleaseHeaderService extends IService<TariffReleaseHeader> {
    /**
     * 查询运价发布主表信息
     *
     * @param rowId 运价发布主表ID
     * @return 运价发布主表信息
     */
    public TariffReleaseHeader selectTariffReleaseHeaderById(String rowId);

    /**
     * 查询运价发布主表列表
     *
     * @param tariffReleaseHeader 运价发布主表信息
     * @return 运价发布主表集合
     */
    public List<TariffReleaseHeader> selectTariffReleaseHeaderList(TariffReleaseHeader tariffReleaseHeader);


    /**
     * 分页模糊查询运价发布主表列表
     * @return 运价发布主表集合
     */
    public Page selectTariffReleaseHeaderListByLike(Query query);



    /**
     * 新增运价发布主表
     *
     * @param tariffReleaseHeader 运价发布主表信息
     * @return 结果
     */
    public int insertTariffReleaseHeader(TariffReleaseHeader tariffReleaseHeader);

    /**
     * 修改运价发布主表
     *
     * @param tariffReleaseHeader 运价发布主表信息
     * @return 结果
     */
    public int updateTariffReleaseHeader(TariffReleaseHeader tariffReleaseHeader);

    public int updateTariffReleaseHeaderByNo(TariffReleaseHeader tariffReleaseHeader);

    /**
     * 删除运价发布主表
     *
     * @param tariffReleaseHeader 运价发布主表ID
     * @return 结果
     */
    public int deleteTariffReleaseHeaderById(TariffReleaseHeader tariffReleaseHeader);

    /**
     * 批量删除运价发布主表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTariffReleaseHeaderByIds(Integer[] rowIds);

}

