package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.CustomerInfo;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.CustomerPlatformInfo;
import com.huazheng.tunny.ocean.api.vo.CustomerInfoVO;

import java.util.List;
import java.util.Map;

/**
 * 客户信息主表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-01 11:52:05
 */
public interface CustomerInfoService extends IService<CustomerInfo> {
    /**
     * 查询客户信息主表信息
     *
     * @param info 客户信息主表ID
     * @return 客户信息主表信息
     */
    public CustomerInfo selectCustomegenNorInfoById(CustomerPlatformInfo info);

    /**
     * 查询客户信息主表信息
     *
     * @param rowId 客户信息主表ID
     * @return 客户信息主表信息
     */
    public CustomerInfo selectCustomegenNorInfoById1(String rowId);

    public CustomerInfo selectCustomegenNorInfoByCustomerCode(CustomerInfo customerInfo);

    public List<Map<String,Object>> listCustomerInfo(Map<String,Object> map);

    /**
     * 查询客户信息主表列表
     *
     * @param customerInfo 客户信息主表信息
     * @return 客户信息主表集合
     */
    public List<CustomerInfo> selectCustomerInfoList(CustomerInfo customerInfo);

    public List<CustomerInfo> getCustomerByPlatformCode(CustomerInfo customerInfo);

    public List<CustomerInfo> getCityByPlatformCode(CustomerInfo customerInfo);

    public List<CustomerInfo> selectCustomerInfoListBySocialUcCode(CustomerInfo customerInfo);

    public List<CustomerInfo> selectCustomerInfoListBySocialUcCode2(CustomerInfo customerInfo);

    public List<CustomerInfo> selectCustomerInfoList1(CustomerInfo customerInfo);

    /**
     * 根据编码查询该用户省级平台信息
     * @param customerCode
     * @return
     */
    public CustomerInfo selectInfoByCode(String customerCode);

    /**
     * 根据平台查询用户编码
     * @param platformCode
     * @return
     */
    public String selectCustomerNoByPlatform(String platformCode);
    /**
     * 分页模糊查询客户信息主表列表
     * @return 客户信息主表集合
     */
    public Page selectCustomerInfoListByLike(Query query);

    public R listXlk(Map<String, Object> params);

    public Page selectCustomerInfoPage(Query query);



    /**
     * 新增客户信息主表
     *
     * @param customerInfo 客户信息主表信息
     * @return 结果
     */
    public int insertCustomerInfo(CustomerInfo customerInfo);

    /**
     * 客户注册时校验以前是否注册过
     *
     * @param customerInfo 客户信息主表信息
     * @return 结果
     */
    public List<CustomerInfo> isHaveMe(CustomerInfo customerInfo);

    /**
     * 新增客户信息主表
     *
     * @param customerInfo 客户信息主表信息
     * @return 结果
     */
    public R cusRegister(CustomerInfoVO customerInfo);


    /**
     * 修改客户信息主表
     *
     * @param customerInfo 客户信息主表信息
     * @return 结果
     */
    public int updateCustomerInfo(CustomerInfo customerInfo);

    /**
     * 删除客户信息主表
     *
     * @param customerInfo 客户信息主表ID
     * @return 结果
     */
    public int deleteCustomerInfoById(CustomerInfo customerInfo);

    /**
     * 批量删除客户信息主表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteCustomerInfoByIds(Integer[] rowIds);

    public List<CustomerInfo> selectCustomerInfoSocialUcCode(CustomerInfo customerInfo);

    public List<CustomerInfo> selectCustomerInfoByresveredField(CustomerInfo customerInfo);

    public int updateCustomerInfoBySocialUcCode(CustomerInfo customerInfo);

    R getUserWithMiniPlatform(CustomerInfo customerInfo);
}

