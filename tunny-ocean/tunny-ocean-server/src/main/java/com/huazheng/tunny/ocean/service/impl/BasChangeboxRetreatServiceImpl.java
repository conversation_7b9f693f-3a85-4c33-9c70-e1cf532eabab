package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.feign.RemoteAdminService;
import com.huazheng.tunny.ocean.api.vo.SysDictVo;
import com.huazheng.tunny.ocean.mapper.*;
import com.huazheng.tunny.ocean.service.BasChangeboxRetreatService;
import com.huazheng.tunny.ocean.service.FdPostTransportService;
import com.huazheng.tunny.ocean.service.SysNoConfigService;
import com.huazheng.tunny.ocean.util.CheckUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service("basChangeboxRetreatService")
public class BasChangeboxRetreatServiceImpl extends ServiceImpl<BasChangeboxRetreatMapper, BasChangeboxRetreat> implements BasChangeboxRetreatService {

    @Autowired
    private BasChangeboxRetreatMapper basChangeboxRetreatMapper;
    @Autowired
    private BasChangeboxDetailMapper basChangeboxDetailMapper;
    @Autowired
    private WaybillContainerInfoMapper waybillContainerInfoMapper;
    @Autowired
    private WaybillGoodsInfoMapper waybillGoodsInfoMapper;
    @Autowired
    private WaybillParticipantsMapper waybillParticipantsMapper;
    @Autowired
    private BookingRequesheaderMapper bookingRequesheaderMapper;
    @Autowired
    private BookingRequesdetailMapper bookingRequesdetailMapper;
    @Autowired
    private SysNoConfigService sysNoConfigService;
    @Autowired
    private FdShippingAccountMapper fdShippingAccountMapper;
    @Autowired
    private WaybillHeaderMapper waybillHeaderMapper;
    @Autowired
    private AudiopinionMapper audiopinionMapper;
    @Autowired
    private BasChangeboxContainerInfoMapper basChangeboxContainerInfoMapper;
    @Autowired
    private ShifmanagementMapper shifmanagementMapper;
    @Autowired
    private StationManagementMapper stationMapper;
    @Autowired
    private ContainerTypeDataMapper containerTypeDataMapper;
    @Autowired
    private SysDictMapper sysDictMapper;
    @Autowired
    private PayCodeMesMapper payCodeMesMapper;
    @Autowired
    private BasChangeboxCostDetailMapper basChangeboxCostDetailMapper;
    @Autowired
    private CustomerPlatformInfoMapper customerPlatformInfoMapper;
    @Autowired
    private FdBusCostMapper fdBusCostMapper;
    @Autowired
    private FdBusCostWaybillMapper fdBusCostWaybillMapper;
    @Autowired
    private FdBusCostDetailMapper fdBusCostDetailMapper;
    @Autowired
    private BillPayCustomerSubMapper billPayCustomerSubMapper;
    @Autowired
    private BillPayCustomerMapper billPayCustomerMapper;
    @Autowired
    private CustomerInfoMapper customerInfoMapper;
    @Autowired
    private FdBalanceDetailMapper fdBalanceDetailMapper;
    @Autowired
    private SpaceOccupyMapper spaceOccupyMapper;
    @Autowired
    private FdShippingAccoundetailMapper fdShippingAccoundetailMapper;
    @Autowired
    private FdTradingDetailsMapper fdTradingDetailsMapper;
    @Autowired
    private BillSubPayCityMapper billSubPayCityMapper;
    @Autowired
    private BillPayProvinceSubMapper billPayProvinceSubMapper;
    @Autowired
    private BillDealWithCityMapper billDealWithCityMapper;
    @Autowired
    private BillPayProvinceMapper billPayProvinceMapper;
    @Autowired
    private StationManagementMapper stationManagementMapper;
    @Autowired
    private RemoteAdminService remoteAdminService;
    @Autowired
    private FdPostTransportService fdPostTransportService;
    @Value("${db.database}")
    private String database;


    /**
     * 查询换箱申请主表信息
     *
     * @param rowId 换箱申请主表ID
     * @return 换箱申请主表信息
     */
    @Override
    public BasChangeboxRetreat selectBasChangeboxRetreatById(String rowId) {
        BasChangeboxRetreat retreat = basChangeboxRetreatMapper.selectBasChangeboxRetreatById(rowId);
        Audiopinion sel = new Audiopinion();
        sel.setOrderNo(retreat.getBusinessid());
        sel.setDeleteFlag("N");
        List<Audiopinion> audiopinions = audiopinionMapper.selectAudiopinionList(sel);
        if (CollUtil.isNotEmpty(audiopinions)) {
            retreat.setAudiopinionList(audiopinions);
        }

        Shifmanagement sel2 = new Shifmanagement();
        sel2.setShiftId(retreat.getShiftNo());
//        sel2.setPlatformCode(retreat.getPlatformCode());
        sel2.setDeleteFlag("N");
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel2);
        if (CollUtil.isNotEmpty(shifmanagements)) {
            retreat.setShifmanagement(shifmanagements.get(0));
        }
        return retreat;
    }

    /**
     * 查询换箱申请主表列表
     *
     * @param basChangeboxRetreat 换箱申请主表信息
     * @return 换箱申请主表集合
     */
    @Override
    public List<BasChangeboxRetreat> selectBasChangeboxRetreatList(BasChangeboxRetreat basChangeboxRetreat) {
        return basChangeboxRetreatMapper.selectBasChangeboxRetreatList(basChangeboxRetreat);
    }


    /**
     * 分页模糊查询换箱申请主表列表
     *
     * @return 换箱申请主表集合
     */
    @Override
    public Page selectBasChangeboxRetreatListByLike(Query query) {
        if (StrUtil.isBlank(query.getOrderByField())) {
            query.setOrderByField("IFNULL(x.update_time,x.add_time)");
            query.setAsc(Boolean.FALSE);
        }
        BasChangeboxRetreat basChangeboxRetreat = BeanUtil.mapToBean(query.getCondition(), BasChangeboxRetreat.class, false);
        basChangeboxRetreat.setCustomerNo(SecurityUtils.getUserInfo().getPlatformCode());
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        if ("0".equals(userInfo.getPlatformLevel()) && "1".equals(userInfo.getDataFlag())) {
            basChangeboxRetreat.setMiniPlatform(userInfo.getMiniPlatform());
        }
        List<BasChangeboxRetreat> basChangeboxRetreats = basChangeboxRetreatMapper.selectBasChangeboxRetreatListByLike(query, basChangeboxRetreat);
        query.setRecords(basChangeboxRetreats);
        return query;
    }

    @Override
    public Page cityPage(Query query) {
        if (StrUtil.isBlank(query.getOrderByField())) {
            query.setOrderByField("IFNULL(x.update_time,x.add_time) ");
            query.setAsc(Boolean.FALSE);
        }
        BasChangeboxRetreat basChangeboxRetreat = BeanUtil.mapToBean(query.getCondition(), BasChangeboxRetreat.class, false);
        basChangeboxRetreat.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        List<BasChangeboxRetreat> basChangeboxRetreats = basChangeboxRetreatMapper.cityPage(query, basChangeboxRetreat);
        query.setRecords(basChangeboxRetreats);
        return query;
    }

    /**
     * 新增换箱申请主表
     *
     * @param basChangeboxRetreat 换箱申请主表信息
     * @return 结果
     */
    @Override
    public int insertBasChangeboxRetreat(BasChangeboxRetreat basChangeboxRetreat) {
        return basChangeboxRetreatMapper.insertBasChangeboxRetreat(basChangeboxRetreat);
    }

    /**
     * 修改换箱申请主表
     *
     * @param basChangeboxRetreat 换箱申请主表信息
     * @return 结果
     */
    @Override
    public int updateBasChangeboxRetreat(BasChangeboxRetreat basChangeboxRetreat) {
        return basChangeboxRetreatMapper.updateBasChangeboxRetreat(basChangeboxRetreat);
    }


    /**
     * 删除换箱申请主表
     *
     * @param rowId 换箱申请主表ID
     * @return 结果
     */
    public int deleteBasChangeboxRetreatById(String rowId) {
        return basChangeboxRetreatMapper.deleteBasChangeboxRetreatById(rowId);
    }


    /**
     * 批量删除换箱申请主表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBasChangeboxRetreatByIds(Integer[] rowIds) {
        return basChangeboxRetreatMapper.deleteBasChangeboxRetreatByIds(rowIds);
    }

    @Override
    public void updateAllfreight(String businessid) {
        BasChangeboxDetail detail = new BasChangeboxDetail();
        detail.setBusinessid(businessid);
        detail.setDeleteFlag("N");
        List<BasChangeboxDetail> basChangeboxDetails = basChangeboxDetailMapper.selectBasChangeboxDetailList(detail);
        BigDecimal all = BigDecimal.valueOf(0);
        for (BasChangeboxDetail d : basChangeboxDetails) {
            if (d.getResveredField08() != null) {
                all = all.add(d.getResveredField08());
            }
        }
        BasChangeboxRetreat retreat = new BasChangeboxRetreat();
        retreat.setBusinessid(businessid);
        retreat.setAllfreight(all);
        basChangeboxRetreatMapper.updateBasChangeboxRetreat(retreat);
    }

    @Override
    public int updateByRowId(BasChangeboxRetreat basChangeboxRetreat) {
        return basChangeboxRetreatMapper.updateByRowId(basChangeboxRetreat);
    }

    public void insertSupBasChangeboxRetreat(BasChangeboxRetreat basChangeboxRetreat, String supCode, String supName, Boolean isCity) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String old = basChangeboxRetreat.getBusinessid();
        String businessid = sysNoConfigService.genNo("HX");
        BasChangeboxRetreat addObj = basChangeboxRetreat;
        addObj.setRowId(UUID.randomUUID().toString());
        addObj.setBusinessid(businessid);
        addObj.setCustomerNo(basChangeboxRetreat.getPlatformCode());
        addObj.setCustomerName(basChangeboxRetreat.getPlatformName());
        addObj.setPlatformCode(supCode);
        addObj.setPlatformName(supName);
        addObj.setStatus("1");
        addObj.setSource("1");
        addObj.setAddWho(userInfo.getUserName());
        addObj.setAddWhoName(userInfo.getRealName());
        addObj.setAddTime(new Date());
        basChangeboxRetreatMapper.insertBasChangeboxRetreat(addObj);

        BasChangeboxContainerInfo sel = new BasChangeboxContainerInfo();
        sel.setBusinessid(old);
        sel.setDeleteFlag("N");
        List<BasChangeboxContainerInfo> infos = basChangeboxContainerInfoMapper.selectBasChangeboxContainerInfoList(sel);
        if (CollUtil.isNotEmpty(infos)) {
            for (BasChangeboxContainerInfo info : infos) {
                if (!"0".equals(info.getBoxnumberStatus())) {
                    info.setRowId(UUID.randomUUID().toString());
                    info.setBusinessid(businessid);
//                info.setRetreatCost(null);
//                info.setUndoType(null);
                    info.setCustomerNo(addObj.getCustomerNo());
                    info.setCustomerName(addObj.getCustomerName());
                    info.setAddWho(userInfo.getUserName());
                    info.setAddWhoName(userInfo.getRealName());
                    info.setAddTime(LocalDateTime.now());
                    basChangeboxContainerInfoMapper.insertBasChangeboxContainerInfo(info);
                }
            }
        }

        WaybillGoodsInfo sel2 = new WaybillGoodsInfo();
        sel2.setHxAppNo(old);
        sel2.setDeleteFlag("N");
        List<WaybillGoodsInfo> waybillGoodsInfos = waybillGoodsInfoMapper.selectWaybillGoodsInfoList(sel2);
        if (CollUtil.isNotEmpty(waybillGoodsInfos)) {
            for (WaybillGoodsInfo info : waybillGoodsInfos) {
                info.setRowId(UUID.randomUUID().toString());
                info.setHxAppNo(businessid);
                info.setAddWho(userInfo.getUserName());
                info.setAddWhoName(userInfo.getRealName());
                info.setAddTime(LocalDateTime.now());
            }
            waybillGoodsInfoMapper.insertWaybillGoodsInfo(waybillGoodsInfos);
        }

        WaybillParticipants sel3 = new WaybillParticipants();
        sel3.setHxAppNo(old);
        sel3.setDeleteFlag("N");
        List<WaybillParticipants> waybillParticipants = waybillParticipantsMapper.selectWaybillParticipantsList(sel3);
        if (CollUtil.isNotEmpty(waybillParticipants)) {
            for (WaybillParticipants participants : waybillParticipants) {
                participants.setRowId(UUID.randomUUID().toString());
                participants.setHxAppNo(businessid);
                participants.setAddWho(userInfo.getUserName());
                participants.setAddWhoName(userInfo.getRealName());
                participants.setAddTime(LocalDateTime.now());
            }
            waybillParticipantsMapper.insertWaybillParticipants(waybillParticipants);
        }


        BasChangeboxCostDetail sel5 = new BasChangeboxCostDetail();
        sel5.setHxAppNo(old);
        sel5.setCostType("1");
        sel5.setDeleteFlag("N");
        List<BasChangeboxCostDetail> details = basChangeboxCostDetailMapper.selectBasChangeboxCostDetailList(sel5);
        if (CollUtil.isNotEmpty(details)) {
            for (BasChangeboxCostDetail detail : details) {
                detail.setId(null);
                detail.setHxAppNo(businessid);
                detail.setCostType("0");
                detail.setPayCode(basChangeboxRetreat.getPlatformCode());
                detail.setPayName(basChangeboxRetreat.getPlatformName());
                detail.setReceiveCode(supCode);
                detail.setReceiveName(supName);
                detail.setAuditStatus("0");
                detail.setBillSubCode(null);
                basChangeboxCostDetailMapper.insertBasChangeboxCostDetail(detail);
            }
        }

//        if(isCity){
        PayCodeMes sel4 = new PayCodeMes();
        sel4.setHxAppNo(old);
        sel4.setDeleteFlag("N");
        List<PayCodeMes> payCodeMes = payCodeMesMapper.selectPayCodeMesList(sel4);
        if (CollUtil.isNotEmpty(payCodeMes)) {
            for (PayCodeMes payCodeMes1 : payCodeMes) {
                payCodeMes1.setRowId(UUID.randomUUID().toString());
                payCodeMes1.setHxAppNo(businessid);
                payCodeMes1.setAddWho(userInfo.getUserName());
                payCodeMes1.setAddWhoName(userInfo.getRealName());
                payCodeMes1.setAddTime(LocalDateTime.now());
                payCodeMesMapper.insertPayCodeMes(payCodeMes1);
            }
        }
//        }
    }

    /**
     * 向上级平台发起撤换箱
     *
     * @Param: basChangeboxRetreat
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/07/12 10:22
     **/
    public void insertSupInfo(BasChangeboxRetreat basChangeboxRetreat, List<FdShippingAccount> fdShippingAccounts, List<Shifmanagement> shifmanagements, String supCode, String supName) {
        Boolean hasSup = false;
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        Boolean isCity = true;
        if (CollUtil.isNotEmpty(shifmanagements)) {
            if (StrUtil.isNotEmpty(shifmanagements.get(0).getSharePlatformCode())) {
                supCode = shifmanagements.get(0).getSharePlatformCode();
                supName = shifmanagements.get(0).getSharePlatformName();
                WaybillHeader sel = new WaybillHeader();
                sel.setPlatformCode(supCode);
                sel.setShiftNo(shifmanagements.get(0).getShiftId());
                sel.setDeleteFlag("N");
                List<WaybillHeader> waybillHeaders = waybillHeaderMapper.selectWaybillHeaderList(sel);
                if (CollUtil.isNotEmpty(waybillHeaders)) {
                    hasSup = true;
                }
            } else {
                if (CollUtil.isNotEmpty(fdShippingAccounts) && !"0".equals(fdShippingAccounts.get(0).getStatus()) && !"9".equals(fdShippingAccounts.get(0).getStatus())) {
                    //台账已发起，向省平台发起撤换箱申请
                    hasSup = true;
                    isCity = false;
                    supCode = userInfo.getSupPlatformCode();
                    if (supCode.contains("_")) {
                        supCode = supCode.split("_")[0];
                    }
                    CustomerInfo cus = new CustomerInfo();
                    cus.setCustomerCode(supCode);
                    cus.setDeleteFlag("N");
                    List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoList(cus);
                    if (CollUtil.isNotEmpty(customerInfos)) {
                        supName = customerInfos.get(0).getCompanyName();
                    }
                }

            }
        }
        if (hasSup) {
            //存在上级市平台，补充撤箱全部数据
            insertSupBasChangeboxRetreat(basChangeboxRetreat, supCode, supName, isCity);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R retreat2(BasChangeboxRetreat basChangeboxRetreat, String auditType) {
        String resveredField01 = basChangeboxRetreat.getResveredField01();
        if (StrUtil.isBlank(basChangeboxRetreat.getBusinessid())) {
            basChangeboxRetreat = basChangeboxRetreatMapper.selectBasChangeboxRetreatById(basChangeboxRetreat.getRowId());
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        //记录审核意见表
        if ("0".equals(basChangeboxRetreat.getSource())) {
            Audiopinion audiopinion = new Audiopinion();
            audiopinion.setRowId(UUID.randomUUID().toString());
            audiopinion.setStatus("1");
            audiopinion.setOrderNo(basChangeboxRetreat.getBusinessid());
            audiopinion.setWaybillNo(basChangeboxRetreat.getWaybillNo());
            audiopinion.setAuditNo(userInfo.getUserName());
            audiopinion.setAuditOpinion("HXSH");
            audiopinion.setAuditTime(LocalDateTime.now());
            audiopinion.setDeleteFlag("N");
            audiopinion.setAddTime(new Date());
            audiopinion.setAddWho(userInfo.getUserName());
            audiopinion.setAddWhoName(userInfo.getRealName());
            audiopinion.setResveredField01(userInfo.getPlatformCode());
            audiopinion.setResveredField02(userInfo.getUserName());
            if (StrUtil.isNotBlank(resveredField01)) {
                audiopinion.setResveredField03(resveredField01);
            } else {
                return new R<>(new Throwable("审核意见为空，请填写"));
            }
            audiopinionMapper.insertAudiopinion(audiopinion);
        }
        //
        Shifmanagement shifmanagement = new Shifmanagement();
        shifmanagement.setShiftId(basChangeboxRetreat.getShiftNo());
        shifmanagement.setDeleteFlag("N");
        shifmanagement.setPlatformCode(basChangeboxRetreat.getPlatformCode());
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(shifmanagement);

        FdShippingAccount fdShippingAccount = new FdShippingAccount();
        fdShippingAccount.setShiftNo(basChangeboxRetreat.getShiftNo());
        fdShippingAccount.setDeleteFlag("N");
        fdShippingAccount.setPlatformCode(basChangeboxRetreat.getPlatformCode());

        /*if ("0".equals(auditType)) {
            //市平台审批
            fdShippingAccount.setPlatformCode(basChangeboxRetreat.getPlatformCode());
        }else if ("1".equals(auditType)) {
            //市平台发起
            fdShippingAccount.setPlatformCode(basChangeboxRetreat.getCustomerNo());
        }*/

        List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountList(fdShippingAccount);

        double del = 0;
        double add = 0;
        BasChangeboxContainerInfo sel = new BasChangeboxContainerInfo();
        sel.setBusinessid(basChangeboxRetreat.getBusinessid());
        sel.setDeleteFlag("N");
        List<BasChangeboxContainerInfo> infos = basChangeboxContainerInfoMapper.selectBasChangeboxContainerInfoList2(sel);
        if (CollUtil.isNotEmpty(infos)) {
            for (BasChangeboxContainerInfo info : infos) {
                if ("1".equals(info.getBoxnumberStatus()) && "2".equals(info.getUndoType())) {
                    if (info.getContainerType().contains("20")) {
                        del += 0.5;
                    } else if (info.getContainerType().contains("40") || info.getContainerType().contains("45")) {
                        del += 1;
                    }
                } else if ("2".equals(info.getBoxnumberStatus())) {
                    if (info.getContainerType().contains("20")) {
                        add += 0.5;
                    } else if (info.getContainerType().contains("40") || info.getContainerType().contains("45")) {
                        add += 1;
                    }
                }
            }
            //新增>撤箱 时舱位数校验
            if (add > del) {
                //班次剩余舱位数
                String num1 = shifmanagementMapper.getNum(basChangeboxRetreat.getShiftNo());
                if (Double.parseDouble(num1) + del - add < 0) {
                    throw new RuntimeException("舱位数不足，不能完成撤换箱审核");
                }
            }

            //向上级平台发起撤换箱
//            if ("0".equals(auditType)) {
            String supCode = null;
            String supName = null;
            BasChangeboxRetreat supObj = new BasChangeboxRetreat();
            BeanUtil.copyProperties(basChangeboxRetreat, supObj);
            supObj.setResveredField01(null);
            insertSupInfo(supObj, fdShippingAccounts, shifmanagements, supCode, supName);
//            }
            List<String> miniPlatformNames = basChangeboxContainerInfoMapper.getMiniPlatformName(basChangeboxRetreat.getBusinessid());
            if ("0".equals(auditType)) {
                //订舱客户发起或市平台审批
                saveBookRequestWithCityAudit2(basChangeboxRetreat, userInfo, infos, miniPlatformNames);
                //更新订单信息开始↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___
                saveWayBillWithCityAudit2(basChangeboxRetreat, userInfo, del, add, infos, miniPlatformNames);
                //更新订单信息结束↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___
                //更新业务流程单开始↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___↓___
                saveFdBusCostWithCityAudit2(basChangeboxRetreat, userInfo, infos, miniPlatformNames);
                //更新业务流程单结束↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___↑___
            } else if ("1".equals(auditType)) {
                List<BasChangeboxContainerInfo> customerNos = basChangeboxContainerInfoMapper.selectCustomerNoGroup2(basChangeboxRetreat.getBusinessid());
                //市平台发起
                saveWayBillWithCitySubmit2(basChangeboxRetreat, userInfo, infos, shifmanagements, customerNos);
//                saveFdBusCostWithCitySubmit(basChangeboxRetreat, userInfo, infos, customerNos);
            }

            //如果台账未提交，则直接更新，如果已提交，则由省平台审核后更新
            if (CollUtil.isNotEmpty(fdShippingAccounts) && "0".equals(fdShippingAccounts.get(0).getStatus())) {
                FdShippingAccoundetail fdShippingAccoundetail = new FdShippingAccoundetail();
                fdShippingAccoundetail.setShiftNo(fdShippingAccounts.get(0).getShiftNo());
                fdShippingAccoundetail.setPlatformCode(fdShippingAccounts.get(0).getPlatformCode());
                List<FdShippingAccoundetail> fdShippingAccoundetails = fdShippingAccoundetailMapper.containerInfoListNew(fdShippingAccoundetail);

                for (BasChangeboxContainerInfo info : infos) {
                    if ("1".equals(info.getBoxnumberStatus())) {
                        FdShippingAccoundetail delObj = new FdShippingAccoundetail();
                        delObj.setAccountCode(fdShippingAccounts.get(0).getAccountCode());
                        delObj.setContainerNo(info.getContainerNo());
                        delObj.setContainerStatus("1");
                        delObj.setDeleteFlag("C");
                        delObj.setDeleteWho(userInfo.getUserName());
                        delObj.setDeleteWhoName(userInfo.getRealName());
                        delObj.setDeleteTime(LocalDateTime.now());
                        fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode(delObj);
                    } else if ("2".equals(info.getBoxnumberStatus())) {
                        for (FdShippingAccoundetail detail : fdShippingAccoundetails) {
                            if (CollUtil.isNotEmpty(fdShippingAccoundetails)) {
                                if (info.getContainerNo().equals(detail.getContainerNumber())) {
                                    detail.setAccountCode(fdShippingAccounts.get(0).getAccountCode());
                                    detail.setContainerStatus("0");
                                    detail.setAddWho(userInfo.getUserName());
                                    detail.setAddWhoName(userInfo.getRealName());
                                    detail.setAddTime(LocalDateTime.now());
                                    fdShippingAccoundetailMapper.insert(detail);
                                }
                            }
                        }
                    }
                }
                fdShippingAccoundetailMapper.updateByAccountCode(fdShippingAccounts.get(0).getAccountCode());
            }
        }

//        if ("0".equals(auditType)) {
        basChangeboxRetreat.setResveredField01(null);
        basChangeboxRetreat.setStatus("2");
        basChangeboxRetreatMapper.updateBasChangeboxRetreat(basChangeboxRetreat);
//        }
        //更新舱位占用表
        spaceOccupyMapper.updateSpaceNumsByShiftNo(basChangeboxRetreat.getShiftNo());
        return new R<>(0, Boolean.TRUE, null, "操作成功");
    }

    public BigDecimal insertCostDetail(List<BasChangeboxCostDetail> basChangeboxCostDetail, FdBusCost fdBusCost, SecruityUser userInfo, String ysBillCode) {
        BigDecimal ysAmount = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(basChangeboxCostDetail)) {
            Map<String, Object> map = new HashMap<>();
            for (BasChangeboxCostDetail detail : basChangeboxCostDetail) {
                FdBusCostDetail addObj = new FdBusCostDetail();
                BeanUtil.copyProperties(detail, addObj);
                addObj.setId(null);
                addObj.setCostCode(fdBusCost.getCostCode());
                addObj.setAuditStatus("1");
                addObj.setRemark(null);
                addObj.setExchangeRateNew(addObj.getExchangeRate());
                addObj.setAddWho(userInfo.getUserName());
                addObj.setAddWhoName(userInfo.getRealName());
                addObj.setAddTime(LocalDateTime.now());
                addObj.setAuditTime(LocalDateTime.now());
                addObj.setBillSubCode(ysBillCode);
                fdBusCostDetailMapper.insertFdBusCostDetail(addObj);
                ysAmount = ysAmount.add(addObj.getLocalAmount());

                //应付
                BasChangeboxCostDetail sel8 = new BasChangeboxCostDetail();
                sel8.setHxAppNo(detail.getHxAppNo());
                sel8.setDeleteFlag("N");
                sel8.setPayCode(detail.getReceiveCode());
                sel8.setContainerNumber(detail.getContainerNumber());
                List<BasChangeboxCostDetail> yfList = basChangeboxCostDetailMapper.selectBasChangeboxCostDetailList(sel8);
                if (CollUtil.isNotEmpty(yfList)) {
                    for (BasChangeboxCostDetail detail2 : yfList) {
                        Object o = map.get(String.valueOf(detail2.getId()));
                        if (o != null) {
                            continue;
                        }
                        map.put(String.valueOf(detail2.getId()), detail2.getId());

                        FdBusCostDetail addObj2 = new FdBusCostDetail();
                        BeanUtil.copyProperties(detail2, addObj2);
                        addObj2.setId(null);
                        addObj2.setCostCode(fdBusCost.getCostCode());
                        addObj2.setAuditStatus("1");
                        addObj2.setRemark(null);
                        addObj2.setExchangeRateNew(addObj2.getExchangeRate());
                        addObj2.setAddWho(userInfo.getUserName());
                        addObj2.setAddWhoName(userInfo.getRealName());
                        addObj2.setAddTime(LocalDateTime.now());
                        addObj2.setAuditTime(LocalDateTime.now());
                        fdBusCostDetailMapper.insertFdBusCostDetail(addObj2);
                    }
                }
            }
        }
        return ysAmount;
    }

    private void saveFdBusCostWithCitySubmit(BasChangeboxRetreat basChangeboxRetreat, SecruityUser userInfo, List<BasChangeboxContainerInfo> infos, List<String> customerNos) {
        BasChangeboxContainerInfo sel = new BasChangeboxContainerInfo();
        sel.setBusinessid(basChangeboxRetreat.getBusinessid());
        sel.setDeleteFlag("N");
        List<BasChangeboxContainerInfo> basChangeboxContainerInfos = basChangeboxContainerInfoMapper.selectBasChangeboxContainerInfoList(sel);

        if (CollUtil.isNotEmpty(customerNos)) {
            for (String customerNo : customerNos) {
                BasChangeboxCostDetail sel8 = new BasChangeboxCostDetail();
                sel8.setHxAppNo(basChangeboxRetreat.getBusinessid());
                sel8.setDeleteFlag("N");
                sel8.setPayCode(customerNo);
                List<BasChangeboxCostDetail> basChangeboxCostDetail = basChangeboxCostDetailMapper.selectBasChangeboxCostDetailList(sel8);

                FdBusCost sel3 = new FdBusCost();
                sel3.setShiftNo(basChangeboxRetreat.getShiftNo());
                sel3.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                sel3.setCustomerCode(customerNo);
                sel3.setDeleteFlag("N");
                List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel3);
                if (CollUtil.isNotEmpty(fdBusCosts)) {

                    //存在业务流程单，更新业务流程单
                    FdBusCost fdBusCost = fdBusCosts.get(0);

                    FdBusCostDetail sel7 = new FdBusCostDetail();
                    sel7.setCostCode(fdBusCost.getCostCode());
                    sel7.setCodeBbCategoriesCode("f_fee_type");
                    sel7.setDeleteFlag("N");
                    sel7.setStatus("0");
                    List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList(sel7);

                    String ysBillCode = null;
                    BillPayCustomerSub sel6 = new BillPayCustomerSub();
                    sel6.setShiftNo(basChangeboxRetreat.getShiftNo());
                    sel6.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                    sel6.setCustomerCode(customerNo);
                    sel6.setDeleteFlag("N");
                    sel6.setStatus("1");
                    List<BillPayCustomerSub> billPayCustomerSubs = billPayCustomerSubMapper.selectBillSubIncomeCityList(sel6);
                    if (CollUtil.isNotEmpty(billPayCustomerSubs)) {
                        BillPayCustomerSub billPayCustomerSub = billPayCustomerSubs.get(billPayCustomerSubs.size() - 1);
                        ysBillCode = CheckUtil.getNumber(billPayCustomerSub.getBillSubCode(), 3);
                    }

                    if (!"0".equals(fdBusCost.getAuditStatus()) && !"9".equals(fdBusCost.getAuditStatus())) {
                        //业务流程单已经入审核
                        /*插入新增费用明细 应收+应付
                        更新撤箱费用明细状态 应收+应付
                        插入撤箱费用明细 负值 应收+应付
                        插入亏舱费 应收
                        新增应收账单*/
                        BigDecimal ysAmount = BigDecimal.ZERO;

                        //插入新增费用明细 应收+应付
                        BigDecimal bigDecimal = insertCostDetail(basChangeboxCostDetail, fdBusCost, userInfo, ysBillCode);
                        ysAmount = ysAmount.add(bigDecimal);
                        //更新撤箱费用明细状态 应收+应付
                        //插入撤箱费用明细 负值 应收+应付
                        if (CollUtil.isNotEmpty(basChangeboxContainerInfos)) {
                            for (BasChangeboxContainerInfo info : basChangeboxContainerInfos) {
                                if ("1".equals(info.getBoxnumberStatus())) {
                                    if (CollUtil.isNotEmpty(detailList)) {
                                        for (FdBusCostDetail fdBusCostDetail : detailList) {
                                            if (info.getContainerNo().equals(fdBusCostDetail.getContainerNumber())) {
                                                if ("0".equals(info.getUndoType()) && !"jndtlyf".equals(fdBusCostDetail.getCodeSsCategoriesCode())) {
                                                    continue;
                                                } else if ("1".equals(info.getUndoType()) && !"jwdtlyf".equals(fdBusCostDetail.getCodeSsCategoriesCode())) {
                                                    continue;
                                                }
                                                FdBusCostDetail delObj = new FdBusCostDetail();
                                                delObj.setId(fdBusCostDetail.getId());
                                                delObj.setStatus("1");
                                                delObj.setDeleteWho(userInfo.getUserName());
                                                delObj.setDeleteWhoName(userInfo.getRealName());
                                                delObj.setDeleteTime(LocalDateTime.now());
                                                fdBusCostDetailMapper.updateFdBusCostDetail(delObj);

                                                fdBusCostDetail.setId(null);
                                                fdBusCostDetail.setLocalAmount(fdBusCostDetail.getLocalAmount().negate());
                                                fdBusCostDetail.setOriginalAmount(fdBusCostDetail.getOriginalAmount().negate());
                                                fdBusCostDetail.setStatus("1");
                                                fdBusCostDetail.setExchangeRateNew(fdBusCostDetail.getExchangeRate());
                                                fdBusCostDetail.setRemark(null);
                                                fdBusCostDetail.setAddWho(userInfo.getUserName());
                                                fdBusCostDetail.setAddWhoName(userInfo.getRealName());
                                                fdBusCostDetail.setAddTime(LocalDateTime.now());
                                                if ("0".equals(fdBusCostDetail.getCostType())) {
                                                    fdBusCostDetail.setBillSubCode(ysBillCode);
                                                    ysAmount = ysAmount.add(fdBusCostDetail.getLocalAmount());
                                                } else {
                                                    fdBusCostDetail.setBillSubCode(null);
                                                }
                                                fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
                                            }
                                        }
                                    }

                                    //插入亏舱费 应收
                                    if (info.getRetreatCost() != null && info.getRetreatCost().compareTo(BigDecimal.ZERO) > 0) {
                                        ysAmount = ysAmount.add(info.getRetreatCost());
                                        //新增亏舱费
                                        FdBusCostDetail addObj = new FdBusCostDetail();
                                        addObj.setCostCode(fdBusCost.getCostCode());
                                        addObj.setCostType("0");
                                        addObj.setContainerNumber(info.getContainerNo());
                                        addObj.setCodeBbCategoriesCode("f_extra_fee_type");
                                        addObj.setCodeBbCategoriesName("额外费用");
                                        addObj.setCodeSsCategoriesCode("f_cancel_fee");
                                        addObj.setCodeSsCategoriesName("亏舱费");
                                        addObj.setReceiveCode(fdBusCost.getPlatformCode());
                                        addObj.setReceiveName(fdBusCost.getPlatformName());
                                        addObj.setPayCode(fdBusCost.getCustomerCode());
                                        addObj.setPayName(fdBusCost.getCustomerName());
                                        addObj.setCurrency("人民币");
                                        addObj.setExchangeRate(BigDecimal.valueOf(1));
                                        addObj.setExchangeRateNew(BigDecimal.valueOf(1));
                                        addObj.setOriginalAmount(info.getRetreatCost());
                                        addObj.setLocalAmount(info.getRetreatCost());
                                        addObj.setAuditStatus("1");
                                        addObj.setShiftNo(fdBusCost.getShiftNo());
                                        addObj.setBillSubCode(ysBillCode);
                                        addObj.setRemark(null);
                                        addObj.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                        addObj.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                        addObj.setAddTime(LocalDateTime.now());
                                        addObj.setAuditTime(LocalDateTime.now());
                                        fdBusCostDetailMapper.insertFdBusCostDetail(addObj);
                                    }
                                }
                            }
                        }
                        if (CollUtil.isNotEmpty(billPayCustomerSubs)) {
                            BillPayCustomerSub billPayCustomerSub = billPayCustomerSubs.get(billPayCustomerSubs.size() - 1);
                            //插入应收账单子表
                            billPayCustomerSub.setId(null);
                            billPayCustomerSub.setBillSubCode(ysBillCode);
                            billPayCustomerSub.setBillAmount(ysAmount);
                            billPayCustomerSub.setBillingState("0");
                            //运费账单已核销，撤箱退费金额为负，则增加余额
                            if (billPayCustomerSub.getBillAmount().compareTo(BigDecimal.ZERO) < 0 && ("VERIFIED".equals(billPayCustomerSubs.get(0).getBillingState()) || "VERIFICATION".equals(billPayCustomerSubs.get(0).getBillingState()))) {
                                billPayCustomerSub.setBillingState("VERIFIED");
                                FdBalanceDetail detail = new FdBalanceDetail();
                                detail.setPlatformCode(billPayCustomerSub.getPlatformCode());
                                detail.setCustomerCode(billPayCustomerSub.getCustomerCode());
                                detail.setCustomerName(billPayCustomerSub.getCustomerName());
                                detail.setShiftNo(billPayCustomerSub.getShiftNo());
                                detail.setCodeBbCategoriesCode("f_fee_type");
                                detail.setCodeBbCategoriesName("发运运费");
                                detail.setCodeSsCategoriesCode("f_clearing_balance");
                                detail.setCodeSsCategoriesName("结算余额");
                                detail.setPaymentType("0");
                                detail.setTotalAmount(billPayCustomerSub.getBillAmount().negate());
                                detail.setRemainingAmount(billPayCustomerSub.getBillAmount().negate());
                                detail.setPlatformLevel("0");
                                detail.setBillCode(billPayCustomerSub.getBillSubCode());
                                detail.setRemarks("");
                                detail.setAddWho(userInfo.getUserName());
                                detail.setAddWhoName(userInfo.getRealName());
                                detail.setAddTime(LocalDateTime.now());
                                fdBalanceDetailMapper.insertFdBalanceDetail(detail);

                                FdTradingDetails fdTradingDetails = new FdTradingDetails();
                                String tsIn = sysNoConfigService.genNo("TS");
                                fdTradingDetails.setUuid(UUID.randomUUID().toString());
                                fdTradingDetails.setTradeSerialNumber(tsIn);
                                fdTradingDetails.setPlatformCode(detail.getPlatformCode());
                                fdTradingDetails.setPlatformName(detail.getPlatformName());
                                fdTradingDetails.setCustomerName(detail.getCustomerName());
                                fdTradingDetails.setCustomerCode(detail.getCustomerCode());
                                fdTradingDetails.setTradingHours(LocalDateTime.now());
                                fdTradingDetails.setPaymentType("0");
                                fdTradingDetails.setTransactionAmount(billPayCustomerSub.getBillAmount());
                                fdTradingDetails.setFromBillCode(billPayCustomerSub.getBillSubCode());
                                fdTradingDetails.setTradingStatus("1");
                                fdTradingDetails.setPlatformLevel("0");
                                fdTradingDetailsMapper.insertFdTradingDetails(fdTradingDetails);
                            }
                            billPayCustomerSub.setStatus("1");
                            billPayCustomerSub.setAddWho(userInfo.getUserName());
                            billPayCustomerSub.setAddWhoName(userInfo.getRealName());
                            billPayCustomerSub.setAddTime(new Date());
                            billPayCustomerSubMapper.insertBillSubIncomeCity(billPayCustomerSub);
                            billPayCustomerSubMapper.updateBoxNumByBillSubCode(billPayCustomerSub.getBillSubCode());

                            //更新订单主表信息
                            BillPayCustomer billPayCustomer = new BillPayCustomer();
                            billPayCustomer.setBillCode(billPayCustomerSub.getBillCode());
                            billPayCustomer.setDelFlag("N");
                            List<BillPayCustomer> billPayCustomers1 = billPayCustomerMapper.selectBillIncomeCityList(billPayCustomer);
                            if (CollUtil.isNotEmpty(billPayCustomers1)) {
                                BillPayCustomer billPayCustomer1 = billPayCustomers1.get(0);
                                billPayCustomer1.setBillAmount(billPayCustomer1.getBillAmount().add(billPayCustomerSub.getBillAmount()));
                                billPayCustomerMapper.updateBillIncomeCity(billPayCustomer1);
                            }
                        }
                    } else {
                        //插入新增费用明细 应收+应付
                        insertCostDetail(basChangeboxCostDetail, fdBusCost, userInfo, ysBillCode);
                        //更新撤箱费用明细状态 应收+应付
                        //插入撤箱费用明细 负值 应收+应付
                        if (CollUtil.isNotEmpty(basChangeboxContainerInfos)) {
                            for (BasChangeboxContainerInfo info : basChangeboxContainerInfos) {
                                if ("1".equals(info.getBoxnumberStatus())) {
                                    if (CollUtil.isNotEmpty(detailList)) {
                                        for (FdBusCostDetail fdBusCostDetail : detailList) {
                                            if (info.getContainerNo().equals(fdBusCostDetail.getContainerNumber())) {
                                                if ("0".equals(info.getUndoType()) && !"jndtlyf".equals(fdBusCostDetail.getCodeSsCategoriesCode())) {
                                                    continue;
                                                } else if ("1".equals(info.getUndoType()) && !"jwdtlyf".equals(fdBusCostDetail.getCodeSsCategoriesCode())) {
                                                    continue;
                                                }
                                                FdBusCostDetail delObj = new FdBusCostDetail();
                                                delObj.setId(fdBusCostDetail.getId());
                                                delObj.setStatus("1");
                                                delObj.setDeleteWho(userInfo.getUserName());
                                                delObj.setDeleteWhoName(userInfo.getRealName());
                                                delObj.setDeleteTime(LocalDateTime.now());
                                                fdBusCostDetailMapper.updateFdBusCostDetail(delObj);

                                                fdBusCostDetail.setId(null);
                                                fdBusCostDetail.setLocalAmount(fdBusCostDetail.getLocalAmount().negate());
                                                fdBusCostDetail.setOriginalAmount(fdBusCostDetail.getOriginalAmount().negate());
                                                fdBusCostDetail.setStatus("1");
                                                fdBusCostDetail.setExchangeRateNew(fdBusCostDetail.getExchangeRate());
                                                fdBusCostDetail.setRemark(null);
                                                fdBusCostDetail.setAddWho(userInfo.getUserName());
                                                fdBusCostDetail.setAddWhoName(userInfo.getRealName());
                                                fdBusCostDetail.setAddTime(LocalDateTime.now());
                                                fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
                                            }
                                        }
                                    }

                                    //插入亏舱费 应收
                                    if (info.getRetreatCost() != null && info.getRetreatCost().compareTo(BigDecimal.ZERO) > 0) {
                                        //新增亏舱费
                                        FdBusCostDetail addObj = new FdBusCostDetail();
                                        addObj.setCostCode(fdBusCost.getCostCode());
                                        addObj.setCostType("0");
                                        addObj.setContainerNumber(info.getContainerNo());
                                        addObj.setCodeBbCategoriesCode("f_extra_fee_type");
                                        addObj.setCodeBbCategoriesName("额外费用");
                                        addObj.setCodeSsCategoriesCode("f_cancel_fee");
                                        addObj.setCodeSsCategoriesName("亏舱费");
                                        addObj.setReceiveCode(fdBusCost.getPlatformCode());
                                        addObj.setReceiveName(fdBusCost.getPlatformName());
                                        addObj.setPayCode(fdBusCost.getCustomerCode());
                                        addObj.setPayName(fdBusCost.getCustomerName());
                                        addObj.setCurrency("人民币");
                                        addObj.setExchangeRate(BigDecimal.valueOf(1));
                                        addObj.setExchangeRateNew(BigDecimal.valueOf(1));
                                        addObj.setOriginalAmount(info.getRetreatCost());
                                        addObj.setLocalAmount(info.getRetreatCost());
                                        addObj.setAuditStatus("1");
                                        addObj.setShiftNo(fdBusCost.getShiftNo());
                                        addObj.setBillSubCode(ysBillCode);
                                        addObj.setRemark(null);
                                        addObj.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                        addObj.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                        addObj.setAddTime(LocalDateTime.now());
                                        addObj.setAuditTime(LocalDateTime.now());
                                        fdBusCostDetailMapper.insertFdBusCostDetail(addObj);
                                    }
                                }
                            }
                        }
                    }
                } else {
                    FdBusCost fdBusCost = new FdBusCost();
                    fdBusCost.setCostCode(sysNoConfigService.genNo("FDC"));
                    fdBusCost.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                    fdBusCost.setPlatformName(basChangeboxRetreat.getPlatformName());
                    fdBusCost.setCustomerCode(customerNo);
                    fdBusCost.setPlatformLevel("0");
                    fdBusCost.setShiftNo(basChangeboxRetreat.getShiftNo());
                    fdBusCost.setAuditStatus("0");
                    fdBusCost.setAddWho(userInfo.getUserName());
                    fdBusCost.setAddWhoName(userInfo.getRealName());
                    fdBusCost.setAddTime(LocalDateTime.now());

                    CustomerInfo sel5 = new CustomerInfo();
                    sel5.setCustomerCode(customerNo);
                    sel5.setDeleteFlag("N");
                    List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoList(sel5);
                    if (CollUtil.isNotEmpty(customerInfos)) {
                        fdBusCost.setCustomerName(customerInfos.get(0).getCompanyName());
                        fdBusCost.setPrincipalName(customerInfos.get(0).getCompanyName());
//                        fdBusCost.setCustomerLiaison(customerInfos.get(0).getCompanyName());
                    }

                    CustomerPlatformInfo sel2 = new CustomerPlatformInfo();
                    sel2.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                    sel2.setCustomerCode(customerNo);
                    sel2.setDeleteFlag("N");
                    List<CustomerPlatformInfo> list = customerPlatformInfoMapper.selectCustomerPlatformInfoList(sel2);
                    if (CollUtil.isNotEmpty(list)) {
                        fdBusCost.setContactsName(list.get(0).getContactPerson());
                        fdBusCost.setContactsPhone(list.get(0).getContactNo());
//                        fdBusCost.setCustomerLiaison(list.get(0).getContactPerson());
//                        fdBusCost.setCustomerPhone(list.get(0).getContactNo());
                    } else {
                        sel2.setPlatformCode(null);
                        List<CustomerPlatformInfo> list2 = customerPlatformInfoMapper.selectCustomerPlatformInfoList(sel2);
                        if (CollUtil.isNotEmpty(list2)) {
                            fdBusCost.setContactsName(list2.get(0).getContactPerson());
                            fdBusCost.setContactsPhone(list2.get(0).getContactNo());
//                            fdBusCost.setCustomerLiaison(list2.get(0).getContactPerson());
//                            fdBusCost.setCustomerPhone(list2.get(0).getContactNo());
                        }
                    }

                    WaybillHeader sel10 = new WaybillHeader();
                    sel10.setShiftNo(basChangeboxRetreat.getShiftNo());
                    sel10.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                    sel10.setCustomerNo(customerNo);
                    sel10.setDeleteFlag("N");
                    List<WaybillHeader> waybillHeaders = waybillHeaderMapper.selectWaybillHeaderList(sel10);
                    if (CollUtil.isNotEmpty(waybillHeaders)) {
                        FdBusCostWaybill fdBusCostWaybill = new FdBusCostWaybill();
                        fdBusCostWaybill.setCostCode(fdBusCost.getCostCode());
                        fdBusCostWaybill.setWaybillNo(waybillHeaders.get(0).getWaybillNo());
                        fdBusCostWaybill.setApplicationNumber(waybillHeaders.get(0).getOrderNo());
                        fdBusCostWaybill.setAuditStatus("2");
                        fdBusCostWaybill.setAddWho(userInfo.getUserName());
                        fdBusCostWaybill.setAddWhoName(userInfo.getRealName());
                        fdBusCostWaybill.setAddTime(LocalDateTime.now());
                        fdBusCostWaybillMapper.insertFdBusCostWaybill(fdBusCostWaybill);
                    }
                    //插入新增相关费用
                    insertCostDetail(basChangeboxCostDetail, fdBusCost, userInfo, null);
                    fdBusCostMapper.insertFdBusCost(fdBusCost);
                }
            }
        }
    }

    private void saveFdBusCostWithCitySubmit2(BasChangeboxRetreat basChangeboxRetreat, SecruityUser userInfo, List<FdBusCostDetail> detailList) {
        List<BasChangeboxContainerInfo> customerNos = basChangeboxContainerInfoMapper.selectCustomerNoGroup3(basChangeboxRetreat.getBusinessid());
        if (CollUtil.isNotEmpty(customerNos)) {
            for (BasChangeboxContainerInfo basChangeboxContainerInfo : customerNos) {
                BasChangeboxContainerInfo sel = new BasChangeboxContainerInfo();
                sel.setBusinessid(basChangeboxRetreat.getBusinessid());
                sel.setDeleteFlag("N");
                sel.setMiniPlatformName(basChangeboxContainerInfo.getMiniPlatformName());
                List<BasChangeboxContainerInfo> basChangeboxContainerInfos = basChangeboxContainerInfoMapper.selectBasChangeboxContainerInfoList2(sel);

                BasChangeboxCostDetail sel8 = new BasChangeboxCostDetail();
                sel8.setHxAppNo(basChangeboxRetreat.getBusinessid());
                sel8.setDeleteFlag("N");
                sel8.setPayCode(basChangeboxContainerInfo.getCustomerNo());
                sel8.setMiniPlatformName(basChangeboxContainerInfo.getMiniPlatformName());
                List<BasChangeboxCostDetail> basChangeboxCostDetail = basChangeboxCostDetailMapper.selectBasChangeboxCostDetailList2(sel8);

                FdBusCost sel3 = new FdBusCost();
                sel3.setShiftNo(basChangeboxRetreat.getShiftNo());
                sel3.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                sel3.setCustomerCode(basChangeboxContainerInfo.getCustomerNo());
                sel3.setDeleteFlag("N");
                List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel3);
                if (CollUtil.isNotEmpty(fdBusCosts)) {
                    //存在业务流程单，更新业务流程单
                    FdBusCost fdBusCost = fdBusCosts.get(0);

                    String ysBillCode = null;
                    BillPayCustomerSub sel6 = new BillPayCustomerSub();
                    sel6.setShiftNo(basChangeboxRetreat.getShiftNo());
                    sel6.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                    sel6.setCustomerCode(basChangeboxContainerInfo.getCustomerNo());
                    sel6.setMiniPlatformName(basChangeboxContainerInfo.getMiniPlatformName());
                    sel6.setDeleteFlag("N");
                    sel6.setStatus("1");
                    List<BillPayCustomerSub> billPayCustomerSubs = billPayCustomerSubMapper.selectBillSubIncomeCityList2(sel6);
                    if (CollUtil.isNotEmpty(billPayCustomerSubs)) {
                        BillPayCustomerSub billPayCustomerSub = billPayCustomerSubs.get(billPayCustomerSubs.size() - 1);
                        ysBillCode = CheckUtil.getNumber(billPayCustomerSub.getBillSubCode(), 3);
                    }

                    if (!"0".equals(fdBusCost.getAuditStatus()) && !"9".equals(fdBusCost.getAuditStatus())) {
                        //业务流程单已经入审核
                        /*插入新增费用明细 应收+应付
                        更新撤箱费用明细状态 应收+应付
                        插入撤箱费用明细 负值 应收+应付
                        插入亏舱费 应收
                        新增应收账单*/
                        BigDecimal ysAmount = BigDecimal.ZERO;

                        //插入新增费用明细 应收+应付
                        BigDecimal bigDecimal = insertCostDetail(basChangeboxCostDetail, fdBusCost, userInfo, ysBillCode);
                        ysAmount = ysAmount.add(bigDecimal);
                        //更新撤箱费用明细状态 应收+应付
                        //插入撤箱费用明细 负值 应收+应付
                        if (CollUtil.isNotEmpty(basChangeboxContainerInfos)) {
                            for (BasChangeboxContainerInfo info : basChangeboxContainerInfos) {
                                if ("1".equals(info.getBoxnumberStatus())) {
                                    if (CollUtil.isNotEmpty(detailList)) {
                                        for (FdBusCostDetail fdBusCostDetail : detailList) {
                                            if (info.getContainerNo().equals(fdBusCostDetail.getContainerNumber())
                                                    && fdBusCost.getCostCode().equals(fdBusCostDetail.getCostCode())
                                                    && basChangeboxContainerInfo.getCustomerNo().equals(fdBusCostDetail.getCustomerCode())
                                                    && basChangeboxContainerInfo.getMiniPlatformName().equals(fdBusCostDetail.getMiniPlatformName())) {
                                                if ("0".equals(info.getUndoType()) && !"jndtlyf".equals(fdBusCostDetail.getCodeSsCategoriesCode())) {
                                                    continue;
                                                } else if ("1".equals(info.getUndoType()) && !"jwdtlyf".equals(fdBusCostDetail.getCodeSsCategoriesCode())) {
                                                    continue;
                                                }
                                                FdBusCostDetail delObj = new FdBusCostDetail();
                                                delObj.setId(fdBusCostDetail.getId());
                                                delObj.setStatus("1");
                                                delObj.setDeleteWho(userInfo.getUserName());
                                                delObj.setDeleteWhoName(userInfo.getRealName());
                                                delObj.setDeleteTime(LocalDateTime.now());
                                                fdBusCostDetailMapper.updateFdBusCostDetail(delObj);

                                                fdBusCostDetail.setId(null);
                                                fdBusCostDetail.setLocalAmount(fdBusCostDetail.getLocalAmount().negate());
                                                fdBusCostDetail.setOriginalAmount(fdBusCostDetail.getOriginalAmount().negate());
                                                fdBusCostDetail.setStatus("1");
                                                fdBusCostDetail.setExchangeRateNew(fdBusCostDetail.getExchangeRate());
                                                fdBusCostDetail.setRemark(null);
                                                fdBusCostDetail.setAddWho(userInfo.getUserName());
                                                fdBusCostDetail.setAddWhoName(userInfo.getRealName());
                                                fdBusCostDetail.setAddTime(LocalDateTime.now());
                                                if ("0".equals(fdBusCostDetail.getCostType())) {
                                                    fdBusCostDetail.setBillSubCode(ysBillCode);
                                                    ysAmount = ysAmount.add(fdBusCostDetail.getLocalAmount());
                                                } else {
                                                    fdBusCostDetail.setBillSubCode(null);
                                                }
                                                fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
                                            }
                                        }
                                    }

                                    //插入亏舱费 应收
                                    if (info.getRetreatCost() != null && info.getRetreatCost().compareTo(BigDecimal.ZERO) > 0) {
                                        ysAmount = ysAmount.add(info.getRetreatCost());
                                        //新增亏舱费
                                        FdBusCostDetail addObj = new FdBusCostDetail();
                                        addObj.setCostCode(fdBusCost.getCostCode());
                                        addObj.setCostType("0");
                                        addObj.setContainerNumber(info.getContainerNo());
                                        addObj.setCodeBbCategoriesCode("f_extra_fee_type");
                                        addObj.setCodeBbCategoriesName("额外费用");
                                        addObj.setCodeSsCategoriesCode("f_cancel_fee");
                                        addObj.setCodeSsCategoriesName("亏舱费");
                                        addObj.setReceiveCode(fdBusCost.getPlatformCode());
                                        addObj.setReceiveName(fdBusCost.getPlatformName());
                                        addObj.setPayCode(fdBusCost.getCustomerCode());
                                        addObj.setPayName(fdBusCost.getCustomerName());
                                        addObj.setCurrency("人民币");
                                        addObj.setExchangeRate(BigDecimal.valueOf(1));
                                        addObj.setExchangeRateNew(BigDecimal.valueOf(1));
                                        addObj.setOriginalAmount(info.getRetreatCost());
                                        addObj.setLocalAmount(info.getRetreatCost());
                                        addObj.setAuditStatus("1");
                                        addObj.setShiftNo(fdBusCost.getShiftNo());
                                        addObj.setBillSubCode(ysBillCode);
                                        addObj.setRemark(null);
                                        addObj.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                        addObj.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                        addObj.setAddTime(LocalDateTime.now());
                                        addObj.setAuditTime(LocalDateTime.now());
                                        fdBusCostDetailMapper.insertFdBusCostDetail(addObj);
                                    }
                                }
                            }
                        }
                        if (CollUtil.isNotEmpty(billPayCustomerSubs)) {
                            BillPayCustomerSub billPayCustomerSub = billPayCustomerSubs.get(billPayCustomerSubs.size() - 1);
                            //插入应收账单子表
                            billPayCustomerSub.setId(null);
                            billPayCustomerSub.setBillSubCode(ysBillCode);
                            billPayCustomerSub.setBillAmount(ysAmount);
                            billPayCustomerSub.setBillingState("0");
                            //运费账单已核销，撤箱退费金额为负，则增加余额
                            if (billPayCustomerSub.getBillAmount().compareTo(BigDecimal.ZERO) < 0 && ("VERIFIED".equals(billPayCustomerSubs.get(0).getBillingState()) || "VERIFICATION".equals(billPayCustomerSubs.get(0).getBillingState()))) {
                                billPayCustomerSub.setBillingState("VERIFIED");
                                FdBalanceDetail detail = new FdBalanceDetail();
                                detail.setPlatformCode(billPayCustomerSub.getPlatformCode());
                                detail.setCustomerCode(billPayCustomerSub.getCustomerCode());
                                detail.setCustomerName(billPayCustomerSub.getCustomerName());
                                detail.setShiftNo(billPayCustomerSub.getShiftNo());
                                detail.setCodeBbCategoriesCode("f_fee_type");
                                detail.setCodeBbCategoriesName("发运运费");
                                detail.setCodeSsCategoriesCode("f_clearing_balance");
                                detail.setCodeSsCategoriesName("结算余额");
                                detail.setPaymentType("0");
                                detail.setTotalAmount(billPayCustomerSub.getBillAmount().negate());
                                detail.setRemainingAmount(billPayCustomerSub.getBillAmount().negate());
                                detail.setPlatformLevel("0");
                                detail.setBillCode(billPayCustomerSub.getBillSubCode());
                                detail.setRemarks("");
                                detail.setAddWho(userInfo.getUserName());
                                detail.setAddWhoName(userInfo.getRealName());
                                detail.setAddTime(LocalDateTime.now());
                                fdBalanceDetailMapper.insertFdBalanceDetail(detail);

                                FdTradingDetails fdTradingDetails = new FdTradingDetails();
                                String tsIn = sysNoConfigService.genNo("TS");
                                fdTradingDetails.setUuid(UUID.randomUUID().toString());
                                fdTradingDetails.setTradeSerialNumber(tsIn);
                                fdTradingDetails.setPlatformCode(detail.getPlatformCode());
                                fdTradingDetails.setPlatformName(detail.getPlatformName());
                                fdTradingDetails.setCustomerName(detail.getCustomerName());
                                fdTradingDetails.setCustomerCode(detail.getCustomerCode());
                                fdTradingDetails.setTradingHours(LocalDateTime.now());
                                fdTradingDetails.setPaymentType("0");
                                fdTradingDetails.setTransactionAmount(billPayCustomerSub.getBillAmount());
                                fdTradingDetails.setFromBillCode(billPayCustomerSub.getBillSubCode());
                                fdTradingDetails.setTradingStatus("1");
                                fdTradingDetails.setPlatformLevel("0");
                                fdTradingDetailsMapper.insertFdTradingDetails(fdTradingDetails);
                            }
                            billPayCustomerSub.setStatus("1");
                            billPayCustomerSub.setAddWho(userInfo.getUserName());
                            billPayCustomerSub.setAddWhoName(userInfo.getRealName());
                            billPayCustomerSub.setAddTime(new Date());
                            billPayCustomerSubMapper.insertBillSubIncomeCity(billPayCustomerSub);
                            billPayCustomerSubMapper.updateBoxNumByBillSubCode(billPayCustomerSub.getBillSubCode());

                            //更新订单主表信息
                            BillPayCustomer billPayCustomer = new BillPayCustomer();
                            billPayCustomer.setBillCode(billPayCustomerSub.getBillCode());
                            billPayCustomer.setDelFlag("N");
                            List<BillPayCustomer> billPayCustomers1 = billPayCustomerMapper.selectBillIncomeCityList(billPayCustomer);
                            if (CollUtil.isNotEmpty(billPayCustomers1)) {
                                BillPayCustomer billPayCustomer1 = billPayCustomers1.get(0);
                                billPayCustomer1.setBillAmount(billPayCustomer1.getBillAmount().add(billPayCustomerSub.getBillAmount()));
                                billPayCustomerMapper.updateBillIncomeCity(billPayCustomer1);
                            }
                        }
                    } else {
                        //插入新增费用明细 应收+应付
                        insertCostDetail(basChangeboxCostDetail, fdBusCost, userInfo, ysBillCode);
                        //更新撤箱费用明细状态 应收+应付
                        //插入撤箱费用明细 负值 应收+应付
                        if (CollUtil.isNotEmpty(basChangeboxContainerInfos)) {
                            for (BasChangeboxContainerInfo info : basChangeboxContainerInfos) {
                                if ("1".equals(info.getBoxnumberStatus())) {
                                    if (CollUtil.isNotEmpty(detailList)) {
                                        for (FdBusCostDetail fdBusCostDetail : detailList) {
                                            if (info.getContainerNo().equals(fdBusCostDetail.getContainerNumber()) && fdBusCost.getCostCode().equals(fdBusCostDetail.getCostCode()) && basChangeboxContainerInfo.getCustomerNo().equals(fdBusCostDetail.getCustomerCode()) && basChangeboxContainerInfo.getMiniPlatformName().equals(fdBusCostDetail.getMiniPlatformName())) {
                                                if ("0".equals(info.getUndoType()) && !"jndtlyf".equals(fdBusCostDetail.getCodeSsCategoriesCode())) {
                                                    continue;
                                                } else if ("1".equals(info.getUndoType()) && !"jwdtlyf".equals(fdBusCostDetail.getCodeSsCategoriesCode())) {
                                                    continue;
                                                }
                                                FdBusCostDetail delObj = new FdBusCostDetail();
                                                delObj.setId(fdBusCostDetail.getId());
                                                delObj.setStatus("1");
                                                delObj.setDeleteWho(userInfo.getUserName());
                                                delObj.setDeleteWhoName(userInfo.getRealName());
                                                delObj.setDeleteTime(LocalDateTime.now());
                                                fdBusCostDetailMapper.updateFdBusCostDetail(delObj);

                                                fdBusCostDetail.setId(null);
                                                fdBusCostDetail.setLocalAmount(fdBusCostDetail.getLocalAmount().negate());
                                                fdBusCostDetail.setOriginalAmount(fdBusCostDetail.getOriginalAmount().negate());
                                                fdBusCostDetail.setStatus("1");
                                                fdBusCostDetail.setExchangeRateNew(fdBusCostDetail.getExchangeRate());
                                                fdBusCostDetail.setRemark(null);
                                                fdBusCostDetail.setAddWho(userInfo.getUserName());
                                                fdBusCostDetail.setAddWhoName(userInfo.getRealName());
                                                fdBusCostDetail.setAddTime(LocalDateTime.now());
                                                fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
                                            }
                                        }
                                    }

                                    //插入亏舱费 应收
                                    if (info.getRetreatCost() != null && info.getRetreatCost().compareTo(BigDecimal.ZERO) > 0) {
                                        //新增亏舱费
                                        FdBusCostDetail addObj = new FdBusCostDetail();
                                        addObj.setCostCode(fdBusCost.getCostCode());
                                        addObj.setCostType("0");
                                        addObj.setContainerNumber(info.getContainerNo());
                                        addObj.setCodeBbCategoriesCode("f_extra_fee_type");
                                        addObj.setCodeBbCategoriesName("额外费用");
                                        addObj.setCodeSsCategoriesCode("f_cancel_fee");
                                        addObj.setCodeSsCategoriesName("亏舱费");
                                        addObj.setReceiveCode(fdBusCost.getPlatformCode());
                                        addObj.setReceiveName(fdBusCost.getPlatformName());
                                        addObj.setPayCode(fdBusCost.getCustomerCode());
                                        addObj.setPayName(fdBusCost.getCustomerName());
                                        addObj.setCurrency("人民币");
                                        addObj.setExchangeRate(BigDecimal.valueOf(1));
                                        addObj.setExchangeRateNew(BigDecimal.valueOf(1));
                                        addObj.setOriginalAmount(info.getRetreatCost());
                                        addObj.setLocalAmount(info.getRetreatCost());
                                        addObj.setAuditStatus("1");
                                        addObj.setShiftNo(fdBusCost.getShiftNo());
                                        addObj.setBillSubCode(ysBillCode);
                                        addObj.setRemark(null);
                                        addObj.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                        addObj.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                        addObj.setAddTime(LocalDateTime.now());
                                        addObj.setAuditTime(LocalDateTime.now());
                                        fdBusCostDetailMapper.insertFdBusCostDetail(addObj);
                                    }
                                }
                            }
                        }
                    }
                } else {
                    //不存在业务流程单
                    FdBusCost fdBusCost = new FdBusCost();
                    fdBusCost.setCostCode(sysNoConfigService.genNo("FDC"));
                    fdBusCost.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                    fdBusCost.setPlatformName(basChangeboxRetreat.getPlatformName());
                    fdBusCost.setCustomerCode(basChangeboxContainerInfo.getCustomerNo());
                    fdBusCost.setPlatformLevel("0");
                    fdBusCost.setShiftNo(basChangeboxRetreat.getShiftNo());
                    fdBusCost.setAuditStatus("0");
                    fdBusCost.setAddWho(userInfo.getUserName());
                    fdBusCost.setAddWhoName(userInfo.getRealName());
                    fdBusCost.setAddTime(LocalDateTime.now());

                    CustomerInfo sel5 = new CustomerInfo();
                    sel5.setCustomerCode(basChangeboxContainerInfo.getCustomerNo());
                    sel5.setDeleteFlag("N");
                    List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoList(sel5);
                    if (CollUtil.isNotEmpty(customerInfos)) {
                        fdBusCost.setCustomerName(customerInfos.get(0).getCompanyName());
                        fdBusCost.setPrincipalName(customerInfos.get(0).getCompanyName());
//                        fdBusCost.setCustomerLiaison(customerInfos.get(0).getCompanyName());
                    }

                    CustomerPlatformInfo sel2 = new CustomerPlatformInfo();
                    sel2.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                    sel2.setCustomerCode(basChangeboxContainerInfo.getCustomerNo());
                    sel2.setDeleteFlag("N");
                    List<CustomerPlatformInfo> list = customerPlatformInfoMapper.selectCustomerPlatformInfoList(sel2);
                    if (CollUtil.isNotEmpty(list)) {
                        fdBusCost.setContactsName(list.get(0).getContactPerson());
                        fdBusCost.setContactsPhone(list.get(0).getContactNo());
//                        fdBusCost.setCustomerLiaison(list.get(0).getContactPerson());
//                        fdBusCost.setCustomerPhone(list.get(0).getContactNo());
                    } else {
                        sel2.setPlatformCode(null);
                        List<CustomerPlatformInfo> list2 = customerPlatformInfoMapper.selectCustomerPlatformInfoList(sel2);
                        if (CollUtil.isNotEmpty(list2)) {
                            fdBusCost.setContactsName(list2.get(0).getContactPerson());
                            fdBusCost.setContactsPhone(list2.get(0).getContactNo());
//                            fdBusCost.setCustomerLiaison(list2.get(0).getContactPerson());
//                            fdBusCost.setCustomerPhone(list2.get(0).getContactNo());
                        }
                    }

                    WaybillHeader sel10 = new WaybillHeader();
                    sel10.setShiftNo(basChangeboxRetreat.getShiftNo());
                    sel10.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                    sel10.setCustomerNo(basChangeboxContainerInfo.getCustomerNo());
                    sel10.setMiniPlatform(basChangeboxContainerInfo.getMiniPlatformName());
                    sel10.setDeleteFlag("N");
                    List<WaybillHeader> waybillHeaders = waybillHeaderMapper.selectWaybillHeaderList2(sel10);
                    if (CollUtil.isNotEmpty(waybillHeaders)) {
                        FdBusCostWaybill fdBusCostWaybill = new FdBusCostWaybill();
                        fdBusCostWaybill.setCostCode(fdBusCost.getCostCode());
                        fdBusCostWaybill.setWaybillNo(waybillHeaders.get(0).getWaybillNo());
                        fdBusCostWaybill.setApplicationNumber(waybillHeaders.get(0).getOrderNo());
                        fdBusCostWaybill.setAuditStatus("2");
                        fdBusCostWaybill.setAddWho(userInfo.getUserName());
                        fdBusCostWaybill.setAddWhoName(userInfo.getRealName());
                        fdBusCostWaybill.setAddTime(LocalDateTime.now());
                        fdBusCostWaybillMapper.insertFdBusCostWaybill(fdBusCostWaybill);
                    }
                    //插入新增相关费用
                    insertCostDetail(basChangeboxCostDetail, fdBusCost, userInfo, null);
                    fdBusCostMapper.insertFdBusCost(fdBusCost);
                }
            }
        }
    }

    /**
     * 市平台审核撤换箱--费用明细更新
     *
     * @Param: basChangeboxRetreat, userInfo, infos
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/07/17 14:13
     **/
    private void saveFdBusCostWithCityAudit(BasChangeboxRetreat basChangeboxRetreat, SecruityUser userInfo, List<BasChangeboxContainerInfo> infos) {
        FdBusCost sel3 = new FdBusCost();
        sel3.setShiftNo(basChangeboxRetreat.getShiftNo());
        sel3.setPlatformCode(basChangeboxRetreat.getPlatformCode());
        sel3.setCustomerCode(basChangeboxRetreat.getCustomerNo());
        sel3.setDeleteFlag("N");
        List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel3);
        if (CollUtil.isNotEmpty(fdBusCosts)) {

            //查询下级平台业务流程单
            FdBusCost sel4 = new FdBusCost();
            sel4.setShiftNo(basChangeboxRetreat.getShiftNo());
            sel4.setPlatformCode(basChangeboxRetreat.getCustomerNo());
            sel4.setDeleteFlag("N");
            List<FdBusCost> lowFdBusCosts = fdBusCostMapper.selectFdBusCostList(sel4);
            //存在业务流程单，更新业务流程单
            FdBusCost fdBusCost = fdBusCosts.get(0);
            String ysBillCode = null;

            BasChangeboxContainerInfo sel = new BasChangeboxContainerInfo();
            sel.setBusinessid(basChangeboxRetreat.getBusinessid());
            sel.setDeleteFlag("N");
            List<BasChangeboxContainerInfo> basChangeboxContainerInfos = basChangeboxContainerInfoMapper.selectBasChangeboxContainerInfoList(sel);

            BasChangeboxCostDetail sel8 = new BasChangeboxCostDetail();
            sel8.setHxAppNo(basChangeboxRetreat.getBusinessid());
            sel8.setDeleteFlag("N");
            List<BasChangeboxCostDetail> basChangeboxCostDetail = basChangeboxCostDetailMapper.selectBasChangeboxCostDetailList(sel8);

            FdBusCostDetail sel7 = new FdBusCostDetail();
            sel7.setCostCode(fdBusCost.getCostCode());
            sel7.setCodeBbCategoriesCode("f_fee_type");
            sel7.setDeleteFlag("N");
            sel7.setStatus("0");
            List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList(sel7);

            BillPayCustomerSub sel6 = new BillPayCustomerSub();
            sel6.setShiftNo(basChangeboxRetreat.getShiftNo());
            sel6.setPlatformCode(basChangeboxRetreat.getPlatformCode());
            sel6.setCustomerCode(basChangeboxRetreat.getCustomerNo());
            sel6.setDeleteFlag("N");
            sel6.setStatus("1");
            List<BillPayCustomerSub> billPayCustomerSubs = billPayCustomerSubMapper.selectBillSubIncomeCityList(sel6);
            if (CollUtil.isNotEmpty(billPayCustomerSubs)) {
                BillPayCustomerSub billPayCustomerSub = billPayCustomerSubs.get(billPayCustomerSubs.size() - 1);
                ysBillCode = CheckUtil.getNumber(billPayCustomerSub.getBillSubCode(), 3);
            }
            if (!"0".equals(fdBusCost.getAuditStatus()) && !"9".equals(fdBusCost.getAuditStatus())) {
                //业务流程单已经入审核
                /*插入新增费用明细 应收+应付
                更新下级费用明细 应付
                更新撤箱费用明细状态 应收+应付
                插入撤箱费用明细 负值 应收+应付
                插入亏舱费 应收
                插入下级亏舱费 应付
                新增应收账单*/
                BigDecimal ysAmount = BigDecimal.ZERO;

                //插入新增费用明细 应收+应付
                if (CollUtil.isNotEmpty(basChangeboxCostDetail)) {
                    for (BasChangeboxCostDetail detail : basChangeboxCostDetail) {
                        FdBusCostDetail addObj = new FdBusCostDetail();
                        BeanUtil.copyProperties(detail, addObj);
                        addObj.setId(null);
                        addObj.setCostCode(fdBusCost.getCostCode());
                        if ("0".equals(detail.getCostType())) {
                            //更新下级费用明细 应付
                            FdBusCostDetail updateObj = new FdBusCostDetail();
                            updateObj.setShiftNo(addObj.getShiftNo());
                            updateObj.setReceiveCode(addObj.getReceiveCode());
                            updateObj.setPayCode(addObj.getPayCode());
                            updateObj.setCostType("1");
                            updateObj.setContainerNumber(addObj.getContainerNumber());
                            updateObj.setCodeSsCategoriesCode(addObj.getCodeSsCategoriesCode());
                            updateObj.setCodeBbCategoriesCode(addObj.getCodeBbCategoriesCode());
                            updateObj.setCurrency(addObj.getCurrency());
                            updateObj.setExchangeRate(addObj.getExchangeRate());
                            updateObj.setLocalAmount(addObj.getLocalAmount());
                            updateObj.setOriginalAmount(addObj.getOriginalAmount());
                            updateObj.setUpdateWho(userInfo.getUserName());
                            updateObj.setUpdateWhoName(userInfo.getRealName());
                            updateObj.setUpdateTime(LocalDateTime.now());
                            int i = fdBusCostDetailMapper.updateFdBusCostDetailByCostCode(updateObj);
                            if (i == 0) {
                                updateObj.setReceiveName(addObj.getReceiveName());
                                updateObj.setPayName(addObj.getPayName());
                                updateObj.setCodeBbCategoriesName(addObj.getCodeBbCategoriesName());
                                updateObj.setCodeSsCategoriesName(addObj.getCodeSsCategoriesName());
                                updateObj.setShiftNo(addObj.getShiftNo());
                                updateObj.setAuditStatus("1");
                                updateObj.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                updateObj.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                updateObj.setAddTime(LocalDateTime.now());
                                updateObj.setAuditTime(LocalDateTime.now());
                                fdBusCostDetailMapper.insertFdBusCostDetail(updateObj);
                            }

                            addObj.setBillSubCode(ysBillCode);
                            ysAmount = ysAmount.add(detail.getLocalAmount());
                        }
                        addObj.setExchangeRateNew(addObj.getExchangeRate());
                        addObj.setRemark(null);
                        addObj.setAddWho(userInfo.getUserName());
                        addObj.setAddWhoName(userInfo.getRealName());
                        addObj.setAddTime(LocalDateTime.now());
                        fdBusCostDetailMapper.insertFdBusCostDetail(addObj);
                    }
                }
                //更新撤箱费用明细状态 应收+应付
                //插入撤箱费用明细 负值 应收+应付
                if (CollUtil.isNotEmpty(basChangeboxContainerInfos)) {
                    for (BasChangeboxContainerInfo info : basChangeboxContainerInfos) {
                        if ("1".equals(info.getBoxnumberStatus())) {
                            if (CollUtil.isNotEmpty(detailList)) {
                                for (FdBusCostDetail fdBusCostDetail : detailList) {
                                    if (info.getContainerNo().equals(fdBusCostDetail.getContainerNumber())) {
                                        if ("0".equals(info.getUndoType()) && !"jndtlyf".equals(fdBusCostDetail.getCodeSsCategoriesCode())) {
                                            continue;
                                        } else if ("1".equals(info.getUndoType()) && !"jwdtlyf".equals(fdBusCostDetail.getCodeSsCategoriesCode())) {
                                            continue;
                                        }
                                        FdBusCostDetail delObj = new FdBusCostDetail();
                                        delObj.setId(fdBusCostDetail.getId());
                                        delObj.setStatus("1");
                                        delObj.setDeleteWho(userInfo.getUserName());
                                        delObj.setDeleteWhoName(userInfo.getRealName());
                                        delObj.setDeleteTime(LocalDateTime.now());
                                        fdBusCostDetailMapper.updateFdBusCostDetail(delObj);

                                        fdBusCostDetail.setId(null);
                                        fdBusCostDetail.setExchangeRateNew(fdBusCostDetail.getExchangeRate());
                                        fdBusCostDetail.setLocalAmount(fdBusCostDetail.getLocalAmount().negate());
                                        fdBusCostDetail.setOriginalAmount(fdBusCostDetail.getOriginalAmount().negate());
                                        fdBusCostDetail.setStatus("1");
                                        fdBusCostDetail.setRemark(null);
                                        fdBusCostDetail.setAddWho(userInfo.getUserName());
                                        fdBusCostDetail.setAddWhoName(userInfo.getRealName());
                                        fdBusCostDetail.setAddTime(LocalDateTime.now());
                                        if ("0".equals(fdBusCostDetail.getCostType())) {
                                            fdBusCostDetail.setBillSubCode(ysBillCode);
                                            ysAmount = ysAmount.add(fdBusCostDetail.getLocalAmount());
                                        } else {
                                            fdBusCostDetail.setBillSubCode(null);
                                        }
                                        fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
                                    }
                                }
                            }

                            //插入亏舱费 应收
                            //插入下级亏舱费 应付
                            if (info.getRetreatCost() != null && info.getRetreatCost().compareTo(BigDecimal.ZERO) > 0) {
                                ysAmount = ysAmount.add(info.getRetreatCost());
                                //新增亏舱费
                                FdBusCostDetail addObj = new FdBusCostDetail();
                                addObj.setCostCode(fdBusCost.getCostCode());
                                addObj.setCostType("0");
                                addObj.setContainerNumber(info.getContainerNo());
                                addObj.setCodeBbCategoriesCode("f_extra_fee_type");
                                addObj.setCodeBbCategoriesName("额外费用");
                                addObj.setCodeSsCategoriesCode("f_cancel_fee");
                                addObj.setCodeSsCategoriesName("亏舱费");
                                addObj.setReceiveCode(fdBusCost.getPlatformCode());
                                addObj.setReceiveName(fdBusCost.getPlatformName());
                                addObj.setPayCode(fdBusCost.getCustomerCode());
                                addObj.setPayName(fdBusCost.getCustomerName());
                                addObj.setCurrency("人民币");
                                addObj.setExchangeRate(BigDecimal.valueOf(1));
                                addObj.setExchangeRateNew(BigDecimal.valueOf(1));
                                addObj.setOriginalAmount(info.getRetreatCost());
                                addObj.setLocalAmount(info.getRetreatCost());
                                addObj.setAuditStatus("1");
                                addObj.setShiftNo(fdBusCost.getShiftNo());
                                addObj.setBillSubCode(ysBillCode);
                                addObj.setRemark(null);
                                addObj.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                addObj.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                addObj.setAddTime(LocalDateTime.now());
                                addObj.setAuditTime(LocalDateTime.now());
                                fdBusCostDetailMapper.insertFdBusCostDetail(addObj);

                                if (CollUtil.isNotEmpty(lowFdBusCosts)) {
                                    FdBusCostDetail lowObj = new FdBusCostDetail();
                                    lowObj.setCostCode(lowFdBusCosts.get(0).getCostCode());
                                    lowObj.setCostType("1");
                                    lowObj.setContainerNumber(info.getContainerNo());
                                    lowObj.setReceiveCode(fdBusCost.getPlatformCode());
                                    lowObj.setReceiveName(fdBusCost.getPlatformName());
                                    lowObj.setPayCode(fdBusCost.getCustomerCode());
                                    lowObj.setPayName(fdBusCost.getCustomerName());
                                    lowObj.setCodeBbCategoriesCode("f_extra_fee_type");
                                    lowObj.setCodeBbCategoriesName("额外费用");
                                    lowObj.setCodeSsCategoriesCode("f_cancel_fee");
                                    lowObj.setCodeSsCategoriesName("亏舱费");
                                    lowObj.setCurrency("人民币");
                                    lowObj.setExchangeRate(BigDecimal.valueOf(1));
                                    lowObj.setExchangeRateNew(BigDecimal.valueOf(1));
                                    lowObj.setOriginalAmount(info.getRetreatCost());
                                    lowObj.setLocalAmount(info.getRetreatCost());
                                    lowObj.setAuditStatus("1");
                                    lowObj.setRemark(null);
                                    lowObj.setShiftNo(basChangeboxRetreat.getShiftNo());
                                    lowObj.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                    lowObj.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                    lowObj.setAddTime(LocalDateTime.now());
                                    lowObj.setAuditTime(LocalDateTime.now());
                                    fdBusCostDetailMapper.insertFdBusCostDetail(lowObj);
                                }
                            }
                        }
                    }
                }
                if (CollUtil.isNotEmpty(billPayCustomerSubs)) {
                    BillPayCustomerSub billPayCustomerSub = billPayCustomerSubs.get(billPayCustomerSubs.size() - 1);
                    //插入应收账单子表
                    billPayCustomerSub.setId(null);
                    billPayCustomerSub.setBillSubCode(ysBillCode);
                    billPayCustomerSub.setBillAmount(ysAmount);
                    billPayCustomerSub.setBillingState("0");
                    //运费账单已核销，撤箱退费金额为负，则增加余额
                    if (billPayCustomerSub.getBillAmount().compareTo(BigDecimal.ZERO) < 0 && ("VERIFIED".equals(billPayCustomerSubs.get(0).getBillingState()) || "VERIFICATION".equals(billPayCustomerSubs.get(0).getBillingState()))) {
                        billPayCustomerSub.setBillingState("VERIFIED");
                        FdBalanceDetail detail = new FdBalanceDetail();
                        detail.setPlatformCode(billPayCustomerSub.getPlatformCode());
                        detail.setCustomerCode(billPayCustomerSub.getCustomerCode());
                        detail.setCustomerName(billPayCustomerSub.getCustomerName());
                        detail.setShiftNo(billPayCustomerSub.getShiftNo());
                        detail.setCodeBbCategoriesCode("f_fee_type");
                        detail.setCodeBbCategoriesName("发运运费");
                        detail.setCodeSsCategoriesCode("f_clearing_balance");
                        detail.setCodeSsCategoriesName("结算余额");
                        detail.setPaymentType("0");
                        detail.setTotalAmount(billPayCustomerSub.getBillAmount().negate());
                        detail.setRemainingAmount(billPayCustomerSub.getBillAmount().negate());
                        detail.setPlatformLevel("0");
                        detail.setBillCode(billPayCustomerSub.getBillSubCode());
                        detail.setRemarks("");
                        detail.setAddWho(userInfo.getUserName());
                        detail.setAddWhoName(userInfo.getRealName());
                        detail.setAddTime(LocalDateTime.now());
                        fdBalanceDetailMapper.insertFdBalanceDetail(detail);

                        FdTradingDetails fdTradingDetails = new FdTradingDetails();
                        String tsIn = sysNoConfigService.genNo("TS");
                        fdTradingDetails.setUuid(UUID.randomUUID().toString());
                        fdTradingDetails.setTradeSerialNumber(tsIn);
                        fdTradingDetails.setPlatformCode(detail.getPlatformCode());
                        fdTradingDetails.setPlatformName(detail.getPlatformName());
                        fdTradingDetails.setCustomerName(detail.getCustomerName());
                        fdTradingDetails.setCustomerCode(detail.getCustomerCode());
                        fdTradingDetails.setTradingHours(LocalDateTime.now());
                        fdTradingDetails.setPaymentType("0");
                        fdTradingDetails.setTransactionAmount(billPayCustomerSub.getBillAmount());
                        fdTradingDetails.setFromBillCode(billPayCustomerSub.getBillSubCode());
                        fdTradingDetails.setTradingStatus("1");
                        fdTradingDetails.setPlatformLevel("0");
                        fdTradingDetailsMapper.insertFdTradingDetails(fdTradingDetails);
                    }
                    billPayCustomerSub.setStatus("1");
                    billPayCustomerSub.setAddWho(userInfo.getUserName());
                    billPayCustomerSub.setAddWhoName(userInfo.getRealName());
                    billPayCustomerSub.setAddTime(new Date());
                    billPayCustomerSubMapper.insertBillSubIncomeCity(billPayCustomerSub);
                    billPayCustomerSubMapper.updateBoxNumByBillSubCode(billPayCustomerSub.getBillSubCode());

                    //更新订单主表信息
                    BillPayCustomer billPayCustomer = new BillPayCustomer();
                    billPayCustomer.setBillCode(billPayCustomerSub.getBillCode());
                    billPayCustomer.setDelFlag("N");
                    List<BillPayCustomer> billPayCustomers1 = billPayCustomerMapper.selectBillIncomeCityList(billPayCustomer);
                    if (CollUtil.isNotEmpty(billPayCustomers1)) {
                        BillPayCustomer billPayCustomer1 = billPayCustomers1.get(0);
                        billPayCustomer1.setBillAmount(billPayCustomer1.getBillAmount().add(billPayCustomerSub.getBillAmount()));
                        billPayCustomerMapper.updateBillIncomeCity(billPayCustomer1);
                    }
                }

            } else {
                //业务流程单未审核--只更新业务流程单数据，不更新账单和下级数据
                //插入新增费用明细 应收+应付
                if (CollUtil.isNotEmpty(basChangeboxCostDetail)) {
                    for (BasChangeboxCostDetail detail : basChangeboxCostDetail) {
                        FdBusCostDetail addObj = new FdBusCostDetail();
                        BeanUtil.copyProperties(detail, addObj);
                        addObj.setId(null);
                        addObj.setCostCode(fdBusCost.getCostCode());
                        addObj.setExchangeRateNew(addObj.getExchangeRate());
                        addObj.setRemark(null);
                        addObj.setAddWho(userInfo.getUserName());
                        addObj.setAddWhoName(userInfo.getRealName());
                        addObj.setAddTime(LocalDateTime.now());
                        fdBusCostDetailMapper.insertFdBusCostDetail(addObj);
                    }
                }
                //更新撤箱费用明细状态 应收+应付
                //插入撤箱费用明细 负值 应收+应付
                if (CollUtil.isNotEmpty(basChangeboxContainerInfos)) {
                    for (BasChangeboxContainerInfo info : basChangeboxContainerInfos) {
                        if ("1".equals(info.getBoxnumberStatus())) {
                            if (CollUtil.isNotEmpty(detailList)) {
                                for (FdBusCostDetail fdBusCostDetail : detailList) {
                                    if (info.getContainerNo().equals(fdBusCostDetail.getContainerNumber())) {
                                        FdBusCostDetail delObj = new FdBusCostDetail();
                                        delObj.setId(fdBusCostDetail.getId());
                                        delObj.setStatus("1");
                                        delObj.setDeleteWho(userInfo.getUserName());
                                        delObj.setDeleteWhoName(userInfo.getRealName());
                                        delObj.setDeleteTime(LocalDateTime.now());
                                        fdBusCostDetailMapper.updateFdBusCostDetail(delObj);

                                        fdBusCostDetail.setId(null);
                                        fdBusCostDetail.setLocalAmount(fdBusCostDetail.getLocalAmount().negate());
                                        fdBusCostDetail.setOriginalAmount(fdBusCostDetail.getOriginalAmount().negate());
                                        fdBusCostDetail.setStatus("1");
                                        fdBusCostDetail.setExchangeRateNew(fdBusCostDetail.getExchangeRate());
                                        fdBusCostDetail.setRemark(null);
                                        fdBusCostDetail.setAddWho(userInfo.getUserName());
                                        fdBusCostDetail.setAddWhoName(userInfo.getRealName());
                                        fdBusCostDetail.setAddTime(LocalDateTime.now());
                                        if ("0".equals(fdBusCostDetail.getCostType())) {
                                            fdBusCostDetail.setBillSubCode(ysBillCode);
                                        } else {
                                            fdBusCostDetail.setBillSubCode(null);
                                        }
                                        fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
                                    }
                                }
                            }

                            //插入亏舱费 应收
                            if (info.getRetreatCost() != null && info.getRetreatCost().compareTo(BigDecimal.ZERO) > 0) {
                                //新增亏舱费
                                FdBusCostDetail addObj = new FdBusCostDetail();
                                addObj.setCostCode(fdBusCost.getCostCode());
                                addObj.setCostType("0");
                                addObj.setContainerNumber(info.getContainerNo());
                                addObj.setCodeBbCategoriesCode("f_extra_fee_type");
                                addObj.setCodeBbCategoriesName("额外费用");
                                addObj.setCodeSsCategoriesCode("f_cancel_fee");
                                addObj.setCodeSsCategoriesName("亏舱费");
                                addObj.setReceiveCode(fdBusCost.getPlatformCode());
                                addObj.setReceiveName(fdBusCost.getPlatformName());
                                addObj.setPayCode(fdBusCost.getCustomerCode());
                                addObj.setPayName(fdBusCost.getCustomerName());
                                addObj.setCurrency("人民币");
                                addObj.setExchangeRate(BigDecimal.valueOf(1));
                                addObj.setExchangeRateNew(BigDecimal.valueOf(1));
                                addObj.setOriginalAmount(info.getRetreatCost());
                                addObj.setLocalAmount(info.getRetreatCost());
                                addObj.setAuditStatus("1");
                                addObj.setShiftNo(fdBusCost.getShiftNo());
                                addObj.setBillSubCode(ysBillCode);
                                addObj.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                addObj.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                addObj.setAddTime(LocalDateTime.now());
                                addObj.setAuditTime(LocalDateTime.now());
                                fdBusCostDetailMapper.insertFdBusCostDetail(addObj);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 市平台审核撤换箱--费用明细更新
     * 适配小客户，拆分客户应付账逻辑
     *
     * @Param: basChangeboxRetreat, userInfo, infos
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/07/17 14:13
     **/
    private void saveFdBusCostWithCityAudit2(BasChangeboxRetreat basChangeboxRetreat, SecruityUser userInfo, List<BasChangeboxContainerInfo> infos, List<String> miniPlatformNames) {
        FdBusCost sel3 = new FdBusCost();
        sel3.setShiftNo(basChangeboxRetreat.getShiftNo());
        sel3.setPlatformCode(basChangeboxRetreat.getPlatformCode());
        sel3.setCustomerCode(basChangeboxRetreat.getCustomerNo());
        sel3.setDeleteFlag("N");
        List<FdBusCost> fdBusCosts = fdBusCostMapper.selectFdBusCostList(sel3);
        if (CollUtil.isNotEmpty(fdBusCosts)) {
            FdBusCost fdBusCost = fdBusCosts.get(0);
            if (!"0".equals(fdBusCost.getAuditStatus()) && !"9".equals(fdBusCost.getAuditStatus())) {
                //业务流程单已经入审核
                /*插入新增费用明细 应收+应付
                更新下级费用明细 应付
                更新撤箱费用明细状态 应收+应付
                插入撤箱费用明细 负值 应收+应付
                插入亏舱费 应收
                插入下级亏舱费 应付
                新增应收账单*/
                FdBusCostDetail sel7 = new FdBusCostDetail();
                sel7.setCostCode(fdBusCost.getCostCode());
                sel7.setCodeBbCategoriesCode("f_fee_type");
                sel7.setDeleteFlag("N");
                sel7.setStatus("0");
                List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList(sel7);

                //查询下级平台业务流程单
                FdBusCost sel4 = new FdBusCost();
                sel4.setShiftNo(basChangeboxRetreat.getShiftNo());
                sel4.setPlatformCode(basChangeboxRetreat.getCustomerNo());
                sel4.setDeleteFlag("N");
                List<FdBusCost> lowFdBusCosts = fdBusCostMapper.selectFdBusCostList(sel4);

                //获取箱对应单位名称，分别执行逻辑
//                List<String> miniPlatformNames = basChangeboxContainerInfoMapper.getMiniPlatformName(basChangeboxRetreat.getBusinessid());
                if (CollUtil.isNotEmpty(miniPlatformNames)) {
                    for (String miniPlatformName : miniPlatformNames) {
                        BigDecimal ysAmount = BigDecimal.ZERO;
                        String ysBillCode = null;

                        BillPayCustomerSub sel6 = new BillPayCustomerSub();
                        sel6.setShiftNo(basChangeboxRetreat.getShiftNo());
                        sel6.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                        sel6.setCustomerCode(basChangeboxRetreat.getCustomerNo());
                        sel6.setMiniPlatformName(miniPlatformName);
                        sel6.setDeleteFlag("N");
                        sel6.setStatus("1");
                        List<BillPayCustomerSub> billPayCustomerSubs = billPayCustomerSubMapper.selectBillSubIncomeCityList2(sel6);
                        if (CollUtil.isNotEmpty(billPayCustomerSubs)) {
                            BillPayCustomerSub billPayCustomerSub = billPayCustomerSubs.get(billPayCustomerSubs.size() - 1);
                            ysBillCode = CheckUtil.getNumber(billPayCustomerSub.getBillSubCode(), 3);
                        }

                        BasChangeboxCostDetail sel8 = new BasChangeboxCostDetail();
                        sel8.setHxAppNo(basChangeboxRetreat.getBusinessid());
                        sel8.setMiniPlatformName(miniPlatformName);
                        sel8.setDeleteFlag("N");
                        List<BasChangeboxCostDetail> basChangeboxCostDetail = basChangeboxCostDetailMapper.selectBasChangeboxCostDetailList2(sel8);

                        BasChangeboxContainerInfo sel = new BasChangeboxContainerInfo();
                        sel.setBusinessid(basChangeboxRetreat.getBusinessid());
                        sel.setMiniPlatformName(miniPlatformName);
                        sel.setDeleteFlag("N");
                        List<BasChangeboxContainerInfo> basChangeboxContainerInfos = basChangeboxContainerInfoMapper.selectBasChangeboxContainerInfoList2(sel);

                        //插入新增费用明细 应收+应付
                        if (CollUtil.isNotEmpty(basChangeboxCostDetail)) {
                            for (BasChangeboxCostDetail detail : basChangeboxCostDetail) {
                                FdBusCostDetail addObj = new FdBusCostDetail();
                                BeanUtil.copyProperties(detail, addObj);
                                addObj.setId(null);
                                addObj.setCostCode(fdBusCost.getCostCode());
                                if ("0".equals(detail.getCostType())) {
                                    //更新下级费用明细 应付
                                    FdBusCostDetail updateObj = new FdBusCostDetail();
                                    updateObj.setShiftNo(addObj.getShiftNo());
                                    updateObj.setReceiveCode(addObj.getReceiveCode());
                                    updateObj.setPayCode(addObj.getPayCode());
                                    updateObj.setCostType("1");
                                    updateObj.setContainerNumber(addObj.getContainerNumber());
                                    updateObj.setCodeSsCategoriesCode(addObj.getCodeSsCategoriesCode());
                                    updateObj.setCodeBbCategoriesCode(addObj.getCodeBbCategoriesCode());
                                    updateObj.setCurrency(addObj.getCurrency());
                                    updateObj.setExchangeRate(addObj.getExchangeRate());
                                    updateObj.setLocalAmount(addObj.getLocalAmount());
                                    updateObj.setOriginalAmount(addObj.getOriginalAmount());
                                    updateObj.setUpdateWho(userInfo.getUserName());
                                    updateObj.setUpdateWhoName(userInfo.getRealName());
                                    updateObj.setUpdateTime(LocalDateTime.now());
                                    int i = fdBusCostDetailMapper.updateFdBusCostDetailByCostCode(updateObj);
                                    if (i == 0) {
                                        updateObj.setReceiveName(addObj.getReceiveName());
                                        updateObj.setPayName(addObj.getPayName());
                                        updateObj.setCodeBbCategoriesName(addObj.getCodeBbCategoriesName());
                                        updateObj.setCodeSsCategoriesName(addObj.getCodeSsCategoriesName());
                                        updateObj.setShiftNo(addObj.getShiftNo());
                                        updateObj.setAuditStatus("1");
                                        updateObj.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                        updateObj.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                        updateObj.setAddTime(LocalDateTime.now());
                                        updateObj.setAuditTime(LocalDateTime.now());
                                        fdBusCostDetailMapper.insertFdBusCostDetail(updateObj);
                                    }

                                    addObj.setBillSubCode(ysBillCode);
                                    ysAmount = ysAmount.add(detail.getLocalAmount());
                                }
                                addObj.setExchangeRateNew(addObj.getExchangeRate());
                                addObj.setRemark(null);
                                addObj.setAddWho(userInfo.getUserName());
                                addObj.setAddWhoName(userInfo.getRealName());
                                addObj.setAddTime(LocalDateTime.now());
                                fdBusCostDetailMapper.insertFdBusCostDetail(addObj);
                            }
                        }

                        //更新撤箱费用明细状态 应收+应付
                        //插入撤箱费用明细 负值 应收+应付
                        if (CollUtil.isNotEmpty(basChangeboxContainerInfos)) {
                            for (BasChangeboxContainerInfo info : basChangeboxContainerInfos) {
                                if ("1".equals(info.getBoxnumberStatus())) {
                                    if (CollUtil.isNotEmpty(detailList)) {
                                        for (FdBusCostDetail fdBusCostDetail : detailList) {
                                            if (info.getContainerNo().equals(fdBusCostDetail.getContainerNumber())) {
                                                if ("0".equals(info.getUndoType()) && !"jndtlyf".equals(fdBusCostDetail.getCodeSsCategoriesCode())) {
                                                    continue;
                                                } else if ("1".equals(info.getUndoType()) && !"jwdtlyf".equals(fdBusCostDetail.getCodeSsCategoriesCode())) {
                                                    continue;
                                                }
                                                FdBusCostDetail delObj = new FdBusCostDetail();
                                                delObj.setId(fdBusCostDetail.getId());
                                                delObj.setStatus("1");
                                                delObj.setDeleteWho(userInfo.getUserName());
                                                delObj.setDeleteWhoName(userInfo.getRealName());
                                                delObj.setDeleteTime(LocalDateTime.now());
                                                fdBusCostDetailMapper.updateFdBusCostDetail(delObj);

                                                fdBusCostDetail.setId(null);
                                                fdBusCostDetail.setExchangeRateNew(fdBusCostDetail.getExchangeRate());
                                                fdBusCostDetail.setLocalAmount(fdBusCostDetail.getLocalAmount().negate());
                                                fdBusCostDetail.setOriginalAmount(fdBusCostDetail.getOriginalAmount().negate());
                                                fdBusCostDetail.setStatus("1");
                                                fdBusCostDetail.setRemark(null);
                                                fdBusCostDetail.setAddWho(userInfo.getUserName());
                                                fdBusCostDetail.setAddWhoName(userInfo.getRealName());
                                                fdBusCostDetail.setAddTime(LocalDateTime.now());
                                                if ("0".equals(fdBusCostDetail.getCostType())) {
                                                    fdBusCostDetail.setBillSubCode(ysBillCode);
                                                    ysAmount = ysAmount.add(fdBusCostDetail.getLocalAmount());
                                                } else {
                                                    fdBusCostDetail.setBillSubCode(null);
                                                }
                                                fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
                                            }
                                        }
                                    }

                                    //插入亏舱费 应收
                                    //插入下级亏舱费 应付
                                    if (info.getRetreatCost() != null && info.getRetreatCost().compareTo(BigDecimal.ZERO) > 0) {
                                        ysAmount = ysAmount.add(info.getRetreatCost());
                                        //新增亏舱费
                                        FdBusCostDetail addObj = new FdBusCostDetail();
                                        addObj.setCostCode(fdBusCost.getCostCode());
                                        addObj.setCostType("0");
                                        addObj.setContainerNumber(info.getContainerNo());
                                        addObj.setCodeBbCategoriesCode("f_extra_fee_type");
                                        addObj.setCodeBbCategoriesName("额外费用");
                                        addObj.setCodeSsCategoriesCode("f_cancel_fee");
                                        addObj.setCodeSsCategoriesName("亏舱费");
                                        addObj.setReceiveCode(fdBusCost.getPlatformCode());
                                        addObj.setReceiveName(fdBusCost.getPlatformName());
                                        addObj.setPayCode(fdBusCost.getCustomerCode());
                                        addObj.setPayName(fdBusCost.getCustomerName());
                                        addObj.setCurrency("人民币");
                                        addObj.setExchangeRate(BigDecimal.valueOf(1));
                                        addObj.setExchangeRateNew(BigDecimal.valueOf(1));
                                        addObj.setOriginalAmount(info.getRetreatCost());
                                        addObj.setLocalAmount(info.getRetreatCost());
                                        addObj.setAuditStatus("1");
                                        addObj.setShiftNo(fdBusCost.getShiftNo());
                                        addObj.setBillSubCode(ysBillCode);
                                        addObj.setRemark(null);
                                        addObj.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                        addObj.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                        addObj.setAddTime(LocalDateTime.now());
                                        addObj.setAuditTime(LocalDateTime.now());
                                        fdBusCostDetailMapper.insertFdBusCostDetail(addObj);

                                        if (CollUtil.isNotEmpty(lowFdBusCosts)) {
                                            FdBusCostDetail lowObj = new FdBusCostDetail();
                                            lowObj.setCostCode(lowFdBusCosts.get(0).getCostCode());
                                            lowObj.setCostType("1");
                                            lowObj.setContainerNumber(info.getContainerNo());
                                            lowObj.setReceiveCode(fdBusCost.getPlatformCode());
                                            lowObj.setReceiveName(fdBusCost.getPlatformName());
                                            lowObj.setPayCode(fdBusCost.getCustomerCode());
                                            lowObj.setPayName(fdBusCost.getCustomerName());
                                            lowObj.setCodeBbCategoriesCode("f_extra_fee_type");
                                            lowObj.setCodeBbCategoriesName("额外费用");
                                            lowObj.setCodeSsCategoriesCode("f_cancel_fee");
                                            lowObj.setCodeSsCategoriesName("亏舱费");
                                            lowObj.setCurrency("人民币");
                                            lowObj.setExchangeRate(BigDecimal.valueOf(1));
                                            lowObj.setExchangeRateNew(BigDecimal.valueOf(1));
                                            lowObj.setOriginalAmount(info.getRetreatCost());
                                            lowObj.setLocalAmount(info.getRetreatCost());
                                            lowObj.setAuditStatus("1");
                                            lowObj.setRemark(null);
                                            lowObj.setShiftNo(basChangeboxRetreat.getShiftNo());
                                            lowObj.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                            lowObj.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                            lowObj.setAddTime(LocalDateTime.now());
                                            lowObj.setAuditTime(LocalDateTime.now());
                                            fdBusCostDetailMapper.insertFdBusCostDetail(lowObj);
                                        }
                                    }
                                }
                            }
                        }

                        if (CollUtil.isNotEmpty(billPayCustomerSubs)) {
                            BillPayCustomerSub billPayCustomerSub = billPayCustomerSubs.get(billPayCustomerSubs.size() - 1);
                            //插入应收账单子表
                            billPayCustomerSub.setId(null);
                            billPayCustomerSub.setBillSubCode(ysBillCode);
                            billPayCustomerSub.setBillAmount(ysAmount);
                            billPayCustomerSub.setBillingState("0");
                            //运费账单已核销，撤箱退费金额为负，则增加余额
                            if (billPayCustomerSub.getBillAmount().compareTo(BigDecimal.ZERO) < 0 && ("VERIFIED".equals(billPayCustomerSubs.get(0).getBillingState()) || "VERIFICATION".equals(billPayCustomerSubs.get(0).getBillingState()))) {
                                billPayCustomerSub.setBillingState("VERIFIED");
                                FdBalanceDetail detail = new FdBalanceDetail();
                                detail.setPlatformCode(billPayCustomerSub.getPlatformCode());
                                detail.setCustomerCode(billPayCustomerSub.getCustomerCode());
                                detail.setCustomerName(billPayCustomerSub.getCustomerName());
                                detail.setShiftNo(billPayCustomerSub.getShiftNo());
                                detail.setCodeBbCategoriesCode("f_fee_type");
                                detail.setCodeBbCategoriesName("发运运费");
                                detail.setCodeSsCategoriesCode("f_clearing_balance");
                                detail.setCodeSsCategoriesName("结算余额");
                                detail.setPaymentType("0");
                                detail.setTotalAmount(billPayCustomerSub.getBillAmount().negate());
                                detail.setRemainingAmount(billPayCustomerSub.getBillAmount().negate());
                                detail.setPlatformLevel("0");
                                detail.setBillCode(billPayCustomerSub.getBillSubCode());
                                detail.setRemarks("");
                                detail.setAddWho(userInfo.getUserName());
                                detail.setAddWhoName(userInfo.getRealName());
                                detail.setAddTime(LocalDateTime.now());
                                fdBalanceDetailMapper.insertFdBalanceDetail(detail);

                                FdTradingDetails fdTradingDetails = new FdTradingDetails();
                                String tsIn = sysNoConfigService.genNo("TS");
                                fdTradingDetails.setUuid(UUID.randomUUID().toString());
                                fdTradingDetails.setTradeSerialNumber(tsIn);
                                fdTradingDetails.setPlatformCode(detail.getPlatformCode());
                                fdTradingDetails.setPlatformName(detail.getPlatformName());
                                fdTradingDetails.setCustomerName(detail.getCustomerName());
                                fdTradingDetails.setCustomerCode(detail.getCustomerCode());
                                fdTradingDetails.setTradingHours(LocalDateTime.now());
                                fdTradingDetails.setPaymentType("0");
                                fdTradingDetails.setTransactionAmount(billPayCustomerSub.getBillAmount());
                                fdTradingDetails.setFromBillCode(billPayCustomerSub.getBillSubCode());
                                fdTradingDetails.setTradingStatus("1");
                                fdTradingDetails.setPlatformLevel("0");
                                fdTradingDetailsMapper.insertFdTradingDetails(fdTradingDetails);
                            }
                            billPayCustomerSub.setStatus("1");
                            billPayCustomerSub.setAddWho(userInfo.getUserName());
                            billPayCustomerSub.setAddWhoName(userInfo.getRealName());
                            billPayCustomerSub.setAddTime(new Date());
                            billPayCustomerSubMapper.insertBillSubIncomeCity(billPayCustomerSub);
                            billPayCustomerSubMapper.updateBoxNumByBillSubCode(billPayCustomerSub.getBillSubCode());
                            //更新订单主表信息
                            BillPayCustomer billPayCustomer = new BillPayCustomer();
                            billPayCustomer.setBillCode(billPayCustomerSub.getBillCode());
                            billPayCustomer.setDelFlag("N");
                            List<BillPayCustomer> billPayCustomers1 = billPayCustomerMapper.selectBillIncomeCityList(billPayCustomer);
                            if (CollUtil.isNotEmpty(billPayCustomers1)) {
                                BillPayCustomer billPayCustomer1 = billPayCustomers1.get(0);
                                billPayCustomer1.setBillAmount(billPayCustomer1.getBillAmount().add(billPayCustomerSub.getBillAmount()));
                                billPayCustomerMapper.updateBillIncomeCity(billPayCustomer1);
                            }
                        }
                    }
                }

            } else {
                //业务流程单未审核--只更新业务流程单数据，不更新账单和下级数据
                BasChangeboxContainerInfo sel = new BasChangeboxContainerInfo();
                sel.setBusinessid(basChangeboxRetreat.getBusinessid());
                sel.setDeleteFlag("N");
                List<BasChangeboxContainerInfo> basChangeboxContainerInfos = basChangeboxContainerInfoMapper.selectBasChangeboxContainerInfoList(sel);

                BasChangeboxCostDetail sel8 = new BasChangeboxCostDetail();
                sel8.setHxAppNo(basChangeboxRetreat.getBusinessid());
                sel8.setDeleteFlag("N");
                List<BasChangeboxCostDetail> basChangeboxCostDetail = basChangeboxCostDetailMapper.selectBasChangeboxCostDetailList(sel8);

                FdBusCostDetail sel7 = new FdBusCostDetail();
                sel7.setCostCode(fdBusCost.getCostCode());
                sel7.setCodeBbCategoriesCode("f_fee_type");
                sel7.setDeleteFlag("N");
                sel7.setStatus("0");
                List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList(sel7);


                //插入新增费用明细 应收+应付
                if (CollUtil.isNotEmpty(basChangeboxCostDetail)) {
                    for (BasChangeboxCostDetail detail : basChangeboxCostDetail) {
                        FdBusCostDetail addObj = new FdBusCostDetail();
                        BeanUtil.copyProperties(detail, addObj);
                        addObj.setId(null);
                        addObj.setCostCode(fdBusCost.getCostCode());
                        addObj.setExchangeRateNew(addObj.getExchangeRate());
                        addObj.setRemark(null);
                        addObj.setAddWho(userInfo.getUserName());
                        addObj.setAddWhoName(userInfo.getRealName());
                        addObj.setAddTime(LocalDateTime.now());
                        fdBusCostDetailMapper.insertFdBusCostDetail(addObj);
                    }
                }
                //更新撤箱费用明细状态 应收+应付
                //插入撤箱费用明细 负值 应收+应付
                if (CollUtil.isNotEmpty(basChangeboxContainerInfos)) {
                    for (BasChangeboxContainerInfo info : basChangeboxContainerInfos) {
                        if ("1".equals(info.getBoxnumberStatus())) {
                            if (CollUtil.isNotEmpty(detailList)) {
                                for (FdBusCostDetail fdBusCostDetail : detailList) {
                                    if (info.getContainerNo().equals(fdBusCostDetail.getContainerNumber())) {
                                        FdBusCostDetail delObj = new FdBusCostDetail();
                                        delObj.setId(fdBusCostDetail.getId());
                                        delObj.setStatus("1");
                                        delObj.setDeleteWho(userInfo.getUserName());
                                        delObj.setDeleteWhoName(userInfo.getRealName());
                                        delObj.setDeleteTime(LocalDateTime.now());
                                        fdBusCostDetailMapper.updateFdBusCostDetail(delObj);

                                        fdBusCostDetail.setId(null);
                                        fdBusCostDetail.setLocalAmount(fdBusCostDetail.getLocalAmount().negate());
                                        fdBusCostDetail.setOriginalAmount(fdBusCostDetail.getOriginalAmount().negate());
                                        fdBusCostDetail.setStatus("1");
                                        fdBusCostDetail.setExchangeRateNew(fdBusCostDetail.getExchangeRate());
                                        fdBusCostDetail.setRemark(null);
                                        fdBusCostDetail.setAddWho(userInfo.getUserName());
                                        fdBusCostDetail.setAddWhoName(userInfo.getRealName());
                                        fdBusCostDetail.setAddTime(LocalDateTime.now());
                                        fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
                                    }
                                }
                            }

                            //插入亏舱费 应收
                            if (info.getRetreatCost() != null && info.getRetreatCost().compareTo(BigDecimal.ZERO) > 0) {
                                //新增亏舱费
                                FdBusCostDetail addObj = new FdBusCostDetail();
                                addObj.setCostCode(fdBusCost.getCostCode());
                                addObj.setCostType("0");
                                addObj.setContainerNumber(info.getContainerNo());
                                addObj.setCodeBbCategoriesCode("f_extra_fee_type");
                                addObj.setCodeBbCategoriesName("额外费用");
                                addObj.setCodeSsCategoriesCode("f_cancel_fee");
                                addObj.setCodeSsCategoriesName("亏舱费");
                                addObj.setReceiveCode(fdBusCost.getPlatformCode());
                                addObj.setReceiveName(fdBusCost.getPlatformName());
                                addObj.setPayCode(fdBusCost.getCustomerCode());
                                addObj.setPayName(fdBusCost.getCustomerName());
                                addObj.setCurrency("人民币");
                                addObj.setExchangeRate(BigDecimal.valueOf(1));
                                addObj.setExchangeRateNew(BigDecimal.valueOf(1));
                                addObj.setOriginalAmount(info.getRetreatCost());
                                addObj.setLocalAmount(info.getRetreatCost());
                                addObj.setAuditStatus("1");
                                addObj.setShiftNo(fdBusCost.getShiftNo());
                                addObj.setAddWho(SecurityUtils.getUserInfo().getUserName());
                                addObj.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                                addObj.setAddTime(LocalDateTime.now());
                                addObj.setAuditTime(LocalDateTime.now());
                                fdBusCostDetailMapper.insertFdBusCostDetail(addObj);
                            }
                        }
                    }
                }
            }
        }
    }

    private void saveWayBillWithCitySubmit(BasChangeboxRetreat basChangeboxRetreat, SecruityUser userInfo, List<BasChangeboxContainerInfo> infos, List<Shifmanagement> shifmanagements, List<String> customerNos) {
        if (CollUtil.isNotEmpty(customerNos)) {
            for (String customerNo : customerNos) {
                WaybillHeader sel1 = new WaybillHeader();
                sel1.setShiftNo(basChangeboxRetreat.getShiftNo());
                sel1.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                sel1.setCustomerNo(customerNo);
                sel1.setDeleteFlag("N");
                List<WaybillHeader> waybillHeaders = waybillHeaderMapper.selectWaybillHeaderList(sel1);
                if (CollUtil.isNotEmpty(waybillHeaders)) {
                    //新增箱属于已有的订舱客户
                    for (BasChangeboxContainerInfo info : infos) {
                        if ("1".equals(info.getBoxnumberStatus()) && "2".equals(info.getUndoType())) {
                            //撤箱&&撤销全部运费
                            if (customerNo.equals(info.getCustomerNo())) {
                                WaybillContainerInfo delObj = new WaybillContainerInfo();
                                delObj.setContainerNo(info.getContainerNo());
                                delObj.setShiftNo(basChangeboxRetreat.getShiftNo());
                                delObj.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                                delObj.setOperFlag("C");
                                delObj.setDeleteFlag("Y");
                                delObj.setDeleteWho(userInfo.getUserName());
                                delObj.setDeleteWhoName(userInfo.getRealName());
                                delObj.setDeleteTime(new Date());
                                waybillContainerInfoMapper.updateContainerInfo1(delObj);
                                waybillGoodsInfoMapper.deleteByBussinessId(basChangeboxRetreat.getBusinessid(), info.getContainerNo());
                                waybillParticipantsMapper.deleteByBussinessId(basChangeboxRetreat.getBusinessid(), info.getContainerNo());
                                payCodeMesMapper.deleteByBussinessId(basChangeboxRetreat.getBusinessid(), info.getContainerNo());

                                if (StrUtil.isNotBlank(waybillHeaders.get(0).getOrderNo())) {
                                    BookingRequesdetail delObj2 = new BookingRequesdetail();
                                    delObj2.setOrderNo(waybillHeaders.get(0).getOrderNo());
                                    delObj2.setContainerNo(info.getContainerNo());
                                    bookingRequesdetailMapper.deleteByOrderNoAndContainerNo(delObj2);
                                }

                            }
                        } else if ("2".equals(info.getBoxnumberStatus())) {
                            //新增箱
                            if (customerNo.equals(info.getCustomerNo())) {
                                WaybillContainerInfo addObj = new WaybillContainerInfo();
                                BeanUtil.copyProperties(info, addObj);
                                addObj.setRowId(UUID.randomUUID().toString());
                                addObj.setWaybillNo(waybillHeaders.get(0).getWaybillNo());
                                addObj.setOrderNo(waybillHeaders.get(0).getOrderNo());
                                addObj.setAddWho(userInfo.getUserName());
                                addObj.setAddWhoName(userInfo.getRealName());
                                addObj.setAddTime(new Date());
                                waybillContainerInfoMapper.insertWaybillContainerInfo(addObj);

                                waybillGoodsInfoMapper.updateByBussinessId(basChangeboxRetreat.getBusinessid(), waybillHeaders.get(0).getWaybillNo(), waybillHeaders.get(0).getOrderNo(), info.getContainerNo());
                                waybillParticipantsMapper.updateByBussinessId(basChangeboxRetreat.getBusinessid(), waybillHeaders.get(0).getWaybillNo(), waybillHeaders.get(0).getOrderNo(), info.getContainerNo());
                                payCodeMesMapper.updateByBussinessId(basChangeboxRetreat.getBusinessid(), waybillHeaders.get(0).getWaybillNo(), info.getContainerNo());

                                if (StrUtil.isNotBlank(waybillHeaders.get(0).getOrderNo())) {
                                    BookingRequesdetail addObj2 = new BookingRequesdetail();
                                    addObj2.setRowId(UUID.randomUUID().toString());
                                    addObj2.setOrderNo(waybillHeaders.get(0).getOrderNo());
                                    addObj2.setBox(info.getContainerOwner());
                                    addObj2.setContainerNo(info.getContainerNo());
                                    addObj2.setDeadWeight(info.getContainerDeadWeight());
                                    addObj2.setGrossWeight(info.getContainerGrossWeight());
                                    addObj2.setNetWeight(info.getContainerNetWeight());
                                    addObj2.setContainerType(info.getContainerType());
                                    addObj2.setResveredField01(info.getIdentification());
                                    addObj2.setContainerTypeCode(info.getContainerTypeCode());
                                    addObj2.setContainerTypeName(info.getContainerTypeName());
                                    addObj2.setDeleteFlag("N");
                                    addObj2.setAddWho(userInfo.getUserName());
                                    addObj2.setAddWhoName(userInfo.getRealName());
                                    addObj2.setAddTime(new Date());
                                    bookingRequesdetailMapper.insertBookingRequesdetail(addObj2);
                                }
                            }
                        }
                    }
                    waybillHeaderMapper.updateSpaceNums2(waybillHeaders.get(0).getWaybillNo());
                    waybillHeaderMapper.updateTotalCases(waybillHeaders.get(0).getWaybillNo());
                    if (StrUtil.isNotBlank(waybillHeaders.get(0).getOrderNo())) {
                        bookingRequesheaderMapper.updateSpaceNums2(waybillHeaders.get(0).getOrderNo());
                        bookingRequesheaderMapper.updateTotalCases(waybillHeaders.get(0).getOrderNo());
                    }
                } else {
                    //新增箱属于新的订舱客户
                    WaybillHeader waybillHeader = new WaybillHeader();
                    waybillHeader.setRowId(UUID.randomUUID().toString());
                    waybillHeader.setWaybillNo(sysNoConfigService.genNo("YD"));
                    waybillHeader.setCustomerNo(customerNo);
                    waybillHeader.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                    waybillHeader.setPlatformName(basChangeboxRetreat.getPlatformName());
                    waybillHeader.setBillStatus("2");
                    waybillHeader.setAuditStatus("1");
                    waybillHeader.setAddWho(userInfo.getUserName());
                    waybillHeader.setAddWhoName(userInfo.getRealName());
                    waybillHeader.setAddTime(new Date());
                    waybillHeader.setResveredField02(basChangeboxRetreat.getPlatformCode());
                    waybillHeader.setResveredField03("0");

                    BookingRequesheader bookingRequesheader = new BookingRequesheader();
                    bookingRequesheader.setRowId(UUID.randomUUID().toString());
                    bookingRequesheader.setOrderNo(sysNoConfigService.genNo("SQ"));

                    waybillHeader.setOrderNo(bookingRequesheader.getOrderNo());

                    bookingRequesheader.setWaybillNo(waybillHeader.getWaybillNo());
                    bookingRequesheader.setBookingCustcode(customerNo);
                    bookingRequesheader.setCityPlatform(basChangeboxRetreat.getPlatformName());
                    bookingRequesheader.setResveredField01("0");
                    bookingRequesheader.setResveredField02(basChangeboxRetreat.getPlatformCode());
                    bookingRequesheader.setAddWho(userInfo.getUserName());
                    bookingRequesheader.setAddWhoName(userInfo.getRealName());
                    bookingRequesheader.setAddTime(new Date());
                    if (CollUtil.isNotEmpty(shifmanagements)) {
                        waybillHeader.setTrainType(shifmanagements.get(0).getTrainType());
                        waybillHeader.setTrainName(shifmanagements.get(0).getShiftName());
                        waybillHeader.setIdentification(shifmanagements.get(0).getBusinessIdentification());
                        waybillHeader.setTrip(shifmanagements.get(0).getTrip());
                        waybillHeader.setShiftNo(shifmanagements.get(0).getShiftId());
                        waybillHeader.setShippingTime(shifmanagements.get(0).getPlanShipTime());
                        waybillHeader.setShippingLine(shifmanagements.get(0).getShippingLine());
                        waybillHeader.setResveredField05(shifmanagements.get(0).getShippingLineCode());

                        bookingRequesheader.setShiftNo(shifmanagements.get(0).getShiftId());
                        bookingRequesheader.setShippingLineCode(shifmanagements.get(0).getShippingLineCode());
                        bookingRequesheader.setShippingLine(shifmanagements.get(0).getShippingLine());
                        bookingRequesheader.setTrainName(shifmanagements.get(0).getShiftName());
                        bookingRequesheader.setDeliveryTime(shifmanagements.get(0).getPlanShipTime());
                        bookingRequesheader.setTrainType(shifmanagements.get(0).getTrainType());
                        bookingRequesheader.setDocumentStatus("2");
                        bookingRequesheader.setIdentification(shifmanagements.get(0).getBusinessIdentification());
                    }

                    for (BasChangeboxContainerInfo info : infos) {
                        if ("2".equals(info.getBoxnumberStatus())) {
                            //新增箱
                            if (customerNo.equals(info.getCustomerNo())) {
                                waybillHeader.setCustomerName(info.getCustomerName());
                                bookingRequesheader.setBookingCustname(info.getCustomerName());
                                WaybillContainerInfo addObj = new WaybillContainerInfo();
                                BeanUtil.copyProperties(info, addObj);
                                addObj.setRowId(UUID.randomUUID().toString());
                                addObj.setWaybillNo(waybillHeader.getWaybillNo());
                                addObj.setOrderNo(waybillHeader.getOrderNo());
                                addObj.setAddWho(userInfo.getUserName());
                                addObj.setAddWhoName(userInfo.getRealName());
                                addObj.setAddTime(new Date());
                                waybillContainerInfoMapper.insertWaybillContainerInfo(addObj);

                                waybillGoodsInfoMapper.updateByBussinessId(basChangeboxRetreat.getBusinessid(), waybillHeader.getWaybillNo(), bookingRequesheader.getOrderNo(), info.getContainerNo());
                                waybillParticipantsMapper.updateByBussinessId(basChangeboxRetreat.getBusinessid(), waybillHeader.getWaybillNo(), bookingRequesheader.getOrderNo(), info.getContainerNo());
                                payCodeMesMapper.updateByBussinessId(basChangeboxRetreat.getBusinessid(), waybillHeader.getWaybillNo(), info.getContainerNo());

                                BookingRequesdetail addObj2 = new BookingRequesdetail();
                                addObj2.setRowId(UUID.randomUUID().toString());
                                addObj2.setOrderNo(bookingRequesheader.getOrderNo());
                                addObj2.setBox(info.getContainerOwner());
                                addObj2.setContainerNo(info.getContainerNo());
                                addObj2.setDeadWeight(info.getContainerDeadWeight());
                                addObj2.setGrossWeight(info.getContainerGrossWeight());
                                addObj2.setNetWeight(info.getContainerNetWeight());
                                addObj2.setContainerType(info.getContainerType());
                                addObj2.setResveredField01(info.getIdentification());
                                addObj2.setContainerTypeCode(info.getContainerTypeCode());
                                addObj2.setContainerTypeName(info.getContainerTypeName());
                                addObj2.setAddWho(userInfo.getUserName());
                                addObj2.setAddWhoName(userInfo.getRealName());
                                addObj2.setAddTime(new Date());
                                bookingRequesdetailMapper.insertBookingRequesdetail(addObj2);
                            }
                        }
                    }
                    waybillHeaderMapper.insertWaybillHeader(waybillHeader);
                    waybillHeaderMapper.updateSpaceNums2(waybillHeader.getWaybillNo());
                    waybillHeaderMapper.updateTotalCases(waybillHeader.getWaybillNo());

                    bookingRequesheaderMapper.insertBookingRequesheader(bookingRequesheader);
                    bookingRequesheaderMapper.updateSpaceNums2(bookingRequesheader.getOrderNo());
                    bookingRequesheaderMapper.updateTotalCases(bookingRequesheader.getOrderNo());

                    if (StrUtil.isNotBlank(waybillHeader.getTrip()) && "R".equals(waybillHeader.getTrip()) && StrUtil.isNotBlank(customerNo) && customerNo.contains("CUS")) {
                        fdPostTransportService.insertPostTransport(waybillHeader);
                    }
                }
            }
        }
        saveFdBusCostWithCitySubmit(basChangeboxRetreat, userInfo, infos, customerNos);
    }

    private void saveWayBillWithCitySubmit2(BasChangeboxRetreat basChangeboxRetreat, SecruityUser userInfo, List<BasChangeboxContainerInfo> infos, List<Shifmanagement> shifmanagements, List<BasChangeboxContainerInfo> customerNos) {
        FdBusCostDetail sel7 = new FdBusCostDetail();
//        sel7.setCostCode(fdBusCost.getCostCode());
        sel7.setCodeBbCategoriesCode("f_fee_type");
        sel7.setDeleteFlag("N");
        sel7.setStatus("0");
//        sel7.setMiniPlatformName(basChangeboxContainerInfo.getMiniPlatformName());
        sel7.setShiftNo(basChangeboxRetreat.getShiftNo());
        sel7.setPlatformCode(basChangeboxRetreat.getPlatformCode());
//        sel7.setCustomerCode(fdBusCost.getCustomerCode());
        List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList2(sel7);

        if (CollUtil.isNotEmpty(customerNos)) {
            for (BasChangeboxContainerInfo basChangeboxContainerInfo : customerNos) {
                WaybillHeader sel1 = new WaybillHeader();
                sel1.setShiftNo(basChangeboxRetreat.getShiftNo());
                sel1.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                sel1.setMiniPlatformCode(basChangeboxContainerInfo.getMiniPlatformCode());
                sel1.setMiniPlatform(basChangeboxContainerInfo.getMiniPlatformName());
                sel1.setDeleteFlag("N");
                List<WaybillHeader> waybillHeaders = waybillHeaderMapper.selectWaybillHeaderList2(sel1);
                if (CollUtil.isNotEmpty(waybillHeaders)) {
                    //新增箱属于已有的订舱客户
                    for (BasChangeboxContainerInfo info : infos) {
                        if ("1".equals(info.getBoxnumberStatus()) && "2".equals(info.getUndoType())) {
                            //撤箱&&撤销全部运费
                            if (basChangeboxContainerInfo.getMiniPlatformCode().equals(info.getMiniPlatformCode()) && basChangeboxContainerInfo.getMiniPlatformName().equals(info.getMiniPlatformName())) {
                                WaybillContainerInfo delObj = new WaybillContainerInfo();
                                delObj.setContainerNo(info.getContainerNo());
                                delObj.setShiftNo(basChangeboxRetreat.getShiftNo());
                                delObj.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                                delObj.setOperFlag("C");
                                delObj.setDeleteFlag("Y");
                                delObj.setDeleteWho(userInfo.getUserName());
                                delObj.setDeleteWhoName(userInfo.getRealName());
                                delObj.setDeleteTime(new Date());
                                waybillContainerInfoMapper.updateContainerInfo1(delObj);
                                waybillGoodsInfoMapper.deleteByBussinessId(basChangeboxRetreat.getBusinessid(), info.getContainerNo());
                                waybillParticipantsMapper.deleteByBussinessId(basChangeboxRetreat.getBusinessid(), info.getContainerNo());
                                payCodeMesMapper.deleteByBussinessId(basChangeboxRetreat.getBusinessid(), info.getContainerNo());

                                if (StrUtil.isNotBlank(waybillHeaders.get(0).getOrderNo())) {
                                    BookingRequesdetail delObj2 = new BookingRequesdetail();
                                    delObj2.setOrderNo(waybillHeaders.get(0).getOrderNo());
                                    delObj2.setContainerNo(info.getContainerNo());
                                    bookingRequesdetailMapper.deleteByOrderNoAndContainerNo(delObj2);
                                }

                            }
                        } else if ("2".equals(info.getBoxnumberStatus())) {
                            //新增箱
                            if (basChangeboxContainerInfo.getMiniPlatformCode().equals(info.getMiniPlatformCode()) && basChangeboxContainerInfo.getMiniPlatformName().equals(info.getMiniPlatformName())) {
                                WaybillContainerInfo addObj = new WaybillContainerInfo();
                                BeanUtil.copyProperties(info, addObj);
                                addObj.setRowId(UUID.randomUUID().toString());
                                addObj.setWaybillNo(waybillHeaders.get(0).getWaybillNo());
                                addObj.setOrderNo(waybillHeaders.get(0).getOrderNo());
                                addObj.setAddWho(userInfo.getUserName());
                                addObj.setAddWhoName(userInfo.getRealName());
                                addObj.setAddTime(new Date());
                                waybillContainerInfoMapper.insertWaybillContainerInfo(addObj);

                                waybillGoodsInfoMapper.updateByBussinessId(basChangeboxRetreat.getBusinessid(), waybillHeaders.get(0).getWaybillNo(), waybillHeaders.get(0).getOrderNo(), info.getContainerNo());
                                waybillParticipantsMapper.updateByBussinessId(basChangeboxRetreat.getBusinessid(), waybillHeaders.get(0).getWaybillNo(), waybillHeaders.get(0).getOrderNo(), info.getContainerNo());
                                payCodeMesMapper.updateByBussinessId(basChangeboxRetreat.getBusinessid(), waybillHeaders.get(0).getWaybillNo(), info.getContainerNo());

                                if (StrUtil.isNotBlank(waybillHeaders.get(0).getOrderNo())) {
                                    BookingRequesdetail addObj2 = new BookingRequesdetail();
                                    addObj2.setRowId(UUID.randomUUID().toString());
                                    addObj2.setOrderNo(waybillHeaders.get(0).getOrderNo());
                                    addObj2.setBox(info.getContainerOwner());
                                    addObj2.setContainerNo(info.getContainerNo());
                                    addObj2.setDeadWeight(info.getContainerDeadWeight());
                                    addObj2.setGrossWeight(info.getContainerGrossWeight());
                                    addObj2.setNetWeight(info.getContainerNetWeight());
                                    addObj2.setContainerType(info.getContainerType());
                                    addObj2.setResveredField01(info.getIdentification());
                                    addObj2.setContainerTypeCode(info.getContainerTypeCode());
                                    addObj2.setContainerTypeName(info.getContainerTypeName());
                                    addObj2.setDeleteFlag("N");
                                    addObj2.setAddWho(userInfo.getUserName());
                                    addObj2.setAddWhoName(userInfo.getRealName());
                                    addObj2.setAddTime(new Date());
                                    bookingRequesdetailMapper.insertBookingRequesdetail(addObj2);
                                }
                            }
                        }
                    }
                    waybillHeaderMapper.updateSpaceNums2(waybillHeaders.get(0).getWaybillNo());
                    waybillHeaderMapper.updateTotalCases(waybillHeaders.get(0).getWaybillNo());
                    if (StrUtil.isNotBlank(waybillHeaders.get(0).getOrderNo())) {
                        bookingRequesheaderMapper.updateSpaceNums2(waybillHeaders.get(0).getOrderNo());
                        bookingRequesheaderMapper.updateTotalCases(waybillHeaders.get(0).getOrderNo());
                    }

                } else {
                    //新增箱属于新的订舱客户
                    WaybillHeader waybillHeader = new WaybillHeader();
                    waybillHeader.setRowId(UUID.randomUUID().toString());
                    waybillHeader.setWaybillNo(sysNoConfigService.genNo("YD"));
                    waybillHeader.setCustomerNo(basChangeboxContainerInfo.getCustomerNo());
                    waybillHeader.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                    waybillHeader.setPlatformName(basChangeboxRetreat.getPlatformName());
                    waybillHeader.setBillStatus("2");
                    waybillHeader.setAuditStatus("1");
                    waybillHeader.setAddTime(new Date());
                    waybillHeader.setResveredField02(basChangeboxRetreat.getPlatformCode());
                    waybillHeader.setResveredField03("0");

                    BookingRequesheader bookingRequesheader = new BookingRequesheader();
                    bookingRequesheader.setRowId(UUID.randomUUID().toString());
                    bookingRequesheader.setOrderNo(sysNoConfigService.genNo("SQ"));

                    waybillHeader.setOrderNo(bookingRequesheader.getOrderNo());

                    bookingRequesheader.setWaybillNo(waybillHeader.getWaybillNo());
                    bookingRequesheader.setBookingCustcode(basChangeboxContainerInfo.getCustomerNo());
                    bookingRequesheader.setCityPlatform(basChangeboxRetreat.getPlatformName());
                    bookingRequesheader.setResveredField01("0");
                    bookingRequesheader.setResveredField02(basChangeboxRetreat.getPlatformCode());
                    bookingRequesheader.setAddTime(new Date());
                    if (CollUtil.isNotEmpty(shifmanagements)) {
                        waybillHeader.setTrainType(shifmanagements.get(0).getTrainType());
                        waybillHeader.setTrainName(shifmanagements.get(0).getShiftName());
                        waybillHeader.setIdentification(shifmanagements.get(0).getBusinessIdentification());
                        waybillHeader.setTrip(shifmanagements.get(0).getTrip());
                        waybillHeader.setShiftNo(shifmanagements.get(0).getShiftId());
                        waybillHeader.setShippingTime(shifmanagements.get(0).getPlanShipTime());
                        waybillHeader.setShippingLine(shifmanagements.get(0).getShippingLine());
                        waybillHeader.setResveredField05(shifmanagements.get(0).getShippingLineCode());

                        bookingRequesheader.setShiftNo(shifmanagements.get(0).getShiftId());
                        bookingRequesheader.setShippingLineCode(shifmanagements.get(0).getShippingLineCode());
                        bookingRequesheader.setShippingLine(shifmanagements.get(0).getShippingLine());
                        bookingRequesheader.setTrainName(shifmanagements.get(0).getShiftName());
                        bookingRequesheader.setDeliveryTime(shifmanagements.get(0).getPlanShipTime());
                        bookingRequesheader.setTrainType(shifmanagements.get(0).getTrainType());
                        bookingRequesheader.setDocumentStatus("2");
                        bookingRequesheader.setIdentification(shifmanagements.get(0).getBusinessIdentification());
                    }

                    for (BasChangeboxContainerInfo info : infos) {
                        if ("2".equals(info.getBoxnumberStatus())) {
                            //新增箱
                            if (basChangeboxContainerInfo.getMiniPlatformCode().equals(info.getMiniPlatformCode()) && basChangeboxContainerInfo.getMiniPlatformName().equals(info.getMiniPlatformName())) {
                                waybillHeader.setAddWho(info.getMiniPlatformCode());
                                waybillHeader.setAddWhoName(info.getUserRealname());
                                bookingRequesheader.setAddWho(info.getMiniPlatformCode());
                                bookingRequesheader.setAddWhoName(info.getUserRealname());

                                waybillHeader.setCustomerName(info.getCustomerName());
                                bookingRequesheader.setBookingCustname(info.getCustomerName());
                                WaybillContainerInfo addObj = new WaybillContainerInfo();
                                BeanUtil.copyProperties(info, addObj);
                                addObj.setRowId(UUID.randomUUID().toString());
                                addObj.setWaybillNo(waybillHeader.getWaybillNo());
                                addObj.setOrderNo(waybillHeader.getOrderNo());
                                addObj.setAddWho(info.getMiniPlatformCode());
                                addObj.setAddWhoName(info.getUserRealname());
                                addObj.setAddTime(new Date());
                                waybillContainerInfoMapper.insertWaybillContainerInfo(addObj);

                                waybillGoodsInfoMapper.updateByBussinessId(basChangeboxRetreat.getBusinessid(), waybillHeader.getWaybillNo(), bookingRequesheader.getOrderNo(), info.getContainerNo());
                                waybillParticipantsMapper.updateByBussinessId(basChangeboxRetreat.getBusinessid(), waybillHeader.getWaybillNo(), bookingRequesheader.getOrderNo(), info.getContainerNo());
                                payCodeMesMapper.updateByBussinessId(basChangeboxRetreat.getBusinessid(), waybillHeader.getWaybillNo(), info.getContainerNo());

                                BookingRequesdetail addObj2 = new BookingRequesdetail();
                                addObj2.setRowId(UUID.randomUUID().toString());
                                addObj2.setOrderNo(bookingRequesheader.getOrderNo());
                                addObj2.setBox(info.getContainerOwner());
                                addObj2.setContainerNo(info.getContainerNo());
                                addObj2.setDeadWeight(info.getContainerDeadWeight());
                                addObj2.setGrossWeight(info.getContainerGrossWeight());
                                addObj2.setNetWeight(info.getContainerNetWeight());
                                addObj2.setContainerType(info.getContainerType());
                                addObj2.setResveredField01(info.getIdentification());
                                addObj2.setContainerTypeCode(info.getContainerTypeCode());
                                addObj2.setContainerTypeName(info.getContainerTypeName());
                                addObj2.setAddWho(info.getMiniPlatformCode());
                                addObj2.setAddWhoName(info.getUserRealname());
                                addObj2.setAddTime(new Date());
                                bookingRequesdetailMapper.insertBookingRequesdetail(addObj2);
                            }
                        }
                    }
                    waybillHeaderMapper.insertWaybillHeader(waybillHeader);
                    waybillHeaderMapper.updateSpaceNums2(waybillHeader.getWaybillNo());
                    waybillHeaderMapper.updateTotalCases(waybillHeader.getWaybillNo());

                    bookingRequesheaderMapper.insertBookingRequesheader(bookingRequesheader);
                    bookingRequesheaderMapper.updateSpaceNums2(bookingRequesheader.getOrderNo());
                    bookingRequesheaderMapper.updateTotalCases(bookingRequesheader.getOrderNo());

                    if (StrUtil.isNotBlank(waybillHeader.getTrip()) && "R".equals(waybillHeader.getTrip()) && StrUtil.isNotBlank(basChangeboxContainerInfo.getCustomerNo()) && basChangeboxContainerInfo.getCustomerNo().contains("CUS")) {
                        fdPostTransportService.insertPostTransport(waybillHeader);
                    }
                }
            }
        }
        saveFdBusCostWithCitySubmit2(basChangeboxRetreat, userInfo, detailList);
    }

    private void saveBookRequestWithCityAudit(BasChangeboxRetreat basChangeboxRetreat, SecruityUser userInfo, List<BasChangeboxContainerInfo> infos) {
        BookingRequesheader sel = new BookingRequesheader();
        sel.setShiftNo(basChangeboxRetreat.getShiftNo());
        sel.setResveredField02(basChangeboxRetreat.getPlatformCode());
        sel.setBookingCustcode(basChangeboxRetreat.getCustomerNo());
        sel.setDeleteFlag("N");
        List<BookingRequesheader> bookingRequesheaders = bookingRequesheaderMapper.selectBookingRequesheaderList(sel);
        if (CollUtil.isNotEmpty(bookingRequesheaders)) {
            for (BasChangeboxContainerInfo info : infos) {
                if ("1".equals(info.getBoxnumberStatus()) && "2".equals(info.getUndoType())) {
                    BookingRequesdetail delObj = new BookingRequesdetail();
                    delObj.setShiftNo(bookingRequesheaders.get(0).getShiftNo());
                    delObj.setContainerNo(info.getContainerNo());
                    bookingRequesdetailMapper.deleteByShiftNoAndContainerNo(delObj);
                } else if ("2".equals(info.getBoxnumberStatus())) {
                    BookingRequesdetail addObj = new BookingRequesdetail();
                    addObj.setRowId(UUID.randomUUID().toString());
                    addObj.setOrderNo(bookingRequesheaders.get(0).getOrderNo());
                    addObj.setBox(info.getContainerOwner());
                    addObj.setContainerNo(info.getContainerNo());
                    addObj.setDeadWeight(info.getContainerDeadWeight());
                    addObj.setGrossWeight(info.getContainerGrossWeight());
                    addObj.setNetWeight(info.getContainerNetWeight());
                    addObj.setContainerType(info.getContainerType());
                    addObj.setResveredField01(info.getIdentification());
                    addObj.setContainerTypeCode(info.getContainerTypeCode());
                    addObj.setContainerTypeName(info.getContainerTypeName());
                    addObj.setDeleteFlag("N");
                    addObj.setAddWho(userInfo.getUserName());
                    addObj.setAddWhoName(userInfo.getRealName());
                    addObj.setAddTime(new Date());
                    bookingRequesdetailMapper.insertBookingRequesdetail(addObj);
                }
            }
            bookingRequesheaderMapper.updateSpaceNums2(bookingRequesheaders.get(0).getOrderNo());
            bookingRequesheaderMapper.updateTotalCases(bookingRequesheaders.get(0).getOrderNo());
        }
    }

    private void saveBookRequestWithCityAudit2(BasChangeboxRetreat basChangeboxRetreat, SecruityUser userInfo, List<BasChangeboxContainerInfo> infos, List<String> miniPlatformNames) {
        if (CollUtil.isNotEmpty(miniPlatformNames)) {
            for (String miniPlatformName : miniPlatformNames) {
                BookingRequesheader sel = new BookingRequesheader();
                sel.setShiftNo(basChangeboxRetreat.getShiftNo());
                sel.setResveredField02(basChangeboxRetreat.getPlatformCode());
                sel.setBookingCustcode(basChangeboxRetreat.getCustomerNo());
                sel.setMiniPlatform(miniPlatformName);
                sel.setDeleteFlag("N");
                List<BookingRequesheader> bookingRequesheaders = bookingRequesheaderMapper.selectBookingRequesheaderList2(sel);
                if (CollUtil.isNotEmpty(bookingRequesheaders)) {
                    for (BookingRequesheader bookingRequesheader : bookingRequesheaders) {
                        for (BasChangeboxContainerInfo info : infos) {
                            //小客户要对应的创建人，其余对应到平台账号
                            if (miniPlatformName.equals(info.getMiniPlatformName()) && bookingRequesheader.getMiniPlatformCode().equals(info.getMiniPlatformCode())) {
                                if ("1".equals(info.getBoxnumberStatus()) && "2".equals(info.getUndoType())) {
                                    BookingRequesdetail delObj = new BookingRequesdetail();
                                    delObj.setShiftNo(bookingRequesheader.getShiftNo());
                                    delObj.setContainerNo(info.getContainerNo());
                                    bookingRequesdetailMapper.deleteByShiftNoAndContainerNo(delObj);
                                } else if ("2".equals(info.getBoxnumberStatus())) {
                                    BookingRequesdetail addObj = new BookingRequesdetail();
                                    addObj.setRowId(UUID.randomUUID().toString());
                                    addObj.setOrderNo(bookingRequesheader.getOrderNo());
                                    addObj.setBox(info.getContainerOwner());
                                    addObj.setContainerNo(info.getContainerNo());
                                    addObj.setDeadWeight(info.getContainerDeadWeight());
                                    addObj.setGrossWeight(info.getContainerGrossWeight());
                                    addObj.setNetWeight(info.getContainerNetWeight());
                                    addObj.setContainerType(info.getContainerType());
                                    addObj.setResveredField01(info.getIdentification());
                                    addObj.setContainerTypeCode(info.getContainerTypeCode());
                                    addObj.setContainerTypeName(info.getContainerTypeName());
                                    addObj.setDeleteFlag("N");
                                    addObj.setAddWho(userInfo.getUserName());
                                    addObj.setAddWhoName(userInfo.getRealName());
                                    addObj.setAddTime(new Date());
                                    addObj.setDestinationName(info.getDestinationName());
                                    addObj.setDestination(info.getDestination());
                                    addObj.setBureauSubordinate(info.getBureau());
                                    addObj.setPortStation(info.getPortStation());
                                    bookingRequesdetailMapper.insertBookingRequesdetail(addObj);
                                }
                            }
                        }
                        bookingRequesheaderMapper.updateSpaceNums2(bookingRequesheader.getOrderNo());
                        bookingRequesheaderMapper.updateTotalCases(bookingRequesheader.getOrderNo());
                    }
                }
            }
        }

    }

    private void saveWayBillWithCityAudit(BasChangeboxRetreat basChangeboxRetreat, SecruityUser userInfo, double del, double add, List<BasChangeboxContainerInfo> infos) {
        WaybillHeader sel2 = new WaybillHeader();
        sel2.setShiftNo(basChangeboxRetreat.getShiftNo());
        sel2.setPlatformCode(basChangeboxRetreat.getPlatformCode());
        sel2.setDeleteFlag("N");
        List<WaybillHeader> waybillHeaders = waybillHeaderMapper.selectWaybillHeaderList(sel2);

        for (BasChangeboxContainerInfo info : infos) {
            if ("1".equals(info.getBoxnumberStatus()) && "2".equals(info.getUndoType())) {
                //撤箱&&撤销全部运费
                WaybillContainerInfo delObj = new WaybillContainerInfo();
                delObj.setContainerNo(info.getContainerNo());
                delObj.setShiftNo(basChangeboxRetreat.getShiftNo());
                delObj.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                delObj.setOperFlag("C");
                delObj.setDeleteFlag("Y");
                delObj.setDeleteWho(userInfo.getUserName());
                delObj.setDeleteWhoName(userInfo.getRealName());
                delObj.setDeleteTime(new Date());
                waybillContainerInfoMapper.updateContainerInfo1(delObj);
                waybillGoodsInfoMapper.deleteByBussinessId(basChangeboxRetreat.getBusinessid(), info.getContainerNo());
                waybillParticipantsMapper.deleteByBussinessId(basChangeboxRetreat.getBusinessid(), info.getContainerNo());
                payCodeMesMapper.deleteByBussinessId(basChangeboxRetreat.getBusinessid(), info.getContainerNo());
            } else if ("2".equals(info.getBoxnumberStatus())) {
                //新增箱
                WaybillContainerInfo addObj = new WaybillContainerInfo();
                BeanUtil.copyProperties(info, addObj);
                if (CollUtil.isNotEmpty(waybillHeaders)) {
                    addObj.setRowId(UUID.randomUUID().toString());
                    addObj.setWaybillNo(waybillHeaders.get(0).getWaybillNo());
                    addObj.setOrderNo(waybillHeaders.get(0).getOrderNo());
                    addObj.setAddWho(userInfo.getUserName());
                    addObj.setAddWhoName(userInfo.getRealName());
                    addObj.setAddTime(new Date());
                    waybillContainerInfoMapper.insertWaybillContainerInfo(addObj);
                }
            }
        }

        if (CollUtil.isNotEmpty(waybillHeaders)) {
            //更新订单舱位数
            WaybillHeader waybillHeader = waybillHeaders.get(0);
            waybillHeaderMapper.updateSpaceNums2(waybillHeader.getWaybillNo());
            waybillHeaderMapper.updateTotalCases(waybillHeader.getWaybillNo());

            waybillGoodsInfoMapper.updateByBussinessId(basChangeboxRetreat.getBusinessid(), waybillHeaders.get(0).getWaybillNo(), waybillHeaders.get(0).getOrderNo(), null);
            waybillParticipantsMapper.updateByBussinessId(basChangeboxRetreat.getBusinessid(), waybillHeaders.get(0).getWaybillNo(), waybillHeaders.get(0).getOrderNo(), null);
            payCodeMesMapper.updateByBussinessId(basChangeboxRetreat.getBusinessid(), waybillHeaders.get(0).getWaybillNo(), null);
        }
    }

    private void saveWayBillWithCityAudit2(BasChangeboxRetreat basChangeboxRetreat, SecruityUser userInfo, double del, double add, List<BasChangeboxContainerInfo> infos, List<String> miniPlatformNames) {
        if (CollUtil.isNotEmpty(miniPlatformNames)) {
            for (String miniPlatformName : miniPlatformNames) {
                WaybillHeader sel2 = new WaybillHeader();
                sel2.setShiftNo(basChangeboxRetreat.getShiftNo());
                sel2.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                sel2.setDeleteFlag("N");
                sel2.setMiniPlatform(miniPlatformName);
                List<WaybillHeader> waybillHeaders = waybillHeaderMapper.selectWaybillHeaderList2(sel2);

                if (CollUtil.isNotEmpty(waybillHeaders)) {
                    for (WaybillHeader waybillHeader : waybillHeaders) {
                        for (BasChangeboxContainerInfo info : infos) {
                            //小客户要对应的创建人，其余对应到平台账号
                            if (miniPlatformName.equals(info.getMiniPlatformName()) && waybillHeader.getMiniPlatformCode().equals(info.getMiniPlatformCode())) {
                                if ("1".equals(info.getBoxnumberStatus()) && "2".equals(info.getUndoType())) {
                                    //撤箱&&撤销全部运费
                                    WaybillContainerInfo delObj = new WaybillContainerInfo();
                                    delObj.setContainerNo(info.getContainerNo());
                                    delObj.setShiftNo(basChangeboxRetreat.getShiftNo());
                                    delObj.setPlatformCode(basChangeboxRetreat.getPlatformCode());
                                    delObj.setOperFlag("C");
                                    delObj.setDeleteFlag("Y");
                                    delObj.setDeleteWho(userInfo.getUserName());
                                    delObj.setDeleteWhoName(userInfo.getRealName());
                                    delObj.setDeleteTime(new Date());
                                    waybillContainerInfoMapper.updateContainerInfo1(delObj);
                                    waybillGoodsInfoMapper.deleteByBussinessId(basChangeboxRetreat.getBusinessid(), info.getContainerNo());
                                    waybillParticipantsMapper.deleteByBussinessId(basChangeboxRetreat.getBusinessid(), info.getContainerNo());
                                    payCodeMesMapper.deleteByBussinessId(basChangeboxRetreat.getBusinessid(), info.getContainerNo());
                                } else if ("2".equals(info.getBoxnumberStatus())) {
                                    //新增箱
                                    WaybillContainerInfo addObj = new WaybillContainerInfo();
                                    BeanUtil.copyProperties(info, addObj);
                                    if (CollUtil.isNotEmpty(waybillHeaders)) {
                                        addObj.setRowId(UUID.randomUUID().toString());
                                        addObj.setWaybillNo(waybillHeader.getWaybillNo());
                                        addObj.setOrderNo(waybillHeader.getOrderNo());
                                        addObj.setAddWho(userInfo.getUserName());
                                        addObj.setAddWhoName(userInfo.getRealName());
                                        addObj.setAddTime(new Date());
                                        waybillContainerInfoMapper.insertWaybillContainerInfo(addObj);
                                    }
                                }
                            }
                        }

                        //更新订单舱位数
                        waybillHeaderMapper.updateSpaceNums2(waybillHeader.getWaybillNo());
                        waybillHeaderMapper.updateTotalCases(waybillHeader.getWaybillNo());

                        waybillGoodsInfoMapper.updateByBussinessId(basChangeboxRetreat.getBusinessid(), waybillHeader.getWaybillNo(), waybillHeader.getOrderNo(), null);
                        waybillParticipantsMapper.updateByBussinessId(basChangeboxRetreat.getBusinessid(), waybillHeader.getWaybillNo(), waybillHeader.getOrderNo(), null);
                        payCodeMesMapper.updateByBussinessId(basChangeboxRetreat.getBusinessid(), waybillHeader.getWaybillNo(), null);
                    }
                }

            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R provinceRetreat(BasChangeboxRetreat basChangeboxRetreat) {
        String resveredField01 = basChangeboxRetreat.getResveredField01();
        if (StrUtil.isBlank(basChangeboxRetreat.getBusinessid())) {
            basChangeboxRetreat = basChangeboxRetreatMapper.selectBasChangeboxRetreatById(basChangeboxRetreat.getRowId());
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        List<FdBusCostDetail> ysList = new ArrayList<>();
        List<FdBusCostDetail> yfList = new ArrayList<>();
        String ysBillSubCode = null;
        String yfBillSubCode = null;
        FdShippingAccount sel = new FdShippingAccount();
        sel.setShiftNo(basChangeboxRetreat.getShiftNo());
        sel.setCustomerNo(userInfo.getPlatformCode());
        sel.setDeleteFlag("N");
        List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountList(sel);
        if (CollUtil.isNotEmpty(fdShippingAccounts)) {
            BasChangeboxContainerInfo sel2 = new BasChangeboxContainerInfo();
            sel2.setBusinessid(basChangeboxRetreat.getBusinessid());
            sel2.setDeleteFlag("N");
            List<BasChangeboxContainerInfo> infos = basChangeboxContainerInfoMapper.selectBasChangeboxContainerInfoList(sel2);

            //省平台应收账单
            BillSubPayCity sel6 = new BillSubPayCity();
            sel6.setShiftNo(basChangeboxRetreat.getShiftNo());
            sel6.setPlatformCode(basChangeboxRetreat.getPlatformCode());
            sel6.setDeleteFlag("N");
            List<BillSubPayCity> billSubPayCities = billSubPayCityMapper.selectBillSubPayCityList(sel6);
            if (CollUtil.isNotEmpty(billSubPayCities)) {
                BillSubPayCity billSubPayCity = billSubPayCities.get(billSubPayCities.size() - 1);
                ysBillSubCode = CheckUtil.getNumber(billSubPayCity.getBillSubCode(), 3);
            }

            //省平台应付账单
            BillPayProvinceSub sel7 = new BillPayProvinceSub();
            sel7.setShiftNo(basChangeboxRetreat.getShiftNo());
            sel7.setCustomerCode(basChangeboxRetreat.getPlatformCode());
            sel7.setDeleteFlag("N");
            List<BillPayProvinceSub> billPayProvinceSubs = billPayProvinceSubMapper.selectBillPayProvinceSubList(sel7);
            if (CollUtil.isNotEmpty(billPayProvinceSubs)) {
                BillPayProvinceSub billPayProvinceSub = billPayProvinceSubs.get(billPayProvinceSubs.size() - 1);
                yfBillSubCode = CheckUtil.getNumber(billPayProvinceSub.getBillSubCode(), 3);
            }

            BasChangeboxCostDetail sel3 = new BasChangeboxCostDetail();
            sel3.setHxAppNo(basChangeboxRetreat.getBusinessid());
            sel3.setDeleteFlag("N");
            List<BasChangeboxCostDetail> details = basChangeboxCostDetailMapper.selectBasChangeboxCostDetailList(sel3);

            if (CollUtil.isNotEmpty(infos)) {
//                List<FdShippingAccoundetail> addList = new ArrayList<>();
//                List<FdShippingAccoundetail> updateList = new ArrayList<>();
                //新增箱台账详情信息
                BasChangeboxCostDetail sel5 = new BasChangeboxCostDetail();
                sel5.setHxAppNo(basChangeboxRetreat.getBusinessid());
                sel5.setDatabase(database);
                List<FdShippingAccoundetail> list1 = basChangeboxCostDetailMapper.containerInfoListNew1(sel5);
                List<FdShippingAccoundetail> list2 = basChangeboxCostDetailMapper.containerInfoListNew2(sel5);
                if (CollUtil.isNotEmpty(list1)) {
                    if (CollUtil.isNotEmpty(list2)) {
                        for (FdShippingAccoundetail f1 : list1) {
                            for (FdShippingAccoundetail f2 : list2) {
                                if (f1.getContainerNumber().equals(f2.getContainerNumber())) {
                                    f1.setAccountCode(fdShippingAccounts.get(0).getAccountCode());
                                    f1.setIsRansit(f2.getIsRansit());
                                    List<WaybillHeader> waybillHeaders = waybillHeaderMapper.selectWayBillByContainerNo(basChangeboxRetreat.getShiftNo(), basChangeboxRetreat.getCustomerNo(), f1.getContainerNo());
                                    if (CollUtil.isNotEmpty(waybillHeaders)) {
                                        f1.setTransportOrderNumber(waybillHeaders.get(0).getOrderNo());
                                        f1.setApplicationNumber(waybillHeaders.get(0).getWaybillNo());
                                    }
                                    f1.setGoodsOwner(f2.getGoodsOwner());
                                    f1.setDestinationName(f2.getDestinationName());
                                    f1.setDestination(f2.getDestination());
                                    f1.setGoodsName(f2.getGoodsName());
                                    f1.setGoodsNums(f2.getGoodsNums());
                                    f1.setGoodsWeight(f2.getGoodsWeight());
                                    f1.setValueUsd(f2.getValueUsd());
                                    f1.setContainerTypeCode(f2.getContainerTypeCode());
                                    f1.setContainerTypeName(f2.getContainerTypeName());
                                    f1.setContainerType(f2.getContainerType());
                                    f1.setContainerOwner(f2.getContainerOwner());
                                    f1.setContainerWeight(f2.getContainerWeight());
                                    f1.setConsignorName(f2.getConsignorName());
                                    f1.setDestinationCountry(f2.getDestinationCountry());
                                    f1.setDestinationCountryCode(f2.getDestinationCountryCode());
                                    f1.setPortAgent(f2.getPortAgent());
                                    f1.setIsFull(f2.getIsFull());
                                    f1.setNonFerrous(f2.getNonFerrous());
                                    f1.setContainerStatus("0");
                                    fdShippingAccoundetailMapper.insert(f1);
                                    break;
                                }
                            }
                        }
                    }
                }
                //撤箱信息
                for (BasChangeboxContainerInfo info : infos) {
                    FdBusCostDetail yf = new FdBusCostDetail();
                    yf.setContainerNumber(info.getContainerNo());
                    yf.setCostType("1");
                    yf.setPayCode(basChangeboxRetreat.getPlatformCode());
                    yf.setReceiveCode("ztdl");
                    yf.setShiftNo(basChangeboxRetreat.getShiftNo());
                    yf.setDeleteFlag("N");

                    if ("1".equals(info.getBoxnumberStatus())) {
                        FdShippingAccoundetail fdShippingAccoundetail = new FdShippingAccoundetail();
                        fdShippingAccoundetail.setAccountCode(fdShippingAccounts.get(0).getAccountCode());
                        fdShippingAccoundetail.setContainerNumber(info.getContainerNo());
                        if (StrUtil.isNotBlank(info.getUndoType())) {
                            if ("2".equals(info.getUndoType())) {
                                fdShippingAccoundetail.setContainerStatus("1");
                                fdShippingAccoundetail.setDeleteFlag("C");

                                yf.setCodeBbCategoriesCode("f_fee_type");
                            } else if ("1".equals(info.getUndoType())) {
                                fdShippingAccoundetail.setOverseasFreightOc(BigDecimal.ZERO);
                                fdShippingAccoundetail.setOverseasFreightCny(BigDecimal.ZERO);
                                fdShippingAccoundetail.setRrOverseasFreightOc(BigDecimal.ZERO);
                                fdShippingAccoundetail.setRrOverseasFreightCny(BigDecimal.ZERO);

                                yf.setCodeSsCategoriesCode("jwdtlyf");
                            } else if ("0".equals(info.getUndoType())) {
                                fdShippingAccoundetail.setDomesticFreight(BigDecimal.ZERO);
                                fdShippingAccoundetail.setRrDomesticFreight(BigDecimal.ZERO);

                                yf.setCodeSsCategoriesCode("jndtlyf");
                            }
                            fdShippingAccoundetailMapper.updateFdShippingAccoundetailByAccountCode(fdShippingAccoundetail);
                        } else {
                            yf.setCodeBbCategoriesCode("f_fee_type");
                        }

                        //插入撤箱应付明细对应负值
                        List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList(yf);
                        if (CollUtil.isNotEmpty(detailList)) {
                            for (FdBusCostDetail fdBusCostDetail : detailList) {
                                fdBusCostDetail.setLocalAmount(fdBusCostDetail.getLocalAmount().negate());
                                fdBusCostDetail.setOriginalAmount(fdBusCostDetail.getOriginalAmount().negate());
                                fdBusCostDetail.setBillSubCode(null);
                                fdBusCostDetail.setStatus("1");
                            }
                            yfList.addAll(detailList);
                        }
                    }
                }
            }

            if ("1".equals(fdShippingAccounts.get(0).getStatus())) {
                //台账未审核
                //插入其他费用
                if (CollUtil.isNotEmpty(details)) {
                    for (BasChangeboxCostDetail detail : details) {
                        if (!"jndtlyf".equals(detail.getCodeSsCategoriesCode()) && !"jwdtlyf".equals(detail.getCodeSsCategoriesCode())) {
                            FdBusCostDetail fdBusCostDetail = new FdBusCostDetail();
                            BeanUtil.copyProperties(detail, fdBusCostDetail);
                            fdBusCostDetail.setId(null);
                            fdBusCostDetail.setCostCode(null);
                            if ("0".equals(fdBusCostDetail.getCostType())) {
                                fdBusCostDetail.setBillSubCode(ysBillSubCode);
                                ysList.add(fdBusCostDetail);
                            } else if ("1".equals(fdBusCostDetail.getCostType())) {
                                fdBusCostDetail.setBillSubCode(yfBillSubCode);
                                fdBusCostDetail.setPayCode(basChangeboxRetreat.getPlatformCode());
                                fdBusCostDetail.setPayName(basChangeboxRetreat.getPlatformName());
                                fdBusCostDetail.setReceiveCode("ztdl");
                                fdBusCostDetail.setReceiveName("中铁国际多式联运有限公司");
                                yfList.add(fdBusCostDetail);
                            }
                        }
                    }
                }

                insertProviceCost(basChangeboxRetreat, ysList, yfList, fdShippingAccounts.get(0).getAccountCode(), fdShippingAccounts.get(0).getAccountCode());
            } else {
                //台账已审核
                //插入全部费用
                if (CollUtil.isNotEmpty(details)) {
                    for (BasChangeboxCostDetail detail : details) {
                        FdBusCostDetail fdBusCostDetail = new FdBusCostDetail();
                        BeanUtil.copyProperties(detail, fdBusCostDetail);
                        fdBusCostDetail.setId(null);
                        fdBusCostDetail.setCostCode(null);
                        if ("0".equals(fdBusCostDetail.getCostType())) {
                            fdBusCostDetail.setBillSubCode(ysBillSubCode);
                            ysList.add(fdBusCostDetail);
                        } else if ("1".equals(fdBusCostDetail.getCostType())) {
                            fdBusCostDetail.setBillSubCode(yfBillSubCode);
                            fdBusCostDetail.setPayCode(basChangeboxRetreat.getPlatformCode());
                            fdBusCostDetail.setPayName(basChangeboxRetreat.getPlatformName());
                            fdBusCostDetail.setReceiveCode("ztdl");
                            fdBusCostDetail.setReceiveName("中铁国际多式联运有限公司");
                            yfList.add(fdBusCostDetail);
                        }
                    }
                }

                insertProviceCost(basChangeboxRetreat, ysList, yfList, ysBillSubCode, yfBillSubCode);
            }

            if (CollUtil.isNotEmpty(infos)) {
                for (BasChangeboxContainerInfo info : infos) {
                    if ("1".equals(info.getBoxnumberStatus()) && info.getRetreatCost() != null) {
                        if ("1".equals(fdShippingAccounts.get(0).getStatus())) {
                            insertProvinceKcf(basChangeboxRetreat, fdShippingAccounts, info, fdShippingAccounts.get(0).getAccountCode(), fdShippingAccounts.get(0).getAccountCode());
                        } else {
                            insertProvinceKcf(basChangeboxRetreat, fdShippingAccounts, info, ysBillSubCode, yfBillSubCode);
                        }
                    }
                }
            }

            if (!"1".equals(fdShippingAccounts.get(0).getStatus())) {
                BillSubPayCity billSubPayCity = new BillSubPayCity();
                billSubPayCity.setBillCode(ysBillSubCode.split("-")[0]);
                billSubPayCity.setBillSubCode(ysBillSubCode);
                billSubPayCity.setAccountCode(fdShippingAccounts.get(0).getAccountCode());
                billSubPayCity.setShiftName(fdShippingAccounts.get(0).getShiftName());
                billSubPayCity.setPortStation(billSubPayCities.get(0).getPortStation());
                billSubPayCity.setShipmentTime(fdShippingAccounts.get(0).getShippingTime());
                billSubPayCity.setPlatformCode(fdShippingAccounts.get(0).getCustomerNo());
                billSubPayCity.setPlatformName(fdShippingAccounts.get(0).getCustomerName());
                billSubPayCity.setPlatformLevel("0");
                billSubPayCity.setShiftNo(fdShippingAccounts.get(0).getShiftNo());
                billSubPayCity.setBillingState("0");
                billSubPayCity.setCustomerCode(fdShippingAccounts.get(0).getPlatformCode());
                billSubPayCity.setCustomerName(fdShippingAccounts.get(0).getPlatformName());
                billSubPayCity.setStatus("1");
                billSubPayCity.setAddWho(SecurityUtils.getUserInfo().getUserName());
                billSubPayCity.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                billSubPayCity.setAddTime(LocalDateTime.now());
                billSubPayCityMapper.insertBillSubPayCity(billSubPayCity);
                billSubPayCityMapper.updateLocalAmountByBillSubCode(ysBillSubCode);
                billDealWithCityMapper.updateBillAmountByBillCode(billSubPayCity.getBillCode());
                BillSubPayCity billSubPayCity1 = billSubPayCityMapper.selectBillSubPayCityById(billSubPayCity.getId());
                if (billSubPayCity1 != null && billSubPayCity1.getBillAmount() != null && billSubPayCity1.getBillAmount().compareTo(BigDecimal.ZERO) < 0 && ("VERIFIED".equals(billPayProvinceSubs.get(0).getBillingState()) || "VERIFICATION".equals(billPayProvinceSubs.get(0).getBillingState()))) {
                    FdBalanceDetail detail = new FdBalanceDetail();
                    detail.setPlatformCode(billSubPayCity1.getPlatformCode());
                    detail.setCustomerCode(billSubPayCity1.getCustomerCode());
                    detail.setCustomerName(billSubPayCity1.getCustomerName());
                    detail.setShiftNo(billSubPayCity1.getShiftNo());
                    detail.setCodeBbCategoriesCode("f_fee_type");
                    detail.setCodeBbCategoriesName("发运运费");
                    detail.setCodeSsCategoriesCode("f_clearing_balance");
                    detail.setCodeSsCategoriesName("结算余额");
                    detail.setPaymentType("0");
                    detail.setTotalAmount(billSubPayCity1.getBillAmount().negate());
                    detail.setRemainingAmount(billSubPayCity1.getBillAmount().negate());
                    detail.setPlatformLevel("0");
                    detail.setBillCode(billSubPayCity1.getBillSubCode());
                    detail.setRemarks("");
                    detail.setAddWho(userInfo.getUserName());
                    detail.setAddWhoName(userInfo.getRealName());
                    detail.setAddTime(LocalDateTime.now());
                    fdBalanceDetailMapper.insertFdBalanceDetail(detail);

                    FdTradingDetails fdTradingDetails = new FdTradingDetails();
                    String tsIn = sysNoConfigService.genNo("TS");
                    fdTradingDetails.setUuid(UUID.randomUUID().toString());
                    fdTradingDetails.setTradeSerialNumber(tsIn);
                    fdTradingDetails.setPlatformCode(detail.getPlatformCode());
                    fdTradingDetails.setPlatformName(detail.getPlatformName());
                    fdTradingDetails.setCustomerName(detail.getCustomerName());
                    fdTradingDetails.setCustomerCode(detail.getCustomerCode());
                    fdTradingDetails.setTradingHours(LocalDateTime.now());
                    fdTradingDetails.setPaymentType("0");
                    fdTradingDetails.setTransactionAmount(billSubPayCity1.getBillAmount());
                    fdTradingDetails.setFromBillCode(billSubPayCity1.getBillSubCode());
                    fdTradingDetails.setTradingStatus("1");
                    fdTradingDetails.setPlatformLevel("0");
                    fdTradingDetailsMapper.insertFdTradingDetails(fdTradingDetails);

                    BillSubPayCity updObj = new BillSubPayCity();
                    updObj.setId(billSubPayCity1.getId());
                    updObj.setBillingState("VERIFIED");
                    billSubPayCityMapper.updateBillSubPayCity(updObj);
                }

                insertBillProvinceSub(userInfo, yfBillSubCode, fdShippingAccounts, billPayProvinceSubs);
            }
        }
        //记录审核意见表
        Audiopinion audiopinion = new Audiopinion();
        audiopinion.setRowId(UUID.randomUUID().toString());
        audiopinion.setStatus("1");
        audiopinion.setOrderNo(basChangeboxRetreat.getBusinessid());
        audiopinion.setWaybillNo(basChangeboxRetreat.getWaybillNo());
        audiopinion.setAuditNo(userInfo.getUserName());
        audiopinion.setAuditOpinion("HXSH");
        audiopinion.setAuditTime(LocalDateTime.now());
        audiopinion.setDeleteFlag("N");
        audiopinion.setAddTime(new Date());
        audiopinion.setAddWho(userInfo.getUserName());
        audiopinion.setAddWhoName(userInfo.getRealName());
        audiopinion.setResveredField01(basChangeboxRetreat.getPlatformCode());
        audiopinion.setResveredField02(userInfo.getUserName());
        if (StrUtil.isNotBlank(resveredField01)) {
            audiopinion.setResveredField03(resveredField01);
        } else {
            throw new RuntimeException("审核意见为空，请填写");
        }
        audiopinionMapper.insertAudiopinion(audiopinion);

        basChangeboxRetreat.setStatus("2");
        basChangeboxRetreatMapper.updateBasChangeboxRetreat(basChangeboxRetreat);
        return new R<>(0, Boolean.TRUE, null, "操作成功");
    }

    /**
     * 插入省平台应付账单 业务余额相关
     *
     * @Param: userInfo, yfBillSubCode, fdShippingAccounts, billPayProvinceSubs
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/07/26 19:05
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertBillProvinceSub(SecruityUser userInfo, String yfBillSubCode, List<FdShippingAccount> fdShippingAccounts, List<BillPayProvinceSub> billPayProvinceSubs) {
        BillPayProvinceSub billPayProvinceSub = new BillPayProvinceSub();
        billPayProvinceSub.setBillCode(yfBillSubCode.split("-")[0]);
        billPayProvinceSub.setBillSubCode(yfBillSubCode);
        billPayProvinceSub.setAccountCode(fdShippingAccounts.get(0).getAccountCode());
        billPayProvinceSub.setShiftName(fdShippingAccounts.get(0).getShiftName());
        billPayProvinceSub.setShipmentTime(Date.from(fdShippingAccounts.get(0).getShippingTime().atZone(ZoneId.systemDefault()).toInstant()));
//                billPayProvinceSub.setCostCode(sysNoConfigService.genNo("FDC"));
        billPayProvinceSub.setPlatformCode("ztdl");
        billPayProvinceSub.setPlatformName("中铁国际多式联运有限公司");
        billPayProvinceSub.setPlatformLevel("0");
        billPayProvinceSub.setShiftNo(fdShippingAccounts.get(0).getShiftNo());
        billPayProvinceSub.setBillingState("0");
        billPayProvinceSub.setCustomerCode(fdShippingAccounts.get(0).getCustomerNo());
        billPayProvinceSub.setCustomerName(fdShippingAccounts.get(0).getCustomerName());
        billPayProvinceSub.setStatus("1");
        billPayProvinceSub.setAddWho(SecurityUtils.getUserInfo().getUserName());
        billPayProvinceSub.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
        billPayProvinceSub.setAddTime(new Date());
        billPayProvinceSubMapper.insertBillPayProvinceSub(billPayProvinceSub);
        billPayProvinceSubMapper.updateLocalAmountByBillSubCode(yfBillSubCode);
        billPayProvinceMapper.updateBillAmountByBillCode(billPayProvinceSub.getBillCode());
        BillPayProvinceSub billPayProvinceSub1 = billPayProvinceSubMapper.selectBillPayProvinceSubById(billPayProvinceSub.getId());
        if (billPayProvinceSub1 != null && billPayProvinceSub1.getBillAmount() != null && billPayProvinceSub1.getBillAmount().compareTo(BigDecimal.ZERO) < 0 && ("VERIFIED".equals(billPayProvinceSubs.get(0).getBillingState()) || "VERIFICATION".equals(billPayProvinceSubs.get(0).getBillingState()))) {
            FdBalanceDetail detail = new FdBalanceDetail();
            detail.setPlatformCode(billPayProvinceSub1.getPlatformCode());
            detail.setCustomerCode(billPayProvinceSub1.getCustomerCode());
            detail.setCustomerName(billPayProvinceSub1.getCustomerName());
            detail.setShiftNo(billPayProvinceSub1.getShiftNo());
            detail.setCodeBbCategoriesCode("f_fee_type");
            detail.setCodeBbCategoriesName("发运运费");
            detail.setCodeSsCategoriesCode("f_clearing_balance");
            detail.setCodeSsCategoriesName("结算余额");
            detail.setPaymentType("0");
            detail.setTotalAmount(billPayProvinceSub1.getBillAmount().negate());
            detail.setRemainingAmount(billPayProvinceSub1.getBillAmount().negate());
            detail.setPlatformLevel("1");
            detail.setBillCode(billPayProvinceSub1.getBillSubCode());
            detail.setRemarks("");
            detail.setAddWho(userInfo.getUserName());
            detail.setAddWhoName(userInfo.getRealName());
            detail.setAddTime(LocalDateTime.now());
            fdBalanceDetailMapper.insertFdBalanceDetail(detail);

            FdTradingDetails fdTradingDetails = new FdTradingDetails();
            String tsIn = sysNoConfigService.genNo("TS");
            fdTradingDetails.setUuid(UUID.randomUUID().toString());
            fdTradingDetails.setTradeSerialNumber(tsIn);
            fdTradingDetails.setPlatformCode(detail.getPlatformCode());
            fdTradingDetails.setPlatformName(detail.getPlatformName());
            fdTradingDetails.setCustomerName(detail.getCustomerName());
            fdTradingDetails.setCustomerCode(detail.getCustomerCode());
            fdTradingDetails.setTradingHours(LocalDateTime.now());
            fdTradingDetails.setPaymentType("0");
            fdTradingDetails.setTransactionAmount(billPayProvinceSub1.getBillAmount());
            fdTradingDetails.setFromBillCode(billPayProvinceSub1.getBillSubCode());
            fdTradingDetails.setTradingStatus("1");
            fdTradingDetails.setPlatformLevel("1");
            fdTradingDetailsMapper.insertFdTradingDetails(fdTradingDetails);

            BillPayProvinceSub updObj = new BillPayProvinceSub();
            updObj.setId(billPayProvinceSub1.getId());
            updObj.setBillingState("VERIFIED");
            billPayProvinceSubMapper.updateBillPayProvinceSub(updObj);
        }
    }

    private void insertProviceCost(BasChangeboxRetreat basChangeboxRetreat, List<FdBusCostDetail> ysList, List<FdBusCostDetail> yfList, String ysBillSubCode, String yfBillSubCode) {
        if (CollUtil.isNotEmpty(ysList)) {
            for (FdBusCostDetail fdBusCostDetail : ysList) {
                fdBusCostDetail.setBillSubCode(ysBillSubCode);
                fdBusCostDetail.setExchangeRateNew(fdBusCostDetail.getExchangeRate());
                FdBusCostDetail sel8 = new FdBusCostDetail();
                sel8.setPayCode(fdBusCostDetail.getPayCode());
                sel8.setReceiveCode(fdBusCostDetail.getReceiveCode());
                sel8.setShiftNo(fdBusCostDetail.getShiftNo());
                sel8.setCodeBbCategoriesCode(fdBusCostDetail.getCodeBbCategoriesCode());
                sel8.setCodeSsCategoriesCode(fdBusCostDetail.getCodeSsCategoriesCode());
                sel8.setContainerNumber(fdBusCostDetail.getContainerNumber());
                sel8.setCostType("1");
                sel8.setDeleteFlag("N");
                List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList(sel8);
                if (CollUtil.isNotEmpty(detailList)) {
                    fdBusCostDetail.setId(detailList.get(0).getId());
                    fdBusCostDetail.setCostCode(detailList.get(0).getCostCode());
                    fdBusCostDetail.setRemark(null);
                    fdBusCostDetailMapper.updateFdBusCostDetail(fdBusCostDetail);
                } else {
                    FdBusCostDetail sel9 = new FdBusCostDetail();
                    sel9.setPayCode(fdBusCostDetail.getPayCode());
                    sel9.setReceiveCode(fdBusCostDetail.getReceiveCode());
                    sel9.setShiftNo(fdBusCostDetail.getShiftNo());
                    sel9.setContainerNumber(fdBusCostDetail.getContainerNumber());
                    sel9.setCostType("1");
                    sel9.setDeleteFlag("N");
                    List<FdBusCostDetail> detailList2 = fdBusCostDetailMapper.selectFdBusCostDetailList(sel9);
                    if (CollUtil.isNotEmpty(detailList2)) {
                        fdBusCostDetail.setCostCode(detailList2.get(0).getCostCode());
                        fdBusCostDetail.setRemark(null);
                        fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(yfList)) {
            for (FdBusCostDetail fdBusCostDetail : yfList) {
                fdBusCostDetail.setBillSubCode(yfBillSubCode);
                fdBusCostDetail.setExchangeRateNew(fdBusCostDetail.getExchangeRate());
                FdBusCostDetail sel8 = new FdBusCostDetail();
                sel8.setPayCode(basChangeboxRetreat.getPlatformCode());
                sel8.setShiftNo(basChangeboxRetreat.getShiftNo());
                sel8.setCodeBbCategoriesCode(fdBusCostDetail.getCodeBbCategoriesCode());
                sel8.setCodeSsCategoriesCode(fdBusCostDetail.getCodeSsCategoriesCode());
                sel8.setContainerNumber(fdBusCostDetail.getContainerNumber());
                sel8.setCostType("1");
                sel8.setDeleteFlag("N");
                List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList(sel8);
                if (CollUtil.isNotEmpty(detailList)) {
                    fdBusCostDetail.setId(detailList.get(0).getId());
                    fdBusCostDetail.setCostCode(detailList.get(0).getCostCode());
                    fdBusCostDetail.setRemark(null);
                    fdBusCostDetailMapper.updateFdBusCostDetail(fdBusCostDetail);
                } else {
                    FdBusCostDetail sel9 = new FdBusCostDetail();
                    sel9.setPayCode(basChangeboxRetreat.getPlatformCode());
                    sel9.setShiftNo(basChangeboxRetreat.getShiftNo());
                    sel9.setContainerNumber(fdBusCostDetail.getContainerNumber());
                    sel9.setCostType("1");
                    sel9.setDeleteFlag("N");
                    List<FdBusCostDetail> detailList2 = fdBusCostDetailMapper.selectFdBusCostDetailList(sel9);
                    if (CollUtil.isNotEmpty(detailList2)) {
                        fdBusCostDetail.setCostCode(detailList2.get(0).getCostCode());
                        fdBusCostDetail.setRemark(null);
                        fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
                    }
                }
            }
        }
    }

    private void insertProvinceKcf(BasChangeboxRetreat basChangeboxRetreat, List<FdShippingAccount> fdShippingAccounts, BasChangeboxContainerInfo info, String ysBillSubCode, String yfBillSubCode) {
        FdBusCostDetail fdBusCostDetail = new FdBusCostDetail();
        fdBusCostDetail.setBillSubCode(ysBillSubCode);
        fdBusCostDetail.setCurrency("人民币");
        fdBusCostDetail.setExchangeRate(BigDecimal.valueOf(1));
        fdBusCostDetail.setExchangeRateNew(BigDecimal.valueOf(1));
        fdBusCostDetail.setOriginalAmount(info.getRetreatCost());
        fdBusCostDetail.setLocalAmount(info.getRetreatCost());
        fdBusCostDetail.setAuditStatus("1");
        fdBusCostDetail.setAuditTime(LocalDateTime.now());
        fdBusCostDetail.setShiftNo(fdShippingAccounts.get(0).getShiftNo());
        FdBusCostDetail sel8 = new FdBusCostDetail();
        sel8.setPayCode(basChangeboxRetreat.getCustomerNo());
        sel8.setReceiveCode(basChangeboxRetreat.getPlatformCode());
        sel8.setShiftNo(basChangeboxRetreat.getShiftNo());
        sel8.setCodeBbCategoriesCode("f_extra_fee_type");
        sel8.setCodeSsCategoriesCode("f_cancel_fee");
        sel8.setContainerNumber(info.getContainerNo());
        sel8.setCostType("1");
        sel8.setDeleteFlag("N");
        //处理应收账单撤销费用账单号
        sel8.setBillSubCode(ysBillSubCode);
        fdBusCostDetailMapper.updateProvinceReceiveCost(sel8);

        if (info.getRetreatCost().compareTo(BigDecimal.ZERO) > 0) {
            List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectFdBusCostDetailList(sel8);
            if (CollUtil.isNotEmpty(detailList)) {
                fdBusCostDetail.setId(detailList.get(0).getId());
                fdBusCostDetail.setCostCode(detailList.get(0).getCostCode());
                fdBusCostDetail.setRemark(null);
                fdBusCostDetailMapper.updateFdBusCostDetail(fdBusCostDetail);
            } else {
                FdBusCostDetail sel9 = new FdBusCostDetail();
                sel9.setPayCode(basChangeboxRetreat.getCustomerNo());
                sel9.setReceiveCode(basChangeboxRetreat.getPlatformCode());
                sel9.setShiftNo(basChangeboxRetreat.getShiftNo());
                sel9.setContainerNumber(info.getContainerNo());
                sel9.setCostType("1");
                sel9.setDeleteFlag("N");
                List<FdBusCostDetail> detailList2 = fdBusCostDetailMapper.selectFdBusCostDetailList(sel9);
                if (CollUtil.isNotEmpty(detailList2)) {
                    fdBusCostDetail.setCostCode(detailList2.get(0).getCostCode());
                    fdBusCostDetail.setCostType("1");
                    fdBusCostDetail.setContainerNumber(info.getContainerNo());
                    fdBusCostDetail.setCodeBbCategoriesCode("f_extra_fee_type");
                    fdBusCostDetail.setCodeBbCategoriesName("额外费用");
                    fdBusCostDetail.setCodeSsCategoriesCode("f_cancel_fee");
                    fdBusCostDetail.setCodeSsCategoriesName("亏舱费");
                    fdBusCostDetail.setReceiveCode(detailList2.get(0).getReceiveCode());
                    fdBusCostDetail.setReceiveName(detailList2.get(0).getReceiveName());
                    fdBusCostDetail.setPayCode(detailList2.get(0).getPayCode());
                    fdBusCostDetail.setPayName(detailList2.get(0).getPayName());
                    fdBusCostDetail.setRemark(null);
                    fdBusCostDetail.setAddWho(SecurityUtils.getUserInfo().getUserName());
                    fdBusCostDetail.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                    fdBusCostDetail.setAddTime(LocalDateTime.now());
                    fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail);
                }
            }

            FdBusCostDetail fdBusCostDetail2 = new FdBusCostDetail();
            fdBusCostDetail2.setBillSubCode(yfBillSubCode);
            fdBusCostDetail2.setCurrency("人民币");
            fdBusCostDetail2.setExchangeRate(BigDecimal.valueOf(1));
            fdBusCostDetail2.setExchangeRateNew(BigDecimal.valueOf(1));
            fdBusCostDetail2.setOriginalAmount(info.getRetreatCost());
            fdBusCostDetail2.setLocalAmount(info.getRetreatCost());
            fdBusCostDetail2.setAuditStatus("1");
            fdBusCostDetail2.setAuditTime(LocalDateTime.now());

            FdBusCostDetail sel10 = new FdBusCostDetail();
            sel10.setPayCode(basChangeboxRetreat.getPlatformCode());
            sel10.setShiftNo(basChangeboxRetreat.getShiftNo());
            sel10.setCodeBbCategoriesCode("f_extra_fee_type");
            sel10.setCodeSsCategoriesCode("f_cancel_fee");
            sel10.setContainerNumber(info.getContainerNo());
            sel10.setCostType("1");
            sel10.setDeleteFlag("N");
            List<FdBusCostDetail> detailList3 = fdBusCostDetailMapper.selectFdBusCostDetailList(sel10);
            if (CollUtil.isNotEmpty(detailList3)) {
                fdBusCostDetail2.setId(detailList3.get(0).getId());
                fdBusCostDetail2.setCostCode(detailList3.get(0).getCostCode());
                fdBusCostDetail.setRemark(null);
                fdBusCostDetailMapper.updateFdBusCostDetail(fdBusCostDetail2);
            } else {
                fdBusCostDetail2.setShiftNo(basChangeboxRetreat.getShiftNo());
                fdBusCostDetail2.setCostType("1");
                fdBusCostDetail2.setContainerNumber(info.getContainerNo());
                fdBusCostDetail2.setCodeBbCategoriesCode("f_extra_fee_type");
                fdBusCostDetail2.setCodeBbCategoriesName("额外费用");
                fdBusCostDetail2.setCodeSsCategoriesCode("f_cancel_fee");
                fdBusCostDetail2.setCodeSsCategoriesName("亏舱费");
                fdBusCostDetail2.setReceiveCode("ztdl");
                fdBusCostDetail2.setReceiveName("中铁国际多式联运有限公司");
                fdBusCostDetail2.setPayCode(basChangeboxRetreat.getPlatformCode());
                fdBusCostDetail2.setPayName(basChangeboxRetreat.getPlatformName());
                fdBusCostDetail2.setRemark(null);
                fdBusCostDetail2.setAddWho(SecurityUtils.getUserInfo().getUserName());
                fdBusCostDetail2.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                fdBusCostDetail2.setAddTime(LocalDateTime.now());
                fdBusCostDetailMapper.insertFdBusCostDetail(fdBusCostDetail2);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R reject(BasChangeboxRetreat basChangeboxRetreat) {
        //记录审核意见表
        BasChangeboxRetreat retreat = basChangeboxRetreatMapper.selectBasChangeboxRetreatById(basChangeboxRetreat.getRowId());
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        Audiopinion audiopinion = new Audiopinion();
        audiopinion.setRowId(UUID.randomUUID().toString());
        audiopinion.setStatus("0");
        audiopinion.setOrderNo(retreat.getBusinessid());
        audiopinion.setWaybillNo(retreat.getWaybillNo());
        audiopinion.setAuditNo(userInfo.getUserName());
        audiopinion.setAuditOpinion("HXSH");
        audiopinion.setAuditTime(LocalDateTime.now());
        audiopinion.setDeleteFlag("N");
        audiopinion.setAddTime(new Date());
        audiopinion.setAddWho(userInfo.getUserName());
        audiopinion.setAddWhoName(userInfo.getRealName());
        audiopinion.setResveredField01(retreat.getPlatformCode());
        audiopinion.setResveredField02(userInfo.getUserName());
        if (userInfo.getUserName().equals(retreat.getPlatformCode())) {
            if (StrUtil.isNotBlank(basChangeboxRetreat.getResveredField01())) {
                audiopinion.setResveredField03(basChangeboxRetreat.getResveredField01());
            } else {
                return new R<>(new Throwable("审核意见为空，请填写"));
            }
        }

        audiopinionMapper.insertAudiopinion(audiopinion);

        basChangeboxRetreat.setStatus("9");
        basChangeboxRetreatMapper.updateBasChangeboxRetreat(basChangeboxRetreat);
        return new R<>(0, Boolean.TRUE, null, "操作成功");
    }

    /**
     * 新增亏舱费
     *
     * @Param: fdBusCost, info, costType, auditStatus, billSubCode
     * @Return: void
     * @Author: zhaohr
     * @Date: 2024/06/21 9:43
     **/
    public void insertKcf(FdBusCost fdBusCost, BasChangeboxContainerInfo info, String costType, String auditStatus, String billSubCode) {
        //新增亏舱费
        FdBusCostDetail addObj = new FdBusCostDetail();
        addObj.setCostCode(fdBusCost.getCostCode());
        addObj.setCostType(costType);
        addObj.setContainerNumber(info.getContainerNo());
        addObj.setCodeBbCategoriesCode("f_extra_fee_type");
        addObj.setCodeBbCategoriesName("额外费用");
        addObj.setCodeSsCategoriesCode("f_cancel_fee");
        addObj.setCodeSsCategoriesName("亏舱费");
        addObj.setReceiveCode(fdBusCost.getPlatformCode());
        addObj.setReceiveName(fdBusCost.getPlatformName());
        addObj.setPayCode(fdBusCost.getCustomerCode());
        addObj.setPayName(fdBusCost.getCustomerName());
        addObj.setCurrency("人民币");
        addObj.setExchangeRate(BigDecimal.valueOf(1));
        addObj.setExchangeRateNew(BigDecimal.valueOf(1));
        addObj.setOriginalAmount(info.getRetreatCost());
        addObj.setLocalAmount(info.getRetreatCost());
        addObj.setAuditStatus(auditStatus);
        addObj.setShiftNo(fdBusCost.getShiftNo());
        addObj.setBillSubCode(billSubCode);
        addObj.setRemark(null);
        addObj.setAddWho(SecurityUtils.getUserInfo().getUserName());
        addObj.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
        addObj.setAddTime(LocalDateTime.now());
        addObj.setDeleteFlag("N");
        fdBusCostDetailMapper.insertFdBusCostDetail(addObj);
    }

    public R saveCustomer(@RequestBody BasChangeboxRetreat basChangeboxRetreat, String status) {
        /*FdShippingAccount fdShippingAccount = new FdShippingAccount();
        fdShippingAccount.setShiftNo(basChangeboxRetreat.getShiftNo());
        fdShippingAccount.setDeleteFlag("N");
        List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountList(fdShippingAccount);
        if (CollUtil.isNotEmpty(fdShippingAccounts)) {
            if (StrUtil.isNotEmpty(fdShippingAccounts.get(0).getStatus()) && !"0".equals(fdShippingAccounts.get(0).getStatus())) {
                return new R<>(500, false, "该箱号已提交台账审核，无法完成换箱审核");
            }
        }*/
        if ("1".equals(status)) {
            /*EntityWrapper<BasChangeboxRetreat> wrapper = new EntityWrapper<>();
            wrapper.eq("shift_no", basChangeboxRetreat.getShiftNo());
            wrapper.eq("platform_code", basChangeboxRetreat.getPlatformCode());
            wrapper.eq("customer_no", SecurityUtils.getUserInfo().getPlatformCode());
            wrapper.eq("delete_flag", "N");
            wrapper.in("status", "1");
            List<BasChangeboxRetreat> basChangeboxRetreats = this.selectList(wrapper);
            if (CollUtil.isNotEmpty(basChangeboxRetreats)) {
                return new R<>(new Throwable("当前用户在该平台/班次已发起撤换箱申请，审核完成前无法再次发起新的申请"));
            }*/
            R r = checkIsApply(basChangeboxRetreat.getShiftNo(), basChangeboxRetreat.getPlatformCode());
            if (r != null) {
                return r;
            }
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        basChangeboxRetreat.setAddWho(userInfo.getUserName());
        basChangeboxRetreat.setAddWhoName(userInfo.getRealName());
        basChangeboxRetreat.setAddTime(new Date());

        BasChangeboxContainerInfo sel = new BasChangeboxContainerInfo();
        sel.setBusinessid(basChangeboxRetreat.getBusinessid());
        sel.setBoxnumberStatus("2");
        sel.setDeleteFlag("N");
        List<BasChangeboxContainerInfo> infos = basChangeboxContainerInfoMapper.selectBasChangeboxContainerInfoList(sel);
        if (CollUtil.isNotEmpty(infos)) {
            basChangeboxRetreat.setIdentity("0");
        } else {
            basChangeboxRetreat.setIdentity("1");
        }
//        basChangeboxRetreat.setSource("0");
        //换箱保存为待提交状态
        String oldStatus = basChangeboxRetreat.getStatus();
        basChangeboxRetreat.setStatus(status);
        basChangeboxRetreatMapper.updateBasChangeboxRetreat(basChangeboxRetreat);

        if ("9".equals(oldStatus)) {
            return new R<>(0, Boolean.TRUE, null, "操作成功");
        }
        //补充市平台撤换箱费用
        Shifmanagement sel3 = new Shifmanagement();
        sel3.setShiftId(basChangeboxRetreat.getShiftNo());
        sel3.setPlatformCode(basChangeboxRetreat.getPlatformCode());
        sel3.setDeleteFlag("N");
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel3);

        CustomerPlatformInfo sel2 = new CustomerPlatformInfo();
        sel2.setCustomerCode(basChangeboxRetreat.getPlatformCode());
        CustomerPlatformInfo customerPlatformInfo = customerPlatformInfoMapper.selectPlatformCodeByCustomerCode(sel2);

        if ("1".equals(status)) {
            BasChangeboxCostDetail jn = new BasChangeboxCostDetail();
            jn.setHxAppNo(basChangeboxRetreat.getBusinessid());
            jn.setCostType("0");
            jn.setCodeBbCategoriesCode("f_fee_type");
            jn.setCodeBbCategoriesName("发运运费");
            jn.setCodeSsCategoriesCode("jndtlyf");
            jn.setCodeSsCategoriesName("国内段包干");
            jn.setPayCode(SecurityUtils.getUserInfo().getPlatformCode());
            jn.setPayName(SecurityUtils.getUserInfo().getPlatformName());
            jn.setReceiveCode(basChangeboxRetreat.getPlatformCode());
            jn.setReceiveName(basChangeboxRetreat.getPlatformName());
            basChangeboxCostDetailMapper.insertBasChangeboxCostDetail(jn);
            if (CollUtil.isNotEmpty(shifmanagements)) {
                jn.setCostType("1");
                jn.setPayCode(basChangeboxRetreat.getPlatformCode());
                jn.setPayName(basChangeboxRetreat.getPlatformName());
                if (StrUtil.isNotEmpty(shifmanagements.get(0).getSharePlatformCode())) {
                    jn.setReceiveCode(shifmanagements.get(0).getSharePlatformCode());
                    jn.setReceiveName(shifmanagements.get(0).getSharePlatformName());
                } else {
                    if (customerPlatformInfo != null) {
                        jn.setReceiveCode(customerPlatformInfo.getPlatformCodePro());
                        jn.setReceiveName(customerPlatformInfo.getResveredField03());
                    }
                }
                basChangeboxCostDetailMapper.insertBasChangeboxCostDetail(jn);
            }


            BasChangeboxCostDetail jw = new BasChangeboxCostDetail();
            jw.setHxAppNo(basChangeboxRetreat.getBusinessid());
            jw.setCostType("0");
            jw.setCodeBbCategoriesCode("f_fee_type");
            jw.setCodeBbCategoriesName("发运运费");
            jw.setCodeSsCategoriesCode("jwdtlyf");
            jw.setCodeSsCategoriesName("国外段包干");
            jw.setPayCode(SecurityUtils.getUserInfo().getPlatformCode());
            jw.setPayName(SecurityUtils.getUserInfo().getPlatformName());
            jw.setReceiveCode(basChangeboxRetreat.getPlatformCode());
            jw.setReceiveName(basChangeboxRetreat.getPlatformName());
            basChangeboxCostDetailMapper.insertBasChangeboxCostDetail(jw);
            if (CollUtil.isNotEmpty(shifmanagements)) {
                jw.setCostType("1");
                jw.setPayCode(basChangeboxRetreat.getPlatformCode());
                jw.setPayName(basChangeboxRetreat.getPlatformName());
                if (StrUtil.isNotEmpty(shifmanagements.get(0).getSharePlatformCode())) {
                    jw.setReceiveCode(shifmanagements.get(0).getSharePlatformCode());
                    jw.setReceiveName(shifmanagements.get(0).getSharePlatformName());
                } else {
                    if (customerPlatformInfo != null) {
                        jw.setReceiveCode(customerPlatformInfo.getPlatformCodePro());
                        jw.setReceiveName(customerPlatformInfo.getResveredField03());
                    }
                }
                basChangeboxCostDetailMapper.insertBasChangeboxCostDetail(jw);
            }
        }

        return new R<>(0, Boolean.TRUE, null, "操作成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveCity(@RequestBody BasChangeboxRetreat basChangeboxRetreat, String status) {
        BasChangeboxRetreat retreat = basChangeboxRetreatMapper.selectBasChangeboxRetreatById(basChangeboxRetreat.getRowId());
        retreat.setResveredField01("市平台提交");
        if ("1".equals(status)) {
            /*EntityWrapper<BasChangeboxRetreat> wrapper = new EntityWrapper<>();
            wrapper.eq("shift_no", retreat.getShiftNo());
            wrapper.eq("platform_code", retreat.getPlatformCode());
//        wrapper.eq("customer_no", SecurityUtils.getUserInfo().getPlatformCode());
            wrapper.eq("delete_flag", "N");
            wrapper.in("status", "1");
            List<BasChangeboxRetreat> basChangeboxRetreats = this.selectList(wrapper);
            if (CollUtil.isNotEmpty(basChangeboxRetreats)) {
                return new R<>(new Throwable("当前平台在该班次已存在撤换箱申请，审核完成前无法再次发起新的申请"));
            }*/
            R r = checkIsApplyCity(retreat);
            if (r != null) {
                return r;
            }
        }

        SecruityUser userInfo = SecurityUtils.getUserInfo();
        basChangeboxRetreat.setAddWho(userInfo.getUserName());
        basChangeboxRetreat.setAddWhoName(userInfo.getRealName());
        basChangeboxRetreat.setAddTime(new Date());

        BasChangeboxContainerInfo sel = new BasChangeboxContainerInfo();
        sel.setBusinessid(retreat.getBusinessid());
        sel.setBoxnumberStatus("2");
        sel.setDeleteFlag("N");
        List<BasChangeboxContainerInfo> infos = basChangeboxContainerInfoMapper.selectBasChangeboxContainerInfoList(sel);
        if (CollUtil.isNotEmpty(infos)) {
            basChangeboxRetreat.setIdentity("0");
        } else {
            basChangeboxRetreat.setIdentity("1");
        }
//        basChangeboxRetreat.setSource("2");
        //换箱保存为待提交状态
        String oldStatus = retreat.getStatus();
//        basChangeboxRetreat.setStatus(status);
//        basChangeboxRetreatMapper.updateBasChangeboxRetreat(basChangeboxRetreat);

        //补充市平台撤换箱费用
        /*Shifmanagement sel3 = new Shifmanagement();
        sel3.setShiftId(basChangeboxRetreat.getShiftNo());
        sel3.setPlatformCode(basChangeboxRetreat.getPlatformCode());
        sel3.setDeleteFlag("N");
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel3);

        CustomerPlatformInfo sel2 = new CustomerPlatformInfo();
        sel2.setCustomerCode(basChangeboxRetreat.getPlatformCode());
        CustomerPlatformInfo customerPlatformInfo = customerPlatformInfoMapper.selectPlatformCodeByCustomerCode(sel2);*/

        if ("1".equals(status) && !"9".equals(oldStatus)) {
            return retreat2(retreat, "1");
        }

        return new R<>(0, Boolean.TRUE, null, "操作成功");
    }

    public R examineNew(BasChangeboxRetreat basChangeboxRetreat) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        EntityWrapper<BasChangeboxRetreat> wrapper = new EntityWrapper<>();
        if (!StrUtil.hasBlank(basChangeboxRetreat.getBusinessid())) {
            wrapper.eq("businessID", basChangeboxRetreat.getBusinessid());
        } else {
            return new R<>(500, false, "请上传业务单据号");
        }
        List<BasChangeboxRetreat> list = this.basChangeboxRetreatMapper.selectList(wrapper);
        if (list.isEmpty()) {
            return new R<>(500, false, "未查到该条信息，请确认单据号是否正确");
        }
        FdShippingAccount fdShippingAccount = new FdShippingAccount();
        fdShippingAccount.setShiftNo(list.get(0).getShiftNo());
        fdShippingAccount.setDeleteFlag("N");
        List<FdShippingAccount> fdShippingAccounts = fdShippingAccountMapper.selectFdShippingAccountList(fdShippingAccount);
        if (CollUtil.isNotEmpty(fdShippingAccounts)) {
            if (StrUtil.isNotEmpty(fdShippingAccounts.get(0).getStatus()) && !"0".equals(fdShippingAccounts.get(0).getStatus())) {
                return new R<>(500, false, "该箱号已提交台账审核，无法完成换箱审核");
            }
        }

        basChangeboxRetreat.setUpdateWho(userInfo.getUserName());
        basChangeboxRetreat.setUpdateWhoName(userInfo.getRealName());
        basChangeboxRetreat.setUpdateTime(new Date());

        if ("0".equals(basChangeboxRetreat.getAuditType())) {//驳回
            basChangeboxRetreat.setStatus("0");//待提交
        } else {
            basChangeboxRetreat.setStatus("2");//通过

            //修改箱主表换箱状态
            WaybillHeader waybillHeader = new WaybillHeader();
            waybillHeader.setWaybillNo(basChangeboxRetreat.getWaybillNo());
            waybillHeader.setResveredField07("0");
            waybillHeaderMapper.updateWaybillHeader(waybillHeader);
            //审核通过之后，取出中间换箱子表箱号信息更新到运单子表中
            BasChangeboxDetail basChangeboxDetail = new BasChangeboxDetail();
            basChangeboxDetail.setDeleteFlag("N");
            basChangeboxDetail.setBusinessid(basChangeboxRetreat.getBusinessid());
            List<BasChangeboxDetail> basChangeboxDetails = basChangeboxDetailMapper.selectBasChangeboxDetailList(basChangeboxDetail);
            if (basChangeboxDetails != null && basChangeboxDetails.size() > 0) {
                List<WaybillGoodsInfo> waybillGoodsInfos = new ArrayList<>();
                for (BasChangeboxDetail changeboxDetail : basChangeboxDetails) {
                    //状态为2的 新增至运单子表；状态为1的 删除运单子表信息
                    if ("2".equals(changeboxDetail.getBoxnumberStatus())) {

                        //插入订舱申请单子表
                        BookingRequesdetail book = new BookingRequesdetail();
                        book.setRowId(UUID.randomUUID().toString());
                        book.setOrderNo(changeboxDetail.getAppNo());
                        book.setDestinationName(changeboxDetail.getResveredField04());
                        book.setDestination(changeboxDetail.getResveredField06());
                        book.setBureauSubordinate(changeboxDetail.getResveredField05());
                        book.setPortStation(changeboxDetail.getResveredField07());
                        book.setBox(changeboxDetail.getBox());
                        book.setContainerNo(changeboxDetail.getContainerNo());
                        book.setDeadWeight(changeboxDetail.getContainerDeadWeight());
                        book.setGrossWeight(changeboxDetail.getContainerGrossWeight());
                        book.setNetWeight(changeboxDetail.getContainerNetWeight());

                        book.setContainerTypeCode(changeboxDetail.getContainerTypeCode());
                        book.setContainerTypeName(changeboxDetail.getContainerTypeName());
                        book.setContainerType(changeboxDetail.getContainerType());

                        book.setGoodsName(changeboxDetail.getResveredField03());
//                        book.setHatchNo();
                        book.setDeleteFlag("N");
                        book.setAddWho(userInfo.getUserName());
                        book.setAddWhoName(userInfo.getRealName());
                        book.setAddTime(new Date());
                        book.setResveredField01(changeboxDetail.getIdentification());
                        bookingRequesdetailMapper.insertBookingRequesdetail(book);
                    } else if ("1".equals(changeboxDetail.getBoxnumberStatus())) {
                        WaybillContainerInfo waybillContainerInfo = new WaybillContainerInfo();
                        waybillContainerInfo.setDeleteFlag("Y");
                        waybillContainerInfo.setContainerNo(changeboxDetail.getContainerNo());
                        waybillContainerInfo.setWaybillNo(changeboxDetail.getWaybillNo());
                        waybillContainerInfoMapper.updateContainerInfo(waybillContainerInfo);
                        //删除订舱申请单子表
                        BookingRequesdetail book = new BookingRequesdetail();
                        book.setOrderNo(changeboxDetail.getAppNo());
                        book.setContainerNo(changeboxDetail.getContainerNo());
                        bookingRequesdetailMapper.deleteByOrderNoAndContainerNo(book);
                    }
                }

                waybillGoodsInfoMapper.insertWaybillGoodsInfo(waybillGoodsInfos);
                waybillHeaderMapper.updateTotalCases(basChangeboxRetreat.getWaybillNo());
                updateAllfreight(basChangeboxRetreat.getBusinessid());
            }
        }
        //记录审核意见表
        Audiopinion audiopinion = new Audiopinion();
        audiopinion.setRowId(UUID.randomUUID().toString());
        audiopinion.setStatus(basChangeboxRetreat.getAuditType());
        audiopinion.setOrderNo(basChangeboxRetreat.getBusinessid());
        audiopinion.setWaybillNo(basChangeboxRetreat.getWaybillNo());
        audiopinion.setAuditNo(userInfo.getUserName());
        audiopinion.setAuditOpinion("HXSH");
        audiopinion.setAuditTime(LocalDateTime.now());
        audiopinion.setDeleteFlag("N");
        audiopinion.setAddTime(new Date());
        audiopinion.setAddWho(userInfo.getUserName());
        audiopinion.setAddWhoName(userInfo.getRealName());
        audiopinion.setResveredField01(basChangeboxRetreat.getPlatformCode());
        audiopinion.setResveredField02(userInfo.getUserName());
        if (!"".equals(basChangeboxRetreat.getResveredField01()) && basChangeboxRetreat.getResveredField01() != null) {
            audiopinion.setResveredField03(basChangeboxRetreat.getResveredField01());
        } else {
            return new R<>(500, false, "审核意见为空，请填写");
        }
        basChangeboxRetreatMapper.updateBasChangeboxRetreat(basChangeboxRetreat);
        audiopinionMapper.insertAudiopinion(audiopinion);

//        spaceOccupyService.updateSpaceNums(list.get(0).getAppNo());


        return new R<>(0, Boolean.TRUE, null, "审核成功");
    }

    public R checkIsApply(String shiftId, String platformCode) {
        EntityWrapper<BasChangeboxRetreat> wrapper = new EntityWrapper<>();
        wrapper.eq("shift_no", shiftId);
        wrapper.eq("platform_code", platformCode);
        wrapper.eq("delete_flag", "N");
        wrapper.in("status", "1");

        String customerNo = SecurityUtils.getUserInfo().getPlatformCode();
        List<BasChangeboxRetreat> basChangeboxRetreats = this.selectList(wrapper);
        if (CollUtil.isNotEmpty(basChangeboxRetreats)) {
            for (BasChangeboxRetreat retreat : basChangeboxRetreats) {
                if (StrUtil.isNotBlank(retreat.getCustomerNo())) {
                    if (customerNo.equals(retreat.getCustomerNo())) {
                        return new R<>(new Throwable("当前用户在该平台/班次已发起撤换箱申请，审核完成前无法再次发起新的申请"));
                    }
                } else {
                    BasChangeboxContainerInfo sel = new BasChangeboxContainerInfo();
                    sel.setBusinessid(retreat.getBusinessid());
                    sel.setDeleteFlag("N");
                    sel.setBoxnumberStatus("2");
                    List<BasChangeboxContainerInfo> infos = basChangeboxContainerInfoMapper.selectBasChangeboxContainerInfoList(sel);
                    if (CollUtil.isNotEmpty(infos)) {
                        for (BasChangeboxContainerInfo info : infos) {
                            if (customerNo.equals(info.getCustomerNo())) {
                                return new R<>(new Throwable("当前用户在该平台/班次已发起撤换箱申请，审核完成前无法再次发起新的申请"));
                            }
                        }
                    }
                }
            }
        }
        return null;
    }

    public R checkIsApplyCity(BasChangeboxRetreat retreat) {
        BasChangeboxContainerInfo sel = new BasChangeboxContainerInfo();
        sel.setBusinessid(retreat.getBusinessid());
        sel.setDeleteFlag("N");
        sel.setBoxnumberStatus("2");
        List<String> customerNos = basChangeboxContainerInfoMapper.selectCustomerNos(sel);
        if (CollUtil.isNotEmpty(customerNos)) {
            for (String customerNo : customerNos) {
                BasChangeboxRetreat sel2 = new BasChangeboxRetreat();
                sel2.setShiftNo(retreat.getShiftNo());
                sel2.setPlatformCode(retreat.getPlatformCode());
                sel2.setCustomerNo(customerNo);
                sel2.setDeleteFlag("N");
                sel2.setStatus("1");
                List<BasChangeboxRetreat> basChangeboxRetreats = basChangeboxRetreatMapper.selectBasChangeboxRetreatList(sel2);
                if (CollUtil.isNotEmpty(basChangeboxRetreats)) {
                    return new R<>(new Throwable("用户" + basChangeboxRetreats.get(0).getCustomerName() + "在该平台/班次已发起撤换箱申请，审核完成前无法再次发起新的申请"));
                }
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveMainInfo(Shifmanagement shifmanagement) {
        R r = checkIsApply(shifmanagement.getShiftId(), shifmanagement.getPlatformCode());
        if (r != null) {
            return r;
        }
        //插入主信息
        BasChangeboxRetreat retreat = new BasChangeboxRetreat();
        retreat.setRowId(UUID.randomUUID().toString());
        retreat.setBusinessid(sysNoConfigService.genNo("HX"));
        retreat.setShiftNo(shifmanagement.getShiftId());
        retreat.setCustomerNo(SecurityUtils.getUserInfo().getPlatformCode());
        retreat.setCustomerName(SecurityUtils.getUserInfo().getPlatformName());
        retreat.setPlatformCode(shifmanagement.getPlatformCode());
        retreat.setPlatformName(shifmanagement.getResveredField02());
        retreat.setIdentification(shifmanagement.getBusinessIdentification());
        retreat.setBackIdentification(shifmanagement.getTrip());
        retreat.setDeliveryTime(shifmanagement.getPlanShipTime());
        retreat.setShippingLine(shifmanagement.getShippingLine());
        retreat.setTrainName(shifmanagement.getShiftName());
        retreat.setStatus("0");
        retreat.setSource("0");
        retreat.setDeleteFlag("N");
        retreat.setAddWho(SecurityUtils.getUserInfo().getUserName());
        retreat.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
        retreat.setAddTime(new Date());
        basChangeboxRetreatMapper.insertBasChangeboxRetreat(retreat);
        //查询并插入箱信息
        WaybillHeader sel2 = new WaybillHeader();
        sel2.setShiftNo(shifmanagement.getShiftId());
        sel2.setPlatformCode(shifmanagement.getPlatformCode());
        sel2.setCustomerNo(SecurityUtils.getUserInfo().getPlatformCode());
        if ("1".equals(SecurityUtils.getUserInfo().getDataFlag())) {
            sel2.setMiniPlatform(SecurityUtils.getUserInfo().getMiniPlatform());
        }
        List<WaybillContainerInfo> waybillContainerInfos = waybillContainerInfoMapper.selectToChangeList(sel2);
        if (CollUtil.isNotEmpty(waybillContainerInfos)) {
            List<BasChangeboxContainerInfo> details = new ArrayList<>();
            for (WaybillContainerInfo info : waybillContainerInfos) {
                BasChangeboxContainerInfo detail = new BasChangeboxContainerInfo();
                detail.setRowId(UUID.randomUUID().toString());
                detail.setBusinessid(retreat.getBusinessid());
                detail.setOrderNo(info.getOrderNo());
                detail.setWaybillNo(info.getWaybillNo());
                detail.setContainerNo(info.getContainerNo());
                detail.setContainerOwner(info.getContainerOwner());
                detail.setContainerTypeCode(info.getContainerTypeCode());
                detail.setContainerTypeName(info.getContainerTypeName());
                detail.setContainerType(info.getContainerType());
                detail.setContainerDeadWeight(info.getContainerDeadWeight());
                detail.setContainerGrossWeight(info.getContainerGrossWeight());
                detail.setContainerNetWeight(info.getContainerNetWeight());
                detail.setBoxnumberStatus("0");
                detail.setIdentification(info.getIdentification());
                detail.setCustomerNo(info.getCustomerNo());
                detail.setCustomerName(info.getCustomerName());
                detail.setMiniPlatformCode(info.getMiniPlatformCode());
                basChangeboxContainerInfoMapper.insertBasChangeboxContainerInfo(detail);
                details.add(detail);
            }
            retreat.setDelList(details);
        }
        return new R<>(0, Boolean.TRUE, retreat, "成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveMainInfoCity(Shifmanagement shifmanagement) {
        /*EntityWrapper<BasChangeboxRetreat> wrapper = new EntityWrapper<>();
        wrapper.eq("shift_no", shifmanagement.getShiftId());
        wrapper.eq("platform_code", shifmanagement.getPlatformCode());
//        wrapper.eq("customer_no", SecurityUtils.getUserInfo().getPlatformCode());
        wrapper.eq("delete_flag", "N");
        wrapper.notIn("status", "2");
        List<BasChangeboxRetreat> basChangeboxRetreats = this.selectList(wrapper);
        if (CollUtil.isNotEmpty(basChangeboxRetreats)) {
            return new R<>(new Throwable("当前平台在该班次已存在撤换箱申请，审核完成前无法再次发起新的申请"));
        }*/
        //插入主信息
        BasChangeboxRetreat retreat = new BasChangeboxRetreat();
        retreat.setRowId(UUID.randomUUID().toString());
        retreat.setBusinessid(sysNoConfigService.genNo("HX"));
        retreat.setShiftNo(shifmanagement.getShiftId());
        retreat.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        retreat.setPlatformName(SecurityUtils.getUserInfo().getPlatformName());
        /*if (StrUtil.isNotEmpty(shifmanagement.getSharePlatformCode())) {
            retreat.setPlatformCode(shifmanagement.getSharePlatformCode());
            retreat.setPlatformName(shifmanagement.getSharePlatformName());
        } else {
            String supPlatformCode = SecurityUtils.getUserInfo().getSupPlatformCode();
            if (supPlatformCode.contains("_")) {
                supPlatformCode = supPlatformCode.split("_")[0];
            }
            retreat.setPlatformCode(supPlatformCode);
            CustomerInfo sel = new CustomerInfo();
            sel.setCustomerCode(supPlatformCode);
            sel.setDeleteFlag("N");
            List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoList(sel);
            if (CollUtil.isNotEmpty(customerInfos)) {
                retreat.setPlatformName(customerInfos.get(0).getCompanyName());
            }
        }*/
        retreat.setIdentification(shifmanagement.getBusinessIdentification());
        retreat.setBackIdentification(shifmanagement.getTrip());
        retreat.setDeliveryTime(shifmanagement.getPlanShipTime());
        retreat.setShippingLine(shifmanagement.getShippingLine());
        retreat.setTrainName(shifmanagement.getShiftName());
        retreat.setStatus("0");
        retreat.setSource("2");
        retreat.setDeleteFlag("N");
        retreat.setAddWho(SecurityUtils.getUserInfo().getUserName());
        retreat.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
        retreat.setAddTime(new Date());
        //查询并插入箱信息
        WaybillHeader sel2 = new WaybillHeader();
        sel2.setShiftNo(shifmanagement.getShiftId());
        sel2.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
//        sel2.setCustomerNo(SecurityUtils.getUserInfo().getPlatformCode());
        List<BasChangeboxContainerInfo> waybillContainerInfos = basChangeboxContainerInfoMapper.selectToChangeListCity(sel2);
        if (CollUtil.isNotEmpty(waybillContainerInfos)) {
            List<BasChangeboxContainerInfo> details = new ArrayList<>();
            for (BasChangeboxContainerInfo info : waybillContainerInfos) {
                info.setRowId(UUID.randomUUID().toString());
                info.setBusinessid(retreat.getBusinessid());
                info.setBoxnumberStatus("0");
                basChangeboxContainerInfoMapper.insertBasChangeboxContainerInfo(info);
                details.add(info);
            }
            retreat.setDelList(details);
        } else {
            return new R<>(new Throwable("该平台班次不存在普通订舱用户，请由下级平台发起撤换箱！"));
        }
        basChangeboxRetreatMapper.insertBasChangeboxRetreat(retreat);
        return new R<>(0, Boolean.TRUE, retreat, "成功");
    }

    @Transactional(rollbackFor = Exception.class)
    public R imported(MultipartFile file, String businessid) {
        List<String> stringList = new ArrayList<>();
        DateTimeFormatter sdf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
        Workbook workbook = null;
        try {
            InputStream is = file.getInputStream();
            //利用WorkbookFactory获取Sheet，无需判断版本
            workbook = WorkbookFactory.create(is);
        } catch (IOException e) {
            e.printStackTrace();
        } catch (InvalidFormatException e) {
            e.printStackTrace();
        }
        List<BasChangeboxContainerInfo> listCon = new ArrayList();   /*集装箱信息组装集合*/
        List<WaybillParticipants> listPants = new ArrayList<>();   /*货物参与方信息组装集合*/
        List<PayCodeMes> listConCharge = new ArrayList<>();
        List<WaybillGoodsInfo> goodsList = new ArrayList<>();
        /*运单舱单信息(必填)sheet*/
        try {
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String usercode = userInfo.getUserName();
            String username = userInfo.getRealName();
            //导入第一张sheet页，现为"运单舱单信息(必填)"
            Sheet sheet = workbook.getSheetAt(0);
            //获取行数
            int lastRowNum = sheet.getLastRowNum();


            Cell serialNo = sheet.getRow(lastRowNum).getCell(0);//最后一行序号
            Cell containerNo1 = sheet.getRow(lastRowNum).getCell(1);//最后一行箱号
            /*最后一行为空白行的话，最后一行下标减1*/
            if (serialNo == null && containerNo1 == null && lastRowNum > 4) {//第一行数据下标为4，5为有两条数据
                lastRowNum = lastRowNum - 1;
            }
            ContainerTypeData sel = new ContainerTypeData();
            sel.setDeleteFlag("N");
            List<ContainerTypeData> containerTypeDataList = containerTypeDataMapper.selectContainerTypeDataList(sel);

            List<String> containerNoList = basChangeboxContainerInfoMapper.selectContainerNoList(businessid);

            BasChangeboxRetreat retreat = basChangeboxRetreatMapper.selectBasChangeboxRetreatByBusinessId(businessid);
            //循环校验标识
            for (int i = 4; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                boolean blankFlag = CheckUtil.isRowEmpty(row);
                if (blankFlag == true) {
                    continue;
                }
                BasChangeboxContainerInfo containerInfo = new BasChangeboxContainerInfo();//集装箱主数据
                WaybillParticipants pants = new WaybillParticipants(); //发货人
                WaybillParticipants pantsRec = new WaybillParticipants(); //收货人

                containerInfo.setBusinessid(businessid);
                if (StrUtil.isBlank(containerInfo.getCustomerNo()) && StrUtil.isNotBlank(retreat.getCustomerNo())) {
                    containerInfo.setCustomerNo(retreat.getCustomerNo());
                    containerInfo.setCustomerName(retreat.getCustomerName());
                }

                //组装收发货人信息
                pants.setHxAppNo(businessid);
                pantsRec.setHxAppNo(businessid);

                //设置文本格式防止转换异常
                if (row.getCell(1) != null) {
                    row.getCell(1).setCellType(CellType.STRING);
                } else {
                    return new R<>(new Throwable("请检查<运单舱单信息>sheet页，第" + (i + 1) + "行箱号未填写或者存在末尾空白行，如有疑问请联系管理员"));
                }
                String str = "";
                String containerNo = row.getCell(1).getStringCellValue();
                containerNo = containerNo.trim();
                str = containerNo;
                stringList.add(str);
                boolean flag2 = CheckUtil.verifyCntrCode(containerNo);
                if (flag2) {
                } else {
                    return new R<>(new Throwable("请检查<运单舱单信息>sheet页，第" + (i + 1) + "行箱号填写有误，如有疑问请联系管理员"));
                }

                List<Cell> cellList = new ArrayList();
                cellList.add(row.getCell(2));
                cellList.add(row.getCell(3));
                cellList.add(row.getCell(4));
                cellList.add(row.getCell(5));
                cellList.add(row.getCell(7));
                cellList.add(row.getCell(9));
                cellList.add(row.getCell(31));
                cellList.add(row.getCell(35));
                cellList.add(row.getCell(63));
                cellList.add(row.getCell(64));
                cellList.add(row.getCell(66));
                cellList.add(row.getCell(67));
                cellList.add(row.getCell(73));
                cellList.add(row.getCell(76));
                R r = this.judgeNone(cellList, i + 1, "<运单舱单信息(必填)>sheet页");
                if (r.getStatusCode() == 200) {
                } else {
                    return r;
                }

                //校验必填项 start****************
                if (row.getCell(10) != null) {
                    row.getCell(10).setCellType(CellType.STRING);
                }
                if (row.getCell(11) != null) {
                    row.getCell(11).setCellType(CellType.STRING);
                }
                if ((row.getCell(10) != null && !row.getCell(10).getStringCellValue().equals("") || (row.getCell(11) != null && !row.getCell(11).getStringCellValue().equals("")))) {
                } else {
                    return new R<>(new Throwable("请检查" + (i + 1) + "行中发货人英文、俄文名称须至少选填一项"));
                }
                if (row.getCell(32) != null) {
                    row.getCell(32).setCellType(CellType.STRING);
                }
                if (row.getCell(33) != null) {
                    row.getCell(33).setCellType(CellType.STRING);
                }

                if ((row.getCell(32) != null && !row.getCell(32).getStringCellValue().equals("") || (row.getCell(33) != null && !row.getCell(33).getStringCellValue().equals("")))) {
                } else {
                    return new R<>(new Throwable("请检查" + (i + 1) + "行中收货人英文、俄文名称须至少选填一项"));
                }

                /*if(row.getCell(36)!=null){
                    row.getCell(36).setCellType(CellType.STRING);
                }
                if(row.getCell(37)!=null){
                    row.getCell(37).setCellType(CellType.STRING);
                }
                if((row.getCell(36)!=null&&!row.getCell(36).getStringCellValue().equals("")
                        ||(row.getCell(37)!=null&&!row.getCell(37).getStringCellValue().equals("")))){}else {
                    return new R(500,Boolean.FALSE,"请检查"+(i+1)+"行中收货人英文、俄文详细地址须至少选填一项");
                }*/

                //校验必填项 end ****************
                if (containerNo.contains("\n")) {
                    containerNo.replace("\n", "");
                }

                if (CollUtil.isNotEmpty(containerNoList) && containerNoList.contains(containerNo)) {
                    return new R<>(new Throwable("该箱号已存在：" + containerNo));
                }
                containerInfo.setContainerNo(containerNo);//集装箱实体添加箱号
                pants.setContainerNo(containerNo);//发货人实体箱号
                pantsRec.setContainerNo(containerNo);//收货人实体箱号
                //组装集装箱信息

//                containerInfo.setContainerType(row.getCell(2).getStringCellValue());//箱型
                String containerTypeCode = row.getCell(2).getStringCellValue();
                Boolean flag = true;
                for (ContainerTypeData data : containerTypeDataList) {
                    if (data.getContainerTypeCode().equals(containerTypeCode)) {
                        containerInfo.setContainerTypeCode(data.getContainerTypeCode());
                        containerInfo.setContainerTypeName(data.getContainerTypeName());
                        containerInfo.setContainerType(data.getContainerTypeSize());
                        flag = false;
                        break;
                    }
                }

                if (flag) {
                    return new R<>(new Throwable("未查询到该箱型代码：" + containerTypeCode));
                }
                //row.getCell(3).setCellType(CellType.NUMERIC);
                containerInfo.setContainerDeadWeight(Float.parseFloat(row.getCell(3).getStringCellValue()));//箱自重
                containerInfo.setStationCompilation(row.getCell(4).getStringCellValue().replace(" ", ""));//发站站编
                String startName = stationMapper.selectStationMesByCode(row.getCell(4).getStringCellValue().trim());//发站站名
                containerInfo.setStartStationName(startName);
                containerInfo.setDestinationName(startName);
                containerInfo.setEndCompilation(row.getCell(5).getStringCellValue().replace(" ", ""));//到站站编
                String endName = stationMapper.selectStationMesByCode(row.getCell(5).getStringCellValue().trim());//到站站名
                containerInfo.setEndStationName(endName);
                containerInfo.setDestination(endName);

                if (row.getCell(6) != null) {
                    row.getCell(6).setCellType(CellType.STRING);
                }
                if (row.getCell(8) != null) {
                    row.getCell(8).setCellType(CellType.STRING);
                }
                if (row.getCell(6) != null && !row.getCell(6).getStringCellValue().equals("")) {
                    containerInfo.setArrivalRemarks(row.getCell(6).getStringCellValue());//到站备注
                }
                containerInfo.setFrontierPortstation(row.getCell(7).getStringCellValue());//国境口岸站编
                if (row.getCell(8) != null && !row.getCell(8).getStringCellValue().equals("")) {
                    containerInfo.setTitle(row.getCell(8).getStringCellValue());//封号
                }

                if (row.getCell(12) != null) {
                    row.getCell(12).setCellType(CellType.STRING);
                }
                if (row.getCell(29) != null) {
                    row.getCell(29).setCellType(CellType.STRING);
                }

                if (row.getCell(30) != null) {
                    row.getCell(30).setCellType(CellType.STRING);
                }
                if (row.getCell(15) != null) {
                    row.getCell(15).setCellType(CellType.STRING);
                }
                if (row.getCell(16) != null) {
                    row.getCell(16).setCellType(CellType.STRING);
                }
                if (row.getCell(17) != null) {
                    row.getCell(17).setCellType(CellType.STRING);
                }

                if (row.getCell(19) != null) {
                    row.getCell(19).setCellType(CellType.STRING);
                }
                if (row.getCell(20) != null) {
                    row.getCell(20).setCellType(CellType.STRING);
                }
                if (row.getCell(21) != null) {
                    row.getCell(21).setCellType(CellType.STRING);
                }

                if (row.getCell(23) != null) {
                    row.getCell(23).setCellType(CellType.STRING);
                }
                if (row.getCell(24) != null) {
                    row.getCell(24).setCellType(CellType.STRING);
                }
                if (row.getCell(25) != null) {
                    row.getCell(25).setCellType(CellType.STRING);
                }

                if (row.getCell(18) != null) {
                    row.getCell(18).setCellType(CellType.STRING);
                }
                if (row.getCell(22) != null) {
                    row.getCell(22).setCellType(CellType.STRING);
                }
                if (row.getCell(26) != null) {
                    row.getCell(26).setCellType(CellType.STRING);
                }
                if (row.getCell(27) != null) {
                    row.getCell(27).setCellType(CellType.STRING);
                }
                if (row.getCell(14) != null) {
                    row.getCell(14).setCellType(CellType.STRING);
                }

                if (row.getCell(36) != null) {
                    row.getCell(36).setCellType(CellType.STRING);
                }
                if (row.getCell(37) != null) {
                    row.getCell(37).setCellType(CellType.STRING);
                }
                if (row.getCell(38) != null) {
                    row.getCell(38).setCellType(CellType.STRING);
                }

                if (row.getCell(40) != null) {
                    row.getCell(40).setCellType(CellType.STRING);
                }
                if (row.getCell(41) != null) {
                    row.getCell(41).setCellType(CellType.STRING);
                }
                if (row.getCell(42) != null) {
                    row.getCell(42).setCellType(CellType.STRING);
                }

                if (row.getCell(44) != null) {
                    row.getCell(44).setCellType(CellType.STRING);
                }
                if (row.getCell(45) != null) {
                    row.getCell(45).setCellType(CellType.STRING);
                }
                if (row.getCell(46) != null) {
                    row.getCell(46).setCellType(CellType.STRING);
                }

                if (row.getCell(48) != null) {
                    row.getCell(48).setCellType(CellType.STRING);
                }

                if (row.getCell(49) != null) {
                    row.getCell(49).setCellType(CellType.STRING);
                }
                if (row.getCell(50) != null) {
                    row.getCell(50).setCellType(CellType.STRING);
                }
                if (row.getCell(51) != null) {
                    row.getCell(51).setCellType(CellType.STRING);
                }

                if (row.getCell(40) != null) {
                    row.getCell(40).setCellType(CellType.STRING);
                }
                if (row.getCell(41) != null) {
                    row.getCell(41).setCellType(CellType.STRING);
                }
                if (row.getCell(42) != null) {
                    row.getCell(42).setCellType(CellType.STRING);
                }

                if (row.getCell(39) != null) {
                    row.getCell(39).setCellType(CellType.STRING);
                }
                if (row.getCell(43) != null) {
                    row.getCell(43).setCellType(CellType.STRING);
                }
                if (row.getCell(48) != null) {
                    row.getCell(48).setCellType(CellType.STRING);
                }
                if (row.getCell(52) != null) {
                    row.getCell(52).setCellType(CellType.STRING);
                }
                if (row.getCell(53) != null) {
                    row.getCell(53).setCellType(CellType.STRING);
                }
                if (row.getCell(54) != null) {
                    row.getCell(54).setCellType(CellType.STRING);
                }
                if (row.getCell(55) != null) {
                    row.getCell(55).setCellType(CellType.STRING);
                }
                if (row.getCell(56) != null) {
                    row.getCell(56).setCellType(CellType.STRING);
                }
                if (row.getCell(59) != null) {
                    row.getCell(59).setCellType(CellType.STRING);
                }
                if (row.getCell(60) != null) {
                    row.getCell(60).setCellType(CellType.STRING);
                }
                if (row.getCell(61) != null) {
                    row.getCell(61).setCellType(CellType.STRING);
                }

                //组装发货人
                pants.setParticipantsType("F");
                pants.setConsignorName(row.getCell(9).getStringCellValue());//发货人名称
                if (row.getCell(10) != null && !row.getCell(10).getStringCellValue().equals("")) {
                    pants.setConsignorEnglishname(row.getCell(10).getStringCellValue());//发货人英文名称
                }
                if (row.getCell(11) != null && !row.getCell(11).getStringCellValue().equals("")) {
                    pants.setConsignorRussianame(row.getCell(11).getStringCellValue());//发货人俄文名称
                }
                if (row.getCell(12) != null && !row.getCell(12).getStringCellValue().equals("")) {
                    pants.setCitizenId(row.getCell(12).getStringCellValue());//公民税务系统识别码
                }
                if (row.getCell(27) != null && !row.getCell(27).getStringCellValue().equals("")) {
                    pants.setPostalCode(row.getCell(27).getStringCellValue());//发货人邮政编码
                }
                if (row.getCell(28) != null) {
                    row.getCell(28).setCellType(CellType.STRING);
                }
                pants.setPhone(row.getCell(28).getStringCellValue());//发货人电话
                if (row.getCell(29) != null && !row.getCell(29).getStringCellValue().equals("")) {
                    pants.setEmail(row.getCell(29).getStringCellValue());//发货人邮箱
                }

                if (row.getCell(30) != null && !row.getCell(30).getStringCellValue().equals("")) {
                    pants.setFax(row.getCell(30).getStringCellValue());//发货人传真
                }
                pants.setCountryCode(row.getCell(13).getStringCellValue());//所属国家代码
                if (row.getCell(15) != null && !row.getCell(15).getStringCellValue().equals("")) {
                    pants.setProvince(row.getCell(15).getStringCellValue());//发货人省
                }
                if (row.getCell(16) != null && !row.getCell(16).getStringCellValue().equals("")) {
                    pants.setCity(row.getCell(16).getStringCellValue());//发货人市
                }
                if (row.getCell(17) != null && !row.getCell(17).getStringCellValue().equals("")) {
                    pants.setDistrict(row.getCell(17).getStringCellValue());//发货人县/区
                }

                if (row.getCell(19) != null && !row.getCell(19).getStringCellValue().equals("")) {
                    pants.setEnglishProvince(row.getCell(19).getStringCellValue());//发货人省
                }
                if (row.getCell(20) != null && !row.getCell(20).getStringCellValue().equals("")) {
                    pants.setEnglishCity(row.getCell(20).getStringCellValue());//发货人市
                }
                if (row.getCell(21) != null && !row.getCell(21).getStringCellValue().equals("")) {
                    pants.setEnglishDistrict(row.getCell(21).getStringCellValue());//发货人县/区
                }

                if (row.getCell(23) != null && !row.getCell(23).getStringCellValue().equals("")) {
                    pants.setRussiaProvince(row.getCell(23).getStringCellValue());//发货人省
                }
                if (row.getCell(24) != null && !row.getCell(24).getStringCellValue().equals("")) {
                    pants.setRussiaCity(row.getCell(24).getStringCellValue());//发货人市
                }
                if (row.getCell(25) != null && !row.getCell(25).getStringCellValue().equals("")) {
                    pants.setRussiaDistrict(row.getCell(25).getStringCellValue());//发货人县/区
                }

                if (row.getCell(18) != null && !row.getCell(18).getStringCellValue().equals("")) {
                    pants.setChineseAddress(row.getCell(18).getStringCellValue());//发货人中文详细地址
                }
                if (row.getCell(22) != null && !row.getCell(22).getStringCellValue().equals("")) {
                    pants.setEnglishAddress(row.getCell(22).getStringCellValue());//发货人英文详细地址
                }
                if (row.getCell(26) != null && !row.getCell(26).getStringCellValue().equals("")) {
                    pants.setRussiaAddress(row.getCell(26).getStringCellValue());//发货人俄文详细地址
                }
                if (row.getCell(14) != null && !row.getCell(14).getStringCellValue().equals("")) {
                    pants.setShipperSignature(row.getCell(14).getStringCellValue());//发货人签字
                }

                //组装收货人
                pantsRec.setParticipantsType("S");
                pantsRec.setConsignorName(row.getCell(31).getStringCellValue());//收货人名称
                if (row.getCell(32) != null && !row.getCell(32).getStringCellValue().equals("")) {
                    pantsRec.setConsignorEnglishname(row.getCell(32).getStringCellValue());//收货人英文名称
                }
                if (row.getCell(33) != null && !row.getCell(33).getStringCellValue().equals("")) {
                    pantsRec.setConsignorRussianame(row.getCell(33).getStringCellValue());//收货人俄文名称
                }
                if (row.getCell(34) != null && !row.getCell(34).getStringCellValue().equals("")) {
                    pantsRec.setCitizenId(row.getCell(34).getStringCellValue());//公民税务系统识别码
                }
                if (row.getCell(48) != null && !row.getCell(48).getStringCellValue().equals("")) {
                    pantsRec.setPostalCode(row.getCell(48).getStringCellValue());//收货人邮政编码
                }
                if (row.getCell(49) != null && !row.getCell(49).getStringCellValue().equals("")) {
                    pantsRec.setPhone(row.getCell(49).getStringCellValue());//收货人电话
                }
                if (row.getCell(50) != null && !row.getCell(50).getStringCellValue().equals("")) {
                    pantsRec.setEmail(row.getCell(50).getStringCellValue());//收货人邮箱
                }
                if (row.getCell(51) != null && !row.getCell(51).getStringCellValue().equals("")) {
                    pantsRec.setFax(row.getCell(51).getStringCellValue());//收货人传真
                }
                pantsRec.setCountryCode(row.getCell(35).getStringCellValue());//所属国家代码

                if (row.getCell(36) != null && !row.getCell(36).getStringCellValue().equals("")) {
                    pantsRec.setProvince(row.getCell(36).getStringCellValue());//收货人省
                }
                if (row.getCell(37) != null && !row.getCell(37).getStringCellValue().equals("")) {
                    pantsRec.setCity(row.getCell(37).getStringCellValue());//收货人市
                }
                if (row.getCell(38) != null && !row.getCell(38).getStringCellValue().equals("")) {
                    pantsRec.setDistrict(row.getCell(38).getStringCellValue());//收货人县/区
                }

                if (row.getCell(40) != null && !row.getCell(40).getStringCellValue().equals("")) {
                    pantsRec.setEnglishProvince(row.getCell(40).getStringCellValue());//收货人省
                }
                if (row.getCell(41) != null && !row.getCell(41).getStringCellValue().equals("")) {
                    pantsRec.setEnglishCity(row.getCell(41).getStringCellValue());//收货人市
                }
                if (row.getCell(42) != null && !row.getCell(42).getStringCellValue().equals("")) {
                    pantsRec.setEnglishDistrict(row.getCell(42).getStringCellValue());//收货人县/区
                }

                if (row.getCell(44) != null && !row.getCell(44).getStringCellValue().equals("")) {
                    pantsRec.setRussiaProvince(row.getCell(44).getStringCellValue());//收货人省
                }
                if (row.getCell(45) != null && !row.getCell(45).getStringCellValue().equals("")) {
                    pantsRec.setRussiaCity(row.getCell(45).getStringCellValue());//收货人市
                }
                if (row.getCell(46) != null && !row.getCell(46).getStringCellValue().equals("")) {
                    pantsRec.setRussiaDistrict(row.getCell(46).getStringCellValue());//收货人县/区
                }

                if (row.getCell(39) != null && !row.getCell(39).getStringCellValue().equals("")) {
                    pantsRec.setChineseAddress(row.getCell(39).getStringCellValue());//收货人中文详细地址
                }
                if (row.getCell(43) != null && !row.getCell(43).getStringCellValue().equals("")) {
                    pantsRec.setEnglishAddress(row.getCell(43).getStringCellValue());//收货人英文详细地址
                }
                if (row.getCell(47) != null && !row.getCell(47).getStringCellValue().equals("")) {
                    pantsRec.setRussiaAddress(row.getCell(47).getStringCellValue());//收货人俄文详细地址
                }
                if (row.getCell(52) != null && !row.getCell(52).getStringCellValue().equals("")) {
                    containerInfo.setResveredField02(row.getCell(52).getStringCellValue());//发货人声明
                }
                if (row.getCell(53) != null && !row.getCell(53).getStringCellValue().equals("")) {
                    containerInfo.setResveredField03(row.getCell(53).getStringCellValue());//何方装车（О--发货人 П--承运人）
                }
                if (row.getCell(54) != null && !row.getCell(54).getStringCellValue().equals("")) {
                    containerInfo.setResveredField04(row.getCell(54).getStringCellValue());//确定重量的方法
                }
                if (row.getCell(55) != null && !row.getCell(55).getStringCellValue().equals("")) {
                    containerInfo.setResveredField05(row.getCell(55).getStringCellValue());//添加附件
                }
                if (row.getCell(56) != null && !row.getCell(56).getStringCellValue().equals("")) {
                    containerInfo.setTwentyFifth(row.getCell(56).getStringCellValue());// [39]列AN列
                }
                if (row.getCell(59) != null && !row.getCell(59).getStringCellValue().equals("")) {
                    containerInfo.setSevenLine(row.getCell(59).getStringCellValue());// [42]列AQ列
                }
                if (row.getCell(60) != null && !row.getCell(60).getStringCellValue().equals("")) {
                    containerInfo.setEightLine(row.getCell(60).getStringCellValue());// [43]八栏
                }
                if (row.getCell(61) != null && !row.getCell(61).getStringCellValue().equals("")) {
                    containerInfo.setTwentyEighthLine(row.getCell(61).getStringCellValue());// [44]二十八栏
                }
                if (row.getCell(57) != null) {
                    row.getCell(57).setCellType(CellType.STRING);
                }
                if (row.getCell(58) != null) {
                    row.getCell(58).setCellType(CellType.STRING);
                }
                if (row.getCell(62) != null) {
                    row.getCell(62).setCellType(CellType.STRING);
                }
                if (row.getCell(63) != null) {
                    row.getCell(63).setCellType(CellType.STRING);
                }
                if (row.getCell(64) != null) {
                    row.getCell(64).setCellType(CellType.STRING);
                }
                if (row.getCell(66) != null) {
                    row.getCell(66).setCellType(CellType.STRING);
                }
                if (row.getCell(69) != null) {
                    row.getCell(69).setCellType(CellType.STRING);
                }
                if (row.getCell(70) != null) {
                    row.getCell(70).setCellType(CellType.STRING);
                }
                if (row.getCell(71) != null) {
                    row.getCell(71).setCellType(CellType.STRING);
                }
                if (row.getCell(72) != null) {
                    row.getCell(72).setCellType(CellType.STRING);
                }
                if (row.getCell(73) != null) {
                    row.getCell(73).setCellType(CellType.STRING);
                }
                if (row.getCell(74) != null) {
                    row.getCell(74).setCellType(CellType.STRING);
                }
                if (row.getCell(75) != null) {
                    row.getCell(75).setCellType(CellType.STRING);
                }
                if (row.getCell(76) != null) {
                    row.getCell(76).setCellType(CellType.STRING);
                }
                if (row.getCell(77) != null) {
                    row.getCell(77).setCellType(CellType.STRING);
                }
                if ((row.getCell(73) != null && !row.getCell(73).getStringCellValue().equals(""))) {
                } else {
                    return new R<>(new Throwable("请检查" + (i + 1) + "快通准备是否填写"));
                }
                if ((row.getCell(76) != null && !row.getCell(76).getStringCellValue().equals(""))) {
                } else {
                    return new R<>(new Throwable("请检查" + (i + 1) + "班列类别是否填写"));
                }

                //组装运单主数据
                /*******************************39栏非必填项，略过******************************/
                if (row.getCell(43) != null && !row.getCell(43).getStringCellValue().equals("")) {
                    containerInfo.setResveredField06(row.getCell(43).getStringCellValue());//特殊承运人
                }
                if (row.getCell(44) != null && !row.getCell(44).getStringCellValue().equals("")) {
                    containerInfo.setResveredField07(row.getCell(44).getStringCellValue());//箱号栏标记重量值
                }
                /*******************************42栏非必填项，暂定不需展示******************************/
                /*******************************43栏非必填项，暂定不需展示******************************/
                /*******************************44栏非必填项，暂定不需展示******************************/
//                if (row.getCell(45) != null&&!row.getCell(45).getStringCellValue().equals("")){
//                    containerInfo.setResveredField08(row.getCell(45).getStringCellValue());//中哈互使箱
//                }
                if (row.getCell(62) != null && !row.getCell(62).getStringCellValue().equals("")) {
                    String portStationCode = row.getCell(62).getStringCellValue();
                    List<SysDict> list1 = sysDictMapper.selectSysDictListByCode(98, portStationCode, database);
                    if (list1 != null && list1.size() > 0) {
                        containerInfo.setPortAgent(list1.get(0).getDictValue());
                    }
                    containerInfo.setResveredField09(portStationCode);//口岸代理编码
                }
                if (row.getCell(63) != null && !row.getCell(63).getStringCellValue().equals("")) {
                    containerInfo.setResveredField10(row.getCell(63).getStringCellValue());//是否海关过境
                }
                containerInfo.setAbroadReachCity(row.getCell(64).getStringCellValue());//境外到达城市
                try {
                    if (row.getCell(65) != null && !row.getCell(65).getStringCellValue().equals("")) {
                        String date = row.getCell(65).getStringCellValue();
                        Date planAbroadTime = sdf2.parse(date);
                        containerInfo.setPlanAbroadTime(planAbroadTime);//预计出/入境时间
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return new R<>(new Throwable("导入<运单舱单信息(必填)>sheet页失败，第" + (i + 1) + "行预计出/入境时间填写有误"));
                }
                if (row.getCell(70) != null && !row.getCell(70).getStringCellValue().equals("")) {
                    containerInfo.setShippingFeePayWays(row.getCell(70).getStringCellValue());//运费支付方式
                }
                if (row.getCell(71) != null && !row.getCell(71).getStringCellValue().equals("")) {
                    containerInfo.setGoodsCustomStatusCode(row.getCell(71).getStringCellValue());//货物海关状态代码
                }
                if (row.getCell(72) != null && !row.getCell(72).getStringCellValue().equals("")) {
                    containerInfo.setManifestRemarks(row.getCell(72).getStringCellValue());//舱单备注
                }
                if (row.getCell(73) != null && !row.getCell(73).getStringCellValue().equals("")) {
                    containerInfo.setFastTrain(row.getCell(73).getStringCellValue());//快通准备
                }
                if (row.getCell(74) != null && !row.getCell(74).getStringCellValue().equals("")) {
                    containerInfo.setDeparturePlaceGq(row.getCell(74).getStringCellValue());//出境启运地关区代码
                }
                if (row.getCell(75) != null && !row.getCell(75).getStringCellValue().equals("")) {
                    containerInfo.setDeparturePlaceKa(row.getCell(75).getStringCellValue());//出境启运地口岸代码
                }
                if (row.getCell(76) != null && !row.getCell(76).getStringCellValue().equals("")) {
                    containerInfo.setBlockType(row.getCell(76).getStringCellValue());//班列类别
                }
                //组装货物信息
                try {
                    row.getCell(66).setCellType(CellType.STRING);
                    containerInfo.setGoodsValue(row.getCell(66).getStringCellValue());//托运货物价值
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                    return new R<>(new Throwable("导入<运单舱单信息(必填)>sheet页失败，第" + (i + 1) + "行货物价值填写有误"));
                }
                containerInfo.setGoodsAmountTypeCode(row.getCell(67).getStringCellValue());//货物金额类型代码
                if (row.getCell(68) != null && !row.getCell(68).getStringCellValue().equals("")) {
                    containerInfo.setTranClauseCode(row.getCell(68).getStringCellValue());//运输条款代码
                }
                try {
                    if (row.getCell(69) != null && !row.getCell(69).getStringCellValue().equals("")) {
                        row.getCell(69).setCellType(CellType.STRING);
                        containerInfo.setGoodsCube(Float.parseFloat(row.getCell(69).getStringCellValue()));//货物体积
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return new R<>(new Throwable("导入<运单舱单信息(必填)>sheet页失败，第" + (i + 1) + "行货物体积填写有误"));
                }

                try {
                    if (row.getCell(78) != null && !row.getCell(78).getStringCellValue().equals("")) {
                        row.getCell(78).setCellType(CellType.STRING);
                        if (row.getCell(78).getStringCellValue().trim().equals("是")) {
                            containerInfo.setIsFull("1");
                        } else if (row.getCell(78).getStringCellValue().trim().equals("否")) {
                            containerInfo.setIsFull("0");
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return new R(500, Boolean.FALSE, "导入<运单舱单信息(必填)>sheet页失败，第" + (i + 1) + "行是否全程填写有误");
                }
                try {
                    if (row.getCell(79) != null && !row.getCell(79).getStringCellValue().equals("")) {
                        row.getCell(79).setCellType(CellType.STRING);
                        if (row.getCell(79).getStringCellValue().trim().equals("是")) {
                            containerInfo.setNonFerrous("1");
                        } else if (row.getCell(79).getStringCellValue().trim().equals("否")) {
                            containerInfo.setNonFerrous("0");
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return new R(500, Boolean.FALSE, "导入<运单舱单信息(必填)>sheet页失败，第" + (i + 1) + "行有色金属填写有误");
                }
                listPants.add(pants);
                listPants.add(pantsRec);
                containerInfo.setBoxnumberStatus("2");
                listCon.add(containerInfo);//添加集装箱信息
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(e.getMessage() + "##################导入<运单舱单信息(必填)>sheet页失败########################");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new R<>(new Throwable("导入<运单舱单信息(必填)>sheet页失败，请检查日期、小数等特殊单元格格式，检查无误后，请联系管理员" + e.getMessage()));
        }
        /*<付费代码>sheet*/
        try {
            //导入第二张sheet页，现为"付费代码"
            Sheet sheet2rd = workbook.getSheetAt(1);
            //获取行数
            int lastRowNum2rd = sheet2rd.getLastRowNum();

            Cell serialNo = sheet2rd.getRow(lastRowNum2rd).getCell(0);//最后一行序号
            Cell containerNo1 = sheet2rd.getRow(lastRowNum2rd).getCell(1);//最后一行箱号
            /*最后一行为空白行的话，最后一行下标减一*/
            if (serialNo == null && containerNo1 == null && lastRowNum2rd > 3) {//第一行数据下标为3，4为有两条数据
                lastRowNum2rd = lastRowNum2rd - 1;
            }
            //循环校验标识
            for (int i = 3; i <= lastRowNum2rd; i++) {
                PayCodeMes charge = new PayCodeMes();
                Row row = sheet2rd.getRow(i);
                boolean blankFlag = CheckUtil.isRowEmpty(row);
                if (blankFlag == true) {
                    continue;
                }
                charge.setHxAppNo(businessid);
                List<Cell> cellListFee = new ArrayList();
//                cellListFee.add(row.getCell(0));
                cellListFee.add(row.getCell(1));
                cellListFee.add(row.getCell(2));
                cellListFee.add(row.getCell(3));
                R r = this.judgeNone(cellListFee, i + 1, "<付费代码>sheet页");
                if (r.getStatusCode() == 200) {
                } else {
                    return r;
                }

                //设置文本格式防止转换异常
                if (row.getCell(0) != null) {
                    row.getCell(0).setCellType(CellType.STRING);
                }

                if (row.getCell(0) != null && StrUtil.isNotEmpty(row.getCell(0).getStringCellValue())) {

                    String containerNo2 = row.getCell(0).getStringCellValue();
                    containerNo2 = containerNo2.trim();
                    boolean flag3 = CheckUtil.verifyCntrCode(containerNo2);
                    if (flag3) {
                    } else {
                        return new R<>(new Throwable("请检查<付费代码>sheet页，第" + (i + 1) + "行箱号填写有误，如有疑问请联系管理员"));
                    }
                    containerNo2 = containerNo2.trim();
                    charge.setContainerNo(containerNo2);//箱号
                    charge.setCarrierName(row.getCell(1).getStringCellValue());//承运人简称
                    charge.setPayerName(row.getCell(2).getStringCellValue());//支付人名称
                    charge.setPayerCode(row.getCell(3).getStringCellValue());//支付人代码
                    if (row.getCell(4) != null) {
                        row.getCell(4).setCellType(CellType.STRING);
                    }
                    if (row.getCell(5) != null) {
                        row.getCell(5).setCellType(CellType.STRING);
                    }
                    if (row.getCell(4) != null && !row.getCell(4).getStringCellValue().equals("")) {
                        charge.setContractNo(row.getCell(4).getStringCellValue());//支付合同号
                    }
                    try {
                        if (row.getCell(5) != null && !row.getCell(5).getStringCellValue().equals("")) {
                            row.getCell(5).setCellType(CellType.STRING);
                            Date paymentDate = sdf2.parse(row.getCell(5).getStringCellValue());
                            charge.setPaymentDate(paymentDate);//支付日期
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        return new R<>(new Throwable("导入<付费代码>sheet页失败，第" + (i + 1) + "行支付日期填写有误"));
                    }
                    listConCharge.add(charge);

                } else {
                    List<PayCodeMes> listConChargeTwo = new ArrayList<>();
                    for (String strList : stringList) {
                        PayCodeMes chargeTwo = new PayCodeMes();
                        chargeTwo.setHxAppNo(businessid);
                        if (strList != null) {
                            chargeTwo.setContainerNo(strList);//箱号
                        } else {
                            return new R<>(new Throwable("请检查<运单舱单信息(必填)>sheet页，箱号为空，如有疑问请联系管理员"));
                        }
                        chargeTwo.setCarrierName(row.getCell(1).getStringCellValue());//承运人简称
                        chargeTwo.setPayerName(row.getCell(2).getStringCellValue());//支付人名称
                        chargeTwo.setPayerCode(row.getCell(3).getStringCellValue());//支付人代码
                        if (row.getCell(4) != null) {
                            row.getCell(4).setCellType(CellType.STRING);
                        }
                        if (row.getCell(5) != null) {
                            row.getCell(5).setCellType(CellType.STRING);
                        }
                        if (row.getCell(4) != null && !row.getCell(4).getStringCellValue().equals("")) {
                            chargeTwo.setContractNo(row.getCell(4).getStringCellValue());//支付合同号
                        }
                        try {
                            if (row.getCell(5) != null && !row.getCell(5).getStringCellValue().equals("")) {
                                row.getCell(5).setCellType(CellType.STRING);
                                Date paymentDate = sdf2.parse(row.getCell(5).getStringCellValue());
                                chargeTwo.setPaymentDate(paymentDate);//支付日期
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            return new R<>(new Throwable("导入<付费代码>sheet页失败，第" + (i + 1) + "行支付日期填写有误"));
                        }
                        listConChargeTwo.add(chargeTwo);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(e.getMessage() + "##################导入<付费代码>sheet页失败########################");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new R<>(new Throwable("导入<付费代码>sheet页失败，请检查日期、小数等特殊单元格格式，检查无误后，请联系管理员" + e.getMessage()));
        }

        /*<货物信息>sheet*/
        try {
            //导入第三张sheet页，现为"货物信息"
            Sheet sheet3rd = workbook.getSheetAt(2);
            //获取行数
            int lastRowNum3rd = sheet3rd.getLastRowNum();
            Cell serialNo = sheet3rd.getRow(lastRowNum3rd).getCell(0);//最后一行序号
            Cell containerNo2 = sheet3rd.getRow(lastRowNum3rd).getCell(1);//最后一行箱号
            /*最后一行为空白行的话，最后一行下标减一*/
            if (serialNo == null && containerNo2 == null && lastRowNum3rd > 3) {//第一行数据下标为3，4为有两条数据
                lastRowNum3rd = lastRowNum3rd - 1;
            }
            //循环校验标识
            Boolean cirFlag = true;
            for (int i = 3; i <= lastRowNum3rd; i++) {
                WaybillGoodsInfo goods = new WaybillGoodsInfo();
                Row row = sheet3rd.getRow(i);
                boolean blankFlag = CheckUtil.isRowEmpty(row);
                if (blankFlag == true) {
                    continue;
                }
                goods.setRowId(UUID.randomUUID().toString());
                goods.setHxAppNo(businessid);
                List<Cell> cellListGoo = new ArrayList();
                cellListGoo.add(row.getCell(0));
                cellListGoo.add(row.getCell(1));
                cellListGoo.add(row.getCell(4));
                cellListGoo.add(row.getCell(7));
                cellListGoo.add(row.getCell(8));
                cellListGoo.add(row.getCell(9));
                R r = this.judgeNone(cellListGoo, i + 1, "<货物信息>sheet页");
                if (r.getStatusCode() == 200) {
                } else {
                    return r;
                }
                //设置文本格式防止转换异常
                if (row.getCell(0) != null) {
                    row.getCell(0).setCellType(CellType.STRING);
                } else {
                    return new R<>(new Throwable("请检查<货物信息>sheet页，第" + (i + 1) + "行箱号未填写或者存在末尾空白行，如有疑问请联系管理员"));
                }
                String containerNo3 = row.getCell(0).getStringCellValue();
                containerNo3 = containerNo3.trim();

                boolean flag4 = CheckUtil.verifyCntrCode(containerNo3);
                if (flag4) {
                } else {
                    return new R<>(new Throwable("请检查<货物信息>sheet页，第" + (i + 1) + "行箱号填写有误，如有疑问请联系管理员"));
                }
                goods.setContainerNo(containerNo3);//箱号
                row.getCell(1).setCellType(CellType.STRING);
                if (row.getCell(2) != null) {
                    row.getCell(2).setCellType(CellType.STRING);
                }
                if (row.getCell(3) != null) {
                    row.getCell(3).setCellType(CellType.STRING);
                }
                if (row.getCell(5) != null) {
                    row.getCell(5).setCellType(CellType.STRING);
                }
                if (row.getCell(6) != null) {
                    row.getCell(6).setCellType(CellType.STRING);
                }
                if (row.getCell(10) != null) {
                    row.getCell(10).setCellType(CellType.STRING);
                }
                if (row.getCell(11) != null) {
                    row.getCell(11).setCellType(CellType.STRING);
                }
                goods.setGoodsCode(row.getCell(1).getStringCellValue());//HS CODE 货物编码  ??????
                if (row.getCell(2) != null && !row.getCell(2).getStringCellValue().equals("")) {
                    goods.setGngCode(row.getCell(2).getStringCellValue());//GNG 通用货物编码
                }
                if (row.getCell(3) != null && !row.getCell(3).getStringCellValue().equals("")) {
                    goods.setEtCode(row.getCell(3).getStringCellValue());//ET 统一运价货物统计编码
                }
                goods.setGoodsChineseName(row.getCell(4).getStringCellValue());//货物中文
                if (row.getCell(5) != null && !row.getCell(5).getStringCellValue().equals("")) {
                    goods.setGoodsEnglishName(row.getCell(5).getStringCellValue());//货物英文
                }
                if (row.getCell(6) != null && !row.getCell(6).getStringCellValue().equals("")) {
                    goods.setGoodsRussianName(row.getCell(6).getStringCellValue());//货物俄文
                }
                goods.setPackageType(row.getCell(7).getStringCellValue());//包装种类
                row.getCell(8).setCellType(CellType.STRING);
                goods.setGoodsNums(row.getCell(8).getStringCellValue());//件数
                try {
                    goods.setGoodsWeight(Float.parseFloat(row.getCell(9).getStringCellValue()));//货物重量
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (row.getCell(10) != null && !row.getCell(10).getStringCellValue().equals("")) {
                    goods.setDangerousGoodsCode(row.getCell(10).getStringCellValue());//危险品编码
                }
                if (row.getCell(11) != null && !row.getCell(11).getStringCellValue().equals("")) {
                    goods.setGoodsRemarks(row.getCell(11).getStringCellValue());//货物备注
                }
                goodsList.add(goods);     //for save
            }

        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(e.getMessage() + "##################导入<货物信息>sheet页失败########################");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new R<>(new Throwable("导入<货物信息>sheet页失败，请检查日期、小数等特殊单元格格式，检查无误后，请联系管理员" + e.getMessage()));
        }


        if (CollUtil.isNotEmpty(listCon)) {
            for (BasChangeboxContainerInfo info : listCon) {
                basChangeboxContainerInfoMapper.deleteByBussinessId(businessid, info.getContainerNo());
                info.setRowId(UUID.randomUUID().toString());
                basChangeboxContainerInfoMapper.insertBasChangeboxContainerInfo(info);
            }

        }

        if (CollUtil.isNotEmpty(listPants)) {
            for (WaybillParticipants participants : listPants) {
                waybillParticipantsMapper.deleteByBussinessId(businessid, participants.getContainerNo());
                participants.setRowId(UUID.randomUUID().toString());
            }
            waybillParticipantsMapper.insertWaybillParticipants(listPants);
        }


        if (CollUtil.isNotEmpty(goodsList)) {
            for (WaybillGoodsInfo goodsInfo : goodsList) {
                waybillGoodsInfoMapper.deleteByBussinessId(businessid, goodsInfo.getContainerNo());
                goodsInfo.setRowId(UUID.randomUUID().toString());
            }
            waybillGoodsInfoMapper.insertWaybillGoodsInfo(goodsList);
        }


        if (CollUtil.isNotEmpty(listConCharge)) {
            for (PayCodeMes payCodeMes : listConCharge) {
                payCodeMesMapper.deleteByBussinessId(businessid, payCodeMes.getContainerNo());
                payCodeMes.setRowId(UUID.randomUUID().toString());
                payCodeMesMapper.insertPayCodeMes(payCodeMes);
            }
        }
        return new R<>(0, Boolean.TRUE, null, "导入完成");
    }

    /*
     * 判断Cell集合是否存在空值
     */
    private R judgeNone(List<Cell> list, int i, String model) {
        if (list != null && list.size() != 0) {
            for (Cell cell : list) {
                if (cell != null) {
                    /*有数字、日期等特殊格式的先转为文本格式（否则非空的特殊格式字段会发生转换异常），进行下一步非空校验*/
                    cell.setCellType(CellType.STRING);
                }
                //判断单元格非空
                if (cell != null && !cell.getStringCellValue().equals("")) {
                } else {
                    return new R<>(new Throwable("请检查" + model + "第" + i + "行是否存在未填项，或末尾空白行"));
                }
            }
            return new R(200, Boolean.TRUE, "校验正确");
        } else {
            return new R<>(new Throwable("请检查" + model + "第" + i + "行是否存在未填项，或末尾空白行"));
        }
    }

    @Override
    public void exportedTz(HttpServletResponse response) throws Exception {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("台账导入模板");
        XSSFRow row = sheet.createRow(0);
        //宽度
        for (int i = 0; i <= 16; i++) {
            sheet.setColumnWidth(i, 60 * 80);
        }
        row.setHeight((short) (10 * 50));
        //字体
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");

        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);//水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setBorderBottom(BorderStyle.THIN); //下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);

        XSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("序号");
        cell0.setCellStyle(style);
        XSSFCell cell1 = row.createCell(1);
        cell1.setCellValue("类型");
        cell1.setCellStyle(style);
        XSSFCell cell2 = row.createCell(2);
        cell2.setCellValue("货源组织单位");
        cell2.setCellStyle(style);
        XSSFCell cell3 = row.createCell(3);
        cell3.setCellValue("收货人(必填)");
        cell3.setCellStyle(style);
        XSSFCell cell4 = row.createCell(4);
        cell4.setCellValue("发站(必填)");
        cell4.setCellStyle(style);
        XSSFCell cell5 = row.createCell(5);
        cell5.setCellValue("目的国(必填)");
        cell5.setCellStyle(style);
        XSSFCell cell6 = row.createCell(6);
        cell6.setCellValue("到站(必填)");
        cell6.setCellStyle(style);
        XSSFCell cell7 = row.createCell(7);
        cell7.setCellValue("口岸代理");
        cell7.setCellStyle(style);
        XSSFCell cell8 = row.createCell(8);
        cell8.setCellValue("品名(必填)");
        cell8.setCellStyle(style);
        XSSFCell cell9 = row.createCell(9);
        cell9.setCellValue("箱型(必填)");
        cell9.setCellStyle(style);
        XSSFCell cell10 = row.createCell(10);
        cell10.setCellValue("箱属(必填)");
        cell10.setCellStyle(style);
        XSSFCell cell11 = row.createCell(11);
        cell11.setCellValue("箱号(必填)");
        cell11.setCellStyle(style);
        XSSFCell cell12 = row.createCell(12);
        cell12.setCellValue("件数");
        cell12.setCellStyle(style);
        XSSFCell cell13 = row.createCell(13);
        cell13.setCellValue("货重");
        cell13.setCellStyle(style);
        XSSFCell cell14 = row.createCell(14);
        cell14.setCellValue("箱重");
        cell14.setCellStyle(style);
        XSSFCell cell15 = row.createCell(15);
        cell15.setCellValue("是否全程(必填)");
        cell15.setCellStyle(style);
        XSSFCell cell16 = row.createCell(16);
        cell16.setCellValue("有色金属(必填)");
        cell16.setCellStyle(style);

        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String("台账导入模板.xlsx".getBytes("GB2312"), "8859_1"));
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R importedTz(MultipartFile file, String businessid) {
        R r = new R();
        try {
            String fileName = file.getOriginalFilename();
            InputStream inputStream = file.getInputStream();
            // 调用工具类中方法，读取excel文件中数据
//            long i = System.currentTimeMillis();
            boolean ret = isXls(fileName);
            Workbook workbook = null;
            // 根据后缀创建不同的对象
            if (ret) {
                workbook = new HSSFWorkbook(inputStream);
            } else {
                workbook = new XSSFWorkbook(inputStream);
            }
            Sheet sheet = getAccuracyContextNum(workbook);
            BasChangeboxRetreat sel = new BasChangeboxRetreat();
            sel.setBusinessid(businessid);
            sel.setDeleteFlag("N");
            List<BasChangeboxRetreat> basChangeboxRetreats = basChangeboxRetreatMapper.selectBasChangeboxRetreatList(sel);
            if (CollUtil.isEmpty(basChangeboxRetreats)) {
                r.setMsg("根据主键查询信息为空");
                r.setB(Boolean.FALSE);
                r.setCode(500);
                return r;
            }
            WaybillHeader waybill = new WaybillHeader();
            waybill.setShiftNo(basChangeboxRetreats.get(0).getShiftNo());
            waybill.setCustomerNo(basChangeboxRetreats.get(0).getCustomerNo());
            waybill.setPlatformCode(basChangeboxRetreats.get(0).getPlatformCode());
            waybill.setBusinessId(businessid);
            waybill.setDeleteFlag("N");
            List<WaybillContainerInfo> waybillContainerInfosWaybills = waybillContainerInfoMapper.selectToChangeList(waybill);
            StringBuilder stringBuilder = new StringBuilder();
            for (WaybillContainerInfo waybills : waybillContainerInfosWaybills) {
                stringBuilder.append(waybills.getContainerNo()).append(",");
            }
            List<WaybillHeader> waybillHeaders = waybillHeaderMapper.selectWaybillHeaderList(waybill);

            int lastRowNum = sheet.getLastRowNum();

            ContainerTypeData sel2 = new ContainerTypeData();
            sel2.setDeleteFlag("N");
            List<ContainerTypeData> containerTypeDataList = containerTypeDataMapper.selectContainerTypeDataList(sel2);

            List<String> containerNoList = basChangeboxContainerInfoMapper.selectContainerNoList(businessid);

            String countryType = remoteAdminService.selectDictByType2("country_type");
            List<SysDictVo> countryList = JSONUtil.toList(JSONUtil.parseArray(countryType), SysDictVo.class);

            List<StationManagement> stationManagements = stationManagementMapper.selectStationManagementList(null);

            List<BasChangeboxContainerInfo> listCon = new ArrayList();
            List<WaybillParticipants> listPants = new ArrayList<>();
            List<WaybillGoodsInfo> goodsList = new ArrayList<>();
            if (lastRowNum > 0) {
                SecruityUser userInfo = SecurityUtils.getUserInfo();
                for (int i = 1; i <= lastRowNum; i++) {
                    BasChangeboxContainerInfo waybillContainerInfo = new BasChangeboxContainerInfo();
                    waybillContainerInfo.setRowId(UUID.randomUUID().toString());
                    waybillContainerInfo.setBusinessid(businessid);

                    //收货人
                    WaybillParticipants waybillParticipantsShou = new WaybillParticipants();
                    waybillParticipantsShou.setParticipantsType("S");
                    waybillParticipantsShou.setHxAppNo(businessid);
                    //发货人
                    WaybillParticipants waybillParticipantsFa = new WaybillParticipants();
                    waybillParticipantsFa.setParticipantsType("F");
                    waybillParticipantsFa.setHxAppNo(businessid);

                    WaybillGoodsInfo waybillGoodsInfo = new WaybillGoodsInfo();
                    waybillGoodsInfo.setRowId(UUID.randomUUID().toString());
                    waybillGoodsInfo.setHxAppNo(businessid);
                    waybillGoodsInfo.setAddWho(userInfo.getUserName());
                    waybillGoodsInfo.setAddWhoName(userInfo.getRealName());
                    waybillGoodsInfo.setAddTime(LocalDateTime.now());

                    if (CollUtil.isNotEmpty(waybillHeaders)) {
                        waybillContainerInfo.setOrderNo(waybillHeaders.get(0).getOrderNo());
                        waybillContainerInfo.setWaybillNo(waybillHeaders.get(0).getWaybillNo());
                        waybillParticipantsShou.setWaybillNo(waybillHeaders.get(0).getWaybillNo());
                        waybillParticipantsFa.setWaybillNo(waybillHeaders.get(0).getWaybillNo());
                        waybillGoodsInfo.setWaybillNo(waybillHeaders.get(0).getWaybillNo());
                    }

                    Row row = sheet.getRow(i);
                    //箱号
                    if (row.getCell(11) != null) {
                        row.getCell(11).setCellType(CellType.STRING);
                        if (!"".equals(row.getCell(11).getStringCellValue()) && row.getCell(11).getStringCellValue() != null) {
                            String containerNo = row.getCell(11).getStringCellValue().replaceAll(" ", "");
                            if (!stringBuilder.toString().contains(containerNo)) {
                                boolean flag4 = CheckUtil.verifyCntrCode(containerNo);
                                if (flag4) {
                                } else {
                                    r.setB(Boolean.FALSE);
                                    r.setMsg(containerNo + "此箱号填写有误");
                                    return r;
                                }

                                if (CollUtil.isNotEmpty(containerNoList) && containerNoList.contains(containerNo)) {
                                    r.setB(Boolean.FALSE);
                                    r.setMsg(containerNo + "该箱号已存在");
                                    return r;
                                }

                                waybillContainerInfo.setContainerNo(containerNo);
                                waybillParticipantsShou.setContainerNo(containerNo);
                                waybillGoodsInfo.setContainerNo(containerNo);
                                waybillParticipantsFa.setContainerNo(containerNo);
                            } else {
                                r.setB(Boolean.FALSE);
                                r.setMsg("请确认" + row.getCell(11).getStringCellValue().replaceAll(" ", "") + "此箱号是否已经存在");
                                return r;
                            }
                        } else {
                            r.setB(Boolean.FALSE);
                            r.setMsg("箱号为空");
                            return r;
                        }
                        //箱型
                        /*if (row.getCell(9) != null) {
                            row.getCell(9).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(9).getStringCellValue()) && row.getCell(9).getStringCellValue() != null) {
//                            waybillContainerInfo.setContainerType(row.getCell(2).getStringCellValue().replaceAll(" ", ""));

                                final String containerTypeCode = row.getCell(9).getStringCellValue().replaceAll(" ", "");
                                Boolean flag = true;
                                for (ContainerTypeData data : containerTypeDataList
                                ) {
                                    if (data.getContainerTypeCode().equals(containerTypeCode)) {
                                        waybillContainerInfo.setContainerTypeCode(data.getContainerTypeCode());
                                        waybillContainerInfo.setContainerTypeName(data.getContainerTypeName());
                                        waybillContainerInfo.setContainerType(data.getContainerTypeSize());
                                        flag = false;
                                        break;
                                    }
                                }

                                if (flag) {
                                    r.setB(Boolean.FALSE);
                                    r.setMsg("未查询到该箱型代码：" + containerTypeCode);
                                    return r;
                                }

                            } else {
                                r.setB(Boolean.FALSE);
                                r.setMsg("箱型代码为空");
                                return r;
                            }
                        } else {
                            r.setB(Boolean.FALSE);
                            r.setMsg("箱型代码为空");
                            return r;
                        }*/
                        //类型
                        if (row.getCell(1) != null) {
                            row.getCell(1).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(1).getStringCellValue()) && row.getCell(1).getStringCellValue() != null) {
                                if (row.getCell(1).getStringCellValue().trim().equals("出口")) {
                                    waybillContainerInfo.setIdentification("E");
                                } else if (row.getCell(1).getStringCellValue().trim().equals("进口")) {
                                    waybillContainerInfo.setIdentification("I");
                                } else if (row.getCell(1).getStringCellValue().trim().equals("过境")) {
                                    waybillContainerInfo.setIdentification("P");
                                }
                            }
                        }

                        //收货人
                        if (row.getCell(3) != null) {
                            row.getCell(3).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(3).getStringCellValue()) && row.getCell(3).getStringCellValue() != null) {
                                waybillParticipantsShou.setConsignorName(row.getCell(3).getStringCellValue().trim());
                            } else {
                                r.setB(Boolean.FALSE);
                                r.setMsg("收货人为空");
                                return r;
                            }
                        } else {
                            r.setB(Boolean.FALSE);
                            r.setMsg("收货人为空");
                            return r;
                        }

                        //发站
                        if (row.getCell(4) != null) {
                            row.getCell(4).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(4).getStringCellValue()) && row.getCell(4).getStringCellValue() != null) {
                                String stationNameF = row.getCell(4).getStringCellValue().trim();
                                if (CollUtil.isNotEmpty(stationManagements)) {
                                    for (StationManagement stationManagement : stationManagements) {
                                        if (stationManagement.getStationName().equals(stationNameF)) {
                                            waybillContainerInfo.setStartStationName(stationNameF);
                                            waybillContainerInfo.setDestinationName(stationNameF);
                                            waybillContainerInfo.setStationCompilation(stationManagement.getStationCode());
                                            break;
                                        }

                                    }
                                }
                                if (StrUtil.isBlank(waybillContainerInfo.getStationCompilation())) {
                                    r.setB(Boolean.FALSE);
                                    r.setMsg("发站不存在：" + stationNameF);
                                    return r;
                                }
                            } else {
                                r.setB(Boolean.FALSE);
                                r.setMsg("发站为空");
                                return r;
                            }
                        } else {
                            r.setB(Boolean.FALSE);
                            r.setMsg("发站为空");
                            return r;
                        }

                        //收货人所属国家代码
                        if (row.getCell(5) != null) {
                            row.getCell(5).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(5).getStringCellValue()) && row.getCell(5).getStringCellValue() != null) {
                                String name = row.getCell(5).getStringCellValue().trim();
                                if (CollUtil.isNotEmpty(countryList)) {
                                    for (SysDictVo s : countryList) {
                                        if (s.getName().equals(name)) {
                                            waybillParticipantsShou.setCountryCode(s.getCode());
                                        }
                                    }
                                }
                                if (StrUtil.isBlank(waybillParticipantsShou.getCountryCode())) {
                                    r.setB(Boolean.FALSE);
                                    r.setMsg("目的国不存在:" + name);
                                    return r;
                                }
                            } else {
                                r.setB(Boolean.FALSE);
                                r.setMsg("目的国为空");
                                return r;
                            }
                        } else {
                            r.setB(Boolean.FALSE);
                            r.setMsg("目的国为空");
                            return r;
                        }

                        //到站
                        if (row.getCell(6) != null) {
                            row.getCell(6).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(6).getStringCellValue()) && row.getCell(6).getStringCellValue() != null) {
                                String stationNameD = row.getCell(6).getStringCellValue().trim();
                                if (CollUtil.isNotEmpty(stationManagements)) {
                                    for (StationManagement stationManagement : stationManagements) {
                                        if (stationManagement.getStationName().equals(stationNameD)) {
                                            waybillContainerInfo.setEndStationName(stationNameD);
                                            waybillContainerInfo.setDestination(stationNameD);
                                            waybillContainerInfo.setEndCompilation(stationManagement.getStationCode());
                                            break;
                                        }

                                    }
                                }
                                if (StrUtil.isBlank(waybillContainerInfo.getEndCompilation())) {
                                    r.setB(Boolean.FALSE);
                                    r.setMsg("到站不存在：" + stationNameD);
                                    return r;
                                }
                            } else {
                                r.setB(Boolean.FALSE);
                                r.setMsg("到站为空");
                                return r;
                            }
                        } else {
                            r.setB(Boolean.FALSE);
                            r.setMsg("到站为空");
                            return r;
                        }

                        //口岸代理
                        if (row.getCell(7) != null) {
                            row.getCell(7).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(7).getStringCellValue()) && row.getCell(7).getStringCellValue() != null) {
                                waybillContainerInfo.setPortAgent(row.getCell(7).getStringCellValue().trim());
                            }
                        }

                        //品名
                        if (row.getCell(8) != null) {
                            row.getCell(8).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(8).getStringCellValue()) && row.getCell(8).getStringCellValue() != null) {
                                waybillGoodsInfo.setGoodsChineseName(row.getCell(8).getStringCellValue().trim());
                                waybillContainerInfo.setGoodsName(row.getCell(8).getStringCellValue().trim());
                            } else {
                                r.setB(Boolean.FALSE);
                                r.setMsg("品名为空");
                                return r;
                            }
                        } else {
                            r.setB(Boolean.FALSE);
                            r.setMsg("品名为空");
                            return r;
                        }

                        //箱型
                        if (row.getCell(9) != null) {
                            row.getCell(9).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(9).getStringCellValue()) && row.getCell(9).getStringCellValue() != null) {
//                            waybillContainerInfo.setContainerType(row.getCell(2).getStringCellValue().replaceAll(" ", ""));

                                final String containerTypeCode = row.getCell(9).getStringCellValue().replaceAll(" ", "");
                                Boolean flag = true;
                                for (ContainerTypeData data : containerTypeDataList) {
                                    if (data.getContainerTypeCode().equals(containerTypeCode)) {
                                        waybillContainerInfo.setContainerTypeCode(data.getContainerTypeCode());
                                        waybillContainerInfo.setContainerTypeName(data.getContainerTypeName());
                                        waybillContainerInfo.setContainerType(data.getContainerTypeSize());
                                        flag = false;
                                        break;
                                    }
                                }

                                if (flag) {
                                    r.setB(Boolean.FALSE);
                                    r.setMsg("未查询到该箱型代码：" + containerTypeCode);
                                    return r;
                                }

                            } else {
                                r.setB(Boolean.FALSE);
                                r.setMsg("箱型代码为空");
                                return r;
                            }
                        } else {
                            r.setB(Boolean.FALSE);
                            r.setMsg("箱型代码为空");
                            return r;
                        }

                        //箱属
                        if (row.getCell(10) != null) {
                            row.getCell(10).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(10).getStringCellValue()) && row.getCell(10).getStringCellValue() != null) {
                                if (row.getCell(10).getStringCellValue().trim().equals("自备箱")) {
                                    waybillContainerInfo.setContainerOwner("0");
                                } else if (row.getCell(10).getStringCellValue().trim().equals("中铁箱")) {
                                    waybillContainerInfo.setContainerOwner("1");
                                }
                            } else {
                                r.setB(Boolean.FALSE);
                                r.setMsg("箱属为空");
                                return r;
                            }
                        } else {
                            r.setB(Boolean.FALSE);
                            r.setMsg("箱属为空");
                            return r;
                        }

                        //件数
                        if (row.getCell(12) != null) {
                            row.getCell(12).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(12).getStringCellValue()) && row.getCell(12).getStringCellValue() != null) {
                                waybillGoodsInfo.setGoodsNums(row.getCell(12).getStringCellValue().trim());
                            }
                        }

                        //货重
                        if (row.getCell(13) != null) {
                            row.getCell(13).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(13).getStringCellValue()) && row.getCell(13).getStringCellValue() != null) {
                                waybillGoodsInfo.setGoodsWeight(Float.valueOf(row.getCell(13).getStringCellValue().trim()));
                            }
                        }
                        //箱重
                        if (row.getCell(14) != null) {
                            row.getCell(14).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(14).getStringCellValue()) && row.getCell(14).getStringCellValue() != null) {
                                waybillContainerInfo.setContainerDeadWeight(Float.valueOf(row.getCell(14).getStringCellValue().trim()));
                            }
                        }

                        //是否全程
                        if (row.getCell(15) != null) {
                            row.getCell(15).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(15).getStringCellValue()) && row.getCell(15).getStringCellValue() != null) {
                                if (row.getCell(15).getStringCellValue().trim().equals("是")) {
                                    waybillContainerInfo.setIsFull("1");
                                } else if (row.getCell(15).getStringCellValue().trim().equals("否")) {
                                    waybillContainerInfo.setIsFull("0");
                                }
                            }
                        }

                        //有色金属
                        if (row.getCell(16) != null) {
                            row.getCell(16).setCellType(CellType.STRING);
                            if (!"".equals(row.getCell(16).getStringCellValue()) && row.getCell(16).getStringCellValue() != null) {
                                if (row.getCell(16).getStringCellValue().trim().equals("是")) {
                                    waybillContainerInfo.setNonFerrous("1");
                                } else if (row.getCell(16).getStringCellValue().trim().equals("否")) {
                                    waybillContainerInfo.setNonFerrous("0");
                                }
                            }
                        }
                    }
                    waybillParticipantsFa.setRowId(UUID.randomUUID().toString());
                    waybillParticipantsFa.setAddWho(userInfo.getUserName());
                    waybillParticipantsFa.setAddWhoName(userInfo.getRealName());
                    waybillParticipantsFa.setAddTime(LocalDateTime.now());

                    waybillParticipantsShou.setAddWho(userInfo.getUserName());
                    waybillParticipantsShou.setAddWhoName(userInfo.getRealName());
                    waybillParticipantsShou.setAddTime(LocalDateTime.now());
                    waybillParticipantsShou.setRowId(UUID.randomUUID().toString());

                    listCon.add(waybillContainerInfo);
                    listPants.add(waybillParticipantsFa);
                    listPants.add(waybillParticipantsShou);
                    goodsList.add(waybillGoodsInfo);
                }
                if (CollUtil.isNotEmpty(listCon)) {
                    for (BasChangeboxContainerInfo info : listCon) {
                        basChangeboxContainerInfoMapper.deleteByBussinessId(businessid, info.getContainerNo());
                        info.setRowId(UUID.randomUUID().toString());
                        info.setBoxnumberStatus("2");
                        basChangeboxContainerInfoMapper.insertBasChangeboxContainerInfo(info);
                    }

                }

                if (CollUtil.isNotEmpty(listPants)) {
                    for (WaybillParticipants participants : listPants) {
                        waybillParticipantsMapper.deleteByBussinessId(businessid, participants.getContainerNo());
                        participants.setRowId(UUID.randomUUID().toString());
                    }
                    waybillParticipantsMapper.insertWaybillParticipants(listPants);
                }


                if (CollUtil.isNotEmpty(goodsList)) {
                    for (WaybillGoodsInfo goodsInfo : goodsList) {
                        waybillGoodsInfoMapper.deleteByBussinessId(businessid, goodsInfo.getContainerNo());
                        goodsInfo.setRowId(UUID.randomUUID().toString());
                    }
                    waybillGoodsInfoMapper.insertWaybillGoodsInfo(goodsList);
                }
            } else {
                r.setB(Boolean.FALSE);
                r.setMsg("表格为空");
                return r;
            }
            workbook.close();
            r.setMsg("上传成功");
            r.setB(Boolean.TRUE);
            inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return r;
    }

    public static boolean isXls(String fileName) {
        // (?i)忽略大小写
        if (fileName.matches("^.+\\.(?i)(xls)$")) {
            return true;
        } else if (fileName.matches("^.+\\.(?i)(xlsx)$")) {
            return false;
        } else {
            throw new RuntimeException("格式不对");
        }
    }

    public Sheet getAccuracyContextNum(Workbook workbook) {
        // 取第一个sheet
        Sheet sheet = workbook.getSheetAt(0);
        // 删除空行
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            // 删除空行
            if (this.isRowEmpty(row)) {
                int lastRowNum = sheet.getLastRowNum();
                if (i >= 0 && i < lastRowNum) {
                    sheet.shiftRows(i + 1, lastRowNum, -1);// 将行号为i+1一直到行号为lastRowNum的单元格全部上移一行，以便删除i行
                }
                if (i == lastRowNum) {
                    if (row != null) {
                        sheet.removeRow(row);
                    }
                }
                i--;
            }
        }
        return sheet;
    }

    public boolean isRowEmpty(Row row) {
        if (row != null) {
            for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
                Cell cell = row.getCell(c);
                if (cell != null) {
                    cell.setCellType(CellType.STRING);
                }
                if (cell != null && !cell.getStringCellValue().equals("")) {
                    return false;//不是空行
                }
            }
        }
        return true;
    }
}
