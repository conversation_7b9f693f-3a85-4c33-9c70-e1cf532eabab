package com.huazheng.tunny.ocean.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.FdBusCostDetailDTO;
import com.huazheng.tunny.ocean.api.entity.FdBusCost;
import com.huazheng.tunny.ocean.api.entity.FdBusCostDetail;
import com.huazheng.tunny.ocean.api.entity.WaybillContainerInfo;
import com.huazheng.tunny.ocean.service.FdBusCostDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 业务流程单子表
 *
 * <AUTHOR>
 * @date 2024-05-23 17:10:53
 */
@Slf4j
@RestController
@RequestMapping("/fdbuscostdetail")
public class FdBusCostDetailController {

    @Autowired
    private FdBusCostDetailService fdBusCostDetailService;

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdBusCostDetailService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdBusCostDetailService.selectFdBusCostDetailListByLike(new Query<>(params));
    }

    /**
     * 应收费用明细列表
     *
     * @param params
     * @return
     */
    @GetMapping("/reveivePage")
    public Map reveivePage(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdBusCostDetailService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdBusCostDetailService.reveivePage(new Query<>(params));
    }

    /**
     * 应付费用明细列表
     *
     * @param params
     * @return
     */
    @GetMapping("/payPage")
    public Map payPage(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdBusCostDetailService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdBusCostDetailService.payPage(new Query<>(params));
    }

    /**
     * 信息
     *
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Integer id) {
        FdBusCostDetail fdBusCostDetail = fdBusCostDetailService.selectById(id);
        return new R<>(fdBusCostDetail);
    }

    /**
     * 保存
     *
     * @param fdBusCostDetail
     * @return R
     */
    @PostMapping
    public R save(@RequestBody FdBusCostDetail fdBusCostDetail) {
        fdBusCostDetailService.insert(fdBusCostDetail);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 保存应收
     *
     * @param fdBusCostDetail
     * @return R
     */
    @PostMapping("/saveReceive")
    public R saveReceive(@RequestBody FdBusCostDetail fdBusCostDetail) {
        return fdBusCostDetailService.saveReceive(fdBusCostDetail);
    }

    /**
     * 保存应收List
     *
     * @param fdBusCostDetail
     * @return R
     */
    @PostMapping("/saveReceiveList")
    public R saveReceiveList(@RequestBody FdBusCostDetail fdBusCostDetail) {
        return fdBusCostDetailService.saveReceiveList(fdBusCostDetail);
    }

    /**
     * 批量保存应收List
     *
     * @param fdBusCost
     * @return R
     */
    @PostMapping("/saveList")
    public R saveList(@RequestBody FdBusCost fdBusCost) {
        return fdBusCostDetailService.saveList(fdBusCost);
    }

    /**
     * 保存应付
     *
     * @param fdBusCostDetail
     * @return R
     */
    @PostMapping("/savePay")
    public R savePay(@RequestBody FdBusCostDetail fdBusCostDetail) {
        return fdBusCostDetailService.savePay(fdBusCostDetail);
    }

    /**
     * 保存应付List
     *
     * @param fdBusCostDetail
     * @return R
     */
    @PostMapping("/savePayList")
    public R savePayList(@RequestBody FdBusCostDetail fdBusCostDetail) {
        return fdBusCostDetailService.savePayList(fdBusCostDetail);
    }

    /**
     * 修改
     *
     * @param fdBusCostDetail
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody FdBusCostDetail fdBusCostDetail) {
        fdBusCostDetailService.updateById(fdBusCostDetail);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改应收
     *
     * @param fdBusCostDetail
     * @return R
     */
    @PostMapping("/updateReceive")
    public R updateReceive(@RequestBody FdBusCostDetail fdBusCostDetail) {
        fdBusCostDetailService.updateReceive(fdBusCostDetail);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改应付
     *
     * @param fdBusCostDetail
     * @return R
     */
    @PostMapping("/updatePay")
    public R updatePay(@RequestBody FdBusCostDetail fdBusCostDetail) {
        fdBusCostDetailService.updatePay(fdBusCostDetail);
        return new R<>(Boolean.TRUE);
    }


    /**
     * 删除
     *
     * @param id
     * @return R
     */
    @GetMapping("/del/{id}")
    public R delete(@PathVariable Integer id) {
        fdBusCostDetailService.deleteFdBusCostDetailById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody Integer[] ids) {
        return fdBusCostDetailService.deleteFdBusCostDetailByIds(ids);
    }

    /**
     * 导出EXCEL
     *
     * @return
     */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {

        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<FdBusCostDetail> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<FdBusCostDetail> list = fdBusCostDetailService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), FdBusCostDetail.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }


    /**
     * 导入EXCEL
     * <p>
     *
     * @param titleStr
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/imported")
    @Transactional
    public R imported(String titleStr, MultipartFile file) throws Exception {

        return new R<>(Boolean.TRUE);
    }

    /**
     * 获取业务流程单箱信息
     *
     * @param fdBusCostDetail
     * @return R
     */
    @PostMapping("/selectListWithBusCost")
    public R<List<WaybillContainerInfo>> selectListWithBusCost(@RequestBody FdBusCostDetail fdBusCostDetail) {
        List<WaybillContainerInfo> infos = fdBusCostDetailService.selectListWithBusCost(fdBusCostDetail);
        return new R<>(infos);
    }

    /**
     * 获取业务流程单箱信息(带删除)
     *
     * @param fdBusCostDetail
     * @return R
     */
    @PostMapping("/selectListWithBusCostDel")
    public R<List<WaybillContainerInfo>> selectListWithBusCostDel(@RequestBody FdBusCostDetail fdBusCostDetail) {
        List<WaybillContainerInfo> infos = fdBusCostDetailService.selectListWithBusCostDel(fdBusCostDetail);
        return new R<>(infos);
    }

    /**
     * 获取业务流程单下拉箱型
     *
     * @param fdBusCostDetail
     * @return R
     */
    @PostMapping("/selectContainerTypeCode")
    public List<WaybillContainerInfo> selectContainerTypeCode(@RequestBody FdBusCostDetail fdBusCostDetail) {
        List<WaybillContainerInfo> list = fdBusCostDetailService.selectContainerTypeCode(fdBusCostDetail);
        return list;
    }

    /**
     * 获取业务流程单下拉到站
     *
     * @param fdBusCostDetail
     * @return R
     */
    @PostMapping("/selectEndCompilation")
    public List<WaybillContainerInfo> selectEndCompilation(@RequestBody FdBusCostDetail fdBusCostDetail) {
        List<WaybillContainerInfo> list = fdBusCostDetailService.selectEndCompilation(fdBusCostDetail);
        return list;
    }

    /**
     * 获取下级市平台应付列表
     *
     * @param fdBusCostDetail
     * @return R
     */
    @PostMapping("/selectFdBusCostDetailFromLower")
    public List<FdBusCostDetailDTO> selectFdBusCostDetailFromLower(@RequestBody FdBusCostDetailDTO fdBusCostDetail) {
        return fdBusCostDetailService.selectFdBusCostDetailFromLower(fdBusCostDetail);
    }

    /**
    * 小程序列表
    * */
    @PostMapping("/weChatList")
    public Map<String, Object> weChatList(@RequestBody FdBusCostDetail fdBusCostDetail) {
        return fdBusCostDetailService.weChatList(fdBusCostDetail);
    }
}
