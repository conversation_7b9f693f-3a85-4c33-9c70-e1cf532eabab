package com.huazheng.tunny.ocean.service.impl;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.ocean.api.entity.FdFreightAccounting;
import com.huazheng.tunny.ocean.mapper.FdFreightAccountingMapper;
import com.huazheng.tunny.ocean.service.FdFreightAccountingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("fdFreightAccountingService")
public class FdFreightAccountingServiceImpl extends ServiceImpl<FdFreightAccountingMapper, FdFreightAccounting> implements FdFreightAccountingService {

    @Autowired
    private FdFreightAccountingMapper fdFreightAccountingMapper;

    public FdFreightAccountingMapper getFdFreightAccountingMapper() {
        return fdFreightAccountingMapper;
    }

    public void setFdFreightAccountingMapper(FdFreightAccountingMapper fdFreightAccountingMapper) {
        this.fdFreightAccountingMapper = fdFreightAccountingMapper;
    }

    @Override
    public List<FdFreightAccounting> getfdFreightAccountingList(String billCode) {
        return fdFreightAccountingMapper.getfdFreightAccountingList(billCode);
    }

    @Override
    public List<FdFreightAccounting> getfdFreightAccountingList2(String provinceShiftNo) {
        return fdFreightAccountingMapper.getfdFreightAccountingList2(provinceShiftNo);
    }

    @Override
    public List<FdFreightAccounting> getfdFreightAccountingList3(FdFreightAccounting fdFreightAccounting) {
        return fdFreightAccountingMapper.getfdFreightAccountingList3(fdFreightAccounting);
    }
}
