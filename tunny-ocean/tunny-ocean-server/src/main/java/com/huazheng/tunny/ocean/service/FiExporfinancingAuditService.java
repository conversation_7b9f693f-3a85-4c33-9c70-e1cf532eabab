package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FiExporfinancingAudit;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 出口融资审核信息表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-05-12 17:08:21
 */
public interface FiExporfinancingAuditService extends IService<FiExporfinancingAudit> {
    /**
     * 查询出口融资审核信息表信息
     *
     * @param rowId 出口融资审核信息表ID
     * @return 出口融资审核信息表信息
     */
    public FiExporfinancingAudit selectFiExporfinancingAuditById(String rowId);

    /**
     * 查询出口融资审核信息表列表
     *
     * @param fiExporfinancingAudit 出口融资审核信息表信息
     * @return 出口融资审核信息表集合
     */
    public List<FiExporfinancingAudit> selectFiExporfinancingAuditList(FiExporfinancingAudit fiExporfinancingAudit);


    /**
     * 分页模糊查询出口融资审核信息表列表
     * @return 出口融资审核信息表集合
     */
    public Page selectFiExporfinancingAuditListByLike(Query query);



    /**
     * 新增出口融资审核信息表
     *
     * @param fiExporfinancingAudit 出口融资审核信息表信息
     * @return 结果
     */
    public int insertFiExporfinancingAudit(FiExporfinancingAudit fiExporfinancingAudit);

    /**
     * 修改出口融资审核信息表
     *
     * @param fiExporfinancingAudit 出口融资审核信息表信息
     * @return 结果
     */
    public int updateFiExporfinancingAudit(FiExporfinancingAudit fiExporfinancingAudit);

    /**
     * 删除出口融资审核信息表
     *
     * @param rowId 出口融资审核信息表ID
     * @return 结果
     */
    public int deleteFiExporfinancingAuditById(String rowId);

    /**
     * 批量删除出口融资审核信息表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiExporfinancingAuditByIds(Integer[] rowIds);

}

