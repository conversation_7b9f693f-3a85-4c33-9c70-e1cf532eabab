package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.ScheduleCycle;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Description: Service
 * @Author: wx
 * @Date: 2023-05-09 15:23:07
 */
public interface ScheduleCycleService extends IService<ScheduleCycle> {

    /**
     * @Description: 分页
     * @Param: query
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-09 15:23:07
     */
    Page page(Query query);

    /**
     * @Description: 详情
     * @Param: id
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-09 15:23:07
     */
    R<ScheduleCycle> info(String id);

    /**
     * @Description: 保存
     * @Param: param
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-09 15:23:07
     */
    R save(ScheduleCycle param);

    /**
     * @Description: 修改
     * @Param: param
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-09 15:23:07
     */
    R update(ScheduleCycle param);

    /**
     * @Description: 删除
     * @Param: rowId
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-09 15:23:07
     */
    R delete(String id);

    /**
     * @Description: 列表
     * @Param: param
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-09 15:23:07
     */
    R<List<ScheduleCycle>> list(ScheduleCycle param);

    R importExcel(ScheduleCycle param, MultipartFile file);

    void exportTemplate(HttpServletResponse response);
}

