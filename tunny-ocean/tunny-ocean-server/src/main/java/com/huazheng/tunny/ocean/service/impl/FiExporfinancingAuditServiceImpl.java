package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FiExporfinancingAuditMapper;
import com.huazheng.tunny.ocean.api.entity.FiExporfinancingAudit;
import com.huazheng.tunny.ocean.service.FiExporfinancingAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fiExporfinancingAuditService")
public class FiExporfinancingAuditServiceImpl extends ServiceImpl<FiExporfinancingAuditMapper, FiExporfinancingAudit> implements FiExporfinancingAuditService {

    @Autowired
    private FiExporfinancingAuditMapper fiExporfinancingAuditMapper;

    public FiExporfinancingAuditMapper getFiExporfinancingAuditMapper() {
        return fiExporfinancingAuditMapper;
    }

    public void setFiExporfinancingAuditMapper(FiExporfinancingAuditMapper fiExporfinancingAuditMapper) {
        this.fiExporfinancingAuditMapper = fiExporfinancingAuditMapper;
    }

    /**
     * 查询出口融资审核信息表信息
     *
     * @param rowId 出口融资审核信息表ID
     * @return 出口融资审核信息表信息
     */
    @Override
    public FiExporfinancingAudit selectFiExporfinancingAuditById(String rowId)
    {
        return fiExporfinancingAuditMapper.selectFiExporfinancingAuditById(rowId);
    }

    /**
     * 查询出口融资审核信息表列表
     *
     * @param fiExporfinancingAudit 出口融资审核信息表信息
     * @return 出口融资审核信息表集合
     */
    @Override
    public List<FiExporfinancingAudit> selectFiExporfinancingAuditList(FiExporfinancingAudit fiExporfinancingAudit)
    {
        return fiExporfinancingAuditMapper.selectFiExporfinancingAuditList(fiExporfinancingAudit);
    }


    /**
     * 分页模糊查询出口融资审核信息表列表
     * @return 出口融资审核信息表集合
     */
    @Override
    public Page selectFiExporfinancingAuditListByLike(Query query)
    {
        FiExporfinancingAudit fiExporfinancingAudit =  BeanUtil.mapToBean(query.getCondition(), FiExporfinancingAudit.class,false);
        query.setRecords(fiExporfinancingAuditMapper.selectFiExporfinancingAuditListByLike(query,fiExporfinancingAudit));
        return query;
    }

    /**
     * 新增出口融资审核信息表
     *
     * @param fiExporfinancingAudit 出口融资审核信息表信息
     * @return 结果
     */
    @Override
    public int insertFiExporfinancingAudit(FiExporfinancingAudit fiExporfinancingAudit)
    {
        return fiExporfinancingAuditMapper.insertFiExporfinancingAudit(fiExporfinancingAudit);
    }

    /**
     * 修改出口融资审核信息表
     *
     * @param fiExporfinancingAudit 出口融资审核信息表信息
     * @return 结果
     */
    @Override
    public int updateFiExporfinancingAudit(FiExporfinancingAudit fiExporfinancingAudit)
    {
        return fiExporfinancingAuditMapper.updateFiExporfinancingAudit(fiExporfinancingAudit);
    }


    /**
     * 删除出口融资审核信息表
     *
     * @param rowId 出口融资审核信息表ID
     * @return 结果
     */
    public int deleteFiExporfinancingAuditById(String rowId)
    {
        return fiExporfinancingAuditMapper.deleteFiExporfinancingAuditById( rowId);
    };


    /**
     * 批量删除出口融资审核信息表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiExporfinancingAuditByIds(Integer[] rowIds)
    {
        return fiExporfinancingAuditMapper.deleteFiExporfinancingAuditByIds( rowIds);
    }

}
