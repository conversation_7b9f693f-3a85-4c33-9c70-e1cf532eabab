package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FiExporfinancingPay;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 出口融资放款信息表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-04-25 12:23:03
 */
public interface FiExporfinancingPayService extends IService<FiExporfinancingPay> {
    /**
     * 查询出口融资放款信息表信息
     *
     * @param rowId 出口融资放款信息表ID
     * @return 出口融资放款信息表信息
     */
    public FiExporfinancingPay selectFiExporfinancingPayById(String rowId);

    public FiExporfinancingPay selectFiExporfinancingPayByAssetCode(FiExporfinancingPay fiExporfinancingPay);
    /**
     * 查询出口融资放款信息表列表
     *
     * @param fiExporfinancingPay 出口融资放款信息表信息
     * @return 出口融资放款信息表集合
     */
    public List<FiExporfinancingPay> selectFiExporfinancingPayList(FiExporfinancingPay fiExporfinancingPay);


    /**
     * 分页模糊查询出口融资放款信息表列表
     * @return 出口融资放款信息表集合
     */
    public Page selectFiExporfinancingPayListByLike(Query query);



    /**
     * 新增出口融资放款信息表
     *
     * @param fiExporfinancingPay 出口融资放款信息表信息
     * @return 结果
     */
    public int insertFiExporfinancingPay(FiExporfinancingPay fiExporfinancingPay);

    /**
     * 修改出口融资放款信息表
     *
     * @param fiExporfinancingPay 出口融资放款信息表信息
     * @return 结果
     */
    public int updateFiExporfinancingPay(FiExporfinancingPay fiExporfinancingPay);

    /**
     * 删除出口融资放款信息表
     *
     * @param rowId 出口融资放款信息表ID
     * @return 结果
     */
    public int deleteFiExporfinancingPayById(String rowId);

    /**
     * 批量删除出口融资放款信息表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiExporfinancingPayByIds(Integer[] rowIds);

}

