package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.PlatformOverviewVariables;

import java.util.List;

/**
 * 平台概览变量 服务接口层
 *
 * <AUTHOR>
 * @date 2025-04-07 10:46:51
 */
public interface PlatformOverviewVariablesService extends IService<PlatformOverviewVariables> {
    /**
     * 查询平台概览变量信息
     *
     * @param id 平台概览变量ID
     * @return 平台概览变量信息
     */
    public PlatformOverviewVariables selectPlatformOverviewVariablesById(Integer id);

    /**
     * 查询平台概览变量列表
     *
     * @param platformOverviewVariables 平台概览变量信息
     * @return 平台概览变量集合
     */
    public List<PlatformOverviewVariables> selectPlatformOverviewVariablesList(PlatformOverviewVariables platformOverviewVariables);


    /**
     * 分页模糊查询平台概览变量列表
     *
     * @return 平台概览变量集合
     */
    public Page selectPlatformOverviewVariablesListByLike(Query query);


    /**
     * 新增平台概览变量
     *
     * @param platformOverviewVariables 平台概览变量信息
     * @return 结果
     */
    public int insertPlatformOverviewVariables(PlatformOverviewVariables platformOverviewVariables);

    /**
     * 修改平台概览变量
     *
     * @param platformOverviewVariables 平台概览变量信息
     * @return 结果
     */
    public int updatePlatformOverviewVariables(PlatformOverviewVariables platformOverviewVariables);

    /**
     * 删除平台概览变量
     *
     * @param id 平台概览变量ID
     * @return 结果
     */
    public int deletePlatformOverviewVariablesById(Integer id);

    /**
     * 批量删除平台概览变量
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deletePlatformOverviewVariablesByIds(Integer[] ids);

}

