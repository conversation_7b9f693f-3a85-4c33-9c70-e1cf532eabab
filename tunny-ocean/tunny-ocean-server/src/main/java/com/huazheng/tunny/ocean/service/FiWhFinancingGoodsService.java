package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FiWhFinancingGoods;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 仓单质押融资货物表 服务接口层
 *
 * <AUTHOR>
 * @date 2022-11-10 15:50:39
 */
public interface FiWhFinancingGoodsService extends IService<FiWhFinancingGoods> {
    /**
     * 查询仓单质押融资货物表信息
     *
     * @param rowId 仓单质押融资货物表ID
     * @return 仓单质押融资货物表信息
     */
    public FiWhFinancingGoods selectFiWhFinancingGoodsById(String rowId);

    /**
     * 查询仓单质押融资货物表列表
     *
     * @param fiWhFinancingGoods 仓单质押融资货物表信息
     * @return 仓单质押融资货物表集合
     */
    public List<FiWhFinancingGoods> selectFiWhFinancingGoodsList(FiWhFinancingGoods fiWhFinancingGoods);


    /**
     * 分页模糊查询仓单质押融资货物表列表
     * @return 仓单质押融资货物表集合
     */
    public Page selectFiWhFinancingGoodsListByLike(Query query);



    /**
     * 新增仓单质押融资货物表
     *
     * @param fiWhFinancingGoods 仓单质押融资货物表信息
     * @return 结果
     */
    public int insertFiWhFinancingGoods(FiWhFinancingGoods fiWhFinancingGoods);

    /**
     * 修改仓单质押融资货物表
     *
     * @param fiWhFinancingGoods 仓单质押融资货物表信息
     * @return 结果
     */
    public int updateFiWhFinancingGoods(FiWhFinancingGoods fiWhFinancingGoods);

    /**
     * 删除仓单质押融资货物表
     *
     * @param rowId 仓单质押融资货物表ID
     * @return 结果
     */
    public int deleteFiWhFinancingGoodsById(String rowId);

    public int deleteFiWhFinancingGoodsByFinancingCode(String financingCode);

    /**
     * 批量删除仓单质押融资货物表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiWhFinancingGoodsByIds(Integer[] rowIds);

}

