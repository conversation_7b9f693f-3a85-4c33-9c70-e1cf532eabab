package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.TariffReleaseDetail;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 运价发布子表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-08 11:24:10
 */
public interface TariffReleaseDetailService extends IService<TariffReleaseDetail> {
    /**
     * 查询运价发布子表信息
     *
     * @param rowId 运价发布子表ID
     * @return 运价发布子表信息
     */
    public TariffReleaseDetail selectTariffReleaseDetailById(String rowId);

    /**
     * 查询运价发布子表列表
     *
     * @param tariffReleaseDetail 运价发布子表信息
     * @return 运价发布子表集合
     */
    public List<TariffReleaseDetail> selectTariffReleaseDetailList(TariffReleaseDetail tariffReleaseDetail);


    /**
     * 分页模糊查询运价发布子表列表
     * @return 运价发布子表集合
     */
    public Page selectTariffReleaseDetailListByLike(Query query);



    /**
     * 新增运价发布子表
     *
     * @param tariffReleaseDetail 运价发布子表信息
     * @return 结果
     */
    public int insertTariffReleaseDetail(TariffReleaseDetail tariffReleaseDetail);

    /**
     * 修改运价发布子表
     *
     * @param tariffReleaseDetail 运价发布子表信息
     * @return 结果
     */
    public int updateTariffReleaseDetail(TariffReleaseDetail tariffReleaseDetail);

    public int updateTariffReleaseDetailByNo(TariffReleaseDetail tariffReleaseDetail);

    /**
     * 删除运价发布子表
     *
     * @param rowId 运价发布子表ID
     * @return 结果
     */
    public int deleteTariffReleaseDetailById(String rowId);

    /**
     * 批量删除运价发布子表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTariffReleaseDetailByIds(Integer[] rowIds);

}

