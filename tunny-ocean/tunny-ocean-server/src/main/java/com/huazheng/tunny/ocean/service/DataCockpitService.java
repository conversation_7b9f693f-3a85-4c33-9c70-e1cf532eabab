package com.huazheng.tunny.ocean.service;

import com.huazheng.tunny.ocean.api.vo.DataCockpitLineChartVO;
import com.huazheng.tunny.ocean.api.vo.DataCockpitTitleNumBerVO;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2024/7/30 13:31
 */
public interface DataCockpitService {

    /**
     * 获取数据驾驶舱-省平台-开行列数，收入总数，毛利
     * @return
     */
    DataCockpitTitleNumBerVO selectDataCockpitTitleNumber(String startTime, String endTime);

    /**
     * 获取收入总计折线图
     * @param startTime
     * @param endTime
     * @return
     */
    DataCockpitLineChartVO selectIncomeSumLineChart(String startTime, String endTime);

    /**
     * 获取各市单车平均收入
     * @param startTime
     * @param endTime
     * @return
     */
    DataCockpitLineChartVO selectCityUnitTrainSum(String startTime, String endTime);

    /**
     * 获取各市单车平均收入(市明细)
     * @param startTime
     * @param endTime
     * @return
     */
    DataCockpitLineChartVO selectCityUnitTrainDetail(String startTime, String endTime);

}
