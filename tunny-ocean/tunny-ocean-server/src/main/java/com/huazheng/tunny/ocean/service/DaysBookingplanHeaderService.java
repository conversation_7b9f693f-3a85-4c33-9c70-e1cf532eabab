package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.DaysBookingplanHeader;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 旬/周计划申请表(订舱客户提交到市平台) 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-07 11:48:23
 */
public interface DaysBookingplanHeaderService extends IService<DaysBookingplanHeader> {
    /**
     * 查询旬/周计划申请表(订舱客户提交到市平台)信息
     *
     * @param rowId 旬/周计划申请表(订舱客户提交到市平台)ID
     * @return 旬/周计划申请表(订舱客户提交到市平台)信息
     */
    public DaysBookingplanHeader selectDaysBookingplanHeaderById(String rowId);

    /**
     * 查询旬/周计划申请表(订舱客户提交到市平台)列表
     *
     * @param daysBookingplanHeader 旬/周计划申请表(订舱客户提交到市平台)信息
     * @return 旬/周计划申请表(订舱客户提交到市平台)集合
     */
    public List<DaysBookingplanHeader> selectDaysBookingplanHeaderList(DaysBookingplanHeader daysBookingplanHeader);


    /**
     * 分页模糊查询旬/周计划申请表(订舱客户提交到市平台)列表
     * @return 旬/周计划申请表(订舱客户提交到市平台)集合
     */
    public Page selectDaysBookingplanHeaderListByLike(Query query);

    /**
     * 分页模糊查询旬/周计划申请表 (管理平台展示)
     * @return 旬/周计划申请表 (管理平台展示)
     */
    public Page selectDaysBookingplanHeaderListByLikeM(Query query);



    /**
     * 新增旬/周计划申请表(订舱客户提交到市平台)
     *
     * @param daysBookingplanHeader 旬/周计划申请表(订舱客户提交到市平台)信息
     * @return 结果
     */
    public R insertDaysBookingplanHeader(DaysBookingplanHeader daysBookingplanHeader);

    /**
     * 修改旬/周计划申请表(订舱客户提交到市平台)
     *
     * @param planCode 旬/周计划申请表(订舱客户提交到市平台)信息
     * @return 结果
     */
    public R updateDaysBookingplanHeader(String[] planCode);

    /**
     * 提交
     *
     * @param planCode 提交旬/周计划申请表(订舱客户提交到市平台)信息
     * @return 结果
     */
    public int commitStatus(String planCode);

    /**
     * 删除旬/周计划申请表(订舱客户提交到市平台)
     *
     * @param rowId 旬/周计划申请表(订舱客户提交到市平台)ID
     * @return 结果
     */
    public int deleteDaysBookingplanHeaderById(String rowId);

    /**
     * 批量删除旬/周计划申请表(订舱客户提交到市平台)
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDaysBookingplanHeaderByIds(Integer[] rowIds);

}

