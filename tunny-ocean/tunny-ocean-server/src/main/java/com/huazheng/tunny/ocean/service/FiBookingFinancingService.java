package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.FiBookingFinancing;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;
import java.util.Map;

/**
 * 订舱单融资 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-03-08 09:41:35
 */
public interface FiBookingFinancingService extends IService<FiBookingFinancing> {
    /**
     * 查询订舱单融资信息
     *
     * @param rowId 订舱单融资ID
     * @return 订舱单融资信息
     */
    public FiBookingFinancing selectFiBookingFinancingById(String rowId);

    public FiBookingFinancing selectFiBookingFinancingByAssetCode(String assetCode);

    /**
     * 查询订舱单融资列表
     *
     * @param fiBookingFinancing 订舱单融资信息
     * @return 订舱单融资集合
     */
    public List<FiBookingFinancing> selectFiBookingFinancingList(FiBookingFinancing fiBookingFinancing);

    public List<FiBookingFinancing> selectFiBookingFinancingExportList(FiBookingFinancing fiBookingFinancing);


    /**
     * 分页模糊查询订舱单融资列表
     * @return 订舱单融资集合
     */
    public Page selectFiBookingFinancingListByLike(Query query);

    public Page selectFiBookingFinancingPage(Query query);

    public R selectFiBookingFinancingSum(Map<String, Object> params);



    /**
     * 新增订舱单融资
     *
     * @param fiBookingFinancing 订舱单融资信息
     * @return 结果
     */
    public int insertFiBookingFinancing(FiBookingFinancing fiBookingFinancing);

    /**
     * 修改订舱单融资
     *
     * @param fiBookingFinancing 订舱单融资信息
     * @return 结果
     */
    public int updateFiBookingFinancing(FiBookingFinancing fiBookingFinancing);

    /**
     * 修改订舱单融资
     *
     * @param fiBookingFinancing 订舱单融资信息
     * @return 结果
     */
    public int updateFiBookingFinancingInfo(FiBookingFinancing fiBookingFinancing);

    /**
     * 删除订舱单融资
     *
     * @param rowId 订舱单融资ID
     * @return 结果
     */
    public int deleteFiBookingFinancingById(String rowId);

    /**
     * 批量删除订舱单融资
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiBookingFinancingByIds(Integer[] rowIds);

}

