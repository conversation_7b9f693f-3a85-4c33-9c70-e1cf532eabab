package com.huazheng.tunny.ocean.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.ocean.api.entity.EfSupplyApplyInfo;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseApply;
import com.huazheng.tunny.ocean.api.entity.OperationLog;
import com.huazheng.tunny.ocean.service.EfSupplyApplyInfoService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.service.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 补充质押通知表
 *
 * <AUTHOR>
 * @date 2023-05-08 15:34:32
 */
@Slf4j
@RestController
@RequestMapping("/efsupplyapplyinfo")
public class EfSupplyApplyInfoController {

    @Autowired
    private EfSupplyApplyInfoService efSupplyApplyInfoService;
    @Autowired
    private SignatureController signatureController;
    @Autowired
    private OperationLogService operationLogService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  efSupplyApplyInfoService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return efSupplyApplyInfoService.selectEfSupplyApplyInfoListByLike(new Query<>(params));
    }

    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        EfSupplyApplyInfo efSupplyApplyInfo =efSupplyApplyInfoService.selectEfSupplyApplyInfoById(rowId);
        return new R<>(efSupplyApplyInfo);
    }

    /**
     * 保存
     * @param efSupplyApplyInfo
     * @return R
     */
    @PostMapping
    public R save(@RequestBody EfSupplyApplyInfo efSupplyApplyInfo) {
        efSupplyApplyInfoService.insert(efSupplyApplyInfo);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param efSupplyApplyInfo
     * @return R
     */
    @PostMapping("/update")
    public String update(@RequestBody EfSupplyApplyInfo efSupplyApplyInfo) {
        String content = null;
        content = efSupplyApplyInfoService.updateEfSupplyApplyInfo(efSupplyApplyInfo);
        return content;
    }

    

    /**
     * 删除
     * @param rowId
     * @return R
     */
    @GetMapping("/del/{rowId}")
    public R delete(@PathVariable  String rowId) {
        efSupplyApplyInfoService.deleteById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        efSupplyApplyInfoService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<EfSupplyApplyInfo> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<EfSupplyApplyInfo> list = efSupplyApplyInfoService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), EfSupplyApplyInfo.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }

    //同步补质押申请
    @PostMapping("/syncSupplyApplyInfo")
    public String syncSupplyApplyInfo(@RequestBody JSONObject jsonObject) {
        String content = null;
        try {
            //调用接口
            String data = signatureController.getPost(jsonObject);
            EfSupplyApplyInfo efSupplyApplyInfo = JSONUtil.toBean(data, EfSupplyApplyInfo.class);

            String content2 = String.valueOf(jsonObject.get("content"));
            OperationLog log4 = new OperationLog();
            log4.setUuid(UUID.randomUUID().toString());
            log4.setProcessType("通知补仓");
            log4.setOperationCode("ZC");
            log4.setOperationName("中钞");
            log4.setOperationTime(new Date());
            log4.setOperationOpinion(content2 + data );
            operationLogService.insertOperationLog(log4);

            //插入数据
            content = efSupplyApplyInfoService.syncSupplyApplyInfo(efSupplyApplyInfo);
        }catch (Exception e){
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE,e.getMessage()), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPost("/v1/ent/warehouse/syncSupplyApplyInfo", content);
        return result;
    }

    //审核补质押
    @PostMapping("/auditSupplyApply")
    public String auditSupplyApply(@RequestBody JSONObject jsonObject) {
        String content = null;
        try {
            //调用接口
            String data = signatureController.getPost(jsonObject);
            EfSupplyApplyInfo efSupplyApplyInfo = JSONUtil.toBean(data, EfSupplyApplyInfo.class);

            String content2 = String.valueOf(jsonObject.get("content"));
            OperationLog log4 = new OperationLog();
            log4.setUuid(UUID.randomUUID().toString());
            log4.setProcessType("审核补仓");
            log4.setOperationCode("ER");
            log4.setOperationName("E融");
            log4.setOperationTime(new Date());
            log4.setOperationOpinion(content2 + data );
            operationLogService.insertOperationLog(log4);

            //插入数据
            content = efSupplyApplyInfoService.auditSupplyApply(efSupplyApplyInfo);
        }catch (Exception e){
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE,e.getMessage()), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPostEf("/efwarehouseapply/auditSupplyApply", content);
        return result;
    }

    //补充质押确认同步接口
    @PostMapping("/syncSupplyConfirmInfo")
    public String syncSupplyConfirmInfo(@RequestBody JSONObject jsonObject) {
        String content = null;
        try {
            //调用接口
            String data = signatureController.getPost(jsonObject);
            EfSupplyApplyInfo efSupplyApplyInfo = JSONUtil.toBean(data, EfSupplyApplyInfo.class);

            String content2 = String.valueOf(jsonObject.get("content"));
            OperationLog log4 = new OperationLog();
            log4.setUuid(UUID.randomUUID().toString());
            log4.setProcessType("确认补仓");
            log4.setOperationCode("ZC");
            log4.setOperationName("中钞");
            log4.setOperationTime(new Date());
            log4.setOperationOpinion(content2 + data );
            operationLogService.insertOperationLog(log4);

            //插入数据
            content = efSupplyApplyInfoService.syncSupplyConfirmInfo(efSupplyApplyInfo);
        }catch (Exception e){
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE,e.getMessage()), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPost("/v1/ent/warehouse/syncSupplyConfirmInfo", content);
        return result;
    }
}
