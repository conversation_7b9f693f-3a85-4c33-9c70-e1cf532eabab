package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.OperationLog;
import com.huazheng.tunny.ocean.mapper.FiLoanRecordMapper;
import com.huazheng.tunny.ocean.api.entity.FiLoanRecord;
import com.huazheng.tunny.ocean.service.FiLoanRecordService;
import com.huazheng.tunny.ocean.service.OperationLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import java.util.UUID;

@Service("fiLoanRecordService")
public class FiLoanRecordServiceImpl extends ServiceImpl<FiLoanRecordMapper, FiLoanRecord> implements FiLoanRecordService {

    @Autowired
    private FiLoanRecordMapper fiLoanRecordMapper;
    @Autowired
    private OperationLogService operationLogService;

    public FiLoanRecordMapper getFiLoanRecordMapper() {
        return fiLoanRecordMapper;
    }

    public void setFiLoanRecordMapper(FiLoanRecordMapper fiLoanRecordMapper) {
        this.fiLoanRecordMapper = fiLoanRecordMapper;
    }

    public OperationLogService getOperationLogService() {
        return operationLogService;
    }

    public void setOperationLogService(OperationLogService operationLogService) {
        this.operationLogService = operationLogService;
    }

    /**
     * 查询企业信用贷-贷款记录信息
     *
     * @param rowId 企业信用贷-贷款记录ID
     * @return 企业信用贷-贷款记录信息
     */
    @Override
    public FiLoanRecord selectFiLoanRecordById(String rowId)
    {
        return fiLoanRecordMapper.selectFiLoanRecordById(rowId);
    }

    /**
     * 查询企业信用贷-贷款记录列表
     *
     * @param fiLoanRecord 企业信用贷-贷款记录信息
     * @return 企业信用贷-贷款记录集合
     */
    @Override
    public List<FiLoanRecord> selectFiLoanRecordList(FiLoanRecord fiLoanRecord)
    {
        return fiLoanRecordMapper.selectFiLoanRecordList(fiLoanRecord);
    }


    /**
     * 分页模糊查询企业信用贷-贷款记录列表
     * @return 企业信用贷-贷款记录集合
     */
    @Override
    public Page selectFiLoanRecordListByLike(Query query)
    {
        FiLoanRecord fiLoanRecord =  BeanUtil.mapToBean(query.getCondition(), FiLoanRecord.class,false);
        query.setRecords(fiLoanRecordMapper.selectFiLoanRecordListByLike(query,fiLoanRecord));
        return query;
    }

    /**
     * 新增企业信用贷-贷款记录
     *
     * @param fiLoanRecord 企业信用贷-贷款记录信息
     * @return 结果
     */
    @Override
    public int insertFiLoanRecord(FiLoanRecord fiLoanRecord)
    {
        return fiLoanRecordMapper.insertFiLoanRecord(fiLoanRecord);
    }

    /**
     * 修改企业信用贷-贷款记录
     *
     * @param fiLoanRecord 企业信用贷-贷款记录信息
     * @return 结果
     */
    @Override
    public int updateFiLoanRecord(FiLoanRecord fiLoanRecord)
    {
        return fiLoanRecordMapper.updateFiLoanRecord(fiLoanRecord);
    }


    /**
     * 删除企业信用贷-贷款记录
     *
     * @param rowId 企业信用贷-贷款记录ID
     * @return 结果
     */
    public int deleteFiLoanRecordById(String rowId)
    {
        return fiLoanRecordMapper.deleteFiLoanRecordById( rowId);
    }


    /**
     * 批量删除企业信用贷-贷款记录对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiLoanRecordByIds(Integer[] rowIds)
    {
        return fiLoanRecordMapper.deleteFiLoanRecordByIds( rowIds);
    }

    @Override
    public R addorUpdateFiLoanRecord(FiLoanRecord fiLoanRecord){
        R r=new R();
        FiLoanRecord selectfiLoanRecord=new FiLoanRecord();
        selectfiLoanRecord.setBusinessCode(fiLoanRecord.getBusinessCode());
        selectfiLoanRecord.setDeleteFlag("N");
        List<FiLoanRecord> fiLoanRecords = fiLoanRecordMapper.selectFiLoanRecordList(selectfiLoanRecord);
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if(!"".equals(fiLoanRecord.getDisbursementDateStr()) && fiLoanRecord.getDisbursementDateStr()!=null){
            fiLoanRecord.setDisbursementDate(LocalDateTime.parse(fiLoanRecord.getDisbursementDateStr()+" 00:00:00", df));
        }
        if(!"".equals(fiLoanRecord.getRepaymentDateStr()) && fiLoanRecord.getRepaymentDateStr()!=null){
            fiLoanRecord.setRepaymentDate(LocalDateTime.parse(fiLoanRecord.getRepaymentDateStr()+" 00:00:00", df));
        }
        //插入操作日志
        OperationLog log = new OperationLog();
        log.setUuid(UUID.randomUUID().toString());
        log.setProcessCode(fiLoanRecord.getBusinessCode());
        //判断新增或者修改
        if(fiLoanRecords.size()>0){
            fiLoanRecord.setUpdateTime(LocalDateTime.now());
            fiLoanRecord.setUpdateWho(userInfo.getUserName());
            fiLoanRecord.setUpdateWhoName(userInfo.getRealName());
            fiLoanRecordMapper.updateFiLoanRecord(fiLoanRecord);
            log.setProcessType("中超-贷款记录-修改");
            log.setOperationResult("修改完成");
            r.setMsg("修改成功");
            r.setB(Boolean.TRUE);
            r.setCode(200);
        }else{
            fiLoanRecord.setAddTime(LocalDateTime.now());
            fiLoanRecord.setAddWho(userInfo.getUserName());
            fiLoanRecord.setAddWhoName(userInfo.getRealName());
            fiLoanRecord.setRowId(UUID.randomUUID().toString());
            fiLoanRecordMapper.insertFiLoanRecord(fiLoanRecord);
            log.setProcessType("中超-贷款记录-新增");
            log.setOperationResult("新增完成");
            r.setMsg("新增成功");
            r.setB(Boolean.TRUE);
            r.setCode(200);
        }
        log.setOperationCode(userInfo.getUserName());
        log.setOperationName(userInfo.getRealName());
        log.setOperationTime(new Date());
        log.setCorInterface("/filoanrecord/addorUpdateFiLoanRecord");
        log.setDeleteFlag("N");
        operationLogService.insertOperationLog(log);
        return r;
    }

}
