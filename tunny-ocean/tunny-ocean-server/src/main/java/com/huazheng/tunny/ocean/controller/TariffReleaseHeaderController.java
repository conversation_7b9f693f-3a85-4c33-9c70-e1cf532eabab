package com.huazheng.tunny.ocean.controller;


import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.util.TokenUtil;
import com.huazheng.tunny.ocean.service.SysNoConfigService;
import com.huazheng.tunny.ocean.service.TariffReleaseDetailService;
import com.huazheng.tunny.ocean.service.TariffReleaseHeaderService;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 运价发布主表
 *
 * <AUTHOR> code ocean
 * @date 2021-07-08 11:24:00
 */
@RestController
@RequestMapping("/tariffreleaseheader")
@Slf4j
public class TariffReleaseHeaderController {
    @Autowired
    private TariffReleaseHeaderService tariffReleaseHeaderService;
    @Autowired
    private SysNoConfigService sysNoConfigService;
    @Autowired
    private TariffReleaseDetailService tariffReleaseDetailService;
    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return tariffReleaseHeaderService.selectTariffReleaseHeaderListByLike(new Query<>(params));
    }


    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        TariffReleaseHeader tariffReleaseHeader =tariffReleaseHeaderService.selectById(rowId);
        return new R<>(tariffReleaseHeader);
    }
    //发布
    @GetMapping("/release")
    @Transactional(rollbackFor = Exception.class)
    public R release(TariffReleaseHeader tariffReleaseHeader) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        tariffReleaseHeader.setUpdateWho(userInfo.getUserName());
        tariffReleaseHeader.setUpdateWhoName(userInfo.getRealName());
        tariffReleaseHeader.setUpdateTime(new Date());
        tariffReleaseHeader.setStatus("1");
        tariffReleaseHeaderService.updateTariffReleaseHeaderByNo(tariffReleaseHeader);
        return new R<>(200,Boolean.TRUE);
    }

    /**
     * 保存
     * @param tariffReleaseHeader
     * @return R
     */
    @PostMapping("/save")
    @Transactional(rollbackFor = Exception.class)
    public R save(@RequestBody TariffReleaseHeader tariffReleaseHeader) {

        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String orderNo = sysNoConfigService.genNo("YJ");
        tariffReleaseHeader.setRowId(UUID.randomUUID().toString());
        tariffReleaseHeader.setTariffCode(orderNo);
        tariffReleaseHeader.setAddWho(userInfo.getUserName());
        tariffReleaseHeader.setAddWhoName(userInfo.getRealName());
        tariffReleaseHeader.setAddTime(new Date());
        tariffReleaseHeader.setDeleteFlag("N");
        tariffReleaseHeaderService.insertTariffReleaseHeader(tariffReleaseHeader);

        List<TariffReleaseDetail> tariffReleaseDetailList = tariffReleaseHeader.getTariffReleaseDetailList();
        if (!tariffReleaseDetailList.isEmpty()){
            for (TariffReleaseDetail tariffReleaseDetail : tariffReleaseDetailList) {
                tariffReleaseDetail.setRowId(UUID.randomUUID().toString());
                tariffReleaseDetail.setTariffCode(orderNo);
                tariffReleaseDetail.setAddTime(new Date());
                tariffReleaseDetail.setAddWho(userInfo.getUserName());
                tariffReleaseDetail.setAddWhoName(userInfo.getRealName());
                tariffReleaseDetail.setDeleteFlag("N");
                tariffReleaseDetailService.insertTariffReleaseDetail(tariffReleaseDetail);
            }
        }
        return new R<>(200,Boolean.TRUE,orderNo);
    }

    /**
     * 提交
     *
     * @param tariffReleaseHeader
     * @return R
     */
    @GetMapping("/confirm")
    @Transactional(rollbackFor = Exception.class)
    public R confirm(TariffReleaseHeader tariffReleaseHeader) {
        //传参-planCode
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        tariffReleaseHeader.setUpdateWho(userInfo.getUserName());
        tariffReleaseHeader.setUpdateTime(new Date());
        tariffReleaseHeader.setUpdateWhoName(userInfo.getRealName());
        tariffReleaseHeader.setResveredField01("1");//置为已提交
        tariffReleaseHeaderService.updateTariffReleaseHeaderByNo(tariffReleaseHeader);
        return new R<>(200, Boolean.TRUE, "提交成功");
    }

    /**
     * 修改
     * @param tariffReleaseHeader
     * @return R
     */
    @PostMapping("/update")
    @Transactional(rollbackFor = Exception.class)
    public R update(@RequestBody TariffReleaseHeader tariffReleaseHeader) {

        SecruityUser userInfo = SecurityUtils.getUserInfo();
        TariffReleaseHeader tariffReleaseHeader1 = new TariffReleaseHeader();
        tariffReleaseHeader1.setDeleteFlag("Y");
        tariffReleaseHeader1.setDeleteTime(new Date());
        tariffReleaseHeader1.setDeleteWho(userInfo.getUserName());
        tariffReleaseHeader1.setDeleteWhoName(userInfo.getRealName());
        tariffReleaseHeader1.setTariffCode(tariffReleaseHeader.getTariffCode());
        tariffReleaseHeaderService.updateTariffReleaseHeaderByNo(tariffReleaseHeader1);

        TariffReleaseDetail tariffReleaseDetail = new TariffReleaseDetail();
        tariffReleaseDetail.setTariffCode(tariffReleaseHeader.getTariffCode());
        tariffReleaseDetail.setDeleteFlag("N");
        List<TariffReleaseDetail> tariffReleaseDetails = tariffReleaseDetailService.selectTariffReleaseDetailList(tariffReleaseDetail);
        if (!tariffReleaseDetails.isEmpty()){
            for (TariffReleaseDetail releaseDetail : tariffReleaseDetails) {
                releaseDetail.setDeleteFlag("Y");
                releaseDetail.setDeleteTime(new Date());
                releaseDetail.setDeleteWho(userInfo.getUserName());
                releaseDetail.setTariffCode(tariffReleaseHeader.getTariffCode());
                tariffReleaseDetailService.updateTariffReleaseDetailByNo(releaseDetail);
            }
        }
        tariffReleaseHeader.setRowId(UUID.randomUUID().toString());
        tariffReleaseHeader.setAddWho(userInfo.getUserName());
        tariffReleaseHeader.setAddWhoName(userInfo.getRealName());
        tariffReleaseHeader.setAddTime(new Date());
        tariffReleaseHeader.setDeleteFlag("N");
        tariffReleaseHeader.setStatus("0");
        tariffReleaseHeaderService.insertTariffReleaseHeader(tariffReleaseHeader);

        List<TariffReleaseDetail> tariffReleaseDetailList = tariffReleaseHeader.getTariffReleaseDetailList();
        if (!tariffReleaseDetailList.isEmpty()){
            for (TariffReleaseDetail releaseDetail : tariffReleaseDetailList) {
                releaseDetail.setRowId(UUID.randomUUID().toString());
                releaseDetail.setTariffCode(tariffReleaseHeader.getTariffCode());
                releaseDetail.setAddTime(new Date());
                releaseDetail.setAddWhoName(userInfo.getRealName());
                releaseDetail.setAddWho(userInfo.getUserName());
                releaseDetail.setDeleteFlag("N");
                tariffReleaseDetailService.insertTariffReleaseDetail(releaseDetail);
            }
        }
        return new R<>(200,Boolean.TRUE,"修改成功");
    }



    /**
     * 删除
     * @param tariffReleaseHeader
     * @return R
     */
    @GetMapping("/delete")
    public R delete(TariffReleaseHeader tariffReleaseHeader) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        tariffReleaseHeader.setDeleteWho(userInfo.getUserName());
        tariffReleaseHeader.setDeleteTime(new Date());
        tariffReleaseHeader.setDeleteWhoName(userInfo.getRealName());
        tariffReleaseHeaderService.deleteTariffReleaseHeaderById(tariffReleaseHeader);
        return new R<>(200,Boolean.TRUE,"交易成功");
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        tariffReleaseHeaderService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<TariffReleaseHeader> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = tariffReleaseHeaderService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<TariffReleaseHeader> list = reader.readAll(TariffReleaseHeader.class);
        tariffReleaseHeaderService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }
}
