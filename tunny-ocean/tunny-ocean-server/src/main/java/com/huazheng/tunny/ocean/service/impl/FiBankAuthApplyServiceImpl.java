package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FiBankAuthApplyMapper;
import com.huazheng.tunny.ocean.api.entity.FiBankAuthApply;
import com.huazheng.tunny.ocean.service.FiBankAuthApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fiBankAuthApplyService")
public class FiBankAuthApplyServiceImpl extends ServiceImpl<FiBankAuthApplyMapper, FiBankAuthApply> implements FiBankAuthApplyService {

    @Autowired
    private FiBankAuthApplyMapper fiBankAuthApplyMapper;

    public FiBankAuthApplyMapper getFiBankAuthApplyMapper() {
        return fiBankAuthApplyMapper;
    }

    public void setFiBankAuthApplyMapper(FiBankAuthApplyMapper fiBankAuthApplyMapper) {
        this.fiBankAuthApplyMapper = fiBankAuthApplyMapper;
    }

    /**
     * 查询经营信息授权-银行申请表信息
     *
     * @param rowId 经营信息授权-银行申请表ID
     * @return 经营信息授权-银行申请表信息
     */
    @Override
    public FiBankAuthApply selectFiBankAuthApplyById(String rowId)
    {
        return fiBankAuthApplyMapper.selectFiBankAuthApplyById(rowId);
    }

    /**
     * 查询经营信息授权-银行申请表列表
     *
     * @param fiBankAuthApply 经营信息授权-银行申请表信息
     * @return 经营信息授权-银行申请表集合
     */
    @Override
    public List<FiBankAuthApply> selectFiBankAuthApplyList(FiBankAuthApply fiBankAuthApply)
    {
        return fiBankAuthApplyMapper.selectFiBankAuthApplyList(fiBankAuthApply);
    }


    /**
     * 分页模糊查询经营信息授权-银行申请表列表
     * @return 经营信息授权-银行申请表集合
     */
    @Override
    public Page selectFiBankAuthApplyListByLike(Query query)
    {
        FiBankAuthApply fiBankAuthApply =  BeanUtil.mapToBean(query.getCondition(), FiBankAuthApply.class,false);
        fiBankAuthApply.setDeleteFlag("N");
        query.setRecords(fiBankAuthApplyMapper.selectFiBankAuthApplyListByLike(query,fiBankAuthApply));
        return query;
    }

    /**
     * 新增经营信息授权-银行申请表
     *
     * @param fiBankAuthApply 经营信息授权-银行申请表信息
     * @return 结果
     */
    @Override
    public int insertFiBankAuthApply(FiBankAuthApply fiBankAuthApply)
    {
        return fiBankAuthApplyMapper.insertFiBankAuthApply(fiBankAuthApply);
    }

    /**
     * 修改经营信息授权-银行申请表
     *
     * @param fiBankAuthApply 经营信息授权-银行申请表信息
     * @return 结果
     */
    @Override
    public int updateFiBankAuthApply(FiBankAuthApply fiBankAuthApply)
    {
        return fiBankAuthApplyMapper.updateFiBankAuthApply(fiBankAuthApply);
    }


    /**
     * 删除经营信息授权-银行申请表
     *
     * @param rowId 经营信息授权-银行申请表ID
     * @return 结果
     */
    public int deleteFiBankAuthApplyById(String rowId)
    {
        return fiBankAuthApplyMapper.deleteFiBankAuthApplyById( rowId);
    }


    /**
     * 批量删除经营信息授权-银行申请表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiBankAuthApplyByIds(Integer[] rowIds)
    {
        return fiBankAuthApplyMapper.deleteFiBankAuthApplyByIds( rowIds);
    }

}
