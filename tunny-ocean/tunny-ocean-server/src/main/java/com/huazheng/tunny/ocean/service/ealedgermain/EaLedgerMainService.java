package com.huazheng.tunny.ocean.service.ealedgermain;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.eabookingorder.EaContainerSummary;
import com.huazheng.tunny.ocean.api.entity.ealedgermain.EaLedgerDetail;
import com.huazheng.tunny.ocean.api.entity.ealedgermain.EaLedgerMain;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 台账主表 服务接口层
 *
 * <AUTHOR>
 * @date 2025-07-02 15:42:27
 */
public interface EaLedgerMainService extends IService<EaLedgerMain> {
    /**
     * 查询台账主表信息
     *
     * @param ledgerId 台账主表ID
     * @return 台账主表信息
     */
    public EaLedgerMain selectEaLedgerMainById(Long ledgerId);

    /**
     * 查询台账主表列表
     *
     * @param eaLedgerMain 台账主表信息
     * @return 台账主表集合
     */
    public List<EaLedgerMain> selectEaLedgerMainList(EaLedgerMain eaLedgerMain);


    /**
     * 分页模糊查询台账主表列表
     *
     * @return 台账主表集合
     */
    public Page selectEaLedgerMainListByLike(Query query);


    /**
     * 新增台账主表
     *
     * @param eaLedgerMain 台账主表信息
     * @return 结果
     */
    public int insertEaLedgerMain(EaLedgerMain eaLedgerMain);

    /**
     * 修改台账主表
     *
     * @param eaLedgerMain 台账主表信息
     * @return 结果
     */
    public int updateEaLedgerMain(EaLedgerMain eaLedgerMain);

    /**
     * 删除台账主表
     *
     * @param ledgerId 台账主表ID
     * @return 结果
     */
    public int deleteEaLedgerMainById(Long ledgerId);

    /**
     * 批量删除台账主表
     *
     * @param ledgerIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEaLedgerMainByIds(Integer[] ledgerIds);

    /**
     * 生成台账
     *
     * @param eaLedgerMain 生成逻辑参数
     * @return 结果
     */
    R generateLedger(EaLedgerMain eaLedgerMain);

    /**
     * 删除台账
     *
     * @param eaLedgerMain 删除逻辑参数
     * @return 结果
     */
    R delLedger(EaLedgerMain eaLedgerMain);
    /**
     * 台账提交
     *
     * @param eaLedgerMain 提交逻辑参数
     * @return 结果
     */
    R submitLedger(EaLedgerMain eaLedgerMain);
    /**
     * 台账审核
     *
     * @param eaLedgerMain 审核逻辑参数
     * @return 结果
     */
    R examineLedger(EaLedgerMain eaLedgerMain);

    public Page selectShifmanagementByLike(Query query);

    R selectFeeListByLedgerMain(EaLedgerDetail daLedgerDetail);

    R examineList(Map<String, Object> params);

    R examineRevoke(EaLedgerMain eaLedgerMain);

    R updateProvinceShiftNo(EaLedgerMain eaLedgerMain);

    R updateShipTime(EaLedgerMain eaLedgerMain);

    void exportEaLedgerMainExcel(EaLedgerMain eaLedgerMain, HttpServletResponse response);

    R isExamine(Map<String, Object> params);
}

