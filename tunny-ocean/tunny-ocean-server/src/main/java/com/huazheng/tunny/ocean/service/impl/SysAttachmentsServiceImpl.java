package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.controller.SignatureController;
import com.huazheng.tunny.ocean.mapper.SysAttachmentsMapper;
import com.huazheng.tunny.ocean.api.entity.SysAttachments;
import com.huazheng.tunny.ocean.service.SysAttachmentsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.time.LocalDateTime;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service("sysAttachmentsService")
public class SysAttachmentsServiceImpl extends ServiceImpl<SysAttachmentsMapper, SysAttachments> implements SysAttachmentsService {

    @Autowired
    private SysAttachmentsMapper sysAttachmentsMapper;
    @Autowired
    private SignatureController signatureController;

    public SysAttachmentsMapper getSysAttachmentsMapper() {
        return sysAttachmentsMapper;
    }

    public void setSysAttachmentsMapper(SysAttachmentsMapper sysAttachmentsMapper) {
        this.sysAttachmentsMapper = sysAttachmentsMapper;
    }

    public SignatureController getSignatureController() {
        return signatureController;
    }

    public void setSignatureController(SignatureController signatureController) {
        this.signatureController = signatureController;
    }

    /**
     * 查询系统上传附件管理信息
     *
     * @param rowId 系统上传附件管理ID
     * @return 系统上传附件管理信息
     */
    @Override
    public SysAttachments selectSysAttachmentsById(String rowId)
    {
        return sysAttachmentsMapper.selectSysAttachmentsById(rowId);
    }

    /**
     * 查询系统上传附件管理列表
     *
     * @param sysAttachments 系统上传附件管理信息
     * @return 系统上传附件管理集合
     */
    @Override
    public List<SysAttachments> selectSysAttachmentsList(SysAttachments sysAttachments)
    {
        return sysAttachmentsMapper.selectSysAttachmentsList(sysAttachments);
    }


    /**
     * 分页模糊查询系统上传附件管理列表
     * @return 系统上传附件管理集合
     */
    @Override
    public Page selectSysAttachmentsListByLike(Query query)
    {
        SysAttachments sysAttachments =  BeanUtil.mapToBean(query.getCondition(), SysAttachments.class,false);
        query.setRecords(sysAttachmentsMapper.selectSysAttachmentsListByLike(query,sysAttachments));
        return query;
    }

    /**
     * 新增系统上传附件管理
     *
     * @param sysAttachments 系统上传附件管理信息
     * @return 结果
     */
    @Override
    public int insertSysAttachments(SysAttachments sysAttachments)
    {
        return sysAttachmentsMapper.insertSysAttachments(sysAttachments);
    }

    /**
     * 修改系统上传附件管理
     *
     * @param sysAttachments 系统上传附件管理信息
     * @return 结果
     */
    @Override
    public int updateSysAttachments(SysAttachments sysAttachments)
    {
        return sysAttachmentsMapper.updateSysAttachments(sysAttachments);
    }


    /**
     * 删除系统上传附件管理
     *
     * @param rowId 系统上传附件管理ID
     * @return 结果
     */
    @Override
    public int deleteSysAttachmentsById(String rowId)
    {
        return sysAttachmentsMapper.deleteSysAttachmentsById( rowId);
    };

    @Override
    public int deleteSysAttachmentsByBusCode(String busCode)
    {
        return sysAttachmentsMapper.deleteSysAttachmentsByBusCode( busCode);
    };


    /**
     * 批量删除系统上传附件管理对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSysAttachmentsByIds(Integer[] rowIds)
    {
        return sysAttachmentsMapper.deleteSysAttachmentsByIds( rowIds);
    }

    @Override
    public R saveFileName(SysAttachments sysAttachments) {
        R r=new R();
        if(sysAttachments!=null){
            if("".equals(sysAttachments.getFileName()) && sysAttachments.getFileName()==null){
                r.setCode(500);
                r.setB(false);
                r.setMsg("文件名称为空");
                return r;
            }else{
                if(isFileName(sysAttachments.getFileName())==false){
                    r.setCode(500);
                    r.setB(false);
                    r.setMsg("文件名称后缀不符合规范，请传入后缀为jpg/jpeg/gif/png/pdf的文件");
                    return r;
                }
            }
            if("".equals(sysAttachments.getFileUrl()) && sysAttachments.getFileUrl() ==null){
                r.setCode(500);
                r.setB(false);
                r.setMsg("文件地址为空");
                return r;
            }else{
                if(isFileName(sysAttachments.getFileUrl())==false){
                    r.setCode(500);
                    r.setB(false);
                    r.setMsg("url地址后缀不符合规范，请传入后缀为jpg/jpeg/gif/png/pdf的文件");
                    return r;
                }
            }
            SysAttachments sysAttach=new SysAttachments();
            sysAttach.setFileName(sysAttachments.getFileName());
            sysAttach.setFileUrl(sysAttachments.getFileUrl());
            String content = JSONUtil.parseObj(sysAttach, true).toStringPretty();
            System.out.println("入参:"+content);
            String doPost = signatureController.doPost("/v1/ent/export/attachment/upload", content);
            System.out.println("返回值:"+doPost);
            JSONObject result = JSONUtil.parseObj(doPost);
            String b = String.valueOf(result.get("code"));
            String str = "0";
            if(!"".equals(b) && b!= null && str.equals(b)){
                String fileId = (String) result.get("detail");
                sysAttachments.setId(fileId);
                sysAttachments.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                sysAttachments.setAddWho(SecurityUtils.getUserInfo().getUserName());
                sysAttachments.setAddTime(LocalDateTime.now());
                sysAttachmentsMapper.insertSysAttachments(sysAttachments);
            }else{
                r.setMsg(String.valueOf(result.get("msg")));
                r.setCode(500);
                r.setB(false);
                return r;
            }
        }
        return r;
    }

    public static Boolean isFileName(String fileName){
        String reg = ".+(.JPEG|.jpeg|.JPG|.jpg|.png|.PNG|.gif|.GIF|.pdf|.PDF)$";
        Matcher matcher = Pattern.compile(reg).matcher(fileName);
        return matcher.find();
    }

}
