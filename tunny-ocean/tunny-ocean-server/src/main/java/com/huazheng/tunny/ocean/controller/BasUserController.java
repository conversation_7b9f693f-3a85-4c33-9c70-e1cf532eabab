package com.huazheng.tunny.ocean.controller;


import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.BasUser;
import com.huazheng.tunny.ocean.api.entity.UserInfo;
import com.huazheng.tunny.ocean.api.util.ResetForm;
import com.huazheng.tunny.ocean.api.util.TokenUtil;
import com.huazheng.tunny.ocean.api.vo.UserVO;
import com.huazheng.tunny.ocean.service.BasUserService;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.time.LocalDate;
import java.util.*;

/**
 * 系统管理员/用户信息表
 *
 * <AUTHOR> code ocean
 * @date 2021-07-01 10:36:38
 */
@RestController
@RequestMapping("/basuser")
@Slf4j
public class BasUserController {
    @Autowired
    private BasUserService basUserService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  basUserService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return basUserService.selectBasUserListByLike(new Query<>(params));
    }


    /**
     * 信息
     *
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        BasUser basUser = basUserService.selectById(rowId);
        return new R<>(basUser);
    }

    /**
     * 保存
     *
     * @param basUser
     * @return R
     */
    @PostMapping("/save")
    public R save(@RequestBody BasUser basUser) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        basUser.setRowId(UUID.randomUUID().toString());
        basUser.setAddWho(userInfo.getUserName());
        basUser.setAddTime(new Date());
        basUserService.insert(basUser);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     *
     * @param basUser
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody BasUser basUser) {
        basUserService.updateById(basUser);
        return new R<>(Boolean.TRUE);
    }


    /**
     * 删除
     *
     * @param rowId
     * @return R
     */
    @DeleteMapping("/{rowId}")
    public R delete(@PathVariable String rowId) {
        BasUser basUser = basUserService.selectById(rowId);
        if (null != basUser) {
            basUser.setDeleteFlag("Y");
            basUserService.updateById(basUser);
        }else {
            return new R<>(500,false,"未存在该条数据");
        }
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     *
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody List<String> rowIds) {
        basUserService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 导出EXCEL
     *
     * @return
     */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<BasUser> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = basUserService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }


    /**
     * 导入EXCEL
     * <p>
     *
     * @param titleStr
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<BasUser> list = reader.readAll(BasUser.class);
        basUserService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }

    //校验当前登录状态
    @PostMapping("/validate")
    public R validate(HttpServletRequest request) {
        UserInfo userInfo;
        try {
            userInfo = TokenUtil.getUserInfo();
            if (null == userInfo) {
                return new R(500, false, "token过期,请重新登陆", "token过期,请重新登陆");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new R(500, false, "token过期,请重新登陆", "token过期,请重新登陆");
        }
        return new R(0, true, "身份验证通过!", "身份验证通过!");
    }

    /**
     * 修改登陆密码
     *
     * @param form
     * @return R
     */
    @PostMapping("/updateAppBasUser")
    public R updateAppBasUser(@ModelAttribute ResetForm form) {
        if (org.apache.commons.lang.StringUtils.isBlank(form.getOldPwd())) {
            return new R(400, false, "旧密码不能为空！", "旧密码不能为空！");
        }
        if (org.apache.commons.lang.StringUtils.isBlank(form.getNewPwd())) {
            return new R(400, false, "新密码不能为空！", "新密码不能为空！");
        }
        if (form.getNewPwd().equals(form.getOldPwd())) {
            return new R(400, false, "新密码与旧密码不能相同！", "新密码与旧密码不能相同！");
        }
        EntityWrapper<BasUser> wrapper = new EntityWrapper<>();
        wrapper.eq("sysAccount", form.getUserId());
        BasUser user = basUserService.selectOne(wrapper);
        if (null == user) {
            return new R(400, false, "用户不存在！", "用户不存在！");
        }
        String oldS = DigestUtils.sha256Hex(form.getOldPwd());
        if (org.apache.commons.lang.StringUtils.isNotBlank(user.getLoginPwd())) {
            if (!oldS.equals(user.getLoginPwd())) {
                return new R(500, false, "旧密码输入错误！", "旧密码输入错误！");
            }
        } else {
            return new R(500, false, "用户信息有误！请联系管理员", "用户信息有误！请联系管理员");
        }
        user.setLoginPwd(DigestUtils.sha256Hex(form.getNewPwd()));
        basUserService.updateById(user);
        return new R<>(0, true, "修改成功", "修改成功");
    }

    @PostMapping("/userCheckAndLogin")
    public R userCheckAndLogin(@RequestBody UserVO userVO, HttpServletRequest request) throws UnsupportedEncodingException {
        R r = basUserService.checkUser(userVO, request);
        if ("success".equals(r.getMsg())) {
            BasUser user = new BasUser();
            user.setDeleteFlag("N");
            user.setLoginFlag("0");
            user.setAbleStatus("1");//有效
            user.setSysAccount(userVO.getUserCode());
            List<BasUser> basUsers = basUserService.selectBasUserList(user);
            if (basUsers.isEmpty()) {
                return new R<>(Boolean.FALSE, "该账号无权限登录");
            } else {
                //是否大于账号有效期止日期
                if (LocalDate.now().isAfter(basUsers.get(0).getContractEnd())) {
                    return new R<>(Boolean.FALSE, "该账号已经超过有效期");
                }
            }
            //修改该账号登陆状态StreamDecoder
            BasUser user1 = new BasUser();
            user1.setRowId(basUsers.get(0).getRowId());
            user1.setLoginFlag("1");
            user1.setUpdateWho(userVO.getUserCode());
            user1.setUpdateTime(new Date());
            basUserService.updateBasUser(user1);

            UserVO userVO1 = new UserVO();
            String token = TokenUtil.createToken(basUsers.get(0).getSysAccount(), userVO.getSystemCode(), basUsers.get(0).getCustomerCode());//系统账号+系统编码+客户编码
            userVO1.setToken(token);
            userVO1.setUserCode(basUsers.get(0).getCustomerCode());
            userVO1.setSystemCode(userVO.getSystemCode());
            userVO1.setBasUser(basUsers.get(0));
            return new R<>(userVO1);
        } else {
            return r;
        }
    }

}
