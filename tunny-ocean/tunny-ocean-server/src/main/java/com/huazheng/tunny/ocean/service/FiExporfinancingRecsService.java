package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FiExporfinancingRecs;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 出口融资还款信息表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-04-25 12:23:13
 */
public interface FiExporfinancingRecsService extends IService<FiExporfinancingRecs> {
    /**
     * 查询出口融资还款信息表信息
     *
     * @param rowId 出口融资还款信息表ID
     * @return 出口融资还款信息表信息
     */
    public FiExporfinancingRecs selectFiExporfinancingRecsById(String rowId);

    /**
     * 查询出口融资还款信息表列表
     *
     * @param fiExporfinancingRecs 出口融资还款信息表信息
     * @return 出口融资还款信息表集合
     */
    public List<FiExporfinancingRecs> selectFiExporfinancingRecsList(FiExporfinancingRecs fiExporfinancingRecs);


    /**
     * 分页模糊查询出口融资还款信息表列表
     * @return 出口融资还款信息表集合
     */
    public Page selectFiExporfinancingRecsListByLike(Query query);



    /**
     * 新增出口融资还款信息表
     *
     * @param fiExporfinancingRecs 出口融资还款信息表信息
     * @return 结果
     */
    public int insertFiExporfinancingRecs(FiExporfinancingRecs fiExporfinancingRecs);

    /**
     * 修改出口融资还款信息表
     *
     * @param fiExporfinancingRecs 出口融资还款信息表信息
     * @return 结果
     */
    public int updateFiExporfinancingRecs(FiExporfinancingRecs fiExporfinancingRecs);

    /**
     * 删除出口融资还款信息表
     *
     * @param rowId 出口融资还款信息表ID
     * @return 结果
     */
    public int deleteFiExporfinancingRecsById(String rowId);

    /**
     * 批量删除出口融资还款信息表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiExporfinancingRecsByIds(Integer[] rowIds);

}

