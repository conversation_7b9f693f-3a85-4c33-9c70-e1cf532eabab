package com.huazheng.tunny.ocean.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.*;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.api.feign.RemoteAdminService;
import com.huazheng.tunny.ocean.api.util.TokenUtil;
import com.huazheng.tunny.ocean.api.vo.SysDictVo;
import com.huazheng.tunny.ocean.mapper.StationManagementMapper;
import com.huazheng.tunny.ocean.mapper.SysDictMapper;
import com.huazheng.tunny.ocean.mapper.WaybillHeaderMapper;
import com.huazheng.tunny.ocean.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 运单主表
 *
 * <AUTHOR> code ocean
 * @date 2021-07-05 10:57:51
 */
@RestController
@RequestMapping("/waybillheader")
@Slf4j
public class WaybillHeaderController {
    @Autowired
    private WaybillHeaderService waybillHeaderService;
    @Autowired
    private AudiopinionService audiopinionService;
    @Autowired
    private WaybillContainerInfoService waybillContainerInfoService;//运单集装箱数据主信息
    @Autowired
    private WaybillGoodsInfoService waybillGoodsInfoService;//运单集装箱货物信息
    @Autowired
    private SysDictMapper sysDictMapper;//运单付费代码信息
    @Autowired
    private StationManagementMapper stationMapper;//运单付费代码信息
    @Autowired
    private WaybillParticipantsService waybillParticipantsService;//运单集装箱参与方信息
    @Autowired
    private WaybillContainerchargeDetailService waybillContainerchargeDetailService;//运单集装箱费用信息
    @Autowired
    private PayCodeMesService payCodeMesService;//运单付费代码信息
    @Autowired
    private WaybillContainerchargeHeaderService waybillContainerchargeHeaderService;
    @Autowired
    private FdCostService fdCostService;
    @Autowired
    private WaybillHeaderMapper waybillHeaderMapper;
    @Autowired
    private OperationLogService operationLogService;
    @Autowired
    private SignatureController signatureController;
    @Autowired
    private ContainerTypeDataService containerTypeDataService;
    @Autowired
    private PlatformCheckService platformCheckService;
    @Autowired
    private ShifmanagementService shifmanagementService;
    @Autowired
    private FdShippingAccoundetailService fdShippingAccoundetailService;
    @Autowired
    private RemoteAdminService remoteAdminService;
    @Value("${path.windowsPath}")
    private String windowsPath;
    @Value("${path.linuxPath}")
    private String linuxPath;
    @Value("${db.database}")
    private String database;

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //运单主表模糊查询
        return waybillHeaderService.selectWaybillHeaderListByLike(new Query<>(params));
    }

    /**
     * 查询审核状态
     *
     * @param jsonObject
     * @return R
     */
    @PostMapping("/queryAuditForSh")
    public String queryAuditForSh(@RequestBody JSONObject jsonObject) {
        String data = null;
//        String content2 = null;
        String content = null;
        try {
            data = signatureController.getPost(jsonObject);
//            content2 = String.valueOf(jsonObject.get("content"));
//
//            OperationLog log4 = new OperationLog();
//            log4.setUuid(UUID.randomUUID().toString());
//            log4.setProcessType("查询订舱审核结果");
//            log4.setOperationCode("sh");
//            log4.setOperationName("上合");
//            log4.setOperationTime(new Date());
//            log4.setOperationOpinion(content2 + data);
//            operationLogService.insertOperationLog(log4);

            content = waybillHeaderService.queryAuditForSh(data);
        } catch (Exception e) {
            System.out.println(e.toString());
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE, "系统错误：" + e), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPostSh2("/waybillheader/queryAuditForSh", content);
        return result;
    }

    /**
     * 客户线路车数统计表
     *
     * @param params
     * @return
     */
    @GetMapping("/selectCustomerShippingLineList")
    public Page selectCustomerShippingLineList(@RequestParam Map<String, Object> params) {
        return waybillHeaderService.selectCustomerShippingLineList(new Query<>(params));
    }

    /**
     * 金融查询历史订舱信息
     */
    @GetMapping("/selectFiBookingFee")
    public R selectFiBookingFee(@RequestParam Map<String, Object> params) throws ParseException {

        return waybillHeaderService.selectFiBookingFee(params);
    }

    /**
     * 信息
     *
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        WaybillHeader waybillHeader = waybillHeaderService.selectById(rowId);
        return new R<>(waybillHeader);
    }

    /**
     * 保存
     *
     * @param waybillHeader
     * @return R
     */
    @PostMapping
    public R save(@RequestBody WaybillHeader waybillHeader) {
        waybillHeaderService.insertWaybillHeader(waybillHeader);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 删除运单相关信息
     *
     * @param waybillHeader
     * @return R
     */
    @PutMapping
    public R update(@RequestBody WaybillHeader waybillHeader) {
        return waybillHeaderService.updateWaybillInfos(waybillHeader);
    }

    /**
     * 修改运单主表
     *
     * @param waybillHeader
     * @return R
     */
    @PutMapping("/updateHeader")
    public R updateHeader(@RequestBody WaybillHeader waybillHeader) {
        int flag = waybillHeaderService.updateWaybillHeader(waybillHeader);
        if (flag != 0) {
            return new R<>(Boolean.TRUE, "提交成功");
        } else {
            return new R<>(Boolean.FALSE, "提交失败");
        }
    }

    /**
     * 1确认补传资料
     *
     * @param waybillHeader
     * @return R
     */
    @PutMapping("/commitStatus")
    public R commitStatus(@RequestBody WaybillHeader waybillHeader) {
        int flag = waybillHeaderService.commitStatus(waybillHeader);
        if (flag != 0) {
            return new R<>(Boolean.TRUE, "提交成功");
        } else {
            return new R<>(Boolean.FALSE, "提交失败");
        }
    }

    @PostMapping("/commitStatusForSh")
    public String commitStatusForSh(@RequestBody JSONObject jsonObject) {
        String data = null;
        String content2 = null;
        String content = null;
        try {
            data = signatureController.getPost(jsonObject);
            content2 = String.valueOf(jsonObject.get("content"));

            OperationLog log4 = new OperationLog();
            log4.setUuid(UUID.randomUUID().toString());
            log4.setProcessType("确认支付");
            log4.setOperationCode("sh");
            log4.setOperationName("上合");
            log4.setOperationTime(new Date());
            log4.setOperationOpinion(content2 + data);
            operationLogService.insertOperationLog(log4);

            content = waybillHeaderService.commitStatusForSh(data);
        } catch (Exception e) {
            System.out.println(e.toString());
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE, "系统错误：" + e), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPostSh2("/waybillheader/commitStatusForSh", content);
        return result;
    }


    /**
     * 功能描述: 判断是否为空行
     *
     * @param row 行对象
     * @return boolean
     * <AUTHOR> zheng
     * @date 2021/10/13
     */
    private boolean isRowEmpty(Row row) {
        if (row != null) {
            for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
                Cell cell = row.getCell(c);
                if (cell != null) {
                    cell.setCellType(CellType.STRING);
                }
                if (cell != null && !"".equals(cell.getStringCellValue())) {
                    return false;//不是空行
                }
            }
        }
        return true;//是空行
    }

    /**
     * 导出接口
     *
     * @param map
     * @param response
     * @throws IOException
     */
    @PostMapping("/exportedFiBookingFee")
    public void exportedFile(@RequestBody Map<String, Object> map, HttpServletResponse response) throws IOException, ParseException {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("历史订舱单信息");
        XSSFRow row = sheet.createRow(0);
        //宽度
        for (int i = 0; i <= 17; i++) {
            sheet.setColumnWidth(i, 60 * 80);
        }
        row.setHeight((short) (10 * 50));
        //字体
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");

        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);//水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setBorderBottom(BorderStyle.THIN); //下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);


        XSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("序号");
        cell0.setCellStyle(style);
        XSSFCell cell1 = row.createCell(1);
        cell1.setCellValue("年");
        cell1.setCellStyle(style);
        XSSFCell cell2 = row.createCell(2);
        cell2.setCellValue("月");
        cell2.setCellStyle(style);
        XSSFCell cell3 = row.createCell(3);
        cell3.setCellValue("订舱费用");
        cell3.setCellStyle(style);
        XSSFCell cell4 = row.createCell(4);
        cell4.setCellValue("已支付费用");
        cell4.setCellStyle(style);
        XSSFCell cell5 = row.createCell(5);
        cell5.setCellValue("支付率(%)");
        cell5.setCellStyle(style);


        R r = this.selectFiBookingFee(map);

        int i = 1;
        List<Map<String, Object>> listMap = (List<Map<String, Object>>) r.getData();
        if (listMap.size() > 0) {
            for (Map<String, Object> maps : listMap) {
                XSSFRow rows = sheet.createRow(i);
                rows.createCell(0).setCellValue(i);
                if (maps.isEmpty() == false) {
                    if (maps.containsKey("year")) {
                        rows.createCell(1).setCellValue(maps.get("year").toString());
                    }
                    if (maps.containsKey("month")) {
                        rows.createCell(2).setCellValue(maps.get("month").toString());
                    }
                    if (maps.containsKey("totalCost")) {
                        rows.createCell(3).setCellValue(maps.get("totalCost").toString());
                    }
                    if (maps.containsKey("havePaid")) {
                        rows.createCell(4).setCellValue(maps.get("havePaid").toString());
                    }
                    if (maps.containsKey("payment")) {
                        rows.createCell(5).setCellValue(maps.get("payment").toString());
                    }
                }
                i++;
            }
        }

        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String("历史融资信息.xls".getBytes("GB2312"), "8859_1"));
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }

    @PostMapping("/saveInfoForSh")
    public String saveInfoForSh(@RequestBody JSONObject jsonObject) {
        String data = null;
        String content2 = null;
        String content = null;
        try {
            data = signatureController.getPost(jsonObject);
            content2 = String.valueOf(jsonObject.get("content"));

            OperationLog log4 = new OperationLog();
            log4.setUuid(UUID.randomUUID().toString());
            log4.setProcessType("订单补充资料");
            log4.setOperationCode("sh");
            log4.setOperationName("上合");
            log4.setOperationTime(new Date());
            log4.setOperationOpinion(content2 + data);
            operationLogService.insertOperationLog(log4);

            content = waybillHeaderService.saveInfoForSh(data);
        } catch (Exception e) {
            System.out.println(e.toString());
            content = JSONUtil.parseObj(new R<>(Boolean.FALSE, "系统错误：" + e), false).toStringPretty();
        }
        //调用接口
        String result = signatureController.returnPostSh2("/bookingrequesheader/saveForSh", content);
        return result;
    }

    /**
     * 导入国联运单
     */
    @PostMapping("/imported")
    @Transactional(rollbackFor = Exception.class)
    public R imported(@RequestParam("file") MultipartFile file, @RequestParam("waybillNo") String waybillNo) {
        WaybillHeader wh = new WaybillHeader();
        wh.setWaybillNo(waybillNo);
        wh.setBillStatus("0");
        List<WaybillHeader> list = waybillHeaderService.selectWaybillHeaderList(wh);
        if (list != null && list.size() != 0) {
        } else {
            return new R(500, Boolean.FALSE, "当前运单非待补传资料状态，不可导入表格数据");
        }
        WaybillContainerInfo wci = new WaybillContainerInfo();
        wci.setWaybillNo(waybillNo);
        List<WaybillContainerInfo> cnList = waybillContainerInfoService.selectWaybillContainerInfoList(wci);
        List<String> stringList = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder();
        for (WaybillContainerInfo containerInfo : cnList) {
            stringBuilder.append(containerInfo.getContainerNo()).append(",");
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Workbook workbook = null;
        try {
            InputStream is = file.getInputStream();
            //利用WorkbookFactory获取Sheet，无需判断版本
            workbook = WorkbookFactory.create(is);
        } catch (IOException e) {
            e.printStackTrace();
        } catch (InvalidFormatException e) {
            e.printStackTrace();
        }
        /*运单舱单信息(必填)sheet*/
        try {
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String usercode = userInfo.getUserName();
            String username = userInfo.getRealName();
            //导入第一张sheet页，现为"运单舱单信息(必填)"
            Sheet sheet = workbook.getSheetAt(0);
            //获取行数
            int lastRowNum = sheet.getLastRowNum();
            List<WaybillContainerInfo> listCon = new ArrayList();   /*集装箱信息组装集合*/
            /*用以更新的集合*/
            List<WaybillParticipants> listPants = new ArrayList<>();   /*货物参与方信息组装集合*/
            /*用以新增的集合*/
            List<WaybillParticipants> listPantsSav = new ArrayList<>();   /*货物参与方信息组装集合*/

            Cell serialNo = sheet.getRow(lastRowNum).getCell(0);//最后一行序号
            Cell containerNo1 = sheet.getRow(lastRowNum).getCell(1);//最后一行箱号
            /*最后一行为空白行的话，最后一行下标减1*/
            if (serialNo == null && containerNo1 == null && lastRowNum > 4) {//第一行数据下标为4，5为有两条数据
                lastRowNum = lastRowNum - 1;
            }
            ContainerTypeData sel = new ContainerTypeData();
            sel.setDeleteFlag("N");
            List<ContainerTypeData> containerTypeDataList = containerTypeDataService.selectContainerTypeDataList(sel);

            String platformCode = platformCheckService.getPlatformCode("DDHCBCZL");
            //循环校验标识
            Boolean cirFlag = true;
            for (int i = 4; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                boolean blankFlag = isRowEmpty(row);
                if (blankFlag == true) {
                    continue;
                }
                WaybillContainerInfo containerInfo = new WaybillContainerInfo();//集装箱主数据
                WaybillParticipants pants = new WaybillParticipants(); //发货人
                WaybillParticipants pantsRec = new WaybillParticipants(); //收货人

                containerInfo.setWaybillNo(waybillNo);
                containerInfo.setUpdateWhoName(username);
                containerInfo.setUpdateWho(usercode);
                containerInfo.setUpdateTime(new Date());
                //组装收发货人信息
                pants.setWaybillNo(waybillNo);
                pantsRec.setWaybillNo(waybillNo);

                //设置文本格式防止转换异常
                if (row.getCell(1) != null) {
                    row.getCell(1).setCellType(CellType.STRING);
                } else {
                    return new R(500, Boolean.FALSE, "请检查<运单舱单信息>sheet页，第" + (i + 1) + "行箱号未填写或者存在末尾空白行，如有疑问请联系管理员");
                }
                String str = "";
                String containerNo = row.getCell(1).getStringCellValue();
                containerNo = containerNo.trim();
                str = containerNo;
                stringList.add(str);
                if (cirFlag == true) {
//                    for (WaybillContainerInfo ci:cnList) {
//                        String cn=ci.getContainerNo();
//                        //如果excel中有大于等于一个的箱号和订舱时箱号匹配，那么校验通过cirFlag置为false,无须再次进入循环校验
//                        if(cn.equals(containerNo)){
//                            cirFlag=false;
//                            break;
//                        }
//                        if(i==lastRowNum){
//                            return new R(500,Boolean.FALSE,"请检查<运单舱单信息>sheet页，没有发现与初次订舱时箱号的匹配");
//                        }
//                    }
                    //如果excel中有大于等于一个的箱号和订舱时箱号匹配，那么校验通过cirFlag置为false,无须再次进入循环校验
                    if (stringBuilder.toString().contains(containerNo)) {
                        cirFlag = false;
//                        break;
                    } else {
                        return new R(500, Boolean.FALSE, "请检查<运单舱单信息>sheet页，箱号: " + containerNo + " 没有发现与初次订舱时箱号的匹配");
                    }
                }
                boolean flag2 = verifyCntrCode(containerNo);
                if (flag2) {
                } else {
                    return new R(500, Boolean.FALSE, "请检查<运单舱单信息>sheet页，第" + (i + 1) + "行箱号填写有误，如有疑问请联系管理员");
                }

                List<Cell> cellList = new ArrayList();
                cellList.add(row.getCell(2));
                cellList.add(row.getCell(3));
                cellList.add(row.getCell(4));
                cellList.add(row.getCell(5));
                cellList.add(row.getCell(7));
                cellList.add(row.getCell(9));
                cellList.add(row.getCell(31));
                cellList.add(row.getCell(35));
                cellList.add(row.getCell(63));
                cellList.add(row.getCell(64));
                cellList.add(row.getCell(66));
                cellList.add(row.getCell(67));
                cellList.add(row.getCell(73));
                cellList.add(row.getCell(76));
                R r = this.judgeNone(cellList, i + 1, "<运单舱单信息(必填)>sheet页");
                if (r.getStatusCode() == 200) {
                } else {
                    return r;
                }

                //校验必填项 start****************
                if (row.getCell(10) != null) {
                    row.getCell(10).setCellType(CellType.STRING);
                }
                if (row.getCell(11) != null) {
                    row.getCell(11).setCellType(CellType.STRING);
                }
                if ((row.getCell(10) != null && !"".equals(row.getCell(10).getStringCellValue())
                        || (row.getCell(11) != null && !"".equals(row.getCell(11).getStringCellValue())))) {
                } else {
                    return new R(500, Boolean.FALSE, "请检查" + (i + 1) + "行中发货人英文、俄文名称须至少选填一项");
                }
                if (row.getCell(32) != null) {
                    row.getCell(32).setCellType(CellType.STRING);
                }
                if (row.getCell(33) != null) {
                    row.getCell(33).setCellType(CellType.STRING);
                }

                if ((row.getCell(32) != null && !"".equals(row.getCell(32).getStringCellValue())
                        || (row.getCell(33) != null && !"".equals(row.getCell(33).getStringCellValue())))) {
                } else {
                    return new R(500, Boolean.FALSE, "请检查" + (i + 1) + "行中收货人英文、俄文名称须至少选填一项");
                }

                /*if(row.getCell(36)!=null){
                    row.getCell(36).setCellType(CellType.STRING);
                }
                if(row.getCell(37)!=null){
                    row.getCell(37).setCellType(CellType.STRING);
                }
                if((row.getCell(36)!=null&&!row.getCell(36).getStringCellValue().equals("")
                        ||(row.getCell(37)!=null&&!row.getCell(37).getStringCellValue().equals("")))){}else {
                    return new R(500,Boolean.FALSE,"请检查"+(i+1)+"行中收货人英文、俄文详细地址须至少选填一项");
                }*/

                //校验必填项 end ****************
                containerNo = containerNo.trim();
                if (containerNo.contains("\n")) {
                    containerNo.replace("\n", "");
                }
                containerInfo.setContainerNo(containerNo);//集装箱实体添加箱号
                pants.setContainerNo(containerNo);//发货人实体箱号
                pantsRec.setContainerNo(containerNo);//收货人实体箱号
                //组装集装箱信息

//                containerInfo.setContainerType(row.getCell(2).getStringCellValue());//箱型
                String containerTypeCode = row.getCell(2).getStringCellValue();
                Boolean flag = true;
                for (ContainerTypeData data : containerTypeDataList
                ) {
                    if (data.getContainerTypeCode().equals(containerTypeCode)) {
                        containerInfo.setContainerTypeCode(data.getContainerTypeCode());
                        containerInfo.setContainerTypeName(data.getContainerTypeName());
                        containerInfo.setContainerType(data.getContainerTypeSize());
                        flag = false;
                        break;
                    }
                }

                if (flag) {
                    return new R(500, Boolean.FALSE, "未查询到该箱型代码：" + containerTypeCode);
                }
                //row.getCell(3).setCellType(CellType.NUMERIC);
                containerInfo.setContainerDeadWeight(Float.parseFloat(row.getCell(3).getStringCellValue()));//箱自重
                containerInfo.setStationCompilation(row.getCell(4).getStringCellValue().replace(" ", ""));//发站站编
                String startName = stationMapper.selectStationMesByCode(row.getCell(4).getStringCellValue().trim());//发站站名
                containerInfo.setStartStationName(startName);
                String code = row.getCell(5).getStringCellValue();
                if (StrUtil.isNotEmpty(code)) {
                    if (code.contains("-")) {
                        code = code.split("-")[1];
                    }
                }
                containerInfo.setEndCompilation(code.replace(" ", ""));//到站站编
                String endName = stationMapper.selectStationMesByCode(code.trim());//到站站名
                containerInfo.setEndStationName(endName);

                if (row.getCell(6) != null) {
                    row.getCell(6).setCellType(CellType.STRING);
                }
                if (row.getCell(8) != null) {
                    row.getCell(8).setCellType(CellType.STRING);
                }
                if (row.getCell(6) != null && !"".equals(row.getCell(6).getStringCellValue())) {
                    containerInfo.setArrivalRemarks(row.getCell(6).getStringCellValue());//到站备注
                }
                containerInfo.setFrontierPortstation(row.getCell(7).getStringCellValue());//国境口岸站编
                if (row.getCell(8) != null && !"".equals(row.getCell(8).getStringCellValue())) {
                    containerInfo.setTitle(row.getCell(8).getStringCellValue());//封号
                }

                if (row.getCell(12) != null) {
                    row.getCell(12).setCellType(CellType.STRING);
                }
                if (row.getCell(29) != null) {
                    row.getCell(29).setCellType(CellType.STRING);
                }

                if (row.getCell(30) != null) {
                    row.getCell(30).setCellType(CellType.STRING);
                }
                if (row.getCell(15) != null) {
                    row.getCell(15).setCellType(CellType.STRING);
                }
                if (row.getCell(16) != null) {
                    row.getCell(16).setCellType(CellType.STRING);
                }
                if (row.getCell(17) != null) {
                    row.getCell(17).setCellType(CellType.STRING);
                }

                if (row.getCell(19) != null) {
                    row.getCell(19).setCellType(CellType.STRING);
                }
                if (row.getCell(20) != null) {
                    row.getCell(20).setCellType(CellType.STRING);
                }
                if (row.getCell(21) != null) {
                    row.getCell(21).setCellType(CellType.STRING);
                }

                if (row.getCell(23) != null) {
                    row.getCell(23).setCellType(CellType.STRING);
                }
                if (row.getCell(24) != null) {
                    row.getCell(24).setCellType(CellType.STRING);
                }
                if (row.getCell(25) != null) {
                    row.getCell(25).setCellType(CellType.STRING);
                }

                if (row.getCell(18) != null) {
                    row.getCell(18).setCellType(CellType.STRING);
                }
                if (row.getCell(22) != null) {
                    row.getCell(22).setCellType(CellType.STRING);
                }
                if (row.getCell(26) != null) {
                    row.getCell(26).setCellType(CellType.STRING);
                }
                if (row.getCell(27) != null) {
                    row.getCell(27).setCellType(CellType.STRING);
                }
                if (row.getCell(14) != null) {
                    row.getCell(14).setCellType(CellType.STRING);
                }

                if (row.getCell(36) != null) {
                    row.getCell(36).setCellType(CellType.STRING);
                }
                if (row.getCell(37) != null) {
                    row.getCell(37).setCellType(CellType.STRING);
                }
                if (row.getCell(38) != null) {
                    row.getCell(38).setCellType(CellType.STRING);
                }

                if (row.getCell(40) != null) {
                    row.getCell(40).setCellType(CellType.STRING);
                }
                if (row.getCell(41) != null) {
                    row.getCell(41).setCellType(CellType.STRING);
                }
                if (row.getCell(42) != null) {
                    row.getCell(42).setCellType(CellType.STRING);
                }

                if (row.getCell(44) != null) {
                    row.getCell(44).setCellType(CellType.STRING);
                }
                if (row.getCell(45) != null) {
                    row.getCell(45).setCellType(CellType.STRING);
                }
                if (row.getCell(46) != null) {
                    row.getCell(46).setCellType(CellType.STRING);
                }

                if (row.getCell(48) != null) {
                    row.getCell(48).setCellType(CellType.STRING);
                }

                if (row.getCell(49) != null) {
                    row.getCell(49).setCellType(CellType.STRING);
                }
                if (row.getCell(50) != null) {
                    row.getCell(50).setCellType(CellType.STRING);
                }
                if (row.getCell(51) != null) {
                    row.getCell(51).setCellType(CellType.STRING);
                }

                if (row.getCell(40) != null) {
                    row.getCell(40).setCellType(CellType.STRING);
                }
                if (row.getCell(41) != null) {
                    row.getCell(41).setCellType(CellType.STRING);
                }
                if (row.getCell(42) != null) {
                    row.getCell(42).setCellType(CellType.STRING);
                }

                if (row.getCell(39) != null) {
                    row.getCell(39).setCellType(CellType.STRING);
                }
                if (row.getCell(43) != null) {
                    row.getCell(43).setCellType(CellType.STRING);
                }
                if (row.getCell(48) != null) {
                    row.getCell(48).setCellType(CellType.STRING);
                }
                if (row.getCell(52) != null) {
                    row.getCell(52).setCellType(CellType.STRING);
                }
                if (row.getCell(53) != null) {
                    row.getCell(53).setCellType(CellType.STRING);
                }
                if (row.getCell(54) != null) {
                    row.getCell(54).setCellType(CellType.STRING);
                }
                if (row.getCell(55) != null) {
                    row.getCell(55).setCellType(CellType.STRING);
                }
                if (row.getCell(56) != null) {
                    row.getCell(56).setCellType(CellType.STRING);
                }
                if (row.getCell(59) != null) {
                    row.getCell(59).setCellType(CellType.STRING);
                }
                if (row.getCell(60) != null) {
                    row.getCell(60).setCellType(CellType.STRING);
                }
                if (row.getCell(61) != null) {
                    row.getCell(61).setCellType(CellType.STRING);
                }

                //组装发货人
                pants.setParticipantsType("F");
                pants.setConsignorName(row.getCell(9).getStringCellValue());//发货人名称
                if (row.getCell(10) != null && !"".equals(row.getCell(10).getStringCellValue())) {
                    pants.setConsignorEnglishname(row.getCell(10).getStringCellValue());//发货人英文名称
                }
                if (row.getCell(11) != null && !"".equals(row.getCell(11).getStringCellValue())) {
                    pants.setConsignorRussianame(row.getCell(11).getStringCellValue());//发货人俄文名称
                }
                if (row.getCell(12) != null && !"".equals(row.getCell(12).getStringCellValue())) {
                    pants.setCitizenId(row.getCell(12).getStringCellValue());//公民税务系统识别码
                }
                if (row.getCell(27) != null && !"".equals(row.getCell(27).getStringCellValue())) {
                    pants.setPostalCode(row.getCell(27).getStringCellValue());//发货人邮政编码
                }
                if (row.getCell(28) != null) {
                    row.getCell(28).setCellType(CellType.STRING);
                }
                pants.setPhone(row.getCell(28).getStringCellValue());//发货人电话
                if (row.getCell(29) != null && !"".equals(row.getCell(29).getStringCellValue())) {
                    pants.setEmail(row.getCell(29).getStringCellValue());//发货人邮箱
                }

                if (row.getCell(30) != null && !"".equals(row.getCell(30).getStringCellValue())) {
                    pants.setFax(row.getCell(30).getStringCellValue());//发货人传真
                }
                pants.setCountryCode(row.getCell(13).getStringCellValue());//所属国家代码
                if (row.getCell(15) != null && !"".equals(row.getCell(15).getStringCellValue())) {
                    pants.setProvince(row.getCell(15).getStringCellValue());//发货人省
                }
                if (row.getCell(16) != null && !"".equals(row.getCell(16).getStringCellValue())) {
                    pants.setCity(row.getCell(16).getStringCellValue());//发货人市
                }
                if (row.getCell(17) != null && !"".equals(row.getCell(17).getStringCellValue())) {
                    pants.setDistrict(row.getCell(17).getStringCellValue());//发货人县/区
                }

                if (row.getCell(19) != null && !"".equals(row.getCell(19).getStringCellValue())) {
                    pants.setEnglishProvince(row.getCell(19).getStringCellValue());//发货人省
                }
                if (row.getCell(20) != null && !"".equals(row.getCell(20).getStringCellValue())) {
                    pants.setEnglishCity(row.getCell(20).getStringCellValue());//发货人市
                }
                if (row.getCell(21) != null && !"".equals(row.getCell(21).getStringCellValue())) {
                    pants.setEnglishDistrict(row.getCell(21).getStringCellValue());//发货人县/区
                }

                if (row.getCell(23) != null && !"".equals(row.getCell(23).getStringCellValue())) {
                    pants.setRussiaProvince(row.getCell(23).getStringCellValue());//发货人省
                }
                if (row.getCell(24) != null && !"".equals(row.getCell(24).getStringCellValue())) {
                    pants.setRussiaCity(row.getCell(24).getStringCellValue());//发货人市
                }
                if (row.getCell(25) != null && !"".equals(row.getCell(25).getStringCellValue())) {
                    pants.setRussiaDistrict(row.getCell(25).getStringCellValue());//发货人县/区
                }

                if (row.getCell(18) != null && !"".equals(row.getCell(18).getStringCellValue())) {
                    pants.setChineseAddress(row.getCell(18).getStringCellValue());//发货人中文详细地址
                }
                if (row.getCell(22) != null && !"".equals(row.getCell(22).getStringCellValue())) {
                    pants.setEnglishAddress(row.getCell(22).getStringCellValue());//发货人英文详细地址
                }
                if (row.getCell(26) != null && !"".equals(row.getCell(26).getStringCellValue())) {
                    pants.setRussiaAddress(row.getCell(26).getStringCellValue());//发货人俄文详细地址
                }
                if (row.getCell(14) != null && !"".equals(row.getCell(14).getStringCellValue())) {
                    pants.setShipperSignature(row.getCell(14).getStringCellValue());//发货人签字
                }

                //组装收货人
                pantsRec.setParticipantsType("S");
                pantsRec.setConsignorName(row.getCell(31).getStringCellValue());//收货人名称
                if (row.getCell(32) != null && !"".equals(row.getCell(32).getStringCellValue())) {
                    pantsRec.setConsignorEnglishname(row.getCell(32).getStringCellValue());//收货人英文名称
                }
                if (row.getCell(33) != null && !"".equals(row.getCell(33).getStringCellValue())) {
                    pantsRec.setConsignorRussianame(row.getCell(33).getStringCellValue());//收货人俄文名称
                }
                if (row.getCell(34) != null && !"".equals(row.getCell(34).getStringCellValue())) {
                    pantsRec.setCitizenId(row.getCell(34).getStringCellValue());//公民税务系统识别码
                }
                if (row.getCell(48) != null && !"".equals(row.getCell(48).getStringCellValue())) {
                    pantsRec.setPostalCode(row.getCell(48).getStringCellValue());//收货人邮政编码
                }
                if (row.getCell(49) != null && !"".equals(row.getCell(49).getStringCellValue())) {
                    pantsRec.setPhone(row.getCell(49).getStringCellValue());//收货人电话
                }
                if (row.getCell(50) != null && !"".equals(row.getCell(50).getStringCellValue())) {
                    pantsRec.setEmail(row.getCell(50).getStringCellValue());//收货人邮箱
                }
                if (row.getCell(51) != null && !"".equals(row.getCell(51).getStringCellValue())) {
                    pantsRec.setFax(row.getCell(51).getStringCellValue());//收货人传真
                }
                pantsRec.setCountryCode(row.getCell(35).getStringCellValue());//所属国家代码

                if (row.getCell(36) != null && !"".equals(row.getCell(36).getStringCellValue())) {
                    pantsRec.setProvince(row.getCell(36).getStringCellValue());//收货人省
                }
                if (row.getCell(37) != null && !"".equals(row.getCell(37).getStringCellValue())) {
                    pantsRec.setCity(row.getCell(37).getStringCellValue());//收货人市
                }
                if (row.getCell(38) != null && !"".equals(row.getCell(38).getStringCellValue())) {
                    pantsRec.setDistrict(row.getCell(38).getStringCellValue());//收货人县/区
                }

                if (row.getCell(40) != null && !"".equals(row.getCell(40).getStringCellValue())) {
                    pantsRec.setEnglishProvince(row.getCell(40).getStringCellValue());//收货人省
                }
                if (row.getCell(41) != null && !"".equals(row.getCell(41).getStringCellValue())) {
                    pantsRec.setEnglishCity(row.getCell(41).getStringCellValue());//收货人市
                }
                if (row.getCell(42) != null && !"".equals(row.getCell(42).getStringCellValue())) {
                    pantsRec.setEnglishDistrict(row.getCell(42).getStringCellValue());//收货人县/区
                }

                if (row.getCell(44) != null && !"".equals(row.getCell(44).getStringCellValue())) {
                    pantsRec.setRussiaProvince(row.getCell(44).getStringCellValue());//收货人省
                }
                if (row.getCell(45) != null && !"".equals(row.getCell(45).getStringCellValue())) {
                    pantsRec.setRussiaCity(row.getCell(45).getStringCellValue());//收货人市
                }
                if (row.getCell(46) != null && !"".equals(row.getCell(46).getStringCellValue())) {
                    pantsRec.setRussiaDistrict(row.getCell(46).getStringCellValue());//收货人县/区
                }

                if (row.getCell(39) != null && !"".equals(row.getCell(39).getStringCellValue())) {
                    pantsRec.setChineseAddress(row.getCell(39).getStringCellValue());//收货人中文详细地址
                }
                if (row.getCell(43) != null && !"".equals(row.getCell(43).getStringCellValue())) {
                    pantsRec.setEnglishAddress(row.getCell(43).getStringCellValue());//收货人英文详细地址
                }
                if (row.getCell(47) != null && !"".equals(row.getCell(47).getStringCellValue())) {
                    pantsRec.setRussiaAddress(row.getCell(47).getStringCellValue());//收货人俄文详细地址
                }
                if (row.getCell(52) != null && !"".equals(row.getCell(52).getStringCellValue())) {
                    containerInfo.setResveredField02(row.getCell(52).getStringCellValue());//发货人声明
                }
                if (row.getCell(53) != null && !"".equals(row.getCell(53).getStringCellValue())) {
                    containerInfo.setResveredField03(row.getCell(53).getStringCellValue());//何方装车（О--发货人 П--承运人）
                }
                if (row.getCell(54) != null && !"".equals(row.getCell(54).getStringCellValue())) {
                    containerInfo.setResveredField04(row.getCell(54).getStringCellValue());//确定重量的方法
                }
                if (row.getCell(55) != null && !"".equals(row.getCell(55).getStringCellValue())) {
                    containerInfo.setResveredField05(row.getCell(55).getStringCellValue());//添加附件
                }
                if (row.getCell(56) != null && !"".equals(row.getCell(56).getStringCellValue())) {
                    containerInfo.setTwentyFifth(row.getCell(56).getStringCellValue());// [39]列AN列
                }
                if (row.getCell(59) != null && !"".equals(row.getCell(59).getStringCellValue())) {
                    containerInfo.setSevenLine(row.getCell(59).getStringCellValue());// [42]列AQ列
                }
                if (row.getCell(60) != null && !"".equals(row.getCell(60).getStringCellValue())) {
                    containerInfo.setEightLine(row.getCell(60).getStringCellValue());// [43]八栏
                }
                if (row.getCell(61) != null && !"".equals(row.getCell(61).getStringCellValue())) {
                    containerInfo.setTwentyEighthLine(row.getCell(61).getStringCellValue());// [44]二十八栏
                }
                if (row.getCell(57) != null) {
                    row.getCell(57).setCellType(CellType.STRING);
                }
                if (row.getCell(58) != null) {
                    row.getCell(58).setCellType(CellType.STRING);
                }
                if (row.getCell(62) != null) {
                    row.getCell(62).setCellType(CellType.STRING);
                }
                if (row.getCell(63) != null) {
                    row.getCell(63).setCellType(CellType.STRING);
                }
                if (row.getCell(64) != null) {
                    row.getCell(64).setCellType(CellType.STRING);
                }
                if (row.getCell(66) != null) {
                    row.getCell(66).setCellType(CellType.STRING);
                }
                if (row.getCell(69) != null) {
                    row.getCell(69).setCellType(CellType.STRING);
                }
                if (row.getCell(70) != null) {
                    row.getCell(70).setCellType(CellType.STRING);
                }
                if (row.getCell(71) != null) {
                    row.getCell(71).setCellType(CellType.STRING);
                }
                if (row.getCell(72) != null) {
                    row.getCell(72).setCellType(CellType.STRING);
                }
                if (row.getCell(73) != null) {
                    row.getCell(73).setCellType(CellType.STRING);
                }
                if (row.getCell(74) != null) {
                    row.getCell(74).setCellType(CellType.STRING);
                }
                if (row.getCell(75) != null) {
                    row.getCell(75).setCellType(CellType.STRING);
                }
                if (row.getCell(76) != null) {
                    row.getCell(76).setCellType(CellType.STRING);
                }
                if (row.getCell(77) != null) {
                    row.getCell(77).setCellType(CellType.STRING);
                }
                if ((row.getCell(73) != null && !"".equals(row.getCell(73).getStringCellValue()))) {
                } else {
                    return new R(500, Boolean.FALSE, "请检查" + (i + 1) + "快通准备是否填写");
                }
                if ((row.getCell(76) != null && !"".equals(row.getCell(76).getStringCellValue()))) {
                } else {
                    return new R(500, Boolean.FALSE, "请检查" + (i + 1) + "班列类别是否填写");
                }

                //组装运单主数据
                /*******************************39栏非必填项，略过******************************/
                if (row.getCell(43) != null && !"".equals(row.getCell(43).getStringCellValue())) {
                    containerInfo.setResveredField06(row.getCell(43).getStringCellValue());//特殊承运人
                }
                if (row.getCell(44) != null && !"".equals(row.getCell(44).getStringCellValue())) {
                    containerInfo.setResveredField07(row.getCell(44).getStringCellValue());//箱号栏标记重量值
                }
                /*******************************42栏非必填项，暂定不需展示******************************/
                /*******************************43栏非必填项，暂定不需展示******************************/
                /*******************************44栏非必填项，暂定不需展示******************************/
//                if (row.getCell(45) != null&&!row.getCell(45).getStringCellValue().equals("")){
//                    containerInfo.setResveredField08(row.getCell(45).getStringCellValue());//中哈互使箱
//                }
                if (row.getCell(62) != null && !"".equals(row.getCell(62).getStringCellValue())) {
                    String portStationCode = row.getCell(62).getStringCellValue();
                    List<SysDict> list1 = sysDictMapper.selectSysDictListByCode(98, portStationCode, database);
                    if (list1 != null && list1.size() > 0) {
                        containerInfo.setPortAgent(list1.get(0).getDictValue());
                    }
                    containerInfo.setResveredField09(portStationCode);//口岸代理编码
                }
                if (row.getCell(63) != null && !"".equals(row.getCell(63).getStringCellValue())) {
                    containerInfo.setResveredField10(row.getCell(63).getStringCellValue());//是否海关过境
                }
                containerInfo.setAbroadReachCity(row.getCell(64).getStringCellValue());//境外到达城市
                try {
                    if (row.getCell(65) != null && !"".equals(row.getCell(65).getStringCellValue())) {
                        Date aboarTime = sdf.parse(row.getCell(65).getStringCellValue());
                        containerInfo.setPlanAbroadTime(aboarTime);//预计出/入境时间
                    }
                } catch (ParseException e) {
                    e.printStackTrace();
                    return new R(500, Boolean.FALSE, "导入<运单舱单信息(必填)>sheet页失败，第" + (i + 1) + "行预计出/入境时间填写有误");
                }
                if (row.getCell(70) != null && !"".equals(row.getCell(70).getStringCellValue())) {
                    containerInfo.setShippingFeePayWays(row.getCell(70).getStringCellValue());//运费支付方式
                }
                if (row.getCell(71) != null && !"".equals(row.getCell(71).getStringCellValue())) {
                    containerInfo.setGoodsCustomStatusCode(row.getCell(71).getStringCellValue());//货物海关状态代码
                }
                if (row.getCell(72) != null && !"".equals(row.getCell(72).getStringCellValue())) {
                    containerInfo.setManifestRemarks(row.getCell(72).getStringCellValue());//舱单备注
                }
                if (row.getCell(73) != null && !"".equals(row.getCell(73).getStringCellValue())) {
                    containerInfo.setFastTrain(row.getCell(73).getStringCellValue());//快通准备
                }
                if (row.getCell(74) != null && !"".equals(row.getCell(74).getStringCellValue())) {
                    containerInfo.setDeparturePlaceGq(row.getCell(74).getStringCellValue());//出境启运地关区代码
                }
                if (row.getCell(75) != null && !"".equals(row.getCell(75).getStringCellValue())) {
                    containerInfo.setDeparturePlaceKa(row.getCell(75).getStringCellValue());//出境启运地口岸代码
                }
                if (row.getCell(76) != null && !"".equals(row.getCell(76).getStringCellValue())) {
                    containerInfo.setBlockType(row.getCell(76).getStringCellValue());//班列类别
                }
                //组装货物信息
                try {
                    row.getCell(66).setCellType(CellType.STRING);
                    containerInfo.setGoodsValue(Float.parseFloat(row.getCell(66).getStringCellValue()));//托运货物价值
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                    return new R(500, Boolean.FALSE, "导入<运单舱单信息(必填)>sheet页失败，第" + (i + 1) + "行货物价值填写有误");
                }
                containerInfo.setGoodsAmountTypeCode(row.getCell(67).getStringCellValue());//货物金额类型代码
                if (row.getCell(68) != null && !"".equals(row.getCell(68).getStringCellValue())) {
                    containerInfo.setTranClauseCode(row.getCell(68).getStringCellValue());//运输条款代码
                }
                try {
                    if (row.getCell(69) != null && !"".equals(row.getCell(69).getStringCellValue())) {
                        row.getCell(69).setCellType(CellType.STRING);
                        containerInfo.setGoodsCube(Float.parseFloat(row.getCell(69).getStringCellValue()));//货物体积
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return new R(500, Boolean.FALSE, "导入<运单舱单信息(必填)>sheet页失败，第" + (i + 1) + "行货物体积填写有误");
                }
                try {
                    if (row.getCell(78) != null && !"".equals(row.getCell(78).getStringCellValue())) {
                        row.getCell(78).setCellType(CellType.STRING);
                        if ("是".equals(row.getCell(78).getStringCellValue().trim())) {
                            containerInfo.setIsFull("1");
                        } else if ("否".equals(row.getCell(78).getStringCellValue().trim())) {
                            containerInfo.setIsFull("0");
                        }
                    }
                    if (platformCode.contains(list.get(0).getPlatformCode())) {
                        if (StrUtil.isBlank(containerInfo.getIsFull())) {
                            return new R(500, Boolean.FALSE, "导入<运单舱单信息(必填)>sheet页失败，第" + (i + 1) + "行是否全程不能为空");
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return new R(500, Boolean.FALSE, "导入<运单舱单信息(必填)>sheet页失败，第" + (i + 1) + "行是否全程填写有误");
                }
                try {
                    if (row.getCell(79) != null && !"".equals(row.getCell(79).getStringCellValue())) {
                        row.getCell(79).setCellType(CellType.STRING);
                        if ("是".equals(row.getCell(79).getStringCellValue().trim())) {
                            containerInfo.setNonFerrous("1");
                        } else if ("否".equals(row.getCell(79).getStringCellValue().trim())) {
                            containerInfo.setNonFerrous("0");
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return new R(500, Boolean.FALSE, "导入<运单舱单信息(必填)>sheet页失败，第" + (i + 1) + "行有色金属填写有误");
                }
                //校验新增/保存
                List listTemp1 = waybillParticipantsService.selectWaybillParticipantsByWayBillNo(pants);
                if (listTemp1 != null && listTemp1.size() != 0) {
                    listPants.add(pants);   //添加发货人for update
                } else {
                    listPantsSav.add(pants); //添加发货人for save
                }
                List listTemp2 = waybillParticipantsService.selectWaybillParticipantsByWayBillNo(pantsRec);
                if (listTemp2 != null && listTemp2.size() != 0) {
                    pantsRec.setUpdateWhoName(username);
                    pantsRec.setUpdateWho(usercode);
                    pantsRec.setUpdateTime(LocalDateTime.now());
                    listPants.add(pantsRec);   //添加收货人for update
                } else {
                    pantsRec.setAddTime(LocalDateTime.now());
                    pantsRec.setAddWho(usercode);
                    pantsRec.setAddWhoName(username);
                    listPantsSav.add(pantsRec); //添加收货人for save
                }
                listCon.add(containerInfo);//添加集装箱信息
            }

            waybillContainerInfoService.updateContainerInfoBatch(listCon);

            if (listPants != null && listPants.size() != 0) {
                waybillParticipantsService.updateWaybillParticipants(listPants);
            }
            if (listPantsSav != null && listPantsSav.size() != 0) {
                waybillParticipantsService.insertWaybillParticipants(listPantsSav);
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(e.getMessage() + "##################导入<运单舱单信息(必填)>sheet页失败########################");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new R(500, Boolean.FALSE, "导入<运单舱单信息(必填)>sheet页失败，请检查日期、小数等特殊单元格格式，检查无误后，请联系管理员" + e.getMessage());
        }
        /*<付费代码>sheet*/
        try {
            //导入第二张sheet页，现为"付费代码"
            Sheet sheet2rd = workbook.getSheetAt(1);
            //获取行数
            int lastRowNum2rd = sheet2rd.getLastRowNum();
            List<PayCodeMes> listConCharge = new ArrayList<>();
            List<PayCodeMes> listConChargeDel = new ArrayList<>();

            Cell serialNo = sheet2rd.getRow(lastRowNum2rd).getCell(0);//最后一行序号
            Cell containerNo1 = sheet2rd.getRow(lastRowNum2rd).getCell(1);//最后一行箱号
            /*最后一行为空白行的话，最后一行下标减一*/
            if (serialNo == null && containerNo1 == null && lastRowNum2rd > 3) {//第一行数据下标为3，4为有两条数据
                lastRowNum2rd = lastRowNum2rd - 1;
            }
            //循环校验标识
            Boolean cirFlag = true;
            PayCodeMes chargeByDel = new PayCodeMes();
            chargeByDel.setWaybillNo(waybillNo);
            payCodeMesService.updatePayCodeMesBatchDelete(chargeByDel);
            for (int i = 3; i <= lastRowNum2rd; i++) {
                PayCodeMes charge = new PayCodeMes();
                PayCodeMes chargeDel = new PayCodeMes();
                Row row = sheet2rd.getRow(i);
                boolean blankFlag = isRowEmpty(row);
                if (blankFlag == true) {
                    continue;
                }
                charge.setWaybillNo(waybillNo);
                chargeDel.setWaybillNo(waybillNo);
                List<Cell> cellListFee = new ArrayList();
//                cellListFee.add(row.getCell(0));
                cellListFee.add(row.getCell(1));
                cellListFee.add(row.getCell(2));
                cellListFee.add(row.getCell(3));
                R r = this.judgeNone(cellListFee, i + 1, "<付费代码>sheet页");
                if (r.getStatusCode() == 200) {
                } else {
                    return r;
                }

                //设置文本格式防止转换异常
                if (row.getCell(0) != null) {
                    row.getCell(0).setCellType(CellType.STRING);
                }

                if (row.getCell(0) != null && StrUtil.isNotEmpty(row.getCell(0).getStringCellValue())) {

                    String containerNo2 = row.getCell(0).getStringCellValue();
                    containerNo2 = containerNo2.trim();
                    if (cirFlag == true) {
//                    for (WaybillContainerInfo ci:cnList) {
//                        String cn=ci.getContainerNo();
//                        //如果excel中有大于等于一个的箱号和订舱时箱号匹配，那么校验通过cirFlag置为false,无须再次进入循环校验
//                        if(cn.equals(containerNo2)){
//                            cirFlag=false;
//                            break;
//                        }
//                        if(i==lastRowNum2rd){
//                            return new R(500,Boolean.FALSE,"请检查<付费代码>sheet页，没有发现与初次订舱时箱号的匹配");
//                        }
//                    }
                        if (stringBuilder.toString().contains(containerNo2)) {
                            cirFlag = false;
//                        break;
                        } else {
                            return new R(500, Boolean.FALSE, "请检查<付费代码>sheet页，箱号: " + containerNo2 + " 没有发现与初次订舱时箱号的匹配");
                        }
                    }
                    boolean flag3 = verifyCntrCode(containerNo2);
                    if (flag3) {
                    } else {
                        return new R(500, Boolean.FALSE, "请检查<付费代码>sheet页，第" + (i + 1) + "行箱号填写有误，如有疑问请联系管理员");
                    }
                    containerNo2 = containerNo2.trim();
                    charge.setContainerNo(containerNo2);//箱号
                    chargeDel.setContainerNo(containerNo2);
                    charge.setCarrierName(row.getCell(1).getStringCellValue());//承运人简称
                    charge.setPayerName(row.getCell(2).getStringCellValue());//支付人名称
                    charge.setPayerCode(row.getCell(3).getStringCellValue());//支付人代码
                    if (row.getCell(4) != null) {
                        row.getCell(4).setCellType(CellType.STRING);
                    }
                    if (row.getCell(5) != null) {
                        row.getCell(5).setCellType(CellType.STRING);
                    }
                    if (row.getCell(4) != null && !"".equals(row.getCell(4).getStringCellValue())) {
                        charge.setContractNo(row.getCell(4).getStringCellValue());//支付合同号
                    }
                    try {
                        if (row.getCell(5) != null && !"".equals(row.getCell(5).getStringCellValue())) {
                            row.getCell(5).setCellType(CellType.STRING);
                            Date paymentDate = sdf.parse(row.getCell(5).getStringCellValue());
                            charge.setPaymentDate(paymentDate);//支付日期
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        return new R(500, Boolean.FALSE, "导入<付费代码>sheet页失败，第" + (i + 1) + "行支付日期填写有误");
                    }
                    chargeDel.setDeleteFlag("Y");
                    listConChargeDel.add(chargeDel);
                    listConCharge.add(charge);

                } else {
                    List<PayCodeMes> listConChargeTwo = new ArrayList<>();
                    List<PayCodeMes> listConChargeDelTwo = new ArrayList<>();
                    for (String strList : stringList) {
                        PayCodeMes chargeTwo = new PayCodeMes();
                        PayCodeMes chargeDelTwo = new PayCodeMes();
                        chargeTwo.setWaybillNo(waybillNo);
                        chargeDelTwo.setWaybillNo(waybillNo);
                        if (strList != null) {
                            chargeTwo.setContainerNo(strList);//箱号
                            chargeDelTwo.setContainerNo(strList);
                        } else {
                            return new R(500, Boolean.FALSE, "请检查<运单舱单信息(必填)>sheet页，箱号为空，如有疑问请联系管理员");
                        }
                        chargeTwo.setCarrierName(row.getCell(1).getStringCellValue());//承运人简称
                        chargeTwo.setPayerName(row.getCell(2).getStringCellValue());//支付人名称
                        chargeTwo.setPayerCode(row.getCell(3).getStringCellValue());//支付人代码
                        if (row.getCell(4) != null) {
                            row.getCell(4).setCellType(CellType.STRING);
                        }
                        if (row.getCell(5) != null) {
                            row.getCell(5).setCellType(CellType.STRING);
                        }
                        if (row.getCell(4) != null && !"".equals(row.getCell(4).getStringCellValue())) {
                            chargeTwo.setContractNo(row.getCell(4).getStringCellValue());//支付合同号
                        }
                        try {
                            if (row.getCell(5) != null && !"".equals(row.getCell(5).getStringCellValue())) {
                                row.getCell(5).setCellType(CellType.STRING);
                                Date paymentDate = sdf.parse(row.getCell(5).getStringCellValue());
                                chargeTwo.setPaymentDate(paymentDate);//支付日期
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            return new R(500, Boolean.FALSE, "导入<付费代码>sheet页失败，第" + (i + 1) + "行支付日期填写有误");
                        }
//                        chargeDelTwo.setDeleteFlag("Y");
                        listConChargeDelTwo.add(chargeDelTwo);
                        listConChargeTwo.add(chargeTwo);
                    }
                    if (listConChargeDelTwo != null && listConChargeDelTwo.size() != 0) {
                        payCodeMesService.updatePayCodeMesBatch(listConChargeDelTwo);
                    }
                    if (listConChargeTwo != null && listConChargeTwo.size() != 0) {
                        payCodeMesService.insertPayCodeMesBatch(listConChargeTwo);
                    }
                    continue;
                }
            }
//            if(listConChargeDel!=null&&listConChargeDel.size()!=0){payCodeMesService.updatePayCodeMesBatch(listConChargeDel);}
            if (listConCharge != null && listConCharge.size() != 0) {
                payCodeMesService.insertPayCodeMesBatch(listConCharge);
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(e.getMessage() + "##################导入<付费代码>sheet页失败########################");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new R(500, Boolean.FALSE, "导入<付费代码>sheet页失败，请检查日期、小数等特殊单元格格式，检查无误后，请联系管理员" + e.getMessage());
        }

        /*<货物信息>sheet*/
        try {
            //导入第三张sheet页，现为"货物信息"
            Sheet sheet3rd = workbook.getSheetAt(2);
            //获取行数
            int lastRowNum3rd = sheet3rd.getLastRowNum();
            List<WaybillGoodsInfo> goodsList = new ArrayList<>();
            List<WaybillGoodsInfo> goodsListDel = new ArrayList<>();
            Cell serialNo = sheet3rd.getRow(lastRowNum3rd).getCell(0);//最后一行序号
            Cell containerNo2 = sheet3rd.getRow(lastRowNum3rd).getCell(1);//最后一行箱号
            /*最后一行为空白行的话，最后一行下标减一*/
            if (serialNo == null && containerNo2 == null && lastRowNum3rd > 3) {//第一行数据下标为3，4为有两条数据
                lastRowNum3rd = lastRowNum3rd - 1;
            }
            //循环校验标识
            Boolean cirFlag = true;
            for (int i = 3; i <= lastRowNum3rd; i++) {
                WaybillGoodsInfo goods = new WaybillGoodsInfo();
                WaybillGoodsInfo goodsDel = new WaybillGoodsInfo();
                Row row = sheet3rd.getRow(i);
                boolean blankFlag = isRowEmpty(row);
                if (blankFlag == true) {
                    continue;
                }
                goods.setRowId(UUID.randomUUID().toString());
                goods.setWaybillNo(waybillNo);
                goodsDel.setWaybillNo(waybillNo);
                //设置文本格式防止转换异常
                if (row.getCell(0) != null) {
                    row.getCell(0).setCellType(CellType.STRING);
                } else {
                    return new R(500, Boolean.FALSE, "请检查<货物信息>sheet页，第" + (i + 1) + "行箱号未填写或者存在末尾空白行，如有疑问请联系管理员");
                }
                String containerNo3 = row.getCell(0).getStringCellValue();
                containerNo3 = containerNo3.trim();

                if (cirFlag == true) {
//                    for (WaybillContainerInfo ci:cnList) {
//                        String cn=ci.getContainerNo();
//                        //如果excel中有大于等于一个的箱号和订舱时箱号匹配，那么校验通过cirFlag置为false,无须再次进入循环校验
//                        if(cn.equals(containerNo3)){
//                            cirFlag=false;
//                            break;
//                        }
//                        if(i==lastRowNum3rd){
//                            return new R(500,Boolean.FALSE,"请检查<运单舱单信息>sheet页，没有发现与初次订舱时箱号的匹配");
//                        }
//                    }
                    if (stringBuilder.toString().contains(containerNo3)) {
                        cirFlag = false;
//                        break;
                    } else {
                        return new R(500, Boolean.FALSE, "请检查<运单舱单信息>sheet页，箱号: " + containerNo2 + " 没有发现与初次订舱时箱号的匹配");
                    }
                }
                boolean flag4 = verifyCntrCode(containerNo3);
                if (flag4) {
                } else {
                    return new R(500, Boolean.FALSE, "请检查<货物信息>sheet页，第" + (i + 1) + "行箱号填写有误，如有疑问请联系管理员");
                }
                goods.setContainerNo(containerNo3);//箱号
                goodsDel.setContainerNo(containerNo3);
                row.getCell(1).setCellType(CellType.STRING);
                if (row.getCell(2) != null) {
                    row.getCell(2).setCellType(CellType.STRING);
                }
                if (row.getCell(3) != null) {
                    row.getCell(3).setCellType(CellType.STRING);
                }
                if (row.getCell(5) != null) {
                    row.getCell(5).setCellType(CellType.STRING);
                }
                if (row.getCell(6) != null) {
                    row.getCell(6).setCellType(CellType.STRING);
                }
                if (row.getCell(10) != null) {
                    row.getCell(10).setCellType(CellType.STRING);
                }
                if (row.getCell(11) != null) {
                    row.getCell(11).setCellType(CellType.STRING);
                }
                goods.setGoodsCode(row.getCell(1).getStringCellValue());//HS CODE 货物编码  ??????
                if (row.getCell(2) != null && !"".equals(row.getCell(2).getStringCellValue())) {
                    goods.setGngCode(row.getCell(2).getStringCellValue());//GNG 通用货物编码
                }
                if (row.getCell(3) != null && !"".equals(row.getCell(3).getStringCellValue())) {
                    goods.setEtCode(row.getCell(3).getStringCellValue());//ET 统一运价货物统计编码
                }
                goods.setGoodsChineseName(row.getCell(4).getStringCellValue());//货物中文
                if (row.getCell(5) != null && !"".equals(row.getCell(5).getStringCellValue())) {
                    goods.setGoodsEnglishName(row.getCell(5).getStringCellValue());//货物英文
                }
                if (row.getCell(6) != null && !"".equals(row.getCell(6).getStringCellValue())) {
                    goods.setGoodsRussianName(row.getCell(6).getStringCellValue());//货物俄文
                }
                goods.setPackageType(row.getCell(7).getStringCellValue());//包装种类
                row.getCell(8).setCellType(CellType.STRING);
                goods.setGoodsNums(row.getCell(8).getStringCellValue());//件数
                try {
                    goods.setGoodsWeight(Float.parseFloat(row.getCell(9).getStringCellValue()));//货物重量
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (row.getCell(10) != null && !"".equals(row.getCell(10).getStringCellValue())) {
                    goods.setDangerousGoodsCode(row.getCell(10).getStringCellValue());//危险品编码
                }
                if (row.getCell(11) != null && !"".equals(row.getCell(11).getStringCellValue())) {
                    goods.setGoodsRemarks(row.getCell(11).getStringCellValue());//货物备注
                }
                goodsDel.setDeleteFlag("Y");
                goodsListDel.add(goodsDel);
                goodsList.add(goods);     //for save
            }

            if (goodsListDel != null && goodsListDel.size() != 0) {
                waybillGoodsInfoService.updateGoodsInfoBatch(goodsListDel);
            }
            if (goodsList != null && goodsList.size() != 0) {
                waybillGoodsInfoService.insertWaybillGoodsInfo(goodsList);
            }
            //waybillGoodsInfoMapper.deleteWaybillGoodsInfo();
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(e.getMessage() + "##################导入<货物信息>sheet页失败########################");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new R(500, Boolean.FALSE, "导入<货物信息>sheet页失败，请检查日期、小数等特殊单元格格式，检查无误后，请联系管理员" + e.getMessage());
        }
        return new R<>(200, Boolean.TRUE, "导入完成");
    }


    /**
     * 订单补充资料--系统模板（去程）
     */
    @PostMapping("/importedTwo")
    @Transactional(rollbackFor = Exception.class)
    public R importedTwo(@RequestParam("file") MultipartFile file, @RequestParam("waybillNo") String waybillNo) {
        WaybillHeader wh = new WaybillHeader();
        wh.setWaybillNo(waybillNo);
        wh.setDeleteFlag("N");
        List<WaybillHeader> list = waybillHeaderService.selectWaybillHeaderList(wh);
        if (CollUtil.isEmpty(list)) {
            return new R(500, Boolean.FALSE, "没有查询到该订单，不可导入表格数据");
        }
        List<String> waybillCodes = new ArrayList<>();
        waybillCodes.add(list.get(0).getWaybillNo());
        waybillContainerInfoService.getWayBillCodes(list.get(0), waybillCodes);

        String countryListData = remoteAdminService.selectDictByType2("country_type");
        List<SysDictVo> countryList = JSONUtil.toList(JSONUtil.parseArray(countryListData), SysDictVo.class);

        WaybillContainerInfo wci = new WaybillContainerInfo();
        wci.setWaybillNo(waybillNo);
        List<WaybillContainerInfo> cnList = waybillContainerInfoService.selectWaybillContainerInfoList(wci);
        List<String> stringList = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder();
        for (WaybillContainerInfo containerInfo : cnList) {
            stringBuilder.append(containerInfo.getContainerNo()).append(",");
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Workbook workbook = null;
        try {
            InputStream is = file.getInputStream();
            //利用WorkbookFactory获取Sheet，无需判断版本
            workbook = WorkbookFactory.create(is);
        } catch (IOException e) {
            e.printStackTrace();
        } catch (InvalidFormatException e) {
            e.printStackTrace();
        }
        /*运单舱单信息(必填)sheet*/
        try {
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            String usercode = userInfo.getUserName();
            String username = userInfo.getRealName();
            //导入第一张sheet页，现为"运单舱单信息(必填)"
            Sheet sheet = workbook.getSheetAt(0);
            //获取行数
            int lastRowNum = sheet.getLastRowNum();
            List<WaybillContainerInfo> listCon = new ArrayList();   /*集装箱信息组装集合*/
            /*用以更新的集合*/
            List<WaybillParticipants> listPants = new ArrayList<>();   /*货物参与方信息组装集合*/
            /*用以新增的集合*/
            List<WaybillParticipants> listPantsSav = new ArrayList<>();   /*货物参与方信息组装集合*/

            Cell serialNo = sheet.getRow(lastRowNum).getCell(0);//最后一行序号
            Cell containerNo1 = sheet.getRow(lastRowNum).getCell(1);//最后一行箱号
            /*最后一行为空白行的话，最后一行下标减1*/
            if (serialNo == null && containerNo1 == null && lastRowNum > 4) {//第一行数据下标为4，5为有两条数据
                lastRowNum = lastRowNum - 1;
            }
            ContainerTypeData sel = new ContainerTypeData();
            sel.setDeleteFlag("N");
            List<ContainerTypeData> containerTypeDataList = containerTypeDataService.selectContainerTypeDataList(sel);

            String platformCode = platformCheckService.getPlatformCode("DDHCBCZL");
            //循环校验标识
            Boolean cirFlag = true;
            for (int i = 4; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                boolean blankFlag = isRowEmpty(row);
                if (blankFlag == true) {
                    continue;
                }
                WaybillContainerInfo containerInfo = new WaybillContainerInfo();//集装箱主数据
                WaybillParticipants pants = new WaybillParticipants(); //发货人
                WaybillParticipants pantsRec = new WaybillParticipants(); //收货人

                containerInfo.setWaybillNo(waybillNo);
                //组装收发货人信息
                pants.setWaybillNo(waybillNo);
                pantsRec.setWaybillNo(waybillNo);

                //设置文本格式防止转换异常
                if (row.getCell(1) != null) {
                    row.getCell(1).setCellType(CellType.STRING);
                } else {
                    return new R(500, Boolean.FALSE, "请检查<运单舱单信息>sheet页，第" + (i + 1) + "行箱号未填写或者存在末尾空白行，如有疑问请联系管理员");
                }
                String str = "";
                String containerNo = row.getCell(1).getStringCellValue();
                containerNo = containerNo.trim();
                str = containerNo;
                stringList.add(str);
                if (cirFlag == true) {
                    //如果excel中有大于等于一个的箱号和订舱时箱号匹配，那么校验通过cirFlag置为false,无须再次进入循环校验
                    if (stringBuilder.toString().contains(containerNo)) {
                        cirFlag = false;
//                        break;
                    } else {
                        return new R(500, Boolean.FALSE, "请检查<运单舱单信息>sheet页，箱号: " + containerNo + " 没有发现与初次订舱时箱号的匹配");
                    }
                }
                boolean flag2 = verifyCntrCode(containerNo);
                if (flag2) {
                } else {
                    return new R(500, Boolean.FALSE, "请检查<运单舱单信息>sheet页，第" + (i + 1) + "行箱号填写有误，如有疑问请联系管理员");
                }

                //校验必填项 start****************
                if (row.getCell(10) != null) {
                    row.getCell(10).setCellType(CellType.STRING);
                }
                if (row.getCell(11) != null) {
                    row.getCell(11).setCellType(CellType.STRING);
                }
                if (row.getCell(32) != null) {
                    row.getCell(32).setCellType(CellType.STRING);
                }
                if (row.getCell(33) != null) {
                    row.getCell(33).setCellType(CellType.STRING);
                }

                //校验必填项 end ****************
                containerNo = containerNo.trim();
                if (containerNo.contains("\n")) {
                    containerNo.replace("\n", "");
                }
                containerInfo.setContainerNo(containerNo);//集装箱实体添加箱号
                pants.setContainerNo(containerNo);//发货人实体箱号
                pantsRec.setContainerNo(containerNo);//收货人实体箱号
                //组装集装箱信息

//                containerInfo.setContainerType(row.getCell(2).getStringCellValue());//箱型
                String containerTypeCode = row.getCell(2).getStringCellValue();
                if (StrUtil.isNotBlank(containerTypeCode)) {
                    if ("20".equals(containerTypeCode) || "40".equals(containerTypeCode) || "45".equals(containerTypeCode)) {
                        containerTypeCode = containerTypeCode + "GP";
                    }
                    Boolean flag = true;
                    for (ContainerTypeData data : containerTypeDataList
                    ) {
                        if (data.getContainerTypeCode().equals(containerTypeCode)) {
                            containerInfo.setContainerTypeCode(data.getContainerTypeCode());
                            containerInfo.setContainerTypeName(data.getContainerTypeName());
                            containerInfo.setContainerType(data.getContainerTypeSize());
                            flag = false;
                            break;
                        }
                    }

                    if (flag) {
                        return new R(500, Boolean.FALSE, "未查询到该箱型代码：" + containerTypeCode);
                    }

                    Boolean isHas = false;
                    String oldType = "";
                    if (CollUtil.isNotEmpty(cnList)) {
                        for (WaybillContainerInfo info : cnList
                        ) {
                            if (info.getContainerNo().equals(containerNo)) {
                                oldType = info.getContainerType();
                                if((info.getContainerType().startsWith("2") && containerInfo.getContainerType().startsWith("2")) || (info.getContainerType().startsWith("4") && containerInfo.getContainerType().startsWith("4"))){
                                    isHas = true;
                                    break;
                                }
                            }
                        }
                    }
                    if (!isHas) {
                        return new R(500, Boolean.FALSE, "该箱号与原箱型尺寸不符-->" + containerNo + ":" + oldType + "-->" + containerInfo.getContainerType());
                    }
                }

                if (row.getCell(3) != null) {
                    row.getCell(3).setCellType(CellType.STRING);
                    if (StrUtil.isNotBlank(row.getCell(3).getStringCellValue())) {
                        containerInfo.setContainerDeadWeight(Float.parseFloat(row.getCell(3).getStringCellValue()));//箱自重
                    }
                }
                if (row.getCell(4) != null) {
                    row.getCell(4).setCellType(CellType.STRING);
                    containerInfo.setStationCompilation(row.getCell(4).getStringCellValue().replace(" ", ""));//发站站编
                    String startName = stationMapper.selectStationMesByCode(row.getCell(4).getStringCellValue().trim());//发站站名
                    containerInfo.setStartStationName(startName);
                }

                if (row.getCell(5) != null) {
                    row.getCell(5).setCellType(CellType.STRING);
                    String code = row.getCell(5).getStringCellValue();
                    if (StrUtil.isNotEmpty(code)) {
                        if (code.contains("-")) {
                            code = code.split("-")[1];
                        }
                    }
                    containerInfo.setEndCompilation(code.replace(" ", ""));//到站站编
                    String endName = stationMapper.selectStationMesByCode(code.trim());//到站站名
                    containerInfo.setEndStationName(endName);
                }

                if (row.getCell(6) != null) {
                    row.getCell(6).setCellType(CellType.STRING);
                }
                if (row.getCell(8) != null) {
                    row.getCell(8).setCellType(CellType.STRING);
                }
                if (row.getCell(6) != null && !"".equals(row.getCell(6).getStringCellValue())) {
                    containerInfo.setArrivalRemarks(row.getCell(6).getStringCellValue());//到站备注
                }
                if (row.getCell(7) != null && StrUtil.isNotBlank(row.getCell(7).getStringCellValue())) {
                    containerInfo.setFrontierPortstation(row.getCell(7).getStringCellValue());//国境口岸站编
                }

                if (row.getCell(8) != null && !"".equals(row.getCell(8).getStringCellValue())) {
                    containerInfo.setTitle(row.getCell(8).getStringCellValue());//封号
                }

                if (row.getCell(12) != null) {
                    row.getCell(12).setCellType(CellType.STRING);
                }
                if (row.getCell(29) != null) {
                    row.getCell(29).setCellType(CellType.STRING);
                }

                if (row.getCell(30) != null) {
                    row.getCell(30).setCellType(CellType.STRING);
                }
                if (row.getCell(15) != null) {
                    row.getCell(15).setCellType(CellType.STRING);
                }
                if (row.getCell(16) != null) {
                    row.getCell(16).setCellType(CellType.STRING);
                }
                if (row.getCell(17) != null) {
                    row.getCell(17).setCellType(CellType.STRING);
                }

                if (row.getCell(19) != null) {
                    row.getCell(19).setCellType(CellType.STRING);
                }
                if (row.getCell(20) != null) {
                    row.getCell(20).setCellType(CellType.STRING);
                }
                if (row.getCell(21) != null) {
                    row.getCell(21).setCellType(CellType.STRING);
                }

                if (row.getCell(23) != null) {
                    row.getCell(23).setCellType(CellType.STRING);
                }
                if (row.getCell(24) != null) {
                    row.getCell(24).setCellType(CellType.STRING);
                }
                if (row.getCell(25) != null) {
                    row.getCell(25).setCellType(CellType.STRING);
                }

                if (row.getCell(18) != null) {
                    row.getCell(18).setCellType(CellType.STRING);
                }
                if (row.getCell(22) != null) {
                    row.getCell(22).setCellType(CellType.STRING);
                }
                if (row.getCell(26) != null) {
                    row.getCell(26).setCellType(CellType.STRING);
                }
                if (row.getCell(27) != null) {
                    row.getCell(27).setCellType(CellType.STRING);
                }
                if (row.getCell(14) != null) {
                    row.getCell(14).setCellType(CellType.STRING);
                }

                if (row.getCell(36) != null) {
                    row.getCell(36).setCellType(CellType.STRING);
                }
                if (row.getCell(37) != null) {
                    row.getCell(37).setCellType(CellType.STRING);
                }
                if (row.getCell(38) != null) {
                    row.getCell(38).setCellType(CellType.STRING);
                }

                if (row.getCell(40) != null) {
                    row.getCell(40).setCellType(CellType.STRING);
                }
                if (row.getCell(41) != null) {
                    row.getCell(41).setCellType(CellType.STRING);
                }
                if (row.getCell(42) != null) {
                    row.getCell(42).setCellType(CellType.STRING);
                }

                if (row.getCell(44) != null) {
                    row.getCell(44).setCellType(CellType.STRING);
                }
                if (row.getCell(45) != null) {
                    row.getCell(45).setCellType(CellType.STRING);
                }
                if (row.getCell(46) != null) {
                    row.getCell(46).setCellType(CellType.STRING);
                }

                if (row.getCell(48) != null) {
                    row.getCell(48).setCellType(CellType.STRING);
                }

                if (row.getCell(49) != null) {
                    row.getCell(49).setCellType(CellType.STRING);
                }
                if (row.getCell(50) != null) {
                    row.getCell(50).setCellType(CellType.STRING);
                }
                if (row.getCell(51) != null) {
                    row.getCell(51).setCellType(CellType.STRING);
                }

                if (row.getCell(40) != null) {
                    row.getCell(40).setCellType(CellType.STRING);
                }
                if (row.getCell(41) != null) {
                    row.getCell(41).setCellType(CellType.STRING);
                }
                if (row.getCell(42) != null) {
                    row.getCell(42).setCellType(CellType.STRING);
                }

                if (row.getCell(39) != null) {
                    row.getCell(39).setCellType(CellType.STRING);
                }
                if (row.getCell(43) != null) {
                    row.getCell(43).setCellType(CellType.STRING);
                }
                if (row.getCell(48) != null) {
                    row.getCell(48).setCellType(CellType.STRING);
                }
                if (row.getCell(52) != null) {
                    row.getCell(52).setCellType(CellType.STRING);
                }
                if (row.getCell(53) != null) {
                    row.getCell(53).setCellType(CellType.STRING);
                }
                if (row.getCell(54) != null) {
                    row.getCell(54).setCellType(CellType.STRING);
                }
                if (row.getCell(55) != null) {
                    row.getCell(55).setCellType(CellType.STRING);
                }
                if (row.getCell(56) != null) {
                    row.getCell(56).setCellType(CellType.STRING);
                }
                if (row.getCell(59) != null) {
                    row.getCell(59).setCellType(CellType.STRING);
                }
                if (row.getCell(60) != null) {
                    row.getCell(60).setCellType(CellType.STRING);
                }
                if (row.getCell(61) != null) {
                    row.getCell(61).setCellType(CellType.STRING);
                }

                //组装发货人
                pants.setParticipantsType("F");
                if (row.getCell(9) != null && StrUtil.isNotBlank(row.getCell(9).getStringCellValue())) {
                    pants.setConsignorName(row.getCell(9).getStringCellValue());//发货人名称
                }

                if (row.getCell(10) != null && !"".equals(row.getCell(10).getStringCellValue())) {
                    pants.setConsignorEnglishname(row.getCell(10).getStringCellValue());//发货人英文名称
                }
                if (row.getCell(11) != null && !"".equals(row.getCell(11).getStringCellValue())) {
                    pants.setConsignorRussianame(row.getCell(11).getStringCellValue());//发货人俄文名称
                }
                if (row.getCell(12) != null && !"".equals(row.getCell(12).getStringCellValue())) {
                    pants.setCitizenId(row.getCell(12).getStringCellValue());//公民税务系统识别码
                }
                if (row.getCell(27) != null && !"".equals(row.getCell(27).getStringCellValue())) {
                    pants.setPostalCode(row.getCell(27).getStringCellValue());//发货人邮政编码
                }
                if (row.getCell(28) != null) {
                    row.getCell(28).setCellType(CellType.STRING);
                }
                if (row.getCell(28) != null && StrUtil.isNotBlank(row.getCell(28).getStringCellValue())) {
                    pants.setPhone(row.getCell(28).getStringCellValue());//发货人电话
                }

                if (row.getCell(29) != null && !"".equals(row.getCell(29).getStringCellValue())) {
                    pants.setEmail(row.getCell(29).getStringCellValue());//发货人邮箱
                }

                if (row.getCell(30) != null && !"".equals(row.getCell(30).getStringCellValue())) {
                    pants.setFax(row.getCell(30).getStringCellValue());//发货人传真
                }
                if (row.getCell(13) != null && StrUtil.isNotBlank(row.getCell(13).getStringCellValue())) {
                    pants.setCountryCode(row.getCell(13).getStringCellValue());//所属国家代码
                }

                if (row.getCell(15) != null && !"".equals(row.getCell(15).getStringCellValue())) {
                    pants.setProvince(row.getCell(15).getStringCellValue());//发货人省
                }
                if (row.getCell(16) != null && !"".equals(row.getCell(16).getStringCellValue())) {
                    pants.setCity(row.getCell(16).getStringCellValue());//发货人市
                }
                if (row.getCell(17) != null && !"".equals(row.getCell(17).getStringCellValue())) {
                    pants.setDistrict(row.getCell(17).getStringCellValue());//发货人县/区
                }

                if (row.getCell(19) != null && !"".equals(row.getCell(19).getStringCellValue())) {
                    pants.setEnglishProvince(row.getCell(19).getStringCellValue());//发货人省
                }
                if (row.getCell(20) != null && !"".equals(row.getCell(20).getStringCellValue())) {
                    pants.setEnglishCity(row.getCell(20).getStringCellValue());//发货人市
                }
                if (row.getCell(21) != null && !"".equals(row.getCell(21).getStringCellValue())) {
                    pants.setEnglishDistrict(row.getCell(21).getStringCellValue());//发货人县/区
                }

                if (row.getCell(23) != null && !"".equals(row.getCell(23).getStringCellValue())) {
                    pants.setRussiaProvince(row.getCell(23).getStringCellValue());//发货人省
                }
                if (row.getCell(24) != null && !"".equals(row.getCell(24).getStringCellValue())) {
                    pants.setRussiaCity(row.getCell(24).getStringCellValue());//发货人市
                }
                if (row.getCell(25) != null && !"".equals(row.getCell(25).getStringCellValue())) {
                    pants.setRussiaDistrict(row.getCell(25).getStringCellValue());//发货人县/区
                }

                if (row.getCell(18) != null && !"".equals(row.getCell(18).getStringCellValue())) {
                    pants.setChineseAddress(row.getCell(18).getStringCellValue());//发货人中文详细地址
                }
                if (row.getCell(22) != null && !"".equals(row.getCell(22).getStringCellValue())) {
                    pants.setEnglishAddress(row.getCell(22).getStringCellValue());//发货人英文详细地址
                }
                if (row.getCell(26) != null && !"".equals(row.getCell(26).getStringCellValue())) {
                    pants.setRussiaAddress(row.getCell(26).getStringCellValue());//发货人俄文详细地址
                }
                if (row.getCell(14) != null && !"".equals(row.getCell(14).getStringCellValue())) {
                    pants.setShipperSignature(row.getCell(14).getStringCellValue());//发货人签字
                }

                //组装收货人
                pantsRec.setParticipantsType("S");
                if (row.getCell(31) != null && StrUtil.isNotBlank(row.getCell(31).getStringCellValue())) {
                    pantsRec.setConsignorName(row.getCell(31).getStringCellValue());//收货人名称
                }

                if (row.getCell(32) != null && !"".equals(row.getCell(32).getStringCellValue())) {
                    pantsRec.setConsignorEnglishname(row.getCell(32).getStringCellValue());//收货人英文名称
                }
                if (row.getCell(33) != null && !"".equals(row.getCell(33).getStringCellValue())) {
                    pantsRec.setConsignorRussianame(row.getCell(33).getStringCellValue());//收货人俄文名称
                }
                if (row.getCell(34) != null && !"".equals(row.getCell(34).getStringCellValue())) {
                    pantsRec.setCitizenId(row.getCell(34).getStringCellValue());//公民税务系统识别码
                }
                if (row.getCell(48) != null && !"".equals(row.getCell(48).getStringCellValue())) {
                    pantsRec.setPostalCode(row.getCell(48).getStringCellValue());//收货人邮政编码
                }
                if (row.getCell(49) != null && !"".equals(row.getCell(49).getStringCellValue())) {
                    pantsRec.setPhone(row.getCell(49).getStringCellValue());//收货人电话
                }
                if (row.getCell(50) != null && !"".equals(row.getCell(50).getStringCellValue())) {
                    pantsRec.setEmail(row.getCell(50).getStringCellValue());//收货人邮箱
                }
                if (row.getCell(51) != null && !"".equals(row.getCell(51).getStringCellValue())) {
                    pantsRec.setFax(row.getCell(51).getStringCellValue());//收货人传真
                }
                if (row.getCell(35) != null && StrUtil.isNotBlank(row.getCell(35).getStringCellValue())) {
                    pantsRec.setCountryCode(row.getCell(35).getStringCellValue());//所属国家代码
                    //把国家和国家代码存到箱信息的目的国和目的国代码存到箱信息里 countryList
                    containerInfo.setDestinationCountryCode(pantsRec.getCountryCode());
                    String destinationCountryName = countryList.stream().filter(item ->
                            item.getCode().equals(containerInfo.getDestinationCountryCode())).findFirst().get().getName();
                    if (destinationCountryName != null) {
                        containerInfo.setDestinationCountryName(destinationCountryName);
                    }
                }

                if (row.getCell(36) != null && !"".equals(row.getCell(36).getStringCellValue())) {
                    pantsRec.setProvince(row.getCell(36).getStringCellValue());//收货人省
                }
                if (row.getCell(37) != null && !"".equals(row.getCell(37).getStringCellValue())) {
                    pantsRec.setCity(row.getCell(37).getStringCellValue());//收货人市
                }
                if (row.getCell(38) != null && !"".equals(row.getCell(38).getStringCellValue())) {
                    pantsRec.setDistrict(row.getCell(38).getStringCellValue());//收货人县/区
                }

                if (row.getCell(40) != null && !"".equals(row.getCell(40).getStringCellValue())) {
                    pantsRec.setEnglishProvince(row.getCell(40).getStringCellValue());//收货人省
                }
                if (row.getCell(41) != null && !"".equals(row.getCell(41).getStringCellValue())) {
                    pantsRec.setEnglishCity(row.getCell(41).getStringCellValue());//收货人市
                }
                if (row.getCell(42) != null && !"".equals(row.getCell(42).getStringCellValue())) {
                    pantsRec.setEnglishDistrict(row.getCell(42).getStringCellValue());//收货人县/区
                }

                if (row.getCell(44) != null && !"".equals(row.getCell(44).getStringCellValue())) {
                    pantsRec.setRussiaProvince(row.getCell(44).getStringCellValue());//收货人省
                }
                if (row.getCell(45) != null && !"".equals(row.getCell(45).getStringCellValue())) {
                    pantsRec.setRussiaCity(row.getCell(45).getStringCellValue());//收货人市
                }
                if (row.getCell(46) != null && !"".equals(row.getCell(46).getStringCellValue())) {
                    pantsRec.setRussiaDistrict(row.getCell(46).getStringCellValue());//收货人县/区
                }

                if (row.getCell(39) != null && !"".equals(row.getCell(39).getStringCellValue())) {
                    pantsRec.setChineseAddress(row.getCell(39).getStringCellValue());//收货人中文详细地址
                }
                if (row.getCell(43) != null && !"".equals(row.getCell(43).getStringCellValue())) {
                    pantsRec.setEnglishAddress(row.getCell(43).getStringCellValue());//收货人英文详细地址
                }
                if (row.getCell(47) != null && !"".equals(row.getCell(47).getStringCellValue())) {
                    pantsRec.setRussiaAddress(row.getCell(47).getStringCellValue());//收货人俄文详细地址
                }
                if (row.getCell(52) != null && !"".equals(row.getCell(52).getStringCellValue())) {
                    containerInfo.setResveredField02(row.getCell(52).getStringCellValue());//发货人声明
                }
                if (row.getCell(53) != null && !"".equals(row.getCell(53).getStringCellValue())) {
                    containerInfo.setResveredField03(row.getCell(53).getStringCellValue());//何方装车（О--发货人 П--承运人）
                }
                if (row.getCell(54) != null && !"".equals(row.getCell(54).getStringCellValue())) {
                    containerInfo.setResveredField04(row.getCell(54).getStringCellValue());//确定重量的方法
                }
                if (row.getCell(55) != null && !"".equals(row.getCell(55).getStringCellValue())) {
                    containerInfo.setResveredField05(row.getCell(55).getStringCellValue());//添加附件
                }
                if (row.getCell(56) != null && !"".equals(row.getCell(56).getStringCellValue())) {
                    containerInfo.setTwentyFifth(row.getCell(56).getStringCellValue());// [39]列AN列
                }
                if (row.getCell(59) != null && !"".equals(row.getCell(59).getStringCellValue())) {
                    containerInfo.setSevenLine(row.getCell(59).getStringCellValue());// [42]列AQ列
                }
                if (row.getCell(60) != null && !"".equals(row.getCell(60).getStringCellValue())) {
                    containerInfo.setEightLine(row.getCell(60).getStringCellValue());// [43]八栏
                }
                if (row.getCell(61) != null && !"".equals(row.getCell(61).getStringCellValue())) {
                    containerInfo.setTwentyEighthLine(row.getCell(61).getStringCellValue());// [44]二十八栏
                }
                if (row.getCell(57) != null) {
                    row.getCell(57).setCellType(CellType.STRING);
                }
                if (row.getCell(58) != null) {
                    row.getCell(58).setCellType(CellType.STRING);
                }
                if (row.getCell(62) != null) {
                    row.getCell(62).setCellType(CellType.STRING);
                }
                if (row.getCell(63) != null) {
                    row.getCell(63).setCellType(CellType.STRING);
                }
                if (row.getCell(64) != null) {
                    row.getCell(64).setCellType(CellType.STRING);
                }
                if (row.getCell(66) != null) {
                    row.getCell(66).setCellType(CellType.STRING);
                }
                if (row.getCell(69) != null) {
                    row.getCell(69).setCellType(CellType.STRING);
                }
                if (row.getCell(70) != null) {
                    row.getCell(70).setCellType(CellType.STRING);
                }
                if (row.getCell(71) != null) {
                    row.getCell(71).setCellType(CellType.STRING);
                }
                if (row.getCell(72) != null) {
                    row.getCell(72).setCellType(CellType.STRING);
                }
                if (row.getCell(73) != null) {
                    row.getCell(73).setCellType(CellType.STRING);
                }
                if (row.getCell(74) != null) {
                    row.getCell(74).setCellType(CellType.STRING);
                }
                if (row.getCell(75) != null) {
                    row.getCell(75).setCellType(CellType.STRING);
                }
                if (row.getCell(76) != null) {
                    row.getCell(76).setCellType(CellType.STRING);
                }
                if (row.getCell(77) != null) {
                    row.getCell(77).setCellType(CellType.STRING);
                }

                //组装运单主数据
                /*******************************39栏非必填项，略过******************************/
                if (row.getCell(43) != null && !"".equals(row.getCell(43).getStringCellValue())) {
                    containerInfo.setResveredField06(row.getCell(43).getStringCellValue());//特殊承运人
                }
                if (row.getCell(44) != null && !"".equals(row.getCell(44).getStringCellValue())) {
                    containerInfo.setResveredField07(row.getCell(44).getStringCellValue());//箱号栏标记重量值
                }
                /*******************************42栏非必填项，暂定不需展示******************************/
                /*******************************43栏非必填项，暂定不需展示******************************/
                /*******************************44栏非必填项，暂定不需展示******************************/
                if (row.getCell(62) != null && !"".equals(row.getCell(62).getStringCellValue())) {
                    String portStationCode = row.getCell(62).getStringCellValue();
                    List<SysDict> list1 = sysDictMapper.selectSysDictListByCode(98, portStationCode, database);
                    if (list1 != null && list1.size() > 0) {
                        containerInfo.setPortAgent(list1.get(0).getDictValue());
                    }
                    containerInfo.setResveredField09(portStationCode);//口岸代理编码
                }
                if (row.getCell(63) != null && !"".equals(row.getCell(63).getStringCellValue())) {
                    containerInfo.setResveredField10(row.getCell(63).getStringCellValue());//是否海关过境
                }
                if (row.getCell(64) != null && StrUtil.isNotBlank(row.getCell(64).getStringCellValue())) {
                    containerInfo.setAbroadReachCity(row.getCell(64).getStringCellValue());//境外到达城市
                }

                try {
                    if (row.getCell(65) != null && !"".equals(row.getCell(65).getStringCellValue())) {
                        Date aboarTime = sdf.parse(row.getCell(65).getStringCellValue());
                        containerInfo.setPlanAbroadTime(aboarTime);//预计出/入境时间
                    }
                } catch (ParseException e) {
                    e.printStackTrace();
                    return new R(500, Boolean.FALSE, "导入<运单舱单信息(必填)>sheet页失败，第" + (i + 1) + "行预计出/入境时间填写有误");
                }
                if (row.getCell(70) != null && !"".equals(row.getCell(70).getStringCellValue())) {
                    containerInfo.setShippingFeePayWays(row.getCell(70).getStringCellValue());//运费支付方式
                }
                if (row.getCell(71) != null && !"".equals(row.getCell(71).getStringCellValue())) {
                    containerInfo.setGoodsCustomStatusCode(row.getCell(71).getStringCellValue());//货物海关状态代码
                }
                if (row.getCell(72) != null && !"".equals(row.getCell(72).getStringCellValue())) {
                    containerInfo.setManifestRemarks(row.getCell(72).getStringCellValue());//舱单备注
                }
                if (row.getCell(73) != null && !"".equals(row.getCell(73).getStringCellValue())) {
                    containerInfo.setFastTrain(row.getCell(73).getStringCellValue());//快通准备
                }
                if (row.getCell(74) != null && !"".equals(row.getCell(74).getStringCellValue())) {
                    containerInfo.setDeparturePlaceGq(row.getCell(74).getStringCellValue());//出境启运地关区代码
                }
                if (row.getCell(75) != null && !"".equals(row.getCell(75).getStringCellValue())) {
                    containerInfo.setDeparturePlaceKa(row.getCell(75).getStringCellValue());//出境启运地口岸代码
                }
                if (row.getCell(76) != null && !"".equals(row.getCell(76).getStringCellValue())) {
                    containerInfo.setBlockType(row.getCell(76).getStringCellValue());//班列类别
                }
                //组装货物信息
                try {
                    if (row.getCell(66) != null) {
                        row.getCell(66).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(66).getStringCellValue())) {
                            containerInfo.setGoodsValue(Float.parseFloat(row.getCell(66).getStringCellValue()));//托运货物价值
                        }
                    }
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                    return new R(500, Boolean.FALSE, "导入<运单舱单信息(必填)>sheet页失败，第" + (i + 1) + "行货物价值填写有误");
                }
                if (row.getCell(67) != null && StrUtil.isNotBlank(row.getCell(67).getStringCellValue())) {
                    containerInfo.setGoodsAmountTypeCode(row.getCell(67).getStringCellValue());//货物金额类型代码
                }
                if (row.getCell(68) != null && !"".equals(row.getCell(68).getStringCellValue())) {
                    containerInfo.setTranClauseCode(row.getCell(68).getStringCellValue());//运输条款代码
                }
                try {
                    if (row.getCell(69) != null && !"".equals(row.getCell(69).getStringCellValue())) {
                        row.getCell(69).setCellType(CellType.STRING);
                        containerInfo.setGoodsCube(Float.parseFloat(row.getCell(69).getStringCellValue()));//货物体积
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return new R(500, Boolean.FALSE, "导入<运单舱单信息(必填)>sheet页失败，第" + (i + 1) + "行货物体积填写有误");
                }
                try {
                    if (row.getCell(78) != null && !"".equals(row.getCell(78).getStringCellValue())) {
                        row.getCell(78).setCellType(CellType.STRING);
                        if ("是".equals(row.getCell(78).getStringCellValue().trim())) {
                            containerInfo.setIsFull("1");
                        } else if ("否".equals(row.getCell(78).getStringCellValue().trim())) {
                            containerInfo.setIsFull("0");
                        }
                    }
                    if (platformCode.contains(list.get(0).getPlatformCode())) {
                        if (StrUtil.isBlank(containerInfo.getIsFull())) {
                            return new R(500, Boolean.FALSE, "导入<运单舱单信息(必填)>sheet页失败，第" + (i + 1) + "行是否全程不能为空");
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return new R(500, Boolean.FALSE, "导入<运单舱单信息(必填)>sheet页失败，第" + (i + 1) + "行是否全程填写有误");
                }
                try {
                    if (row.getCell(79) != null && !"".equals(row.getCell(79).getStringCellValue())) {
                        row.getCell(79).setCellType(CellType.STRING);
                        if ("是".equals(row.getCell(79).getStringCellValue().trim())) {
                            containerInfo.setNonFerrous("1");
                        } else if ("否".equals(row.getCell(79).getStringCellValue().trim())) {
                            containerInfo.setNonFerrous("0");
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return new R(500, Boolean.FALSE, "导入<运单舱单信息(必填)>sheet页失败，第" + (i + 1) + "行有色金属填写有误");
                }
                listPants.add(pants);
                listPants.add(pantsRec);
                //添加集装箱信息
                listCon.add(containerInfo);
            }

            waybillContainerInfoService.insertOrUpdateContainerInfo(listCon, waybillCodes);

            if (CollUtil.isNotEmpty(listPants)) {
                waybillParticipantsService.insertOrUpdateWaybillParticipants(listPants, waybillCodes);
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(e.getMessage() + "##################导入<运单舱单信息(必填)>sheet页失败########################");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new R(500, Boolean.FALSE, "导入<运单舱单信息(必填)>sheet页失败，请检查日期、小数等特殊单元格格式，检查无误后，请联系管理员" + e.getMessage());
        }
        /*<付费代码>sheet*/
        try {
            //导入第二张sheet页，现为"付费代码"
            Sheet sheet2rd = workbook.getSheetAt(1);
            //获取行数
            int lastRowNum2rd = sheet2rd.getLastRowNum();
            List<PayCodeMes> listConCharge = new ArrayList<>();
//            List<PayCodeMes>listConChargeDel=new ArrayList<>();

            Cell serialNo = sheet2rd.getRow(lastRowNum2rd).getCell(0);//最后一行序号
            Cell containerNo1 = sheet2rd.getRow(lastRowNum2rd).getCell(1);//最后一行箱号
            /*最后一行为空白行的话，最后一行下标减一*/
            if (serialNo == null && containerNo1 == null && lastRowNum2rd > 3) {//第一行数据下标为3，4为有两条数据
                lastRowNum2rd = lastRowNum2rd - 1;
            }
            //循环校验标识
            Boolean cirFlag = true;
            for (int i = 3; i <= lastRowNum2rd; i++) {
                PayCodeMes charge = new PayCodeMes();
//                PayCodeMes chargeDel=new PayCodeMes();
                Row row = sheet2rd.getRow(i);
                boolean blankFlag = isRowEmpty(row);
                if (blankFlag == true) {
                    continue;
                }
                charge.setWaybillNo(waybillNo);

                Boolean flag = false;

                //设置文本格式防止转换异常
                if (row.getCell(0) != null) {
                    row.getCell(0).setCellType(CellType.STRING);
                }

                if (row.getCell(0) != null && StrUtil.isNotEmpty(row.getCell(0).getStringCellValue())) {

                    String containerNo2 = row.getCell(0).getStringCellValue();
                    containerNo2 = containerNo2.trim();
                    if (cirFlag == true) {
                        if (stringBuilder.toString().contains(containerNo2)) {
                            cirFlag = false;
//                        break;
                        } else {
                            return new R(500, Boolean.FALSE, "请检查<付费代码>sheet页，箱号: " + containerNo2 + " 没有发现与初次订舱时箱号的匹配");
                        }
                    }
                    boolean flag3 = verifyCntrCode(containerNo2);
                    if (flag3) {
                    } else {
                        return new R(500, Boolean.FALSE, "请检查<付费代码>sheet页，第" + (i + 1) + "行箱号填写有误，如有疑问请联系管理员");
                    }
                    containerNo2 = containerNo2.trim();
                    charge.setContainerNo(containerNo2);//箱号
                    if (row.getCell(1) != null && StrUtil.isNotBlank(row.getCell(1).getStringCellValue())) {
                        charge.setCarrierName(row.getCell(1).getStringCellValue());//承运人简称
                        flag = true;
                    }
                    if (row.getCell(2) != null && StrUtil.isNotBlank(row.getCell(2).getStringCellValue())) {
                        charge.setPayerName(row.getCell(2).getStringCellValue());//支付人名称
                        flag = true;
                    }
                    if (row.getCell(3) != null) {
                        row.getCell(3).setCellType(CellType.STRING);
                        if (row.getCell(3) != null && StrUtil.isNotBlank(row.getCell(3).getStringCellValue())) {
                            charge.setPayerCode(row.getCell(3).getStringCellValue());//支付人代码
                            flag = true;
                        }
                    }

                    if (row.getCell(4) != null) {
                        row.getCell(4).setCellType(CellType.STRING);
                    }
                    if (row.getCell(5) != null) {
                        row.getCell(5).setCellType(CellType.STRING);
                    }
                    if (row.getCell(4) != null && !"".equals(row.getCell(4).getStringCellValue())) {
                        charge.setContractNo(row.getCell(4).getStringCellValue());//支付合同号
                        flag = true;
                    }
                    try {
                        if (row.getCell(5) != null && !"".equals(row.getCell(5).getStringCellValue())) {
                            row.getCell(5).setCellType(CellType.STRING);
                            Date paymentDate = sdf.parse(row.getCell(5).getStringCellValue());
                            charge.setPaymentDate(paymentDate);//支付日期
                            flag = true;
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        return new R(500, Boolean.FALSE, "导入<付费代码>sheet页失败，第" + (i + 1) + "行支付日期填写有误");
                    }
                    if (flag) {
                        listConCharge.add(charge);
                    }

                } else {
                    List<PayCodeMes> listConChargeTwo = new ArrayList<>();
                    for (String strList : stringList) {
                        PayCodeMes chargeTwo = new PayCodeMes();
                        chargeTwo.setWaybillNo(waybillNo);
                        if (strList != null) {
                            chargeTwo.setContainerNo(strList);//箱号
                        } else {
                            return new R(500, Boolean.FALSE, "请检查<运单舱单信息(必填)>sheet页，箱号为空，如有疑问请联系管理员");
                        }
                        if (row.getCell(1) != null && StrUtil.isNotBlank(row.getCell(1).getStringCellValue())) {
                            chargeTwo.setCarrierName(row.getCell(1).getStringCellValue());//承运人简称
                            flag = true;
                        }
                        if (row.getCell(2) != null && StrUtil.isNotBlank(row.getCell(2).getStringCellValue())) {
                            chargeTwo.setPayerName(row.getCell(2).getStringCellValue());//支付人名称
                            flag = true;
                        }
                        if (row.getCell(3) != null && StrUtil.isNotBlank(row.getCell(3).getStringCellValue())) {
                            chargeTwo.setPayerCode(row.getCell(3).getStringCellValue());//支付人代码
                            flag = true;
                        }

                        if (row.getCell(4) != null) {
                            row.getCell(4).setCellType(CellType.STRING);
                        }
                        if (row.getCell(5) != null) {
                            row.getCell(5).setCellType(CellType.STRING);
                        }
                        if (row.getCell(4) != null && !"".equals(row.getCell(4).getStringCellValue())) {
                            chargeTwo.setContractNo(row.getCell(4).getStringCellValue());//支付合同号
                            flag = true;
                        }
                        try {
                            if (row.getCell(5) != null && !"".equals(row.getCell(5).getStringCellValue())) {
                                row.getCell(5).setCellType(CellType.STRING);
                                Date paymentDate = sdf.parse(row.getCell(5).getStringCellValue());
                                chargeTwo.setPaymentDate(paymentDate);//支付日期
                                flag = true;
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            return new R(500, Boolean.FALSE, "导入<付费代码>sheet页失败，第" + (i + 1) + "行支付日期填写有误");
                        }
                        if (flag) {
                            listConChargeTwo.add(chargeTwo);
                        }
                    }
                    if (listConChargeTwo != null && listConChargeTwo.size() != 0) {
                        payCodeMesService.insertOrUpdatePayCodeMesBatch(listConChargeTwo);
                    }
                }
            }
            if (listConCharge != null && listConCharge.size() != 0) {
                payCodeMesService.insertOrUpdatePayCodeMesBatch(listConCharge);
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(e.getMessage() + "##################导入<付费代码>sheet页失败########################");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new R(500, Boolean.FALSE, "导入<付费代码>sheet页失败，请检查日期、小数等特殊单元格格式，检查无误后，请联系管理员" + e.getMessage());
        }

        /*<货物信息>sheet*/
        try {
            //导入第三张sheet页，现为"货物信息"
            Sheet sheet3rd = workbook.getSheetAt(2);
            //获取行数
            int lastRowNum3rd = sheet3rd.getLastRowNum();
            List<WaybillGoodsInfo> goodsList = new ArrayList<>();
            Cell serialNo = sheet3rd.getRow(lastRowNum3rd).getCell(0);//最后一行序号
            Cell containerNo2 = sheet3rd.getRow(lastRowNum3rd).getCell(1);//最后一行箱号
            /*最后一行为空白行的话，最后一行下标减一*/
            if (serialNo == null && containerNo2 == null && lastRowNum3rd > 3) {//第一行数据下标为3，4为有两条数据
                lastRowNum3rd = lastRowNum3rd - 1;
            }
            //循环校验标识
            Boolean cirFlag = true;
            for (int i = 3; i <= lastRowNum3rd; i++) {
                WaybillGoodsInfo goods = new WaybillGoodsInfo();
                Row row = sheet3rd.getRow(i);
                boolean blankFlag = isRowEmpty(row);
                if (blankFlag == true) {
                    continue;
                }
                goods.setRowId(UUID.randomUUID().toString());
                goods.setWaybillNo(waybillNo);
                //设置文本格式防止转换异常
                if (row.getCell(0) != null) {
                    row.getCell(0).setCellType(CellType.STRING);
                } else {
                    return new R(500, Boolean.FALSE, "请检查<货物信息>sheet页，第" + (i + 1) + "行箱号未填写或者存在末尾空白行，如有疑问请联系管理员");
                }
                String containerNo3 = row.getCell(0).getStringCellValue();
                containerNo3 = containerNo3.trim();

                if (cirFlag == true) {
                    if (stringBuilder.toString().contains(containerNo3)) {
                        cirFlag = false;
//                        break;
                    } else {
                        return new R(500, Boolean.FALSE, "请检查<运单舱单信息>sheet页，箱号: " + containerNo2 + " 没有发现与初次订舱时箱号的匹配");
                    }
                }
                boolean flag4 = verifyCntrCode(containerNo3);
                if (flag4) {
                } else {
                    return new R(500, Boolean.FALSE, "请检查<货物信息>sheet页，第" + (i + 1) + "行箱号填写有误，如有疑问请联系管理员");
                }
                goods.setContainerNo(containerNo3);//箱号
                row.getCell(1).setCellType(CellType.STRING);
                if (row.getCell(2) != null) {
                    row.getCell(2).setCellType(CellType.STRING);
                }
                if (row.getCell(3) != null) {
                    row.getCell(3).setCellType(CellType.STRING);
                }
                if (row.getCell(5) != null) {
                    row.getCell(5).setCellType(CellType.STRING);
                }
                if (row.getCell(6) != null) {
                    row.getCell(6).setCellType(CellType.STRING);
                }
                if (row.getCell(10) != null) {
                    row.getCell(10).setCellType(CellType.STRING);
                }
                if (row.getCell(11) != null) {
                    row.getCell(11).setCellType(CellType.STRING);
                }
                if (row.getCell(1) != null && StrUtil.isNotBlank(row.getCell(1).getStringCellValue())) {
                    goods.setGoodsCode(row.getCell(1).getStringCellValue());//HS CODE 货物编码  ??????
                }

                if (row.getCell(2) != null && !"".equals(row.getCell(2).getStringCellValue())) {
                    goods.setGngCode(row.getCell(2).getStringCellValue());//GNG 通用货物编码
                }
                if (row.getCell(3) != null && !"".equals(row.getCell(3).getStringCellValue())) {
                    goods.setEtCode(row.getCell(3).getStringCellValue());//ET 统一运价货物统计编码
                }
                if (row.getCell(4) != null && StrUtil.isNotBlank(row.getCell(4).getStringCellValue())) {
                    goods.setGoodsChineseName(row.getCell(4).getStringCellValue());//货物中文
                }

                if (row.getCell(5) != null && !"".equals(row.getCell(5).getStringCellValue())) {
                    goods.setGoodsEnglishName(row.getCell(5).getStringCellValue());//货物英文
                }
                if (row.getCell(6) != null && !"".equals(row.getCell(6).getStringCellValue())) {
                    goods.setGoodsRussianName(row.getCell(6).getStringCellValue());//货物俄文
                }
                if (row.getCell(7) != null && StrUtil.isNotBlank(row.getCell(7).getStringCellValue())) {
                    goods.setPackageType(row.getCell(7).getStringCellValue());//包装种类
                }

                if (row.getCell(8) != null) {
                    row.getCell(8).setCellType(CellType.STRING);
                    if (StrUtil.isNotBlank(row.getCell(8).getStringCellValue())) {
                        goods.setGoodsNums(row.getCell(8).getStringCellValue());//件数
                    }
                }

                try {
                    if (row.getCell(9) != null) {
                        row.getCell(9).setCellType(CellType.STRING);
                        if (StrUtil.isNotBlank(row.getCell(9).getStringCellValue())) {
                            goods.setGoodsWeight(Float.parseFloat(row.getCell(9).getStringCellValue()));//货物重量
                        }
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (row.getCell(10) != null && !"".equals(row.getCell(10).getStringCellValue())) {
                    goods.setDangerousGoodsCode(row.getCell(10).getStringCellValue());//危险品编码
                }
                if (row.getCell(11) != null && !"".equals(row.getCell(11).getStringCellValue())) {
                    goods.setGoodsRemarks(row.getCell(11).getStringCellValue());//货物备注
                }
                if (StrUtil.isNotBlank(goods.getContainerNo())) {
                    goodsList.add(goods);
                }
            }

            if (goodsList != null && goodsList.size() != 0) {
                waybillGoodsInfoService.insertOrUpdateWaybillGoodsInfo(goodsList, waybillCodes);
            }

            //更新台账
            Shifmanagement shifmanagement = new Shifmanagement();
            shifmanagement.setShiftId(list.get(0).getShiftNo());
            List<Shifmanagement> shifmanagementList = shifmanagementService.selectShifmanagementList(shifmanagement);
            if (CollUtil.isNotEmpty(shifmanagementList)) {
                for (Shifmanagement shif : shifmanagementList
                ) {
                    if (StrUtil.isBlank(shif.getParentId())) {
                        FdShippingAccoundetail fdShippingAccoundetail = new FdShippingAccoundetail();
                        fdShippingAccoundetail.setShiftNo(shif.getShiftId());
                        fdShippingAccoundetail.setPlatformCode(shif.getPlatformCode());
                        fdShippingAccoundetailService.updateFdShippingAccoundetailByWaybill(fdShippingAccoundetail);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(e.getMessage() + "##################导入<货物信息>sheet页失败########################");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new R(500, Boolean.FALSE, "导入<货物信息>sheet页失败，请检查日期、小数等特殊单元格格式，检查无误后，请联系管理员" + e.getMessage());
        }
        return new R<>(200, Boolean.TRUE, "导入完成");
    }

    //校验柜号是否正确
    /*
     * 1、第一部分由4位英文字母组成。前三位代码 (Owner Code) 主要说明箱主、经营人，第四位代码说明集装箱的类型。列如CBHU
     *  开头的标准集装箱是表明箱主和经营人为中远集运。
     * 2、 第二部分由6位数字组成。是箱体注册码（Registration Code），用于一个集装箱箱体持有的唯一标识。
     * 3、 第三部分为校验码（Check Digit）由前4位字母和6位数字经过校验规则运算得到，用于识别在校验时是否发生错误。即第11位数字。
     * 根据校验规则箱号的每个字母和数字都有一个运算的对应值。箱号的前10位字母和数字的对应值从0到Z对应数值为10到38，11、22、33不能对11取模数，所以要除去
     * 地址：https://blog.csdn.net/weixin_38611617/article/details/115069232
     */
    public static boolean verifyCntrCode(String strCode) {
        boolean result = true;
        strCode = strCode.trim();
        try {
            if (strCode.length() != 11) {
                return false;
            }
            if (strCode.startsWith("JSQ5") || strCode.startsWith("JSQ6")) {
                String str = strCode.substring(4);
                char[] codeChars = str.toCharArray();
                String charCode = "0123456789";
                for (int i = 0; i < 7; i++) {
                    int idx = charCode.indexOf(codeChars[i]);
                    if (idx == -1 || charCode.charAt(idx) == '?') {
                        return false;
                    }
                }
                return true;
            } else {
                char[] codeChars = strCode.toCharArray();
                String charCode = "0123456789A?BCDEFGHIJK?LMNOPQRSTU?VWXYZ";
                int num = 0;
                for (int i = 0; i < 10; i++) {
                    int idx = charCode.indexOf(codeChars[i]);
                    if (idx == -1 || charCode.charAt(idx) == '?') {
                        return false;
                    }
                    idx = (int) (idx * Math.pow(2, i));
                    num += idx;
                }
                num = (num % 11) % 10;
                result = Integer.parseInt(String.valueOf(codeChars[10])) == num;
            }

        } catch (Exception e) {
            result = false;
        }
        return result;
    }

    /*
     * 判断Cell集合是否存在空值
     */
    private R judgeNone(List<Cell> list, int i, String model) {
        if (list != null && list.size() != 0) {
            for (Cell cell : list) {
                if (cell != null) {
                    /*有数字、日期等特殊格式的先转为文本格式（否则非空的特殊格式字段会发生转换异常），进行下一步非空校验*/
                    cell.setCellType(CellType.STRING);
                }
                //判断单元格非空
                if (cell != null && !"".equals(cell.getStringCellValue())) {
                } else {
                    return new R(500, Boolean.FALSE, "请检查" + model + "第" + i + "行是否存在未填项，或末尾空白行");
                }
            }
            return new R(200, Boolean.TRUE, "校验正确");
        } else {
            return new R(500, Boolean.FALSE, "请检查" + model + "第" + i + "行是否存在未填项，或末尾空白行");
        }
    }

    /**
     * 新订单审核
     */
    @PostMapping("/waybillExamine")
    @Transactional(rollbackFor = Exception.class)
    public R waybillExamine(@RequestBody WaybillHeader waybillHeader) {
        return waybillHeaderService.waybillExamine(waybillHeader);
    }

    @PostMapping("/examine")
    @Transactional(rollbackFor = Exception.class)
    public R examine(@RequestBody WaybillHeader waybillHeader) {

        SecruityUser userInfo = SecurityUtils.getUserInfo();
        EntityWrapper<WaybillHeader> wrapper = new EntityWrapper<>();
        if (!StrUtil.hasBlank(waybillHeader.getWaybillNo())) {
            wrapper.eq("waybill_no", waybillHeader.getWaybillNo());
        } else {
            return new R<>(500, false, "请上传运单号");
        }
        List<WaybillHeader> list = this.waybillHeaderService.selectList(wrapper);
        if (list.isEmpty()) {
            return new R<>(500, false, "未查到该条运单信息，请确认运单号是否正确");
        }

        WaybillHeader waybillHeader1 = new WaybillHeader();
        waybillHeader1.setRowId(list.get(0).getRowId());
        waybillHeader1.setUpdateTime(new Date());
        waybillHeader1.setUpdateWho(userInfo.getUserName());
        waybillHeader1.setUpdateWhoName(userInfo.getRealName());
        if ("0".equals(waybillHeader.getAuditStatus())) {
            waybillHeader1.setAuditStatus("0");
            waybillHeader1.setBillStatus("0");
        } else {
            waybillHeader1.setAuditStatus("1");
            waybillHeader1.setBillStatus("2");

            fdCostService.savecostandcodstdetails(waybillHeader.getRowId());

            //审核通过后，插入费用主子表信息
//            String account = sysNoConfigService.genNo("ZD");
//            WaybillContainerInfo waybillContainerInfo = new WaybillContainerInfo();
//            waybillContainerInfo.setWaybillNo(waybillHeader.getWaybillNo());
//            List<WaybillContainerInfo> waybillContainerInfos = waybillContainerInfoService.selectWaybillContainerInfoList(waybillContainerInfo);
//            if (waybillContainerInfos!=null && waybillContainerInfos.size()>0){
//
//                for (WaybillContainerInfo containerInfo : waybillContainerInfos) {
//                    WaybillContainerchargeDetail waybillContainerchargeDetail = new WaybillContainerchargeDetail();
//                    waybillContainerchargeDetail.setRowId(UUID.randomUUID().toString());
//                    waybillContainerchargeDetail.setOrderNo(waybillHeader.getOrderNo());
//                    waybillContainerchargeDetail.setWaybillNo(waybillHeader.getWaybillNo());
//                    waybillContainerchargeDetail.setBillCode(account);
//                    waybillContainerchargeDetail.setPlatformCategory("01");
//                    waybillContainerchargeDetail.setContainerNo(containerInfo.getContainerNo());
//                    waybillContainerchargeDetail.setBox(containerInfo.getContainerOwner());
//                    waybillContainerchargeDetail.setContainerType(containerInfo.getContainerType());
//                    waybillContainerchargeDetail.setDomesticUnitprice(containerInfo.getDomesticUnitprice());
//                    waybillContainerchargeDetail.setDomesticFreight(containerInfo.getDomesticFreight());
//                    waybillContainerchargeDetail.setOverseasUnitprice(containerInfo.getOverseasUnitprice());
//                    waybillContainerchargeDetail.setOverseasFreight(containerInfo.getOverseasFreight());
//                    waybillContainerchargeDetail.setExchangeRate(waybillHeader.getExchangeRate());
//                    waybillContainerchargeDetail.setMonetaryValue(waybillHeader.getMonetaryValue());
//                    waybillContainerchargeDetail.setCarrierName(waybillHeader.getCarrierName());
//                    waybillContainerchargeDetail.setPayerName(waybillHeader.getPayerName());
//                    waybillContainerchargeDetail.setPayerCode(waybillHeader.getPayerCode());
//                    waybillContainerchargeDetail.setContractNo(waybillHeader.getPaymentContractNo());
//                    waybillContainerchargeDetail.setPaymentDate(waybillHeader.getPaymentDate());
//                    waybillContainerchargeDetail.setExpenseType("YF");
//                    waybillContainerchargeDetail.setAddWho(userInfo.getUserName());
//                    waybillContainerchargeDetail.setAddTime(new Date());
//                    waybillContainerchargeDetail.setAddWhoName(userInfo.getRealName());
//                    waybillContainerchargeDetail.setBillFee(containerInfo.getDomesticFreight().add(containerInfo.getOverseasFreight()).setScale(2, RoundingMode.HALF_UP));
//                    waybillContainerchargeDetailService.insertWaybillContainerchargeDetail(waybillContainerchargeDetail);
//                }
//            }
//            WaybillContainerchargeHeader waybillContainerchargeHeader = new WaybillContainerchargeHeader();
//            waybillContainerchargeHeader.setRowId(UUID.randomUUID().toString());
//            waybillContainerchargeHeader.setBillCode(account);
//            waybillContainerchargeHeader.setPlatformCode(waybillHeader.getPlatformCode());
//            waybillContainerchargeHeader.setOrderNo(waybillHeader.getOrderNo());
//            waybillContainerchargeHeader.setWaybillNo(waybillHeader.getWaybillNo());
//            waybillContainerchargeHeader.setExpenseType("YF");
//            waybillContainerchargeHeader.setAddTime(new Date());
//            waybillContainerchargeHeader.setAddWho(userInfo.getUserName());
//            waybillContainerchargeHeader.setAddWhoName(userInfo.getRealName());
//            waybillContainerchargeHeaderService.insertWaybillContainerchargeHeader(waybillContainerchargeHeader);
        }
        waybillHeaderService.updateWaybillHeader(waybillHeader1);
        //记录审核意见表
        Audiopinion audiopinion = new Audiopinion();
        audiopinion.setRowId(UUID.randomUUID().toString());
        audiopinion.setStatus(waybillHeader.getAuditStatus());
        audiopinion.setOrderNo(waybillHeader.getOrderNo());
        audiopinion.setWaybillNo(waybillHeader.getWaybillNo());
        audiopinion.setAuditNo(userInfo.getUserName());
        audiopinion.setAuditOpinion("YDSH");
        audiopinion.setAuditTime(LocalDateTime.now());
        audiopinion.setDeleteFlag("N");
        audiopinion.setAddTime(new Date());
        audiopinion.setAddWho(userInfo.getUserName());
        audiopinion.setAddWhoName(userInfo.getRealName());
        audiopinion.setResveredField01(waybillHeader.getPlatformCode());
        audiopinion.setResveredField02(waybillHeader.getOrderNo());
        if (!waybillHeader.getResveredField01().isEmpty()) {
            audiopinion.setResveredField03(waybillHeader.getResveredField01());
        }
        audiopinionService.insertAudiopinion(audiopinion);

        //插入操作日志
        OperationLog log = new OperationLog();
        log.setUuid(UUID.randomUUID().toString());
        log.setProcessCode(waybillHeader.getWaybillNo());
        log.setProcessType("市-运单-审核");
        log.setOperationCode(userInfo.getUserName());
        log.setOperationName(userInfo.getRealName());
        log.setOperationTime(new Date());
        if (waybillHeader.getAuditStatus() != null && !"".equals(waybillHeader.getAuditStatus())) {
            if ("1".equals(waybillHeader.getAuditStatus())) {
                log.setOperationResult("同意");
            } else if ("0".equals(waybillHeader.getAuditStatus())) {
                log.setOperationResult("驳回");
            }
        }
        log.setOperationOpinion(waybillHeader.getResveredField01());
        log.setCorInterface("/waybillheader/examine");
        log.setDeleteFlag("N");
        operationLogService.insertOperationLog(log);

        return new R<>(200, Boolean.TRUE, "审核成功");
    }

    @PostMapping("/confirm")
    @Transactional(rollbackFor = Exception.class)
    public R confirm(@RequestBody WaybillHeader waybillHeader) {
        UserInfo userInfo = TokenUtil.getUserInfo();
        WaybillContainerchargeDetail waybillContainerchargeDetail = new WaybillContainerchargeDetail();
        waybillContainerchargeDetail.setWaybillNo(waybillHeader.getWaybillNo());

        List<WaybillContainerchargeDetail> waybillContainerchargeDetails = waybillContainerchargeDetailService.selectWaybillContainerchargeDetailList(waybillContainerchargeDetail);
        if (!waybillContainerchargeDetails.isEmpty()) {
            WaybillContainerchargeHeader waybillContainerchargeHeader = new WaybillContainerchargeHeader();
            waybillContainerchargeHeader.setWaybillNo(waybillHeader.getWaybillNo());
            waybillContainerchargeHeader.setUpdateWho(userInfo.getUserCode());
            waybillContainerchargeHeader.setUpdateTime(new Date());
            BigDecimal decimal = BigDecimal.valueOf(0);
            for (WaybillContainerchargeDetail containerchargeDetail : waybillContainerchargeDetails) {
                decimal = containerchargeDetail.getDomesticFreight().add(containerchargeDetail.getOverseasFreight().add(decimal));
            }
            waybillContainerchargeHeader.setBillFee(decimal.setScale(2, BigDecimal.ROUND_HALF_UP));
            waybillContainerchargeHeaderService.updateFee(waybillContainerchargeHeader);
        }

        return new R<>(200, Boolean.TRUE, "交易成功");
    }


    //青分-发运统计表 第一张
    @GetMapping("/platformReport")
    public R platformReport(WaybillHeader waybillHeader) {
        waybillHeader.setPlatformCode(SecurityUtils.getUserInfo().getUserName());
        List<PlatFormDetailDTO> platFormDetailDTOS = waybillHeaderService.platformReportList(waybillHeader);
        return new R<>(200, Boolean.TRUE, platFormDetailDTOS);
    }

    //青分-发运统计表 第二张
    @GetMapping("/monthReport")
    public R monthReport(WaybillHeader waybillHeader) {
        waybillHeader.setPlatformCode(SecurityUtils.getUserInfo().getUserName());
        List<PortDTO> portDTOS = waybillHeaderService.monthReportList(waybillHeader);
        return new R<>(200, Boolean.TRUE, portDTOS);
    }

    //青分-发运统计表 报表导出
    @GetMapping("/shippingReport")
    public void shippingAccountingExport(WaybillHeader waybillHeader, HttpServletResponse res) throws Exception {
        //青分-发运统计表 sheet1
        List<PlatFormDetailDTO> platFormDetailDTOS = waybillHeaderService.platformReportList(waybillHeader);
        //青分-发运统计表 sheet2
        List<PortDTO> portDTOS = waybillHeaderService.monthReportList(waybillHeader);

        ExcelWriter excelWriter = null;
        String templateFileName = "shippingReport.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)) {
            templateFileName = linuxPath + templateFileName;
        } else {
            templateFileName = windowsPath + templateFileName;
        }
        String fileName = URLEncoder.encode("青分-发运统计表", "UTF-8").replaceAll("\\+", "%20");
        //ClassUtils.getDefaultClassLoader().getResource("").getPath()
        res.setHeader("Content-disposition", "attachment;filename*=" + fileName + ".xlsx");
        excelWriter = EasyExcel.write(res.getOutputStream()).withTemplate(templateFileName).build();

        WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "青分-发运统计表1").build();
        WriteSheet writeSheet2 = EasyExcel.writerSheet(1, "青分-发运统计表2").build();
        HashMap<String, Object> map = new HashMap<>();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(waybillHeader.getShippingTime());                    //放入Date类型数据
        int year = calendar.get(Calendar.YEAR);//获取年份
        int month = calendar.get(Calendar.MONTH);                    //获取月份

        if (waybillHeader.getShippingTime() != null) {
            map.put("shippingTime", year + "年" + month);
        }
        if (map != null) {
            excelWriter.fill(map, writeSheet1);//存入map
        }
        if (platFormDetailDTOS != null) {
            excelWriter.fill(platFormDetailDTOS, writeSheet1);
        }
        if (portDTOS != null) {
            excelWriter.fill(portDTOS, writeSheet2);
        }
        excelWriter.finish();
    }

    //济南分-发运班列场站发运一览表
    @GetMapping("/jinanReportList")
    public Page<JiNanDTO> jinanReportList(@RequestParam Map<String, Object> params) {
        return waybillHeaderService.jiNanList(new Query<>(params));
    }

    //4济南分-欧亚班列月度统计表、报表5和6 shippingLine传值：5-”中欧线“、6-”中亚线“ 4不传
    @GetMapping("/censusReportList")
    public R censusReportList(WaybillHeader waybillHeader) {
        List<JiNanMonthDTO> jiNanMonthDTOS = waybillHeaderService.selectCensusList(waybillHeader);
        return new R<>(200, Boolean.TRUE, jiNanMonthDTOS);
    }

    //7济南分-济南班列月度统计表
    @GetMapping("/trainsMonthly")
    public void trainsMonthly(WaybillHeader waybillHeader, HttpServletResponse res) throws Exception {

        SimpleDateFormat dd = new SimpleDateFormat("yyyy");
        List<TrainsMonthlyDTO> trainsMonthlyDTOS;
        //shippingTime为空查询当年 不为空筛选查询年-月
        Date shippingTime = waybillHeader.getShippingTime();

        if (shippingTime == null) {
            Calendar now = Calendar.getInstance();
            String s = now.get(Calendar.YEAR) + "";
            Date parse = dd.parse(s);
            waybillHeader.setShippingTime(parse);
            trainsMonthlyDTOS = waybillHeaderMapper.trainsMonthlyListYear(waybillHeader);
        } else {
            trainsMonthlyDTOS = waybillHeaderMapper.trainsMonthlyListMonth(waybillHeader);
        }

        if (!trainsMonthlyDTOS.isEmpty()) {
            for (TrainsMonthlyDTO trainsMonthlyDTO : trainsMonthlyDTOS) {
                //进出口类型（G去程、R回程）
                String trip = "";
                if (StrUtil.isNotEmpty(trainsMonthlyDTO.getTrip())) {
                    switch (trainsMonthlyDTO.getTrip()) {
                        case "G":
                            trip = "去程";
                            break;
                        case "R":
                            trip = "回程";
                            break;
                    }
                }
                trainsMonthlyDTO.setTrip(trip);
            }
        }
        ExcelWriter excelWriter = null;
        String templateFileName = "trainsMonthly.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)) {
            templateFileName = linuxPath + templateFileName;
        } else {
            templateFileName = windowsPath + templateFileName;
        }
        String fileName = URLEncoder.encode("济南分-济南班列月度统计表", "UTF-8").replaceAll("\\+", "%20");
        res.setHeader("Content-disposition", "attachment;filename*=" + fileName + ".xlsx");
        EasyExcel.write(res.getOutputStream(), TrainsMonthlyDTO.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("济南分-济南班列月度统计表")
                .doWrite(trainsMonthlyDTOS);
//        return new R<>(Boolean.TRUE);

    }

    //7济南分-济南班列月度统计表
    @GetMapping("/trainsMonthlyList")
    public R trainsMonthlyList(WaybillHeader waybillHeader, HttpServletResponse res) throws Exception {
        SimpleDateFormat dd = new SimpleDateFormat("yyyy");
        List<TrainsMonthlyDTO> trainsMonthlyDTOS;
        //shippingTime为空查询当年 不为空筛选查询年-月
        Date shippingTime = waybillHeader.getShippingTime();

        if (shippingTime == null) {
            Calendar now = Calendar.getInstance();
            String s = now.get(Calendar.YEAR) + "";
            Date parse = dd.parse(s);
            waybillHeader.setShippingTime(parse);
            trainsMonthlyDTOS = waybillHeaderMapper.trainsMonthlyListYear(waybillHeader);
        } else {
            trainsMonthlyDTOS = waybillHeaderMapper.trainsMonthlyListMonth(waybillHeader);
        }
        return new R<>(200, Boolean.TRUE, trainsMonthlyDTOS);

    }


    @GetMapping("/jinanReport")
    public void jinanReport(WaybillHeader waybillHeader, HttpServletResponse res) throws Exception {

        List<JiNanDTO> jiNanDTOS = waybillHeaderService.jiNanList2(waybillHeader);
        int i = 1;
        if (!jiNanDTOS.isEmpty()) {
            for (JiNanDTO jiNanDTO : jiNanDTOS) {
                jiNanDTO.setId(i);
                i++;
            }
        }
        ExcelWriter excelWriter = null;
        String templateFileName = "jinanReport.xlsx";
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)) {
            templateFileName = linuxPath + templateFileName;
        } else {
            templateFileName = windowsPath + templateFileName;
        }
        String fileName = URLEncoder.encode("济南分-发运班列场站发运一览表", "UTF-8").replaceAll("\\+", "%20");
        //ClassUtils.getDefaultClassLoader().getResource("").getPath()
        res.setHeader("Content-disposition", "attachment;filename*=" + fileName + ".xlsx");
        excelWriter = EasyExcel.write(res.getOutputStream()).withTemplate(templateFileName).build();

        WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "济南分-发运班列场站发运一览表").build();
        excelWriter.fill(jiNanDTOS, writeSheet1);
        excelWriter.finish();
    }

    //4济南分-欧亚班列月度统计表、报表5和6 shippingLine传值：5-”中欧线“、6-”中亚线“ 4不传
    @GetMapping("/censusReport")
    public void censusReport(WaybillHeader waybillHeader, HttpServletResponse res) throws Exception {
        List<JiNanMonthDTO> jiNanMonthDTOS = waybillHeaderService.selectCensusList(waybillHeader);
        ExcelWriter excelWriter = null;
        String templateFileName = null;
        String excelName = null;
        if ("中亚线".equals(waybillHeader.getShippingLine())) {
            templateFileName = "censusReportZY.xlsx";
            excelName = "济南分-中亚班列统计表";
        } else if ("中欧线".equals(waybillHeader.getShippingLine())) {
            templateFileName = "censusReportZO.xlsx";
            excelName = "济南分-中欧班列统计表";
        } else {
            templateFileName = "censusReport.xlsx";
            excelName = "济南分-欧亚班列月度统计表";
        }
        String osName = System.getProperties().getProperty("os.name");
        if ("Linux".equals(osName)) {
            templateFileName = linuxPath + templateFileName;
        } else {
            templateFileName = windowsPath + templateFileName;
        }
        String fileName = URLEncoder.encode(excelName, "UTF-8").replaceAll("\\+", "%20");
        res.setHeader("Content-disposition", "attachment;filename*=" + fileName + ".xlsx");
        excelWriter = EasyExcel.write(res.getOutputStream()).withTemplate(templateFileName).build();
        WriteSheet writeSheet1 = EasyExcel.writerSheet(0, excelName).build();
        excelWriter.fill(jiNanMonthDTOS, writeSheet1);
        excelWriter.finish();
    }

}
