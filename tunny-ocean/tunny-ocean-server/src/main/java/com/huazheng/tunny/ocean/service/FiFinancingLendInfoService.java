package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FiFinancingLendInfo;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 融资放款信息 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-03-08 09:42:20
 */
public interface FiFinancingLendInfoService extends IService<FiFinancingLendInfo> {
    /**
     * 查询融资放款信息信息
     *
     * @param rowId 融资放款信息ID
     * @return 融资放款信息信息
     */
    public FiFinancingLendInfo selectFiFinancingLendInfoById(String rowId);

    /**
     * 查询融资放款信息列表
     *
     * @param fiFinancingLendInfo 融资放款信息信息
     * @return 融资放款信息集合
     */
    public List<FiFinancingLendInfo> selectFiFinancingLendInfoList(FiFinancingLendInfo fiFinancingLendInfo);


    /**
     * 分页模糊查询融资放款信息列表
     * @return 融资放款信息集合
     */
    public Page selectFiFinancingLendInfoListByLike(Query query);



    /**
     * 新增融资放款信息
     *
     * @param fiFinancingLendInfo 融资放款信息信息
     * @return 结果
     */
    public int insertFiFinancingLendInfo(FiFinancingLendInfo fiFinancingLendInfo);

    /**
     * 修改融资放款信息
     *
     * @param fiFinancingLendInfo 融资放款信息信息
     * @return 结果
     */
    public int updateFiFinancingLendInfo(FiFinancingLendInfo fiFinancingLendInfo);

    /**
     * 删除融资放款信息
     *
     * @param rowId 融资放款信息ID
     * @return 结果
     */
    public int deleteFiFinancingLendInfoById(String rowId);

    /**
     * 批量删除融资放款信息
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiFinancingLendInfoByIds(Integer[] rowIds);

}

