package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.EfFinancingGoods;
import com.huazheng.tunny.ocean.api.entity.EfFinancingWarehouse;
import com.huazheng.tunny.ocean.api.entity.EfWarehouseGoods;
import com.huazheng.tunny.ocean.mapper.EfFinancingGoodsMapper;
import com.huazheng.tunny.ocean.mapper.EfFinancingWarehouseMapper;
import com.huazheng.tunny.ocean.mapper.EfWarehouseGoodsMapper;
import com.huazheng.tunny.ocean.service.EfFinancingWarehouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import java.util.UUID;

@Service("efFinancingWarehouseService")
public class EfFinancingWarehouseServiceImpl extends ServiceImpl<EfFinancingWarehouseMapper, EfFinancingWarehouse> implements EfFinancingWarehouseService {

    @Autowired
    private EfFinancingWarehouseMapper efFinancingWarehouseMapper;

    public EfFinancingWarehouseMapper getEfFinancingWarehouseMapper() {
        return efFinancingWarehouseMapper;
    }

    public void setEfFinancingWarehouseMapper(EfFinancingWarehouseMapper efFinancingWarehouseMapper) {
        this.efFinancingWarehouseMapper = efFinancingWarehouseMapper;
    }

    /**
     * 查询仓单融资仓单表信息
     *
     * @param rowId 仓单融资仓单表ID
     * @return 仓单融资仓单表信息
     */
    @Override
    public EfFinancingWarehouse selectEfFinancingWarehouseById(String rowId)
    {
        return efFinancingWarehouseMapper.selectEfFinancingWarehouseById(rowId);
    }

    /**
     * 查询仓单融资仓单表列表
     *
     * @param efFinancingWarehouse 仓单融资仓单表信息
     * @return 仓单融资仓单表集合
     */
    @Override
    public List<EfFinancingWarehouse> selectEfFinancingWarehouseList(EfFinancingWarehouse efFinancingWarehouse)
    {
        return efFinancingWarehouseMapper.selectEfFinancingWarehouseList(efFinancingWarehouse);
    }


    /**
     * 分页模糊查询仓单融资仓单表列表
     * @return 仓单融资仓单表集合
     */
    @Override
    public Page selectEfFinancingWarehouseListByLike(Query query)
    {
        EfFinancingWarehouse efFinancingWarehouse =  BeanUtil.mapToBean(query.getCondition(), EfFinancingWarehouse.class,false);
        query.setRecords(efFinancingWarehouseMapper.selectEfFinancingWarehouseListByLike(query,efFinancingWarehouse));
        return query;
    }


    /**
     * 修改仓单融资仓单表
     *
     * @param efFinancingWarehouse 仓单融资仓单表信息
     * @return 结果
     */
    @Override
    public int updateEfFinancingWarehouse(EfFinancingWarehouse efFinancingWarehouse)
    {
        return efFinancingWarehouseMapper.updateEfFinancingWarehouse(efFinancingWarehouse);
    }


    /**
     * 删除仓单融资仓单表
     *
     * @param rowId 仓单融资仓单表ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingWarehouseById(String rowId)
    {
        return efFinancingWarehouseMapper.deleteEfFinancingWarehouseById( rowId);
    };


    /**
     * 批量删除仓单融资仓单表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingWarehouseByIds(Integer[] rowIds)
    {
        return efFinancingWarehouseMapper.deleteEfFinancingWarehouseByIds( rowIds);
    }

}
