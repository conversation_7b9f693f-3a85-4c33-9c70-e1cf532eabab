package com.huazheng.tunny.ocean.controller;

import com.huazheng.tunny.ocean.api.entity.Announcement;
import com.huazheng.tunny.ocean.service.AnnouncementService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @Description: 公告信息表
 * @Author: wx
 * @Date: 2023-05-08 15:12:44
 */
@RestController
@RequestMapping("/announcement")
public class AnnouncementController {

    @Autowired
    private AnnouncementService service;

    /**
     * @Description: 分页
     * @Param: params
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-08 15:12:44
     */
    @GetMapping("/page")
    public R page(@RequestParam Map<String, Object> params) {
        return new R<>(service.page(new Query<>(params)));
    }

    /**
     * @Description: 详情
     * @Param: id
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-08 15:12:44
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Integer id) {
        return service.info(id);
    }

    /**
     * @Description: 保存
     * @Param: param
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-08 15:12:44
     */
    @PostMapping("/save")
    public R save(@RequestBody Announcement param) {
        return service.save(param);
    }

    /**
     * @Description: 修改
     * @Param: param
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-08 15:12:44
     */
    @PostMapping("/renew")
    public R update(@RequestBody Announcement param) {
        return service.update(param);
    }

    /**
     * @Description: 删除
     * @Param: id
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-08 15:12:44
     */
    @PostMapping("/del/{id}")
    public R delete(@PathVariable Integer id) {
        return service.delete(id);
    }

    /**
     * @Description: 首页查询所有启用公告列表
     * @Param: param
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-08 15:12:44
     */
    @GetMapping("/list")
    public R list(Announcement param) {
        return service.list(param);
    }

    /**
     * @Description: 启用禁用
     * @Param: param
     * @Return: R
     * @Author: wx
     * @Date: 2023-05-08 15:12:44
     */
    @PostMapping("/switch/type")
    public R switchType(@RequestBody Announcement param) {
        return service.switchType(param);
    }

}
