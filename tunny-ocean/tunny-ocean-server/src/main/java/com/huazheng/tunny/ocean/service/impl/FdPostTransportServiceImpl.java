package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.FdPostTransportDTO;
import com.huazheng.tunny.ocean.api.entity.*;
import com.huazheng.tunny.ocean.mapper.CustomerPlatformInfoMapper;
import com.huazheng.tunny.ocean.mapper.FdPostTransportDetailMapper;
import com.huazheng.tunny.ocean.mapper.FdPostTransportMapper;
import com.huazheng.tunny.ocean.mapper.ShifmanagementMapper;
import com.huazheng.tunny.ocean.service.FdPostTransportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("fdPostTransportService")
public class FdPostTransportServiceImpl extends ServiceImpl<FdPostTransportMapper, FdPostTransport> implements FdPostTransportService {

    @Autowired
    private FdPostTransportMapper fdPostTransportMapper;

    @Autowired
    private CustomerPlatformInfoMapper customerPlatformInfoMapper;

    @Autowired
    private ShifmanagementMapper shifmanagementMapper;

    @Autowired
    private FdPostTransportDetailMapper fdPostTransportDetailMapper;
    @Value("${path.windowsPath}")
    private String windowsPath;
    @Value("${path.linuxPath}")
    private String linuxPath;
    /**
     * 查询后程转运表信息
     *
     * @param id 后程转运表ID
     * @return 后程转运表信息
     */
    @Override
    public FdPostTransportDTO selectFdPostTransportById(Integer id)
    {
        FdPostTransport fdPostTransport = fdPostTransportMapper.selectFdPostTransportById(id);
        FdPostTransportDTO post = new FdPostTransportDTO();
        BeanUtil.copyProperties(fdPostTransport,post);
        if(fdPostTransport != null && StrUtil.isNotBlank(fdPostTransport.getShiftNo())){
            Shifmanagement sel = new Shifmanagement();
            sel.setShiftId(fdPostTransport.getShiftNo());
            sel.setPlatformCode(fdPostTransport.getPlatformCode());
            sel.setDeleteFlag("N");
            List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
            if(CollUtil.isNotEmpty(shifmanagements)){
                post.setShiftName(shifmanagements.get(0).getShiftName());
                post.setPlanShipTime(shifmanagements.get(0).getPlanShipTime());
                post.setShippingLine(shifmanagements.get(0).getShippingLine());
                post.setTrip(shifmanagements.get(0).getTrip());
                post.setDestinationName(shifmanagements.get(0).getDestinationName());
                post.setDestination(shifmanagements.get(0).getDestination());
                post.setOverseasAgency(shifmanagements.get(0).getOverseasAgency());
                post.setNumOfTruePositions(shifmanagements.get(0).getNumOfTruePositions());
                post.setPortStation(shifmanagements.get(0).getPortStation());
            }
        }

        if(fdPostTransport != null || StrUtil.isNotBlank(fdPostTransport.getWaybillNo())){
            FdPostTransportDetail sel2 = new FdPostTransportDetail();
            sel2.setWaybillNo(fdPostTransport.getWaybillNo());
            List<FdPostTransportDetail> fdPostTransportDetails = fdPostTransportDetailMapper.selectFdPostTransportDetailList2(sel2);
            if(CollUtil.isNotEmpty(fdPostTransportDetails)){
                String shipperInfo ="";
                if(StrUtil.isNotBlank(fdPostTransport.getCustomerName())){
                    shipperInfo = shipperInfo + "托运人名称："+fdPostTransport.getCustomerName()+"；托运人名称： ";
                }
                if(StrUtil.isNotBlank(fdPostTransport.getContactPerson()) || StrUtil.isNotBlank(fdPostTransport.getContactPhone())){
                    shipperInfo = shipperInfo + "联系方式：";
                    if(StrUtil.isNotBlank(fdPostTransport.getContactPerson())){
                        shipperInfo = shipperInfo + fdPostTransport.getContactPerson()+"  ";
                    }
                    if(StrUtil.isNotBlank(fdPostTransport.getContactPhone())){
                        shipperInfo = shipperInfo +fdPostTransport.getContactPhone()+"；";
                    }
                }

                for (FdPostTransportDetail fdPostTransportDetail:fdPostTransportDetails
                     ) {
                    if(StrUtil.isBlank(fdPostTransportDetail.getShipperInfo())){
                        fdPostTransportDetail.setShipperInfo(shipperInfo);
                    }
                }
                post.setCustomerList(fdPostTransportDetails);
            }
        }

        return post;
    }

    @Override
    public FdPostTransportDTO infoCity(FdPostTransport fdPostTransport)
    {
        FdPostTransportDTO post = new FdPostTransportDTO();
        post.setShiftNo(fdPostTransport.getShiftNo());
//        fdPostTransport.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        fdPostTransport.setDeleteFlag("N");
        List<FdPostTransport> fdPostTransports = fdPostTransportMapper.selectFdPostTransportList2(fdPostTransport);
        if(CollUtil.isNotEmpty(fdPostTransports)){
            post.setCityList(fdPostTransports);
            for (FdPostTransport postTransport:fdPostTransports
                 ) {
                if(StrUtil.isNotBlank(postTransport.getTitle())){
                    post.setTitle(postTransport.getTitle());
                    break;
                }
            }

            Shifmanagement sel = new Shifmanagement();
            sel.setShiftId(fdPostTransports.get(0).getShiftNo());
            sel.setPlatformCode(fdPostTransports.get(0).getPlatformCode());
            sel.setDeleteFlag("N");
            List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
            if(CollUtil.isNotEmpty(shifmanagements)){
                post.setShiftName(shifmanagements.get(0).getShiftName());
                post.setPlanShipTime(shifmanagements.get(0).getPlanShipTime());
                post.setShippingLine(shifmanagements.get(0).getShippingLine());
                post.setTrip(shifmanagements.get(0).getTrip());
                post.setDestinationName(shifmanagements.get(0).getDestinationName());
                post.setDestination(shifmanagements.get(0).getDestination());
                post.setOverseasAgency(shifmanagements.get(0).getOverseasAgency());
                post.setNumOfTruePositions(shifmanagements.get(0).getNumOfTruePositions());
                post.setPortStation(shifmanagements.get(0).getPortStation());
            }
            List<FdPostTransportDetail> customerList = new ArrayList<>();
            for (FdPostTransport postTransport:fdPostTransports
            ) {
                if(postTransport != null || StrUtil.isNotBlank(postTransport.getShiftNo())){
                    FdPostTransportDetail sel2 = new FdPostTransportDetail();
                    sel2.setShiftNo(postTransport.getShiftNo());
                    sel2.setWaybillNo(postTransport.getWaybillNo());
                    List<FdPostTransportDetail> fdPostTransportDetails = fdPostTransportDetailMapper.selectFdPostTransportDetailList2(sel2);
                    if(CollUtil.isNotEmpty(fdPostTransportDetails)){
                        String shipperInfo ="";
                        if(StrUtil.isNotBlank(postTransport.getCustomerName())){
                            shipperInfo = shipperInfo + "托运人名称："+postTransport.getCustomerName()+"；托运人名称： ";
                        }
                        if(StrUtil.isNotBlank(postTransport.getContactPerson()) || StrUtil.isNotBlank(postTransport.getContactPhone())){
                            shipperInfo = shipperInfo + "联系方式：";
                            if(StrUtil.isNotBlank(postTransport.getContactPerson())){
                                shipperInfo = shipperInfo + postTransport.getContactPerson()+"  ";
                            }
                            if(StrUtil.isNotBlank(postTransport.getContactPhone())){
                                shipperInfo = shipperInfo +postTransport.getContactPhone()+"；";
                            }
                        }

                        for (FdPostTransportDetail fdPostTransportDetail:fdPostTransportDetails
                        ) {
                            if(StrUtil.isBlank(fdPostTransportDetail.getShipperInfo())){
                                fdPostTransportDetail.setShipperInfo(shipperInfo);
                            }
                        }
                        customerList.addAll(fdPostTransportDetails);
                    }
                }
            }
            if(CollUtil.isNotEmpty(customerList)){
                post.setCustomerList(customerList);
            }
        }

        return post;
    }

    /**
     * 查询后程转运表列表
     *
     * @param fdPostTransport 后程转运表信息
     * @return 后程转运表集合
     */
    @Override
    public List<FdPostTransport> selectFdPostTransportList(FdPostTransport fdPostTransport)
    {
        return fdPostTransportMapper.selectFdPostTransportList(fdPostTransport);
    }


    /**
     * 分页模糊查询后程转运表列表
     * @return 后程转运表集合
     */
    @Override
    public Page selectFdPostTransportListByLike(Query query)
    {
        FdPostTransport fdPostTransport =  BeanUtil.mapToBean(query.getCondition(), FdPostTransport.class,false);
        query.setRecords(fdPostTransportMapper.selectFdPostTransportListByLike(query,fdPostTransport));
        return query;
    }

    @Override
    public Page pageCustomer(Query query)
    {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.plan_ship_time DESC,x.shift_no");
            query.setAsc(Boolean.FALSE);
        }
        FdPostTransport fdPostTransport =  BeanUtil.mapToBean(query.getCondition(), FdPostTransport.class,false);
        fdPostTransport.setCustomerCode(SecurityUtils.getUserInfo().getPlatformCode());
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        if("0".equals(userInfo.getPlatformLevel()) && "1".equals(userInfo.getDataFlag())){
            fdPostTransport.setMiniPlatform(userInfo.getMiniPlatform());
        }
        query.setRecords(fdPostTransportMapper.selectFdPostTransportListByLike2(query,fdPostTransport));
        return query;
    }

    @Override
    public Page pageCity(Query query)
    {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.plan_ship_time DESC,x.shift_no");
            query.setAsc(Boolean.FALSE);
        }
        FdPostTransport fdPostTransport =  BeanUtil.mapToBean(query.getCondition(), FdPostTransport.class,false);
        fdPostTransport.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        query.setRecords(fdPostTransportMapper.selectFdPostTransportListByLike3(query,fdPostTransport));
        return query;
    }

    /**
     * 新增后程转运表
     *
     * @param fdPostTransport 后程转运表信息
     * @return 结果
     */
    @Override
    public int insertFdPostTransport(FdPostTransport fdPostTransport)
    {
        return fdPostTransportMapper.insertFdPostTransport(fdPostTransport);
    }

    /**
     * 修改后程转运表
     *
     * @param fdPostTransport 后程转运表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateCustomer(FdPostTransportDTO fdPostTransport)
    {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        FdPostTransport post = new FdPostTransport();
        BeanUtil.copyProperties(fdPostTransport,post);
        if(CollUtil.isNotEmpty(fdPostTransport.getCustomerList())){
            List<FdPostTransportDetail> customerList = fdPostTransport.getCustomerList();
            for (FdPostTransportDetail customer:customerList
                 ) {
                if(StrUtil.isNotBlank(customer.getIsDedicated()) && "1".equals(customer.getIsDedicated()) && StrUtil.isBlank(customer.getDedicatedLineName())){
                    return new R<>(new Throwable("箱号："+customer.getContainerNo()+"缺少专用线名称！"));
                }
                if(customer.getId() != null){
                    customer.setUpdateWho(userInfo.getUserName());
                    customer.setUpdateWhoName(userInfo.getRealName());
                    customer.setUpdateTime(LocalDateTime.now());
                    fdPostTransportDetailMapper.updateFdPostTransportDetail(customer);
                }else{
                    customer.setAddWho(userInfo.getUserName());
                    customer.setAddWhoName(userInfo.getRealName());
                    customer.setAddTime(LocalDateTime.now());
                    fdPostTransportDetailMapper.insertFdPostTransportDetail(customer);
                }
            }
        }
        post.setAuditWho(userInfo.getUserName());
        post.setAuditWhoName(userInfo.getRealName());
        post.setAuditTime(LocalDateTime.now());
        fdPostTransportMapper.updateFdPostTransport(post);
        return new R<>(0, Boolean.TRUE, null, "操作成功！");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateCity(FdPostTransportDTO fdPostTransport)
    {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        FdPostTransport post = new FdPostTransport();
        BeanUtil.copyProperties(fdPostTransport,post);
        post.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        if(CollUtil.isNotEmpty(fdPostTransport.getCustomerList())){
            List<FdPostTransportDetail> customerList = fdPostTransport.getCustomerList();
            for (FdPostTransportDetail customer:customerList
            ) {
                if(StrUtil.isNotBlank(customer.getIsDedicated()) && "1".equals(customer.getIsDedicated()) && StrUtil.isBlank(customer.getDedicatedLineName())){
                    return new R<>(new Throwable("箱号："+customer.getContainerNo()+"缺少专用线名称！"));
                }
                if(customer.getId() != null){
                    customer.setUpdateWho(userInfo.getUserName());
                    customer.setUpdateWhoName(userInfo.getRealName());
                    customer.setUpdateTime(LocalDateTime.now());
                    fdPostTransportDetailMapper.updateFdPostTransportDetail(customer);
                }else{
                    customer.setAddWho(userInfo.getUserName());
                    customer.setAddWhoName(userInfo.getRealName());
                    customer.setAddTime(LocalDateTime.now());
                    fdPostTransportDetailMapper.insertFdPostTransportDetail(customer);
                }
            }
        }
        post.setAuditWhoCity(userInfo.getUserName());
        post.setAuditWhoNameCity(userInfo.getRealName());
        post.setAuditTimeCity(LocalDateTime.now());
        fdPostTransportMapper.updateFdPostTransport2(post);
        return new R<>(0, Boolean.TRUE, null, "操作成功！");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int auditCity(FdPostTransport fdPostTransport)
    {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        fdPostTransport.setAuditWho(userInfo.getUserName());
        fdPostTransport.setAuditWhoName(userInfo.getRealName());
        fdPostTransport.setAuditTime(LocalDateTime.now());
        return fdPostTransportMapper.updateFdPostTransport(fdPostTransport);
    }


    /**
     * 删除后程转运表
     *
     * @param id 后程转运表ID
     * @return 结果
     */
    public int deleteFdPostTransportById(Integer id)
    {
        return fdPostTransportMapper.deleteFdPostTransportById( id);
    };


    /**
     * 批量删除后程转运表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFdPostTransportByIds(Integer[] ids)
    {
        return fdPostTransportMapper.deleteFdPostTransportByIds( ids);
    }

    @Override
    public void insertPostTransport(WaybillHeader waybillHeader){
        Shifmanagement sel = new Shifmanagement();
        sel.setShiftId(waybillHeader.getShiftNo());
        sel.setPlatformCode(waybillHeader.getPlatformCode());
        sel.setDeleteFlag("N");
        List<Shifmanagement> shifmanagements = shifmanagementMapper.selectShifmanagementList(sel);
        if(CollUtil.isNotEmpty(shifmanagements) && StrUtil.isNotBlank(shifmanagements.get(0).getTrip()) && "R".equals(shifmanagements.get(0).getTrip())){
            SecruityUser userInfo = SecurityUtils.getUserInfo();
            FdPostTransport addObj = new FdPostTransport();
            addObj.setPlatformCode(waybillHeader.getPlatformCode());
            addObj.setPlatformName(waybillHeader.getPlatformName());
            addObj.setCustomerCode(waybillHeader.getCustomerNo());
            addObj.setCustomerName(waybillHeader.getCustomerName());
            addObj.setShiftNo(waybillHeader.getShiftNo());
            addObj.setWaybillNo(waybillHeader.getWaybillNo());
            addObj.setAuditStatus("0");
            addObj.setAddTime(LocalDateTime.now());
            addObj.setAddWho(userInfo.getUserName());
            addObj.setAddWhoName(userInfo.getRealName());

            String title = shifmanagements.get(0).getShiftName()+"--铁路转运到站分拨信息表";
            FdPostTransport sel3 = new FdPostTransport();
            sel3.setShiftNo(waybillHeader.getShiftNo());sel.setPlatformCode(waybillHeader.getPlatformCode());
            sel3.setDeleteFlag("N");
            List<FdPostTransport> fdPostTransports = fdPostTransportMapper.selectFdPostTransportList(sel3);
            if(CollUtil.isNotEmpty(fdPostTransports)){
                for (FdPostTransport post:fdPostTransports
                     ) {
                    if(StrUtil.isNotBlank(post.getTitle())){
                        title = post.getTitle();
                        break;
                    }
                }
            }
            addObj.setTitle(title);
            CustomerPlatformInfo sel2 = new CustomerPlatformInfo();
            sel2.setPlatformCode(waybillHeader.getPlatformCode());
            sel2.setCustomerCode(waybillHeader.getCustomerNo());
            sel2.setDeleteFlag("N");
            List<CustomerPlatformInfo> list = customerPlatformInfoMapper.selectCustomerPlatformInfoList(sel2);
            if(CollUtil.isNotEmpty(list)){
                addObj.setContactPerson(list.get(0).getContactPerson());
                addObj.setContactPhone(list.get(0).getContactNo());
            }else{
                sel2.setPlatformCode(null);
                List<CustomerPlatformInfo> list2 = customerPlatformInfoMapper.selectCustomerPlatformInfoList(sel2);
                if(CollUtil.isNotEmpty(list2)){
                    addObj.setContactPerson(list2.get(0).getContactPerson());
                    addObj.setContactPhone(list2.get(0).getContactNo());
                }
            }
            fdPostTransportMapper.insertFdPostTransport(addObj);
        }
    }

    public void fdPostTransportExported(FdPostTransport fdPostTransport, HttpServletResponse res) throws Exception {
//        fdPostTransport.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        fdPostTransport.setDeleteFlag("N");
        List<FdPostTransport> fdPostTransports = fdPostTransportMapper.selectFdPostTransportList2(fdPostTransport);
        if(CollUtil.isNotEmpty(fdPostTransports)){
            Map<String,String> map = new HashMap<>();
            map.put("title","");
            for (FdPostTransport post:fdPostTransports
                 ) {
                if(StrUtil.isNotBlank(post.getTitle())){
                    map.put("title",post.getTitle());
                    break;
                }
            }
            List<FdPostTransportDetail> customerList =new ArrayList<>();

            FdPostTransportDetail sel2 = new FdPostTransportDetail();
            sel2.setShiftNo(fdPostTransport.getShiftNo());
            for (FdPostTransport post:fdPostTransports
            ) {
                sel2.setWaybillNo(post.getWaybillNo());
                List<FdPostTransportDetail> fdPostTransportDetails = fdPostTransportDetailMapper.selectFdPostTransportDetailList2(sel2);
                if(CollUtil.isNotEmpty(fdPostTransportDetails)){
                    for (int i = 0; i < fdPostTransportDetails.size(); i++) {
                        if("0".equals(fdPostTransportDetails.get(i).getTransportType())){
                            fdPostTransportDetails.get(i).setTransportType("铁路");
                        }else if("1".equals(fdPostTransportDetails.get(i).getTransportType())){
                            fdPostTransportDetails.get(i).setTransportType("汽运提");
                        }
                        if("0".equals(fdPostTransportDetails.get(i).getIsCombined())){
                            fdPostTransportDetails.get(i).setIsCombined("否");
                        }else if("1".equals(fdPostTransportDetails.get(i).getIsCombined())){
                            fdPostTransportDetails.get(i).setIsCombined("是");
                        }
                        if("0".equals(fdPostTransportDetails.get(i).getIsExpenses())){
                            fdPostTransportDetails.get(i).setIsExpenses("否");
                        }else if("1".equals(fdPostTransportDetails.get(i).getIsExpenses())){
                            fdPostTransportDetails.get(i).setIsExpenses("是");
                        }
                        if("0".equals(fdPostTransportDetails.get(i).getIsDedicated())){
                            fdPostTransportDetails.get(i).setIsDedicated("否");
                        }else if("1".equals(fdPostTransportDetails.get(i).getIsDedicated())){
                            fdPostTransportDetails.get(i).setIsDedicated("是");
                        }
                    }
                    customerList.addAll(fdPostTransportDetails);
                }
            }

            if(CollUtil.isNotEmpty(customerList)){
                for (int i = 0; i < customerList.size(); i++
                     ) {
                    customerList.get(i).setId(i+1);
                }
                com.alibaba.excel.ExcelWriter excelWriter=null;
                String templateFileName = "fdPostTransportExported.xlsx";
                String osName = System.getProperties().getProperty("os.name");
                if ("Linux".equals(osName)){
                    templateFileName = linuxPath + templateFileName;
                }else{
                    templateFileName = windowsPath + templateFileName;
                }
                String fileName = URLEncoder.encode("后程转运分拨信息表", "UTF-8").replaceAll("\\+", "%20");
                res.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                res.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + ".xlsx\"");

                excelWriter = EasyExcel.write(res.getOutputStream()).withTemplate(templateFileName).build();
                WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "后程转运分拨信息表").build();
                excelWriter.fill(map, writeSheet1);
                if (CollUtil.isNotEmpty(customerList)) {
                    excelWriter.fill(customerList, writeSheet1);
                }

                excelWriter.finish();
            }
        }
    }
}
