package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.api.entity.EfFinancingApprovalinfo;
import com.huazheng.tunny.ocean.mapper.EfFinancingApprovalinfoMapper;
import com.huazheng.tunny.ocean.service.EfFinancingApprovalinfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("efFinancingApprovalinfoService")
public class EfFinancingApprovalinfoServiceImpl extends ServiceImpl<EfFinancingApprovalinfoMapper, EfFinancingApprovalinfo> implements EfFinancingApprovalinfoService {

    @Autowired
    private EfFinancingApprovalinfoMapper efFinancingApprovalinfoMapper;

    public EfFinancingApprovalinfoMapper getEfFinancingApprovalinfoMapper() {
        return efFinancingApprovalinfoMapper;
    }

    public void setEfFinancingApprovalinfoMapper(EfFinancingApprovalinfoMapper efFinancingApprovalinfoMapper) {
        this.efFinancingApprovalinfoMapper = efFinancingApprovalinfoMapper;
    }

    /**
     * 查询仓单质押银行审批信息信息
     *
     * @param rowId 仓单质押银行审批信息ID
     * @return 仓单质押银行审批信息信息
     */
    @Override
    public EfFinancingApprovalinfo selectEfFinancingApprovalinfoById(String rowId)
    {
        return efFinancingApprovalinfoMapper.selectEfFinancingApprovalinfoById(rowId);
    }

    /**
     * 查询仓单质押银行审批信息列表
     *
     * @param efFinancingApprovalinfo 仓单质押银行审批信息信息
     * @return 仓单质押银行审批信息集合
     */
    @Override
    public List<EfFinancingApprovalinfo> selectEfFinancingApprovalinfoList(EfFinancingApprovalinfo efFinancingApprovalinfo)
    {
        return efFinancingApprovalinfoMapper.selectEfFinancingApprovalinfoList(efFinancingApprovalinfo);
    }


    /**
     * 分页模糊查询仓单质押银行审批信息列表
     * @return 仓单质押银行审批信息集合
     */
    @Override
    public Page selectEfFinancingApprovalinfoListByLike(Query query)
    {
        EfFinancingApprovalinfo efFinancingApprovalinfo =  BeanUtil.mapToBean(query.getCondition(), EfFinancingApprovalinfo.class,false);
        query.setRecords(efFinancingApprovalinfoMapper.selectEfFinancingApprovalinfoListByLike(query,efFinancingApprovalinfo));
        return query;
    }

    /**
     * 新增仓单质押银行审批信息
     *
     * @param efFinancingApprovalinfo 仓单质押银行审批信息信息
     * @return 结果
     */
    @Override
    public int insertEfFinancingApprovalinfo(EfFinancingApprovalinfo efFinancingApprovalinfo)
    {
        return efFinancingApprovalinfoMapper.insertEfFinancingApprovalinfo(efFinancingApprovalinfo);
    }

    /**
     * 修改仓单质押银行审批信息
     *
     * @param efFinancingApprovalinfo 仓单质押银行审批信息信息
     * @return 结果
     */
    @Override
    public int updateEfFinancingApprovalinfo(EfFinancingApprovalinfo efFinancingApprovalinfo)
    {
        return efFinancingApprovalinfoMapper.updateEfFinancingApprovalinfo(efFinancingApprovalinfo);
    }


    /**
     * 删除仓单质押银行审批信息
     *
     * @param rowId 仓单质押银行审批信息ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingApprovalinfoById(String rowId)
    {
        return efFinancingApprovalinfoMapper.deleteEfFinancingApprovalinfoById( rowId);
    };


    /**
     * 批量删除仓单质押银行审批信息对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEfFinancingApprovalinfoByIds(Integer[] rowIds)
    {
        return efFinancingApprovalinfoMapper.deleteEfFinancingApprovalinfoByIds( rowIds);
    }

}
