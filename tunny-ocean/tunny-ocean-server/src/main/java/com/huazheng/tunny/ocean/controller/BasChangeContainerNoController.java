package com.huazheng.tunny.ocean.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.BasChangeContainerNo;
import com.huazheng.tunny.ocean.service.BasChangeContainerNoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 箱号变更管理
 *
 * <AUTHOR>
 * @date 2024-07-10 10:02:46
 */
@Slf4j
@RestController
@RequestMapping("/baschangecontainerno")
public class BasChangeContainerNoController {

    @Autowired
    private BasChangeContainerNoService basChangeContainerNoService;

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  basChangeContainerNoService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return basChangeContainerNoService.selectBasChangeContainerNoListByLike(new Query<>(params));
    }

    /**
     * 信息
     *
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Integer id) {
        BasChangeContainerNo basChangeContainerNo = basChangeContainerNoService.selectById(id);
        return new R<>(basChangeContainerNo);
    }

    /**
     * 保存
     *
     * @param basChangeContainerNo
     * @return R
     */
    @PostMapping
    public R save(@RequestBody BasChangeContainerNo basChangeContainerNo) {
        basChangeContainerNoService.insert(basChangeContainerNo);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     *
     * @param basChangeContainerNo
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody BasChangeContainerNo basChangeContainerNo) {
        basChangeContainerNoService.updateById(basChangeContainerNo);
        return new R<>(Boolean.TRUE);
    }


    /**
     * 删除
     *
     * @param id
     * @return R
     */
    @GetMapping("/del/{id}")
    public R delete(@PathVariable Integer id) {
        basChangeContainerNoService.deleteById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody List<Integer> ids) {
        basChangeContainerNoService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 下载模板
     *
     * @return
     */
    @PostMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) throws Exception {
        basChangeContainerNoService.exportTemplate(response);
    }


    /**
     * 导入模板
     * <p>
     *
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/importedTemplate")
    @Transactional
    public R importedTemplate(@RequestParam("file") MultipartFile file) throws Exception {
        return basChangeContainerNoService.importedTemplate(file);
    }
}
