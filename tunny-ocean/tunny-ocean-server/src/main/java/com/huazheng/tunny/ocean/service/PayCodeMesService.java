package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.PayCodeMes;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 付费代码信息表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-08-25 19:05:18
 */
public interface PayCodeMesService extends IService<PayCodeMes> {
    /**
     * 查询付费代码信息表信息
     *
     * @param rowId 付费代码信息表ID
     * @return 付费代码信息表信息
     */
    public PayCodeMes selectPayCodeMesById(String rowId);

    /**
     * 查询付费代码信息表列表
     *
     * @param payCodeMes 付费代码信息表信息
     * @return 付费代码信息表集合
     */
    public List<PayCodeMes> selectPayCodeMesList(PayCodeMes payCodeMes);


    /**
     * 分页模糊查询付费代码信息表列表
     * @return 付费代码信息表集合
     */
    public Page selectPayCodeMesListByLike(Query query);



    /**
     * 新增付费代码信息表
     *
     * @param payCodeMes 付费代码信息表信息
     * @return 结果
     */
    public int insertPayCodeMes(PayCodeMes payCodeMes);

    /**
     * 新增付费代码信息表
     *
     * @param payCodeMes 付费代码信息表信息
     * @return 结果
     */
    public int insertPayCodeMesBatch(List<PayCodeMes> payCodeMes);

    public void insertOrUpdatePayCodeMesBatch(List<PayCodeMes> payCodeMes);

    /**
     * 修改付费代码信息表
     *
     * @param payCodeMes 付费代码信息表信息
     * @return 结果
     */
    public int updatePayCodeMes(PayCodeMes payCodeMes);

    /**
     * 批量修改付费代码信息表
     *
     * @param payCodeMes 付费代码信息表信息
     * @return 结果
     */
    public int updatePayCodeMesBatch(List<PayCodeMes> payCodeMes);

    public int updatePayCodeMesBatchDelete(PayCodeMes payCodeMe);

    /**
     * 删除付费代码信息表
     *
     * @param rowId 付费代码信息表ID
     * @return 结果
     */
    public int deletePayCodeMesById(String rowId);

    /**
     * 批量删除付费代码信息表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deletePayCodeMesByIds(Integer[] rowIds);

}

