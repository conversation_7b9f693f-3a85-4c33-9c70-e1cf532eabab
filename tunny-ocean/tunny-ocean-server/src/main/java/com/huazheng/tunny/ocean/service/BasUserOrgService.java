package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.BasUserOrg;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 用户机构关联表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-01 09:19:56
 */
public interface BasUserOrgService extends IService<BasUserOrg> {
    /**
     * 查询用户机构关联表信息
     *
     * @param rowId 用户机构关联表ID
     * @return 用户机构关联表信息
     */
    public BasUserOrg selectBasUserOrgById(String rowId);

    /**
     * 查询用户机构关联表列表
     *
     * @param basUserOrg 用户机构关联表信息
     * @return 用户机构关联表集合
     */
    public List<BasUserOrg> selectBasUserOrgList(BasUserOrg basUserOrg);


    /**
     * 分页模糊查询用户机构关联表列表
     * @return 用户机构关联表集合
     */
    public Page selectBasUserOrgListByLike(Query query);



    /**
     * 新增用户机构关联表
     *
     * @param basUserOrg 用户机构关联表信息
     * @return 结果
     */
    public int insertBasUserOrg(BasUserOrg basUserOrg);

    /**
     * 修改用户机构关联表
     *
     * @param basUserOrg 用户机构关联表信息
     * @return 结果
     */
    public int updateBasUserOrg(BasUserOrg basUserOrg);

    /**
     * 删除用户机构关联表
     *
     * @param rowId 用户机构关联表ID
     * @return 结果
     */
    public int deleteBasUserOrgById(String rowId);

    /**
     * 批量删除用户机构关联表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteBasUserOrgByIds(Integer[] rowIds);

}

