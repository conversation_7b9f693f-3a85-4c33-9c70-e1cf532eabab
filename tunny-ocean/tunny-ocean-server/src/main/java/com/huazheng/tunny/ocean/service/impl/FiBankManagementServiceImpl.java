package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.FiBankManagement;
import com.huazheng.tunny.ocean.mapper.FiBankManagementMapper;
import com.huazheng.tunny.ocean.service.FiBankManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;
import java.util.Map;

@Service("fiBankManagementService")
public class FiBankManagementServiceImpl extends ServiceImpl<FiBankManagementMapper, FiBankManagement> implements FiBankManagementService {

    @Autowired
    private FiBankManagementMapper fiBankManagementMapper;

    public FiBankManagementMapper getFiBankManagementMapper() {
        return fiBankManagementMapper;
    }

    public void setFiBankManagementMapper(FiBankManagementMapper fiBankManagementMapper) {
        this.fiBankManagementMapper = fiBankManagementMapper;
    }

    /**
     * 查询信息
     *
     * @param id ID
     * @return 信息
     */
    @Override
    public FiBankManagement selectFiBankManagementById(Integer id)
    {
        return fiBankManagementMapper.selectFiBankManagementById(id);
    }

    /**
     * 查询列表
     *
     * @param fiBankManagement 信息
     * @return 集合
     */
    @Override
    public List<FiBankManagement> selectFiBankManagementList(FiBankManagement fiBankManagement)
    {
        return fiBankManagementMapper.selectFiBankManagementList(fiBankManagement);
    }

    @Override
    public List<FiBankManagement> selectFiBankManagementList2(FiBankManagement fiBankManagement)
    {
        return fiBankManagementMapper.selectFiBankManagementListByLike(fiBankManagement);
    }

    @Override
    public R selectHeadBankName(Map<String, Object> params) {
        R r=new R();
        FiBankManagement fiBankManagement =  BeanUtil.mapToBean(params, FiBankManagement.class,false);
        List<FiBankManagement> fiBankManagements = fiBankManagementMapper.selectHeadBankName(fiBankManagement);
        r.setCode(200);
        r.setB(true);
        r.setData(fiBankManagements);
        return r;
    }

    @Override
    public R selectBankName(Map<String, Object> params) {
        R r=new R();
        FiBankManagement fiBankManagement =  BeanUtil.mapToBean(params, FiBankManagement.class,false);
        List<FiBankManagement> fiBankManagements = fiBankManagementMapper.selectBankName(fiBankManagement);
        r.setCode(200);
        r.setB(true);
        r.setData(fiBankManagements);
        return r;
    }


    /**
     * 分页模糊查询列表
     * @return 集合
     */
    @Override
    public Page selectFiBankManagementListByLike(Query query)
    {
        FiBankManagement fiBankManagement =  BeanUtil.mapToBean(query.getCondition(), FiBankManagement.class,false);
        query.setRecords(fiBankManagementMapper.selectFiBankManagementListByLike(query,fiBankManagement));
        return query;
    }

    /**
     * 新增
     *
     * @param fiBankManagement 信息
     * @return 结果
     */
    @Override
    public int insertFiBankManagement(FiBankManagement fiBankManagement)
    {
        return fiBankManagementMapper.insertFiBankManagement(fiBankManagement);
    }

    /**
     * 修改
     *
     * @param fiBankManagement 信息
     * @return 结果
     */
    @Override
    public int updateFiBankManagement(FiBankManagement fiBankManagement)
    {
        return fiBankManagementMapper.updateFiBankManagement(fiBankManagement);
    }


    /**
     * 删除
     *
     * @param id ID
     * @return 结果
     */
    public int deleteFiBankManagementById(Integer id)
    {
        return fiBankManagementMapper.deleteFiBankManagementById( id);
    };


    /**
     * 批量删除对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiBankManagementByIds(Integer[] ids)
    {
        return fiBankManagementMapper.deleteFiBankManagementByIds( ids);
    }

}
