package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.ocean.api.entity.StoredCityArriveStatistics;
import com.huazheng.tunny.ocean.api.entity.StoredCityArriveStatisticsSub;
import com.huazheng.tunny.ocean.mapper.StoredCityArriveStatisticsMapper;
import com.huazheng.tunny.ocean.mapper.StoredCityArriveStatisticsSubMapper;
import com.huazheng.tunny.ocean.service.StoredCityArriveStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service("storedCityArriveStatisticsService")
public class StoredCityArriveStatisticsServiceImpl extends ServiceImpl<StoredCityArriveStatisticsMapper, StoredCityArriveStatistics> implements StoredCityArriveStatisticsService {

    @Autowired
    private StoredCityArriveStatisticsMapper storedCityArriveStatisticsMapper;

    @Autowired
    private StoredCityArriveStatisticsSubMapper storedCityArriveStatisticsSubMapper;

    /**
     * 查询各地市到达国家车数统计表信息
     *
     * @param rowId 各地市到达国家车数统计表ID
     * @return 各地市到达国家车数统计表信息
     */
    @Override
    public StoredCityArriveStatistics selectStoredCityArriveStatisticsById(Integer rowId)
    {
        return storedCityArriveStatisticsMapper.selectStoredCityArriveStatisticsById(rowId);
    }

    /**
     * 查询各地市到达国家车数统计表列表
     *
     * @param storedCityArriveStatistics 各地市到达国家车数统计表信息
     * @return 各地市到达国家车数统计表集合
     */
    @Override
    public List<StoredCityArriveStatistics> selectStoredCityArriveStatisticsList(StoredCityArriveStatistics storedCityArriveStatistics)
    {
        StoredCityArriveStatisticsSub sub = new StoredCityArriveStatisticsSub();
        if(storedCityArriveStatistics!=null && StrUtil.isNotEmpty(storedCityArriveStatistics.getDate())){
            String date= LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
            storedCityArriveStatistics.setDate(date);
        }
        sub.setDate(storedCityArriveStatistics.getDate());
        List<StoredCityArriveStatistics> list = storedCityArriveStatisticsMapper.selectStoredCityArriveStatisticsList(storedCityArriveStatistics);
        List<StoredCityArriveStatisticsSub> subList = storedCityArriveStatisticsSubMapper.selectStoredCityArriveStatisticsSubList(sub);
        List<StoredCityArriveStatisticsSub> subListTotal = storedCityArriveStatisticsSubMapper.selectStoredCityArriveStatisticsSubListTotal(sub);
        for (StoredCityArriveStatistics l:list
        ) {
            if(StrUtil.isNotEmpty(l.getLine())&&l.getLine().contains("汇总")){
                for (StoredCityArriveStatisticsSub slt:subListTotal
                ) {
                    if(StrUtil.isNotEmpty(slt.getTotalNum())){
                        slt.setTotalNum("0");
                    }
                    if (StrUtil.isNotEmpty(l.getAreaCode()) && StrUtil.isNotEmpty(l.getTrip())
                            && StrUtil.isNotEmpty(slt.getAreaCode()) && StrUtil.isNotEmpty(slt.getTrip())
                            && l.getAreaCode().equals(slt.getAreaCode()) && l.getTrip().equals(slt.getTrip())
                            && StrUtil.isNotEmpty(slt.getCountryName())){

                        getCountryNum(l,slt);
                    }
                    if(l.getLine().equals("欧亚班列汇总")){
                        getCountryNum(l,slt);
                    }
                }
            }else{
                for (StoredCityArriveStatisticsSub sl:subList
                ) {
                    if(sl.getTotalNum()==null || "".equals(sl.getTotalNum())){
                        sl.setTotalNum("0");
                    }
                    if (StrUtil.isNotEmpty(l.getIdentification()) && StrUtil.isNotEmpty(sl.getIdentification())
                            && l.getIdentification().equals(sl.getIdentification())
                            && StrUtil.isNotEmpty(l.getAreaCode()) && StrUtil.isNotEmpty(l.getTrip())
                            && StrUtil.isNotEmpty(sl.getAreaCode()) && StrUtil.isNotEmpty(sl.getTrip())
                            && l.getAreaCode().equals(sl.getAreaCode()) && l.getTrip().equals(sl.getTrip())
                            && StrUtil.isNotEmpty(l.getStation()) && StrUtil.isNotEmpty(l.getPortStation())
                            && StrUtil.isNotEmpty(sl.getStation()) && StrUtil.isNotEmpty(sl.getPortStation())
                            && l.getStation().equals(sl.getStation()) && l.getPortStation().equals(sl.getPortStation())
                            && StrUtil.isNotEmpty(sl.getCountryName())){
                        getCountryNum(l,sl);
                    }
                }
            }
        }

        checkEmpty(list);
        return list;
    }

    private static void checkEmpty(List<StoredCityArriveStatistics> list) {
        for (StoredCityArriveStatistics l: list
        ) {
            if(l.getRuNum()==null || "".equals(l.getRuNum())){
                l.setRuNum("0");
            }
            if(l.getBlrNum()==null || "".equals(l.getBlrNum())){
                l.setBlrNum("0");
            }
            if(l.getGerNum()==null || "".equals(l.getGerNum())){
                l.setGerNum("0");
            }
            if(l.getPolNum()==null || "".equals(l.getPolNum())){
                l.setPolNum("0");
            }
            if(l.getUkrNum()==null || "".equals(l.getUkrNum())){
                l.setUkrNum("0");
            }
            if(l.getNedNum()==null || "".equals(l.getNedNum())){
                l.setNedNum("0");
            }
            if(l.getBelNum()==null || "".equals(l.getBelNum())){
                l.setBelNum("0");
            }
            if(l.getCzeNum()==null || "".equals(l.getCzeNum())){
                l.setCzeNum("0");
            }
            if(l.getFinNum()==null || "".equals(l.getFinNum())){
                l.setFinNum("0");
            }
            if(l.getHunNum()==null || "".equals(l.getHunNum())){
                l.setHunNum("0");
            }
            if(l.getGbrNum()==null || "".equals(l.getGbrNum())){
                l.setGbrNum("0");
            }
            if(l.getItaNum()==null || "".equals(l.getItaNum())){
                l.setItaNum("0");
            }
            if(l.getUrNum()==null || "".equals(l.getUrNum())){
                l.setUrNum("0");
            }
            if(l.getLaoNum()==null || "".equals(l.getLaoNum())){
                l.setLaoNum("0");
            }
            if(l.getKazNum()==null || "".equals(l.getKazNum())){
                l.setKazNum("0");
            }
            if(l.getUzbNum()==null || "".equals(l.getUzbNum())){
                l.setUzbNum("0");
            }
            if(l.getKgzNum()==null || "".equals(l.getKgzNum())){
                l.setKgzNum("0");
            }
            if(l.getTjkNum()==null || "".equals(l.getTjkNum())){
                l.setTjkNum("0");
            }
            if(l.getTkmNum()==null || "".equals(l.getTkmNum())){
                l.setTkmNum("0");
            }
            if(l.getVieNum()==null || "".equals(l.getVieNum())){
                l.setVieNum("0");
            }
            if(l.getMglNum()==null || "".equals(l.getMglNum())){
                l.setMglNum("0");
            }
            if(l.getAzeNum()==null || "".equals(l.getAzeNum())){
                l.setAzeNum("0");
            }
            if(l.getGeoNum()==null || "".equals(l.getGeoNum())){
                l.setGeoNum("0");
            }
            if(l.getIriNum()==null || "".equals(l.getIriNum())){
                l.setIriNum("0");
            }
            if(l.getScgNum()==null || "".equals(l.getScgNum())){
                l.setScgNum("0");
            }
            if(l.getEspNum()==null || "".equals(l.getEspNum())){
                l.setEspNum("0");
            }
            if(l.getFraNum()==null || "".equals(l.getFraNum())){
                l.setFraNum("0");
            }
            if(l.getThaNum()==null || "".equals(l.getThaNum())){
                l.setThaNum("0");
            }
        }
    }

    public void getCountryNum (StoredCityArriveStatistics l,StoredCityArriveStatisticsSub sl){
        String countryName = sl.getCountryName();
        double totalNum = Double.parseDouble(sl.getTotalNum());

        if (countryName != null) {
            switch (countryName) {
                case "俄罗斯":
                    l.setRuNum(String.valueOf(Double.parseDouble(l.getRuNum() == null ? "0" : l.getRuNum()) + totalNum));
                    break;
                case "白俄罗斯":
                    l.setBlrNum(String.valueOf(Double.parseDouble(l.getBlrNum() == null ? "0" : l.getBlrNum()) + totalNum));
                    break;
                case "德国":
                    l.setGerNum(String.valueOf(Double.parseDouble(l.getGerNum() == null ? "0" : l.getGerNum()) + totalNum));
                    break;
                case "波兰":
                    l.setPolNum(String.valueOf(Double.parseDouble(l.getPolNum() == null ? "0" : l.getPolNum()) + totalNum));
                    break;
                case "乌克兰":
                    l.setUkrNum(String.valueOf(Double.parseDouble(l.getUkrNum() == null ? "0" : l.getUkrNum()) + totalNum));
                    break;
                case "荷兰":
                    l.setNedNum(String.valueOf(Double.parseDouble(l.getNedNum() == null ? "0" : l.getNedNum()) + totalNum));
                    break;
                case "比利时":
                    l.setBelNum(String.valueOf(Double.parseDouble(l.getBelNum() == null ? "0" : l.getBelNum()) + totalNum));
                    break;
                case "捷克":
                    l.setCzeNum(String.valueOf(Double.parseDouble(l.getCzeNum() == null ? "0" : l.getCzeNum()) + totalNum));
                    break;
                case "芬兰":
                    l.setFinNum(String.valueOf(Double.parseDouble(l.getFinNum() == null ? "0" : l.getFinNum()) + totalNum));
                    break;
                case "匈牙利":
                    l.setHunNum(String.valueOf(Double.parseDouble(l.getHunNum() == null ? "0" : l.getHunNum()) + totalNum));
                    break;
                case "英国":
                    l.setGbrNum(String.valueOf(Double.parseDouble(l.getGbrNum() == null ? "0" : l.getGbrNum()) + totalNum));
                    break;
                case "意大利":
                    l.setItaNum(String.valueOf(Double.parseDouble(l.getItaNum() == null ? "0" : l.getItaNum()) + totalNum));
                    break;
                case "土耳其":
                    l.setUrNum(String.valueOf(Double.parseDouble(l.getUrNum() == null ? "0" : l.getUrNum()) + totalNum));
                    break;
                case "老挝":
                    l.setLaoNum(String.valueOf(Double.parseDouble(l.getLaoNum() == null ? "0" : l.getLaoNum()) + totalNum));
                    break;
                case "哈萨克斯坦":
                    l.setKazNum(String.valueOf(Double.parseDouble(l.getKazNum() == null ? "0" : l.getKazNum()) + totalNum));
                    break;
                case "乌兹别克斯坦":
                    l.setUzbNum(String.valueOf(Double.parseDouble(l.getUzbNum() == null ? "0" : l.getUzbNum()) + totalNum));
                    break;
                case "吉尔吉斯斯坦":
                    l.setKgzNum(String.valueOf(Double.parseDouble(l.getKgzNum() == null ? "0" : l.getKgzNum()) + totalNum));
                    break;
                case "塔吉克斯坦":
                    l.setTjkNum(String.valueOf(Double.parseDouble(l.getTjkNum() == null ? "0" : l.getTjkNum()) + totalNum));
                    break;
                case "土库曼斯坦":
                    l.setTkmNum(String.valueOf(Double.parseDouble(l.getTkmNum() == null ? "0" : l.getTkmNum()) + totalNum));
                    break;
                case "越南":
                    l.setVieNum(String.valueOf(Double.parseDouble(l.getVieNum() == null ? "0" : l.getVieNum()) + totalNum));
                    break;
                case "蒙古":
                    l.setMglNum(String.valueOf(Double.parseDouble(l.getMglNum() == null ? "0" : l.getMglNum()) + totalNum));
                    break;
                case "阿塞拜疆":
                    l.setAzeNum(String.valueOf(Double.parseDouble(l.getAzeNum() == null ? "0" : l.getAzeNum()) + totalNum));
                    break;
                case "格鲁吉亚":
                    l.setGeoNum(String.valueOf(Double.parseDouble(l.getGeoNum() == null ? "0" : l.getGeoNum()) + totalNum));
                    break;
                case "伊朗":
                    l.setIriNum(String.valueOf(Double.parseDouble(l.getIriNum() == null ? "0" : l.getIriNum()) + totalNum));
                    break;
                case "塞尔维亚":
                    l.setScgNum(String.valueOf(Double.parseDouble(l.getScgNum() == null ? "0" : l.getScgNum()) + totalNum));
                    break;
                case "西班牙":
                    l.setEspNum(String.valueOf(Double.parseDouble(l.getEspNum() == null ? "0" : l.getEspNum()) + totalNum));
                    break;
                case "法国":
                    l.setFraNum(String.valueOf(Double.parseDouble(l.getFraNum() == null ? "0" : l.getFraNum()) + totalNum));
                    break;
                case "泰国":
                    l.setThaNum(String.valueOf(Double.parseDouble(l.getThaNum() == null ? "0" : l.getThaNum()) + totalNum));
                    break;
                default:
                    // 其他情况的处理
                    break;
            }
        }
    }


    /**
     * 分页模糊查询各地市到达国家车数统计表列表
     * @return 各地市到达国家车数统计表集合
     */
    @Override
    public Page selectStoredCityArriveStatisticsListByLike(Query query)
    {
        StoredCityArriveStatistics storedCityArriveStatistics =  BeanUtil.mapToBean(query.getCondition(), StoredCityArriveStatistics.class,false);
        query.setRecords(storedCityArriveStatisticsMapper.selectStoredCityArriveStatisticsListByLike(query,storedCityArriveStatistics));
        return query;
    }

    /**
     * 新增各地市到达国家车数统计表
     *
     * @param storedCityArriveStatistics 各地市到达国家车数统计表信息
     * @return 结果
     */
    @Override
    public int insertStoredCityArriveStatistics(StoredCityArriveStatistics storedCityArriveStatistics)
    {
        return storedCityArriveStatisticsMapper.insertStoredCityArriveStatistics(storedCityArriveStatistics);
    }

    /**
     * 修改各地市到达国家车数统计表
     *
     * @param storedCityArriveStatistics 各地市到达国家车数统计表信息
     * @return 结果
     */
    @Override
    public int updateStoredCityArriveStatistics(StoredCityArriveStatistics storedCityArriveStatistics)
    {
        return storedCityArriveStatisticsMapper.updateStoredCityArriveStatistics(storedCityArriveStatistics);
    }


    /**
     * 删除各地市到达国家车数统计表
     *
     * @param rowId 各地市到达国家车数统计表ID
     * @return 结果
     */
    public int deleteStoredCityArriveStatisticsById(Integer rowId)
    {
        return storedCityArriveStatisticsMapper.deleteStoredCityArriveStatisticsById( rowId);
    };


    /**
     * 批量删除各地市到达国家车数统计表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteStoredCityArriveStatisticsByIds(Integer[] rowIds)
    {
        return storedCityArriveStatisticsMapper.deleteStoredCityArriveStatisticsByIds( rowIds);
    }

}
