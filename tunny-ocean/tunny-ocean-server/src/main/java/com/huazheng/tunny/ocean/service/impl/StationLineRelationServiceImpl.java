package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.StationLineRelationMapper;
import com.huazheng.tunny.ocean.api.entity.StationLineRelation;
import com.huazheng.tunny.ocean.service.StationLineRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.time.LocalDateTime;
import java.util.List;

@Service("stationLineRelationService")
public class StationLineRelationServiceImpl extends ServiceImpl<StationLineRelationMapper, StationLineRelation> implements StationLineRelationService {

    @Autowired
    private StationLineRelationMapper stationLineRelationMapper;

    public StationLineRelationMapper getStationLineRelationMapper() {
        return stationLineRelationMapper;
    }

    public void setStationLineRelationMapper(StationLineRelationMapper stationLineRelationMapper) {
        this.stationLineRelationMapper = stationLineRelationMapper;
    }

    /**
     * 查询站点线路管理信息
     *
     * @param rowId 站点线路管理ID
     * @return 站点线路管理信息
     */
    @Override
    public List<StationLineRelation> selectStationLineRelationByStationCode(String rowId)
    {
        return stationLineRelationMapper.selectStationLineRelationByStationCode(rowId);
    }

    /**
     * 查询站点线路管理列表
     *
     * @param stationLineRelation 站点线路管理信息
     * @return 站点线路管理集合
     */
    @Override
    public List<StationLineRelation> selectStationLineRelationList(StationLineRelation stationLineRelation)
    {
        return stationLineRelationMapper.selectStationLineRelationList(stationLineRelation);
    }


    /**
     * 分页模糊查询站点线路管理列表
     * @return 站点线路管理集合
     */
    @Override
    public Page selectStationLineRelationListByLike(Query query)
    {
        StationLineRelation stationLineRelation =  BeanUtil.mapToBean(query.getCondition(), StationLineRelation.class,false);
        query.setRecords(stationLineRelationMapper.selectStationLineRelationListByLike(query,stationLineRelation));
        return query;
    }

    /**
     * 新增站点线路管理
     *
     * @param stationLineRelation 站点线路管理信息
     * @return 结果
     */
    @Override
    public int insertStationLineRelation(StationLineRelation stationLineRelation)
    {
        return stationLineRelationMapper.insertStationLineRelation(stationLineRelation);
    }

    /**
     * 修改站点线路管理
     *
     * @param stationLineRelation 站点线路管理信息
     * @return 结果
     */
    @Override
    public int updateStationLineRelation(StationLineRelation stationLineRelation)
    {
        stationLineRelation.setDeleteFlag("Y");
        stationLineRelation.setDeleteTime(LocalDateTime.now());
        stationLineRelation.setDeleteWho("111111111111");
        stationLineRelation.setDeleteWhoName("11111111111");
        return stationLineRelationMapper.cancelShip(stationLineRelation);
    }


    /**
     * 删除站点线路管理
     *
     * @param rowId 站点线路管理ID
     * @return 结果
     */
    @Override
    public int deleteStationLineRelationById(String rowId)
    {
        return stationLineRelationMapper.deleteStationLineRelationById( rowId);
    };


    /**
     * 批量删除站点线路管理对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteStationLineRelationByIds(Integer[] rowIds)
    {
        return stationLineRelationMapper.deleteStationLineRelationByIds(rowIds);
    }

}
