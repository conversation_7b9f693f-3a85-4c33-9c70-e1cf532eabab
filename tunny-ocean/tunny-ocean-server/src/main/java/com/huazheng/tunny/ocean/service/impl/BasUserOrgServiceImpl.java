package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.BasUserOrgMapper;
import com.huazheng.tunny.ocean.api.entity.BasUserOrg;
import com.huazheng.tunny.ocean.service.BasUserOrgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("basUserOrgService")
public class BasUserOrgServiceImpl extends ServiceImpl<BasUserOrgMapper, BasUserOrg> implements BasUserOrgService {

    @Autowired
    private BasUserOrgMapper basUserOrgMapper;

    public BasUserOrgMapper getBasUserOrgMapper() {
        return basUserOrgMapper;
    }

    public void setBasUserOrgMapper(BasUserOrgMapper basUserOrgMapper) {
        this.basUserOrgMapper = basUserOrgMapper;
    }

    /**
     * 查询用户机构关联表信息
     *
     * @param rowId 用户机构关联表ID
     * @return 用户机构关联表信息
     */
    @Override
    public BasUserOrg selectBasUserOrgById(String rowId)
    {
        return basUserOrgMapper.selectBasUserOrgById(rowId);
    }

    /**
     * 查询用户机构关联表列表
     *
     * @param basUserOrg 用户机构关联表信息
     * @return 用户机构关联表集合
     */
    @Override
    public List<BasUserOrg> selectBasUserOrgList(BasUserOrg basUserOrg)
    {
        return basUserOrgMapper.selectBasUserOrgList(basUserOrg);
    }


    /**
     * 分页模糊查询用户机构关联表列表
     * @return 用户机构关联表集合
     */
    @Override
    public Page selectBasUserOrgListByLike(Query query)
    {
        BasUserOrg basUserOrg =  BeanUtil.mapToBean(query.getCondition(), BasUserOrg.class,false);
        query.setRecords(basUserOrgMapper.selectBasUserOrgListByLike(query,basUserOrg));
        return query;
    }

    /**
     * 新增用户机构关联表
     *
     * @param basUserOrg 用户机构关联表信息
     * @return 结果
     */
    @Override
    public int insertBasUserOrg(BasUserOrg basUserOrg)
    {
        return basUserOrgMapper.insertBasUserOrg(basUserOrg);
    }

    /**
     * 修改用户机构关联表
     *
     * @param basUserOrg 用户机构关联表信息
     * @return 结果
     */
    @Override
    public int updateBasUserOrg(BasUserOrg basUserOrg)
    {
        return basUserOrgMapper.updateBasUserOrg(basUserOrg);
    }


    /**
     * 删除用户机构关联表
     *
     * @param rowId 用户机构关联表ID
     * @return 结果
     */
    public int deleteBasUserOrgById(String rowId)
    {
        return basUserOrgMapper.deleteBasUserOrgById( rowId);
    };


    /**
     * 批量删除用户机构关联表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBasUserOrgByIds(Integer[] rowIds)
    {
        return basUserOrgMapper.deleteBasUserOrgByIds( rowIds);
    }

}
