package com.huazheng.tunny.ocean.controller;


import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.FdFreightWriteOff;
import com.huazheng.tunny.ocean.api.entity.FdShippingAccount;
import com.huazheng.tunny.ocean.api.vo.FdShippingAccountVO;
import com.huazheng.tunny.ocean.api.vo.GoodsVO;
import com.huazheng.tunny.ocean.api.vo.PayMesVO;
import com.huazheng.tunny.ocean.api.vo.ShippingAccountExVO;
import com.huazheng.tunny.ocean.mapper.PayCodeMesMapper;
import com.huazheng.tunny.ocean.mapper.WaybillContainerInfoMapper;
import com.huazheng.tunny.ocean.mapper.WaybillGoodsInfoMapper;
import com.huazheng.tunny.ocean.service.FdFreightWriteOffService;
import com.huazheng.tunny.ocean.service.FdShippingAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 发运台账主表
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:30:06
 */
@RestController
@RequestMapping("/fdfreightwriteoff")
@Slf4j
public class FdFreightWriteOffController {
    @Autowired
    private FdFreightWriteOffService fdFreightWriteOffService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdShippingAccountService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdFreightWriteOffService.getFdFreightWriteOffList(new Query<>(params));
    }

    @GetMapping("/getTotal")
    public FdFreightWriteOff getTotal(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fdShippingAccountService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fdFreightWriteOffService.getFdFreightWriteOffListTotal(new Query<>(params));
    }

}
