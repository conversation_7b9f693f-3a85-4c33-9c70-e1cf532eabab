package com.huazheng.tunny.ocean.service.eabalance.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.eabalance.EaBalance;
import com.huazheng.tunny.ocean.api.entity.eabalance.EaBalanceRemittance;
import com.huazheng.tunny.ocean.api.entity.eabalance.EaBalanceTransaction;
import com.huazheng.tunny.ocean.api.enums.TransactionStatusEnum;
import com.huazheng.tunny.ocean.mapper.eabalance.EaBalanceMapper;
import com.huazheng.tunny.ocean.mapper.eabalance.EaBalanceRemittanceMapper;
import com.huazheng.tunny.ocean.mapper.eabalance.EaBalanceTransactionMapper;
import com.huazheng.tunny.ocean.service.eabalance.EaBalanceRemittanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service("eaBalanceRemittanceService")
public class EaBalanceRemittanceServiceImpl extends ServiceImpl<EaBalanceRemittanceMapper, EaBalanceRemittance> implements EaBalanceRemittanceService {

    @Autowired
    private EaBalanceRemittanceMapper eaBalanceRemittanceMapper;
    @Autowired
    private EaBalanceTransactionMapper eaBalanceTransactionMapper;
    @Autowired
    private EaBalanceMapper eaBalanceMapper;

    /**
     * 查询余额汇款表信息
     *
     * @param transactionId 余额汇款表ID
     * @return 余额汇款表信息
     */
    @Override
    public EaBalanceRemittance selectEaBalanceRemittanceById(Long transactionId) {
        return eaBalanceRemittanceMapper.selectEaBalanceRemittanceById(transactionId);
    }

    /**
     * 查询余额汇款表列表
     *
     * @param eaBalanceRemittance 余额汇款表信息
     * @return 余额汇款表集合
     */
    @Override
    public List<EaBalanceRemittance> selectEaBalanceRemittanceList(EaBalanceRemittance eaBalanceRemittance) {
        return eaBalanceRemittanceMapper.selectEaBalanceRemittanceList(eaBalanceRemittance);
    }


    /**
     * 分页模糊查询余额汇款表列表
     *
     * @return 余额汇款表集合
     */
    @Override
    public Page selectEaBalanceRemittanceListByLike(Query query) {
        EaBalanceRemittance eaBalanceRemittance = BeanUtil.mapToBean(query.getCondition(), EaBalanceRemittance.class, false);
        query.setRecords(eaBalanceRemittanceMapper.selectEaBalanceRemittanceListByLike(query, eaBalanceRemittance));
        return query;
    }

    /**
     * 新增余额汇款表
     *
     * @param eaBalanceRemittance 余额汇款表信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R insertEaBalanceRemittance(EaBalanceRemittance eaBalanceRemittance) {
        if (eaBalanceRemittance == null) {
            return R.error("参数不全");
        }
        EaBalance eaBalance = eaBalanceMapper.selectEaBalanceById(eaBalanceRemittance.getBalanceId());
        if (eaBalance == null) {
            return R.error("余额记录不存在");
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        eaBalanceRemittance.setPayerCode(eaBalance.getPayerCode());
        eaBalanceRemittance.setPayerName(eaBalance.getPayerName());
        eaBalanceRemittance.setPayeeCode(eaBalance.getPayeeCode());
        eaBalanceRemittance.setPayeeName(eaBalance.getPayeeName());
        eaBalanceRemittance.setCreateBy(userInfo.getRealName());
        eaBalanceRemittance.setCreateById(userInfo.getId());
        eaBalanceRemittance.setCreateTime(LocalDateTime.now());

        if (eaBalance.getPayerCode().startsWith("MC") && eaBalance.getPayeeCode().startsWith("MP")) {
            eaBalanceRemittance.setClaimStatus(TransactionStatusEnum.UNCLAIMED.getKey());
        } else {
            BigDecimal balance = eaBalance.getAvailableBalance();
            //修改余额数据
            eaBalance.setTotalIncome(eaBalance.getTotalIncome().add(eaBalanceRemittance.getBillAmount()));
            eaBalance.setAvailableBalance(null);
            eaBalance.setUpdateBy(userInfo.getRealName());
            eaBalance.setUpdateById(userInfo.getId());
            eaBalanceMapper.updateEaBalance(eaBalance);
            //保余额汇款
            balance = balance.add(eaBalanceRemittance.getBillAmount());
            eaBalanceRemittance.setClaimStatus(TransactionStatusEnum.CLAIMED.getKey());
            EaBalanceTransaction eaBalanceTransaction = new EaBalanceTransaction();
            BeanUtil.copyProperties(eaBalanceRemittance, eaBalanceTransaction);
            eaBalanceTransaction.setTransactionType("收入");
            eaBalanceTransaction.setBalance(balance);
            eaBalanceTransaction.setTransactionStatus(eaBalanceRemittance.getClaimStatus());
            eaBalanceTransaction.setOperationTime(eaBalanceRemittance.getCreateTime());
            eaBalanceTransaction.setOperatorName(userInfo.getRealName());
            StringBuilder remark = new StringBuilder();
            remark.append(userInfo.getRealName()).append("于").append(eaBalanceRemittance.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    .append("进行").append(eaBalanceRemittance.getPaymentMethodName()).append(eaBalanceTransaction.getTransactionType())
                    .append(eaBalanceRemittance.getBillAmount()).append("；");
            eaBalanceTransaction.setRemark(remark.toString());
            eaBalanceTransactionMapper.insertEaBalanceTransaction(eaBalanceTransaction);
            eaBalanceRemittance.setClaimStatus(TransactionStatusEnum.CLAIMED.getKey());
            eaBalanceRemittance.setClaimName(userInfo.getRealName());
            eaBalanceRemittance.setClaimTime(LocalDateTime.now());
        }
        eaBalanceRemittanceMapper.insertEaBalanceRemittance(eaBalanceRemittance);
        return R.success();
    }

    /**
     * 修改余额汇款表
     *
     * @param eaBalanceRemittance 余额汇款表信息
     * @return 结果
     */
    @Override
    public R updateEaBalanceRemittance(EaBalanceRemittance eaBalanceRemittance) {
        if (eaBalanceRemittance == null) {
            return R.error("参数不全！");
        }
        EaBalanceRemittance oldRemittance = eaBalanceRemittanceMapper.selectEaBalanceRemittanceById(eaBalanceRemittance.getRemittanceId());
        if (oldRemittance == null) {
            return R.error("数据不存在！");
        }
        EaBalance eaBalance = eaBalanceMapper.selectEaBalanceById(eaBalanceRemittance.getBalanceId());
        if (eaBalance == null) {
            return R.error("余额记录不存在！");
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        eaBalanceRemittance.setUpdateBy(userInfo.getRealName());
        eaBalanceRemittance.setUpdateById(userInfo.getId());
        eaBalanceRemittance.setUpdateTime(LocalDateTime.now());

        if (TransactionStatusEnum.CLAIMED.getKey().equals(eaBalanceRemittance.getClaimStatus())) {
            BigDecimal balance = eaBalance.getAvailableBalance();
            int compare = eaBalanceRemittance.getBillAmount().compareTo(oldRemittance.getBillAmount());
            if (compare != 0) {
                BigDecimal chaVal = eaBalanceRemittance.getBillAmount().subtract(oldRemittance.getBillAmount());
                //修改余额数据
                eaBalance.setTotalIncome(eaBalance.getTotalIncome().add(chaVal));
                eaBalance.setAvailableBalance(null);
                eaBalance.setUpdateBy(userInfo.getRealName());
                eaBalance.setUpdateById(userInfo.getId());
                eaBalanceMapper.updateEaBalance(eaBalance);
                //保余额汇款
                balance = balance.add(chaVal);
                eaBalanceRemittance.setClaimStatus(TransactionStatusEnum.CLAIMED.getKey());
                EaBalanceTransaction eaBalanceTransaction = new EaBalanceTransaction();
                BeanUtil.copyProperties(eaBalanceRemittance, eaBalanceTransaction);
                if (compare > 0) {
                    eaBalanceTransaction.setBillAmount(chaVal);
                    eaBalanceTransaction.setTransactionType("收入");
                } else {
                    eaBalanceTransaction.setBillAmount(BigDecimal.ZERO.subtract(chaVal));
                    eaBalanceTransaction.setTransactionType("支出");
                }
                eaBalanceTransaction.setBalance(balance);
                eaBalanceTransaction.setTransactionStatus(eaBalanceRemittance.getClaimStatus());
                StringBuilder transactionRemark = new StringBuilder();
                transactionRemark.append(userInfo.getRealName()).append("于").append(eaBalanceRemittance.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                        .append("进行").append(eaBalanceRemittance.getPaymentMethodName()).append("数据编辑，金额由 ")
                        .append(oldRemittance.getBillAmount()).append(" 变更为 ")
                        .append(eaBalanceRemittance.getBillAmount()).append("；");
                eaBalanceTransactionMapper.insertEaBalanceTransaction(eaBalanceTransaction);
            }
        }
        eaBalanceRemittanceMapper.updateEaBalanceRemittance(eaBalanceRemittance);
        return R.success();
    }

    /**
     * 删除余额汇款表
     *
     * @param transactionId 余额汇款表ID
     * @return 结果
     */
    @Override
    public int deleteEaBalanceRemittanceById(Long transactionId) {
        return eaBalanceRemittanceMapper.deleteEaBalanceRemittanceById(transactionId);
    }

    /**
     * 批量删除余额汇款表对象
     *
     * @param transactionIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEaBalanceRemittanceByIds(Integer[] transactionIds) {
        return eaBalanceRemittanceMapper.deleteEaBalanceRemittanceByIds(transactionIds);
    }

    /**
     * 认领/取消认领
     *
     * @param eaBalanceRemittance
     * @return R
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R claimTransactionList(EaBalanceRemittance eaBalanceRemittance) {
        if (eaBalanceRemittance == null || CollUtil.isEmpty(eaBalanceRemittance.getIdList())
                || StrUtil.isBlank(eaBalanceRemittance.getClaimStatus())) {
            return R.error("参数不全");
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        EaBalance eaBalance = new EaBalance();
        BigDecimal balance = BigDecimal.ZERO;
        BigDecimal billAmount = BigDecimal.ZERO;
        List<EaBalanceTransaction> transactionList = new ArrayList<>();
        for (long id : eaBalanceRemittance.getIdList()) {
            EaBalanceRemittance remittance = eaBalanceRemittanceMapper.selectEaBalanceRemittanceById(id);
            if (remittance == null) {
                return R.error("该汇款不存在");
            }
            if (TransactionStatusEnum.VOID.getKey().equals(remittance.getClaimStatus())) {
                return R.error("该汇款已作废");
            }
            if (eaBalanceRemittance.getClaimStatus().equals(remittance.getClaimStatus())) {
                if (TransactionStatusEnum.CLAIMED.getKey().equals(remittance.getClaimStatus())) {
                    return R.error("该汇款已认领,不可重复认领");
                }
                if (TransactionStatusEnum.UNCLAIMED.getKey().equals(remittance.getClaimStatus())) {
                    return R.error("该汇款未认领");
                }
            }
            if (balance.compareTo(BigDecimal.ZERO) == 0) {
                eaBalance = eaBalanceMapper.selectEaBalanceById(remittance.getBalanceId());
                balance = eaBalance.getAvailableBalance();
            }
            if (eaBalance.getPayerCode().startsWith("MC") && eaBalance.getPayeeCode().startsWith("MP")) {
                if (TransactionStatusEnum.CLAIMED.getKey().equals(remittance.getClaimStatus())
                        && TransactionStatusEnum.VOID.getKey().equals(eaBalanceRemittance.getClaimStatus())) {
                    return R.error("该汇款已认领，不可作废");
                }
            }
            if (TransactionStatusEnum.CLAIMED.getKey().equals(remittance.getClaimStatus()) &&
                    TransactionStatusEnum.VOID.getKey().equals(eaBalanceRemittance.getClaimStatus())) {
                billAmount = billAmount.add(remittance.getBillAmount());
                setTransactionList(remittance, transactionList, userInfo, "支出", balance, eaBalanceRemittance.getClaimStatus());
            }
            if (TransactionStatusEnum.CLAIMED.getKey().equals(remittance.getClaimStatus()) &&
                    TransactionStatusEnum.UNCLAIMED.getKey().equals(eaBalanceRemittance.getClaimStatus())) {
                billAmount = billAmount.add(remittance.getBillAmount());
                setTransactionList(remittance, transactionList, userInfo, "支出", balance, eaBalanceRemittance.getClaimStatus());
            }
            if (TransactionStatusEnum.CLAIMED.getKey().equals(eaBalanceRemittance.getClaimStatus())) {
                billAmount = billAmount.subtract(remittance.getBillAmount());
                setTransactionList(remittance, transactionList, userInfo, "收入", balance, eaBalanceRemittance.getClaimStatus());
            }
        }

        if (TransactionStatusEnum.UNCLAIMED.getKey().equals(eaBalanceRemittance.getClaimStatus())) {
            if (billAmount.compareTo(eaBalance.getAvailableBalance()) > 0) {
                return R.error("余额不足,不能取消认领");
            }
        }
        if (TransactionStatusEnum.VOID.getKey().equals(eaBalanceRemittance.getClaimStatus())) {
            if (billAmount.compareTo(eaBalance.getAvailableBalance()) > 0) {
                return R.error("余额不足,不能作废");
            }
        }
        if (billAmount.compareTo(BigDecimal.ZERO) != 0) {
            eaBalance.setTotalIncome(eaBalance.getTotalIncome().subtract(billAmount));
            eaBalanceMapper.updateEaBalance(eaBalance);
        }
        eaBalanceRemittance.setUpdateBy(userInfo.getRealName());
        eaBalanceRemittance.setUpdateById(userInfo.getId());
        eaBalanceRemittance.setClaimName(userInfo.getRealName());
        eaBalanceRemittance.setClaimTime(LocalDateTime.now());
        eaBalanceRemittanceMapper.claimTransactionList(eaBalanceRemittance);

        if (transactionList.size() > 0) {
            int step = 50;
            for (int i = 0; i < transactionList.size(); i += step) {
                eaBalanceTransactionMapper.insertEaBalanceTransactionList(transactionList.subList(i, (i + step) < transactionList.size() ? (i + step) : transactionList.size()));
            }
        }
        return R.success();
    }

    private void setTransactionList(EaBalanceRemittance remittance,
                                    List<EaBalanceTransaction> transactionList, SecruityUser userInfo, String type, BigDecimal
                                            balance, String claimStatus) {

        EaBalanceTransaction transaction = new EaBalanceTransaction();
        BeanUtil.copyProperties(remittance, transaction);
        transaction.setTransactionType(type);
        if ("收入".equals(type)) {
            transaction.setBalance(balance.add(transaction.getBillAmount()));
        } else {
            transaction.setBalance(balance.subtract(transaction.getBillAmount()));
        }
        transaction.setTransactionStatus(TransactionStatusEnum.CLAIMED.getKey());
        transaction.setOperationTime(LocalDateTime.now());
        transaction.setOperatorName(userInfo.getRealName());
        transaction.setCreateBy(userInfo.getRealName());
        transaction.setCreateById(userInfo.getId());
        transaction.setCreateTime(LocalDateTime.now());
        StringBuilder remark = new StringBuilder();
        remark.append(userInfo.getRealName()).append("于").append(transaction.getOperationTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .append("进行");
        if (TransactionStatusEnum.CLAIMED.getKey().equals(claimStatus)) {
            remark.append("认领");
        } else if (TransactionStatusEnum.UNCLAIMED.getKey().equals(claimStatus)) {
            remark.append("取消认领");
        } else {
            remark.append("作废");
        }
        remark.append(transaction.getPaymentMethodName()).append(transaction.getTransactionType()).append(transaction.getBillAmount()).append("；");

        transaction.setRemark(remark.toString());
        transactionList.add(transaction);
    }

}
