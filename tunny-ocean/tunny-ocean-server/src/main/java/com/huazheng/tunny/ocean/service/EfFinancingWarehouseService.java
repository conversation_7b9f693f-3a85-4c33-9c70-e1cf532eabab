package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.EfFinancingWarehouse;

import java.util.List;

/**
 * 仓单融资仓单表 服务接口层
 *
 * <AUTHOR>
 * @date 2023-02-06 14:10:39
 */
public interface EfFinancingWarehouseService extends IService<EfFinancingWarehouse> {
    /**
     * 查询仓单融资仓单表信息
     *
     * @param rowId 仓单融资仓单表ID
     * @return 仓单融资仓单表信息
     */
    public EfFinancingWarehouse selectEfFinancingWarehouseById(String rowId);

    /**
     * 查询仓单融资仓单表列表
     *
     * @param efFinancingWarehouse 仓单融资仓单表信息
     * @return 仓单融资仓单表集合
     */
    public List<EfFinancingWarehouse> selectEfFinancingWarehouseList(EfFinancingWarehouse efFinancingWarehouse);


    /**
     * 分页模糊查询仓单融资仓单表列表
     * @return 仓单融资仓单表集合
     */
    public Page selectEfFinancingWarehouseListByLike(Query query);



    /**
     * 新增仓单融资仓单表
     *
     * @param efFinancingWarehouse 仓单融资仓单表信息
     * @return 结果
     */
//    public int insertEfFinancingWarehouse(EfFinancingWarehouse efFinancingWarehouse);

//    public R insertList(List<EfFinancingWarehouse> list);
    /**
     * 修改仓单融资仓单表
     *
     * @param efFinancingWarehouse 仓单融资仓单表信息
     * @return 结果
     */
    public int updateEfFinancingWarehouse(EfFinancingWarehouse efFinancingWarehouse);

    /**
     * 删除仓单融资仓单表
     *
     * @param rowId 仓单融资仓单表ID
     * @return 结果
     */
    public int deleteEfFinancingWarehouseById(String rowId);

    /**
     * 批量删除仓单融资仓单表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEfFinancingWarehouseByIds(Integer[] rowIds);

}

