package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.PlatformCheck;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 平台校验 服务接口层
 *
 * <AUTHOR>
 * @date 2024-10-24 09:56:16
 */
public interface PlatformCheckService extends IService<PlatformCheck> {
    /**
     * 查询平台校验信息
     *
     * @param id 平台校验ID
     * @return 平台校验信息
     */
    public PlatformCheck selectPlatformCheckById(Integer id);

    /**
     * 查询平台校验列表
     *
     * @param platformCheck 平台校验信息
     * @return 平台校验集合
     */
    public List<PlatformCheck> selectPlatformCheckList(PlatformCheck platformCheck);


    /**
     * 分页模糊查询平台校验列表
     * @return 平台校验集合
     */
    public Page selectPlatformCheckListByLike(Query query);



    /**
     * 新增平台校验
     *
     * @param platformCheck 平台校验信息
     * @return 结果
     */
    public int insertPlatformCheck(PlatformCheck platformCheck);

    /**
     * 修改平台校验
     *
     * @param platformCheck 平台校验信息
     * @return 结果
     */
    public int updatePlatformCheck(PlatformCheck platformCheck);

    /**
     * 删除平台校验
     *
     * @param id 平台校验ID
     * @return 结果
     */
    public int deletePlatformCheckById(Integer id);

    /**
     * 批量删除平台校验
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deletePlatformCheckByIds(Integer[] ids);

    public String getPlatformCode(String checkType);
}

