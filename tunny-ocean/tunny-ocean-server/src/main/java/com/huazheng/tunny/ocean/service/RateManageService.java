package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.RateManage;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 汇率设置表（省平台维护） 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-08-09 13:56:48
 */
public interface RateManageService extends IService<RateManage> {
    /**
     * 查询汇率设置表（省平台维护）信息
     *
     * @param rowId 汇率设置表（省平台维护）ID
     * @return 汇率设置表（省平台维护）信息
     */
    public RateManage selectRateManageById(String rowId);

    /**
     * 查询汇率设置表（省平台维护）列表
     *
     * @param rateManage 汇率设置表（省平台维护）信息
     * @return 汇率设置表（省平台维护）集合
     */
    public List<RateManage> selectRateManageList(RateManage rateManage);


    /**
     * 分页模糊查询汇率设置表（省平台维护）列表
     * @return 汇率设置表（省平台维护）集合
     */
    public Page selectRateManageListByLike(Query query);



    /**
     * 新增汇率设置表（省平台维护）
     *
     * @param rateManage 汇率设置表（省平台维护）信息
     * @return 结果
     */
    public int insertRateManage(RateManage rateManage);

    /**
     * 修改汇率设置表（省平台维护）
     *
     * @param rateManage 汇率设置表（省平台维护）信息
     * @return 结果
     */
    public int updateRateManage(RateManage rateManage);

    /**
     * 删除汇率设置表（省平台维护）
     *
     * @param rateManage 汇率设置表（省平台维护）ID
     * @return 结果
     */
    public int deleteRateManageById(RateManage rateManage);

    /**
     * 批量删除汇率设置表（省平台维护）
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteRateManageByIds(Integer[] rowIds);

}

