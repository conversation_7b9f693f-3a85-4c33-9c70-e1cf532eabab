package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.StoredCompareConvertMapper;
import com.huazheng.tunny.ocean.api.entity.StoredCompareConvert;
import com.huazheng.tunny.ocean.service.StoredCompareConvertService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service("storedCompareConvertService")
public class StoredCompareConvertServiceImpl extends ServiceImpl<StoredCompareConvertMapper, StoredCompareConvert> implements StoredCompareConvertService {

    @Autowired
    private StoredCompareConvertMapper storedCompareConvertMapper;

    /**
     * 查询省平台-数据同比（进出口、过境、回程）信息
     *
     * @param rowId 省平台-数据同比（进出口、过境、回程）ID
     * @return 省平台-数据同比（进出口、过境、回程）信息
     */
    @Override
    public StoredCompareConvert selectStoredCompareConvertById(Integer rowId)
    {
        return storedCompareConvertMapper.selectStoredCompareConvertById(rowId);
    }

    /**
     * 查询省平台-数据同比（进出口、过境、回程）列表
     *
     * @param storedCompareConvert 省平台-数据同比（进出口、过境、回程）信息
     * @return 省平台-数据同比（进出口、过境、回程）集合
     */
    @Override
    public List<StoredCompareConvert> selectStoredCompareConvertList(StoredCompareConvert storedCompareConvert)
    {
        if(storedCompareConvert!=null && (storedCompareConvert.getDate() ==null || "".equals(storedCompareConvert.getDate()))){
            String date= LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
            storedCompareConvert.setDate(date);
        }
        String year = storedCompareConvert.getDate().split("-")[0];
        StoredCompareConvert scc = new StoredCompareConvert();
        scc.setYear(year);

        List<StoredCompareConvert> allList = new ArrayList<>();
        int month = Integer.parseInt(storedCompareConvert.getDate().split("-")[1]);
        for(int i = 1; i <= month; i++){
            String date = "";
            if(i > 9){
                date = year + "-" + i;
            }else{
                date = year + "-0" + i;
            }
            scc.setDate(date);
            List<StoredCompareConvert> list = storedCompareConvertMapper.selectStoredCompareConvertList(scc);
            if(list != null && list.size() > 0){
                for (StoredCompareConvert s:list
                     ) {
                    allList.add(s);
                }
            }
        }
        return allList;
    }


    /**
     * 分页模糊查询省平台-数据同比（进出口、过境、回程）列表
     * @return 省平台-数据同比（进出口、过境、回程）集合
     */
    @Override
    public Page selectStoredCompareConvertListByLike(Query query)
    {
        StoredCompareConvert storedCompareConvert =  BeanUtil.mapToBean(query.getCondition(), StoredCompareConvert.class,false);
        query.setRecords(storedCompareConvertMapper.selectStoredCompareConvertListByLike(query,storedCompareConvert));
        return query;
    }

    /**
     * 新增省平台-数据同比（进出口、过境、回程）
     *
     * @param storedCompareConvert 省平台-数据同比（进出口、过境、回程）信息
     * @return 结果
     */
    @Override
    public int insertStoredCompareConvert(StoredCompareConvert storedCompareConvert)
    {
        return storedCompareConvertMapper.insertStoredCompareConvert(storedCompareConvert);
    }

    /**
     * 修改省平台-数据同比（进出口、过境、回程）
     *
     * @param storedCompareConvert 省平台-数据同比（进出口、过境、回程）信息
     * @return 结果
     */
    @Override
    public int updateStoredCompareConvert(StoredCompareConvert storedCompareConvert)
    {
        return storedCompareConvertMapper.updateStoredCompareConvert(storedCompareConvert);
    }


    /**
     * 删除省平台-数据同比（进出口、过境、回程）
     *
     * @param rowId 省平台-数据同比（进出口、过境、回程）ID
     * @return 结果
     */
    public int deleteStoredCompareConvertById(Integer rowId)
    {
        return storedCompareConvertMapper.deleteStoredCompareConvertById( rowId);
    };


    /**
     * 批量删除省平台-数据同比（进出口、过境、回程）对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteStoredCompareConvertByIds(Integer[] rowIds)
    {
        return storedCompareConvertMapper.deleteStoredCompareConvertByIds( rowIds);
    }

}
