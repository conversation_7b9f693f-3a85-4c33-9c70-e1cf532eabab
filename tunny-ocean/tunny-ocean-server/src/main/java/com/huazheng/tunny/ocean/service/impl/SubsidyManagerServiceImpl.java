package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.SubsidyManager;
import com.huazheng.tunny.ocean.mapper.SubsidyManagerMapper;
import com.huazheng.tunny.ocean.service.SubsidyManagerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service("subsidyManagerService")
public class SubsidyManagerServiceImpl extends ServiceImpl<SubsidyManagerMapper, SubsidyManager> implements SubsidyManagerService {

    @Autowired
    private SubsidyManagerMapper subsidyManagerMapper;

    /**
     * 查询补贴管理表信息
     *
     * @param id 补贴管理表ID
     * @return 补贴管理表信息
     */
    @Override
    public SubsidyManager selectSubsidyManagerById(Integer id)
    {
        return subsidyManagerMapper.selectSubsidyManagerById(id);
    }

    /**
     * 查询补贴管理表列表
     *
     * @param subsidyManager 补贴管理表信息
     * @return 补贴管理表集合
     */
    @Override
    public List<SubsidyManager> selectSubsidyManagerList(SubsidyManager subsidyManager)
    {
        return subsidyManagerMapper.selectSubsidyManagerList(subsidyManager);
    }


    /**
     * 分页模糊查询补贴管理表列表
     * @return 补贴管理表集合
     */
    @Override
    public Page selectSubsidyManagerListByLike(Query query)
    {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.create_time");
            query.setAsc(Boolean.FALSE);
        }
        SubsidyManager subsidyManager =  BeanUtil.mapToBean(query.getCondition(), SubsidyManager.class,false);
        query.setRecords(subsidyManagerMapper.selectSubsidyManagerListByLike(query,subsidyManager));
        return query;
    }

    /**
     * 新增补贴管理表
     *
     * @param subsidyManager 补贴管理表信息
     * @return 结果
     */
    @Override
    public R insertSubsidyManager(SubsidyManager subsidyManager)
    {
        R r = new R();
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        String username = userInfo.getUserName();
        String platformCode = userInfo.getPlatformCode();
        String platformLevel = userInfo.getPlatformLevel();

        subsidyManager.setCreateBy(username);
        subsidyManager.setCreateName(userInfo.getRealName());
        subsidyManager.setCreateTime(new Date());

        subsidyManager.setPlatformCode(platformCode);
        subsidyManager.setPlatformLevel(platformLevel);

        // 存储数据前进行
        Integer integer = subsidyManagerMapper.selectSubsidySaveHistory(subsidyManager);
        if(integer>0){
            r.setCode(500);
            r.setMsg("当前时间段已存在数据");
            return r;
        }
        int i = subsidyManagerMapper.insertSubsidyManager(subsidyManager);
        if(i<=0){
            r.setCode(500);
            r.setMsg("存储失败");
        }
        return r;
    }

    /**
     * 修改补贴管理表
     *
     * @param subsidyManager 补贴管理表信息
     * @return 结果
     */
    @Override
    public R updateSubsidyManager(SubsidyManager subsidyManager)
    {
        R r = new R();

        // 存储数据前进行拆线
        Integer integer = subsidyManagerMapper.selectSubsidySaveHistory(subsidyManager);
        if(integer>0){
            r.setCode(500);
            r.setMsg("当前时间段已存在数据");
            return r;
        }
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        subsidyManager.setUpdateBy(userInfo.getUserName());
        subsidyManager.setUpdateName(userInfo.getRealName());
        subsidyManager.setUpdateTime(new Date());

        int i = subsidyManagerMapper.updateSubsidyManager(subsidyManager);
        if(i<=0){
            r.setMsg("更新失败");
            r.setCode(500);
        }
        return r;
    }


    /**
     * 删除补贴管理表
     *
     * @param id 补贴管理表ID
     * @return 结果
     */
    public int deleteSubsidyManagerById(Integer id)
    {
        return subsidyManagerMapper.deleteSubsidyManagerById( id);
    };


    /**
     * 批量删除补贴管理表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSubsidyManagerByIds(Integer[] ids)
    {
        return subsidyManagerMapper.deleteSubsidyManagerByIds( ids);
    }

}
