package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.FdBusCostDetailHisMapper;
import com.huazheng.tunny.ocean.api.entity.FdBusCostDetailHis;
import com.huazheng.tunny.ocean.service.FdBusCostDetailHisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("fdBusCostDetailHisService")
public class FdBusCostDetailHisServiceImpl extends ServiceImpl<FdBusCostDetailHisMapper, FdBusCostDetailHis> implements FdBusCostDetailHisService {

    @Autowired
    private FdBusCostDetailHisMapper fdBusCostDetailHisMapper;

    /**
     * 查询业务流程单历史表信息
     *
     * @param id 业务流程单历史表ID
     * @return 业务流程单历史表信息
     */
    @Override
    public FdBusCostDetailHis selectFdBusCostDetailHisById(Integer id)
    {
        return fdBusCostDetailHisMapper.selectFdBusCostDetailHisById(id);
    }

    /**
     * 查询业务流程单历史表列表
     *
     * @param fdBusCostDetailHis 业务流程单历史表信息
     * @return 业务流程单历史表集合
     */
    @Override
    public List<FdBusCostDetailHis> selectFdBusCostDetailHisList(FdBusCostDetailHis fdBusCostDetailHis)
    {
        return fdBusCostDetailHisMapper.selectFdBusCostDetailHisList(fdBusCostDetailHis);
    }


    /**
     * 分页模糊查询业务流程单历史表列表
     * @return 业务流程单历史表集合
     */
    @Override
    public Page selectFdBusCostDetailHisListByLike(Query query)
    {
        FdBusCostDetailHis fdBusCostDetailHis =  BeanUtil.mapToBean(query.getCondition(), FdBusCostDetailHis.class,false);
        query.setRecords(fdBusCostDetailHisMapper.selectFdBusCostDetailHisListByLike(query,fdBusCostDetailHis));
        return query;
    }

    /**
     * 新增业务流程单历史表
     *
     * @param fdBusCostDetailHis 业务流程单历史表信息
     * @return 结果
     */
    @Override
    public int insertFdBusCostDetailHis(FdBusCostDetailHis fdBusCostDetailHis)
    {
        return fdBusCostDetailHisMapper.insertFdBusCostDetailHis(fdBusCostDetailHis);
    }

    /**
     * 修改业务流程单历史表
     *
     * @param fdBusCostDetailHis 业务流程单历史表信息
     * @return 结果
     */
    @Override
    public int updateFdBusCostDetailHis(FdBusCostDetailHis fdBusCostDetailHis)
    {
        return fdBusCostDetailHisMapper.updateFdBusCostDetailHis(fdBusCostDetailHis);
    }


    /**
     * 删除业务流程单历史表
     *
     * @param id 业务流程单历史表ID
     * @return 结果
     */
    public int deleteFdBusCostDetailHisById(Integer id)
    {
        return fdBusCostDetailHisMapper.deleteFdBusCostDetailHisById( id);
    };


    /**
     * 批量删除业务流程单历史表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFdBusCostDetailHisByIds(Integer[] ids)
    {
        return fdBusCostDetailHisMapper.deleteFdBusCostDetailHisByIds( ids);
    }

}
