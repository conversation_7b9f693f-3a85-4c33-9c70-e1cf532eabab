package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.StationPlatformRelation;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 站点平台关系表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-13 17:44:15
 */
public interface StationPlatformRelationService extends IService<StationPlatformRelation> {

    /**
     * 查询站点pingtai 平台管理信息
     *
     * @param rowId 站点线路管理ID
     * @return 站点线路管理信息
     */
    public List<StationPlatformRelation> selectPlatByStationCode(String rowId);
    /**
     * 查询站点平台关系表信息
     *
     * @param rowId 站点平台关系表ID
     * @return 站点平台关系表信息
     */
    public StationPlatformRelation selectStationPlatformRelationById(String rowId);

    /**
     * 查询站点平台关系表列表
     *
     * @param stationPlatformRelation 站点平台关系表信息
     * @return 站点平台关系表集合
     */
    public List<StationPlatformRelation> selectStationPlatformRelationList(StationPlatformRelation stationPlatformRelation);


    /**
     * 分页模糊查询站点平台关系表列表
     * @return 站点平台关系表集合
     */
    public Page selectStationPlatformRelationListByLike(Query query);



    /**
     * 新增站点平台关系表
     *
     * @param stationPlatformRelation 站点平台关系表信息
     * @return 结果
     */
    public int insertStationPlatformRelation(List<StationPlatformRelation> stationPlatformRelation);

    /**
     * 修改站点平台关系表
     *
     * @param stationPlatformRelation 站点平台关系表信息
     * @return 结果
     */
    public int updateStationPlatformRelation(List<StationPlatformRelation> stationPlatformRelation);

    /**
     * 删除站点平台关系表
     *
     * @param rowId 站点平台关系表ID
     * @return 结果
     */
    public int deleteStationPlatformRelationById(String rowId);

    /**
     * 批量删除站点平台关系表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteStationPlatformRelationByIds(Integer[] rowIds);

}

