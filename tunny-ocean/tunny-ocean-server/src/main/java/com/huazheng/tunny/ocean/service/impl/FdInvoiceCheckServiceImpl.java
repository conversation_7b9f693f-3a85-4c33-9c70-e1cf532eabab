package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.dto.InvoiceStatisticsDTO;
import com.huazheng.tunny.ocean.api.entity.FdBusCostDetail;
import com.huazheng.tunny.ocean.api.entity.FdInvoiceCheck;
import com.huazheng.tunny.ocean.api.entity.FdInvoiceCheckHistory;
import com.huazheng.tunny.ocean.api.enums.InvoiceCheckEnum;
import com.huazheng.tunny.ocean.api.vo.InvoiceStatisticsDetailListVO;
import com.huazheng.tunny.ocean.api.vo.InvoiceStatisticsVO;
import com.huazheng.tunny.ocean.api.vo.SelectInvoiceStatisticsCostListVO;
import com.huazheng.tunny.ocean.mapper.FdBusCostDetailMapper;
import com.huazheng.tunny.ocean.mapper.FdInvoiceCheckMapper;
import com.huazheng.tunny.ocean.service.FdInvoiceCheckHistoryService;
import com.huazheng.tunny.ocean.service.FdInvoiceCheckService;
import com.huazheng.tunny.ocean.util.ExportStyleUtil;
import com.huazheng.tunny.tools.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("fdInvoiceCheckService")
public class FdInvoiceCheckServiceImpl extends ServiceImpl<FdInvoiceCheckMapper, FdInvoiceCheck> implements FdInvoiceCheckService {

    @Autowired
    private FdInvoiceCheckMapper fdInvoiceCheckMapper;

    @Autowired
    private FdInvoiceCheckHistoryService fdInvoiceCheckHistoryService;
    @Autowired
    private FdBusCostDetailMapper fdBusCostDetailMapper;

    /**
     * 查询信息
     *
     * @param id ID
     * @return 信息
     */
    @Override
    public FdInvoiceCheck selectFdInvoiceCheckById(Integer id) {
        return fdInvoiceCheckMapper.selectFdInvoiceCheckById(id);
    }

    /**
     * 查询列表
     *
     * @param fdInvoiceCheck 信息
     * @return 集合
     */
    @Override
    public List<FdInvoiceCheck> selectFdInvoiceCheckList(FdInvoiceCheck fdInvoiceCheck) {
        return fdInvoiceCheckMapper.selectFdInvoiceCheckList(fdInvoiceCheck);
    }

    /**
     * 发票核对接口
     *
     * @param map
     * @return
     */
    @Override
    public List<FdInvoiceCheck> selectFdInvoiceCheckListNotPage(Map<String, Object> map) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        map.put("createBy", userInfo.getUserName());
        FdInvoiceCheck fdInvoiceCheck = BeanUtil.mapToBean(map, FdInvoiceCheck.class, false);
        List<FdInvoiceCheck> fdInvoiceChecks = fdInvoiceCheckMapper.selectFdInvoiceCheckListNotPage(fdInvoiceCheck);
        if (CollUtil.isNotEmpty(fdInvoiceChecks)) {
            String provinceShiftNo = fdInvoiceChecks.get(0).getProvinceShiftNo();
            List<FdBusCostDetail> detailList = fdBusCostDetailMapper.selectLocalAmountByProvinceShiftNo(provinceShiftNo);
            for (FdInvoiceCheck check : fdInvoiceChecks
            ) {
                check.setCityAmount(BigDecimal.ZERO);
                if (!provinceShiftNo.equals(check.getProvinceShiftNo())) {
                    provinceShiftNo = check.getProvinceShiftNo();
                    detailList = fdBusCostDetailMapper.selectLocalAmountByProvinceShiftNo(provinceShiftNo);
                }
                // 使用 Stream 查找匹配的 containerNumber
                if (CollUtil.isNotEmpty(detailList)) {
                    detailList.stream()
                            .filter(detail -> check.getContainerNumber().equals(detail.getContainerNumber()))
                            .findFirst()
                            .ifPresent(detail -> check.setCityAmount(detail.getLocalAmount()));
                }
            }
        }
        List<FdInvoiceCheck> re = new ArrayList<>();
        Map<String, List<FdInvoiceCheck>> fdMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(fdInvoiceChecks)) {
            for (FdInvoiceCheck fd : fdInvoiceChecks) {
                List<FdInvoiceCheck> fdInvoiceChecks1 = fdMap.get(fd.getProvinceShiftNo());
                if (CollectionUtil.isNotEmpty(fdInvoiceChecks1)) {
                    fdInvoiceChecks1.add(fd);
                    fdMap.put(fd.getProvinceShiftNo(), fdInvoiceChecks1);
                } else {
                    fdInvoiceChecks1 = new ArrayList<>();
                    fdInvoiceChecks1.add(fd);
                    fdMap.put(fd.getProvinceShiftNo(), fdInvoiceChecks1);
                }
            }

            for (String key : fdMap.keySet()) {
                List<FdInvoiceCheck> fdInvoiceChecks1 = fdMap.get(key);
                FdInvoiceCheck cc = new FdInvoiceCheck();
                FdInvoiceCheck check = fdInvoiceChecks1.get(0);
                cc.setId(check.getId());
                cc.setProvinceShiftNo(check.getProvinceShiftNo());
                cc.setContainerNumber(check.getContainerNumber());
                cc.setContainerType(check.getContainerType());
                cc.setPortStation(check.getPortStation());
                cc.setDestination(check.getDestination());
                cc.setChurchyardAmount(check.getChurchyardAmount());
                cc.setAbroadAmount(check.getAbroadAmount());
                cc.setCheckStatus(check.getCheckStatus());
                cc.setDeleteFlag(check.getDeleteFlag());
                cc.setCreateBy(check.getCreateBy());
                cc.setCreateTime(check.getCreateTime());
                cc.setPlatformCode(check.getPlatformCode());
                cc.setPlatformLevel(check.getPlatformLevel());
                cc.setDlAmount(check.getDlAmount());
                cc.setCityAmount(check.getCityAmount());
                fdInvoiceChecks1.remove(check);
                cc.setList(fdInvoiceChecks1);
                re.add(cc);
            }
        }
        return re;
    }


    /**
     * 分页模糊查询列表
     *
     * @return 集合
     */
    @Override
    public Page selectFdInvoiceCheckListByLike(Query query) {
        FdInvoiceCheck fdInvoiceCheck = BeanUtil.mapToBean(query.getCondition(), FdInvoiceCheck.class, false);
        query.setRecords(fdInvoiceCheckMapper.selectFdInvoiceCheckListByLike(query, fdInvoiceCheck));
        return query;
    }

    /**
     * 新增
     *
     * @param fdInvoiceCheck 信息
     * @return 结果
     */
    @Override
    public int insertFdInvoiceCheck(FdInvoiceCheck fdInvoiceCheck) {
        return fdInvoiceCheckMapper.insertFdInvoiceCheck(fdInvoiceCheck);
    }

    /**
     * 修改
     *
     * @param fdInvoiceCheck 信息
     * @return 结果
     */
    @Override
    public int updateFdInvoiceCheck(FdInvoiceCheck fdInvoiceCheck) {
        return fdInvoiceCheckMapper.updateFdInvoiceCheck(fdInvoiceCheck);
    }


    /**
     * 删除
     *
     * @param id ID
     * @return 结果
     */
    public int deleteFdInvoiceCheckById(Integer id) {
        return fdInvoiceCheckMapper.deleteFdInvoiceCheckById(id);
    }

    ;


    /**
     * 批量删除对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFdInvoiceCheckByIds(Integer[] ids) {
        return fdInvoiceCheckMapper.deleteFdInvoiceCheckByIds(ids);
    }

    /**
     * 核对信息
     *
     * @param list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean selectCheckByList(List<FdInvoiceCheck> list, SecruityUser userInfo) {
        try {
            Set<String> provinceShiftSet = list.stream().map(FdInvoiceCheck::getProvinceShiftNo).collect(Collectors.toSet());
            List<FdInvoiceCheck> fdInvoiceChecks = fdInvoiceCheckMapper.selectCheckByList(provinceShiftSet);
            if (CollectionUtil.isNotEmpty(fdInvoiceChecks)) {
                Map<String, List<String>> checkMap = new HashMap<>();
                for (FdInvoiceCheck fdInvoiceCheck : fdInvoiceChecks) {
                    String provinceShiftNo = fdInvoiceCheck.getProvinceShiftNo();
                    List<String> strings = checkMap.get(provinceShiftNo);
                    if (CollectionUtil.isNotEmpty(strings)) {
                        strings.add(fdInvoiceCheck.getContainerNumber());
                        checkMap.put(provinceShiftNo, strings);
                    } else {
                        strings = new ArrayList<>();
                        strings.add(fdInvoiceCheck.getContainerNumber());
                        checkMap.put(provinceShiftNo, strings);
                    }
                }
                // 比对数据
                for (FdInvoiceCheck fdInvoiceCheck : list) {
                    List<String> strings = checkMap.get(fdInvoiceCheck.getProvinceShiftNo());
                    if (CollectionUtil.isNotEmpty(strings)) {
                        if (strings.contains(fdInvoiceCheck.getContainerNumber())) {
                            fdInvoiceCheck.setCheckStatus(InvoiceCheckEnum.NOT_CHECK.getKey());
                        } else {
                            fdInvoiceCheck.setCheckStatus(InvoiceCheckEnum.NOT_INFO.getKey());
                        }
                    } else {
                        fdInvoiceCheck.setCheckStatus(InvoiceCheckEnum.NOT_INFO.getKey());
                    }
                }

            } else {
                list.stream().forEach(fdInvoiceCheck -> fdInvoiceCheck.setCheckStatus(InvoiceCheckEnum.NOT_INFO.getKey()));
            }

            // 删除历史导入数据
            fdInvoiceCheckMapper.updateDelByPlatformCode(userInfo.getUserName(), userInfo.getPlatformCode());
            // 保存最新的导入数据
            this.insertBatch(list);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 批量存储核对历史数据
     *
     * @param fdInvoiceChecks
     */
    @Override
    public void insertInvoiceCheckBatch(List<FdInvoiceCheck> fdInvoiceChecks) {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        List<FdInvoiceCheckHistory> histories = new ArrayList<>();
        List<FdInvoiceCheckHistory> updateHistory = new ArrayList<>();
        for (FdInvoiceCheck fdInvoiceCheck : fdInvoiceChecks) {
            // 查询历史数据
            FdInvoiceCheckHistory invoiceHistoryMysql = fdInvoiceCheckMapper.selectInvoiceHistoryByProvinceShiftNoAndBoxNum(fdInvoiceCheck.getProvinceShiftNo(), fdInvoiceCheck.getContainerNumber());
            if (ObjectUtils.isEmpty(invoiceHistoryMysql)) {
                FdInvoiceCheckHistory fdInvoiceCheckHistory = new FdInvoiceCheckHistory();
                fdInvoiceCheck.setCheckStatus(InvoiceCheckEnum.NOT_APPLY_CHECK.getKey());
                fdInvoiceCheck.setUpdateBy(userInfo.getUserName());
                fdInvoiceCheck.setUpdateTime(new Date());
                BeanUtil.copyProperties(fdInvoiceCheck, fdInvoiceCheckHistory);
                fdInvoiceCheckHistory.setId(null);
                fdInvoiceCheckHistory.setCheckStatus(InvoiceCheckEnum.NOT_APPLY_CHECK.getKey());
                fdInvoiceCheckHistory.setCreateTime(new Date());
                fdInvoiceCheckHistory.setPlatformCode(userInfo.getPlatformCode());
                fdInvoiceCheckHistory.setPlatformLevel(userInfo.getPlatformLevel());
                histories.add(fdInvoiceCheckHistory);
            } else {
                if (invoiceHistoryMysql.getChurchyardAmount() == null ||
                        invoiceHistoryMysql.getChurchyardAmount().compareTo(new BigDecimal(0)) == 0) {
                    invoiceHistoryMysql.setChurchyardAmount(fdInvoiceCheck.getChurchyardAmount());
                }
                if (invoiceHistoryMysql.getAbroadAmount() == null ||
                        invoiceHistoryMysql.getAbroadAmount().compareTo(new BigDecimal(0)) == 0) {
                    invoiceHistoryMysql.setAbroadAmount(fdInvoiceCheck.getAbroadAmount());
                }
                fdInvoiceCheck.setCheckStatus(InvoiceCheckEnum.NOT_APPLY_CHECK.getKey());
                updateHistory.add(invoiceHistoryMysql);
            }

        }
        this.updateBatchById(fdInvoiceChecks);
        if (CollectionUtil.isNotEmpty(histories)) {
            fdInvoiceCheckHistoryService.insertBatch(histories);
        }
        if (CollectionUtil.isNotEmpty(updateHistory)) {
            fdInvoiceCheckHistoryService.updateBatchById(updateHistory);
        }

    }

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    @Override
    public Page selectFdInvoiceCheckHistoryListByLike(Query query) {
        return fdInvoiceCheckHistoryService.selectFdInvoiceCheckHistoryListByLike(query);
    }

    /**
     * 发票统计接口
     */
    @Override
    public Page selectInvoiceStatistics(Query query) {
        if (StrUtil.isBlank(query.getOrderByField())) {
            query.setOrderByField("x.planShipTime");
            query.setAsc(Boolean.FALSE);
        }
        // 查询出所有的班次信息， 根据班次信息查询出每个班次下的箱信息
        InvoiceStatisticsDTO invoiceStatisticsDTO = BeanUtil.mapToBean(query.getCondition(), InvoiceStatisticsDTO.class, false);

        List<InvoiceStatisticsVO> invoiceStatisticsVOS = fdInvoiceCheckMapper.selectShiftInfo(query, invoiceStatisticsDTO);

        // 循环取车数和金额
        for (InvoiceStatisticsVO invoiceStatisticsVO : invoiceStatisticsVOS) {
            // 未开票
            InvoiceStatisticsVO not = fdInvoiceCheckMapper.selectInvoiceCheckStatisticsNotByShiftInfo(invoiceStatisticsVO.getPlatformCode(),
                    invoiceStatisticsVO.getPlanShipTime(),
                    invoiceStatisticsVO.getShippingLine(),
                    invoiceStatisticsVO.getTrip());
            if (!ObjectUtils.isEmpty(not)) {
                invoiceStatisticsVO.setNotKpAmount(not.getNotKpAmount());
                invoiceStatisticsVO.setNotKpCs(not.getNotKpCs());
            }
            // 已开票
            InvoiceStatisticsVO already = fdInvoiceCheckMapper.selectInvoiceCheckStatisticsAlreadyByShiftInfo(invoiceStatisticsVO.getPlatformCode(),
                    invoiceStatisticsVO.getPlanShipTime(),
                    invoiceStatisticsVO.getShippingLine(),
                    invoiceStatisticsVO.getTrip());
            if (!ObjectUtils.isEmpty(already)) {
                invoiceStatisticsVO.setAlreadyAmount(already.getAlreadyAmount());
                invoiceStatisticsVO.setAlreadyKpCs(already.getAlreadyKpCs());
            }
        }
        query.setRecords(invoiceStatisticsVOS);
        return query;
    }

    /**
     * 统计详情
     *
     * @param platformCode
     * @param planShipTime
     * @param shippingLine
     * @param trip
     * @return
     */
    @Override
    public List<InvoiceStatisticsDetailListVO> selectInvoiceStatisticsDetail(String platformCode, String planShipTime, String shippingLine, String trip, String provinceShiftNo) {

        // 查询出设计的所有班次

        List<InvoiceStatisticsDetailListVO> invoiceStatisticsVOS = fdInvoiceCheckMapper.selectShiftNoByShiftInfo(platformCode,
                planShipTime,
                shippingLine,
                trip,
                provinceShiftNo);

        if (CollectionUtil.isNotEmpty(invoiceStatisticsVOS)) {
            for (InvoiceStatisticsDetailListVO invoiceStatisticsDetailListVO : invoiceStatisticsVOS) {
                InvoiceStatisticsDetailListVO statisticsDetailListVO = fdInvoiceCheckMapper.selectInvoiceCheckStatisticsSumByShiftNo(invoiceStatisticsDetailListVO.getShiftNo());
                if (!ObjectUtils.isEmpty(statisticsDetailListVO)) {
                    invoiceStatisticsDetailListVO.setSumAmount(statisticsDetailListVO.getSumAmount());
                    invoiceStatisticsDetailListVO.setNotAmount(statisticsDetailListVO.getNotAmount());
                    invoiceStatisticsDetailListVO.setAlreadyAmount(statisticsDetailListVO.getAlreadyAmount());
                }
            }
        }

        return invoiceStatisticsVOS;
    }

    /**
     * 根据班次号获取已开票或者未开票的明细
     *
     * @param shiftNo
     * @param isInvoice
     * @return
     */
    @Override
    public List<SelectInvoiceStatisticsCostListVO> selectInvoiceStatisticsCostListVO(String shiftNo, Integer isInvoice) {
        return fdInvoiceCheckMapper.selectInvoiceStatisticsCostListVO(shiftNo, isInvoice);
    }


    /**
     * 导入模板
     *
     * @param file
     * @return
     * <AUTHOR>
     * @since 2025/5/19 15:13
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R importCostLedgerExcel(MultipartFile file) throws IOException {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Object> headerRow = reader.readRow(3);
        if (checkExcelTitle(headerRow, "costLedger")) {
            return R.error("导入失败，请检查模板是否正确");
        }
        //检查省级班列号
        List<Object> provinceShiftNoRow = reader.readRow(1);
        if (Objects.isNull(provinceShiftNoRow) || provinceShiftNoRow.isEmpty()) {
            return R.error("导入失败，请检查省级班列号是否正确");
        }
        String provinceShiftNo = provinceShiftNoRow.get(2).toString();
        if (StrUtil.isBlank(provinceShiftNo)) {
            return R.error("导入失败，请检查省级班列号是否正确");
        }

        List<List<Object>> dataRows = reader.read(4);

        // 将每行数据转成 Map（列名 => 值）
        List<Map<String, Object>> rowMapList = new ArrayList<>();
        for (List<Object> row : dataRows) {
            if (Objects.isNull(row) || row.isEmpty()) {
                continue;
            }
            if ("小计".equals(ExportStyleUtil.removeAllSpaces(row.get(0).toString())) ||
                    "合计".equals(ExportStyleUtil.removeAllSpaces(row.get(0).toString()))) {
                continue;
            }
            Map<String, Object> rowMap = getStringObjectMap(row, headerRow);
            rowMapList.add(rowMap);
        }
        List<FdInvoiceCheck> fdInvoiceChecks = new ArrayList<>();
        for (Map<String, Object> rowMap : rowMapList) {
            FdInvoiceCheck fdInvoiceCheck = new FdInvoiceCheck();
            fdInvoiceCheck.setProvinceShiftNo(provinceShiftNo);
            fdInvoiceCheck.setPortStation(rowMap.get("口岸").toString());
            fdInvoiceCheck.setContainerNumber(rowMap.get("箱号").toString());
            fdInvoiceCheck.setContainerType(rowMap.get("箱型").toString());
            fdInvoiceCheck.setChurchyardAmount(ExportStyleUtil.toBigDecimalSafe(rowMap.get("境内运费")));
            fdInvoiceCheck.setAbroadAmount(ExportStyleUtil.toBigDecimalSafe(rowMap.get("境外运费")));
            fdInvoiceCheck.setCreateBy(userInfo.getUserName());
            fdInvoiceCheck.setCreateTime(new Date());
            fdInvoiceCheck.setPlatformLevel(userInfo.getPlatformLevel());
            fdInvoiceCheck.setPlatformCode(userInfo.getPlatformCode());
            fdInvoiceChecks.add(fdInvoiceCheck);
        }
        Boolean b = selectCheckByList(fdInvoiceChecks, userInfo);
        if (!b) {
            return R.error("导入失败");
        }
        //更新到站字段
        FdInvoiceCheck fdInvoiceCheck = new FdInvoiceCheck();
        fdInvoiceCheck.setCreateBy(userInfo.getUserName());
        fdInvoiceCheckMapper.updateMissingFields(fdInvoiceCheck);
        return b ? R.success() : R.error("导入失败");
    }

    /**
     * @param row       明细数据
     * @param headerRow 标题数据
     * @return Map<String, Object>
     * <AUTHOR>
     * @since 2025/5/19 16:12
     **/

    public Map<String, Object> getStringObjectMap(List<Object> row, List<Object> headerRow) {
        Map<String, Object> rowMap = new LinkedHashMap<>();
        for (int i = 0; i < headerRow.size(); i++) {
            String key = String.valueOf(headerRow.get(i)).trim();
            Object value = i < row.size() ? row.get(i) : null;
            rowMap.put(key, value);
        }
        return rowMap;
    }


    /**
     * 校验标题
     *
     * @param headerRow 标题
     * @param excelType 模板类型
     * @return boolean
     * <AUTHOR>
     * @since 2025/5/19 17:07
     **/
    public boolean checkExcelTitle(List<Object> headerRow, String excelType) {

        //成本台账的标题
        List<String> costLedgerHeaders = Arrays.asList("序号", "国家", "口岸", "箱型", "箱属", "箱号", "境内运费", "境外运费");

        //中亚台账标题
        List<String> centralAsiaLedgerHeaders = Arrays.asList("序号", "日期", "客户", "发站", "箱号", "箱型", "箱属", "类型", "客户到站", "境内运费", "境外运费");

        List<String> expectedHeaders;
        if ("costLedger".equals(excelType)) {
            expectedHeaders = costLedgerHeaders;
        } else if ("centralAsiaLedger".equals(excelType)) {
            expectedHeaders = centralAsiaLedgerHeaders;
        } else {
            return true;
        }

        // 长度校验
        if (headerRow.size() != expectedHeaders.size()) {
            return true;
        }
        // 内容校验（不考虑顺序）
        for (String expected : expectedHeaders) {
            if (!headerRow.contains(expected)) {
                return true;
            }
        }
        return false;
    }


    /**
     * 导入中亚台账模板
     *
     * @param file excel文件
     * @return R
     * <AUTHOR>
     * @since 2025/5/19 16:30
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R importCentralAsiaLedgerExcel(MultipartFile file) throws Exception {
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        List<Object> headerRow = new ArrayList<>();
        List<List<Object>> allSheetData = new ArrayList<>();
        // 读取文件内容为 byte[]
        byte[] fileBytes = file.getBytes();
        // 创建 Workbook
        Workbook workbook = WorkbookFactory.create(new ByteArrayInputStream(fileBytes));

        int numberOfSheets = workbook.getNumberOfSheets();
        for (int i = 0; i < numberOfSheets; i++) {
            // 为每个 sheet 创建新的 ExcelReader
            ExcelReader reader = ExcelUtil.getReader(new ByteArrayInputStream(fileBytes), i);
            // 校验标题行
            headerRow = reader.readRow(1);
            if (checkExcelTitle(headerRow, "centralAsiaLedger")) {
                return R.error("第 " + (i + 1) + " 个 Sheet 模板格式不正确");
            }
            // 读取数据（假设从第 5 行开始是数据）
            List<List<Object>> rows = reader.read(2);

            List<List<Object>> dataRows = new ArrayList<>();
            //遍历数据，若是日清为空，则序号为省级班列号以下数据的省级班列号
            //省级班列号
            String provinceShiftNo = "";
            for (List<Object> row : rows) {
                if (CollUtil.isEmpty(row)) {
                    continue;
                }
                log.info(JSONUtil.toJsonStr(row));
                if (row.get(0) != null && (row.get(1) == null || StrUtil.isBlank(row.get(1).toString()))) {
                    provinceShiftNo = row.get(0).toString();
                } else {
                    row.add(provinceShiftNo);
                    allSheetData.add(row);
                }
            }

        }
        // 将每行数据转成 Map（列名 => 值）
        headerRow.add("省级班列号");
        List<Map<String, Object>> rowMapList = new ArrayList<>();
        for (List<Object> row : allSheetData) {
            Map<String, Object> rowMap = getStringObjectMap(row, headerRow);
            rowMapList.add(rowMap);
        }
        List<FdInvoiceCheck> fdInvoiceChecks = new ArrayList<>();
        for (Map<String, Object> rowMap : rowMapList) {
            FdInvoiceCheck fdInvoiceCheck = new FdInvoiceCheck();
            fdInvoiceCheck.setProvinceShiftNo(rowMap.get("省级班列号").toString());
            fdInvoiceCheck.setContainerNumber(rowMap.get("箱号").toString());
            fdInvoiceCheck.setContainerType(rowMap.get("箱型").toString());
            fdInvoiceCheck.setChurchyardAmount(ExportStyleUtil.toBigDecimalSafe(rowMap.get("境内运费")));
            fdInvoiceCheck.setAbroadAmount(ExportStyleUtil.toBigDecimalSafe(rowMap.get("境外运费")));
            fdInvoiceCheck.setCreateBy(userInfo.getUserName());
            fdInvoiceCheck.setCreateTime(new Date());
            fdInvoiceCheck.setPlatformLevel(userInfo.getPlatformLevel());
            fdInvoiceCheck.setPlatformCode(userInfo.getPlatformCode());
            fdInvoiceChecks.add(fdInvoiceCheck);
        }
        Boolean b = selectCheckByList(fdInvoiceChecks, userInfo);
        if (!b) {
            return R.error("导入失败");
        }
        //更新口岸字段
        FdInvoiceCheck fdInvoiceCheck = new FdInvoiceCheck();
        fdInvoiceCheck.setCreateBy(userInfo.getUserName());
        fdInvoiceCheckMapper.updateMissingFields(fdInvoiceCheck);
        return R.success();
    }

}
