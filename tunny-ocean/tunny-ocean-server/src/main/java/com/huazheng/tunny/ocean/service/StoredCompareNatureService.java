package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.StoredCompareNature;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 省平台-数据同比（进出口、过境、回程）---自然列 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-10-13 13:51:51
 */
public interface StoredCompareNatureService extends IService<StoredCompareNature> {
    /**
     * 查询省平台-数据同比（进出口、过境、回程）---自然列信息
     *
     * @param rowId 省平台-数据同比（进出口、过境、回程）---自然列ID
     * @return 省平台-数据同比（进出口、过境、回程）---自然列信息
     */
    public StoredCompareNature selectStoredCompareNatureById(Integer rowId);

    /**
     * 查询省平台-数据同比（进出口、过境、回程）---自然列列表
     *
     * @param storedCompareNature 省平台-数据同比（进出口、过境、回程）---自然列信息
     * @return 省平台-数据同比（进出口、过境、回程）---自然列集合
     */
    public List<StoredCompareNature> selectStoredCompareNatureList(StoredCompareNature storedCompareNature);


    /**
     * 分页模糊查询省平台-数据同比（进出口、过境、回程）---自然列列表
     * @return 省平台-数据同比（进出口、过境、回程）---自然列集合
     */
    public Page selectStoredCompareNatureListByLike(Query query);



    /**
     * 新增省平台-数据同比（进出口、过境、回程）---自然列
     *
     * @param storedCompareNature 省平台-数据同比（进出口、过境、回程）---自然列信息
     * @return 结果
     */
    public int insertStoredCompareNature(StoredCompareNature storedCompareNature);

    /**
     * 修改省平台-数据同比（进出口、过境、回程）---自然列
     *
     * @param storedCompareNature 省平台-数据同比（进出口、过境、回程）---自然列信息
     * @return 结果
     */
    public int updateStoredCompareNature(StoredCompareNature storedCompareNature);

    /**
     * 删除省平台-数据同比（进出口、过境、回程）---自然列
     *
     * @param rowId 省平台-数据同比（进出口、过境、回程）---自然列ID
     * @return 结果
     */
    public int deleteStoredCompareNatureById(Integer rowId);

    /**
     * 批量删除省平台-数据同比（进出口、过境、回程）---自然列
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteStoredCompareNatureByIds(Integer[] rowIds);

}

