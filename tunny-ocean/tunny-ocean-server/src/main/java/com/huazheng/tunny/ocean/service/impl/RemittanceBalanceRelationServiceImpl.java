package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.RemittanceBalanceRelation;
import com.huazheng.tunny.ocean.mapper.RemittanceBalanceRelationMapper;
import com.huazheng.tunny.ocean.service.RemittanceBalanceRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("remittanceBalanceRelationService")
public class RemittanceBalanceRelationServiceImpl extends ServiceImpl<RemittanceBalanceRelationMapper, RemittanceBalanceRelation> implements RemittanceBalanceRelationService {

    @Autowired
    private RemittanceBalanceRelationMapper remittanceBalanceRelationMapper;

    /**
     * 查询收款结算关系表信息
     *
     * @param id 收款结算关系表ID
     * @return 收款结算关系表信息
     */
    @Override
    public RemittanceBalanceRelation selectRemittanceBalanceRelationById(Integer id) {
        return remittanceBalanceRelationMapper.selectRemittanceBalanceRelationById(id);
    }

    /**
     * 查询收款结算关系表列表
     *
     * @param remittanceBalanceRelation 收款结算关系表信息
     * @return 收款结算关系表集合
     */
    @Override
    public List<RemittanceBalanceRelation> selectRemittanceBalanceRelationList(RemittanceBalanceRelation remittanceBalanceRelation) {
        return remittanceBalanceRelationMapper.selectRemittanceBalanceRelationList(remittanceBalanceRelation);
    }


    /**
     * 分页模糊查询收款结算关系表列表
     *
     * @return 收款结算关系表集合
     */
    @Override
    public Page selectRemittanceBalanceRelationListByLike(Query query) {
        RemittanceBalanceRelation remittanceBalanceRelation = BeanUtil.mapToBean(query.getCondition(), RemittanceBalanceRelation.class, false);
        query.setRecords(remittanceBalanceRelationMapper.selectRemittanceBalanceRelationListByLike(query, remittanceBalanceRelation));
        return query;
    }

    /**
     * 新增收款结算关系表
     *
     * @param remittanceBalanceRelation 收款结算关系表信息
     * @return 结果
     */
    @Override
    public int insertRemittanceBalanceRelation(RemittanceBalanceRelation remittanceBalanceRelation) {
        return remittanceBalanceRelationMapper.insertRemittanceBalanceRelation(remittanceBalanceRelation);
    }

    /**
     * 修改收款结算关系表
     *
     * @param remittanceBalanceRelation 收款结算关系表信息
     * @return 结果
     */
    @Override
    public int updateRemittanceBalanceRelation(RemittanceBalanceRelation remittanceBalanceRelation) {
        return remittanceBalanceRelationMapper.updateRemittanceBalanceRelation(remittanceBalanceRelation);
    }


    /**
     * 删除收款结算关系表
     *
     * @param id 收款结算关系表ID
     * @return 结果
     */
    public int deleteRemittanceBalanceRelationById(Integer id) {
        return remittanceBalanceRelationMapper.deleteRemittanceBalanceRelationById(id);
    }

    ;


    /**
     * 批量删除收款结算关系表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteRemittanceBalanceRelationByIds(Integer[] ids) {
        return remittanceBalanceRelationMapper.deleteRemittanceBalanceRelationByIds(ids);
    }

}
