package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.ShiftStatDetail;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
 * Service
 *
 * <AUTHOR>
 * @since 2025-05-06 16:18:59
 */
public interface ShiftStatDetailService extends IService<ShiftStatDetail> {

    /**
     * 班次统计明细表分页
     *
     * @param shiftStatDetail 班次统计明细表
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:18:59
     */
    Page<ShiftStatDetail> page(ShiftStatDetail shiftStatDetail);

    /**
     * 班次统计明细表详情
     *
     * @param detailId id
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:18:59
     */
    R<ShiftStatDetail> info(Integer detailId);

    /**
     * 班次统计明细表保存
     *
     * @param shiftStatDetail 班次统计明细表
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:18:59
     */
    R<T> save(ShiftStatDetail shiftStatDetail);

    /**
     * 班次统计明细表修改
     *
     * @param shiftStatDetail 班次统计明细
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:18:59
     */
    R<T> update(ShiftStatDetail shiftStatDetail);

    /**
     * 班次统计明细表删除
     *
     * @param detailIds ids
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:18:59
     */
    R<T> delete(Integer[] detailIds);

    /**
     * 班次统计明细表列表
     *
     * @param shiftStatDetail 班次统计明细表
     * @return R
     * <AUTHOR>
     * @since 2025-05-06 16:18:59
     */
    R<List<ShiftStatDetail>> list(ShiftStatDetail shiftStatDetail);

}

