package com.huazheng.tunny.ocean.service.eabillmain;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.dto.eabillmain.EaFeeDTO;
import com.huazheng.tunny.ocean.api.entity.eabillmain.EaFee;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 费用表 服务接口层
 *
 * <AUTHOR>
 * @date 2025-07-08 17:04:20
 */
public interface EaFeeService extends IService<EaFee> {
    /**
     * 查询费用表信息
     *
     * @param feeId 费用表ID
     * @return 费用表信息
     */
    public EaFee selectEaFeeById(Long feeId);

    /**
     * 查询费用表列表
     *
     * @param eaFee 费用表信息
     * @return 费用表集合
     */
    public List<EaFeeDTO> selectEaFeeList(EaFeeDTO eaFee);


    /**
     * 分页模糊查询费用表列表
     * @return 费用表集合
     */
    public Page selectEaFeeListByLike(Query query);



    /**
     * 新增费用表
     *
     * @param eaFee 费用表信息
     * @return 结果
     */
    public int insertEaFee(EaFee eaFee);

    /**
     * 修改费用表
     *
     * @param eaFee 费用表信息
     * @return 结果
     */
    public int updateEaFee(EaFee eaFee);

    /**
     * 删除费用表
     *
     * @param feeId 费用表ID
     * @return 结果
     */
    public int deleteEaFeeById(Long feeId);

    /**
     * 批量删除费用表
     *
     * @param feeIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEaFeeByIds(Integer[] feeIds);

}

