package com.huazheng.tunny.ocean.service.ealedgermain;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.ealedgermain.EaLedgerDetail;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.vo.ealedgermain.EaLedgerTieLuFeeVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 台账细表 服务接口层
 *
 * <AUTHOR>
 * @date 2025-07-02 15:42:19
 */
public interface EaLedgerDetailService extends IService<EaLedgerDetail> {
    /**
     * 查询台账细表信息
     *
     * @param ledgerDetailId 台账细表ID
     * @return 台账细表信息
     */
    public EaLedgerDetail selectEaLedgerDetailById(Long ledgerDetailId);

    /**
     * 查询台账细表列表
     *
     * @param eaLedgerDetail 台账细表信息
     * @return 台账细表集合
     */
    public List<EaLedgerDetail> selectEaLedgerDetailList(EaLedgerDetail eaLedgerDetail);


    /**
     * 分页模糊查询台账细表列表
     * @return 台账细表集合
     */
    public Page selectEaLedgerDetailListByLike(Query query);



    /**
     * 新增台账细表
     *
     * @param eaLedgerDetail 台账细表信息
     * @return 结果
     */
    public int insertEaLedgerDetail(EaLedgerDetail eaLedgerDetail);

    /**
     * 修改台账细表
     *
     * @param eaLedgerDetail 台账细表信息
     * @return 结果
     */
    public int updateEaLedgerDetail(EaLedgerDetail eaLedgerDetail);

    /**
     * 删除台账细表
     *
     * @param ledgerDetailId 台账细表ID
     * @return 结果
     */
    public int deleteEaLedgerDetailById(Long ledgerDetailId);

    /**
     * 批量删除台账细表
     *
     * @param ledgerDetailIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteEaLedgerDetailByIds(Integer[] ledgerDetailIds);

    R containerFeeList(Map<String, Object> params);

    R updateTieLuFee(List<EaLedgerTieLuFeeVo> params);

    void exportEaLedgerDetailExcel(EaLedgerDetail eaLedgerDetail, HttpServletResponse response);
}

