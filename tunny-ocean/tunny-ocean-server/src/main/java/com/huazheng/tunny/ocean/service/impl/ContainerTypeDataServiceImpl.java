package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.mapper.ContainerTypeDataMapper;
import com.huazheng.tunny.ocean.api.entity.ContainerTypeData;
import com.huazheng.tunny.ocean.service.ContainerTypeDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service("containerTypeDataService")
public class ContainerTypeDataServiceImpl extends ServiceImpl<ContainerTypeDataMapper, ContainerTypeData> implements ContainerTypeDataService {

    @Autowired
    private ContainerTypeDataMapper containerTypeDataMapper;

    public ContainerTypeDataMapper getContainerTypeDataMapper() {
        return containerTypeDataMapper;
    }

    public void setContainerTypeDataMapper(ContainerTypeDataMapper containerTypeDataMapper) {
        this.containerTypeDataMapper = containerTypeDataMapper;
    }

    /**
     * 查询箱型数据管理表信息
     *
     * @param rowId 箱型数据管理表ID
     * @return 箱型数据管理表信息
     */
    @Override
    public ContainerTypeData selectContainerTypeDataById(String rowId)
    {
        return containerTypeDataMapper.selectContainerTypeDataById(rowId);
    }

    /**
     * 查询箱型数据管理表列表
     *
     * @param containerTypeData 箱型数据管理表信息
     * @return 箱型数据管理表集合
     */
    @Override
    public List<ContainerTypeData> selectContainerTypeDataList(ContainerTypeData containerTypeData)
    {
        return containerTypeDataMapper.selectContainerTypeDataList(containerTypeData);
    }


    /**
     * 分页模糊查询箱型数据管理表列表
     * @return 箱型数据管理表集合
     */
    @Override
    public Page selectContainerTypeDataListByLike(Query query)
    {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.add_time");
            query.setAsc(Boolean.FALSE);
        }
        ContainerTypeData containerTypeData =  BeanUtil.mapToBean(query.getCondition(), ContainerTypeData.class,false);
        query.setRecords(containerTypeDataMapper.selectContainerTypeDataListByLike(query,containerTypeData));
        return query;
    }

    @Override
    public List<ContainerTypeData> selectContainerTypeDataListByLike(ContainerTypeData containerTypeData)
    {
        containerTypeData.setDeleteFlag("N");
        return containerTypeDataMapper.selectContainerTypeDataListByLike(containerTypeData);
    }

    /**
     * 新增箱型数据管理表
     *
     * @param containerTypeData 箱型数据管理表信息
     * @return 结果
     */
    @Override
    public int insertContainerTypeData(ContainerTypeData containerTypeData)
    {
        containerTypeData.setRowId(UUID.randomUUID().toString());
        containerTypeData.setAddWho(SecurityUtils.getUserInfo().getUserName());
        containerTypeData.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
        containerTypeData.setAddTime(LocalDateTime.now());
        return containerTypeDataMapper.insertContainerTypeData(containerTypeData);
    }

    /**
     * 修改箱型数据管理表
     *
     * @param containerTypeData 箱型数据管理表信息
     * @return 结果
     */
    @Override
    public int updateContainerTypeData(ContainerTypeData containerTypeData)
    {
        containerTypeData.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
        containerTypeData.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
        containerTypeData.setUpdateTime(LocalDateTime.now());
        return containerTypeDataMapper.updateContainerTypeData(containerTypeData);
    }


    /**
     * 删除箱型数据管理表
     *
     * @param rowId 箱型数据管理表ID
     * @return 结果
     */
    public int deleteContainerTypeDataById(String rowId)
    {
        return containerTypeDataMapper.deleteContainerTypeDataById( rowId);
    };


    /**
     * 批量删除箱型数据管理表对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteContainerTypeDataByIds(Integer[] rowIds)
    {
        return containerTypeDataMapper.deleteContainerTypeDataByIds( rowIds);
    }

    @Override
    public List<ContainerTypeData> selectDuplicate(ContainerTypeData containerTypeData)
    {
        return containerTypeDataMapper.selectDuplicate(containerTypeData);
    }

    /**
     * 查询箱型尺寸
     * @param params
     * @return
     */
    @Override
    public List<ContainerTypeData> selectSizeList(Map<String, Object> params) {
        return containerTypeDataMapper.selectSizeList(params);
    }
}
