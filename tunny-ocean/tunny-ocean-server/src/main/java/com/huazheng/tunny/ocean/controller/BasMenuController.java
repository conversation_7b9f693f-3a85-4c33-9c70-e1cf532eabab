package com.huazheng.tunny.ocean.controller;


import com.huazheng.tunny.ocean.api.entity.BasMenu;
import com.huazheng.tunny.ocean.api.entity.BasOrg;
import com.huazheng.tunny.ocean.service.BasMenuService;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 菜单信息管理表
 *
 * <AUTHOR> code ocean
 * @date 2021-07-01 09:18:58
 */
@RestController
@RequestMapping("/basmenu")
@Slf4j
public class BasMenuController {
    @Autowired
    private BasMenuService basMenuService;

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  basMenuService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return basMenuService.selectBasMenuListByLike(new Query<>(params));
    }


    /**
     * 信息
     *
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        BasMenu basMenu = basMenuService.selectById(rowId);
        return new R<>(basMenu);
    }

    /**
     * 保存
     *
     * @param basMenu
     * @return R
     */
    @PostMapping
    public R save(@RequestBody BasMenu basMenu) {
        basMenuService.insert(basMenu);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     *
     * @param basMenu
     * @return R
     */
    @PutMapping
    public R update(@RequestBody BasMenu basMenu) {
        basMenuService.updateById(basMenu);
        return new R<>(Boolean.TRUE);
    }


    /**
     * 删除
     *
     * @param rowId
     * @return R
     */
    @DeleteMapping("/{rowId}")
    public R delete(@PathVariable String rowId) {
        BasMenu basMenu = basMenuService.selectById(rowId);
        if (null != basMenu) {
            basMenu.setDeleteFlag("Y");
            basMenuService.updateById(basMenu);
        } else {
            return new R<>(500, false, "未存在该条数据");
        }
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     *
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody List<String> rowIds) {
        basMenuService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 导出EXCEL
     *
     * @return
     */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title,
                         HttpServletResponse response) throws Exception {
        // 通过工具类创建writer，默认创建xls格式
        //ExcelWriter writer = ExcelUtil.getWriter();
        //创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //设置sql查询筛选字段集合
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keyList.add(titleMap.get("key"));
            //自定义标题别名
            writer.addHeaderAlias(titleMap.get("key"), titleMap.get("label"));
        }
        EntityWrapper<BasMenu> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keyList));
        List<Map<String, Object>> list = basMenuService.selectMaps(entityWrapper);
        writer.write(list);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=table-list.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out);
        // 关闭writer，释放内存
        writer.close();
    }


    /**
     * 导入EXCEL
     * <p>
     *
     * @param titleStr
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/imported")
    public R imported(String titleStr,
                      MultipartFile file) throws Exception {
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map> title = JSONUtil.toList(JSONUtil.parseArray(titleStr), Map.class);
        for (Map<String, String> titleMap : title) {
            //自定义标题别名
            reader.addHeaderAlias(titleMap.get("label"), titleMap.get("key"));
        }
        List<BasMenu> list = reader.readAll(BasMenu.class);
        basMenuService.insertBatch(list);
        return new R<>(Boolean.TRUE);
    }
}
