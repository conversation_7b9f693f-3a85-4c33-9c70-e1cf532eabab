package com.huazheng.tunny.ocean.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.api.entity.FiWhGoods;
import com.huazheng.tunny.ocean.api.entity.FiWhInfo;
import com.huazheng.tunny.ocean.api.entity.SysAttachments;
import com.huazheng.tunny.ocean.service.FiWhGoodsService;
import com.huazheng.tunny.ocean.service.FiWhInfoService;
import com.huazheng.tunny.ocean.service.SysAttachmentsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;


/**
 * 仓单入库申请表
 *
 * <AUTHOR>
 * @date 2022-11-09 14:01:58
 */
@Slf4j
@RestController
@RequestMapping("/fiwhinfo")
public class FiWhInfoController {

    @Autowired
    private FiWhInfoService fiWhInfoService;

    @Autowired
    private FiWhGoodsService fiWhGoodsService;

    @Autowired
    private SysAttachmentsService sysAttachmentsService;

    /**
    *  列表
    * @param params
    * @return
    */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  fiWhInfoService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return fiWhInfoService.selectFiWhInfoListByLike(new Query<>(params));
    }

    /**
     * 信息
     * @param rowId
     * @return R
     */
    @GetMapping("/{rowId}")
    public R info(@PathVariable("rowId") String rowId) {
        FiWhInfo fiWhInfo =fiWhInfoService.selectById(rowId);
        FiWhGoods good = new FiWhGoods();
        good.setWrCode(fiWhInfo.getWrCode());
        good.setDeleteFlag("0");
        List<FiWhGoods> fiWhGoods = fiWhGoodsService.selectFiWhGoodsList(good);
        fiWhInfo.setGoodsList(fiWhGoods);
        SysAttachments att = new SysAttachments();
        att.setBusCode(fiWhInfo.getWrCode());
        att.setDeleteFlag("0");
        List<SysAttachments> sysAttachments = sysAttachmentsService.selectSysAttachmentsList(att);
        fiWhInfo.setAttachmentsList(sysAttachments);

        return new R<>(fiWhInfo);
    }

    /**
     * 保存
     * @param fiWhInfo
     * @return R
     */
    @PostMapping
    public R save(@RequestBody FiWhInfo fiWhInfo) {
        fiWhInfo.setRowId(UUID.randomUUID().toString());
        fiWhInfo.setWrCode(UUID.randomUUID().toString());
        fiWhInfo.setAddWho(SecurityUtils.getUserInfo().getUserName());
        fiWhInfo.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
        fiWhInfo.setAddTime(LocalDateTime.now());
        if(fiWhInfo.getGoodsList()!=null && fiWhInfo.getGoodsList().size()>0){
            for (FiWhGoods good:fiWhInfo.getGoodsList()
                 ) {
                good.setRowId(UUID.randomUUID().toString());
                good.setWrCode(fiWhInfo.getWrCode());
                good.setAddWho(SecurityUtils.getUserInfo().getUserName());
                good.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                good.setAddTime(LocalDateTime.now());
                fiWhGoodsService.insertFiWhGoods(good);
            }

        }
        if(fiWhInfo.getAttachmentsList()!=null && fiWhInfo.getAttachmentsList().size()>0){
            for (SysAttachments att:fiWhInfo.getAttachmentsList()
                 ) {
                att.setRowId(UUID.randomUUID().toString());
                att.setBusCode(fiWhInfo.getWrCode());
                att.setBusType("fiwhinfo");
                att.setAddWho(SecurityUtils.getUserInfo().getUserName());
                att.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                att.setAddTime(LocalDateTime.now());
                sysAttachmentsService.insertSysAttachments(att);
            }

        }
        fiWhInfoService.insertFiWhInfo(fiWhInfo);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param fiWhInfo
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody FiWhInfo fiWhInfo) {
        fiWhInfo.setUpdateWho(SecurityUtils.getUserInfo().getUserName());
        fiWhInfo.setUpdateWhoName(SecurityUtils.getUserInfo().getRealName());
        fiWhInfo.setUpdateTime(LocalDateTime.now());
        fiWhInfoService.updateFiWhInfo(fiWhInfo);
        if(fiWhInfo.getGoodsList()!=null && fiWhInfo.getGoodsList().size()>0){
            fiWhGoodsService.deleteFiWhGoodsByWrCode(fiWhInfo.getWrCode());
            for (FiWhGoods good:fiWhInfo.getGoodsList()
            ) {
                good.setRowId(UUID.randomUUID().toString());
                good.setWrCode(fiWhInfo.getWrCode());
                good.setAddWho(SecurityUtils.getUserInfo().getUserName());
                good.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                good.setAddTime(LocalDateTime.now());
                fiWhGoodsService.insertFiWhGoods(good);
            }

        }
        if(fiWhInfo.getAttachmentsList()!=null && fiWhInfo.getAttachmentsList().size()>0){
            sysAttachmentsService.deleteSysAttachmentsByBusCode(fiWhInfo.getWrCode());
            for (SysAttachments att:fiWhInfo.getAttachmentsList()
            ) {
                att.setRowId(UUID.randomUUID().toString());
                att.setBusCode(fiWhInfo.getWrCode());
                att.setBusType("fiwhinfo");
                att.setAddWho(SecurityUtils.getUserInfo().getUserName());
                att.setAddWhoName(SecurityUtils.getUserInfo().getRealName());
                att.setAddTime(LocalDateTime.now());
                sysAttachmentsService.insertSysAttachments(att);
            }
        }
        return new R<>(Boolean.TRUE);
    }

    

    /**
     * 删除
     * @param rowId
     * @return R
     */
    @GetMapping("/del/{rowId}")
    public R delete(@PathVariable  String rowId) {
        fiWhInfoService.deleteById(rowId);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     * @param rowIds
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody  List<String> rowIds) {
        fiWhInfoService.deleteBatchIds(rowIds);
        return new R<>(Boolean.TRUE);
    }

    /**
    * 导出EXCEL
    *
    * @return
    */
    @PostMapping("/exported")
    public void exported(@RequestBody List<Map<String, String>> title, HttpServletResponse response) throws Exception {
        
        //设置sql查询筛选字段集合，根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> keySqlList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        for (Map<String, String> titleMap : title) {
            keySqlList.add(StrUtil.toUnderlineCase(titleMap.get("field")));
            keyList.add(titleMap.get("field"));
        }
        EntityWrapper<FiWhInfo> entityWrapper = new EntityWrapper<>();
        entityWrapper.setSqlSelect(String.join(",", keySqlList));
        List<FiWhInfo> list = fiWhInfoService.selectList(entityWrapper);
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(response.getOutputStream(), FiWhInfo.class)
                .includeColumnFiledNames(keyList)
                .sheet(1)
                .doWrite(list);
    }



    /**
    * 导入EXCEL
    * <p>
    *
    * @param titleStr
    * @param file
    * @return
    * @throws Exception
    */
    /*@PostMapping("/imported")
    @Transactional
    public R imported(String titleStr, MultipartFile file) throws Exception {
        
        List list = new ArrayList();
        try {
            EasyExcel.read(file.getInputStream(), FiWhInfo.class, new EasyExcelListener<FiWhInfo>() {
                //数据处理逻辑，需要实现数据校验必须重写父级的invoke方法
                @Override
                public void invoke(FiWhInfo entity, AnalysisContext analysisContext) {
                    //log.info("解析到一条数据:{}", JSON.toJSONString(excelItem));
                    //数据校验逻辑
                    //String name = entity.getId();
                    //if (name.length()>10) {
                    //    throw new RuntimeException(String.format("第%s行错误，名称过长", analysisContext.readRowHolder().getRowIndex() + 1));
                    //}
                    //每读取1000条数据保存一次
                    list.add(entity);
                    if (list.size() >= 1000) {
                        saveData(list);
                        list.clear();
                    }
                }

                *//**
                 * 所有数据解析完成了就会来调用，确保最后遗留的数据也存储到数据库
                 *//*
                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    saveData(list);
                    list.clear();
                }

                //数据批量保存逻辑
                @Override
                public void saveData(List<FiWhInfo> infoList) {
                        fiWhInfoService.insertBatch(infoList);
                }
                //headRowNumber()声明标题行占用行数
            }).headRowNumber(1).sheet().doRead();
        }catch (Exception e){
            e.printStackTrace();
            return new R<>(Boolean.FALSE, e.getMessage());
        }
        return new R<>(Boolean.TRUE);
    }*/
}
