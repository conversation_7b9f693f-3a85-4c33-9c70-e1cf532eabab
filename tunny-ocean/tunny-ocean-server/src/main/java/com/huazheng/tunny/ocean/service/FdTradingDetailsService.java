package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.dto.FdAggregateBalanceDTO;
import com.huazheng.tunny.ocean.api.entity.FdTradingDetails;
import com.huazheng.tunny.ocean.api.vo.FdBillVO;
import com.huazheng.tunny.ocean.api.vo.Remittance;

import java.util.List;

/**
 * 交易明细表 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2021-08-18 12:29:45
 */
public interface FdTradingDetailsService extends IService<FdTradingDetails> {
    /**
     * 查询交易明细表信息
     *
     * @param id 交易明细表ID
     * @return 交易明细表信息
     */
    public FdTradingDetails selectFdTradingDetailsById(Integer id);

    /**
     * 查询交易明细表列表
     *
     * @param fdTradingDetails 交易明细表信息
     * @return 交易明细表集合
     */
    public List<FdTradingDetails> selectFdTradingDetailsList(FdTradingDetails fdTradingDetails);

    public List<FdTradingDetails> selectFdTradingDetailsList2(FdTradingDetails fdTradingDetails);

    /**
     * 分页模糊查询交易明细表列表
     * @return 交易明细表集合
     */
    public Page selectFdTradingDetailsListByLike(Query query);

    public R insertFdTradingDetailsShi(FdTradingDetails fdTradingDetails) throws Exception;

    /**
     * 查询客户余额一级List
     * @param query
     * @return
     */
    public Page selectAggregateBalanceList(Query query);

    public Page selectAggregateBalanceList2(Query query);

    public Page selectAggregateBalanceList3(Query query);

    public Page selectProvinceInRailway(Query query);

    public Page selectCityInProvince(Query query);

    public List<FdAggregateBalanceDTO>selectAggregateBalanceListSub(FdAggregateBalanceDTO fdAggregateBalanceDTO);

    /**
     * 保存
     * @param fdTradingDetails
     * @return
     */
    public R insertTrandingDetails(FdTradingDetails fdTradingDetails) throws Exception;
    /**
     * 修改
     * @param fdTradingDetails
     * @return R
     */
    public R update(FdTradingDetails fdTradingDetails);

    public R updateNew(FdTradingDetails fdTradingDetails);

    public R updateFdTradingDetailsById(FdTradingDetails fdTradingDetails);

    /**
     * 根据客户编码查询余额
     * @param fdTradingDetails
     * @return
     */
    public R selectBalanceByCode(FdTradingDetails fdTradingDetails);



    /**
     * 新增交易明细表
     *
     * @param fdTradingDetails 交易明细表信息
     * @return 结果
     */
    public int insertFdTradingDetails(FdTradingDetails fdTradingDetails);

    /**
     * 修改交易明细表
     *
     * @param fdTradingDetails 交易明细表信息
     * @return 结果
     */
    public int updateFdTradingDetails(FdTradingDetails fdTradingDetails);

    public int deleteFdTradingDetailsByBillCode(FdTradingDetails fdTradingDetails);

    /**
     * 删除交易明细表
     *
     * @param id 交易明细表ID
     * @return 结果
     */
    public int deleteFdTradingDetailsById(Integer id);

    /**
     * 批量删除交易明细表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteFdTradingDetailsByIds(Integer[] ids);

    void invalidbybillcode(String billCode);

    Boolean insertFdTradingDetailsbyRemittance(FdBillVO fdBillVO);

    Boolean insertFdTradingDetailsbyRemittance2(FdBillVO fdBillVO);


    public List<FdTradingDetails> selectHnrbList(FdTradingDetails fdTradingDetails);

    /**
     *  查询客户余额流水列表
     * @param fdTradingDetails
     * @return
     */
    List<FdTradingDetails> getList(FdTradingDetails fdTradingDetails);

    List<FdTradingDetails> getRemittanceList(FdTradingDetails fdTradingDetails);
}

