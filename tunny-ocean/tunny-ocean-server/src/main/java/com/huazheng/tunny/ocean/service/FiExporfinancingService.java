package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.FiExporfinancing;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;
import java.util.Map;

/**
 * 出口融资主表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-04-25 13:55:16
 */
public interface FiExporfinancingService extends IService<FiExporfinancing> {
    /**
     * 查询出口融资主表信息
     *
     * @param rowId 出口融资主表ID
     * @return 出口融资主表信息
     */
    public FiExporfinancing selectFiExporfinancingById(String rowId);

    /**
     * 查询出口融资主表列表
     *
     * @param fiExporfinancing 出口融资主表信息
     * @return 出口融资主表集合
     */
    public List<FiExporfinancing> selectFiExporfinancingList(FiExporfinancing fiExporfinancing);


    /**
     * 分页模糊查询出口融资主表列表
     * @return 出口融资主表集合
     */
    public Page selectFiExporfinancingListByLike(Query query);

    public R selectByAssetCode(Map<String, Object> params);



    /**
     * 新增出口融资主表
     *
     * @param fiExporfinancing 出口融资主表信息
     * @return 结果
     */
    public int insertFiExporfinancing(FiExporfinancing fiExporfinancing);

    /**
     * 修改出口融资主表
     *
     * @param fiExporfinancing 出口融资主表信息
     * @return 结果
     */
    public int updateFiExporfinancing(FiExporfinancing fiExporfinancing);

    /**
     * 删除出口融资主表
     *
     * @param rowId 出口融资主表ID
     * @return 结果
     */
    public int deleteFiExporfinancingById(String rowId);

    /**
     * 批量删除出口融资主表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiExporfinancingByIds(Integer[] rowIds);

}

