package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.druid.support.json.JSONUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.util.StringUtils;
import com.huazheng.tunny.ocean.api.vo.FiHisFinancingVo;
import com.huazheng.tunny.ocean.api.vo.FiHisVo;
import com.huazheng.tunny.ocean.controller.SignatureController;
import com.huazheng.tunny.ocean.mapper.FiHisFinancingInfoMapper;
import com.huazheng.tunny.ocean.api.entity.FiHisFinancingInfo;
import com.huazheng.tunny.ocean.service.FiHisFinancingInfoService;
import com.sun.scenario.effect.impl.sw.sse.SSEBlend_SRC_OUTPeer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("fiHisFinancingInfoService")
public class FiHisFinancingInfoServiceImpl extends ServiceImpl<FiHisFinancingInfoMapper, FiHisFinancingInfo> implements FiHisFinancingInfoService {

    @Autowired
    private FiHisFinancingInfoMapper fiHisFinancingInfoMapper;
    @Autowired
    private SignatureController signatureController;

    /**
     * 查询历史融资信息信息
     *
     * @param rowId 历史融资信息ID
     * @return 历史融资信息信息
     */
    @Override
    public FiHisFinancingInfo selectFiHisFinancingInfoById(String rowId)
    {
        return fiHisFinancingInfoMapper.selectFiHisFinancingInfoById(rowId);
    }

    /**
     * 查询历史融资信息列表
     *
     * @param fiHisFinancingInfo 历史融资信息信息
     * @return 历史融资信息集合
     */
    @Override
    public List<FiHisFinancingInfo> selectFiHisFinancingInfoList(FiHisFinancingInfo fiHisFinancingInfo)
    {
        return fiHisFinancingInfoMapper.selectFiHisFinancingInfoList(fiHisFinancingInfo);
    }


    /**
     * 分页模糊查询历史融资信息列表
     * @return 历史融资信息集合
     */
    @Override
    public Page selectFiHisFinancingInfoListByLike(Query query)
    {
        FiHisFinancingInfo fiHisFinancingInfo =  BeanUtil.mapToBean(query.getCondition(), FiHisFinancingInfo.class,false);
        query.setRecords(fiHisFinancingInfoMapper.selectFiHisFinancingInfoListByLike(query,fiHisFinancingInfo));
        return query;
    }

    @Override
    public R selectFiHisFinancing(Map<String, Object> params) {
        R r=new R();
        FiHisFinancingInfo fiHisFinancingInfo =  BeanUtil.mapToBean(params, FiHisFinancingInfo.class,false);
        String content = JSONUtil.parseObj(fiHisFinancingInfo, true).toStringPretty();
        System.out.println("入参:"+content);
        String doPost = signatureController.doPost("/v1/ent/financing/history", content);
        System.out.println("返回值:"+doPost);
        JSONObject result = JSONUtil.parseObj(doPost);
        String b = String.valueOf(result.get("b"));
        if(!"".equals(b) && b!= null && "true".equals(b)){
            if(null !=result.get("data").toString()) {
                Object data = result.get("data");
                JSONObject objects = JSONUtil.parseObj(data);
                FiHisFinancingVo fiHisFinancingVo = JSONUtil.toBean(objects, FiHisFinancingVo.class);
                if(fiHisFinancingVo !=null) {
                    List<FiHisVo> assetList = fiHisFinancingVo.getAssetList();
                    List<FiHisVo> assetListTwo=new ArrayList<>(assetList);
                    for (int i=assetListTwo.size()-1;i>=0;i--) {
                        if(null==assetListTwo.get(i).getPayCurrency()){
                            assetListTwo.remove(i);
                        }
                    }
                        ArrayList<FiHisVo> collect = assetListTwo.stream().collect(
                                Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(FiHisVo::getPayCurrency))), ArrayList::new));
                        for (FiHisVo fiHisVo : assetListTwo) {
                            if ("" != fiHisVo.getPayAmount() && fiHisVo.getPayAmount() != null) {
                                if (BigDecimal.valueOf(Double.parseDouble(fiHisVo.getPayAmount())).compareTo(BigDecimal.valueOf(0)) > 0) {
                                    fiHisVo.setPayAmount(BigDecimal.valueOf(Double.parseDouble(fiHisVo.getPayAmount())).divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                                }
                            }
                            if ("" != fiHisVo.getPayAmountDollar() && fiHisVo.getPayAmountDollar() != null) {
                                if (BigDecimal.valueOf(Double.parseDouble(fiHisVo.getPayAmountDollar())).compareTo(BigDecimal.valueOf(0)) > 0) {
                                    fiHisVo.setPayAmountDollar(BigDecimal.valueOf(Double.parseDouble(fiHisVo.getPayAmountDollar())).divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                                }
                            }
                            if ("" != fiHisVo.getRepayAmountDollar() && fiHisVo.getRepayAmountDollar() != null) {
                                if (BigDecimal.valueOf(Double.parseDouble(fiHisVo.getRepayAmountDollar())).compareTo(BigDecimal.valueOf(0)) > 0) {
                                    fiHisVo.setRepayAmountDollar(BigDecimal.valueOf(Double.parseDouble(fiHisVo.getRepayAmountDollar())).divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                                }
                            }
                        }
                        List<Map<String, Object>> recmapList = new ArrayList<>();
                        List<Map<String, Object>> payMapList = new ArrayList<>();
                        for (FiHisVo collectListOne : collect) {
                            Map<String, Object> recMap = new HashMap<>();
                            Map<String, Object> payMap = new HashMap<>();
                            int i = 0;
                            int j = 0;
                            //放款金额合计
                            BigDecimal repayAmountSum = BigDecimal.valueOf(0);
                            //还款金额合计
                            BigDecimal payAmountSum = BigDecimal.valueOf(0);
                            for (FiHisVo collectListTwo : assetListTwo) {
                                if (collectListTwo.getPayCurrency().equals(collectListOne.getPayCurrency())) {
                                    ++i;
                                    if (StringUtils.isNotBlank(collectListTwo.getPayAmount())) {
                                        if (BigDecimal.valueOf(Double.parseDouble(collectListTwo.getPayAmount())).compareTo(BigDecimal.valueOf(0)) > 0) {
                                            repayAmountSum = repayAmountSum.add(BigDecimal.valueOf(Double.parseDouble(collectListTwo.getPayAmount())));
                                        }
                                    }
                                    if (StringUtils.isNotBlank(collectListTwo.getAssetStateName())) {
                                        if (collectListTwo.getAssetStateName().equals("已还款")) {
                                            if (StringUtils.isNotBlank(collectListTwo.getRepayAmount())) {
                                                j++;
                                                payAmountSum = payAmountSum.add(BigDecimal.valueOf(Double.parseDouble(collectListTwo.getRepayAmount())));
                                            }
                                        }
                                    }
                                }
                            }
                            recMap.put("payCurrency", collectListOne.getPayCurrency());
                            recMap.put("payCurrencySize", i);
                            recMap.put("repayAmountSum", repayAmountSum);
                            recmapList.add(recMap);
                            payMap.put("payCurrency", collectListOne.getPayCurrency());
                            payMap.put("payCurrencySize", j);
                            payMap.put("payAmountSum", payAmountSum);
                            payMapList.add(payMap);
                        }
                        fiHisFinancingVo.setRecList(recmapList);
                        fiHisFinancingVo.setPayList(payMapList);
                        System.out.println("放款 " + JSONUtil.toJsonStr(recmapList));
                        System.out.println("还款 " + JSONUtil.toJsonStr(payMapList));
                        if ("" != fiHisFinancingVo.getOutstandingAmountTotal() && fiHisFinancingVo.getOutstandingAmountTotal() != null) {
                            if (BigDecimal.valueOf(Double.parseDouble(fiHisFinancingVo.getOutstandingAmountTotal())).compareTo(BigDecimal.valueOf(0)) > 0) {
                                fiHisFinancingVo.setOutstandingAmountTotal(BigDecimal.valueOf(Double.parseDouble(fiHisFinancingVo.getOutstandingAmountTotal())).divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                            }
                        }

                        if ("" != fiHisFinancingVo.getPayAmountTotal() && fiHisFinancingVo.getPayAmountTotal() != null) {
                            if (BigDecimal.valueOf(Double.parseDouble(fiHisFinancingVo.getPayAmountTotal())).compareTo(BigDecimal.valueOf(0)) > 0) {
                                fiHisFinancingVo.setPayAmountTotal(BigDecimal.valueOf(Double.parseDouble(fiHisFinancingVo.getPayAmountTotal())).divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                            }
                        }

                        if ("" != fiHisFinancingVo.getRepayAmountTotal() && fiHisFinancingVo.getRepayAmountTotal() != null) {
                            if (BigDecimal.valueOf(Double.parseDouble(fiHisFinancingVo.getRepayAmountTotal())).compareTo(BigDecimal.valueOf(0)) > 0) {
                                fiHisFinancingVo.setRepayAmountTotal(BigDecimal.valueOf(Double.parseDouble(fiHisFinancingVo.getRepayAmountTotal())).divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                            }
                        }
                    }

                    r.setData(fiHisFinancingVo);
                    r.setB(Boolean.TRUE);
                    r.setCode(Integer.valueOf(result.get("code").toString()));
                    r.setMsg(result.get("msg").toString());
            }else{
                r.setB(Boolean.FALSE);
                r.setCode(200);
                r.setMsg("数据为空");
            }
        }else{
            r.setB(Boolean.FALSE);
            r.setCode(Integer.valueOf(result.get("code").toString()));
            r.setMsg(result.get("msg").toString());
        }
        return r;
    }

    /**
     * 新增历史融资信息
     *
     * @param fiHisFinancingInfo 历史融资信息信息
     * @return 结果
     */
    @Override
    public int insertFiHisFinancingInfo(FiHisFinancingInfo fiHisFinancingInfo)
    {
        return fiHisFinancingInfoMapper.insertFiHisFinancingInfo(fiHisFinancingInfo);
    }

    /**
     * 修改历史融资信息
     *
     * @param fiHisFinancingInfo 历史融资信息信息
     * @return 结果
     */
    @Override
    public int updateFiHisFinancingInfo(FiHisFinancingInfo fiHisFinancingInfo)
    {
        return fiHisFinancingInfoMapper.updateFiHisFinancingInfo(fiHisFinancingInfo);
    }


    /**
     * 删除历史融资信息
     *
     * @param rowId 历史融资信息ID
     * @return 结果
     */
    public int deleteFiHisFinancingInfoById(String rowId)
    {
        return fiHisFinancingInfoMapper.deleteFiHisFinancingInfoById( rowId);
    }


    /**
     * 批量删除历史融资信息对象
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteFiHisFinancingInfoByIds(Integer[] rowIds)
    {
        return fiHisFinancingInfoMapper.deleteFiHisFinancingInfoByIds( rowIds);
    }

}
