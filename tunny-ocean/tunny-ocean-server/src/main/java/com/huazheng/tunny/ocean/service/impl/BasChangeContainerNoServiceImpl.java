package com.huazheng.tunny.ocean.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.ocean.mapper.BasChangeContainerNoMapper;
import com.huazheng.tunny.ocean.api.entity.BasChangeContainerNo;
import com.huazheng.tunny.ocean.service.BasChangeContainerNoService;
import com.huazheng.tunny.ocean.util.CheckUtil;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.*;

@Service("basChangeContainerNoService")
public class BasChangeContainerNoServiceImpl extends ServiceImpl<BasChangeContainerNoMapper, BasChangeContainerNo> implements BasChangeContainerNoService {

    @Autowired
    private BasChangeContainerNoMapper basChangeContainerNoMapper;

    /**
     * 查询箱号变更管理信息
     *
     * @param id 箱号变更管理ID
     * @return 箱号变更管理信息
     */
    @Override
    public BasChangeContainerNo selectBasChangeContainerNoById(Integer id)
    {
        return basChangeContainerNoMapper.selectBasChangeContainerNoById(id);
    }

    /**
     * 查询箱号变更管理列表
     *
     * @param basChangeContainerNo 箱号变更管理信息
     * @return 箱号变更管理集合
     */
    @Override
    public List<BasChangeContainerNo> selectBasChangeContainerNoList(BasChangeContainerNo basChangeContainerNo)
    {
        return basChangeContainerNoMapper.selectBasChangeContainerNoList(basChangeContainerNo);
    }


    /**
     * 分页模糊查询箱号变更管理列表
     * @return 箱号变更管理集合
     */
    @Override
    public Page selectBasChangeContainerNoListByLike(Query query)
    {
        if(StrUtil.isBlank(query.getOrderByField())){
            query.setOrderByField("x.add_time");
            query.setAsc(Boolean.FALSE);
        }
        BasChangeContainerNo basChangeContainerNo =  BeanUtil.mapToBean(query.getCondition(), BasChangeContainerNo.class,false);
        basChangeContainerNo.setPlatformCode(SecurityUtils.getUserInfo().getPlatformCode());
        basChangeContainerNo.setDeleteFlag("N");
        query.setRecords(basChangeContainerNoMapper.selectBasChangeContainerNoListByLike(query,basChangeContainerNo));
        return query;
    }

    /**
     * 新增箱号变更管理
     *
     * @param basChangeContainerNo 箱号变更管理信息
     * @return 结果
     */
    @Override
    public int insertBasChangeContainerNo(BasChangeContainerNo basChangeContainerNo)
    {
        return basChangeContainerNoMapper.insertBasChangeContainerNo(basChangeContainerNo);
    }

    /**
     * 修改箱号变更管理
     *
     * @param basChangeContainerNo 箱号变更管理信息
     * @return 结果
     */
    @Override
    public int updateBasChangeContainerNo(BasChangeContainerNo basChangeContainerNo)
    {
        return basChangeContainerNoMapper.updateBasChangeContainerNo(basChangeContainerNo);
    }


    /**
     * 删除箱号变更管理
     *
     * @param id 箱号变更管理ID
     * @return 结果
     */
    public int deleteBasChangeContainerNoById(Integer id)
    {
        return basChangeContainerNoMapper.deleteBasChangeContainerNoById( id);
    };


    /**
     * 批量删除箱号变更管理对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBasChangeContainerNoByIds(Integer[] ids)
    {
        return basChangeContainerNoMapper.deleteBasChangeContainerNoByIds( ids);
    }

    @Override
    public void exportTemplate(HttpServletResponse response)throws IOException{
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("箱号变更信息");
        XSSFRow row = sheet.createRow(0);
        //宽度
        for (int i = 0; i <= 14; i++) {
            sheet.setColumnWidth(i, 60 * 80);
        }

        row.setHeight((short) (10 * 50));
        //字体1
        XSSFCellStyle style = setStyle(workbook);
        setTitle(row, style);

        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String(("箱号变更导入模板.xlsx").getBytes("GB2312"), "8859_1"));
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
        workbook.close();
    }

    public void setTitle(XSSFRow row, XSSFCellStyle style) {
        XSSFCell cell0 = row.createCell(0);
        cell0.setCellValue("班次号");
        cell0.setCellStyle(style);
        XSSFCell cell1 = row.createCell(1);
        cell1.setCellValue("变更前箱号");
        cell1.setCellStyle(style);
        XSSFCell cell2 = row.createCell(2);
        cell2.setCellValue("变更后箱号");
        cell2.setCellStyle(style);

    }

    public XSSFCellStyle setStyle(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");
        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        style.setRightBorderColor(HSSFColor.BLACK.index);
        style.setTopBorderColor(HSSFColor.BLACK.index);
        return style;
    }

    @Transactional(rollbackFor = Exception.class)
    public R importedTemplate(MultipartFile file) throws Exception {
        Workbook workbook = null;
        try {
            InputStream is = file.getInputStream();
            //利用WorkbookFactory获取Sheet，无需判断版本
            workbook = WorkbookFactory.create(is);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //导入第一张sheet页
        Sheet sheet = workbook.getSheetAt(0);
        SecruityUser userInfo = SecurityUtils.getUserInfo();
        //获取行数
        StringBuffer sb = new StringBuffer();
        Map<String,String> map = new HashMap<>();
        int lastRowNum = sheet.getLastRowNum();
        List<BasChangeContainerNo> list = new ArrayList<>();
        for (int i = 1; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            boolean blankFlag = CheckUtil.isRowEmpty(row);
            if (blankFlag) {
                continue;
            }
            BasChangeContainerNo basChangeContainerNo = new BasChangeContainerNo();
            if (row.getCell(0) != null) {
                row.getCell(0).setCellType(CellType.STRING);
                String shiftNo = row.getCell(0).getStringCellValue();
                basChangeContainerNo.setShiftNo(shiftNo);
            }else{
                sb.append("行" + (i + 1) + "班次号不能为空");
            }
            if (row.getCell(1) != null) {
                row.getCell(1).setCellType(CellType.STRING);
                String containerNo = row.getCell(1).getStringCellValue();
                boolean b = CheckUtil.verifyCntrCode(containerNo);
                if (b) {
                    basChangeContainerNo.setContainerNoOld(containerNo);
                } else {
                    sb.append("行" + (i + 1) + "变更前箱号格式错误：" + containerNo+" ");
                }
                String val = map.get(containerNo);
                if(StrUtil.isNotEmpty(val)){
                    sb.append("变更前/后箱号禁止重复，如必须更换，请分开导入" + containerNo+" ");
                }else{
                    map.put(containerNo,containerNo);
                }
            }else{
                sb.append("行" + (i + 1) + "变更前箱号不能为空"+" ");
            }
            if (row.getCell(2) != null) {
                row.getCell(2).setCellType(CellType.STRING);
                String containerNo = row.getCell(2).getStringCellValue();
                boolean b = CheckUtil.verifyCntrCode(containerNo);
                if (b) {
                    basChangeContainerNo.setContainerNoNew(containerNo);
                    List<BasChangeContainerNo> basChangeContainerNos = basChangeContainerNoMapper.selectWayBillInfo(basChangeContainerNo);
                    if(CollUtil.isNotEmpty(basChangeContainerNos)){
                        sb.append(basChangeContainerNo.getShiftNo() + "该班次已经存在该箱号" + basChangeContainerNo.getContainerNoNew()+" ");
                    }
                } else {
                    sb.append("行" + (i + 1) + "变更后箱号格式错误：" + containerNo+" ");
                }
                String val = map.get(containerNo);
                if(StrUtil.isNotEmpty(val)){
                    sb.append("变更前/后箱号禁止重复，如必须更换，请分开导入" + containerNo+" ");
                }else{
                    map.put(containerNo,containerNo);
                }
            }else{
                sb.append("行" + (i + 1) + "变更后箱号不能为空"+" ");
            }
            basChangeContainerNo.setPlatformCode(userInfo.getPlatformCode());
            basChangeContainerNo.setPlatformName(userInfo.getPlatformName());
            List<BasChangeContainerNo> basChangeContainerNos = basChangeContainerNoMapper.selectShiftInfo(basChangeContainerNo);
            if(CollUtil.isNotEmpty(basChangeContainerNos)){
                basChangeContainerNo.setCustomerNo(basChangeContainerNos.get(0).getCustomerNo());
                basChangeContainerNo.setCustomerName(basChangeContainerNos.get(0).getCustomerName());
                basChangeContainerNo.setShiftName(basChangeContainerNos.get(0).getShiftName());
                basChangeContainerNo.setShippingTime(basChangeContainerNos.get(0).getShippingTime());
            }else{
                sb.append(basChangeContainerNo.getShiftNo()+"该班次的订单中不存在该箱号:"+basChangeContainerNo.getContainerNoOld()+" ");
            }
            basChangeContainerNo.setAddWho(userInfo.getUserName());
            basChangeContainerNo.setAddWhoName(userInfo.getRealName());
            basChangeContainerNo.setAddTime(LocalDateTime.now());
            list.add(basChangeContainerNo);
        }
        Set<String> seen = new HashSet<>();
        for (BasChangeContainerNo obj : list) {
            String key = obj.getShiftNo() + "  " + obj.getContainerNoOld();
            if (!seen.add(key)) {
                // 如果 key 已经存在于集合中，表示有重复
                sb.append(key + "该数据重复"+" ");
            }
            String key2 = obj.getShiftNo() + "  " + obj.getContainerNoNew();
            if (!seen.add(key2)) {
                // 如果 key 已经存在于集合中，表示有重复
                sb.append(key2 + "该数据重复"+" ");
            }
        }
        if(sb.length()>0){
            return new R<>(new Throwable(sb.toString()));
        }
        if(CollUtil.isNotEmpty(list)){
            for (BasChangeContainerNo basChangeContainerNo:list
                 ) {
                basChangeContainerNoMapper.insertBasChangeContainerNo(basChangeContainerNo);
                basChangeContainerNoMapper.updateBookingRequestDetail(basChangeContainerNo);
                basChangeContainerNoMapper.updateWaybillContainerInfo(basChangeContainerNo);
                basChangeContainerNoMapper.updateWaybillGoodsInfo(basChangeContainerNo);
                basChangeContainerNoMapper.updateWaybillParticipants(basChangeContainerNo);
                basChangeContainerNoMapper.updatePayCodeMes(basChangeContainerNo);
                basChangeContainerNoMapper.updateFdBusCostDetail(basChangeContainerNo);
                basChangeContainerNoMapper.updateFdShippingAccountDetail(basChangeContainerNo);
                basChangeContainerNoMapper.updateBasChangeboxContainerInfo(basChangeContainerNo);
                basChangeContainerNoMapper.updateBasChangeboxCostDetail(basChangeContainerNo);
            }
        }

        return new R<>(0, Boolean.TRUE, null, "操作成功");
    }

}
