package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.ocean.api.entity.BookingRequesdetail;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.ocean.api.entity.BookingRequesheader;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 订舱申请单子表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2021-07-02 14:38:08
 */
public interface BookingRequesdetailService extends IService<BookingRequesdetail> {
    /**
     * 查询订舱申请单子表信息
     *
     * @param rowId 订舱申请单子表ID
     * @return 订舱申请单子表信息
     */
    public BookingRequesdetail selectBookingRequesdetailById(String rowId);

    /**
     * 查询订舱申请单子表列表
     *
     * @param bookingRequesdetail 订舱申请单子表信息
     * @return 订舱申请单子表集合
     */
    public List<BookingRequesdetail> selectBookingRequesdetailList(BookingRequesdetail bookingRequesdetail);


    /**
     * 分页模糊查询订舱申请单子表列表
     * @return 订舱申请单子表集合
     */
    public Page selectBookingRequesdetailListByLike(Query query);

    public Map<String,Object> weChatList(BookingRequesdetail bookingRequesdetail);

    /**
     * 新增订舱申请单子表
     *
     * @param bookingRequesdetail 订舱申请单子表信息
     * @return 结果
     */
    public int insertBookingRequesdetail(BookingRequesdetail bookingRequesdetail);

    /**
     * 修改订舱申请单子表
     *
     * @param bookingRequesdetail 订舱申请单子表信息
     * @return 结果
     */
    public R updateBookingRequesdetail(BookingRequesdetail bookingRequesdetail);

    /**
     * 逻辑删除
     */
    public Integer updateBookingRequesdetails(BookingRequesheader bookingRequesheader);

    /**
     * 删除订舱申请单子表
     *
     * @param rowId 订舱申请单子表ID
     * @return 结果
     */
    public int deleteBookingRequesdetailById(String rowId);

    /**
     * 批量删除订舱申请单子表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteBookingRequesdetailByIds(Integer[] rowIds);

    public int deleteByOrderNoAndContainerNo(BookingRequesdetail book);

    public List<String> selectBookingRequesdetailByShiftNo(String shiftNo);

    R importTzTemplate(MultipartFile file, String shiftNo,String platformCode) throws Exception;
}

