package com.huazheng.tunny.ocean.service.impl;

import com.huazheng.tunny.ocean.mapper.TariffReleaseDetailMapper;
import com.huazheng.tunny.ocean.api.entity.TariffReleaseDetail;
import com.huazheng.tunny.ocean.service.TariffReleaseDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("tariffReleaseDetailService")
public class TariffReleaseDetailServiceImpl extends ServiceImpl<TariffReleaseDetailMapper, TariffReleaseDetail> implements TariffReleaseDetailService {

    @Autowired
    private TariffReleaseDetailMapper tariffReleaseDetailMapper;

    public TariffReleaseDetailMapper getTariffReleaseDetailMapper() {
        return tariffReleaseDetailMapper;
    }

    public void setTariffReleaseDetailMapper(TariffReleaseDetailMapper tariffReleaseDetailMapper) {
        this.tariffReleaseDetailMapper = tariffReleaseDetailMapper;
    }

    /**
     * 查询运价发布子表信息
     *
     * @param rowId 运价发布子表ID
     * @return 运价发布子表信息
     */
    @Override
    public TariffReleaseDetail selectTariffReleaseDetailById(String rowId)
    {
        return tariffReleaseDetailMapper.selectTariffReleaseDetailById(rowId);
    }

    /**
     * 查询运价发布子表列表
     *
     * @param tariffReleaseDetail 运价发布子表信息
     * @return 运价发布子表集合
     */
    @Override
    public List<TariffReleaseDetail> selectTariffReleaseDetailList(TariffReleaseDetail tariffReleaseDetail)
    {
        return tariffReleaseDetailMapper.selectTariffReleaseDetailList(tariffReleaseDetail);
    }


    /**
     * 分页模糊查询运价发布子表列表
     * @return 运价发布子表集合
     */
    @Override
    public Page selectTariffReleaseDetailListByLike(Query query)
    {
        TariffReleaseDetail tariffReleaseDetail =  BeanUtil.mapToBean(query.getCondition(), TariffReleaseDetail.class,false);
        List<TariffReleaseDetail> tariffReleaseDetails = tariffReleaseDetailMapper.selectTariffReleaseDetailListByLike(query, tariffReleaseDetail);
        query.setRecords(tariffReleaseDetails);
        query.setTotal(tariffReleaseDetailMapper.selectAllNo(tariffReleaseDetail));
        return query;
    }

    /**
     * 新增运价发布子表
     *
     * @param tariffReleaseDetail 运价发布子表信息
     * @return 结果
     */
    @Override
    public int insertTariffReleaseDetail(TariffReleaseDetail tariffReleaseDetail)
    {
        return tariffReleaseDetailMapper.insertTariffReleaseDetail(tariffReleaseDetail);
    }

    /**
     * 修改运价发布子表
     *
     * @param tariffReleaseDetail 运价发布子表信息
     * @return 结果
     */
    @Override
    public int updateTariffReleaseDetail(TariffReleaseDetail tariffReleaseDetail)
    {
        return tariffReleaseDetailMapper.updateTariffReleaseDetail(tariffReleaseDetail);
    }

    @Override
    public int updateTariffReleaseDetailByNo(TariffReleaseDetail tariffReleaseDetail)
    {
        return tariffReleaseDetailMapper.updateTariffReleaseDetailByNo(tariffReleaseDetail);
    }
    /**
     * 删除运价发布子表
     *
     * @param rowId 运价发布子表ID
     * @return 结果
     */
    public int deleteTariffReleaseDetailById(String rowId)
    {
        return tariffReleaseDetailMapper.deleteTariffReleaseDetailById( rowId);
    };


    /**
     * 批量删除运价发布子表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteTariffReleaseDetailByIds(Integer[] rowIds)
    {
        return tariffReleaseDetailMapper.deleteTariffReleaseDetailByIds( rowIds);
    }

}
