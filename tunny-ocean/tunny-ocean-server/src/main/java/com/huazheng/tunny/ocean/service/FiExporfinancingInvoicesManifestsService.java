package com.huazheng.tunny.ocean.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.ocean.api.entity.FiExporfinancingInvoicesManifests;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 出口融资发票子表 服务接口层
 *
 * <AUTHOR> code ocean
 * @date 2022-04-25 12:22:51
 */
public interface FiExporfinancingInvoicesManifestsService extends IService<FiExporfinancingInvoicesManifests> {
    /**
     * 查询出口融资发票子表信息
     *
     * @param rowId 出口融资发票子表ID
     * @return 出口融资发票子表信息
     */
    public FiExporfinancingInvoicesManifests selectFiExporfinancingInvoicesManifestsById(String rowId);

    /**
     * 查询出口融资发票子表列表
     *
     * @param fiExporfinancingInvoicesManifests 出口融资发票子表信息
     * @return 出口融资发票子表集合
     */
    public List<FiExporfinancingInvoicesManifests> selectFiExporfinancingInvoicesManifestsList(FiExporfinancingInvoicesManifests fiExporfinancingInvoicesManifests);


    /**
     * 分页模糊查询出口融资发票子表列表
     * @return 出口融资发票子表集合
     */
    public Page selectFiExporfinancingInvoicesManifestsListByLike(Query query);



    /**
     * 新增出口融资发票子表
     *
     * @param fiExporfinancingInvoicesManifests 出口融资发票子表信息
     * @return 结果
     */
    public int insertFiExporfinancingInvoicesManifests(FiExporfinancingInvoicesManifests fiExporfinancingInvoicesManifests);

    /**
     * 修改出口融资发票子表
     *
     * @param fiExporfinancingInvoicesManifests 出口融资发票子表信息
     * @return 结果
     */
    public int updateFiExporfinancingInvoicesManifests(FiExporfinancingInvoicesManifests fiExporfinancingInvoicesManifests);

    /**
     * 删除出口融资发票子表
     *
     * @param rowId 出口融资发票子表ID
     * @return 结果
     */
    public int deleteFiExporfinancingInvoicesManifestsById(String rowId);

    /**
     * 批量删除出口融资发票子表
     *
     * @param rowIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteFiExporfinancingInvoicesManifestsByIds(Integer[] rowIds);

}

